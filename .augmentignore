# Augment 忽略文件配置
# 此文件定义了 Augment AI 在分析代码时应该忽略的文件和目录

# ===== Laravel/Lumen 框架相关 =====
# Composer 依赖包
vendor/
composer.lock

# Laravel 缓存和编译文件
bootstrap/cache/
storage/app/
storage/framework/cache/
storage/framework/sessions/
storage/framework/views/
storage/logs/

# ===== 环境配置文件 =====
# 包含敏感信息的环境配置
.env
.env.*
dev.env
prod.env
test.env

# ===== 日志文件 =====
# 各种日志文件
*.log
logs/
storage/logs/
laravel.log

# ===== 临时文件和缓存 =====
# 系统临时文件
*.tmp
*.temp
*.cache
.DS_Store
Thumbs.db

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# ===== 构建和编译产物 =====
# 前端构建产物
node_modules/
npm-debug.log
yarn-error.log
public/js/
public/css/
public/mix-manifest.json

# PHP 编译缓存
*.compiled

# ===== 数据库相关 =====
# 数据库文件
*.sqlite
*.db

# 数据库备份文件
*.sql
*.dump
database/*.sql
database/dev_*.sql

# ===== 文件上传和存储 =====
# 用户上传的文件
public/uploads/
public/storage/
storage/app/public/

# Excel 文件和导出文件
*.xlsx
*.xls
*.csv
public/profit_bill/

# ===== 测试相关 =====
# 测试覆盖率报告
coverage/
.phpunit.result.cache

# ===== 版本控制 =====
# Git 相关
.git/
.gitignore

# ===== 文档和说明 =====
# 大型文档文件（保留重要的 API 文档）
README.md
doc/api.md
doc/document.md

# 但忽略其他文档
doc/database/
*.md
!doc/api.md
!doc/document.md
!README.md

# ===== 第三方库和工具 =====
# PHPExcel 旧版本
vendor/PHPExcel-1.8/

# Laravel IDE 辅助文件
vendor/_laravel_ide/

# ===== 项目特定忽略 =====
# 监控和统计相关的大文件
app/Illuminate/BillSpecialCustomer.rar

# 大型数据处理文件
app/Models/ClickHouse/
app/Models/Common/
app/Models/Cost/
app/Models/Customer/
app/Models/Income/
app/Models/Invoice/
app/Models/Monitor/
app/Models/MonitorCustom/
app/Models/Opdata/
app/Models/PreTestManage/
app/Models/Receiver/
app/Models/ReportDay/
app/Models/Usage/
app/Models/feishu/

# 大型 Repository 目录
app/Repositories/ClickHouse/
app/Repositories/Cost/
app/Repositories/Customer/
app/Repositories/Income/
app/Repositories/Invoice/

# 大型 Provider 目录
app/Providers/Approval/
app/Providers/Auth/
app/Providers/BillCost/
app/Providers/BillCostV2/
app/Providers/BillIncome/
app/Providers/BillIncomeV2/
app/Providers/BillInterface/
app/Providers/ClickHouse/
app/Providers/Monitor/
app/Providers/RedisCache/
app/Providers/Tool/

# 特殊导出类目录
app/Exports/SpecialCustomerExports/
app/Exports/StatTmp/

# 作业任务的版本化目录
app/Jobs/BillCostV2/
app/Jobs/BillIncomeV2/

# ===== 配置文件选择性忽略 =====
# 保留核心配置，忽略特定业务配置
config/approval.php
config/contract.php
config/customer.php
config/feishu.php
config/monitor.php
config/push_receipt.php

# ===== 大型数据文件 =====
# 任何大于 1MB 的文件
*.zip
*.tar
*.gz
*.rar

# ===== 敏感信息 =====
# 可能包含密钥的文件
*key*
*secret*
*password*
*token*

# 但保留配置模板
!*.example
!*.template
