# 📘 控制器与路由映射表

## 📂 控制器：BillController

- GET /v1/bill → bills
- GET /v1/bill/logs/{uuid} → logs
- GET /v1/bill/history/customer/{id} → history
- POST /v1/bill/email → email
- POST /v1/bill/sorts → sorts

## 📂 控制器：ChannelController

- GET /channel/getChannelList → getChannelList
- POST /channel/addChannel → addChannel
- GET /channel/getInterfaceList → getInterfaceList
- POST /channel/deletePrice → deletePrice

## 📂 控制器：InvoiceController

- POST /invoice/apply → apply
- POST /invoice/pass → pass
- POST /invoice/reject → reject
- POST /invoice/relate → relate

## 📂 控制器：StatProductController

- POST /statProduct/customer → statCustomerList
- POST /statProduct/month/product → statProductByMonth

## 📂 控制器：OptionsController

- GET /options/customer → getCustomerOption
- GET /options/product → getProductOption

## 📂 控制器：Monitor\*Controller

- POST /monitor/product/getSuccessInfoV2 → getSuccessInfoV2
- POST /monitor/channel/getExceptionList → getExceptionList

## 📂 控制器：Dashboard (ReportDay\StatProductController)

- POST /dashboard/mobile/reportData/list → getReportList
- POST /dashboard/pc/annualData → annualData
