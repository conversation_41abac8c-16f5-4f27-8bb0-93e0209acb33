## 任务调度表

### 路由 AI 任务导入表

CREATE TABLE `cldc_route_ai_import` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`apikey` varchar(50) NOT NULL COMMENT '账号的 apikey',
`task_id` varchar(100) NOT NULL COMMENT '任务 id',
`task_name` varchar(100) NOT NULL COMMENT '任务名称',
`task_create_time` datetime NOT NULL COMMENT '任务创建时间',
`channel_tag` varchar(100) NOT NULL COMMENT '渠道标识',
`import_date` date NOT NULL COMMENT '导入日期',
`total` int(11) NOT NULL DEFAULT '0' COMMENT '导入数量',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`),
UNIQUE KEY `idx_unq_customer_task_channel_import_date` (`apikey`,`task_id`,`channel_tag`,`import_date`),
KEY `idx_task_channel_import_date` (`task_id`,`channel_tag`,`import_date`)
) ENGINE=InnoDB AUTO_INCREMENT=87 DEFAULT CHARSET=utf8 COMMENT='路由 ai 任务导入表'

### 路由 AI 任务回调表

CREATE TABLE `cldc_route_ai_callback` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`apikey` varchar(50) NOT NULL COMMENT '账号的 apikey',
`task_id` varchar(100) NOT NULL COMMENT '任务 id',
`task_name` varchar(100) NOT NULL COMMENT '任务名称',
`task_create_time` datetime NOT NULL COMMENT '任务创建时间',
`channel_tag` varchar(100) NOT NULL COMMENT '渠道标识',
`import_date` date NOT NULL COMMENT '导入日期',
`callback_date` date NOT NULL COMMENT '回调日期',
`call_count` int(11) NOT NULL DEFAULT '0' COMMENT '拨打数量',
`through` int(11) NOT NULL DEFAULT '0' COMMENT '接通数量',
`ab_count` int(11) NOT NULL DEFAULT '0' COMMENT 'AB 意向数',
`duration` int(11) NOT NULL DEFAULT '0' COMMENT '通话总时长',
`user_hung_up_count` int(11) NOT NULL DEFAULT '0' COMMENT '用户挂断数量',
`bill_minutes` int(11) NOT NULL DEFAULT '0' COMMENT '计费分钟数',
`send_msg_count` int(11) NOT NULL DEFAULT '0' COMMENT '短信发送数',
`send_msg_success_count` int(11) NOT NULL DEFAULT '0' COMMENT '发送短信成功数',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`),
UNIQUE KEY `idx_unq_customer_task_channel_import_date_callback_date` (`apikey`,`task_id`,`channel_tag`,`import_date`,`callback_date`),
KEY `idx_task_channel_import_date_callback_date` (`task_id`,`channel_tag`,`import_date`,`callback_date`)
) ENGINE=InnoDB AUTO_INCREMENT=162 DEFAULT CHARSET=utf8 COMMENT='路由 ai 任务回调数据表'

### ER 图

erDiagram
cldc_route_ai_import {
int id PK
string apikey
string task_id
string task_name
datetime task_create_time
string channel_tag
date import_date
int total
datetime create_time
datetime update_time
}

    cldc_route_ai_callback {
        int id PK
        string apikey
        string task_id
        string task_name
        datetime task_create_time
        string channel_tag
        date import_date
        date callback_date
        int call_count
        int through
        int ab_count
        int duration
        int user_hung_up_count
        int bill_minutes
        int send_msg_count
        int send_msg_success_count
        datetime create_time
        datetime update_time
    }

    cldc_route_ai_import ||--o{ cldc_route_ai_callback : "generates"

### 任务调度的流程图

```mermaid
flowchart TD
    A[创建AI任务] --> B[导入任务数据]
    B --> C[记录导入信息]
    C --> D{是否导入成功}
    D -->|是| E[开始执行任务]
    D -->|否| F[任务导入失败]
    F --> B
    E --> G[任务执行]
    G --> H[回调数据处理]
    H --> I[记录回调信息]
    I --> J{是否有更多数据}
    J -->|是| G
    J -->|否| K[任务完成]

    subgraph 回调数据统计
    H --> H1[统计拨打数量]
    H1 --> H2[统计接通数量]
    H2 --> H3[统计意向数量]
    H3 --> H4[统计通话时长]
    H4 --> H5[统计短信发送]
    end
```

### 任务状态转换图

```mermaid
stateDiagram-v2
    [*] --> 任务创建
    任务创建 --> 数据导入: 开始导入
    数据导入 --> 导入失败: 导入异常
    导入失败 --> 数据导入: 重新导入
    数据导入 --> 任务执行: 导入成功
    任务执行 --> 数据回调: 执行完成
    数据回调 --> 回调记录: 处理回调
    回调记录 --> 任务执行: 继续执行
    回调记录 --> 任务完成: 全部完成
    任务完成 --> [*]
```

### 数据统计维度图

flowchart TD
A[任务回调数据] --> B[通话统计]
A --> C[短信统计]
A --> D[时间统计]

    B --> B1[拨打数量]
    B --> B2[接通数量]
    B --> B3[AB意向数]
    B --> B4[用户挂断数]

    C --> C1[发送总数]
    C --> C2[发送成功数]

    D --> D1[通话时长]
    D --> D2[计费分钟]
