本文件记录系统核心业务中的 API 请求处理全流程，适用于理解调用链路、性能优化点、日志埋点策略以及账单计费逻辑。

---

## API 调用流程图

```mermaid
flowchart TD
    A[客户发起 API 请求] --> B[鉴权验证（APIKey / 白名单 / IP）]
    B --> C[产品路由（匹配 product_id）]
    C --> D[渠道策略调度（运营商 / 价格 / 成功率）]
    D --> E[请求上游数据源（三大运营商 / 外部服务商）]
    E --> F[数据解析与结构化处理]
    F --> G[结果返回给客户]
    G --> H[使用量记录（按账号 / 产品）]
    H --> I[账单生成（计费 / 对账）]
    I --> J[MongoDB 写入日志 / Redis 异常缓存]
```

---

## 📌 关键流程说明

### 1. 客户请求（入口）

- 支持 POST/GET 请求
- 通常带有 `apikey`、`product_id` 参数

### 2. 鉴权层

- 校验 apikey / ip 白名单 / 签名（可选）
- 拒绝非法请求，记录失败日志

### 3. 产品路由

- 根据 product_id 选择产品配置
- 读取 fee_config / stat_config / push_url

### 4. 渠道调度策略

- 动态根据运营商 / 成本 / 成功率选择渠道
- 可切换主/备通道

### 5. 上游调用

- 支持同步 HTTP/JSON-RPC 请求
- 异常时可支持重试、报警

### 6. 数据处理

- 解密、映射、字段标准化
- 可缓存部分字段结果

### 7. 结果返回

- JSON 数据结构
- 响应码规范：200、401、500、9999 等

### 8. 使用量记录

- 按 apikey + product 维度计入 Redis
- 支持单日、单月限流与统计

### 9. 账单生成

- 依据 fee_config 匹配价格规则
- 定期同步至账单明细表

### 10. 日志与告警

- MongoDB 存储响应日志（按月分表）
- Redis 存储异常请求或失败上报
