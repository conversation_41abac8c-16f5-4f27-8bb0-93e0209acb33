📝 25_Mongo 日志存储流程图

该文档描述系统在 API 调用与账单处理过程中，如何使用 MongoDB 实现日志落库、按月分表与结构化存储。

⸻

✅ MongoDB 日志存储流程图（Mermaid 源码）

flowchart TD
A[调用处理完成] --> B[生成日志内容：调用参数 / 返回值 / 错误码 / 时间]
B --> C[确定日志模型（按业务类型）]
C --> D[写入目标 Mongo 集合（logs_XXX_YYYYMM）]
D --> E[MongoDB 后台定期归档]
E --> F[日志分析系统读取]

⸻

📌 关键说明

1. 日志触发位置
   • Controller 中请求结束时
   • Job 异步处理完成后
   • 异常 Catch 块中

2. 日志模型定义
   • 继承 Jenssegers\Mongodb\Eloquent\Model
   • 动态设定 $collection = logs_xxx_YYYYMM
   • 自动按月创建集合，如：logs_bill_customer_income_202406

3. 数据字段结构
   • apikey / customer_id / product_id
   • 请求内容 / 响应内容 / code
   • latency / ip / 时间戳 / source

4. 后台存储策略
   • 默认写入 MongoDB 副本集 backend 库
   • 可设置 TTL 索引进行日志过期清理

5. 分析与追踪
   • 用于账单溯源、产品异常定位、客户投诉分析
   • 可接入 Graylog / ELK / ClickHouse 等进行统一检索
