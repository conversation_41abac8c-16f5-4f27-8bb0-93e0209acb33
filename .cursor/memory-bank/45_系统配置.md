## 基础配置表

### 账单运营商配置表 DDL

CREATE TABLE `config_operator_bill` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`en_name` varchar(20) NOT NULL DEFAULT '' COMMENT '英文名称',
`cn_name` varchar(50) NOT NULL DEFAULT '' COMMENT '中文名称',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='账单运营商配置表'

### 统计运营商配置表

CREATE TABLE `config_operator_statistics` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`en_name` varchar(20) NOT NULL DEFAULT '' COMMENT '英文名称',
`cn_name` varchar(50) NOT NULL DEFAULT '' COMMENT '中文名称',
`bill_operator` varchar(20) NOT NULL DEFAULT '' COMMENT '所属账单运营商',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='统计运营商配置表'

### 客户价格配置表

CREATE TABLE `config_price_customer` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`father_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父产品 ID',
`product_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '子产品 IDS（多个产品 ID 以,进行分割）',
`accord` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '计费依据（1-成功调用量，2-查得量）',
`methods` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '计费方式（0-不计费 1-包年 2-固定价格 3-累进阶梯 4-到达阶梯）',
`period` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '计费周期（0-无意义 1-日 2-月 3-年 4-无周期）',
`diff_operator` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否区分运营商（0-不区分 1-区分）',
`mode` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '多级产品计费模式（0-无计费模式 1-独立子产品 2-打包 3-汇总子产品）',
`price` text COMMENT '单价',
`start_date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费开始时间（Ymd）',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
`delete_time` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
`admin` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `father_id` (`father_id`) USING BTREE,
KEY `start_date` (`start_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1491 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户单价配置表'

### 接口价格配置表

CREATE TABLE `config_price_interface` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`father_id` int(11) NOT NULL DEFAULT '0' COMMENT '所属父级产品 id，方便查找',
`price` varchar(1000) NOT NULL DEFAULT '',
`start_date` int(11) NOT NULL DEFAULT '0' COMMENT '日期',
`delete_time` datetime DEFAULT NULL COMMENT '删除时间',
`create_time` datetime DEFAULT NULL,
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1631 DEFAULT CHARSET=utf8

## 业务配置表

### 邦信分策略配置表

CREATE TABLE `bxf_strategy_config` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`account_id` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户 id',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`strategy_id` varchar(512) NOT NULL DEFAULT '' COMMENT '策略,多个使用逗号分割',
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '策略状态 1 可用,2 禁用',
`remark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注说明',
`admin` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='邦信分-策略配置表'

### 通用信息记录表

CREATE TABLE `common_info` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`content` mediumtext COMMENT '信息内容',
`remark` text COMMENT '备注信息',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='通用信息记录表'

### 数据库迁移记录表

CREATE TABLE `migrations` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
`batch` int(11) NOT NULL,
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

### ER 关系图

```mermaid
erDiagram
    config_operator_bill ||--o{ config_operator_statistics : "maps_to"
    config_price_customer ||--o{ config_price_interface : "references"

    config_operator_bill {
        int id PK "自增主键"
        string en_name "英文名称"
        string cn_name "中文名称"
    }

    config_operator_statistics {
        int id PK "自增主键"
        string en_name "英文名称"
        string cn_name "中文名称"
        string bill_operator FK "所属账单运营商"
    }

    config_price_customer {
        int id PK "自增主键"
        string apikey "账号标识"
        int father_id "父产品ID"
        string product_ids "子产品IDS"
        int accord "计费依据"
        int methods "计费方式"
        int period "计费周期"
        int diff_operator "区分运营商"
        int mode "计费模式"
        text price "单价配置"
        int start_date "开始时间"
        string admin "操作人"
        int source "来源"
    }

    config_price_interface {
        int id PK "自增主键"
        int interface_id "接口ID"
        int father_id "父产品ID"
        string price "价格配置"
        int start_date "开始时间"
        int source "来源"
    }

    bxf_strategy_config {
        int id PK "自增主键"
        string account_id "账户ID"
        int product_id "产品ID"
        string strategy_id "策略ID"
        int status "状态"
        string remark "备注"
        string admin "操作人"
    }

    common_info {
        int id PK "自增主键"
        text content "信息内容"
        text remark "备注信息"
    }

    migrations {
        int id PK "自增主键"
        string migration "迁移名称"
        int batch "批次号"
    }
```

1. config_operator_bill → config_operator_statistics

关系说明：maps_to（映射）
• 含义：每个账单运营商在统计维度下有多个映射项，用于数据统计与展示层命名规范分离。
• 字段映射：
• config_operator_statistics.bill_operator 外键 → config_operator_bill.en_name
• 实际应用：
• 支持将一个账单运营商（如 "CMCC"）映射为多个统计指标维度（如 "CMCC_Billing", "CMCC_Usage"）

⸻

2. config_price_customer → config_price_interface

关系说明：references（引用）
• 含义：每个客户的计费配置引用了多个接口级别的价格配置，用于更细粒度的计费定价。
• 字段逻辑：
• 虽未直接通过外键显式关联，但二者通过：
• father_id（父产品 ID）
• interface_id（接口 ID）
• 实际业务中按客户 + 产品映射接口价格。
• 实际应用：
• 支持某客户、某产品、某接口的单独定价策略（差异化合同价）

### 配置表的分类关系图

```mermaid
graph TD
    A[系统配置] --> B[基础配置]
    A --> C[业务配置]
    A --> D[数据配置]

    B --> B1[运营商配置]
    B --> B2[价格配置]
    B --> B3[系统参数配置]

    B1 --> BA1[账单运营商配置表]
    B1 --> BA2[统计运营商配置表]

    B2 --> BB1[客户价格配置表]
    B2 --> BB2[接口价格配置表]

    C --> C1[邦信分策略配置表]
    C --> C2[通用信息记录表]

    D --> D1[数据库迁移记录表]

    subgraph 配置管理
    E[配置类型] --> E1[静态配置]
    E --> E2[动态配置]
    E1 --> E11[系统级配置]
    E1 --> E12[业务级配置]
    E2 --> E21[运行时配置]
    E2 --> E22[用户级配置]
    end
```

### 配置表的数据流转图

```mermaid
graph LR
    A[业务系统] --> B[配置管理层]
    B --> C[配置存储层]

    subgraph 配置管理层
    B1[配置读取] --> B2[配置验证]
    B2 --> B3[配置转换]
    B3 --> B4[配置缓存]
    end

    subgraph 配置存储层
    C1[运营商配置] --> D1[账单系统]
    C1 --> D2[统计系统]

    C2[价格配置] --> D3[计费系统]
    C2 --> D4[结算系统]

    C3[策略配置] --> D5[风控系统]
    C3 --> D6[业务系统]
    end

    D1 --> E[数据统计]
    D2 --> E
    D3 --> F[财务结算]
    D4 --> F
    D5 --> G[业务处理]
    D6 --> G
```
