

该文档描述系统中产品接口调用前，如何根据运营商、成本、成功率等维度动态选择最优渠道。

---

## ✅ 渠道调度流程图（Mermaid 源码）

```mermaid
flowchart TD
    A[接收到调用请求] --> B[解析产品 ID / 用户 ID]
    B --> C[获取可用渠道列表（按 product_id）]
    C --> D[筛选支持运营商的渠道]
    D --> E[读取渠道状态 & 成本 & 成功率]
    E --> F{渠道优先级算法}
    F --> G1[选择主渠道]
    F --> G2[准备备用渠道]
    G1 --> H[执行上游请求]
    H --> I{是否成功？}
    I -- 是 --> J[记录成功 & 统计成功率]
    I -- 否 --> G2[切换备用渠道重试]
    G2 --> H
```

---

##  关键调度逻辑说明

### 输入条件：

* `product_id`
* `operator`（移动/联通/电信）
* `account_id`、可选的调用上下文

### 筛选条件：

* 渠道是否可用：状态 = 开启
* 渠道是否支持该运营商（字段匹配）
* 成功率 ≥ 阈值（如 90%）
* 成本 ≤ 控制价格（防止亏损）

### 优先级策略（支持加权）

* 默认优先级字段（如 sort）
* 成本最优优先 / 成功率优先 / 路由动态权重
* 灰度策略：部分账号走新渠道

### 切换机制

* 主渠道失败立即尝试备用渠道
* 重试次数可配置
* 所有渠道失败可 fallback 给默认响应

### 数据依赖

* Redis 缓存渠道权重/状态/健康指标
* 实时更新成功率指标（分钟级）
* 渠道切换行为写入日志用于复盘
