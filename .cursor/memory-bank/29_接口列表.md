## 📋 **系统接口列表详细梳理**

### **1. 基础服务接口 (Basic Services)**

#### **🔧 系统基础接口**

```yaml
微信预警: GET /wechat - 微信预警通知

测试接口: GET /test/test - 系统测试
  POST /test/testChannel - 渠道测试
  GET /test/channelTestRes - 渠道测试结果

认证接口: GET /passport - 认证测试
  GET /auth/fresh - 清理权限缓存

文件上传: GET /upload/index/{key} - 上传页面
  POST /upload/callback/{key} - 上传回调

工具接口: POST /tool/sendMail - 发送邮件
```

### **2. 账单管理接口 (Bill Management)**

#### **💰 账单核心接口**

```yaml
v1版本账单接口 (/v1/bill): GET /logs/{uuid} - 账单日志
  GET /logs/warning/{w-uuid} - 预警日志
  GET /history/customer/{customer_id} - 客户结算单
  GET /excel/customer/{customer_id} - 消费明细Excel
  POST /email - 发送邮件
  GET /customers - 客户对账单列表
  POST /sorts - 账单排序
  GET / - 账单查询
  POST /history/excel - 账单列表下载
  GET /customer/product/{customer_id} - 客户产品对账单
  GET /product - 产品对账单
  GET /day - 每天消费情况
  GET /list - 账单月份列表

v2版本账单接口 (/v2/bill): GET /history/customer/{customer_id} - 客户结算单V2
  POST /customer_profit - 客户权责利润报表
  POST /product_profit - 产品权责利润报表

v3版本账单接口 (/bill): POST /operator/check - 运营商账单检查
  POST /operator/BillOperatorList - 运营商账单列表
  POST /operator/ConfirmOperatorBill - 确认运营商账单
  POST /income/recreate - 营收账单重新生成
  POST /income/customerStatementList - 客户对账单列表
  POST /income/customerStatementDetails - 客户对账单明细
  POST /income/sendStatement - 发送对账单邮件
  POST /rerunBill - 重跑账单
  POST /customerBillAdjust/save - 客户账单调整
  POST /customerExpend/list - 客户特殊消耗列表
```

### **3. 统计报表接口 (Statistics & Reports)**

#### **📊 数据统计接口**

```yaml
基础统计 (/statistic): POST /list - 列表统计
  POST /detail - 详情统计
  POST /day - 每天统计
  POST /report - 日报

产品统计 (/statProduct): POST /customer - 客户统计
  POST /product - 产品统计
  POST /channel - 渠道统计
  POST /main - 主要统计
  POST /compare - 对比分析
  POST /month/customer - 客户月度统计
  POST /month/product - 产品月度统计
  POST /quarter/product - 产品季度统计
  POST /customer/chart - 客户趋势图
  POST /product/chart - 产品趋势图

产品视角统计 (/statPM): GET /sub_product_list - 子产品列表
  POST /main - 主要统计
  POST /customer - 客户统计
  POST /product - 产品统计
  POST /channel - 渠道统计

上游统计 (/v1/upstream): POST /shortcuts - 邦信分快捷版统计入库
  POST /verification - 邦秒验统计入库
  POST /enterprise_query - 邦企查统计入库
  GET /shortcuts - 邦信分快捷版统计列表
  GET /verification - 邦秒验统计列表
```

### **4. 渠道管理接口 (Channel Management)**

#### **🔗 渠道配置接口**

```yaml
渠道管理 (/channel): GET /getChannelOptions - 渠道下拉列表
  GET /getInterfaceOptions - 接口下拉列表
  GET /getChannelList - 渠道列表
  GET /getChannelProductList - 渠道产品列表
  GET /getProductChannelList - 产品渠道列表
  POST /addChannel - 添加渠道
  POST /saveChannel - 保存渠道
  GET /getInterfaceList - 接口列表
  POST /addInterface - 添加接口
  POST /saveInterface - 保存接口
  GET /getPriceList - 计费配置列表
  POST /deletePrice - 删除计费配置
  GET /log - 渠道日志

渠道补数配置: POST /getComplementList - 补数配置列表
  POST /saveChannelComplement - 保存补数配置
  GET /setChannelComplement - 设置补数开关

渠道分流配置: POST /getShuntList - 分流配置列表
  POST /saveChannelShunt - 保存分流配置
  GET /setChannelShunt - 设置分流开关

渠道资质管理: GET /getQualicationList - 资质列表
  POST /addQualication - 添加资质
  POST /editQualication - 编辑资质
  GET /downQualicationDetailFile - 下载资质文件
```

### **5. 监控告警接口 (Monitoring & Alerting)**

#### **🚨 监控系统接口**

```yaml
产品监控 (/monitor/product): POST /getSuccessInfoV2 - 产品成功率监控
  POST /getStatisInfo - 产品统计信息
  POST /valueSpreadV2 - 值分布监控
  POST /getSuccessInfoHistory - 历史监控记录

渠道监控 (/monitor/channel): POST /getSuccessInfoV2 - 渠道成功率监控
  POST /getSuccessInfoHistory - 渠道历史监控
  POST /getExceptionList - 渠道异常列表
  POST /saveChannelException - 保存异常记录

蚂蚁监控 (/monitor/ant_financial): GET /detail - 蚂蚁监控详情
  POST /switchNetWork/list - 携号转网批次列表
  GET /switchNetWork/set - 设置携号转网

告警管理 (/monitor/manage): POST /alarmLogList - 告警日志列表
  POST /batchDeal - 批量处理告警
  POST /deal - 处理告警

邦信分快捷版监控 (/monitor/bxfshort): POST /score - 评分信息监控
  POST /statis - 统计信息监控
  POST /valueSpread - 值分布监控
  POST /successRatio - 成功率监控
  POST /channelStatis - 渠道统计监控
```

### **6. 客户管理接口 (Customer Management)**

#### **👥 客户信息接口**

```yaml
客户基础信息 (/customer):
  开票信息 (/invoice): GET /list - 开票信息列表
    POST /add - 添加开票信息

  地址信息 (/address): GET /list - 地址列表
    POST /add - 添加地址

  客户分组 (/group): POST /list - 客户分组列表
    POST /group_map - 分组映射
    POST /add - 添加分组
    POST /edit - 编辑分组
    GET /group_customer_list - 分组客户列表

客户新老分析 (/customer_new_old): POST /options - 获取选项
  POST /list - 新老客户列表
```

### **7. 发票管理接口 (Invoice Management)**

#### **🧾 发票系统接口**

```yaml
发票管理 (/invoice): POST /options - 发票选项
  POST /customer_list - 客户列表
  POST /informations - 开票信息
  POST /invoice_list - 发票列表
  POST /invoice_info - 发票详情
  POST /unapplied_list - 未申请开票列表
  POST /receipt_list - 收款单列表
  POST /contract_list - 合同列表

发票状态操作: POST /apply - 申请开票
  POST /reject - 驳回
  POST /cancel - 撤销
  POST /pass - 审核通过
  POST /batch_pass - 批量审核通过
  POST /issue_by_excel - Excel批量开票
  POST /flush - 红冲
  POST /relate - 回款认票

发票导入导出: POST /import/issue - 导入开票数据
  POST /import/done - 导入回款数据
  GET /export/export - 导出发票列表
```

### **8. 配置管理接口 (Configuration Management)**

#### **⚙️ 系统配置接口**

```yaml
产品配置 (/config): GET /getProductInfo - 产品配置列表
  POST /createProduct - 创建产品

选项配置 (/options): GET /product - 产品选项
  GET /customer - 客户选项
  GET /account - 账号选项
  GET /operator - 运营商选项
  GET /channel - 渠道选项
  GET /interface - 接口选项
  POST /setAccountProductCustom - 设置账号产品自定义
  POST /historyCostAdjust - 历史成本调整
  POST /batchAddCustomerExpend - 批量添加客户消耗
  POST /batchAddCustomerCost - 批量添加客户成本

值分布配置 (/config/product/spread): POST /spreadList - 值分布列表
  POST /spreadAdd - 添加值分布配置
  POST /spreadEdit - 编辑值分布配置
  GET /getOptions - 获取选项

渠道查得率配置 (/config/channel/statis): POST /statisList - 查得率列表
  POST /statisAdd - 添加查得率配置
  POST /statisEdit - 编辑查得率配置

客户单价配置 (/customer/price): GET /info - 客户单价信息
```

### **9. 数据分析接口 (Data Analytics)**

#### **📈 分析统计接口**

```yaml
数据校正 (/v1/correct): POST / - 生成校正日志
  GET / - 校正日期列表
  GET /operators - 操作用户列表
  POST /excel - 校正列表下载
  POST /comment - 增加备注
  GET /comment/{uuid} - 备注列表

日志查询 (/v1/log): GET / - 日志查询
  GET /statistic - 统计信息查询
  GET /progress - 统计字段入库日志

数据核验 (/checkdata): POST /getList - 核验列表
  POST /getLastCall - 最后调用记录
  POST /getChannelList - 渠道列表

存量洞察 (/cldc): POST /stat/list - 存量统计列表
  GET /statAi/getOptions - AI统计选项
  POST /statAi/getAiList - AI外呼数据
```

### **10. 移动端接口 (Mobile APIs)**

#### **📱 移动应用接口**

```yaml
飞书集成 (/mobile): POST /getFeiShuUserToken - 获取飞书用户Token
  POST /getFeiShuUserInfo - 获取飞书用户信息

移动驾驶舱 (/dashboard/mobile): POST /reportData/list - 报表数据列表

PC端驾驶舱 (/dashboard/pc): POST /annualData - 年度数据
  POST /productData - 产品数据
  POST /productCategoryIncomeData - 产品分类收入
  POST /customerTopIncomeData - TOP5客户收入
  POST /twoWeekIncomeData - 近两周收入
  POST /monthIncomeData - 月度收入
  POST /deptStatData - 区域任务数据
  POST /monthlyChannelData - 渠道月度数据
  POST /weeklyChannelData - 渠道周度数据
```

### **11. 第三方集成接口 (Third-party Integration)**

#### **🔌 外部系统接口**

```yaml
邦秒验产品 (/bmy): GET /getCustomerPrice - 客户单价
  GET /getUpstreamPrice - 上游单价
  GET /getUpstreamStatus - 上游状态
  POST /getProductChannel - 产品渠道
  POST /netTimeRecheck/list - 在网时长复核列表
  POST /netTimeRecheck/save - 保存复核数据
  POST /netTimeRecheck/recheck - 执行复核

邦信分快捷版 (/cuishouExpress): POST /checkChannel - 检查渠道

邦信分策略配置 (/bxf_strategy): POST /list - 策略列表
  POST /add - 添加策略
  POST /edit - 编辑策略
  POST /del - 删除策略
  POST /config/list - 策略配置列表
  POST /config/add - 添加策略配置

数据推送接口: POST /statistics/customer/sendUsage - 客户调用量统计
  POST /statistics/interface/sendUsage - 接口调用量统计
  POST /v1/bill_info/service_line - 流水推送
```

### **12. 业务流程接口 (Business Process)**

#### **🔄 业务管理接口**

```yaml
合同管理 (/contract): POST /options - 合同选项
  POST /add - 添加合同
  POST /list - 合同列表
  POST /edit - 编辑合同
  POST /del - 删除合同
  POST /company_list - 公司列表
  POST /salesman_list - 商务列表
  POST /apply_download - 申请下载权限

收款单管理 (/receipt): POST /getSplitCustomer - 获取拆分客户
  POST /split - 拆分收款单
  POST /splitPrice - 拆分价格
  POST /getSplitPrice - 获取拆分价格

认款拆单 (/remit): POST /getSplitPrice - 获取拆分价格列表
  GET /download - 下载拆分价格

账期推送 (/period): POST /getPeriodCompareResult - 账期对比结果
  POST /getPeriodData - 账期数据
  POST /doPeriod - 执行账期推送
  POST /hmfPeriod - HMF账期
  POST /getShieldPeriod - 获取屏蔽账期

审批管理 (/system/approval): POST /list - 审批列表
  POST /deal - 处理审批
  POST /config - 审批配置

售前测试管理 (/pre_test_manage): POST /option_list - 选项列表
  POST /my_list - 我的申请列表
  POST /list - 测试申请列表
  POST /save_feedback - 保存反馈
  POST /save_access - 保存接入信息
  POST /statistics_overview - 统计总览
  POST /risk_list - 风险统计列表
  POST /monitor_customer_list - 客户监控列表
```

这套接口系统涵盖了财务管理的**全业务流程**，包括账单管理、统计分析、渠道管理、客户服务、发票管理等核心功能，支持**多端访问**（PC 端、移动端）和**第三方集成**，具备完整的**监控告警**和**数据分析**能力。
