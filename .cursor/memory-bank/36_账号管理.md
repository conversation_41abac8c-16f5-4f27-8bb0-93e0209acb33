## 账号基础表

account -- 账号基础信息表
account_product -- 账号产品关联表
account_product_custom -- 账号产品自定义配置表
account_product_limit -- 账号产品限量测试表
account_push -- 账号推送记录表
account_transfer_config -- 账号转移配置表

### 账号基础信息表 DDL

CREATE TABLE `account` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`account_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '账户 id',
`cid` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '根据 account_id 生成(先 md5，在截取前 8 位)',
`ctag` char(32) DEFAULT '' COMMENT '根据 account_id + "ctag" 生成(先 md5，再截取前 8 位)',
`user_agent_number` tinyint(2) DEFAULT '0' COMMENT '代调用户编号, 默认为 0 没有编号则不可代调，1 朴道,2 百行',
`account_name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账号名称',
`customer_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '所属客户 id',
`group_id` char(32) CHARACTER SET utf8 NOT NULL COMMENT '主体 id',
`father_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '父级为 0，子级别指向父级 account_id',
`email` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '登录邮箱',
`password` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '登录密码',
`status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '账号状态,1 可用，0 不可用',
`type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '账号类型：1 正式，0 测试',
`apikey` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'apikey',
`appsecret` char(200) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT 'appsecret',
`end_time` bigint(11) NOT NULL COMMENT '账号截止时间',
`concurrency` int(10) NOT NULL DEFAULT '-1' COMMENT '账号秒并发',
`access_ip` text CHARACTER SET utf8 COMMENT 'ip 白名单',
`mark` text CHARACTER SET utf8 COMMENT '备注',
`admin` varchar(30) CHARACTER SET utf8 DEFAULT '' COMMENT '操作人',
`appsecret_bak` char(100) NOT NULL DEFAULT '',
`close_zhilian` tinyint(2) NOT NULL DEFAULT '0' COMMENT '直连关闭 0 不关闭，1 关闭',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`is_delete` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
`cache_days` int(10) NOT NULL DEFAULT '1' COMMENT '缓存时长 天',
PRIMARY KEY (`id`),
UNIQUE KEY `account_name` (`account_name`) USING BTREE,
KEY `account_id` (`account_id`) USING BTREE,
KEY `account_father_id` (`father_id`) USING BTREE,
KEY `account_type` (`type`) USING BTREE,
KEY `account_end_time` (`end_time`) USING BTREE,
KEY `account_email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3753 DEFAULT CHARSET=utf8mb4 COMMENT='账号表'

### 账号产品关联表

CREATE TABLE `account_product` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`account_id` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户 id',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '账号-产品状态：1 可用，2 禁用',
`use_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '产品使用类型:1 外部调用,2 内部调用',
`contract_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '签约状态:1 已签约已付款,2 已签约未付费,3 未签约,4 其他,5 特殊客户',
`end_time` int(11) DEFAULT NULL,
`daily_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '日限额用量',
`month_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '月限额用量',
`year_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '年限额量',
`total_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '总限额量',
`concurrency` int(10) NOT NULL DEFAULT '1' COMMENT '秒并发',
`data` text CHARACTER SET utf8 COMMENT '产品配置参数',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`limit_start_date` date DEFAULT NULL COMMENT '总限量预警开始时间',
`source_tags` varchar(6) DEFAULT '' COMMENT '切换渠道标记,仅标记子产品 填写已换签的渠道 id 例如 ,1,2,99,100,',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE KEY `ca_acc_pro_unique_id` (`account_id`,`product_id`) USING BTREE,
KEY `ca_account_id` (`account_id`) USING BTREE,
KEY `ca_product_id` (`product_id`) USING BTREE,
KEY `ca_status` (`status`) USING BTREE,
KEY `ca_cntract_status` (`contract_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16659 DEFAULT CHARSET=utf8mb4 COMMENT='账号-产品-关联表'

### 账号产品自定义配置表 DDL

CREATE TABLE `account_product_custom` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`father_id` int(11) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`product_id` int(11) NOT NULL DEFAULT '0' COMMENT '子产品 id',
`apikey` char(100) NOT NULL DEFAULT '' COMMENT '账户 apikey',
`extend` char(50) NOT NULL DEFAULT '' COMMENT '配置的扩展字段，比如版本等',
`type` tinyint(2) NOT NULL DEFAULT '30' COMMENT '业务类型',
`data` text COMMENT '配置参数',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`delete_at` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
PRIMARY KEY (`id`),
UNIQUE KEY `config_unique_id` (`apikey`,`father_id`,`product_id`,`extend`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=2264 DEFAULT CHARSET=utf8 COMMENT='账号-产品-配置表(定制版，是 account_product 表的补充)'

### 账号产品限量测试表 DDL

CREATE TABLE `account_product_limit` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`apikey` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户 id',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
`end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
`total_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '总限额量',
`remark` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
`create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE KEY `acc_pro_unique_id` (`apikey`,`product_id`) USING BTREE,
KEY `account_id` (`apikey`) USING BTREE,
KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='账号-产品-限量测试表'

### 账号推送记录表

CREATE TABLE `account_push` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`apikey` varchar(40) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'apikey',
`product_id` int(5) NOT NULL COMMENT '产品编号',
`status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '推送状态（0：未推送，1：推送失败，2：推送成功）',
`is_error` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否预警（0：未报警，1：已报警）',
`input` text CHARACTER SET utf8 NOT NULL COMMENT '推送数据',
`output` text CHARACTER SET utf8 COMMENT '返回数据',
`push_number` tinyint(2) NOT NULL DEFAULT '0' COMMENT '推送次数',
`create_time` int(11) NOT NULL COMMENT '创建时间',
`update_time` int(11) NOT NULL DEFAULT '0' COMMENT '修改时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5822 DEFAULT CHARSET=utf8mb4

### 账号转移配置表

CREATE TABLE `account_transfer_config` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 id',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`from_apikey` char(32) NOT NULL DEFAULT '' COMMENT '主账号 apikey(被转换的 apikey)',
`to_apikey` char(32) NOT NULL DEFAULT '' COMMENT '副账号 apikey(要转换到的 apikey)',
`type` tinyint(4) NOT NULL DEFAULT '10' COMMENT '业务类型,可设置为 10、20、30 有规律的值',
`is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除:0 未删除、1 已删除',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
`create_at` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`),
KEY `from_apikey_index` (`from_apikey`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='账号转移配置表;比如一个账号的量要转移到另一个账号上类似配置'

### ER 图

```mermaid
erDiagram
    account ||--o{ account_product : "has"
    account ||--o{ account_product_custom : "has"
    account ||--o{ account_product_limit : "has"
    account ||--o{ account_push : "has"
    account ||--o{ account_transfer_config : "has"

    account {
        int id PK
        char account_id
        char cid
        char ctag
        varchar account_name
        char customer_id
        char group_id
        char father_id
        varchar email
        tinyint status
        char apikey
        char appsecret
    }

    account_product {
        int id PK
        char account_id FK
        int product_id
        tinyint status
        tinyint use_type
        tinyint contract_status
        int daily_limit
        int month_limit
    }

    account_product_custom {
        int id PK
        int father_id
        int product_id
        char apikey FK
        char extend
        tinyint type
        text data
    }

    account_product_limit {
        int id PK
        char apikey FK
        int product_id
        int start_time
        int end_time
        int total_limit
    }

    account_push {
        int id PK
        varchar apikey FK
        int product_id
        tinyint status
        text input
        text output
    }

    account_transfer_config {
        int id PK
        char customer_id
        int father_id
        char from_apikey FK
        char to_apikey
        tinyint type
    }
```

1. account → account_product

关系类型：一对多（account_id 为外键）
含义：账号被授权访问哪些产品，以及该授权的使用规则。
字段说明：
• use_type：使用场景（测试、生产）
• contract_status：合同状态（是否签约）
• daily_limit、month_limit：限额设置（默认值）

⸻

2. account → account_product_custom

关系类型：一对多（通过 apikey）
含义：针对某产品的 自定义配置信息（覆盖默认配置）。
字段说明：
• father_id：父账号（绑定层级）
• type：配置类型（例如字段加白、特殊路由等）
• data：JSON 结构，表示定制参数内容

⸻

3. account → account_product_limit

关系类型：一对多（apikey 为外键）
含义：设置 某产品的限时限量使用策略（有效期+额度控制）
字段说明：
• start_time, end_time：时间窗口
• total_limit：期间调用上限（例如活动期 500 次）

⸻

4. account → account_push

关系类型：一对多（apikey 为外键）
含义：配置产品接口 推送回调机制，如异步回传数据或通知
字段说明：
• input：请求参数模板
• output：响应数据格式模板
• status：推送启用与否

⸻

5. account → account_transfer_config

关系类型：一对多（from_apikey 为外键）
含义：配置 账号之间的调用透传关系（主账号调用替下属账号发起）
字段说明：
• from_apikey：转发方
• to_apikey：被透传方
• type：转发类型（默认/特殊）

### 创建账号管理的业务流程图

```mermaid
graph TD
    A["账号创建"] --> B["基础信息配置"]
    B --> C["产品授权"]
    C --> D{"是否需要自定义配置"}
    D -->|是| E["配置产品自定义参数"]
    D -->|否| F["设置使用限制"]
    E --> F
    F --> G{"是否需要账号转移"}
    G -->|是| H["配置账号转移规则"]
    G -->|否| I["账号启用"]
    H --> I
    I --> J["使用监控"]
    J --> K["数据推送"]
```

### 创建账号状态流转图

```mermaid
stateDiagram-v2
    [*] --> 创建
    创建 --> 测试账号: type=0
    创建 --> 正式账号: type=1
    测试账号 --> 禁用: status=0
    正式账号 --> 禁用: status=0
    测试账号 --> 正式账号: 升级
    禁用 --> 测试账号: 重新启用
    禁用 --> 正式账号: 重新启用
    测试账号 --> [*]: 删除
    正式账号 --> [*]: 删除
    禁用 --> [*]: 删除
```

### 创建账号产品关系图

```mermaid
graph LR
    A[账号] --> B[产品授权]
    B --> C[基础产品配置]
    B --> D[自定义产品配置]

    C --> E[使用限制]
    C --> F[并发控制]
    C --> G[时效控制]

    D --> H[特殊参数]
    D --> I[扩展配置]
    D --> J[版本控制]

    E --> K[日限额]
    E --> L[月限额]
    E --> M[年限额]
    E --> N[总限额]
```
