## 发票管理

-- 发票基础表
invoice_info -- 发票信息表
customer_invoice_log -- 客户发票日志表

-- 发票关联表
invoice_consume -- 发票消费关联表
invoice_remit -- 发票收款关联表
rel_invoice_consume -- 发票消费关系表
rel_invoice_remit -- 发票收款关系表

### 发票基础表

##### 发票信息表 DDL

CREATE TABLE `invoice_info` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`invoice_id` char(32) NOT NULL COMMENT '发票流水号',
`invoice_code` varchar(12) NOT NULL COMMENT '发票代码',
`invoice_no` varchar(32) NOT NULL COMMENT '发票号码',
`money` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '金额 这张发票的金额',
`date` datetime DEFAULT NULL COMMENT '开票日期',
`product_name` varchar(32) NOT NULL COMMENT '商品名称 发票内容信息',
`number` int(10) NOT NULL COMMENT '数量',
`price` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
`tax_rate` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '税率',
`tax` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '税额',
`total_money` decimal(15,2) DEFAULT NULL COMMENT '价税合计',
`invoice_status` tinyint(1) NOT NULL COMMENT '发票状态 1:正常发票 2:负数发票 3:填开作废',
`invoice_type` tinyint(1) NOT NULL COMMENT '发票类型 1:专票 2:普票',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2661 DEFAULT CHARSET=utf8 COMMENT='发票流水号和发票代码 发票号码关系'

#### 客户发票日志表 DDL

CREATE TABLE `customer_invoice_log` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`invoice_id` char(32) NOT NULL COMMENT '发票流水号',
`status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态 0:初始化 10:已申请:完成申请回款申请,等待财务开具发票 15:已驳回:被财务驳回 16:已撤销:商务自行撤销 20:已审核:商务审核开票信息 30:已开票:财务已经开具开票 50:已邮寄:发票已经邮寄 70:已退票:发票被客户退回,须调整开票申请 90:部分回款:客户打款为能全部分配至产品 100:已回款:客户打款完成',
`admin` varchar(30) NOT NULL DEFAULT '' COMMENT '操作人',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2506 DEFAULT CHARSET=utf8mb4 COMMENT='发票记录表'

### 发票关联表

#### 发票消费关联表 DDL

CREATE TABLE `invoice_consume` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`month` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '月份 Ym',
`father_id` int(10) DEFAULT NULL COMMENT '主产品 id',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`consume` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '消耗',
`customer_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '客户余额',
`invoice_id` varchar(512) DEFAULT NULL COMMENT '发票流水号',
`invoice_date` datetime DEFAULT NULL COMMENT '开票时间',
`receipt_serial` varchar(512) DEFAULT '' COMMENT '流水单号',
`remit_date` datetime DEFAULT NULL COMMENT '回款时间',
`remit_money` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '回款金额',
`salesman` varchar(100) NOT NULL COMMENT '销售跟进人',
`payment_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '付款类型(1-预付款客户 2-后付款客户)',
`source` tinyint(1) NOT NULL DEFAULT '0' COMMENT '来源 -1 全部 0 电话邦 1 朴道',
`status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态 0:初始化 30:已开票:财务已经开具开票 90:部分回款:客户打款为能全部分配至产品 100:已回款',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`),
KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4928 DEFAULT CHARSET=utf8 COMMENT='发票与消耗关系表'

#### 发票收款关联表 DDL

CREATE TABLE `invoice_remit` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`invoice_id` char(32) NOT NULL COMMENT '发票流水号',
`receipt_serial` char(50) NOT NULL COMMENT '收款流水单号',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1319 DEFAULT CHARSET=utf8 COMMENT='发票与收款单关联表'

#### 发票消费关系表 DDL

CREATE TABLE `rel_invoice_consume` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源(0:电话邦,1:朴道,10:浙数交)',
`invoice_id` char(32) NOT NULL DEFAULT '' COMMENT '发票流水号',
`invoice_money` varchar(255) NOT NULL DEFAULT '' COMMENT '开票金额',
`consume_id` int(10) NOT NULL DEFAULT '0' COMMENT 'customer_consume 表主键',
`consume_month` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消耗月份 Ym',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`consume_money` varchar(255) NOT NULL DEFAULT '' COMMENT '消耗金额',
`rel_money` varchar(255) NOT NULL DEFAULT '' COMMENT '关联金额',
`invoice_model` tinyint(3) NOT NULL DEFAULT '0' COMMENT '开票模式[1:按消耗(主产品),2:按消耗(主产品,可拆金额),3:按消耗(子产品),4:按消耗(子产品,可拆金额),5:按到款,6:按到款(先票后款后消耗)]',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`),
KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5622 DEFAULT CHARSET=utf8 COMMENT='发票消耗关系表'

#### 发票收款关系表 DDL

CREATE TABLE `rel_invoice_remit` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源(0:电话邦,1:朴道,10:浙数交)',
`invoice_id` char(32) NOT NULL DEFAULT '' COMMENT '发票流水号',
`invoice_money` varchar(255) NOT NULL DEFAULT '' COMMENT '开票金额',
`remit_serial` char(50) NOT NULL DEFAULT '' COMMENT '到款流水单号',
`receipt_serial` char(50) NOT NULL DEFAULT '' COMMENT '收款流水单号',
`remit_money` varchar(255) NOT NULL DEFAULT '' COMMENT '到款金额',
`invoice_model` tinyint(3) NOT NULL DEFAULT '0' COMMENT '开票模式[1:按消耗(主产品),2:按消耗(主产品,可拆金额),3:按消耗(子产品),4:按消耗(子产品,可拆金额),5:按到款,6:按到款(先票后款后消耗)]',
`rel_money` varchar(255) NOT NULL DEFAULT '' COMMENT '关联金额',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`),
KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3993 DEFAULT CHARSET=utf8 COMMENT='发票到款关系表'

#### ER 图

```mermaid
erDiagram
    invoice_info ||--o{ customer_invoice_log : "tracks"
    invoice_info ||--o{ invoice_consume : "relates"
    invoice_info ||--o{ invoice_remit : "links"
    invoice_info ||--o{ rel_invoice_consume : "details"
    invoice_info ||--o{ rel_invoice_remit : "records"

    invoice_info {
        int id PK
        string invoice_id
        string invoice_code
        string invoice_no
        decimal money
        decimal tax_rate
        decimal tax
        int invoice_status
        int invoice_type
    }

    customer_invoice_log {
        int id PK
        string invoice_id FK
        int status
        string admin
    }

    invoice_consume {
        int id PK
        string customer_id
        int month
        decimal consume
        string invoice_id FK
        decimal remit_money
    }

    rel_invoice_consume {
        int id PK
        string customer_id
        string invoice_id FK
        decimal invoice_money
        decimal consume_money
        int invoice_model
    }

    rel_invoice_remit {
        int id PK
        string customer_id
        string invoice_id FK
        string remit_serial
        decimal remit_money
        int invoice_model
    }
```

1. invoice_info 发票主表

系统中所有开具的发票记录保存在该表，每条发票具有唯一的 invoice_id，是核心主表。

⸻

2. invoice_info 一对多 customer_invoice_log

关系说明：
• 一个发票会被多次记录操作行为，如创建、审核、驳回等。
• 对应的日志信息以“操作历史”形式保存在 customer_invoice_log 表中。
• 等价关系：invoice_info.invoice_id = customer_invoice_log.invoice_id

⸻

3. invoice_info 一对多 invoice_consume

关系说明：
• 一个发票可能与多个客户月度消费记录绑定。
• 每条记录代表某客户在某月用于该发票报销的金额。
• 等价关系：invoice_info.invoice_id = invoice_consume.invoice_id

⸻

4. invoice_info 一对多 rel_invoice_consume

关系说明：
• 该表为 invoice_info 与 invoice_consume 的「明细拆分」记录。
• 支持多客户、多金额的精准拆分配置。
• 等价关系：invoice_info.invoice_id = rel_invoice_consume.invoice_id

⸻

5. invoice_info 一对多 rel_invoice_remit

关系说明：
• 每张发票可能与多个实际的汇款流水关联（用于开票抵扣匹配）。
• 通过 rel_invoice_remit 记录发票与收款对账流水的对应关系。
• 等价关系：invoice_info.invoice_id = rel_invoice_remit.invoice_id

#### 发票处理流程图

```mermaid
graph TD
    A["发票申请"] --> B["商务审核"]
    B --> C{"审核结果"}
    C -->|"通过"| D["财务开票"]
    C -->|"驳回"| E["申请驳回"]
    C -->|"撤销"| F["申请撤销"]

    D --> G["发票开具"]
    G --> H["发票邮寄"]
    H --> I["客户接收"]

    I --> J{"发票状态"}
    J -->|"正常"| K["等待回款"]
    J -->|"退票"| L["退票处理"]

    K --> M["部分回款"]
    K --> N["完成回款"]

    L --> O["重新开票"]
    O --> A
```

#### 发票分类和状态图

```mermaid
graph TD
    A["发票管理"] --> B["发票类型"]
    A --> C["开票模式"]
    A --> D["发票状态"]

    B --> E["增值税专票"]
    B --> F["增值税普票"]

    C --> G["按消耗开票"]
    C --> H["按到款开票"]

    G --> I["主产品"]
    G --> J["子产品"]
    I --> K["可拆分金额"]
    J --> L["可拆分金额"]

    H --> M["先票后款"]
    H --> N["先款后票"]

    D --> O["初始化:0"]
    D --> P["已申请:10"]
    D --> Q["已驳回:15"]
    D --> R["已撤销:16"]
    D --> S["已审核:20"]
    D --> T["已开票:30"]
    D --> U["已邮寄:50"]
    D --> V["已退票:70"]
    D --> W["部分回款:90"]
    D --> X["已回款:100"]
```
