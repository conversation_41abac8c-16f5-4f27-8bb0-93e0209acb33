本图用于完整展示系统从外部接入、核心应用服务、业务中台、数据分析、监控告警到基础设施的全栈架构布局，体现平台作为金融风控 SaaS 系统的核心能力闭环。

⸻

# 全景架构图（Mermaid 源码）

flowchart TD

subgraph 客户侧
A[API 调用方 / 客户系统 / SDK 接入]
end

subgraph 接入层
B[API Gateway]
B1[鉴权 / 限流 / 灰度 / traceId 初始化]
end

subgraph 应用层
C1[账单服务]
C2[客户服务]
C3[渠道管理服务]
C4[统计报表服务]
C5[产品管理服务]
end

subgraph 中台与领域服务
D1[账号权限中心]
D2[配置中心]
D3[产品定价服务]
D4[调用限额服务]
D5[日志聚合服务]
end

subgraph 数据与基础设施层
E1[(MySQL - 业务主库)]
E2[(MongoDB - 日志库)]
E3[(Redis - 限流缓存)]
E4[(ClickHouse - 统计分析)]
E5[(MQ / 消息服务)]
end

subgraph 可观测性与运营层
F1[日志平台 (调用 / 异常 / 审计)]
F2[Prometheus / Grafana 监控]
F3[告警推送 (飞书 / 邮件 / 微信)]
F4[移动端驾驶舱]
F5[PC 端分析报表]
end

A --> B --> B1 --> C1 & C2 & C3 & C4 & C5
C1 & C2 & C3 & C4 & C5 --> D1 & D2 & D3 & D4 & D5
D1 & D2 & D3 & D4 --> E1 & E3
D5 --> E2
C4 --> E4
所有模块 --> F1 & F2 & F3
E4 --> F5
F2 --> F4
