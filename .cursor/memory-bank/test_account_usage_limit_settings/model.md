# **数据架构特点**

- **双表设计**：常规限量(AccountProduct) + 测试限量(AccountProductLimit)
- **时间维度**：支持按日/月/年/总量四个维度限制
- **灵活配置**：-1 表示无限制，>0 表示具体限量
- **时间范围**：支持指定测试时间段

#数据表设计

## AccountProductLimit 表（测试限量专用表）

```sql
CREATE TABLE `account_product_limit` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `apikey` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户id',
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品id',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `total_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '总限额量',
  `remark` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `acc_pro_unique_id` (`apikey`,`product_id`) USING BTREE,
  KEY `account_id` (`apikey`) USING BTREE,
  KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='账号-产品-限量测试表'
```

## AccountProduct 表（常规限量配置）

```sql
CREATE TABLE `account_product` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `account_id` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户id',
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品id',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '账号-产品状态：1可用，2禁用',
  `use_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '产品使用类型:1外部调用,2内部调用',
  `contract_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '签约状态:1已签约已付款,2已签约未付费,3未签约,4其他,5特殊客户',
  `end_time` int(11) DEFAULT NULL,
  `daily_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '日限额用量',
  `month_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '月限额用量',
  `year_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '年限额量',
  `total_limit` int(10) NOT NULL DEFAULT '-1' COMMENT '总限额量',
  `concurrency` int(10) NOT NULL DEFAULT '1' COMMENT '秒并发',
  `data` text CHARACTER SET utf8 COMMENT '产品配置参数',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  `limit_start_date` date DEFAULT NULL COMMENT '总限量预警开始时间',
  `source_tags` varchar(6) DEFAULT '' COMMENT '切换渠道标记,仅标记子产品 填写已换签的渠道id 例如 ,1,2,99,100,',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ca_acc_pro_unique_id` (`account_id`,`product_id`) USING BTREE,
  KEY `ca_account_id` (`account_id`) USING BTREE,
  KEY `ca_product_id` (`product_id`) USING BTREE,
  KEY `ca_status` (`status`) USING BTREE,
  KEY `ca_cntract_status` (`contract_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16680 DEFAULT CHARSET=utf8mb4 COMMENT='账号-产品-关联表'
```
