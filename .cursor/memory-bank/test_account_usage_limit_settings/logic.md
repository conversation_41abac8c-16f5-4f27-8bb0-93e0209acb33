### **三、核心业务功能架构**

#### **3.1 限量配置管理**

**功能模块：**

```11:33:app/Http/Controllers/AccountProductLimitController.php
public function limitList()    // 限量配置列表
public function limitInfo()    // 限量配置详情
public function saveLimit()    // 保存限量配置
public function delLimit()     // 删除限量配置
```

**业务流程：**

1. **配置创建**：指定客户、产品、时间范围、限量数值
2. **唯一性校验**：同一客户同一产品不能重复配置
3. **权限验证**：需要登录用户权限验证
4. **数据持久化**：保存到 AccountProductLimit 表

#### **3.2 限量监控与统计**

**实时监控架构：**

```47:100:app/Console/Commands/Customer/CustomerProductLimitWarning.php
// 获取限量配置的客户产品
$this->limit_account = AccountProduct::getLimitAccountProduct();

// 按维度统计用量
$day_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$day,$day);
$month_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$month,$day);
$year_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$year,$day);
$total_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$start_date,$day);
```

**统计维度：**

- **日限量监控**：当日用量统计
- **月限量监控**：当月累计用量统计
- **年限量监控**：当年累计用量统计
- **总限量监控**：从限制开始日期到当前的总用量

#### **3.3 预警机制架构**

**多级预警体系：**

```63:139:config/params.php
'list_limit_standard' => [
    'sign_a_contract' => [    // 已签约客户预警标准
        'daily_limit' => [
            ['standard' => ['left' => 0.8, 'right' => 1], 'prefix_cache' => 'product_invoked_limit_waring_day1']
        ],
        'month_limit' => [
            ['standard' => ['left' => 0.8, 'right' => 0.9], 'prefix_cache' => 'product_invoked_limit_waring_month1'],
            ['standard' => ['left' => 0.9, 'right' => 1], 'prefix_cache' => 'product_invoked_limit_waring_month2']
        ]
    ],
    'not_sign_a_contract' => [ // 未签约客户预警标准
        'daily_limit' => [
            ['standard' => ['left' => 0.8, 'right' => 0.9], 'prefix_cache' => 'product_invoked_limit_waring_day1']
        ]
    ]
]
```

**预警触发机制：**

- **阈值预警**：按比例触发（50%、80%、90%等）
- **分级预警**：已签约和未签约客户不同标准
- **防重复预警**：Redis 缓存防止重复发送
- **邮件通知**：自动发送预警邮件

### **四、技术架构实现**

#### **4.1 缓存架构设计**

**多层缓存策略：**

```487:540:app/Jobs/ProductInvokedAmountWarningJob.php
// 产品限额缓存
private $prefix_product_limit_cache = 'product_limit_';

// 预警标记缓存
private function getCacheKeyForLimitEmail(string $prefix_cache): string
{
    $apikey = $this->product_invoked_item['apikey'];
    $product_id = $this->product_invoked_item['product_id'];
    return $prefix_cache . '_' . $apikey . '_' . $product_id;
}

// 用量数据缓存
private $cache_key_invoked_prefix = 'product_invoked_';
```

**缓存层次：**

1. **限额配置缓存**：避免频繁查询数据库
2. **用量统计缓存**：实时用量数据缓存
3. **预警状态缓存**：防止重复预警
4. **过期策略**：按时间维度设置不同过期时间

#### **4.2 异步处理架构**

**队列任务设计：**

```1:50:app/Jobs/ProductInvokedAmountWarningJob.php
class ProductInvokedAmountWarningJob extends Job
{
    // 产品调用量预警任务
    private $product_invoked_item;
    private $queue_name_send_mail = 'queue_amount_warning_by_email';

    public function handle()
    {
        $this->validateParams();      // 参数校验
        $this->setLimitStandard();    // 设置限额标准
        $this->validateLimit();       // 校验是否超限
    }
}
```

**异步处理流程：**

1. **实时监控**：用量数据实时推送到队列
2. **异步计算**：后台异步计算用量占比
3. **预警判断**：根据配置标准判断是否需要预警
4. **通知发送**：异步发送邮件通知

#### **4.3 数据一致性保障**

**事务处理：**

```54:90:app/Http/Controllers/AccountProductLimitController.php
public function saveLimit()
{
    try {
        if ($id > 0) { // 编辑
            $res = AccountProductLimit::updateData($id, $data);
        } else {
            // 唯一性校验
            $res = AccountProductLimit::where(['apikey' => $data['apikey'], 'product_id' => $data['product_id']])->first();
            if ($res) {
                $this->createBaseResponse('该客户的产品已添加', 5001, []);
            }
            $res = AccountProductLimit::addData($data);
        }
    } catch (Exception $e) {
        $this->createBaseResponse("操作失败:" . $e->getMessage(), 5007, []);
    }
}
```

### **五、系统集成架构**

#### **5.1 与统计系统集成**

**用量统计接口：**

```php
// 核心统计方法
StatisticsCustomerUsage::getUsageByApikeyProductDate($apikey, $product_id, $start_date, $end_date);
```

**统计数据源：**

- **MongoDB 统计表**：实时用量数据
- **MySQL 统计表**：历史用量数据
- **Redis 缓存**：高频查询数据缓存

#### **5.2 与监控系统集成**

**监控命令：**

```17:50:app/Console/Commands/Customer/CustomerProductLimitWarning.php
class CustomerProductLimitWarning extends Command
{
    protected $signature = "customer_product_limit_warning";

    public function handle()
    {
        $stay_alert_data = $this->getConsumptionByApikey();  // 获取用量数据
        $this->sendStayAlert($stay_alert_data);              // 发送预警
    }
}
```

#### **5.3 与通知系统集成**

**邮件预警：**

```17:91:app/Console/Commands/Customer/AccountProductLimitAlarm.php
class AccountProductLimitAlarm extends Command
{
    protected $addressee = [
        '<EMAIL>',
        '<EMAIL>',
    ];

    // 生成预警邮件内容
    protected function mailTemplate($header, $data)
    {
        // HTML表格格式邮件模板
    }
}
```

### **六、架构优势与特点**

#### **6.2 设计特点**

1. **双表架构**：测试限量与正式限量分离
2. **多维限制**：支持日/月/年/总量四个维度
3. **智能预警**：分级预警 + 防重复机制
4. **灵活配置**：支持个性化测试策略

### **七、扩展性设计**

#### **7.1 功能扩展点**

- **更多限制维度**：可扩展小时、周等时间维度
- **更复杂规则**：支持组合限制条件
- **更智能预警**：机器学习预测用量趋势

#### **7.2 技术扩展点**

- **分布式缓存**：支持集群环境
- **实时计算**：流式处理实时用量
- **可视化监控**：图表展示用量趋势
