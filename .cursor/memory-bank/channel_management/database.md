## 4. 数据模型关系

### a) 核心表结构：

```sql
-- 渠道主表
channel (id, channel_id, name, label, status, balance, contract_end_date)

-- 渠道接口表
channel_interface (id, channel_id, name, label, status)

-- 渠道产品关联表
channel_product (id, channel_id, product_id, param, status)

-- 接口价格配置表
config_price_interface (id, interface_id, price, start_date)
```

### b) 数据关联逻辑：

```php
// 一对多关系
Channel → ChannelInterface (一个渠道有多个接口)
Channel → ChannelProduct (一个渠道支持多个产品)

// 多对多关系
Product ←→ Channel (通过 channel_product 表关联)

// 配置关系
ChannelInterface → ConfigPriceInterface (接口价格配置)
```

## 5. 缓存和性能优化

### a) Redis 缓存使用：

```php
// 产品ID与名称映射缓存
RedisCache::instance('productId_productName_mapping')->get($product_id);

// 渠道ID与标签映射缓存
RedisCache::instance('channelId_label_mapping')->get($channel_id);

// 产品父子关系映射缓存
RedisCache::instance('productId_fatherId_mapping')->get($product_id);
```
