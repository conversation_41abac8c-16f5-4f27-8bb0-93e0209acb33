⸻

功能解释：渠道补数配置（邦信分）

作用：
为“邦信分”这个产品配置在某个渠道下的补数规则，即：

当调用渠道接口未返回完整数据（如评分字段为空、API 超时等）时，系统通过预设逻辑或其他渠道补全该数据，以保障业务连续性和数据完整性。

⸻

典型应用场景：

场景 描述
渠道数据返回异常 渠道调用返回为空值、报错、缺字段，需要用默认值或其他渠道结果补齐
多渠道容灾配置 主渠道失败后，从备用渠道拉取数据作为“补数”
数据延迟补入 渠道只支持异步推送，暂时用占位数据，后续补上完整字段
历史数据补录 为已有的调用记录补录缺失字段（如评分、命中等级等）

⸻

配置内容可能包括：
• 渠道选择（如主用、备用渠道）
• 产品类型（邦信分）
• 补数字段定义（如信用分数、风险等级）
• 补数策略类型（默认值填充 / 调用备用接口 / 异步回填）
• 启用状态 / 有效期
• 操作日志 / 配置说明

⸻

系统处理方式可能是：

调用渠道接口 → 判断返回值完整性 → 若缺失 → 走补数策略 → 更新数据 → 回传客户

⸻

## 二、数据模型设计

### 2.1 核心数据表结构

**表名**: `account_product_custom`

- **type = 30**: 渠道补数配置标识

- **father_id**: 父产品 ID（限制为 210 或 1000）

- **apikey**: 客户标识

- **extend**: 渠道 ID（复用字段存储渠道信息）

### 2.2 数据关联关系

```
account_product_custom (补数配置)
    ├── account (账户表) - 通过apikey关联
    └── channel (渠道表) - 通过extend字段关联channel_id
```

### 4.2 业务约束设计

```php
// 父产品限制
if(!in_array(intval($params['father_id']), [210, 1000])) {
    $this->createBaseResponse("父产品ID只能为210或1000", 5003);
}

// 唯一性检查
$find = ['father_id'=> intval($params['father_id']), 'apikey' => $params['apikey'], 'extend' => $cid,  'type' => 30, 'delete_at' => 0];
$info = self::getOneItemByCondition($find);
```
