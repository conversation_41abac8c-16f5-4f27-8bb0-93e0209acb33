## 1. 核心业务流程架构

### a) 主要业务流程分类：

```php
// 基于ChannelController.php实际实现的功能分类
1. 渠道基础管理流程
2. 渠道接口管理流程
3. 渠道产品配置流程
4. 渠道价格配置流程
5. 渠道特殊配置流程
6. 渠道资质管理流程
7. 渠道监控统计流程
```

## 2. 渠道基础管理业务流程

### a) 新增渠道流程：

```mermaid
graph TD
    A[用户提交新增渠道] --> B[参数验证]
    B --> C{渠道ID是否存在?}
    C -->|存在| D[返回错误:渠道已存在]
    C -->|不存在| E[收集渠道信息]
    E --> F[基础信息:<br/>channel_id, label, name]
    F --> G[财务信息:<br/>balance, balance_start_date]
    G --> H[调用配置:<br/>rest_num, max_call_num]
    H --> I[合同信息:<br/>contract_end_date]
    I --> J[保存到数据库]
    J --> K[返回成功响应]
```

### b) 实际代码实现：

```php
// addChannel() 方法
public function addChannel() {
    // 1. 验证渠道ID唯一性
    $channel_id = request()->post('channel_id');
    $count = Channel::where('channel_id', $channel_id)->count();
    if ($count) {
        $this->createSimpleResponse('channel.14100');
    }

    // 2. 收集渠道信息
    $label = request()->post('channel_name');
    $name = request()->post('name');
    $balance = request()->post('balance', 0);
    // ... 其他字段

    // 3. 直接插入数据库 (无Service层)
    Channel::insert(compact('label', 'name', 'channel_id', ...));
}
```

## 3. 渠道产品配置业务流程

### a) 产品渠道配置流程：

```mermaid
graph TD
    A[配置产品渠道] --> B{新增 or 编辑?}

    B -->|新增| C[验证渠道和产品存在]
    B -->|编辑| D[获取现有配置]

    C --> E[产品ID父子关系验证]
    D --> E

    E --> F[配置参数处理]
    F --> F1[回溯日期格式验证]
    F --> F2[缓存日期格式验证]
    F --> F3[支持产品列表处理]
    F --> F4[检测产品验证]

    F1 --> G[参数JSON序列化]
    F2 --> G
    F3 --> G
    F4 --> G

    G --> H[保存配置到数据库]
    H --> I[设置渠道账期缓存]
    I --> J[返回操作结果]
```

### b) 关键业务逻辑：

```php
// saveProductChannel() 核心逻辑
public function saveProductChannel() {
    // 1. 产品列表验证
    if (!empty($product_ids)) {
        $p_list = explode(',', $product_ids);
        $father_list = [];
        foreach($p_list as $pid) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($pid);
            $father_list[] = $father_id;
        }
        // 确保所有产品属于同一父产品
        if (count(array_unique($father_list)) > 1) {
            $this->createBaseResponse('支持产品中含有其它父产品的产品', 50001);
        }
    }

    // 2. 参数配置
    $param = [
        'enable_date' => substr($back_date, 0, 6),
        'back_day' => substr($back_date, -2),
        'cache_date' => $cache_date,
        'product_ids' => $product_ids,
        // ...
    ];

    // 3. 保存配置
    ChannelProduct::editChannelInfoById($id, [
        'param' => json_encode($param, JSON_UNESCAPED_UNICODE),
        'status' => $status
    ]);
}
```

## 4. 渠道状态管理流程

### a) 渠道上下线流程：

```mermaid
graph TD
    A[渠道状态变更请求] --> B[用户权限验证]
    B --> C[获取渠道产品信息]
    C --> D[获取渠道基础信息]
    D --> E[执行状态更新]
    E --> F[微信报警通知]
    F --> G[记录操作日志]
    G --> H[返回操作结果]
```

### b) 关键实现：

```php
// editChannelStatus() 核心逻辑
public function editChannelStatus() {
    // 1. 权限验证
    $user_name = Func::getUserNameFromCookie($user_cookie);

    // 2. 获取渠道信息
    $channel_product_info = ChannelProduct::where('id', $id)->first();
    $channel_info = Channel::where('channel_id', $channel_product_info['channel_id'])->first();

    // 3. 状态更新
    ChannelProduct::where('id', $id)->update(['status' => $status]);

    // 4. 发送报警通知
    $statusStr = $status == 1 ? "开启" : "关闭";
    $noticeStr = "邦信分 {$channel_name} 渠道于 {$time} {$statusStr} ，操作人：$user_name";
    $this->wechatException($noticeStr, "wechat.bxf");
}
```

## 5. 渠道特殊配置业务流程

### a) 渠道补量配置流程：

```php
// 业务类型定义 (基于AccountProductCustom模型)
const TYPE_COMPLEMENT = 30;  // 渠道补量配置
const TYPE_SHUNT = 70;       // 自有分流配置
const TYPE_CMCC_SCORE = 50;  // 移动评分配置
```

### b) 补量配置处理：

```mermaid
graph TD
    A[补量配置请求] --> B{新增 or 编辑?}

    B -->|新增| C[验证父产品ID]
    B -->|编辑| D[获取现有配置]

    C --> E[批量渠道处理]
    D --> F[单渠道更新]

    E --> G[配置参数处理]
    F --> G

    G --> G1[complement: 补量值]
    G --> G2[timeout: 超时设置]

    G1 --> H[保存到account_product_custom表]
    G2 --> H

    H --> I[返回操作结果]
```

## 6. 渠道价格配置业务流程

### a) 价格配置查询流程：

```php
// getPriceList() 实现
public function getPriceList() {
    $data = ConfigPriceInterface::select([
        'config_price_interface.id',
        'channel.label as channel_name',
        'channel_interface.label as interface_name',
        'config_price_interface.start_date',
        'config_price_interface.price',
    ])
    ->leftJoin('channel_interface', 'channel_interface.id', '=', 'config_price_interface.interface_id')
    ->leftJoin('channel', 'channel.channel_id', '=', 'channel_interface.channel_id')
    ->orderBy('start_date', 'desc')
    ->get()
    ->map(function ($item) {
        // 价格信息格式化处理
        $price = json_decode($item->price, true);
        $price = array_map(function ($item) {
            $operator = array_get($this->operator, $item['operator'], '未知');
            $encrypt_way = array_get($this->encrypt_way, $item['encrypt_way'], '未知');
            $model = $this->getPriceModel($item['price_model']);
            $price = $item['price'];

            return "{$price}（【{$operator}】【{$encrypt_way}】【{$model}】）";
        }, $price);

        return $item;
    });
}
```

## 7. 渠道资质管理业务流程

### a) 资质管理流程 (基于 ChannelQualicationRepository)：

```mermaid
graph TD
    A[资质管理请求] --> B{操作类型}

    B -->|查询| C[获取资质列表]
    B -->|新增| D[添加资质信息]
    B -->|编辑| E[修改资质信息]
    B -->|文件| F[文件管理操作]

    C --> C1[分页查询资质]
    C --> C2[关联资质详情]
    C --> C3[关联文件信息]

    D --> D1[验证资质唯一性]
    D --> D2[事务处理]
    D --> D3[保存资质详情]

    E --> E1[权限验证]
    E --> E2[批量更新处理]
    E --> E3[文件状态管理]

    F --> F1[文件下载]
    F --> F2[文件删除]
```

### c) 关键业务约束：

- 渠道 ID 唯一性验证
- 产品父子关系验证
- 用户权限控制
- 配置格式验证
- 状态变更通知
