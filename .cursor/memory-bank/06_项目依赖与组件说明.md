# 📦 项目依赖与组件说明

本项目基于 Laravel Lumen 5.5 框架，具备金融级高性能、高安全、高可扩展性特点。以下是 `composer.json` 中关键依赖项说明：

---

## 🌐 核心框架

- **laravel/lumen-framework**：微服务核心框架
- **vlucas/phpdotenv**：环境变量加载（.env 配置）

---

## 🔐 认证授权

- **dusterio/lumen-passport**：OAuth2 API 授权

---

## 🗄️ 数据存储层

- **illuminate/redis**：Redis 缓存支持
- **jenssegers/mongodb**：MongoDB ORM 兼容扩展
- **smi2/phpclickhouse**：ClickHouse 数据查询支持
- **league/flysystem**：抽象文件系统（支持本地/云）

---

## 📧 第三方集成

- **phpmailer/phpmailer**、**illuminate/mail**：邮件服务
- **guzzlehttp/guzzle**：HTTP 客户端
- **overtrue/laravel-wechat**：微信企业号、小程序等 SDK
- **pimple/pimple**：轻量依赖注入容器

---

## 📊 数据处理组件

- **maatwebsite/excel**：Excel 文件导入导出
- **ramsey/uuid**：UUID 生成
- **yulore/apikey-masking**：API 密钥脱敏日志组件

---

## 🧪 开发与测试工具

- **phpunit/phpunit**、**mockery/mockery**：测试框架
- **fzaninotto/faker**：模拟数据生成

---

## 🧩 PHP 扩展依赖

- **ext-bcmath**：高精度运算
- **ext-curl**：外部请求
- **ext-json**：JSON 处理
- **ext-openssl**：SSL 支持

---
