# 🌐 路由概览与功能结构

## 路由入口文件

- `routes/web.php`
- 含 150+ 路由，覆盖财务、合同、客户、产品、监控、报表等业务域

---

## 📊 路由按模块分类

### ✅ 无认证接口

- 微信预警、测试接口、渠道测试等

### ✅ 账单管理（v1/bill）

- 查询账单、历史账单、账单邮件发送、Excel 导出、日志查看等

### ✅ 渠道管理（channel）

- 渠道创建、配置、接口管理、价格配置等

### ✅ 产品与监控（monitor/product & monitor/channel）

- 成功率统计、异常监控、报警处理等

### ✅ 统计报表（statistic, statProduct）

- 客户统计、产品统计、月度趋势、同比对比

### ✅ 合同、发票管理（contract, invoice）

- 合同信息、发票申请、发票审核、回款认票

### ✅ 客户与配置（options）

- 客户、账号、产品、渠道、接口下拉选项接口

### ✅ 报表驾驶舱（dashboard）

- 移动端与 PC 端数据总览、任务进度、收入趋势等

### ✅ 售前测试管理（pre_test_manage）

- 售前申请、反馈记录、接入信息、权限管理
