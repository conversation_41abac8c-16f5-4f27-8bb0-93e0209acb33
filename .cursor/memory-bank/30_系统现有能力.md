graph TB
subgraph "财务管理 API 系统 - 当前已有功能能力"
direction TB

        subgraph "💰 账单管理能力"
            A1["📋 账单核心功能<br/>• 客户账单生成与查询<br/>• 产品账单统计<br/>• 运营商账单对账<br/>• 账单校正与调整<br/>• 客户特殊消耗处理<br/>• 账单邮件发送"]

            A2["💳 计费算法引擎<br/>• 多种计费模式支持<br/>• 按时间/按量计费<br/>• 累进阶梯计费<br/>• 运营商区分计费<br/>• 固定价格计费<br/>• 达到阶梯计费"]

            A3["🔄 账单版本管理<br/>• V1基础账单功能<br/>• V2权责利润报表<br/>• V3运营商对账<br/>• 账单重跑机制<br/>• 历史账单查询<br/>• 账单状态跟踪"]
        end

        subgraph "📊 统计分析能力"
            B1["📈 多维度统计<br/>• 客户维度统计<br/>• 产品维度统计<br/>• 渠道维度统计<br/>• 时间维度统计<br/>• 对比分析功能<br/>• 趋势图表展示"]

            B2["🎯 专项统计<br/>• 上游渠道统计<br/>• 邦秒验产品统计<br/>• 邦信分快捷版统计<br/>• 邦企查统计<br/>• 调用量统计<br/>• 成功率统计"]

            B3["📋 报表生成<br/>• 日报自动生成<br/>• 周报统计<br/>• 月报统计<br/>• 季度报表<br/>• Excel导出功能<br/>• 邮件推送报表"]
        end

        subgraph "🔗 渠道管理能力"
            C1["⚙️ 渠道配置<br/>• 渠道基础信息管理<br/>• 接口配置管理<br/>• 计费价格配置<br/>• 渠道状态控制<br/>• 渠道日志记录<br/>• 渠道资质管理"]

            C2["🔀 智能分流<br/>• 渠道补数配置<br/>• 自有分流配置<br/>• 移动评分映射<br/>• 渠道异常处理<br/>• 分流策略配置<br/>• 渠道切换机制"]

            C3["💰 成本管理<br/>• 渠道成本统计<br/>• 成本分摊计算<br/>• 成本调整功能<br/>• 渠道价格管理<br/>• 成本趋势分析<br/>• 盈利能力分析"]
        end

        subgraph "🚨 监控告警能力"
            D1["📊 实时监控<br/>• 产品调用监控<br/>• 渠道状态监控<br/>• 成功率监控<br/>• 值分布监控<br/>• 异常检测<br/>• 历史数据监控"]

            D2["⚠️ 告警处理<br/>• 多级告警配置<br/>• 告警日志管理<br/>• 批量告警处理<br/>• 告警确认机制<br/>• 微信告警推送<br/>• 告警统计分析"]

            D3["🔍 专项监控<br/>• 蚂蚁金服监控<br/>• 邦信分快捷版监控<br/>• 携号转网监控<br/>• 渠道异常监控<br/>• 数据质量监控<br/>• 系统性能监控"]
        end

        subgraph "👥 客户管理能力"
            E1["📝 客户信息<br/>• 客户基础信息管理<br/>• 客户分组管理<br/>• 开票信息管理<br/>• 地址信息管理<br/>• 客户权限控制<br/>• 新老客户分析"]

            E2["🔐 权限管理<br/>• 用户角色管理<br/>• 产品权限控制<br/>• 数据权限控制<br/>• 操作权限控制<br/>• 登录认证<br/>• 权限审计"]

            E3["📊 客户分析<br/>• 客户消费统计<br/>• 客户价值分析<br/>• 客户行为分析<br/>• 客户成本分析<br/>• 客户盈利分析<br/>• 客户风险评估"]
        end

        subgraph "🧾 发票管理能力"
            F1["📋 发票流程<br/>• 开票申请流程<br/>• 发票审核机制<br/>• 发票状态管理<br/>• 批量开票处理<br/>• 发票导入导出<br/>• 发票邮寄跟踪"]

            F2["💰 回款管理<br/>• 回款认票功能<br/>• 收款单管理<br/>• 认款拆单<br/>• 回款状态跟踪<br/>• 回款统计分析<br/>• 坏账风险控制"]

            F3["📊 发票统计<br/>• 发票汇总统计<br/>• 开票金额统计<br/>• 发票状态统计<br/>• 回款情况统计<br/>• 发票明细查询<br/>• 税务数据统计"]
        end

        subgraph "⚙️ 配置管理能力"
            G1["🔧 系统配置<br/>• 产品配置管理<br/>• 价格策略配置<br/>• 计费规则配置<br/>• 系统参数配置<br/>• 选项配置管理<br/>• 配置热更新"]

            G2["📊 监控配置<br/>• 值分布阈值配置<br/>• 渠道查得率配置<br/>• 告警规则配置<br/>• 监控项配置<br/>• 异常处理配置<br/>• 监控策略配置"]

            G3["🎯 业务配置<br/>• 邦信分策略配置<br/>• 客户单价配置<br/>• 渠道补数配置<br/>• 分流策略配置<br/>• 产品主题配置<br/>• 业务规则配置"]
        end

        subgraph "🔄 业务流程能力"
            H1["📋 审批流程<br/>• 审批任务管理<br/>• 审批配置管理<br/>• 审批状态跟踪<br/>• 审批历史记录<br/>• 钉钉审批集成<br/>• 审批超时处理"]

            H2["📊 数据处理<br/>• 数据校正功能<br/>• 历史数据调整<br/>• 数据导入导出<br/>• 数据验证机制<br/>• 数据同步功能<br/>• 数据备份恢复"]

            H3["🔗 第三方集成<br/>• 邦秒验产品集成<br/>• 邦信分快捷版集成<br/>• 邦企查集成<br/>• 蚂蚁金服对接<br/>• 运营商系统对接<br/>• 飞书系统集成"]
        end

        subgraph "📱 用户界面能力"
            I1["💻 PC端功能<br/>• 管理后台界面<br/>• 数据查询界面<br/>• 报表展示界面<br/>• 配置管理界面<br/>• 监控大屏<br/>• 操作日志界面"]

            I2["📱 移动端功能<br/>• 移动驾驶舱<br/>• 移动报表查看<br/>• 移动审批功能<br/>• 飞书集成<br/>• 移动告警<br/>• 移动数据查询"]

            I3["📊 数据展示<br/>• 图表可视化<br/>• 实时数据展示<br/>• 趋势分析图<br/>• 对比分析图<br/>• 数据导出<br/>• 自定义报表"]
        end

        subgraph "🛠️ 技术支撑能力"
            J1["🏗️ 系统架构<br/>• Laravel/Lumen框架<br/>• MySQL数据库<br/>• MongoDB日志存储<br/>• Redis缓存<br/>• ClickHouse分析<br/>• 消息队列处理"]

            J2["🔒 安全机制<br/>• 用户认证<br/>• 权限控制<br/>• 数据加密<br/>• 操作审计<br/>• 访问日志<br/>• 安全中间件"]

            J3["⚡ 性能优化<br/>• 数据库优化<br/>• 缓存机制<br/>• 异步处理<br/>• 批量操作<br/>• 索引优化<br/>• 查询优化"]
        end
    end

    %% 连接关系
    A1 --> B1
    A2 --> C3
    A3 --> F2

    B1 --> D1
    B2 --> D3
    B3 --> I3

    C1 --> D1
    C2 --> D2
    C3 --> B1

    D1 --> I1
    D2 --> I2
    D3 --> H3

    E1 --> F1
    E2 --> G1
    E3 --> B1

    F1 --> H1
    F2 --> H2
    F3 --> I3

    G1 --> J1
    G2 --> D1
    G3 --> H3

    H1 --> I1
    H2 --> J3
    H3 --> J1

    I1 --> J2
    I2 --> J2
    I3 --> J3

    %% 样式定义
    classDef billCapability fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef statCapability fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef channelCapability fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef monitorCapability fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef customerCapability fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef invoiceCapability fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef configCapability fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef processCapability fill:#fafafa,stroke:#616161,stroke-width:2px
    classDef uiCapability fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef techCapability fill:#fff8e1,stroke:#ffa000,stroke-width:2px

    class A1,A2,A3 billCapability
    class B1,B2,B3 statCapability
    class C1,C2,C3 channelCapability
    class D1,D2,D3 monitorCapability
    class E1,E2,E3 customerCapability
    class F1,F2,F3 invoiceCapability
    class G1,G2,G3 configCapability
    class H1,H2,H3 processCapability
    class I1,I2,I3 uiCapability
    class J1,J2,J3 techCapability

## 🏢 **财务管理 API 系统 - 当前已有功能能力全景**

基于对实际代码的深入分析，以下是系统当前**真实具备**的功能能力：

### **📊 核心业务能力矩阵**

| 功能模块        | 已实现功能                                       | 技术实现                                  | 业务价值         |
| --------------- | ------------------------------------------------ | ----------------------------------------- | ---------------- |
| **💰 账单管理** | 多版本账单系统(V1/V2/V3)、计费算法引擎、账单校正 | Laravel 服务、多种计费驱动、数据库事务    | 支持复杂计费场景 |
| **📊 统计分析** | 客户/产品/渠道多维统计、专项产品统计、报表生成   | ClickHouse 分析、MongoDB 聚合、Excel 导出 | 全方位数据洞察   |
| **🔗 渠道管理** | 渠道配置、智能分流、成本管理、资质管理           | 配置化管理、状态机、成本算法              | 渠道效益最大化   |
| **🚨 监控告警** | 实时监控、多级告警、专项监控(蚂蚁/邦信分)        | 实时数据采集、告警引擎、微信推送          | 系统稳定运行     |
| **👥 客户管理** | 客户信息、权限管理、客户分析                     | 用户权限系统、角色管理、数据权限          | 精细化客户服务   |
| **🧾 发票管理** | 发票全流程、回款管理、发票统计                   | 工作流引擎、状态机、批量处理              | 财务流程自动化   |

### **🎯 系统核心特色**

#### **1. 💰 账单管理核心能力**

```yaml
计费算法引擎:
  - DateModuleDriver: 按时间计费
  - NumberFixedModuleDriver: 按量固定价格
  - NumberProgressionModuleDriver: 累进阶梯
  - NumberReachModuleDriver: 达到阶梯
  - OperatorModuleDriver: 运营商区分计费

账单版本管理:
  - V1: 基础账单功能
  - V2: 权责利润报表
  - V3: 运营商对账、营收账单

特殊功能:
  - 账单重跑机制
  - 客户账单调整
  - 特殊消耗处理
  - 运营商账单对账
```

#### **2. 📊 统计分析核心能力**

```yaml
多维度统计:
  - 客户维度: StatCustomerRepository
  - 产品维度: StatProductController
  - 渠道维度: StatChannelController
  - 产品视角: StatPMController

专项统计:
  - 邦秒验: BmyController
  - 邦信分快捷版: 产品210专项处理
  - 邦企查: enterprise_query统计
  - 上游渠道: UpstreamController

报表生成:
  - 日报: ReportDayProfit系列
  - 周报: ReportWeekProfit系列
  - 月报: ReportMonthProfit系列
```

#### **3. 🔗 渠道管理核心能力**

```yaml
渠道配置管理:
  - 渠道基础信息: ChannelController
  - 接口管理: Interface配置
  - 计费价格: Price配置
  - 渠道状态: 启用/禁用控制

智能分流:
  - 渠道补数: ChannelComplement
  - 自有分流: ChannelShunt
  - 移动评分映射: CMCCScore
  - 异常处理: ChannelException

成本管理:
  - 成本分摊: ChannelCostSpread
  - 成本调整: ChannelCostAdjust
  - 成本统计: 渠道盈利分析
```

#### **4. 🚨 监控告警核心能力**

```yaml
实时监控:
  - 产品监控: ProductStatisController
  - 渠道监控: ChannelStatisController
  - 值分布: ValueSpread监控
  - 成功率: SuccessInfo监控

专项监控:
  - 蚂蚁监控: AntFinancialMonitor
  - 邦信分监控: BxfShortMonitoring
  - 携号转网: SwitchNetWork
  - 最近记录: LastRecord

告警处理:
  - 告警管理: ManageController
  - 微信告警: WechatWarning
  - 批量处理: BatchDeal
  - 告警日志: AlarmLog
```

#### **5. 👥 客户管理核心能力**

```yaml
客户信息管理:
  - 客户分组: CustomerGroup
  - 开票信息: CustomerInvoiceInformation
  - 地址管理: CustomerAddress
  - 新老客户: CustomerNewOld

权限管理:
  - 用户角色: SystemUserRole
  - 产品权限: SystemUserProduct
  - 数据权限: 基于角色的数据访问
  - 操作审计: SystemAccessLog

客户分析:
  - 消费统计: CustomerExpend
  - 价值分析: 客户贡献度
  - 风险评估: 客户风险等级
```

#### **6. 🧾 发票管理核心能力**

```yaml
发票流程:
  - 申请开票: InvoiceController@apply
  - 审核通过: InvoiceController@pass
  - 批量开票: InvoiceController@issueByExcel
  - 发票红冲: InvoiceController@flush

回款管理:
  - 回款认票: InvoiceController@relate
  - 收款单: ReceiptController
  - 认款拆单: RemitSplitPrice
  - 回款统计: 回款情况分析

发票统计:
  - 发票汇总: InvoiceController@collect
  - 明细列表: InvoiceController@invoiceDetailList
  - 结余统计: uninvoiceMoneyCount
```

### **🛠️ 技术架构支撑**

#### **框架与数据库**

```yaml
后端框架: Laravel/Lumen
数据库:
  - MySQL: 业务主库
  - MongoDB: 日志存储
  - Redis: 缓存层
  - ClickHouse: 数据分析

核心组件:
  - 计费引擎: BillManager
  - 统计引擎: StatisticProvider
  - 监控引擎: Monitor命名空间
  - 权限系统: 中间件+角色管理
```

#### **业务特色功能**

```yaml
产品210专项支持:
  - 邦信分快捷版
  - 区分评分字段和通信字段
  - 专项监控和统计
  - 特殊计费处理

多产品支持:
  - 邦秒验(BMY)
  - 邦企查
  - 存量洞察(CLDC)
  - 蚂蚁金服产品

定时任务:
  - 360+个定时任务
  - 报表自动生成
  - 数据同步处理
  - 监控告警
```

### **📱 用户界面能力**

#### **PC 端管理后台**

- 完整的管理界面
- 数据查询和展示
- 配置管理界面
- 监控大屏

#### **移动端支持**

- 移动驾驶舱
- 飞书集成
- 移动审批
- 实时告警

这套系统是一个**成熟的财务管理平台**，具备完整的账单管理、统计分析、渠道管理、监控告警等核心能力，支持多种复杂的业务场景和计费模式。
