## 系统菜单功能点梳理

### 系统菜单 Mermaid 图

```mermaid
graph TD
    A[系统设置] --> B[管理用户]
    A --> C[管理节点]
    A --> D[删除缓存]
    A --> E[部门管理]

    B --> B1[用户信息维护]
    B --> B2[部门关联]
    B --> B3[用户状态管理]
    B --> B4[权限角色分配]

    C --> C1[服务节点配置]
    C --> C2[负载均衡]
    C --> C3[节点监控]
    C --> C4[节点路由]

    D --> D1[Redis缓存清理]
    D --> D2[权限认证缓存清理]
    D --> D3[批量缓存重载]

    D1 --> D11[API Key映射缓存]
    D1 --> D12[客户信息缓存]
    D1 --> D13[产品信息缓存]
    D1 --> D14[渠道信息缓存]

    E --> E1[部门信息维护]
    E --> E2[部门层级管理]
    E --> E3[部门负责人设置]
    E --> E4[用户部门分配]
```

```mermaid
graph TD
    A[账户管理] --> B[客户账户管理]
    A --> C[API Key管理]
    A --> D[余额管理]
    A --> E[产品授权]
    A --> F[账户状态控制]

    B --> B1[客户信息维护]
    B --> B2[账户配置]
    B --> B3[账户类型管理]

    C --> C1[API Key生成]
    C --> C2[API Key授权]
    C --> C3[API Key状态管理]

    D --> D1[余额监控]
    D --> D2[余额预警]
    D --> D3[余额调整]

    E --> E1[产品访问权限]
    E --> E2[产品使用限制]
    E --> E3[产品计费配置]

    F --> F1[账户启用]
    F --> F2[账户禁用]
    F --> F3[账户锁定]

```

### 系统设置

这是一个系统管理的主菜单，包含以下子功能：

### 系统菜单的结构和关系

#### 管理用户

**功能描述**：系统用户管理功能

- **核心功能**：
  - 用户信息维护：用户名、真实姓名、邮箱等基本信息管理
  - 部门关联：用户与部门的绑定关系管理
  - 用户状态管理：在职、离职状态控制（disabled 字段）
  - 权限角色分配：用户权限和角色配置
  - 用户查询：支持按用户名、真实姓名、部门等条件查询
- **数据表**：`crs_system_user`表
- **相关模型**：`SystemUser.php`、`SystemUserToken.php`

#### 管理节点

**功能描述**：系统节点/服务节点管理

- **核心功能**：
  - 服务节点配置：各个微服务节点的配置管理
  - 负载均衡：节点间的负载分配
  - 节点监控：节点运行状态监控
  - 节点路由：请求路由到不同节点的配置

#### 删除缓存

**功能描述**：系统缓存管理功能

- **核心功能**：
  - **Redis 缓存清理**：
    - API Key 到客户 ID 的映射缓存 (`apikey_customerId_mapping`)
    - API Key 到账户 ID 的映射缓存 (`apikey_accountId_mapping`)
    - 客户 ID 到客户名称的映射缓存 (`customerId_customerName_mapping`)
    - 产品 ID 到产品名称的映射缓存 (`productId_productName_mapping`)
    - 渠道 ID 映射缓存 (`channelId_label_mapping`)
    - 产品信息缓存 (`product_info`)
  - **权限认证缓存清理**：通过 `/auth/fresh` 路由清理权限认证产生的缓存
  - **批量缓存重载**：使用 `redis_cache:reload` 命令重载所有缓存数据
- **实现位置**：
  - `app/Http/Repository/ProductAuthRepository.php`
  - `app/Console/Commands/RedisCache/RedisCacheReloadCommand.php`
  - `config/redis_cache.php` 配置文件

#### 部门管理

**功能描述**：组织架构管理功能

- **核心功能**：
  - 部门信息维护：部门名称、层级关系等
  - 部门层级管理：上下级部门关系
  - 部门负责人设置：部门领导指定（is_leader 字段）
  - 用户部门分配：员工归属部门管理
- **数据表**：`crs_system_dept`表
- **关联功能**：与用户管理紧密关联

### 账户管理

**功能描述**：金融业务账户管理

- **核心功能**：
  - **客户账户管理**：B2B 客户的账户信息维护
  - **API Key 管理**：客户接入的 API 密钥管理
  - **余额管理**：客户账户余额监控和预警
  - **产品授权**：客户可使用产品的授权管理
  - **账户状态控制**：账户启用/禁用状态管理
- **相关模型**：`Account.php`、`AccountProduct.php`、`Customer.php`
- **定义文件**：`app/Define/AccountApplyManage.php`

## 系统整体特点

1. **多数据源架构**：使用 MySQL 系统库(`mysql_system`)独立管理系统用户和部门数据
2. **多级缓存体系**：Redis 缓存、应用缓存等多层缓存优化性能
3. **权限分离设计**：系统管理权限与业务权限分离
4. **日志审计**：所有操作都有访问日志记录(`SystemAccessLog`)
5. **金融级安全**：API Key、Token 等多重认证机制

这套菜单体现了一个企业级金融 API 服务平台完整的系统管理功能，涵盖了用户权限、系统运维、缓存优化和业务账户等核心管理需求。
