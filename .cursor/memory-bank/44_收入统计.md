## 收入聚合表

### 客户维度聚合统计表 DDL

CREATE TABLE `statistics_gather_customer` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日期（Ymd）',
`customer_id` char(32) NOT NULL DEFAULT '0' COMMENT '客户 id',
`dept_id` varchar(20) NOT NULL DEFAULT '0' COMMENT '二级部门 id',
`money` varchar(255) NOT NULL DEFAULT '' COMMENT '收入金额(元)',
`number` varchar(255) NOT NULL DEFAULT '' COMMENT '计费用量',
`cost_money` varchar(255) NOT NULL DEFAULT '' COMMENT '成本金额(元)',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生成时间',
`modify_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `idx_date_customer` (`date`,`customer_id`) USING BTREE,
KEY `idx_date_dept` (`date`,`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=154463 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户维度每日聚合表'

### 产品维度聚合统计表 DDL

CREATE TABLE `statistics_gather_product` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日期（Ymd）',
`father_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父产品 ID',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '子产品 ID',
`money` varchar(255) NOT NULL DEFAULT '' COMMENT '收入金额(元)',
`number` varchar(255) NOT NULL DEFAULT '' COMMENT '计费用量',
`cost_money` varchar(255) NOT NULL DEFAULT '' COMMENT '成本金额(元)',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生成时间',
`modify_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `idx_date_father` (`date`,`father_id`) USING BTREE,
KEY `idx_date_product` (`date`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18180 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='产品维度每日聚合表'

### 客户中间聚合表

CREATE TABLE `gather_middle_customer` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日期（Ymd）',
`customer_id` char(32) NOT NULL DEFAULT '0' COMMENT '客户 id',
`dept_id` varchar(20) NOT NULL DEFAULT '0' COMMENT '二级部门 id',
`money` varchar(255) NOT NULL DEFAULT '' COMMENT '收入金额(元)',
`number` varchar(255) NOT NULL DEFAULT '' COMMENT '计费用量',
`cost_money` varchar(255) NOT NULL DEFAULT '' COMMENT '成本金额(元)',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生成时间',
`modify_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `idx_date_customer` (`date`,`customer_id`) USING BTREE,
KEY `idx_date_dept` (`date`,`dept_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=175596 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户维度每日聚合表'

### 产品中间聚合表

CREATE TABLE `gather_middle_product` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日期（Ymd）',
`father_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父产品 ID',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '子产品 ID',
`money` varchar(255) NOT NULL DEFAULT '' COMMENT '收入金额(元)',
`number` varchar(255) NOT NULL DEFAULT '' COMMENT '计费用量',
`cost_money` varchar(255) NOT NULL DEFAULT '' COMMENT '成本金额(元)',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生成时间',
`modify_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `idx_date_father` (`date`,`father_id`) USING BTREE,
KEY `idx_date_product` (`date`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=151367 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='产品维度每日聚合表'

### 产品分类日汇总数据

CREATE TABLE `statistics_summary_daily_prodcut_category` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`category_name` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名',
`show_prodcut_name` varchar(60) NOT NULL DEFAULT '' COMMENT '展示产品名',
`total` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日总调用量',
`bill_number` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日计费用量',
`income` decimal(15,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '日收入',
`cost` decimal(15,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '日成本',
`profit` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '日毛利',
`year_income` decimal(15,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '年收入',
`year_cost` decimal(15,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '年成本',
`year_profit` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '年毛利',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '统计日期（Ymd）',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生成时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='产品分类日汇总数据'

### 生成产品分类数据表的 ER 关系图

```mermaid
erDiagram
    statistics_summary_daily_prodcut_category ||--o{ product : "belongs_to"
    statistics_customer_usage ||--o{ statistics_summary_daily_prodcut_category : "aggregates"
    bill_product_income_v2 ||--o{ statistics_summary_daily_prodcut_category : "provides_income"
    bill_cost_v2 ||--o{ statistics_summary_daily_prodcut_category : "provides_cost"

    statistics_summary_daily_prodcut_category {
        string category_name
        string show_prodcut_name
        int total
        int bill_number
        decimal income
        decimal cost
        decimal profit
        decimal year_income
        decimal year_cost
        decimal year_profit
        date date
        int create_time
    }

    statistics_customer_usage {
        string apikey
        int product_id
        int total
        int success
        int valid
        int cache
        date date
    }

    product {
        int product_id
        string name
        int father_id
        int type
        int status
    }
```

- 原始数据到客户维度的关系：
  statistics_customer_usage → statistics_gather_customer
  一条客户调用记录可以被聚合到客户维度统计中
  按照 customer_id 和 date 进行分组聚合
  聚合计算调用量、计费量、收入和成本

- 原始数据到产品维度的关系：
  statistics_customer_usage → statistics_gather_product
  一条调用记录可以被聚合到产品维度统计中
  按照 product_id、father_id 和 date 进行分组聚合
  聚合计算各产品的调用量、计费量、收入和成本

- 客户维度的中间聚合关系：
  statistics_gather_customer → gather_middle_customer
  用于存储客户维度的中间计算结果
  保持与客户聚合表相同的结构
  支持多次计算和数据校验

- 产品维度的中间聚合关系：
  statistics_gather_product → gather_middle_product
  用于存储产品维度的中间计算结果
  保持与产品聚合表相同的结构
  支持多次计算和数据校验

- 产品分类汇总关系：
  statistics_gather_product → statistics_summary_daily_prodcut_category
  将产品维度数据按分类进行汇总
  计算每个分类的日调用量、收入、成本和利润

### 数据流转图

```mermaid
graph TD
    A[API调用数据] --> B[statistics_customer_usage]
    B --> C[按产品分类汇总]

    D[账单收入数据] --> E[bill_product_income_v2]
    E --> C

    F[账单成本数据] --> G[bill_cost_v2]
    G --> C

    C --> H[statistics_summary_daily_prodcut_category]

    H --> I[日报表展示]
    H --> J[月度统计]
    H --> K[年度统计]

    subgraph 统计维度
    L[分类维度] --> M[总调用量]
    L --> N[计费数量]
    L --> O[收入金额]
    L --> P[成本金额]
    L --> Q[利润金额]
    end
```

### 生成产品分类的层级结构图

```mermaid
graph TD
    A[产品分类] --> B[基础服务]
    A --> C[增值服务]
    A --> D[金融服务]
    A --> E[其他服务]

    B --> B1[号码核验]
    B --> B2[号码状态]
    B --> B3[基础认证]

    C --> C1[反欺诈]
    C --> C2[信用评估]
    C --> C3[营销服务]

    D --> D1[风控服务]
    D --> D2[信贷服务]
    D --> D3[支付服务]

    subgraph 统计规则
    F[按分类汇总] --> G[日调用量]
    F --> H[日收入]
    F --> I[日利润]
    F --> J[年收入]
    F --> K[年利润]
    end
```

### 生成产品分类统计的业务流程图

```mermaid
graph LR
    A[数据采集] --> B[数据清洗]
    B --> C[数据分类]
    C --> D[数据汇总]

    D --> E[按日统计]
    D --> F[按月统计]
    D --> G[按年统计]

    E --> H[日报表]
    F --> I[月报表]
    G --> J[年报表]

    subgraph 统计处理
    K[原始数据] --> L[分类映射]
    L --> M[数据聚合]
    M --> N[统计计算]
    N --> O[数据存储]
    end

    subgraph 数据应用
    P[领导报表] --> Q[收入分析]
    P --> R[利润分析]
    P --> S[趋势分析]
    end
```
