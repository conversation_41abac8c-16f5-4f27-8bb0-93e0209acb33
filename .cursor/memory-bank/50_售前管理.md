## 售前申请表

### 测试主产品申请表 pre_test_apply_father

CREATE TABLE `pre_test_apply_father` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`apply_id` int(10) NOT NULL DEFAULT '0' COMMENT '申请表 ID',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`product_key` int(10) NOT NULL COMMENT '业务产品 ID',
`apply_father_name` varchar(64) NOT NULL DEFAULT '' COMMENT '申请主产品名称',
`product_list` varchar(255) DEFAULT NULL,
`test_status` int(2) NOT NULL DEFAULT '0' COMMENT '测试进度状态',
`test_result` int(2) NOT NULL DEFAULT '0' COMMENT '测试结果',
`test_result_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '测试结果备注',
`source` int(2) NOT NULL DEFAULT '0' COMMENT '消息来源(1:产品侧,2:商务侧,3:其他)',
`source_name` varchar(64) NOT NULL COMMENT '来源人姓名',
`can_communicate` int(2) NOT NULL DEFAULT '0' COMMENT '能否线下沟通效果(1:能,2:不能)',
`access_action` int(2) NOT NULL DEFAULT '0' COMMENT '接入意向',
`not_access_reason` int(2) NOT NULL DEFAULT '0' COMMENT '不接入原因',
`access_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '接入备注',
`schedule_time` datetime DEFAULT NULL COMMENT '反馈期限日期',
`have_objective` int(2) DEFAULT '0' COMMENT '是否填写指标',
`salesman` varchar(32) NOT NULL COMMENT '销售跟进人',
`is_schedule` int(2) NOT NULL DEFAULT '-1' COMMENT '打卡状态(-1:初始化默认值,0:未打卡,1:已打卡,2:无需打卡)',
`is_call` int(2) NOT NULL DEFAULT '-1' COMMENT '是否调用',
`call_time` datetime DEFAULT NULL COMMENT '调用时间',
`apply_time` datetime DEFAULT NULL COMMENT '申请时间',
`start_time` datetime DEFAULT NULL COMMENT '测试开始时间',
`return_time` datetime DEFAULT NULL COMMENT '返回时间(测试结束时间)',
`feedback_time` datetime DEFAULT NULL COMMENT '反馈时间',
`access_time` datetime DEFAULT NULL COMMENT '填写接入时间',
`bind_time` varchar(255) DEFAULT NULL COMMENT '绑定客户 ID 时间',
`date_total_90d` int(10) NOT NULL DEFAULT '0' COMMENT '近 90 天有调用总天数',
`number_total_90d` int(10) NOT NULL DEFAULT '0' COMMENT '近 90 天总调用量',
`is_open` int(2) DEFAULT '0' COMMENT '产品开通状态',
`number_total` int(10) DEFAULT '0' COMMENT '产品总调用量',
`sample_size` varchar(32) DEFAULT NULL COMMENT '样本量',
`is_delete` int(2) DEFAULT '0' COMMENT '逻辑删除(0:正常, 1: 已删除)',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`),
KEY `idx_apply_father` (`apply_id`,`father_id`) USING BTREE,
KEY `idx_apply_time` (`apply_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1765 DEFAULT CHARSET=utf8mb4 COMMENT='测试主产品申请表'

### 测试产品申请表 DDL

CREATE TABLE `pre_test_apply_product` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`apply_id` int(10) NOT NULL DEFAULT '0' COMMENT '申请表 ID',
`apply_father_id` int(10) NOT NULL COMMENT '申请主产品表 ID',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`apply_father_name` varchar(64) NOT NULL DEFAULT '' COMMENT '申请主产品名称',
`apply_product_name` varchar(64) NOT NULL DEFAULT '' COMMENT '申请子产品名称',
`test_status` int(2) NOT NULL DEFAULT '0' COMMENT '测试进度状态',
`test_result` int(2) NOT NULL DEFAULT '0' COMMENT '测试结果',
`test_result_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '测试结果备注',
`access_action` int(2) NOT NULL DEFAULT '0' COMMENT '接入意向',
`not_access_reason` int(2) NOT NULL DEFAULT '0' COMMENT '不接入原因',
`access_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '接入备注',
`is_schedule` int(2) NOT NULL DEFAULT '-1' COMMENT '打卡状态(-1:初始化默认值,0:未打卡,1:已打卡,2:无需打卡)',
`is_call` int(2) NOT NULL DEFAULT '-1' COMMENT '是否调用',
`call_time` varchar(255) DEFAULT NULL COMMENT '调用时间',
`apply_time` datetime DEFAULT NULL COMMENT '申请时间',
`start_time` varchar(255) DEFAULT NULL COMMENT '测试开始时间',
`return_time` varchar(255) DEFAULT NULL COMMENT '返回时间(测试结束时间)',
`bind_time` varchar(255) DEFAULT NULL COMMENT '绑定客户 ID 时间',
`date_total_90d` int(10) NOT NULL DEFAULT '0' COMMENT '近 90 天有调用总天数',
`number_total_90d` int(10) NOT NULL DEFAULT '0' COMMENT '近 90 天总调用量',
`is_open` int(2) DEFAULT '0' COMMENT '产品开通状态',
`number_total` int(10) DEFAULT '0' COMMENT '产品总调用量',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`),
KEY `idx_apply_product` (`apply_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9196 DEFAULT CHARSET=utf8mb4 COMMENT='测试产品申请表'

### 测试主产品指标表 DDL

CREATE TABLE `pre_test_apply_father_objective` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`apply_id` int(10) NOT NULL DEFAULT '0' COMMENT '申请表 ID',
`apply_father_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品申请表 ID',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`objective_id` int(10) NOT NULL DEFAULT '0' COMMENT '指标项 ID',
`target_value` varchar(64) NOT NULL DEFAULT '' COMMENT '目标值',
`result_value` varchar(64) NOT NULL DEFAULT '' COMMENT '结果值',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`),
KEY `idx_apply_father` (`apply_id`,`father_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=305 DEFAULT CHARSET=utf8mb4 COMMENT='测试主产品指标项列表'

### 测试产品指标表

CREATE TABLE `pre_test_apply_product_objective` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`apply_id` int(10) NOT NULL DEFAULT '0' COMMENT '申请表 ID',
`apply_product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品申请表 ID',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`objective_id` int(10) NOT NULL DEFAULT '0' COMMENT '指标项 ID',
`target_value` varchar(64) NOT NULL DEFAULT '' COMMENT '目标值',
`result_value` varchar(64) NOT NULL DEFAULT '' COMMENT '结果值',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`),
KEY `idx_apply_product` (`apply_id`,`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=142 DEFAULT CHARSET=utf8mb4 COMMENT='测试产品指标项列表'

## 售前业务表

### 售前业务产品表

    CREATE TABLE `pre_test_business_product` (

`id` int(10) NOT NULL AUTO_INCREMENT,
`business_product_name` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '业务产品名称',
`product_key` int(10) NOT NULL DEFAULT '0' COMMENT '业务产品 key',
`father_key` int(10) NOT NULL DEFAULT '0' COMMENT '业务主产品 key',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '系统产品 ID',
`fs_key` varchar(32) NOT NULL COMMENT '飞书枚举值',
`product_list` varchar(512) NOT NULL DEFAULT '' COMMENT '系统子产品 list',
`feedback_dim` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '反馈维度(0:本产品,1:子产品)',
`status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '可用状态(0:可用,1:禁用)',
`create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
PRIMARY KEY (`id`),
KEY `idx_product_key` (`product_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='业务产品表'

### 售前销售排期表

CREATE TABLE `pre_test_sales_schedule` (
`id` bigint(10) NOT NULL AUTO_INCREMENT,
`week_id` int(10) NOT NULL,
`schedule_detail` text,
`created_at` datetime DEFAULT NULL,
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8

## 售前测试结果表

### 邦秒配测试结果表

CREATE TABLE `pre_sales_result104` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
`pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
`rate_score` float(5,2) unsigned DEFAULT NULL COMMENT '测试命中占比（0-100）',
`result` varchar(100) NOT NULL DEFAULT '' COMMENT '测试结果',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦秒配产品测试结果'

### 邦信分详单版测试结果表

CREATE TABLE `pre_sales_result105` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
`pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
`rate_good` float(5,2) unsigned DEFAULT NULL COMMENT '好样本数据占比（0-100）',
`rate_sure` float(5,2) unsigned DEFAULT NULL COMMENT '催收命中占比（0-100）',
`rate_notsure` float(5,2) unsigned DEFAULT NULL COMMENT '疑似催收命中占比（0-100）',
`rate_accord` float(5,2) unsigned DEFAULT NULL COMMENT '只有主叫（0-100）',
`rate_passivity` float(5,2) unsigned DEFAULT NULL COMMENT '只有被叫（0-100）',
`rate_sure_five` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收 5 个以上（0-100）',
`rate_sure_ten` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收 10 个以上（0-100）',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦信分（详单版 V2）产品测试结果'

### 邦秒验测试结果表

CREATE TABLE `pre_sales_result200` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
`pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
`product_children_id` varchar(255) NOT NULL DEFAULT '' COMMENT '子产品 ID',
`rate_result` float(5,2) unsigned DEFAULT NULL COMMENT '查得率（0-100）',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='邦秒验产品测试结果'

### 邦信分快捷版测试结果表

CREATE TABLE `pre_sales_result210` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
`pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
`apply_time` varchar(50) DEFAULT NULL COMMENT '申请时间范围',
`detail_time` varchar(50) DEFAULT NULL COMMENT '详单时间范围',
`rate_sure` float(5,2) unsigned DEFAULT NULL COMMENT '催收命中占比（0-100）',
`rate_notsure` float(5,2) unsigned DEFAULT NULL COMMENT '疑似催收命中占比（0-100）',
`rate_accord` float(5,2) unsigned DEFAULT NULL COMMENT '只有主叫（0-100）',
`rate_passivity` float(5,2) unsigned DEFAULT NULL COMMENT '只有被叫（0-100）',
`rate_sure_five` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收 5 个以上（0-100）',
`rate_sure_ten` float(5,2) unsigned DEFAULT NULL COMMENT '确认催收 10 个以上（0-100）',
`operator` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '运营商（1 联通 2 电信 3 移动）',
`real_num` int(10) unsigned DEFAULT NULL COMMENT '实际测试数量',
`is_important` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否重要客户（0,1）',
`is_pay` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否付费测试（0,1）',
`cost` float(10,2) unsigned DEFAULT NULL COMMENT '测试成本',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE,
KEY `is_important` (`is_important`) USING BTREE,
KEY `is_pay` (`is_pay`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦信分（快捷版）产品测试结果'

### 邦企查测试结果表

CREATE TABLE `pre_sales_result401` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`ps_customer_id` varchar(40) NOT NULL DEFAULT '' COMMENT '售前客户 ID',
`pub_result_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公用测试数据 ID',
`start_time` int(10) unsigned DEFAULT NULL COMMENT '开始外呼时间',
`end_time` int(10) unsigned DEFAULT NULL COMMENT '结束外呼时间',
`rate_sure` float(5,2) unsigned DEFAULT NULL COMMENT '精准一致（0-100）',
`rate_notsure` float(5,2) unsigned DEFAULT NULL COMMENT '模糊一致（0-100）',
`rate_not` float(5,2) unsigned DEFAULT NULL COMMENT '不一致（0-100）',
`rate_undefined` float(5,2) unsigned DEFAULT NULL COMMENT '无法匹配（0-100）',
`result` varchar(100) NOT NULL DEFAULT '' COMMENT '测试结果',
PRIMARY KEY (`id`),
KEY `ps_customer_id` (`ps_customer_id`) USING BTREE,
KEY `pub_result_id` (`pub_result_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='邦企查产品测试结果'

### ER 图

```mermaid
erDiagram
    %% 各产品测试结果表
    pre_sales_result104 {
        int id PK
        string ps_customer_id
        int pub_result_id
        float rate_score
        string result
    }

    pre_sales_result105 {
        int id PK
        string ps_customer_id
        int pub_result_id
        float rate_good
        float rate_sure
        float rate_notsure
        float rate_accord
        float rate_passivity
        float rate_sure_five
        float rate_sure_ten
    }

    pre_sales_result200 {
        int id PK
        string ps_customer_id
        int pub_result_id
        string product_children_id
        float rate_result
    }

    pre_sales_result210 {
        int id PK
        string ps_customer_id
        int pub_result_id
        string apply_time
        string detail_time
        float rate_sure
        float rate_notsure
        int operator
        float cost
        int is_important
        int is_pay
    }

    pre_sales_result401 {
        int id PK
        string ps_customer_id
        int pub_result_id
        int start_time
        int end_time
        float rate_sure
        float rate_notsure
        float rate_not
        float rate_undefined
        string result
    }

    pre_test_apply_father ||--o{ pre_sales_result104 : "generates"
    pre_test_apply_father ||--o{ pre_sales_result105 : "generates"
    pre_test_apply_father ||--o{ pre_sales_result200 : "generates"
    pre_test_apply_father ||--o{ pre_sales_result210 : "generates"
    pre_test_apply_father ||--o{ pre_sales_result401 : "generates"
```

### 售前测试流程图

```mermaid
flowchart TD
    A[开始申请] --> B{是否有主产品}
    B -->|是| C[创建主产品申请]
    B -->|否| D[选择主产品]
    D --> C
    C --> E[设置测试指标]
    E --> F[创建子产品申请]
    F --> G[设置子产品指标]
    G --> H{是否需要测试}
    H -->|是| I[进行产品测试]
    H -->|否| J[跳过测试]
    I --> K[记录测试结果]
    J --> L[更新申请状态]
    K --> L
    L --> M{是否完成}
    M -->|是| N[结束申请]
    M -->|否| O[继续测试]
    O --> H
```

### 售前管理状态转换图

```mermaid
stateDiagram-v2
    [*] --> 申请中
    申请中 --> 测试中: 审核通过
    申请中 --> 已驳回: 审核不通过
    测试中 --> 测试完成: 完成测试
    测试中 --> 测试异常: 测试出错
    测试完成 --> 待反馈: 等待客户反馈
    待反馈 --> 已接入: 客户同意接入
    待反馈 --> 已放弃: 客户放弃接入
    测试异常 --> 测试中: 重新测试
    已驳回 --> 申请中: 重新申请
    已接入 --> [*]
    已放弃 --> [*]
```
