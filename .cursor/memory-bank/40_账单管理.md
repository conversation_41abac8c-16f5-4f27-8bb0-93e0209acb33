## 账单管理

-- 收入账单表
bill_customer_income_v2 -- 客户收入账单表 V2
bill_product_income_v2 -- 产品收入账单表 V2
bill_income_month_data -- 收入账单月中间表

-- 成本账单表
bill_cost_v2 -- 成本账单表 V2
bill_interface -- 接口账单表
bill_operator_month_check -- 运营商月账单对账表

-- 朴道账单表
bill_pudao_cost -- 朴道成本对账表
bill_pudao_income -- 朴道收入对账表

-- 账单管理表
rerun_bill_record -- 账单重跑记录表
bill_operator_month_logs -- 账单运营商月日志表

### 收入账单表

#### 客户收入账单表 V2 DDL

CREATE TABLE `bill_customer_income_v2` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`father_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父产品 ID',
`product_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '子产品 IDS（多个产品 ID 以,进行分割）',
`operator` varchar(255) NOT NULL DEFAULT '' COMMENT '运营商',
`together_call_number` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '同时调用子产品数量',
`config_price_customer_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联计费配置表',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日期（Ymd）',
`price` decimal(10,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '单价',
`number` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费用量',
`money` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '金额',
`money_agent` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT 'source 为征信机构的计算后金额',
`money_original` decimal(15,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '金额',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '账单生成时间',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `father_id` (`father_id`) USING BTREE,
KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14659 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户收入账单表'

#### 产品收入账单表 V2 DDL

CREATE TABLE `bill_product_income_v2` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`father_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父产品 ID',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '子产品 ID',
`call_product_id` int(11) NOT NULL DEFAULT '0' COMMENT '调用产品 id',
`operator` varchar(255) NOT NULL DEFAULT '' COMMENT '运营商',
`bill_customer_income_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户收入账单 ID',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日期（Ymd）',
`number` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费用量',
`money` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '金额',
`money_finance` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '金融收入',
`money_agent` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT 'source 为征信机构的计算后金额',
`money_original` decimal(15,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '金额',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '账单生成时间',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`) USING BTREE,
KEY `father_id` (`father_id`) USING BTREE,
KEY `product_id` (`product_id`) USING BTREE,
KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=27182 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='产品收入账单表'

#### 收入账单月中间表 DDL

CREATE TABLE `bill_income_month_data` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`customer_id` char(32) NOT NULL DEFAULT '0' COMMENT '客户 id',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`product_id` int(11) NOT NULL DEFAULT '0' COMMENT '子产品 ID',
`call_product_id` int(11) NOT NULL DEFAULT '0' COMMENT '调用子产品 ID',
`operator` varchar(255) NOT NULL DEFAULT '' COMMENT '运营商',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`number` int(11) NOT NULL DEFAULT '0' COMMENT '计费用量',
`money` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '金额',
`month` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '月份（Ym）',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生成时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `customer_id` (`customer_id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `call_product_id` (`call_product_id`) USING BTREE,
KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8656 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='收入账单月中间表'

### 成本账单表

#### 成本账单表 V2 DDL

CREATE TABLE `bill_cost_v2` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`config_price_interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '计费配置 ID',
`apikey` varchar(100) NOT NULL DEFAULT '',
`operator` varchar(20) NOT NULL DEFAULT '' COMMENT '运营商',
`encrypt` varchar(20) NOT NULL DEFAULT '' COMMENT '加密方式',
`product_id` int(11) NOT NULL DEFAULT '0' COMMENT '子产品 id',
`date` int(11) NOT NULL DEFAULT '0' COMMENT '日期',
`number` int(11) NOT NULL DEFAULT '0' COMMENT '计费用量',
`money` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '金额',
`money_original` decimal(15,6) unsigned NOT NULL DEFAULT '0.000000' COMMENT '金额',
`aes_encrypt_number` varchar(255) NOT NULL DEFAULT '' COMMENT '加密后的数量',
`aes_encrypt_money` varchar(255) NOT NULL DEFAULT '' COMMENT '加密后的金额',
`create_time` int(11) NOT NULL DEFAULT '0',
`update_time` int(11) NOT NULL DEFAULT '0',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`),
KEY `apikey` (`apikey`) USING BTREE,
KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34507 DEFAULT CHARSET=utf8

#### 接口账单表 DDL

CREATE TABLE `bill_interface` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`iid` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`apikey` varchar(100) NOT NULL DEFAULT '',
`operator` char(15) NOT NULL DEFAULT '' COMMENT '运营商',
`encrypt` char(15) NOT NULL DEFAULT '' COMMENT '加密方式',
`product_id` int(11) NOT NULL DEFAULT '0' COMMENT '子产品 id',
`date` int(11) NOT NULL DEFAULT '0' COMMENT '日期',
`number` int(11) NOT NULL DEFAULT '0' COMMENT '计费用量',
`money` decimal(10,0) NOT NULL DEFAULT '0' COMMENT '金额',
`create_time` int(11) NOT NULL DEFAULT '0',
`update_time` int(11) NOT NULL DEFAULT '0',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8

#### 运营商月账单对账表 DDL

CREATE TABLE `bill_operator_month_check` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`operator_pid` varchar(150) NOT NULL DEFAULT '0' COMMENT '运营商侧产品 id',
`month` int(10) NOT NULL DEFAULT '0' COMMENT '月份',
`bill_method` varchar(15) NOT NULL COMMENT 'total/valid/success - 总量/查得量/成功量',
`total` int(11) NOT NULL DEFAULT '0' COMMENT '总调用量',
`success` int(11) NOT NULL DEFAULT '0' COMMENT '成功量',
`valid` int(11) NOT NULL DEFAULT '0' COMMENT '查得量',
`cost` decimal(15,4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '成本',
`operator_total` int(11) NOT NULL DEFAULT '0' COMMENT '运营商侧总调用量',
`operator_success` int(11) NOT NULL DEFAULT '0' COMMENT '运营商侧成功量',
`operator_valid` int(11) NOT NULL DEFAULT '0' COMMENT '运营商侧查得量',
`operator_minimum_fee` decimal(15,4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '运营商侧保底费用',
`operator_other_fee` decimal(15,4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '运营商侧其他费用',
`operator_cost` decimal(15,4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '运营商侧最终成本',
`operator_product_count` int(11) NOT NULL DEFAULT '0' COMMENT '运营商渠道产品调用量',
`operator_product_price` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '运营商侧渠道产品单价',
`operator_product_cost` decimal(15,4) NOT NULL DEFAULT '0.0000' COMMENT '运营商渠道产品消耗金额(成本)',
`create_time` int(11) NOT NULL DEFAULT '0',
`update_time` int(11) NOT NULL DEFAULT '0',
`status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '状态 0 未对账 1 已确认 2 待确认',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=120 DEFAULT CHARSET=utf8 COMMENT='运营商月账单'

### 朴道账单表

#### 朴道成本对账表 DDL

CREATE TABLE `bill_pudao_cost` (
`id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '朴道对账单成本记录 ID',
`month` mediumint(8) unsigned NOT NULL COMMENT '调用月份，格式：年月 6 位数字',
`apikey` varchar(32) NOT NULL COMMENT '账号',
`product_id` int(11) unsigned NOT NULL COMMENT '子产品 ID',
`interface_id` int(11) unsigned NOT NULL COMMENT '数据源接口 ID',
`operator` varchar(255) NOT NULL COMMENT '运营商',
`channel_id` int(10) unsigned NOT NULL COMMENT '数据源渠道 ID',
`channel_label` varchar(255) NOT NULL COMMENT '数据源名称',
`interface_operator_name` varchar(255) NOT NULL COMMENT '数据源产品名称-运营商',
`valid` int(11) unsigned NOT NULL COMMENT '查得量',
`number` int(11) unsigned NOT NULL COMMENT '最终付费次数',
`price` decimal(15,6) NOT NULL COMMENT '单价（元）',
`money` decimal(15,6) NOT NULL COMMENT '结算金额（元）',
`created_at` int(10) unsigned NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
UNIQUE KEY `monthly` (`month`,`apikey`,`product_id`,`interface_id`,`operator`),
KEY `apikey` (`apikey`)
) ENGINE=InnoDB AUTO_INCREMENT=454 DEFAULT CHARSET=utf8 COMMENT='朴道对账单成本'

#### 朴道收入对账表 DDL

CREATE TABLE `bill_pudao_income` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '朴道对账单收入自增 ID',
`month` mediumint(8) unsigned NOT NULL COMMENT '调用月份，格式：年月 6 位数',
`customer_id` varchar(255) NOT NULL COMMENT '客户 ID',
`company_name` varchar(255) NOT NULL COMMENT '公司全称',
`short_name` varchar(255) NOT NULL COMMENT '公司简称',
`apikey` varchar(32) NOT NULL COMMENT '客户账号',
`product_name` varchar(255) NOT NULL COMMENT '合同产品名',
`product_id` int(11) NOT NULL COMMENT '子产品 ID，科技公司产品唯一标识',
`operator` varchar(255) NOT NULL COMMENT '运营商',
`accord` tinyint(3) unsigned NOT NULL COMMENT '调用方式，计费依据（1-成功调用量，2-查得量）',
`valid` int(11) unsigned NOT NULL COMMENT '结算周期总查得量',
`number` int(11) unsigned NOT NULL COMMENT '计费次数',
`price` decimal(15,6) NOT NULL COMMENT '朴道对客单价（元）',
`money` decimal(15,6) NOT NULL COMMENT '朴道对客结算金额（元）',
`created_at` int(10) unsigned NOT NULL COMMENT '创建时间',
PRIMARY KEY (`id`),
UNIQUE KEY `monthly` (`month`,`apikey`,`product_id`,`operator`),
KEY `apikey` (`apikey`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8 COMMENT='朴道对账单收入'

### 账单管理表

#### 账单重跑记录表 DDL

CREATE TABLE `rerun_bill_record` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 id，为 all 时代表所有客户',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`days` int(10) NOT NULL DEFAULT '0' COMMENT '生成多少日的账单',
`start_date` int(10) NOT NULL DEFAULT '0' COMMENT '开始日期 Ymd 格式',
`end_date` int(10) NOT NULL DEFAULT '0' COMMENT '结束日期 Ymd 格式',
`type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '重跑账单类型:1 营收账单、2 成本账单',
`remarks` varchar(255) NOT NULL DEFAULT '' COMMENT '备注原因',
`update_at` int(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
`create_at` int(10) NOT NULL DEFAULT '0' COMMENT '创建时间',
`is_rerun_month_data` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否重跑月数据',
`admin` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=240 DEFAULT CHARSET=utf8 COMMENT='账单重跑记录表'

### 账单运营商月日志表 DDL

CREATE TABLE `bill_operator_month_logs` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`month` int(10) NOT NULL DEFAULT '0' COMMENT '月份',
`result` text NOT NULL COMMENT '接口返回结果',
`create_time` int(11) NOT NULL DEFAULT '0',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8 COMMENT='运营商月账单推送日志'

#### ER 图

```mermaid
erDiagram
    bill_customer_income_v2 ||--o{ bill_product_income_v2 : "generates"
    bill_customer_income_v2 ||--o{ bill_income_month_data : "summarizes"
    bill_cost_v2 ||--o{ bill_interface : "records"
    bill_cost_v2 ||--o{ bill_operator_month_check : "reconciles"

    bill_customer_income_v2 {
        int id PK
        string apikey
        int father_id
        string product_ids
        decimal money
        decimal money_agent
        int source
    }

    bill_product_income_v2 {
        int id PK
        string apikey
        int father_id
        int product_id
        decimal money
        decimal money_finance
    }

    bill_cost_v2 {
        int id PK
        int interface_id
        string apikey
        int product_id
        decimal money
        int source
    }

    bill_pudao_income {
        int id PK
        string customer_id
        string apikey
        int product_id
        decimal price
        decimal money
    }

    bill_pudao_cost {
        int id PK
        string apikey
        int product_id
        int interface_id
        decimal price
        decimal money
    }
```

1. bill_customer_income_v2（客户账单汇总表）

作用：客户维度的总收入账单（按月/日），为主账单表
字段说明：
• apikey: 区分客户账号
• product_ids: 本期涉及产品清单
• money: 实收金额
• money_agent: 分销返佣金额
• source: 来源渠道或统计来源标识

⸻

2. bill_product_income_v2（产品级账单明细）

作用：某个客户在某个产品上的收入细分记录
关联字段：
• father_id：对齐主账单 bill_customer_income_v2.father_id
• product_id：区分收入来源的产品
• money：实际计入收入
• money_finance：财务核定金额

3. bill_income_month_data（月维度客户账单汇总）

作用：按客户 + 月度维度聚合账单
• 多用于趋势分析、月度结算和对账系统对齐
• 来源于 bill_customer_income_v2 的周期性聚合 4. bill_cost_v2（接口调用成本账单）

作用：平台每笔接口调用成本（按接口、产品、客户）
字段说明：
• interface_id: 对应供应商接口
• product_id: 产品维度分摊
• money: 成本金额 5. bill_operator_month_check（运营商月度对账）

作用：从平台向运营商结算成本的月度账单
与 bill_cost_v2 聚合关联，进行月度对账核算。 6. bill_pudao_income（朴道分销收入）
• 表示通过朴道渠道产生的客户收入
• 用于与平台分账核对或代理成本收益

字段关键：
• customer_id: 所属客户
• price: 售价
• money: 总金额（= price × 调用量）

⸻

7. bill_pudao_cost（朴道接口成本）
   • 平台向朴道对接产生的调用成本记录
   • 作用与 bill_cost_v2 类似，但专属朴道通道

字段关键：
• interface_id: 朴道接口 ID
• price: 单次成本
• money: 总成本金额

bill_customer_income_v2 → bill_product_income_v2
主账单 → 产品明细
bill_customer_income_v2 → bill_income_month_data
客户收入账单 → 月汇总视图
bill_cost_v2 → bill_interface
成本账单 → 接口维度调用日志
bill_cost_v2 → bill_operator_month_check
成本账单 → 运营商月对账
bill_pudao_income, bill_pudao_cost 专属渠道账单维度

#### 创建账单处理流程图

```mermaid
graph TD
    A["数据采集"] --> B["账单生成"]
    B --> C["收入账单"]
    B --> D["成本账单"]

    C --> E["客户收入账单"]
    C --> F["产品收入账单"]
    C --> G["月度汇总"]

    D --> H["接口成本账单"]
    D --> I["运营商对账"]
    D --> J["渠道成本账单"]

    E --> K["账单核对"]
    F --> K
    H --> K
    I --> K
    J --> K

    K --> L["账单确认"]
    L --> M["账单重跑"]
    L --> N["账单归档"]

    M --> B
    N --> O["结算完成"]
```

#### 创建账单分类层次图

```mermaid
graph TD
    A["账单类型"] --> B["收入账单"]
    A --> C["成本账单"]

    B --> D["客户收入账单V2"]
    B --> E["产品收入账单V2"]
    B --> F["朴道收入账单"]

    C --> G["成本账单V2"]
    C --> H["接口账单"]
    C --> I["朴道成本账单"]
    C --> J["运营商月账单"]

    D --> K["电话邦收入"]
    D --> L["朴道收入"]
    D --> M["百行收入"]

    G --> N["电话邦成本"]
    G --> O["朴道成本"]
    G --> P["百行成本"]

    J --> Q["总量计费"]
    J --> R["查得量计费"]
    J --> S["成功量计费"]
```
