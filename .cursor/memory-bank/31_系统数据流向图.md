## 系统数据流向图核心组成

### 数据流向图（Mermaid 源码）

```mermaid
graph TD
    %% 外部数据源层
    A1[客户API调用] --> B1[API Gateway<br/>认证/限流/路由]
    A2[第三方API<br/>邦秒验/邦企查/邦信分] --> B1
    A3[上游统计接口] --> B1
    A4[移动端调用] --> B1
    A5[PC端管理] --> B1

    %% 接入层处理
    B1 --> C1[身份认证<br/>Token/Cookie验证]
    B1 --> C2[权限校验<br/>产品权限/数据权限]
    B1 --> C3[参数验证<br/>业务逻辑校验]

    %% 应用层数据流
    C1 --> D1[统计数据收集]
    C2 --> D2[账单数据处理]
    C3 --> D3[监控告警处理]
    C1 --> D4[客户管理数据]
    C2 --> D5[渠道管理数据]

    %% 数据处理与转换
    D1 --> E1[统计数据入库Job<br/>StatisticsCustomerUsageJob]
    D1 --> E2[接口统计Job<br/>StatisticsInterfaceUsageJob]
    D2 --> E3[账单生成Job<br/>CreateBillCustomerIncomeJob]
    D2 --> E4[成本账单Job<br/>CreateBillCostJob]
    D3 --> E5[预警处理Job<br/>ProductInvokedAmountWarningJob]
    D4 --> E6[客户数据同步Job]
    D5 --> E7[渠道数据更新Job]

    %% 消息队列层
    E1 --> F1[Redis队列<br/>redis_backend]
    E2 --> F1
    E3 --> F1
    E4 --> F1
    E5 --> F1
    E6 --> F1
    E7 --> F1

    %% 缓存层处理
    F1 --> G1[Redis缓存<br/>业务数据缓存]
    D1 --> G2[Redis映射缓存<br/>apikey/产品/客户映射]
    D2 --> G3[Redis限额缓存<br/>产品调用限额]
    D3 --> G4[Redis预警缓存<br/>告警状态标记]

    %% 数据存储层
    F1 --> H1[MySQL主库<br/>yulore_finance]
    F1 --> H2[MySQL系统库<br/>crs]
    F1 --> H3[MySQL监控库<br/>alarm]
    F1 --> H4[MySQL测试库<br/>pre_test]
    F1 --> H5[MongoDB<br/>backend]
    F1 --> H6[ClickHouse<br/>ods_financial_log]

    %% 具体数据表分流
    H1 --> I1[账单表<br/>bill_customer_income<br/>bill_product_income<br/>bill_cost]
    H1 --> I2[统计表<br/>statistics_customer_usage<br/>statistics_interface_usage]
    H1 --> I3[配置表<br/>config_price_customer<br/>products<br/>accounts]
    H1 --> I4[客户表<br/>customers<br/>customer_groups]

    H2 --> I5[系统表<br/>users<br/>departments<br/>permissions]

    H3 --> I6[监控表<br/>log_product<br/>log_channel<br/>config_ant_financial_batch]

    H5 --> I7[日志集合<br/>api_log_YYYYMM<br/>logs_bill_customer_income<br/>invoked_log_YYYYMM]
    H5 --> I8[统计集合<br/>product_invoked<br/>bill_month]

    H6 --> I9[财务日志<br/>实时统计分析<br/>大数据报表]

    %% 数据输出层
    I1 --> J1[账单报表<br/>日/月/年账单]
    I2 --> J2[统计报表<br/>调用量/成功率统计]
    I3 --> J3[配置管理<br/>价格/权限配置]
    I6 --> J4[监控告警<br/>邮件/微信/飞书通知]
    I7 --> J5[操作日志<br/>审计追踪]
    I9 --> J6[数据分析<br/>BI报表/数据洞察]

    %% 外部集成
    J1 --> K1[财务系统<br/>发票管理/回款确认]
    J4 --> K2[通知系统<br/>邮件/微信/飞书]
    J2 --> K3[客户系统<br/>API调用方]
    J6 --> K4[第三方BI<br/>数据可视化]

    %% 数据流反馈
    K1 --> L1[收款单数据]
    K2 --> L2[告警确认状态]
    K3 --> L3[客户调用反馈]

    L1 --> H1
    L2 --> H3
    L3 --> G1

    %% 样式定义
    classDef dataSource fill:#e1f5fe
    classDef processing fill:#fff3e0
    classDef storage fill:#e8f5e8
    classDef output fill:#fce4ec
    classDef queue fill:#f3e5f5

    class A1,A2,A3,A4,A5 dataSource
    class C1,C2,C3,D1,D2,D3,D4,D5 processing
    class H1,H2,H3,H4,H5,H6,I1,I2,I3,I4,I5,I6,I7,I8,I9 storage
    class J1,J2,J3,J4,J5,J6,K1,K2,K3,K4 output
    class F1,G1,G2,G3,G4,E1,E2,E3,E4,E5,E6,E7 queue
```

### 1. **数据输入层**（蓝色）

- **客户 API 调用**：通过各种产品 API 接入
- **第三方接口**：邦秒验、邦企查、邦信分等产品
- **上游统计接口**：外部系统统计数据推送
- **管理端访问**：PC 端管理和移动端查询

### 2. **数据处理层**（橙色）

- **身份认证**：Token/Cookie 验证，支持 PC 端和移动端
- **权限校验**：9 种角色权限，4 级数据权限
- **业务逻辑处理**：统计、账单、监控、客户、渠道数据处理

### 3. **异步处理层**（紫色）

- **消息队列**：基于 Redis 的多队列任务处理
- **Job 任务**：统计入库、账单生成、告警处理等异步任务
- **缓存机制**：Redis 多库缓存，包括映射缓存、限额缓存、预警缓存

### 4. **数据存储层**（绿色）

- **MySQL 集群**：主业务库、系统库、监控库、测试库
- **MongoDB**：日志存储，按月分表
- **ClickHouse**：大数据分析存储
- **Redis**：缓存和队列存储

### 5. **数据输出层**（粉色）

- **报表系统**：账单、统计、配置管理
- **告警系统**：邮件、微信、飞书通知
- **第三方集成**：财务系统、BI 系统、客户系统

### 6. **关键数据流特点**

1. **多数据源输入**：支持 API 调用、第三方接口、管理端等多种数据输入
2. **异步处理机制**：大量使用 Job 队列进行异步数据处理，避免阻塞
3. **多层缓存架构**：Redis 多库分离，不同业务使用不同缓存库
4. **多存储引擎**：MySQL 处理业务数据，MongoDB 存储日志，ClickHouse 进行大数据分析
5. **实时告警反馈**：监控数据实时处理并推送告警
6. **数据闭环管理**：从数据输入到输出形成完整的业务闭环

这个数据流向图准确反映了当前系统的实际架构和数据处理流程，展现了一个复杂的金融管理系统的完整数据生命周期。
