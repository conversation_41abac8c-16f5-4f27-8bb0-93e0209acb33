## **审批管理功能架构梳理**

### **一、业务背景与价值**

#### **1.1 业务场景**

- **风险控制**：对关键业务操作进行审批控制，降低操作风险
- **权限管理**：实现分级审批，确保操作权限的合规性
- **流程规范**：标准化业务流程，提升操作透明度和可追溯性
- **多系统集成**：支持内部审批和外部飞书审批的统一管理

#### **1.2 业务价值**

- **合规保障**：确保重要操作符合内控要求
- **效率提升**：自动化审批流程，减少人工干预
- **风险防控**：建立多层审批机制，防范操作风险

### **二、系统架构设计**

#### **2.1 双轨制审批架构**

**内部审批系统**：

- 基于 Laravel 的自建审批流
- 支持本系统内部业务审批
- 数据存储在 MySQL 中

**外部审批系统（飞书）**：

- 集成飞书审批 API
- 支持复杂表单和多级审批
- 实现与内部系统的数据同步

### **五、飞书审批集成架构**

#### **5.1 飞书审批适配器**

**多种审批类型支持：**

```110:173:app/Repositories/FeishuRepository.php
/**
 * 创建产品过期审批
 *
 * @throws Exception
 */
public function create_product_expire_approval($user_id, $customer_name, $expire_time, $ext_time, $products, $count) {
    $approval_code = 'F3B0E1F4-C8E2-4C9A-9B7A-C8CABAA75ED9';

    // 表单字段映射和数据处理
    $form = [
        [
            //延期次数
            "id"    => "widget17090903048580001",
            'type'  => 'radioV2',
            'value' => $delay_times_map[$count],
        ],
        // ... 其他字段配置
    ];

    return $this->feishu_obj->create_approval($data);
}
```

#### **5.2 审批状态同步机制**

**定时同步任务：**

```39:77:app/Console/Commands/PullDatas/PullFeishuPublicityApprovalProcess.php
public function handle()
{
    //获取近一周且状态为审批中的公出审批数据
    $start_date = date('Ymd', strtotime('-7 days'));
    $start_time = strtotime($start_date);
    $list = $this->getApprovelProcessData($start_time)->toArray();
    if(empty($list)){
        return false;
    }

    try{
        $all_status = array_keys(FeishuPublicityApprovalProcess::$status);
        $this->feishuRep = new FeishuRepository();
        $this->feishuDaKaRep = new FeishuRepository('feishu_daka');
        foreach ($list as $item){
            $id = $item['id'];
            $instance_id = $item['process_instance_id'];
            $info = $this->feishuRep->get_publicity_approval_instance_info($instance_id);
            usleep(200000);
            if(isset($info['code']) && $info['code'] == 0 && in_array($info['data']['status'], $all_status)){
                $this->updateApprovalProcess($id, $info['data']['status'], $item);
            }
        }
    }catch (\Exception $e){
        $this->sendException('', $e->getMessage(), '拉取飞书公出审批异常');
    }
}
```

### **六、业务模块支持**

#### **6.1 支持的审批类型**

**内部审批类型：**

```17:25:app/Models/Approval.php
const URL_BXF_STRATEGY_ADD         = 'bxf_strategy/add';
const URL_BXF_STRATEGY_EDIT        = 'bxf_strategy/edit';
const URL_BXF_STRATEGY_DEL         = 'bxf_strategy/del';
const URL_BXF_STRATEGY_CONFIG_ADD  = 'bxf_strategy/config/add';
const URL_BXF_STRATEGY_CONFIG_EDIT = 'bxf_strategy/config/edit';
const URL_BXF_STRATEGY_CONFIG_DEL  = 'bxf_strategy/config/del';
const URL_CONTRACT_FILE_DOWNLOAD   = 'contract/file_download';
const URL_BILL_EXPEND_SAVE         = 'bill/expend_save';
```

**飞书审批类型：**

```12:30:app/Define/PreTestManage.php
/**
 * [售前测试流程-新版] 审批定义模板code
 */
const APPROVAL_CODE_TEST = 'A743783F-3E0D-4A70-B1CF-254E9EE59E9A';
/**
 * [售前测试&测试账号申请] 审批定义模板code
 */
const APPROVAL_CODE_TEST_WITH_ACCOUNT = '37E23EE8-E880-4A8B-A291-C00E50B54A23';
/**
 * [产品建模&定制申请] 审批定义模板code
 */
const APPROVAL_CODE_TEST_WITH_MODEL = 'BD65205C-84C4-437B-8BE9-ABDED8451972';
```

#### **6.2 审批工单转换器**

**工厂模式实现：**

```20:48:app/Models/PreTestManage/Approval/ApprovalTransfer.php
public static function makeApproval($aData = [], BusinessProductData $oBusinessProduct = null) {
    $sApprovalCode = $aData['approval_code'] ?? '';
    switch ($sApprovalCode) {
        case PreTestManage::APPROVAL_CODE_TEST_WITH_ACCOUNT : // 售前测试&测试账号申请
        case PreTestManage::APPROVAL_CODE_TEST_WITH_MODEL : // 产品建模&定制申请
            $oInstance = new PreTest($aData, $oBusinessProduct);
        break;
        case PreTestMonitor::APPROVAL_CODE_CALL_STABLE : // 调用量级稳定
            $oInstance = new CallStable($aData);
            break;
        case PreTestMonitor::APPROVAL_CODE_GRAYSCALE : // 灰度上线时间
            $oInstance = new Grayscale($aData);
            break;
        case PreTestMonitor::APPROVAL_CODE_EXPECT_ONLINE :// 超出预计上线时间
            $oInstance = new ExpectOnline($aData);
            break;
        case AccountApplyManage::APPROVAL_CODE_TEST_WITH_APPLY_ACCOUNT:
            $oInstance = new AccountApply($aData);
            break;
        default:
            // 审批工单基类
            $oInstance = new Approval([]);
            break;
    }

    return $oInstance;
}
```

### **七、架构特点与优势**

#### **7.1 架构优势**

- **双轨制设计**：内部审批+外部审批并行，满足不同业务需求
- **可扩展性**：支持新增审批类型和审批规则
- **状态同步**：实时同步外部审批状态，保证数据一致性
- **权限控制**：细粒度权限控制，支持多级审批

#### **7.2 技术特点**

- **策略模式**：不同审批类型采用不同处理策略
- **工厂模式**：统一的审批工单创建入口
- **异步处理**：定时任务同步审批状态，避免阻塞
- **异常处理**：完善的异常处理和日志记录机制

#### **7.3 业务支撑能力**

- **多业务场景**：支持产品管理、合同管理、账单管理等多种业务审批
- **灵活配置**：审批规则可配置，支持业务快速变化
- **数据追溯**：完整的审批记录，支持审计和追溯
- **集成能力**：与飞书、CRM 等外部系统无缝集成

这套审批管理系统体现了成熟的企业级架构设计思想，既保证了业务的灵活性，又确保了系统的稳定性和可维护性。
