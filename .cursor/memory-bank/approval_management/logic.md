## **审批逻辑完整梳理**

### **一、审批触发逻辑**

#### **1.1 触发条件判断**

```php
// 特殊消耗超过10000元触发审批
if (10000 <= $value['money']) {
    $this->expendToApproval($value, $user_cookie);
    continue;
}
```

**触发场景：**

- **金额阈值**：特殊消耗金额 ≥10000 元
- **敏感操作**：策略配置的增删改
- **权限控制**：合同文件下载
- **业务规则**：其他需要审批的业务操作

#### **1.2 审批单创建逻辑**

**核心创建方法：**

```262:284:app/Http/Repository/ApprovalRepository.php
public static function addApproval($customer_id,$account_id,$product_id,$uri,$data,$apply_content,$user_cookie): bool {
    $user_name = Func::getUserNameFromCookie($user_cookie);

    $now = time();
    $flow_no = self::getFlowNo();

    $appr['flow_no']       = $flow_no;
    $appr['applicant']     = $user_name;
    $appr['approver']      = '';//审批人
    $appr['op_type']       = $uri;
    $appr['status']        = self::STATUS_APPROVAL;
    $appr['apply_content'] = $apply_content;//比较差异的文本 这个值需要自定义
    $appr['customer_id']   = $customer_id;
    $appr['account_id']    = $account_id;
    $appr['product_id']    = $product_id;
    $appr['create_at']     = $now;
    $appr['update_at']     = $now;

    Approval::insert($appr);
```

**流水号生成规则：**

```306:310:app/Http/Repository/ApprovalRepository.php
private static function getFlowNo(): string {
    [$t1, $t2] = explode(' ', microtime());
    return  Func::randString(11) . (float) sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
}
```

### **二、审批状态管理**

#### **2.1 状态定义**

```14:18:app/Http/Repository/ApprovalRepository.php
//1审批中，2审批通过，3审批驳回，4撤销
const STATUS_APPROVAL = 1;
const STATUS_PASS     = 2;
const STATUS_REJECT   = 3;
const STATUS_CANCEL   = 4;
```

#### **2.2 状态流转逻辑**

**状态流转规则：**

- **初始状态**：STATUS_APPROVAL（审批中）
- **终态**：STATUS_PASS（通过）、STATUS_REJECT（驳回）、STATUS_CANCEL（撤销）
- **权限控制**：只有申请人可以撤销，审批人可以通过/驳回

### **三、权限控制逻辑**

#### **3.1 审批权限验证**

**基于配置的权限控制：**

```77:85:app/Http/Repository/ApprovalRepository.php
if($tab_type == 'approver'){//审批人
    //获取有权审批的操作类型
    $op_types = ApprovalConfig::getNode($user_name);
    if(!empty($op_type) && in_array($op_type,$op_types)){
        $parmas[] = ['op_type','=', $op_type];
    }else{
        $parmas[] = ['op_type','in',$op_types];
    }
    $is_set_op_type = true;
}
```

#### **3.2 撤销权限控制**

**申请人撤销验证：**

```155:160:app/Http/Repository/ApprovalRepository.php
if($params['status'] == self::STATUS_CANCEL){
    $info = Approval::find($params['id']);
    if($sysName != $info['applicant']){
        throw new Exception("操作异常!");
    }
```

### **四、审批处理逻辑**

#### **4.1 双路径处理机制**

**BMP 系统 vs 内部系统：**

```130:148:app/Http/Repository/ApprovalRepository.php
// 审核通过 bmp请求重新处理 finance-manage-api调用相应审批通过方法
if($params['status'] == self::STATUS_PASS){
    //需要请求bmp的审批
    if(in_array($requestInfo['url'], $bmp_urls)){
        // 先处理请求
        $yulore_finance_domain = config('approval.yulore_finance_domain');
        $url = $yulore_finance_domain . '/' . $requestInfo['url'];

        $data['approval_token'] = $params['id'];
        $res                    = $this->postData($url, $data);
    }else{
        $res = $this->finance_manage_api_deal($requestInfo['url'],$data);
    }

    ApprovalRequest::where('flow_no', $info['flow_no'])->update(['op_result' => $res]);
}
```

#### **4.2 业务处理路由**

**基于 URL 的业务分发：**

```320:348:app/Http/Repository/ApprovalRepository.php
private function finance_manage_api_deal($url, $data): bool {
    switch ($url){
        case Approval::URL_BXF_STRATEGY_ADD://邦信分添加策略
            $res = BxfStrategyRepository::approval_deal_add($data);
            break;
        case Approval::URL_BXF_STRATEGY_EDIT://邦信分添加策略
            $res = BxfStrategyRepository::approval_deal_edit($data);
            break;
        case Approval::URL_BXF_STRATEGY_DEL://邦信分删除策略
            $res = BxfStrategyRepository::approval_deal_del($data);
            break;
        case Approval::URL_CONTRACT_FILE_DOWNLOAD://合同管理 文件下载
            $res = ContractRepositorie::deal_download_permission($data);
            break;
        case Approval::URL_BILL_EXPEND_SAVE:
            $res = BillV3Repository::saveExpendByApproval($data);
            break;
        default:
            break;
    }
    return $res;
}
```

### **五、具体业务审批逻辑**

#### **5.1 特殊消耗审批逻辑**

**审批申请生成：**

```3159:3170:app/Http/Repository/BillV3Repository.php
private function expendToApproval($aData, $sUserCookie): bool {
    $sCustomerId = $aData['customer_id'];
    $aCustomer = Customer::getCustomerInfo($sCustomerId);
    // ... 获取产品信息和格式化数据

    $sApplyContent = sprintf('特殊消耗, 客户:<%s>, 主产品:「%s」, 子产品:「%s」, 来源: %s, 调整月份: %s, 原因:「%s」, 类型: 「%s」, 金额: 「%s」',
         $aCustomer['name'], $aProductMap[$iFatherId] ?? '', $aProductMap[$iProductId] ?? '', $sSourceName, $sStartDate, $sRemark, $sType, $sMoney);

    return ApprovalRepository::addApproval($sCustomerId,'',$iProductId,Approval::URL_BILL_EXPEND_SAVE, $aData, $sApplyContent, $sUserCookie);
}
```

**审批通过处理：**

```3178:3180:app/Http/Repository/BillV3Repository.php
public static function saveExpendByApproval($aData) {
    return CustomerExpend::addData($aData);
}
```

#### **5.2 策略配置审批逻辑**

**策略添加审批：**

```245:253:app/Http/Repository/BxfStrategyRepository.php
$apply_content = $this->add_diff($strategy_data);

//添加到审批
return ApprovalRepository::addApproval('','','',Approval::URL_BXF_STRATEGY_ADD,$strategy_data,$apply_content,$user_cookie);
```

**审批通过处理：**

```284:287:app/Http/Repository/BxfStrategyRepository.php
public static function approval_deal_add($strategy_data): bool {
    return BxfStrategy::add($strategy_data);
}
```

### **六、审批差异对比逻辑**

#### **6.1 添加操作差异**

```262:271:app/Http/Repository/BxfStrategyRepository.php
private function add_diff($strategy_data): string {
    $apply_content = '添加策略:'.PHP_EOL;
    $apply_content .= '策略名: ' . $strategy_data['strategy_name'] . PHP_EOL;
    $apply_content .= 'pv_dict: ' .PHP_EOL. json_encode(json_decode($strategy_data['pv_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL;
    // ... 其他字段差异
    return $apply_content;
}
```

#### **6.2 编辑操作差异**

```349:370:app/Http/Repository/BxfStrategyRepository.php
private function edit_diff($origin,$new): string {
    $apply_content = '';
    if($origin['strategy_name'] != $new['strategy_name']){
        $apply_content .= '策略名: ' . $origin['strategy_name']. ' -> '. $new['strategy_name'] . PHP_EOL;
    }
    // ... 逐字段对比差异
    return $apply_content;
}
```

### **七、异常处理逻辑**

#### **7.1 权限异常**

```117:121:app/Http/Repository/ApprovalRepository.php
if (!isset($sysName)) {
    return ['msg' => '非法操作', 'code' => 50002, 'data' => []];
}
```

#### **7.2 业务异常**

```477:481:app/Http/Repository/BxfStrategyRepository.php
//校验是否存在策略配置使用该策略
$res = BxfStrategyConfig::getListLikeStrategyId($strategy_id);
if(!empty($res)){
    throw new Exception("该策略正在被使用,不可禁用");
}
```

### **八、审批逻辑总结**

#### **8.1 核心特点**

- **条件触发**：基于业务规则自动触发审批
- **权限分离**：申请人、审批人权限明确分离
- **状态管理**：完整的状态流转和控制
- **双路径处理**：支持内部和外部审批系统
- **业务解耦**：审批逻辑与具体业务逻辑分离

#### **8.2 处理流程**

1. **触发阶段**：业务操作检查是否需要审批
2. **创建阶段**：生成审批单和请求记录
3. **等待阶段**：审批单处于待审批状态
4. **处理阶段**：审批人进行审批决策
5. **执行阶段**：根据审批结果执行相应业务逻辑
6. **完成阶段**：更新审批状态和结果记录

#### **8.3 设计优势**

- **可扩展性**：新增审批类型只需添加 URL 常量和处理方法
- **可追溯性**：完整记录审批过程和结果
- **灵活性**：支持不同业务场景的审批需求
- **安全性**：严格的权限控制和状态管理

这套审批逻辑设计体现了企业级应用的成熟架构思想，既保证了业务的合规性，又保持了系统的灵活性和可维护性。
