#### **2.2 核心数据架构**

**审批主表（approval）：**

```sql
CREATE TABLE `approval` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `flow_no` varchar(32) NOT NULL DEFAULT '' COMMENT '审批流水号',
  `op_type` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '操作类型,',
  `account_id` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '账户id',
  `customer_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '客户id',
  `product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品id',
  `apply_content` text CHARACTER SET utf8 NOT NULL COMMENT '审核内容',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '审批状态：1审批中，2审批通过，3审批驳回，4撤销',
  `applicant` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '申请人',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '申请时间',
  `approver` char(32) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '审批人',
  `approve_at` int(11) NOT NULL DEFAULT '0' COMMENT '审批时间',
  `approval_comments` varchar(11) NOT NULL DEFAULT '' COMMENT '审批意见',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `a_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2310 DEFAULT CHARSET=utf8mb4 COMMENT='后台操作审批表'
```

**审批请求表（approval_request）：**

```sql
CREATE TABLE `approval_request` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `flow_no` varchar(32) NOT NULL DEFAULT '' COMMENT '审批流水号',
  `url` char(100) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '请求地址',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态：1待请求，2请求完成，3请求错误',
  `data` text CHARACTER SET utf8 NOT NULL COMMENT '接口参数',
  `op_result` text NOT NULL COMMENT '请求结果',
  `create_at` int(11) NOT NULL DEFAULT '0' COMMENT '申请时间',
  `update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `a_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2130 DEFAULT CHARSET=utf8mb4 COMMENT='审批请求表'
```


