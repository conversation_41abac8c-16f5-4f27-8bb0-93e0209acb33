### **四、权限控制架构**

#### **4.1 权限配置表（approval_config）**

**权限控制机制：**

```27:50:app/Models/ApprovalConfig.php
/**
 * 获取操作配置
 * op_type 操作类型
 * name 操作类型名称
 * approver 拥有审批权限的用户
 * @return mixed
 */
public static function getConfig(){
    return static::where('status','=',1)->get()->toArray();
}

/**
 * 获取该用户可审批的操作类型
 * @param $username 用户名
 * @return mixed
 */
public static function getNode($username){
    return static::select("node_url")->where('status','=',1)->where('approver','like', '%'.$username.'%')->get()->pluck('node_url')->toArray();
}
```

#### **4.2 角色权限控制**

**基于用户名的权限验证：**

```47:85:app/Http/Repository/ApprovalRepository.php
if($user_name !== ''){
    $tab_type = request()->post('tab_type', null);
    if($tab_type == 'approver'){//审批人
        //获取有权审批的操作类型
        $op_types = ApprovalConfig::getNode($user_name);
        if(!empty($op_type) && in_array($op_type,$op_types)){
            $parmas[] = ['op_type','=', $op_type];
        }else{
            $parmas[] = ['op_type','in',$op_types];
        }
        $is_set_op_type = true;
    }else if($tab_type == 'applicant'){//申请人
        $parmas[] = ['applicant','=',$user_name];
    }
}
```
