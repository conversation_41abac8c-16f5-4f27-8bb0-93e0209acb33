## 渠道管理

-- 渠道基础表
channel -- 渠道基础信息表
channel_interface -- 渠道接口表
channel_product -- 渠道产品关联表
channel_mapping -- 渠道映射表
channel_account -- 渠道账号表
channel_account_interface -- 渠道账号接口关联表

-- 渠道业务表
channel_exception_record -- 渠道异常记录表
channel_remit -- 渠道打款单表
channel_remit_log -- 渠道打款日志表
channel_cost_minconsume_spread -- 渠道成本保底分摊表
channel_contract_approval_process -- 渠道合同审批流程表
channel_qualication_detail -- 渠道资质信息表
channel_qualication_detail_file -- 渠道资质文件表

-- 上游渠道表
upstream_channel -- 上游渠道配置表
upstream_channel_price -- 上游渠道价格配置表

-- 渠道配置表
config_interface_shunt -- 接口分流配置表
bxfshort_all_cmcc_config -- 邦信分移动渠道配置表

### 渠道基础表

#### 渠道基础信息表 DDL

CREATE TABLE `channel` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`father_id` int(11) NOT NULL DEFAULT '0' COMMENT '父级产品 id',
`name` varchar(20) NOT NULL DEFAULT '' COMMENT '渠道名称',
`label` varchar(40) NOT NULL DEFAULT '' COMMENT '渠道的中文名称',
`company` varchar(150) DEFAULT '' COMMENT '公司名称',
`status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '客户状态',
`num` int(11) NOT NULL DEFAULT '0' COMMENT '渠道对应的编号',
`channel_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '渠道 ID',
`sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
`balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
`real_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实时余额',
`bottom_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '渠道保底金额',
`balance_start_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '余额开始计算时间',
`rest_num` int(11) NOT NULL DEFAULT '0' COMMENT '剩余调用量',
`rest_num_start_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '剩余调用量开始计算时间',
`max_call_num` int(11) NOT NULL DEFAULT '0' COMMENT '每天最大调用次数',
`daily_call_num` int(11) NOT NULL DEFAULT '0' COMMENT '日均调用次数',
`contract_end_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '合同到期日',
`channel_follower` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道对应商务跟进人',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8

#### 渠道接口表 DDL

CREATE TABLE `channel_interface` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`cid` int(11) NOT NULL DEFAULT '0' COMMENT 'channel 表的 id',
`channel_id` int(11) NOT NULL DEFAULT '0',
`name` varchar(40) NOT NULL DEFAULT '' COMMENT '接口名称',
`label` varchar(40) NOT NULL DEFAULT '' COMMENT '渠道的中文解释',
`status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '可用状态',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=926 DEFAULT CHARSET=utf8

#### 渠道产品关联表 DDL

CREATE TABLE `channel_product` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`cid` int(11) NOT NULL DEFAULT '0' COMMENT 'channel 表的 id',
`pid` int(11) NOT NULL DEFAULT '0' COMMENT '产品 id',
`product_id` int(11) NOT NULL DEFAULT '0',
`channel_id` int(11) NOT NULL DEFAULT '0',
`param` text COMMENT '该渠道在该产品下的配置',
`status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '渠道上下架',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=172 DEFAULT CHARSET=utf8

#### 渠道映射表 DDL

CREATE TABLE `channel_mapping` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`label` varchar(40) NOT NULL DEFAULT '' COMMENT '渠道名称',
`operator_channel_id` varchar(120) NOT NULL DEFAULT '' COMMENT '运营商侧渠道 id',
`operator_label` varchar(120) NOT NULL DEFAULT '' COMMENT '运营商侧渠道名称',
`status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '可用状态',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8 COMMENT='运营商渠道名称和金融渠道名称映射表'

#### 渠道账号表

CREATE TABLE `channel_account` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
`account` varchar(255) NOT NULL DEFAULT '' COMMENT '渠道账户',
`channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`rest_num` bigint(11) NOT NULL DEFAULT '0' COMMENT '渠道账号剩余调用量',
`rest_num_start_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '渠道账号剩余调用量计算时间',
`max_call_num` int(11) NOT NULL DEFAULT '0' COMMENT '每天最大调用量',
`daily_call_num` int(11) NOT NULL DEFAULT '0' COMMENT '日均调用量',
`contract_end_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '合同到期日',
`balance_start_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '渠道账号剩余余额计算时间',
`balance` decimal(12,2) NOT NULL DEFAULT '0.00',
`real_balance` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '当前真实余额',
`external_channel_id` varchar(64) DEFAULT '' COMMENT '拉取渠道 id 标识',
`status` tinyint(4) DEFAULT '0' COMMENT '是否有效：0-有效，1-无效',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8

#### 渠道账号接口关联表 DDL

     CREATE TABLE `channel_account_interface` (

`id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`channel_account_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道账号 id',
`channel_interface_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '渠道接口 id',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=334 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC

### 渠道业务表

#### 渠道异常记录表 DDL

CREATE TABLE `channel_exception_record` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`channel_id` int(10) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`start_time` int(10) NOT NULL DEFAULT '0' COMMENT '开始时间(时间戳)',
`keep_time` int(10) NOT NULL DEFAULT '0' COMMENT '持续时间(秒数)',
`total_num` int(10) NOT NULL DEFAULT '0' COMMENT '总条数',
`affect_num` int(10) NOT NULL DEFAULT '0' COMMENT '影响条数',
`ratio` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '占比',
`reason` varchar(255) NOT NULL DEFAULT '' COMMENT '原因',
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1 正常、0 删除',
`created_at` int(10) NOT NULL DEFAULT '0',
`updated_at` int(10) NOT NULL DEFAULT '0',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 COMMENT='渠道异常信息记录表'

#### 渠道打款单表 DDL

CREATE TABLE `channel_remit` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`channel_id` char(32) NOT NULL DEFAULT '' COMMENT '渠道 ID',
`receipt_serial` char(20) NOT NULL DEFAULT '' COMMENT '收款流水单号',
`remit_serial` char(20) NOT NULL DEFAULT '' COMMENT '打款流水单号',
`name` varchar(255) NOT NULL DEFAULT '' COMMENT '收款方名称',
`money` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '收款金额',
`bank` varchar(255) NOT NULL DEFAULT '' COMMENT '收款方银行名称',
`remit_date` varchar(30) NOT NULL DEFAULT '' COMMENT '收款日期',
`contract_no` varchar(100) NOT NULL DEFAULT '' COMMENT '合同编号',
`proof_image` varchar(100) NOT NULL DEFAULT '' COMMENT '凭证图片',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`admin` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
`status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '当前状态（0-已提交 1-已修改 2-已驳回 3-已认款）',
`remark` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
`channel_account_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道账号 id',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=822 DEFAULT CHARSET=utf8 COMMENT='渠道打款单'

#### 渠道打款日志表 DDL

CREATE TABLE `channel_remit_log` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`remit_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '打款单 ID',
`type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '类型（0-提交 1-修改 2-驳回 3-认款）',
`admin` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
`do_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作的时间',
`remark` text COMMENT '备注（驳回原因）',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 COMMENT='渠道打款单日志'

#### 渠道成本保底分摊表 DDL

CREATE TABLE `channel_cost_minconsume_spread` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
`run_id` char(32) NOT NULL DEFAULT '' COMMENT '任务 id，自动生成',
`operator` varchar(20) NOT NULL DEFAULT '0' COMMENT '运营商标识',
`month` int(10) NOT NULL DEFAULT '0' COMMENT '调整月份 Ym 格式',
`money` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '调整金额',
`type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '重跑类型:1 重跑删除旧记录',
`run_status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '运行状态 1 未计算 2 计算中 3 计算完成 4 计算出错',
`remarks` varchar(255) NOT NULL COMMENT '备注原因',
`is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态 1 删除 0 未删除',
`delete_at` int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`admin` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
`category` tinyint(2) NOT NULL DEFAULT '0' COMMENT '任务类型:1 保底分摊,2 对账调整',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COMMENT='渠道成本保底分摊记录表'

#### 渠道合同审批流程表 DDL

CREATE TABLE `channel_contract_approval_process` (
`id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`user_id` varchar(30) NOT NULL DEFAULT '' COMMENT '商务飞书中对应的 uid',
`channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`contract_end_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '合同到期日',
`contract_due_days` int(11) NOT NULL DEFAULT '0' COMMENT '距离合同到期天数',
`channel_follower` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道对应商务跟进人',
`process_instance_id` varchar(255) NOT NULL DEFAULT '' COMMENT '飞书审批流程 id',
`is_renewal` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否续约 0 否(不续约)、1 是(续约)',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注(如不续约的原因)',
`is_complete_renewal` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否完成续约 0 否(未完成续约)、1 是(完成续约)',
`new_contract_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '预计合同完成日期(预估新合同回来开始签的日期)',
`status` varchar(16) NOT NULL DEFAULT 'PENDING' COMMENT '审批实例状态 PENDING：审批中,APPROVED：通过,REJECTED：拒绝,CANCELED：撤回,DELETED：删除',
`real_result` int(11) NOT NULL DEFAULT '1' COMMENT '1 新建 2 已审批 (表示处理完毕)',
`update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
`create_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='渠道合同到期飞书审批表'

#### 渠道资质信息表 DDL

CREATE TABLE `channel_qualication_detail` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
`channel_qualication_id` int(11) NOT NULL COMMENT 'channel_qualication 表 id',
`qualication_name` varchar(225) NOT NULL DEFAULT '' COMMENT '资质名称',
`end_date` date NOT NULL COMMENT '资质到期日期',
`product_description` varchar(1000) NOT NULL DEFAULT '' COMMENT '产品描述',
`remark` varchar(225) NOT NULL DEFAULT '' COMMENT '备注',
`status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态 0 默认 1、正常 -1 删除',
`is_alarm` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 默认 1、预警 2、不预警',
`created_at` bigint(11) NOT NULL DEFAULT '0' COMMENT '创建时间 ',
`updated_at` bigint(11) NOT NULL DEFAULT '0' COMMENT '更新时间 ',
PRIMARY KEY (`id`),
KEY `idx_channel_qualication` (`channel_qualication_id`)
) ENGINE=InnoDB AUTO_INCREMENT=97 DEFAULT CHARSET=utf8 COMMENT='渠道资质信息表'

#### 渠道资质文件表 DDL

CREATE TABLE `channel_qualication_detail_file` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
`channel_qualication_detail_id` int(11) NOT NULL COMMENT 'channel_qualication 表 id',
`original_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文件原名称',
`qualication_file` varchar(225) NOT NULL DEFAULT '' COMMENT '资质文件',
`status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态 0 默认 1、正常 -1 删除',
`created_at` bigint(11) NOT NULL DEFAULT '0' COMMENT '创建时间 ',
`updated_at` bigint(11) NOT NULL DEFAULT '0' COMMENT '更新时间 ',
PRIMARY KEY (`id`),
KEY `idx_channel_qualication` (`channel_qualication_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=105 DEFAULT CHARSET=utf8 COMMENT='渠道资质信息文件表'

### 上游渠道表

#### 上游渠道配置表 DDL

CREATE TABLE `upstream_channel` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品 ID',
`product_id_sub` int(11) NOT NULL DEFAULT '0',
`name` varchar(200) NOT NULL DEFAULT '' COMMENT '渠道名称',
`channel` varchar(150) NOT NULL DEFAULT '渠道（数据源）标记',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数据拉取时间',
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:1 正常,0 失效',
`yd_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '移动状态:1 正常,0 失效',
`lt_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '联通状态:1 正常,0 失效',
`dx_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '电信状态:1 正常,0 失效',
`type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:评分字段,2:统计字段',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2976 DEFAULT CHARSET=utf8 COMMENT='各产品上游渠道（数据源）统一配置表，数据来源是通过 product 表中的 channel_stat 整理而成'

#### 上游渠道价格配置表 DDL

CREATE TABLE `upstream_channel_price` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`upstream_channel_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '各产品上游渠道 id',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品 ID',
`channel` varchar(150) NOT NULL DEFAULT '' COMMENT '渠道（数据源）标记',
`price` varchar(150) NOT NULL DEFAULT '' COMMENT '价格，此处存储为一个 JSON，JSON 格式为{all:0.00,succ:0.16,yd:1.12,dx:1.01,lt:1.00}，计算的账单将会是每个字段费用之和',
`start_date` date DEFAULT NULL COMMENT '计费开始时间（包前不包后）',
`admin` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '价格创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '价格修改时间',
`is_billing` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否计费（如果计费的话，就算是价格为 0，也会存在账单的）',
`delete_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2929 DEFAULT CHARSET=utf8 COMMENT='各产品上游渠道的计费配置，数据来源于运营的添加'

### 渠道配置表

#### 接口分流配置表 DDL

CREATE TABLE `config_interface_shunt` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '接口 ID',
`channel_id` int(11) NOT NULL DEFAULT '0',
`operator` varchar(10) NOT NULL DEFAULT '' COMMENT '运营商',
`encrypt` varchar(10) NOT NULL DEFAULT '' COMMENT '加密方式',
`ratio` float NOT NULL DEFAULT '0' COMMENT '分流占比 0-100',
`create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COMMENT='渠道分流配置表'

#### 邦信分移动渠道配置表 DDL

CREATE TABLE `bxfshort_all_cmcc_config` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`name` varchar(200) NOT NULL DEFAULT '' COMMENT '渠道名称',
`flag` varchar(200) NOT NULL DEFAULT '' COMMENT '英文标记',
`sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序号(倒序)',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COMMENT='邦信分快捷版全国移动渠道配置'

#### ER 图

```mermaid
erDiagram
    channel ||--o{ channel_interface : "has"
    channel ||--o{ channel_product : "configures"
    channel ||--o{ channel_mapping : "maps"
    channel ||--o{ channel_account : "owns"
    channel_account ||--o{ channel_account_interface : "supports"
    channel_interface ||--o{ channel_account_interface : "linked_to"
    channel ||--o{ channel_exception_record : "records"
    channel ||--o{ channel_remit : "processes"
    channel ||--o{ channel_qualication_detail : "requires"
    channel_qualication_detail ||--o{ channel_qualication_detail_file : "contains"

    channel {
        int id PK
        int father_id
        string name
        string label
        string company
        decimal balance
        decimal real_balance
        date contract_end_date
    }

    channel_interface {
        int id PK
        int cid FK
        string name
        string label
        int status
    }

    channel_account {
        int id PK
        int channel_id FK
        string account
        decimal balance
        decimal real_balance
        date contract_end_date
    }

    channel_product {
        int id PK
        int cid FK
        int pid FK
        text param
        int status
    }
```

1. channel → channel_interface

含义：渠道提供哪些 API 接口能力（如：身份认证、分级查询）
字段说明：
• status: 启停状态
• label: 接口展示名称（如“手机号实名认证”）

⸻

2. channel → channel_product

含义：渠道实际支持哪些 产品（pid）的调用能力，可带参数
用途：决定是否参与产品调度或备选渠道池
字段说明：
• param: JSON 格式的通道级特殊参数（如字段映射、限速配置）

⸻

3. channel → channel_mapping

含义：记录与其他系统的 渠道映射关系，常用于对接第三方平台的渠道编码转换等。

⸻

4. channel → channel_account

含义：渠道在系统中的 子账户配置（如多个供应商账号）
用途：
• 适配不同认证方式
• 支持并发调度
• 金额独立核算

⸻

5. channel_account → channel_account_interface

含义：某个渠道子账号支持的 接口范围
用途：
• 可灵活关闭某账号的某些接口（如限流）
• 支持账号级别负载调度

⸻

6. channel_interface → channel_account_interface

含义：某接口（如实名认证）与账号间的连接表
作用：为多账号多接口做能力注册（即能用、是否支持、调度优先级等）

⸻

7. channel → channel_exception_record

含义：该渠道下所有接口调用产生的 异常记录
用途：
• 支持渠道稳定性评分
• 用于报警与调度剔除逻辑

⸻

8. channel → channel_remit

含义：该渠道的 资金汇款流水记录
用途：
• 财务管理、可用余额核对
• 接口计费结算确认

⸻

9. channel → channel_qualication_detail

含义：渠道需提供的各项 接入资质项
例如：API 对接协议、企业法人授权、服务承诺函等

channel_qualication_detail → channel_qualication_detail_file

#### 渠道业务流程图

```mermaid
graph TD
    A["渠道入驻"] --> B["资质审核"]
    B --> C["合同签署"]
    C --> D["渠道配置"]
    D --> E["接口配置"]
    D --> F["产品配置"]
    D --> G["账号配置"]
    E --> H["运营监控"]
    F --> H
    G --> H
    H --> I["异常记录"]
    H --> J["余额管理"]
    J --> K["打款处理"]
    K --> L["打款确认"]
    H --> M["合同到期管理"]
    M --> N["续约流程"]
    M --> O["终止合作"]
```

#### 渠道分类层次图

```mermaid
graph TD
    A["渠道分类"] --> B["基础渠道"]
    A --> C["上游渠道"]

    B --> D["直连渠道"]
    B --> E["代理渠道"]

    C --> F["运营商渠道"]
    C --> G["第三方渠道"]

    F --> H["移动"]
    F --> I["联通"]
    F --> J["电信"]

    G --> K["数据服务商"]
    G --> L["支付服务商"]

    D --> M["自营渠道"]
    D --> N["合作渠道"]

    E --> O["一级代理"]
    E --> P["二级代理"]
```
