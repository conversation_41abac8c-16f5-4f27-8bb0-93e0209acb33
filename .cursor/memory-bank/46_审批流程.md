## 审批配置表 DDL

CREATE TABLE `approval_config` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`node_url` char(100) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '节点地址',
`name` varchar(100) DEFAULT NULL COMMENT '地址中文名称',
`approve_column` varchar(1000) DEFAULT '' COMMENT '通用的字段和中文名映射关系',
`status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态：1 生效，2 不生效',
`approver` varchar(200) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '审批人，多个逗号分隔',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '申请时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `a_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='审批配置表'

## 审批规则表

CREATE TABLE `approval_rule` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`node_url` char(100) NOT NULL DEFAULT '0' COMMENT '节点地址',
`url_name` varchar(256) NOT NULL COMMENT '地址中文名称',
`column` varchar(256) NOT NULL COMMENT '通用的字段名',
`rule` varchar(100) NOT NULL COMMENT '规则规则 equal:相等,greater_than:大于,less_than:小于,free:不需要审批',
`value` varchar(128) NOT NULL COMMENT '比较的值 \_\_old:原始值,数字值,"":空字符串(不进行比较)',
`status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态: 1 生效, 0 不生效',
`type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '规则类型: 1 符合该规则不需要审核, 0 符合该规则需要审核',
`remark` varchar(128) DEFAULT '' COMMENT '规则备注',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='审批生效配置表'

## 审批请求表

CREATE TABLE `approval_request` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`flow_no` varchar(32) NOT NULL DEFAULT '' COMMENT '审批流水号',
`url` char(100) CHARACTER SET latin1 NOT NULL DEFAULT '0' COMMENT '请求地址',
`status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态：1 待请求，2 请求完成，3 请求错误',
`data` text CHARACTER SET utf8 NOT NULL COMMENT '接口参数',
`op_result` text NOT NULL COMMENT '请求结果',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '申请时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `a_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2115 DEFAULT CHARSET=utf8mb4 COMMENT='审批请求表'

## 审批配置表

CREATE TABLE `approval_config` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`node_url` char(100) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '节点地址',
`name` varchar(100) DEFAULT NULL COMMENT '地址中文名称',
`approve_column` varchar(1000) DEFAULT '' COMMENT '通用的字段和中文名映射关系',
`status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态：1 生效，2 不生效',
`approver` varchar(200) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '审批人，多个逗号分隔',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '申请时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
PRIMARY KEY (`id`) USING BTREE,
KEY `a_status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='审批配置表'

### 审批系统的数据表 ER 关系图

```mermaid
erDiagram
    approval_config ||--o{ approval_request : "configures"
    approval_request ||--o{ pre_test_monitor_instance : "monitors"
    approval_request ||--o{ pre_test_apply_instance : "applies"
    approval_request ||--o{ pre_test_apply_manual : "processes"

    approval_config {
        int id PK "自增ID"
        string node_url "审批节点URL"
        string approver "审批人"
        int status "状态"
        string op_type "操作类型"
        string name "操作类型名称"
    }

    approval_request {
        int id PK "自增ID"
        string type "审批类型"
        string status "审批状态"
        string applicant "申请人"
        datetime apply_time "申请时间"
        text content "审批内容"
        int config_id FK "关联配置ID"
    }

    pre_test_monitor_instance {
        int id PK "自增ID"
        int monitor_id "监控ID"
        string customer_id "客户ID"
        string approval_code "审批代码"
        string instance_code "实例代码"
        string instance_status "实例状态"
        int request_id FK "关联请求ID"
    }

    pre_test_apply_instance {
        int id PK "自增ID"
        string approval_code "审批代码"
        string instance_code "实例代码"
        string instance_status "实例状态"
        int request_id FK "关联请求ID"
    }

    pre_test_apply_manual {
        int id PK "自增ID"
        string approval_code "审批代码"
        string instance_code "实例代码"
        string business_type "业务类型"
        string customer_id "客户ID"
        string product_list "产品列表"
        int request_id FK "关联请求ID"
    }
```

### 审批系统的整体架构图

```mermaid
graph TD
    A[审批系统] --> B[基础审批]
    A --> C[测试审批]
    A --> D[监控审批]

    B --> B1[审批配置]
    B --> B2[审批请求]
    B --> B3[审批流转]

    C --> C1[售前测试申请]
    C --> C2[产品建模申请]
    C --> C3[账号申请]

    D --> D1[调用量级稳定]
    D --> D2[灰度上线时间]
    D --> D3[超出预计上线]

    subgraph 审批状态流转
    E[待审批] --> F[审批中]
    F --> G[通过]
    F --> H[拒绝]
    F --> I[撤销]
    end
```

### 审批流程的数据流转图

```mermaid
graph LR
    A[用户发起] --> B[审批请求]
    B --> C[获取审批配置]
    C --> D[创建审批实例]

    D --> E{审批类型}
    E --> F[监控审批]
    E --> G[测试审批]
    E --> H[人工审批]

    F --> I[pre_test_monitor_instance]
    G --> J[pre_test_apply_instance]
    H --> K[pre_test_apply_manual]

    I --> L[审批处理]
    J --> L
    K --> L

    L --> M[更新状态]
    M --> N[记录日志]
    N --> O[通知结果]

    subgraph 数据存储
    P[(审批配置表)]
    Q[(审批请求表)]
    R[(审批实例表)]
    end

    C --> P
    B --> Q
    D --> R
```
