# 主体管理列表接口 `customer/group/list`

## 业务场景

用于展示和管理所有主体信息及其关联客户信息。

## 数据流向

前端请求 → CustomerGroupController@list → CustomerGroupRepositorie@list → 多个 Model 查询 → 数据整合 → JSON 响应

## 输入参数

- `group_id`（可选）
- `page`（默认 1）
- `limit`（默认 20）

## 主要逻辑说明

1. 查询主体列表：CustomerGroup::getListByCondition
2. 获取管理员信息：SystemUser::getUserInfoByNames
3. 获取客户列表：Customer::getListByGroupIds
4. 数据整合格式化：增加状态名、管理员名、客户计数等字段

## 响应示例

```json
{
  "status": 0,
  "data": {
    "list": [...],
    "count": 25
  }
}
```

## 业务价值

- 管理员统一视图
- 客户归属关系清晰
- 支持分页性能友好
