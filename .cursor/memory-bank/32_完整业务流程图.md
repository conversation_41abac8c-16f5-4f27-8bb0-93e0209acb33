## 完整业务流程图核心阶段

Mermaid 源码

```mermaid
graph TD
    %% 客户接入阶段
    A1[客户商务洽谈] --> A2[产品需求确认]
    A2 --> A3[合同签订]
    A3 --> A4[客户信息录入]
    A4 --> A5[账号创建配置]
    A5 --> A6[产品开通授权]
    A6 --> A7[计费配置设置]
    A7 --> A8[技术对接集成]
    A8 --> A9[客户测试验证]
    A9 --> A10[正式上线运行]

    %% 产品配置阶段
    B1[产品管理配置] --> B2[渠道配置分流]
    B2 --> B3[价格策略配置]
    B3 --> B4[限额配置管理]
    B4 --> B5[监控规则配置]
    B5 --> B6[告警阈值设置]

    %% 调用统计阶段
    C1[API请求接入] --> C2[身份认证鉴权]
    C2 --> C3[权限验证检查]
    C3 --> C4[参数校验处理]
    C4 --> C5[渠道路由分流]
    C5 --> C6[第三方接口调用]
    C6 --> C7[结果数据返回]
    C7 --> C8[调用量统计记录]
    C8 --> C9[日志数据存储]

    %% 统计处理阶段
    D1[实时调用统计] --> D2[按维度聚合统计]
    D2 --> D3[异步队列处理]
    D3 --> D4[统计数据入库]
    D4 --> D5[缓存更新维护]
    D5 --> D6[报表数据生成]

    %% 账单生成阶段
    E1[定时任务触发] --> E2[获取计费配置]
    E2 --> E3[获取统计用量]
    E3 --> E4[计费算法计算]
    E4 --> E5[账单数据生成]
    E5 --> E6[收入成本分摊]
    E6 --> E7[账单版本管理]
    E7 --> E8[账单数据入库]

    %% 计费算法细化
    E4 --> E4A[固定价格计费]
    E4 --> E4B[累进阶梯计费]
    E4 --> E4C[到达阶梯计费]
    E4 --> E4D[打包计费模式]

    %% 发票管理阶段
    F1[商务开票申请] --> F2[开票信息录入]
    F2 --> F3[消耗数据关联]
    F3 --> F4[审核流程处理]
    F4 --> F5[财务开票操作]
    F5 --> F6[发票邮寄配送]
    F6 --> F7[回款单关联]
    F7 --> F8[发票状态更新]

    %% 发票状态流转
    F4 --> F4A[已申请]
    F4A --> F4B[已审核/已驳回]
    F4B --> F4C[已开票]
    F4C --> F4D[已邮寄]
    F4D --> F4E[已回款]

    %% 监控告警阶段
    G1[系统监控检测] --> G2[阈值规则判断]
    G2 --> G3[异常状态识别]
    G3 --> G4[告警信息生成]
    G4 --> G5[通知渠道推送]
    G5 --> G6[处理流程跟踪]
    G6 --> G7[问题解决确认]

    %% 告警类型细化
    G3 --> G3A[余额预警]
    G3 --> G3B[限量预警]
    G3 --> G3C[渠道异常预警]
    G3 --> G3D[系统异常预警]
    G3 --> G3E[合同到期预警]

    %% 通知推送阶段
    H1[消息触发机制] --> H2[通知规则匹配]
    H2 --> H3[消息内容生成]
    H3 --> H4[多渠道消息推送]
    H4 --> H5[推送结果确认]
    H5 --> H6[失败重试机制]

    %% 通知渠道细化
    H4 --> H4A[企业微信通知]
    H4 --> H4B[邮件通知]
    H4 --> H4C[飞书通知]
    H4 --> H4D[短信通知]
    H4 --> H4E[电话告警]
    H4 --> H4F[钉钉审批流程]

    %% 数据分析阶段
    I1[数据收集汇总] --> I2[多维度分析处理]
    I2 --> I3[趋势预测分析]
    I3 --> I4[报表可视化展示]
    I4 --> I5[决策支持建议]
    I5 --> I6[数据导出分享]

    %% 流程关联
    A10 --> C1
    B6 --> G1
    C9 --> D1
    D6 --> E1
    E8 --> F1
    G4 --> H1
    D6 --> I1

    %% 异常处理
    C6 --> X1[接口异常处理]
    E4 --> X2[计费异常处理]
    F4 --> X3[开票异常处理]
    G4 --> X4[告警异常处理]
    X1 --> H1
    X2 --> H1
    X3 --> H1
    X4 --> H1

    %% 样式定义
    classDef customerPhase fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef configPhase fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef callPhase fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef statsPhase fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef billPhase fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef invoicePhase fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef monitorPhase fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef notifyPhase fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef analysisPhase fill:#e8eaf6,stroke:#1a237e,stroke-width:2px
    classDef exceptionPhase fill:#ffecb3,stroke:#ff8f00,stroke-width:2px

    %% 应用样式
    class A1,A2,A3,A4,A5,A6,A7,A8,A9,A10 customerPhase
    class B1,B2,B3,B4,B5,B6 configPhase
    class C1,C2,C3,C4,C5,C6,C7,C8,C9 callPhase
    class D1,D2,D3,D4,D5,D6 statsPhase
    class E1,E2,E3,E4,E5,E6,E7,E8,E4A,E4B,E4C,E4D billPhase
    class F1,F2,F3,F4,F5,F6,F7,F8,F4A,F4B,F4C,F4D,F4E invoicePhase
    class G1,G2,G3,G4,G5,G6,G7,G3A,G3B,G3C,G3D,G3E monitorPhase
    class H1,H2,H3,H4,H5,H6,H4A,H4B,H4C,H4D,H4E,H4F notifyPhase
    class I1,I2,I3,I4,I5,I6 analysisPhase
    class X1,X2,X3,X4 exceptionPhase
```

### 1. **客户接入阶段**（蓝色）

- **商务洽谈** → **需求确认** → **合同签订** → **信息录入** → **账号创建** → **产品开通** → **计费配置** → **技术对接** → **测试验证** → **正式上线**

### 2. **产品配置阶段**（紫色）

- **产品管理配置** → **渠道配置分流** → **价格策略配置** → **限额配置管理** → **监控规则配置** → **告警阈值设置**

### 3. **调用统计阶段**（绿色）

- **API 请求接入** → **身份认证鉴权** → **权限验证检查** → **参数校验处理** → **渠道路由分流** → **第三方接口调用** → **结果数据返回** → **调用量统计记录** → **日志数据存储**

### 4. **统计处理阶段**（橙色）

- **实时调用统计** → **按维度聚合统计** → **异步队列处理** → **统计数据入库** → **缓存更新维护** → **报表数据生成**

### 5. **账单生成阶段**（粉色）

- **定时任务触发** → **获取计费配置** → **获取统计用量** → **计费算法计算** → **账单数据生成** → **收入成本分摊** → **账单版本管理** → **账单数据入库**
- **计费算法类型**：固定价格、累进阶梯、到达阶梯、打包计费

### 6. **发票管理阶段**（青色）

- **商务开票申请** → **开票信息录入** → **消耗数据关联** → **审核流程处理** → **财务开票操作** → **发票邮寄配送** → **回款单关联** → **发票状态更新**
- **状态流转**：已申请 → 已审核/已驳回 → 已开票 → 已邮寄 → 已回款

### 7. **监控告警阶段**（红色）

- **系统监控检测** → **阈值规则判断** → **异常状态识别** → **告警信息生成** → **通知渠道推送** → **处理流程跟踪** → **问题解决确认**
- **告警类型**：余额预警、限量预警、渠道异常、系统异常、合同到期

### 8. **通知推送阶段**（浅绿色）

- **消息触发机制** → **通知规则匹配** → **消息内容生成** → **多渠道消息推送** → **推送结果确认** → **失败重试机制**
- **通知渠道**：企业微信、邮件、飞书、短信、电话告警、钉钉审批流程

### 9. **数据分析阶段**（蓝紫色）

- **数据收集汇总** → **多维度分析处理** → **趋势预测分析** → **报表可视化展示** → **决策支持建议** → **数据导出分享**

### 10. **异常处理机制**（黄色）

- **接口异常处理**、**计费异常处理**、**开票异常处理**、**告警异常处理**

## 关键业务特点

1. **端到端流程**：从客户接入到数据分析的完整闭环
2. **多维度监控**：覆盖业务、技术、财务等多个维度
3. **异步处理机制**：大量使用队列和异步任务处理
4. **多样化计费模式**：支持固定、阶梯、打包等多种计费方式
5. **完善的告警体系**：多级别、多渠道的告警通知机制
6. **规范的发票流程**：支持预付费和后付费的完整发票管理
7. **强异常处理**：各个环节都有对应的异常处理和告警机制

这个流程图完整展示了金融数据服务系统的业务全景，帮助理解系统各个模块之间的协作关系和数据流转过程。
