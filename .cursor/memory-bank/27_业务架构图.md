# 业务架构图

本图用于描述系统核心模块的架构层次划分，展示从客户调用入口到后端数据库与日志、监控系统的各层职责和依赖，是理解技术解耦与职责分工的关键。

⸻

## 业务架构分层图（Mermaid 源码）

flowchart TB

    subgraph 客户端 / 外部系统
        A[客户系统 / SDK / API请求方]
    end

    subgraph 接入层
        B[API Gateway / 接口层（路由、鉴权）]
    end

    subgraph 业务逻辑层
        C[Controller 控制器层]
        D[Service 服务封装层]
    end

    subgraph 数据访问层
        E[Repository / DAO 封装查询逻辑]
    end

    subgraph 存储与基础设施层
        F1[(MySQL - 主业务库)]
        F2[(MongoDB - 日志库)]
        F3[(Redis - 缓存 / 限额)]
        F4[(ClickHouse - 聚合分析)]
    end

    subgraph 日志与监控层
        G1[日志服务 - 操作 / 账单 / 异常日志]
        G2[监控与告警系统 - 成功率、延迟]
    end

    A --> B --> C --> D --> E
    E --> F1
    E --> F2
    E --> F3
    E --> F4
    C --> G1
    D --> G2

⸻

## 分层说明

接入层
• 负责路由、权限校验、API 版本控制、访问频控等

控制器层（Controller）
• 接收请求、参数校验、路由业务调用链

服务层（Service）
• 封装业务逻辑、组合模型操作、调用第三方服务

数据访问层（Repository）
• 封装 DB 读写、Mongo 查询、ClickHouse 聚合

存储层
• MySQL：主业务数据
• MongoDB：日志、调用历史、动态集合
• Redis：缓存、调用限制、IP 频控、账号状态
• ClickHouse：统计分析、趋势对比、报表底表

日志与监控层
• 日志系统：审计、操作、请求链路追踪
• 监控系统：调用成功率、响应时间、渠道告警等

⸻

## 应用价值

    •	有助于团队协同理解职责边界
    •	支撑中台化拆分、性能瓶颈识别
    •	作为技术汇报、需求对接、监控建设的统一参考视图
