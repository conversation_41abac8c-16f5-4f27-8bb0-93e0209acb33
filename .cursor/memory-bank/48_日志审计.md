## 操作日志

### 操作日志表 DDL

CREATE TABLE `handle_log` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`handle_type` varchar(30) NOT NULL COMMENT '类型 eg: create update report',
`description` varchar(255) NOT NULL COMMENT '本次错误的简短描述',
`content` text NOT NULL COMMENT '错误的详细数据',
`created_at` int(11) NOT NULL,
`handle_user` varchar(50) NOT NULL COMMENT '操作人',
`handle_result` enum('1','0') NOT NULL DEFAULT '1' COMMENT '1 代表成功的操作 0 代表是失败的操作',
PRIMARY KEY (`id`),
KEY `created_at` (`created_at`),
KEY `type` (`handle_type`),
KEY `handle_user` (`handle_user`),
KEY `handle_result` (`handle_result`)
) ENGINE=InnoDB AUTO_INCREMENT=144318 DEFAULT CHARSET=utf8 COMMENT='日志'

### API 调用日志表

CREATE TABLE `api_log` (
`id` int(11) NOT NULL COMMENT '主键 ID',
`log_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT 'api 日志类型（1:账号配置同步,2:用量同步,3 数据统计汇总）',
`is_success` tinyint(2) NOT NULL DEFAULT '0' COMMENT 'success:0,error:1',
`log_user` int(5) NOT NULL DEFAULT '0' COMMENT 'product_id, system:0',
`node_area` tinyint(2) NOT NULL DEFAULT '0' COMMENT 'system:0,beijing:1,shenzhen:2',
`input_data` text COMMENT '入参数据',
`output_data` text COMMENT '出参数据',
`create_at` int(11) NOT NULL COMMENT '时间戳',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8

## 业务日志表

### 客户发送对账单日志表

CREATE TABLE `logs_mail_customer_income` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`customer_id` varchar(15) NOT NULL DEFAULT '' COMMENT '客户 ID',
`uuid` varchar(255) NOT NULL DEFAULT '' COMMENT '邮件任务的 uuid',
`send_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发送时间',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道',
PRIMARY KEY (`id`) USING BTREE,
KEY `customer_id` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户发送对账单日志'
