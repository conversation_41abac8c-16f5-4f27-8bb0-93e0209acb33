## 产品管理

-- 产品基础表
product -- 产品基础信息表
product_category -- 产品分类表
product_amount -- 产品用量统计表
product_stat -- 产品统计数据表
product_wechat_warning -- 产品微信预警配置表

-- 业务产品表
business_product -- 业务产品表
bargain_product -- 合同产品表

-- 邦企查产品表
bang_products -- 邦企查产品表
bang_product_fields -- 邦企查产品字段关联表

-- 产品监控表
bxf_channel_psi_monitor -- 百信分渠道 PSI 监控表

### 产品基础表 DDL

#### 产品表 DDL

CREATE TABLE `product` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品编号',
`product_name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '产品名称',
`product_enname` varchar(100) NOT NULL DEFAULT '',
`product_key` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '产品业务 key',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父级产品 ID',
`password` varchar(255) DEFAULT '$2y$10$k1LpRx5OOh/ysWOj/XAlkOIxdqzBqcI89YX.xIsk7sK9d9lRgsQi6' COMMENT 'api password grant',
`data` text CHARACTER SET utf8 COMMENT '配置参数[type：1 单行文本框、2 多行文本框、3 单选框、4 多选框、5 时间控件，option 表示单选多选框的枚举值]',
`stat_config` longtext CHARACTER SET utf8 COMMENT '客户统计配置',
`fee_config` text CHARACTER SET utf8 COMMENT '计费配置参数',
`product_param` varchar(300) NOT NULL DEFAULT '',
`back_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '产品后台展示状态（1 展示，0 不展示）',
`search_show` tinyint(2) NOT NULL DEFAULT '0' COMMENT '主产品在统计等表单查询中是否展示（1 展示，-1 不展示）',
`channel_stat` text COMMENT '渠道统计配置',
`channel_fee` text COMMENT '渠道计费配置',
`admin` varchar(30) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '操作人',
`mark` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '备注',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '产品可用状态（0-禁用 1-可用）',
`sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号（倒序）',
`type` int(11) NOT NULL DEFAULT '0' COMMENT '产品类型（1-普通产品 2-父产品 3-子产品 4-金盾分流产品 5-虚拟产品）',
`bill_config` text COMMENT '产品类型（1-普通产品 2-父产品 3-子产品 4-金盾分流产品 5-虚拟产品）',
PRIMARY KEY (`id`),
KEY `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=748 DEFAULT CHARSET=utf8mb4 COMMENT='产品表'

#### 产品分类表 DDL

CREATE TABLE `product_category` (
`id` int(10) NOT NULL AUTO_INCREMENT COMMENT '自增 id',
`category_id` int(10) NOT NULL COMMENT '分类编号',
`category_name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '分类名称',
`status` tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT '分类状态（2-禁用 1-可用 0-默认）',
`sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序号（倒序）',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`admin` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='产品分类定义表'

#### 产品用量统计表 DDL

CREATE TABLE `product_amount` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
`apikey` char(32) NOT NULL DEFAULT '' COMMENT '客户账号 apikey',
`product_id` int(5) NOT NULL DEFAULT '0' COMMENT '产品 ID',
`daily_amount` int(11) NOT NULL DEFAULT '0' COMMENT '日用量',
`amount_date` date NOT NULL COMMENT '用量日期(2018-11-22)',
`update_at` int(10) NOT NULL COMMENT '更新时间',
PRIMARY KEY (`id`),
UNIQUE KEY `apikey_id_date` (`apikey`,`product_id`,`amount_date`) USING BTREE,
KEY `product_id` (`product_id`),
KEY `amount_date` (`amount_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1712 DEFAULT CHARSET=utf8

#### 产品统计数据表 DDL

CREATE TABLE `product_stat` (
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
`apikey` char(32) NOT NULL DEFAULT '' COMMENT '账号 apikey',
`product_id` int(5) NOT NULL DEFAULT '0' COMMENT '产品 ID',
`amount_date` date NOT NULL COMMENT '用量日期',
`node_area` tinyint(2) NOT NULL DEFAULT '0' COMMENT '节点地区(beijing:1/shenzhen:2)',
`stat_data` text COMMENT '用量数据(json)',
`update_at` int(11) NOT NULL COMMENT '更新时间',
PRIMARY KEY (`id`),
UNIQUE KEY `apikey_id_date` (`apikey`,`product_id`,`amount_date`,`node_area`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8

#### 产品微信预警配置表 DDL

CREATE TABLE `product_wechat_warning` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`product_id` int(11) NOT NULL COMMENT '产品 ID',
`type` varchar(50) NOT NULL COMMENT '场景',
`slug` varchar(255) NOT NULL COMMENT '微信预警配置表 slug,用来确定企业微信配置信息, 多个用逗号拼接',
`operater` varchar(30) NOT NULL COMMENT '操作人',
`created_at` int(11) NOT NULL COMMENT '创建时间',
`updated_at` int(11) NOT NULL COMMENT '更新时间',
PRIMARY KEY (`id`),
UNIQUE KEY `product_id` (`product_id`,`type`) USING BTREE,
KEY `type` (`type`),
KEY `operater` (`operater`),
KEY `updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COMMENT='各个产品各个场景需要触发的微信预警关系表'

### 业务产品表 DDL

#### 业务产品表 DDL

CREATE TABLE `business_product` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`business_product_name` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '业务产品名称',
`product_key` int(10) NOT NULL DEFAULT '0' COMMENT '业务产品 key',
`father_key` int(10) NOT NULL DEFAULT '0' COMMENT '业务主产品 key',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '系统产品 ID',
`fs_key` varchar(32) NOT NULL COMMENT '飞书枚举值',
`product_list` varchar(512) NOT NULL DEFAULT '' COMMENT '系统子产品 list',
`feedback_dim` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '反馈维度(0:本产品,1:子产品)',
`status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '可用状态(0:可用,1:禁用)',
`create_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
PRIMARY KEY (`id`),
KEY `idx_product_key` (`product_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='业务产品表'

#### 合同产品表 DDL

CREATE TABLE `bargain_product` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`bargain_id` varchar(20) NOT NULL DEFAULT '' COMMENT '合同编号',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品 ID',
PRIMARY KEY (`id`),
KEY `bargain_id` (`bargain_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=158 DEFAULT CHARSET=utf8 COMMENT='合同产品表'

### 邦企查产品表 DDL

#### 邦企查产品表 DDL

CREATE TABLE `bang_products` (
`id` int(10) NOT NULL AUTO*INCREMENT,
`contract_status` tinyint(1) DEFAULT '3' COMMENT '1 已签约已付费 2 已签约未付费 3 未签约未付费 4 其他',
`name` varchar(255) NOT NULL COMMENT '产品的名字',
`product_type` tinyint(1) DEFAULT '1' COMMENT '1 交行 0 普通',
`apikey` varchar(128) NOT NULL COMMENT 'apikey',
`apisecret` varchar(128) NOT NULL COMMENT 'apisecret',
`limit_ip` text COMMENT 'IP 白名单',
`limit_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '限额的类型 （调用类型 1 日限制 2 月限制 3 年限制 0 不限制）',
`limit_num` int(11) DEFAULT NULL COMMENT '限额类型下的限额数量',
`limit_total` int(11) NOT NULL COMMENT '生命周期的限额总量',
`expiration_date` int(11) NOT NULL COMMENT '账号的有效期',
`limit_second` int(11) NOT NULL COMMENT '秒并发限制',
`remark` text COMMENT '备注',
`status` tinyint(1) NOT NULL COMMENT '账号状态 1 可用 2 禁用',
`encrypt_way` tinyint(1) NOT NULL DEFAULT '0' COMMENT '加密方式:0:默认明文,1:md5,2:sm3',
`algorithm_switch` varchar(40) DEFAULT '' COMMENT '算法模块的开关,1:搜索引擎模块,2:有数金服模糊搜索接口,3:行业一致性;多个用*隔开',
`created_at` int(11) DEFAULT '0' COMMENT '创建的时间',
`updated_at` int(11) DEFAULT NULL COMMENT '更新的时间',
PRIMARY KEY (`id`),
UNIQUE KEY `name` (`name`),
UNIQUE KEY `apikey` (`apikey`),
UNIQUE KEY `apisecret` (`apisecret`),
KEY `status` (`status`),
KEY `contract_status` (`contract_status`)
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8 COMMENT='邦企查产品表'

#### 邦企查产品字段关联表 DDL

CREATE TABLE `bang_product_fields` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`product_id` int(11) NOT NULL COMMENT '产品的 ID',
`field_id` int(11) NOT NULL COMMENT '邦企查输出字段 ID',
`created_at` int(11) DEFAULT '0',
`updated_at` int(11) DEFAULT '0',
PRIMARY KEY (`id`),
KEY `product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1339 DEFAULT CHARSET=utf8 COMMENT='邦企查产品和输出字段的多对多关系表'

### 产品监控表 DDL

CREATE TABLE `bxf_channel_psi_monitor` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`product_name` varchar(20) DEFAULT NULL,
`field` varchar(32) NOT NULL DEFAULT '' COMMENT '字段名',
`channel` varchar(10) NOT NULL DEFAULT '' COMMENT '渠道',
`psi_ab` decimal(15,4) NOT NULL DEFAULT '0.0000' COMMENT 'PSI_AB',
`psi_ac` decimal(15,4) NOT NULL DEFAULT '0.0000' COMMENT 'PSI_AC',
`cnt_1` int(10) NOT NULL DEFAULT '0' COMMENT '昨日调用量',
`cnt_2` int(10) NOT NULL DEFAULT '0' COMMENT '前日调用量',
`cnt_7` int(10) NOT NULL DEFAULT '0' COMMENT '前七日调用量',
`cday` date NOT NULL COMMENT '创建日期',
PRIMARY KEY (`id`) USING BTREE,
KEY `idx_cday` (`cday`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='产品名'

#### ER 图

```mermaid
erDiagram
    product ||--o{ product_amount : "has"
    product ||--o{ product_stat : "has"
    product ||--o{ product_wechat_warning : "has"
    product ||--o{ business_product : "mapped_to"
    product ||--o{ bargain_product : "included_in"
    product_category ||--o{ product : "contains"

    product {
        int id PK
        int product_id
        varchar product_name
        varchar product_key
        int father_id
        text data
        text fee_config
        tinyint status
        int type
    }

    product_category {
        int id PK
        int category_id
        varchar category_name
        tinyint status
        int sort
    }

    product_amount {
        int id PK
        char apikey
        int product_id FK
        int daily_amount
        date amount_date
    }

    product_stat {
        int id PK
        char apikey
        int product_id FK
        date amount_date
        text stat_data
    }

    business_product {
        int id PK
        varchar business_product_name
        int product_key
        int product_id FK
        varchar product_list
    }

    bargain_product {
        int id PK
        varchar bargain_id
        int product_id FK
    }
```

1. product_category → product

关系：product_category 包含多个 product
说明：用于对产品进行类型分类管理，如“实名类产品”、“风控评分类产品”。

⸻

2. product → product_amount

关系：一个产品 has 多条 product_amount 记录
说明：存储产品每日调用限额、使用量等数据
典型字段：daily_amount、amount_date

⸻

3. product → product_stat

关系：一个产品 has 多条 product_stat 记录
说明：存储每个产品在每个客户下的统计结果，如每日调用次数、成功率等
典型字段：stat_data 为序列化统计字段

⸻

4. product → product_wechat_warning

关系：一个产品配置多个预警规则（如成功率告警）
说明：该表未出现在字段定义中，但推测其用于产品级别的微信预警配置
用途：与“产品监控”、“通知中心”联动

⸻

5. product → business_product

关系：一个产品 mapped_to 多个业务组合产品
说明：产品与业务线之间的映射表。例如将多个原子产品组合为一个业务套餐
典型字段：product_list 为组成该业务产品的子产品 ID 列表

⸻

6. product → bargain_product

关系：一个产品 included_in 多个议价单产品记录
说明：产品可被纳入多个议价方案中，支持灵活商务定价策略
典型字段：bargain_id 表示议价批次或策略

#### 创建产品分类和类型层次图

```mermaid
graph TD
    A[产品分类] --> B[普通产品]
    A --> C[父产品]
    A --> D[子产品]
    A --> E[金盾分流产品]
    A --> F[虚拟产品]

    B --> B1[基础产品配置]
    B --> B2[计费配置]
    B --> B3[统计配置]

    C --> C1[产品组管理]
    C --> C2[子产品关联]
    C --> C3[组级配置]

    D --> D1[父产品关联]
    D --> D2[独立配置]
    D --> D3[继承配置]

    E --> E1[路由策略]
    E --> E2[分流规则]
    E --> E3[监控配置]

```

#### 产品管理业务流程图

```mermaid
graph TD
    A[产品创建] --> B[基础信息配置]
    B --> C[产品分类设置]
    C --> D[配置参数设置]
    D --> E{产品类型判断}

    E -->|普通产品| F1[基础配置]
    E -->|父产品| F2[组配置]
    E -->|子产品| F3[关联配置]
    E -->|分流产品| F4[路由配置]

    F1 --> G1[计费规则]
    F1 --> G2[统计规则]
    F1 --> G3[预警配置]

    F2 --> H1[子产品管理]
    F2 --> H2[组级规则]

    F3 --> I1[继承配置]
    F3 --> I2[独立配置]

    F4 --> J1[分流策略]
    F4 --> J2[监控规则]

    G1 --> K[产品启用]
    G2 --> K
    G3 --> K
    H1 --> K
    H2 --> K
    I1 --> K
    I2 --> K
    J1 --> K
    J2 --> K
```

#### 产品监控和预警流程图

```mermaid
graph LR
    A[产品监控] --> B[用量统计]
    A --> C[性能监控]
    A --> D[质量监控]

    B --> E[日用量统计]
    B --> F[月用量统计]
    B --> G[总量统计]

    C --> H[响应时间]
    C --> I[成功率]
    C --> J[并发量]

    D --> K[PSI监控]
    D --> L[数据质量]
    D --> M[异常预警]

    E --> N[预警触发]
    F --> N
    G --> N
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N

    N --> O[微信通知]
    N --> P[邮件通知]
    N --> Q[短信通知]
```
