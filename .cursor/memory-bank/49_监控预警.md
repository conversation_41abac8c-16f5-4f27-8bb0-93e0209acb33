## 余额预警表

### 余额预警表 DDL

CREATE TABLE `balance_warning` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`customer_id` varchar(50) NOT NULL DEFAULT '' COMMENT '客户 ID',
`days` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '触发预警的阈值（30 天/15 天/7 天）（3 天的不记录）',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='余额预警表（用于记录预警是否发送过，避免重复发送）'

### 余额预警基础数据表 DDL

CREATE TABLE `balance_warning_basics` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`customer_id` varchar(50) NOT NULL DEFAULT '' COMMENT '客户 ID',
`today_income` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '当日的消费',
`this_month_income` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '本月的消费（包含当日）',
`balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '当前余额',
`credit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '授信额度',
`credit_balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '授信余额',
`surplus_credit` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '剩余授信额度',
`everyday_income` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '每日消费基准值',
`days` int(10) NOT NULL DEFAULT '0' COMMENT '预估使用天数',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数据创建日期',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数据更新日期',
PRIMARY KEY (`id`) USING BTREE,
KEY `sid` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=596 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='余额预警基础数据（实时更新）'

### 失败任务表 DDL

CREATE TABLE `failed_jobs` (
`id` int(9) NOT NULL AUTO_INCREMENT,
`connection` varchar(200) NOT NULL DEFAULT '',
`queue` varchar(200) NOT NULL DEFAULT '',
`payload` text,
`failed_at` varchar(10) NOT NULL DEFAULT '',
UNIQUE KEY `id_key` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
