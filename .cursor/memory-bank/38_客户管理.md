## 客户基础表

customer -- 客户基础信息表
customer_balance -- 客户余额表
customer_balance_transfer -- 客户余额转移表
customer_balance_warning -- 客户余额预警表
customer_balance_log -- 客户余额变动日志表
customer_consume -- 客户消费记录表
customer_cost_adjust -- 客户成本调整表
customer_bill_adjust -- 客户账单调整表

### 客户基础信息表 DDL

CREATE TABLE `customer` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '客户 id',
`name` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '客户名',
`company` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '公司名称',
`agent_company` varchar(255) NOT NULL DEFAULT '' COMMENT '代理机构对应的签约公司名称,格式为 json',
`c_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '公司类别 1 数据公司,2 风控系统,3 综合类,4p2p,5 现金分期,6 消费金融（3C),7 农商行,8 汽车金融,9 消费金融,10 其他',
`type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '客户类型',
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '客户状态 1 可用 2 禁用',
`operator` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '运营跟进人',
`salesman` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '销售跟进人',
`admin` varchar(30) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '操作人',
`create_at` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(11) NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`is_delete` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除',
`bill_email` varchar(500) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账单收件邮箱',
`balance` float(11,2) DEFAULT NULL COMMENT '余额预警值（null 代表未设置）',
`balance_percent` float(5,4) DEFAULT NULL COMMENT '余额百分比预警（null 代表未设置）',
`available_days` int(10) unsigned DEFAULT NULL COMMENT '预警剩余消耗天数',
`bill_cc_email` varchar(500) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账单抄送人',
`contract_status` tinyint(3) unsigned NOT NULL DEFAULT '4' COMMENT '客户签约状态（1--已签约已付款 2--未签约未付款 3--未签约 4--其他 5--特殊客户）',
`frequency` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '账单发送频率（0-人工发送、1-每月发送、2-每季度发送、3-每年发送）',
`payment_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '付款类型(1-预付款客户 2-后付款客户)',
`credit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户授信额度',
`email_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '邮件类型 1 标准 2 非标准',
`customer_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '用户类型：1-金融用户，2-企服用户',
`channel_mode` tinyint(3) NOT NULL DEFAULT '2' COMMENT '客户类型 是否为渠道客户: 1-渠道客户,2-非渠道客户(直客)',
`reconciliation_cycle` tinyint(3) NOT NULL DEFAULT '1' COMMENT '对账方式 对账周期: 1-月度,2-季度,3-年度',
`dhb_sign_corp` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT 'yulore' COMMENT '客户跟电话邦签约的公司简称，全称见 common_enum 表 value 值',
`channel_follower` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '渠道跟进人',
`source_id` varchar(100) NOT NULL DEFAULT '-1' COMMENT '征信机构 id -1 不限,0 电话邦, 1 朴道。多个用英文逗号分隔。',
`sign_type` varchar(100) NOT NULL DEFAULT '0' COMMENT '征信客户分类 0: 电话邦签约, 10: 朴道签约 逗号分隔',
`group_id` char(32) DEFAULT '' COMMENT '主体 id',
`introduce_salesman` varchar(100) NOT NULL DEFAULT '' COMMENT '推荐商务',
`level` tinyint(3) unsigned NOT NULL DEFAULT '100' COMMENT '客户分类 客户级别 头部客户:10, 重要客户:50, 一般客户:100',
`level_scale` varchar(16) NOT NULL DEFAULT '' COMMENT '客户级别 1(规模)',
`level_income` tinyint(3) NOT NULL DEFAULT '0' COMMENT '客户级别 2(收入)',
`level_scale_income` varchar(16) NOT NULL DEFAULT '' COMMENT '客户级别 3(规模+收入)',
PRIMARY KEY (`id`),
UNIQUE KEY `customer_name` (`name`) USING BTREE,
KEY `customer_status` (`status`) USING BTREE,
KEY `customer_id` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=969 DEFAULT CHARSET=utf8mb4 COMMENT='客户表'

CREATE TABLE `customer_group` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`group_id` char(32) NOT NULL COMMENT '主体 id',
`group_name` varchar(32) NOT NULL COMMENT '主体名称',
`status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '主体状态 1 可用 2 禁用',
`admin` varchar(30) NOT NULL DEFAULT '' COMMENT '操作人',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`remark` varchar(250) DEFAULT NULL,
PRIMARY KEY (`id`),
KEY `group_id_IDX` (`group_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8 COMMENT='客户主体表'

CREATE TABLE `customer_invoice_information` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`company_name` varchar(32) NOT NULL COMMENT '购方企业名称',
`invoice_company` varchar(32) NOT NULL COMMENT '开票企业名称',
`tax_number` varchar(22) NOT NULL COMMENT '购方税号',
`bank` varchar(256) NOT NULL COMMENT '银行',
`bank_account` varchar(32) NOT NULL COMMENT '银行账户',
`address` varchar(256) NOT NULL DEFAULT '' COMMENT '地址',
`phone` varchar(32) NOT NULL COMMENT '电话',
`post_receiver` varchar(10) NOT NULL COMMENT '邮寄收件人',
`post_phone` varchar(20) NOT NULL COMMENT '收件人电话',
`post_address` varchar(32) NOT NULL COMMENT '邮寄地址',
`email` varchar(256) DEFAULT NULL COMMENT '电子邮箱,电子发票使用',
`invoice_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发票类型 1:专票 2:普票 3:电子普票 4:电子专票',
`invoice_content` varchar(256) NOT NULL DEFAULT '' COMMENT '开票内容',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=243 DEFAULT CHARSET=utf8 COMMENT='客户开票信息'

### 客户余额管理 DDL

CREATE TABLE `balance_warning_basics` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`customer_id` varchar(50) NOT NULL DEFAULT '' COMMENT '客户 ID',
`today_income` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '当日的消费',
`this_month_income` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '本月的消费（包含当日）',
`balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '当前余额',
`credit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '授信额度',
`credit_balance` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '授信余额',
`surplus_credit` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '剩余授信额度',
`everyday_income` decimal(16,6) NOT NULL DEFAULT '0.000000' COMMENT '每日消费基准值',
`days` int(10) NOT NULL DEFAULT '0' COMMENT '预估使用天数',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数据创建日期',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数据更新日期',
PRIMARY KEY (`id`) USING BTREE,
KEY `sid` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=596 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='余额预警基础数据（实时更新）'

CREATE TABLE `customer_monthly_balance` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '0' COMMENT '客户 id',
`month_date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '月份 Ym',
`balance` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '余额',
`consume` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '消耗 (含特殊消耗)',
`remit` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '当月回款,充值 (含特殊消耗)',
`unremit` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '当月未回款',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 -1 全部 0 电话邦 1 朴道 2 百行',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
`pre_time` datetime DEFAULT NULL,
`remark` varchar(512) DEFAULT NULL,
PRIMARY KEY (`id`),
KEY `customer_id` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1014306 DEFAULT CHARSET=utf8 COMMENT='客户每月余额'

### 客户计费管理

CREATE TABLE `statistics_customer_usage` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品 ID',
`node` varchar(20) NOT NULL DEFAULT 'default' COMMENT '节点',
`operator` varchar(255) NOT NULL DEFAULT 'DEFAULT' COMMENT '运营商',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户调用日期（Ymd 格式）',
`total` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总调用量',
`success` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '成功调用量',
`valid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查得量',
`charge` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费量',
`extend_one` int(11) NOT NULL DEFAULT '0' COMMENT '扩展数量计数字段，存量洞察产品代表提交量',
`cache` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '换存量',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`call_product_id` int(10) NOT NULL DEFAULT '0' COMMENT '客户调用产品 ID',
PRIMARY KEY (`id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `product_id` (`product_id`) USING BTREE,
KEY `date` (`date`) USING BTREE,
KEY `apikey_product_id_date_operator` (`apikey`,`product_id`,`date`,`operator`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=951 DEFAULT CHARSET=utf8 COMMENT='客户调用量统计表'

CREATE TABLE `config_price_customer` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`father_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父产品 ID',
`product_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '子产品 IDS（多个产品 ID 以,进行分割）',
`accord` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '计费依据（1-成功调用量，2-查得量）',
`methods` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '计费方式（0-不计费 1-包年 2-固定价格 3-累进阶梯 4-到达阶梯）',
`period` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '计费周期（0-无意义 1-日 2-月 3-年 4-无周期）',
`diff_operator` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否区分运营商（0-不区分 1-区分）',
`mode` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '多级产品计费模式（0-无计费模式 1-独立子产品 2-打包 3-汇总子产品）',
`price` text COMMENT '单价',
`start_date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费开始时间（Ymd）',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
`delete_time` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
`admin` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `father_id` (`father_id`) USING BTREE,
KEY `start_date` (`start_date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1491 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客户单价配置表'

### 客户开票管理 DDL

CREATE TABLE `customer_invoice_config` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`customer_id` varchar(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源(0:电话邦,1:朴道,10:浙数交)',
`model` tinyint(3) NOT NULL DEFAULT '0' COMMENT '开票模式[1:后付费(主产品),2:后付费(主产品,可拆金额),3:后付费(子产品),4:后付费(子产品,可拆金额),5:预付费,6:预付费(先票后款后消耗)]',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE KEY `idx_customer_source` (`customer_id`,`source`) USING BTREE,
KEY `customer_id` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2970 DEFAULT CHARSET=utf8 COMMENT='客户渠道开票模式配置表'

### 客户消费记录表 DDL

CREATE TABLE `customer_consume` (
`id` int(10) NOT NULL AUTO_INCREMENT,
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源(0:电话邦,1:朴道,10:浙数交)',
`consume_month` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消耗月份 Ym',
`product_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品 id',
`father_id` int(10) NOT NULL DEFAULT '0' COMMENT '父产品 id',
`consume_money` varchar(255) NOT NULL DEFAULT '' COMMENT '消耗金额',
`consume_balance` varchar(255) NOT NULL DEFAULT '' COMMENT '未开票消耗金额',
`consume_unpaid` varchar(255) NOT NULL DEFAULT '' COMMENT '未到款消耗金额',
`invoice_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '开票状态(0:未申请,10:已申请,20:已审核,25:部分开票,30:已开票,40:红冲中)',
`salesman` varchar(100) NOT NULL COMMENT '销售跟进人',
`created_at` datetime DEFAULT NULL COMMENT '创建时间',
`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
`deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
PRIMARY KEY (`id`),
UNIQUE KEY `idx_unique` (`customer_id`,`source`,`consume_month`,`product_id`) USING BTREE,
KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9797 DEFAULT CHARSET=utf8 COMMENT='客户每月消耗数据表'

### 客户账单调整表 DDL

CREATE TABLE `customer_bill_adjust` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`father_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父产品 id',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '子产品 id',
`operator` varchar(20) NOT NULL DEFAULT '' COMMENT '运营商',
`channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`date` int(10) NOT NULL DEFAULT '0' COMMENT '调整日期 Ymd 格式',
`money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本费用',
`fee_number` int(11) NOT NULL DEFAULT '0' COMMENT '计费用量',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息',
`create_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后一次修改时间',
`delete_at` int(10) NOT NULL DEFAULT '0' COMMENT '删除时间',
`admin` varchar(20) NOT NULL DEFAULT '' COMMENT '最后一次操作人',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`is_rerun_month_data` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否重跑月数据',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=508 DEFAULT CHARSET=utf8 COMMENT='客户成本账单调整记录表'

### ER 图

```mermaid
erDiagram
    customer ||--o{ customer_group : "belongs_to"
    customer ||--o{ customer_invoice_information : "has"
    customer ||--o{ balance_warning_basics : "monitors"
    customer ||--o{ customer_monthly_balance : "tracks"
    customer ||--o{ customer_consume : "records"
    customer ||--o{ customer_bill_adjust : "adjusts"
    customer ||--o{ customer_invoice_config : "configures"
    customer ||--o{ statistics_customer_usage : "tracks_usage"
    customer ||--o{ config_price_customer : "sets_price"

    customer {
        int id PK
        string customer_id UK
        string name UK
        string company
        string agent_company
        int c_type
        int type
        int status
        string operator
        string salesman
        string admin
        int payment_type
        int credit
        string group_id FK
        int level
        string level_scale
        int level_income
    }

    customer_group {
        int id PK
        string group_id UK
        string group_name
        int status
        string admin
        string remark
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }

    customer_invoice_information {
        int id PK
        string customer_id FK
        string company_name
        string invoice_company
        string tax_number
        string bank
        string bank_account
        string address
        string phone
        string post_receiver
        string post_phone
        int invoice_type
    }

    balance_warning_basics {
        int id PK
        string customer_id FK
        decimal today_income
        decimal this_month_income
        decimal balance
        int credit
        decimal credit_balance
        decimal surplus_credit
        decimal everyday_income
        int days
    }

    customer_monthly_balance {
        int id PK
        string customer_id FK
        int month_date
        decimal balance
        decimal consume
        decimal remit
        decimal unremit
        int source
    }

    customer_consume {
        int id PK
        string customer_id FK
        int source
        int consume_month
        int product_id
        int father_id
        string consume_money
        string consume_balance
        string consume_unpaid
        int invoice_status
        string salesman
    }

    customer_bill_adjust {
        int id PK
        string customer_id FK
        string title
        int father_id
        int product_id
        string operator
        int channel_id
        int interface_id
        int date
        decimal money
        int fee_number
    }

    customer_invoice_config {
        int id PK
        string customer_id FK
        int source
        int model
        datetime created_at
        datetime updated_at
        datetime deleted_at
    }

    statistics_customer_usage {
        int id PK
        string apikey
        int product_id
        string node
        string operator
        int date
        int total
        int success
        int valid
        int cache
        int source
    }

    config_price_customer {
        int id PK
        string apikey
        int father_id
        string product_ids
        int accord
        int methods
        int period
        int diff_operator
        int mode
        text price
        int start_date
    }
```

一个客户(customer)可以:

- 属于一个客户分组(customer_group)
- 有多个开票信息(customer_invoice_information)
- 有多个余额预警记录(balance_warning_basics)
- 有多个月度余额记录(customer_monthly_balance)
- 有多个消费记录(customer_consume)
- 有多个账单调整记录(customer_bill_adjust)
- 有多个开票配置(customer_invoice_config)
- 有多个调用统计记录(statistics_customer_usage)
- 有多个计费配置(config_price_customer)

### 客户管理的业务流程图

```mermaid
graph TD
    A[客户入驻] --> B[基础信息录入]
    B --> C{选择付款类型}
    C -->|预付费| D[设置预存款]
    C -->|后付费| E[设置授信额度]
    D --> F[分配销售]
    E --> F
    F --> G[选择分组]
    G --> H{是否需要开票}
    H -->|是| I[录入开票信息]
    H -->|否| J[完成入驻]
    I --> J
    J --> K[开始使用]
    K --> L[调用统计]
    L --> M[余额监控]
    M --> N{是否预警}
    N -->|是| O[发送预警通知]
    N -->|否| L

```

### 客户账务管理流程图

```mermaid
graph LR
    A[客户账务] --> B[预付费管理]
    A --> C[后付费管理]

    B --> B1[预存款管理]
    B --> B2[余额监控]
    B --> B3[自动预警]

    C --> C1[授信管理]
    C --> C2[账期管理]
    C --> C3[信用评估]

    B1 --> D[对账单生成]
    B2 --> D
    B3 --> D
    C1 --> D
    C2 --> D
    C3 --> D

    D --> E[开票管理]
    E --> F[收款管理]

    subgraph 开票流程
    E --> E1[开票申请]
    E1 --> E2[开票审核]
    E2 --> E3[开票确认]
    end

    subgraph 收款流程
    F --> F1[收款确认]
    F1 --> F2[入账处理]
    F2 --> F3[余额更新]
    end
```

### 客户调用统计数据流图

```mermaid
graph TD
    A[API调用] --> B[实时统计]
    B --> C[按维度汇总]

    C --> D[客户维度]
    C --> E[产品维度]
    C --> F[运营商维度]

    D --> G[调用量统计]
    D --> H[成功率统计]
    D --> I[命中率统计]

    G --> J[日报表]
    H --> J
    I --> J
    J --> K[月报表]

    subgraph 统计维度
    G --> G1[总调用量]
    G --> G2[成功调用量]
    G --> G3[查得量]
    G --> G4[缓存量]
    end

    subgraph 报表应用
    K --> L[账单生成]
    K --> M[费用计算]
    K --> N[业务分析]
    end
```

### 客户计费规则配置图

```mermaid
graph TD
    A[计费配置] --> B[计费依据]
    A --> C[计费方式]
    A --> D[计费周期]

    B --> B1[成功调用量]
    B --> B2[查得量]

    C --> C1[不计费]
    C --> C2[包年]
    C --> C3[固定价格]
    C --> C4[累进阶梯]
    C --> C5[到达阶梯]

    D --> D1[按日]
    D --> D2[按月]
    D --> D3[按年]
    D --> D4[无周期]

    subgraph 多级产品计费
    A --> E[计费模式]
    E --> E1[独立子产品]
    E --> E2[打包]
    E --> E3[汇总子产品]
    end

    subgraph 运营商计费
    A --> F[运营商区分]
    F --> F1[区分]
    F --> F2[不区分]
    end
```
