## 系统代码目录结构与模块职责说明

### 项目根目录结构

```
finance-manage-api/
├── app/                    # 核心应用代码
├── config/                 # 配置文件
├── routes/                 # 路由定义
├── database/              # 数据库相关
├── resources/             # 资源文件
├── public/                # 公共资源
├── tests/                 # 测试文件
├── vendor/                # 第三方依赖
├── bootstrap/             # 启动文件
├── doc/                   # 文档
├── composer.json          # 依赖管理
└── artisan               # 命令行工具
```

### app/ 核心应用目录

#### Http/ - HTTP 层

```
Http/
├── Controllers/           # 控制器层
│   ├── Monitor/          # 监控相关控制器
│   ├── ReportDay/        # 日报相关控制器
│   ├── Feishu/           # 飞书集成控制器
│   ├── CldcStat/         # 统计相关控制器
│   ├── Bill/             # 账单相关控制器
│   ├── Channel/          # 渠道管理控制器
│   ├── Customer/         # 客户管理控制器
│   └── Invoice/          # 发票管理控制器
├── Middleware/           # 中间件
└── Requests/            # 请求验证
```

#### Models/ - 数据模型层

```
Models/
├── Bill/                 # 账单相关模型
├── Channel/              # 渠道相关模型
├── Customer/             # 客户相关模型
├── Invoice/              # 发票相关模型
├── Monitor/              # 监控相关模型
├── Product/              # 产品相关模型
└── Statistics/           # 统计相关模型
```

#### Jobs/ - 异步任务层

```
Jobs/
├── Bill/                 # 账单生成任务
├── Statistics/           # 统计计算任务
├── Monitor/              # 监控检查任务
├── Notification/         # 通知发送任务
└── Sync/                 # 数据同步任务
```

#### Console/Commands/ - 命令行工具

```
Commands/
├── Bill/                 # 账单处理命令
├── Statistics/           # 统计处理命令
├── Monitor/              # 监控处理命令
├── Data/                 # 数据处理命令
└── System/              # 系统维护命令
```

#### Repositories/ - 数据仓库层

```
Repositories/
├── Bill/                 # 账单数据仓库
├── Channel/              # 渠道数据仓库
├── Customer/             # 客户数据仓库
├── Invoice/              # 发票数据仓库
└── Statistics/           # 统计数据仓库
```

#### Services/ - 业务服务层

```
Services/
├── Bill/                 # 账单业务服务
├── Channel/              # 渠道业务服务
├── Customer/             # 客户业务服务
├── Invoice/              # 发票业务服务
├── Monitor/              # 监控业务服务
└── Statistics/           # 统计业务服务
```

### config/ - 配置目录

```
config/
├── app.php               # 应用配置
├── database.php          # 数据库配置
├── queue.php            # 队列配置
├── cache.php            # 缓存配置
├── logging.php          # 日志配置
└── services/            # 第三方服务配置
```

### routes/ - 路由目录

```
routes/
├── web.php              # Web路由定义
├── api.php              # API路由定义
└── console.php         # 命令行路由
```

### 模块职责说明

1. **控制器层 (Controllers)**

   - 处理 HTTP 请求响应
   - 参数验证和权限检查
   - 调用相应的业务服务
   - 返回统一格式的响应

2. **模型层 (Models)**

   - 定义数据库表结构
   - 处理数据关联关系
   - 提供数据访问接口
   - 实现基础的业务规则

3. **服务层 (Services)**

   - 实现核心业务逻辑
   - 处理复杂的业务规则
   - 协调多个 Repository 的操作
   - 提供事务管理

4. **数据仓库层 (Repositories)**

   - 封装数据访问逻辑
   - 提供数据 CRUD 操作
   - 实现数据缓存策略
   - 处理复杂的数据查询

5. **任务层 (Jobs)**

   - 处理异步业务逻辑
   - 执行定时统计任务
   - 处理监控告警任务
   - 管理消息通知任务

6. **命令行工具 (Commands)**

   - 提供维护工具
   - 执行数据处理脚本
   - 处理定时任务
   - 系统诊断工具

7. **中间件层 (Middleware)**

   - 请求前处理
   - 权限认证
   - 日志记录
   - 响应处理

8. **配置管理 (Config)**
   - 系统配置管理
   - 环境配置管理
   - 服务配置管理
   - 第三方集成配置
