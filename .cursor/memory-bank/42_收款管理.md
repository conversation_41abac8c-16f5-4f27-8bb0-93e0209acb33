## 收款相关表

### 收款记录表 DDL

CREATE TABLE `remit` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`customer_id` char(32) NOT NULL DEFAULT '' COMMENT '客户 ID',
`receipt_serial` char(50) NOT NULL DEFAULT '' COMMENT '收款流水单号',
`remit_serial` char(50) NOT NULL DEFAULT '' COMMENT '打款流水单号',
`name` varchar(255) NOT NULL DEFAULT '' COMMENT '打款方名称',
`money` decimal(15,6) NOT NULL DEFAULT '0.000000' COMMENT '打款金额',
`bank` varchar(255) NOT NULL DEFAULT '' COMMENT '打款方银行名称',
`remit_date` varchar(30) NOT NULL DEFAULT '' COMMENT '打款日期',
`contract_no` varchar(100) NOT NULL DEFAULT '' COMMENT '合同编号',
`proof_image` varchar(100) NOT NULL DEFAULT '' COMMENT '凭证图片',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`admin` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
`status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '当前状态（0-已提交 1-已修改 2-已驳回 3-已认款）',
`remark` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`parent_serial` char(50) NOT NULL DEFAULT '' COMMENT '记录拆分来源的流水单号',
`invoice_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '开票状态(0:未申请,10:已申请,20:已审核,25:部分开票,30:已开票,40:红冲中)',
`remit_unconsumed` varchar(255) NOT NULL DEFAULT '' COMMENT '未消耗到款金额',
`remit_balance` varchar(255) NOT NULL DEFAULT '' COMMENT '未开票到款金额',
PRIMARY KEY (`id`) USING BTREE,
KEY `receipt_serial` (`receipt_serial`)
) ENGINE=InnoDB AUTO_INCREMENT=4471 DEFAULT CHARSET=utf8 COMMENT='打款单'

### 收据表 DDL

CREATE TABLE `receipt` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`receipt_serial` char(50) NOT NULL DEFAULT '' COMMENT '流水单号',
`name` varchar(255) NOT NULL DEFAULT '' COMMENT '打款方名称',
`account` varchar(255) NOT NULL DEFAULT '' COMMENT '打款方账号',
`money` double(16,6) NOT NULL DEFAULT '0.000000' COMMENT '金额',
`bank` varchar(255) NOT NULL DEFAULT '' COMMENT '付款方银行名称',
`remit_date` int(30) unsigned NOT NULL DEFAULT '0' COMMENT '交易日期',
`status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '0, 1 未认款, 2 已认款, 3 拆分',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '增加时间',
`delete_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
`admin` varchar(100) NOT NULL DEFAULT '' COMMENT '操作人',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`parent_serial` char(50) NOT NULL DEFAULT '' COMMENT '记录拆分来源的流水单号',
`from` int(1) NOT NULL DEFAULT '0' COMMENT '来源 (0: 正常收款数据, 1:先票场景生成的占位数据)',
PRIMARY KEY (`id`),
KEY `receipt_serial` (`receipt_serial`)
) ENGINE=InnoDB AUTO_INCREMENT=4625 DEFAULT CHARSET=utf8 COMMENT='收款单'

### 渠道账号调整表 DDL

CREATE TABLE `channel_account_adjust` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
`title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
`customer_id` varchar(100) NOT NULL DEFAULT '' COMMENT '客户 ID',
`account_id` varchar(100) NOT NULL DEFAULT '' COMMENT '账号 ID',
`apikey` varchar(100) NOT NULL DEFAULT '' COMMENT '账号 apikey',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品',
`date` date DEFAULT NULL COMMENT '日期',
`money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本费用',
`fee_number` int(11) NOT NULL DEFAULT '0' COMMENT '计费用量',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注信息',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后一次修改时间',
`admin` varchar(20) NOT NULL DEFAULT '' COMMENT '最后一次操作人',
`channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '渠道 id',
`interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`encrypt` varchar(10) NOT NULL DEFAULT 'NO' COMMENT '加密方式:SHA256、CLEAR、MD5、NO',
`operator` varchar(20) NOT NULL DEFAULT '' COMMENT '运营商标识',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`run_id` char(32) NOT NULL DEFAULT '' COMMENT '调差任务 id',
`is_rerun_month_data` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否重跑月数据',
`category` tinyint(2) NOT NULL DEFAULT '0' COMMENT '任务类型:1 保底分摊,2 对账调整',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4970 DEFAULT CHARSET=utf8 COMMENT='渠道账号特殊消耗'

### ER 图

```mermaid
erDiagram
    remit ||--o{ receipt : "generates"
    remit ||--o{ channel_account_adjust : "adjusts"

    remit {
        int id PK
        string customer_id
        string receipt_serial
        string remit_serial
        decimal money
        int status
        int invoice_status
        decimal remit_unconsumed
        decimal remit_balance
    }

    receipt {
        int id PK
        string receipt_serial FK
        string name
        string account
        decimal money
        int status
        int from
    }

    channel_account_adjust {
        int id PK
        string customer_id
        string apikey
        int product_id
        decimal money
        int fee_number
        string operator
        int category
    }
```

1. remit：收款主表
   • 描述：客户汇款记录的主表，包含汇款编号、状态、可用余额、发票状态等。
   • 核心字段：
   • remit_serial：系统内唯一识别号
   • remit_unconsumed：未分配金额
   • remit_balance：当前余额（如被冲销则减少）
   • invoice_status：是否已开发票

⸻

2. remit 一对多 receipt （关系：generates）

说明：
• 每笔汇款可以生成一张或多张收款凭证（receipt）
• receipt.receipt_serial = remit.receipt_serial
• 逻辑是：一笔汇款可以“拆分”或“匹配”多个收据（如部分收、分账户收款）
• 等价字段：receipt.receipt_serial = remit.receipt_serial

⸻

3. remit 一对多 channel_account_adjust （关系：adjusts）

说明：
• 当某笔收款到账后，系统可能会做手动或自动“分摊”到账户余额或渠道账户
• 该表记录了此类对“账户余额”的分摊或调整行为
• 其中 channel_account_adjust.customer_id = remit.customer_id
• 用于同步客户维度下的资产变动
• 非完全等值，但为主外键绑定的包含关系

### 收款处理流程图

```mermaid
graph TD
    A["收款提交"] --> B["收据生成"]
    B --> C{"认款状态"}

    C -->|"未认款"| D["待认款处理"]
    C -->|"已认款"| E["生成打款单"]
    C -->|"拆分"| F["金额拆分"]

    D --> G["人工核实"]
    G --> E

    E --> H["打款单处理"]
    H --> I{"处理结果"}

    I -->|"已提交"| J["等待处理"]
    I -->|"已修改"| K["修改确认"]
    I -->|"已驳回"| L["重新提交"]
    I -->|"已认款"| M["完成认款"]

    M --> N["开票状态更新"]
    N --> O["未消耗金额记录"]
    N --> P["未开票金额记录"]

    F --> Q["生成子收据"]
    Q --> B
```

### 收款状态转换图

```mermaid
graph LR
    A["收据状态"] --> B["未认款:1"]
    A --> C["已认款:2"]
    A --> D["拆分:3"]

    E["打款单状态"] --> F["已提交:0"]
    E --> G["已修改:1"]
    E --> H["已驳回:2"]
    E --> I["已认款:3"]

    J["开票状态"] --> K["未申请:0"]
    J --> L["已申请:10"]
    J --> M["已审核:20"]
    J --> N["部分开票:25"]
    J --> O["已开票:30"]
    J --> P["红冲中:40"]

    Q["收款来源"] --> R["电话邦:0"]
    Q --> S["朴道:1"]
    Q --> T["百行:2"]

    U["调整类型"] --> V["保底分摊:1"]
    U --> W["对账调整:2"]
```
