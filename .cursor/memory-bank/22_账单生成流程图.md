该文档描述系统中账单生成的核心流程，用于梳理从调用数据到最终收款确认的业务逻辑。

⸻

## 账单生成流程图（Mermaid 源码）

flowchart TD
A[每日调用量统计] --> B[成本核算（渠道成本 / 使用量）]
B --> C[客户价格匹配（fee_config）]
C --> D[账单生成（按客户 / 产品聚合）]
D --> E[客户对账确认]
E --> F[发票申请与审核]
F --> G[收款确认]

⸻

## 关键流程说明

1. 调用数据统计
   • 按 account_id + product_id 汇总
   • 来源于 Redis 缓存、ClickHouse 或日志落库

2. 成本核算
   • 汇总当日渠道调用量
   • 结合 fee_config/channel_config 计算单位成本

3. 客户价格匹配
   • 不同客户使用不同定价策略：
   • 固定价格
   • 阶梯计费
   • 按运营商维度定价

4. 账单生成
   • 写入 bill_customer_income_v2 / v3 表
   • 可按月自动生成账单记录
   • 多币种 / 多渠道可拆分账单明细

5. 对账确认
   • 前台界面展示明细供客户校验
   • 支持账单导出 / 邮件发送

6. 发票申请
   • 客户通过系统申请开票
   • 可设置审批流、红冲、历史记录

7. 收款确认
   • 财务线下回款确认
   • 更新 remit 表、调整账单状态
   • 可触发回款认票逻辑（发票状态更新）
