## 客户调用统计

### 客户调用量统计表 DDL

CREATE TABLE `statistics_customer_usage` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品 ID',
`node` varchar(20) NOT NULL DEFAULT 'default' COMMENT '节点',
`operator` varchar(255) NOT NULL DEFAULT 'DEFAULT' COMMENT '运营商',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户调用日期（Ymd 格式）',
`total` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总调用量',
`success` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '成功调用量',
`valid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查得量',
`charge` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费量',
`extend_one` int(11) NOT NULL DEFAULT '0' COMMENT '扩展数量计数字段，存量洞察产品代表提交量',
`cache` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '换存量',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`call_product_id` int(10) NOT NULL DEFAULT '0' COMMENT '客户调用产品 ID',
PRIMARY KEY (`id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `product_id` (`product_id`) USING BTREE,
KEY `date` (`date`) USING BTREE,
KEY `apikey_product_id_date_operator` (`apikey`,`product_id`,`date`,`operator`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=951 DEFAULT CHARSET=utf8 COMMENT='客户调用量统计表'

### 客户联合调用统计表 DDL

CREATE TABLE `statistics_customer_together_call_usage` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费产品 ID',
`node` varchar(20) NOT NULL DEFAULT 'default' COMMENT '节点',
`operator` varchar(255) NOT NULL DEFAULT 'DEFAULT' COMMENT '运营商',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户调用日期（Ymd 格式）',
`sub_product_number` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '同时调用的子产品个数',
`total` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总调用量',
`success` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '成功调用量',
`valid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查得量',
`charge` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费量',
`extend_one` int(11) NOT NULL DEFAULT '0' COMMENT '扩展数量计数字段，存量洞察产品代表提交量',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `product_id` (`product_id`) USING BTREE,
KEY `date` (`date`) USING BTREE,
KEY `sub_product_number` (`sub_product_number`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1285376 DEFAULT CHARSET=utf8 COMMENT='子产品同时调用统计表'

## 产品调用统计

### 产品调用统计表 DDL

CREATE TABLE `statistics_customer_usage` (
`id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
`apikey` varchar(32) NOT NULL DEFAULT '' COMMENT '账号唯一标识',
`product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品 ID',
`node` varchar(20) NOT NULL DEFAULT 'default' COMMENT '节点',
`operator` varchar(255) NOT NULL DEFAULT 'DEFAULT' COMMENT '运营商',
`date` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '客户调用日期（Ymd 格式）',
`total` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总调用量',
`success` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '成功调用量',
`valid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查得量',
`charge` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费量',
`extend_one` int(11) NOT NULL DEFAULT '0' COMMENT '扩展数量计数字段，存量洞察产品代表提交量',
`cache` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '换存量',
`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
`update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后更新时间',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
`call_product_id` int(10) NOT NULL DEFAULT '0' COMMENT '客户调用产品 ID',
PRIMARY KEY (`id`) USING BTREE,
KEY `apikey` (`apikey`) USING BTREE,
KEY `product_id` (`product_id`) USING BTREE,
KEY `date` (`date`) USING BTREE,
KEY `apikey_product_id_date_operator` (`apikey`,`product_id`,`date`,`operator`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=951 DEFAULT CHARSET=utf8 COMMENT='客户调用量统计表'

### 接口调用统计表 DDL

CREATE TABLE `statistics_interface_usage` (
`id` int(11) NOT NULL AUTO_INCREMENT,
`iid` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`apikey` varchar(100) NOT NULL DEFAULT '',
`operator` char(15) NOT NULL DEFAULT '' COMMENT '运营商',
`encrypt` char(15) NOT NULL DEFAULT '' COMMENT '加密方式',
`product_id` int(11) NOT NULL DEFAULT '0' COMMENT '子产品 id',
`node` varchar(15) NOT NULL DEFAULT '' COMMENT '节点名称',
`date` int(11) NOT NULL DEFAULT '0' COMMENT '日期',
`total` int(11) NOT NULL DEFAULT '0' COMMENT '总调用量',
`success` int(11) NOT NULL DEFAULT '0' COMMENT '有效量，即接口调用成功',
`valid` int(11) NOT NULL DEFAULT '0' COMMENT '查得量',
`charge` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '计费量',
`extend_one` int(11) NOT NULL DEFAULT '0' COMMENT '扩展数量计数字段，存量洞察产品代表提交量',
`create_time` int(11) NOT NULL DEFAULT '0',
`update_time` int(11) NOT NULL DEFAULT '0',
`interface_id` int(11) NOT NULL DEFAULT '0' COMMENT '接口 id',
`source` tinyint(3) NOT NULL DEFAULT '0' COMMENT '来源 0 电话邦 1 朴道 2 百行',
PRIMARY KEY (`id`),
KEY `interface_id` (`interface_id`) USING BTREE,
KEY `date` (`date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1906 DEFAULT CHARSET=utf8

### ER 图

```mermaid
erDiagram
    statistics_customer_usage ||--o{ statistics_customer_together_call_usage : "relates"
    statistics_customer_usage ||--o{ statistics_interface_usage : "details"

    statistics_customer_usage {
        int id PK
        string apikey
        int product_id
        string node
        string operator
        int date
        int total
        int success
        int valid
        int charge
        int cache
        int source
        int call_product_id
    }

    statistics_customer_together_call_usage {
        int id PK
        string apikey
        int product_id
        string node
        string operator
        int date
        int sub_product_number
        int total
        int success
        int valid
        int charge
    }

    statistics_interface_usage {
        int id PK
        int interface_id
        string apikey
        string operator
        string encrypt
        int product_id
        string node
        int total
        int success
        int valid
        int charge
    }
```

1. statistics_customer_usage：客户产品调用统计主表
   • 作用：每日记录某客户（apikey）对某产品的调用概况。
   • 主要字段说明：
   • apikey：客户账号
   • product_id：产品编号
   • operator：运营商，如移动/电信
   • node：节点标识（可理解为调用链路或逻辑入口）
   • total/success/valid/charge/cache：统计量，如总量、成功量、计费量、缓存命中等
   • call_product_id：如为多产品聚合场景，表示该调用实际归属的子产品

⸻

2. statistics_customer_usage 一对多 statistics_customer_together_call_usage

关系名称：relates
描述：
• 用于记录联合调用/打包产品中各子产品的调用明细。
• 典型场景如邦信分私有云、套餐类产品。
• 字段映射：
• apikey = apikey
• product_id = product_id
• node = node
• operator = operator
• date = date
• 包含含义：每条 statistics_customer_usage 可对应多个 statistics_customer_together_call_usage 子明细，记录了联合调用中的“分摊子产品”调用量。

⸻

3. statistics_customer_usage 一对多 statistics_interface_usage

关系名称：details
描述：
• 用于记录某次产品调用实际落入了哪个上游接口（即哪个供应商、哪个通道）
• 字段映射：
• apikey = apikey
• product_id = product_id
• operator = operator
• node = node
• interface_id 表示具体上游通道接口。
• 包含含义：一条客户调用统计可能关联多个接口（如分流场景），此表用于进一步细化接口维度统计。

### 调用统计维度图

```mermaid
graph TD
    A["调用统计维度"] --> B["时间维度"]
    A --> C["空间维度"]
    A --> D["业务维度"]
    A --> E["来源维度"]

    B --> F["按日统计"]
    B --> G["按月统计"]
    B --> H["按年统计"]

    C --> I["节点维度"]
    C --> J["运营商维度"]

    D --> K["产品维度"]
    D --> L["接口维度"]
    D --> M["客户维度"]

    K --> N["单产品"]
    K --> O["联合调用"]

    L --> P["接口调用"]
    L --> Q["加密方式"]

    M --> R["客户调用"]
    M --> S["调用量"]

    E --> T["电话邦"]
    E --> U["朴道"]
    E --> V["百行"]
```

### 调用量计算流程图

```mermaid
graph TD
    A["API调用"] --> B["总调用量统计"]
    B --> C{"调用结果"}

    C -->|"成功"| D["成功调用量"]
    C -->|"失败"| E["失败调用量"]

    D --> F{"数据来源"}
    F -->|"缓存"| G["缓存调用量"]
    F -->|"实时"| H["实时调用量"]

    H --> I{"查询结果"}
    I -->|"有数据"| J["查得量"]
    I -->|"无数据"| K["未查得量"]

    J --> L["计费量计算"]
    G --> L

    L --> M["单产品计费"]
    L --> N["联合调用计费"]

    M --> O["生成账单"]
    N --> O

    O --> P["统计入库"]
```
