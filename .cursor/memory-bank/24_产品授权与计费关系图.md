该文档描述系统中账号、产品与计费配置之间的关联关系，适用于权限控制设计、账单精度分析、接口调用限额逻辑梳理。

---

## 产品授权与计费关系图（Mermaid 源码）

```mermaid
  erDiagram
      CUSTOMER ||--o{ ACCOUNT : 拥有
      ACCOUNT ||--o{ ACCOUNT_PRODUCT : 绑定
      PRODUCT ||--o{ ACCOUNT_PRODUCT : 授权
      PRODUCT ||--o{ FEE_CONFIG : 配置
      CHANNEL ||--o{ FEE_CONFIG : 决定价格
      ACCOUNT_PRODUCT ||--o{ LIMIT_CONFIG : 限额控制
```

---

## 实体关系说明

### 1. CUSTOMER → ACCOUNT

- 一个客户可拥有多个账号（如按部门分配）

### 2. ACCOUNT → ACCOUNT_PRODUCT → PRODUCT

- 一个账号可以被授权访问多个产品
- 授权控制包含：使用类型、合同状态、有效期、并发限制等

### 3. PRODUCT → FEE_CONFIG

- 每个产品对应多个定价策略（可按客户、运营商、等级）
- 价格与 account_product 配置联动

### 4. CHANNEL → FEE_CONFIG

- 上游渠道价格与客户价格差形成利润空间

### 5. ACCOUNT_PRODUCT → LIMIT_CONFIG

- 存储账号/产品级别调用限制：日限、月限、总量、并发数等

---

## 典型业务场景

- 判断某客户能否调用某产品？查 `account_product.status`
- 计费价格来自哪里？查 `fee_config.product_id + operator`
- 某客户月度封顶怎么设？查 `limit_config`
- 产品价格亏损？需对比 `fee_config.cost_price < sale_price`
