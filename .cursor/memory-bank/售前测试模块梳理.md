

## 售前测试模块业务全景分析

### 一、模块架构概览

售前测试模块是羽乐科技金融风控平台的核心业务模块，主要负责客户接入前的产品试用、效果验证和商机转化。该模块与飞书审批系统深度集成，实现了从申请到接入的全流程数字化管理。

### 二、核心业务流程

#### 1. 申请入口（飞书集成）
- **三种申请模板**：
  - 售前测试流程-新版 (`A743783F-3E0D-4A70-B1CF-254E9EE59E9A`)
  - 售前测试&测试账号申请 (`37E23EE8-E880-4A8B-A291-C00E50B54A23`)
  - 产品建模&定制申请 (`BD65205C-84C4-437B-8BE9-ABDED8451972`)

- **自动拉取机制**：
  - 定时任务：`PullApplyList.php` 拉取飞书审批数据
  - 表单映射：80+个字段的系统与飞书表单映射
  - 状态同步：审批状态与业务状态自动同步

#### 2. 测试状态管理（核心业务逻辑）
```php
// 主要状态流转
5: 待测试（初始值/默认值）
10: 测试中
15: 已终止（测试暂停/终止）
20: 待反馈（售前或技术测试完毕，等待客户反馈）
  21: 已逾期
  23: 超3个月未反馈
30: 已反馈（已反馈结果，等待接入意向反馈）
40: 不接入（无法接入，终态）
45: 已撤回（申请撤回）
50: 可接入
60: 已接入
```

### 三、测试产品体系

#### 1. 主要产品线
- **邦信分系列**：通信指数、通信评分
- **邦秒验系列**：在网时长核验、在网状态
- **金盾系列**：风险验证、策略字段
- **号码融系列**：融合产品、内部产品
- **联合建模**：仅售前测试使用
- **其他产品**：号码分、号码风险等级、事件分等

#### 2. 产品配置管理
- **样本量管理**：不同产品对应不同样本量要求
- **特征变量**：支持自定义特征变量数量
- **建模场景**：联合建模场景和方式配置

### 四、权限与角色管理

#### 1. 角色体系
- **销售人员**：提交申请、反馈结果、接入跟踪
- **售前人员**：测试执行、状态管理、技术支持
- **管理人员**：数据统计、风险监控、决策支持

#### 2. 操作权限
- **销售操作权限**：仅可操作待反馈、已反馈等特定状态
- **售前操作权限**：可修改待测试、测试中、已终止、待反馈状态
- **部门权限**：售前测试部门可查看全部销售数据

### 五、核心功能模块

#### 1. 移动端功能（销售H5）
```php
// 核心接口
myList()         // 我的申请列表
stayAll()        // 全部无反馈
fatherFeedback() // 反馈操作-产品列表
fatherAccess()   // 接入操作-产品列表
saveFeedback()   // 保存反馈信息
saveAccess()     // 保存接入信息
```

#### 2. PC端管理功能
```php
// 管理功能
list()           // 测试申请列表
processStatus()  // 修改状态
bindProduct()    // 增加测试产品
deleteProduct()  // 删除测试产品
bindCustomer()   // 绑定客户
batchSaveAccess() // 批量保存接入信息
```

#### 3. 统计分析功能
```php
// 数据统计
deptStatistics()      // 部门统计信息
salesStatistics()     // 销售统计信息
statisticsOverview()  // 总览数据
statisticsFeedback()  // 反馈数据
statisticsResult()    // 效果数据
statisticsAccess()    // 接入&调用统计
```

### 六、监控预警体系

#### 1. 反馈监控
- **逾期提醒**：自动检测超期未反馈的申请
- **定时预警**：通过飞书机器人发送预警消息
- **催促机制**：`FeedbackClock.php` 实现自动催促

#### 2. 风险监控
```php
// 风险统计功能
riskList()        // 部门人员风险统计列表
riskProductList() // 产品风险统计列表
riskDetailList()  // 风险明细列表
```

#### 3. 调用监控
- **客户监控**：跟踪客户API调用情况
- **稳定性监控**：监控调用稳定性
- **预警触发**：量级异常自动预警

### 七、数据库设计

#### 1. 核心表结构
- **pre_test_apply_customer**：客户申请主表
- **pre_test_apply_father**：主产品申请表
- **pre_test_apply_product**：子产品申请表
- **pre_test_apply_manual**：人工处理记录表
- **pre_test_action_log**：操作日志表
- **pre_test_action_record**：操作统计表

#### 2. 审批流转表
- **pre_test_approval**：审批主表
- **pre_test_approval_monitor**：监控审批表
- **pre_test_approval_transfer**：审批流转表

### 八、自动化运维

#### 1. 定时任务体系
```php
// 主要定时任务
PullApplyList          // 拉取飞书审批申请
CheckApplyStatus       // 检查申请状态
CheckManual            // 检查人工处理
FeedbackClock          // 反馈提醒
RecordActionLog        // 记录操作日志
ReportWeeklyPre        // 售前周报统计
```

#### 2. 数据同步机制
- **实时同步**：飞书审批数据实时拉取
- **状态同步**：业务状态与审批状态双向同步
- **异常处理**：同步失败自动重试机制

### 九、业务价值与特色

#### 1. 商业价值
- **提升转化率**：通过规范化流程提高客户接入成功率
- **降低成本**：自动化流程减少人工干预
- **风险控制**：多维度监控降低业务风险
- **决策支持**：丰富的统计数据支持业务决策

#### 2. 技术特色
- **深度集成**：与飞书审批系统无缝集成
- **状态机制**：完善的状态流转管理
- **监控预警**：全方位的监控预警体系
- **数据驱动**：基于数据的业务分析和优化

### 十、系统边界与集成

#### 1. 外部系统集成
- **飞书审批**：申请流程管理
- **客户系统**：客户信息同步
- **计费系统**：测试账号限额管理
- **监控系统**：API调用监控

#### 2. 内部模块协作
- **账单模块**：测试费用结算
- **客户模块**：客户信息管理
- **产品模块**：产品配置管理
- **统计模块**：数据分析报表

售前测试模块作为客户接入的关键环节，通过完善的流程管理、智能化的监控预警和数据驱动的决策支持，为公司的业务增长提供了重要保障。该模块体现了羽乐科技在金融科技领域的技术实力和业务理解深度。