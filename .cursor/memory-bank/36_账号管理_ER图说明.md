# 账号管理模块 ER 关系图详细说明

## 一、核心实体（account 表）

account 表是整个账号管理模块的核心实体，包含以下关键信息：

- 主键：id（自增）
- 业务主键：account_id（32 位字符）
- 身份标识：
  - cid（根据 account_id 生成的 MD5 前 8 位）
  - ctag（根据 account_id+"ctag"生成的 MD5 前 8 位）
- 基础信息：
  - account_name（账号名称）
  - email（登录邮箱）
  - password（登录密码）
- 业务属性：
  - customer_id（所属客户）
  - group_id（主体 ID）
  - father_id（父级账号）
  - type（账号类型：0 测试/1 正式）
  - status（状态：0 不可用/1 可用）
- 安全认证：
  - apikey（API 密钥）
  - appsecret（密钥）
  - access_ip（IP 白名单）
- 使用限制：
  - end_time（账号截止时间）
  - concurrency（秒并发数）
  - cache_days（缓存时长）

## 二、关联实体及关系

### 1. account_product（账号产品关联表）

- 关系：account 1:N account_product
- 关键字段：
  - account_id（外键）
  - product_id（产品 ID）
  - status（状态）
  - use_type（使用类型）
  - contract_status（签约状态）
- 限制配置：
  - daily_limit（日限额）
  - month_limit（月限额）
  - year_limit（年限额）
  - total_limit（总限额）
  - concurrency（并发数）

### 2. account_product_custom（账号产品自定义配置表）

- 关系：account 1:N account_product_custom
- 用途：对 account_product 的补充配置
- 关键字段：
  - father_id（父产品 ID）
  - product_id（子产品 ID）
  - apikey（账户密钥）
  - extend（扩展字段）
  - type（业务类型）
  - data（配置参数）

### 3. account_product_limit（账号产品限量测试表）

- 关系：account 1:N account_product_limit
- 用途：产品使用量限制管理
- 关键字段：
  - apikey（账户 ID）
  - product_id（产品 ID）
  - start_time（开始时间）
  - end_time（结束时间）
  - total_limit（总限额）

### 4. account_push（账号推送记录表）

- 关系：account 1:N account_push
- 用途：记录数据推送情况
- 关键字段：
  - apikey（账户密钥）
  - product_id（产品 ID）
  - status（推送状态）
  - is_error（是否预警）
  - input/output（输入输出数据）

### 5. account_transfer_config（账号转移配置表）

- 关系：account 1:N account_transfer_config
- 用途：管理账号间的调用量转移
- 关键字段：
  - customer_id（客户 ID）
  - father_id（父产品 ID）
  - from_apikey（源账号）
  - to_apikey（目标账号）
  - type（业务类型）

## 三、关系约束说明

1. 主键约束：

   - 所有表都使用自增 id 作为主键
   - account 表的 account_id 作为业务主键

2. 唯一性约束：

   - account 表：account_name 唯一
   - account_product 表：account_id + product_id 唯一
   - account_product_custom 表：apikey + father_id + product_id + extend + type 唯一
   - account_product_limit 表：apikey + product_id 唯一

3. 外键关系：

   - 所有关联表通过 apikey 或 account_id 关联到 account 表
   - 采用逻辑外键而非物理外键，提高性能

4. 索引设计：
   - account 表：建立了 account_id、father_id、type、end_time、email 等索引
   - 关联表：主要在关联字段上建立索引
   - 复合索引：根据查询需求设计的组合索引

## 四、业务完整性

1. 状态完整性：

   - 账号状态管理
   - 产品授权状态管理
   - 推送状态管理

2. 数据完整性：

   - 创建时间/更新时间的记录
   - 软删除机制
   - 备注信息的保存

3. 业务规则：
   - 账号层级关系（father_id）
   - 产品使用限制
   - 账号转移规则
