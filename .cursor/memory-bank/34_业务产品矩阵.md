# 邦信分

⸻

## 邦信分产品介绍（金融风控评分产品）

邦信分 是羽乐科技旗下核心数据产品之一，面向金融机构、信贷平台、保险科技、征信场景等，提供基于手机号维度的通信行为风控评分，评估用户的风险等级、偿还能力、欺诈倾向等。

⸻

## 产品体系结构

邦信分并非单一评分，而是一个系列化评分产品体系，包括但不限于以下子产品：

子产品版本 产品编码 特点和定位
邦信分-通信评分 1000 核心评分模型，基于用户通信行为特征建模，输出 0~1000 评分
邦信分-通信字段 210 基础字段接口，提供用户通话、短信、上网、活跃度等指标
邦信分快捷版 1100 轻量化评分服务，适用于低额度、秒贷场景
邦信分详单版 1101 提供详单级别字段统计分析，适配精细化风控需求
邦信分私有云版本 501 提供给大型金融机构的本地化部署能力

⸻

## 核心能力特征

评分维度设计
• 📱 通话行为：活跃度、通话频次、通话持续时间等
• 🧍 人际网络：联系人数量、频繁联系人的通话深度
• 📶 网络行为：上网活跃、晚间活跃时间段分布
• 🕒 稳定性评估：号码使用周期、在网时长、换号频率
• 🔍 异常行为：频繁更换设备、关联高风险用户等

⸻

## 模型能力说明

• 多因子融合评分模型（含监督学习+规则引擎）
• 模型版本可升级，具备评分解释能力（score explain）
• 默认评分输出为 0~1000，风险等级按区间划分：
• 800+：优质用户
• 600~800：中低风险
• 600 以下：高风险

⸻

## 接入与使用方式

• API 接口调用（支持 RESTful）
• 按次计费、支持测试账号
• 调用参数：手机号、身份证、姓名（部分产品）
• 返回内容：分值 + 等级标签 + 可解释字段 + trace_id

⸻

## 典型应用场景

应用场景 使用目的
信贷风控 用于贷款申请阶段的准入评估与定价策略
反欺诈识别 快速识别虚假用户、异常行为模式
客户分层营销 根据邦信分高低决定营销节奏与转化路径
风控预警 连续评分波动、异常下降触发二次核查机制
信用分补全 弥补传统征信缺失人群的信用评估盲区

⸻

## 商业模式与计费

• 按次计费：如每次调用计 0.05~0.2 元，视产品等级与客户而定
• 阶梯定价：调用量越大单价越低
• 私有部署：大客户可申请邦信分评分引擎本地化部署，按年授权

⸻

## 核心优势

能力项 说明
数据丰富度 基于运营商通信数据，远高于传统仅靠征信的数据广度
评分稳定性 算法经数千万数据训练优化，误报率低，风险识别精准
接入门槛低 仅需手机号等基础信息即可调用，覆盖高比例人群
模型透明度 支持可解释因子输出，便于用户理解评分来源
多产品适配能力 可适用于小额贷、现金贷、信用卡、保险、租赁等不同业务场景

⸻

# 邦秒验

⸻

邦秒验产品介绍（手机号三要素实时验证）

邦秒验 是羽乐科技推出的通信实名核验类产品，面向金融、支付、注册认证等场景，提供手机号、身份证、姓名等三要素实名验证与通信状态识别能力。其目标是帮助企业判断用户身份的真实性、号码有效性与活跃状态。

⸻

一、产品结构

邦秒验是一个产品族，涵盖多个独立子接口，围绕手机号属性展开验证。

接口名称 编码 功能说明
手机号三要素验证 200 实时核验手机号是否属于对应身份证和姓名
手机号在网时长查询 201 获取号码入网月数（反映活跃度和稳定性）
手机号在网状态 202 查询号码是否在网、停机或销户等状态
手机号消费等级 203 查询用户通信消费能力（分为高、中、低）
手机号常在地址 204 返回用户常用活动地省市（用于异常检测）
手机号异地使用风险评分 205 判断用户是否存在频繁异地高风险行为

⸻

二、应用价值

1. 实名验证
   • 在注册、开户、认证等场景中核验用户身份信息
   • 拦截黑产虚假注册、羊毛党行为

2. 通信状态评估
   • 判断手机号是否为注销、停机、不可用状态
   • 作为风控系统中的“联系稳定性”因子

3. 活跃与稳定性建模
   • 入网时长与消费等级反映号码的真实活跃度
   • 用户使用区域判断是否存在设备共享、跨省异常

⸻

三、典型应用场景

场景 目标描述
实名注册 注册环节进行三要素实名核验，防止虚假注册
贷款风控 用通信状态辅助评估用户是否可联系、是否稳定
客户分层 消费等级与活跃周期辅助营销和用户信用建模
欺诈识别 异地使用、多频切换设备等信号用于反欺诈模型
风控补全 用运营商级数据对征信缺失人群进行补充判断

⸻

四、接入方式
• 方式：REST API（HTTPS 调用）
• 参数：手机号必填，实名验证接口还需身份证和姓名
• 响应时间：通常 200ms 内响应
• 响应内容：返回验证结果、运营商归属、省市、状态等信息
• 调用频率：支持高并发调用，适配注册或授信实时处理场景

⸻

五、返回结果示例（手机号三要素接口）

{
"code": "0000",
"message": "验证成功",
"data": {
"is_match": true,
"carrier": "China Mobile",
"province": "Beijing",
"city": "Beijing",
"trace_id": "yz_20250617_xxxx"
}
}

⸻

六、定价与商业模式

模式 描述
按次计费 每次调用固定单价，按月结算
套餐包年 按调用量阶梯打包，享受单价折扣
定制组合 可与邦信分、金盾等风控产品组合销售

⸻

七、产品优势总结
• 接入轻便：参数简洁、快速集成、毫秒级响应
• 数据可信：基于三大运营商实名数据
• 功能丰富：支持多维判断（实名、状态、等级、活跃度）
• 模型友好：支持输出可量化字段用于风控建模
• 适配场景广：覆盖注册、认证、信贷、反欺诈、营销等多场景

⸻

## 金盾产品

⸻

金盾产品介绍（号码风险等级识别）

金盾 是羽乐科技推出的通信反欺诈产品，聚焦于通过手机号行为、通信模式、风险信号等多维信息，识别高风险用户，拦截欺诈行为，服务于金融、支付、保险、出行等需要“反欺诈”和“信任度识别”的场景。

⸻

一、产品结构与子产品分类

金盾产品线由多个子产品组成，提供从黑灰名单识别、风险等级评分到欺诈标签输出的多维能力。

子产品名称 编码 功能说明
号码风险等级评分 615 对手机号进行多因子风险评分，输出风险等级
金盾贷前查询 616 聚焦于信贷场景，评估手机号是否存在异常风险
号码风险标签识别 617 输出手机号的标签画像（如黑产、羊毛党等）
风险规则命中明细 618 返回风险命中详情，便于客户理解原因

⸻

二、核心能力说明

1. 风险评分
   • 对手机号风险程度进行量化评分
   • 输出风险等级：高风险 / 中风险 / 低风险 / 无风险
   • 结合规则命中、标签权重、历史行为评分等计算

2. 多维标签识别
   • 包含黑名单、异常呼叫行为、设备频繁切换、投诉记录等标签
   • 输出标签数量、标签命中概率等结构化字段

3. 场景适配优化
   • 针对贷前风控、注册核验、支付验证、保险投保等场景定制特征
   • 结果结构灵活，支持评分 + 标签 + 规则明细输出

⸻

三、数据来源与建模逻辑
• 多维数据融合：通信行为、活跃轨迹、黑名单库、同号群组、用户画像等
• 标签体系建设：超 50 个风险标签（如设备共享、诈骗投诉、高频换号）
• 模型算法：基于规则引擎 + 风险聚类分析 + 异常模式识别组合

⸻

四、典型应用场景

场景 目标用途
贷款准入 在贷前阶段评估用户手机号是否具备潜在风险
注册验证 在平台注册时快速识别批量注册、黑产行为
营销拦截 避免向高风险号码发短信、推送广告等浪费资源
欺诈识别 用于反欺诈模型中作为独立变量或规则命中因子
多账户识别 识别设备/号码共用场景，判断是否为团伙化操作

⸻

五、产品输出说明
• 输出字段：风险等级、风险评分、标签列表、命中规则详情、traceId
• 调用方式：RESTful API，支持高并发请求
• 响应时间：300ms 内
• 稳定性：支持 99.99% SLA，异常情况自动重试

⸻

六、返回结果示例

{
"code": "0000",
"message": "查询成功",
"data": {
"risk_level": "高风险",
"risk_score": 850,
"tags": ["黑产手机号", "设备共享", "短期换号"],
"rules_hit": [
{"rule_id": "R01", "description": "命中设备共享黑名单"},
{"rule_id": "R05", "description": "30 日内出现高频注册行为"}
],
"trace_id": "jd_20250617_xxxx"
}
}

⸻

七、计费与商业模式

模式 说明
按次计费 按每次风险查询进行计费，不同字段/结构计费不同
套餐组合 可与邦信分、邦秒验组合销售，形成信用+实名+反欺诈闭环
包年授权 大型客户支持包年接入和 SLA 服务保障

⸻

八、产品优势总结
• 全面：融合数十类风险标签，覆盖黑产、设备行为、团伙等多维风险
• 快速：毫秒级响应，支持高并发调用，适配实时决策系统
• 可解释：返回命中规则与标签，便于系统联动与人工审核
• 易集成：标准 JSON 结构，兼容金融、保险等 API 平台
• 可扩展：支持客户自定义风险等级标准和字段输出

⸻

# 邦企查

⸻

邦企查产品功能说明

邦企查 是羽乐科技推出的一款面向 B 端客户的企业信息查询服务，聚焦于帮助金融、保险、采购、风控等行业对目标企业进行资质核查、风险排查和经营画像分析。

⸻

一、产品定位

邦企查通过整合工商注册数据、司法风险、经营行为、股权结构等企业级信息，为业务方提供实时、结构化、合规可查的企业全景画像。

适用于客户准入、合同审核、授信审批、供应商引入、尽调风控等场景。

⸻

二、核心功能特点

1. 企业基础信息查询
   • 查询企业名称、统一社会信用代码、注册资本、成立时间、法人代表等基本工商数据
   • 数据来源于国家企业信用信息公示系统等权威渠道

2. 法律风险查询
   • 涉诉信息（作为原告/被告）
   • 法院公告、执行公告
   • 失信被执行人信息
   • 限高记录、司法冻结情况

3. 股权结构分析
   • 直接/间接股东列表
   • 股权比例与控制关系链
   • 最终受益人识别
   • 实际控制人定位

4. 经营行为追踪
   • 历史经营异常记录
   • 行政处罚记录
   • 年报公示信息
   • 纳税信用等级

5. 关联企业网络
   • 法人/股东名下其他企业
   • 高管交叉任职关系
   • 企业对外投资网络
   • 多层股权穿透结果

6. 风险等级评分（部分版本支持）
   • 系统评估企业经营稳定性、法律合规性、信用风险等指标
   • 输出风险标签与评分，供信贷/授信策略参考

⸻

三、使用方式
• 接口调用：提供 RESTful API 接口，支持企业名/统一信用代码查询
• 批量查询：支持对企业批量查询，适配尽调、反洗钱、KYC 场景
• 异步回调：支持大企业网络结构查询任务型返回（异步回调结果）

⸻

四、典型应用场景

场景 目标用途
客户准入审核 审查目标企业是否合法合规，核查黑名单记录
信贷授信审批 审核企业风险等级与经营状况，评估信用额度
供应商尽调 核查供应商资质、关联公司、法律风险
合作伙伴调研 梳理股权结构、法人背景、是否存在关联风险
风控模型变量输入 将司法记录、成立年限、税务等级等作为风险模型因子

⸻

五、返回结果字段（示例）

{
"company_name": "深圳市某科技有限公司",
"credit_code": "91440300MA5XXXXXX9",
"status": "存续",
"legal_person": "张某",
"register_capital": "5000 万人民币",
"established_date": "2017-08-15",
"risk_tags": ["曾列入经营异常", "被执行人记录"],
"shareholders": [...],
"actual_controller": "李某（间接控制）",
"lawsuits": 2,
"penalties": 1
}

⸻

六、产品优势

维度 优势说明
数据广泛 涵盖工商、司法、税务、公告、股权、行为等多维数据
查询稳定 实时接口响应，支持高并发调用
合规合法 数据来源正规、结构化处理，满足监管审查需求
分析能力强 提供结构化穿透、控制人识别、风险标签、网络图能力
接入灵活 支持按单调用、批量处理、异步任务等多种接入方式

⸻

# 邦秒配

⸻

邦秒配产品说明文档

一、产品简介

邦秒配 是羽乐科技推出的 智能路由分发引擎，主要服务于 API 接口中台架构，解决“同一类接口在多渠道、多价格、多成功率”下的最优调度问题，以提升整体服务稳定性和成功率。

它通过预配置规则、实时质量反馈、价格优先级与重试机制，实现 API 请求在多个可用“渠道”之间的自动调度与容灾重试。

⸻

二、核心功能特点

1. 渠道智能调度（Channel Routing）
   • 根据接口调用类型、客户标识、请求参数等维度，动态分配合适的上游渠道
   • 支持静态权重配置与动态调整相结合

2. 多策略分发
   • 成功率优先：自动选择成功率最高的渠道
   • 价格优先：优先选择价格最低渠道，适用于盈利最大化
   • 权重随机：基于权重进行渠道随机分发，防止单一渠道压力过高
   • 黑白名单控制：按产品、客户配置排除某些渠道或强制走特定通道

3. 渠道状态监控
   • 定时轮询上游渠道状态（Ping 机制）
   • 实时统计成功率、超时率、异常错误码等
   • 支持自定义“降级判定策略”：自动下线故障渠道、熔断机制触发

4. 多级容灾重试机制
   • 支持同步/异步重试
   • 一次请求失败后可根据预设“重试策略”选择其他渠道再次请求
   • 可配置最大重试次数、重试等待时间、渠道兜底策略

5. 接口响应结果比对（差异检测）
   • 可配置多渠道并发请求（竞速模式），取最快成功返回
   • 可对返回字段做一致性校验，识别上游数据差异

6. 动态规则引擎
   • 支持按“客户等级、产品维度、时间段”等配置调度规则
   • 规则引擎支持热更新，无需重启系统即可生效
   • 可接入外部评分系统，如“渠道健康度评分”模块

⸻

三、系统架构简要说明

graph TD
A[客户请求 API 接口] --> B{路由引擎}
B --> C1[渠道 A]
B --> C2[渠道 B]
B --> C3[渠道 C]
C1 --> D[结果返回]
C2 --> D
C3 --> D

    •	入口网关：统一接收请求，调用邦秒配组件决策
    •	邦秒配内核：根据当前产品、客户、时间、请求质量评分进行决策
    •	渠道组件：每个上游渠道封装为独立连接器
    •	反馈系统：收集每次调用结果反馈更新模型参数

⸻

四、典型应用场景

应用场景 示例说明
多渠道比价场景 同一手机号认证接口在多个渠道价格不同，选择最优
高可用场景 上游 A 故障时自动切换至 B，保障业务不中断
成本控制场景 实现按时间段、按客户动态选择低价渠道
服务治理场景 对渠道进行性能评分、异常自动剔除

⸻

五、核心价值

维度 价值体现
成功率提升 动态调度+重试策略显著提升 API 调用成功率
成本优化 通过价格优先或策略组合实现调用成本可控
系统稳定性 多通道冗余+容灾机制保障服务高可用
运维便捷 可视化配置+动态热更新，大幅减少人工介入
风控合规 可配置渠道黑白名单、对客户及行业做精准策略控制

⸻

六、返回字段示例

{
"code": 0,
"message": "success",
"channel_used": "XFYD",
"cost": 0.015,
"retry_count": 1,
"result": {
"status": "verified",
"details": "实名一致"
}
}

⸻

# 号码分

以下是对 号码分（也称“手机号码评分”）产品的介绍，适用于金融风控、实名核验、用户画像等应用场景：

⸻

📱 号码分产品介绍

一、产品概述

号码分 是羽乐科技旗下的智能风控产品之一，针对中国大陆手机号，基于运营商数据、使用行为、号段风险、历史黑名单、欺诈行为等多维度数据进行建模，为每个手机号输出一个风险评分值，帮助企业识别号码安全等级。

该产品广泛应用于 金融信贷、注册风控、电商下单、社交实名 等场景，是客户风险画像的重要组成部分。

⸻

二、核心功能

功能模块 功能说明
风险评分 输出手机号在当前时间点的风险评分（0~100 分，分值越高风险越低）
风险等级划分 将分值划分为低风险、中风险、高风险三档，支持策略联动
号段风险识别 判断号码是否为虚拟运营商号段、170/171 类高风险号段
黑名单检测 检查该手机号是否历史出现在黑名单或命中欺诈库
运营商归属地分析 返回运营商类型（移动/联通/电信/虚拟）、归属地省市信息等
使用活跃度估算 基于历史使用频次和活跃行为进行活跃度评分
关联风险提示 判断是否与高风险身份证、设备、IP 存在频繁绑定关系（适用于养号识别）

⸻

三、产品输出字段

{
"code": 0,
"message": "success",
"data": {
"mobile": "13800138000",
"score": 83,
"risk_level": "低风险",
"carrier": "移动",
"province": "北京",
"city": "北京市",
"is_virtual": false,
"blacklist": false,
"activity": "活跃",
"remarks": "历史无风险"
}
}

⸻

四、典型应用场景

场景 应用说明
注册风控 新用户注册时校验手机号是否为真实、是否高风险，决定是否放行或走人脸识别
授信前评估 结合“邦信分/号码分”判断用户是否可放款，是否需二次认证
营销反欺诈 拦截垃圾营销号、黑产注册、模拟用户等
投放用户筛选 广告/短信投放前基于号码评分进行精准筛选

⸻

五、核心优势
• ✅ 评分模型精准：融合数百维度特征，结合大模型与规则引擎
• ✅ 支持实时接入：接口毫秒级响应，支持高并发调用
• ✅ 本地化运营商覆盖：覆盖三大运营商及 170/171 虚商，全面支持
• ✅ 灵活定制策略：可按行业/客户需求配置评分阈值与响应字段

⸻

六、产品定位
• 是“邦信分”的组成之一，可作为风控链路中手机号层级风控模块
• 与“金盾”、“邦秒验”联动，实现多维交叉验证
• 可扩展接入微信实名、支付宝实名、银行卡预留号等上下游接口增强能力

⸻

# 号码分

以下是对 号码分（也称“手机号码评分”）产品的介绍，适用于金融风控、实名核验、用户画像等应用场景：

⸻

一、产品概述

号码分 是羽乐科技旗下的智能风控产品之一，针对中国大陆手机号，基于运营商数据、使用行为、号段风险、历史黑名单、欺诈行为等多维度数据进行建模，为每个手机号输出一个风险评分值，帮助企业识别号码安全等级。

该产品广泛应用于 金融信贷、注册风控、电商下单、社交实名 等场景，是客户风险画像的重要组成部分。

⸻

二、核心功能

功能模块 功能说明
风险评分 输出手机号在当前时间点的风险评分（0~100 分，分值越高风险越低）
风险等级划分 将分值划分为低风险、中风险、高风险三档，支持策略联动
号段风险识别 判断号码是否为虚拟运营商号段、170/171 类高风险号段
黑名单检测 检查该手机号是否历史出现在黑名单或命中欺诈库
运营商归属地分析 返回运营商类型（移动/联通/电信/虚拟）、归属地省市信息等
使用活跃度估算 基于历史使用频次和活跃行为进行活跃度评分
关联风险提示 判断是否与高风险身份证、设备、IP 存在频繁绑定关系（适用于养号识别）

⸻

三、产品输出字段

{
"code": 0,
"message": "success",
"data": {
"mobile": "13800138000",
"score": 83,
"risk_level": "低风险",
"carrier": "移动",
"province": "北京",
"city": "北京市",
"is_virtual": false,
"blacklist": false,
"activity": "活跃",
"remarks": "历史无风险"
}
}

⸻

四、典型应用场景

场景 应用说明
注册风控 新用户注册时校验手机号是否为真实、是否高风险，决定是否放行或走人脸识别
授信前评估 结合“邦信分/号码分”判断用户是否可放款，是否需二次认证
营销反欺诈 拦截垃圾营销号、黑产注册、模拟用户等
投放用户筛选 广告/短信投放前基于号码评分进行精准筛选

⸻

五、核心优势
• ✅ 评分模型精准：融合数百维度特征，结合大模型与规则引擎
• ✅ 支持实时接入：接口毫秒级响应，支持高并发调用
• ✅ 本地化运营商覆盖：覆盖三大运营商及 170/171 虚商，全面支持
• ✅ 灵活定制策略：可按行业/客户需求配置评分阈值与响应字段

⸻

六、产品定位
• 是“邦信分”的组成之一，可作为风控链路中手机号层级风控模块
• 与“金盾”、“邦秒验”联动，实现多维交叉验证
• 可扩展接入微信实名、支付宝实名、银行卡预留号等上下游接口增强能力

⸻
