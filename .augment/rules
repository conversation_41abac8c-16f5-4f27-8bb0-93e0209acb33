# Augment 项目规则配置

## 基本交流规范
- **必须使用中文进行所有对话交流**
- 代码注释和文档说明优先使用中文
- 变量命名和函数命名使用英文，但注释说明使用中文

## 项目技术栈与框架规范

### Laravel Lumen 5.5 框架规范
- 基于 Laravel Lumen 5.5 微框架开发
- PHP 7.1+ 环境要求
- 严格遵循 PSR-4 自动加载规范
- 使用 Composer 进行依赖管理

### 数据库操作规范
- **优先使用 Eloquent ORM** 进行数据库操作
- 对于复杂查询使用 Query Builder
- 支持多数据库：MySQL（主要）、MongoDB、ClickHouse、Redis
- 使用 BaseModel 作为所有模型的基类
- 支持数据库分表机制（通过 setTableSuffix 方法）
- 敏感数据使用 AES 加密存储（参考 BaseModel 中的 aes_decrypt 配置）

### 控制器开发规范
- 所有控制器继承自 CommonController
- 使用 Repository 模式分离业务逻辑
- 控制器方法应简洁，主要负责请求处理和响应
- 统一使用 ResponseTrait 处理 API 响应格式

### API 开发规范
- 遵循 RESTful API 设计原则
- 使用统一的响应格式（通过 ResponseTrait）
- API 路由统一在 routes/web.php 中定义
- 使用中间件进行权限控制和请求预处理
- 支持 OAuth2 认证（通过 lumen-passport）

### 代码组织结构规范
- **Controllers**: HTTP 请求处理层
- **Repository**: 数据访问层，封装数据库操作
- **Models**: 数据模型层，继承 BaseModel
- **Providers**: 服务提供者，用于依赖注入和服务绑定
- **Jobs**: 异步任务处理
- **Exports/Imports**: Excel 文件处理
- **TraitUpgrade**: 通用功能 Trait
- **Utils**: 工具类集合

### 金融业务特定规范
- 金额计算使用 decimal 类型，保持精度
- 敏感财务数据必须加密存储
- 账单相关操作需要详细日志记录
- 客户数据访问需要权限验证
- 支持多种计费模式和分账机制

## PHP 代码风格规范

### 命名规范
- 类名使用 PascalCase（如：ProductController）
- 方法名使用 camelCase（如：getCustomerInfo）
- 变量名使用 camelCase（如：$customerData）
- 常量使用 UPPER_SNAKE_CASE（如：MAX_RETRY_COUNT）
- 数据库表名使用 snake_case（如：customer_bills）

### 代码结构规范
- 每个类文件必须包含完整的命名空间声明
- 使用 use 语句导入依赖类
- 方法和属性必须有适当的可见性声明
- 复杂业务逻辑封装在 Repository 或 Service 类中

### 注释规范
- 类和方法必须有 PHPDoc 注释
- 注释使用中文描述功能和用途
- 复杂业务逻辑需要详细的行内注释
- 示例格式：
```php
/**
 * 获取客户账单信息
 * @param int $customerId 客户ID
 * @param string $month 月份（格式：YYYY-MM）
 * @return array 账单数据
 */
public function getCustomerBill($customerId, $month)
```

## 第三方集成规范

### 微信企业号集成
- 使用 overtrue/laravel-wechat 包
- 配置文件位于 config/wechat.php
- 告警通知统一通过微信发送

### MongoDB 集成
- 使用 jenssegers/mongodb 包
- MongoDB 模型以 Mongo 前缀命名
- 用于日志存储和大数据分析

### Excel 处理
- 使用 maatwebsite/excel 包
- 导出类放在 app/Exports 目录
- 导入类放在 app/Imports 目录

### 队列任务
- 使用 Laravel 队列系统处理异步任务
- 任务类放在 app/Jobs 目录
- 重要业务操作使用队列异步处理

## 安全规范
- 所有用户输入必须验证和过滤
- 敏感数据使用 AES 加密
- API 接口使用 OAuth2 认证
- 数据库查询防止 SQL 注入
- 文件上传需要类型和大小限制

## 性能优化规范
- 使用 Redis 进行缓存
- 数据库查询优化，避免 N+1 问题
- 大数据量操作使用分页
- 耗时操作使用队列异步处理
- 合理使用数据库索引

## 错误处理规范
- 使用统一的异常处理机制
- 错误信息记录到日志文件
- API 错误返回标准格式
- 生产环境不暴露敏感错误信息

## 测试规范
- 编写单元测试覆盖核心业务逻辑
- 使用 PHPUnit 进行测试
- 测试文件放在 tests 目录
- 重要功能必须有测试用例

## 部署和环境规范
- 使用环境变量管理配置
- 区分开发、测试、生产环境
- 使用 Composer 管理依赖
- 遵循 12-Factor App 原则

## 日志记录规范
- 重要业务操作必须记录日志
- 使用结构化日志格式
- 敏感信息不记录到日志
- 日志文件按时间分割存储

## 代码审查规范
- 新功能开发必须经过代码审查
- 遵循项目既定的代码风格
- 确保代码可读性和可维护性
- 重构代码时保持向后兼容
