<?php

namespace App\Http\Controllers;

use App\Http\Repository\ApprovalRepository;
use App\Models\SystemSession;

/**
 *  审批流
 * @package App\Http\Controllers
 */
class ApprovalController extends CommonController
{
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new ApprovalRepository();
	}

    /**
     * 获取待审批列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function approvalList()
    {
        try {
            $data = $this->repository->approvalList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
	public function approvalDeal()
    {
        try {
			$session_id = request()->post('user_cookie','');
            $data = $this->repository->approvalDeal((new SystemSession())->getNameBySessionId($session_id));
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

    public function getApprovalConfig()
    {
        try {
            $user_cookie = request()->post('user_cookie', null);
            $user_name = '';
            if ($user_cookie) {
                $user_name = SystemSession::where(['session_id' => $user_cookie])->first();
                if (empty($user_name)) {
                    $this->createBaseResponse('登录已过期，请重新登录', 50001, []);
                }
                $user_name = $user_name->toArray();
                if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)){
                    $user_name = $match[1];
                }
            }
            $data = $this->repository->getApprovalConfig($user_name);

            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }
}