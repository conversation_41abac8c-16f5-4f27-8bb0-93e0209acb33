<?php

namespace App\Http\Controllers;

use App\Http\Repository\WeeklyStatisticRepository;
use Illuminate\Http\JsonResponse;
use Exception;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class WeeklyStatisticController extends CommonController
{
    private $repository;

    /**
     * WechatWarningController constructor.
     * @param WeeklyStatisticRepository $repository
     */
    public function __construct(WeeklyStatisticRepository $repository) {
        parent::__construct();
        $this->repository = $repository;
    }


    /**
     * 获取任务列表
     *
     * @throws Exception
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 11:36:17
     */
    public function taskList() {
        $data = $this->repository->task_list();
        return $this->response(0, $data);
    }


    /**
     * 获取任务类型列表
     *
     * @throws Exception
     * @return JsonResponse
     * <AUTHOR> 2024-01-25 15:57:12
     *
     */
    public function taskTypeList() {
        $data = $this->repository->task_type_list();
        return $this->response(0, $data);
    }

    public function taskStatusList() {
        $data = $this->repository->task_status_list();
        return $this->response(0, $data);
    }

    /**
     * 获取任务类型列表
     *
     * @throws Exception
     * @return JsonResponse
     * <AUTHOR> 2024-01-25 15:57:12
     *
     */
    public function taskAdd() {
        $data = $this->repository->add_task();
        return $this->response(0, $data);
    }


    /**
     *
     *
     * @return BinaryFileResponse
     * @throws Exception
     * <AUTHOR> 2024-01-25 16:42:44
     *
     */
    public function export($id) {
        return $this->repository->export($id);
    }


    /**
     * 任务结果预览
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-01-25 16:58:36
     */
    public function info($id) {
        $data = $this->repository->info($id);
        return $this->response(0, $data);
    }
}
