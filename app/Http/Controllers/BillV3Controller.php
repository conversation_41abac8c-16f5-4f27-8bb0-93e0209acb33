<?php

namespace App\Http\Controllers;

use App\Http\Repository\BillV3Repository;
use App\Models\RerunBillRecord;
use App\Providers\BillCustomerIncome\BillCustomerIncome;
use App\Providers\BillCustomerIncome\CreateBillCustomerIncome;
use App\Providers\BillIncome\CreateBillIncome;
use App\Providers\RedisCache\RedisCache;

/**
 * Class BillV3Controller 新版账单控制器
 * @package App\Http\Controllers
 */
//class BillV3Controller extends Controller
class BillV3Controller extends CommonController
{
	/**
	 * @var BillV3Repository 代码仓库
	 */
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new BillV3Repository();
	}
	
	/**
	 * 重新生成营收账单
	 */
	public function reCreateIncome()
	{
		$status = $this->repository->reCreateBillIncome();
		//返回响应
		$this->createSimpleResponse('bill.' . $status);
	}
	
	/**
	 * 客户对账单列表
	 */
	public function getCustomerStatementList()
	{
		$result = $this->repository->customerStatementList();
		
		if (is_array($result)) {
			$this->createDataResponse($result);
		}
		$this->createSimpleResponse('bill.' . $result);
	}
	
	/**
	 * 客户对账单明细
	 */
	public function getCustomerStatementDetails()
	{
		$result = $this->repository->customerStatementDetails();
		
		if (is_array($result)) {
			$this->createDataResponse($result);
		}
		$this->createSimpleResponse('bill.' . $result);
	}
	
	/**
	 * 客户对账单发送邮件
	 */
	public function sendStatementMail()
	{
		$result = $this->repository->sendStatementMail();
		
		$this->createSimpleResponse('bill.' . $result);
	}

    /**
     * 客户对账单批量发送
     */
    public function bulkSendStatementMail()
    {
        $result = $this->repository->bulkSendStatementMail();
        if (is_array($result)) {
            $this->createDataResponse($result);
        }
        $this->createSimpleResponse('bill.' . $result);
    }

	//重跑账单
    public function rerunBill(){
        $result = $this->repository->rerunBillRecord();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function getRerunBillList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $father_id = request()->post('father_id', '');
        $customer_id = request()->post('customer_id', '');
        $offset = ($page - 1) * $limit;
        $where = ['type' => 1];
        if($father_id){
            $where['father_id'] = $father_id;
        }
        if($customer_id){
            $where['customer_id'] = $customer_id;
        }

        $res = RerunBillRecord::getList($where, $offset, $limit);
        foreach ($res['data'] as &$val){

            $val['father_name'] = RedisCache::instance('productId_productName_mapping')->get($val['father_id']);
            if($val['customer_id'] == 'all'){
                $val['customer_name'] = '全部';
            }else{
                $val['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($val['customer_id']);

            }

            $val['start_date'] = date('Y-m-d', strtotime($val['start_date']));
            $val['end_date'] = date('Y-m-d', strtotime($val['end_date']));
            $val['create_at'] = $val['create_at'] ? date('Y-m-d H:i:s', $val['create_at']) : '';

        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);

    }

    public function saveCustomerBillAdjust(){
        $result = $this->repository->saveCustomerBillAdjust();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function customerBillAdjustList(){
        $result = $this->repository->customerBillAdjustList();
        $this->createDataResponse(['list' => $result['data'], 'count' => $result['count'] ?? 0, 'sum_number' =>$result['sum_number'], 'sum_money' =>$result['sum_money'] ]);
    }

    public function customerBillAdjustInfo(){
        $result = $this->repository->customerBillAdjustInfo();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function customerBillAdjustDel(){
        $result = $this->repository->customerBillAdjustDel();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function getCustomerExpendList()
    {
        $result = $this->repository->getCustomerExpendList();
        $this->createDataResponse($result);
    }

    public function getCustomerExpendInfo()
    {
        $result = $this->repository->getCustomerExpendInfo();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function saveCustomerExpend()
    {
        $result = $this->repository->saveCustomerExpend();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function delCustomerExpend()
    {
        $result = $this->repository->delCustomerExpend();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function downloadCustomerExpend()
    {
        try {
            return $this->repository->downloadCustomerExpend();
        } catch (\Exception $e) {
            return $this->createBaseResponse('导出失败', 100, []);
        }
    }

    public function downloadCustomerStatementList()
    {
        $res = $this->repository->downloadCustomerStatementList();
        if (!$res) {
            $this->response(100, [], '获取数据失败');
        }
        return $this->response(0, $res);
    }

    public function checkBillOperator(){
        $result = $this->repository->checkBillOperator();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function billOperatorList(){
        $result = $this->repository->billOperatorList();
        $this->createDataResponse(['list' => $result['data'], 'count' => $result['count'] ]);
    }

    public function confirmOperatorBill(){
        $result = $this->repository->confirmOperatorBill();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

}