<?php

namespace App\Http\Controllers;

use App\Http\Repository\UserRepository;

/**
 * @uses UserController
 */
class UserController extends CommonController
{

    /**
     * PC端-user Ticket
     * @return \Illuminate\Http\JsonResponse
     * @uses userTicket
     */
    public function userTicket() {
        try {
            $data = (new UserRepository())->userTicket();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage() . ', ' .$exception->getLine());
        }
    }
}