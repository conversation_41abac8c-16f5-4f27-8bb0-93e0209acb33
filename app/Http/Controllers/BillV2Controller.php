<?php

/**
 * @apiDefine BillName 账单
 **/

namespace App\Http\Controllers;

use App\Http\Repository\BillRepository;
use App\Http\Repository\BillV2Repository;
use App\Http\Repository\V2ProfitRepository;
use App\Models\MongoCacheExcelV2;
use App\Models\MongoLog;
use App\Support\CustomException;
use App\TraitUpgrade\ResponseTrait;
use Laravel\Lumen\Routing\Controller;

//class BillV2Controller extends Controller
class BillV2Controller extends CommonController
{
    use ResponseTrait;

    private $repository;

    /**
     * BillController constructor.
     *
     * @param $repository
     */
    public function __construct(BillV2Repository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 邮件发送对账单
     * @return \Illuminate\Http\JsonResponse
     */
    public function email()
    {
        try {
            $this->repository->email();

            return $this->response(['msg' => '邮件发送成功']);
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage().' at line '.$e->getLine().' at file '.$e->getFile());
        }
    }


    /**
     * 对账单
     *
     * @param $customer_id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function history($customer_id)
    {
        try {
            $history = $this->repository->history($customer_id);

            return $this->response(compact('history'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage().' at line '.$e->getLine().' at file'.$e->getFile());
        }
    }


    /**
     * 生成excel对应的消费明细
     *
     * @param $customer_id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function excel($customer_id)
    {
        try {
            $consumption = $this->repository->excel($customer_id);
            //            $model = new MongoCacheExcelV2;
            //            $month = date('Ym', strtotime('last day of last month'));
            //            $data  = $model->where('customer_id', $customer_id)
            //                ->where('month', intval($month))
            //                ->orderBy('created_at', 'desc')
            //                ->pluck('excel')
            //                ->toArray();
            //
            //            if (empty($data)) {
            //                throw new \Exception('不存在缓存的账单数据');
            //            }
            //
            //            $consumption = json_decode($data[0], true);
            return $this->response(compact('consumption'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage().' at file '.$e->getFile().' at line '.$e->getLine());
        }
    }

    /**
     * 客户对账单列表
     *
     * @access public
     *
     * @return \Illuminate\Http\JsonResponse
     **/
    public function customers()
    {
        try {
            $lists = $this->repository->customers();

            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage().' at line '.$e->getLine().' at file '.$e->getFile());
        }
    }

    /**
     * 客户产品对账单列表
     *
     * @access public
     *
     * @return \Illuminate\Http\JsonResponse
     **/
    public function getProductBillOfCustomer($customer_id)
    {
        try {
            $lists = $this->repository->getProductBillOfCustomer($customer_id);

            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage().' at line '.$e->getLine().' at file '.$e->getFile());
        }
    }

    /**
     * 产品对账单
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBillProductList()
    {
        try {
            $lists = $this->repository->getBillProductList();

            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage().' at line '.$e->getLine().' at file '.$e->getFile());
        }
    }

    /**
     * @api                 {post} /v2/bill/customer_profit
     * @apiName             权责利润（客户）
     * @apiDescription      获取客户维度的权责利润数据
     * @apiGroup            BillName
     * @apiVersion          v1.0.0
     *
     * @apiParam {Number} start_date 开始日期，格式为Y-m-d
     * @apiParam {Number} end_date 截止日期，格式Y-m-d
     * @apiParam {String[]} [customerIds] 客户ID
     * @apiParam {Number[]} [productIds] 产品ID
     *
     * @apiSuccess {Number} status 状态码，为0代表成功
     * @apiSuccess {Object} data 数据内容
     * @apiSuccess {Object} data.C20200515SHW2BJ 每个客户的数据，其中"C20200515SHW2BJ"表示客户的ID
     * @apiSuccess {String} data.C20200515SHW2BJ.customer_id 客户ID
     * @apiSuccess {Object} data.C20200515SHW2BJ.income_arr 营收的统计数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.income_arr.money 营收的金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.income_arr.number 营收的计费用量
     * @apiSuccess {Object} data.C20200515SHW2BJ.cost_arr 成本的统计数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.cost_arr.money 成本的金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.cost_arr.number 成本的计费用量
     * @apiSuccess {Object} data.C20200515SHW2BJ.profit_arr 利润的统计数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.profit_arr.money 利润的金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.profit_arr.number 利润的计费用量（营收计费用量-成本的计费用量）
     * @apiSuccess {Object} data.C20200515SHW2BJ.balance_arr 余额数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.balance_arr.recharge 总充值金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.balance_arr.income 总消费金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.balance_arr.balance 当前余额
     *
     * @apiSuccessExample   Success-Response:
     *  HTTP/1.1 200 OK
     *  {
     *      "status": 0,
     *      "data": {
     *          "C20200515SHW2BJ": {
     *              "customer_id": "C20200515SHW2BJ",
     *              "income_arr": {
     *                  "money": 1.28,
     *                  "number": 4
     *              },
     *              "cost_arr": {
     *                  "money": 3.01,
     *                  "number": 14
     *              },
     *              "profit_arr": {
     *                  "money": -1.7299999999999998,
     *                  "number": -10
     *              },
     *              "balance_arr": {
     *                  "recharge": 0,
     *                  "income": 1.28,
     *                  "balance": -1.28
     *              }
     *          }
     *      }
     *  }
     *
     * @apiError {String} status 错误状态码
     * @apiError {String} msg 错误信息
     *
     * @apiErrorExample     Error-Response:
     *  HTTP/1.1 200 OK
     *  {
     *      "status": 1478,
     *      "msg": "不存在end_date参数 at line 548 at file
     *      /home/<USER>/www/html/backapi-cui.dianhua.cn/app/Http/Repository/V2ProfitRepository.php"
     *  }
     */
    public function getCustomerProfit()
    {
        $repository = new V2ProfitRepository();
        try {
            $data   = $repository->getCustomerProfit();
            $status = 0;

            return $this->response(compact('status', 'data'));
        } catch (\Exception $exception) {
            return $this->setStatus(1478)
                ->responseError($exception->getMessage().' at line '.$exception->getLine().' at file '.$exception->getFile());
        }
    }

    /**
     * @api                 {post} /v2/bill/product_profit
     * @apiName             权责利润（产品）
     * @apiDescription      获取产品维度的权责利润数据
     * @apiGroup            BillName
     * @apiVersion          v1.0.0
     *
     * @apiParam {Number} start_date 开始日期，格式为Y-m-d
     * @apiParam {Number} end_date 截止日期，格式Y-m-d
     * @apiParam {String[]} [customerIds] 客户ID
     * @apiParam {Number[]} [productIds] 产品ID
     *
     * @apiSuccess {Number} status 状态码，为0代表成功
     * @apiSuccess {Object} data 数据内容
     * @apiSuccess {Object} data.C20200515SHW2BJ 每个客户的数据，其中"C20200515SHW2BJ"表示客户的ID
     * @apiSuccess {String} data.C20200515SHW2BJ.customer_id 客户ID
     * @apiSuccess {Object} data.C20200515SHW2BJ.income_arr 营收的统计数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.income_arr.money 营收的金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.income_arr.number 营收的计费用量
     * @apiSuccess {Object} data.C20200515SHW2BJ.cost_arr 成本的统计数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.cost_arr.money 成本的金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.cost_arr.number 成本的计费用量
     * @apiSuccess {Object} data.C20200515SHW2BJ.profit_arr 利润的统计数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.profit_arr.money 利润的金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.profit_arr.number 利润的计费用量（营收计费用量-成本的计费用量）
     * @apiSuccess {Object} data.C20200515SHW2BJ.balance_arr 余额数据
     * @apiSuccess {Number} data.C20200515SHW2BJ.balance_arr.recharge 总充值金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.balance_arr.income 总消费金额
     * @apiSuccess {Number} data.C20200515SHW2BJ.balance_arr.balance 当前余额
     *
     * @apiSuccessExample   Success-Response:
     *  HTTP/1.1 200 OK
     *  {
     *      "status": 0,
     *      "data": {
     *          "C20200515SHW2BJ": {
     *              "customer_id": "C20200515SHW2BJ",
     *              "income_arr": {
     *                  "money": 1.28,
     *                  "number": 4
     *              },
     *              "cost_arr": {
     *                  "money": 3.01,
     *                  "number": 14
     *              },
     *              "profit_arr": {
     *                  "money": -1.7299999999999998,
     *                  "number": -10
     *              },
     *              "balance_arr": {
     *                  "recharge": 0,
     *                  "income": 1.28,
     *                  "balance": -1.28
     *              }
     *          }
     *      }
     *  }
     *
     * @apiError {String} status 错误状态码
     * @apiError {String} msg 错误信息
     *
     * @apiErrorExample     Error-Response:
     *  HTTP/1.1 200 OK
     *  {
     *      "status": 1478,
     *      "msg": "不存在end_date参数 at line 548 at file
     *      /home/<USER>/www/html/backapi-cui.dianhua.cn/app/Http/Repository/V2ProfitRepository.php"
     *  }
     */

    public function getProductProfit()
    {
        $repository = new V2ProfitRepository();
        try {
            $data   = $repository->getProductProfit();
            $status = 0;

            return $this->response(compact('status', 'data'));
        } catch (\Exception $exception) {
            return $this->setStatus(1478)
                ->responseError($exception->getMessage().' at line '.$exception->getLine().' at file '.$exception->getFile());
        }
    }


}