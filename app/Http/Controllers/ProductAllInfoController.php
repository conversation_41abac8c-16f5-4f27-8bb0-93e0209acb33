<?php

namespace App\Http\Controllers;

use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ChannelProduct;
use App\Models\Product;

/**
 * Class ProductAllInfoController 各产品组拉取各类信息的控制器
 * @package App\Http\Controllers
 */
//class ProductAllInfoController extends Controller
class ProductAllInfoController extends CommonController
{
	protected $valueSection = [
		251 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		252 => [
			'max_value' => '',
			'min_value' => '',
		],
		253 => [
			'max_value' => '',
			'min_value' => '',
		],
		254 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		255 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		256 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		257 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		258 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		259 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		260 => [
			'max_value' => '',
			'min_value' => '',
		],
		261 => [
			'max_value' => '',
			'min_value' => '',
		],
		262 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		263 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		264 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		265 => [
			'max_value' => 999,
			'min_value' => -6,
		],
		266 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		267 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		268 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		269 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		270 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		271 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		272 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		273 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		274 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		275 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		276 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		277 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		278 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		279 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		280 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		281 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		282 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		283 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		284 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		285 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		286 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		287 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		288 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		241 => [
			'max_value' => 19,
			'min_value' => 0,
		],
		242 => [
			'max_value' => 19,
			'min_value' => 0,
		],
		243 => [
			'max_value' => 19,
			'min_value' => 0,
		],
		244 => [
			'max_value' => 7,
			'min_value' => 0,
		],
		245 => [
			'max_value' => 7,
			'min_value' => 0,
		],
		246 => [
			'max_value' => 7,
			'min_value' => 0,
		],
		247 => [
			'max_value' => 7,
			'min_value' => 0,
		],
		289 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		290 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		291 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		292 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		293 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		294 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		295 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		296 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		297 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		298 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		299 => [
			'max_value' => 900,
			'min_value' => 0,
		],
		711 => [
			'max_value' => 900,
			'min_value' => -6,
		],
		712 => [
			'max_value' => 900,
			'min_value' => -6,
		]
	];
	
	public function getProductInfo()
	{
		$data      = $this->getJsonByRaw();
		$productId = $data['product_id'];
		
		$checkResult = $this->checkParams($data);
		if ($checkResult != 0) {
			$this->createSimpleResponse('common.' . $checkResult);
		}
		
		$result = Product::where('father_id', $productId)
						 ->select('product_id', 'product_name', 'product_enname', 'product_param', 'status')
						 ->get()
						 ->toArray();
		$pinfo  = [];
		foreach ($result as $k => $v) {
			
			$item           = [];
			$item['id']     = $v['product_id'];
			$item['name']   = $v['product_enname'];
			$item['cnname'] = $v['product_name'];
			$item['status'] = $v['status'];
			$item['param']  = $v['product_param'] ? json_decode($v['product_param'], true) : [];
			
			$product_id = $v['product_id'];
			if (array_key_exists($product_id, $this->valueSection)) {
				if (array_key_exists('max_value', $this->valueSection[$product_id])){
					$item['param']['max_value'] = $this->valueSection[$product_id]['max_value'];
				}
				if (array_key_exists('min_value', $this->valueSection[$product_id])){
					$item['param']['min_value'] = $this->valueSection[$product_id]['min_value'];
				}
			}
			
			$pinfo[] = $item;
		}
		
		//如果是邦信分的 通讯产品 和 评分产品，则进行特殊处理，通讯产品不返回 评分产品
		
		$this->createBaseResponse('success', 0, $pinfo);
	}
	
	
	public function getChannelInfo()
	{
		$data = $this->getJsonByRaw();
		
		$checkResult = $this->checkParams($data);
		if ($checkResult != 0) {
			$this->createSimpleResponse('common.' . $checkResult);
		}
		
		$result = ChannelProduct::getChannelInfoByFid($data['product_id']);
		
		$channelInfo = [];
		foreach ($result as $v) {
			$item           = [];
			$item['id']     = $v['num'];
			$item['name']   = $v['name'];
			$item['cnname'] = $v['label'];
			$item['status'] = $v['status'];
			$item['param']  = json_decode($v['param'], true);
			
			$channelInfo[] = $item;
		}
		
		$this->createBaseResponse('success', 0, $channelInfo);
	}
	
	
	/**
	 * 拉取账号_产品数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/22 19:16
	 *
	 * @return void
	 */
	public function getStoreProductInfo()
	{
		$inParams = $this->getJsonByRaw();
		
		$code = $this->checkParams($inParams);
		
		if ($code) {
			$this->createSimpleResponse('common.' . $code);
		}
		
		//返回开通了这个产品及子产品的相关信息
		$product_ids = explode(',', array_get($inParams, 'product_id'));
		$products    = Product::whereIn('product_id', $product_ids)
							  ->pluck('product_name', 'product_id')
							  ->toArray();
		
		
		$account_products = AccountProduct::select([
			'account_id',
			'product_id',
			'daily_limit',
			'month_limit',
			'year_limit',
			'total_limit',
			'concurrency',
			'data',
			'limit_start_date',
			'end_time',
			'status',
		])
										  ->whereIn('product_id', $product_ids)
										  ->get()
										  ->toArray();
		
		$account_ids = array_values(array_unique(array_column($account_products, 'account_id')));
		
		$accounts = Account::select([
			'account_id',
			'apikey',
			'account_name',
			'id',
			'apikey',
			'father_id',
			'appsecret',
			'end_time',
			'access_ip',
			'status',
		])
						   ->whereIn('account_id', $account_ids)
						   ->get()
						   ->toArray();
		
		//整合数据
		$result = [];
		array_walk($accounts, function ($account) use (&$result) {
			$account_id          = $account['account_id'];
			$account_info        = [
				'account_real_id' => $account['id'],
				'account_name'    => $account['account_name'],
				'apikey'          => $account['apikey'],
				'appsecret'       => $account['appsecret'],
				'end_time'        => $account['end_time'],
				'access_ip'       => [],
				'status'          => $account['status'],
			];
			$product_info        = [];
			$result[$account_id] = compact('account_info', 'product_info');
		});
		
		array_walk($account_products, function ($account_product) use (&$result, $products) {
			$account_id = $account_product['account_id'];
			$product_id = $account_product['product_id'];
			
			if (!array_key_exists($account_id, $result)) {
				return;
			}
			$product_info_item = [
				'product_name'     => $products[$product_id],
				'daily_limit'      => $account_product['daily_limit'],
				'month_limit'      => $account_product['month_limit'],
				'year_limit'       => $account_product['year_limit'],
				'total_limit'      => $account_product['total_limit'],
				'concurrency'      => $account_product['concurrency'],
				'end_time'         => $account_product['end_time'],
				'status'           => $account_product['status'],
				'limit_start_date' => $account_product['limit_start_date'],
			];
			$special           = [];
			if ($account_product['data'] != 'null' && $account_product['data']) {
				$special = json_decode($account_product['data'], true);
			}
			$result[$account_id]['product_info'][$product_id] = array_merge($product_info_item, $special);
		});
		
		$result = array_combine(array_column(array_column($result, 'account_info'), 'apikey'), $result);
		
		$this->createDataResponse($result);
	}
	
	/**
	 * 校验入参数据，并返回code
	 *
	 * @access protected
	 *
	 * @param $inParams array 入参数据
	 *
	 * @return integer 返回值为响应code，为0带表无问题
	 */
	protected function checkParams($inParams)
	{
		//校验产品数据
		$productId = array_get($inParams, 'product_id');
		if (!$productId) {
			return 10003;
		}
		
		$productKey = array_get($inParams, 'product_key');
		if (!$productKey) {
			return 10004;
		}
		
		if ($productKey != (new CacheDriverFacade())->getProductKeyByProductId($productId)) {
			return 10005;
		}
		
		return 0;
	}
}