<?php

namespace App\Http\Controllers;

use App\Http\Repository\OptionsRepository;
use App\Imports\CustomerCostAdjustImport;
use App\Imports\CustomerExpendImport;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ConfigPriceInterface;
use App\Models\Customer;
use App\Models\Product;
use App\Models\SystemSession;
use App\Models\Account;
use App\Models\AccountProductCustom;
use App\Imports\AccountProductCustomImport;;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\UploadService;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

/**
 * Class OptionsController 各类下拉选项框的接口
 * @package App\Http\Controllers
 */
//class OptionsController extends Controller
class OptionsController extends CommonController
{
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new OptionsRepository();
	}

    /**
     * 获取开通该产品的所有账号
     */
    public function getApikeyOption()
    {
        $data = $this->repository->getApikeyOption();

        $this->createDataResponse($data);
    }

	/**
	 * 获取产品的选择框数据
	 */
	public function getProductOption()
	{
		$data = $this->repository->getProductOption();
		
		$this->createDataResponse($data);
	}
    /**
     * 获取客户的选择框数据
     */
    public function getCustomerOption()
    {
        $data = $this->repository->getCustomerOption();

        $this->createDataResponse($data);
    }
    /**
     * 获取账号的选择框数据
     */
    public function getAccountOption()
    {
        $data = $this->repository->getAccountOption();

        $this->createDataResponse($data);
    }
    /**
     * 获取渠道的选择框数据
     */
    public function getOperatorOption()
    {
        $data = $this->repository->getOperatorOption();

        $this->createDataResponse($data);
    }

    /**
     * 获取渠道的选择框数据
     */
    public function getChannelOption()
    {
        $data = $this->repository->getChannelOption();

        $this->createDataResponse($data);
    }
    /**
     * 获取渠道的选择框数据
     */
    public function getInterfaceOption()
    {
        $data = $this->repository->getInterfaceOption();

        $this->createDataResponse($data);
    }
	
	/**
	 * 获取产品类型的选择框数据
	 */
	public function getProductTypeOption()
	{
		$data = $this->repository->getProductTypeOption();
		
		$this->createDataResponse($data);
	}
	
	/**
	 * 蚂蚁跑批监控的批次选择框数据
	 */
	public function getAntFinancialMonitorBatchOption()
	{
		$data = $this->repository->getAntFinancialMonitorBatchOption();
		
		$this->createDataResponse($data);
	}
	
	/**
	 * 蚂蚁跑批监控的产品选择框数据
	 */
	public function getAntFinancialMonitorProductOption()
	{
		$data = $this->repository->getAntFinancialMonitorProductOption();
		
		$this->createDataResponse($data);
	}
	
	/**
	 * 蚂蚁跑批监控的渠道选择框数据
	 */
	public function getAntFinancialMonitorChannelOption()
	{
		$data = $this->repository->getAntFinancialMonitorChannelOption();
		
		$this->createDataResponse($data);
	}
    /**
     * 获取主产品的选择框数据
     */
    public function getMainProductOption()
    {
        $data = $this->repository->getMainProductOption();

        $this->createDataResponse($data);
    }

    /**
     * 获取主产品下对应的子产品列表
     */
    public function getChildrenProductList()
    {
        $data = $this->repository->getChildrenProductList();

        $this->createDataResponse($data);
    }

	/**
     * 获取客户、账号、产品、运营商名称映射
     */
	public function getMap()
    {
        $map = [];
        if(request()->post('group', null)){
            $map['group'] = $this->repository->getGroupMap();
        }
        if(request()->post('customer', null)){
            $test = request()->get('test', null);
            //是否包含group_id
            $include_group_id = request()->get('include_group_id', false);
            $map['customer'] = $this->repository->getCustomerMap($test,$include_group_id);
        }
        if(request()->post('customer_with_group', null)){
            $group_id = request()->get('group_id', false);
            $map['customer_with_group'] = $this->repository->getcustomerbygroupid($group_id);
        }
        if(request()->post('customer_group_map', null)){
            $map['customer_group_map'] = $this->repository->getCustomerGroupMap();
        }
        if(request()->post('account', null)){
            $map['account'] = $this->repository->getAccountIdMap();
        }
        if(request()->post('customer_type', null)){
            $map['customer_type'] = $this->repository->getCustomerIdTypeMap();
        }
        if(request()->post('customer_salesman', null)){
            $map['customer_salesman'] = $this->repository->getCustomerIdSalesmanMap();
        }
        if(request()->post('apikey', null)){
            $map['apikey'] = $this->repository->getAccountMap();
        }
        if(request()->post('product', null)){
            $map['product'] = $this->repository->getProductMap();
        }
        if(request()->post('operator', null)){
            $map['operator'] = $this->repository->getOperatorMap();
        }
        if(request()->post('channel', null)){
            $is_all_channel = request()->get('is_all_channel', null);
            $map['channel'] = $this->repository->getChannelMap('', $is_all_channel);
        }
        // 获取用户可查看的渠道
        if(request()->post('channel_with_user', null)){
            $sysName = $this->get_sys_user();
            $map['channel'] = $this->repository->getChannelMap($sysName);
        }
        if(request()->post('interface', null)){
            $map['interface'] = $this->repository->getInterfaceMap();
        }
        if(request()->post('main_product', null)){
            $map['main_product'] = $this->repository->getMainProductMap();
        }
        if(request()->post('main_product_list', null)){
            $map['main_product_list'] = $this->repository->getMainProductList();
        }

        if (request()->post('product_list', null)) {
            $map['product_list'] = $this->repository->getProductList();
        }

        if (request()->post('source', null)) {
            $map['source'] = $this->repository->getSourceList();
        }

        $company_type = request()->get('company_type', null);
        if($company_type){
            $map['company_type'] = $this->repository->getCompanyTypeMap($company_type);
        }

        if (request()->get('user_auth',null)){
            $sysName = $this->get_sys_user();
            $map['user_auth'] = $this->repository->getUserAuth($sysName);
        }

        if (request()->get('dept_list',null)){
            $map['dept_list'] = $this->repository->getDeptList();
        }

        if (request()->post('finance_auth',null)){
            $map['finance_auth'] = $this->repository->getFinanceAuth();
        }

        // 获取存量洞察渠道
        if(request()->post('cldc_channel', null)){
            $map['channel'] = $this->repository->getCldcChannelMap();
        }
        // 获取存量洞察接口
        if(request()->post('cldc_interface', null)){
            $channel_id = request()->post('channel_id');
            $map['interface'] = $this->repository->getCldcInterfaceMap($channel_id);
        }
        // 获取存量洞察账号
        if(request()->post('cldc_account', null)){
            $map['account'] = $this->repository->getCldcAccountMap();
        }

        // 获取存量洞察产品
        if (request()->post('cldc_product', null)) {
            $map['product'] = $this->repository->getCldcProduct();
        }

        // 获取公司名称
        if (request()->post('company', null)) {
            $map['company'] = $this->repository->getCompanyMap();
        }

        $this->createDataResponse($map);

    }

    private function get_sys_user(){
        $user_cookie = request()->get('user_cookie');
        $user_name = SystemSession::where(['session_id'=>$user_cookie])->first()->toArray();
        preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match);
        return $match[1];
    }

    public function getAccountProductCustomList(){
        $params['account_apikey'] = request()->get('account_apikey', '');
        $params['product_id'] = request()->get('product_id', '');
        $ruleType = request()->get('ruleType', '615_level');
        $export = request()->get('export', '');
        $page = request()->get('page', 0);
        $limit = request()->get('limit', 20);
        if($export == true){
            $limit = 100000;
        }
        $start = ($page - 1) * $limit;
        switch ($ruleType) {
            case '615_level';
                $where = ['account_product_custom.father_id' => 615, 'account_product_custom.type' => 20];
                break;
            case '615_score';
                $where = ['account_product_custom.father_id' => 615, 'account_product_custom.type' => 21];
                break;
            default:
                $this->createBaseResponse('参数异常', 50001, []);
        }

        $fields = ['account_product_custom.*','account.account_name'];
        $list = AccountProductCustom::getListByCondition($where, $fields, $start, $limit, $params);
        foreach($list['data'] as $key => &$val){
            $val['product_name'] = RedisCache::instance('productId_productName_mapping')->get($val['product_id']);
            $val['update_at'] = $val['update_at'] ? date('Y-m-d H:i:s', $val['update_at']) : '';
            if($val['apikey'] == 'default'){
                $val['account_name'] = 'default';
            }
        }

        if($export == true){
            $this->createBaseResponse('', 0, $list['data']);
        }

        $this->CreateLayuiResponse($list['data'], $list['count']);
    }

    public function setAccountProductCustom(Excel $excel)
    {

        $file = request()->file('file');
        $type = request()->get('ruleType', '615_level');
        if (!$file->isValid()) {
            $this->createBaseResponse('文件不合法', '50004', ['文件不合法']);
        }
        //扩展名
        $ext = $file->getClientOriginalExtension();
        if (!in_array($ext, ['xls', 'xlsx'])) {
            $this->createBaseResponse('上传文件类型不对,请检查是否为xls或 xlsx类型', '50006', ['上传文件类型不对,请检查是否为xls或 xlsx类型']);
        }
        $size = $file->getSize();//获取上传文件的大小
        if(500 * 1024 < $size){
            $this->createBaseResponse('文件超过500KB,请分批上传', '50007', ['文件超过500KB,请分批上传']);
        }

        //原文件名
        $originalName = $file->getClientOriginalName();
        $originalName = explode('.', $originalName);
        $noOriginalName = $originalName[0];//不带扩展名的文件名
        $realPath = $file->getRealPath();
        $filename = $noOriginalName . '_' . date('YmdHi') . '_' . uniqid() . '.' . $ext;

        try{
            $bool = Storage::disk('excel')->put($filename, file_get_contents($realPath));

            if (!$bool) {
                $this->createBaseResponse('上传文件服务存储有误,请联系技术查看', '50008', ['上传文件服务存储有误,请联系技术查看']);
            }
            $saveFilePath = storage_path() . '/excel/upload/' . $filename;

            $data = Excel::toArray(new AccountProductCustomImport, $saveFilePath);
            switch ($type) {
                case '615_level'://号码风险等级映射
                    $res = AccountProductCustomImport::readExcelArray($data);
                    break;
                case '615_score'://号码风险等级 分点位
                    $res = AccountProductCustomImport::setShieldScoreRule($data);
                    break;
                default:
                    $res = ['msg' => '参数类型错误', 'code' => 50001, 'data' => []];
                    break;
            }
            $this->createBaseResponse($res['msg'], $res['code'], $res['data']);
        }catch(Exception $e){
            $this->createBaseResponse($e->getMessage(), '50009', ['抛出异常:'.$e->getMessage()]);
        }

    }

    //批量添加客户特殊消耗
    public function batchAddCustomerExpend(Excel $excel)
    {

        $file = request()->file('file');
        //$type = request()->get('ruleType', '615_level');
        if (!$file->isValid()) {
            $this->createBaseResponse('文件不合法', '50004', ['文件不合法']);
        }
        //扩展名
        $ext = $file->getClientOriginalExtension();
        if (!in_array($ext, ['xls', 'xlsx'])) {
            $this->createBaseResponse('上传文件类型不对,请检查是否为xls或 xlsx类型', '50006', ['上传文件类型不对,请检查是否为xls或 xlsx类型']);
        }
        $size = $file->getSize();//获取上传文件的大小
        if(2 * 1024 * 1024 < $size){
            $this->createBaseResponse('文件超过2M,请分批上传', '50007', ['文件超过2M,请分批上传']);
        }

        //原文件名
        $originalName = $file->getClientOriginalName();
        $originalName = explode('.', $originalName);
        $noOriginalName = $originalName[0];//不带扩展名的文件名
        $realPath = $file->getRealPath();
        $filename = $noOriginalName . '_' . date('YmdHi') . '_' . uniqid() . '.' . $ext;

        try{
            $bool = Storage::disk('excel')->put($filename, file_get_contents($realPath));

            if (!$bool) {
                $this->createBaseResponse('上传文件服务存储有误,请联系技术查看', '50008', ['上传文件服务存储有误,请联系技术查看']);
            }
            $saveFilePath = storage_path() . '/excel/upload/' . $filename;

            $data = Excel::toArray(new CustomerExpendImport, $saveFilePath);
            $res = CustomerExpendImport::readExcelArray($data);

            $this->createBaseResponse($res['msg'], $res['code'], $res['data']);
        }catch(Exception $e){
            $this->createBaseResponse($e->getMessage(), '50009', ['抛出异常:'.$e->getMessage()]);
        }

    }

    //批量添加客户成本
    public function batchAddCustomerCost(Excel $excel)
    {
        $file = request()->file('file');
        //$type = request()->get('ruleType', '615_level');
        if (!$file->isValid()) {
            $this->createBaseResponse('文件不合法', '50004', ['文件不合法']);
        }
        //扩展名
        $ext = $file->getClientOriginalExtension();
        if (!in_array($ext, ['xls', 'xlsx'])) {
            $this->createBaseResponse('上传文件类型不对,请检查是否为xls或 xlsx类型', '50006', ['上传文件类型不对,请检查是否为xls或 xlsx类型']);
        }
        $size = $file->getSize();//获取上传文件的大小
        if(2 * 1024 * 1024 < $size){
            $this->createBaseResponse('文件超过2M,请分批上传', '50007', ['文件超过2M,请分批上传']);
        }

        //原文件名
        $originalName = $file->getClientOriginalName();
        $originalName = explode('.', $originalName);
        $noOriginalName = $originalName[0];//不带扩展名的文件名
        $realPath = $file->getRealPath();
        $filename = $noOriginalName . '_' . date('YmdHi') . '_' . uniqid() . '.' . $ext;

        try{
            $bool = Storage::disk('excel')->put($filename, file_get_contents($realPath));

            if (!$bool) {
                //$this->createBaseResponse('上传文件服务存储有误,请联系技术查看', '50008', ['上传文件服务存储有误,请联系技术查看']);
                return $this->Response(50008, ['上传文件服务存储有误,请联系技术查看'], '上传文件服务存储有误,请联系技术查看');
            }
            $saveFilePath = storage_path() . '/excel/upload/' . $filename;

            $data = Excel::toArray(new CustomerCostAdjustImport, $saveFilePath);
            $res = CustomerCostAdjustImport::readExcelArray($data, $this->sysName);

            //$this->createBaseResponse($res['msg'], $res['code'], $res['data']);
            return $this->Response($res['code'], $res['data'], $res['msg']);
        }catch(\Exception $e){
            return $this->Response(50009, ['抛出异常:'.$e->getMessage().',file:'.$e->getFile().',line:'.$e->getLine()], $e->getMessage());
        }

    }


    //历史成本调整
    public function historyCostAdjust(){
        $params = request()->post();
        $ids = $params['list'];
        $year = $params['year'];
        $type = $params['type'];//channel:渠道成本处理  customer:客户成本处理
        $list = \App\Models\UpstreamBillAdjust::getListByCondition([], $ids)->toArray();
        $filePath = storage_path()."/logs/".$year.'_'.$type.'_cost.txt';

        if($year == 2021 && $type == 'customer'){
            foreach ($list as $val){
                $channel_id = historyChannelMap($val['channel']);
                if($channel_id == false){//说明没有匹配到
                    $channel_id = $this->getOtherChannelId($val['channel']);
                    if(empty($channel_id)){
                        echo 'not find:'.$val['id'].'##'.$val['channel'].PHP_EOL;
                        continue;
                    }
                }

                $operator = $this->getOperator($channel_id);
                $interface = ChannelInterface::getInterfaceInfo(['channel_id' => $channel_id, 'status' => 1 ])->toArray();
                $product_id = $this->getPnameByInterface($interface['label']);
                if($product_id == 0){
                    echo 'pid error:'.$val['id'].'##'.$interface['label'].PHP_EOL;
                    continue;
                }

                $product_info = Product::getProductInfoByProductId($product_id);
                if($product_info['father_id'] == 0){
                    $father_id = $product_id;
                }else{
                    $father_id = $product_info['father_id'];
                }

                $date = str_replace("-", "", $val['date']);

                $sql = "insert into customer_bill_adjust (`title`, `customer_id`,"
                    ."`father_id`, `product_id`, `operator`, `date`, `money`, `fee_number`, `remark`, `create_at`, "
                    ."`update_at`, `admin`) VALUES ("
                    ."'".$val['title']."', '".$val['customer_id']."', "
                    .$father_id.", ".$product_id." ,'".$operator."', ".$date.", ".$val['money']
                    .", ".$val['fee_number'].", '".$val['remark']."', ".$val['create_time'].", **********, '".$val['admin']
                    ."');";
                echo $sql.PHP_EOL;

            }

            dd("2021 customer ok");
        }


        if($year == 2021 && $type == 'channel'){
            foreach ($list as $val){
                $sql = "insert into channel_account_adjust (`title`, `customer_id`, `account_id`, "
                    ."`apikey`, `product_id`, `date`, `money`, `fee_number`, `remark`, `create_time`, "
                    ."`update_time`, `admin`, `channel_id`, `interface_id`, `encrypt`, `operator`) VALUES ("
                    ."'".$val['title']."', '".$val['customer_id']."', '".$val['account_id']."', "
                    ."'".$val['apikey']."', ".$val['product_id']." ,'".$val['date']."', ".$val['money']
                    .", ".$val['fee_number'].", '".$val['remark']."', ".$val['create_time'].", **********, '".$val['admin']
                    ."', ".$val['channel_id'].", ".$val['interface_id'].", 'NO', '".$val['operator']."');";
                echo $sql.PHP_EOL;

            }
            dd("2021 ok");
        }

        //2020 channel
        foreach ($list as $val){
            $channel_id = historyChannelMap($val['channel']);
            if($channel_id == false){//说明没有匹配到
                $channel_id = $this->getOtherChannelId($val['channel']);
                if(empty($channel_id)){
                    echo 'not find:'.$val['id'].'##'.$val['channel'].PHP_EOL;
                    continue;
                }
            }

            $interface = ChannelInterface::getInterfaceInfo(['channel_id' => $channel_id, 'status' => 1 ])->toArray();
            $pid = $this->getPnameByInterface($interface['label']);
            if($pid == 0){
                echo 'pid error:'.$val['id'].'##'.$val['channel'].PHP_EOL;
                continue;
            }

            $operator = $this->getOperator($channel_id);
            $sql = "insert into channel_account_adjust (`title`, `customer_id`, `account_id`, "
                ."`apikey`, `product_id`, `date`, `money`, `fee_number`, `remark`, `create_time`, "
                ."`update_time`, `admin`, `channel_id`, `interface_id`, `encrypt`, `operator`) VALUES ("
                ."'".$val['title']."', '".$val['customer_id']."', '".$val['account_id']."', "
                ."'".$val['apikey']."', ".$val['product_id']." ,'".$val['date']."', ".$val['money']
                .", ".$val['fee_number'].", '".$val['remark']."', ".$val['create_time'].", **********, '".$val['admin']
                ."', ".$channel_id.", ".$interface['id'].", 'NO', '".$operator."');";
            echo $sql.PHP_EOL;

        }

        dd('2020 ok');

    }

    public function getChannelPriceByFatherId(){
        $params = request()->post();
        if(!isset($params['father_id'])){
            dd('father_id不存在');
        }
        $father_id = $params['father_id'];
        if($father_id == 200){//说明要查邦秒验渠道
            $where = [['channel_id', '>=', 100], ['status', '=', 1]];
        }
        $channel_list = Channel::getChannelByCondition($where, ['label', 'channel_id']);
        $channel_list = array_column($channel_list, 'label', 'channel_id');
        $channel_ids = array_keys($channel_list);

        $iids = ChannelInterface::getListByCondition([], ['*'], $channel_ids)->toArray();
        $result = [];
        //dd($iids);
        $sort = 1;
        foreach($iids as $item){
            $iid = $item['id'];
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($iid);
            if(empty($channel_id)){
                echo 'iid='.$iid.'接口的渠道信息在缓存中获取不到'.PHP_EOL;
                continue;
            }
            $channel_name = $channel_list[$channel_id] ?? '';
            $iid_name = $item['label'];

            $row = ConfigPriceInterface::getLatestPriceByIid($iid);
            if(empty($row)){
                echo 'iid='.$iid.'接口的计费配置不存在,对应接口名:'.$iid_name.', 所属渠道:'.$channel_name.PHP_EOL;
                continue;
            }

            $price_id = $row['id'];//计费配置id
            $price = json_decode($row['price'], true);
            if(empty($price) || !is_array($price)){
                echo 'iid='.$iid.'接口的计费配置price解析有误;计费配置id='.$row['id'].PHP_EOL;
                continue;
            }
            foreach($price as $val){
                $operator = '未定义';
                if($val['operator'] == 'CMCC'){
                    $operator = '移动';
                }

                if($val['operator'] == 'CTCC'){
                    $operator = '电信';
                }

                if($val['operator'] == 'CUCC'){
                    $operator = '联通';
                }

                if($val['operator'] == 'ALL'){
                    $operator = '不区分';
                }

                $encrypt_way = $val['encrypt_way'] == 'ALL' ? '不区分' : $val['encrypt_way'];

                $iid_price = $val['price'];

                $detail = [
                    'price_id' => $price_id,
                    'iid' => $iid,
                    'channel_id' => $channel_id,
                    'sort' => $sort,
                    'channel_name' => $channel_name,
                    'iid_name' => $iid_name,
                    'operator' => $operator,
                    'encrypt_way' => $encrypt_way,
                    'price' => $iid_price
                ];

                $result[] = $detail;
                $sort++;

            }

            //dd($row);
            //echo PHP_EOL;
        }

        array_multisort(array_column($result, 'channel_id'), SORT_DESC, $result);
        //dd($result);
        //dd(array_column($result, 'channel_id'));
        //array_multisort(array_column($result, 'sort'), SORT_DESC, SORT_NUMERIC, $result);

        foreach ($result as $item){

            echo $item['price_id']."\t".$item['iid']."\t".$item['channel_name']."\t".$item['iid_name']."\t".$item['operator']."\t".$item['encrypt_way']."\t".$item['price'].PHP_EOL;

        }

        dd('ok');

    }

    public function getOtherChannelId($name){
	    if(strpos($name, 'CUCC') !== FALSE){
            return 1;
        }

        if(strpos($name, 'JSCMCC') !== FALSE){
            return 4;
        }

        if(strpos($name, 'BJCMCC') !== FALSE){
            return 3;
        }

        if(strpos($name, 'SCCMCC') !== FALSE){
            return 5;
        }

        if(strpos($name, 'rongshu') !== FALSE){
            return 123;
        }

        if(strpos($name, 'tianyancha') !== FALSE){
            return 201;
        }

        if(strpos($name, 'xiaoan') !== FALSE){
            return 104;
        }

        return '';

    }

    public function getPnameByInterface($name){
	    if($name == '(催收)最近一次通话时间间隔'){
	        return 252;
        }
        if($name == '通用信用分(高阶版)'){
            return 238;
        }
        if($name == '手机号实时在网状态'){
            return 216;
        }
        if($name == '手机号在网状态'){
            return 203;
        }
        if($name == '手机号三要素验证详版'){
            return 201;
        }
        if($name == '手机号在网时长'){
            return 202;
        }
        if($name == '通用分4'){
            return 317;
        }
        if($name == '手机号三要素验证'){
            return 213;
        }
        if($name == '手机号姓名二要素验证'){
            return 214;
        }
        if($name == '号查名接口'){
            return 401;
        }
        if($name == '通话总次数'){
            return 241;
        }
        if($name == '(催收)最近一次通话时间'){
            return 252;
        }
        if($name == 'A型催收历史分'){
            return 251;
        }

        return 0;
    }

    public function getOperator($cid){
        $arr = [
            '1' => 'CUCC',
            '2' => 'CTCC',
            '3' => 'BJCMCC',
            '4' => 'JSCMCC',
            '5' => 'SCCMCC',
            '6' => 'SDCMCC',
        ];

        if(isset($arr[$cid])){
            return $arr[$cid];
        }else{
            return 'CMCC';
        }

    }



}