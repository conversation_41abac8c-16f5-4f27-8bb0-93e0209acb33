<?php

namespace App\Http\Controllers;

use App\Repositories\AccountProductRepositorie;
use Exception;
use Illuminate\Http\JsonResponse;

//use Laravel\Lumen\Routing\Controller as BaseController;

//class AccountProductController extends BaseController
class AccountProductController extends CommonController
{
    public $product;

    public function __construct()
    {
        parent::__construct();
        $this->product = new AccountProductRepositorie();
    }

    /*
     * 查询产品信息
     * @param post json
     * @return json
     * */

    public function getAccountProductInfo()
    {
        $res = $this->product->getList($this->data);
        //处理返回值，根据返回值判断，区分不同类型错误信息返回
        if(isset($res['error'])){
            return response()->json(['status'=>1,'msg'=>$res['msg'],'data'=>[]]);
        }else{
            return response()->json(['status'=>0,'msg'=>'0','data'=>$res]);
        }
    }


    /**
     * 获取客户开通的产品
     *
     * @return JsonResponse
     */
    public function productList(){
        try {
            $list = $this->product->getProductList($this->data);
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 换签操作
     *
     * @return JsonResponse
     */
    public function changeSource(){
        try {
            $list = $this->product->changeSource($this->data);
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }
}