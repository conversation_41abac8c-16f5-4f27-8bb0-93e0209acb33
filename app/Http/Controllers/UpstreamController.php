<?php

namespace App\Http\Controllers;

use App\Exports\UpstreamVerificationExport;
use App\Http\Repository\UpstreamRepository;
use App\TraitUpgrade\ResponseTrait;
use Laravel\Lumen\Routing\Router;
use App\Support\CustomException;
use Maatwebsite\Excel\Facades\Excel;

//class UpstreamController extends Router
class UpstreamController extends CommonController
{
    use ResponseTrait;

    private $repository;

    /**
     * UpstreamController constructor.
     * @param $repository
     */
    public function __construct(UpstreamRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 上游 邦妙验详情下载
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function verificationDetailsExcel()
    {
        try {
            return $this->repository->verificationDetailsExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 上游 邦妙验列表按天下载
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function verificationDayExcel()
    {
        try {
            return $this->repository->verificationDayExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 上游 邦妙验列表下载
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function verificationExcel()
    {
        try {
            $list = request()->post('list');
            return Excel::download(new UpstreamVerificationExport($list), 'export.xlsx');
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 上游 邦妙验详情
     * @param $product_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function verificationDetails($product_id)
    {
        try {
            $lists = $this->repository->verificationDetails($product_id);
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 上游 邦妙验列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function verification()
    {
        try {
            $lists = $this->repository->verification();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 邦信分快捷版上游数据通详情导出excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function shortcutsDetailsExcel()
    {
        try {
            return $this->repository->shortcutsDetailsExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }


    /**
     * 按天导出excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function shortcutsDayExcel()
    {
        try {
            return $this->repository->shortcutsDayExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 邦信分快捷版上游数据通计列表下载
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function shortcutsExcel()
    {
        try {
            return $this->repository->shortcutsExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 邦信分快捷版上游数据通计列表
     */
    public function shortcuts()
    {
        try {
            $lists = $this->repository->shortcuts();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 邦信分快捷版上游数据通详情
     * @return \Illuminate\Http\JsonResponse
     */
    public function shortcutDetails($field)
    {
        try {
            $lists = $this->repository->shortcutDetails($field);
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }
}