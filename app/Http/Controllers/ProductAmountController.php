<?php

namespace App\Http\Controllers;

use App\Http\Repository\ProductAmountRepository;

//class ProductAmountController extends BaseController
class ProductAmountController extends CommonController
{
    protected $msg = [
        0 => '请求成功',
        1 => '请求失败',
        2 => '参数错误',
        500 => '发生错误,请重试',
    ];

    private $repository;

    public function __construct(ProductAmountRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function stat()
    {
        try {
            $amount_info = $this->repository->stat($this->data);
            return $this->response(0, $amount_info);
        } catch (\Exception $e) {
            return $this->response(2, [], $e->getMessage());
        }
    }
}
