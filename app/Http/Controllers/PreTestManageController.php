<?php

namespace App\Http\Controllers;

use App\Http\Repository\PreTestManage\PreTestManageRepository;
use App\Http\Repository\PreTestManage\ApplyRepository;
use App\Http\Repository\PreTestManage\MobileRepository;
use App\Http\Repository\PreTestManage\MonitorRepository;
use App\Http\Repository\PreTestManage\RiskRepository;
use App\Http\Repository\PreTestManage\StatisticsRepository;

/**
 * @uses PreTestManageController
 */
class PreTestManageController extends CommonController
{

    /**
     * 销售H5: 我的申请列表
     * @return \Illuminate\Http\JsonResponse
     * @uses myList
     */
    public function myList() {
        try {
            $data = (new MobileRepository())->myList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 销售H5: 全部无反馈
     * @return \Illuminate\Http\JsonResponse
     * @uses stayAll
     */
    public function stayAll() {
        try {
            $data = (new MobileRepository())->stayAll();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 销售H5: 反馈操作-产品列表
     * @return \Illuminate\Http\JsonResponse
     * @uses fatherFeedback
     */
    public function fatherFeedback() {
        try {
            $data = (new MobileRepository())->fatherFeedback();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 销售H5: 接入操作-产品列表
     * @return \Illuminate\Http\JsonResponse
     * @uses fatherAccess
     */
    public function fatherAccess() {
        try {
            $data = (new MobileRepository())->fatherAccess();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 销售H5/PC端: 无反馈
     * @return \Illuminate\Http\JsonResponse
     * @uses staySingle
     */
    public function staySingle() {
        try {
            $data = (new ApplyRepository())->staySingle();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 销售H5/PC端: 保存反馈信息
     * @return \Illuminate\Http\JsonResponse
     * @uses saveFeedback
     */
    public function saveFeedback() {
        try {
            $data = (new ApplyRepository())->saveFeedback();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 销售H5/PC端: 保存接入信息
     * @return \Illuminate\Http\JsonResponse
     * @uses saveAccess
     */
    public function saveAccess() {
        try {
            $data = (new ApplyRepository())->saveAccess();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端: 保存接入信息-批量
     * @return \Illuminate\Http\JsonResponse
     * @uses batchSaveAccess
     */
    public function batchSaveAccess() {
        try {
            $data = (new ApplyRepository())->batchSaveAccess();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端: 测试申请列表
     * @return \Illuminate\Http\JsonResponse
     * @uses list
     */
    public function list(){
        try {
            $data = (new ApplyRepository())->list();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端: 测试申请列表下载
     */
    public function listDownload(){
        try {
           return (new ApplyRepository())->listDownload();
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-处理状态
     * @return \Illuminate\Http\JsonResponse
     * @uses processStatus
     */
    public function processStatus() {
        try {
            $data = (new ApplyRepository())->processStatus();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-测试完成修改状态
     * @return \Illuminate\Http\JsonResponse
     * @uses optionList
     */
    public function optionList() {
        try {
            $data = (new PreTestManageRepository())->optionList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-可测试产品option
     * @return \Illuminate\Http\JsonResponse
     * @uses productOption
     */
    public function productOption() {
        try {
            $data = (new ApplyRepository())->productOption();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-增加测试产品
     * @return \Illuminate\Http\JsonResponse
     * @uses bindProduct
     */
    public function bindProduct() {
        try {
            $data = (new ApplyRepository())->bindProduct();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage() . ', ' . $exception->getFile() . ', ' . $exception->getLine());
        }
    }

    /**
     * PC端-删除测试产品
     * @return \Illuminate\Http\JsonResponse
     * @uses deleteProduct
     */
    public function deleteProduct() {
        try {
            $data = (new ApplyRepository())->deleteProduct();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-查看测试申请详情
     * @return \Illuminate\Http\JsonResponse
     * @uses applyInfo
     */
    public function applyInfo() {
        try {
            $data = (new ApplyRepository())->applyInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端: 测试申请产品列表
     * @return \Illuminate\Http\JsonResponse
     * @uses applyProductInfo
     */
    public function applyProductInfo() {
        try {
            $data = (new ApplyRepository())->applyProductInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端: 绑定客户
     * @return \Illuminate\Http\JsonResponse
     * @uses bindCustomer
     */
    public function bindCustomer() {
        try {
            $data = (new ApplyRepository())->bindCustomer();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-部门统计信息
     * @return \Illuminate\Http\JsonResponse
     * @uses deptStatistics
     */
    public function deptStatistics() {
        try {
            $data = (new StatisticsRepository())->deptStatistics();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-销售统计信息
     * @return \Illuminate\Http\JsonResponse
     * @uses salesStatistics
     */
    public function salesStatistics() {
        try {
            $data = (new StatisticsRepository())->salesStatistics();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-数据统计-总览数据
     * @return \Illuminate\Http\JsonResponse
     * @uses statisticsOverview
     */
    public function statisticsOverview() {
        try {
            $data = (new StatisticsRepository())->statisticsOverview();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-数据统计-反馈数据
     * @return \Illuminate\Http\JsonResponse
     * @uses statisticsFeedback
     */
    public function statisticsFeedback() {
        try {
            $data = (new StatisticsRepository())->statisticsFeedback();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-数据统计-效果数据
     * @return \Illuminate\Http\JsonResponse
     * @uses statisticsResult
     */
    public function statisticsResult() {
        try {
            $data = (new StatisticsRepository())->statisticsResult();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-数据统计-接入&调用统计
     * @return \Illuminate\Http\JsonResponse
     * @uses statisticsAccess
     */
    public function statisticsAccess() {
        try {
            $data = (new StatisticsRepository())->statisticsAccess();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-部门人员-风险统计列表
     * @return \Illuminate\Http\JsonResponse
     * @uses riskList
     */
    public function riskList() {
        try {
            $data = (new RiskRepository())->riskList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-产品-风险统计列表
     * @return \Illuminate\Http\JsonResponse
     * @uses riskProductList
     */
    public function riskProductList() {
        try {
            $data = (new RiskRepository())->riskProductList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-风险明细
     * @return \Illuminate\Http\JsonResponse
     * @uses riskDetailList
     */
    public function riskDetailList() {
        try {
            $data = (new RiskRepository())->riskDetailList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-日志列表
     * @return \Illuminate\Http\JsonResponse
     * @uses logOptionList
     */
    public function logOptionList() {
        try {
            $data = (new PreTestManageRepository())->logOptionList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-日志列表
     * @return \Illuminate\Http\JsonResponse
     * @uses logList
     */
    public function logList() {
        try {
            $data = (new PreTestManageRepository())->logList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     *  跑数管理
     * @return \Illuminate\Http\JsonResponse
     * @uses runScore
     */
    public function runScore() {
        try {

            return $this->response(0);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     *  文件上传
     * @return \Illuminate\Http\JsonResponse
     * @uses fileUpload
     */
    public function fileUpload() {
        try {

            return $this->response(0);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-操作统计
     * @return \Illuminate\Http\JsonResponse
     * @uses actionRecord
     */
    public function actionRecord() {
        try {
            $data = (new PreTestManageRepository())->actionRecord();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-客户运营全流程-列表 - option
     * @return \Illuminate\Http\JsonResponse
     * @uses monitorOptionList
     */
    public function monitorOptionList() {
        try {
            $data = (new MonitorRepository())->monitorOptionList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-客户运营全流程-列表
     * @return \Illuminate\Http\JsonResponse
     * @uses monitorCustomerList
     */
    public function monitorCustomerList() {
        try {
            $data = (new MonitorRepository())->monitorCustomerList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-客户运营全流程 - 保存信息
     * @return \Illuminate\Http\JsonResponse
     * @uses saveMonitorInfo
     */
    public function saveMonitorInfo() {
        try {
            $data = (new MonitorRepository())->saveMonitorInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-客户运营全流程 - 发起审批
     * @return \Illuminate\Http\JsonResponse
     * @uses createApproval
     */
    public function createApproval() {
        try {
            $data = (new MonitorRepository())->createApproval();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, false, $exception->getMessage());
        }
    }

    /**
     * PC端-客户运营全流程-列表 - 下载 - 数据总页数
     * @return \Illuminate\Http\JsonResponse
     * @uses monitorCustomerCount
     */
    public function monitorCustomerCount() {
        try {
            $data = (new MonitorRepository())->monitorCustomerCount();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-客户运营全流程OA-列表
     * @return \Illuminate\Http\JsonResponse
     * @uses monitorApprovalList
     */
    public function monitorApprovalList() {
        try {
            $data = (new MonitorRepository())->monitorApprovalList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-客户运营全流程OA-列表 - 下载 - 提醒工单页数
     * @return \Illuminate\Http\JsonResponse
     * @uses monitorApprovalCount
     */
    public function monitorApprovalCount() {
        try {
            $data = (new MonitorRepository())->monitorApprovalCount();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-测试读写
     * @return \Illuminate\Http\JsonResponse
     * @uses logWrite
     */
    public function logWrite() {
        try {
            $data = (new PreTestManageRepository())->logWrite();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * PC端-测试只读
     * @return \Illuminate\Http\JsonResponse
     * @uses logRead
     */
    public function logRead() {
        try {
            $data = (new PreTestManageRepository())->logRead();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 人工操作完成标记
     * @return \Illuminate\Http\JsonResponse
     * @uses manualDone
     */
    public function manualDone() {
        try {
            $data = (new PreTestManageRepository())->manualDone();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
}