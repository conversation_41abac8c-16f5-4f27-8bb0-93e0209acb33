<?php

namespace App\Http\Controllers;

use App\Http\Repository\ApprovalRepository;
use App\Http\Repository\ContractRepositorie;
use App\Http\Repository\DeptRepository;
use App\Models\Dept;
use App\Models\Product;
use App\Models\SystemSession;
use App\Models\SystemUser;
use App\Providers\Auth\DataAuth;
use App\Utils\Helpers\Func;
use Exception;
use Illuminate\Http\JsonResponse;

/**
 *  审批流
 * @package App\Http\Controllers
 */
class ContractController extends CommonController
{
	protected $repository;

	public function __construct()
	{
		parent::__construct();
		$this->repository = new ApprovalRepository();
	}


    /**
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-26 15:10:14
     *
     */
    public function options(){
        try {
            $res = ContractRepositorie::options();
            return $this->response(0, $res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取合同列表
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:09:31
     *
     */
    public function contractList(){
        $contract_name        = request()->post('contract_name');                    //合同名
        $serial_number        = request()->post('serial_number');                    //OA审批编号
        $contract_no          = request()->post('contract_no');                      //合同编号
        $contract_category    = request()->post('contract_category');                //合同类别
        $contract_type        = request()->post('contract_type');                    //合同类型
        $contract_start_start = request()->post('contract_start_start');             //合同开始日期
        $contract_start_end   = request()->post('contract_start_end');               //合同开始日期
        $contract_end_start   = request()->post('contract_end_start');               //合同结束日期
        $contract_end_end     = request()->post('contract_end_end');                 //合同结束日期
        $created_at_start     = request()->post('created_at_start');                 //归档日期 开始
        $created_at_end       = request()->post('created_at_end');                   //归档日期 结束
        $company_name         = request()->post('company_name');                     //公司名称
        $customer_name        = request()->post('customer_name');                    //客户名称
        // $salesman_name     = request()->post('salesman_name');                        //公司名称
        $channel_type         = request()->post('channel_type');                     //渠道类型
        $channel_name         = request()->post('channel_name');                     //渠道简称
        $channel_full_name	  = request()->post('channel_full_name');                //渠道全称
        $channel_salesman	  = request()->post('channel_salesman');                 //渠道商务
        $sign_year            = request()->post('sign_year');                        //签约年份
        $father_id            = request()->post('father_id');                        //主产品id
        $archive_status       = request()->post('archive_status',-1);                //是否归档
        $only_sale = request()->post('only_sale', 'no');                      //是否仅为商务 该参数可能不传
        $dept_id   = request()->post('dept_id');                                     //是否仅为商务 该参数可能不传

        $page      = intval(request()->post('page', 1));
        $page_size = intval(request()->post('page_size', 10));

        //获取用户名
        $session_id = request()->post('user_cookie','');
        $username = (new SystemSession())->getNameBySessionId($session_id);
        //处理 区域 人员 条件
        if(!empty($dept_id)){
            [$dept_id_type,$dept_id_value] = explode("_", $dept_id[count($dept_id) - 1]);
            if($dept_id_type == 'dept'){
                $dept_id = $dept_id_value;
            }
            if($dept_id_type == 'salesman'){
                $salesman = $dept_id_value;
            }
        }
        $salesman_arr = [];
        $salesman_name_arr = [];

        if ($contract_category == ContractRepositorie::CONTRACT_CATEGORY_CUSTOMER) {
            //仅仅展示商务
            if ($only_sale == 'no') {
                if (empty($salesman) && empty($dept_id)) {
                    $dataAuthService = new DataAuth($username);
                    $salesman_arr    = $dataAuthService->getBelongUsers();
                    $salesman_arr    = array_unique(array_merge($salesman_arr, [$username]));
                }
                if (!empty($salesman)) {//人员选择不为空 以人员为标注过滤客户
                    $salesman_arr = [$salesman];
                }
                if (!empty($dept_id) && empty($salesman)) {//人员为空 部门不为空 以部门领导所能查看的客户数据
                    if ($dept_id == 'no-dept-id') {//处理没有商务的合同
                        $salesman_arr = [];
                    } else {
                        $salesman_arr = DeptRepository::getDeptSalesmans($dept_id);
                    }
                }
            } else {
                $salesman_arr = [$username];
            }

            $salesman_name_arr = SystemUser::getRealNameByNames($salesman_arr);
        } elseif (!empty($channel_salesman)) {
            $salesman_name_arr = [$channel_salesman];
        }

        $params = [
            'contract_name'        => $contract_name,
            'serial_number'        => $serial_number,
            'contract_no'          => $contract_no,
            'contract_category'    => $contract_category,
            'contract_type'        => $contract_type,
            'contract_start_start' => $contract_start_start,
            'contract_start_end'   => $contract_start_end,
            'contract_end_start'   => $contract_end_start,
            'contract_end_end'     => $contract_end_end,
            'created_at_start'     => $created_at_start,
            'created_at_end'       => $created_at_end,
            'company_name'         => $company_name,
            'salesman_name'        => $salesman_name_arr,
            'customer_name'        => $customer_name,
            'salesman_arr'         => $salesman_arr,
            'channel_type'         => $channel_type,
            'channel_name'         => $channel_name,
            'channel_full_name'    => $channel_full_name,
            'archive_status'       => $archive_status,
            'sign_year'            => $sign_year,
            'father_id'            => $father_id,
            'page'                 => $page,
            'page_size'            => $page_size,
        ];

        try {
            $list  = ContractRepositorie::get_list($params,$username);
            $count = ContractRepositorie::get_count($params);
            $res = [
                'list'                  => $list,
                'count'                 => $count,
                'page'                  => $page,
                'page_size'             => $page_size,
            ];
            return $this->response(0, $res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }


    /**
     * 合同归档
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-29 17:35:27
     *
     */
    public function archive(){
        $contract_category = request()->post('category');                //合同类别
        $excel             = request()->post('excel');                   //excel文件
        $pdf               = request()->post('pdf');                     //pdf列表

        $user_cookie = request()->post('user_cookie', '');
        $user_name   = Func::getUserNameFromCookie($user_cookie);

        try {
            $res = ContractRepositorie::archive($contract_category,$excel,$pdf,$user_name);
            return $this->response(0, $res);
        } catch (Exception $e) {
            // return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 编辑合同
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-29 17:35:27
     *
     */
    public function edit(){
        $user_cookie = request()->post('user_cookie', "");
        $user_name   = Func::getUserNameFromCookie($user_cookie);
        $salesman_name = request()->post('salesman_name');

        $salesman_info = SystemUser::getUserInfoByRealname($salesman_name);

        $id                       = request()->post('id');
        $serial_number            = request()->post('serial_number');
        // $contract_no              = request()->post('contract_no');
        $contract_category        = request()->post('contract_category');
        $contract_start_timestamp = request()->post('contract_start_timestamp');
        $contract_end_timestamp   = request()->post('contract_end_timestamp');
        $contract_type            = request()->post('contract_type');
        $sign_type                = request()->post('sign_type');
        $customer_id              = request()->post('customer_id');
        $company_name             = request()->post('company_name');
        $channel_name             = request()->post('channel_name');
        $channel_full_name        = request()->post('channel_full_name');
        $third_party_company_name = request()->post('third_party_company_name');
        $third_product_name       = request()->post('third_product_name');
        $channel_product_name     = request()->post('channel_product_name');
        $salesman                 = $salesman_info['username'] ?? '';
        $product_id_arr           = request()->post('product_id_arr');
        $father_id_arr            = request()->post('father_id_arr');
        $archive_num              = request()->post('archive_num');
        $remark                   = request()->post('remark');
        $channel_type             = request()->post('channel_type');
        $sign_group               = request()->post('sign_group');
        $sign_year                = request()->post('sign_year_timestamp');

        if(!empty($sign_year)) {
            $sign_year = date("Y",$sign_year/1000);
        }

        $contract_info = [
            'serial_number'            => $serial_number,
            'contract_category'        => $contract_category,
            'contract_start_timestamp' => $contract_start_timestamp,
            'contract_end_timestamp'   => $contract_end_timestamp,
            'contract_type'            => $contract_type,
            'sign_type'                => $sign_type,
            'customer_id'              => $customer_id,
            'company_name'             => $company_name,
            'channel_name'             => $channel_name,
            'channel_full_name'        => $channel_full_name,
            'third_party_company_name' => $third_party_company_name,
            'third_product_name'       => $third_product_name,
            'channel_product_name'     => $channel_product_name,
            'salesman'                 => $salesman,
            'product_id_arr'           => $product_id_arr,
            'father_id_arr'            => $father_id_arr,
            'archive_num'              => $archive_num,
            'remark'                   => $remark,
            'channel_type'             => $channel_type,
            'sign_group'               => $sign_group,
            'sign_year'                => $sign_year,
        ];


        try {
            $res = ContractRepositorie::edit($id,$contract_info,$user_name);
            return $this->response(0, $res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

    /**
     * 删除合同
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:32:54
     */
    public function del(){
        $contract_id = request()->post('contract_id'); //合同编号
        $user_cookie = request()->post('user_cookie', '');
        try {
            ContractRepositorie::del($contract_id,$user_cookie);
            return $this->response(0);
        }catch(Exception $e){
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取公司列表
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:33:29
     */
    public function companyList(){
        $company_name = request()->post('company_name'); //合同编号

        try {
            $res = ContractRepositorie::company_list($company_name);
            return $this->response(0, $res);
        }catch(Exception $e){
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 获取公司列表
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:33:29
     */
    public function salesmanList(){
        $salesman_name = request()->post('salesman_name'); //商务名字

        try {
            $res = ContractRepositorie::salesman_list($salesman_name);
            return $this->response(0, $res);
        }catch(Exception $e){
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 发起 下载合同文件 申请
     *
     * 下载审批通过后可以下载一次
     *
     * 发起审批(1) -> 通过(10) -> 下载(作废40)
     * 发起审批(1) -> 驳回(20) 审批者驳回
     * 发起审批(1) -> 撤销(30) 申请者自己操作
     *
     * @return JsonResponse
     * <AUTHOR> 2024-06-03 17:58:15
     *
     */
    public function getDownloadPermission(){
        $contract_no = request()->post('contract_no'); //合同
        $user_cookie = request()->post('user_cookie'); //登陆用户cookie
        $apply_remark = request()->post('apply_remark'); //登陆用户cookie

        try {
            $res = ContractRepositorie::get_download_permission($contract_no,$user_cookie,$apply_remark);
            return $this->response(0, $res);
        }catch(Exception $e){
            return $this->response(100, [], $e->getMessage());
        }
    }


    public function abandonPermission(){
        $contract_no = request()->post('contract_no'); //合同
        $user_cookie = request()->post('user_cookie'); //登陆用户cookie

        try {
            $res = ContractRepositorie::abandon_download_permission($contract_no,$user_cookie);
            return $this->response(0, $res);
        }catch(Exception $e){
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

}