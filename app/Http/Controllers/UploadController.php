<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Lumen\Routing\Controller;
use App\Http\Repository\UploadRepository;

//class UploadController extends Controller
class UploadController extends CommonController
{
    private $repository;
    public function __construct(UploadRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }
    /**
     * 查询接口
     * @param string $key
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request,$key = '')
    {
        if(empty($key) || $key != 'eb829d5b2124783e785ce0cbc93c6b16'){
            return response()->json(['status' => 1002, 'msg' => "参数有误", 'data' => []]);
        }
        $node_area = $request->input('node_area');
        if(empty($node_area)){
            $node_area = 'shenzhen';
        }
        if(!in_array($node_area, ['shenzhen', 'beijing'])){
            return response()->json(['status' => 1005, 'msg' => "环境参数有误", 'data' => []]);
        }
        $where = [];
        if($node_area == 'beijing'){
            $where['bj'] = 2;
        }else{
            $where['status'] = 2;
        }
        $fields = ['id', 'json_data'];
        $res = $this->repository->getNewInfo($where, $fields);
        if(empty($res)){
            return response()->json(['status' => 1003, 'msg' => "失败", 'data' => []]);
        }
        return response()->json(['status' => 1001, 'msg' => "成功", 'data' => $res]);
    }

    /**
     * 回调更新接口
     * @param string $key
     * @return \Illuminate\Http\JsonRespons
     */

    public function callback(Request $request, $key = '')
    {
        if(empty($key) || $key != 'eb829d5b2124783e785ce0cbc93c6b16'){
            return response()->json(['status' => 1002, 'msg' => "参数有误", 'data' => []]);
        }
        $id = $request->input('id');
        $node_area = $request->input('node_area');
        if(empty($node_area)){
            $node_area = 'shenzhen';
        }
        if(empty($id)){
            return response()->json(['status' => 1004, 'msg' => "id不能为空", 'data' => []]);
        }
        if(!in_array($node_area, ['shenzhen', 'beijing'])){
            return response()->json(['status' => 1005, 'msg' => "环境参数有误", 'data' => []]);
        }
        $where = ['id'=>$id];
        if($node_area == 'beijing'){
            $where['bj'] = 2;
        }

        $res = $this->repository->updateInfo($where);
        if(empty($res)){
            return response()->json(['status' => 1003, 'msg' => "失败", 'data' => []]);
        }
        return response()->json(['status' => 1001, 'msg' => "成功", 'data' => []]);
    }
}
