<?php

namespace App\Http\Controllers;

use App\Http\Repository\ToolRepository;

/**
 * Class ToolController 工具控制器
 * @package App\Http\Controllers
 */
//class ToolController extends Controller
class ToolController extends CommonController
{
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new ToolRepository();
	}
	
	public function sendMail()
	{
		$res = $this->repository->sendMail();
		$this->createSimpleResponse('tool.' . $res);
	}
}