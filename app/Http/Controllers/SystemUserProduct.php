<?php

namespace App\Http\Controllers;

use App\Models\SystemUserProduct as Model;


//class SystemUserProduct extends BaseController
class SystemUserProduct extends CommonController
{

    /**
     * 添加产品所属用户
     */
    public function addUserProduct()
    {
        try {
            $username = request()->post('username', null);
            $father_id = request()->post('father_id', null);
            $is_show_money = request()->post('is_show_money', 0);//1 默认控制 收入成本毛利
            $is_show_money_finance = request()->post('is_show_money_finance', 0);//1 默认控制 金融收入
            $is_show_cost = request()->post('is_show_cost', 0);//1 默认控制 收入成本毛利
            $is_show_money_agent = request()->post('is_show_money_agent', 0);//1 默认控制 收入成本毛利
            $is_show_cache = request()->post('is_show_cache', 0);//缓存

            if (!$username || !$father_id) throw new \Exception('缺少必要参数');
            $res = Model::insert([
                'username'=>trim($username), 
                'father_id'=>intval($father_id), 
                'is_show_money'=>intval($is_show_money),
                'is_show_money_finance'=>intval($is_show_money_finance),
                'is_show_cost'=>intval($is_show_cost),
                'is_show_money_agent'=>intval($is_show_money_agent),
                'is_show_cache'=>intval($is_show_cache
                ),
            ]);
            return $this->response(0, $res);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 删除产品所属用户
     */
    public function delUserProduct()
    {
        try {
            $username = request()->post('username', null);
            $father_id = request()->post('father_id', null);

            if (!$username || !$father_id) throw new \Exception('缺少必要参数');
            $res = Model::where(['username'=>trim($username), 'father_id'=>intval($father_id)])->delete();
            return $this->response(0, $res);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
}
