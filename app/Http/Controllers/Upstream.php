<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/16 0016
 * Time: 16:52
 */

namespace App\Http\Controllers;

use App\Http\Repository\Upstream\Driver;
use App\Http\Repository\Upstream\Upstream200Driver;
use App\Http\Repository\Upstream\Upstream210Driver;
use App\Http\Repository\Upstream\Upstream401Driver;
use App\Models\MongoUpstreamStatisticsLog;
use App\Http\Repository\Upstream\Upstream801Driver;

//class Upstream extends BaseController
class Upstream extends CommonController
{
    public function send()
    {
        try {
            $upstreamDriver = $this->upstreamDriver();

            //校验内容
            $message = $upstreamDriver->verify($this->data);

            if ($message !== true) {
                $status = 1001;
                return response()->json(compact('status', 'message'));
            }
            //插入数据
            $upstreamDriver->create();
            $message = '';
            $status  = 0;
        } catch (\Exception $exception) {
            $message = $exception->getMessage();
            $status  = 1002;
        }

        $this->write_log($status, $message);
        return response()->json(compact('status', 'message'));
    }

    /**
     * 记录访问日志
     *
     * @access protected
     *
     * @param $status  integer 状态码
     * @param $message string 错误信息
     *
     * @return void
     **/
    protected function write_log($status, $message)
    {
        $product_id = array_get($this->data, 'product_id');
        $date       = array_get($this->data, 'date');
        $apikey     = array_get($this->data, 'apikey');
        $node       = array_get($this->data, 'node');
        $data       = array_get($this->data, 'data');
        $require_date = date('YmdHis');
        //记录请求日志
        $logModel = new MongoUpstreamStatisticsLog();
        $logModel->insert(compact('status', 'message', 'product_id', 'date', 'apikey', 'node', 'data', 'require_date'));
    }

    /**
     * 获取Driver对象
     *
     * @access protected
     *
     * @return Driver
     **/
    protected function upstreamDriver()
    {
        $key = array_key_exists('key', $this->data) ? $this->data['key'] : '';
        switch ($key) {
            case 'fbaace1340a8706863ed6ae17560355c':
                return new Upstream200Driver();
                break;
            case '99bbdb8426f8b4e8d0cc3ebd92484590':
                return new Upstream210Driver();
                break;
            case '54ab929b16136d61edb141cf5acf787f':
                return new Upstream401Driver();
                break;
            case '31064ac828ebea24f969485e336262af':
                return new Upstream801Driver();
                break;
            default:
                throw new \Exception("非法的key字段 [{$key}]");
                break;
        }
    }
}