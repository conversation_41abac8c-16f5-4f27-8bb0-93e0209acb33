<?php

namespace App\Http\Controllers;

use App\Http\Repository\ConfigChannelStatis;

//class ConfigChannelStatisController extends BaseController
class ConfigChannelStatisController extends CommonController
{
    protected $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = new ConfigChannelStatis();
    }
    /**
     * 产品监控-查得率
     */
    public function statisList()
    {
        try {
            $data = $this->repository->statisList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 产品监控-查得率
     */
    public function statisInfo()
    {
        try {
            $data = $this->repository->statisInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品监控-查得率-修改
     */
    public function statisEdit()
    {
        try {
            $data = $this->repository->statisEdit();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品监控-查得率-添加
     */
    public function statisAdd()
    {
        try {
            $data = $this->repository->statisAdd();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
}
