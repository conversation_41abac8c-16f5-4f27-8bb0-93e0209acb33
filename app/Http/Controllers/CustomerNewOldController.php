<?php

namespace App\Http\Controllers;

use App\Define\Common;
use App\Http\Repository\ApprovalRepository;
use App\Http\Repository\ContractRepositorie;
use App\Http\Repository\CustomerNewOldRepositorie;
use App\Http\Repository\DeptRepository;
use App\Models\Customer;
use App\Models\Customer\CustomerGroup;
use App\Models\Dept;
use App\Models\SystemSession;
use App\Models\SystemUser;
use App\Providers\Auth\DataAuth;
use App\Utils\Helpers\Func;
use Exception;
use Illuminate\Http\JsonResponse;

/**
 *  审批流
 * @package App\Http\Controllers
 */
class CustomerNewOldController extends CommonController
{

	public function __construct()
	{
		parent::__construct();
	}


    /**
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-26 15:10:14
     *
     */
    public function options(){
        try {
            $res = CustomerNewOldRepositorie::options();
            return $this->response(0, $res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取列表
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-21 18:09:31
     *
     */
    public function customerNewOldList(){
        $collect_type    = request()->post('collect_type');    //统计类型
        $rel_id          = request()->post('rel_id');          //rel_id
        $month           = request()->post('month');           //
        $is_new_customer = request()->post('is_new_customer'); //


        $page      = intval(request()->post('page', 1));
        $page_size = intval(request()->post('page_size', 10));

        //获取用户名
        $session_id = request()->post('user_cookie', '');
        $username   = (new SystemSession())->getNameBySessionId($session_id);

        $params = [
            'collect_type'    => $collect_type,
            'rel_id'          => $rel_id,
            'month'           => $month,
            'is_new_customer' => $is_new_customer,
            'page'            => $page,
            'page_size'       => $page_size,
        ];

        try {
            $list  = CustomerNewOldRepositorie::get_list($params);
            $count = CustomerNewOldRepositorie::get_count($params);


            $rel_ids = array_column($list, 'rel_id');

            $group_list = [];
            $customer_list = Customer::getCustomerListByCustomerIds(['customer_id','name'], $rel_ids);
            $customer_list = array_column($customer_list,'name', 'customer_id');
            if(Common::CUSTOMER_NEW_OLD_COLLECT_TYPE_CUSTOMER != $collect_type){
                $group_list = CustomerGroup::getListByGroupIds(['group_id','group_name'],$rel_ids);
                $group_list = array_column($group_list,'group_name', 'group_id');
            }

            foreach ($list as &$item) {
                $item['rel_name'] = $customer_list[$item['rel_id']] ?? ($group_list[$item['rel_id']] ?? '');
            }
            $res = [
                'list'      => $list,
                'count'     => $count,
                'page'      => $page,
                'page_size' => $page_size,
            ];
            return $this->response(0, $res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }
}