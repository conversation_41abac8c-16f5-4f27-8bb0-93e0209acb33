<?php
namespace App\Http\Controllers;

use App\Http\Repository\ReceiptRepository;

class ReceiptController extends CommonController
{
    public function __construct()
    {
        $this->repository = new ReceiptRepository();
    }

    public function getSplitCustomer()
    {
        try {
            $data = $this->repository->getSplitCustomer();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function split(){
        try {
            $data = $this->repository->split();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function split_by_excel(){
        try {
            $data = $this->repository->split_by_excel();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function split_price(){
        try {
            $data = $this->repository->split_price();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function get_split_price(){
        try {
            $data = $this->repository->get_split_price();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function get_split_price_test(){
        try {
            $data = $this->repository->get_split_price_test();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function split_price_test(){
        try {
            $data = $this->repository->split_price_test();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }
}