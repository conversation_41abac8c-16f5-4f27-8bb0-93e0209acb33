<?php

namespace App\Http\Controllers;

use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Jobs\StatisticsInterfaceUsageJob;
use App\Models\ChannelProduct;
use App\Models\Product;

/**
 * Class ProdoctController 获取产品的相关信息的各类接口（控制器）
 *
 * @package App\Http\Controllers
 */
//class ProdoctController extends Controller
class ProdoctController extends CommonController
{
    private $bxfScoreProductId = 1000;
    private $bxfFieldProductId = 210;

    private $bxfScoreProducts = [
        251,
        288,
        289,
        290,
        291,
        292,
        293,
        294,
        295,
        296,
        297,
        298,
        711,
        299,
    ];

    public function getProductInfo()
    {
        $data = $this->getJsonByRaw();

        $productId = $data['product_id'];
        $getInfoProductId = $productId == $this->bxfScoreProductId ? $this->bxfFieldProductId : $productId;

        $checkResult = $this->checkUsageStatistics($data);
        if( $checkResult != 0 ){
            $this->createSimpleResponse('common.'.$checkResult);
        }

        $result = Product::where('father_id', $getInfoProductId)->select('product_id', 'product_name','product_enname', 'product_param', 'status')->get()->toArray();
        $pinfo = [];
        foreach ( $result as $k=>$v ){

            $item = [];
            $item['id'] = $v['product_id'];
            $item['name'] = $v['product_enname'];
            $item['cnname'] = $v['product_name'];
            $item['status'] = $v['status'];
            $item['param'] = $v['product_param'] ? json_decode($v['product_param'], true) : "";

            $pinfo[] = $item;
        }

        //如果是邦信分的 通讯产品 和 评分产品，则进行特殊处理，通讯产品不返回 评分产品
        if( 210 == $productId ){
            foreach ( $pinfo as $k => $v ){
                if( in_array($v['id'], $this->bxfScoreProducts) ){
                    unset($pinfo[$k]);
                }
            }

            $pinfo = array_values($pinfo);
        }
        if( 1000 == $productId ){
            foreach ( $pinfo as $k => $v ){
                if( !in_array($v['id'], $this->bxfScoreProducts) ){
                    unset($pinfo[$k]);
                }
            }

            $pinfo = array_values($pinfo);
        }

        $this->createBaseResponse('success', 0, $pinfo);
    }


    public function getChannelInfo()
    {
        $data = $this->getJsonByRaw();

        $checkResult = $this->checkUsageStatistics($data);
        if( $checkResult != 0 ){
            $this->createSimpleResponse('common.'.$checkResult);
        }

        $result = ChannelProduct::getChannelInfoByFid($data['product_id']);

        $channelInfo = [];
        foreach ( $result as $v ){
            $item = [];
            $item['id'] = $v['num'];
            $item['name'] = $v['name'];
            $item['cnname'] = $v['label'];
            $item['status'] = $v['status'];
            $item['param'] = json_decode($v['param'], true);

            $channelInfo[] = $item;
        }

        $this->createBaseResponse('success', 0, $channelInfo);
    }

    protected function checkUsageStatistics($data)
	{
		//校验产品数据
		$productId = array_get($data, 'product_id');
		if (!$productId) {
			return 10003;
		}

		$productKey = array_get($data, 'product_key');
		if (!$productKey) {
			return 10004;
		}

		if ($productKey != (new CacheDriverFacade())->getProductKeyByProductId($productId)) {
			return 10005;
		}

		return 0;
	}
}