<?php

namespace App\Http\Controllers;

use App\Models\ProductClickHouse;
use App\Models\ProductTopicMonitoring;
use App\Models\ProductTopicMonitoringCondition;
use http\Env\Request;
use Illuminate\Validation\Rules\Unique;
use Matrix\Exception;

/**
 * Class ProductController 产品配置控制器
 * @package App\Http\Controllers
 */
//class ProductController extends Controller
class ProducThemeAlarmController extends CommonController
{
	protected $productclickhouse;
	
	public function __construct()
	{
		parent::__construct();
		$this->productclickhouse = new ProductClickHouse();
	}

	public function producThemeAlarmList()
    {
        $topic = Request()->get('topic','');
        $where = [];
        if (!empty($topic)){
            $where[] = ['topic','=',$topic];
        }
        $product_theme_list = ProductTopicMonitoring::getList($where);
        return $this->response(0, ['data'=>$product_theme_list]);
    }

    public function updateStatus()
    {
        $id = Request()->get('id',0);
        $status = Request()->get('status',0);

        $res = ProductTopicMonitoring::updateStatus($id,['status'=>$status]);

        if ($res){
            return $this->response(0);
        }else{
            return $this->response(1);
        }
    }

    public function getEdit()
    {
        $id = Request()->get('id');
        $ptm = ProductTopicMonitoring::getPtmByid($id);
        $ptmc = ProductTopicMonitoringCondition::getPtmcByPtmId($ptm['ptm_id']);
        $ptm['conditions'] = $ptmc;

        return $this->response(1,['data'=>$ptm]);
    }

	public function testSql()
    {
        $testsql = '';
        $sql = Request()->post('sql','');
        $condition = Request()->post('condition','');
        $onlysql = Request()->post('onlysql',0);

        if (!$onlysql && empty($condition)){
            if (empty($sql)){
                return $this->response(1, ['msg'=>'请填写sql']);
            }
            return $this->response(1, ['msg'=>'请填写条件']);
        }

        $testsql = $sql;

        if (!$onlysql){
            $testsql = $testsql . ' where ' . $condition;
        }


        try {
            $data = $this->productclickhouse->performSql($testsql);
            return $this->response(0, ['data'=>$data,'msg'=>'sql执行成功']);
        }catch (\Exception $exception){
            $msg = $exception->getMessage();
            return $this->response(1, ['msg'=>$msg]);
        }
    }

    public function addOrEditTheme()
    {
        $topic = Request()->post('topic','');
        $time_interval = Request()->post('time_interval',10);
        $sql = Request()->post('sql','');
        $contains_column = Request()->post('contains_column','');
        $conditions = Request()->post('conditions','');
        $ptm_id = Request()->post('ptm_id','');

        if (empty($conditions)){
            return false;
        }

        if (empty($ptm_id)){
            $ptm_id = $this->uuid();
            $insertData['ptm_id'] = $ptm_id;
            $insertData['topic'] = $topic;
            $insertData['sql'] = $sql;
            $insertData['contains_column'] = $contains_column;
            $insertData['time_interval'] = $time_interval;
            $insertData['time_count'] = $time_interval;
            $insertData['create_at'] = time();

            ProductTopicMonitoring::insertData($insertData);

        }else{
            $updateData['topic'] = $topic;
            $updateData['sql'] = $sql;
            $updateData['contains_column'] = $contains_column;
            $updateData['time_interval'] = $time_interval;
            $updateData['time_count'] = $time_interval;
            ProductTopicMonitoring::updateByPtmId($ptm_id,$updateData);
            ProductTopicMonitoringCondition::deleteByPtmId($ptm_id);

        }

        array_walk($conditions,function ($item) use($ptm_id){
            $item['ptm_id'] = $ptm_id;
            $item['create_at'] = time();
            $item['level'] = (int)$item['level'];
            ProductTopicMonitoringCondition::insertData($item);
        });


        return $this->response(0);
    }

   private function uuid() {
        if (function_exists ( 'com_create_guid' )) {
            return com_create_guid ();
        } else {
            mt_srand ( ( double ) microtime () * 10000 );
            $charid = strtoupper ( md5 ( uniqid ( rand (), true ) ) );
            $hyphen = chr ( 45 );
            $uuid = '' . substr ( $charid, 0, 8 ) . $hyphen . substr ( $charid, 8, 4 ) . $hyphen . substr ( $charid, 12, 4 ) . $hyphen . substr ( $charid, 16, 4 ) . $hyphen . substr ( $charid, 20, 12 );
            return $uuid;
        }
    }
}