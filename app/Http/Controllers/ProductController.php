<?php

namespace App\Http\Controllers;

use App\Http\Repository\ProductRepository;

/**
 * Class ProductController 产品配置控制器
 * @package App\Http\Controllers
 */
//class ProductController extends Controller
class ProductController extends CommonController
{
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new ProductRepository();
	}
	
	/**
	 * 产品列表
	 */
	public function index()
	{
		$data = $this->repository->getInfo();
		$this->createDataResponse($data);
	}
	
	/**
	 * 增加产品
	 */
	public function add()
	{
		$code = $this->repository->add();
		
		$this->createSimpleResponse('product.' . $code);
	}
}