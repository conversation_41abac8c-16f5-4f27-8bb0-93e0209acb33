<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\CuishouExpress;
use App\TraitUpgrade\CurlTrait;

//class CuishouExpressController extends BaseController
class CuishouExpressController extends CommonController
{
    use CurlTrait;
    public function checkChannel()
    {
        $channel = request()->post('channel', 0);
        $pid = request()->post('pid', 0);
        $num = request()->post('num', 0);

        if (!$channel || !$pid) {
            return $this->response(100, [], '参数错误');
        }
        if (!isset(CuishouExpress::$phones[$channel][$num])) {
            return $this->response(100, [],'号码不存在');
        }

        $expressObj = new CuishouExpress();
        $params = $expressObj->getParams(CuishouExpress::$phones[$channel][$num], $pid);
        $res = $this->post(env('CUISHOU_EXPRESS_API'), $params);
        $res['phone'] = CuishouExpress::$phones[$channel][$num];

        return $this->response(0, $res);
    }
}