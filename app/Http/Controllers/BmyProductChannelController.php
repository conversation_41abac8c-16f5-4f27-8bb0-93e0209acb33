<?php

namespace App\Http\Controllers;

use App\Http\Repository\BmyProductChannelRepository;

class BmyProductChannelController extends CommonController
{
    public function __construct()
    {
        $this->repository = new BmyProductChannelRepository();
    }

    public function getPriceList()
    {
        try {
            $data = $this->repository->getConfigPriceInterfaceList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function getChannelList()
    {
        try {
            $data = $this->repository->getChannelList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 获取渠道列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChannelListv2()
    {
        try {
            $data = $this->repository->getChannelListV2();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function getInterfaceList()
    {
        try {
            $data = $this->repository->getInterfaceList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 获取产品列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductListV2()
    {
        try {
            $data = $this->repository->getProductListV2();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 获取账号与产品联动数据
     */
    public function getAccountProductList()
    {
        try {
            $data = $this->repository->getAccountProductList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }

    }

    public function getShuntList()
    {
        try {
            $data = $this->repository->getShuntList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 渠道分流数据列表V2
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShuntListV2()
    {
        try {
            $data = $this->repository->getShuntListV2();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function doShunt()
    {
        try {
            $res = $this->repository->saveShunt();
            $msg = $res == true ? 'ok' : '失败';
            $status = $res == true ? 0 : 100;
            return $this->response($status, [], $msg);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function getConfigChannelAccountList()
    {
        try {
            $data = $this->repository->getConfigChannelAccountList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 渠道分流变动历史列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInterfacePriceChange()
    {
        try {
            $data = $this->repository->getInterfacePriceChange();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function getConfigChannelAccountInfo()
    {
        try {
            $data = $this->repository->getConfigChannelAccountInfo();
            var_dump($data);
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function doConfigChannelAccount()
    {
        try {
            $res = $this->repository->saveConfigChannelAccount();
            $msg = $res == true ? 'ok' : '失败';
            $status = $res == true ? 0 : 100;
            return $this->response($status, [], $msg);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function delConfigChannelAccount()
    {
        try {
            $res = $this->repository->delConfig();
            $msg = $res == true ? 'ok' : '失败';
            $status = $res == true ? 0 : 100;
            return $this->response($status, [], $msg);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }
}