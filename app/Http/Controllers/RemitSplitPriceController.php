<?php
namespace App\Http\Controllers;

use App\Http\Repository\RemitSplitPriceRepository;

class RemitSplitPriceController extends CommonController
{
    public function __construct()
    {
        $this->repository = new RemitSplitPriceRepository();
    }

    public function getSplitPriceList()
    {
        try {
            $data = $this->repository->getSplitPriceList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function downloadSplitPrice() {
        try {
            return $this->repository->downloadSplitPrice();
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }
}