<?php

namespace App\Http\Controllers\CldcStat;

use App\Http\Controllers\CommonController;
use App\Http\Repository\StatCldcAiRepository;
use App\Http\Repository\StatCldcRepository;
use Exception;
use Illuminate\Http\JsonResponse;

class StatCldcController extends CommonController
{

    // 渠道统计数据
    public function getList()
    {
        $repository = new StatCldcRepository();
        $params = request()->post();
        $export_type = $params['export_type'] ?? '';//导出类型

        try {
            if ($export_type) { // 按日导出 按月导出
                $data = $repository->statListExport($export_type);
            } else {
                $data = $repository->statList();
            }
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }


    }

    /**
     *  路由数据统计开始
     */

    //  下拉任务选择项
    public function getOptions()
    {
        $repository = new StatCldcAiRepository();

        try {
            $data = $repository->getOptions();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }


    }



    // ai外呼统计
    public function getAiList()
    {
        $repository = new StatCldcAiRepository();
        try {
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }


    }

}
