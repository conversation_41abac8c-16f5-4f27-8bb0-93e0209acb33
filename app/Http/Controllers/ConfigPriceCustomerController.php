<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Providers\RedisCache\RedisCache;
use App\Models\ConfigPriceCustomer;


class ConfigPriceCustomerController extends CommonController
{

    public function getInfo(){
        $id = request()->get('id', 0);

        $config = ConfigPriceCustomer::where(['id' => $id])->first()->toArray();
        if(empty($config)){
            $this->createBaseResponse('你要复制的配置id有误', 5001, []);
        }

        $config['price'] = json_decode($config['price'], true);

        $account_info = Account::getAccountByApikey($config['apikey']);

        $config['account_id'] = $account_info['account_id'];

        $this->createBaseResponse('succes', 0, $config);

    }



}