<?php

namespace App\Http\Controllers;

use App\Http\Repository\CheckDataRepository;
use App\Models\SystemSession;

/**
 *  数据核验页面接口
 * @package App\Http\Controllers
 */
class CheckDataController extends CommonController
{
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new CheckDataRepository();
	}

    public function getList()
    {
        try {
            $data = $this->repository->getCkList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    // 获取最近一次调用 
    public function getLastCall()
    {
        try {
            $data = $this->repository->getLastestCall();
            if($data){
                return $this->response(0, $data);
            }else{
                return $this->response(100, '','未查询到数据');
            }
        } catch (\Exception $e) {
            return $this->response(100, null, $e->getMessage());
        }
    }
    

    public function getChannelList(){
        try {
            $data = $this->repository->getChannelList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }
	
}