<?php

namespace App\Http\Controllers;

use App\Http\Repository\StatCustomerRepository;
use App\Http\Repository\StatPMRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Exception;


/**
 * 产品(pm)视角统计数据
 */
class StatPMController extends CommonController
{

    /**
     * 产品视角 主产品统计
     */
    public function statMainList() {
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_main_stat_list();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }



    /**
     * 产品视角 客户维度统计
     */
    public function statCustomerList(){
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_customer_stat_list();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }

    /**
     * 产品视角 客户维度统计 详情
     *
     * @return JsonResponse
     * <AUTHOR> 2024-07-12 11:56:33
     *
     */
    public function statCustomerCompare(){
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_customer_stat_compare();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }

    /**
     * 产品视角 产品维度统计 详情
     *
     * @return JsonResponse
     * <AUTHOR> 2024-07-12 11:56:33
     *
     */
    public function statProductCompare(){
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_product_stat_compare();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }

    /**
     * 产品视角 产品维度统计
     */
    public function statProductList(){
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_product_stat_list();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }


    /**
     * 产品视角 渠道维度统计
     */
    public function statChannelList(){
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_channel_stat_list();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }

    /**
     * 渠道明细
     *
     * @return JsonResponse
     * <AUTHOR> 2024-07-19 16:04:01
     *
     */
    public function statChannelCompare(){
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_channel_stat_compare();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }






    /**
     * 获取配置过的子产品列表
     */
    public function statSubProductList(){
        try {
            $repository = new StatPMRepository();
            $data = $repository->pm_sub_product_list();
            return $this->response(0, $data);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().','.$e->getFile().':'.$e->getLine());
        }
    }
}
