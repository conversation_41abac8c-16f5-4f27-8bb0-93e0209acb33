<?php

namespace App\Http\Controllers;

use App\Http\Repository\BillRepository;
use App\Models\MongoCacheExcel;
use App\Models\MongoLog;
use App\Support\CustomException;
use App\TraitUpgrade\ResponseTrait;
use <PERSON><PERSON>\Lumen\Routing\Controller;

//class BillController extends Controller
class BillController extends CommonController
{
    use ResponseTrait;

    private $repository;

    /**
     * BillController constructor.
     *
     * @param $repository
     */
    public function __construct(BillRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 获取日账单日志列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDayLogLists()
    {
        try {
            $lists = $this->repository->getDayLogLists();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 获取日账单列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDayLists()
    {
        try {
            $lists = $this->repository->getDayLists();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 某个客户消费的金额
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerBill()
    {
        try {
            $lists = $this->repository->getCustomerBill();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 特定计费配置对应的总调用量,金额消费信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSectionList()
    {
        try {
            $lists = $this->repository->getSectionList();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 账单列表
     */
    public function sourceList()
    {
        try {
            $lists = $this->repository->sourceList();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 日账单excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadDay()
    {
        try {
            return $this->repository->downloadDay();
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 各天的账单信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function dayList()
    {
        try {
            $lists = $this->repository->dayList();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 客户每月的账单excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function genCustomerMonthExcel()
    {
        try {
            return $this->repository->genCustomerMonthExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 客户每月的账单
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerBillGroupMonth()
    {
        try {
            $lists = $this->repository->getCustomerBillGroupMonth();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 产品每月的消费excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function genProductMonthExcel()
    {
        try {
            return $this->repository->genProductMonthExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 产品每月的消费
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductBillGroupMonth()
    {
        try {
            $lists = $this->repository->getProductBillGroupMonth();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 产品客户对账单生成excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function genProductCustomerExcel()
    {
        try {
            return $this->repository->genProductCustomerExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /*
     * 删除客户余额监控标记
     * @param $customer_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function warningFlag($customer_id)
    {
        try {
            $this->repository->warningFlag($customer_id);
            return $this->response(['msg' => '清理成功']);
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        }
    }

    /**
     * 产品客户对账单
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductCustomerList()
    {
        try {
            $lists = $this->repository->getProductCustomerList();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /*
     * 日志(预警)
     * @param $w_uuid
     * @return \Illuminate\Http\JsonResponse
     */
    public function warningLogs($w_uuid)
    {
        try {
            $logs = MongoLog::getListByCondition(compact('w_uuid'));
            return $this->response(compact('logs'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        }
    }

    /**
     * 产品对账单到处excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function productBillDownload()
    {
        try {
            return $this->repository->productBillDownload();
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 产品对账单
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBillProductList()
    {
        try {
            $lists = $this->repository->getBillProductList();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 客户产品对账单excel
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function CPExcel()
    {
        try {
            return $this->repository->CPExcel();
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 获取一个客户下的产品账单信息
     *
     * @param $customer_id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductBillOfCustomer($customer_id)
    {
        try {
            $lists = $this->repository->getProductBillOfCustomer($customer_id);
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 账单列表下载excel
     *
     */
    public function download()
    {
        try {
            return $this->repository->download();
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 对账单进行排序
     * @return \Illuminate\Http\JsonResponse
     */
    public function sorts()
    {
        try {
            $lists = $this->repository->sorts();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 客户账单列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function customers()
    {
        try {
            $lists = $this->repository->customers();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 邮件发送对账单
     * @return \Illuminate\Http\JsonResponse
     */
    public function email()
    {
        try {
            $this->repository->email();
            return $this->response(['msg' => '邮件发送成功']);
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 对账单
     *
     * @param $customer_id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function history($customer_id)
    {
        try {
            $history = $this->repository->history($customer_id);
            return $this->response(compact('history'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file' . $e->getFile());
        }
    }

    /**
     * 生成excel对应的消费明细
     *
     * @param $customer_id
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function excel($customer_id)
    {
        $mongo = new MongoCacheExcel();
        $excel = $mongo->where('customer_id', $customer_id)
            ->where('month', intval(date('Ym', strtotime('last day of last month'))))
            ->orderBy('month desc')
            ->pluck('excel')
            ->toArray();
        if (empty($excel)) {
            return $this->setStatus(1478)
                ->responseError('暂无缓存数据');
        }
        $consumption = $excel[0];
        return $this->response(compact('consumption'));
//        try {
//            $consumption = $this->repository->excel($customer_id);
//            return $this->response(compact('consumption'));
//        } catch (CustomException $e) {
//            return $this->setStatus(1478)->responseError($e->getMessage());
//        } catch (\Exception $e) {
//            return $this->setStatus(1478)->responseError($e->getMessage() . ' at file ' . $e->getFile() . ' at line ' . $e->getLine());
//        }
    }

    /**
     * 查询日志
     *
     * @param $uuid
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logs($uuid)
    {
        try {
            $logs = MongoLog::getListByCondition(compact('uuid'));
            return $this->response(compact('logs'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        }
    }

    /**
     * 账单查询
     * @return \Illuminate\Http\JsonResponse
     */
    public function bills()
    {
        try {
            $list = $this->repository->bills();
            return $this->response(compact('list'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . 'at file ' . $e->getFile());
        }
    }
}