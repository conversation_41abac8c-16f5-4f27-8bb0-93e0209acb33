<?php

namespace App\Http\Controllers;

use App\Http\Repository\StatRepository;
use App\Models\HandleLog;
use App\Models\MongoLog;
use App\Models\MongoUpstreamEnterpriseQuery;
use App\Support\CustomException;
use App\TraitUpgrade\ResponseTrait;
use <PERSON><PERSON>\Lumen\Routing\Controller;

//class StatController extends Controller
class StatController extends CommonController
{
    use ResponseTrait;

    private $repository_stat;

    /**
     * StatController constructor.
     *
     * @param $repository_stat
     */
    public function __construct(StatRepository $repository_stat)
    {
        parent::__construct();
        $this->repository_stat = $repository_stat;
    }

    /**
     * 账号每天的爬取成功的具体的号码列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function crawlerTelList()
    {
        try {
            $lists = $this->repository_stat->crawlerTelList();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     *  账号每天的爬取成功的具体的号码
     */
    public function crawlerTel()
    {
        try {
            $item = $this->repository_stat->crawlerTel();
            return $this->response(compact('item'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }


    /**
     * 列表统计
     * @return \Illuminate\Http\JsonResponse
     */
    public function list()
    {
        try {
            $list_stat = $this->repository_stat->list();
            return $this->response(compact('list_stat'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 详情统计
     * @return \Illuminate\Http\JsonResponse
     */
    public function detail()
    {
        try {
            $list_stat = $this->repository_stat->detail();
            return $this->response(compact('list_stat'));
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 每天调用信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function day()
    {
        try {
            $list_stat = $this->repository_stat->day();
            return $this->response(compact('list_stat'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 日报信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function report()
    {
        try {
            $list_stat = $this->repository_stat->report();
            return $this->response(compact('list_stat'));
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 产品调用量统计
     * @return \Illuminate\Http\JsonResponse
     */
    public function amount()
    {
        try {
            $amount = $this->repository_stat->amount();
            return $this->response(compact('amount'));
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage());
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     *
     * 催收快捷版上游的数据入库
     * @return \Illuminate\Http\JsonResponse
     */
    public function shortcuts()
    {
        try {
            $this->repository_stat->shortcuts();

            // log && response
            $msg = 'success';
            MongoLog::create([
                'action'  => 'upstream',
                'type'    => 'shortcut',
                'source'  => request()->post(),
                'msg'     => $msg,
                'success' => true
            ]);
            return $this->response(compact('msg'));
        } catch (CustomException $e) {
            $msg = $e->getMessage();
            MongoLog::create([
                'action'  => 'upstream',
                'type'    => 'shortcut',
                'source'  => request()->post(),
                'msg'     => $msg,
                'success' => false
            ]);
            return $this->setStatus(1478)
                ->responseError($msg);
        } catch (\Exception $e) {
            $msg = $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
            MongoLog::create([
                'action'  => 'upstream',
                'type'    => 'shortcut',
                'source'  => request()->post(),
                'msg'     => $msg,
                'success' => false
            ]);
            return $this->setStatus(1478)
                ->responseError($msg);
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    public function verification()
    {
        try {
            $this->repository_stat->verification();

            // log && response
            $msg = 'success';
            MongoLog::create([
                'action'  => 'upstream',
                'type'    => 'verification',
                'source'  => request()->post(),
                'msg'     => $msg,
                'success' => true
            ]);
            return $this->response(compact('msg'));
        } catch (CustomException $e) {
            $msg = $e->getMessage();
            MongoLog::create([
                'action'  => 'upstream',
                'type'    => 'verification',
                'source'  => request()->post(),
                'msg'     => $msg,
                'success' => false
            ]);
            return $this->setStatus(1478)
                ->responseError($msg);
        } catch (\Exception $e) {
            $msg = $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
            MongoLog::create([
                'action'  => 'upstream',
                'type'    => 'verification',
                'source'  => request()->post(),
                'msg'     => $msg,
                'success' => false
            ]);
            return $this->setStatus(1478)
                ->responseError($msg);
        } catch (\Error $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }

    }

    //邦企查上游统计数据入库
    //17:13 2019/12/19 0019
    //简单的入库
    public function enterprise_query()
    {
        try {
            $upstream = request()->post('upstream');
            $day      = request()->post('day');
            $node     = request()->post('node');
            $data     = request()->post('data');

            if (empty($upstream) || empty($day) || empty($node) || empty($data)) {
                throw new \Exception('post day is empty');
            }

            MongoUpstreamEnterpriseQuery::updateOrCreate(compact('upstream', 'day', 'node'),
                compact('upstream', 'day', 'node', 'data'));

            return $this->setStatus(0)->responseError('success');
        } catch (\Exception $e) {
            return $this->setStatus(1478)
                ->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }
}
