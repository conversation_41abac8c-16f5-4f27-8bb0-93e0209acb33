<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Models\Channel;
use App\Models\Monitor\ChannelStatisExceptionPeriod;
use App\Models\Monitor\ChannelStatisPeriod;
use App\Models\Monitor\ChannelStatus;
use App\Models\Monitor\LogProduct;
use App\Models\Monitor\ProductStatisExceptionPeriod;
use App\Models\Monitor\ProductStatisPeriod;
use App\Models\Monitor\ProductStatus;
use App\Models\Monitor\ProductValueSpreadDetailPeriod;
use App\Models\Monitor\ProductValueSpreadPeriod;
use App\Providers\Monitor\ProductStatusService;
use App\Providers\Monitor\ValueSpreadGoalValueService;
use App\Providers\RedisCache\RedisCache;

//class ChannelHistoryStatisController extends Controller
class ChannelHistoryStatisController extends CommonController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function getHeadColumn()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
//        $productId = request()->post('product_id');
        $channelId = request()->post('channel_id', 0);
        $timeSection = request()->post('time_section');
        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            ['start_time', '>=', $startTime],
            ['end_time', '<=', $endTime],
//            ['product_id', '=', $productId],
            ['channel_id', '=', $channelId],
            ['apikey', '=', $apikey],
            ['status', '!=', 0],
        ];

        //获取异常合计信息
        $where[] = ['status','!=',0];
        $totalException = ChannelStatisPeriod::getExceptionTotalCountByCondition($where);

        $exceptionColumn = $this->getExceptionColumn($totalException);
        $head = $this->getHead($exceptionColumn);

        $this->createDataResponse($head);
    }

    public function getSuccessInfo()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
//        $productId = request()->post('product_id');
        $channelId = request()->post('channel_id', 0);
        $timeSection = request()->post('time_section');
        $page = request()->post('page', 1);
        $limit = request()->post('limit', 10);
        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            ['start_time', '>=', $startTime],
            ['end_time', '<=', $endTime],
//            ['product_id', '=', $productId],
            ['channel_id', '=', $channelId],
            ['apikey', '=', $apikey],
        ];

        $statisData = ChannelStatisPeriod::getList($where, $page, $limit);
        $ids = array_column($statisData, 'id');
        //获取异常数据
        $exceptionData = ChannelStatisExceptionPeriod::getListByPsId($ids);
        $statisData = $this->mergeStatisDataException($statisData, $exceptionData);
        //获取时间段
        $statisData = $this->getSomeInfo($statisData);
        //获取总数
        $countInfo = ChannelStatisPeriod::getCount($where);
        $result['count'] = isset($countInfo['count']) ? $countInfo['count'] : 0;

        //获取合计信息
        $totalData = ChannelStatisPeriod::getTotalCountByCondition($where);
        $totalData = $this->ratioAndTimeToTotalInfo($totalData);
        //获取异常合计信息
        $where[] = ['status','!=',0];
        $totalException = ChannelStatisPeriod::getExceptionTotalCountByCondition($where);
        $totalData = $this->mergeTotalException($totalData, $totalException);
        $totalData['time_section'] = '合计';

        $exceptionColumn = $this->getExceptionColumn($totalException);
        $head = $this->getHead($exceptionColumn);
        $totalData = $this->formatRow($totalData, $head);
        $statisData = $this->formatStatisRow($statisData, $head);

        $rowData[] = $totalData;
        $result['data'] = array_merge($rowData, $statisData);

        $this->createDataResponse($result);
    }

    private function ratioAndTimeToTotalInfo($totalInfo)
    {
        if( $totalInfo ){
            $totalInfo['ratio'] = divGetRatio($totalInfo['success'], $totalInfo['all'], 4);
            $totalInfo['run_time'] = intval($totalInfo['run_time']);
            return $totalInfo;
        }else{
            return [];
        }
    }

    private function mergeTotalException($totalInfo, $exception)
    {
        foreach ( $exception as $v ){
            $totalInfo['status_'.$v['status']] = $v['num'];
        }

        return $totalInfo;
    }

    private function getSomeInfo($statisData)
    {
        foreach ( $statisData as $k=>$v ){
            $statisData[$k]['time_section'] = date('m-d H:i', $v['start_time']).' - '.date('m-d H:i', $v['end_time']);
            $statisData[$k]['ratio'] = $v['ratio'].'%';
        }

        return $statisData;
    }

    private function mergeStatisDataException($statisData, $exceptionData)
    {
        $statisData = array_column($statisData, null, 'id');
        $exceptionWithPsid = [];
        foreach ( $exceptionData as $v ){
            $exceptionWithPsid[$v['channel_statis_id']][] = $v;
        }

        foreach ( $statisData as $id=> $v ){
            if( !isset($exceptionWithPsid[$id]) ){
                continue;
            }

            foreach ( $exceptionWithPsid[$id] as $vv ){
                $statisData[$id]['status_'.$vv['status']] = $vv['num'];
            }
        }

        return $statisData;
    }

    private function getExceptionColumn($exception = [])
    {
        $result = [];
        foreach ( $exception as $v ){
            $result[] = 'status_'.$v['status'];
        }

        return $result;
    }

    private function getHead($exceptionColumn)
    {
        $head = [
            'time_section'=>'时间段',
            'all'=>'总量',
            'success'=>'成功量',
            'ratio'=>'成功率',
            'run_time'=>'平均响应时间',
        ];

        foreach( $exceptionColumn as $v ){
            $statusInfo = explode('_', $v);
            $status = $statusInfo[1];
            $info = ChannelStatus::getInfoByStatus($status);

            $head[$v] = isset($info['info']) ? $status.'('.$info['info'].')' : $status;
        }

        return $head;
    }

    private function formatStatisRow($statisInfo, $exceptionColumn)
    {
        $result = [];
        foreach ( $statisInfo as $v ){
            $result[] = $this->formatRow($v, $exceptionColumn);
        }

        return $result;
    }

    private function formatRow($info, $head)
    {
        $result = [];

        $head = array_keys($head);

        foreach ( $head as $key ){
            if( isset($info[$key]) ){
                $result[$key] = $info[$key];
            }else{
                $result[$key] = '--';
            }
        }

        return $result;
    }
}