<?php

namespace App\Http\Controllers\Monitor;


use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Http\Repository\Monitor\AntFinancialMonitorRepository;
use App\Models\Account;
use App\Models\Monitor\BxfMysql\BxfAntCode;
use Illuminate\Support\Facades\Redis;

//class AntFinancialMonitorController extends Controller
class AntFinancialMonitorController extends CommonController
{
	private $repository;
	
	public function __construct()
	{
	    parent::__construct();
		$this->repository = new AntFinancialMonitorRepository();
	}
	
	public function detail()
	{
		$res = $this->repository->getInfoForDetail();
		if (!is_array($res)) {
			$this->createSimpleResponse('ant_financial.' . $res);
		}
		
		$this->createDataResponse($res);
	}
	
	/**
	 * 获取每一个产品的数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 21:24
	 *
	 * @param $product_id integer 产品ID
	 * @param $redis      \Redis
	 *
	 * @return array
	 */
	protected function getItem($product_id, $redis)
	{
		$apikey = config('params.mayi_apikey');
		$key    = $apikey . '_' . $product_id;
		
		//总调用量
		$total = $redis->hGet('MonitorCache', $key . '_all') ?: 0;
		
		//有效调用量
		$success = $redis->hGet('MonitorCache', $key . '_success') ?: 0;
		
		//成功调用率
		$rate = $total ? bcmul(bcdiv($success, $total, 6), 100, 2) . '%' : '--';
		
		//去重总调用量
		$unique_total = $redis->hGet('MonitorCache', $key . '_unique_total') ?: 0;
		
		//去重成功调用量
		$unique_success = $redis->hGet('MonitorCache', $key . '_unique_success') ?: 0;
		
		$unique_rate = $unique_total ? bcmul(bcdiv($unique_success, $unique_total, 6), 100, 2) . '%' : '--';
		
		//数据更新时间
		$update_datetime = $redis->hGet('MonitorCache', $key . '_update_at') ?: '--';
		
		//值分布、去重后的总量、去重后的平均响应时间
		$result = $redis->hGet('MonitorCache', $key);
		if (empty($result)) {
			$value_spread = [];
			
			//去重后的平均响应时间
			$unique_run_time = '--';
		} else {
			$result       = json_decode($result, true);
			$value_spread = $result['items'];
			
			//去重后的平均响应时间
			$unique_run_time = $result['run_time'];
			
			ksort($value_spread);
			$value_spread = array_map(function ($count) use ($unique_total) {
				$rate = $unique_total ? bcmul(bcdiv($count, $unique_total, 6), 100, 2) . '%' : '--';
				
				return compact('count', 'rate');
			}, $value_spread);
			
		}
		
		return compact('total', 'unique_total', 'success', 'unique_success', 'rate', 'unique_rate', 'unique_run_time', 'value_spread', 'update_datetime');
	}
	
	public function setTtl()
	{
		$key    = request()->post('key');
		$o_type = request()->post('otype');
		
		$time = 0;
		switch ($o_type) {
			case 1:
				$time = 600;
				break;
			case 2:
				$time = 3600;
				break;
			case 3:
				$time = 43200;
				break;
			case 4:
				$time = 86400;
				break;
		}
		
		if ($time && $key) {
			
			$redis = Redis::connection('monitor_queue');
			$redis->setex($key, $time, 1);
		}
		
		return $this->createBaseResponse();
	}


    public function getSwitchNetWorkList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $product_id = request()->post('product_id', '');
        $code = request()->post('code', '');
        $apikey = request()->post('apikey', '');
        $status = request()->post('status', '');
        $offset = ($page - 1) * $limit;
        $where = [];
        $account_list = Account::select(['apikey', 'account_name'])->get()->toArray();
        $account_list = array_column($account_list, 'account_name', 'apikey');

        $apikeys = '';
        if(!empty($apikey)){
            $accounts = Account::where('apikey', 'like', '%'.$apikey.'%')
                ->orWhere('account_name', 'like', '%'.$apikey.'%')
                ->select(['apikey', 'account_name'])->get()->toArray();
            $apikeys = array_column($accounts,'apikey');
        }

        if(!empty($code)){
            $where[] = ['code',  'like',  '%'.$code.'%'];
        }

        if(!empty($product_id)){
            $where[] = ['pids',  '=',  $product_id];
        }

        if($status != ''){
            $where[] = ['status',  '=',  $status];
        }

        $fields = ['*'];
        $res = BxfAntCode::getList($where, $fields, $offset, $limit, $apikeys);
        foreach ($res['data'] as &$val){
            $val['account_name'] = $account_list[$val['apikey']] ?? $val['apikey'];

            //$val['created_at'] = $val['created_at'] ? date('Y-m-d H:i:s', $val['created_at']) : '';
            //$val['updated_at'] = $val['updated_at'] ? date('Y-m-d H:i:s', $val['updated_at']) : '';
        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);
    }

    public function setSwitchNetWork(){
        $id = request()->get('id');
        $status = request()->get('status');

        try{
            $info = BxfAntCode::where(['id' => $id])->first();

            if(empty($info)){
                throw new \Exception('id不存在');
            }else{

                $set['updated_at'] = time();
                $set['status'] = intval($status);
                $up = BxfAntCode::where(['id' => $id])->update($set);
            }

            if(isset($up) && $up){
                $this->createBaseResponse("设置成功", 0, $info);
            }
            $this->createBaseResponse("设置失败", 0, $info);

        }catch (Exception $e){
            $this->createBaseResponse("获取数据失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据失败", 5001);

    }

}