<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Models\Monitor\LogProduct;
use App\Models\Monitor\ProductStatisExceptionPeriod;
use App\Models\Monitor\ProductStatisPeriod;
use App\Models\Monitor\ProductStatus;
use App\Models\Monitor\ProductValueSpreadDetailPeriod;
use App\Models\Monitor\ProductValueSpreadPeriod;
use App\Providers\Monitor\ProductStatusService;
use App\Providers\Monitor\ValueSpreadGoalValueService;
use App\Providers\RedisCache\RedisCache;

//class ProductHistoryStatisController extends Controller
class ProductHistoryStatisController extends CommonController
{
    private $valueSpreadService;

    public function __construct()
    {
        parent::__construct();

        $this->valueSpreadService = new ValueSpreadGoalValueService();
    }

    public function getHeadColumn()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
        $productId = request()->post('product_id');
        $channelId = request()->post('channel_id', 0);
        $timeSection = request()->post('time_section');
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }
        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            ['start_time', '>=', $startTime],
            ['end_time', '<=', $endTime],
            ['product_id', '=', $productId],
            ['channel_id', '=', $channelId],
            ['apikey', '=', $apikey],
            ['status', '!=', 0],
        ];

        //获取异常合计信息
        $where[] = ['status','!=',0];
        $totalException = ProductStatisPeriod::getExceptionTotalCountByCondition($where);

        $exceptionColumn = $this->getExceptionColumn($totalException);
        $head = $this->getHead($exceptionColumn, $productId);

        $this->createDataResponse($head);
    }

    public function getSuccessInfo()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
        $productId = request()->post('product_id');
        $channelId = request()->post('channel_id', 0);
        $timeSection = request()->post('time_section');
        $page = request()->post('page', 1);
        $limit = request()->post('limit', 10);
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }
        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            ['start_time', '>=', $startTime],
            ['end_time', '<=', $endTime],
            ['product_id', '=', $productId],
            ['channel_id', '=', $channelId],
            ['apikey', '=', $apikey],
        ];

        $statisData = ProductStatisPeriod::getList($where, $page, $limit);
        $ids = array_column($statisData, 'id');
        //获取异常数据
        $exceptionData = ProductStatisExceptionPeriod::getListByPsId($ids);
        $statisData = $this->mergeStatisDataException($statisData, $exceptionData);
        //获取时间段
        $statisData = $this->getSomeInfo($statisData);
        //获取总数
        $countInfo = ProductStatisPeriod::getCount($where);
        $result['count'] = isset($countInfo['count']) ? $countInfo['count'] : 0;

        //获取合计信息
        $totalData = ProductStatisPeriod::getTotalCountByCondition($where);
        $totalData = $this->ratioAndTimeToTotalInfo($totalData);
        //获取异常合计信息
        $where[] = ['status','!=',0];
        $totalException = ProductStatisPeriod::getExceptionTotalCountByCondition($where);
        $totalData = $this->mergeTotalException($totalData, $totalException);
        $totalData['time_section'] = '合计';

        $exceptionColumn = $this->getExceptionColumn($totalException);
        $head = $this->getHead($exceptionColumn, $productId);
        $totalData = $this->formatRow($totalData, $head);
        $statisData = $this->formatStatisRow($statisData, $head);

        $rowData[] = $totalData;
        $result['data'] = array_merge($rowData, $statisData);

        $this->createDataResponse($result);
    }

    public function valueSpreadHeadColumn()
    {
        $productId = request()->post('product_id');
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $result = $this->getValueSpreadHead($productId);

        $this->createDataResponse($result);
    }

    public function getValueSpread()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
        $productId = request()->post('product_id');
        $channelId = request()->post('channel_id', 1);
        $timeSection = request()->post('time_section');
        $page = request()->post('page', 1);
        $limit = request()->post('limit', 10);
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            ['start_time', '>=', $startTime],
            ['end_time', '<=', $endTime],
            ['product_id', '=', $productId],
            ['channel_id', '=', $channelId],
            ['apikey', '=', $apikey],
        ];

        $statisData = ProductValueSpreadPeriod::getList($where, $page, $limit);
        $ids = array_column($statisData, 'id');
        $detailData = ProductValueSpreadDetailPeriod::getListByPsId($ids);
        $statisData = $this->mergeValue($statisData, $detailData);
        //获取时间段
        $statisData = $this->getSomeInfo($statisData);
        //获取总数
        $countInfo = ProductValueSpreadPeriod::getCount($where);
        $result['count'] = isset($countInfo['count']) ? $countInfo['count'] : 0;

        //获取合计信息
        $totalData = ProductValueSpreadPeriod::getValueTotalCountByCondition($where);
        $totalData = $this->getTotalValueWithValue($totalData);
        $totalData['time_section'] = '合计';

        $head = $this->getValueSpreadHead($productId);

        $totalData = $this->getValueSpreadRowItem($totalData, $head);
        $statisData = $this->getValueSpreadRows($statisData, $head);

        $rowData[] = $totalData;
        $result['data'] = array_merge($rowData, $statisData);

        $this->createDataResponse($result);
    }

    private function ratioAndTimeToTotalInfo($totalInfo)
    {
        if( $totalInfo ){
            $totalInfo['ratio'] = divGetRatio($totalInfo['success'], $totalInfo['all'], 4);
            $totalInfo['run_time'] = intval($totalInfo['run_time']);
            return $totalInfo;
        }else{
            return [];
        }
    }

    private function getTotalValueWithValue($data)
    {
        $result = [];
        $counts = array_column($data, 'num');
        $total = array_sum($counts);
        foreach ( $data as $v ){
            $ratio = divGetRatio($v['num'], $total, 4);
            $result['value_'.$v['value']] = $ratio.'（'.$v['num'].'）';
        }

        return $result;
    }

    private function mergeTotalException($totalInfo, $exception)
    {
        foreach ( $exception as $v ){
            $totalInfo['status_'.$v['status']] = $v['num'];
        }

        return $totalInfo;
    }

    private function mergeValue($data, $valueData)
    {
        $data = array_column($data, null, 'id');
        $valuePvid = [];
        foreach ( $valueData as $v ){
            $valuePvid[$v['product_value_spread_id']][] = $v;
        }

        foreach ( $data as $id=> $v ){
            if( isset($valuePvid[$id]) ){
                foreach ( $valuePvid[$id] as $vv ){
                    $data[$id]['value_'.$vv['value']] = $vv['ratio'].'%'.'（'.$vv['num'].'）';
                }
            }
        }

        return $data;
    }

    private function getSomeInfo($statisData)
    {
        foreach ( $statisData as $k=>$v ){
            $statisData[$k]['time_section'] = date('m-d H:i', $v['start_time']).' - '.date('m-d H:i', $v['end_time']);
            if( isset($v['ratio']) ){
                $statisData[$k]['ratio'] = $v['ratio'].'%';
            }
        }

        return $statisData;
    }

    private function mergeStatisDataException($statisData, $exceptionData)
    {
        $statisData = array_column($statisData, null, 'id');
        $exceptionWithPsid = [];
        foreach ( $exceptionData as $v ){
            $exceptionWithPsid[$v['product_statis_id']][] = $v;
        }

        foreach ( $statisData as $id=> $v ){
            if( !isset($exceptionWithPsid[$id]) ){
                continue;
            }

            foreach ( $exceptionWithPsid[$id] as $vv ){
                $statisData[$id]['status_'.$vv['status']] = $vv['num'];
            }
        }

        return $statisData;
    }

    private function getValueSpreadRows($valueInfos, $head)
    {
        $result = [];
        foreach ( $valueInfos as $v ){
            $result[] = $this->getValueSpreadRowItem($v, $head);
        }

        return $result;
    }

    private function getValueSpreadRowItem($valueInfo, $head)
    {
        $head = array_keys($head);

        $result = [];
        foreach ( $head as $key){
            if( isset($valueInfo[$key]) ){
                $result[$key] = $valueInfo[$key];
            }else{
                $result[$key] = "--（0）";
            }
        }

        return $result;
    }

    private function getValueSpreadHead($productId)
    {
        $values = $this->valueSpreadService->getGoalValues($productId);

        $head = [
            'time_section' => '时间段',
        ];
        foreach ( $values as $v ){
            $head['value_'.$v] = $v;
        }

        return $head;
    }

    private function getExceptionColumn($exception = [])
    {
        $result = [];
        foreach ( $exception as $v ){
            $result[] = 'status_'.$v['status'];
        }

        return $result;
    }

    private function getHead($exceptionColumn, $productId)
    {
        $head = [
            'time_section'=>'时间段',
            'all'=>'总量',
            'success'=>'成功量',
            'ratio'=>'成功率',
            'run_time'=>'平均响应时间',
        ];

        //获取fatherId
        $fatherId = RedisCache::instance('productId_fatherId_mapping')
            ->get($productId);

        foreach( $exceptionColumn as $v ){
            $statusInfo = explode('_', $v);
            $status = $statusInfo[1];
            $info = ProductStatus::getInfoByPidAndStatus($fatherId, $status);

            $head[$v] = isset($info['info']) ? $status.'('.$info['info'].')' : $status;
        }

        return $head;
    }

    private function formatStatisRow($statisInfo, $exceptionColumn)
    {
        $result = [];
        foreach ( $statisInfo as $v ){
            $result[] = $this->formatRow($v, $exceptionColumn);
        }

        return $result;
    }

    private function formatRow($info, $head)
    {
        $result = [];

        $head = array_keys($head);

        foreach ( $head as $key ){
            if( isset($info[$key]) ){
                $result[$key] = $info[$key];
            }else{
                $result[$key] = '--';
            }
        }

        return $result;
    }
}