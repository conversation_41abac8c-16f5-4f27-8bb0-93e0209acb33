<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Http\Repository\Monitor\ProductStatisRepository;
use App\Models\ClickHouse\RequestProductLog;
use App\Models\Monitor\LogProduct;
use App\Models\Monitor\ProductStatis;
use App\Models\Monitor\ProductStatisPeriod;
use App\Models\Monitor\ProductStatus;
use App\Models\Monitor\ProductValueSpreadPeriod;
use App\Providers\Monitor\ProductStatusService;
use App\Providers\Monitor\ValueSpreadGoalValueService;
use App\Providers\RedisCache\RedisCache;

//class ProductStatisController extends Controller
class ProductStatisController extends CommonController
{
    private $valueSpreadService;
    private $productStatisRepository;

    public function __construct()
    {
        parent::__construct();

        $this->valueSpreadService = new ValueSpreadGoalValueService();
        $this->productStatisRepository = new ProductStatisRepository();
    }

    //获取 统计过滤的信息，比如客户、产品
    public function getStatisFilter()
    {
        $fatherId = request()->post('father_id', 0);
        if( !$fatherId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $products = $this->productStatisRepository->getProductIdByFatherId($fatherId);
        $clients = $this->productStatisRepository->getProductAndApikeyByFatherId($fatherId);

        $result = [
            'client' => $clients,
            'products' => $products,
        ];

        $this->createDataResponse($result);
    }

    //通用产品 调用统计
    public function getStatisInfo()
    {
        $apikey = request()->post('apikey', "");
        $productId = request()->post('product_id', 0);
        $stime = request()->post('stime', date('Y-m-d H:i:s', time()));
        $etime = request()->post('etime', date('Y-m-d H:i:s', time()));
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $stime = strtotime($stime);
        $etime = strtotime($etime);
        $where = [
            ['start_time', '>=', $stime],
            ['end_time', '<=', $etime],
            ['product_id', '=', $productId],
        ];
        if( $apikey ){
            $where[] = ['apikey', '=', $apikey];
        }

        //获取合计信息
        $totalData = $this->productStatisRepository->getTotalInfo($where);
        //获取各个渠道信息
        $channelData = $this->productStatisRepository->getChannelInfo($where);

        //获取表格头部
        $header = $this->productStatisRepository->getStatisHeader();

        $result['header'] = $header;
        $result['items'][] = $totalData;
        foreach ( $channelData as $v ){
            $result['items'][] = $v;
        }

        $this->createDataResponse($result);
    }

    //通用，值分布统计
    public function getValueSpreadInfo()
    {
        $apikey = request()->post('apikey', "");
        $productId = request()->post('product_id', 0);
        $stime = request()->post('stime', date('Y-m-d H:i:s', time()));
        $etime = request()->post('etime', date('Y-m-d H:i:s', time()));
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $stime = strtotime($stime);
        $etime = strtotime($etime);

        //获取合计
        $where = [
            ['start_time', '>=', $stime],
            ['end_time', '<=', $etime],
            ['product_id', '=', $productId],
        ];
        if( $apikey ){
            $where[] = ['apikey', '=', $apikey];
        }

        $totalData = $this->productStatisRepository->getTotalValueSpreadInfo($where);
        $channelData = $this->productStatisRepository->getChannelValueSpreadInfo($where);

        //获取表格头部
        $header = $this->productStatisRepository->getValueSpreadHeader($productId);

        $result['header'] = $header;
        $result['items'][] = $totalData;
        foreach ( $channelData as $v ){
            $result['items'][] = $v;
        }

        $this->createDataResponse($result);
    }

    //通过 source 分组的值分布
    public function getSourceValueSpreadInfo()
    {
        $apikey = request()->post('apikey', "");
        $productId = request()->post('product_id', 0);
        $stime = request()->post('stime', date('Y-m-d H:i:s', time()));
        $etime = request()->post('etime', date('Y-m-d H:i:s', time()));
        if( !$productId || !$apikey ){
            $this->createSimpleResponse('monitor.10001');
        }

        $stime = strtotime($stime);
        $etime = strtotime($etime);

        //获取合计
        $where = [
            ['start_time', '>=', $stime],
            ['end_time', '<=', $etime],
            ['product_id', '=', $productId],
            ['apikey', '=', $apikey],
        ];
        $totalData = $this->productStatisRepository->getTotalValueSpreadInfo($where);
        $sourceData = $this->productStatisRepository->getSourceValueSpreadInfo($where);

        //获取表格头部
        $header = $this->productStatisRepository->getSourceValueSpreadHeader($productId);

        $result['header'] = $header;
        $result['items'][] = $totalData;
        foreach ( $sourceData as $v ){
            $result['items'][] = $v;
        }

        $this->createDataResponse($result);
    }

    //专门给蚂蚁的 新版
    public function getSuccessInfoV2()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
        $productId = request()->post('product_id');
        $timeSection = request()->post('time_section');
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'product_id' => $productId,
            'apikey' => $apikey,
        ];

        $rpModel = new RequestProductLog();
        $totalData = $rpModel->getTotalCountByCondition($where);
        $totalData = $this->getRowData($totalData);

        //获取渠道的统计信息
        $channelData = $rpModel->getChannelCountByCondition($where);
        $channelData = $this->getChannelInfoWithCid($channelData);
        $channelData = $this->getChannelRow($channelData);

        //获取 异常列
        $exceptionColumn = $this->getExceptionColumn($totalData['exception']);

        //通过 异常列，格式化数据
        //获取 表头
        $result['head'] = $this->getHead($exceptionColumn, $productId);
        $totalData = $this->formatRow($totalData, $exceptionColumn);
        $channelsData = $this->formatChannelRow($channelData,$exceptionColumn);
        $rowData[] = $totalData;
        $result['data'] = array_merge($rowData, $channelsData);

        $this->createDataResponse($result);
    }


    //专门给蚂蚁的
    public function getSuccessInfo()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
        $productId = request()->post('product_id');
        $timeSection = request()->post('time_section');
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);
        $where = [
            ['time', '>=', $startTime],
            ['time', '<=', $endTime],
            ['product_id', '=', $productId],
            ['apikey', '=', $apikey],
        ];

        //获取合计统计信息
        $totalData = LogProduct::getTotalCountByCondition($where);
        $totalData = $this->getRowData($totalData);

        //获取渠道的统计信息
        $channelData = LogProduct::getChannelCountByCondition($where);
        $channelData = $this->getChannelInfoWithCid($channelData);
        $channelData = $this->getChannelRow($channelData);

        //获取 异常列
        $exceptionColumn = $this->getExceptionColumn($totalData['exception']);

        //通过 异常列，格式化数据
        //获取 表头
        $result['head'] = $this->getHead($exceptionColumn, $productId);
        $totalData = $this->formatRow($totalData, $exceptionColumn);
        $channelsData = $this->formatChannelRow($channelData,$exceptionColumn);
        $rowData[] = $totalData;
        $result['data'] = array_merge($rowData, $channelsData);

        $this->createDataResponse($result);
    }

    //专门给蚂蚁的 新版
    public function getValueSpreadV2()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
        $productId = request()->post('product_id');
        $timeSection = request()->post('time_section');
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'product_id' => $productId,
            'apikey' => $apikey,
            'status' => 0,
        ];

        $rpModel = new RequestProductLog();
        $valueSpread = $rpModel->getValueSpeadByCondition($where);
        $valueSpread = $this->getValueSpreadWithCid($valueSpread);
        $valueSpread = $this->formatValueSpread($valueSpread, $productId);
        $valueSpread = $this->getValueSpreadRatio($valueSpread);
        $valueSpread = $this->getChannelNameAndId($valueSpread);
        $result['head'] = $this->getValueSpreadHead($productId);
        $result['data'] = $this->getValueSpreadRows($valueSpread, $result['head']);

        $this->createDataResponse($result);
    }

    //专门给蚂蚁的
    public function getValueSpread()
    {
        $apikey = request()->post('apikey', config('params.mayi_apikey'));
        $productId = request()->post('product_id');
        $timeSection = request()->post('time_section');
        if( !$productId ){
            $this->createSimpleResponse('monitor.10001');
        }

        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);
        $where = [
            ['time', '>=', $startTime],
            ['time', '<=', $endTime],
            ['product_id', '=', $productId],
            ['apikey', '=', $apikey],
            ['status', '=', 0],
        ];

        $valueSpread = LogProduct::getValueSpeadByCondition($where);
        $valueSpread = $this->getValueSpreadWithCid($valueSpread);
        $valueSpread = $this->formatValueSpread($valueSpread, $productId);
        $valueSpread = $this->getValueSpreadRatio($valueSpread);
        $valueSpread = $this->getChannelNameAndId($valueSpread);
        $result['head'] = $this->getValueSpreadHead($productId);
        $result['data'] = $this->getValueSpreadRows($valueSpread, $result['head']);

        $this->createDataResponse($result);
    }

    private function getValueSpreadRows($valueSpreadWithRatio, $head)
    {
        $result = [];
        foreach ( $valueSpreadWithRatio as $channelId => $v ){
            $result[] = $this->getValueSpreadRowItem($v, $head);
        }

        return $result;
    }

    private function getValueSpreadRowItem($valueSpreadWithRatio, $head)
    {
        $result = [];
        $head['channel_id'] = 0;
        foreach ( $head as $key => $v ){
            if( isset($valueSpreadWithRatio[$key]) ){
                $result[$key] = $valueSpreadWithRatio[$key];
            }else{
                $result[$key] = '--（0）';
            }
        }

        return $result;
    }

    private function getValueSpreadHead($productId)
    {
        $values = $this->valueSpreadService->getGoalValues($productId);

        $head = [
            'channel_name'=>'渠道',
        ];

        foreach ( $values as $v ){
            $head['value_'.$v] = $v;
        }

        return $head;
    }

    private function getChannelNameAndId($valueInfo)
    {
        foreach ( $valueInfo as $channelId => $v ){
            $valueInfo[$channelId]['channel_id'] = $channelId;
            $valueInfo[$channelId]['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($channelId);
        }

        return $valueInfo;
    }

    private function getValueSpreadRatio($valueInfo)
    {
        foreach ( $valueInfo as $channelId => $v ){
            $total = $this->getValueSpreadTotal($v);

            foreach ( $v as $value => $vv ){
                $ratio = divGetRatio($vv['count'], $total, 4);
                $valueInfo[$channelId][$value] = $ratio.'（'.$vv['count'].'）';
            }
        }

        return $valueInfo;
    }

    private function formatValueSpread($valueInfo, $productId)
    {
        $result = [];
        foreach ( $valueInfo as $channelId => $v ){

            $item = [];
            foreach ( $v as $vv ){
                $valueKey = $this->valueSpreadService->getGoalValue($vv['value'], $productId);
                $valueKey = 'value_'.$valueKey;
                if( isset($item[$valueKey]) ){
                    $item[$valueKey]['count'] += $vv['count'];
                }else{
                    $item[$valueKey]['count'] = $vv['count'];
                }
            }

            $result[$channelId] = $item;
        }

        return $result;
    }

    private function getValueSpreadWithCid($valueInfo)
    {
        $result = [];
        foreach ( $valueInfo as $v ){
            $result[$v['channel_id']][] = [
                'value' => $v['value'],
                'count' => $v['count'],
            ];
        }

        return $result;
    }

    private function getValueSpreadTotal($valueInfo)
    {
        if( $valueInfo ){
            $counts = array_column($valueInfo, 'count');
            $total = array_sum($counts);
            return $total;
        }else{
            return 0;
        }
    }

    private function getRowData($info, $channelId = 0)
    {
        $all = 0;
        $success = 0;
        $runTime = 0;
        $exception = [];

        foreach ( $info as $v ){
            $all += $v['count'];
            $runTime += $v['run_time'];

            if( $v['status'] == 0 ){
                $success = $v['count'];
            }else{
                $exception['status_'.$v['status']] = $v['count'];
            }
        }

        $result['channel_id'] = $channelId;
        $result['all'] = $all;
        $result['success'] = $success;
        $result['ratio'] = divGetRatio($success, $all, 4);
        $result['run_time'] = strval(div($runTime, $all, 0));
        $result['exception'] = $exception;

        return $result;
    }

    private function getChannelInfoWithCid($channelInfo)
    {
        $result = [];
        foreach ( $channelInfo as $v ){
            $result[$v['channel_id']][] = [
                'status' => $v['status'],
                'count' => $v['count'],
                'run_time' => $v['run_time'],
            ];
        }

        return $result;
    }

    private function getChannelRow($channelInfo)
    {
        $result = [];

        foreach ( $channelInfo as $channelId => $v ){
            $result[] = $this->getRowData($v, $channelId);
        }

        return $result;
    }

    private function getExceptionColumn($exception = [])
    {
        $exception = array_keys($exception);

        return $exception;
    }

    private function getHead($exceptionColumn, $productId)
    {
        $head = [
            'channel_name'=>'渠道',
            'all'=>'总量',
            'success'=>'成功量',
            'ratio'=>'成功率',
            'run_time'=>'平均响应时间',
        ];

        //获取fatherId
        $fatherId = RedisCache::instance('productId_fatherId_mapping')
            ->get($productId);

        foreach( $exceptionColumn as $v ){
            $statusInfo = explode('_', $v);
            $status = $statusInfo[1];
            $info = ProductStatus::getInfoByPidAndStatus($fatherId, $status);

            $head[$v] = isset($info['info']) ? $status.'('.$info['info'].')' : $status;
        }

        return $head;
    }

    private function formatChannelRow($channelInfo, $exceptionColumn)
    {
        $result = [];
        foreach ( $channelInfo as $v ){
            $result[] = $this->formatRow($v, $exceptionColumn);
        }

        return $result;
    }

    private function formatRow($info, $exceptionColumn)
    {
        $result = [
            'channel_id' => $info['channel_id'],
            'all' => $info['all'],
            'success' => $info['success'],
            'ratio' => $info['ratio'],
            'run_time' => $info['run_time'],
        ];

        if( $info['channel_id'] == 0 ){
            $result['channel_name'] = '合计';
        }else{
            $result['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($info['channel_id']);
        }

        foreach ( $exceptionColumn as $status ){
            if( isset($info['exception'][$status]) ){
                $result[$status] = $info['exception'][$status];
            }else{
                $result[$status] = 0;
            }
        }

        return $result;
    }
}