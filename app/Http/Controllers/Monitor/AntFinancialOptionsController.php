<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Models\Channel;
use App\Models\Monitor\ConfigAntFinancialBatch;

//蚂蚁监控专用的一些options服务
//class AntFinancialOptionsController extends Controller
class AntFinancialOptionsController extends CommonController
{
	private $storeProducts = [
		280 => '【280】近30天催收号码数',
		281 => '【281】近30天被叫催收次数',
	];
	
	//获取批次选择框内容
	public function getBatchOption()
	{
		//按ID倒序
		$data = ConfigAntFinancialBatch::orderBy('id', 'desc')
									   ->pluck('name', 'id')
									   ->toArray();
		$data = createOption($data, request()->get('default', ''));
		$this->createDataResponse($data);
	}
	
	//获取产品选择框内容
	public function getProductOption()
	{
		$data = createOption($this->storeProducts, request()->get('default'));
		$this->createDataResponse($data);
	}
	
	//获取产品选择项数据
	public function getProductInfo()
	{
		$this->createDataResponse($this->storeProducts);
	}
	
	//获取渠道选择框内容
	public function getChannelOption()
	{
		$data = Channel::select(['channel.channel_id', 'channel.label'])
					   ->leftJoin('channel_product', 'channel_product.channel_id', '=', 'channel.channel_id')
					   ->where('channel_product.product_id', 210)
					   ->get()
					   ->toArray();
		$data = createOption(array_column($data, 'label', 'channel_id'), request()->get('default', ''));
		$this->createDataResponse($data);
	}
}