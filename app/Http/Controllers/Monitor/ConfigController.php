<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Models\Monitor\ConfigProductValueSpread;

//class ConfigController extends Controller
class ConfigController extends CommonController
{
    public function getProductValueSpread()
    {
        $result = ConfigProductValueSpread::getConfig();
        $this->createDataResponse($result);
    }
}