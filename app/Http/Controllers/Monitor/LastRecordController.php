<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Models\Account;
use App\Models\AccountProductModel;
use App\Models\Channel;
use App\Models\Monitor\LogChannel;
use App\Models\Monitor\LogProduct;
use App\Models\Product;
use App\Models\ClickHouse\RequestProductLog;
use App\Providers\RedisCache\RedisCache;

//class LastRecordController extends Controller
class LastRecordController extends CommonController
{

    public function __construct()
    {
        parent::__construct();
    }

    public function getProduct()
    {
        $product = [
            [
                'product_id' => '200',
                'product_name' => '邦秒验',
            ],
            [
                'product_id' => '210',
                'product_name' => '邦信分-通信字段',
            ],
            [
                'product_id' => '1000',
                'product_name' => '邦信分-通信评分',
            ],
            [
                'product_id' => '615',
                'product_name' => '号码风险等级',
            ],
            [
                'product_id' => '10000',
                'product_name' => '号码分',
            ],
        ];

        $this->createDataResponse($product);
    }

    public function getApikeyByProductId()
    {
        $productId = request()->post('product_id', 0);

        $productIds = $this->getSubProductId($productId);
        $accountProductModel = new AccountProductModel([]);
        $apikeyInfoResult = $accountProductModel->getList(['product_id'=>$productIds]);

        $haveApikeys = [];
        $result = [];
        foreach ( $apikeyInfoResult as &$v){

            if( in_array($v['apikey'], $haveApikeys) ){
                continue;
            }else{
                $item = [
                    'name' => $v['account_name'],
                    'apikey' => $v['apikey'],
                ];

                $result[] = $item;
                $haveApikeys[] = $v['apikey'];
            }
        }

        $this->createDataResponse($result);
    }

    public function getProductRecord()
    {
        $productStatus = config("monitor.product_status");

        $productId = request()->input('product_id', 0);
        $apikey = request()->input('apikey', "");
        $channel_id = request()->input('channel_id', "");
        $date_start= request()->input('date_start', 0);
        $date_end = request()->input('date_end', "");
        $page = request()->get('page', 0);
        $limit = request()->get('limit', 20);
        $start = ($page - 1) * $limit;

        if (!empty($date_start)){
            $date_start = strtotime($date_start);
        }
        if (!empty($date_end)){
            $date_end = strtotime($date_end);
        }
        $subProductIds = $this->getSubProductId($productId);
        $rpModel = new RequestProductLog();
        $productRecord = $rpModel->getRecordByProductIdAndApikey($subProductIds, $apikey, $channel_id, $date_start, $date_end, $start,$limit);
        //$productRecord = LogProduct::getRecordByProductIdAndApikey($subProductIds, $apikey, $date_start, $date_end, $start,$limit);
        if(isset($productRecord['data']) && isset($productRecord['count'])){
            $count = $productRecord['count'];
            $productRecord = $productRecord['data'];
        }
        foreach ( $productRecord as &$v ){
            $accountInfo = Account::getAccountByApikey($v['apikey']);
            $v['account_name'] = isset($accountInfo['account_name']) ? $accountInfo['account_name'] : $v['apikey'];
            $productInfo = Product::getProductInfoByProductId($v['product_id']);
            $v['time'] = date('Y-m-d H:i:s', $v['request_at']);
            $v['status_text'] = isset($productStatus[$v['status']]) ? $productStatus[$v['status']] : $v['status'];
            $v['product_name'] = isset($productInfo['product_name']) ? $productInfo['product_name'] : $v['product_id'];
            $v['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($v['channel_id']);

        }

        if($date_start && $date_end){//为了兼容之前的地方调用该接口可能会报错所以加的判断
            $this->CreateLayuiResponse($productRecord, $count);
        }else{
            $this->createDataResponse($productRecord);

        }

    }

    public function getProductRecordbak()
    {
        $productStatus = config("monitor.product_status");

        $productId = request()->input('product_id', 0);
        $apikey = request()->input('apikey', "");
        $date_start= request()->input('date_start', 0);
        $date_end = request()->input('date_end', "");
        $page = request()->get('page', 0);
        $limit = request()->get('limit', 20);
        $start = ($page - 1) * $limit;

        if (!empty($date_start)){
            $date_start = strtotime($date_start);
        }
        if (!empty($date_end)){
            $date_end = strtotime($date_end);
        }
        $subProductIds = $this->getSubProductId($productId);
        $productRecord = LogProduct::getRecordByProductIdAndApikey($subProductIds, $apikey, $date_start, $date_end, $start,$limit);
        if(isset($productRecord['data']) && isset($productRecord['count'])){
            $count = $productRecord['count'];
            $productRecord = $productRecord['data'];
        }
        foreach ( $productRecord as &$v ){
            $accountInfo = Account::getAccountByApikey($v['apikey']);
            $v['account_name'] = isset($accountInfo['account_name']) ? $accountInfo['account_name'] : $v['apikey'];
            $productInfo = Product::getProductInfoByProductId($v['product_id']);
            $v['time'] = date('Y-m-d H:i:s', $v['time']);
            $v['status_text'] = isset($productStatus[$v['status']]) ? $productStatus[$v['status']] : $v['status'];
            $v['product_name'] = isset($productInfo['product_name']) ? $productInfo['product_name'] : $v['product_id'];

        }

        if($date_start && $date_end){//为了兼容之前的地方调用该接口可能会报错所以加的判断
            $this->CreateLayuiResponse($productRecord, $count);
        }else{
            $this->createDataResponse($productRecord);

        }

    }


    private function getSubProductId($fatherId)
    {
        if( $fatherId == 0 ){
            return [];
        }

        $where = [
            ['father_id','=', $fatherId],
        ];
        $productList = Product::getListByCondition($where, ['product_id','product_name'])->toArray();
        $productIds = [];
        foreach ( $productList as $v ){
            $productIds[] = $v['product_id'];
        }

        return $productIds;
    }

    public function getChannel()
    {
        $channels = Channel::getAllChannel();
        $this->createDataResponse($channels);
    }

    public function getRecord()
    {
        $channelStatus = config("monitor.channel_status");

        $channelId = request()->post('channel_id', 0);
        $channeRecord = LogChannel::getRecordByChannelId($channelId);
        foreach ( $channeRecord as &$v ){
            $channelInfo = Channel::getChannelById($v['channel_id']);
            $productInfo = Product::getProductInfoByProductId($v['custom_key']);
            $v['channel_name'] = isset($channelInfo['label']) ? $channelInfo['label'] : "";
            $v['time'] = date('Y-m-d H:i:s', $v['time']);
            $v['status_text'] = isset($channelStatus[$v['status']]) ? $channelStatus[$v['status']] : $v['status'];
            $v['value'] = $v['status'] == 0 ? $v['value'] : "";
            $v['interface'] = isset($productInfo['product_name']) ? $productInfo['product_name'] : $v['custom_key'];
        }

        $this->createDataResponse($channeRecord);
    }


    public function getAllRecord()
    {
        $channelStatus = config("monitor.channel_status");
        $where = [];
        $channelId = request()->get('channel_id', 0);
        $date_start = request()->get('date_start', 0);
        $date_end = request()->get('date_end', 0);
        $page = request()->get('page', 0);
        $limit = request()->get('limit', 20);
        $start = ($page - 1) * $limit;
        if (!empty($channelId)){
            $where[] = ['channel_id', '=', $channelId];
        }
        if (!empty($date_start)){
            $date_start = strtotime($date_start);
            $where[] = ['time', '>', $date_start];
        }
        if (!empty($date_end)){
            $date_end = strtotime($date_end);
            $where[] = ['time', '<', $date_end];
        }

        $channeRecord = LogChannel::getRecordByCondition($where,$start,$limit);

        foreach ( $channeRecord['data'] as &$v ){

            $channelInfo = Channel::getChannelById($v['channel_id']);
            $productInfo = Product::getProductInfoByProductId($v['custom_key']);
            $v['channel_name'] = isset($channelInfo['label']) ? $channelInfo['label'] : "";
            $v['time'] = date('Y-m-d H:i:s', $v['time']);
            $v['status_text'] = isset($channelStatus[$v['status']]) ? $channelStatus[$v['status']] : $v['status'];
            $v['value'] = $v['status'] == 0 ? $v['value'] : "";
            $v['interface'] = isset($productInfo['product_name']) ? $productInfo['product_name'] : $v['custom_key'];
        }

        $this->CreateLayuiResponse($channeRecord['data'],$channeRecord['count']);
    }

    public function getAllChannellabel()
    {
        $channels = Channel::getAllChannellabel();
        $this->createDataResponse($channels);
    }

    public function getChannelUseing()
    {
        $channels = Channel::getChannelUsing();
        $this->createDataResponse($channels);
    }

}
