<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Models\ClickHouse\RequestChannelLog;
use App\Models\Monitor\AlarmLog;
use App\Models\Monitor\ChannelStatus;
use App\Models\Monitor\ChannelExceptionRecord;
use App\Models\Monitor\ConfigMonitorItems;
use App\Models\Monitor\LogChannel;
use App\Models\Channel;
use App\Models\Monitor\Manager;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Support\Facades\Redis;

//class ChannelStatisController extends Controller
class ManageController extends CommonController
{
    public $otype = [
        "0" => [
            'field' => '',
            'ttl' => 0,
        ],
        "1" => [
            'field' => '暂停报警 10分钟',
            'ttl' => 10 * 60,
        ],
        "2" => [
            'field' => '暂停报警 1小时',
            'ttl' => 60 * 60,
        ],
        "3" => [
            'field' => '暂停报警 半天',
            'ttl' => 60 * 60 * 12,
        ],
        "4" => [
            'field' => '暂停报警 1天',
            'ttl' => 60 * 60 * 24,
        ],
        "5" => [
            'field' => '永久关闭报警',
            'ttl' => -1,
        ],
        "6" => [
            'field' => '已处理',
            'ttl' => 0,
        ],
    ];

    //报警日志列表
    public function getAlarmLogList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $start_time = request()->post('start_time', '');
        $end_time = request()->post('end_time', '');
        $level = request()->post('level', 0);
        $haveo = request()->post('haveo', '');
        $ruid = request()->post('ruid', '');
        $aid = request()->post('aid', '');
        $business_type = request()->post('business_type', '');
        $offset = ($page - 1) * $limit;
        $where = [];
        if($start_time){
            $start_time = strtotime($start_time);
            $where[] = ['ctime_', '>=', $start_time];
        }

        if($end_time){
            $end_time = strtotime($end_time);
            $where[] = ['ctime_', '<=', $end_time];
        }

        if($level){
            $where[] = ['level', '=', $level];
        }

        if($haveo !== ''){
            $where[] = ['haveo', '=', $haveo];
        }

        if($ruid){
            $member = Manager::getOne(['id' => $ruid]);
            if(isset($member['name'])){
                $where[] = ['rname', '=', $member['name']];
            }else{
                $where[] = ['ruid', '=', $ruid];
            }
        }

        if($business_type){
            $where[] = ['key', 'like', trim($business_type)];
        }

        if($aid){
            $where[] = ['id', '=', trim($aid)];
        }

        $members = Manager::getList();
        $members = array_column($members, 'name', 'id');

        $res = AlarmLog::getList($where, $offset, $limit);
        foreach ($res['data'] as &$val){
            if($val['type'] == 1){
                $val['type_name'] = '系统自身';
            }else{
                $val['type_name'] = '项目组推送';
            }

            if($val['level'] == 2){
                $val['level_name'] = '严重';
            }else if($val['level'] == 3){
                $val['level_name'] = '关注';
            }else{
                $val['level_name'] = '提醒';
            }

            if($val['haveo'] == 1){
                $val['haveo'] = '已处理';
                $val['is_haveo'] = true;
            }else{
                $val['haveo'] = '未处理';
                $val['is_haveo'] = false;
            }

            $val['oname'] = $members[$val['uid']] ?? '';

        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);
    }

    //报警日志批量处理
    public function batchDeal(){
        $ids = request()->post('ids', []);
        $sysName = $this->getSystemNameByCookie();
        $member = Manager::getOne(['cname' => $sysName]);
        if(empty($member)){
            $this->createBaseResponse("操作人信息异常", 5004);
        }

        try{
            if(!empty($ids)){
                $arr = [
                    'otype' => 6,
                    'haveo' => 1,
                    'otime' => time(),
                    'uid' => $member['id'],
                    'oname' => $member['name'],
                ];

                $up = AlarmLog::batchUpdate($ids, $arr);
                if($up){
                    $this->createBaseResponse("批量处理成功");
                }else{
                    $this->createBaseResponse("批量处理失败", 5001);
                }
            }
        }catch (Exception $e){

            $this->createBaseResponse("批量失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("批量数据不能为空", 5003);
    }

    //报警日志暂停处理
    public function deal(){
        $params = request()->post();
        if(!isset($params['id']) || intval($params['id']) == 0){
            $this->createBaseResponse("处理id为0", 5003);
        }

        if(!isset($params['note']) || empty($params['note'])){
            $this->createBaseResponse("备注不能为空", 5004);
        }

        if(!isset($params['otype']) || empty($params['otype'])){
            $this->createBaseResponse("暂停选项不能为空", 5005);
        }

        $sysName = $this->getSystemNameByCookie();
        $member = Manager::getOne(['cname' => $sysName]);
        if(empty($member)){
            $this->createBaseResponse("操作人信息异常", 5008);
        }

        #todo
        $redis = Redis::connection('monitor');
        //$redis->setex($key, $time, 1);

        try{
            if($params['id'] > 0){//处理
                //获取管理员id和姓名
                $logInfo = AlarmLog::getById($params['id']);
                if (!$logInfo) {
                    $this->createBaseResponse('系统中没有该条报警信息', 5006);
                }

                $arr = [
                    'uid' => $member['id'],
                    'otype' => $params['otype'],
                    'note'  => $params['note'],
                    'haveo' => 1,
                    'otime' => time(),
                    'oname' => $member['name'],
                ];

                $up = AlarmLog::updateById($params['id'], $arr);
                if($up){
                    $ttl = $this->otype[$params['otype']]['ttl'] ?? 0;
                    if ($ttl == 0) {
                        $this->createBaseResponse('系统中没有符合的暂停选项', 5007);
                    }

                    if ($ttl == -1) {
                        $redis->set($logInfo['key'], 1);
                    } else {
                        $redis->setex($logInfo['key'], $ttl, 1);
                    }
                    $this->createBaseResponse("处理成功");
                }
            }
        }catch (Exception $e){
            $this->createBaseResponse("处理失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("处理失败", 5001);

    }

    public function getMembers(){
        $data = Manager::getList();
        $this->createDataResponse($data);
    }

    public function getMonitorItems(){
        $data = ConfigMonitorItems::getList(['status' => 1]);
        $this->createDataResponse($data);
    }
    



}