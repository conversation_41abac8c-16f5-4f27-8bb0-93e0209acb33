<?php

namespace App\Http\Controllers\Monitor;

use App\Http\Controllers\Controller;
use App\Http\Controllers\CommonController;
use App\Models\ClickHouse\RequestChannelLog;
use App\Models\Monitor\ChannelStatus;
use App\Models\Monitor\ChannelExceptionRecord;
use App\Models\Monitor\LogChannel;
use App\Models\Channel;
use App\Providers\RedisCache\RedisCache;

//class ChannelStatisController extends Controller
class ChannelStatisController extends CommonController
{

    //新版
    public function getSuccessInfoV2()
    {
        $timeSection = request()->post('time_section');
        $apikey = request()->post('apikey', config('params.mayi_apikey'));

        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);

        $where = [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'apikey' => $apikey,
        ];

        $rcModel = new RequestChannelLog();

        //获取合计统计信息
        $totalData = $rcModel->getTotalCountByCondition($where);
        $totalData = $this->getRowData($totalData);

        //获取渠道的统计信息
        $channelData = $rcModel->getChannelCountByCondition($where);
        $channelData = $this->getChannelInfoWithCid($channelData);
        $channelData = $this->getChannelRow($channelData);

        //获取 异常列
        $exceptionColumn = $this->getExceptionColumn($totalData['exception']);

        //通过 异常列，格式化数据
        //获取 表头
        $result['head'] = $this->getHead($exceptionColumn);
        $totalData = $this->formatRow($totalData, $exceptionColumn);
        $channelsData = $this->formatChannelRow($channelData,$exceptionColumn);
        $rowData[] = $totalData;
        $result['data'] = array_merge($rowData, $channelsData);

        $this->createDataResponse($result);
    }

    public function getSuccessInfo()
    {
        $timeSection = request()->post('time_section');
        $apikey = request()->post('apikey', config('params.mayi_apikey'));

        $timeSection = explode(' - ', $timeSection);
        $startTime = strtotime($timeSection[0]);
        $endTime = strtotime($timeSection[1]);
        $where = [
            ['time', '>=', $startTime],
            ['time', '<=', $endTime],
            ['apikey', '=', $apikey],
        ];

        //获取合计统计信息
        $totalData = LogChannel::getTotalCountByCondition($where);
        $totalData = $this->getRowData($totalData);

        //获取渠道的统计信息
        $channelData = LogChannel::getChannelCountByCondition($where);
        $channelData = $this->getChannelInfoWithCid($channelData);
        $channelData = $this->getChannelRow($channelData);

        //获取 异常列
        $exceptionColumn = $this->getExceptionColumn($totalData['exception']);

        //通过 异常列，格式化数据
        //获取 表头
        $result['head'] = $this->getHead($exceptionColumn);
        $totalData = $this->formatRow($totalData, $exceptionColumn);
        $channelsData = $this->formatChannelRow($channelData,$exceptionColumn);
        $rowData[] = $totalData;
        $result['data'] = array_merge($rowData, $channelsData);

        $this->createDataResponse($result);
    }

    private function getRowData($info, $channelId = 0)
    {
        $all = 0;
        $success = 0;
        $runTime = 0;
        $exception = [];

        foreach ( $info as $v ){
            $all += $v['count'];
            $runTime += $v['run_time'];

            if( $v['status'] == 0 ){
                $success = $v['count'];
            }else{
                $exception['status_'.$v['status']] = $v['count'];
            }
        }

        $result['channel_id'] = $channelId;
        $result['all'] = $all;
        $result['success'] = $success;
        $result['ratio'] = divGetRatio($success, $all, 4);
        $result['run_time'] = strval(div($runTime, $all, 0));

        $result['exception'] = $exception;

        return $result;
    }

    private function getChannelInfoWithCid($channelInfo)
    {
        $result = [];
        foreach ( $channelInfo as $v ){
            $result[$v['channel_id']][] = [
                'status' => $v['status'],
                'count' => $v['count'],
                'run_time' => $v['run_time'],
            ];
        }

        return $result;
    }

    private function getChannelRow($channelInfo)
    {
        $result = [];

        foreach ( $channelInfo as $channelId => $v ){
            $result[] = $this->getRowData($v, $channelId);
        }

        return $result;
    }

    private function getExceptionColumn($exception = [])
    {
        $exception = array_keys($exception);

        return $exception;
    }

    private function getHead($exceptionColumn)
    {
        $head = [
            'channel_name'=>'渠道',
            'all'=>'总量',
            'success'=>'成功量',
            'ratio'=>'成功率',
            'run_time'=>'平均响应时间',
        ];

        foreach( $exceptionColumn as $v ){
            $statusInfo = explode('_', $v);
            $status = $statusInfo[1];
            $info = ChannelStatus::getInfoByStatus($status);

            $head[$v] = isset($info['info']) ? $status.'('.$info['info'].')' : $status;
        }

        return $head;
    }

    private function formatChannelRow($channelInfo, $exceptionColumn)
    {
        $result = [];
        foreach ( $channelInfo as $v ){
            $result[] = $this->formatRow($v, $exceptionColumn);
        }

        return $result;
    }

    private function formatRow($info, $exceptionColumn)
    {
        $result = [
            'channel_id' => $info['channel_id'],
            'all' => $info['all'],
            'success' => $info['success'],
            'ratio' => $info['ratio'],
            'run_time' => $info['run_time'],
        ];

        if( $info['channel_id'] == 0 ){
            $result['channel_name'] = '合计';
        }else{
            $result['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($info['channel_id']);
        }

        foreach ( $exceptionColumn as $status ){
            if( isset($info['exception'][$status]) ){
                $result[$status] = $info['exception'][$status];
            }else{
                $result[$status] = 0;
            }
        }

        return $result;
    }


    public function getExceptionList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $channel_id = request()->post('channel_id', '');
        $offset = ($page - 1) * $limit;
        $where = ['status' => 1];
        if($channel_id){
            $where['channel_id'] = $channel_id;
        }

        $channel_list = Channel::select(['channel_id', 'label', 'name'])->get()->toArray();
        $channel_list = array_column($channel_list, 'label', 'channel_id');

        $res = ChannelExceptionRecord::getList($where, $offset, $limit);
        foreach ($res['data'] as &$val){
            $start_time = $val['start_time'];
            $val['channel_name'] = $channel_list[$val['channel_id']];
            $val['start_time'] = date('Y-m-d H:i:s', $start_time);
            $val['end_time'] = date('Y-m-d H:i:s', $start_time + $val['keep_time']);
            $val['keep_time'] = ceil($val['keep_time']/60);
            $val['ratio'] = 100 * $val['ratio'].'%';

        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);
    }


    public function saveChannelException(){
        $params = request()->post();
        if(intval($params['total_num']) == 0 || intval($params['affect_num']) == 0){
            $this->createBaseResponse("总条数和影响条数必须为大于0的数字", 5003);
        }

        $params['start_time'] = strtotime($params['start_time']);
        $params['end_time'] = strtotime($params['end_time']);
        if($params['start_time'] >= $params['end_time']){
            $this->createBaseResponse("结束时间必须大于开始时间", 5004);

        }
        $params['keep_time'] = $params['end_time'] - $params['start_time'];
        $params['ratio'] = bcdiv(intval($params['affect_num']), intval($params['total_num']), 2);
        $id = $params['id']?:0;
        unset($params['id']);
        unset($params['end_time']);//end_time只用于方便计算keep_time,数据库没有该字段

        try{
            if($id > 0){//编辑
                $op = '编辑';
                $params['updated_at'] = time();
                $up = ChannelExceptionRecord::where(['id' => $id])->update($params);
                if($up){
                    $this->createBaseResponse("编辑成功");
                }
            }else{//添加
                $op = '添加';
                $params['created_at'] = time();
                $insert = ChannelExceptionRecord::insert($params);
                if($insert){
                    $this->createBaseResponse("添加成功");
                }
            }
        }catch (Exception $e){
            $this->createBaseResponse($op."失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse($op."失败", 5001);

    }

    public function getChannelException(){
        $id = request()->get('id');

        try{
            $info = ChannelExceptionRecord::where(['id' => $id])->first();
            if($info){
                $start_time = $info['start_time'];
                $info['start_time'] = date('Y-m-d H:i:s', $start_time);
                $info['end_time'] = date('Y-m-d H:i:s', $start_time + $info['keep_time']);
                //$info['keep_time'] = ceil($info['keep_time']/60);
                $this->createBaseResponse("获取数据成功", 0, $info);
            }
        }catch (Exception $e){
            $this->createBaseResponse("获取数据成功失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据成功失败", 5001);

    }

    public function delChannelException(){
        $id = request()->get('id');

        try{
            $info = ChannelExceptionRecord::where(['id' => $id])->first();
            if(empty($info)){
                $this->createBaseResponse("数据不存在", 0, $info);
            }

            $params['updated_at'] = time();
            $params['status'] = 0;
            $up = ChannelExceptionRecord::where(['id' => $id])->update($params);
            if($up){
                $this->createBaseResponse("删除成功");
            }

        }catch (Exception $e){
            $this->createBaseResponse("数据异常:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("删除失败", 5001);

    }






}