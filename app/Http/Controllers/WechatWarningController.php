<?php

namespace App\Http\Controllers;

use App\Http\Repository\WechatWarningRepository;
use App\TraitUpgrade\HandleLogTrait;
use App\TraitUpgrade\ResponseTrait;
use <PERSON><PERSON>\Lumen\Routing\Controller;

//class WechatWarningController extends Controller
class WechatWarningController extends CommonController
{
    use HandleLogTrait, ResponseTrait;
    private $repository;

    /**
     * WechatWarningController constructor.
     * @param WechatWarningRepository $repository
     */
    public function __construct(WechatWarningRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 微信预警
     * @throws \Exception
     */
    public function warning()
    {
        try {
            $this->repository->warning();
            $msg = 'success';
            return $this->response(compact('msg'));
        } catch (\Exception $e) {

            // 记录日志
            $log_info = [
                'handle_type' => 'wechat',
                'description' => 'back-api微信预警出错',
                'content' => $e->getMessage(),
                'handle_user' => 'system',
                'handle_result' => '0'
            ];
            $this->log($log_info);
            return $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
