<?php

namespace App\Http\Controllers;

use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\ResponseTrait;
use App\Repositories\ProductValueThresholdRepositorie;



//class ProductValueThresholdController extends Controller
class ProductValueThresholdController extends CommonController
{

    //产品阈值实现

	use ResponseTrait, CurlTrait;

	public function  __construct()
    {
        parent::__construct();
        $this->productvaluethresholdrepositorie = new ProductValueThresholdRepositorie();
    }

    //获取下拉选择项
    public function getOptions()
    {
        return $this->productvaluethresholdrepositorie->getOptions();
    }
	
}
