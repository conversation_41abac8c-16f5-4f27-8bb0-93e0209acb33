<?php

namespace App\Http\Controllers;

use App\Models\AccountProductLimit;
use App\Models\SystemSession;
use App\Providers\RedisCache\RedisCache;

class AccountProductLimitController extends CommonController
{
    public function limitList()
    {
        $limit = intval(request()->post('limit', 20));
        $page = intval(request()->post('page', 1));
        $product_id = intval(request()->post('product_id', ''));
        $account_id = request()->post('account_id', '');
        $apikey = request()->post('apikey', '');

        $params = compact('product_id', 'account_id', 'apikey', 'product_id');
        $offset = ($page - 1) * $limit;
        $result = AccountProductLimit::getList($params, $offset, $limit);

        foreach ($result as &$value) {
            $value['product_name'] = RedisCache::instance('productId_productName_mapping')->get($value['product_id']);
            $value['start_time'] = date('Y-m-d', $value['start_time']);
            $value['end_time'] = date('Y-m-d', $value['end_time']);
        }

        $data['list'] = $result;
        $data['count'] = AccountProductLimit::getCount($params);
        $this->createBaseResponse('获取数据成功', 0, $data);
    }

    public function limitInfo()
    {
        $id = intval(request()->post('id', ''));

        try {
            $info = AccountProductLimit::where(['id' => $id])->first();
            if ($info) {
                $info['start_time'] = date('Ymd', $info['start_time']);
                $info['end_time'] = date('Ymd', $info['end_time']);
                $info['product_id'] = (string)$info['product_id'];
                $this->createBaseResponse('获取数据成功', 0, $info);
            }
        } catch (Exception $e) {
            $this->createBaseResponse('获取数据异常', 5002, []);
        }

        $this->createBaseResponse('获取数据失败', 5001, []);
    }

    public function saveLimit()
    {
        $data = request()->post();
        $user_cookie = request()->post('user_cookie', null);
        $id = intval(request()->post('id', null));
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id' => $user_cookie])->first();
            if (empty($user_name)) {
                $this->createBaseResponse('登录已过期，请重新登录', 50001, []);
            }
            $user_name = $user_name->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)) {
                $sysName = $match[1];
            }
        }
        if (!isset($sysName)) {
            $this->createBaseResponse('非法操作', 5002, []);
        }

        try {
            if ($id > 0) { // 编辑
                $res = AccountProductLimit::updateData($id, $data);
                if ($res) {
                    $this->createBaseResponse('编辑成功', 0, []);
                } else {
                    $this->createBaseResponse('编辑失败，请联系技术', 5006, []);
                }
            } else {
                $res = AccountProductLimit::where(['apikey' => $data['apikey'], 'product_id' => $data['product_id']])->first();
                if ($res) {
                    $this->createBaseResponse('该客户的产品已添加', 5001, []);
                }
                $res = AccountProductLimit::addData($data);
                if ($res) {
                    $this->createBaseResponse('添加成功', 0, []);
                } else {
                    $this->createBaseResponse('添加失败，请联系技术', 5006, []);
                }
            }
        } catch (Exception $e) {
            $this->createBaseResponse("任务记录操作失败:" . $e->getMessage() . "，请联系技术", 5007, []);
        }
    }

    public function delLimit()
    {
        $id = intval(request()->get('id', ''));
        $user_cookie = request()->get('user_cookie', '');
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id' => $user_cookie])->first();
            if (empty($user_name)) {
                $this->createBaseResponse('登录已过期，请重新登录', 50001, []);
            }
            $user_name = $user_name->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)) {
                $sysName = $match[1];
            }
        }
        if (!isset($sysName)) {
            $this->createBaseResponse('非法操作', 5002, []);
        }

        if (!$id) {
            $this->createBaseResponse('非法操作', 5002, []);
        }

        try {
            $del = AccountProductLimit::where(['id' => $id])->delete();
            if ($del) {
                $this->createBaseResponse('删除成功', 0, []);
            }
            $this->createBaseResponse('删除失败', 5005, []);
        } catch (Exception $e) {
            $this->createBaseResponse("删除数据失败:" . $e->getMessage(), 5006);
        }

    }
}