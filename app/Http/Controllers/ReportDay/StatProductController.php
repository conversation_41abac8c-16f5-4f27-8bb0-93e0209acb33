<?php

namespace App\Http\Controllers\ReportDay;

use App\Define\StatDefine;
use App\Http\Repository\ReportDay\StatRepository;
use App\Http\Controllers\CommonController;
use App\Repositories\FeishuRepository;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\DeptGrade;
use App\Models\Product;
use App\Models\StatisticsGatherCustomer;
use App\Models\StatisticsGatherProduct;
use App\Repositories\Income\MainRepository;
use App\Utils\Helpers\Func;
use SebastianBergmann\CodeCoverage\Report\PHP;

//class StatProductController extends BaseController

class StatProductController extends CommonController
{
    private $channel_group_map = [
        1   => '运营商_联通01',
        110 => '运营商_联通01',
        42  => '运营商_移动01',
        40  => '运营商_移动01',
        43  => '运营商_移动03',
        44  => '运营商_移动04',
        53  => '运营商_电信01',
        54  => '运营商_电信01',
        52  => '运营商_电信02',
        113 => '核验类_诚智天扬',
        120 => '核验类_创蓝',
        119 => '核验类_马上汇',
        126 => '核验类_大衍',
        128 => '核验类_极推',
        201 => '核验类_天眼查',
        132 => '核验类_慧辰',
        134 => '核验类_银联',
        140 => '核验类_火山',
        143 => '核验类_中移互联',
        144 => '核验类_锦锐',
        145 => '核验类_旺店',
        309 => '存量洞察_百应',
        318 => '存量洞察_云动',
        307 => '存量洞察_道未',
        320 => '存量洞察_盾山',
        321 => '存量洞察_坷伽',
        810 => '号码融_【号码融】C4',
        809 => '号码融_【号码融】C5',
        811 => '号码融_【号码融】C5',
        807 => '号码融_【号码融】C6',
        813 => '号码融_【号码融】D-C10',
        812 => '号码融_【号码融】D-C14',
        814 => '号码融_浙数 （百度云）',
        815 => '号码融_浙数（腾讯天域）',
    ];

    //聚合(101)、朴道(141)剔除，不展示
    //移动02(41)，C7(806)、1(805)、2(804)、10(803)，号码风险等级(801) 成本0，不展示
    //目前不展示，后续有需要再加
    private $skip_channle_id = [41, 101, 801, 803, 804, 805, 806,141];

    public function getReportList(){
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        $date = request()->post('date', date('Ymd', strtotime('-1 days')));

        try {
            $repository = new StatRepository();

            $data = $repository->getReportList($date);

            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }

    /**
     * 领导驾驶舱(pc端): 产品分类收入
     * @return \Illuminate\Http\JsonResponse
     */
    public function productCategoryIncomeData() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {

            $aConfig = [
                '运营商' => [
                    '邦信分-通信字段' => [
                        210
                    ],
                    '邦信分-通信评分' => [
                        1000
                    ],
                ],
                '代理' => [
                    '在网时长' => [
                        '200-202',
                    ],
                    '三要素' => [
                        '200-213',
                        '200-201',
                        '200-41006',
                    ],
                    '实时在网状态' => [
                        '200-216',
                        '200-41007',
                    ],
                    '在网状态' => [
                        '200-203',
                    ],
                    '读秒分' => [
                        '200-310',
                        '200-311',
                        '200-312',
                        '200-313',
                        '200-314',
                        '200-315',
                        '200-316',
                        '200-317',
                        '200-318',
                        '200-41004',
                    ],
                    '其他' => [
                        '200-other'
                    ],
                ],
                '自有' => [
                    '号码风险等级' => [
                        615,
                        614
                    ],
                    '号码分' => [
                        10000
                    ],
                    '邦秒验自有分' => [
                        '200-41003',
                        '200-41005',
                        '200-331',
                        '200-330',
                        '200-329',
                        '200-328',
                        '200-327',
                        '200-326',
                        '200-325',
                        '200-324',
                        '200-323',
                        '200-322',
                        '200-321',
                        '200-320',
                    ],
                    '前筛产品' => [
                        '200-224',
                        '200-225',
                        '200-41002',
                        '200-41001',
                    ],
                    '金盾' => [
                        '50000-other',
                        '612'
                    ],
                    '其他' => [
                        '401',
                        '604',
                        '601',
                        '104',
                        '3000',
                    ],
                    '企服-品牌号' => [
                        '3100-3103',
                        '3100-3104',
                    ],
                    '企服-神盾' => [
                        '3100-other',
                        '50000-50101'
                    ],
                    '存量洞察-邦运营' => [
                        '30000-1200'
                    ],
                    '存量洞察-存量分层' => [
                        '30000-other'
                    ],
                    '号码融' => [
                        '70000'
                    ],
                ],
            ];


            $aFatherGather = [];
            $aFatherSumMap = [];
            $aNotOtherGather = [];
            $aFatherId = [];
            foreach ($aConfig as $sCateName => $aFatherList) {
                foreach ($aFatherList as $sFatherName => $aProductList) {
                    $aFatherGather[$sCateName][] = $sFatherName;
                    foreach ($aProductList as $mProductId) {
                        $aFatherSumMap[$mProductId] = $sCateName . '_' . $sFatherName;

                        if (is_numeric($mProductId)) {
                            $aFatherId[] = $mProductId;
                            continue;
                        }
                        [$iFatherId, $mProductId] = explode('-', $mProductId);
                        if ('other' != $mProductId) {
                            $aNotOtherGather[$iFatherId][] = $mProductId;
                        }
                    }
                }
            }


            // 填充实际数据
            $iYesterday = date('Ymd', strtotime('-1 day', time()));
            $iDayFirst = date('Y') . '0101';
            // 昨日数据
            //$iYesterday = 20240701; // todo
            $aDayGatherList = StatisticsGatherProduct::getProductDataList([['date', '=', $iYesterday]]);

            $aDayGatherMap = [];
            foreach ($aDayGatherList as $aItem) {
                $iFatherId = $aItem['father_id'];
                $iProductId = $aItem['product_id'];

                if (in_array($iFatherId, $aFatherId)) {
                    $sKey = $iFatherId;
                } else if (isset($aNotOtherGather[$iFatherId]) && in_array($iProductId, $aNotOtherGather[$iFatherId])) {
                    $sKey = $iFatherId . '-' . $iProductId;
                } else {
                    $sKey = $iFatherId . '-other';
                }

                $sFatherName = $aFatherSumMap[$sKey] ?: '';
                if (!$sFatherName) {
                    continue;
                }

                $fProfit = bcsub($aItem['money'], $aItem['cost_money'], 6);

                if (!isset($aDayGatherMap[$sFatherName])) {
                    $aDayGatherMap[$sFatherName] = [
                        'number' => $aItem['number'],
                        'money' => $aItem['money'],
                        'profit' => $fProfit,
                    ];
                } else {
                    $aDayGatherMap[$sFatherName]['number'] += $aItem['number'];
                    $aDayGatherMap[$sFatherName]['money'] = bcadd($aDayGatherMap[$sFatherName]['money'], $aItem['money'], 6);
                    $aDayGatherMap[$sFatherName]['profit'] = bcadd($aDayGatherMap[$sFatherName]['profit'], $fProfit, 6);
                }
            }
            // 全年数据
            $aYearGatherList = StatisticsGatherProduct::getGatherDataByProduct([['date', '>=', $iDayFirst], ['date', '<=', $iYesterday]]);

            $aYearGatherMap = [];
            foreach ($aYearGatherList as $aItem) {
                $iFatherId = $aItem['father_id'];
                $iProductId = $aItem['product_id'];

                if (in_array($iFatherId, $aFatherId)) {
                    $sKey = $iFatherId;
                } else if (isset($aNotOtherGather[$iFatherId]) && in_array($iProductId, $aNotOtherGather[$iFatherId])) {
                    $sKey = $iFatherId . '-' . $iProductId;
                } else {
                    $sKey = $iFatherId . '-other';
                }

                $sFatherName = $aFatherSumMap[$sKey] ?: '';
                if (!$sFatherName) {
                    continue;
                }

                $fProfit = bcsub($aItem['money'], $aItem['cost_money'], 6);

                if (!isset($aYearGatherMap[$sFatherName])) {
                    $aYearGatherMap[$sFatherName] = [
                        'number' => $aItem['number'],
                        'money' => $aItem['money'],
                        'profit' => $fProfit,
                    ];
                } else {
                    $aYearGatherMap[$sFatherName]['number'] += $aItem['number'];
                    $aYearGatherMap[$sFatherName]['money'] = bcadd($aYearGatherMap[$sFatherName]['money'], $aItem['money'], 6);
                    $aYearGatherMap[$sFatherName]['profit'] = bcadd($aYearGatherMap[$sFatherName]['profit'], $fProfit, 6);
                }
            }

            // 根据配置聚合数据
            $aData_ = [];
            // 总计
            $iTotalDayNumber = 0;
            $fTotalDayMoney = 0;
            $fTotalDayProfit = 0;
            $fTotalYearMoney = 0;
            $fTotalYearProfit = 0;

            foreach ($aFatherGather as $sCateName => $aFatherName) {
                $aDataList = [];
                $t = count($aFatherName) + 1;
                // 类别小计
                $iSumDayNumber = 0;
                $fSumDayMoney = 0;
                $fSumDayProfit = 0;
                $fSumYearMoney = 0;
                $fSumYearProfit = 0;

                foreach ($aFatherName as $sFatherName) {
                    $sKey = $sCateName . '_' . $sFatherName;
                    // 产品数据
                    $iDayNumber = $aDayGatherMap[$sKey]['number'] ?? 0;
                    $fDayMoney = $aDayGatherMap[$sKey]['money'] ?? 0;
                    $fDayProfit = $aDayGatherMap[$sKey]['profit'] ?? 0;
                    $fYearMoney = $aYearGatherMap[$sKey]['money'] ?? 0;
                    $fYearProfit = $aYearGatherMap[$sKey]['profit'] ?? 0;;
                    // 累加小计
                    $iSumDayNumber += $iDayNumber;
                    $fSumDayMoney = bcadd($fSumDayMoney, $fDayMoney, 6);
                    $fSumDayProfit = bcadd($fSumDayProfit, $fDayProfit, 6);
                    $fSumYearMoney = bcadd($fSumYearMoney, $fYearMoney, 6);
                    $fSumYearProfit = bcadd($fSumYearProfit, $fYearProfit, 6);

                    $aDataList[] = [
                        'cate' => $sCateName,
                        'name' => $sFatherName,
                        'day_number' => $this->convertToChineseNumeration($iDayNumber),
                        'day_money' => $this->convertToChineseNumeration($fDayMoney),
                        'day_profit' => $this->convertToChineseNumeration($fDayProfit),
                        'year_money' => $this->convertToChineseNumeration($fYearMoney),
                        'year_profit' => $this->convertToChineseNumeration($fYearProfit),
                        't' => $t,
                    ];
                }

                $aSum = [
                    'cate' => $sCateName,
                    'name' => '小计',
                    'day_number' => $this->convertToChineseNumeration($iSumDayNumber),
                    'day_money' => $this->convertToChineseNumeration($fSumDayMoney),
                    'day_profit' => $this->convertToChineseNumeration($fSumDayProfit),
                    'year_money' => $this->convertToChineseNumeration($fSumYearMoney),
                    'year_profit' => $this->convertToChineseNumeration($fSumYearProfit),
                    't' => $t,
                ];
                // 累加总计
                $iTotalDayNumber += $iSumDayNumber;
                $fTotalDayMoney = bcadd($fTotalDayMoney, $fSumDayMoney, 6);
                $fTotalDayProfit = bcadd($fTotalDayProfit, $fSumDayProfit, 6);
                $fTotalYearMoney = bcadd($fTotalYearMoney, $fSumYearMoney, 6);
                $fTotalYearProfit = bcadd($fTotalYearProfit, $fSumYearProfit, 6);

                $aData_[] = $aSum;
                $aData_ = array_merge($aData_, $aDataList);
            }

            $aTotal = [
                'cate' => '总计',
                'name' => '',
                'day_number' => $this->convertToChineseNumeration($iTotalDayNumber),
                'day_money' => $this->convertToChineseNumeration($fTotalDayMoney),
                'day_profit' => $this->convertToChineseNumeration($fTotalDayProfit),
                'year_money' => $this->convertToChineseNumeration($fTotalYearMoney),
                'year_profit' => $this->convertToChineseNumeration($fTotalYearProfit),
                't' => 1,
            ];

            $aData[] = $aTotal;
            $aData = array_merge($aData, $aData_);

            return $this->response(0, $aData);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 领导驾驶舱(pc端): 产品收入波动图 + 产品毛利波动图
     * @return \Illuminate\Http\JsonResponse
     */
    public function productData(){
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            //$params = request()->post();

            // 展示产品配置
            $aConfig = [
                'father_id' => [
                    200, 210, 615, 1000, 10000, 30000
                ],
                'product_id' => [
                    //201, 202
                ],
                'rename' => [
                    1000 => '邦信分',
                ],
            ];
            // 信息合并配置
            $aCombine = [
                210 => 1000,
            ];

            $aDateList = [];
            $aDateListText = [];
            $iNow = time();
            //$iNow = strtotime(20240720); // todo
            for ($i = 10; $i > 0; $i--) {
                $aDateList[] = date('Ymd', strtotime("-$i days", $iNow));
                $aDateListText[] = date('md', strtotime("-$i days", $iNow));
            }

            $aAllProductId = array_merge($aConfig['father_id'], $aConfig['product_id']);
            $aProductList = Product::getProductListByProductIds($aAllProductId);
            $aProductMap = array_column($aProductList, 'product_name', 'product_id');
            $aConfig['rename'] and $aProductMap = $aConfig['rename'] + $aProductMap;

            // 初始化数据, 填充0值
            $aIncomeGatherData = [];
            $aProfitGatherData = [];
            foreach ($aProductMap as $iProductId => $sProductName) {
                if (isset($aCombine[$iProductId])) {
                    continue;
                }
                foreach ($aDateList as $sDate) {
                    $sDate = substr($sDate, 4, 4);
                    $aIncomeGatherData[$sProductName][$sDate] = [
                        'total' => '0',
                    ];
                    $aProfitGatherData[$sProductName][$sDate] = [
                        'total' => '0',
                    ];
                }
            }
            // 根据配置查询数据
            $aGatherList = [];
            if ($aConfig['father_id']) {
                $aWhereIn = [
                    'date' => $aDateList,
                    'father_id' => $aConfig['father_id'],
                ];
                $aFatherGatherList_ = StatisticsGatherProduct::getGatherDataByDateFather([], $aWhereIn);

                foreach ($aFatherGatherList_ as $aItem) {
                    $aGatherList[$aItem['father_id']][$aItem['date']] = $aItem;
                }
            }
            if ($aConfig['product_id']) {
                $aWhereIn = [
                    'date' => $aDateList,
                    'product_id' => $aConfig['product_id'],
                ];
                $aProductGatherList_ = StatisticsGatherProduct::getProductDataList([], $aWhereIn);

                foreach ($aProductGatherList_ as $aItem) {
                    $aGatherList[$aItem['product_id']][$aItem['date']] = $aItem;
                }
            }
            // 根据配置合并数据
            if ($aCombine) {
                foreach ($aCombine as $iFromId => $iToId) {
                    $aFromData = $aGatherList[$iFromId] ?? [];
                    $aToData = $aGatherList[$iToId] ?? [];

                    foreach ($aFromData as $sDate => $aItem) {
                        $aToData[$sDate]['money'] = bcadd($aToData[$sDate]['money'] ?? 0, $aItem['money'] ?? 0, 6);
                        $aToData[$sDate]['cost_money'] = bcadd($aToData[$sDate]['cost_money'] ?? 0, $aItem['cost_money'] ?? 0, 6);
                    }

                    $aGatherList[$iToId] = $aToData;
                    unset($aGatherList[$iFromId]);
                }
            }

            // 填充实际数据
            foreach ($aGatherList as $iProductId => $aItemList) {
                foreach ($aItemList as $sDate => $aItem) {
                    $sProductName = $aProductMap[$iProductId] ?? '';

                    $sDate = substr($sDate, 4, 4);

                    $aIncomeGatherData[$sProductName][$sDate] = [
                        'total' => bcdiv($aItem['money'], 10000, 1),
                    ];
                    $aProfitGatherData[$sProductName][$sDate] = [
                        'total' => bcdiv(bcsub($aItem['money'], $aItem['cost_money'], 6), 10000, 1),
                    ];
                }
            }

            $aData = [
                'income_data_list' => [
                    'x_val' => $aDateListText,
                    'y_val' => $aIncomeGatherData,
                ],
                'profit_data_list' => [
                    'x_val' => $aDateListText,
                    'y_val' => $aProfitGatherData,
                ],
            ];
            return $this->response(0, $aData);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 领导驾驶舱(pc端): top5客户近10日收入波动图
     * @return \Illuminate\Http\JsonResponse
     */
    public function customerTopIncomeData() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            $iNow = time();
            //$iNow = strtotime(20240720); // todo

            $aDateList = [];
            $aDateListText = [];
            for ($i = 10; $i > 0; $i--) {
                $aDateList[] = date('Ymd', strtotime("-$i days", $iNow));
                $aDateListText[] = date('md', strtotime("-$i days", $iNow));
            }

            // 计算前10天top5客户
            $aTopList = StatisticsGatherCustomer::getGatherDataByCustoemr([], ['date' => $aDateList]);
            $aCustomerId = array_column($aTopList, 'customer_id');
            $aCustomerList = Customer::getCustomerByIdList($aCustomerId);
            $aCustomerName = array_column($aCustomerList, 'name', 'customer_id');
            // 初始化数据, 填充0值
            $aIncomeGatherData = [];
            foreach ($aCustomerName as $sCustomerName) {
                foreach ($aDateList as $sDate) {
                    $sDate = substr($sDate, 4, 4);
                    $aIncomeGatherData[$sCustomerName][$sDate] = [
                        'total' => '0',
                    ];
                }
            }
            // 填充实际数据
            $aGatherList = StatisticsGatherCustomer::getCustomerDataList([], ['date' => $aDateList, 'customer_id' => $aCustomerId]);
            foreach ($aGatherList as $aItem) {
                $sCustomerName = $aCustomerName[$aItem['customer_id']] ?? '';
                $sDate = substr($aItem['date'], 4, 4);

                $aIncomeGatherData[$sCustomerName][$sDate] = [
                    'total' => bcdiv($aItem['money'], 10000, 1),
                ];
            }

            $aData = [
                'x_val' => $aDateListText,
                'y_val' => $aIncomeGatherData,
            ];
            return $this->response(0, $aData);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 领导驾驶舱(pc端): 近两周每日权责
     * @return \Illuminate\Http\JsonResponse
     */
    public function twoWeekIncomeData() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            $sText = '收入';
            $iNow = time();
            //$iNow = strtotime(20240720); // todo

            $aDateList = [];
            $aDateListText = [];
            for ($i = 14; $i > 0; $i--) {
                $aDateList[] = date('Ymd', strtotime("-$i days", $iNow));
                $aDateListText[] = date('md', strtotime("-$i days", $iNow));
            }
            // 初始化数据,填充0值
            $aIncomeGatherData = [];
            foreach ($aDateList as $sDate) {
                $sDate = substr($sDate, 4, 4);
                $aIncomeGatherData[$sText][$sDate] = [
                    'total' => '0',
                ];
            }
            // 填充实际数据
            $aWhereIn = [
                'date' => $aDateList,
            ];
            $aGatherList = StatisticsGatherProduct::getGatherDataByDate([], $aWhereIn);
            foreach ($aGatherList as $aItem) {
                $sDate = substr($aItem['date'], 4, 4);

                $aIncomeGatherData[$sText][$sDate] = [
                    'total' => bcdiv($aItem['money'], 10000, 1),
                ];
            }

            $aData = [
                'x_val' => $aDateListText,
                'y_val' => $aIncomeGatherData,
            ];
            return $this->response(0, $aData);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 领导驾驶舱(pc端): 年度任务 + 本年回款指标进度
     * @return \Illuminate\Http\JsonResponse
     */
    public function annualData(){
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            //$params = request()->post();

            // 统计起止年份
            $iStartYear = 2021;
            $iEndYear = date('Y');
            // 同期数据 '月日'
            $iDayPeriod = date('md', strtotime('-1 day', time()));
            // 本年第一天日期
            $iDayFirst = $iEndYear . '0101';
            // 本年已过日期天数
            $iCostDay = ceil((time() - strtotime($iDayFirst)) / 86400);
            // 同期数据
            $aSamePeriod = [];
            for ($i = $iStartYear; $i <= $iEndYear; $i++) {
                $aWhere = [
                    ['date', '>=', $i . '0101'],
                    ['date', '<=', $i . $iDayPeriod],
                ];
                $aSamePeriod[$i] = StatisticsGatherProduct::getSumData($aWhere);
            }

            // 年度聚合数据
            $aWhere = [
                ['date', '>=', $iStartYear . '0101'],
                ['date', '<', $iEndYear . '0101'],
            ];
            $aYearGather = StatisticsGatherProduct::getGatherDataByYear($aWhere);
            $aYearGatherMap = array_column($aYearGather, null, 'year');
            // 本年全年数据 = 本年同期数据
            $aYearGatherMap[$iEndYear] = $aSamePeriod[$iEndYear];
            // 年度任务
            $iAnnualTaskTotal = config('params.year_task_money');
            $iAnnualTaskDone = $aSamePeriod[$iEndYear]['money'] ?? 0;
            $iAnnualIncomeEstimation = $iAnnualTaskDone / $iCostDay * 365; // todo 推测逻辑
            $iAnnualResidue = ($iAnnualTaskTotal - $iAnnualTaskDone) / (365 - $iCostDay);
            // 年度数据 (18-20年数据后台是缺失的，写死即可)
            $initial_value = 13274900;//18年年度权责
            $aAnnualDataList = [
                [
                    'year' => 2018,
                    'annual_total' => $this->convertToChineseNumeration($initial_value),//年度权责
                    'corresponding_period_total' => '-',
                    'profit' => '-',
                    'profit_rate' => '-',
                    'profit_rate_ins' => '-',
                    'compound_growth_rate' => '-'
                ],
                [
                    'year' => 2019,
                    'annual_total' => $this->convertToChineseNumeration(26882800),//年度权责
                    'corresponding_period_total' => '-',
                    'profit' => '-',
                    'profit_rate' => '-',
                    'profit_rate_ins' => '-',
                    'compound_growth_rate' => '102.0%'
                ],
                [
                    'year' => 2020,
                    'annual_total' => $this->convertToChineseNumeration(45563300),//年度权责
                    'corresponding_period_total' => '-',
                    'profit' => '-',
                    'profit_rate' => '-',
                    'profit_rate_ins' => '-',
                    'compound_growth_rate' => '85.2%'
                ],
            ];

            for ($i = $iStartYear; $i <= $iEndYear; $i++) {
                // 年度权责
                $fMoney = $aYearGatherMap[$i]['money'] ?? 0;
                // 同期收入
                $fPeriodMoney = $aSamePeriod[$i]['money'] ?? 0;
                // 同期成本
                $fPeriodCostMoney = $aSamePeriod[$i]['cost_money'] ?? 0;
                // 同期毛利
                $fProfit = bcsub($fPeriodMoney, $fPeriodCostMoney, 6);
                // 同期毛利率=同期毛利/同期权责
                if($fMoney == 0){
                    $fProfitRate = '-';
                }else{
                    $fProfitRate = round($fProfit / $fPeriodMoney * 100, 1) . '%';
                }

                // 同期同比增长
                if ($iStartYear == $i) {
                    $fRateIns = '-';
                } else {
                    $fLastMoney = $aSamePeriod[$i - 1]['money'] ?? 0;

                    $fRateIns = $fLastMoney ? round(($fPeriodMoney - $fLastMoney) / $fLastMoney * 100, 1) . '%' : '-';
                }

                //年复合增长率，公式：=POWER(终值/初值,1/年数)-1【终值初值使用“年度权责”】
                //其中初值为2018年度权责
                if($iEndYear == $i){
                    $compound_growth_rate = '-';
                }else if($fMoney == 0){
                    $compound_growth_rate = '-';
                }else{
                    $base_value = bcdiv($fMoney, $initial_value, 4);// 终值/初值
                    $exponent = bcdiv(1, $i-2018, 4);//指数(1/年数)
                    $compound_growth_rate = Func::bcpowWithDecimalExponent($base_value, $exponent);
                    $compound_growth_rate = bcsub($compound_growth_rate, 1, 4);
                    $compound_growth_rate = bcmul($compound_growth_rate, 100, 1).'%';
                }

                $aAnnualDataList[] = [
                    'year' => $i,
                    'annual_total' => $this->convertToChineseNumeration($fMoney),
                    'corresponding_period_total' => $this->convertToChineseNumeration($fPeriodMoney),
                    'profit' => $this->convertToChineseNumeration($fProfit),
                    'profit_rate' => $fProfitRate,
                    'profit_rate_ins' => $fRateIns,
                    'compound_growth_rate' => $compound_growth_rate
                ];
            }

            //按年倒序排序
            array_multisort(array_column($aAnnualDataList, 'year'), SORT_DESC, $aAnnualDataList);

            $aData = [
                'annual_task' => [
                    'annual_task_total_text' => $this->convertToChineseNumeration($iAnnualTaskTotal, true, true, 2),
                    'annual_task_done_text' => $this->convertToChineseNumeration($iAnnualTaskDone, true, true),
                    'annual_income_estimation_text' => $this->convertToChineseNumeration($iAnnualIncomeEstimation, true, true),
                    'annual_residue' => $this->convertToChineseNumeration($iAnnualResidue),
                    'annual_task_total' => number_format($iAnnualTaskTotal, 0, '.', ','),
                    'annual_task_done' => number_format($iAnnualTaskDone, 0, '.', ','),
                    'rate' => (int)(($iAnnualTaskDone / $iAnnualTaskTotal) * 100),
                ],
                'annual_data_list' => $aAnnualDataList
            ];
            return $this->response(0, $aData);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getFile().';'.$e->getLine().';'.$e->getMessage());
        }
    }

    /**
     * 领导驾驶舱(pc端): 区域任务
     * @return \Illuminate\Http\JsonResponse
     */
    public function deptStatData() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            // 本年全年数据
            $iDayFirst = date('Y') . '0101';
            $aYearGatherDataList = StatisticsGatherCustomer::getGatherDataByDept([['date', '>=', $iDayFirst]], [], ['dept_id' => ['DEPT2020061910095792', 'DEPT2020062210573567', 'DEPT2019061815295230']]);
            $aYearGatherDataMap = array_column($aYearGatherDataList, null, 'dept_id');
            // 本月全月数据
            $iMonthFirst = date('Ym') . '01';
            //$iMonthFirst = date('Ym', strtotime("-1 month", time())) . '01';
            $aMonthGatherDataList = StatisticsGatherCustomer::getGatherDataByDept([['date', '>=', $iMonthFirst]], [], ['dept_id' => ['DEPT2020061910095792', 'DEPT2020062210573567', 'DEPT2019061815295230']]);
            $aMonthGatherDataMap = array_column($aMonthGatherDataList, null, 'dept_id');
            // 本年全量部门
            $aDeptId = array_keys($aYearGatherDataMap);
            // 部门信息
            $aDeptList = SystemDept::getAllDeptInfo();
            $aDeptMap = array_column($aDeptList, null, 'dept_id');
            // 部门层级关系
            $aDeptGradeList = DeptGrade::getAllDept();
            $aDeptGradeMap = array_column($aDeptGradeList, null, 'grade_dept_id');
            // 构造 倒二级 : [倒一级] map
            $aMap = [];
            foreach ($aDeptId as $sDeptId) {
                $aGrade = $aDeptGradeMap[$sDeptId];

                $aMap[$aGrade['grade_father_id']][] = $sDeptId;
            }
            // 部门数据聚合
            $aData = [];
            foreach ($aMap as $sFather => $aChild) {
                foreach ($aChild as $sDept) {
                    $aData[] = [
                        'father_dept_name' => $aDeptMap[$sFather]['dept_name'] ?? $sFather,
                        'dept_name' => $aDeptMap[$sDept]['dept_name'] ?? $sDept,
                        'monthly_income' => '-',
                        'monthly_done' => $this->convertToChineseNumeration($aMonthGatherDataMap[$sDept]['money'] ?? 0),
                        'monthly_rate' => '-',
                        'yearly_income' => '-',
                        'yearly_done' => $this->convertToChineseNumeration($aYearGatherDataMap[$sDept]['money'] ?? 0),
                        'yearly_rate' => '-',
                    ];
                }
            }

            return $this->response(0, $aData);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 领导驾驶舱(pc端): 月度收入成本毛利
     * @return \Illuminate\Http\JsonResponse
     */
    public function monthIncomeData() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            $aText = ['收入', '成本', '毛利'];

            $iNow = time();
            $aMonthList = [];
            for ($i = 12; $i > 0; $i--) {
                $aMonthList[] = date('Ym', strtotime("-$i months", $iNow));
            }
            // 初始化数据, 填充0值
            $aGatherData = [];
            foreach ($aText as $sText) {
                foreach ($aMonthList as $sMonth) {
                    $aGatherData[$sText][$sMonth] = [
                        'total' => '0',
                    ];
                }
            }
            // 填充实际数据
            $sStartMonth = current($aMonthList);
            $sStartDay = $sStartMonth . '01';
            $aGatherList = StatisticsGatherProduct::getGatherDataByMonth([['date', '>=', $sStartDay]]);
            foreach ($aGatherList as $aItem) {
                $sMonth = $aItem['month'];

                $fMoney = $aItem['money'];
                $fCostMoney = $aItem['cost_money'];
                $fProfit = bcsub($fMoney, $fCostMoney, 6);

                $aGatherData['收入'][$sMonth] = [
                    'total' => bcdiv($fMoney, 10000, 1),
                ];
                $aGatherData['成本'][$sMonth] = [
                    'total' => bcdiv($fCostMoney, 10000, 1),
                ];
                $aGatherData['毛利'][$sMonth] = [
                    'total' => bcdiv($fProfit, 10000, 1),
                ];
            }

            $aData = [
                'x_val' => $aMonthList,
                'y_val' => $aGatherData,
            ];
            return $this->response(0, $aData);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 领导驾驶舱(pc端): 年度渠道数据 请求多次 每次一个月
     *
     * @return \Illuminate\Http\JsonResponse
     * <AUTHOR> 2025-04-11 17:38:31
     *
     */
    public function monthlyChannelData() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        $month = request()->post('month', '');

        if(empty($month)){
            return $this->response(1, '', 'month 必传');
        }

        $start_date = $month . '01';
        $end_date = date('Ymt', strtotime($start_date));
        $current_month = date('Ym');
        if($current_month == $month){
            $end_date = date('Ymd');
        }

        $mr = new MainRepository();
        $channel_cost_list = $mr->getBaseCost(StatDefine::COST_DIMENSION_MONTH_CHANNEL, $start_date , $end_date ,[], [],[], [],['is_query_month_data' => 1, 'filter_customer_adjust' => 1]);
        if ($channel_cost_list['status'] != 0) {
            return $this->response(1, '', '查询失败: '.$channel_cost_list['msg']);
        }
        $channel_cost_list = $channel_cost_list['data'];

        $res = [];
        foreach($this->channel_group_map as $channel_id => $type_and_name){
            [$type_name, $channel_name] = explode('_', $type_and_name);
            $res[$type_name][$channel_name] = 0;
        }

        foreach($channel_cost_list as $item){
            if(in_array($item['channel_id'], $this->skip_channle_id)){
                continue;
            }
            if(!isset($this->channel_group_map[$item['channel_id']])){
                // echo $item['channel_id'].'数据不在分类中!';
                continue;
            }
            [$type_name, $channel_name] = explode('_', $this->channel_group_map[$item['channel_id']]);
            $res[$type_name][$channel_name] = $res[$type_name][$channel_name] + $item['cost'];
        }


        $ans = [];
        foreach($res as $type_name => $type_info){
            foreach($type_info as $channel_name => $money) {
                $tmp_key = Func::get_tmp_key($month,$type_name,$channel_name);
                $ans[$tmp_key] = $money;
            }
        }

        return $this->response(0, $ans);
    }


    /**
     *
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     * <AUTHOR> 2025-05-27 15:21:05
     *
     */
    public function weeklyChannelData(){
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        $type = request()->post('type', '');

        try {
            $p_range_info = [];

            if ($type == 'week' ||  $type == 'all') {
                $_p_week_info = Func::get_previous_two_weeks();

                $last_week      = $_p_week_info[1]['week_ordinal'] ? 'W' . $_p_week_info[1]['week_ordinal'] : '';
                $last_last_week = $_p_week_info[0]['week_ordinal'] ? 'W' . $_p_week_info[0]['week_ordinal'] : '';

                foreach ($_p_week_info as $week_info) {
                    $p_range_info[] = [
                        'range_type' => 'week',
                        'ordinal'    => $week_info['week_ordinal'] ? 'W' . $week_info['week_ordinal'] : '',
                        'start_date' => min($week_info['range']),
                        'end_date'   => max($week_info['range']),
                    ];
                }
            }

            if ($type == 'month' ||  $type == 'all') {

                $yesterday = strtotime('-1 day');

                $this_month = date('Ym', $yesterday);

                $this_month_start = date('Ym01', $yesterday);
                $this_month_end   = date('Ymd', $yesterday);

                $last_month_timestamp = strtotime('-1 month', strtotime($this_month_start));
                $last_month_start     = date('Ym01', $last_month_timestamp);
                $last_month           = date('Ym', $last_month_timestamp);

                $yesterday_date     = date('d', $yesterday);
                $last_month_lastday = date('t', $last_month_timestamp);

                if ($last_month_lastday < $yesterday_date) {
                    $last_month_end = date('Ymt', $last_month_timestamp);
                } else {
                    $last_month_end = date('Ym' . $yesterday_date, $last_month_timestamp);
                }

                $p_range_info[] = [
                    'range_type' => 'month',
                    'ordinal'    => $last_month,
                    'start_date' => $last_month_start,
                    'end_date'   => $last_month_end,
                ];
                $p_range_info[] = [
                    'range_type' => 'month',
                    'ordinal'    => $this_month,
                    'start_date' => $this_month_start,
                    'end_date'   => $this_month_end,
                ];
            }

            $channel_type_arr = ['运营商','核验类','存量洞察'];
            $mr = new MainRepository();

            $res = [];
            $diff = [];

            foreach($p_range_info as $week_info){
                $range_type = $week_info['range_type'];
                $ordinal    = $week_info['ordinal'];
                $start_date = $week_info['start_date'];
                $end_date   = $week_info['end_date'];

                //init res
                foreach($this->channel_group_map as $type_and_name){
                    [$_type_name, $_channel_name] = explode('_', $type_and_name);
                    if(!in_array($_type_name,$channel_type_arr)){
                        continue;
                    }
                    $res[$range_type][$_type_name][$_channel_name][$ordinal] = 0;
                }

                $channel_cost_list = $mr->getBaseCost(StatDefine::COST_DIMENSION_MONTH_CHANNEL, $start_date , $end_date ,[], [],[], [],['is_query_month_data' => 1, 'filter_customer_adjust' => 1]);
                if ($channel_cost_list['status'] != 0) {
                    return $this->response(1, '', '查询失败: '.$channel_cost_list['msg']);
                }
                $channel_cost_list = $channel_cost_list['data'];
                foreach($channel_cost_list as $item){
                    if(!isset($this->channel_group_map[$item['channel_id']])) {
                        continue;
                    }
                    [$type_name, $channel_name] = explode('_', $this->channel_group_map[$item['channel_id']]);
                    if(!in_array($type_name,$channel_type_arr)){
                        continue;
                    }
                    $res[$range_type][$type_name][$channel_name][$ordinal] += $item['cost'];
                }
            }

            $res_p_diff = [];
            $res_c_diff = [];
            foreach($res as $range_type => $range_info) {
                foreach($range_info as $type_name => $type_info) {
                    foreach($type_info as $channel_name => $diff_info) {
                        if(!isset($res_c_diff[$range_type][$type_name]['diff_count'])){
                            $res_c_diff[$range_type][$type_name]['diff_count'] = [
                                'earlier' => 0,
                                'later'   => 0,
                            ];
                        }
                        if($range_type == 'week') {
                            $last_week_count      = $diff_info[$last_week] ?? 0;
                            $last_last_week_count = $diff_info[$last_last_week] ?? 0;

                            $diff_p = $this->get_diff_p($last_last_week_count,$last_week_count);
                            $res_p_diff[$range_type][$type_name][$channel_name]['diff'] = $diff_p;

                            $res_c_diff[$range_type][$type_name]['diff_count'] = [
                                'earlier' => $res_c_diff[$range_type][$type_name]['diff_count']['earlier'] + $last_last_week_count,
                                'later'   => $res_c_diff[$range_type][$type_name]['diff_count']['later'] + $last_week_count,
                            ];
                        }
                        if($range_type == 'month') {
                            $this_month_count = $diff_info[$this_month] ?? 0;
                            $last_month_count = $diff_info[$last_month] ?? 0;

                            $diff_p = $this->get_diff_p($last_month_count,$this_month_count);
                            $res_p_diff[$range_type][$type_name][$channel_name]['diff'] = $diff_p;

                            $res_c_diff[$range_type][$type_name]['diff_count'] = [
                                'earlier' => $res_c_diff[$range_type][$type_name]['diff_count']['earlier'] + $last_month_count,
                                'later'   => $res_c_diff[$range_type][$type_name]['diff_count']['later'] + $this_month_count,
                            ];
                        }
                    }
                }
            }

            $result = [];
            foreach($res as $range_type => $range_info) {
                foreach ($range_info as $type_name => $type_info) {
                    foreach ($type_info as $channel_name => $info) {
                        $result[$range_type][$type_name]['x_val'][]   = $channel_name;
                        $result[$range_type][$type_name]['type_name'] = $type_name;


                        $diff_count = $res_c_diff[$range_type][$type_name]['diff_count'];

                        $diff_p = $this->get_diff_p($diff_count['earlier'],$diff_count['later']);

                        $diff_count = round(($diff_count['later'] - $diff_count['earlier'])/10000,2);

                        $week_or_month = $range_type == 'week' ? '周' : '月';

                        $result[$range_type][$type_name]['title'] = '本'.$week_or_month.'成本比上'.$week_or_month.'增长'.$diff_count.'万元，环比增长'.$diff_p.'%';

                        foreach ($info as $ordinal => $cost) {
                            $result[$range_type][$type_name]['y_val'][$ordinal][$channel_name] = ['total' => round($cost/10000,1)];
                        }
                    }
                }
            }

            foreach($res_p_diff as $range_type => $range_info) {
                foreach ($range_info as $type_name => $type_info) {
                    foreach ($type_info as $channel_name => $diff_info) {
                        $result[$range_type][$type_name]['y_val']['环比'][$channel_name] = ['total' => $diff_info['diff']];
                    }
                }
            }

            $channel_type_arr = array_flip($channel_type_arr);

            foreach($result as &$range_info) {
                usort($range_info, function ($a, $b) use ($channel_type_arr) {
                    return $channel_type_arr[$a['type_name']] > $channel_type_arr[$b['type_name']] ? 1 : - 1;
                });
            }

            return $this->response(0, $result);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getFile().' '.$e->getLine().' '.$e->getMessage());
        }
    }

    /**
     * 获取环比
     *
     * @param $earlier
     * @param $later
     *
     * @return float|int
     * <AUTHOR> 2025-05-29 16:35:45
     */
    private function get_diff_p($earlier,$later) {
        if ($earlier == 0) {
            $diff_p = 100;
            if ($later == 0) {
                $diff_p = 0;
            }
        } else {
            $diff_p = round((($later - $earlier) / $earlier) * 100, 0);
        }

        if($diff_p > 100) {
            $diff_p = 100;
        }

        return  $diff_p;
    }


    /**
     * 将数字转换为中文计数
     * [0, 1000) 直接返回
     * [1000, ~) 根据 $bShowThousand 开关, 将 123456789 转换为 1亿2345.6万 或者 1亿2345万
     * [100000000, ~) 根据 $bOnlyBillion 开关, 将 160000000 转换为 1.6亿 或者 1亿6000万
     * @param int $iNumber
     * @param bool $bShowThousand
     * @param bool $bOnlyBillion
     * @return string
     */
    private function convertToChineseNumeration($iNumber = 0, $bShowThousand = true, $bOnlyBillion = false, $degree = 1) {
        if($bShowThousand && $bOnlyBillion){
            return convertToChineseNumeration($iNumber, $bShowThousand, $bOnlyBillion, $degree);
        }

        return $this->formatNumStr($iNumber);
    }


    private function formatNumStr($num)
    {
        $return = $num;
        if ($num >= 1000 || $num <= -1000) {
            $return = sprintf ( "%.1f", $num / 10000 );
            $return = number_format($return, 1) . '万';
        }else{
            $return = round($return, 2);
        }
        return $return;
    }

}
