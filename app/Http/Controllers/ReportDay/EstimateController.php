<?php

namespace App\Http\Controllers\ReportDay;

use App\Define\StatDefine;
use App\Http\Repository\ReportDay\StatRepository;
use App\Http\Controllers\CommonController;
use App\Models\Common\FlexConfig;
use App\Models\Remit;
use App\Models\WeekIncomeEstimate;
use App\Repositories\FeishuRepository;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\DeptGrade;
use App\Models\Product;
use App\Models\StatisticsGatherCustomer;
use App\Models\StatisticsGatherProduct;
use App\Repositories\Income\MainRepository;
use App\Utils\Helpers\Func;
use Exception;
use Illuminate\Http\JsonResponse;
use SebastianBergmann\CodeCoverage\Report\PHP;

//class StatProductController extends BaseController

/**
 *
 */
class EstimateController extends CommonController
{

    public function __construct() {
        parent::__construct();
    }


    /**
     *
     *
     * @return JsonResponse
     * <AUTHOR> 2025-07-23 17:12:33
     *
     */
    public function getEstimateIncomeList(){
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        $y = date('Y');

        try {

            $key  = 'dashboard_mobile_estimate_income_target';
            $estimate_income_target = FlexConfig::getConfig($key);
            $estimate_income_target = $estimate_income_target[$y] ?? $estimate_income_target['2025'];
            $weekly_avg_money = round(($estimate_income_target / 53)/10000,2);

            $sum_real_money = WeekIncomeEstimate::getAllYearData($y);

            $current_week = 1;

            $weeks          = [];
            $income_data    = [];
            $estimated_data = [];
            $daily_avg_data = [];

            foreach ($sum_real_money as $item) {
                $weeks[]          = $item->week_number;
                $income_data[]    = $item->real_money == 0 ? '' : round($item->real_money/10000,0);
                $estimated_data[] = $item->estimate_money == 0 ? '' : round($item->estimate_money/10000,0);
                $daily_avg_data[] = round($item->avg_money/10000,0);
                $current_week = max($current_week,$item->status == 1 ? $item->week_number : 0);
            }

            $data = [
                'weeks'           => $weeks,
                'income_data'     => $income_data,
                'estimated_data'  => $estimated_data,
                'daily_avg_data'  => $daily_avg_data,
                'current_week'    => $current_week,
                'current_year'    => $y,
                'weekly_avg_money'=> $weekly_avg_money,
            ];

            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }


    /**
     * 预估收入
     *
     * @return JsonResponse
     * <AUTHOR> 2025-07-24 15:39:39
     *
     */
    public function getEstimateKPI() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            $y = date('Y');

            $key  = 'dashboard_mobile_estimate_kpi';
            $data = FlexConfig::getConfig($key);
            $data = $data[$y] ?? $data['2025'];

            $month             = [];
            $month_estimate    = [];
            $salesman_estimate = [];
            $real_money        = [];
            foreach ($data as $k => $v) {
                //获取每月的真实收入
                $true_income = MainRepository::getIncomeByMonth($y, $v['fmt_month']);
                $month[]             = $v['fmt_month'].'月';
                $month_estimate[]    = $v['month_estimate'];
                $salesman_estimate[] = $v['salesman_estimate'];
                $real_money[]        = $true_income == 0 ? '' : round(bcdiv($true_income,10000,6),0);//$v['real_money'];
            }

            $data = [
                'month'             => $month,
                'month_estimate'    => $month_estimate,
                'salesman_estimate' => $salesman_estimate,
                'real_money'        => $real_money,
                'current_month'     => date('n'),
            ];

            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }

    /**
     * 预估回款
     *
     * @return JsonResponse
     * <AUTHOR> 2025-07-25 17:43:41
     *
     */
    public function getEstimateRemit() {
        $valid = $this->isValidMobileToken();
        if($valid !== true){
            return $this->response($valid['status'], $valid['data'], $valid['msg']);
        }

        try {
            $y = date('Y');

            $key  = 'dashboard_mobile_estimate_remmit';
            $data = FlexConfig::getConfig($key);
            $data = $data[$y] ?? $data['2025'];

            $month             = [];
            $salesman_estimate = [];
            $real_remit_money  = [];
            foreach ($data as $k => $v) {
                $true_remit = Remit::getRemitByMonth($y, $v['fmt_month']);
                $month[]             = $v['fmt_month'].'月';
                $salesman_estimate[] = $v['salesman_estimate'];
                $real_remit_money[]  = $true_remit == 0 ? '' : round(bcdiv($true_remit,10000,6),0);//$v['real_money']; $v['real_remit_money'];
            }

            $data = [
                'month'             => $month,
                'salesman_estimate' => $salesman_estimate,
                'real_remit_money'  => $real_remit_money,
                'current_month'     => date('n'),
            ];

            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

}
