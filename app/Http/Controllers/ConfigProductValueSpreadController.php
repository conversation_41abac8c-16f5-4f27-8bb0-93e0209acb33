<?php

namespace App\Http\Controllers;

use App\Http\Repository\ConfigProductValueSpread;
use App\Models\Account;
use App\Models\Channel;
use App\Models\Product;
use Illuminate\Http\Request;

//class ConfigProductValueSpreadController extends BaseController
class ConfigProductValueSpreadController extends CommonController
{
    protected $repository;

    public function __construct()
    {
        parent::__construct();
        $select = Request()->header('select','');
        if ($select == 'true'){
            $select = true;
        }else{
            $select = false;
        }
        $this->repository = new ConfigProductValueSpread($select);
    }
    /**
     * 产品监控-查得率
     */
    public function spreadList()
    {
        try {
            $data = $this->repository->spreadList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 产品监控-查得率
     */
    public function spreadInfo()
    {
        try {
            $data = $this->repository->spreadInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品监控-查得率-修改
     */
    public function spreadEdit()
    {
        try {
            $data = $this->repository->spreadEdit();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品监控-查得率-添加
     */
    public function spreadAdd()
    {
        try {
            $data = $this->repository->spreadAdd();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 获取下拉选择框数据
     */
    public function getOptions()
    {
        //获取账号 产品 渠道
        try {
            $data = $this->repository->getOptions();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }

    /*
     * 获取渠道阈值列表
     */
    public function getConfigProductValueSpread()
    {
        try{
            $data = $this->repository->getConfigProductValueSpread();
            return $this->response(0, $data);
        }catch (\Exception $exception){
            return $this->response(100, [], $exception->getMessage());
        }

    }
    /**
     * 根据产品获取配置产品值分布
     */
    public function getProductValueSpreadByPid()
    {
        try {
            $data = $this->repository->getConfigProductValue();
            return  $this->response(0,$data);
        }catch (\Exception $exception){
            return $this->response(100,[],$exception->getMessage());
        }
    }

    /*
     * 添加值分布配置阈值
     */
    public function addConfigProductValueSpread()
    {
        try {
          $res =  $this->repository->addConfigProductValueSpread();
          if ($res){
              return  $this->response(0,[],'添加成功');
          }else{
              return  $this->response(1,[],'添加失败');
          }
        }catch (\Exception $exception){
            return $this->response(100,[],$exception->getMessage());
        }
    }

    /*
     * 修改值分布配置阈值
     */
    public function updateConfigProductValueSpread()
    {
        try {
            $res =  $this->repository->updateConfigProductValueSpread();
            if ($res){
                return  $this->response(0,[],'修改成功');
            }else{
                return  $this->response(1,[],'修改失败');
            }
        }catch (\Exception $exception){
            return $this->response(100,[],$exception->getMessage());
        }
    }
}
