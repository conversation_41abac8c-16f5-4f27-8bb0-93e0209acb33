<?php

namespace App\Http\Controllers\BxfStrategy;

use App\Http\Controllers\CommonController;
use App\Http\Repository\BxfStrategyRepository;
use Exception;
use Illuminate\Http\JsonResponse;

class StrategyController extends CommonController
{
    protected $repository;

    public function __construct() {
        parent::__construct();
        $this->repository = new BxfStrategyRepository();
    }


    /**
     * 获取策略列表
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 11:36:17
     *
     */
    public function strategyList() {
        try {
            $data = $this->repository->strategy_list();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 获取策略名称列表
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-11 16:00:24
     *
     */
    public function strategyNameList() {
        try {
            $data = $this->repository->strategy_name_list();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 添加策略
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 14:55:23
     *
     */
    public function add() {
        try {
            $data = $this->repository->add();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 策略详情
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 14:57:57
     *
     */
    public function info() {
        try {
            $data = $this->repository->info();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 策略编辑
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 14:58:08
     *
     */
    public function edit() {
        try {
            $data = $this->repository->edit();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 删除策略
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 14:58:08
     *
     */
    public function del() {
        try {
            $data = $this->repository->del();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 获取策略产品配置
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-09 18:46:34
     *
     */
    public function scoreOptions(){
        $score_map = [
            ['score_id' => 61001, 'score_product_name' => '号码风险等级评分03'],
            ['score_id' => 10118, 'score_product_name' => '标准分18'],
        ];
        return $this->response(0, $score_map);
    }
}
