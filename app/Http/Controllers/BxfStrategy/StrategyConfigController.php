<?php

namespace App\Http\Controllers\BxfStrategy;

use App\Http\Controllers\CommonController;
use App\Http\Repository\BxfStrategyConfigRepository;
use Exception;
use Illuminate\Http\JsonResponse;

class StrategyConfigController extends CommonController
{
    protected $repository;

    public function __construct() {
        parent::__construct();
        $this->repository = new BxfStrategyConfigRepository();
    }


    /**
     * 获取策略列表
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 11:36:17
     *
     */
    public function strategyConfigList() {
        try {
            $data = $this->repository->strategy_config_list();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 添加策略
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 14:55:23
     *
     */
    public function add() {
        try {
            $data = $this->repository->add();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 策略详情
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 14:57:57
     *
     */
    public function info() {
        try {
            $data = $this->repository->info();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 策略编辑
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-08 14:58:08
     *
     */
    public function edit() {
        try {
            $data = $this->repository->edit();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    /**
     * 删除策略配置
     *
     * @return JsonResponse
     * <AUTHOR> 2024-01-12 00:00:00
     *
     */
    public function del() {
        try {
            $data = $this->repository->del();
            return $this->response(0, $data);
        } catch (Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
}
