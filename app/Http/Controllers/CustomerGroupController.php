<?php
namespace App\Http\Controllers;

use App\Repositories\Customer\CustomerGroupRepositorie;
use Illuminate\Http\JsonResponse;
use Exception;

class CustomerGroupController extends CommonController
{

    private $repository;

    /**
     *
     */
    public function __construct(CustomerGroupRepositorie $repository){
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 获取客户开票信息
     * @return JsonResponse
     */
    public function list(){
        try {
            $list = $this->repository->list();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getFile().$e->getLine().$e->getMessage());
            // return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取客户开票信息
     * @return JsonResponse
     */
    public function groupMap(){
        try {
            $list = $this->repository->group_map();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getFile().$e->getLine().$e->getMessage());
            // return $this->response(100, [], $e->getMessage());
        }
    }



    /**
     * 添加
     * @return JsonResponse
     */
    public function add(){
        try {
            $list = $this->repository->add();
            return $this->response(0,$list);
        } catch (Exception $e) {
            // return $this->response(100, [], $e->getFile().$e->getLine().$e->getMessage());
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 修改
     * @return JsonResponse
     */
    public function edit(){
        try {
            $list = $this->repository->edit();
            return $this->response(0,$list);
        } catch (Exception $e) {
            // return $this->response(100, [], $e->getFile().$e->getLine().$e->getMessage());
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function editGroupCustomerList(){
        try {
            $list = $this->repository->edit_group_customer_list();
            return $this->response(0,$list);
        } catch (Exception $e) {
            // return $this->response(100, [], $e->getFile().$e->getLine().$e->getMessage());
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function groupCustomerList(){
        try {
            $list = $this->repository->group_customer_list();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getFile().$e->getLine().$e->getMessage());
            // return $this->response(100, [], $e->getMessage());
        }
    }
}