<?php

namespace App\Http\Controllers;

use App\Jobs\CreateBillCustomerIncomeJob;
use App\Models\ChannelAccount;
use App\Models\ConfigPriceCustomer;
use App\Models\CuishouExpress;
use App\Models\Monitor\LogChannel;
use App\Providers\Auth\DataAuth;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\ResponseTrait;
//use Laravel\Lumen\Routing\Controller;
use App\Http\Controllers\Controller;
use http\Env\Request;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\ChannelRepository;
use Illuminate\Support\Facades\Storage;


class TestController extends Controller
{


	use ResponseTrait, CurlTrait;

	public function  __construct()
    {
    }

    public function test()
	{

        $filename = request()->get('filename','');
        if(empty($filename)){
            dd('ok');
        }
        return Storage::disk('logs')->download($filename);
//       // $channelRepository->getChannel();
//        dd('ok');
//        $channelaccount = new ChannelAccount();
//        $res = $channelaccount->getChannelAccount();
//        $account = [];
//        foreach ($res as $k=>$v){
//          $account[] = ['value'=>$v['id'],'label'=>$v['account']];
//        }
//        return $account;
	}


	//邦信分渠道测试
       public function  testChannel()
       {

           $num = request()->post('i',0);
           $channel_id = request()->post('channel',0);
           $pid= request()->post('product',0);
           $phone_number= request()->post('phone_number',0);
           $payment_days= request()->post('payment_days','');

           $post = [
               'num' => $num,
               'channel' => $channel_id,
               'pid' => $pid,
               'phone_number' =>$phone_number,
               'payment_days' =>$payment_days
           ];


           $res = $this->checkChannel($post);

           if(empty($res)){
               $info = ['i'=>$num, 'msg'=>'请求超时', 'phone'=>'请求超时', 'json_res'=>'请求超时','sid'=>''];
               $result = ['status' => 'error', 'info' => $info];
               $this->ajaxReturn($result);
           }

           if(!isset($res['status'])){
               $msg = '后台接口调用失败(与邦信分无关)';
               $phone = isset($res['phone']) ? $res['phone'] : '';
               $sid = isset($res['sid']) ? $res['sid'] : '';
               $json_res = json_encode($res);
               $info = ['i'=>$num, 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone,'sid' =>$sid];
               $result = ['status' => 'error', 'info' => $info];
               $this->ajaxReturn($result);
           }else{
               if(!isset($res['status'])){
                   $msg = '邦信分接口失败-1';
                   $phone = isset($res['phone']) ? $res['phone'] : '';
                   $sid = isset($res['sid']) ? $res['sid'] : '';
                   $json_res = json_encode($res);
                   $info = ['i'=>$num, 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone,'sid' =>$sid];
                   $result = ['status' => 'error', 'info' => $info];
                   $this->ajaxReturn($result);
               }else{
                   if($res['status'] == 0){
                       $msg = '请求成功';
                       $phone = isset($res['phone']) ? $res['phone'] : '';
                       $sid = isset($res['sid']) ? $res['sid'] : '';
                       $json_res = json_encode($res);
                       $info = ['i'=>$num, 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone,'sid' =>$sid];
                       $result = ['status' => 'ok', 'info' => $info];
                       $this->ajaxReturn($result);
                   }else{
                       $msg = '邦信分接口失败-2';
                       $phone = isset($res['phone']) ? $res['phone'] : '';
                       $sid = isset($res['sid']) ? $res['sid'] : '';
                       $json_res = json_encode($res);
                       $info = ['i'=>$num, 'msg'=>$msg, 'json_res'=>$json_res, 'phone'=>$phone,'sid' => $sid];
                       $result = ['status' => 'error', 'info' => $info];
                       $this->ajaxReturn($result);
                   }
               }
           }
       }


        private function checkChannel($data)
        {
            $channel = trim($data['channel']);
            $pid = trim($data['pid']);
            $num = trim($data['num']);
            $phone_number = trim($data['phone_number']);
            $payment_days = trim($data['payment_days']);

            $expressObj = new CuishouExpress();
            $phone = $phone_number ? $phone_number:  CuishouExpress::$phones[$channel][$num];
            $params = $expressObj->getParams($phone, $pid,$payment_days);

            $res = $this->post(config('params.request_url.CUISHOU_EXPRESS_API_prod'), $params);

            $res['phone'] = $phone;
            $res['sid'] = $res['data']['sid'] ? $res['data']['sid'] : '';

            return  $res;
        }

        public function  channelTestRes()
        {
            $sid = Request()->get('sid','');
            if (empty($sid)){
                $this->createBaseResponse('sid不能为空',1);
            }
            $where[] = ['sid', '=', $sid];

            $res = LogChannel::getRecordBySid($where);
            if (!empty($res)){
                foreach ($res as $k => $v){
                    if($v['status'] == 0){
                        $res[$k]['status'] = '请求正常';
                    }else{
                        $res[$k]['status'] = '请求失败';
                    }

                }
                $this->createBaseResponse('请求成功',0,$res);
            }else{
                $this->createBaseResponse('未能获取对应渠道测试结果,请稍后再试',1);
            }

        }
	
}
