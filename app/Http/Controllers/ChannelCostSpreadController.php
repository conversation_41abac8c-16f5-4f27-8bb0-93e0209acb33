<?php

namespace App\Http\Controllers;

use App\Http\Repository\ChannelCostSpreadRepository;
use App\Models\SystemSession;

/**
 *  渠道成本分摊计算任务
 * @package App\Http\Controllers
 */
class ChannelCostSpreadController extends CommonController
{
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new ChannelCostSpreadRepository();
	}

    public function getSpreadList()
    {
        try {
            $data = $this->repository->getSpreadList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }
	
	public function saveSpreadRecord()
    {
        try {
			$session_id = request()->post('user_cookie','');
            $data = $this->repository->saveSpreadTask((new SystemSession())->getNameBySessionId($session_id));
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

	public function deleteSpreadRecord(){
		try {
			$run_status = $this->repository->getRunStatus();
			if(!in_array($run_status,[1,3,4])){// 不能删除计算中
				return $this->response(1,[],'任务状态不允许删除');
			}
			$session_id = request()->post('user_cookie','');
            $data = $this->repository->deleteSpreadRecord((new SystemSession())->getNameBySessionId($session_id));
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
	}

    public function getSpreadDetail(){
        try {
            $data = $this->repository->getSpreadDetail();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }
	
}