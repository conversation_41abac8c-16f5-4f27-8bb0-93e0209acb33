<?php
namespace App\Http\Controllers;

// use App\TraitUpgrade\ResponseTrait;
use Illuminate\Http\JsonResponse;
use App\Repositories\Invoice\InvoiceRepositorie;
use Exception;

class InvoiceController extends CommonController
{
    // use ResponseTrait;

    private $repository;

    /**
     *
     */
    public function __construct(InvoiceRepositorie $repository){
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 获取商务所有的客户的产品
     * @return JsonResponse
     */
    public function options(){
        try {
            $list = $this->repository->options();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取商务所有的客户的产品
     * @return JsonResponse
     */
    public function customerList(){
        try {
            $list = $this->repository->customerList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 获取客户的开票信息列表
     * @return JsonResponse
     */
    public function invoiceInformation(){
        try {
            $list = $this->repository->customerInvoiceInformation();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取客户的开票信息列表
     * @return JsonResponse
     */
    public function InvoiceCompanyList(){
        try {
            $list = $this->repository->InvoiceCompanyList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 获取客户未开票列表
     * @return JsonResponse
     */
    public function unappliedList(){
        try {
            $list = $this->repository->unappliedList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 获取客户未开票消耗列表
     * @return JsonResponse
     */
    public function unappliedConsumeList(){
        try {
            $list = $this->repository->unappliedConsumeList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取客户未开票消耗列表
     * @return JsonResponse
     */
    public function unappliedReceiptList(){
        try {
            $list = $this->repository->unappliedReceiptList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }



    /**
     * 获取客户回款列表
     * @return JsonResponse
     */
    public function receiptList(){
        try {
            $list = $this->repository->receiptList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }



    /**
     * 获取所有发票
     * @return JsonResponse
     */
    public function consumeInfoList(){
        try {
            $list = $this->repository->consumeInfoList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

    /**
     * 获取所有发票
     * @return JsonResponse
     */
    public function invoiceList(){
        try {
            $list = $this->repository->invoiceList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

    /**
     * 获取发票详情
     * @return JsonResponse
     */
    public function invoiceInfo(){
        try {
            $list = $this->repository->invoiceInfo();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }


    public function apply(){
        try {
            $list = $this->repository->apply();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    public function cancel(){
        try {
            $list = $this->repository->cancel();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    public function void(){
        try {
            $list = $this->repository->void();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    public function reject(){
        try {
            $list = $this->repository->reject();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function pass(){
        try {
            $res = $this->repository->pass();
            return $this->response(0,$res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function batchPass(){
        try {
            $res = $this->repository->batchPass();
            return $this->response(0,$res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function issueByExcel(){
        try {
            $list = $this->repository->issueByExcel();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function flush(){
        try {
            $list = $this->repository->flush();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    public function relate() {
        try {
            $list = $this->repository->relate();
            return $this->response(0, $list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function addExpressNo(){
        try {
            $res = $this->repository->addExpressNo();
            return $this->response(0,$res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function collect(){
        try {
            $list = $this->repository->collect();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 费客户发票明细列表
     * @return JsonResponse
     */
    public function invoiceDetailList(){
        try {
            $list = $this->repository->invoiceDetailList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 发票结余合计
     * @return JsonResponse
     */
    public function uninvoiceMoneyCount(){
        try {
            $list = $this->repository->uninvoiceMoneyCount();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }



    /**
     * 获取客户/渠道未关联回款的发票列表 返回公司名 金额 流水号
     * @return JsonResponse
     */
    public function unrelInvoiceList(){
        try {
            $list = $this->repository->unrelInvoiceList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 认票 选择发票后展示
     * 获取发票对应的消耗 以及客户当月消耗
     *
     * @return JsonResponse
     */
    public function invoiceConsumeList(){
        try {
            $list = $this->repository->invoiceConsumeList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }


    /**
     * 获取先票后款开票客户合同列表
     *
     * @return JsonResponse
     * <AUTHOR> 2024-10-14 19:20:40
     *
     */
    public function contractList(){
        try {
            $list = $this->repository->contractList();
            return $this->response(0,$list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }




    /**
     * 开票时辅助勾选金额
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-02-29 17:35:27
     *
     */
    public function autoCheck(){
        try {
            $list = $this->repository->auto_check();
            return $this->response(0, $list);
        } catch (Exception $e) {
            // return $this->response(100, [], $e->getMessage());
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }


    /**
     * 更新客户开票消耗
     *
     * @return JsonResponse
     * <AUTHOR> 2025-04-02 18:15:38
     *
     */
    public function consumeUpdate(){
        try {
            $list = $this->repository->consume_update();
            return $this->response(0, $list);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }
}