<?php

namespace App\Http\Controllers;

use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Jobs\CreateLogsJob;
use App\Jobs\StatisticsCustomerUsageJob;
use App\Models\CommonInfoModel;
use App\Providers\RedisCache\RedisCache;

/**
 * Class StatisticsCustomerController 客户统计数据的各类接口（控制器）
 *
 * @package App\Http\Controllers
 */
//class StatisticsCustomerController extends Controller
class StatisticsCustomerController extends CommonController
{
	/**
	 * 接收各产品组发送的客户调用量统计数据，校验并入库
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/8/26 14:54
	 *
	 * @return void
	 */
	public function sendUsageStatistics()
	{
		//校验数据
		$data = $this->checkUsageStatistics();
		
		//获取传递的数据
		$node       = $data['node'];
		$product_id = $data['product_id'];
		$date       = $data['date'];
		$data       = array_get($data, 'data', []);
		if (empty($data)) {
			$this->response('11009');
		}
		
		//遍历异步入库
		array_walk($data, function ($item) use ($node, $product_id, $date) {
			$item = array_merge($item, compact('node', 'product_id', 'date'));
			
			//(new StatisticsCustomerUsageJob($item))->handle();
			dispatch((new StatisticsCustomerUsageJob($item)));
		});
		
		$this->response('0');
	}
	
	/**
	 * 客户调用量统计数据接口中数据校验
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/8/26 15:34
	 *
	 * @return array
	 */
	protected function checkUsageStatistics()
	{
		$data = $this->getJsonByRaw();
		
		//校验产品数据
		$productId = array_get($data, 'product_id');
		if (!$productId) {
			$this->response('11001');
		}
		$productKey = array_get($data, 'product_key');
		if (!$productKey) {
			$this->response('11002');
		}
		if ($productKey != (new CacheDriverFacade())->getProductKeyByProductId($productId)) {
			$this->response('11003');
		}
		
		//校验节点
		$node = array_get($data, 'node');
		if (!$node) {
			$this->response('11004');
		}
		if (!in_array($node, config('params.list_nodes'))) {
			$this->response('11005');
		}
		
		//校验日期
		$date = array_get($data, 'date');
		if (!$date) {
			$this->response('11006');
		}
		if (!preg_match('/^\d{8}$/', $date)) {
			$this->response('11007');
		}
		
		//当日的数据允许在第二天的4:20之前重复推送，如果推送的是之前的数据，需要根据数据库的配置项，来确认是否允许推送历史统计
		$datetime = strtotime($date);
		if ($datetime + 102000 < time()) {
			//确认是否允许推送历史统计数据
			if (!$this->allowHistory($productId, $date)) {
				$this->response('11008');
			}
		}
		
		return $data;
	}
	
	/**
	 * 确认是否允许历史数据推送
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/12 10:06
	 *
	 * @param $product_id integer 产品ID
	 * @param $date       integer 日期
	 *
	 * @return boolean
	 */
	private function allowHistory($product_id, $date)
	{
		$config = CommonInfoModel::where('id', 3)
								 ->value('content');
		$config = @json_decode($config, true);
		
		if (empty($config)) {
			return false;
		}
		
		//确认该产品是否允许
		$father_id = RedisCache::instance('productId_fatherId_mapping')
							   ->get($product_id) ?: $product_id;
		
		$allowProductIds = array_get($config, 'product_ids', []);
		$allowProductIds = is_array($allowProductIds) ? $allowProductIds : [];
		//如果父产品、子产品均不允许，则证明不能推送历史的数据
		if (!in_array($product_id, $allowProductIds) && !in_array($father_id, $allowProductIds)) {
			return false;
		}
		
		//确认该时间是否允许
		$allowDate = array_get($config, 'date');
		if (empty($date)) {
			return false;
		}
		
		//是否为区间格式
		if ('between' == substr($allowDate, 0, 7)) {
			//使用的是区间格式：between:20200101,20200130
			$allowDate = explode(',', array_last(explode(':', $allowDate)));
			$min       = min($allowDate);
			$max       = max($allowDate);
			if ($date >= $min && $date <= $max) {
				return true;
			}
		} else {
			//使用的是单独匹配的格式：20200101,20200102
			$allowDate = explode(',', $allowDate);
			if (empty($allowDate)) {
				return false;
			}
			if (in_array($date, $allowDate)) {
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * 创建响应
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/21 16:18
	 *
	 * @param $code integer 响应code值
	 *
	 * @return void
	 */
	protected function response($code)
	{
		//记录日志
		$this->createLog($code);
		
		//创建响应
		$this->createSimpleResponse('customer_usage.' . $code);
	}
	
	/**
	 * 记录日志
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/21 16:09
	 *
	 * @param $code integer 响应code值
	 *
	 * @return void
	 */
	protected function createLog($code)
	{
		$ip               = request()->ip();
		$request_datetime = intval(date('YmdHis'));
		$response_code    = intval($code);
		$data             = $this->getJsonByRaw();
		$product_id       = array_get($data, 'product_id');
		$node             = array_get($data, 'node');
		$date             = intval(array_get($data, 'date'));
		
		$data = compact('product_id', 'node', 'date', 'ip', 'request_datetime', 'response_code', 'data');
		dispatch(new CreateLogsJob('MongoLogsStatisticsCustomerUsage', $data));
	}
	
	
}