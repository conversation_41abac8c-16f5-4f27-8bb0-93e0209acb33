<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/14 0014
 * Time: 16:13
 */

namespace App\Http\Controllers;


use App\Models\Config;

//class CommonConfig extends BaseController
class CommonConfig extends CommonController
{
    protected $configModel;
    protected $map = [
        1 => ['fbaace1340a8706863ed6ae17560355c'],
        2 => ['99bbdb8426f8b4e8d0cc3ebd92484590']
    ];

    public function __construct()
    {
        parent::__construct();
        $this->configModel = new Config();
    }

    public function getConfigInfo()
    {
        $type = request()->post('type', 0);
        if (empty($type) || !array_key_exists($type, $this->map)) {
            return $this->errorJson(1001, '不存在此配置类型');
        }

        //校验与之对应的key是否正确
        $allowKey = $this->map[$type];
        $key      = request()->post('key', '');
        if (!in_array($key, $allowKey)) {
            return $this->errorJson(1002, '不能通过此key调用此接口');
        }

        $config = $this->configModel->where('id', $type)->pluck('config')->toArray();
        return $this->successJson(json_decode($config[0], true));
    }

    protected function successJson($data, $status = 0, $msg = '')
    {
        return response()->json(compact('data', 'status', 'msg'));
    }

    protected function errorJson($status, $msg, $data = [])
    {
        return response()->json(compact('data', 'status', 'msg'));
    }
}