<?php

/**
 * @apiDefine UpstreamBillName 数据源账单
 **/

namespace App\Http\Controllers;


use App\Http\Repository\V3UpstreamRepository;

//class V3UpstreamBillController extends BaseController
class V3UpstreamBillController extends CommonController
{
    /**
     * @api                 {post} /bill/upstream/index
     * @apiName             数据源账单（列表）
     * @apiDescription      以客户维度、渠道维度、产品维度三个维度统计在指定时间范围内的上游数据源账单
     * @apiGroup            UpstreamBillName
     *
     * @apiParam {String} start_date 查询的开始时间，格式为Y-m-d
     * @apiParam {String} end_date 查询的截止时间，格式为Y-m-d
     * @apiParam {String} [customer_id] 删选客户（客户ID），不存在则查询所有
     * @apiParam {Number} [product_id] 筛选产品（产品ID），不存在则查询所有
     * @apiParam {String[]} [channel] 筛选渠道（渠道标记），为空或不存在则查询所有
     *
     * @apiSuccess {Number} status 状态标识码，code = 0
     * @apiSuccess {String} msg 错误信息
     * @apiSuccess {Object} data 返回的数据
     *
     * @apiSuccessExample   Success-Response:
     *      {
     *          "status":0,
     *          "msg":"",
     *          "data":[
     *              {
     *                  "product_id":210,
     *                  "customer_id":'',
     *                  "channel":"SCCMCC_yscs_phone_times_over_60s",
     *                  "all":2,
     *                  "failed":0,
     *                  "succ":2,
     *                  "yd":0,
     *                  "lt":0,
     *                  "dx":0
     *              }
     *          ]
     *      }
     *
     * @apiError {String} code 状态标识码，code!=0
     * @apiError {String} message 错误信息
     * @apiError {Object} data 返回的数据
     *
     * @apiErrorExample     Error-Response:
     *  {
     *      "status": 1001,
     *      "msg": "截止时间不存在或格式不正确",
     *      "data": []
     *  }
     */
    public function index()
    {
        $repository = new V3UpstreamRepository();

        try {
            $data   = $repository->getBillData();
            $status = 0;
            $msg    = '';
        } catch (\Exception $exception) {
            $status = 1001;
            $msg    = $exception->getMessage();
            $data   = [];
        }
        return compact('status', 'msg', 'data');
    }
}