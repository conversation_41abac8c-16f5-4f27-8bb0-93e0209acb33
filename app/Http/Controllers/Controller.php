<?php

namespace App\Http\Controllers;

use Illuminate\Http\Exceptions\HttpResponseException;


/**
 * Class Controller 新版基础控制器
 * @package App\Http\Controllers
 */
class Controller extends \Laravel\Lumen\Routing\Controller
{
	/**
	 * @var array 通过RAW传递的JSON数据
	 */
	protected $rawData;
	
	public function __construct()
	{
	
	}
	
	/**
	 * 获取RAW方式传递的JSON数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/8/26 15:01
	 *
	 * @return array
	 */
	protected function getJsonByRaw()
	{
		if (is_null($this->rawData)) {
			$data = file_get_contents("php://input");
			if (!$data) {
				$this->createSimpleResponse('common.10001');
			}
			if (!is_json($data)) {
				$this->createSimpleResponse('common.10002');
			}
			$this->rawData = json_decode($data, true);
		}
		
		return $this->rawData;
	}
	
	/**
	 * 创建一个接口的数据响应，调用该方法后，后续的代码将不会执行
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/8/26 15:20
	 *
	 * @param $data mixed 数据
	 *
	 * @return void
	 */
	protected function createDataResponse($data)
	{
		$this->createBaseResponse('', 0, $data);
	}

	/*
	 * layui 表单分页数据展示 格式要求
	 */

    public function CreateLayuiResponse($data = [],$count = 0,$code = 0,$message = '')
    {
        $response = response()->json(compact('code', 'message','count', 'data'));
        throw new HttpResponseException($response);
    }
	
	/**
	 * 创建一个简单的响应，调用该方法后，系统将自动根据标识寻找提示信息，然后为用户返回响应，值得注意的是，执行后，后续的代码将不会执行
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/8/26 15:21
	 *
	 * @param $needle string 错误配置文件中的标识 参数为 common.10001
	 *
	 * @return void
	 */
	protected function createSimpleResponse($needle)
	{
		$needleArr = explode('.', $needle);
		$code      = array_pop($needleArr);
		$message   = config("response.{$needle}") ?: '您遇到了一个未知错误';
		
		$this->createBaseResponse($message, $code);
	}
	
	/**
	 * 创建一个接口的响应，调用该方法后，后续的代码将不会执行
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/8/26 15:04
	 *
	 * @param $message string 信息
	 * @param $code    integer 错误标识码，为0代表成功
	 * @param $data    array 接口响应的额数据
	 *
	 * @return void
	 */
	protected function createBaseResponse($message = '', $code = 0, $data = [])
	{
		$status   = $code;
		$msg      = $message;
		$response = response()->json(compact('code', 'message', 'status', 'msg', 'data'));
		throw new HttpResponseException($response);
	}

    protected function ajaxReturn($data = [],$status = 'ok')
    {
        $status   = $status;
        $response = response()->json(compact( 'status',  'data'));
        throw new HttpResponseException($response);
    }


}