<?php

namespace App\Http\Controllers;


use App\Http\Repository\Receiver\PushReceiptRepository;

use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Http\JsonResponse;


/**
 * 获取其他系统推送数据控制器
 */
class ReceiverController extends CommonController
{
    /**
     * 接受财务系统推送的收款数据
     *
     *
     * @return JsonResponse
     * @throws Exception
     * <AUTHOR> 2024-06-17 17:06:07
     *
     */
	public function receipt_info() {
        try {
            $task_id = PushReceiptRepository::get_push_receipt();
            return response()->json([
                'msg'     => '已收到',
                'status'  => 0,
                'task_id' => $task_id,
            ]);
        } catch (Exception $e) {
            $msg = $e->getFile() . ' ' . $e->getLine() . ' ' . $e->getMessage();

            $feishu = new FeishuRepository();
            $mess = [];
            $feishu->send_card_message_to_chat_group('财务同步认款失败! 流水号:' . $msg,$mess);

            return response()->json([
                'msg'     => $msg,
                'status'  => 100,
            ]);
        }
    }
}