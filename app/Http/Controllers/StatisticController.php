<?php
/**
 * Created by PhpStorm.
 * User: liuchang
 * Date: 2018/11/26
 * Time: 2:18 PM
 */

namespace App\Http\Controllers;

use App\Jobs\SynStatJob;
use App\Models\MongoLog;
use App\Models\MongoStatis;
use App\Models\MongoStatLog;
use App\Models\Product;
use App\TraitUpgrade\WechatExceptionTrait;

//class StatisticController extends BaseController
class StatisticController extends CommonController
{
    use WechatExceptionTrait;
    const DAILY_OVER_TIME = 1859;

    /** @var int 每月一号允许昨天传入的控制时间是20分钟 */
    const DELAY_CONTROL_TIME = 1200;

    /** @var string redis链接 */
    private $redis_bill_connection = 'db_backend';

    /** @var string 微信预警计数器的key前缀 */
    private $prefix_key_stat_warning = 'prefix_key_stat_warning';

    /** @var int 一个产品一天预警的最大次数 */
    private $times_max_warning = 2;

    /** @var string 如果修改历史数据 希望明白自己在做啥 */
    private $heading_what_doing = 'i am trying to fix bug data';

    /** @var string 被遗弃的盐 */
    private $key_cacke_abandon_salt = 'key_abandon_salt_for_update_past_data';

    /** @var string 操作ID */
    private $uuid = '';

    /** @var array 允许的爬虫 */
    private $list_crawler = [
        'yulore'    => '羽乐',
        'moxie'     => '魔蝎',
        'xinde'     => '信德',
        'shujumohe' => '聚信立',
        'juxinli'   => '数聚魔盒',
        'none'      => '未知'
    ];

    /** @var array 允许填充的运营商 */
    private $list_operator = [
        'yidong'   => '移动',
        'liantong' => '联通',
        'dianxin'  => '电信',
        'none'     => '未知'
    ];


    /** @var array 102产品允许的爬虫 */
    private $list_report_crawler = [
        'bmp'  => '自由',
        'trd'  => '第三方',
        'none' => '未知',
    ];

    /** @var array 允许的省份 */
    private $list_province = [
        'shanghai'     => '上海',
        'yunnan'       => '云南',
        'neimenggu'    => '内蒙古',
        'beijing'      => '北京',
        'jilin'        => '吉林',
        'sichuan'      => '四川',
        'tianjin'      => '天津',
        'ningxia'      => '宁夏',
        'anhui'        => '安徽',
        'shandong'     => '山东',
        'shan1xi'      => '山西',
        'guangdong'    => '广东',
        'guangxi'      => '广西',
        'xinjiang'     => '新疆',
        'jiangsu'      => '江苏',
        'jiangxi'      => '江西',
        'hebei'        => '河北',
        'henan'        => '河南',
        'zhejiang'     => '浙江',
        'hainan'       => '海南',
        'hubei'        => '湖北',
        'hunan'        => '湖南',
        'gansu'        => '甘肃',
        'fujian'       => '福建',
        'xizang'       => '西藏',
        'guizhou'      => '贵州',
        'liaoning'     => '辽宁',
        'chongqing'    => '重庆',
        'shan3xi'      => '陕西',
        'qinghai'      => '青海',
        'heilongjiang' => '黑龙江',
        'none'         => '未知'
    ];

    /** @var string 统计入库 */
    private $action = 'stat write';

    /** @var array 特殊节点 */
    private $list_special_node = 'sandbox';

    /** @var array 支持特殊节点的产品 */
    private $list_special_node_product_id = [
        105,
        104,
        604
    ];

    /** @var string 特殊的apikey */
    private $apikey_special = 'a81e8602d594f50d91c3c4ae49bf11d3';

    /** @var bool 特殊产品是否成功 */
    private $determine_special_success = true;

    /**
     * 接受号码状态查询的统计信息
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function sendStatisInfo()
    {
        $this->uuid = \Ramsey\Uuid\Uuid::uuid4()
            ->toString();

        $rowData    = $this->data;
        $productId  = $rowData['product_id'] ?? 0;
        $amountDate = $rowData['amount_date'] ?? "";
        $nodeArea   = $rowData['node_area'] ?? "";
        $data       = $rowData['data'] ?? [];

        if ($this->determineParamsIsInvalid($productId, $amountDate, $nodeArea, $data)) {
            $this->_logTime('参数错误或者传递的字段都是0');
            return $this->_responseJust(1, '参数错误或者传递的字段都是0');
        }

        // 当月数据随意更新, 但是历史数据不允许修改
        // 如果是每月的1号,可以时间00:00 -- 00:10之间是可以接受对历史数据处理，主要是防止一些定时任务执行到了00:05的尴尬
        if (!$this->determineInsertDataOfThisMonth($productId, $amountDate)) {

            // 妄图修改历史数据
            if (!$this->determineUpdateYesterdayWhenFirstDayOfThisMonth($amountDate)) {
                $this->_logTime('不可以修改非本月数据');
                return $this->_responseJust(1, '不可以修改非本月数据');
            } else {
                // 如果是1号 但是如果不是在可控的时间范围内也是不可以的
                if (!$this->determineTheTimeInControl()) {
                    $this->_logTime('在每月1号的允许修改昨天的数据,但是请将时间控制在20分钟内');
                    return $this->_responseJust(1, '在每月1号的允许修改昨天的数据,但是请将时间控制在20分钟内');
                }
            }
        }

        //处理统计数据
        $data = $this->checkStatisBySet($productId, $data);
        if (!$data) {
            $this->_logTime('该产品统计配置为空');
            return $this->_responseJust(1, '该产品统计配置为空');
        }

        //写入redis
        foreach ($data as $apikey => $stat_data) {
            // 生成要统计的单元 && 分发JOB
            if ($this->canGoOn($apikey, $nodeArea)) {
                $item_job = $this->_genItem($stat_data, $apikey);
                dispatch((new SynStatJob($item_job))->onQueue(config('params.syn_stat_job')));
            }
        }

        // 修改历史数据 则预警
        $this->warningIfUpdateHistoryData();
        $this->_logTime('成功', true);
        return $this->_responseJust(0, '成功');
    }

    /**
     * 返回
     *
     * @param int    $status
     * @param string $msg
     *
     * @return \Illuminate\Http\JsonResponse
     */
    private function _responseJust(int $status, string $msg)
    {
        return response()->json(['status' => $status, 'msg' => $msg, 'log_id' => $this->uuid, 'data' => []]);
    }

    /**
     * 日志
     *
     * @param string $msg
     * @param bool   $success
     */
    private function _logTime(string $msg, $success = false)
    {
        MongoStatLog::create([
                'msg'         => $msg,
                'log_id'      => $this->uuid,
                'product_id'  => (int)trim(request()->post('product_id')),
                'node_area'   => trim(request()->post('node_area')),
                'amount_date' => date('Ymd', strtotime(request()->post('amount_date'))),
                'province'    => trim(request()->post('province')), // 省份
                'operator'    => trim(request()->post('operator')), // 运营商
                'crawler'     => trim(request()->post('crawler')), // 爬虫
                'action'      => $this->action,  // 动作
                'source'      => request()->post(),
                'success'     => $success
            ]);

    }

    /**
     * 生成统计单元
     *
     * @param array  $stat_data
     * @param string $apikey
     *
     * @return array
     */
    private function _genItem(array $stat_data, string $apikey): array
    {
        list($product_id, $amount_date, $node_area, $province, $operator, $crawler, $log_id) = [
            (int)request()->post('product_id'),
            date('Ymd', strtotime(request()->post('amount_date'))),
            trim(request()->post('node_area')),
            trim(request()->post('province')),
            trim(request()->post('operator')),
            trim(request()->post('crawler')),
            $this->uuid
        ];

        if ($this->_determineIsCrawler()) {
            return compact('product_id', 'amount_date', 'node_area', 'apikey', 'stat_data', 'province', 'operator',
                'crawler', 'log_id');
        }

        return compact('product_id', 'amount_date', 'node_area', 'apikey', 'stat_data', 'log_id');
    }

    /**
     * 是否是对应的特殊产品， 特殊节点， 特殊的账号的
     *
     * @param string $apikey
     * @param string $node
     *
     * @return bool
     * @throws \Exception
     */
    private function canGoOn(string $apikey, string $node): bool
    {
        if ($node != $this->list_special_node) {
            return true;
        }

        // 异常预警
        if (!$yep = $apikey == $this->apikey_special) {
            $this->determine_special_success = false;
            $this->wechatException('product_id : ' . implode(',',
                    $this->list_special_node_product_id) . ' node :' . $node . ' apikey :' . $apikey . ' 不是合法的apikey : ' . $this->apikey_special . ' source: ' . json_encode(request()->post(),
                    JSON_UNESCAPED_UNICODE));
        }
        return $yep;
    }


    /**
     * 参数是否不和合法
     *
     * @param mixed ...$list_params
     *
     * @return bool
     * @throws \Exception
     */
    private function determineParamsIsInvalid(...$list_params): bool
    {
        list($productId, $amountDate, $nodeArea, $data) = [
            $list_params[0],
            $list_params[1],
            strtolower($list_params[2]),
            $list_params[3]
        ];

        if (!$productId || !$amountDate || !$nodeArea || !$data) {
            return true;
        }

        // 如果节点不合法 则抛出异常
        if (!$this->determineNodeValid()) {
            $this->_logTime('统计字段推送节点异常, 推送节点');
            $this->wechatException('product: ' . $productId . ' 统计字段推送节点异常, 推送节点: ' . $nodeArea . ' msg : ' . json_encode($this->data,
                    JSON_UNESCAPED_UNICODE));
            return true;
        }

        // 301 302 102 产品是否包含必须的三种参数
        if ($this->_determineIsCrawler() && !$this->_crawlerHasNecessaryAttr()) {
            $this->_logTime('缺少必备的属性 province operator crawler');
            $this->wechatException('log_id: ' . $this->uuid . ' product_id : ' . $productId . ' province operator crawler 属性不合法');
            return true;
        }

        $valid = false;
        foreach ($data as $list_fields) {
            foreach ($list_fields as $value) {
                $valid = (bool)$value;
                if ($valid) {
                    break;
                }
            }
            if ($valid) {
                break;
            }
        }
        return !$valid;
    }

    /**
     * 是否是爬虫产品
     * @return bool
     */
    private function _determineIsCrawler(): bool
    {
        $product_id = trim(request()->post('product_id'));
        return in_array($product_id, [301, 302, 102]);
    }

    /**
     * 爬虫产品是否拥有必须的属性
     * @return bool
     */
    private function _crawlerHasNecessaryAttr(): bool
    {
        list($province, $operator, $crawler, $product_id) = [
            trim(request()->post('province')),
            trim(request()->post('operator')),
            trim(request()->post('crawler')),
            trim(request()->post('product_id')),
        ];

        // 301 302 需要校验
        if ($product_id != 102) {
            return array_key_exists($province, $this->list_province) && array_key_exists($operator,
                    $this->list_operator) && array_key_exists($crawler, $this->list_crawler);
        }

        // 如果是102 产品 则需要校验
        return array_key_exists($province, $this->list_province) && array_key_exists($operator,
                $this->list_operator) && array_key_exists($crawler, $this->list_report_crawler);
    }

    /*
     * 节点是否合法
     * @return bool
     */
    private function determineNodeValid(): bool
    {
        list($product_id, $node) = [
            (int)trim(request()->post('product_id')),
            trim(request()->post('node_area')),
        ];

        // 如果是特殊的产品 则
        return !in_array($product_id, $this->list_special_node_product_id) ? in_array($node,
            config('params.list_nodes')) : ($node == $this->list_special_node || in_array($node,
                config('params.list_nodes')));
    }

    /**
     * 修改历史数据 则预警
     * @throws \Exception
     */
    protected function warningIfUpdateHistoryData()
    {
        // 如果不需要微信预警
        if (!$this->determineWechatWarning()) {
            return;
        }

        // 如果是正在初始化的产品
        if ($this->_determineIsInitProduct()) {
            return;
        }

        //$msg = '历史数据修改预警, ' . PHP_EOL . 'product_id : ' . $this->data['product_id'] . ' 妄图修改' . $this->data['amount_date'] . '的数据' . PHP_EOL . ' msg : ' . json_encode($this->data);
        //$this->wechatException($msg);
    }

    /**
     * 是否是在初始化产品的数据统计
     * @return bool
     */
    private function _determineIsInitProduct(): bool
    {
        list($product_init_str, $product_id) = [
            env('THE_PRODUCT_WHICH_CAN_UPDATE_HISTORY'),
            trim(request()->post('product_id'))
        ];

        return strpos($product_init_str, $product_id) !== false;
    }

    /**
     * 是否发送微信预警
     * @return bool
     */
    protected function determineWechatWarning(): bool
    {
        // 要点参数
        list($date_amount, $date_today, $date_yesterday) = [
            date('Ymd', strtotime($this->data['amount_date'])),
            date('Ymd'),
            date('Ymd', strtotime('-1 day')),
        ];
        $time_diff = time() - strtotime($date_today);

        // 是否推送的今天的数据
        if ($date_amount == $date_today) {
            return false;
        }

        // 如果是昨天的且在十分钟内的也可以
        if ($date_amount == $date_yesterday && $time_diff <= self::DELAY_CONTROL_TIME) {
            return false;
        }

        // 如果预警太多也不再预警
        if ($this->determineWarningTooMuch()) {
            return false;
        }

        // 预警 && 设置计数器存活时间
        $this->setWarningKeyExpireTime();
        return true;
    }

    /**
     * 设置计数器存活时间
     */
    protected function setWarningKeyExpireTime()
    {
        $key_warning = $this->getWarningKey();

        // 如果存在的话 则不需要设置什么东西
        if (app('redis')
            ->connection($this->redis_bill_connection)
            ->exists($key_warning)
        ) {
            app('redis')
                ->connection($this->redis_bill_connection)
                ->incr($key_warning);
            return;
        }

        // 设置初始值 && 生存时间
        app('redis')
            ->connection($this->redis_bill_connection)
            ->set($key_warning, 1);

        $time_tomorrow = strtotime(date('Y-m-d', strtotime('+1 day')));
        app('redis')
            ->connection($this->redis_bill_connection)
            ->expireAt($key_warning, $time_tomorrow);
    }

    /**
     * @return bool
     */
    protected function determineWarningTooMuch(): bool
    {
        $key_warning    = $this->getWarningKey();
        $number_warning = (int)app('redis')
            ->connection($this->redis_bill_connection)
            ->get($key_warning);
        return $number_warning >= $this->times_max_warning;
    }

    /**
     * 获取本次计数器的key
     * @return string
     */
    private function getWarningKey(): string
    {
        list($product_id, $amount_date) = [
            $this->data['product_id'],
            date('Ymd', strtotime($this->data['amount_date']))
        ];
        return $this->prefix_key_stat_warning . '_' . $product_id . '_' . $amount_date;
    }

    /**
     * 即使是一号在修改昨天的数据 那时间控制在了20分钟内了吗？
     * @return bool
     * @throws \Exception
     */
    private function determineTheTimeInControl(): bool
    {
        // 如果是催收分私有云的话 不需要校验这一步伐
        if (trim(request()->post('product_id')) == 501) {
            $this->wechatException('在本月一号检测到501产品在更新昨天的数据 Time:' . date('Y-m-d H:i:s'));
            return true;
        }

        return (time() - strtotime(date('Y-m-d'))) < self::DELAY_CONTROL_TIME;
    }

    /**
     * 是在本月的一号修改昨天的信息吗
     *
     * @param string $amount_date
     *
     * @return bool
     */
    private function determineUpdateYesterdayWhenFirstDayOfThisMonth(string $amount_date): bool
    {
        if (date('d') != '01') {
            return false;
        }

        return strtotime($amount_date) == strtotime(date('Y-m-d', strtotime('last day of last month')));
    }

    /**
     * 是否插入的是今天的数据
     *
     * @param string $productId
     * @param string $amount_date
     *
     * @return bool
     * @throws \Exception
     */
    private function determineInsertDataOfThisMonth(string $productId, string $amount_date): bool
    {
        // 是否是新推送产品  是否是特殊修改的历史数据  是否是本月的数据 是否是特殊配置中允许的时间段内的特定产品的历史数据
        return $this->_determineIsThisMonth($amount_date) || $this->_determineNewProduct() || $this->determineIsSpecialRepair() || $this->determineAllowConfig($productId,
                $amount_date);
    }

    /**
     * 是否是特殊配置中允许的时间段内的特定产品的历史数据
     *
     * @access private
     *
     * @param $productId   string 产品ID
     * @param $amount_date string 日期
     *
     * @return boolean
     **/
    private function determineAllowConfig($productId, $amount_date)
    {
        //转化日期格式
        $amount_date    = date('Ymd', strtotime($amount_date));

        $allowProductId = env('ALLOW_HISTORY_DATA_SEND_PRODUCT_ID');
        //产品不再特殊配置内
        if (!in_array($productId, explode(',', $allowProductId))) {
            return false;
        }
        $allowAmountDate = env('ALLOW_HISTORY_DATA_SEND_AMOUNT_DATE');
        //多个日期的条件是否符合
        if (strpos($allowAmountDate, 'between:') === false) {
            return in_array($amount_date, explode(',', $allowAmountDate)) ? true : false;
        }
        //单个日期条件是否符合
        if (strpos($allowAmountDate, ':') === false) {
            return $allowAmountDate == $amount_date;
        }
        //日期区间是否符合
        list($flag, $section) = explode(':', $allowAmountDate);
        if ($flag != 'between' || empty($section) || strpos($section, ',') === false) {
            return false;
        }
        list($min, $max) = explode(',', $section);
        if (!empty($min) && $min > $amount_date) {
            return false;
        }
        if (!empty($max) && $max < $amount_date) {
            return false;
        }
        return true;
    }

    /**
     * 是否在更新本月的数据
     *
     * @param string $amount_date
     *
     * @return bool
     */
    private function _determineIsThisMonth(string $amount_date): bool
    {
        // 是否在修改本月的数据
        $amount_date = date('Y-m', strtotime($amount_date));
        $today       = date('Y-m');
        return $amount_date == $today;
    }

    /**
     * 是否是新产品
     */
    protected function _determineNewProduct(): bool
    {
        // 检查产品是否在允许的更新历史的产品列表中
        $product_id                       = trim(request()->post('product_id'));
        $product_which_can_update_history = env('THE_PRODUCT_WHICH_CAN_UPDATE_HISTORY');
        if (!$product_which_can_update_history) {
            return false;
        }

        $list_permissions = explode(',', $product_which_can_update_history);
        return in_array($product_id, $list_permissions);
    }

    /**
     * 是否是特殊的修复数据的请求
     * @return bool
     * @throws \Exception
     */
    private function determineIsSpecialRepair(): bool
    {
        // 判断月份
        $amount_date = request()->post('amount_date');
        list($month_update, $month_last, $sign, $salt) = [
            date('Y-m', strtotime($amount_date)),
            date('Y-m', strtotime('first day of last month')),
            request()->header('sign'),
            env('SLAT_UPDATE_PAST_DATA', 'i will update past data'),
        ];

        if ($month_last != $month_update) {
            return false;
        }

        // 检查是否明白自己在做啥
        if (request()->header('what-doing') != $this->heading_what_doing) {
            return false;
        }

        // 检查盐是否一致
        if ($salt != $sign) {
            return false;
        }

        // 检查是盐否已经被废弃了
        $abandon = app('redis')
            ->connection($this->redis_bill_connection)
            ->hExists($this->key_cacke_abandon_salt, $salt);

        // 如果校验通过 则存储新旧数据 && report
        !$abandon && $this->cacheLogAndReportIfUpdatePastData();
        return !$abandon;
    }

    /**
     * @throws \Exception
     */
    private function cacheLogAndReportIfUpdatePastData()
    {
        //  log and report
        $this->logAndReport();

        // 废弃盐
        $salt = env('SLAT_UPDATE_PAST_DATA', 'i will update past data');
        app('redis')
            ->connection($this->redis_bill_connection)
            ->hset($this->key_cacke_abandon_salt, $salt, true);
    }

    private function logAndReport()
    {
        list($product_id, $day) = [
            request()->post('product_id'),
            request()->post('amount_date'),
        ];

        // 微信预警
        $msg = 'product_id :' . $product_id . ' day : ' . $day . ' 正在通过后门修改历史数据';
        $this->wechatException($msg);

        // 新旧数据对比
        $data_old = $this->getOldDataForUpdatePast();
        $data_new = request()->post('data');
        $msg      = '后门更新上个月的数据';
        $type     = 'update-past';
        MongoLog::create(compact('data_new', 'data_old', 'product_id', 'day', 'msg', 'type'));
    }

    /**
     * 获取旧数据
     * @return mixed
     */
    private function getOldDataForUpdatePast()
    {
        // 条件组合
        list($product_id, $amount_date, $data, $node_area) = [
            (int)trim(request()->post('product_id')),
            date('Ymd', strtotime(request()->post('amount_date'))),
            request()->post('data'),
            trim(request()->post('node_area')),
        ];
        $list_apikeys = array_keys($data);

        // 查询
        $lists = MongoStatis::where(compact('product_id', 'amount_date', 'node_area'))
            ->whereIn('apikey', $list_apikeys)
            ->get();
        return $lists ? $lists->toArray() : [];
    }

    /**
     * 根据后台统计的配置处理用户传来的数据
     *
     * @param $productId
     * @param $data
     *
     * @return array|null
     */
    private function checkStatisBySet($productId, $data)
    {
        if (!$productId || !$data) {
            return null;
        }
        //获取该产品设置的字段
        $productInfo = Product::where("product_id", $productId)
            ->first()
            ->toArray();
        if (!$productInfo || !$productInfo['stat_config']) {
            return null;
        }

        $result          = [];
        $statConfigArray = json_decode($productInfo['stat_config'], true);

        foreach ($data as $k => $v) {
            $resultItem = [];
            foreach ($statConfigArray as $ck => $cv) {
                $resultItem[$cv['name']] = $this->_formatValue($cv['name'], $v, $productId);
            }
            $result[$k] = $resultItem;
        }

        return $result;
    }

    /**
     * @param string $field
     * @param array  $data_item
     * @param int    $product_id
     *
     * @return int
     */
    private function _formatValue(string $field, array $data_item, int $product_id): int
    {
        $value = $data_item[$field] ?? '';

        if ($product_id == 501 && trim($field) == 'status') {

            // 如果数值等于0 则取1, 如果数值是1则取0 否则取整形本身
            return $value == 0 ? 1 : ($value == 1 ? 0 : (int)$value);
        }

        return (int)$value;
    }

    /**
     * 日报
     */
    public function getDailyInfo()
    {
        //$rowJson = file_get_contents("php://input");
        $rowData   = $this->data;
        $productId = isset($rowData['product_id']) ? $rowData['product_id'] : 0;
        if ($productId <= 0) {
            return response()->json(['status' => 1, 'msg' => '该产品id为空', 'data' => []]);
        }

        $date      = date('Y-m-d', strtotime('-1 day'));
        $dailyData = MongoStatis::where(['product_id' => $productId, 'amount_date' => $date])
            ->get()
            ->toArray();
        if (!$dailyData) {
            return response()->json(['status' => 1, 'msg' => '失败', 'data' => []]);
        }

        //获取处理后的数据
        $result = $this->operateDailyInfo($productId, $dailyData);
        if (!$result) {
            return response()->json(['status' => 1, 'msg' => '失败', 'data' => []]);
        }

        return response()->json(['status' => 0, 'msg' => '成功', 'data' => $result]);
    }

    private function operateDailyInfo($productId, $data)
    {
        switch ($productId) {
            case 801:
                return $this->operatePhoneStatusByKey($data);
                break;
            default:
                return null;

        }
    }

    private function operatePhoneStatusByKey($data)
    {
        //将不同节点的数据加在一起
        $productData = [];
        foreach ($data as $k => $v) {
            $productDataInfo = json_decode($v['stat_data'], true);
            if (isset($productData[$v['apikey']])) {
                $productData[$v['apikey']]['total']           += $productDataInfo['total'];
                $productData[$v['apikey']]['errorPhoneNum']   += $productDataInfo['errorPhoneNum'];
                $productData[$v['apikey']]['correctPhoneNum'] += $productDataInfo['correctPhoneNum'];
                $productData[$v['apikey']]['getPhoneNum']     += $productDataInfo['getPhoneNum'];
                $productData[$v['apikey']]['usePhoneNum']     += $productDataInfo['usePhoneNum'];
                $productData[$v['apikey']]['stopPhoneNum']    += $productDataInfo['stopPhoneNum'];
                $productData[$v['apikey']]['noKnowPhoneNum']  += $productDataInfo['noKnowPhoneNum'];
            } else {
                $productData[$v['apikey']]['total']           = $productDataInfo['total'];
                $productData[$v['apikey']]['errorPhoneNum']   = $productDataInfo['errorPhoneNum'];
                $productData[$v['apikey']]['correctPhoneNum'] = $productDataInfo['correctPhoneNum'];
                $productData[$v['apikey']]['getPhoneNum']     = $productDataInfo['getPhoneNum'];
                $productData[$v['apikey']]['usePhoneNum']     = $productDataInfo['usePhoneNum'];
                $productData[$v['apikey']]['stopPhoneNum']    = $productDataInfo['stopPhoneNum'];
                $productData[$v['apikey']]['noKnowPhoneNum']  = $productDataInfo['noKnowPhoneNum'];
            }
        }
        foreach ($productData as $pk => &$pv) {
            $totalCorrectPhoneNum = $pv['usePhoneNum'] + $pv['stopPhoneNum'] + $pv['noKnowPhoneNum'];
            $usePhoneRatio        = round($pv['usePhoneNum'] / $totalCorrectPhoneNum, 4);
            $stopPhoneRatio       = round($pv['stopPhoneNum'] / $totalCorrectPhoneNum, 4);
            $noKnowPhoneRatio     = round($pv['noKnowPhoneNum'] / $totalCorrectPhoneNum, 4);

            $pv['usePhoneRatio']    = $usePhoneRatio * 100 . '%';
            $pv['stopPhoneRatio']   = $stopPhoneRatio * 100 . '%';
            $pv['noKnowPhoneRatio'] = $noKnowPhoneRatio * 100 . '%';
        }
        return $productData;
    }

    /**
     * 统计列表
     */
    public function getListInfo()
    {
        //$rowJson = file_get_contents("php://input");
        $rowData   = $this->data;
        $productId = isset($rowData['product_id']) ? $rowData['product_id'] : 0;
        $startTime = isset($rowData['start_time']) ? $rowData['start_time'] : "";
        $endTime   = isset($rowData['end_time']) ? $rowData['end_time'] : "";
        if ($productId <= 0 || !$startTime || !$endTime) {
            return response()->json(['status' => 1, 'msg' => '参数不全', 'data' => []]);
        }

        //处理时间
        $startTime = date('Y-m-d', strtotime($startTime));
        $endTime   = date('Y-m-d', strtotime($endTime));
        $listData  = MongoStatis::where("product_id", $productId)
            ->where("amount_date", '>=', $startTime)
            ->where("amount_date", '<=', $endTime)
            ->get()
            ->toArray();
        if (!$listData) {
            return response()->json(['status' => 1, 'msg' => '失败', 'data' => []]);
        }

        //获取处理后的数据
        $result = $this->operateListInfo($productId, $listData);
        if (!$result) {
            return response()->json(['status' => 1, 'msg' => '失败', 'data' => []]);
        }

        return response()->json(['status' => 0, 'msg' => '成功', 'data' => $result]);
    }

    /**
     * 处理统计列表数据
     *
     * @param $productId
     * @param $data
     *
     * @return array|null
     */
    private function operateListInfo($productId, $data)
    {
        switch ($productId) {
            case 801:
                return $this->operatePhoneStatusByKey($data);
                break;
            default:
                return null;

        }
    }

    /**
     * 统计详情
     */
    public function getDetailInfo()
    {
        //$rowJson = file_get_contents("php://input");
        $rowData   = $this->data;
        $productId = isset($rowData['product_id']) ? $rowData['product_id'] : 0;
        $startTime = isset($rowData['start_time']) ? $rowData['start_time'] : "";
        $endTime   = isset($rowData['end_time']) ? $rowData['end_time'] : "";
        if ($productId <= 0 || !$startTime || !$endTime) {
            return response()->json(['status' => 1, 'msg' => '该产品id为空', 'data' => []]);
        }

        //处理时间
        $startTime = date('Y-m-d', strtotime($startTime));
        $endTime   = date('Y-m-d', strtotime($endTime));
        $listData  = MongoStatis::where("product_id", $productId)
            ->where("amount_date", '>=', $startTime)
            ->where("amount_date", '<=', $endTime)
            ->get()
            ->toArray();
        if (!$listData) {
            return response()->json(['status' => 1, 'msg' => '失败', 'data' => []]);
        }

        //获取处理后的数据
        $result = $this->operateDetailInfo($productId, $listData);
        if (!$result) {
            return response()->json(['status' => 1, 'msg' => '失败', 'data' => []]);
        }

        return response()->json(['status' => 0, 'msg' => '成功', 'data' => $result]);
    }

    /**
     * 处理统计详情数据
     *
     * @param $productId
     * @param $data
     *
     * @return array|null
     */
    private function operateDetailInfo($productId, $data)
    {
        switch ($productId) {
            case 801:
                return $this->operatePhoneStatusByDate($data);
                break;
            default:
                return null;

        }
    }

    private function operatePhoneStatusByDate($data)
    {
        //将不同节点但日期相同的数据加在一起
        $productData = [];
        foreach ($data as $k => $v) {
            $productDataInfo = json_decode($v['stat_data'], true);
            $date            = date('Ymd', strtotime($v['amount_date']));
            if (isset($productData[$date])) {
                $productData[$date]['total']           += $productDataInfo['total'];
                $productData[$date]['errorPhoneNum']   += $productDataInfo['errorPhoneNum'];
                $productData[$date]['correctPhoneNum'] += $productDataInfo['correctPhoneNum'];
                $productData[$date]['getPhoneNum']     += $productDataInfo['getPhoneNum'];
                $productData[$date]['usePhoneNum']     += $productDataInfo['usePhoneNum'];
                $productData[$date]['stopPhoneNum']    += $productDataInfo['stopPhoneNum'];
                $productData[$date]['noKnowPhoneNum']  += $productDataInfo['noKnowPhoneNum'];
            } else {
                $productData[$date]['total']           = $productDataInfo['total'];
                $productData[$date]['errorPhoneNum']   = $productDataInfo['errorPhoneNum'];
                $productData[$date]['correctPhoneNum'] = $productDataInfo['correctPhoneNum'];
                $productData[$date]['getPhoneNum']     = $productDataInfo['getPhoneNum'];
                $productData[$date]['usePhoneNum']     = $productDataInfo['usePhoneNum'];
                $productData[$date]['stopPhoneNum']    = $productDataInfo['stopPhoneNum'];
                $productData[$date]['noKnowPhoneNum']  = $productDataInfo['noKnowPhoneNum'];
            }
        }

        foreach ($productData as $pk => &$pv) {
            $totalCorrectPhoneNum = $pv['usePhoneNum'] + $pv['stopPhoneNum'] + $pv['noKnowPhoneNum'];
            $usePhoneRatio        = round($pv['usePhoneNum'] / $totalCorrectPhoneNum, 4);
            $stopPhoneRatio       = round($pv['stopPhoneNum'] / $totalCorrectPhoneNum, 4);
            $noKnowPhoneRatio     = round($pv['noKnowPhoneNum'] / $totalCorrectPhoneNum, 4);

            $pv['usePhoneRatio']    = $usePhoneRatio * 100 . '%';
            $pv['stopPhoneRatio']   = $stopPhoneRatio * 100 . '%';
            $pv['noKnowPhoneRatio'] = $noKnowPhoneRatio * 100 . '%';
        }
        return $productData;
    }
}