<?php

namespace App\Http\Controllers;

use App\Http\Repository\LogRepository;
use App\TraitUpgrade\ResponseTrait;
use <PERSON><PERSON>\Lumen\Routing\Controller;
use App\Support\CustomException;

//class LogController extends Controller
class LogController extends CommonController
{
    use ResponseTrait;

    private $repository;

    /**
     * LogController constructor.
     * @param $repository
     */
    public function __construct(LogRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 统计字段入库日志
     * @return \Illuminate\Http\JsonResponse
     */
    public function logs()
    {
        try {
            $lists = $this->repository->logs();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        } catch (\Error $e){
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 日志列表查询
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $lists = $this->repository->index();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 统计单元列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistic()
    {
        try {
            $lists = $this->repository->statistic();
            return $this->response(compact('lists'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }
}
