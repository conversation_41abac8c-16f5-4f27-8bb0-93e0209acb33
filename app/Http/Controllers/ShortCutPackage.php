<?php

namespace App\Http\Controllers;


//class ShortCutPackage extends BaseController
class ShortCutPackage extends CommonController
{

    public function sendInfo()
    {
        try {
            $data = $this->checkRequest();

            $res = \App\Models\ShortcutPackage::updateOrCreate([
                'sid' => $data['sid']
            ], $data);
            if (!$res) {
                throw new \Exception('保存失败');
            }
            return ['status' => 0, 'msg' => 'success'];

        } catch (\Exception $exception) {
            $status = 1001;
            $msg = $exception->getMessage();
            return compact('status', 'msg');
        }
    }

    private function checkRequest()
    {
        if (request()->post('key') != '99bbdb8426f8b4e8d0cc3ebd92484590') {
            throw new \Exception('身份验证不通过');
        }
        $apikey       = $this->getPostParams('apikey');
        $sid          = $this->getPostParams('sid');
        $request_date = $this->getPostParams('request_date');

        if (!is_numeric($request_date)) {
            throw new \Exception('request_date格式不正确');
        }

        $normal_number = $this->getPostParams('normal_number');
        if (!is_numeric($normal_number)) {
            throw new \Exception('normal_number格式不正确');
        }

        $graded_number = $this->getPostParams('graded_number');
        if (!is_numeric($normal_number)) {
            throw new \Exception('graded_number格式不正确');
        }
        return compact('apikey', 'sid', 'request_date', 'normal_number', 'graded_number');
    }

    /**
     * 获取POST的参数
     *
     * @access private
     *
     * @param $field string 请求字段
     *
     * @return string
     **/
    private function getPostParams($field)
    {
        $value = request()->post($field, null);
        if (is_null($value)) {
            throw new \Exception("未找到数据内容 [{$field}]");
        }
        return $value;
    }

    /**
     * 创建快捷版打包表的model对象
     *
     * @access protected
     *
     * @return \App\Models\ShortcutPackage
     **/
    protected function getShortCutPackageModel()
    {
        return new \App\Models\ShortcutPackage();
    }
}