<?php

namespace App\Http\Controllers;



use App\Http\Controllers\Controller;
use App\Models\Monitor\ProductDuty;
use http\Env\Request;
use  App\Models\Monitor\Manager;
use  App\Models\Monitor\DutyOperateLog;

//class MonitorConfirmController extends Controller
class MonitorConfirmAllController extends Controller
{


	//use ResponseTrait, CurlTrait;

	public function  __construct()
    {
        parent::__construct();
        $this->dutyOperateLog = new DutyOperateLog();
        $this->productDuty = new ProductDuty();
    }

    //监控确认
    public function Confirm()
	{
        //获取值班人员信息
        $dutyUser = $this->productDuty->getDutyUser();
        if (empty($dutyUser)){
            $this->createBaseResponse('not find duty user',1);
        }

        $duty_model = ['ant_total','ant_in_time'];
        $nowtime = time();
        $add = [];
        foreach ($duty_model as $model){
            $row['mid'] = $dutyUser['mid'];
            $row['status'] = 0;
            $row['pid'] = 210;
            $row['model'] = $model;
            $row['time'] = date('Y-m-d H:i:s', $nowtime);
            $info = $this->dutyOperateLog->getLastRecrod($row);

            if(!empty($info)){
                $diff = $nowtime - strtotime($info['time']);
                //30分钟内无需过多操作
                if($diff < 1800){
                    continue;
                }
            }
            $add[] = $row;
        }

        if(empty($add)){
            $this->createBaseResponse('No need for frequent operation',1);
        }

        $res = $this->dutyOperateLog->add($add);
        if ($res){
            $this->createBaseResponse('成功',0);
        }else{
            $this->createBaseResponse('失败',1);
        }

	}

	protected function getUuid($username)
    {
        $uid = Manager::getUidByName($username);
        return $uid['id'];
    }
	
}
