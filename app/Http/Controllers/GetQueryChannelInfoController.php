<?php

namespace App\Http\Controllers;

use App\Jobs\CreateBillCustomerIncomeJob;
use App\Models\ChannelAccount;
use App\Models\ConfigPriceCustomer;
use App\Models\CuishouExpress;
use App\Models\Monitor\LogChannel;
use App\Providers\Auth\DataAuth;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\ResponseTrait;
//use Laravel\Lumen\Routing\Controller;
use App\Http\Controllers\Controller;
use http\Env\Request;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\ChannelRepository;


class getQueryChannelInfoController extends Controller
{

    public function test()
	{
       var_dump(1111);
       die;
	}


	
}
