<?php

namespace App\Http\Controllers;

use App\Jobs\ApiLogJob;
use App\Utils\Helpers\Func;
use <PERSON><PERSON>\Lumen\Routing\Controller as Controller;
use Mockery\Generator\Parameter;
use Symfony\Component\HttpFoundation\ParameterBag;

class BaseController extends Controller
{
    protected $data;

    //返回状态和变量 子类可以在此基础上重新定义
    protected $msg = [
        0 => '请求成功',
        1 => '请求失败',
        500 => '发生错误,请重试',
    ];

    public function __construct()
    {
        $json = file_get_contents("php://input");
        if (!is_json($json)) {
            return response()->json(['status' => 1, 'msg' => "参数有误", 'data' => []]);
        }
        $this->data = json_decode($json, true);
        $_POST = array_merge($_POST, $this->data);
        request()->request = new ParameterBag($_POST);
    }

    //获取IP段所在区域
    public function getNodeArea()
    {
        $client_ip = Func::getClientIp();
        $client_ip = substr($client_ip, 0, strrpos($client_ip, '.'));
        $node_area_ips = config('params.node_area_ips');
        $node_area = array_key_exists($client_ip, $node_area_ips) ? $node_area_ips[$client_ip] : 'other';
        return $node_area;
    }

    /**
     * @param int $status
     * @param array $data
     * @param string $msg
     * @return \Illuminate\Http\JsonResponse
     */
    protected function response($status = 0, $data = [], $msg = '')
    {
        $msg = $this->msg[$status] ?? $msg;
        $response = compact('msg', 'status', 'data');
        $this->addApiLog($response);
        return response()->json($response);
    }

    /**
     * <AUTHOR> g
     * @desc 记录用户日志,写入队列，并入库
     * @date 20181128
     */
    protected function addApiLog($response)
    {
//        $log = [
//            'ip' => Func::getClientIp(),
//            'log_user' => $this->data['product_id'] ?: 'system',
//            'api_name' => get_called_class(),
//            'is_success' => $response['status'] === 0 ? 0 : 1,
//            'node_area' => $this->data['node_area'],
//            'output_data' => json_encode($response),
//            'input_data' => $this->data,
//            'create_at' => time(),
//            'server' => $_SERVER
//        ];
        //dispatch((new ApiLogJob($log))->onQueue(config('params.api_log_job')));
        return true;
    }
}
