<?php

namespace App\Http\Controllers;

use App\Http\Repository\CorrectRepository;
use App\Support\CustomException;
use App\TraitUpgrade\ResponseTrait;
use Lara<PERSON>\Lumen\Routing\Router;

//class CorrectController extends Router
class CorrectController extends CommonController
{
    use ResponseTrait;
    private $repository;

    /**
     * CorrectController constructor.
     * @param $repository
     */
    public function __construct(CorrectRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 备注列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function comments($uuid)
    {
        try {
            $list= $this->repository->comments($uuid);
            return $this->response(compact('list'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 增加备注
     * @return \Illuminate\Http\JsonResponse
     */
    public function addComment()
    {
        try {
            $this->repository->addComment();
            return $this->response(['msg' => 'success']);
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 校正日期列表下载
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function excel()
    {
        try {
            return $this->repository->excel();
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 操作用户列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function operators()
    {
        try {
            $list = $this->repository->operators();
            return $this->response(compact('list'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 新建矫正数据
     * @return \Illuminate\Http\JsonResponse
     */
    public function create()
    {
        try {
            $this->repository->create();
            return $this->response(['msg' => 'success']);
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }

    /**
     * 校正日期列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function lists()
    {
        try {
            $list = $this->repository->lists();
            return $this->response(compact('list'));
        } catch (CustomException $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile());
        }
    }
}