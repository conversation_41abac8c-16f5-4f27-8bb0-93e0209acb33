<?php

namespace App\Http\Controllers;

use App\Models\SystemSession;
use App\Models\SystemAccessLog;
use App\Jobs\ApiLogJob;
use App\Utils\Helpers\Func;
use <PERSON><PERSON>\Lumen\Routing\Controller as Controller;
use Matrix\Exception;
use Mockery\Generator\Parameter;
use Symfony\Component\HttpFoundation\ParameterBag;
use Illuminate\Http\Exceptions\HttpResponseException;

class CommonController extends Controller
{
    protected $data;
    /**
     * @var array 通过RAW传递的JSON数据
     */
    protected $rawData;

    public $checkMobileTokenResult = [];
    public $sysName = '';

    //返回状态和变量 子类可以在此基础上重新定义
    protected $msg = [
        0 => '请求成功',
        1 => '请求失败',
        500 => '发生错误,请重试',
    ];

    public function __construct()
    {
        $host = request()->server('HTTP_HOST');
        //目前来自手机端的域名请求由于为公网 对其token必须强验
        if($host == 'fin-stat-api.dianhua.cn'){
//        if($host == 'finance-manage-api-guangli.dianhua.cn'){
            $this->checkMobileTokenResult = $this->checkAuthMobileToken();
        }else{
            $this->checkAuthCookie();//登录检查(这是pc端请求 且走内网的， 由于历史原因 这里的token没有强验)
        }

        $json = file_get_contents("php://input");
        if (is_json($json)) {//兼容之前的BaseController
            $this->data = json_decode($json, true);
            $_POST = array_merge($_POST, $this->data);
            request()->request = new ParameterBag($_POST);
        }

        $this->addRequestLog();
    }

    //获取IP段所在区域
    public function getNodeArea()
    {
        $client_ip = Func::getClientIp();
        $client_ip = substr($client_ip, 0, strrpos($client_ip, '.'));
        $node_area_ips = config('params.node_area_ips');
        $node_area = array_key_exists($client_ip, $node_area_ips) ? $node_area_ips[$client_ip] : 'other';
        return $node_area;
    }

    /**
     * @param int $status
     * @param array $data
     * @param string $msg
     * @return \Illuminate\Http\JsonResponse
     */
    protected function response($status = 0, $data = [], $msg = '')
    {
        $msg = $this->msg[$status] ?? $msg;
        $response = compact('msg', 'status', 'data');
        $this->addApiLog($response);
        return response()->json($response);
    }


    /**
     * 获取RAW方式传递的JSON数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/8/26 15:01
     *
     * @return array
     */
    protected function getJsonByRaw()
    {
        if (is_null($this->rawData)) {
            $data = file_get_contents("php://input");
            if (!$data) {
                $this->createSimpleResponse('common.10001');
            }
            if (!is_json($data)) {
                $this->createSimpleResponse('common.10002');
            }
            $this->rawData = json_decode($data, true);
        }

        return $this->rawData;
    }

    /**
     * 创建一个接口的数据响应，调用该方法后，后续的代码将不会执行
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/8/26 15:20
     *
     * @param $data mixed 数据
     *
     * @return void
     */
    protected function createDataResponse($data)
    {

        $this->createBaseResponse('', 0, $data);
    }

    /*
     * layui 表单分页数据展示 格式要求
     */

    public function CreateLayuiResponse($data = [],$count = 0,$code = 0,$message = '')
    {
        $response = response()->json(compact('code', 'message','count', 'data'));
        throw new HttpResponseException($response);
    }

    /**
     * 创建一个简单的响应，调用该方法后，系统将自动根据标识寻找提示信息，然后为用户返回响应，值得注意的是，执行后，后续的代码将不会执行
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/8/26 15:21
     *
     * @param $needle string 错误配置文件中的标识 参数为 common.10001
     *
     * @return void
     */
    protected function createSimpleResponse($needle)
    {
        $needleArr = explode('.', $needle);
        $code      = array_pop($needleArr);
        $message   = config("response.{$needle}") ?: '您遇到了一个未知错误';

        $this->createBaseResponse($message, $code);
    }

    /**
     * 创建一个接口的响应，调用该方法后，后续的代码将不会执行
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/8/26 15:04
     *
     * @param $message string 信息
     * @param $code    integer 错误标识码，为0代表成功
     * @param $data    array 接口响应的额数据
     *
     * @return void
     */
    protected function createBaseResponse($message = '', $code = 0, $data = [])
    {
        $status   = $code;
        $msg      = $message;
        $response = response()->json(compact('code', 'message', 'status', 'msg', 'data'));
        throw new HttpResponseException($response);

    }

    protected function ajaxReturn($data = [],$status = 'ok')
    {
        $status   = $status;
        $response = response()->json(compact( 'status',  'data'));
        throw new HttpResponseException($response);
    }

    protected function checkAuthMobileToken(){
        $token = request()->header('mobile-token');

        if(empty($token)){
            return ['status' => 100, 'data' => [], 'msg' => 'token无权限-1'];
        }

        $payload = Func::decodeJwt($token, config('feishu.mobile_report_config.jwt_token_secret'));
        if(!empty($payload) && isset($payload['exp'])){
            if(time() > $payload['exp']){
                //过期失效从新登录
                return ['status' => 101, 'data' => [], 'msg' => 'token无权限-2'];
            }
        }else{
            return ['status' => 100, 'data' => [], 'msg' => 'token无权限-3'];
        }

        if(isset($payload['data']['name']) && isset($payload['data']['email'])){
            $white_list = config('feishu.mobile_report_config.white_list');
            if(!in_array($payload['data']['name'], $white_list)){
                return ['status' => 100, 'data' => [], 'msg' => 'token无权限-4'];
            }
        }else{
            return ['status' => 100, 'data' => [], 'msg' => 'token无权限-5'];
        }

        $email_arr = explode('@', $payload['data']['email']);
        $this->sysName = $email_arr[0];

        return ['status' => 0, 'data' => [], 'msg' => ''];
    }

    protected function isValidMobileToken(){
        if(isset($this->checkMobileTokenResult['status']) && $this->checkMobileTokenResult['status'] != 0){
            return $this->checkMobileTokenResult;
        }else{
            return true;
        }
    }

    protected function checkAuthCookie(){
        $token = request()->header('Authorization');

        if(empty($token)){
            //return false;
            //$this->returnJson(201, '非法请求');
            $user_cookie = request()->all('user_cookie');
            if(!isset($user_cookie['user_cookie']) || empty($user_cookie['user_cookie'])){
                return false;
            }else{
                $token = $user_cookie['user_cookie'];
            }
        }

        $user_name = SystemSession::where(['session_id'=>$token])->first()->toArray();
        if(!isset($user_name['session_data'])){
            return false;
        }
        preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match);
        $this->sysName = $match[1];

        return true;
    }

    protected function returnJson($code = 200, $msg = '', $data = []){
        $arr = [
            "code" => $code,
            "status" => $code,
            "message" => $msg,
            "msg" => $msg,
            "data" => $data
        ];

        echo json_encode($arr, JSON_UNESCAPED_UNICODE);exit;
    }

    protected function addRequestLog(){
        $params = request()->all();
        $res = SystemAccessLog::addLog($params, $this->sysName);
        return $res;
    }

    protected function getSystemNameByCookie(){
        $user_cookie = request()->all('user_cookie');
        $user_name = SystemSession::where(['session_id'=>$user_cookie])->first()->toArray();
        preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match);
        $sysName = isset($match[1]) ? $match[1] : '';
        return $sysName;
    }

    /**
     * <AUTHOR> g
     * @desc 记录用户日志,写入队列，并入库
     * @date 20181128
     */
    protected function addApiLog($response)
    {
//        $log = [
//            'ip' => Func::getClientIp(),
//            'log_user' => $this->data['product_id'] ?: 'system',
//            'api_name' => get_called_class(),
//            'is_success' => $response['status'] === 0 ? 0 : 1,
//            'node_area' => $this->data['node_area'],
//            'output_data' => json_encode($response),
//            'input_data' => $this->data,
//            'create_at' => time(),
//            'server' => $_SERVER
//        ];
        //dispatch((new ApiLogJob($log))->onQueue(config('params.api_log_job')));
        return true;
    }
}
