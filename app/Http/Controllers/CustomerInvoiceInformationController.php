<?php
namespace App\Http\Controllers;

use App\TraitUpgrade\ResponseTrait;
use App\Repositories\Customer\InvoiceInformationRepositorie;
use Illuminate\Http\JsonResponse;
use Exception;

class CustomerInvoiceInformationController extends CommonController
{
    use ResponseTrait;

    private $repository;

    /**
     *
     */
    public function __construct(InvoiceInformationRepositorie $repository){
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 获取客户开票信息
     * @return JsonResponse
     */
    public function list(){
        try {
            $list = $this->repository->list();
            return $this->response($list);
        } catch (Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage() . ' at line ' . $e->getLine() . 'at file ' . $e->getFile());
        }
    }
}