<?php

namespace App\Http\Controllers;

use App\Http\Repository\ProductAuthRepository;
use App\TraitUpgrade\ResponseTrait;
use Lara<PERSON>\Lumen\Routing\Controller;

//class ProductAuthController extends Controller
class ProductAuthController extends CommonController
{
    use ResponseTrait;

    private $repository;

    /**
     * ProductAuthController constructor.
     * @param $repository
     */
    public function __construct(ProductAuthRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 无脑清理权限认证的缓存信息
     */
    public function clearAuthCache()
    {
        try {
            $this->repository->clearAuthCache();
            return $this->response(['msg' => 'success']);
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
