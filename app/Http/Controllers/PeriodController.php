<?php

namespace App\Http\Controllers;

use App\Http\Repository\StatPeroidRepository;
use App\Models\PeriodCompareResult;
use App\TraitUpgrade\CurlTrait;

class PeriodController extends CommonController
{
    public function getPeriodCompareResult()
    {
        $period = request()->post('period');
        $pid = request()->post('pid');
        $data = request()->post("data");
        $type = request()->post("type", "");

        if (!$pid) {
            return $this->response(100, [], '产品id错误');
        }

        if (!$period) {
            return $this->response(100, [], '账期错误');
        }

        if (!$data) {
            return $this->response(100, [], '数据错误');
        }

        if (!$type) {
            return $this->response(100, [], '类型错误');
        }

        try {
            foreach ($data as $pids => $value) {
                $where = ['pid' => $pid, 'pids' => $pids, 'period' => $period, 'type' => $type];
                $info = [];
                $info['content'] = json_encode($value);
                $info['update_time'] = time();
                $res = PeriodCompareResult::getDataByPeriod($where);
                if ($res) {
                    PeriodCompareResult::updateData($where, $info);
                } else {
                    $info['create_time'] = time();
                    $info = array_merge($where, $info);
                    PeriodCompareResult::add($info);
                }
            }
            return $this->response(0, [], '推送成功');
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    public function getPeriodData()
    {
        $period = request()->post('period');

        $pid = request()->post('pid');

        if (!$pid) {
            return $this->response(100, [],'产品id错误');
        }

        if (!$period) {
            return $this->response(100, [], '账期错误');
        }

        $where = ['pid' => $pid, 'period' => $period];
        // 比对结果
        $period_list = PeriodCompareResult::getDataByPeriod($where);
        $list = [];
        foreach ($period_list as $key => $value) {
            $content = json_decode($value['content']);
            foreach ($content as $kk => $val) {
                foreach ($val as $kkk => $vv) {
                    $list[$kkk]['type'] = $kkk;
                    $list[$kkk][$value['pids'].'_'.$kk] = $vv;
                }
            }
        }

        return $this->response(0, $list, 'ok');
    }

    public function doPeriod()
    {
        $pid = request()->post('pid');

        $period = request()->post('period');

        $type = request()->post('type', '');

        if (!in_array($type, ['cutover', 'backup'])) {
            return $this->response(100, [],'type错误');
        }

        if (!$period) {
            return $this->response(100, [],'账期错误');
        }

        $params = ['period' => $period, 'type' => $type];
        $result = $this->getPeriodInfo($params);
        
        if (isset($result['status']) && $result['status'] == 0) {
            return $this->response(0, [],'切换成功');
        }

        $msg = isset($result['msg']) ? $result['msg'] : '切换失败';
        return $this->response(100, [], $msg);
    }

    protected function getPeriodInfo($params)
    {

        $shield_info = config('params.shield_info');

        $timestamp = time();
        $apikey = $shield_info['apikey'];
        $appsecret = $shield_info['appsecret'];
        $nonce = rand(1000, 9999);

        $tmpArr = array($timestamp, $apikey, $appsecret, $nonce);
        sort($tmpArr, SORT_STRING);
        $signature = sha1(implode($tmpArr));

        $get = compact('timestamp', 'nonce', 'apikey', 'signature');

        // 账期地址
        $period_url = $shield_info['period_url'] . '?' . http_build_query($get, '&');

        $result = [];
        for ($i = 0; $i < 2; $i++) {
            $result = CurlTrait::postData($period_url, $params);
            if ($result && isset($result['status']) && $result['status'] == 0) {
                break;
            }
        }

        return $result;
    }



    public function hmfPeriod(){
        /*
        获取当前账期（post）：https://hmf.dianhua.cn/tabdate/get
        */
        try {
            $repository = new StatPeroidRepository();
            $data = $repository->getPeriodListFromMongo();

            $data = ['list' => $data['list'], 'count' => $data['count']];
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }

    public function setHmfPeriod(){
        $params = request()->post();
        try {
            $repository = new StatPeroidRepository();
            $data = $repository->setHmfPeriod($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }


    public function getShieldPeriod(){
        $period = request()->post('period', '');
        $type = request()->post('type', '');
        $params['period'] = $period;
        $params['type'] = $type;//new old
        try {
            $repository = new StatPeroidRepository();
            $data = $repository->getShieldPeriod($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }


    public function setShieldPeriod(){
        $period = request()->post('period', '');
        $type = request()->post('type', '');
        $params['period'] = $period;
        $params['type'] = $type;//cutover fallback
        try {
            $repository = new StatPeroidRepository();
            $res = $repository->setShieldPeriod($params);
            if($res){
                return $this->response(0);
            }
            return $this->response(100, [], '号码风险等级接口请求失败');
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }


    public function getShieldPeriodV2(){
        try {
            $repository = new StatPeroidRepository();
            $data = $repository->getShieldPeriodV2();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }

    public function setShieldPeriodV2(){
        $period = request()->post('period', '');
        $type = request()->post('periodType', '');
        $params['period'] = $period;
        $params['periodType'] = $type;
        try {
            $repository = new StatPeroidRepository();
            $res = $repository->setShieldPeriodV2($params);
            if($res){
                return $this->response(0);
            }
            return $this->response(100, [], '号码风险等级接口请求失败');
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }





}
