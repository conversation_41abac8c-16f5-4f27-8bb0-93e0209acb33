<?php
/**
 * Created by PhpStorm.
 * User: gang8
 * Date: 2021/2/4
 * Time: 14:04
 */

namespace App\Http\Controllers;

use App\Http\Repository\ChannelQualicationRepository;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\AccountProductCustom;
use App\Models\Product;
use App\Models\ConfigPriceInterface;
use App\Models\SystemAccessLog;
use App\Models\SystemUser;
use App\Providers\RedisCache\RedisCache;
use App\TraitUpgrade\WechatExceptionTrait;
use App\Utils\Helpers\Func;
use Illuminate\Support\Facades\DB;
use App\Models\Monitor\LogChannelStatisDaily;
use Illuminate\Support\Facades\Redis;

//渠道管理
//class ChannelController extends Controller
class ChannelController extends CommonController
{
    use WechatExceptionTrait;

	private $operator = [
		'ALL'  => '不区分',
		'CMCC' => '移动',
		'CUCC' => '联通',
		'CTCC' => '电信',
		'CBN' => '广电',
	];
	
	private $encrypt_way = [
		'ALL'    => '不区分',
		'CLEAR'  => '明文',
		'SHA256' => 'SHA256',
		'MD5'    => 'MD5',
	];
	
	//获取产品的Options
	public function getProductOptions()
	{
		$product_ids = [200, 210, 401, 1000];
		$data        = array_map(function ($product_id) {
			$product_name = RedisCache::instance('productId_productName_mapping')
									  ->get($product_id);
			
			return compact('product_id', 'product_name');
		}, $product_ids);
		
		return createOption(array_column($data, 'product_name', 'product_id'));
	}
	
	//获取渠道的Options
	public function getChannelOptions()
	{
		$data = Channel::select(['channel_id', 'label', 'name'])
					   ->get()
					   ->map(function ($item) {
						   $channel_id   = $item->channel_id;
						   $channel_name = "[{$channel_id}] {$item->label} ($item->name)";
			
						   return compact('channel_id', 'channel_name');
					   })
					   ->toArray();
		
		$default = request()->get('default');
		$options = createOption(array_column($data, 'channel_name', 'channel_id'), $default);
		
		$this->createDataResponse($options);
	}

    //获取渠道全部相关的Options
    public function getAllChannelOptions()
    {
        // 转换渠道类型映射
        $channelTypeMap = Channel::$channelTypeMap;
        $channelTypeOptions = array_map(function($value, $key) {
            if ($key == 0){
                $value = '默认';
            }
            return [
                'value' => $key,
                'label' => $value
            ];
        }, $channelTypeMap, array_keys($channelTypeMap));
        // 转换付费类型映射
        $payTypeMap = Channel::$payTypeMap;
        $payTypeOptions = array_map(function($value, $key) {
            if ($key == 0){
                $value = '默认';
            }
            return [
                'value' => $key,
                'label' => $value
            ];
        }, $payTypeMap, array_keys($payTypeMap));
        //商务跟进人
        $allUsers = SystemUser::getAllUsers();
        $userOptions = [];
        foreach ($allUsers as $user){
            $value = $user['realname'] ?: $user['username'];
            $userOptions[] = [
                'value' => $value,
                'label' => $value
            ];
        }
        $data['channelTypeOptions'] = $channelTypeOptions;
        $data['payTypeOptions'] = $payTypeOptions;
        $data['userOptions'] = $userOptions;
        $this->createDataResponse($data);
    }
	
	//获取接口的Options
	public function getInterfaceOptions()
	{
		$data    = ChannelInterface::select(['id', 'label', 'name'])
								   ->where(function ($query) {
									   $channel_id = request()->get('channel_id');
									   if ($channel_id) {
										   $query->where('channel_id', $channel_id);
									   }
								   })
								   ->get()
								   ->map(function ($item) {
									   $id   = $item->id;
									   $name = "{$item->label} ($item->name)";
			
									   return compact('id', 'name');
								   })
								   ->toArray();
		$default = request()->get('default');
		$options = createOption(array_column($data, 'name', 'id'), $default);
		
		$this->createDataResponse($options);
	}
	
	//获取渠道列表数据
	public function getChannelList()
	{
        // 默认按照合同到期时间递减排序
        $field = request()->get('field','contract_end_date');
        $order = request()->get('order','desc');
		$channel_interface_count = ChannelInterface::select(DB::raw('count(1) as count'), 'channel_id')
												   ->groupBy('channel_id')
												   ->get()
												   ->toArray();
		$channel_interface_count = array_column($channel_interface_count, 'count', 'channel_id');
        $channel_type_map = Channel::$channelTypeMap;
        $pay_type_map = Channel::$payTypeMap;
		
		$data = Channel::select([
			'channel_id',
			'label',
			'name',
			'status',
            'contract_end_date',
            'channel_follower',
            'channel_type',
            'pay_type'
		])
					   ->where(function ($query) {
						   $channel_id = request()->get('channel_id');
						   if ($channel_id) {
							   $query->where('channel_id', $channel_id);
						   }
						   $status = request()->get('status');
						   if (0 != strlen($status)) {
							   $query->where('status', intval($status));
						   }
					   })
                       ->orderByRaw("${field} ${order}")
					   ->get()
					   ->map(function ($item) use ($channel_interface_count,$channel_type_map,$pay_type_map) {
						   $channel_id            = $item->channel_id;
						   $item->interface_count = array_get($channel_interface_count, $channel_id, 0);
                            // 合同到期时间转化为时间戳 用于排序
                           $item->contract_end_times = strtotime($item->contract_end_date);
                           $item->channel_type_text = $channel_type_map[$item->channel_type] ?? '';
                           $item->pay_type_text = $pay_type_map[$item->pay_type] ?? '';
						   return $item;
					   })
					   ->toArray();
		$this->createDataResponse($data);
	}

    //获取渠道列表数据
    public function getChannelProductList()
    {
        $product_id = request()->post('product_id', "210");
        $product_channel = ChannelProduct::getChannelInfoByPid($product_id);


        //则去掉电信 和 河北移动
        foreach ( $product_channel as $k=>$v ){
            if( $v['channel_id'] == 2 || $v['channel_id'] == 7 ){
                unset($product_channel[$k]);
            }

            if( $v['channel_id'] == 8 && $product_id == 210 ){
                unset($product_channel[$k]);
            }
        }

        $this->createDataResponse($product_channel);
    }

    //获取产品渠道列表数据
    public function getProductChannelList()
    {
        $channel_id = request()->get('channel_id', "");
        $product_channel = ChannelProduct::getChannelInfoByCid($channel_id);
        $channelInfo = [];
        foreach ( $product_channel as $v ){
            $item = [];
            $item['id'] = $v['id'];
            $item['num'] = $v['num'];
            $item['product_id'] = $v['product_id'];
            $item['pname'] = RedisCache::instance('productId_productName_mapping')
                ->get($v['pid']);
            $item['name'] = $v['name'];
            $item['cnname'] = $v['label'];
            $item['status'] = $v['status'];
            $item['param'] = json_decode($v['param'], true);

            $channelInfo[] = $item;
        }

        $this->createDataResponse($channelInfo);
    }

    //获取产品渠道列表数据
    public function getProductChannelInfo()
    {
        $id = request()->get('id', "");
        $product_channel = ChannelProduct::findOneItem(['id' => $id]);
        $info = json_decode($product_channel['param'], true);
        if(!empty($info) && isset($info['enable_date']) && isset($info['back_day'])){
            $info['back_date'] = substr($info['enable_date'], 0, 4).substr($info['enable_date'], -2).$info['back_day'];
        }else{
            $info['back_date'] = date('Ymd');
        }

        if(intval($info['back_date']) == 0){//因为历史数据有写0的情况,故处理一下
            $info['back_date'] = '';
        }

        if(!isset($info['cache_date'])){
            $info['cache_date'] = '';
        }

        if(!isset($info['beijing_proxy'])){
            $info['beijing_proxy'] = '';
        }

        if(!isset($info['shenzhen_proxy'])){
            $info['shenzhen_proxy'] = '';
        }

        if(!empty($info) && isset($info['product_ids']) && !empty($info['product_ids'])){
            $info['product_ids_count'] = count(explode(',', $info['product_ids']));
        }else{
            $info['product_ids_count'] = 0;
        }
        $info['id'] = $product_channel['id'];
        $info['status'] = $product_channel['status'];
        $info['period_date'] = $this->getPeriodDateCache($product_channel['product_id'], $product_channel['channel_id']);

        $this->createDataResponse($info);
    }

    //获取产品渠道列表数据
    public function saveProductChannel()
    {
        $id = request()->post('id', "");
        $back_date = trim(request()->post('back_date', ""));
        $cache_date = trim(request()->post('cache_date', ""));
        $period_date = trim(request()->post('period_date', ""));
        $product_ids = trim(request()->post('product_ids', ""));
        $beijing_proxy = trim(request()->post('beijing_proxy', ""));
        $shenzhen_proxy = trim(request()->post('shenzhen_proxy', ""));
        $check_product_ids = trim(request()->post('check_product_ids', ""));
        $check_tel = trim(request()->post('check_tel', ""));
        $status = request()->post('status', "");
        if(!empty($product_ids)){
            $product_ids = str_replace('，',',', $product_ids);
            $product_ids = str_replace(["\r\n", "\r", "\n", "\t", ' '],'', $product_ids);
            $p_list = explode(',', $product_ids);
            $father_list = [];
            foreach($p_list as $pid){
                if($pid == 0){
                    $this->createBaseResponse('支持产品中含有0的产品', 50001);
                }
                $father_id = RedisCache::instance('productId_fatherId_mapping')
                    ->get($pid);
                $father_list[] = $father_id;
            }
            $father_list = array_unique($father_list);
            if(count($father_list) > 1){
                $this->createBaseResponse('支持产品中含有其它父产品的产品', 50001);
            }

            if(!empty($check_product_ids) && !empty($father_list)){
                $check_father_id = RedisCache::instance('productId_fatherId_mapping')
                    ->get($check_product_ids);
                if(!in_array($check_father_id, $father_list)){
                    $this->createBaseResponse('检测产品和支持产品不属同一父产品', 50002);
                }
            }

        }

        if(!empty($back_date)){
            if(!preg_match("/^\d{8}$/", $back_date)){
                $this->createBaseResponse('回朔日期格式有误,正确写法如20210101', 50002);
            }
        }

        if(!empty($period_date)){
            if(!preg_match("/^\d{8}$/", $period_date)){
                $this->createBaseResponse('渠道账期格式有误,正确写法如20210101', 50002);
            }

        }

        if(!empty($cache_date)){
            if(!preg_match("/^\d{8}$/", $cache_date)){
                $this->createBaseResponse('缓存日期格式有误,正确写法如20210101', 50002);
            }

        }

        if(!empty($id)){//更新
            $cpInfo = ChannelProduct::findOneItem(['id' => $id]);
            //dd($cpInfo['product_id']);
            if(isset($father_list) && !empty($father_list)){
                if($cpInfo['product_id'] != $father_list[0]){
                    $this->createBaseResponse('支持产品或检测产品有不属父产品'.$cpInfo['product_id'].'的产品', 50002);
                }
            }

            $param = [
                'enable_date' => substr($back_date, 0, 6),
                'back_day' => substr($back_date, -2),
                'cache_date' => $cache_date,
                'product_ids' => $product_ids,
                'beijing_proxy' => $beijing_proxy,
                'shenzhen_proxy' => $shenzhen_proxy,
                'check_product_ids' => $check_product_ids,
                'check_tel' => $check_tel
            ];
            $up = ChannelProduct::editChannelInfoById($id, ['param' => json_encode($param, JSON_UNESCAPED_UNICODE), 'status' => $status, 'update_at'=>time() ]);
            if($up){

                $cache = $this->setPeriodDateCache($period_date, $cpInfo['product_id'], $cpInfo['channel_id']);
                if(!$cache){
                    $this->createBaseResponse('渠道账期缓存设置失败,其它属性字段更新成功');
                }
                $this->createBaseResponse('更新成功');
            }else{
                $this->createBaseResponse('更新失败', 50005);
            }

        }else{//新增
            $channel_id = request()->post('channel_id', "");
            $product_id = trim(request()->post('product_id', ""));
            if(empty($channel_id)){
                $this->createBaseResponse('所属渠道不能为空', 50006);
            }
            $channel_info = Channel::getChannelById($channel_id);
            if(empty($channel_info)){
                $this->createBaseResponse('所属渠道不存在', 50006);
            }

            if(empty($product_id)){
                $this->createBaseResponse('父产品id不能为空', 50006);
            }

            $pinfo = Product::findOneItem(['product_id' => $product_id]);
            if(empty($pinfo)){
                $this->createBaseResponse('父产品id不存在', 50006);
            }

            if(isset($father_list) && !empty($father_list)){
                if($product_id != $father_list[0]){
                    $this->createBaseResponse('支持产品或检测产品有不属父产品'.$product_id.'的产品', 50002);
                }
            }

            $param = [
                'enable_date' => substr($back_date, 0, 6),
                'back_day' => substr($back_date, -2),
                'cache_date' => $cache_date,
                'product_ids' => $product_ids,
                'check_product_ids' => $check_product_ids,
                'check_tel' => $check_tel
            ];

            $add_set = [
                'cid' => $channel_info['id'],
                'pid' => $product_id,
                'product_id' => $product_id,
                'channel_id' => $channel_id,
                'param' => json_encode($param, JSON_UNESCAPED_UNICODE),
                'status' => $status,
                'create_at' => time(),
            ];
            $add = ChannelProduct::insert($add_set);
            if($add){
                $cache = $this->setPeriodDateCache($period_date, $product_id, $channel_id);
                if(!$cache){
                    $this->createBaseResponse('渠道账期缓存设置失败,其它属性字段添加成功');
                }
                $this->createBaseResponse('添加成功');
            }else{
                $this->createBaseResponse('添加失败', 50009);
            }

        }


    }


    //上下架产品对应的渠道
    public function editChannelStatus()
    {
        $name_chiness = [
            // 'yuheng.wang' => "王煜恒",
            'chengsheng.yang' => "杨成胜",
            'yanming.li' => "李晏铭",
            'ren.zhang' => "张韧",
            'wei.fan' => "樊巍",
            'chang.liu' => "刘畅",
        ];

        //获取参数 并 校验
        $id = request()->post('id', 0);
        $status = request()->post('status', 1);
        $user_cookie = request()->post('user_cookie', "");
        $user_name = Func::getUserNameFromCookie($user_cookie);
        if( !$user_name ){
            $this->createSimpleResponse('channel.14103');
        }
        if( !$id ){
            $this->createSimpleResponse('channel.14103');
        }
        $user_name = isset($name_chiness[$user_name]) ? $name_chiness[$user_name] : $user_name;

        //获取渠道信息
        $channel_product_info = ChannelProduct::where('id', $id)->get()->first();
        $channel_product_info = $channel_product_info ? $channel_product_info->toArray() : [];
        if( !isset($channel_product_info['channel_id']) ){
            $this->createSimpleResponse('channel.14100');
        }
        $channel_info = Channel::where('channel_id', $channel_product_info['channel_id'])->get()->first();
        $channel_info = $channel_info ? $channel_info->toArray() : [];
        if( !isset($channel_info['label']) ){
            $this->createSimpleResponse('channel.14100');
        }
        $channel_name = $channel_info['label'];

        //渠道上下线
        $result = ChannelProduct::where('id', $id)->update(['status'=>$status]);
        if( $result === false ){
            $this->createSimpleResponse('channel.14104');
        }

        //发送报警
        $statusStr = $status == 1 ? "开启" : "关闭";
        $time = date("Y-m-d H:i:s");
        $noticeStr = "邦信分 {$channel_name} 渠道于 {$time} {$statusStr} ，操作人：$user_name";
        $this->wechatException($noticeStr, "wechat.bxf");

        $this->createSimpleResponse('channel.0');
    }

	//增加渠道
	public function addChannel()
	{
		$channel_id = request()->post('channel_id');
		//确认这个渠道ID是否存在
		$count = Channel::where('channel_id', $channel_id)
						->count();
		if ($count) {
			$this->createSimpleResponse('channel.14100');
		}
		$label = request()->post('channel_name');
		$name  = request()->post('name');
		//新增 监控通知
        $balance = request()->post('balance', 0);
        $balance_start_date = request()->post('balance_start_date', "1970-01-01");
        $rest_num = intval(request()->post('rest_num', 0));
        $rest_num_start_date = request()->post('rest_num_start_date', "1970-01-01");
        $max_call_num = intval(request()->post('max_call_num', 0));
        $contract_end_date = request()->post('contract_end_date', "1970-01-01");
        $daily_call_num = request()->post('daily_call_num', 0);
        $channel_follower = request()->post('channel_follower', '');
        $channel_type = request()->post('channel_type', 0);
        $pay_type = request()->post('pay_type', 0);

		$num  = $channel_id;

		Channel::insert(compact('label', 'name', 'channel_id', 'num','balance','balance_start_date'
            ,'rest_num','rest_num_start_date','max_call_num','contract_end_date','daily_call_num','channel_follower','channel_type','pay_type'));

		$this->createSimpleResponse('channel.0');
	}

	//获取某个渠道的数据
	public function getChannelInfo()
	{
		$channel_id = request()->get('channel_id');


//       DB::connection()->enableQueryLog();
//		$data      = Channel::select(['channel_id', 'name', 'label', 'status'])
//							 ->where('channel_id', $channel_id)
//							 ->find();
       $data = Channel::getChannelById($channel_id);

		if (empty($data)) {
			$this->createSimpleResponse('channel.14101');
		}
		$this->createDataResponse($data);
	}
	
	//修改某个渠道的数据
	public function saveChannel()
	{
		$channel_id = request()->post('channel_id');

		//确认这个渠道ID是否存在
		$count = Channel::where('channel_id', $channel_id)
						->count();
		if (!$count) {
			$this->createSimpleResponse('channel.14101');
		}
		$label  = request()->post('channel_name');
		$name   = request()->post('name');
		$status = request()->post('status');
		$balance = request()->post('balance');
		$balance_start_date = request()->post('balance_start_date');
		$rest_num = request()->post('rest_num');
		$rest_num_start_date = request()->post('rest_num_start_date');
		$max_call_num = request()->post('max_call_num');
		$contract_end_date = request()->post('contract_end_date');
		$daily_call_num = request()->post('daily_call_num');
        $channel_follower = request()->post('channel_follower', '');
        $channel_type = request()->post('channel_type', 0);
        $pay_type = request()->post('pay_type', 0);


		Channel::where('channel_id', $channel_id)
			   ->update(compact('label', 'name', 'status','balance','balance_start_date',
               'rest_num','rest_num_start_date','max_call_num','contract_end_date','daily_call_num','channel_follower','channel_type','pay_type'));
		
		$this->createSimpleResponse('channel.0');
	}
	
	//获取接口数据
	public function getInterfaceList()
	{
		$data = ChannelInterface::select([
			'channel_interface.id',
			'channel_interface.channel_id',
			'channel_interface.name',
			'channel_interface.label',
			'channel_interface.status',
			'channel.label as channel_name',
		])
								->leftJoin('channel', 'channel.channel_id', '=', 'channel_interface.channel_id')
								->where(function ($query) {
									$channel_id = request()->get('channel_id');
									if ($channel_id) {
										$query->where('channel_interface.channel_id', $channel_id);
									}
									$status = request()->get('status');
									if (strlen($status) > 0) {
										$query->where('channel_interface.status', $status);
									}
								})
								->get()
								->toArray();
		
		$this->createDataResponse($data);
	}
	
	//获取接口信息
	public function getInterfaceInfo()
	{
		$interface_id = request()->get('interface_id');
		$data         = ChannelInterface::select(['id', 'channel_id', 'name', 'label', 'status'])
										->where('id', $interface_id)
										->get()
										->first()
										->toArray();
		if (empty($data)) {
			$this->createSimpleResponse('channel.14103');
		}
		$this->createDataResponse($data);
	}
	
	//修改接口
	public function saveInterface()
	{
		$id = request()->post('id');
		//确认这个接口ID是否存在
		$count = ChannelInterface::where('id', $id)
								 ->count();
		if (!$count) {
			$this->createSimpleResponse('channel.14103');
		}
		$label  = request()->post('label');
		$name   = request()->post('name');
		$status = request()->post('status');
		
		ChannelInterface::where('id', $id)
						->update(compact('label', 'name', 'status'));
		
		$this->createSimpleResponse('channel.0');
	}
	
	//增加接口数据
	public function addInterface()
	{
		$channel_id = request()->post('channel_id');
		$label      = request()->post('label');
		$name       = request()->post('name');
		
		//确认这个接口是否已经存在了
		$count = ChannelInterface::where('channel_id', $channel_id)
								 ->where('name', $name)
								 ->count();
		if ($count) {
			$this->createSimpleResponse('channel.14102');
		}
		
		ChannelInterface::insert(compact('channel_id', 'label', 'name'));
		
		$this->createSimpleResponse('channel.0');
	}
	
	//计费配置列表数据
	public function getPriceList()
	{
		$data = ConfigPriceInterface::select([
			'config_price_interface.id',
			'channel.label as channel_name',
			'channel_interface.label as interface_name',
			'config_price_interface.start_date',
			'config_price_interface.price',
		])
									->leftJoin('channel_interface', 'channel_interface.id', '=', 'config_price_interface.interface_id')
									->leftJoin('channel', 'channel.channel_id', '=', 'channel_interface.channel_id')
									->where(function ($query) {
										$channel_id = request()->get('channel_id');
										if ($channel_id) {
											$query->where('channel.channel_id', $channel_id);
										}
										$interface_id = request()->get('interface_id');
										if ($interface_id) {
											$query->where('channel_interface.id', $interface_id);
										}
									})
									->orderBy('config_price_interface.interface_id')
									->orderBy('start_date', 'desc')
									->get()
									->map(function ($item) {
										$item->start_date = date('Y-m-d', strtotime($item->start_date));
			
										$price = json_decode($item->price, true);
										$price = array_map(function ($item) {
											$operator    = array_get($this->operator, $item['operator'], '未知');
											$encrypt_way = array_get($this->encrypt_way, $item['encrypt_way'], '未知');
                                            if($item['price_model'] == 1){
                                                $model = '成功调用量';
                                            }else if($item['price_model'] == 2){
                                                $model = '查得量';
                                            }else if($item['price_model'] == 3){
                                                $model = '计费量';
                                            }else{
                                                $model = '未知';
                                            }

											$price       = $item['price'];
				
											return "{$price}（【{$operator}】【{$encrypt_way}】【{$model}】）";
										}, $price);
			
										$item->price = '<p class="price_line">' . implode('</p><p class="price_line">', $price) . '</p>';
			
										return $item;
									})
									->toArray();
		
		$this->createDataResponse($data);
	}
	
	//删除计费配置
	public function deletePrice()
	{
		$id = request()->post('id');
		ConfigPriceInterface::where('id', $id)
							->delete();
		$this->createSimpleResponse('channel.0');
	}

	/**
	 * 渠道日志统计
	 */
	public function log()
	{
		$channel_info = ChannelProduct::getChannelInfoByPid(210);
        $channel_name = [];
        foreach ( $channel_info as $v ){
            $channel_name[$v['channel_id']] = $v['label'];
        }

		$date_start = request()->post('date_start');
		$date_end = request()->post('date_end');
		$end_time = !empty($date_end) ? $date_end : date('Y-m-d');
		$start_time = !empty($date_start) ? $date_start : date('Y-m-d');

		if($start_time > $end_time){
			return  response()->json(['status' => 10002, 'msg' => "开始时间不能大于结束时间", 'data' => []]);
		}

		$where = [
			['day', '>=', $start_time],
			['day', '<=', $end_time],
		];

		$daily_info = LogChannelStatisDaily::where($where)->orderBy('day', 'desc')->get()->toArray();

		$diff_time = strtotime($end_time .' 23:59:59') - strtotime($start_time . ' 00:00:00');

		$start_time_pre = date('Y-m-d', strtotime($start_time) - $diff_time);
		$end_time_pre = strtotime(date('Y-m-d', strtotime($start_time)));
		$end_time_pre = date('Y-m-d', $end_time_pre - 1);
		$where_pre = [
			['day', '>=', $start_time_pre],
			['day', '<=', $end_time_pre],
		];

		$daily_info_pre = LogChannelStatisDaily::where($where_pre)->orderBy('day', 'desc')->get()->toArray();

		if(empty($daily_info)){
			return  response()->json(['status' => 10001, 'msg' => "该时间段内数据为空", 'data' => []]);
		}

		$info_arr = [];
		foreach($daily_info as $key=>$value){
			$info_arr[$value['channel_id']][] = $value;
		}
		$info_sum = [];
		foreach($info_arr as $key=>$value) {
			$total = 0;
			$success = 0;
			$run_time_avg = 0;
			$over_one_num = 0;
			foreach ($value as $kk => $vv) {
				$total += $vv['total'];
				$success += $vv['success'];
				$run_time_avg += $vv['total'] * $vv['run_time_avg'];
				$over_one_num += $vv['over_one_num'];
			}
			$info_sum[$key] = [
				'total' => $total,
				'success' => $success,
				'run_time_avg' => $total == 0 ? 0 : round($run_time_avg / $total, 2),
				'over_one_num' => $over_one_num
			];
		}

		$info_pre_arr = [];
		foreach($daily_info_pre as $key=>$value){
			$info_pre_arr[$value['channel_id']][] = $value;
		}
		$info_pre_sum = [];
		foreach($info_pre_arr as $key=>$value) {
			$total = 0;
			$run_time_avg = 0;
			foreach ($value as $kk => $vv) {
				$total += $vv['total'];
				$run_time_avg += $vv['total'] * $vv['run_time_avg'];
			}
			$info_pre_sum[$key] = [
				'total' => $total,
				'run_time_avg' => $total == 0 ? 0 : round($run_time_avg / $total, 2),
			];
		}

		$arr = [];
		foreach($info_sum as $key=>$value){
			$kekao_num = $value['total'] != 0 ? ($value['success'] == 0 ? 0 : round($value['success'] / $value['total'], 7)) : 0;
			$mix_1s_num = $value['total'] != 0 ? ($value['over_one_num'] == 0 ? 0 : round($value['over_one_num'] / $value['total'], 7)) : 0;
			$arr[$key] = [
				'channel_name' => isset($channel_name[$key]) ? $channel_name[$key] : '未知',
				'total_num' => $value['total'],
				'success_num' => $value['success'],
				'kekao_num' => !empty($kekao_num) ? ($kekao_num * 100).'%' : 0,
				'avg_time' => $value['run_time_avg'],
				'avg_time_pre' => isset($info_pre_sum[$key]['run_time_avg']) ? $info_pre_sum[$key]['run_time_avg'] : '无',
				'pass_1s_num' => $value['over_one_num'],
				'mix_1s_num' => !empty($mix_1s_num) ? ($mix_1s_num * 100).'%' : 0,
			];

		}
		return  response()->json(['status' => 0, 'msg' => "", 'data' => ['items'=>$arr]]);
	}


	public function setPeriodDateCache($period_date = '', $product_id = '', $channel_id = ''){
        $redis = Redis::connection('default');
        if(!empty($period_date)){
            $key = 'periodDate_'.$channel_id.'_'.$product_id;
            $flag = $redis->set($key, $period_date);
            $redis->expire($key, 300);
            if(!$flag){
                return false;
            }
        }

        return true;
    }

    public function getPeriodDateCache($product_id = '', $channel_id = ''){
        $redis = Redis::connection('default');
        $key = 'periodDate_'.$channel_id.'_'.$product_id;
        $date = $redis->get($key);

        return $date ? $date : '';
    }

    public function getComplementList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $channel_id = request()->post('channel_id', '');
        $apikey = request()->post('apikey', '');
        $offset = ($page - 1) * $limit;
        $where = ['account_product_custom.type' => 30, 'account_product_custom.delete_at' => 0];
        if($channel_id){
            $where['account_product_custom.extend'] = (string)$channel_id;
        }

        $channel_list = Channel::select(['channel_id', 'label', 'name'])->get()->toArray();
        $channel_list = array_column($channel_list, 'label', 'channel_id');
        $fields = ['account_product_custom.*','account.account_name'];
        $res = AccountProductCustom::getComplementList($where, $fields, $offset, $limit, $apikey);
        foreach ($res['data'] as &$val){
            if(empty($val['account_name'])){
                $val['account_name'] = 'default';
            }

            $data = json_decode($val['data'], true);
            if (JSON_ERROR_NONE != json_last_error()) {
                $val['data'] = '配置存在解析错误';
            }

            $val['complement'] = $data['complement'] ?? 0;
            $val['channel_id'] = $val['extend'];
            $val['channel_name'] = $channel_list[$val['channel_id']] ?? $val['channel_id'];
            $val['update_at'] = $val['update_at'] ? date('Y-m-d H:i:s', $val['update_at']) : '';
            $val['create_at'] = $val['create_at'] ? date('Y-m-d H:i:s', $val['create_at']) : '';

        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);

    }

    public function saveChannelComplement(){
        $params = request()->post();
        if(!in_array(intval($params['father_id']), [210, 1000])) {
            $this->createBaseResponse("父产品ID只能为210或1000", 5003);
        }

        $id = $params['id']?:0;
        unset($params['id']);

        try{
            if($id > 0){//编辑
                $op = '编辑';
                $up = AccountProductCustom::updaetComplementRecord(['id' => $id], $params);
                if($up){
                    $this->createBaseResponse("编辑成功");
                }
            }else{//添加
                $op = '添加';
                $channel_ids = $params['channel_id'];
                if(!is_array($channel_ids)) {
                    $this->createBaseResponse("channel_id不是array", 5003);
                }

                unset($params['channel_id']);
                $insert = AccountProductCustom::batchAddComplementRecord($channel_ids, $params);
                if($insert['code'] == 0){
                    $this->createBaseResponse("添加成功");
                }
                $this->createBaseResponse($op."失败:".$insert['msg'], 5001);
            }
        }catch (Exception $e){
            $this->createBaseResponse($op."失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse($op."失败", 5001);

    }

    public function getChannelComplement(){
        $id = request()->get('id');

        try{
            $info = AccountProductCustom::where(['id' => $id])->first();
            if($info){
                $info['create_at'] = $info['create_at'] ? date('Y-m-d H:i:s', $info['create_at']) : '';
                $info['update_at'] = $info['update_at'] ? date('Y-m-d H:i:s', $info['update_at']) : '';
                if(empty($info['data'])){
                    $info['timeout'] = 0;
                    $info['complement'] = 0;
                }else{
                    $data = json_decode($info['data'], true);
                    if (JSON_ERROR_NONE != json_last_error()) {
                        throw new \Exception('配置解析错误');
                    }
                    $info['timeout'] = $data['timeout'] ?? 0;
                    $info['complement'] = $data['complement'] ?? 0;
                }
                $info['channel_id'] = [intval($info['extend'])];

                $this->createBaseResponse("获取数据成功", 0, $info);
            }
        }catch (Exception $e){
            $this->createBaseResponse("获取数据失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据失败", 5001);

    }

    public  function setChannelComplement(){
        $id = request()->get('id');
        $complement = request()->get('complement');

        try{
            $info = AccountProductCustom::where(['id' => $id])->first();
            if($info){
                if(empty($info['data'])){
                    throw new \Exception('配置有误');
                }else{
                    $data = json_decode($info['data'], true);
                    if (JSON_ERROR_NONE != json_last_error()) {
                        throw new \Exception('配置解析错误');
                    }
                    $set['timeout'] = $data['timeout'] ?? 0;
                    $set['complement'] = intval($complement);
                    $up = AccountProductCustom::where(['id' => $id])->update(['data' => json_encode($set), 'update_at' => time()]);
                }

                if(isset($up) && $up){
                    $this->createBaseResponse("设置成功", 0, $info);
                }
                $this->createBaseResponse("设置失败", 0, $info);
            }
        }catch (Exception $e){
            $this->createBaseResponse("获取数据失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据失败", 5001);

    }


    public  function delChannelComplement(){
        $id = request()->get('id');

        try{
            $info = AccountProductCustom::where(['id' => $id])->first();
            if($info){
                //$up = AccountProductCustom::where(['id' => $id])->update(['delete_at' => time(), 'update_at' => time()]);
                $this->addExtendRequestLog($info->toArray());
                $up = AccountProductCustom::where(['id' => $id])->delete();
                if($up){
                    $this->createBaseResponse("删除成功", 0, $info);
                }
                $this->createBaseResponse("删除失败", 0, $info);
            }
        }catch (Exception $e){
            $this->createBaseResponse("获取数据失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据失败", 5001);
    }

    //fl 自有分流配置
    public function getShuntList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $channel_id = request()->post('channel_id', '');
        $apikey = request()->post('apikey', '');
        $offset = ($page - 1) * $limit;
        $where = ['account_product_custom.type' => 70];
        if($channel_id){
            $where['account_product_custom.extend'] = (string)$channel_id;
        }

        $channel_list = Channel::select(['channel_id', 'label', 'name'])->get()->toArray();
        $channel_list = array_column($channel_list, 'label', 'channel_id');
        $fields = ['account_product_custom.*','account.account_name'];
        $res = AccountProductCustom::getComplementList($where, $fields, $offset, $limit, $apikey);
        foreach ($res['data'] as &$val){
            if(empty($val['account_name'])){
                $val['account_name'] = 'default';
            }

            $data = json_decode($val['data'], true);
            if (JSON_ERROR_NONE != json_last_error()) {
                $val['data'] = '配置存在解析错误';
            }

            $val['shunt'] = $data['shunt'] ?? 0;
            $val['channel_id'] = $val['extend'];
            $val['channel_name'] = $channel_list[$val['channel_id']];
            $val['update_at'] = $val['update_at'] ? date('Y-m-d H:i:s', $val['update_at']) : '';
            $val['create_at'] = $val['create_at'] ? date('Y-m-d H:i:s', $val['create_at']) : '';

        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);

    }

    public function saveChannelShunt(){
        $params = request()->post();
        if(!in_array(intval($params['father_id']), [210, 1000])) {
            $this->createBaseResponse("父产品ID只能为210或1000", 5003);
        }

        $id = $params['id']?:0;
        unset($params['id']);

        try{
            if($id > 0){//编辑
                $op = '编辑';
                $up = AccountProductCustom::updaetShuntRecord(['id' => $id], $params);
                if($up){
                    $this->createBaseResponse("编辑成功");
                }
            }else{//添加
                $op = '添加';
                $insert = AccountProductCustom::addShuntRecord($params);
                if($insert){
                    $this->createBaseResponse("添加成功");
                }
            }
        }catch (Exception $e){
            $this->createBaseResponse($op."失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse($op."失败", 5001);

    }

    public function getChannelShunt(){
        $id = request()->get('id');

        try{
            $info = AccountProductCustom::where(['id' => $id])->first();
            if($info){
                $info['create_at'] = $info['create_at'] ? date('Y-m-d H:i:s', $info['create_at']) : '';
                $info['update_at'] = $info['update_at'] ? date('Y-m-d H:i:s', $info['update_at']) : '';
                if(empty($info['data'])){
                    $info['timeout'] = 0;
                    $info['shunt'] = 0;
                }else{
                    $data = json_decode($info['data'], true);
                    if (JSON_ERROR_NONE != json_last_error()) {
                        throw new \Exception('配置解析错误');
                    }
                    $info['timeout'] = $data['timeout'] ?? 0;
                    $info['shunt'] = $data['shunt'] ?? 0;
                }
                $info['channel_id'] = intval($info['extend']);

                $this->createBaseResponse("获取数据成功", 0, $info);
            }
        }catch (Exception $e){
            $this->createBaseResponse("获取数据失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据失败", 5001);

    }

    public  function setChannelShunt(){
        $id = request()->get('id');
        $shunt = request()->get('shunt');

        try{
            $info = AccountProductCustom::where(['id' => $id])->first();
            if($info){
                if(empty($info['data'])){
                    throw new \Exception('配置有误');
                }else{
                    $data = json_decode($info['data'], true);
                    if (JSON_ERROR_NONE != json_last_error()) {
                        throw new \Exception('配置解析错误');
                    }
                    $set['timeout'] = $data['timeout'] ?? 0;
                    $set['shunt'] = intval($shunt);
                    $up = AccountProductCustom::where(['id' => $id])->update(['data' => json_encode($set), 'update_at' => time()]);
                }

                if(isset($up) && $up){
                    $this->createBaseResponse("设置成功", 0, $info);
                }
                $this->createBaseResponse("设置失败", 0, $info);
            }
        }catch (Exception $e){
            $this->createBaseResponse("获取数据失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据失败", 5001);

    }


    public function getCMCCScoreList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $product_id = request()->post('product_id', '');
        $apikey = request()->post('apikey', '');
        $offset = ($page - 1) * $limit;
        $where = ['account_product_custom.type' => 50];
        if($product_id){
            $where['account_product_custom.extend'] = $product_id;
        }

        //$channel_list = Channel::select(['channel_id', 'label', 'name'])->get()->toArray();
        //$channel_list = array_column($channel_list, 'label', 'channel_id');
        $fields = ['account_product_custom.*','account.account_name'];
        $res = AccountProductCustom::getCMCCScoreList($where, $fields, $offset, $limit, $apikey);
        foreach ($res['data'] as &$val){
            if(empty($val['account_name'])){
                $val['account_name'] = 'default';
            }

            $data = json_decode($val['data'], true);
            if (JSON_ERROR_NONE != json_last_error()) {
                $val['min_org'] = '配置存在解析错误';
                $val['max_org'] = '配置存在解析错误';
                $val['min_trans'] = '配置存在解析错误';
                $val['max_trans'] = '配置存在解析错误';
            }else{
                $val['min_org'] = $data['min_org'];
                $val['max_org'] = $data['max_org'];
                $val['min_trans'] = $data['min_trans'];
                $val['max_trans'] = $data['max_trans'];
            }

            $val['product_id'] = $val['extend'];
            $val['product_name'] = RedisCache::instance('productId_productName_mapping')->get($val['product_id']);
            $val['update_at'] = $val['update_at'] ? date('Y-m-d H:i:s', $val['update_at']) : '';
            $val['create_at'] = $val['create_at'] ? date('Y-m-d H:i:s', $val['create_at']) : '';

        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);

    }


    public function saveCMCCScore(){
        $params = request()->post();

        $father_id = $product_name = RedisCache::instance('productId_fatherId_mapping')
            ->get($params['product_id']);
        if($father_id != 1000) {
            $this->createBaseResponse("所选产品不是父产品1000下的产品", 5003);
        }

        $id = $params['id']?:0;
        unset($params['id']);

        try{
            if($id > 0){//编辑
                $op = '编辑';
                $up = AccountProductCustom::updaetCMCCScoreRecord(['id' => $id], $params);
                if($up){
                    $this->createBaseResponse("编辑成功");
                }
            }else{//添加
                $op = '添加';
                $insert = AccountProductCustom::addCMCCScoreRecord($params);
                if($insert){
                    $this->createBaseResponse("添加成功");
                }else{
                    $this->createBaseResponse("添加失败,该账号和产品可能已存在，请核查");
                }
            }
        }catch (Exception $e){
            //dd($e->getLine(), $e->getFile(), $e->getCode());
        //}catch (Exception $e){
            $this->createBaseResponse($op."失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse($op."失败", 5001);

    }

    public function getCMCCScore(){
        $id = request()->get('id');

        try{
            $info = AccountProductCustom::where(['id' => $id])->first();
            if($info){
                $info['create_at'] = $info['create_at'] ? date('Y-m-d H:i:s', $info['create_at']) : '';
                $info['update_at'] = $info['update_at'] ? date('Y-m-d H:i:s', $info['update_at']) : '';
                if(empty($info['data'])){
                    $info['min_org'] = 0;
                    $info['max_org'] = 0;
                    $info['min_trans'] = 0;
                    $info['max_trans'] = 0;
                }else{
                    $data = json_decode($info['data'], true);
                    if (JSON_ERROR_NONE != json_last_error()) {
                        throw new \Exception('配置解析错误');
                    }

                    $info['min_org'] = $data['min_org'] ?? 0;
                    $info['max_org'] = $data['max_org'] ?? 0;
                    $info['min_trans'] = $data['min_trans'] ?? 0;
                    $info['max_trans'] = $data['max_trans'] ?? 0;
                }
                $info['product_id'] = $info['extend'];

                $this->createBaseResponse("获取数据成功", 0, $info);
            }
        }catch (Exception $e){
            $this->createBaseResponse("获取数据失败:".$e->getMessage(), 5002);
        }

        $this->createBaseResponse("获取数据失败", 5001);

    }


    public function delCMCCScore(){
        $id = request()->get('id', '');

        $info = AccountProductCustom::where(['id' => $id])->first()->toArray();
        if(empty($info)){
            $this->createBaseResponse("非法操作", 5001);
        }
        $home = base_path();
        $logpath = $home.'/storage/logs/'.date('Ym').'_cmcc_score.log';

        try{
            file_put_contents($logpath, '操作时间:'.date('Y-m-d H:i:s').'删除记录:'.json_encode($info).PHP_EOL, FILE_APPEND);
            $up = AccountProductCustom::where(['id' => $id])->delete();
            if($up){
                $this->createBaseResponse("删除成功", 0);
            }

            $this->createBaseResponse("删除失败:", 5005);

        }catch (Exception $e){
            $this->createBaseResponse("删除数据失败:".$e->getMessage(), 5006);
        }
    }

    public function addExtendRequestLog($info)
    {
        $params = request()->all();
        $params = array_merge($params, $info);
        $res = SystemAccessLog::addLog($params, $this->sysName);
        return $res;
    }


    public function getQualicationList(){
         try{
             $channelQualication = new ChannelQualicationRepository();
             $list = $channelQualication->getChannelQualication();
             $this->createDataResponse($list);
         }catch (Exception $e){
             $this->createBaseResponse("获取渠道资质列表异常:".$e->getMessage(), 1);
         }
    }


    public function addQualication(){
        try{
            $channelQualication = new ChannelQualicationRepository();
            $res = $channelQualication->addChannelQualication();
            if ($res){
                $this->createBaseResponse("添加渠道资质成功", 0);
            }
        }catch (Exception $e){
            $this->createBaseResponse("添加渠道资质异常:".$e->getMessage(), 1);
        }
    }

    public function editQualication(){
        try{
            $channelQualication = new ChannelQualicationRepository();
            $res = $channelQualication->editChannelQualication();
            if ($res){
                $this->createBaseResponse("修改渠道资质成功", 0);
            }
        }catch (Exception $e){
            $this->createBaseResponse("修改渠道资质异常:".$e->getMessage(), 1);
        }
    }


    public function downQualicationDetailFile(){
        try{
            $channelQualication = new ChannelQualicationRepository();
            $path = $channelQualication->downQualicationDetailFile();
            return response()->download($path);
        }catch (Exception $e){
            $this->createBaseResponse("下载文件异常:".$e->getMessage(), 1);
        }
    }


    public function delQualicationDetailFile(){
        try{
            $channelQualication = new ChannelQualicationRepository();
            $res = $channelQualication->delQualicationDetailFile();
            if ($res){
                $this->createBaseResponse("删除文件成功", 0);
            }
        }catch (Exception $e){
            $this->createBaseResponse("删除文件异常:".$e->getMessage(), 1);
        }
    }

    public function getQualicationOptions(){
        try{
            $channelQualication = new ChannelQualicationRepository();
            $list = $channelQualication->getQualicationOptions();
            $this->createDataResponse($list);
        }catch (Exception $e){
            $this->createBaseResponse("获取渠道资质options异常:".$e->getMessage(), 1);
        }
    }


}