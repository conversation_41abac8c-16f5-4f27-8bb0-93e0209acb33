<?php

namespace App\Http\Controllers;

use App\Http\Repository\ApprovalRepository;
use App\Http\Repository\ChannelCostAdjustRepository;
use App\Http\Repository\ContractRepositorie;
use App\Models\SystemSession;
use App\Models\SystemUser;
use App\Utils\Helpers\Func;
use Exception;
use Illuminate\Http\JsonResponse;

/**
 *  审批流
 * @package App\Http\Controllers
 */
class ChannelCostAdjustController extends CommonController
{
    private $repository;

	public function __construct(ChannelCostAdjustRepository $repository)
	{
		parent::__construct();
        $this->repository = $repository;
	}


    public function batchAdd(){
        try {
            $res = $this->repository->batch_add();
            return $this->response(0,$res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

    public function Add(){
        try {
            $res = $this->repository->add();
            return $this->response(0,$res);
        } catch (Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

}