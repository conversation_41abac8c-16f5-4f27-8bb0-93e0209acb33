<?php

namespace App\Http\Controllers;

use App\Http\Repository\CommissionRepository;
use App\Models\SystemSession;
use Illuminate\Support\Facades\Log;

/**
 *  销售提成计算任务
 * @package App\Http\Controllers
 */
//class ToolController extends Controller
class CommissionController extends CommonController
{
	protected $repository;
	
	public function __construct()
	{
		parent::__construct();
		$this->repository = new CommissionRepository();
	}

    public function getCommissionTaskList()
    {
        try {
            $data = $this->repository->getCommissionTaskList();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }
	
	public function saveCommissionTask()
    {
        try {
			$session_id = request()->post('user_cookie','');
            $data = $this->repository->saveCommissionTask((new SystemSession())->getNameBySessionId($session_id));
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }

	public function deleteCommissionTask(){
		try {
			$run_status = $this->repository->getRunStatus();
			if(!in_array($run_status,[1,4])){// 只能删除未计算和出错的
				return $this->response(1,[],'任务状态不允许删除');
			}
			$session_id = request()->post('user_cookie','');
            $data = $this->repository->deleteCommissionTask((new SystemSession())->getNameBySessionId($session_id));
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
	}

	public function downloadFile()
    {
        try {
            $data = $this->repository->getRunFile();
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage().$e->getFile().$e->getLine());
        }
    }
	
}