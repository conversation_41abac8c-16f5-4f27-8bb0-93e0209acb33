<?php

namespace App\Http\Controllers\Feishu;

use App\Http\Repository\ReportDay\StatRepository;
use App\Utils\Helpers\Func;
use <PERSON><PERSON>\Lumen\Routing\Controller as Controller;
use App\Repositories\FeishuRepository;


class FeishuAuthController extends Controller
{

    public function getFeiShuUserToken(){
        $code = request()->post('auth_code', '');
        try {
            $repository = new FeishuRepository();
            $res = $repository->getFeiShuUserTokenByCode($code);
            if(isset($res['code']) && $res['code'] == 0){
                return $this->response(0,  $res['data']);
            }

            return $this->response($res['code'], [], $res['msg']);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getFile().';'.$exception->getMessage());
        }
    }

    public function getFeiShuUserInfo(){
        $access_token = request()->post('access_token', '');

        try {
            $repository = new FeishuRepository();
            $res = $repository->getFeiShuUserInfoByAccessToken($access_token);
            if(isset($res['code']) && $res['code'] == 0){
                $white_list = config('feishu.mobile_report_config.white_list');
                if(in_array($res['data']['name'], $white_list)){
//                    $statRep = new StatRepository();
                    $token = Func::createJwtToken(config('feishu.mobile_report_config.jwt_token_secret'), $res['data']);
                    return $this->response(0,  ['user_token' => $token]);
                }else{
                    return $this->response(403,  '', '无权限');
                }

            }

            return $this->response($res['code'], [], $res['msg']);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getFile().';'.$exception->getMessage());
        }
    }

    /*
    public function getTicketFromMobile(){
        $token = request()->header('mobile-token');
        if(empty($token)){
            return $this->response(100, [], 'ticket获取失败');
        }

        $statRep = new StatRepository();
        $payload = $statRep->decodeJwt($token, config('feishu.mobile_report_config.jwt_token_secret'));

        if(!empty($payload) && isset($payload['data']['name'])){
            $ticket = $statRep->createJwtToken(config('feishu.mobile_report_config.jwt_ticket_secret'), $payload['data'], config('feishu.mobile_report_config.jwt_ticket_expire'));
            return $this->response(0,  ['ticket' => $ticket]);
        }

        return $this->response(100, [], 'ticket获取失败');
    }

    public function getUserInfoByTicket(){
        $ticket = request()->header('ticket');
        if(empty($ticket)){
            return $this->response(100, [], '用户信息获取失败-1');
        }

        $validate_type = request()->post('validate_type', '');
        if(empty($validate_type) || $validate_type != 'ticket'){
            return $this->response(100, [], '用户信息获取失败-2');
        }

        $statRep = new StatRepository();
        $payload = $statRep->decodeJwt($ticket, config('feishu.mobile_report_config.jwt_ticket_secret'));
        if(!empty($payload) && isset($payload['exp'])){
            if(time() > $payload['exp']){
                return $this->response(100, [], '用户信息获取失败-3');
            }
        }else{
            return $this->response(100, [], '用户信息获取失败-4');
        }

        if(isset($payload['data']['name']) && isset($payload['data']['email'])){
            return $this->response(0,  $payload['data']);
        }

        return $this->response(100, [], '用户信息获取失败-5');
    }
    */

    public function response($status = 0, $data = [], $msg = '')
    {
        //设置请求头
        $msg = $this->msg[$status] ?? $msg;
        $response = compact('msg', 'status', 'data');
//        return response()->json($response)->send();
        return response()->json($response);
    }

}
