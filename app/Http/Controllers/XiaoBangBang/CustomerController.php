<?php
namespace App\Http\Controllers\XiaoBangBang;

use App\Http\Controllers\CommonController;
use App\Models\Customer;
use App\Models\SystemUser;
use App\Models\XbbCustomer;
use App\Repositories\Customer\CustomerGroupRepositorie;
use Illuminate\Http\JsonResponse;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CustomerController extends CommonController
{
    /**
     * 获取从销帮帮同步过来的客户列表数据
     * @return JsonResponse
     */
    public function getList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $group_name = request()->post('group_name', '');
        $company = request()->post('company', '');
        $customer_name = request()->post('customer_name', '');
        $status = request()->post('status', '');
        $is_allow_update = request()->post('is_allow_update', '');
        $offset = ($page - 1) * $limit;

        $where = [];
        if($group_name){
            $where[] = ['group_name', 'like', '%'.$group_name.'%'];
        }
        if($company){
            $where[] = ['company', 'like', '%'.$company.'%'];
        }
        if($customer_name){
            $where[] = ['customer_name', 'like', '%'.$customer_name.'%'];
        }
        if($status){
            $where[] = ['status', '=', $status];
        }
        if($is_allow_update){
            $where[] = ['is_allow_update', '=', $is_allow_update];
        }

        $user_data = SystemUser::select(['username', 'realname'])->where(['disabled' => 1])->get()->toArray();
        $user_data = array_column($user_data, 'realname', 'username');
        $res = XbbCustomer::getListByPage($where, ['*'], $offset, $limit);
        foreach ($res['data'] as &$val){
            if($val['status'] == 2){
                $val['status_name'] = '已转换';
            }else if($val['status'] == 3){
                $val['status_name'] = '标记无效';
            }else{
                $val['status_name'] = '未转换';
            }

            if($val['type'] == 2){
                $val['team_name'] = '银行';
            }else{
                $val['team_name'] = '非银';
            }
            $val['salesman_name'] = $user_data[$val['salesman']] ?? '';
            $val['update_at'] = $val['update_at'] ? date('Y-m-d H:i:s', $val['update_at']) : '';
            $val['create_at'] = $val['create_at'] ? date('Y-m-d H:i:s', $val['create_at']) : '';
        }

        $this->createDataResponse(['list' => $res['data'], 'count' => $res['count'] ]);
    }


    public function convertCheck(){
        $group_name = request()->post('group_name', '');
        $company = request()->post('company', '');
        $customer_name = request()->post('customer_name', '');
        $level_scale = request()->post('level_scale', '');
        $level_income = request()->post('level_income', 0);
        $level_scale_income = request()->post('level_scale_income', '');
        $xbb_data_id = request()->post('xbb_data_id', 0);

        //xbb_data_id存在，则更新(更新信息：三要素、客户级别)
        $info = Customer::getCustomerInfoByXbbId($xbb_data_id);
        if(!empty($info)){
            $res = $this->checkCustomerChange($info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income);
            return $this->response($res['status'], $res['data'], $res['msg']);
        }

        //xbb_data_id不存在，判断三要素、二要素是否存在
        //三要素存在，更新数据(更新信息：客户级别、xbb_data_id)
        //根据三要素匹配客户信息
        $info = $this->getCustomerInfoByThreeElements($group_name, $company, $customer_name);
        if(!empty($info)){
            $res = $this->checkCustomerChange($info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income);
            return $this->response($res['status'], $res['data'], $res['msg']);
        }

        //三要素不存在，判断二要素是否存在
        //二要素存在，更新数据(新增：主体，更新信息：客户级别、xbb_data_id)
        //根据二要素匹配客户信息
        $info = $this->getCustomerInfoByTwoElements($company, $customer_name);
        if(!empty($info)){
            $res = $this->checkCustomerChange($info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income);
            return $this->response($res['status'], $res['data'], $res['msg']);
        }

        //二要素也不存在，新增数据(新增：三要素、客户级别、商务、xbb_data_id)
        //新增的时判断是否连带主体新增
        $group = Customer\CustomerGroup::getOneByCondition(['group_name' => $group_name]);
        $group_id = $group['group_id'] ?? '';
        if(empty($group_id)){
            return $this->response(0, ['新增一条客户记录,且连带新增一个内部统称'], '成功');
        }

        return $this->response(0, ['新增一条客户记录,不涉及新增内部统称'], '成功');
    }

    public function convertDone(){
        $group_name = request()->post('group_name', '');
        $company = request()->post('company', '');
        $customer_name = request()->post('customer_name', '');
        $level_scale = request()->post('level_scale', '');
        $level_income = request()->post('level_income', 0);
        $level_scale_income = request()->post('level_scale_income', '');
        $xbb_data_id = request()->post('xbb_data_id', 0);

        Log::info($xbb_data_id.'转化操作入参', request()->post());

        //xbb_data_id存在，则更新(更新信息：三要素、客户级别)
        $info = Customer::getCustomerInfoByXbbId($xbb_data_id);
        if(!empty($info)){
            $res = $this->updateCustomerInfo($xbb_data_id, $info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income);
            return $this->response($res['status'], [], $res['msg']);
        }

        //xbb_data_id不存在，判断三要素、二要素是否存在
        //三要素存在，更新数据(更新信息：客户级别、关联xbb_data_id)
        //根据三要素匹配客户信息
        $info = $this->getCustomerInfoByThreeElements($group_name, $company, $customer_name);
        if(!empty($info)){
            $res = $this->updateCustomerInfo($xbb_data_id, $info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income);
            return $this->response($res['status'], [], $res['msg']);
        }

        //三要素不存在，判断二要素是否存在
        //二要素存在，更新数据(新增：主体，更新信息：客户级别、关联xbb_data_id)
        //根据二要素匹配客户信息
        $info = $this->getCustomerInfoByTwoElements($company, $customer_name);
        if(!empty($info)){
            $res = $this->updateCustomerInfo($xbb_data_id, $info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income);
            return $this->response($res['status'], [], $res['msg']);
        }

        //二要素也不存在，新增数据(新增：三要素、客户级别、商务、xbb_data_id)
        //新增的时判断是否连带主体新增
        $group_id = $this->getGroupId($xbb_data_id, $group_name);
        Log::info($xbb_data_id.'转化操作跳转至客户添加页面');

        return $this->response(200, ['group_id' => $group_id], '成功');
    }

    public function makeInvalid(){
        $id = request()->post('id', 0);

        Log::info('标记无效操作入参:'.$id);
        $xbb_info = XbbCustomer::getCustomerInfoById($id);
        if(empty($xbb_info)){
            return $this->response(401, [], '传参异常');
        }

        if($xbb_info['status'] != 1){
            return $this->response(402, [], '只有未转换状态,才允许标记无效');
        }

        //更新xbb_customer表信息
        //更新前信息
        $up_data_before = [
            'status' => $xbb_info['status'],
            'admin' => $xbb_info['admin'],
        ];
        $admin = $this->sysName;
        //更新后信息
        $up_data_after = [
            'status' => XbbCustomer::STATUS_INVALID,
            'update_at' => time(),
            'admin' => $admin,
        ];
        $up = XbbCustomer::where(['id' => $xbb_info['id']])->update($up_data_after);
        Log::info('标记无效操作更新xbb_customer:'.$id, ['up_data_before' => $up_data_before, 'up_data_after' => $up_data_after, 'result' => $up]);
        return $this->response(0, [], '成功');
    }

    public function getCustomerInfoByThreeElements($group_name, $company, $customer_name){
        $group = Customer\CustomerGroup::getOneByCondition(['group_name' => $group_name]);
        $group_id = $group['group_id'] ?? '';
        if(empty($group_id)){
            return [];
        }

        $info = Customer::getOneByCondition(['is_delete' => 0, 'group_id' => $group_id, 'company' => $company, 'name' => $customer_name]);
        return $info;
    }

    public function getCustomerInfoByTwoElements($company, $customer_name){
        $info = Customer::getOneByCondition(['is_delete' => 0, 'company' => $company, 'name' => $customer_name]);
        return $info;
    }

    public function updateCustomerInfo($xbb_data_id, $info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income){
        $nowtime = time();
        $admin = $this->sysName;
        //开启事务
        DB::beginTransaction();
        try {
            $group = Customer\CustomerGroup::getOneByCondition(['group_name' => $group_name]);
            $group_id = $group['group_id'] ?? '';
            $insert = true;
            if(empty($group_id)){
                //需新增主体
                $group_rep = new CustomerGroupRepositorie();
                $group_id = $group_rep->createGroupId();
                $insert = Customer\CustomerGroup::add($group_id, $group_name, $admin);
                Log::info($xbb_data_id.'转化操作新增主体', [$group_id, $group_name, $admin, $insert]);
            }

            //更新customer表信息
            //更新前信息
            $up_data_before1 = [
                'group_id' => $info['group_id'],
                'company' => $info['company'],
                'name' => $info['name'],
                'level_scale' => $info['level_scale'],
                'level_income' => $info['level_income'],
                'level_scale_income' => $info['level_scale_income'],
                'xbb_data_id' => $info['xbb_data_id'],
                'update_at' => $info['update_at'],
                'admin' => $info['admin'],
            ];
            //更新后信息
            $up_data_after1 = [
                'group_id' => $group_id,
                'company' => $company,
                'name' => $customer_name,
                'level_scale' => $level_scale,
                'level_income' => $level_income,
                'level_scale_income' => $level_scale_income,
                'xbb_data_id' => $xbb_data_id,
                'update_at' => $nowtime,
                'admin' => $admin
            ];
            $up1 = Customer::where(['id' => $info['id']])->update($up_data_after1);
            Log::info($xbb_data_id.'转化操作更新customer', ['id' => $info['id'], 'up_data_before1' => $up_data_before1, 'up_data_after1' => $up_data_after1, 'result' => $up1]);

            //更新xbb_customer表信息
            $xbb_info = XbbCustomer::getCustomerInfoByXbbId($xbb_data_id);
            $transfer_number = $xbb_info['transfer_number'] + 1;
            //更新前信息
            $up_data_before2 = [
                'status' => $xbb_info['status'],
                'is_allow_update' => $xbb_info['is_allow_update'],
                'transfer_number' => $xbb_info['transfer_number'],
                'update_at' => $xbb_info['update_at'],
                'admin' => $xbb_info['admin'],
            ];
            //更新后信息
            $up_data_after2 = [
                'status' => XbbCustomer::STATUS_CONVERTED,
                'is_allow_update' => 0,
                'transfer_number' => $transfer_number,
                'update_at' => $nowtime,
                'admin' => $admin,
            ];
            $up2 = XbbCustomer::where(['id' => $xbb_info['id']])->update($up_data_after2);
            Log::info($xbb_data_id.'转化操作更新xbb_customer', ['id' => $xbb_info['id'], 'up_data_before2' => $up_data_before2, 'up_data_after2' => $up_data_after2, 'result' => $up2]);

            if($insert && $up1 && $up2){
                DB::commit();
                Log::info($xbb_data_id.'转化操作事物提交');
                return ['status' => 0, 'msg' => '转化成功'];
            }else{
                DB::rollBack();
                Log::info($xbb_data_id.'转化操作事物回滚', [$insert, $up1, $up2]);
                return ['status' => 101, 'msg' => '转化失败'];
            }
        }catch (Exception $e){
            DB::rollBack();
            Log::info($xbb_data_id.'转化操作事物回滚:'.$e->getMessage());
            return ['status' => 501, 'msg' => '转化异常:'.$e->getMessage()];
        }
    }

    public function getGroupId($xbb_data_id, $group_name){
        $group = Customer\CustomerGroup::getOneByCondition(['group_name' => $group_name]);
        $group_id = $group['group_id'] ?? '';
        if(empty($group_id)){
            //需新增主体
            $group_rep = new CustomerGroupRepositorie();
            $group_id = $group_rep->createGroupId();
            $admin = $this->sysName;
            $res = Customer\CustomerGroup::add($group_id, $group_name, $admin);
            Log::info($xbb_data_id.'转化操作新增主体', [$group_id, $group_name, $admin, $res]);
        }

        return $group_id;
    }

    public function checkCustomerChange($info, $group_name, $company, $customer_name, $level_scale, $level_income, $level_scale_income){
        $data = [];
        $group = Customer\CustomerGroup::getOneByCondition(['group_id' => $info['group_id']]);
        $info['group_name'] = $group['group_name'] ?? '';
        if($info['group_name'] == $group_name){
            $data[] = '内部统称无变化';
        }else{
            $data[] = empty($info['group_name']) ? '内部统称会新增' : '内部统称有变化';
        }

        if($info['company'] == $company){
            $data[] = '公司全称无变化';
        }else{
            $data[] = '公司全称有变化';
        }

        if($info['name'] == $customer_name){
            $data[] = '公司简称无变化';
        }else{
            $data[] = '公司简称有变化';
        }

        if($info['level_scale'] == $level_scale && $info['level_income'] == $level_income  && $info['level_scale_income'] == $level_scale_income){
            $data[] = '客户级别无变化';
        }else{
            $data[] = '客户级别有变化';
        }

        return ['status' => 0, 'data' => $data, 'msg' => '成功'];
    }

}