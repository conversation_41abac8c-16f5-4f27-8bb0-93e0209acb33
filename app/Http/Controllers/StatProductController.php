<?php

namespace App\Http\Controllers;

use App\Http\Repository\StatBaseRepository;
use App\Http\Repository\StatChannelMainRepository;
use App\Http\Repository\StatChannelProfitRepository;
use App\Http\Repository\StatChannelRepository;
use App\Http\Repository\StatCompareCustomerRepository;
use App\Http\Repository\StatCompareProductRepository;
use App\Http\Repository\StatCustomerMainRepository;
use App\Http\Repository\StatCustomerRepository;
use App\Http\Repository\StatMixRepository;
use App\Http\Repository\StatPeroidRepository;
use App\Http\Repository\StatProductRepository;
use App\Http\Repository\StatMainRepository;
use App\Http\Repository\StatCompareRepository;
use App\Http\Repository\StatJdskRepository;
use App\Http\Repository\StatChartRepository;
use App\Http\Repository\StatProductMainRepository;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\DeptGrade;
use App\Models\Product;
use App\Models\StatisticsGatherCustomer;
use App\Models\StatisticsGatherProduct;
use App\Repositories\Income\CustomerIncomeRepository;

//class StatProductController extends BaseController
class StatProductController extends CommonController
{
    private $monthLables = [
        1 => '客户月度权责收入及毛利统计',
        2 => '各产品月度权责收入及毛利数据统计',
        3 => '渠道维度统计',
        4 => '每月权责收入及数据成本统计',
    ];

    private $qLables = [
        1 => '各产品各季度权责收入及毛利数据统计',
        2 => '每季度权责收入及数据成本统计',
    ];


    /**
     * 主产品统计
     */
    public function statMainList()
    {
        try {
            $repository = new StatMainRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 产品维度统计
     */
    public function statProductList()
    {
        try {
            $repository = new StatProductRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品维度统计
     */
    public function statProductWeekList()
    {
        try {
            $repository = new StatProductRepository();
            $data = $repository->statProductWeekList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 客户维度统计
     */
    public function statCustomerList()
    {
        try {
            $repository = new StatCustomerRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 客户、主产品维度统计
     */
    public function statCustomerMainList()
    {
        try {
            $repository = new StatCustomerMainRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getFile().$exception->getLine().$exception->getMessage());
        }
    }
    /*
     * 京东数科 月度报告
     */
    public function statJdskData()
    {
        try{
            $repository = new StatJdskRepository();
            $data = $repository->getJdsk();
            return $this->response(0, $data);
        }catch (\Exception $exception){
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 渠道、主产品维度统计
     */
    public function statChannelMainList()
    {
        $params = request()->post();
        $export_type = $params['export_type'] ?? '';//导出类型
        try {
            $repository = new StatChannelMainRepository();
            if($export_type){
                $num = $repository->getDiffMonthNum($params['start_date'], $params['end_date']);
                if($num > 12){
                    return $this->response(100, [], '导出数据月份不能跨度超过12个月');
                }
                $data = $repository->statListExport();
            }else{
                $data = $repository->statList();
            }

            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 客户维度对比
     */
    public function statCompare()
    {
        try {
            $repository = new StatCompareRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 客户维度对比
     */
    public function statCustomerCompare()
    {
        try {
            $repository = new StatCompareCustomerRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品维度对比
     */
    public function statProductCompare()
    {
        try {
            $repository = new StatCompareProductRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 渠道维度统计
     */
    public function statChannelList()
    {
        try {
            $repository = new StatChannelRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 渠道维度统计
     */
    public function statChannelCompare()
    {
        try {
            $repository = new StatChannelRepository();
            $data = $repository->statCompareList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage().$exception->getFile().$exception->getLine());
        }
    }



    public function getMonthLables()
    {
        return $this->response(0, $this->monthLables);
    }

    public function getQuarterLables()
    {
        return $this->response(0, $this->qLables);
    }

    /**
     * 月度数据统计-客户维度
     */
    public function statCustomerByMonth()
    {
        $params = request()->post();
        $params['month'] = empty($params['month']) ? date('Ym') : str_replace('-', '', $params['month']);
        $params['customer_id'] = empty($params['customer_id']) ? '' : $params['customer_id'];
        try {
            $repository = new StatCustomerRepository();
            $data = $repository->statMonthList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 月度数据统计-产品维度
     */
    public function statProductByMonth()
    {
        $params = request()->post();
        //'202101'
        $params['month'] = empty($params['month']) ? date('Ym') : str_replace('-', '', $params['month']);
        $params['father_id'] = empty($params['father_id']) ? '' : $params['father_id'];
        try {
            $repository = new StatProductRepository();
            $data = $repository->statProductMonthList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 月度数据统计-渠道维度
     */
    public function statChannelByMonth()
    {
        $params = request()->post();
        //202001
        $params['month'] = empty($params['month']) ? date('Ym') : str_replace('-', '', $params['month']);
        $params['channel_id'] = empty($params['channel_id']) ? '' : $params['channel_id'];
        try {
            $repository = new StatChannelRepository();
            $data = $repository->statMonthList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 月度数据统计-每月收入和成本
     */
    public function statByMonth()
    {
        $params = request()->post();
        $params['year'] = empty($params['year']) ? date('Y') : $params['year'];
        try {
            $repository = new StatProductRepository();
            $data = $repository->statMonthList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 季度数据统计-各产品各季度权责收入及毛利数据统计
     */
    public function statProductByQuarter()
    {
        $params = request()->post();
        $params['year'] = empty($params['year']) ? date('Y') : $params['year'];
        $params['father_id'] = empty($params['father_id']) ? '' : $params['father_id'];
        $params['start_q'] = empty($params['start_q']) ? 1 : (int)$params['start_q'];//起始季度
        $params['end_q'] = empty($params['end_q']) ? 1 : (int)$params['end_q'];//结束季度
        if($params['start_q']>$params['end_q']){
            return $this->response(100, [], '选择季度范围有误');
        }

        try {
            $repository = new StatProductRepository();
            $data = $repository->statProductQuarterList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    public function statByQuarter()
    {
        $params = request()->post();
        $params['year'] = empty($params['year']) ? date('Y') : $params['year'];
        try {
            $repository = new StatProductRepository();
            $data = $repository->statQuarterList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }

    /**
     * 客户统计趋势图
     */
    public function statCustomerChart()
    {
        try {
            $repository = new StatCustomerMainRepository();
            $data = $repository->statChartList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品统计趋势图
     */
    public function statProductChart()
    {
        try {
            $repository = new StatCompareProductRepository();
            $data = $repository->statChartList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }


    //客户产品首次产生权责收入
    public function customerFirstIncome()
    {
        set_time_limit(0);
        try {
            $repository = new StatCustomerRepository();
            $data = $repository->customerFirstIncome();
            //return $this->response(0, $data);
            $this->createDataResponse(['list' => $data['data'], 'count' => $data['count'] ]);
        } catch (Exception $exception) {
            dd($exception->getMessage());
            $this->createBaseResponse($exception->getMessage(), 501, []);
            //return $this->response(100, [], $exception->getMessage());
        }
    }


    public function getPeroidList(){
        $params = request()->post();
        try {
            $repository = new StatPeroidRepository();
            $data = $repository->getPeroidList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }

    public function getHistoryPeroidList(){
        $params = request()->post();
        try {
            $repository = new StatPeroidRepository();
            $data = $repository->getHistoryPeroidList($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }

    }


    public function statChart()
    {
        try {
            $repository = new StatChartRepository();
            $data = $repository->statChartList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    public function statProduct()
    {
        try {
            $repository = new StatProductMainRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * excel导出
     */
    public function getExportDatas()
    {
        try {
            $type = request()->get('export', null);
            $type = explode('_', $type);
            $special = isset($type[1]) ? $type[1] : 0;
            switch ($type[0]) {
                case 'mainProduct':
                    $repository = new StatMainRepository();
                    $data = $repository->getDownlandMainProductDatas($special);
                    break;
                case 'mainProductByMonth':
                    $repository = new StatMainRepository();
                    $data = $repository->getDownlandMainProductDatasForMonth();
                    break;
                case 'customerByMonth':
                    $repository = new StatCustomerMainRepository();
                    $data = $repository->getDownlandCustomerDatasForMonth();
                    break;
                case 'customerByMonthAndSalesman':
                    $repository = new StatCustomerMainRepository();
                    $data = $repository->getDownlandCustomerDatasForMonthAndSalesman();
                    break;

            }
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 客户消耗
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCustomerCostDatas()
    {
        try {
            $params = request()->post();
            $cir_obj = new CustomerIncomeRepository();
            $data = $cir_obj->getCustomerIncomeTotalForMonth($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 添加商务预计回款备注
     * @return \Illuminate\Http\JsonResponse
     */
    public function addRemark()
    {
        try {
            $params = request()->post();
            $cir_obj = new CustomerIncomeRepository();
            $data = $cir_obj->addRemark($params);
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }
    /**
     * 确认消耗
     */
    public function customerCostAffirm(){
        try {
            $params = request()->post();
            $cir_obj = new CustomerIncomeRepository();
            $data = $cir_obj->affirm($params);
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
        }
    }

    /**
     * 客户收入波动
     */
    public function getCustomerIncomeWave(){
        try {
            $params = request()->post();
            $start_date = $params['start_date'] ?? '';
            $end_date = $params['end_date'] ?? '';

            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date)) {
                throw new \Exception("起始日期格式不正确");
            }
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
                throw new \Exception("结束日期格式不正确");
            }
            $params['start_date'] = str_replace('-', '', $start_date);
            $params['end_date'] = str_replace('-', '', $end_date);

            $cir_obj = new CustomerIncomeRepository();
            $data = $cir_obj->getCustomerIncomeWave($params);
            return $this->response(0, $data);
        } catch (\Exception $e) {
            return $this->response(100, [], $e->getMessage());
            //return $this->response(100, [], $e->getMessage().';'.$e->getFile().';'.$e->getLine());
        }
    }


    /**
     * 渠道收入、毛利率统计
     */
    public function statChannelProfit()
    {
        $params = request()->post();
        try {
            $repository = new StatChannelProfitRepository();

            $num = $repository->getDiffMonthNum($params['start_date'], $params['end_date']);
            if($num >=1){
                return $this->response(100, [], '导出数据月份不能跨度超过1个月');
            }

            $data = $repository->statChannelProfit();

            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    public function statChannelType(){
        $repository = new StatChannelProfitRepository();

        $data = $repository->getChannelTypeList();

        return $this->response(0, ['channelToTypeMap' => $data['channelToTypeMap'], 'channelTypeList' => $data['channelTypeList']]);
    }

    public function getSubPidsByProductView(){
        $father_id = request()->get('father_id', '');
        $repository = new StatChannelProfitRepository();

        $data = $repository->getProductListByProductView($father_id);
        return $this->response(0, $data);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     *
     * 融合页面列表数据
     */
    public function mixStatList(){
        try {
            $repository = new StatMixRepository();
            $data = $repository->statList();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getFile().$exception->getLine().$exception->getMessage());
        }
    }

    /**
     *  融合页面数据下载
     */
    public function mixStatDownload(){
        try {
            $repository = new StatMixRepository();
            return $repository->listDownload();
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage() . ', ' .$exception->getLine());
        }
    }

}