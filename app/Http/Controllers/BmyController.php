<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/30 0030
 * Time: 10:44
 */

namespace App\Http\Controllers;

use App\Http\Repository\BmyRepository;
use <PERSON><PERSON>\Lumen\Routing\Controller;

//class BmyController extends Controller
class BmyController extends CommonController
{
    public function getCustomerPrice(BmyRepository $repository)
    {
        $data = $repository->getAllCustomerPrice();
        return $this->getInfoResponse($data);
    }

    public function getUpstreamPrice(BmyRepository $repository)
    {
        $data = $repository->getAllUpstreamPrice();
        return $this->getInfoResponse($data);
    }

    public function getUpstreamStatus(BmyRepository $repository)
    {
        $data = $repository->getUpstreamStatus();
        return $this->getInfoResponse($data);
    }

    protected function getInfoResponse($data)
    {
        $status = 0;
        $msg    = '获取成功';
        return compact('status', 'msg', 'data');
    }


    public function recheckList(BmyRepository $repository){
        $result = $repository->recheckList();
        $this->createDataResponse(['list' => $result['data'], 'count' => $result['count'] ]);
    }

    //在网时长状态复核数据添加
    public function recheckSave(BmyRepository $repository){
        $result = $repository->recheckSave();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    //在网时长状态复核数据添加
    public function recheck(BmyRepository $repository){
        $result = $repository->recheck();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function recheckDel(BmyRepository $repository){
        $result = $repository->recheckDel();
        $this->createBaseResponse($result['msg'], $result['code'], $result['data']);
    }

    public function history(BmyRepository $repository){
        $result = $repository->history();
        $this->createDataResponse(['list' => $result['data'], 'count' => $result['count'] ]);
    }

    public function getSelfRuleMonitorList(BmyRepository $repository) {
        $params = request()->all();
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        $result = $repository->getSelfRuleMonitorList($params, $page, $limit);
        $this->createDataResponse($result);
    }

}