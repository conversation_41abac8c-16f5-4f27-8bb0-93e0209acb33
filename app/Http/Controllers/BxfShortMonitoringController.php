<?php

namespace App\Http\Controllers;

use App\Http\Repository\BxfShortMonitoringRepository;

//class BxfShortMonitoringController extends BaseController
class BxfShortMonitoringController extends CommonController
{
    protected $repository;

    public function __construct()
    {
        parent::__construct();
        $this->repository = new BxfShortMonitoringRepository();
    }

    public function scoreInfo()
    {
        try {
            $data = $this->repository->scoreInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    public function statisInfo()
    {
        try {
            $data = $this->repository->statisInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品监控-值分布
     */
    public function valueSpreadInfo()
    {
        try {
            $data = $this->repository->valueSpreadInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 产品监控-查得率
     */
    public function successRatioInfo()
    {
        try {
            $data = $this->repository->successRatioInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 渠道查得率
     */
    public function channelStatisInfo()
    {
        try {
            $data = $this->repository->channelStatisInfo();
            return $this->response(0, $data);
        } catch (\Exception $exception) {
            return $this->response(100, [], $exception->getMessage());
        }
    }

    /**
     * 邦信分快捷版账期通知
     */
    public function receiveData($key = '')
    {
        if($key != '99bbdb8426f8b4e8d0cc3ebd92484590'){
            return  response()->json(['status' => 1002, 'msg' => "参数有误", 'data' => []]);
        }
        $channel = $this->data['channel'] ?? '';
        //$uri = $this->data['uri'] ?? '';
        $channel_name = $this->data['channel_name'] ?? '';
        $period = $this->data['period'] ?? '';
        $params = compact('channel', 'channel_name', 'period');
        if(empty($params['channel']) || empty($params['period'])){
            return  response()->json(['status' => 1003, 'msg' => "渠道ID和账期不能为空", 'data' => []]);
        }
        try {
            $res = $this->repository->receiveData($params);
            if($res){
                return  response()->json(['status' => 1001, 'msg' => "成功", 'data' => []]);
            }
            return  response()->json(['status' => 1004, 'msg' => "失败", 'data' => []]);
        } catch (\Exception $exception) {
            return  response()->json(['status' => 1004, 'msg' => $exception->getMessage(), 'data' => []]);
        }
    }
}
