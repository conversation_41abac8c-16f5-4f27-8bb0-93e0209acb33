<?php

namespace App\Http\Controllers;


use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\ResponseTrait;
use App\Http\Controllers\Controller;
use http\Env\Request;
use  App\Models\Monitor\Manager;
use  App\Models\Monitor\DutyOperateLog;



//class MonitorConfirmController extends Controller
class MonitorConfirmController extends CommonController
{


	//use ResponseTrait, CurlTrait;

	public function  __construct()
    {
        parent::__construct();
        $this->DutyOperateLog = new DutyOperateLog();
    }

    //监控确认
    public function Confirm()
	{
        $username = Request()->get('username','');
        $status = Request()->get('status');
        $model = Request()->get('model');

        $uid = $this->getUuid($username);

        $data['mid'] = $uid;
        $data['status'] = $status;
        $data['pid'] = 210;
        $data['model'] = $model;
        $data['time'] = date('Y-m-d H:i:s',time());

        $res = $this->DutyOperateLog->insert($data);

        if ($res){
            $this->createBaseResponse('成功',0);
        }else{
            $this->createBaseResponse('失败',1);
        }

	}

	protected function getUuid($username)
    {
        $uid = Manager::getUidByName($username);
        return $uid['id'];
    }
	
}
