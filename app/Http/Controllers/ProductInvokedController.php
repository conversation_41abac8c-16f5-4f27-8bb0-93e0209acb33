<?php

namespace App\Http\Controllers;

use App\Http\Repository\ProductInvokedRepository;
use App\TraitUpgrade\ResponseTrait;
use Laravel\Lumen\Routing\Controller;

//class ProductInvokedController extends Controller
class ProductInvokedController extends CommonController
{
    use ResponseTrait;
    private $repository;
    public function __construct(ProductInvokedRepository $repository)
    {
        parent::__construct();
        $this->repository = $repository;
    }

    /**
     * 全量调用量入库
     * @return \Illuminate\Http\JsonResponse
     */
    public function dayInvoked()
    {
        try {
            $data = $this->repository->dayInvoked();
            return $this->response(compact('data'));
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        }
    }

    /**
     * 清除用量预警的flag
     */
    public function clearWarningFlag()
    {
        try {
            $this->repository->clearWarningFlag();
            return $this->response(['msg' => '预警标记已经被重置']);
        } catch (\Exception $e) {
            return $this->setStatus(1478)->responseError($e->getMessage());
        }
    }
}
