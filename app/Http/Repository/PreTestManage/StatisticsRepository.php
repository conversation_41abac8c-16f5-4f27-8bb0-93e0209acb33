<?php

namespace App\Http\Repository\PreTestManage;

use Exception;
use App\Define\PreTestManage;
use App\Models\Crs\SystemDept;
use App\Models\DeptGrade;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\SystemUser;

/**
 * 数据统计
 * @package App\Http\Repository\PreTestManage
 * @class StatisticsRepository
 */
class StatisticsRepository extends PreTestManageRepository
{
    const TAB_DEPT = 'dept';
    const TAB_PRODUCT = 'product';
    const TAB_CUSTOMER = 'customer';

    /**
     * 总览
     * @var array
     */
    private $aOverviewDemo = [
        'primary_count' => 0,
        'primary_key' => '',
        'apply_count' => 0,
        'father_count' => 0,
        'product_count' => 0,
        'cost_avg' => 0,
        'is_top_count' => 0,
        'is_top_unique_count' => 0,
        'name' => '',
        'dept_key' => '',
        'product_name' => '',
        'product_key' => '',
        'child' => [],
        'total_flag' => false,
    ];

    /**
     * 反馈
     * @var array
     */
    private $aFeedbackDemo = [
        'primary_count' => 0,
        'primary_key' => '',
        'all_test_done_count' => 0,
        'test_done_count' => 0,
        'test_done_count_ratio' => 0,
        'feedback_count' => 0,
        'feedback_count_ratio' => 0,
        'have_objective_count' => 0,
        'have_objective_count_ratio' => 0,
        'schedule_cost_avg' => 0,
        'schedule_undone_count' => 0,
        'schedule_undone_count_ratio' => 0,
        'schedule_over3_count' => 0,
        'schedule_over3_count_ratio' => 0,
        'name' => '',
        'dept_key' => '',
        'product_name' => '',
        'product_key' => '',
        'child' => [],
        'total_flag' => false,
    ];

    /**
     * 效果
     * @var array
     */
    private $aResultDemo = [
        'primary_count' => 0,
        'result_count' => 0,
        'result_good_count' => 0,
        'result_good_count_ratio' => 0,
        'result_bad_count' => 0,
        'result_bad_count_ratio' => 0,
        'result_average_count' => 0,
        'result_average_count_ratio' => 0,
        'result_unknown_count' => 0,
        'result_unknown_count_ratio' => 0,
        'good_close_count' => 0,
        'good_close_count_ratio' => 0,
        'bad_open_count' => 0,
        'bad_open_count_ratio' => 0,
        'name' => '',
        'dept_key' => '',
        'product_name' => '',
        'product_key' => '',
        'child' => [],
        'total_flag' => false,
    ];

    /**
     * 接入&调用
     * @var array
     */
    private $aAccessDemo = [
        'primary_count' => 0,
        'access_count' => 0,
        'call_count' => 0,
        'call_count_ratio' => 0,
        'formal_call_count' => 0,
        'formal_call_count_ratio' => 0,
        'test_call_count' => 0,
        'test_call_count_ratio' => 0,
        'not_call_count' => 0,
        'not_call_count_ratio' => 0,
        // todo
        'dept_key' => '',
        'total_flag' => false,
    ];

    /**
     * @return array[]
     * @throws Exception
     */
    public function deptStatistics() {
        $aCond = $this->initCond();

        // 申请列表
        $aField = ApplyCustomer::CORE_FIELD;
        $aApplyList = ApplyCustomer::aGetListByCond($aCond, $aField);
        if (empty($aApplyList)) {
            return [
                'list' => [],
            ];
        }
        $aApplyDataList = $this->aFormatApplyList($aApplyList, $aField);
        // 聚合 产品 至 测试申请
        $aApplyMap = $this->statGatherFather($aApplyDataList);
        // 聚合 至 部门维度
        $aDataList = $this->statGatherApplyToDeptSales($aApplyMap);
        // 计算 比例 和 总计
        (!$this->bSearchSalesMan && !$this->bSearchDept) and $aDataList = $this->statRatioAndTotal($aDataList, ['dept_name' => '合计', 'name' => '--']);

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @param $aApplyMap
     * @return array
     */
    private function statGatherApplyToDeptSales($aApplyMap = []) {
        // 部门信息
        $aAllDept = SystemDept::getAllDeptInfo();
        $aAllDeptMap = array_column($aAllDept, 'dept_name', 'dept_id');
        // 已离职员工
        $aResignMember = $this->getAllResignMember();
        // 聚合 至 销售维度
        list($aSalesMap, $aSalesDeptMap) = $this->statGatherApplyToSales($aApplyMap);
        // 聚合 至 部门维度
        $sOtherDeptName = '其他';
        $aDeptMap = [];
        foreach ($aSalesDeptMap as $sRealName => $sDeptId) {
            $sDeptId = $sDeptId ?: $sOtherDeptName;
            $sDeptName = $aAllDeptMap[$sDeptId] ?? $sOtherDeptName;
            $aData = $aSalesMap[$sRealName] ?? $this->initStatData();
            if ((in_array($sRealName, $aResignMember) || $this->bSearchSalesMan) && !$aData['apply_count']) {
                continue;
            }

            $aDeptMap[$sDeptId][] = array_merge(['dept_name' => $sDeptName, 'name' => $sRealName], $aData);
        }
        // 按照 部门排序 计算合计
        $aFatherDeptMap = DeptGrade::aGetSaleDeptMap();
        // 没有人员提交过测试的部门
        $aExcludeDept = [
            'DEPT2022120220115177', // 生态合作部
            'DEPT2023040320452534', // 企服-内部
            'DEPT2023060810155155', // 营销-内部
            'DEPT2024031811103235', // 海外
        ];
        $aDeptDataList = [];
        foreach ($aFatherDeptMap as $aDept) {
            $aDept = array_unique(array_diff($aDept, $aExcludeDept));
            foreach ($aDept as $sDept) {
                $sDeptName = $aAllDeptMap[$sDept] ?? '';

                $aDeptData = $aDeptMap[$sDept] ?? [];

                if (($this->bSearchSalesMan && empty($aDeptData))
                    || ($this->bSearchDept && $this->sSearchDeptId != $sDept)
                ) {
                    continue;
                }

                $aDeptData = $this->statRatioAndTotal($aDeptData, ['dept_name' => $sDeptName, 'name' => '合计']);

                $aDeptDataList = array_merge($aDeptDataList, $aDeptData);
            }
        }
        if (!$this->bSearchDept && !$this->bSearchSalesMan) {
            $aOtherDeptData = $aDeptMap[$sOtherDeptName] ?? [['apply_count' => 0]];
            $aOtherDeptData[0]['apply_count'] and $aDeptDataList = array_merge($aDeptDataList, $aOtherDeptData);
        }

        return $aDeptDataList;
    }

    /**
     * @return array
     */
    private function getAllResignMember() {
        $aList = SystemUser::getAllResignUser();

        return array_column($aList, 'realname');
    }

    /**
     * @param $aDataList
     * @param $aGatherData
     * @return array
     */
    private function statRatioAndTotal($aDataList = [], $aGatherData = []) {
        $aTotal = $this->initStatData();
        foreach ($aDataList as &$aData) {
            $aData = $this->statRatio($aData);

            if ($aData['total_flag']) {
                continue;
            }

            $aTotal['apply_count'] += $aData['apply_count'];
            $aTotal['father_count'] += $aData['father_count'];
            $aTotal['test_done_count'] += $aData['test_done_count'];
            $aTotal['result_count'] += $aData['result_count'];
            $aTotal['result_undone_count'] += $aData['result_undone_count'];
            $aTotal['result_undone_over_count'] += $aData['result_undone_over_count'];
            $aTotal['access_count'] += $aData['access_count'];
            $aTotal['online_count'] += $aData['online_count'];
            $aTotal['call_count'] += $aData['call_count'];
            $aTotal['2week_before_count'] += $aData['2week_before_count'];
            $aTotal['2week_in_count'] += $aData['2week_in_count'];
            $aTotal['status_testing'] += $aData['status_testing'];
            $aTotal['status_test_done'] += $aData['status_test_done'];
            $aTotal['status_feedback'] += $aData['status_feedback'];
            $aTotal['status_access_unable'] += $aData['status_access_unable'];
            $aTotal['status_access_able'] += $aData['status_access_able'];
            $aTotal['status_access_done'] += $aData['status_access_done'];
            $aTotal['is_pay_count'] += $aData['is_pay_count'];
            $aTotal['is_top_count'] += $aData['is_top_count'];
            $aTotal['schedule_undone_count'] += $aData['schedule_undone_count'];
            $aTotal['total_flag'] = true;
        }
        unset($aData);

        if (!$this->bSearchSalesMan) {
            $aTotal = $this->statRatio($aTotal);
            $aDataList[] = array_merge($aTotal, $aGatherData);
        }

        return $aDataList;
    }

    /**
     * @return array[]
     * @throws Exception
     */
    public function salesStatistics() {
        $aCond = $this->initCond();

        // 申请列表
        $aField = ApplyCustomer::CORE_FIELD;
        $aApplyList = ApplyCustomer::aGetListByCond($aCond, $aField);
        if (empty($aApplyList)) {
            return [
                'list' => [],
            ];
        }
        $aApplyDataList = $this->aFormatApplyList($aApplyList, $aField);
        // 聚合 产品 至 测试申请
        $aApplyMap = $this->statGatherFather($aApplyDataList);

        list($aSalesMap, $aSalesDeptMap) = $this->statGatherApplyToSales($aApplyMap);

        $aSalesDataList = [];
        foreach ($aSalesMap as $sName => $aItem) {
            if (!$aItem['apply_count']) {
                continue;
            }
            $aItem = $this->statRatio($aItem);

            $aSalesDataList[] = array_merge(['name' => $sName], $aItem);
        }

        return [
            'list' => $aSalesDataList,
        ];
    }

    /**
     * @param $aApplyDataList
     * @return array
     */
    protected function statGatherFather($aApplyDataList = []) {
        $aApplyMap = array_column($aApplyDataList, null, 'apply_id');

        $aApplyId = array_column($aApplyDataList, 'apply_id');
        $aApplyFatherList = ApplyFather::aGetListByApplyIdList($aApplyId);

        $sWeek2Before = date('Y-m-d', strtotime('-14 days')) . ' 00:00:00';
        $sMonth1Before = date('Y-m-d', strtotime('-30 days')) . ' 00:00:00';
        foreach ($aApplyFatherList as $aItem) {
            $aApplyMap[$aItem['apply_id']]['father_count'] += 1;
            $aItem['test_status'] >= PreTestManage::STATUS_TEST_DONE and $aApplyMap[$aItem['apply_id']]['test_done_count'] += 1;
            if ($aItem['test_result']) {
                $aApplyMap[$aItem['apply_id']]['result_count'] += 1;
            } else {
                $aItem['test_status'] >= PreTestManage::STATUS_TEST_DONE and $aApplyMap[$aItem['apply_id']]['result_undone_count'] += 1;
                if ($aItem['return_time'] <= $sMonth1Before) {
                    $aApplyMap[$aItem['apply_id']]['result_undone_over_count'] += 1;
                }
            }
            PreTestManage::ACCESS_ACTION_ABLE == $aItem['access_action'] and $aApplyMap[$aItem['apply_id']]['access_count'] += 1;
            $aItem['father_id'] > 0 and $aApplyMap[$aItem['apply_id']]['online_count'] += 1;
            $aItem['date_total_90d'] and $aApplyMap[$aItem['apply_id']]['call_count'] += 1;
            if ($aItem['test_status'] >= PreTestManage::STATUS_TEST_DONE) {
                if ($aItem['return_time'] >= $sWeek2Before) {
                    $aApplyMap[$aItem['apply_id']]['2week_in_count'] += 1;
                } else {
                    $aApplyMap[$aItem['apply_id']]['2week_before_count'] += 1;
                }
            }
        }

        return $aApplyMap;
    }

    /**
     * @param $aApplyMap
     * @return array
     */
    private function statGatherApplyToSales($aApplyMap = []) {
        $aDataDemo = $this->initStatData();
        list($aSalesMap, $aSalesDeptMap) = $this->initSalesMap($aDataDemo);
        $aSalesMap[PreTestManage::ACTION_ADMIN_UNKNOWN] = $aDataDemo;
        $aSalesDeptMap[PreTestManage::ACTION_ADMIN_UNKNOWN] = '';

        foreach ($aApplyMap as $aApply) {
            $sSalesName = $aApply['salesman'] ?: PreTestManage::ACTION_ADMIN_UNKNOWN;
            if (!isset($aSalesMap[$sSalesName])) {
                continue;
            }
            $aSalesMap[$sSalesName]['apply_count'] += 1;
            $aSalesMap[$sSalesName]['father_count'] += $aApply['father_count'];
            $aSalesMap[$sSalesName]['test_done_count'] += $aApply['test_done_count'];
            $aSalesMap[$sSalesName]['result_count'] += $aApply['result_count'];
            $aSalesMap[$sSalesName]['result_undone_count'] += $aApply['result_undone_count'];
            $aSalesMap[$sSalesName]['result_undone_over_count'] += $aApply['result_undone_over_count'];
            $aSalesMap[$sSalesName]['access_count'] += $aApply['access_count'];
            $aSalesMap[$sSalesName]['online_count'] += $aApply['online_count'];
            $aSalesMap[$sSalesName]['call_count'] += $aApply['call_count'];
            $aSalesMap[$sSalesName]['2week_before_count'] += $aApply['2week_before_count'];
            $aSalesMap[$sSalesName]['2week_in_count'] += $aApply['2week_in_count'];
            $aSalesMap[$sSalesName]['schedule_undone_count'] += $aApply['schedule_undone_count'];
            switch ($aApply['status']) {
                case PreTestManage::STATUS_TESTING:
                    $aSalesMap[$sSalesName]['status_testing'] += 1;
                    break;
                case PreTestManage::STATUS_TEST_DONE:
                    $aSalesMap[$sSalesName]['status_test_done'] += 1;
                    break;
                case PreTestManage::STATUS_FEEDBACK:
                    $aSalesMap[$sSalesName]['status_feedback'] += 1;
                    break;
                case PreTestManage::STATUS_ACCESS_UNABLE:
                    $aSalesMap[$sSalesName]['status_access_unable'] += 1;
                    break;
                case PreTestManage::STATUS_ACCESS_ABLE:
                    $aSalesMap[$sSalesName]['status_access_able'] += 1;
                    break;
                case PreTestManage::STATUS_ACCESS_DONE:
                    $aSalesMap[$sSalesName]['status_access_done'] += 1;
                    break;
                default:
                    break;
            }
            $aApply['is_pay'] and $aSalesMap[$sSalesName]['is_pay_count'] += 1;
            $aApply['is_top'] and $aSalesMap[$sSalesName]['is_top_count'] += 1;
        }
        if (!$aSalesMap[PreTestManage::ACTION_ADMIN_UNKNOWN]['apply_count']) {
            unset($aSalesMap[PreTestManage::ACTION_ADMIN_UNKNOWN]);
        }

        return [$aSalesMap, $aSalesDeptMap];
    }

    /**
     * @return int[]
     */
    private function initStatData() {
        return [
            'apply_count' => 0,
            'father_count' => 0,
            // 基于产品维度
            'test_done_count' => 0,
            'test_done_count_ratio' => 0,
            'result_count' => 0,
            'result_count_ratio' => 0,
            'result_undone_count' => 0,
            'result_undone_count_ratio' => 0,
            'result_undone_over_count' => 0,
            'result_undone_over_count_ratio' => 0,
            'access_count' => 0,
            'access_count_ratio' => 0,
            'online_count' => 0,
            'online_count_ratio' => 0,
            'call_count' => 0,
            'call_count_ratio' => 0,
            '2week_before_count' => 0,
            '2week_before_count_ratio' => 0,
            '2week_in_count' => 0,
            '2week_in_count_ratio' => 0,
            // 基于测试申请维度
            // 测试状态统计 以下
            'status_testing' => 0,
            'status_testing_ratio' => 0,
            'status_test_done' => 0,
            'status_test_done_ratio' => 0,
            'status_feedback' => 0,
            'status_feedback_ratio' => 0,
            'status_access_unable' => 0,
            'status_access_unable_ratio' => 0,
            'status_access_able' => 0,
            'status_access_able_ratio' => 0,
            'status_access_done' => 0,
            'status_access_done_ratio' => 0,
            // 测试状态统计 以上
            'is_pay_count' => 0,
            'is_pay_count_ratio' => 0,
            'is_top_count' => 0,
            'is_top_count_ratio' => 0,
            'total_flag' => false,
            // 其他
            'schedule_undone_count' => 0,
            'schedule_undone_count_ratio' => 0,
        ];
    }

    /**
     * @param $aData
     * @return array
     */
    private function statRatio($aData = []) {
        if ($aData['father_count']) {
            $aData['test_done_count_ratio'] = round(($aData['test_done_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['result_count_ratio'] = round(($aData['result_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['result_undone_count_ratio'] = round(($aData['result_undone_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['access_count_ratio'] = round(($aData['access_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['online_count_ratio'] = round(($aData['online_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['call_count_ratio'] = round(($aData['call_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['2week_before_count_ratio'] = round(($aData['2week_before_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['2week_in_count_ratio'] = round(($aData['2week_in_count'] / $aData['father_count']) * 100, 1) . '%';
            $aData['schedule_undone_count_ratio'] = round(($aData['schedule_undone_count'] / $aData['father_count']) * 100, 1) . '%';
        }
        if ($aData['apply_count']) {
            $aData['status_testing_ratio'] = round(($aData['status_testing'] / $aData['apply_count']) * 100, 1) . '%';
            $aData['status_test_done_ratio'] = round(($aData['status_test_done'] / $aData['apply_count']) * 100, 1) . '%';
            $aData['status_feedback_ratio'] = round(($aData['status_feedback'] / $aData['apply_count']) * 100, 1) . '%';
            $aData['status_access_unable_ratio'] = round(($aData['status_access_unable'] / $aData['apply_count']) * 100, 1) . '%';
            $aData['status_access_able_ratio'] = round(($aData['status_access_able'] / $aData['apply_count']) * 100, 1) . '%';
            $aData['status_access_done_ratio'] = round(($aData['status_access_done'] / $aData['apply_count']) * 100, 1) . '%';
            $aData['is_pay_count_ratio'] = round(($aData['is_pay_count'] / $aData['apply_count']) * 100, 1) . '%';
            $aData['is_top_count_ratio'] = round(($aData['is_top_count'] / $aData['apply_count']) * 100, 1) . '%';
        }

        return $aData;
    }

    /**
     * @return array|array[]
     * @throws Exception
     */
    public function statisticsOverview() {
        $sTab = trim(request()->post('tab', ''));
        if (!$sTab) {
            throw new Exception('请求参数错误,数据标签为空');
        }
        $aCond = $this->initStatisticsCond();
        $aApplyFatherList = ApplyFather::aGetDataByCond($aCond);
        if (empty($aApplyFatherList)) {
            return ['list' => []];
        }

        $aDataList = [];
        switch ($sTab) {
            case self::TAB_DEPT:
                $aDataList = $this->statisticsOverviewForDept($aApplyFatherList);
                break;
            case self::TAB_PRODUCT:
                $aDataList = $this->statisticsOverviewForProduct($aApplyFatherList);
                break;
            case self::TAB_CUSTOMER:
                $aDataList = $this->statisticsOverviewForCustomer($aApplyFatherList);
                break;
            default:
                break;
        }

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @param $aApplyFatherList
     * @return array
     */
    private function statisticsOverviewForDept($aApplyFatherList) {
        list($aSalesMap, $aSalesDeptMap) = $this->initSalesMap($this->aOverviewDemo);

        $aApplyId = array_unique(array_column($aApplyFatherList, 'apply_id'));
        $aCond = [
            ['id', 'in', $aApplyId],
            ['is_top', '=', PreTestManage::IS_YES],
        ];
        $aTopApplyList = ApplyCustomer::aGetListByCond($aCond, ['id', 'is_top', 'company_short_name']);
        $aTopApplyMap = array_column($aTopApplyList, 'company_short_name', 'id');

        $aSalesDataMap = [];
        foreach ($aApplyFatherList as $aItem) {
            $sSales = $aItem['salesman'];
            $aSalesDataMap[$sSales]['apply_id'][] = $aItem['apply_id'];
            $aSalesDataMap[$sSales]['apply_father_id'][] = $aItem['id'];
            $aSalesDataMap[$sSales]['product'][] = explode(',', $aItem['product_list']);
            $aSalesDataMap[$sSales]['cost'][] = strtotime($aItem['return_time']) - strtotime($aItem['start_time']);
            $aSalesDataMap[$sSales]['is_top'][] = $aTopApplyMap[$aItem['apply_id']] ?? 0;
        }
        foreach ($aSalesDataMap as $sSales => $aData) {
            $iProductCount = 0;
            if ($aData['product']) {
                foreach ($aData['product'] as $aProductId) {
                    $iProductCount += count($aProductId);
                }
            }
            $aCost = array_filter($aData['cost'], function ($i) {return $i > 0;});
            $iCostCount = count($aCost);
            $iApplyCount = count(array_unique($aData['apply_id']));
            $iFatherCount = count(array_unique($aData['apply_father_id']));
            $aTop = array_filter($aData['is_top']);
            $iIsTopCount = count($aTop);
            $iIsTopUniqueCount = count(array_unique($aTop));

            $aSalesMap[$sSales]['primary_count'] = $iFatherCount;
            $aSalesMap[$sSales]['apply_count'] = $iApplyCount;
            $aSalesMap[$sSales]['father_count'] = $iFatherCount;
            $aSalesMap[$sSales]['product_count'] = $iProductCount;
            $aSalesMap[$sSales]['cost_avg'] = $iCostCount ? (int)(array_sum($aCost) / $iCostCount / 86400) : 0;
            $aSalesMap[$sSales]['is_top_count'] = $iIsTopCount;
            $aSalesMap[$sSales]['is_top_unique_count'] = $iIsTopUniqueCount;
        }

        return $this->aGatherSalesDataToDept($aSalesMap, $aSalesDeptMap, 'statTotalForOverview');
    }

    /**
     * @param $aApplyFatherList
     * @return array
     */
    private function statisticsOverviewForProduct($aApplyFatherList) {
        $aFatherId = [];
        foreach ($aApplyFatherList as $aData) {
            PreTestManage::IS_BIND == $aData['is_schedule'] and $aFatherId[] = $aData['father_id'];
        }
        list($aDataMap, $aProductMap) = $this->initProductMap($this->aOverviewDemo, $aFatherId);

        $aApplyId = array_unique(array_column($aApplyFatherList, 'apply_id'));
        $aCond = [
            ['id', 'in', $aApplyId],
            ['is_top', '=', PreTestManage::IS_YES],
        ];
        $aTopApplyList = ApplyCustomer::aGetListByCond($aCond, ['id', 'is_top', 'company_short_name']);
        $aTopApplyMap = array_column($aTopApplyList, 'company_short_name', 'id');

        $aProductDataMap = [];
        foreach ($aApplyFatherList as $aItem) {
            if (PreTestManage::IS_BIND == $aItem['is_schedule']) {
                $sPrimaryKey = 'sp_' . $aItem['father_id'];
            } else {
                $sPrimaryKey = 'bp_' . $aItem['product_key'];
            }

            $aProductDataMap[$sPrimaryKey]['apply_id'][] = $aItem['apply_id'];
            $aProductDataMap[$sPrimaryKey]['apply_father_id'][] = $aItem['id'];
            $aProductDataMap[$sPrimaryKey]['product'][] = explode(',', $aItem['product_list']);
            $aProductDataMap[$sPrimaryKey]['cost'][] = strtotime($aItem['return_time']) - strtotime($aItem['start_time']);
            $aProductDataMap[$sPrimaryKey]['is_top'][] = $aTopApplyMap[$aItem['apply_id']] ?? 0;
        }

        foreach ($aProductDataMap as $sPrimaryKey => $aData) {
            $iProductCount = 0;
            if ($aData['product']) {
                foreach ($aData['product'] as $aProductId) {
                    $iProductCount += count($aProductId);
                }
            }
            $aCost = array_filter($aData['cost'], function ($i) {return $i > 0;});
            $iCostCount = count($aCost);
            $iApplyCount = count(array_unique($aData['apply_id']));
            $iFatherCount = count(array_unique($aData['apply_father_id']));
            $aTop = array_filter($aData['is_top']);
            $iIsTopCount = count($aTop);
            $iIsTopUniqueCount = count(array_unique($aTop));

            $aDataMap[$sPrimaryKey]['primary_count'] = $iFatherCount;
            $aDataMap[$sPrimaryKey]['apply_count'] = $iApplyCount;
            $aDataMap[$sPrimaryKey]['father_count'] = $iFatherCount;
            $aDataMap[$sPrimaryKey]['product_count'] = $iProductCount;
            $aDataMap[$sPrimaryKey]['cost_avg'] = $iCostCount ? (int)(array_sum($aCost) / $iCostCount / 86400) : 0;
            $aDataMap[$sPrimaryKey]['is_top_count'] = $iIsTopCount;
            $aDataMap[$sPrimaryKey]['is_top_unique_count'] = $iIsTopUniqueCount;
        }

        return $this->aGatherProductDataToProduct($aDataMap, $aProductMap, 'statTotalForOverview');
    }

    /**
     * @param $aApplyFatherList
     * @return array
     */
    private function statisticsOverviewForCustomer($aApplyFatherList) {

        return [];
    }

    /**
     * @return array
     * @throws Exception
     */
    private function initStatisticsCond() {
        $iFatherKey = (int)request()->post('father_id', 0);
        $iProductKey = (int)request()->post('product_id', 0);
        $aApplyTimeRange = (array)request()->post('apply_time_range', []);
        list($sApplyTimeStart, $sApplyTimeEnd) = $aApplyTimeRange;
        if (strtotime($sApplyTimeStart) > strtotime($sApplyTimeEnd)) {
            throw new Exception(sprintf('请求参数错误,开始时间(%s)不能大于结束时间(%s)', $sApplyTimeStart, $sApplyTimeEnd));
        }
        list ($aSalesMan, $sSalesRealName) = $this->initUserAuthCond();
        $aCond = [];
        if ($sApplyTimeStart) {
            $aCond[] = ['apply_time', '>=', $sApplyTimeStart . ' 00:00:00'];
        }
        if ($sApplyTimeEnd) {
            $aCond[] = ['apply_time', '<=', $sApplyTimeEnd . ' 23:59:59'];
        }
        if ($aSalesMan) {
            $aCond[] = ['salesman', 'in', $aSalesMan];
        }
        if ($sSalesRealName) {
            $aCond[] = ['salesman', '=', $sSalesRealName];
        }
        if ($iFatherKey) {
            $aFatherCond = $this->buildFatherKeyCond($iFatherKey);
            $aCond = array_merge($aCond, $aFatherCond);
        }
        if ($iProductKey) {
            $aCond[] = ['product_key', '=', $iProductKey];
        }

        return $aCond;
    }

    /**
     * @param $aDataList
     * @param $aGatherData
     * @param $bOnlyTotal
     * @return array
     * @uses statTotalForOverview
     */
    protected function statTotalForOverview($aDataList = [], $aGatherData = [], $bOnlyTotal = false) {
        $aTotal = $this->aOverviewDemo;
        $i = 0;
        $iAvg = 0;
        foreach ($aDataList as $aData) {
            if ($aData['total_flag']) {
                continue;
            }
            $i++;
            $iAvg = bcadd($iAvg, $aData['cost_avg']);

            $aTotal['primary_count'] += $aData['primary_count'];
            $aTotal['apply_count'] += $aData['apply_count'];
            $aTotal['father_count'] += $aData['father_count'];
            $aTotal['product_count'] += $aData['product_count'];
            $aTotal['cost_avg'] = round($iAvg / $i, 2);
            $aTotal['is_top_count'] += $aData['is_top_count'];
            $aTotal['is_top_unique_count'] += $aData['is_top_unique_count'];
            $aTotal['total_flag']  = true;
        }

        $aTotal = array_merge($aTotal, $aGatherData);
        if ($bOnlyTotal) {
            return $aTotal;
        }
        $aDataList[] = $aTotal;

        return $aDataList;
    }

    /**
     * @return array[]
     * @throws Exception
     */
    public function statisticsFeedback() {
        $sTab = trim(request()->post('tab', ''));
        if (!$sTab) {
            throw new Exception('请求参数错误,数据标签为空');
        }
        $aCond = $this->initStatisticsCond();
        $aCond[] = [
            'test_status', '>=', PreTestManage::STATUS_TEST_DONE
        ];
        $aApplyFatherList = ApplyFather::aGetDataByCond($aCond);
        if (empty($aApplyFatherList)) {
            return ['list' => []];
        }

        $aDataList = [];
        switch ($sTab) {
            case self::TAB_DEPT:
                list($aSalesMap, $aSalesDeptMap) = $this->initSalesMap($this->aFeedbackDemo);
                $aSalesBaseDataMap = $this->baseDataMapForFeedback($aSalesMap, 'dept', $aApplyFatherList);
                $aSalesMap = $this->computeBaseDataMapForFeedback($aSalesMap, $aSalesBaseDataMap);
                $aDataList = $this->aGatherSalesDataToDept($aSalesMap, $aSalesDeptMap, 'statTotalForFeedback');
                break;
            case self::TAB_PRODUCT:
                $aFatherId = [];
                foreach ($aApplyFatherList as $aData) {
                    PreTestManage::IS_BIND == $aData['is_schedule'] and $aFatherId[] = $aData['father_id'];
                }
                list($aDataMap, $aProductMap) = $this->initProductMap($this->aFeedbackDemo, $aFatherId);
                $aBaseDataMap = $this->baseDataMapForFeedback($aDataMap, 'product', $aApplyFatherList);
                $aDataMap = $this->computeBaseDataMapForFeedback($aDataMap, $aBaseDataMap);
                $aDataList = $this->aGatherProductDataToProduct($aDataMap, $aProductMap, 'statTotalForFeedback');
                break;
            case self::TAB_CUSTOMER:
                // todo
                break;
            default:
                break;
        }

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @param $aDataMap
     * @param $sDim
     * @param $aApplyFatherList
     * @return array|mixed
     */
    private function baseDataMapForFeedback($aDataMap = [], $sDim = '', $aApplyFatherList = []){
        $iNow = time();
        $iMonth3Before = strtotime('-90 days', $iNow);

        foreach ($aApplyFatherList as $aItem) {
            if ($sDim == 'dept') {
                $sPrimaryKey = $aItem['salesman'];
            } else if ($sDim == 'product') {
                if (PreTestManage::IS_BIND == $aItem['is_schedule']) {
                    $sPrimaryKey = 'sp_' . $aItem['father_id'];
                } else {
                    $sPrimaryKey = 'bp_' . $aItem['product_key'];
                }
            } else if ($sDim == 'customer') {
                $sPrimaryKey = '';
            }

            if (!isset($aDataMap[$sPrimaryKey])) {
                continue;
            }
            $aDataMap[$sPrimaryKey]['primary_count']++;
            $aDataMap[$sPrimaryKey]['all_test_done_count']++;

            if (PreTestManage::STATUS_TEST_DONE <= $aItem['test_status']
                && PreTestManage::TEST_RESULT_DEFAULT == $aItem['test_result']
            ) {
                $aDataMap[$sPrimaryKey]['test_done_count']++;
            }
            if (PreTestManage::TEST_RESULT_DEFAULT < $aItem['test_result']) {
                $aDataMap[$sPrimaryKey]['feedback_count']++;
            }
            if (PreTestManage::IS_YES == $aItem['have_objective']) {
                $aDataMap[$sPrimaryKey]['have_objective_count']++;
            }
            if (PreTestManage::TEST_RESULT_DEFAULT < $aItem['test_result'] && PreTestManage::STATUS_FEEDBACK <= $aItem['test_status']) {
                $aDataMap[$sPrimaryKey]['schedule_cost'][] = strtotime($aItem['feedback_time']) - strtotime($aItem['return_time']);
            }
            if (PreTestManage::IS_NO == $aItem['is_schedule']
                && PreTestManage::TEST_RESULT_DEFAULT == $aItem['test_result']
                && PreTestManage::STATUS_TEST_DONE <= $aItem['test_status']
                && $iNow > strtotime($aItem['schedule_time'])
            ) {
                $aDataMap[$sPrimaryKey]['schedule_undone_count']++;
            }
            if (PreTestManage::IS_NO == $aItem['is_schedule']
                && PreTestManage::TEST_RESULT_DEFAULT == $aItem['test_result']
                && PreTestManage::STATUS_TEST_DONE <= $aItem['test_status']
                && $iMonth3Before > strtotime($aItem['return_time'])
            ) {
                $aDataMap[$sPrimaryKey]['schedule_over3_count']++;
            }
        }

        return $aDataMap;
    }

    /**
     * @param $aDataMap
     * @param $aBaseDataMap
     * @return array|mixed
     */
    private function computeBaseDataMapForFeedback($aDataMap = [], $aBaseDataMap = []) {
        foreach ($aBaseDataMap as $sPrimaryKey => $aData) {
            if (!$aData['primary_count']) {
                continue;
            }
            $aCost = array_filter($aData['schedule_cost'] ?? [], function ($i) {return $i > 0;});
            $iCostCount = count($aCost);

            $aDataMap[$sPrimaryKey]['primary_count'] = $aData['primary_count'];
            $aDataMap[$sPrimaryKey]['all_test_done_count'] = $aData['all_test_done_count'];
            $aDataMap[$sPrimaryKey]['test_done_count'] = $aData['test_done_count'];
            $aDataMap[$sPrimaryKey]['test_done_count_ratio'] = round(($aData['test_done_count'] / $aData['all_test_done_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['feedback_count'] = $aData['feedback_count'];
            $aDataMap[$sPrimaryKey]['feedback_count_ratio'] = round(($aData['feedback_count'] / $aData['all_test_done_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['have_objective_count'] = $aData['have_objective_count'];
            $aDataMap[$sPrimaryKey]['have_objective_count_ratio'] = round(($aData['have_objective_count'] / $aData['all_test_done_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['schedule_cost_avg'] = $iCostCount ? (int)(array_sum($aCost) / $iCostCount / 86400) : 0;
            $aDataMap[$sPrimaryKey]['schedule_undone_count'] = $aData['schedule_undone_count'];
            $aDataMap[$sPrimaryKey]['schedule_undone_count_ratio'] = round(($aData['schedule_undone_count'] / $aData['all_test_done_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['schedule_over3_count'] = $aData['schedule_over3_count'];
            $aDataMap[$sPrimaryKey]['schedule_over3_count_ratio'] = round(($aData['schedule_over3_count'] / $aData['all_test_done_count']) * 100, 2);
        }

        return $aDataMap;
    }

    /**
     * @param $aDataList
     * @param $aGatherData
     * @param $bOnlyTotal
     * @return array|mixed
     * @uses statTotalForFeedback
     */
    protected function statTotalForFeedback($aDataList = [], $aGatherData = [], $bOnlyTotal = false) {
        $aTotal = $this->aFeedbackDemo;
        $i = 0;
        $iAvg = 0;
        foreach ($aDataList as $aData) {
            if ($aData['total_flag']) {
                continue;
            }
            $i++;
            $iAvg = bcadd($iAvg, $aData['schedule_cost_avg']);

            $aTotal['primary_count'] += $aData['primary_count'];
            $aTotal['all_test_done_count'] += $aData['all_test_done_count'];
            $aTotal['test_done_count'] += $aData['test_done_count'];
            $aTotal['test_done_count_ratio'] = round(($aTotal['test_done_count'] / $aTotal['all_test_done_count']) * 100, 2);
            $aTotal['feedback_count'] += $aData['feedback_count'];
            $aTotal['feedback_count_ratio'] = round(($aTotal['feedback_count'] / $aTotal['all_test_done_count']) * 100, 2);
            $aTotal['have_objective_count'] += $aData['have_objective_count'];
            $aTotal['have_objective_count_ratio'] = round(($aTotal['have_objective_count'] / $aTotal['all_test_done_count']) * 100, 2);
            $aTotal['schedule_cost_avg'] = round($iAvg / $i, 2);
            $aTotal['schedule_undone_count'] += $aData['schedule_undone_count'];
            $aTotal['schedule_undone_count_ratio'] = round(($aTotal['schedule_undone_count'] / $aTotal['all_test_done_count']) * 100, 2);
            $aTotal['schedule_over3_count'] += $aData['schedule_over3_count'];
            $aTotal['schedule_over3_count_ratio'] = round(($aTotal['schedule_over3_count'] / $aTotal['all_test_done_count']) * 100, 2);
            $aTotal['total_flag'] = true;
        }

        $aTotal = array_merge($aTotal, $aGatherData);
        if ($bOnlyTotal) {
            return $aTotal;
        }
        $aDataList[] = $aTotal;

        return $aDataList;
    }

    /**
     * @return array|array[]
     * @throws Exception
     */
    public function statisticsResult() {
        $sTab = trim(request()->post('tab', ''));
        if (!$sTab) {
            throw new Exception('请求参数错误,数据标签为空');
        }
        $aCond = $this->initStatisticsCond();
        $aCond[] = [
            'test_result', '>', PreTestManage::TEST_RESULT_DEFAULT
        ];
        $aApplyFatherList = ApplyFather::aGetDataByCond($aCond);
        if (empty($aApplyFatherList)) {
            return ['list' => []];
        }

        $aDataList = [];
        switch ($sTab) {
            case self::TAB_DEPT:
                list($aSalesMap, $aSalesDeptMap) = $this->initSalesMap($this->aResultDemo);
                $aSalesBaseDataMap = $this->baseDataMapForResult($aSalesMap, 'dept', $aApplyFatherList);
                $aSalesMap = $this->computeBaseDataMapForResult($aSalesMap, $aSalesBaseDataMap);
                $aDataList = $this->aGatherSalesDataToDept($aSalesMap, $aSalesDeptMap, 'statTotalForResult');
                break;
            case self::TAB_PRODUCT:
                $aFatherId = [];
                foreach ($aApplyFatherList as $aData) {
                    PreTestManage::IS_BIND == $aData['is_schedule'] and $aFatherId[] = $aData['father_id'];
                }
                list($aDataMap, $aProductMap) = $this->initProductMap($this->aResultDemo, $aFatherId);
                $aBaseDataMap = $this->baseDataMapForResult($aDataMap, 'product', $aApplyFatherList);
                $aDataMap = $this->computeBaseDataMapForResult($aDataMap, $aBaseDataMap);
                $aDataList = $this->aGatherProductDataToProduct($aDataMap, $aProductMap, 'statTotalForResult');
                break;
            case self::TAB_CUSTOMER:
                // todo
                break;
            default:
                break;
        }

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @param $aDataMap
     * @param $sDim
     * @param $aApplyFatherList
     * @return array
     */
    private function baseDataMapForResult($aDataMap = [], $sDim = '', $aApplyFatherList = []){
        foreach ($aApplyFatherList as $aItem) {
            if ($sDim == 'dept') {
                $sPrimaryKey = $aItem['salesman'];
            } else if ($sDim == 'product') {
                if (PreTestManage::IS_BIND == $aItem['is_schedule']) {
                    $sPrimaryKey = 'sp_' . $aItem['father_id'];
                } else {
                    $sPrimaryKey = 'bp_' . $aItem['product_key'];
                }
            } else if ($sDim == 'customer') {
                $sPrimaryKey = '';
            }

            if (!isset($aDataMap[$sPrimaryKey])) {
                continue;
            }
            $aDataMap[$sPrimaryKey]['primary_count']++;
            $aDataMap[$sPrimaryKey]['result_count']++;

            switch ($aItem['test_result']) {
                case PreTestManage::TEST_RESULT_GOOD:
                    $aDataMap[$sPrimaryKey]['result_good_count']++;
                    if (PreTestManage::IS_NO == $aItem['is_open']) {
                        $aDataMap[$sPrimaryKey]['good_close_count']++;
                    }
                    break;
                case PreTestManage::TEST_RESULT_BAD:
                    $aDataMap[$sPrimaryKey]['result_bad_count']++;
                    if (PreTestManage::IS_YES == $aItem['is_open']) {
                        $aDataMap[$sPrimaryKey]['bad_open_count']++;
                    }
                    break;
                case PreTestManage::TEST_RESULT_AVERAGE:
                    $aDataMap[$sPrimaryKey]['result_average_count']++;
                    break;
                case PreTestManage::TEST_RESULT_UNABLE:
                case PreTestManage::TEST_RESULT_NA:
                $aDataMap[$sPrimaryKey]['result_unknown_count']++;
                    break;
                default:
                    break;
            }
        }

        return $aDataMap;
    }

    /**
     * @param $aDataMap
     * @param $aBaseDataMap
     * @return array
     */
    private function computeBaseDataMapForResult($aDataMap = [], $aBaseDataMap = []) {
        foreach ($aBaseDataMap as $sPrimaryKey => $aData) {
            if (!$aData['primary_count']) {
                continue;
            }

            $aDataMap[$sPrimaryKey]['primary_count'] = $aData['primary_count'];
            $aDataMap[$sPrimaryKey]['result_count'] = $aData['result_count'];
            $aDataMap[$sPrimaryKey]['result_good_count'] = $aData['result_good_count'];
            $aDataMap[$sPrimaryKey]['result_good_count_ratio'] = round(($aData['result_good_count'] / $aData['result_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['result_bad_count'] = $aData['result_bad_count'];
            $aDataMap[$sPrimaryKey]['result_bad_count_ratio'] = round(($aData['result_bad_count'] / $aData['result_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['result_average_count'] = $aData['result_average_count'];
            $aDataMap[$sPrimaryKey]['result_average_count_ratio'] = round(($aData['result_average_count'] / $aData['result_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['result_unknown_count'] = $aData['result_unknown_count'];
            $aDataMap[$sPrimaryKey]['result_unknown_count_ratio'] = round(($aData['result_unknown_count'] / $aData['result_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['good_close_count'] = $aData['good_close_count'];
            $aDataMap[$sPrimaryKey]['good_close_count_ratio'] = $aData['result_good_count'] ? round(($aData['good_close_count'] / $aData['result_good_count']) * 100, 2) : 0;
            $aDataMap[$sPrimaryKey]['bad_open_count'] = $aData['bad_open_count'];
            $aDataMap[$sPrimaryKey]['bad_open_count_ratio'] = $aData['result_bad_count'] ? round(($aData['bad_open_count'] / $aData['result_bad_count']) * 100, 2) : 0;
        }

        return $aDataMap;
    }

    /**
     * @param $aDataList
     * @param $aGatherData
     * @param $bOnlyTotal
     * @return array|mixed
     * @uses statTotalForResult
     */
    protected function statTotalForResult($aDataList = [], $aGatherData = [], $bOnlyTotal = false) {
        $aTotal = $this->aResultDemo;

        foreach ($aDataList as $aData) {
            if ($aData['total_flag']) {
                continue;
            }

            $aTotal['primary_count'] += $aData['primary_count'];
            $aTotal['result_count'] += $aData['result_count'];
            $aTotal['result_good_count'] += $aData['result_good_count'];
            $aTotal['result_good_count_ratio'] = round(($aTotal['result_good_count'] / $aTotal['result_count']) * 100, 2);
            $aTotal['result_bad_count'] += $aData['result_bad_count'];
            $aTotal['result_bad_count_ratio'] = round(($aTotal['result_bad_count'] / $aTotal['result_count']) * 100, 2);
            $aTotal['result_average_count'] += $aData['result_average_count'];
            $aTotal['result_average_count_ratio'] = round(($aTotal['result_average_count'] / $aTotal['result_count']) * 100, 2);
            $aTotal['result_unknown_count'] += $aData['result_unknown_count'];
            $aTotal['result_unknown_count_ratio'] = round(($aTotal['result_unknown_count'] / $aTotal['result_count']) * 100, 2);
            $aTotal['good_close_count'] += $aData['good_close_count'];
            $aTotal['good_close_count_ratio'] = $aTotal['result_good_count'] ? round(($aTotal['good_close_count'] / $aTotal['result_good_count']) * 100, 2) : 0;
            $aTotal['bad_open_count'] += $aData['bad_open_count'];
            $aTotal['bad_open_count_ratio'] = $aTotal['result_bad_count'] ? round(($aTotal['bad_open_count'] / $aTotal['result_bad_count']) * 100, 2) : 0;
            $aTotal['total_flag'] = true;
        }

        $aTotal = array_merge($aTotal, $aGatherData);
        if ($bOnlyTotal) {
            return $aTotal;
        }
        $aDataList[] = $aTotal;

        return $aDataList;
    }

    /**
     * @return array|array[]
     * @throws Exception
     */
    public function statisticsAccess() {
        $sTab = trim(request()->post('tab', ''));
        if (!$sTab) {
            throw new Exception('请求参数错误,数据标签为空');
        }
        $aCond = $this->initStatisticsCond();
        $aCond[] = [
            'is_open', '=', PreTestManage::IS_YES,
        ];
        $aApplyFatherList = ApplyFather::aGetDataByCond($aCond);
        if (empty($aApplyFatherList)) {
            return ['list' => []];
        }

        $aDataList = [];
        switch ($sTab) {
            case self::TAB_DEPT:
                list($aSalesMap, $aSalesDeptMap) = $this->initSalesMap($this->aAccessDemo);
                $aSalesBaseDataMap = $this->baseDataMapForAccess($aSalesMap, 'dept', $aApplyFatherList);
                $aSalesMap = $this->computeBaseDataMapForAccess($aSalesMap, $aSalesBaseDataMap);
                $aDataList = $this->aGatherSalesDataToDept($aSalesMap, $aSalesDeptMap, 'statTotalForAccess');
                break;
            case self::TAB_PRODUCT:
                $aFatherId = [];
                foreach ($aApplyFatherList as $aData) {
                    PreTestManage::IS_BIND == $aData['is_schedule'] and $aFatherId[] = $aData['father_id'];
                }
                list($aDataMap, $aProductMap) = $this->initProductMap($this->aAccessDemo, $aFatherId);
                $aBaseDataMap = $this->baseDataMapForAccess($aDataMap, 'product', $aApplyFatherList);
                $aDataMap = $this->computeBaseDataMapForAccess($aDataMap, $aBaseDataMap);
                $aDataList = $this->aGatherProductDataToProduct($aDataMap, $aProductMap, 'statTotalForAccess');
                break;
            case self::TAB_CUSTOMER:
                // todo
                break;
            default:
                break;
        }

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @param $aDataMap
     * @param $aApplyFatherList
     * @return array
     */
    private function baseDataMapForAccess($aDataMap = [], $sDim = '', $aApplyFatherList = []){
        foreach ($aApplyFatherList as $aItem) {
            if ($sDim == 'dept') {
                $sPrimaryKey = $aItem['salesman'];
            } else if ($sDim == 'product') {
                if (PreTestManage::IS_BIND == $aItem['is_schedule']) {
                    $sPrimaryKey = 'sp_' . $aItem['father_id'];
                } else {
                    $sPrimaryKey = 'bp_' . $aItem['product_key'];
                }
            } else if ($sDim == 'customer') {
                $sPrimaryKey = '';
            }

            if (!isset($aDataMap[$sPrimaryKey])) {
                continue;
            }
            $aDataMap[$sPrimaryKey]['primary_count']++;
            $aDataMap[$sPrimaryKey]['access_count']++;

            if ($aItem['number_total'] > 0) {
                $aDataMap[$sPrimaryKey]['call_count']++;
                if ($aItem['number_total'] > 100) {
                    $aDataMap[$sPrimaryKey]['formal_call_count']++;
                } else {
                    $aDataMap[$sPrimaryKey]['test_call_count']++;
                }
            } else {
                $aDataMap[$sPrimaryKey]['not_call_count']++;
            }
        }

        return $aDataMap;
    }

    /**
     * @param $aDataMap
     * @param $aBaseDataMap
     * @return array
     */
    private function computeBaseDataMapForAccess($aDataMap = [], $aBaseDataMap = []) {
        foreach ($aBaseDataMap as $sPrimaryKey => $aData) {
            if (!$aData['primary_count']) {
                continue;
            }

            $aDataMap[$sPrimaryKey]['primary_count'] = $aData['primary_count'];
            $aDataMap[$sPrimaryKey]['access_count'] = $aData['access_count'];
            $aDataMap[$sPrimaryKey]['call_count'] = $aData['call_count'];
            $aDataMap[$sPrimaryKey]['call_count_ratio'] = round(($aData['call_count'] / $aData['access_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['formal_call_count'] = $aData['formal_call_count'];
            $aDataMap[$sPrimaryKey]['formal_call_count_ratio'] = round(($aData['formal_call_count'] / $aData['access_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['test_call_count'] = $aData['test_call_count'];
            $aDataMap[$sPrimaryKey]['test_call_count_ratio'] = round(($aData['test_call_count'] / $aData['access_count']) * 100, 2);
            $aDataMap[$sPrimaryKey]['not_call_count'] = $aData['not_call_count'];
            $aDataMap[$sPrimaryKey]['not_call_count_ratio'] = round(($aData['not_call_count'] / $aData['access_count']) * 100, 2);
        }

        return $aDataMap;
    }

    /**
     * @param $aDataList
     * @param $aGatherData
     * @param $bOnlyTotal
     * @return array|mixed
     * @uses statTotalForAccess
     */
    protected function statTotalForAccess($aDataList = [], $aGatherData = [], $bOnlyTotal = false) {
        $aTotal = $this->aAccessDemo;

        foreach ($aDataList as $aData) {
            if ($aData['total_flag']) {
                continue;
            }

            $aTotal['primary_count'] += $aData['primary_count'];
            $aTotal['access_count'] += $aData['access_count'];
            $aTotal['call_count'] += $aData['call_count'];
            $aTotal['call_count_ratio'] = round(($aTotal['call_count'] / $aTotal['access_count']) * 100, 2);
            $aTotal['formal_call_count'] += $aData['formal_call_count'];
            $aTotal['formal_call_count_ratio'] = round(($aTotal['formal_call_count'] / $aTotal['access_count']) * 100, 2);
            $aTotal['test_call_count'] += $aData['test_call_count'];
            $aTotal['test_call_count_ratio'] = round(($aTotal['test_call_count'] / $aTotal['access_count']) * 100, 2);
            $aTotal['not_call_count'] += $aData['not_call_count'];
            $aTotal['not_call_count_ratio'] = round(($aTotal['not_call_count'] / $aTotal['access_count']) * 100, 2);
            $aTotal['total_flag'] = true;
        }

        $aTotal = array_merge($aTotal, $aGatherData);
        if ($bOnlyTotal) {
            return $aTotal;
        }
        $aDataList[] = $aTotal;

        return $aDataList;
    }
}