<?php

namespace App\Http\Repository\PreTestManage;

use Exception;
use App\Define\PreTestManage;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;

/**
 * 移动端
 * @package App\Http\Repository\PreTestManage
 * @class MobileRepository
 */
class MobileRepository extends PreTestManageRepository
{
    /**
     * @return array
     */
    public function myList() {
        $sSalesMan = trim(request()->post('salesman', ''));
        $iStatus = (int)request()->post('status', 0);
        $sCompanyShortName = trim(request()->post('company_short_name', ''));
        $iSort = (int)request()->post('sort', 0);
        $sSort = 0 == $iSort ? 'DESC' : 'ASC';
        $iPage = (int)request()->post('page', 1);
        $iPageSize = (int)request()->post('page_size', 10);

        if (!$sSalesMan) {
            //throw new Exception('请求参数[销售(salesman)]不能为空'); // todo
        }

        $aCond = [];
        if ($sSalesMan) {
            $aCond[] = ['salesman', '=', $sSalesMan];
        }
        if ($iStatus) {
            $aCond[] = ['status', '=', $iStatus];
        }
        if ($sCompanyShortName) {
            $aCond[] = ['company_short_name', 'like', '%' . $sCompanyShortName . '%'];
        }
        $iOffset = ($iPage - 1) * $iPageSize;

        list($iTotal, $aDataList) = $this->getCustomerListData($aCond, $iOffset, $iPageSize, $sSort);

        return [
            'total' => $iTotal,
            'list' => $aDataList,
            'page' => $iPage,
            'page_size' => $iPageSize,
            'total_page' => ceil($iTotal / $iPageSize),
        ];
    }

    /**
     * @param $aCond
     * @param $iOffset
     * @param $iPageSize
     * @param $sSort
     * @return array
     */
    private function getCustomerListData($aCond, $iOffset, $iPageSize, $sSort) {
        $iTotal = ApplyCustomer::iGetCountByCond($aCond);
        if (!$iTotal) {
            return [0, []];
        }

        // 申请列表
        $aApplyList = ApplyCustomer::aGetPageListByCond($aCond, $iOffset, $iPageSize, $sSort, 'apply_time');
        // 人员部门信息
        $aDataList = $this->aFormatApplyList($aApplyList);

        return [$iTotal, $aDataList];
    }

    /**
     * @return bool
     */
    public function stayAll() {
        $iApplyId = (int)request()->post('apply_id', 0);
        if (!$iApplyId) {
            throw new Exception('请求参数[申请表ID(apply_id)]不能为空');
        }

        $aApplyFather = ApplyFather::aGetListByApplyIdList([$iApplyId]);
        if (empty($aApplyFather)) {
            throw new Exception('测试申请信息未查得');
        }

        $aCond = [
            ['apply_id', '=', $iApplyId],
//            ['is_schedule', '=', PreTestManage::IS_NO],
        ];
        $aUpdateData = [
//            'is_schedule' => PreTestManage::IS_YES,
            'schedule_time' => date('Y-m-d H:i:s', strtotime('+1 week')),
            'updated_at' => date("Y-m-d H:i:s"),
        ];
        ApplyFather::iUpdateByCond($aCond, $aUpdateData);

        return true;
    }

    /**
     * @return array
     */
    public function fatherFeedback() {
        $iApplyId = (int)request()->post('apply_id', 0);
        if (!$iApplyId) {
            throw new Exception('请求参数[申请表ID(apply_id)]不能为空');
        }
        // 测试申请信息
        $oApply = ApplyCustomer::find($iApplyId);
        if (is_null($oApply)) {
            throw new Exception(sprintf('申请信息查询为空, 查询ID(%s)', $iApplyId));
        }
        $aApply = $oApply->toArray();
        // 测试产品
        $aApplyFather = ApplyFather::aGetListByApplyIdList([$iApplyId]);
        // 系统产品名称map
        list($aFatherMap, $aFatherCreatedMap) = $this->aBuildFatherMap($aApplyFather);
        // 测试产品指标列表
        $aApplyFatherId = array_column($aApplyFather, 'id');
        $aFatherObjectiveList = $this->getFatherObjectiveList($aApplyFatherId);
        // 测试产品list
        $aApplyFatherList = [];
        foreach ($aApplyFather as $item) {
            $aObjectiveList = $aFatherObjectiveList[$item['id']] ?? [];
            $iTestResult = $item['test_result'];

            $aData = [
                'apply_father_id' => $item['id'],
                'father_id' => $item['father_id'],
                'father_name' => $aFatherMap[$item['father_id']] ?? '',
                'apply_father_name' => $item['apply_father_name'],
                'test_status' => $item['test_status'],
                'test_status_text' => PreTestManage::STATUS_TEXT_MAP[$item['test_status']] ?? '',
                'test_result' => $iTestResult,
                'test_result_text' => PreTestManage::TEST_RESULT_TEXT_MAP[$item['test_result']] ?? '',
                'test_result_remark' => $item['test_result_remark'],
                'is_schedule' => $item['is_schedule'],
                'schedule_time' => $item['schedule_time'],
                'objective_list' => $aObjectiveList,
            ];

            if ($iTestResult) {
                $aApplyFatherList[] = $aData;
            } else {
                array_unshift($aApplyFatherList, $aData);
            }
        }

        return [
            'apply_id' => $aApply['id'],
            'company_short_name' => $aApply['company_short_name'],
            'company_name' => $aApply['company_name'],
            'apply_time' => substr($aApply['apply_time'], 0, 10),
            'status' => $aApply['status'],
            'status_text' => PreTestManage::STATUS_TEXT_MAP[$aApply['status']] ?? '',
            'apply_father_list' => $aApplyFatherList,
        ];
    }

    /**
     * @return array
     */
    public function fatherAccess() {
        $iApplyId = (int)request()->post('apply_id', 0);
        if (!$iApplyId) {
            throw new Exception('请求参数[申请表ID(apply_id)]不能为空');
        }
        // 测试申请信息
        $aApply = ApplyCustomer::find($iApplyId)->toArray();
        // 测试产品
        $aApplyFather = ApplyFather::aGetListByApplyIdList([$iApplyId]);
        // 系统产品名称map
        list($aFatherMap, $aFatherCreatedMap) = $this->aBuildFatherMap($aApplyFather);
        // 测试产品list
        $aApplyFatherList = [];
        foreach ($aApplyFather as $aItem) {
            $iAccessAction = $aItem['access_action'];
            $aData = [
                'apply_father_id' => $aItem['id'],
                'father_id' => $aItem['father_id'],
                'father_name' => $aFatherMap[$aItem['father_id']] ?? '',
                'apply_father_name' => $aItem['apply_father_name'],
                'test_status' => $aItem['test_status'],
                'test_status_text' => PreTestManage::STATUS_TEXT_MAP[$aItem['test_status']] ?? '',
                'access_action' => $iAccessAction,
                'access_action_text' => PreTestManage::ACCESS_ACTION_TEXT_MAP[$aItem['access_action']] ?? '',
                'not_access_reason' => $aItem['not_access_reason'] ?: '',
                'not_access_reason_text' => PreTestManage::UNABLE_REASON_TEXT_MAP[$aItem['not_access_reason']] ?? '',
                'access_remark' => $aItem['access_remark'],
            ];

            if ($iAccessAction) {
                $aApplyFatherList[] = $aData;
            } else {
                array_unshift($aApplyFatherList, $aData);
            }
        }

        return [
            'apply_id' => $aApply['id'],
            'company_short_name' => $aApply['company_short_name'],
            'company_name' => $aApply['company_name'],
            'apply_time' => substr($aApply['apply_time'], 0, 10),
            'status' => $aApply['status'],
            'status_text' => PreTestManage::STATUS_TEXT_MAP[$aApply['status']] ?? '',
            'apply_father_list' => $aApplyFatherList,
        ];
    }
}