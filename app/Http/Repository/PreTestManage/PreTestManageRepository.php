<?php

namespace App\Http\Repository\PreTestManage;

use App\Http\Repository\DeptRepository;
use App\Models\DeptGrade;
use Exception;
use App\Define\PreTestManage;
use App\Models\Common\CommonEnumModel;
use App\Models\Crs\SystemDept;
use App\Models\Dept;
use App\Models\PreTestManage\ActionRecord;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyFatherObjective;
use App\Models\PreTestManage\ApplyManual;
use App\Models\PreTestManage\ApplyProduct;
use App\Models\PreTestManage\BusinessProduct;
use App\Models\Product;
use App\Models\SystemAccessLog;
use App\Models\SystemUser;
use App\Utils\Helpers\Func;

/**
 * 售前测试管理系统
 * @package App\Http\Repository\PreTestManage
 * @class PreTestManageRepository
 */
class PreTestManageRepository
{
    protected $aObjectiveMap = [];
    protected $bSearchDept = false;
    protected $sSearchDeptId = '';
    protected $bSearchSalesMan = false;
    protected $aFatherCond = [];

    const DEPT_OTHER = 'DEPT_OTHER';
    /**
     * @var array
     */
    protected $aOtherDept = [
        [
            'dept_id' => self::DEPT_OTHER,
            'dept_name' => '系统支持',
            'user_list' => [
                'ren.zhang' => '张韧',
                'chang.liu' => '刘畅',
                'kai.lin' => '林凯',
            ],
        ],
    ];

    /**
     * @return array|false
     * @throws Exception
     */
    protected function initCond() {
        $aApplyTimeRange = (array)request()->post('apply_time_range', []);
        list($sApplyTimeStart, $sApplyTimeEnd) = $aApplyTimeRange;
        if (strtotime($sApplyTimeStart) > strtotime($sApplyTimeEnd)) {
            throw new Exception(sprintf('请求参数错误,开始时间(%s)不能大于结束时间(%s)', $sApplyTimeStart, $sApplyTimeEnd));
        }
        !$sApplyTimeStart and $sApplyTimeStart = date('Y-m-d', strtotime('-30 days'));
        !$sApplyTimeEnd and $sApplyTimeEnd = date('Y-m-d');
        $iStatus = (int)request()->post('status', 0);
        $sCompanyName = trim(request()->post('company_name', ''));
        $sCompanyShortName = trim(request()->post('company_short_name', ''));
        $iCompanyType = (int)request()->post('company_type', 0);
        $sApplyProduct = trim(request()->post('apply_product', ''));
        $iHaveObjective = (int)request()->post('have_objective', 0);
        $iTestResult = (int)request()->post('test_result', 0);
        $iAccessAction = (int)request()->post('access_action', 0);
        $iUnableReason = (int)request()->post('unable_reason', 0);
        $iIsCall = (int)request()->post('is_call', 0);
        $iFatherKey = (int)request()->post('father_id', 0);
        $iProductKey = (int)request()->post('product_id', 0);
        $iSysFatherId = (int)request()->post('sys_father_id', 0);
        $iSysProductId = (int)request()->post('sys_product_id', 0);
        $sTestResultRemark = trim(request()->post('test_result_remark', ''));
        $iDataSource = (int)request()->post('data_source', 0);
        $iPurpose = (int)request()->post('purpose', 0);
        $sReturnEndTime = request()->post('return_end_time', '');
        $sFeedbackStartTime = request()->post('feedback_start_time', '');
        $sFeedbackEndTime = request()->post('feedback_end_time', '');
        $sActivateStartTime = request()->post('activate_start_time', '');
        $sActivateEndTime = request()->post('activate_end_time', '');

        $aCond = [];
        $aSubCond = [];
        list ($aSalesMan, $sSalesRealName) = $this->initUserAuthCond();
        if ($aSalesMan) {
            $aCond[] = ['salesman', 'in', $aSalesMan];
            $aSubCond[] = ['salesman', 'in', $aSalesMan];
        }
        if ($sSalesRealName) {
            $aCond[] = ['salesman', '=', $sSalesRealName];
            $aSubCond[] = ['salesman', '=', $sSalesRealName];
        }
        if ($sApplyTimeStart) {
            $aCond[] = ['apply_time', '>=', $sApplyTimeStart . ' 00:00:00'];
        }
        if ($sApplyTimeEnd) {
            $aCond[] = ['apply_time', '<=', $sApplyTimeEnd . ' 23:59:59'];
        }
        if ($sReturnEndTime) {
            $aSubCond[] = ['return_time', '<=', $sReturnEndTime . ' 23:59:59'];
            $aSubCond[] = ['test_status', '>=', PreTestManage::STATUS_TEST_DONE];
        }
        if ($sFeedbackStartTime) {
            $aSubCond[] = ['feedback_time', '>=', $sFeedbackStartTime . ' 00:00:00'];
            $aSubCond[] = ['test_status', '>=', PreTestManage::STATUS_FEEDBACK];
        }
        if ($sFeedbackEndTime) {
            $aSubCond[] = ['feedback_time', '<=', $sFeedbackEndTime . ' 23:59:59'];
            $aSubCond[] = ['test_status', '>=', PreTestManage::STATUS_FEEDBACK];
        }
        if ($sActivateStartTime) {
            $aSubCond[] = ['activate_time', '>=', $sActivateStartTime . ' 00:00:00'];
            $aSubCond[] = ['test_status', '>=', PreTestManage::STATUS_ACCESS_DONE];
        }
        if ($sActivateEndTime) {
            $aSubCond[] = ['activate_time', '<=', $sActivateEndTime . ' 23:59:59'];
            $aSubCond[] = ['test_status', '>=', PreTestManage::STATUS_ACCESS_DONE];
        }
        if ($iStatus) {
            if (PreTestManage::STATUS_SCHEDULE_UNDONE == $iStatus) {
                $iStatus = PreTestManage::STATUS_TEST_DONE;
                $aSubCond = array_merge($aSubCond, [
                    ['is_schedule', '=', PreTestManage::IS_NO],
                    ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
                    ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
                    ['schedule_time', '<=', date('Y-m-d H:i:s')],
                ]);
            } else if (PreTestManage::STATUS_SCHEDULE_OVER3 == $iStatus) {
                $iStatus = PreTestManage::STATUS_TEST_DONE;
                $aSubCond = array_merge($aSubCond, [
                    ['is_schedule', '=', PreTestManage::IS_NO],
                    ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
                    ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
                    ['return_time', '<=', date('Y-m-d H:i:s', strtotime('-90 days'))],
                ]);
            }
//            $aCond[] = ['status', '=', $iStatus];
            $aSubCond[] = ['test_status', '=', $iStatus];
        }
        if ($sCompanyName) {
            $aCond[] = ['company_name', 'like', '%' . $sCompanyName . '%'];
        }
        if ($sCompanyShortName) {
            $aCond[] = ['company_short_name', 'like', '%' . $sCompanyShortName . '%'];
        }
        if ($iCompanyType) {
            $aCond[] = ['company_type', '=', $iCompanyType];
        }
        if ($sApplyProduct) {
            $aSubCond = array_merge($aSubCond, [['apply_father_name', '=', $sApplyProduct]]);
        }
        if ($iHaveObjective) {
            $iHaveObjective--;
            $aSubCond = array_merge($aSubCond, [['have_objective', '=', $iHaveObjective]]);
        }
        if ($iTestResult) {
            if (PreTestManage::IS_MINUS_ALL == $iTestResult) {
                $aSubCond = array_merge($aSubCond, [['test_result', 'in', array_keys(PreTestManage::TEST_RESULT_TEXT_MAP)]]);
            } else if (PreTestManage::TEST_RESULT_UNDONE == $iTestResult) {
                $aSubCond = array_merge($aSubCond, [['test_result', 'in', [PreTestManage::TEST_RESULT_UNABLE, PreTestManage::TEST_RESULT_NA]]]);
            } else if (PreTestManage::TEST_RESULT_DONE == $iTestResult) {
                $aSubCond = array_merge($aSubCond, [['test_result', 'in', [PreTestManage::TEST_RESULT_GOOD, PreTestManage::TEST_RESULT_AVERAGE, PreTestManage::TEST_RESULT_BAD]]]);
            } else {
                $iTestResult = PreTestManage::IS_MINUS_ONE == $iTestResult ? 0 : $iTestResult;
                $aSubCond = array_merge($aSubCond, [['test_result', '=', $iTestResult], ['test_status', '>=', PreTestManage::STATUS_TEST_DONE]]);
            }
        }
        if ($iAccessAction) {
            if (PreTestManage::IS_MINUS_ALL == $iAccessAction) {
                $aSubCond = array_merge($aSubCond, [['access_action', 'in', array_keys(PreTestManage::ACCESS_ACTION_TEXT_MAP)]]);
            } else {
                $iAccessAction = PreTestManage::IS_MINUS_ONE == $iAccessAction ? 0 : $iAccessAction;
                $aSubCond = array_merge($aSubCond, [['access_action', '=', $iAccessAction]]);
            }
        }
        if ($iUnableReason) {
            $aSubCond = array_merge($aSubCond, [['not_access_reason', '=', $iUnableReason]]);
        }
        switch ($iIsCall) {
            case 1: // 未接入
                $aSubCond = array_merge($aSubCond, [['is_open', '=', PreTestManage::IS_NO]]);
                break;
            case 2: // 已接入(已开通)-未调用
                $aSubCond = array_merge($aSubCond, [['is_open', '=', PreTestManage::IS_YES]]);
                $aSubCond = array_merge($aSubCond, [['number_total', '=', 0]]);
                break;
            case 3: // 已接入(已开通)-已调用
                $aSubCond = array_merge($aSubCond, [['is_open', '=', PreTestManage::IS_YES]]);
                $aSubCond = array_merge($aSubCond, [['number_total', '>', 0]]);
                break;
            case 4: // 已接入
                $aSubCond = array_merge($aSubCond, [['is_open', '=', PreTestManage::IS_YES]]);
                break;
            default:
                break;
        }
        if ($iFatherKey) {
            $aFatherCond = $this->buildFatherKeyCond($iFatherKey);
            $aSubCond = array_merge($aSubCond, $aFatherCond);
        }
        if ($iProductKey) {
            $aSubCond = array_merge($aSubCond, [['product_key', '=', $iProductKey]]);
        }
        if ($iSysFatherId) {
            $aSubCond = array_merge($aSubCond, [['father_id', '=', $iSysFatherId]]);
        }
        if ($iSysProductId) {
            $aSubCond = array_merge($aSubCond, [['product_list', 'like', '%' . $iSysProductId . '%']]);
        }
        if ($sTestResultRemark) {
            $aSubCond = array_merge($aSubCond, [['test_result_remark', 'like', '%' . $sTestResultRemark . '%']]);
        }
        if ($iDataSource) {
            if (PreTestManage::IS_MINUS_ONE == $iDataSource) {
                $aSubCond = array_merge($aSubCond, [['is_schedule', '<>', PreTestManage::IS_BIND]]);
            } else if (PreTestManage::IS_BIND == $iDataSource) {
                $aSubCond = array_merge($aSubCond, [['is_schedule', '=', PreTestManage::IS_BIND]]);
            }
        }
        if ($iPurpose) {
            $aSubCond = array_merge($aSubCond, [['purpose', '=', $iPurpose]]);
        }

        if ($aSubCond) {
            $aSubCond[] = ['apply_time', '>=', $sApplyTimeStart . ' 00:00:00'];
            $aSubCond[] = ['apply_time', '<=', $sApplyTimeEnd . ' 23:59:59'];

            $aApplyId = ApplyFather::aGetApplyIdByCond($aSubCond);
            if ($aApplyId) {
                $aCond[] = ['id', 'in', $aApplyId];
                $this->aFatherCond = $aSubCond;
            } else {
                return false;
            }
        }

        return $aCond;
    }

    /**
     * @return array
     */
    protected function initSort() {
        $sSortBy = (string)request()->post('sort_by', '');
        $sSortBy = $sSortBy ?: 'apply_time';
        $iSort = (int)request()->post('sort', 1);
        $sSort = $iSort ? 'DESC' : 'ASC';

        return [$sSortBy, $sSort];
    }

    /**
     * @param $iFatherKey
     * @return array[]
     */
    protected function buildFatherKeyCond($iFatherKey) {
        $aMap = BusinessProduct::aGetHaveChildDataMap();
        if (isset($aMap[$iFatherKey])) {
            $aKey = array_merge($aMap[$iFatherKey], [$iFatherKey]);
            $aCond = [['product_key', 'in', $aKey]];
        } else {
            $aCond = [['product_key', '=', $iFatherKey]];
        }

        return $aCond;
    }

    /**
     * @return array
     */
    protected function initUserAuthCond() {
        $sUserCookie = (string)request()->post('user_cookie', '');
        $sDeptId = trim(request()->post('dept', ''));
        $sSalesMan = trim(request()->post('salesman', ''));

        $aSalesMan = [];
        $sSalesRealName = '';
        list($isSale, $aUnderlingRealName, $iSelfOnly) = $this->aUserInfo($sUserCookie);
        // 如果只能查看销售本人数据, 则清空部门搜索和销售姓名模糊搜索
        if ($isSale == 1) {
            // 数据权限内所有销售list
            $aSalesMan = $aUnderlingRealName;
            if ($iSelfOnly) {
                $sDeptId = '';
                $sSalesMan = '';
            }
        }
        if ($sDeptId) {
            $this->bSearchDept = true;
            $this->sSearchDeptId = $sDeptId;
            $aDeptSalesMan = $this->getDeptSalesmanList($sDeptId);
            // 取交集 : 部门下所有销售list 和 数据权限内所有销售list
            $aSalesMan = array_intersect($aUnderlingRealName, $aDeptSalesMan);
        }
        if ($sSalesMan) {
            $this->bSearchSalesMan = true;
            $aUserInfo = SystemUser::getUserInfoByName($sSalesMan);
            $sSalesRealName = $aUserInfo['realname'];
        }

        return [$aSalesMan, $sSalesRealName];
    }

    /**
     * @param $aApplyList
     * @param $aField
     * @return array
     */
    protected function aFormatApplyList($aApplyList = [], $aField = []) {
        if (empty($aApplyList)) {
            return [];
        }
        if (empty($aField)) {
            $aField = ApplyCustomer::ALL_FIELD;
        }

        // 人员部门信息
        $aSalesmanDeptMap = [];
        $aDeptMap = [];
        if (in_array('email', $aField)) {
            $aSalesmanDeptMap = $this->aGetSalesmanDeptMap();
            $aDeptList = SystemDept::getAllDeptInfo();
            $aDeptMap = array_column($aDeptList, 'dept_name', 'dept_id');
        }

        $aDataList = [];
        foreach ($aApplyList as $aApply) {
            $aFormat = [
                'apply_id' => $aApply['id'],
                'company_short_name' => $aApply['company_short_name'] ?: $aApply['company_name'],
                'father_count' => 0,
                'test_done_count' => 0,
                'result_count' => 0,
                'result_undone_count' => 0,
                'result_undone_over_count' => 0,
                'access_count' => 0,
                'online_count' => 0,
                'call_count' => 0,
                '2week_before_count' => 0,
                '2week_in_count' => 0,
                'schedule_undone_count' => 0,
            ];
            if (in_array('email', $aField)) {
                $sDeptId = $aSalesmanDeptMap[$aApply['email']] ?? '';
                $aFormat['dept_id'] = $sDeptId ?? '';
                $aFormat['dept_name'] = $aDeptMap[$sDeptId] ?? '其他';
            }
            in_array('apply_time', $aField) and $aFormat['apply_time'] = $aApply['apply_time'];
            in_array('start_time', $aField) and $aFormat['start_time'] = substr($aApply['start_time'], 0, 10);
            in_array('return_time', $aField) and $aFormat['return_time'] = substr($aApply['return_time'], 0, 10);
            in_array('company_type', $aField) and $aFormat['company_type_text'] = PreTestManage::COMPANY_TYPE_TEXT_MAP[$aApply['company_type']] ?? '';
            in_array('status', $aField) and $aFormat['status_text'] = PreTestManage::STATUS_TEXT_MAP[$aApply['status']] ?? '';
            in_array('is_customer_call', $aField) and $aFormat['is_customer_call_text'] = PreTestManage::WHETHER_TEXT_MAP[$aApply['is_customer_call'] ?? $aApply['is_call']] ?? '';

            $aDataList[] = array_merge($aApply, $aFormat);
        }

        return $aDataList;
    }

    /**
     * @return array
     */
    private function aGetSalesmanDeptMap() {
        $aUserList = SystemUser::getAllUsers();
        $aSalesmanDeptMap = array_column($aUserList, 'dept_id', 'email');
        // 王龙亮, 解雨欣 飞书中邮箱和系统邮箱不一致
        $aSalesmanDeptMap['<EMAIL>'] = 'DEPT2019061815103928';
        $aSalesmanDeptMap['<EMAIL>'] = 'DEPT2019061815103928';

        return $aSalesmanDeptMap;
    }

    /**
     * @param $aApplyFatherList
     * @return array
     */
    protected function aBuildFatherMap($aApplyFatherList = []){
        // 主产品
        $aFatherId = array_column($aApplyFatherList, 'father_id');
        // 子产品
        $aProductListGroup = array_column($aApplyFatherList, 'product_list');
        $sProductIdList = implode(',', $aProductListGroup);
        $aProductIdList = array_unique(explode(',', $sProductIdList));

        $aAllProductId = array_values(array_unique(array_filter(array_merge($aFatherId, $aProductIdList))));
        $aProductList = Product::getProductListByProductIds($aAllProductId, ['product_id', 'product_name', 'create_at']);
        $aProductMap = array_column($aProductList, 'product_name', 'product_id');
//        $aProductMap = $aProductMap + [PreTestManage::FAKE_PRODUCT_ID => '', 0 => ''];

        $aCreatedMap = array_column($aProductList, 'create_at', 'product_id');
//        $aCreatedMap = $aCreatedMap + [PreTestManage::FAKE_PRODUCT_ID => '--', 0 => '--'];

        return [$aProductMap, $aCreatedMap];
    }

    /**
     * @param $aApplyFatherId
     * @return array
     */
    protected function getFatherObjectiveList($aApplyFatherId = []) {
        $aFatherObjective = ApplyFatherObjective::aGetListByApplyIdList($aApplyFatherId);

        // 指标项map
        $aObjectiveMap = $this->aGetObjectiveMap();
        // 测试产品表主键 => [指标项] map
        $aFatherObjectiveList = [];
        foreach ($aFatherObjective as $aItem) {
            $aFatherObjectiveList[$aItem['apply_father_id']][] = [
                'feedback_id' => $aItem['id'],
                'objective_id' => $aItem['objective_id'],
                'objective_name' => $aObjectiveMap[$aItem['objective_id']],
                'result_value' => $aItem['result_value'],
                'remark' => $aItem['remark'],
            ];
        }

        return $aFatherObjectiveList;
    }

    /**
     * @return array
     */
    private function aGetObjectiveMap() {
        if (empty($this->aObjectiveMap)) {
            $this->aObjectiveMap = CommonEnumModel::aGetPreTestMap();
        }

        return $this->aObjectiveMap;
    }

    /**
     * @return array
     */
    public function optionList() {
        list($aObjectiveOption, $aObjectiveDesc, $aObjectiveMap) = CommonEnumModel::aGetPreTestOptionAndDesc();
        // 销售 筛选
        $sUserCookie = (string)request()->post('user_cookie', '');
        $sUserName = $this->sGetUserName($sUserCookie);
        $aUserInfo = SystemUser::getUserInfoByName($sUserName);
        $sDeptId = $aUserInfo['dept_id'];
        $iIsLeader = $aUserInfo['is_leader'];
        // 售前测试部门展示全部销售
        if ('DEPT2020062210573567' == $aUserInfo['dept_id']) {
            $sDeptId = Dept::SALE_DEPT_ID;
            $iIsLeader = 1;
        }
        // 获取所有的销售部门
        $aDeptData = DeptRepository::getSaleDeptRecursion($sDeptId, $iIsLeader);
        $aDeptTreeOption = $aDeptData['dept_infos'];
        $aSalesTreeMap = $this->aBuildSalesTreeData($aDeptTreeOption);

        list($isSale, $aAllSalesman) = $this->isSale($sUserName);
//        $aSalesUserList = SystemUser::getUserInfoByNames($aAllSalesman);
//        usort($aSalesUserList, function($a, $b) {
//            return $a['disabled'] - $b['disabled'];
//        });
//        $aSalesUserMap = array_filter(array_column($aSalesUserList, 'realname', 'realname'));
//        $aSalesUserMap[PreTestManage::ACTION_ADMIN_UNKNOWN] = PreTestManage::ACTION_ADMIN_UNKNOWN;
//        // 部门 筛选
//        $aDeptTextMap = SystemDept::aGetSalesDeptNameMap();
//        unset($aDeptTextMap['DEPT2022120220115177']);
//        unset($aDeptTextMap['DEPT2023040320452534']);
//        unset($aDeptTextMap['DEPT2023060810155155']);
//        unset($aDeptTextMap['DEPT2024031811103235']);
        // 测试效果 筛选
        $aTestResultMap = PreTestManage::TEST_RESULT_TEXT_MAP;
        $aTestResultMapNew = [];
        foreach ($aTestResultMap as $i => $sText) {
            $aTestResultMapNew[(string)$i] = $sText;
        }
        $aTestResultMapNew[PreTestManage::TEST_RESULT_DONE] = '(已获得)';
        $aTestResultMapNew[PreTestManage::TEST_RESULT_UNDONE] = '(未获得)';
        $aTestResultMapNew[PreTestManage::IS_MINUS_ALL] = '(已填写)';
        $aTestResultMapNew[PreTestManage::IS_MINUS_ONE] = '(未填写)';

        // 接入意向 筛选
        $aAccessActionMap = PreTestManage::ACCESS_ACTION_TEXT_MAP;
        $aAccessActionMapNew = [];
        foreach ($aAccessActionMap as $i => $sText) {
            $aAccessActionMapNew[(string)$i] = $sText;
        }
        $aAccessActionMapNew[PreTestManage::IS_MINUS_ALL] = '(已填写)';
        $aAccessActionMapNew[PreTestManage::IS_MINUS_ONE] = '(未填写)';
        // 产品
        list($aSysProductTextOption, $aSysProductTreeMap) = $this->aProductOptionAndMap();
        list($aProductTextOption, $aProductTreeMap) = $this->aBusinessProductOptionAndMap();
        // 数据来源
        $aDataSourceMap = [
            PreTestManage::IS_MINUS_ONE => '测试申请',
            PreTestManage::IS_BIND => '产品绑定',
        ];
        // 测试目的
        $aPurposeMap = [
            PreTestManage::IS_DOCKING => '对接产品',
            PreTestManage::IS_IS_DOCKING_NOT => '非对接',
        ];

        return [
            'status_text_map' => PreTestManage::STATUS_TEXT_MAP,
            'status_text_option' => $this->buildStatusOption(),
            'test_result_text_map' => $aTestResultMapNew,
            'test_result_text_option' => $this->mapToOption(PreTestManage::TEST_RESULT_TEXT_MAP),
            'access_action_text_map' => $aAccessActionMapNew,
            'access_action_text_option' => $this->mapToOption(PreTestManage::ACCESS_ACTION_TEXT_MAP),
            'unable_reason_text_map' => PreTestManage::UNABLE_REASON_TEXT_MAP,
            'unable_reason_text_option' => $this->mapToOption(PreTestManage::UNABLE_REASON_TEXT_MAP),
            'product_text_option' => $aProductTextOption,
            'product_tree_map' => $aProductTreeMap,
            'sys_product_text_option' => $aSysProductTextOption,
            'sys_product_tree_map' => $aSysProductTreeMap,
            'company_type_text_map' => PreTestManage::COMPANY_TYPE_TEXT_MAP,
            'sample_source_text_map' => PreTestManage::SAMPLE_SOURCE_TEXT_MAP,
            'objective_text_option' => $aObjectiveOption,
            'objective_text_map' => $aObjectiveMap,
            'objective_desc' => implode(", ", $aObjectiveDesc),
//            'dept_text_map' => $aDeptTextMap,
//            'salesman_map' => $aSalesUserMap,
            'is_sale' => (int)$isSale,
            'dept_sales_option' => $aDeptTreeOption,
            'sales_tree_map' => $aSalesTreeMap,
            'feedback_source_text_option' => $this->mapToOption(PreTestManage::FEEDBACK_SOURCE_TEXT_MAP),
            'feedback_communicate_text_option' => $this->mapToOption(PreTestManage::WHETHER_CAN_TEXT_MAP),
            'data_source_map' => $aDataSourceMap,
            'purpose_map' => $aPurposeMap,
        ];
    }

    /**
     * @param $aNodeList
     * @param $aFatherKey
     * @return array
     */
    private function aBuildSalesTreeData($aNodeList = [], $aFatherKey = []){
        $aSalesTreeData = [];
        foreach ($aNodeList as $aNode) {
            $aKey = array_merge($aFatherKey, [$aNode['key']]);
            if ($aNode['type'] == 'dept') {
                $aDept[$aNode['value']] = $aKey;
                $aData = $this->aBuildSalesTreeData($aNode['children'], $aKey);
                $aSalesTreeData = array_merge($aSalesTreeData, $aData, $aDept);
            } else if ($aNode['type'] == 'salesman') {
                $aSalesTreeData[$aNode['label']] = $aKey;
            }
        }

        return $aSalesTreeData;
    }

    /**
     * @return array
     */
    private function buildStatusOption() {
        $aMap = PreTestManage::STATUS_TEXT_MAP;
        $aSubMap = PreTestManage::STATUS_TEXT_MAP_SUB;

        $aRet = [];

        foreach ($aMap as $key => $name) {
            $aChild = [];
            if (isset($aSubMap[$key])) {
                foreach ($aSubMap[$key] as $iSubKey => $subName) {
                    $aChild[] = [
                        'value' => $iSubKey,
                        'label' => $subName,
                    ];
                }
            }
            $aData = [
                'value' => $key,
                'label' => $name,
            ];

            $aChild and $aData = array_merge($aData, ['children' => $aChild]);

            $aRet[] = $aData;
        }

        return $aRet;
    }

    /**
     * @return array
     */
    protected function aProductOptionAndMap() {
        // 所有测试产品
        $aProductGroup = ApplyProduct::aGetGroupByProduct();

        return $this->aBuildProductOptionAndMap($aProductGroup);
    }

    /**
     * @param $aProductGroup
     * @return array
     */
    private function aBuildProductOptionAndMap($aProductGroup = []) {
        $aGather = [];
        $aAllProductId = [];
        foreach ($aProductGroup as $aItem) {
            $aGather[$aItem['father_id']][] = $aItem['product_id'];
            $aAllProductId[] = $aItem['father_id'];
            $aAllProductId[] = $aItem['product_id'];
        }
        $aAllProductId = array_unique($aAllProductId);
        $aAllProductList = Product::getProductListByProductIds($aAllProductId, ['product_id', 'product_name']);
        $aProductNameMap = array_column($aAllProductList, 'product_name', 'product_id');

        $aProductOption = [];
        foreach ($aGather as $iFatherId => $aProductId) {
            $aChild = [];
            foreach ($aProductId as $iProductId) {
                if ($iProductId == $iFatherId) {
                    continue;
                }
                $aChild[] = [
                    'value' => $iProductId,
                    'label' => $aProductNameMap[$iProductId] ?? '(' . $iProductId . ')',
                    'type' => 'product',
                    'key' => 'p_' . $iProductId,
                ];
            }

            $aData = [
                'value' => $iFatherId,
                'label' => $aProductNameMap[$iFatherId] ?? '(' . $iFatherId . ')',
                'type' => 'father',
                'key' => 'f_' . $iFatherId,
            ];
            $aChild and $aData = array_merge($aData, ['children' => $aChild]);

            $aProductOption[] = $aData;
        }

        $aMap = $this->aBuildProductTreeData($aProductOption);

        return [$aProductOption, $aMap];
    }

    /**
     * @return array
     */
    protected function aSysProductOptionAndMap() {
        $aProductGroup = Product::getAllSubProduct();

        return $this->aBuildProductOptionAndMap($aProductGroup);
    }

    /**
     * @return array
     */
    private function aBusinessProductOptionAndMap() {
        // 所有测试产品
        $aProductGroup = BusinessProduct::aGetFeiShuDataMap();
        $aProductOption = [];
        foreach ($aProductGroup as $aProduct) {
            $aChild = [];
            foreach ($aProduct['child'] as $aItem) {
                $aChild[] = [
                    'value' => $aItem['product_key'],
                    'label' => $aItem['business_product_name'],
                    'type' => 'product',
                    'key' => 'p_' . $aItem['product_key'],
                ];
            }

            $aData = [
                'value' => $aProduct['product_key'],
                'label' => $aProduct['business_product_name'],
                'type' => 'father',
                'key' => 'f_' . $aProduct['product_key'],
            ];
            $aChild and $aData = array_merge($aData, ['children' => $aChild]);

            $aProductOption[] = $aData;
        }

        $aMap = $this->aBuildProductTreeData($aProductOption);

        return [$aProductOption, $aMap];
    }

    /**
     * @param $aNodeList
     * @param $aFatherKey
     * @return array
     */
    private function aBuildProductTreeData($aNodeList = [], $aFatherKey = []){
        $aSalesTreeData = [];
        foreach ($aNodeList as $aNode) {
            $aKey = array_merge($aFatherKey, [$aNode['key']]);
            if ($aNode['type'] == 'father') {
                $aData[$aNode['value']] = [$aNode['key']];
                $aChildData = [];
                if (isset($aNode['children'])) {
                    $aChildData = $this->aBuildProductTreeData($aNode['children'], $aKey);
                }
                $aSalesTreeData = $aSalesTreeData + $aData + $aChildData;
            } else if ($aNode['type'] == 'product') {
                $aSalesTreeData[$aNode['value']] = $aKey;
            }
        }

        return $aSalesTreeData;
    }

    /**
     * @param $aMap
     * @return array
     */
    private function mapToOption($aMap = []) {
        $aRet = [];

        foreach ($aMap as $key => $name) {
            $aRet[] = [
                'value' => $key,
                'label' => $name,
            ];
        }

        return $aRet;
    }

    /**
     * 通过user_cookie获取当前用户是否是商务
     * @param $sUserCookie
     * @return array
     */
    protected function aUserInfo($sUserCookie) {
        $sUserName = $this->sGetUserName($sUserCookie);
        // 本人信息
        $aUserInfo = SystemUser::getUserInfoByName($sUserName);
        // 下属信息
        $aUnderlingData = DeptRepository::getUnderLing($sUserName);
        $aUnderlingRealName = $aUnderlingData['realname'];
        $aUnderlingRealName[] = $aUserInfo['realname'];

        // 判断是否只能查看自己
        $aNameCount = array_count_values($aUnderlingRealName);
        $iSelfOnly = count($aNameCount) == 1;
        list($isSale, $aAllSalesman) = $this->isSale($sUserName);

        return [$isSale, $aUnderlingRealName, $iSelfOnly];
    }

    /**
     * @param $sUserCookie
     * @return string
     */
    private function sGetUserName($sUserCookie) {
//        return 'mengsi.wang';
//        return 'binfang.ma';
//        return 'zhihui.zhao01';
        return Func::getUserNameFromCookie($sUserCookie);
    }

    /**
     * @param $sUserName
     * @return array
     */
    private function isSale($sUserName) {
        list($aAllSalesman, $aTestUser, $aOperationUser) = $this->getSalesmanList();
        $isSale = 0;
        if (in_array($sUserName, $aAllSalesman)) {
            $isSale = 1;
        } else if (in_array($sUserName, $aTestUser)) {
            $isSale = 2;
        } else if (in_array($sUserName, $aOperationUser)) {
            $isSale = 3;
        }

        return [$isSale, $aAllSalesman];
    }

    /**
     * @param $sDeptId
     * @param $bRealName
     * @return array
     */
    protected function getDeptSalesmanList($sDeptId = '', $bRealName = true) {
        if (empty($sDeptId)) {
            return [];
        }

        if ($sDeptId != self::DEPT_OTHER) {
            $aDeptInfo = DeptRepository::getSaleDeptRecursion($sDeptId, true);
            $aDeptData = $aDeptInfo['dept_infos'] ?? [];
            $aList = $this->getDeptSalesmanListByOption($aDeptData, $bRealName);
        } else {
            $aDeptMap = array_column($this->aOtherDept, null, 'dept_id');
            $aUserList = $aDeptMap[$sDeptId]['user_list'] ?? [];
            $aList = $bRealName ? array_values($aUserList) : array_keys($aUserList);
        }

        return $aList;
    }

    /**
     * @param $aNodeList
     * @return array
     */
    private function getDeptSalesmanListByOption($aNodeList = [], $bRealName = true) {
        $aSalesmanList = [];
        foreach ($aNodeList as $aNode) {
            if ($aNode['type'] == 'dept') {
                $aData = $this->getDeptSalesmanListByOption($aNode['children'], $bRealName);
                $aSalesmanList = array_merge($aSalesmanList, $aData);
            } else if ($aNode['type'] == 'salesman') {
                $aSalesmanList[] = $bRealName ? $aNode['label'] : $aNode['value'];
            }
        }

        return $aSalesmanList;
    }

    /**
     * @return array
     */
    private function getSalesmanList(){
        // 所有销售
        $aAllSalesman = DeptRepository::getAllSalesman();
//        $aAllSalesman[] = 'xiaoqun.chen';
        // 排除 售前测试部
        $aTestUser = SystemUser::getUserInfoByDeptId('DEPT2020062210573567');
        $aTestUser = array_column($aTestUser, 'username');
        $aOnlySalesman = array_diff($aAllSalesman, $aTestUser);
        // 售前测试部
        $aTestUser = ['zepeng.lu', 'yumeng.wang'];
        // 运营部
        $aOperationUser = SystemUser::getUserInfoByDeptId('DEPT2019061815295230');
        $aOperationUser = array_column($aOperationUser, 'username');
        $aOperationUser[] = 'kai.lin';
        $aOperationUser[] = 'chang.liu';
        $aOperationUser[] = 'yanming.li';

        return [$aOnlySalesman, $aTestUser, $aOperationUser];
    }

    /**
     * @param $aDataDemo
     * @return array[]
     */
    protected function initSalesMap($aDataDemo = []) {
        $aSalesMap = [];
        $aSalesDeptMap = [];

        // 初始化 销售 信息结构
        list($aAllSalesman, $aTestUser, $aOperationUser) = $this->getSalesmanList();
        $aSalesUserList = SystemUser::getUserInfoByNames($aAllSalesman);

        foreach ($aSalesUserList as $aSales) {
            if (!$aSales['realname']) {
                continue;
            }
            $aSalesMap[$aSales['realname']] = $aDataDemo;
            $aSalesDeptMap[$aSales['realname']] = $aSales['dept_id'];
        }

        return [$aSalesMap, $aSalesDeptMap];
    }

    /**
     * @param $aDataDemo
     * @param $aFatherId
     * @return array[]
     */
    protected function initProductMap($aDataDemo = [], $aFatherId = []) {
        $aDataMap = [];
        $aProductMap = [];

        $aBusinessProduct = BusinessProduct::aGetAllData();
        foreach ($aBusinessProduct as $aItem) {
            $sPrimaryKey = 'bp_' . $aItem['product_key'];

            $aDataMap[$sPrimaryKey] = $aDataDemo;
            $aProductMap[$sPrimaryKey] = [
                'product_name' => $aItem['business_product_name'],
                'product_key' => $aItem['product_key'],
                'father_id' => 0,
                'is_schedule' => 0,
            ];

            $aItem['father_key'] and $aProductMap[$sPrimaryKey]['father_id'] = 'bp_' . $aItem['father_key'];
        }

        if ($aFatherId) {
            $aSystemProduct = Product::getProductListByProductIds($aFatherId);
            $aSystemProductMap = array_column($aSystemProduct, 'product_name', 'product_id');
            foreach ($aFatherId as $iFatherId) {
                $sPrimaryKey = 'sp_' . $iFatherId;
                $aDataMap[$sPrimaryKey] = $aDataDemo;
                $aProductMap[$sPrimaryKey] = [
                    'product_name' => $aSystemProductMap[$iFatherId],
                    'product_key' => $iFatherId,
                    'father_id' => 0,
                    'is_schedule' => PreTestManage::IS_BIND,
                ];
            }
        }

        return [$aDataMap, $aProductMap];
    }

    /**
     * @param $aSalesMap
     * @param $aSalesDeptMap
     * @param $fStatTotal
     * @return mixed
     */
    protected function aGatherSalesDataToDept($aSalesMap = [], $aSalesDeptMap = [], $fStatTotal = null) {
        // 部门信息
        $aAllDept = SystemDept::getAllDeptInfo();
        $aAllDeptMap = array_column($aAllDept, 'dept_name', 'dept_id');
        // 聚合 至 部门维度
        $aDeptMap = [];
        foreach ($aSalesDeptMap as $sRealName => $sDeptId) {
            $sDeptName = $aAllDeptMap[$sDeptId] ?? '';
            $aData = $aSalesMap[$sRealName] ?? ['primary_count' => 0];
            if (!$sDeptName || !$aData['primary_count']) {
                continue;
            }

            $aDeptMap[$sDeptId][] = array_merge($aData, ['dept_name' => $sDeptName, 'name' => $sRealName, 'primary_key' => $sRealName]);
        }
        // 按照 部门排序 计算合计
        $aFatherDeptMap = DeptGrade::aGetSaleDeptMap();
        // 没有人员提交过测试的部门
        $aExcludeDept = [
            'DEPT2022120220115177', // 生态合作部
            'DEPT2023040320452534', // 企服-内部
            'DEPT2023060810155155', // 营销-内部
            'DEPT2024031811103235', // 海外
        ];
        $aDeptDataList = [];
        foreach ($aFatherDeptMap as $aDept) {
            $aDept = array_unique(array_diff($aDept, $aExcludeDept));
            foreach ($aDept as $sDept) {
                $sDeptName = $aAllDeptMap[$sDept] ?? '';
                $aDeptData = $aDeptMap[$sDept] ?? [];
                if (empty($aDeptData)) {
                    continue;
                }

                $aDeptData = $this->$fStatTotal($aDeptData, ['dept_name' => $sDeptName, 'name' => '合计', 'dept_key' => $sDept, 'primary_key' => $sDept]);
                $aDeptDataList = array_merge($aDeptDataList, $aDeptData);
            }
        }
        return $this->$fStatTotal($aDeptDataList, ['dept_name' => '总计', 'name' => '--', 'dept_key' => '', 'primary_key' => 'total']);
    }

    /**
     * @param $aDataMap
     * @param $aProductMap
     * @param $fStatTotal
     * @return array
     */
    protected function aGatherProductDataToProduct($aDataMap = [], $aProductMap = [], $fStatTotal = null) {
        $aMap = [];
        $aFatherMap = [];
        foreach ($aDataMap as $sPrimaryKey => $aData) {
            $aProduct = $aProductMap[$sPrimaryKey] ?? [];
            if (!$aData['primary_count'] || empty($aProduct)) {
                continue;
            }

            $aMap[$sPrimaryKey] = array_merge($aData, ['product_name' => $aProduct['product_name'], 'product_key' => $aProduct['product_key'], 'is_schedule' => $aProduct['is_schedule'], 'primary_key' => $sPrimaryKey]);
            $sFatherKey = $aProduct['father_id'];
            if ($sFatherKey) {
                $aFatherMap[$sFatherKey][] = $sPrimaryKey;
            }
        }

        foreach ($aFatherMap as $sFatherKey => $aChildKey) {
            $aFatherData = $aMap[$sFatherKey] ?? [];
            $aFather = $aProductMap[$sFatherKey] ?? [];

            $aChild = [];
            foreach ($aChildKey as $sChildKey) {
                if (isset($aMap[$sChildKey])) {
                    $aChild[] = $aMap[$sChildKey];
                    unset($aMap[$sChildKey]);
                }
            }
            if (empty($aChild)) {
                continue;

            }
            $aChild_ = $aChild;
            $aFatherData and $aChild_[] = $aFatherData;
            $aFatherData = $this->$fStatTotal($aChild_, ['product_name' => $aFather['product_name'] ?? '', 'product_key' => $aFather['product_key'], 'primary_key' => $sFatherKey, 'total_flag' => false], true);
            $aFatherData['child'] = $aChild;

            $aMap[$sFatherKey] = $aFatherData;
        }

        return $this->$fStatTotal(array_values($aMap), ['product_name' => '总计', 'product_key' => 0, 'primary_key' => 'total']);
    }

    /**
     * @return array
     */
    public function logOptionList() {
        // 销售
        $sSalesDeptId = Dept::SALE_DEPT_ID;
        $aSalesDeptInfo = DeptRepository::getSaleDeptRecursion($sSalesDeptId, true);
        $aSalesDeptData = $aSalesDeptInfo['dept_infos'];

        $aOtherDeptData = [];
        foreach ($this->aOtherDept as $aItem) {
            $aChild = [];

            foreach ($aItem['user_list'] as $sKey => $sRealName) {
                $aChild[] = [
                    'value' => $sKey,
                    'key' => 'salesman_' . $sKey,
                    'type' => 'salesman',
                    'label' => $sRealName,
                ];
            }

            $aOtherDeptData[] = [
                'value' => $aItem['dept_id'],
                'key' => 'dept_' . $aItem['dept_id'],
                'label' => $aItem['dept_name'],
                'type' => 'dept',
                'children' => $aChild,
            ];
        }

        $aUserOption = array_merge($aSalesDeptData, $aOtherDeptData);

        return [
            'user_option' => $aUserOption,
        ];
    }

    /**
     * @return array[]
     * @throws Exception
     */
    public function logList() {
        $aTimeRange = (array)request()->post('time_range', []);
        list($sTimeStart, $sTimeEnd) = $aTimeRange;
        if (strtotime($sTimeStart) > strtotime($sTimeEnd)) {
            throw new Exception(sprintf('请求参数错误,开始时间(%s)不能大于结束时间(%s)', $sTimeStart, $sTimeEnd));
        }
        $sDeptId = trim(request()->post('dept', ''));
        $sSalesMan = trim(request()->post('salesman', ''));

        $aCond = [];
        if ($sTimeStart) {
            $aCond[] = ['created_at', '>=', strtotime( $sTimeStart . ' 00:00:00')];
        }
        if ($sTimeEnd) {
            $aCond[] = ['created_at', '<=', strtotime($sTimeEnd . ' 23:59:59')];
        }
        if ($sDeptId) {
            $aUserList = $this->getDeptSalesmanList($sDeptId, false);
            $aCond[] = ['username', 'in', $aUserList];
        }
        if ($sSalesMan) {
            $aCond[] = ['username', '=', $sSalesMan];
        }
        $aCond[] = ['uri', 'like', 'pre_test_manage%'];
        $aLogList = SystemAccessLog::getDataByCond($aCond);

        $aRet = $this->aFormatLog($aLogList);

        return [
            'list' => $aRet,
            'total' => count($aRet),
        ];
    }

    /**
     * @param $aLogList
     * @return array
     */
    private function aFormatLog($aLogList = []) {
        $aRet = [];

        if (empty($aLogList)) {
            return $aRet;
        }
        $aUserList = SystemUser::getAllUsers();
        $aUserNameMap = array_column($aUserList, 'realname', 'username');
        foreach ($aLogList as $aLog) {
            if (!$aLog['username']) {
                continue;
            }
            $sUri = str_replace('pre_test_manage/', '', $aLog['uri']);
            $sAction = PreTestManage::LOG_ACTION_MAP[$sUri] ?? '';
            if (!$sAction) {
                continue;
            }

            $aRet[] = [
                'username' => $aUserNameMap[$aLog['username']] ?? $aLog['username'],
                'uri' => $sUri,
                'action' => $sAction,
                'param' => $aLog['content'],
                'time' => $aLog['created_at'],
            ];
        }

        return $aRet;
    }

    /**
     * @return array[]
     * @throws Exception
     */
    public function actionRecord () {
        $aTimeRange = (array)request()->post('time_range', []);
        list($sTimeStart, $sTimeEnd) = $aTimeRange;
        if (strtotime($sTimeStart) > strtotime($sTimeEnd)) {
            throw new Exception(sprintf('请求参数错误,开始时间(%s)不能大于结束时间(%s)', $sTimeStart, $sTimeEnd));
        }
        $sDeptId = trim(request()->post('dept', ''));
        $sSalesMan = trim(request()->post('salesman', ''));

        $aCond = [];
        if ($sTimeStart) {
            $aCond[] = ['date', '>=', date('Ymd', strtotime( $sTimeStart . ' 00:00:00'))];
        }
        if ($sTimeEnd) {
            $aCond[] = ['date', '<=', date('Ymd', strtotime($sTimeEnd . ' 23:59:59'))];
        }
        if ($sDeptId) {
            $aUserList = $this->getDeptSalesmanList($sDeptId, false);
            $aCond[] = ['username', 'in', $aUserList];
        }
        if ($sSalesMan) {
            $aCond[] = ['username', '=', $sSalesMan];
        }
        $aDataList = ActionRecord::getDataByCond($aCond);

        $aTitleText = [];
        $aPvData = [];
        $aUvData = [];

        if (empty($aDataList)) {
            return [
                'pv_data' => [
                    'x_val' => $aTitleText,
                    'y_val' => $aPvData,
                ],
                'uv_data' => [
                    'x_val' => $aTitleText,
                    'y_val' => $aUvData,
                ],
            ];
        }

        for ($i = $sTimeStart; $i <= $sTimeEnd; $i = date("Y-m-d", strtotime("+1 day", strtotime($i)))) {
            $aTitleText[] = date("Ymd", strtotime($i));
        }

        $aDataMap = [];
        foreach ($aDataList as $aData) {
            $aRecord = json_decode($aData['content'], true);

            $aDataMap[$aData['username']][$aData['date']] = [
                'pv' => array_sum($aRecord),
                'uv' => count($aRecord),
            ];
        }
        $aUserName = array_keys($aDataMap);
        $aUserData = SystemUser::getRealNameByNames($aUserName);

        foreach ($aDataMap as $sUserName => $aData) {
            $sRealName = $aUserData[$sUserName] ?? $sUserName;

            foreach ($aTitleText as $sDate) {
                $aPvData[$sRealName][] = ['total' => $aData[$sDate]['pv'] ?? 0];
                $aUvData[$sRealName][] = ['total' => $aData[$sDate]['uv'] ?? 0];
            }
        }

        return [
            'pv_data' => [
                'x_val' => $aTitleText,
                'y_val' => $aPvData,
            ],
            'uv_data' => [
                'x_val' => $aTitleText,
                'y_val' => $aUvData,
            ],
        ];
    }

    /**
     * @return int
     */
    public function logWrite() {
        \App\Models\PreTestManage\ActionLog::log(1, ['test' => 'test'], 'SYS');

        $iCount = \App\Models\PreTestManage\ActionLog::select()->count();

        return $iCount;
    }

    /**
     * @return int
     */
    public function logRead() {
        $iCount = \App\Models\PreTestManage\ActionLog::select()->count();

        return $iCount;
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function manualDone() {
        $aId = (array)request()->post('id', []);
        if (empty($aId)) {
            throw new Exception('ID为空');
        }
        $aCond = [
            ['id', 'in', $aId],
            ['is_done', '=', PreTestManage::IS_NO],
        ];
        $aApplyManualList = ApplyManual::oGetDataListByCond($aCond);
        if (empty($aApplyManualList->toArray())) {
            return false;
        }
        foreach ($aApplyManualList as $oApplyManual) {
            $oApplyManual->is_done = PreTestManage::IS_YES;
            $oApplyManual->save();
        }

        return true;
    }
}