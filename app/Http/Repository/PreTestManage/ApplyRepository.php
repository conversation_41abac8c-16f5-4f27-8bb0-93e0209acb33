<?php

namespace App\Http\Repository\PreTestManage;

use App\Exports\PretestManageListExport;
use App\Models\Common\CommonEnumModel;
use Exception;
use App\Define\PreTestManage;
use App\Models\Customer;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyFatherObjective;
use App\Models\PreTestManage\ApplyProduct;
use App\Models\PreTestManage\BusinessProduct;
use App\Models\Product;
use Maatwebsite\Excel\Facades\Excel;

/**
 * 测试明细
 * @package App\Http\Repository\PreTestManage
 * @class ApplyRepository
 */
class ApplyRepository extends PreTestManageRepository
{

    /**
     * @return array
     * @throws Exception
     */
    public function list() {
        $aDataList = $this->getDataList();
        return [
            'list' => $aDataList,
            'total' => count($aDataList),
        ];
    }

    /**
     * 测试明细列表下载excel
     */
    public function listDownload() {
        $aDataList = $this->getDataList();
        return Excel::download(new PretestManageListExport($aDataList), '售前测试明细.xlsx');
    }

    /**
     * @return array
     * @throws Exception
     */
    private function getDataList()
    {
        $aCond = $this->initCond();
        if (false === $aCond) {
            return [
                'list' => [],
                'total' => 0,
            ];
        }
        $iIsGather = (int)request()->post('is_gather', 1);
        list($sSortBy, $sSort) = $this->initSort();

        $aDataList = [];
        // 申请列表
        $aField = ApplyCustomer::CORE_FIELD;
        $aApplyList = ApplyCustomer::aGetListByCond($aCond, $aField);
        if ($aApplyList) {
            $aApplyDataList = $this->aFormatApplyList($aApplyList, $aField);

            $aApplyMap = array_column($aApplyDataList, null, 'apply_id');
            $aApplyId = array_column($aApplyList, 'id');
            $this->aFatherCond = array_merge($this->aFatherCond, [['apply_id', 'in', $aApplyId], ['is_delete', '=', PreTestManage::IS_NO]]);
            $aApplyFatherList = ApplyFather::aGetDataByCond($this->aFatherCond, ['*'], $sSortBy, $sSort);

            $aFatherListMap = $this->aFormatFatherList($aApplyFatherList);

            if ($iIsGather) {
                $aGatherMap = [];
                foreach ($aApplyDataList as $aApply) {
                    $aGatherMap[$aApply['company_short_name']][] = $aApply['apply_id'];
                }
                foreach ($aGatherMap as $aApplyId) {
                    foreach ($aApplyId as $iApplyId) {
                        $aApply = $aApplyMap[$iApplyId];
                        if (empty($aApply)) {
                            continue;
                        }
                        $aFatherList = $aFatherListMap[$iApplyId] ?? [];
                        if (empty($aFatherList)) {
                            continue;
                        }

                        $aList = $this->aFormatProductListWithCustomer($aFatherList, $aApply);
                        $aDataList = array_merge($aDataList, $aList);
                    }
                }
            } else {
                foreach ($aFatherListMap as $iApplyId => $aFatherList) {
                    $aApply = $aApplyMap[$iApplyId];
                    if (empty($aApply)) {
                        continue;
                    }

                    $aList = $this->aFormatProductListWithCustomer($aFatherList, $aApply);
                    $aDataList = array_merge($aDataList, $aList);
                }
            }
        }
        
        return $aDataList;
    }

    /**
     * @param $aApplyFatherList
     * @return array
     */
    private function aFormatFatherList($aApplyFatherList){
        // 产品信息
        list($aProductMap, $aProductCreatedMap) = $this->aBuildFatherMap($aApplyFatherList);
        // 业务产品
        $aBusinessProductMap = BusinessProduct::aGetDataMap();
        // 测试产品指标列表
        $aApplyFatherId = array_column($aApplyFatherList, 'id');
        $aFatherObjectiveList = $this->getFatherObjectiveList($aApplyFatherId);

        $sNow = date('Y-m-d H:i:s');
        $aProductListMap = [];
        foreach ($aApplyFatherList as $item) {
            $aBusinessProduct = $aBusinessProductMap[$item['product_key']] ?? [];
            $iFatherId = $item['father_id'];
            $aObjectiveList = $aFatherObjectiveList[$item['id']] ?? [];
            $sFatherName = (PreTestManage::IS_BIND <> $item['is_schedule'] && !empty($aBusinessProduct)) ? $aBusinessProduct['business_product_name'] : $aProductMap[$iFatherId];
            $sProductCreateTime = ($aBusinessProduct && $aBusinessProduct['product_id'] > 0) ? '标品已上线' : (isset($aProductCreatedMap[$iFatherId]) ? substr($aProductCreatedMap[$iFatherId], 0, 10) : '-');

            $aProductId = $item['product_list'] ? explode(',', $item['product_list']) : [];
            $aProductName = [];
            foreach ($aProductId as $iProductId) {
                $aProductName[] = $aProductMap[$iProductId] ?? '';
            }

            $aProductListMap[$item['apply_id']][] = [
                'apply_id' => $item['apply_id'],
                'apply_father_id' => $item['id'],
                'father_id' => $iFatherId,
                'product_key' => $item['product_key'],
                'father_name' => $sFatherName,
                'product_list_name' => implode(',', $aProductName) ?: '-',
                'test_status' => $item['test_status'],
                'test_status_text' => PreTestManage::STATUS_TEXT_MAP[$item['test_status']] ?? '',
                'test_result' => $item['test_result'],
                'test_result_text' => PreTestManage::TEST_RESULT_TEXT_MAP[$item['test_result']] ?? '',
                'test_result_remark' => $item['test_result_remark'],
                'access_action' => $item['access_action'],
                'access_action_text' => PreTestManage::ACCESS_ACTION_TEXT_MAP[$item['access_action']] ?? '',
                'not_access_reason' => $item['not_access_reason'],
                'not_access_reason_text' => PreTestManage::UNABLE_REASON_TEXT_MAP[$item['not_access_reason']] ?? '',
                'access_remark' => $item['access_remark'],
                'is_open' => $item['is_open'],
                'is_open_text' => $item['is_open'] ? '是' : '否',
                'is_call_text' => $item['is_open'] ? ($item['number_total'] > 0 ? '是' : '否') : '-',
                'call_time' => $item['call_time'] ? substr($item['call_time'], 0, 10) : '',
                'date_total_90d' => $item['date_total_90d'] ?: '',
                'number_total_90d' => $item['number_total_90d'] ? convertToChineseNumeration($item['number_total_90d']) : '-',
                'number_total' => $item['is_open'] ? convertToChineseNumeration($item['number_total']) : '-',
                'product_create_time' => $sProductCreateTime,
                'must_have_objective' => $item['test_status'] <= PreTestManage::STATUS_TEST_DONE ? PreTestManage::IS_YES : $item['have_objective'],
                'objective_list' => $aObjectiveList,
                'is_schedule' => $item['test_status'] != PreTestManage::STATUS_TEST_DONE ? $item['is_schedule'] : ($item['schedule_time'] > $sNow ? 1 : 0),
                'schedule_time' => $item['schedule_time'],
                'start_time' => $item['start_time'] ?: '-',
                'return_time' => $item['return_time'] ?: '-',
                'feedback_time' => $item['feedback_time'] ?: '-',
                'access_time' => $item['access_time'] ?: '-',
                'activate_time' => $item['activate_time'] ?: '-',
                'source' => $item['source'] ?: '',
                'source_text' => PreTestManage::FEEDBACK_SOURCE_TEXT_MAP[$item['source']] ?? '-',
                'source_name' => $item['source_name'],
                'can_communicate' => $item['can_communicate'] ?: '',
                'can_communicate_text' => PreTestManage::WHETHER_CAN_TEXT_MAP[$item['can_communicate']] ?? '-',
                'sample_size' => $item['sample_size'] ?: '',
            ];
        }

        return $aProductListMap;
    }

    /**
     * @param $aApplyFatherList
     * @param $aApply
     * @return array
     */
    private function aFormatProductListWithCustomer($aApplyFatherList = [], $aApply = []){
        $aRet = [];
        foreach ($aApplyFatherList as $aFatherData) {
            $aRet[] = array_merge($aFatherData, $aApply);
        }

        return $aRet;
    }

    /**
     * @return bool
     */
    public function staySingle() {
        $iApplyFatherId = (int)request()->post('apply_father_id', 0);
        if (!$iApplyFatherId) {
            throw new Exception('请求参数[测试产品表ID(apply_father_id)]不能为空');
        }

        $oApplyFather = ApplyFather::find($iApplyFatherId);
        if (empty($oApplyFather)) {
            throw new Exception('测试产品信息未查得');
        }

//        $oApplyFather->is_schedule = PreTestManage::IS_YES;
        $oApplyFather->schedule_time = date('Y-m-d H:i:s', strtotime('+1 week'));
        $oApplyFather->updated_at = date("Y-m-d H:i:s");
        $oApplyFather->save();

        return true;
    }

    /**
     * @return bool
     */
    public function saveFeedback() {
        $iApplyId = (int)request()->post('apply_id', 0);
        $iApplyFatherId = (int)request()->post('apply_father_id', 0);
        $iFatherId = (int)request()->post('father_id', 0);
        $iTestResult = (int)request()->post('test_result', 0);
        $sTestResultRemark = (string)request()->post('test_result_remark', '');
        $aObjectiveList = (array)request()->post('objective_list', []);
        $iSource = (int)request()->post('source', 0);
        $sSourceName = trim(request()->post('source_name', ''));
        $iCanCommunicate = (int)request()->post('can_communicate', 0);
        $iMustHaveObjective = (int)request()->post('must_have_objective', 0);
        $iAccessAction = (int)request()->post('access_action', 0);
        $iNotAccessReason = (int)request()->post('not_access_reason', 0);
        $sAccessRemark = trim(request()->post('access_remark', ''));
        $sTime = date("Y-m-d H:i:s");

        if (!$iApplyId || !$iApplyFatherId || !$iTestResult) {
            throw new Exception('请求参数[申请表ID(apply_id), 测试产品表ID(apply_product_id), 测试效果(test_result)]不能为空');
        }
        if (in_array($iTestResult, [PreTestManage::TEST_RESULT_GOOD, PreTestManage::TEST_RESULT_AVERAGE, PreTestManage::TEST_RESULT_BAD]) && $iMustHaveObjective && count($aObjectiveList) < 1) {
            throw new Exception('请填写测试效果指标信息');
        }

        // 保存 反馈效果
        $oApplyFather = ApplyFather::find($iApplyFatherId);
        $iHaveObjective = $oApplyFather->have_objective;
        // 指标
        $aObjectiveInsertList = [];
        if (!empty($aObjectiveList)) {
            foreach ($aObjectiveList as $aItem) {
                $iObjectiveId = $aItem['objective_id'];
                $sResultValue = $aItem['result_value'] ?: '';
                $sRemark = $aItem['remark'] ?: '';
                if (!$sResultValue && !$sRemark) {
                    continue;
                }

                $aObjectiveInsertList[] = [
                    'apply_id' => $iApplyId,
                    'apply_father_id' => $iApplyFatherId,
                    'father_id' => $oApplyFather->father_id,
                    'objective_id' => $iObjectiveId,
                    'result_value' => $sResultValue,
                    'remark' => $sRemark,
                    'created_at' => $sTime,
                ];
            }
        }
        $oApplyFather->have_objective = count($aObjectiveInsertList) ? PreTestManage::IS_YES : PreTestManage::IS_NO;
        if (-1 == $oApplyFather->father_id) {
            $oApplyFather->father_id = $iFatherId;
        }
        $oApplyFather->test_result = $iTestResult;
        $oApplyFather->test_result_remark = $sTestResultRemark;
        $oApplyFather->is_schedule = max($oApplyFather->is_schedule, PreTestManage::IS_DONE);
        $oApplyFather->feedback_time = $oApplyFather->feedback_time ?: $sTime;
        $oApplyFather->updated_at = $sTime;
        $oApplyFather->source = $iSource;
        $oApplyFather->source_name = $sSourceName;
        $oApplyFather->can_communicate = $iCanCommunicate;
        $iAccessStatus = 0;
        if ($iAccessAction) {
            $oApplyFather->access_action = $iAccessAction;
            $oApplyFather->not_access_reason = $iNotAccessReason;
            $oApplyFather->access_remark = $sAccessRemark;
            $oApplyFather->access_time = $oApplyFather->access_time ?: $sTime;
            $iAccessStatus = $iAccessAction == PreTestManage::ACCESS_ACTION_ABLE ? PreTestManage::STATUS_ACCESS_ABLE : PreTestManage::STATUS_ACCESS_UNABLE;
        }
        $oApplyFather->test_status = max($oApplyFather->test_status, PreTestManage::STATUS_FEEDBACK, $iAccessStatus);
        $oApplyFather->save();

        // 保存 指标
        // 先删后存
        $iHaveObjective and ApplyFatherObjective::iDeleteByApplyFatherId([$iApplyFatherId]);
        if (!empty($aObjectiveInsertList)) {
            ApplyFatherObjective::batchInsert($aObjectiveInsertList);
        }

        // 检查其他产品的反馈效果 => 修改 申请表状态
        $this->updateApplyStatus($iApplyId);

        return true;
    }

    /**
     * @param $iApplyId
     * @return true
     */
    private function updateApplyStatus($iApplyId = 0) {
        $iHighStatus = $this->calculateStatus($iApplyId);
        if ($iHighStatus) {
            $aUpdate = [
                'status' => $iHighStatus
            ];
            if (PreTestManage::STATUS_TESTING == $iHighStatus) {
                $aUpdate['start_time'] = date("Y-m-d H:i:s");
            } else if (PreTestManage::STATUS_TEST_DONE == $iHighStatus) {
                $aUpdate['return_time'] = date("Y-m-d H:i:s");
            }
            ApplyCustomer::iUpdateByApplyId([$iApplyId], $aUpdate);
        }

        return true;
    }

    /**
     * @param $iApplyId
     * @return mixed|null
     */
    private function calculateStatus($iApplyId){
        $aApplyFatherList = ApplyFather::aGetListByApplyIdList([$iApplyId]);
        $aStatusList = array_column($aApplyFatherList, 'test_status');
        return $this->calculateStatusPriority($aStatusList);
    }

    /**
     * @param $aStatusList
     * @return mixed|null
     */
    private function calculateStatusPriority($aStatusList) {
        $aPriority = PreTestManage::STATUS_PRIORITY;
        // 最高优先级
        $iHighPriority = 0;
        // 最高优先级状态
        $iHighStatus = null;

        foreach ($aStatusList as $iStatus) {
            if (isset($aPriority[$iStatus]) && $aPriority[$iStatus] > $iHighPriority) {
                $iHighPriority = $aPriority[$iStatus];
                $iHighStatus = $iStatus;
            }
        }

        return $iHighStatus;
    }

    /**
     * @return bool
     */
    public function saveAccess() {
        $iApplyId = (int)request()->post('apply_id', 0);
        $iApplyFatherId = (int)request()->post('apply_father_id', 0);
        $iAccessAction = (int)request()->post('access_action', 0);
        $iNotAccessReason = (int)request()->post('not_access_reason', 0);
        $sAccessRemark = (string)request()->post('access_remark', '');
        $sTime = date("Y-m-d H:i:s");

        if (!$iApplyId || !$iApplyFatherId || !$iAccessAction) {
            throw new Exception('请求参数[申请表ID(apply_id), 测试产品表(apply_father_id), 接入结果(access_action)]不能为空');
        }

        // 保存 接入信息
        $oApplyFather = ApplyFather::find($iApplyFatherId);
        if ($oApplyFather->test_status < PreTestManage::STATUS_TEST_DONE) {
            throw new Exception('该产品没有测试完成, 无法填写接入信息');
        }
        $oApplyFather->access_action = $iAccessAction;
        $oApplyFather->not_access_reason = $iAccessAction == PreTestManage::ACCESS_ACTION_UNABLE ? $iNotAccessReason : PreTestManage::UNABLE_REASON_DEFAULT;
        $oApplyFather->access_remark = $sAccessRemark;
        if (in_array($oApplyFather->test_status, PreTestManage::CAN_SAVE_ACCESS_STATUS)
            && PreTestManage::ACCESS_ACTION_DEFAULT != $iAccessAction
        ) {
            $oApplyFather->test_status = PreTestManage::ACCESS_ACTION_STATUS_MAP[$iAccessAction] ?? $oApplyFather->test_status;
        }
        $oApplyFather->updated_at = $sTime;
        $oApplyFather->is_schedule = max($oApplyFather->is_schedule, PreTestManage::IS_DONE);
        $oApplyFather->access_time = $oApplyFather->access_time ?: $sTime;
        $oApplyFather->save();

        // 检查其他产品的反馈效果 => 修改 申请表状态
        $this->updateApplyStatus($iApplyId);

        return true;
    }

    /**
     * @return bool
     */
    public function batchSaveAccess() {
        $iApplyId = (int)request()->post('apply_id', 0);
        $aApplyFatherId = (array)request()->post('apply_father_id_list', []);
        $iAccessAction = (int)request()->post('access_action', 0);
        $iNotAccessReason = (int)request()->post('not_access_reason', 0);
        $sAccessRemark = (string)request()->post('access_remark', '');
        $sTime = date("Y-m-d H:i:s");

        if (!$iApplyId || !$aApplyFatherId || !$iAccessAction) {
            throw new Exception('请求参数[申请表ID(apply_id), 测试产品表(apply_father_id_list), 接入结果(access_action)]不能为空');
        }

        $aApplyFatherList = ApplyFather::aGetDataByCond([['id', 'in', $aApplyFatherId]]);
        // 保存 接入信息
        $aUpdate = [];
        foreach ($aApplyFatherList as $aApplyFather) {
            $iTestStatus = $aApplyFather['test_status'];
            if ($iTestStatus < PreTestManage::STATUS_ACCESS_DONE
                && in_array($iTestStatus, PreTestManage::CAN_SAVE_ACCESS_STATUS)
                && PreTestManage::ACCESS_ACTION_DEFAULT != $iAccessAction
            ) {
                $iTestStatus = PreTestManage::ACCESS_ACTION_STATUS_MAP[$iAccessAction] ?? $iTestStatus;
            }

            $aUpdate[] = [
                'id' => $aApplyFather['id'],
                'test_status' => $iTestStatus,
                'access_action' => $iAccessAction,
                'not_access_reason' => $iAccessAction == PreTestManage::ACCESS_ACTION_UNABLE ? $iNotAccessReason : PreTestManage::UNABLE_REASON_DEFAULT,
                'access_remark' => $sAccessRemark,
                'access_time' => $aApplyFather['access_time'] ?: $sTime,
                'updated_at' => $sTime,
            ];
        }
        if ($aUpdate) {
            ApplyFather::batchUpdateData($aUpdate, 'id');
        }

        // 检查其他产品的反馈效果 => 修改 申请表状态
        $this->updateApplyStatus($iApplyId);

        return true;
    }

    /**
     * @return bool
     */
    public function bindCustomer() {
        $iApplyId = (int)request()->post('apply_id', 0);
        $sCustomerId = trim(request()->post('customer_id', ''));
        if (!$iApplyId || '' == $sCustomerId) {
            throw new Exception('请求参数[申请表ID(apply_id), 绑定客户ID(customer_id)]不能为空');
        }
        // 用 特殊值0来清空绑定
        if ('0' == $sCustomerId) {
            $sCustomerId = '';
        }
        if ($sCustomerId) {
            $oCustomer = Customer::getCustomerInfo($sCustomerId);
            if (is_null($oCustomer)) {
                throw new Exception('客户信息(customer_id)不存在');
            }
        }

        $sNow = date("Y-m-d H:i:s");
        $oApply = ApplyCustomer::find($iApplyId);
        if (is_null($oApply)) {
            return false;
        }
        $oApply->customer_id = $sCustomerId;
        $oApply->updated_at = date("Y-m-d H:i:s");
        if (!$sCustomerId) {
            $aApplyFatherList = ApplyFather::aGetListByApplyIdList([$iApplyId]);
            $aStatusList = [];
            // 回退产品维度进度
            $aUpdate = [];
            foreach ($aApplyFatherList as $aItem) {
                $iStatus = PreTestManage::STATUS_WAITING;
                if ($aItem['access_action']) {
                    $iStatus = $aItem['access_action'] == PreTestManage::ACCESS_ACTION_ABLE ? PreTestManage::STATUS_ACCESS_ABLE : PreTestManage::STATUS_ACCESS_UNABLE;
                } else if ($aItem['test_result']) {
                    $iStatus = PreTestManage::STATUS_FEEDBACK;
                } else if ($aItem['return_time']) {
                    $iStatus = PreTestManage::STATUS_TEST_DONE;
                } else if ($aItem['start_time']) {
                    $iStatus = PreTestManage::STATUS_TESTING;
                }
                $aStatusList[] = $iStatus;
                $aUpdate[] = [
                    'id' => $aItem['id'],
                    'is_open' => PreTestManage::IS_NO,
                    'test_status' => $iStatus,
                ];
            }
            if ($aUpdate) {
                ApplyFather::batchUpdateData($aUpdate, 'id');
            }
            // 重新计算申请维度
            $oApply->status = $this->calculateStatusPriority($aStatusList);
            $oApply->save();

            return true;
        }

        $oApply->is_call = PreTestManage::IS_NO;
        $oApply->status = PreTestManage::STATUS_ACCESS_DONE;
        $oApply->bind_time = $sNow;
        $oApply->save();

        $aUpdateData = [
            'bind_time' => $sNow,
            'updated_at' => $sNow,
        ];
        ApplyFather::iUpdateByApplyId($iApplyId, $aUpdateData);

        return true;
    }

    /**
     * @return bool
     */
    public function processStatus() {
        $iApplyId = (int)request()->post('apply_id', 0);
        $aApplyFatherId = (array)request()->post('apply_father_id_list', []);
        $iTestStatus = (int)request()->post('test_status', 0);

        if (!$iApplyId || empty($aApplyFatherId)) {
            throw new Exception('请求参数[申请表ID(apply_id), 测试产品表ID(apply_father_id_list)]不能为空');
        }
        $oApply = ApplyCustomer::find($iApplyId);
        if (is_null($oApply)) {
            return false;
        }

        foreach ($aApplyFatherId as $iApplyFatherId) {
            $this->modifyStatus($iApplyId, $iApplyFatherId, $iTestStatus, $oApply);
        }

        return true;
    }

    /**
     * @param $iApplyId
     * @param $iApplyFatherId
     * @param $iTestStatus
     * @param ApplyCustomer $oApply
     * @return bool
     */
    private function modifyStatus($iApplyId, $iApplyFatherId, $iTestStatus, ApplyCustomer $oApply){
        $sTime = date("Y-m-d H:i:s");
        $oApplyFather = ApplyFather::find($iApplyFatherId);
        if ($oApplyFather->test_status >= $iTestStatus) {
            return true;
        }
        // 执行操作的状态限制
        if (!in_array($oApplyFather->test_status, PreTestManage::CAN_MODIFY_STATUS)) {
            return false;
        }
        //  可变更的状态限制
        if (!in_array($iTestStatus, PreTestManage::CAN_MODIFY_STATUS)) {
            return false;
        }
        // 保存
        $oApplyFather->test_status = $iTestStatus;
        $oApplyFather->updated_at = $sTime;
        switch ($oApplyFather->test_status) {
            case PreTestManage::STATUS_TESTING:
                $oApplyFather->start_time = $sTime;
                break;
            case PreTestManage::STATUS_TEST_ABORT:
                $oApplyFather->is_schedule = PreTestManage::IS_DONE;
                break;
            case PreTestManage::STATUS_TEST_DONE:
                // 如果[售前]周五才完成测试,则本周不标记[销售]需要打卡
                $isFriday = 5 == date('w', time());
                $iScheduleLimit = in_array($oApply->company_type, PreTestManage::LONG_SCHEDULE_LIMIT) ? 4 : 2;
                $oApplyFather->is_schedule = $isFriday ? PreTestManage::IS_YES : PreTestManage::IS_NO;
                $oApplyFather->schedule_time = date('Y-m-d H:i:s', strtotime('+' . $iScheduleLimit . ' weeks'));
                $oApplyFather->start_time = $oApplyFather->start_time ?: $sTime;
                $oApplyFather->return_time = $sTime;
                break;
            default:
                break;
        }
        $oApplyFather->save();

        // 检查其他产品的反馈效果 => 修改 申请表状态
        $this->updateApplyStatus($iApplyId);

        return true;
    }

    /**
     * @return array[]
     */
    public function productOption() {
        $iApplyId = (int)request()->post('apply_id', 0);
        if (!$iApplyId) {
            throw new Exception('请求参数[申请ID(apply_id)]不能为空');
        }
        $aApplyProductList = ApplyProduct::aGetListByApplyIdList([$iApplyId]);
        $aChosenProductData = [];
        foreach ($aApplyProductList as $aItem) {
            $aItem['father_id'] > 0 and $aChosenProductData[] = [$aItem['father_id'] ?: $aItem['product_id'], $aItem['product_id']];
        }

        $aFatherList = Product::getFatherProducts(['product_id', 'product_name', 'father_id', 'status']);
        $aProductList = Product::getAllSubProduct(['product_id', 'product_name', 'father_id', 'status']);

        $aUsedProductId = [];
        $aBusinessProductList = BusinessProduct::aGetSystemDataByCond();
        foreach ($aBusinessProductList as $aItem) {
            $aUsedProductId[] = $aItem['product_id'];
            $aProductId = explode(',', $aItem['product_list']);
            $aUsedProductId = array_merge($aUsedProductId, $aProductId);
        }
        $aUsedProductId = array_values(array_unique($aUsedProductId));

        $aProductOption = [];
        foreach ($aProductList as $aProduct) {
            if (0 == $aProduct['status'] && !in_array($aProduct['product_id'], $aUsedProductId)) {
                continue;
            }
            $aProductOption[$aProduct['father_id']][] = [
                'value' => $aProduct['product_id'],
                'label' => $aProduct['product_name'],
            ];
        }
        $aOption = [];
        foreach ($aFatherList as $aFather) {
            if (0 == $aFather['status']) {
                continue;
            }
            $aOption[] = [
                'value' => $aFather['product_id'],
                'label' => $aFather['product_name'],
                'children' => $aProductOption[$aFather['product_id']] ?? [[
                        'value' => $aFather['product_id'],
                        'label' => $aFather['product_name'],
                    ]],
            ];
        }

        return [
            'bind_product_option' => $aOption,
            'chosen_product_data' => $aChosenProductData,
        ];
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function bindProduct() {
        $iApplyId = (int)request()->post('apply_id', 0);
        $iApplyFatherId = (int)request()->post('apply_father_id', 0);
        $aNowProductList = (array)request()->post('product_list', []);

        return $this->bindProductFunc($iApplyId, $iApplyFatherId, $aNowProductList);
    }

    /**
     * @param $iApplyId
     * @param $iApplyFatherId
     * @param $aNowProductList
     * @return true
     * @throws Exception
     */
    public function bindProductFunc($iApplyId = 0, $iApplyFatherId = 0, $aNowProductList = []) {
        if (!$iApplyId || !$iApplyFatherId || empty($aNowProductList)) {
            throw new Exception('请求参数[申请ID(apply_id), 测试产品表ID(apply_product_id), 产品列表(product_list)]不能为空');
        }

        // 申请信息
        $oApply = ApplyCustomer::find($iApplyId);
        if (is_null($oApply)) {
            throw new Exception(sprintf('申请信息查询为空, 查询ID(%s)', $iApplyId));
        }
        $oApplyFather = ApplyFather::find($iApplyFatherId);
        if (empty($oApplyFather)) {
            throw new Exception('测试产品信息未查得');
        }

        // 所有 已绑定主产品
        $aCond = [
            ['apply_id', '=', $iApplyId],
            ['father_id', '>', 0],
        ];
        $aExistFatherDataList = ApplyFather::aGetDataByCond($aCond, ['id', 'father_id', 'product_list', 'is_schedule']);
        $aExistFatherMap = [];
        $aCanDeleteFatherMap = [];
        foreach ($aExistFatherDataList as $aItem) {
            $aExistFatherMap[$aItem['father_id']] = $aItem['id'];
            if ($aItem['product_list'] && $aItem['is_schedule'] == PreTestManage::IS_BIND) {
                $aCanDeleteFatherMap[$aItem['father_id']] = $aItem['id'];
            }
        }

        // 所有 已绑定子产品
        $aCond = [
            ['apply_id', '=', $iApplyId],
            ['father_id', '>', 0],
            ['product_id', '>', 0],
        ];
        $aApplyProductList = ApplyProduct::aGetDataByCond($aCond, ['*'], ['deleted_at']);
        $aExistProductMap = [];
        $aExistApplyFatherMap = [];
        $aExistProductId = [];
        foreach ($aApplyProductList as $aItem) {
            $aExistProductMap[$aItem['product_id']] = $aItem['id'];
            $aExistApplyFatherMap[$aItem['product_id']] = $aItem['apply_father_id'];
            $aExistProductId[] = $aItem['product_id'];
        }

        $aNowProductId = [];
        foreach ($aNowProductList as $aItem) {
            list($iFatherId, $iProductId) = $aItem;
            $aNowProductId[] = $iProductId;
        }
        // 需要新增测试产品
        $aNewProductId = array_diff($aNowProductId, $aExistProductId);
        // 需要删除测试产品
        $aDeleteProductId = array_diff($aExistProductId, $aNowProductId);
        if (empty($aNewProductId) && empty($aDeleteProductId)) {
            return true;
        }
        // 处理新增产品信息
        if (!empty($aNewProductId)) {
            $aAddApplyFatherMap = $this->bindNewProduct($aNewProductId, $aExistFatherMap, $oApplyFather, $oApply);
            $aExistApplyFatherMap = $aExistApplyFatherMap + $aAddApplyFatherMap;
        }
        // 处理删除产品
        if (!empty($aDeleteProductId)) {
            $this->moveProduct($aDeleteProductId, $aExistProductMap, $aExistApplyFatherMap, $aCanDeleteFatherMap);
        }
        // 重新计算 名称,调用量,开通信息
        $this->restoreApplyFather($iApplyId);

        if ($oApplyFather->father_id <= 0) {
            $oApplyFather->is_delete = PreTestManage::IS_YES;
            $oApplyFather->save();
        }

        return true;
    }

    /**
     * @param $aNewProductId
     * @param $aExistFatherMap
     * @param ApplyFather|null $oApplyFather
     * @param ApplyCustomer|null $oApply
     * @return array
     */
    private function bindNewProduct($aNewProductId = [], $aExistFatherMap = [], ApplyFather $oApplyFather = null, ApplyCustomer $oApply = null){
        $sTime = date("Y-m-d H:i:s");

        $aProductData = Product::getProductListByProductIds($aNewProductId, ['product_id', 'product_name', 'father_id']);
        // 主子产品关系
        $aProductTree = [];
        $aFatherId = [];
        $aAddProduct = [];
        foreach ($aProductData as $aData) {
            $iFatherId = $aData['father_id'] ?: $aData['product_id'];

            if (!isset($aExistFatherMap[$iFatherId])) {
                $aFatherId[] = $iFatherId;
                $aProductTree[$iFatherId][] = $aData['product_id'];
            } else {
                $aAddProduct[] = [
                    'father_id' => $iFatherId,
                    'product_id' => $aData['product_id'],
                    'apply_father_id' => $aExistFatherMap[$iFatherId],
                ];
            }
        }

        // 主产品信息
        $aFatherData = Product::getProductListByProductIds($aFatherId, ['product_id', 'product_name', 'father_id']);
        $aFatherNameMap = array_column($aFatherData, 'product_name', 'product_id');

        $aObjectList = [];
        if ($oApplyFather->have_objective) {
            $aObjectList = ApplyFatherObjective::aGetDataByApplyFatherId($oApplyFather->id);
        }
        $iApplyId = $oApply->id;

        $aProductInsertList = [];
        $aObjectInsertList = [];
        foreach ($aProductTree as $iFatherId => $aProduct) {
            $sFatherNameMap = $aFatherNameMap[$iFatherId] ?? '';
            $sName = implode(',', $aProduct);
            $aInsertFatherData = [
                'apply_id' => $iApplyId,
                'father_id' => $iFatherId,
                'apply_father_name' => $sFatherNameMap. '【' . $sName . '】' ?? '',
                'product_list' => $sName,
                'test_status' => PreTestManage::STATUS_ACCESS_ABLE,
                'test_result' => $oApplyFather->test_result,
                'test_result_remark' => $oApplyFather->test_result_remark,
                'source' => $oApplyFather->source,
                'source_name' => $oApplyFather->source_name,
                'can_communicate' => $oApplyFather->can_communicate,
                'salesman' => $oApplyFather->salesman,
                'access_action' => PreTestManage::ACCESS_ACTION_ABLE,
                'not_access_reason' => PreTestManage::UNABLE_REASON_DEFAULT,
                'access_remark' => $oApplyFather->access_remark,
                'schedule_time' => $sTime,
                'have_objective' => $oApplyFather->have_objective,
                'is_schedule' => PreTestManage::IS_BIND,
                'is_call' => PreTestManage::IS_NO,
                'is_open' => PreTestManage::IS_NO,
                'date_total_90d' => 0,
                'number_total_90d' => 0,
                'start_time' => $oApplyFather->start_time,
                'return_time' => $oApplyFather->return_time,
                'feedback_time' => $oApplyFather->feedback_time,
                'bind_time' => $oApply->bind_time,
                'apply_time' => $oApply->apply_time,
                'created_at' => $sTime,
            ];
            $iTheApplyFatherId = ApplyFather::insertGetId($aInsertFatherData);

            foreach ($aObjectList as $aObject) {
                $aObjectInsertList[] = [
                    'apply_id' => $iApplyId,
                    'apply_father_id' => $iTheApplyFatherId,
                    'father_id' => $iFatherId,
                    'objective_id' => $aObject['objective_id'],
                    'result_value' => $aObject['result_value'],
                    'remark' => $aObject['remark'],
                    'created_at' => $sTime,
                ];
            }

            foreach ($aProduct as $iProductId) {
                $aProductInsertList[] = [
                    'apply_id' => $iApplyId,
                    'apply_father_id' => $iTheApplyFatherId,
                    'product_id' => $iProductId,
                    'father_id' => $iFatherId,
                    'apply_father_name' => 'PC端新增',
                    'apply_product_name' => '',
                    'test_status' => PreTestManage::STATUS_ACCESS_ABLE,
                    'test_result' => PreTestManage::TEST_RESULT_DEFAULT,
                    'test_result_remark' => '',
                    'access_action' => PreTestManage::ACCESS_ACTION_ABLE,
                    'not_access_reason' => PreTestManage::UNABLE_REASON_DEFAULT,
                    'access_remark' => '',
                    'is_schedule' => PreTestManage::IS_BIND,
                    'is_call' => PreTestManage::IS_NO,
                    'is_open' => PreTestManage::IS_NO,
                    'date_total_90d' => 0,
                    'number_total_90d' => 0,
                    'start_time' => $oApplyFather->start_time,
                    'return_time' => $oApplyFather->return_time,
                    'bind_time' => $oApply->bind_time,
                    'apply_time' => $oApply->apply_time,
                    'created_at' => $sTime,
                ];
            }
        }
        if ($aAddProduct) {
            foreach ($aAddProduct as $aItem) {
                $aProductInsertList[] = [
                    'apply_id' => $iApplyId,
                    'apply_father_id' => $aItem['apply_father_id'],
                    'product_id' => $aItem['product_id'],
                    'father_id' => $aItem['father_id'],
                    'apply_father_name' => 'PC端新增',
                    'apply_product_name' => '',
                    'test_status' => PreTestManage::STATUS_ACCESS_ABLE,
                    'test_result' => PreTestManage::TEST_RESULT_DEFAULT,
                    'test_result_remark' => '',
                    'access_action' => PreTestManage::ACCESS_ACTION_ABLE,
                    'not_access_reason' => PreTestManage::UNABLE_REASON_DEFAULT,
                    'access_remark' => '',
                    'is_schedule' => PreTestManage::IS_BIND,
                    'is_call' => PreTestManage::IS_NO,
                    'is_open' => PreTestManage::IS_NO,
                    'date_total_90d' => 0,
                    'number_total_90d' => 0,
                    'start_time' => $oApplyFather->start_time,
                    'return_time' => $oApplyFather->return_time,
                    'bind_time' => $oApply->bind_time,
                    'apply_time' => $oApply->apply_time,
                    'created_at' => $sTime,
                ];
            }
        }

        if ($aProductInsertList) {
            ApplyProduct::insert($aProductInsertList);
        }
        if ($aObjectInsertList) {
            ApplyFatherObjective::insert($aObjectInsertList);
        }

        return array_column($aAddProduct, 'apply_father_id', 'product_id');
    }

    /**
     * @param $aDeleteProductId
     * @param $aExistProductMap
     * @param $aExistApplyFatherMap
     * @param $aExistFatherMap
     * @return true
     */
    private function moveProduct($aDeleteProductId = [], $aExistProductMap = [], $aExistApplyFatherMap = [], $aExistFatherMap = []){
        $aDeleteApplyProductId = [];
        foreach ($aDeleteProductId as $iProductId) {
            $aDeleteApplyProductId[] = $aExistProductMap[$iProductId];
            unset($aExistApplyFatherMap[$iProductId]);
        }

        // 删除 product维度
        if ($aDeleteApplyProductId) {
            ApplyProduct::iDeleteByIdList($aDeleteApplyProductId);
        }

        $aAllApplyFatherId = array_values(array_unique($aExistFatherMap));
        $aLeaveApplyFatherId = array_values(array_unique($aExistApplyFatherMap));
        $aDeleteApplyFatherId = array_diff($aAllApplyFatherId, $aLeaveApplyFatherId);
        // 删除 father维度
        if ($aDeleteApplyFatherId) {
            ApplyFather::iDeleteByIdList($aDeleteApplyFatherId);
        }

        return true;
    }

    /**
     * @param $iApplyId
     * @return true
     */
    private function restoreApplyFather($iApplyId = 0){
        $aCond = [
            ['apply_id', '=', $iApplyId],
            ['father_id', '>', 0],
        ];
        $aApplyFatherList = ApplyFather::aGetDataByCond($aCond);
        $aCond = [
            ['apply_id', '=', $iApplyId],
            ['father_id', '>', 0],
            ['product_id', '>', 0],
        ];
        $aApplyProductList = ApplyProduct::aGetDataByCond($aCond, ['*'], ['deleted_at']);
        if (empty($aApplyProductList)) {
            $aUpdate = [
                'product_list' => '',
                'number_total' => 0,
                'number_total_90d' => 0,
                'is_open' => 0,
            ];
            ApplyFather::iUpdateByApplyId($iApplyId, $aUpdate);

            return true;
        }

        $aProductMap = [];
        $aNumberMap = [];
        $aNumber90dMap = [];
        $aIsOpenMap = [];
        foreach ($aApplyProductList as $aItem) {
            $aProductMap[$aItem['father_id']][] = $aItem['product_id'];
            $aNumberMap[$aItem['father_id']][] = $aItem['number_total'];
            $aNumber90dMap[$aItem['father_id']][] = $aItem['number_total_90d'];
            $aIsOpenMap[$aItem['father_id']][] = $aItem['is_open'];
        }
        // 主产品信息
        $aFatherId = array_column($aApplyFatherList, 'father_id');
        $aFatherData = Product::getProductListByProductIds($aFatherId, ['product_id', 'product_name', 'father_id']);
        $aFatherNameMap = array_column($aFatherData, 'product_name', 'product_id');

        $aUpdate = [];
        foreach ($aApplyFatherList as $aItem) {
            $iFatherId = $aItem['father_id'];

            $sFatherNameMap = $aFatherNameMap[$iFatherId] ?? '';
            $aProductId = $aProductMap[$iFatherId] ?? [];
            if (empty($aProductId)) {
                continue;
            }
            $sName = implode(',', $aProductId);
            $aNumberId = $aNumberMap[$iFatherId] ?? [];
            $aNumber90dId = $aNumber90dMap[$iFatherId] ?? [];
            $aIsOpen = $aIsOpenMap[$iFatherId] ?? [];

            $aUpdate[] = [
                'id' => $aItem['id'],
//                'apply_father_name' => $sFatherNameMap . '【' . $sName . '】' ?? '',
                'apply_father_name' => $sFatherNameMap,
                'product_list' => $sName,
                'number_total' => array_sum($aNumberId),
                'number_total_90d' => array_sum($aNumber90dId),
                'is_open' => $aIsOpen ? max($aIsOpen) : 0,
            ];
        }
        if ($aUpdate) {
            ApplyFather::batchUpdateData($aUpdate, 'id');
        }

        return true;
    }

    /**
     * @return true
     * @throws Exception
     */
    public function deleteProduct() {
        $iApplyId = (int)request()->post('apply_id', 0);
        $iApplyFatherId = (int)request()->post('apply_father_id', 0);
        if (!$iApplyId || !$iApplyFatherId) {
            throw new Exception('请求参数[申请ID(apply_id), 测试产品表ID(apply_product_id)]不能为空');
        }

        // 申请信息
        $oApply = ApplyCustomer::find($iApplyId);
        if (is_null($oApply)) {
            throw new Exception(sprintf('申请信息查询为空, 查询ID(%s)', $iApplyId));
        }
        $oApplyFather = ApplyFather::find($iApplyFatherId);
        if (empty($oApplyFather)) {
            throw new Exception('测试产品信息未查得');
        }
        $oApplyFather->is_delete = PreTestManage::IS_YES;
        $oApplyFather->deleted_at = date('Y-m-d H:i:s');
        $oApplyFather->save();

        ApplyProduct::iDeleteByApplyFatherId($iApplyFatherId);

        return true;
    }

    /**
     * @return array
     */
    public function applyInfo(){
        $iApplyId = (int)request()->post('apply_id', 0);
        if (!$iApplyId) {
            throw new Exception("请求参数[申请ID({$iApplyId})]不能为空");
        }

        $aApply = ApplyCustomer::aGetByApplyId($iApplyId);
        if (empty($aApply)) {
            throw new Exception("无法获取[申请ID({$iApplyId})]详细信息");
        }

        $aApply['status_text'] = PreTestManage::STATUS_TEXT_MAP[$aApply['status']] ?? '';
        $aApply['company_type_text'] = PreTestManage::COMPANY_TYPE_TEXT_MAP[$aApply['company_type']] ?? '';
        $aApply['is_call_text'] = PreTestManage::WHETHER_TEXT_MAP[$aApply['is_call']] ?? '';
        $aApply['is_top_text'] = PreTestManage::WHETHER_TEXT_MAP[$aApply['is_top']] ?? '';
        $aApply['is_pay_text'] = PreTestManage::WHETHER_TEXT_MAP[$aApply['is_pay']] ?? '';
        $aApply['sample_source_text'] = PreTestManage::SAMPLE_SOURCE_TEXT_MAP[$aApply['sample_source']] ?? '';
        $aApply['apply_time'] = substr($aApply['apply_time'], 0, 10);
        $aApply['start_time'] = substr($aApply['start_time'], 0, 10);
        $aApply['return_time'] = substr($aApply['return_time'], 0, 10);
        $aApply['extend_text_list'] = $this->transExtendToText($aApply);

        return $aApply;
    }

    /**
     * @param $aApply
     * @return array
     */
    private function transExtendToText($aApply){
        $aExtendList = json_decode($aApply['extend_list'], true);
        $aProductInfo = json_decode($aApply['product_info'], true);

        $aRet = [];
        foreach ($aExtendList as $sKey => $mValue) {
            $aRet[PreTestManage::FORM_NAME_MAP[$sKey] ?? $sKey] = $mValue;
        }
        $aProductFieldList = PreTestManage::PRODUCT_LIST;
        $aProductFieldList[] = 'father_list';

        foreach ($aProductInfo as $sKey => $mValue) {
            if (in_array($sKey, $aProductFieldList)) {
                $mValue = json_decode($mValue, true);
            }
            $aRet[PreTestManage::FORM_NAME_MAP[$sKey] ?? $sKey] = $mValue;
        }

        return $aRet;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function applyProductInfo() {
        $iApplyId = (int)request()->post('apply_id', 0);
        if (!$iApplyId) {
            throw new Exception('请求参数[申请表ID(apply_id)]不能为空');
        }

        $aApplyFatherList = ApplyFather::aGetListByApplyIdList([$iApplyId]);
        $aFatherListMap = $this->aFormatFatherList($aApplyFatherList);
        $aFatherList = $aFatherListMap[$iApplyId] ?? [];

        list($aObjectiveOption, $aObjectiveDesc, $aObjectiveMap) = CommonEnumModel::aGetPreTestOptionAndDesc();
        return [
            'objective_text_map' => $aObjectiveMap,
            'objective_desc' => implode(", ", $aObjectiveDesc),
            'list' => $aFatherList,
        ];
    }
}