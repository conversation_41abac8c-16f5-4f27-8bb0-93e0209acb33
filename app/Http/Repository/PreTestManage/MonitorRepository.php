<?php

namespace App\Http\Repository\PreTestManage;

use App\Define\PreTestManage;
USE App\Define\PreTestMonitor;
use App\Http\Repository\DeptRepository;
use App\Providers\Approval\MonitorApprovalHandle;
use App\Providers\Approval\Strategy\StrategyApi;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Customer\CustomerGroup;
use App\Models\Dept;
use App\Models\PreTestManage\MonitorApproval;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\PreTestManage\MonitorWeekStatistics;
use App\Models\SystemUser;
use Exception;

/**
 * 监控
 * @package App\Http\Repository\PreTestManage
 * @class MonitorRepository
 */
class MonitorRepository extends PreTestManageRepository
{
    /**
     * @return array
     */
    public function monitorCustomerList() {
        $aCond = $this->initListCond();
        if (false === $aCond) {
            return [
                'total' => 0,
                'list' => [],
                'week_title' => [],
            ];
        }
        list($iOffset, $iPerPage) = $this->initPage();
        $iTotal = MonitorCustomerProduct::iGetCountByCond($aCond);
        if (!$iTotal) {
            return [
                'total' => 0,
                'list' => [],
                'week_title' => [],
            ];
        }
        $aDataList = MonitorCustomerProduct::aGetDataByCond($aCond, ['*'], $iOffset, $iPerPage);
        list($aList, $aWeekDataTitle) = $this->formatDataList($aDataList);

        return [
            'total' => $iTotal,
            'list' => $aList,
            'week_title' => $aWeekDataTitle,
        ];
    }

    /**
     * @return array | bool
     */
    private function initListCond() {
        $iActivateTimeStart = (int)request()->post('activate_time_start', 0);
        $iActivateTimeEnd = (int)request()->post('activate_time_end', 0);
        $sCustomerId = trim(request()->post('customer_id', ''));
        $sCustomerName = trim(request()->post('customer_name', ''));
        $sCustomerLevel = trim(request()->post('customer_level', ''));
        $iCustomerStatus = (int)request()->post('customer_status', 0);
        $sGroupId = trim(request()->post('group_id', ''));
        $sGroupName = trim(request()->post('group_name', ''));
        $iFatherId = (int)request()->post('father_id', 0);
        $iProductId = (int)request()->post('product_id', 0);
        $iExpectOnlineTime = (int)request()->post('expect_online_time', 0);
        $iDockingStatus = (int)request()->post('docking_status', 0);
        $iActivateStatus = (int)request()->post('activate_status', 0);
        $sDept = trim(request()->post('dept', ''));
        $sSalesman = trim(request()->post('salesman', ''));
        $aCond = [];
        $aCustomerCond = [];
        if ($sCustomerName) {
            $aCustomerCond[] = ['name', 'like', '%' . $sCustomerName . '%'];
        }
        if ($sCustomerLevel) {
            if (strlen($sCustomerLevel) > 1) {
                $aCustomerCond[] = ['level_scale_income', '=', strtoupper($sCustomerLevel)];
            } else if (is_numeric($sCustomerLevel)) {
                $aCustomerCond[] = ['level_income', '=', $sCustomerLevel];
            } else {
                $aCustomerCond[] = ['level_scale', '=', strtoupper($sCustomerLevel)];
            }
        }
        if ($iCustomerStatus) {
            $iCustomerStatus--;
            $aCustomerCond[] = ['status', '=', $iCustomerStatus];
        }
        if ($aCustomerCond) {
            $aCustomerList = Customer::aGetListByCond($aCustomerCond, ['customer_id','name','level']);
            $aCustomerId = array_column($aCustomerList, 'customer_id');
            if (empty($aCustomerId)) {
                return false;
            }
            $aCond[] = ['customer_id', 'in', $aCustomerId];
        }
        if ($sDept) {
            $aDeptSalesMan = $this->getDeptSalesmanList($sDept, false);
            $aCond[] = ['salesman', 'in', $aDeptSalesMan];
        }
        if ($sSalesman) {
            $aCond[] = ['salesman', '=', $sSalesman];
        }
        if ($iActivateTimeStart) {
            $aCond[] = ['activate_time', '>=', $iActivateTimeStart];
        }
        if ($iActivateTimeEnd) {
            $iActivateTimeEnd += 86400;
            $aCond[] = ['activate_time', '<=', $iActivateTimeEnd];
        }
        if ($sCustomerId) {
            $aCond[] = ['customer_id', '=', $sCustomerId];
        }
        if ($sGroupId) {
            $aCond[] = ['group_id', '=', $sGroupId];
        }
        if ($sGroupName) {
            $aGroupList = CustomerGroup::getListByLikeName($sGroupName);
            $aGroupId = array_column($aGroupList, 'group_id');
            if (empty($aGroupId)) {
                return false;
            }
            $aCond[] = ['group_id', 'in', $aGroupId];
        }
        if ($iFatherId) {
            $aCond[] = ['father_id', '=', $iFatherId];
        }
        if ($iProductId) {
            $aCond[] = ['product_id', '=', $iProductId];
        }
        if ($iExpectOnlineTime) {
            $aCond[] = ['expect_online_time', '>=', $iExpectOnlineTime];
            $aCond[] = ['expect_online_time', '<', ($iExpectOnlineTime + 86400)];
        }
        if ($iDockingStatus) {
            $aCond[] = ['docking_status', '=', $iDockingStatus];
        }
        if ($iActivateStatus) {
            $iActivateStatus--;
            $aCond[] = ['activate_status', '=', $iActivateStatus];
        }

        return $aCond;
    }

    /**
     * @return array
     */
    private function initPage() {
        $iPage = (int)request()->post('page', 1);
        if ($iPage < 1) {
            $iPage = 1;
        }
        $iPerPage = (int)request()->post('per_page', 200);
        $iOffset = ($iPage - 1) * $iPerPage;

        return [$iOffset, $iPerPage];
    }

    /**
     * @param $aMonitorId
     * @return array
     */
    private function getWeekData($aMonitorId = []) {
        // 数据归属所在周ID向右偏移3天
        $iNow = time();
        // $iNow = strtotime('2023-07-01'); // todo
        $iLaterDay = strtotime('+3 days', $iNow); // 3天后日期
        $iThisThursday = strtotime("thursday this week", $iLaterDay); // 3天后日期 所在周 周四
        $iStartTime = strtotime('-5 weeks', $iThisThursday); // 5周前 周四
        $iEndTime = strtotime('-1 weeks', $iThisThursday); // 1周前 周四

        $aWeekMap = [];
        $aWeekDataTitle = [];
        for ($i = 0; $i < 5; $i++) {
            $aWeekMap['w' . $i] = date('YW', strtotime('+' . $i . ' weeks', $iStartTime));
            $aWeekDataTitle['w' . $i . '_title'] = 'W' . date('W', strtotime('+' . $i . ' weeks', $iStartTime));
        }
        $iStartWeek = date('YW', strtotime('+3 days', $iStartTime)); // week_id 向右偏移3天
        $iEndWeek = date('YW', strtotime('+3 days', $iEndTime)); // week_id 向右偏移3天

        $aStatDataList = MonitorWeekStatistics::aGetListByMonitorId($aMonitorId, $iStartWeek, $iEndWeek);
        $aDataMap = [];
        foreach ($aStatDataList as $aData) {
            $aDataMap[$aData['monitor_id']][$aData['week_id']] = [
                'number' => $aData['number'],
                'income' => $aData['income'],
            ];
        }

        return [$aWeekMap, $aDataMap, $aWeekDataTitle];
    }

    /**
     * @param $aDataList
     * @return array
     */
    private function formatDataList($aDataList) {
        $aRet = [];
        $aMonitorId = array_column($aDataList, 'id');
        list($aWeekMap, $aDataMap, $aWeekDataTitle) = $this->getWeekData($aMonitorId);
        list($aCustomerMap, $aGroupMap, $aProductMap, $aSalesmanMap) = $this->getDataMap($aDataList);
        $aCustomerStatusMap = [
            1 => '可用',
            0 => '禁用',
        ];

        foreach ($aDataList as $aData) {
            $aWeekDataList = $aDataMap[$aData['id']] ?? [];

            $aWeekFieldData = [];
            foreach ($aWeekMap as $f => $iWeekId) {
                $aWeekData = $aWeekDataList[$iWeekId] ?? ['number' => 0, 'income' => 0];
                $aWeekFieldData[$f. '_title'] = 'W' . substr($iWeekId, 4, 2);;
                $aWeekFieldData[$f. '_number'] = $aWeekData['number'] ? convertToChineseNumeration($aWeekData['number']) : 0;
                $aWeekFieldData[$f. '_income'] = $aWeekData['income'] ? convertToChineseNumeration($aWeekData['income']) : 0;
            }
            $sCustomerLevel = $aCustomerMap[$aData['customer_id']]['level_scale_income'] ?? '';
            $iCustomerStatus = $aCustomerMap[$aData['customer_id']]['status'] ?? -1;

            $aItem = [
                'monitor_id' => $aData['id'],
                'customer_id' => $aData['customer_id'],
                'customer_name' => $aCustomerMap[$aData['customer_id']]['name'] ?? '',
                'customer_level' => $sCustomerLevel,
                'customer_status' => $aCustomerStatusMap[$iCustomerStatus] ?? '-',
                'salesman' => $aSalesmanMap[$aData['salesman']] ?? '',
                'group_id' => $aData['group_id'],
                'group_name' => $aGroupMap[$aData['group_id']]['group_name'] ?? '',
                'father_id' => $aData['father_id'],
                'father_name' => $aProductMap[$aData['father_id']] ?? '',
                'product_id' => $aData['product_id'],
                'product_name' => $aProductMap[$aData['product_id']] ?? '',
                'docking_status' => $aData['docking_status'],
                'docking_status_text' => MonitorCustomerProduct::DOCKING_STATUS_TEXT_MAP[$aData['docking_status']] ?? MonitorCustomerProduct::DOCKING_STATUS_TEXT_MAP[MonitorCustomerProduct::DOCKING_STATUS_NORMAL],
                'activate_time' => $aData['activate_time'] ? date('Y-m-d H:i:s', $aData['activate_time']) : '',
                'activate_end_time' => $aData['activate_end_time'] ? date('Y-m-d H:i:s', $aData['activate_end_time']) : '',
                'debug_time' => $aData['debug_time'] ? date('Y-m-d H:i:s', $aData['debug_time']) : '',
                'fee_time' => $aData['fee_time'] ? date('Y-m-d H:i:s', $aData['fee_time']) : '',
                'call_time' => $aData['call_time'] ? date('Y-m-d H:i:s', $aData['call_time']) : '',
                'time_diff' => $aData['time_diff'],
                'expect_online_time' => $aData['expect_online_time'] ? date('Y-m-d', $aData['expect_online_time']) : '',
                'grayscale_start_time' => $aData['grayscale_start_time'] ? date('Y-m-d', $aData['grayscale_start_time']) : '',
                'grayscale_end_time' => $aData['grayscale_end_time'] ? date('Y-m-d', $aData['grayscale_end_time']) : '',
                'call_days' => $aData['call_days'],
                'stat_call_days' => MonitorCustomerProduct::iGetStatCllDays($aData['grayscale_end_time'], $aData['fee_time']),
                'reason' => $aData['reason'],
                'activate_status' => $aData['activate_status'],
                'activate_status_text' => $aData['activate_status'] ? '可用' : '禁用',
            ];

            $aRet[] = array_merge($aItem, $aWeekFieldData);
        }

        return [$aRet, $aWeekDataTitle];
    }

    /**
     * @param $aDataList
     * @return array
     */
    private function getDataMap($aDataList = []) {
        // 客户
        $aCustomerId = array_values(array_unique(array_column($aDataList, 'customer_id')));
        $aCustomerList = Customer::getCustomerByIdList($aCustomerId);
        $aCustomerMap = array_column($aCustomerList, null, 'customer_id');
        // 销售
        $aSalesman = array_values(array_unique(array_column($aDataList, 'salesman')));
        $aSalesmanMap = SystemUser::getRealNameByNames($aSalesman);
        // 主体
        $aGroupId = array_values(array_filter(array_unique(array_column($aDataList, 'group_id'))));
        $aGroupList = CustomerGroup::getGroupByIdList($aGroupId);
        $aGroupMap = array_column($aGroupList, null, 'group_id');
        // 产品
        $aFatherId = array_column($aDataList, 'father_id');
        $aProductId = array_column($aDataList, 'product_id');
        $aAllProductId = array_values(array_unique(array_merge($aFatherId, $aProductId)));
        $aProductList = Product::getProductListByProductIds($aAllProductId, ['product_id', 'product_name', 'create_at']);
        $aProductMap = array_column($aProductList, 'product_name', 'product_id');

        return [$aCustomerMap, $aGroupMap, $aProductMap, $aSalesmanMap];
    }

    /**
     * @return array
     * @throws Exception
     */
    public function saveMonitorInfo() {
        $iMonitorId = (int)request()->post('monitor_id', 0);
        $iExpectOnlineTime = (int)request()->post('expect_online_time', 0);
        $iDebugTime = (int)request()->post('debug_time', 0);
        $iFeeTime = (int)request()->post('fee_time', 0);
        $iCallTime = (int)request()->post('call_time', 0);
        $iGrayscaleStartTime = (int)request()->post('grayscale_start_time', 0);
        $iGrayscaleEndTime = (int)request()->post('grayscale_end_time', 0);
        $sReason = trim(request()->post('reason', ''));
        $iDockingStatus = (int)request()->post('docking_status', 0);
        if (!$iMonitorId) {
            throw new Exception('请求参数[主键ID(monitor_id)]不能为空');
        }
        if (($iGrayscaleStartTime && !$iGrayscaleEndTime) || (!$iGrayscaleStartTime && $iGrayscaleEndTime)) {
            throw new Exception('灰度时间不能只填写[开始时间]或者[结束时间]');
        }

        $oMonitor = MonitorCustomerProduct::find($iMonitorId);
        if (!$oMonitor->activate_status) {
            return [
                'docking_status' => $oMonitor->docking_status,
                'time_diff' => 0,
                'call_days' => 0,
                'stat_call_days' => 0,
            ];
        }
        $oMonitor->reason = $sReason;
        $oMonitor->expect_online_time = $iExpectOnlineTime;
        $oMonitor->grayscale_start_time = $iGrayscaleStartTime ? strtotime(date('Y-m-d', $iGrayscaleStartTime) . ' 00:00:00') : 0;
        $oMonitor->grayscale_end_time = $iGrayscaleEndTime ? strtotime(date('Y-m-d', $iGrayscaleEndTime) . ' 23:59:59') : 0;
        $oMonitor->debug_time = $iDebugTime;
        $oMonitor->fee_time = $iFeeTime;
        $oMonitor->call_time = $iCallTime;
        list($iCallDays, $iCallNumber, $iStatCallDays) = MonitorCustomerProduct::aGetCallDays($oMonitor);
        $oMonitor->call_days = $iCallDays;
        $oMonitor->call_number = $iCallNumber;
        $iTimeDiff = 0;
        if (!$iDockingStatus) {
            list($iDockingStatus, $iTimeDiff) = MonitorCustomerProduct::aGetDockingStatus($oMonitor);
        }
        $oMonitor->docking_status = $iDockingStatus;
        $oMonitor->time_diff = $iTimeDiff;
        $oMonitor->save();

        return [
            'docking_status' => $iDockingStatus,
            'time_diff' => $iTimeDiff,
            'call_days' => $iCallDays,
            'stat_call_days' => $iStatCallDays,
        ];
    }

    /**
     * @return true
     * @throws Exception
     */
    public function createApproval() {
        $aMonitorId = (array)request()->post('monitor_id_list', []);
        $sApprovalCode = trim(request()->post('approval_code', ''));
        if (empty($aMonitorId) || empty($sApprovalCode)) {
            throw new Exception('请求参数异常, 请联系研发查看');
        }

        $aCond = [
            ['id', 'in', $aMonitorId],
        ];
        $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond);
        if (!$oDataList->count()) {
            throw new Exception('数据异常, 请稍后重试');
        }

        $oHandle = new MonitorApprovalHandle($oDataList, $sApprovalCode);
        $oHandle->checkBeforeApproval();
        $oHandle->setStrategy(new StrategyApi())->buildGatherList()->handle();

        return true;
    }

    /**
     * @return array
     */
    public function monitorOptionList() {
        // 产品
        list($aSysProductTextOption, $aSysProductTreeMap) = $this->aSysProductOptionAndMap();
        // 客户 搜索关联
        $aAllCustomer = Customer::getAllCustomer(['customer_id as value', 'name as label']);
        // 主体 搜索关联
        $aAllGroup = CustomerGroup::getGroupList(['group_id as value', 'group_name as label']);

        $aDeptData = DeptRepository::getSaleDeptRecursion(Dept::SALE_DEPT_ID, 1);
        $aDeptTreeOption = $aDeptData['dept_infos'];

        return [
            'product_text_option' => $aSysProductTextOption,
            'product_tree_map' => $aSysProductTreeMap,
            'customer_text_option' => $aAllCustomer,
            'group_text_option' => $aAllGroup,
            'dept_sales_option' => $aDeptTreeOption,
            'approval_code_map' => PreTestMonitor::APPROVAL_CODE_MAP,
            'instance_status_map' => PreTestManage::INSTANCE_STATUS_MAP,
            'reason_type_map' => PreTestMonitor::REASON_TYPE_MAP,
        ];
    }

    /**
     * @return array
     */
    public function monitorCustomerCount() {
        $aCond = $this->initListCond();
        if (false === $aCond) {
            return [
                'total_page' => 0,
            ];
        }
        list($iOffset, $iPerPage) = $this->initPage();
        $iTotal = MonitorCustomerProduct::iGetCountByCond($aCond);

        return [
            'total_page' => ceil($iTotal / $iPerPage),
        ];
    }

    /**
     * @return array
     */
    public function monitorApprovalList() {
        $aCond = $this->initApprovalListCond();
        $iTotal = MonitorApproval::iGetCountByCond($aCond);
        if (!$iTotal) {
            return [
                'total' => 0,
                'list' => [],
            ];
        }
        list($iOffset, $iPerPage) = $this->initPage();
        $aDataList = MonitorApproval::aGetDataByCond($aCond, ['*'], $iOffset, $iPerPage);
        $aList = $this->formatApprovalDataList($aDataList);

        return [
            'total' => count($aList),
            'list' => $aList,
        ];
    }

    /**
     * @param $aDataList
     * @return array
     */
    private function formatApprovalDataList($aDataList = []) {
        $aList = [];

        list($aCustomerMap, $aGroupMap, $aProductMap, $aSalesmanMap) = $this->getDataMap($aDataList);
        foreach ($aDataList as $aData) {
            $aContent = json_decode($aData['content'], true);
            $iIsContinue = $aContent['is_extend'] ?? ($aContent['is_continue_gray'] ?? -1);
            $iReasonType = $aData['reason_type'] ?: PreTestMonitor::REASON_TYPE_4;

            $aList[] = [
                'id' => $aData['id'],
                'approval_title' => PreTestMonitor::APPROVAL_CODE_MAP[$aData['approval_code']] ?? '',
                'notice_times' => $aContent['notice_times'] ?? 0,
                'monitor_id' => $aData['monitor_id'],
                'customer_id' => $aData['customer_id'],
                'customer_name' => $aCustomerMap[$aData['customer_id']]['name'] ?? '',
                'father_id' => $aData['father_id'],
                'father_name' => $aProductMap[$aData['father_id']] ?? '',
                'product_id' => $aData['product_id'],
                'product_name' => $aProductMap[$aData['product_id']] ?? '',
                'salesman' => $aSalesmanMap[$aData['salesman']] ?? '',
                'instance_status' => $aData['instance_status'],
                'instance_status_text' => PreTestManage::INSTANCE_STATUS_MAP[$aData['instance_status']] ?? '',
                'apply_time' => substr($aData['apply_time'], 0, 10),
                'activate_time' => $aContent['activate_time'] ?? '-',
                'debug_time' => $aContent['debug_time'] ?? '-',
                'grayscale_start_time' => $aContent['grayscale_start_time'] ?? '-',
                'grayscale_end_time' => $aContent['grayscale_end_time'] ?? '-',
                'expect_online_time' => $aContent['expect_online_time'] ?? '-',
                'new_grayscale_start_time' => $aContent['new_grayscale_start_time'] ?? '-',
                'new_grayscale_end_time' => $aContent['new_grayscale_end_time'] ?? '-',
                'new_expect_online_time' => $aContent['new_expect_online_time'] ?? '-',
                'reason_type' => $aData['reason_type'],
                'reason_type_text' => PreTestMonitor::REASON_TYPE_MAP[$iReasonType] ?? '',
                'reason' => $aContent['reason'] ?? '',
                'is_continue' => PreTestManage::WHETHER_TEXT_MAP[$iIsContinue] ?? '',
            ];
        }

        return $aList;
    }

    /**
     * @return array
     */
    public function monitorApprovalCount() {
        $aCond = $this->initApprovalListCond();
        list($iOffset, $iPerPage) = $this->initPage();
        $iTotal = MonitorApproval::iGetCountByCond($aCond);

        return [
            'total_page' => ceil($iTotal / $iPerPage),
        ];
    }

    /**
     * @return array
     */
    private function initApprovalListCond() {
        $iApplyTimeStart = (int)request()->post('apply_time_start', 0);
        $iApplyTimeEnd = (int)request()->post('apply_time_end', 0);
        $sCustomerId = trim(request()->post('customer_id', ''));
        $iFatherId = (int)request()->post('father_id', 0);
        $iProductId = (int)request()->post('product_id', 0);
        $sDept = trim(request()->post('dept', ''));
        $sSalesman = trim(request()->post('salesman', ''));
        $sApprovalCode = trim(request()->post('approval_code', ''));
        $sInstanceStatus = trim(request()->post('instance_status', ''));
        $iReasonType = (int)request()->post('reason_type', 0);

        $aCond = [];
        if ($iApplyTimeStart) {
            $sApplyTimeStart = date('Y-m-d', $iApplyTimeStart) . ' 00:00:00';
            $aCond[] = ['apply_time', '>=', $sApplyTimeStart];
        }
        if ($iApplyTimeEnd) {
            $sApplyTimeEnd = date('Y-m-d', $iApplyTimeEnd) . ' 23:59:59';
            $aCond[] = ['apply_time', '<=', $sApplyTimeEnd];
        }
        if ($sCustomerId) {
            $aCond[] = ['customer_id', '=', $sCustomerId];
        }
        if ($iFatherId) {
            $aCond[] = ['father_id', '=', $iFatherId];
        }
        if ($iProductId) {
            $aCond[] = ['product_id', '=', $iProductId];
        }
        if ($sDept) {
            $aDeptSalesMan = $this->getDeptSalesmanList($sDept, false);
            $aCond[] = ['salesman', 'in', $aDeptSalesMan];
        }
        if ($sSalesman) {
            $aCond[] = ['salesman', '=', $sSalesman];
        }
        if ($sApprovalCode) {
            $aCond[] = ['approval_code', '=', $sApprovalCode];
        }
        if ($sInstanceStatus) {
            $aCond[] = ['instance_status', '=', $sInstanceStatus];
        }
        if (PreTestMonitor::APPROVAL_CODE_CALL_STABLE != $sApprovalCode) {
            if ($iReasonType) {
                $aCond[] = ['reason_type', '=', $iReasonType];
            }
        }

        return $aCond;
    }
}