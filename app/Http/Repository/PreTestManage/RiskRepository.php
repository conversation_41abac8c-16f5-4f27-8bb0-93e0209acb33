<?php

namespace App\Http\Repository\PreTestManage;

use Exception;
use App\Define\PreTestManage;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyProduct;
use App\Models\Product;

/**
 * 风险统计
 * @package App\Http\Repository\PreTestManage
 * @class RiskRepository
 */
class RiskRepository extends PreTestManageRepository
{

    /**
     * @return array
     */
    public function riskList() {
        $sUserCookie = (string)request()->post('user_cookie', '');
        $aApplyTimeRange = (array)request()->post('apply_time_range', []);
        list($sApplyTimeStart, $sApplyTimeEnd) = $aApplyTimeRange;
        if (strtotime($sApplyTimeStart) > strtotime($sApplyTimeEnd)) {
            throw new Exception(sprintf('请求参数错误,开始时间(%s)不能大于结束时间(%s)', $sApplyTimeStart, $sApplyTimeEnd));
        }
        $aInitCond = [];
        $aSalesCond = [];
        if ($sApplyTimeStart) {
            $aInitCond[] = ['apply_time', '>=', $sApplyTimeStart . ' 00:00:00'];
        }
        if ($sApplyTimeEnd) {
            $aInitCond[] = ['apply_time', '<=', $sApplyTimeEnd . ' 23:59:59'];
        }
        list($isSale, $aUnderlingRealName, $iSelfOnly) = $this->aUserInfo($sUserCookie);
        if ($isSale == 1) {
            $aSalesCond[] = ['salesman', 'in', $aUnderlingRealName];
        }
        // 完成测试
        $aCond = [
            ['test_status', '>=', PreTestManage::STATUS_TEST_DONE],
        ];
        $aCond = array_merge($aCond, $aInitCond, $aSalesCond);
        $aTestDoneData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
        if (empty($aTestDoneData)) {
            return [
                'list' => [],
            ];
        }
        $aTestDoneDataMap = array_column($aTestDoneData, 'count', 'salesman');
        // 逾期未反馈
        $aCond = [
            ['apply_time', '>=', '2024-10-01 00:00:00'],
            ['is_schedule', '=', PreTestManage::IS_NO],
            ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
            ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
            ['schedule_time', '<=', date('Y-m-d H:i:s')],
        ];
        $aCond = array_merge($aCond, $aInitCond, $aSalesCond);
        $aScheduleUndoneData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
        $aScheduleUndoneDataMap = array_column($aScheduleUndoneData, 'count', 'salesman');
        // 超过3个月未反馈
        $aCond = [
            ['apply_time', '>=', '2024-10-01 00:00:00'],
            ['is_schedule', '=', PreTestManage::IS_NO],
            ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
            ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
            ['return_time', '<=', date('Y-m-d H:i:s', strtotime('-90 days'))],
        ];
        $aCond = array_merge($aCond, $aSalesCond);
        $aScheduleOver3Data = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
        $aScheduleOver3DataMap = array_column($aScheduleOver3Data, 'count', 'salesman');
        // 没有调用的量
        $aCond = [
            ['is_open', '=', PreTestManage::IS_YES],
            ['number_total', '=', 0],
            ['purpose', '=', PreTestManage::IS_YES],
        ];
        $aCond = array_merge($aCond, $aInitCond, $aSalesCond);
        $aNotCallData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
        $aNotCallDataMap = array_column($aNotCallData, 'count', 'salesman');

        // 销售, 效果, 是否开通, 是否填写指标
        $aCond = [];
        $aCond = array_merge($aCond, $aInitCond, $aSalesCond);
        $aGroupField = ['salesman', 'test_result', 'is_open', 'have_objective', 'purpose'];
        $aGroupDataList = ApplyFather::aGetGroupDataByCond($aCond, $aGroupField);

        $aResultUnknownDataMap = []; // 效果未获取
        $aResultMap = []; // 已反馈
        $aNotObjectiveDataMap = []; // 效果已获取&未填写指标
        $aCanObjectiveMap = []; // 效果已获取
        $aGoodAndCLoseMap = []; // 效果好&未开通
        $aGoodMap = []; // 效果好
        $aBadAndOpenMap = []; // 效果不好&已开通
        $aBadMap = []; // 效果不好
        $aOpenMap = [];
        foreach ($aGroupDataList as $aData) {
            $sSalesman = $aData['salesman'];
            // 已开通
            if (PreTestManage::IS_YES == $aData['is_open']) {
                $aOpenMap[$sSalesman] = ($aOpenMap[$sSalesman] ?? 0) + $aData['count'];
            }
            if (!$aData['test_result']) {
                continue;
            }
            // 已反馈
            $aResultMap[$sSalesman] = ($aResultMap[$sSalesman] ?? 0) + $aData['count'];
            if (in_array($aData['test_result'], [PreTestManage::TEST_RESULT_UNABLE, PreTestManage::TEST_RESULT_NA])) {
                // 效果:未获取
                $aResultUnknownDataMap[$sSalesman] = ($aResultUnknownDataMap[$sSalesman] ?? 0) + $aData['count'];
            } else {
                // 效果:已获取(可以填写指标的量)
                $aCanObjectiveMap[$sSalesman] = ($aCanObjectiveMap[$sSalesman] ?? 0) + $aData['count'];
                if (PreTestManage::IS_NO == $aData['have_objective']) {
                    // 未填写指标的量
                    $aNotObjectiveDataMap[$sSalesman] = ($aNotObjectiveDataMap[$sSalesman] ?? 0) + $aData['count'];
                }
                if (PreTestManage::TEST_RESULT_GOOD == $aData['test_result']) {
                    // 效果好
                    $aGoodMap[$sSalesman] = ($aGoodMap[$sSalesman] ?? 0) + $aData['count'];
                    // 效果好&未开通
                    if (PreTestManage::IS_NO == $aData['is_open'] && PreTestManage::IS_DOCKING == $aData['purpose']) {
                        $aGoodAndCLoseMap[$sSalesman] = ($aGoodAndCLoseMap[$sSalesman] ?? 0) + $aData['count'];
                    }
                } else if (PreTestManage::TEST_RESULT_BAD == $aData['test_result']) {
                    // 效果不好
                    $aBadMap[$sSalesman] = ($aBadMap[$sSalesman] ?? 0) + $aData['count'];
                    // 效果不好&已开通
                    if (PreTestManage::IS_YES == $aData['is_open'] && PreTestManage::IS_DOCKING == $aData['purpose']) {
                        $aBadAndOpenMap[$sSalesman] = ($aBadAndOpenMap[$sSalesman] ?? 0) + $aData['count'];
                    }
                }
            }
        }
//        $this->aStatisticsCount();

        $aDataDemo = [
            'primary_count' => 0,
            'schedule_undone_count' => 0,
            'schedule_undone_count_ratio' => 0,
            'schedule_over3_count' => 0,
            'schedule_over3_count_ratio' => 0,
            'result_unknown_count' => 0,
            'result_count' => 0,
            'result_unknown_count_ratio' => 0,
            'not_objective_count' => 0,
            'can_objective_count' => 0,
            'not_objective_count_ratio' => 0,
            'not_call_count' => 0,
            'open_count' => 0,
            'not_call_count_ratio' => 0,
            'good_close_count' => 0,
            'good_count' => 0,
            'good_close_count_ratio' => 0,
            'bad_open_count' => 0,
            'bad_count' => 0,
            'bad_open_count_ratio' => 0,
            'dept_key' => '',
            'total_flag' => false,
        ];
        list($aSalesMap, $aSalesDeptMap) = $this->initSalesMap($aDataDemo);
        // 聚合 至 销售维度
        foreach ($aSalesMap as $sSalesMan => &$aItem) {
            $iTestDone = $aTestDoneDataMap[$sSalesMan] ?? 0;
            if (!$iTestDone) {
                continue;
            }
            $iUndone = $aScheduleUndoneDataMap[$sSalesMan] ?? 0;
            $iOver3 = $aScheduleOver3DataMap[$sSalesMan] ?? 0;
            $iUnknown = $aResultUnknownDataMap[$sSalesMan] ?? 0;
            $iResult = $aResultMap[$sSalesMan] ?? 0;
            $iNotObjective = $aNotObjectiveDataMap[$sSalesMan] ?? 0;
            $iCanObjective = $aCanObjectiveMap[$sSalesMan] ?? 0;
            $iNotCall = $aNotCallDataMap[$sSalesMan] ?? 0;
            $iOpen = $aOpenMap[$sSalesMan] ?? 0;
            $iGoodCLose = $aGoodAndCLoseMap[$sSalesMan] ?? 0;
            $iGood = $aGoodMap[$sSalesMan] ?? 0;
            $iBadOpen = $aBadAndOpenMap[$sSalesMan] ?? 0;
            $iBad = $aBadMap[$sSalesMan] ?? 0;

            $aSalesMap[$sSalesMan]['primary_count'] = $iTestDone;
            $aSalesMap[$sSalesMan]['schedule_undone_count'] = $iUndone;
            $aSalesMap[$sSalesMan]['schedule_undone_count_ratio'] = round(($iUndone / $iTestDone) * 100, 2);
            $aSalesMap[$sSalesMan]['schedule_over3_count'] = $iOver3;
            $aSalesMap[$sSalesMan]['schedule_over3_count_ratio'] = round(($iOver3 / $iTestDone) * 100, 2);
            $aSalesMap[$sSalesMan]['result_unknown_count'] = $iUnknown;
            $aSalesMap[$sSalesMan]['result_count'] = $iResult;
            $aSalesMap[$sSalesMan]['result_unknown_count_ratio'] = $iResult ? round(($iUnknown / $iResult) * 100, 2) : 0;
            $aSalesMap[$sSalesMan]['not_objective_count'] = $iNotObjective;
            $aSalesMap[$sSalesMan]['can_objective_count'] = $iCanObjective;
            $aSalesMap[$sSalesMan]['not_objective_count_ratio'] = $iCanObjective ? round(($iNotObjective / $iCanObjective) * 100, 2) : 0;
            $aSalesMap[$sSalesMan]['not_call_count'] = $iNotCall;
            $aSalesMap[$sSalesMan]['open_count'] = $iOpen;
            $aSalesMap[$sSalesMan]['not_call_count_ratio'] = $iOpen ? round(($iNotCall / $iOpen) * 100, 2) : 0;
            $aSalesMap[$sSalesMan]['good_close_count'] = $iGoodCLose;
            $aSalesMap[$sSalesMan]['good_count'] = $iGood;
            $aSalesMap[$sSalesMan]['good_close_count_ratio'] = $iGood ? round(($iGoodCLose / $iGood) * 100, 2) : 0;
            $aSalesMap[$sSalesMan]['bad_open_count'] = $iBadOpen;
            $aSalesMap[$sSalesMan]['bad_count'] = $iBad;
            $aSalesMap[$sSalesMan]['bad_open_count_ratio'] = $iBad ? round(($iBadOpen / $iBad) * 100, 2) : 0;
        }
        unset($aItem);

        $aDataList = $this->aGatherSalesDataToDept($aSalesMap, $aSalesDeptMap, 'statTotalForRisk');

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @return array
     */
//    public function riskList_Bak() {
//        $sUserCookie = (string)request()->post('user_cookie', '');
//        $aApplyTimeRange = (array)request()->post('apply_time_range', []);
//        list($sApplyTimeStart, $sApplyTimeEnd) = $aApplyTimeRange;
//        if (strtotime($sApplyTimeStart) > strtotime($sApplyTimeEnd)) {
//            throw new Exception(sprintf('请求参数错误,开始时间(%s)不能大于结束时间(%s)', $sApplyTimeStart, $sApplyTimeEnd));
//        }
//        $aInitCond = [];
//        if ($sApplyTimeStart) {
//            $aInitCond[] = ['apply_time', '>=', $sApplyTimeStart . ' 00:00:00'];
//        }
//        if ($sApplyTimeEnd) {
//            $aInitCond[] = ['apply_time', '<=', $sApplyTimeEnd . ' 23:59:59'];
//        }
//        list($isSale, $aUnderlingRealName, $iSelfOnly) = $this->aUserInfo($sUserCookie);
//        if ($isSale) {
//            $aInitCond[] = ['salesman', 'in', $aUnderlingRealName];
//        }
//        // 基数
//        $aCond = [
//            ['test_status', '>=', PreTestManage::STATUS_TEST_DONE],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aTestDoneData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        if (empty($aTestDoneData)) {
//            return [
//                'list' => [],
//            ];
//        }
//        $aTestDoneDataMap = array_column($aTestDoneData, 'count', 'salesman');
//        // 逾期未反馈
//        $aCond = [
//            ['is_schedule', '=', PreTestManage::IS_NO],
//            ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
//            ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
//            ['schedule_time', '<=', date('Y-m-d H:i:s')],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aScheduleUndoneData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        $aScheduleUndoneDataMap = array_column($aScheduleUndoneData, 'count', 'salesman');
//        // 超过3个月未反馈
//        $aCond = [
//            ['is_schedule', '=', PreTestManage::IS_NO],
//            ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
//            ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
//            ['return_time', '<=', date('Y-m-d H:i:s', strtotime('-90 days'))],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aScheduleOver3Data = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        $aScheduleOver3DataMap = array_column($aScheduleOver3Data, 'count', 'salesman');
//        // 效果:未获取
//        $aCond = [
//            ['test_result', 'in', [PreTestManage::TEST_RESULT_UNABLE, PreTestManage::TEST_RESULT_NA]],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aResultUnknownData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        $aResultUnknownDataMap = array_column($aResultUnknownData, 'count', 'salesman');
//        // 未填写指标的量
//        $aCond = [
//            ['test_result', 'in', [PreTestManage::TEST_RESULT_GOOD, PreTestManage::TEST_RESULT_AVERAGE, PreTestManage::TEST_RESULT_BAD]],
//            ['have_objective', '=', PreTestManage::IS_NO],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aNotObjectiveData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        $aNotObjectiveDataMap = array_column($aNotObjectiveData, 'count', 'salesman');
//        // 没有调用的量
//        $aCond = [
//            ['is_open', '=', PreTestManage::IS_YES],
//            ['number_total', '=', 0],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aNotCallData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        $aNotCallDataMap = array_column($aNotCallData, 'count', 'salesman');
//        // 效果好&未开通
//        $aCond = [
//            ['test_result', '=', PreTestManage::TEST_RESULT_GOOD],
//            ['is_open', '=', PreTestManage::IS_NO],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aGoodAndCLoseData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        $aGoodAndCLoseMap = array_column($aGoodAndCLoseData, 'count', 'salesman');
//        // 效果不好&已开通
//        $aCond = [
//            ['test_result', '=', PreTestManage::TEST_RESULT_BAD],
//            ['is_open', '=', PreTestManage::IS_YES],
//        ];
//        $aCond = array_merge($aCond, $aInitCond);
//        $aBadAndOpenData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
//        $aBadAndOpenMap = array_column($aBadAndOpenData, 'count', 'salesman');
//
////        $this->aStatisticsCount();
//
//        $aDataDemo = [
//            'primary_count' => 0,
//            'schedule_undone_count' => 0,
//            'schedule_undone_count_ratio' => 0,
//            'schedule_over3_count' => 0,
//            'schedule_over3_count_ratio' => 0,
//            'result_unknown_count' => 0,
//            'result_count' => 0,
//            'result_unknown_count_ratio' => 0,
//            'not_objective_count' => 0,
//            'can_objective_count' => 0,
//            'not_objective_count_ratio' => 0,
//            'not_call_count' => 0,
//            'open_count' => 0,
//            'not_call_count_ratio' => 0,
//            'good_close_count' => 0,
//            'good_count' => 0,
//            'good_close_count_ratio' => 0,
//            'bad_open_count' => 0,
//            'bad_count' => 0,
//            'bad_open_count_ratio' => 0,
//            'dept_key' => '',
//            'total_flag' => false,
//        ];
//        list($aSalesMap, $aSalesDeptMap) = $this->initSalesMap($aDataDemo);
//        // 聚合 至 销售维度
//        foreach ($aSalesMap as $sSalesMan => &$aItem) {
//            $iTestDone = $aTestDoneDataMap[$sSalesMan] ?? 0;
//            if (!$iTestDone) {
//                continue;
//            }
//            $iUndone = $aScheduleUndoneDataMap[$sSalesMan] ?? 0;
//            $iNotObjective = $aNotObjectiveDataMap[$sSalesMan] ?? 0;
//            $iNotCall = $aNotCallDataMap[$sSalesMan] ?? 0;
//            $iGoodCLose = $aGoodAndCLoseMap[$sSalesMan] ?? 0;
//            $iBadOpen = $aBadAndOpenMap[$sSalesMan] ?? 0;
//            $iOver3 = $aScheduleOver3DataMap[$sSalesMan] ?? 0;
//            $iUnknown = $aResultUnknownDataMap[$sSalesMan] ?? 0;
//
//            $aSalesMap[$sSalesMan]['primary_count'] = $iTestDone;
//            $aSalesMap[$sSalesMan]['schedule_undone_count'] = $iUndone;
//            $aSalesMap[$sSalesMan]['not_objective_count'] = $iNotObjective;
//            $aSalesMap[$sSalesMan]['not_call_count'] = $iNotCall;
//            $aSalesMap[$sSalesMan]['good_close_count'] = $iGoodCLose;
//            $aSalesMap[$sSalesMan]['bad_open_count'] = $iBadOpen;
//            $aSalesMap[$sSalesMan]['schedule_over3_count'] = $iOver3;
//            $aSalesMap[$sSalesMan]['result_unknown_count'] = $iUnknown;
//
//            $aSalesMap[$sSalesMan]['schedule_undone_count_ratio'] = round(($iUndone / $iTestDone) * 100, 2);
//            $aSalesMap[$sSalesMan]['not_objective_count_ratio'] = round(($iNotObjective / $iTestDone) * 100, 2);
//            $aSalesMap[$sSalesMan]['not_call_count_ratio'] = round(($iNotCall / $iTestDone) * 100, 2);
//            $aSalesMap[$sSalesMan]['good_close_count_ratio'] = round(($iGoodCLose / $iTestDone) * 100, 2);
//            $aSalesMap[$sSalesMan]['bad_open_count_ratio'] = round(($iBadOpen / $iTestDone) * 100, 2);
//            $aSalesMap[$sSalesMan]['schedule_over3_count_ratio'] = round(($iOver3 / $iTestDone) * 100, 2);
//            $aSalesMap[$sSalesMan]['result_unknown_count_ratio'] = round(($iUnknown / $iTestDone) * 100, 2);
//        }
//        unset($aItem);
//
//        $aDataList = $this->aGatherSalesDataToDept($aSalesMap, $aSalesDeptMap, 'statTotalForRisk');
//
//        return [
//            'list' => $aDataList,
//        ];
//    }

    /**
     * @param $aDataList
     * @param $aGatherData
     * @return array
     * @uses statTotalForRisk
     */
    protected function statTotalForRisk($aDataList = [], $aGatherData = []) {
        $aTotal = [
            'primary_count' => 0,
            'schedule_undone_count' => 0,
            'schedule_over3_count' => 0,
            'result_unknown_count' => 0,
            'result_count' => 0,
            'not_objective_count' => 0,
            'can_objective_count' => 0,
            'not_call_count' => 0,
            'open_count' => 0,
            'good_close_count' => 0,
            'good_count' => 0,
            'bad_open_count' => 0,
            'bad_count' => 0,
            'dept_key' => '',
            'total_flag' => true,
        ];
        foreach ($aDataList as $aData) {
            if ($aData['total_flag']) {
                continue;
            }

            $aTotal['primary_count'] += $aData['primary_count'];
            $aTotal['schedule_undone_count'] += $aData['schedule_undone_count'];
            $aTotal['schedule_undone_count_ratio'] = round(($aTotal['schedule_undone_count'] / $aTotal['primary_count']) * 100, 2);
            $aTotal['schedule_over3_count'] += $aData['schedule_over3_count'];
            $aTotal['schedule_over3_count_ratio'] = round(($aTotal['schedule_over3_count'] / $aTotal['primary_count']) * 100, 2);
            $aTotal['result_unknown_count'] += $aData['result_unknown_count'];
            $aTotal['result_count'] += $aData['result_count'];
            $aTotal['result_unknown_count_ratio'] = $aTotal['result_count'] ? round(($aTotal['result_unknown_count'] / $aTotal['result_count']) * 100, 2) : 0;
            $aTotal['not_objective_count'] += $aData['not_objective_count'];
            $aTotal['can_objective_count'] += $aData['can_objective_count'];
            $aTotal['not_objective_count_ratio'] = $aTotal['can_objective_count'] ? round(($aTotal['not_objective_count'] / $aTotal['can_objective_count']) * 100, 2) : 0;
            $aTotal['not_call_count'] += $aData['not_call_count'];
            $aTotal['open_count'] += $aData['open_count'];
            $aTotal['not_call_count_ratio'] = $aTotal['open_count'] ? round(($aTotal['not_call_count'] / $aTotal['open_count']) * 100, 2) : 0;
            $aTotal['good_close_count'] += $aData['good_close_count'];
            $aTotal['good_count'] += $aData['good_count'];
            $aTotal['good_close_count_ratio'] = $aTotal['good_count'] ? round(($aTotal['good_close_count'] / $aTotal['good_count']) * 100, 2) : 0;
            $aTotal['bad_open_count'] += $aData['bad_open_count'];
            $aTotal['bad_count'] += $aData['bad_count'];
            $aTotal['bad_open_count_ratio'] = $aTotal['bad_count'] ? round(($aTotal['bad_open_count'] / $aTotal['bad_count']) * 100, 2) : 0;
        }

        $aDataList[] = array_merge($aTotal, $aGatherData);

        return $aDataList;
    }

    /**
     * @return void
     * @throws Exception
     * @deprecated demo
     */
    private function aStatisticsCount(){
        $aApplyTimeRange = (array)request()->post('apply_time_range', []);
        list($sApplyTimeStart, $sApplyTimeEnd) = $aApplyTimeRange;
        if (strtotime($sApplyTimeStart) > strtotime($sApplyTimeEnd)) {
            throw new Exception(sprintf('请求参数错误,开始时间(%s)不能大于结束时间(%s)', $sApplyTimeStart, $sApplyTimeEnd));
        }
        $t1 = microtime(true);
        $aCond = [];
        if ($sApplyTimeStart) {
            $aCond[] = ['apply_time', '>=', $sApplyTimeStart . ' 00:00:00'];
        }
        if ($sApplyTimeEnd) {
            $aCond[] = ['apply_time', '<=', $sApplyTimeEnd . ' 23:59:59'];
        }
        $aDataList = ApplyFather::aGetDataByCond($aCond);

        $sNow = date('Y-m-d H:i:s');
        $sMonthBefore = date('Y-m-d H:i:s', strtotime('-30 days'));
        $aMap1 = []; // 逾期未反馈
        $aMap2 = []; // 长期未反馈
        $aMap3 = []; // 填写指标的量
        $aMap4 = []; // 没有调用的量
        $aMap5 = []; // 可接入&未开通
        $aMap6 = []; // 不接入&已开通
        foreach ($aDataList as $aData) {
            // 未反馈
            if ($aData['is_schedule'] == PreTestManage::IS_NO && $aData['test_result'] == PreTestManage::TEST_RESULT_DEFAULT && $aData['test_status'] == PreTestManage::STATUS_TEST_DONE) {
                // 逾期未反馈
                if ($aData['schedule_time'] <= $sNow) {
                    $aMap1[$aData['apply_id']] = isset($aMap1[$aData['apply_id']]) ? ++$aMap1[$aData['apply_id']] : 1;
                }
                // 长期未反馈
                if ($aData['return_time'] <= $sMonthBefore) {
                    $aMap2[$aData['apply_id']] = isset($aMap2[$aData['apply_id']]) ? ++$aMap2[$aData['apply_id']] : 1;
                }
            }
            // 未填写指标的量
            else if ($aData['test_result'] > PreTestManage::TEST_RESULT_DEFAULT && $aData['have_objective'] == PreTestManage::IS_NO) {
                $aMap3[$aData['apply_id']] = isset($aMap3[$aData['apply_id']]) ? ++$aMap3[$aData['apply_id']] : 1;
            }
            // 没有调用的量
            if ($aData['is_open'] == PreTestManage::IS_YES && $aData['number_total'] == 0) {
                $aMap4[$aData['apply_id']] = isset($aMap4[$aData['apply_id']]) ? ++$aMap4[$aData['apply_id']] : 1;
            }
            // 可接入&未开通
            if ($aData['access_action'] == PreTestManage::ACCESS_ACTION_ABLE && $aData['is_open'] == PreTestManage::IS_NO) {
                $aMap5[$aData['apply_id']] = isset($aMap5[$aData['apply_id']]) ? ++$aMap5[$aData['apply_id']] : 1;
            }
            // 不接入&已开通
            if ($aData['access_action'] == PreTestManage::ACCESS_ACTION_UNABLE && $aData['is_open'] == PreTestManage::IS_YES) {
                $aMap6[$aData['apply_id']] = isset($aMap6[$aData['apply_id']]) ? ++$aMap6[$aData['apply_id']] : 1;
            }
        }
        $t3 = microtime(true);

        echo '<pre>';
        print_r($t3 - $t1);
        echo '</pre>';

    }

    /**
     * @return array[]
     * @deprecated demo
     */
    public function riskProductList() {
        // 所有测试产品
        $aProductGroup = ApplyProduct::aGetGroupByProduct();
        $aFatherId = array_column($aProductGroup, 'father_id');

        $aFatherList = Product::getProductListByProductIds($aFatherId, ['product_id', 'product_name']);

        $aDataDemo = [
            'apply_count' => 0,
            'schedule_undone_count' => 0,
            'schedule_over_count' => 0,
            'schedule_over3_count' => 0,
            'result_unknown_count' => 0,
            'not_objective_count' => 0,
            'not_call_count' => 0,
            'good_close_count' => 0,
            'bad_open_count' => 0,
            'total_flag' => false,
        ];

        $aList = [];
        foreach ($aFatherList as $aFather) {
            $aList[] = [
                'father_id' => $aFather['product_id'],
                'father_name' => $aFather['product_name'],
                'apply_count' => rand(0, 5),
                'schedule_undone_count' => rand(0, 5),
                'schedule_over_count' => rand(0, 5),
                'schedule_over3_count' => rand(0, 5),
                'result_unknown_count' => rand(0, 5),
                'not_objective_count' => rand(0, 5),
                'not_call_count' => rand(0, 5),
                'good_close_count' => rand(0, 5),
                'bad_open_count' => rand(0, 5),
                'total_flag' => false,
            ];
        }
        $aDataList = $this->statTotalForRisk($aList, ['father_id' => 0, 'father_name' => '合计']);

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @return array
     */
    public function riskDetailList() {
        $aDataList = [];

        // 超时未开始测试
        $aGroup1 = $this->riskForLateStart();
        // 超时未完成测试
        $aGroup2 = $this->riskForLateComplete();
        // 超时未填写测试效果
        $aGroup3 = $this->riskForLateFeedback();
        // 超时未上线
        $aGroup4 = $this->riskForLateOnline(); // todo 实现方案未知
        // 效果好但未对接
        $aGroup5 = $this->riskForUnable();
        // 已上线未调用
        $aGroup6 = $this->riskForLateCall();

        $aDataList = array_merge($aDataList, $aGroup1, $aGroup2, $aGroup3, $aGroup4, $aGroup5, $aGroup6);

        return [
            'list' => $aDataList,
        ];
    }

    /**
     * @return array
     */
    private function riskForLateStart() {
        $sRiskType = '超时未开始测试';
        $iTermDay = 7;
        $sApplyTime = date('Y-m-d H:i:s', strtotime('-' . $iTermDay . ' days'));
        $aCond = [
            ['test_status', '=', PreTestManage::STATUS_WAITING],
            ['apply_time', '<=', $sApplyTime],
        ];
        $sTimeField = 'apply_time';
        $fFormatMsg = function ($aItem) {
            return sprintf('%s申请测试, 已%s天未开启测试', $aItem['apply_time'], $aItem['dx']);
        };

        return $this->formatRisk($sRiskType, $aCond, $sTimeField, $fFormatMsg);
    }

    /**
     * @return array
     */
    private function riskForLateComplete() {
        $sRiskType = '超时未完成测试';
        $iTermDay = 30;
        $sStartTime = date('Y-m-d H:i:s', strtotime('-' . $iTermDay . ' days'));
        $aCond = [
            ['test_status', '=', PreTestManage::STATUS_TESTING],
            ['start_time', '<=', $sStartTime],
        ];
        $sTimeField = 'start_time';
        $fFormatMsg = function ($aItem) {
            return sprintf('%s开始测试, 已%s天未完成测试', $aItem['start_time'], $aItem['dx']);
        };

        return $this->formatRisk($sRiskType, $aCond, $sTimeField, $fFormatMsg);
    }

    /**
     * @return array
     */
    private function riskForLateFeedback() {
        $sRiskType = '超时未填写测试效果';
        $iTermDay = 60;
        $sStartTime = date('Y-m-d H:i:s', strtotime('-' . $iTermDay . ' days'));
        $aCond = [
            ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
            ['return_time', '<=', $sStartTime],
            ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
        ];
        $sTimeField = 'return_time';
        $fFormatMsg = function ($aItem) {
            return sprintf('%s完成测试, 已%s天未填写测试效果', $aItem['return_time'], $aItem['dx']);
        };

        return $this->formatRisk($sRiskType, $aCond, $sTimeField, $fFormatMsg);
    }

    /**
     * @return array
     */
    private function riskForLateOnline() {
        $sRiskType = '超时未上线';
        return [];
    }

    /**
     * @return array
     */
    private function riskForUnable() {
        $sRiskType = '效果好但未对接';
        $aCond = [
            ['test_status', '=', PreTestManage::STATUS_ACCESS_UNABLE],
            ['test_result', '=', PreTestManage::TEST_RESULT_GOOD],
            ['access_action', '=', PreTestManage::ACCESS_ACTION_UNABLE],
        ];
        $sTimeField = 'return_time';
        $fFormatMsg = function ($aItem) {
            return sprintf('未对接原因: %s', $aItem['not_access_reason_text']);
        };

        return $this->formatRisk($sRiskType, $aCond, $sTimeField, $fFormatMsg);
    }

    /**
     * @return array
     */
    private function riskForLateCall() {
        $sRiskType = '已上线未调用';
        $iTermDay = 7;
        $sBindTime = date('Y-m-d H:i:s', strtotime('-' . $iTermDay . ' days'));
        $aCond = [
            ['test_status', '=', PreTestManage::STATUS_ACCESS_DONE],
            ['is_open', '=', PreTestManage::IS_YES],
            ['number_total', '=', 0],
            ['bind_time', '<=', $sBindTime],
        ];
        $sTimeField = 'bind_time';
        $fFormatMsg = function ($aItem) {
            return sprintf('%s完成绑定上线, 已%s天未发生调用', $aItem['bind_time'], $aItem['dx']);
        };

        return $this->formatRisk($sRiskType, $aCond, $sTimeField, $fFormatMsg);
    }

    /**
     * @param $sRiskType
     * @param $aCond
     * @param $sTimeField
     * @param $fFormatMsg
     * @return array
     */
    private function formatRisk($sRiskType = '', $aCond = [], $sTimeField = '', $fFormatMsg = null) {
//        $aFormCond = $this->initCondForFather();
        $aFormCond = [];
        $aFormCond and $aCond = array_merge($aCond, $aFormCond);
        $aApplyFatherList = ApplyFather::aGetDataByCond($aCond);
        if (empty($aApplyFatherList)) {
            return [];
        }
        $sNow = date('Y-m-d H:i:s');
//        list($aFatherMap, $aFatherCreatedMap) = $this->aBuildFatherMap($aApplyFatherList);
        $aApplyId = array_unique(array_column($aApplyFatherList, 'apply_id'));
        $aApplyList = ApplyCustomer::aGetListByApplyIdList($aApplyId);
        $aApplyMap = array_column($aApplyList, null, 'id');

        $aTestingHandleMap = [
            '号码分-特征变量' => '芦泽蓬',
            '号码分' => '芦泽蓬',
            '号码风险等级' => '芦泽蓬',
            '号码风险等级-特征变量' => '芦泽蓬',
            '邦信分-通信指数' => '芦泽蓬',
            '号码活跃指数' => '芦泽蓬',
            '在网状态及时长核验' => '芦泽蓬',
            '空号检测' => '芦泽蓬',
            '其他产品（ 备注中说明）' => '芦泽蓬',

            '事件分' => '王雨萌',
            '金盾-风险验证【50101】' => '王雨萌',
            '号码融' => '王雨萌',
            '号码融内部建模' => '王雨萌',
            '联合建模' => '王雨萌',
        ];

        $aDataList = [];
        foreach ($aApplyFatherList as $aItem) {
            $iApplyId = $aItem['apply_id'];
            $aApply = $aApplyMap[$iApplyId];
            $aItem['not_access_reason_text'] = PreTestManage::UNABLE_REASON_TEXT_MAP[$aItem['not_access_reason']] ?? '';
            $aItem['apply_time'] = substr($aItem['apply_time'], 0, 10);
            $aItem['start_time'] = substr($aItem['start_time'], 0, 10);
            $aItem['return_time'] = substr($aItem['return_time'], 0, 10);
            $aItem['bind_time'] = substr($aApply['bind_time'], 0, 10);
            $aItem['dx'] = $this->overDay($aItem[$sTimeField], $sNow);

            $sHandleMan = $aApply['salesman'];
            if ('超时未开始测试' == $sRiskType || '超时未完成测试' == $sRiskType) {
                $sHandleMan = $aTestingHandleMap[$aItem['apply_father_name']] ?? '芦泽蓬';
            }

            $aDataList[] = [
                'id' => $aItem['id'],
                'risk_type' => $sRiskType,
                'apply_id' => $iApplyId,
                'company_short_name' => $aApply['company_short_name'] ?: $aApply['company_name'],
                'test_status' => $aItem['test_status'],
                'apply_time' => $aItem['apply_time'],
                'start_time' => $aItem['start_time'],
                'return_time' => $aItem['return_time'],
                'father_id' => $aItem['father_id'],
                'apply_father_name' => $aItem['apply_father_name'],
//                'father_name' => $aFatherMap[$aItem['product_id']],
                'handle_man' => $sHandleMan,
                'msg' => $fFormatMsg ? $fFormatMsg($aItem) : '',
            ];
        }

        return $this->buildTree($aDataList);
    }

    /**
     * @param $sStart
     * @param $sEnd
     * @return int
     */
    private function overDay($sStart = '', $sEnd = ''){
        $x = strtotime($sEnd) - strtotime($sStart);

        return (int)($x / 86400);
    }

    /**
     * @param $aDataList
     * @return array|mixed
     */
    private function buildTree($aDataList = []) {
        if (empty($aDataList)) {
            return [];
        }
        $aFirst = array_shift($aDataList);
//        $aFirst['hasChildren'] = true;
        $aFirst['children'] = $aDataList;

        return [$aFirst];
    }
}