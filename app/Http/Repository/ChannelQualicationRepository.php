<?php

namespace App\Http\Repository;

use App\Models\ChannelQualication;
use App\Models\ChannelQualicationDetail;
use App\Models\ChannelQualicationDetailFile;
use App\Models\Crs\SystemUser;
use Exception;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;


class ChannelQualicationRepository
{

    /**
     * 查询渠道资质信息
     * @return array
     */
    public function getChannelQualication()
    {
        $params = request()->all();

        $aWhere = $this->aGetWhere($params);
        $aDetailWhere = $this->aGetDetailWhere($params);

        if ($aDetailWhere) {
            $aDetailList = ChannelQualicationDetail::aGetDataListByCond($aDetailWhere);
            if (empty($aDetailList)) {
                return ['data' => [], 'total' => 0];
            } else {
                $aWhere[] = ['id', 'in', array_unique(array_values(array_column($aDetailList, 'channel_qualication_id')))];
            }
        }
        // 获取分页数据
        $aPageParam = $this->aGetPageParams();
        $iCount = ChannelQualication::getCount($aWhere);
        $aList = ChannelQualication::getAll($aWhere, ['*'], $aPageParam, ['id', 'desc']);
        // 补充详情数据
        $aList = $this->getDetailList($aList);
        return ['list' => $aList, 'total' => $iCount];

    }

    /**
     * @return bool
     * @throws Exception
     */
    public function addChannelQualication()
    {
        $params = request()->all();
        $aWhere = $this->aGetWhere($params);
        $aList = ChannelQualication::getAll($aWhere);
        if (!empty($aList)) {
            throw new Exception('新增渠道已存在,不能重复添加');
        }
        $aParams = $this->aGetInputParams();
        $aParam = $aParams['params'];
        $aDetailParams = $aParams['detailParams'];
        $aCQParams = array_merge($aParam, $this->aGetCreatedAt(), $this->aGetUpdatedAt());

        try {
            // 开启事务
            DB::beginTransaction();
            $insertId = ChannelQualication::add($aCQParams, true);
            $this->saveQualicationDetail($aDetailParams, $insertId);
            DB::commit();
            return true;
        } catch (Exception $e) {
            // 回滚事务（任意一步失败）
            DB::rollBack();
            throw new Exception('新增渠道资质异常:' . $e->getMessage());
        }
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function editChannelQualication()
    {
        $params = request()->all();
        $id = request()->get('id', 0); //渠道资质详情
        if (empty($id)) {
            throw new Exception('编辑渠道资质失败,参数异常');
        }
        $aWhere = $this->aGetWhere($params);
        array_push($aWhere, ['id', '!=', $id]);
        $aChannelQualicationList = ChannelQualication::getAll($aWhere);
        if (!empty($aChannelQualicationList)) {
            throw new Exception('新增渠道已存在,不能重复添加');
        }
        $aParams = $this->aGetInputParams();
        $aParam = $aParams['params'];
        $aDetailParams = $aParams['detailParams'];


        try {
            DB::beginTransaction();
            // 修改渠道资质信息
            $this->updateChannelQualication($id, $aParam);
            // 本次删除资质详情
            $this->delChannelQualicationDetail($id, $aDetailParams);
            // 本次新增或者修改资质详情
            $this->batchUpdateChannelQualicationDetail($id, $aDetailParams);
            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception('编辑渠道资质异常:' . $e->getMessage());
        }
    }

    /**
     * @return string
     * @throws Exception
     */
    public function downQualicationDetailFile()
    {
        $id = request()->get('id', 0);
        if (!$id) {
            throw new Exception('参数异常');
        }
        $aCond = [
            ['id', '=', $id],
        ];
        $aFileInfo = ChannelQualicationDetailFile::getOne($aCond);

        if (!$aFileInfo) {
            throw new Exception('文件不存在');
        }
        return storage_path($aFileInfo['qualication_file']);
    }


    /**
     * @return boolean
     * @throws Exception
     */
    public function delQualicationDetailFile()
    {

        $id = request()->get('id', 0);
        if (!$id) {
            throw new Exception('参数异常');
        }
        $aCond = [
            ['id', '=', $id],
        ];

        ChannelQualicationDetailFile::edit($aCond, ['status' => -1]);
        return true;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function getQualicationOptions()
    {
        $userInfo = SystemUser::getUserInfoMap();
        return [
            'salesmanOptions' => array_values($userInfo),
        ];
    }

    /**
     * 查询渠道资质详情
     * @params  aList
     * @return array
     */
    public function getDetailList($aList)
    {
        if (empty($aList)) {
            return [];
        }

        $ids = array_column($aList, 'id');
        $aCond = [
            ['channel_qualication_id', 'in', $ids],
            ['status', '=', 1]
        ];
        $aDetailList = ChannelQualicationDetail::aGetDataListByCond($aCond);
        $detailIds = array_column($aDetailList, 'id');
        $groupedDetailFiles = [];
        if ($detailIds) { //查询资质文件
            $aCond = [
                ['channel_qualication_detail_id', 'in', $detailIds],
                ['status', '=', 1]
            ];
            $aFileList = ChannelQualicationDetailFile::aGetDataListByCond($aCond);
            foreach ($aFileList as $file) {
                $qualicationDetailId = $file['channel_qualication_detail_id'];
                $qualicationFile = $file['qualication_file'];
                $file['show_url'] = $this->sGetShowUrl($qualicationFile);

                if (!isset($groupedDetailFiles[$qualicationDetailId])) {
                    $groupedDetailFiles[$qualicationDetailId] = [];
                }
                $groupedDetailFiles[$qualicationDetailId][] = $file;
            }
        }
        $groupedDetails = [];
        foreach ($aDetailList as $detail) {
            $qualicationId = $detail['channel_qualication_id'];
            if (!isset($groupedDetails[$qualicationId])) {
                $groupedDetails[$qualicationId] = [];
            }
            $detail['detail_file_list'] = $groupedDetailFiles[$detail['id']] ?? [];
            $groupedDetails[$qualicationId][] = $detail;
        }

        // 合并到主资质列表中
        foreach ($aList as &$qualication) {
            $qualicationId = $qualication['id'];
            $qualication['qualication_detail'] = $groupedDetails[$qualicationId] ?? [];
        }
        unset($qualication);

        return $aList;
    }

    /**
     * @param $aDetailParams
     * @param $insertId
     *
     * @return boolean
     */
    private function saveQualicationDetail($aDetailParams, $insertId)
    {

        if ($aDetailParams) {
            $aDetailFiles = [];
            foreach ($aDetailParams as $index => $aDetailParam) {
                $aDetailInsertParam = [
                    'channel_qualication_id' => $insertId,
                    'qualication_name' => $aDetailParam['qualication_name'],
                    'end_date' => $aDetailParam['end_date'],
                    'is_alarm' => $aDetailParam['is_alarm'],
                    'remark' => $aDetailParam['remark'],
                    'product_description' => $aDetailParam['product_description'],
                    'created_at' => time(),
                    'updated_at' => time(),
                    'status' => 1,
                ];
                $iDetailId = ChannelQualicationDetail::add($aDetailInsertParam, true);

                $fileList = request()->file("qualication_detail.{$index}.qualication_file_list", []);

                if ($fileList && $iDetailId) {
                    $aDetailFiles[$iDetailId] = $fileList;
                }
            }
            if ($aDetailFiles) { //批量插入文件
                $this->saveQualicationDetailFile($aDetailFiles);
            }
        }

        return true;
    }

    /**
     * @param $aDetailFiles
     * @return boolean
     */
    private function saveQualicationDetailFile($aDetailFiles)
    {

        if ($aDetailFiles) {
            $aDetailInsertParams = [];
            foreach ($aDetailFiles as $detailId => $aDetailFileParams) {
                foreach ($aDetailFileParams as $detailFile) {
                    $originalName = $this->sGetFileOriginalName($detailFile);
                    $qualicationFile = $this->sGetFilePath($detailFile);
                    if ($qualicationFile) {
                        $aDetailInsertParam = [
                            'channel_qualication_detail_id' => $detailId,
                            'original_name' => $originalName,
                            'qualication_file' => $qualicationFile,
                            'created_at' => time(),
                            'updated_at' => time(),
                            'status' => 1,
                        ];
                        $aDetailInsertParams[] = $aDetailInsertParam;
                    }
                }
            }
            if ($aDetailInsertParams) {
                ChannelQualicationDetailFile::addAll($aDetailInsertParams);
            }
        }
        return true;
    }

    /**
     * @param $file
     * @return string
     */
    private function sGetFilePath($file)
    {
        if ($file && $file->isValid()) {
            $relativePath = 'app/public/qualications';
            $destinationPath = storage_path($relativePath);
            // 确保目录存在
            if (!file_exists($destinationPath)) {
                mkdir($destinationPath, 0755, true);
            }

            $filename = 'qualication_' . uniqid() . '.' . $file->getClientOriginalExtension();
            $file->move($destinationPath, $filename);

            return $relativePath . '/' . $filename;
        }
        return '';
    }

    /**
     * @param $file
     * @return string
     */
    private function sGetFileOriginalName($file)
    {
        if ($file && $file->isValid()) {
            return $file->getClientOriginalName();
        }
        return '';
    }

    /**
     * @param $params
     * @return array|array[]
     */
    private function aGetDetailWhere($params)
    {
        $where = [];
        isset($params['start_date']) && $params['start_date'] !== '' && $where[] = ['end_date', '>=', $params['start_date']];
        isset($params['end_date']) && $params['end_date'] !== '' && $where[] = ['end_date', '<=', $params['end_date']];
        return $where;
    }

    /**
     * @param $params
     *
     * @return array|array[]
     */
    private function aGetWhere($params)
    {
        $where = [];
        isset($params['channel_name']) && $where = [['channel_name', '=', $params['channel_name']]];
        return $where;
    }

    /**
     * @return array
     */
    private function aGetPageParams()
    {
        $page = request()->get('page', 1);
        $limit = request()->get('limit', 10);
        $offset = ($page - 1) * $limit;
        return ['limit' => $limit, 'offset' => $offset];
    }

    /**
     * @return array
     * @throws Exception
     */
    private function aGetInputParams()
    {
        $channelName = request()->post('channel_name', '');
        $channelFullName = request()->post('channel_fullname', '');
        $salesman = request()->post('salesman', '');
        $isQualicationPassed = request()->post('is_qualication_passed', 0);
        $channelType = request()->post('channel_type', 0);
        $cooperationStatus = request()->post('cooperation_status', 0);
        $qualicationDetail = request()->post('qualication_detail', []);

        if (!$channelName) {
            throw new Exception('参数异常');
        }
        $params = [
            'channel_name' => trim($channelName),
            'channel_fullname' => trim($channelFullName),
            'salesman' => trim($salesman),
            'is_qualication_passed' => $isQualicationPassed,
            'channel_type' => $channelType,
            'cooperation_status' => $cooperationStatus,
        ];

        return ['params' => $params, 'detailParams' => $qualicationDetail];
    }


    /**
     * @return array
     */
    private function aGetCreatedAt()
    {
        return ['created_at' => time()];
    }

    /**
     * @return array
     */
    private function aGetUpdatedAt()
    {
        return ['updated_at' => time()];
    }

    /**
     * @param $path
     * @return string
     *
     */
    public function sGetShowUrl($path)
    {
        $relativePath = Str::after($path, 'app/');
        if (!Storage::exists($relativePath)) {
            return '';
        }
        $url = Storage::url($relativePath);
        return $url;
    }

    /**
     * @param $id
     * @param $params
     */
    protected function updateChannelQualication($id, $params)
    {
        ChannelQualication::where('id', $id)
            ->update(array_merge($params, $this->aGetUpdatedAt()));
    }

    /**
     * @param $id
     * @param $params
     */
    protected function delChannelQualicationDetail($id, $params)
    {
        $aCond = [
            ['channel_qualication_id', '=', $id],
        ];
        if (!empty($params)) {
            $ids = array_column($params, 'id');
            $aCond[] = ['id', 'notIn', $ids];
        }

        $aDetailList = ChannelQualicationDetail::aGetDataListByCond($aCond);
        if ($aDetailList) {
            $delIds = array_column($aDetailList, 'id');
            $aCond = [
                ['id', 'in', $delIds],
            ];
            ChannelQualicationDetail::edit($aCond, ['status' => -1]); // 删除资质详情

            $aCond = [
                ['channel_qualication_detail_id', 'in', $delIds],
            ];
            ChannelQualicationDetailFile::edit($aCond, ['status' => -1]); // 删除资质文件
        }
    }

    /**
     * @param $channelQualicationId
     * @param $params
     */
    protected function batchUpdateChannelQualicationDetail($channelQualicationId, $params)
    {
        $editDetailDatas = [];
        $aDetailFiles = [];
        foreach ($params as $index => $aDetailParam) {
            $id = $aDetailParam['id'];
            $detailParam = [
                'channel_qualication_id' => $channelQualicationId,
                'qualication_name' => $aDetailParam['qualication_name'],
                'end_date' => $aDetailParam['end_date'],
                'is_alarm' => $aDetailParam['is_alarm'],
                'remark' => $aDetailParam['remark'],
                'product_description' => $aDetailParam['product_description'],
                'updated_at' => time(),
                'status' => 1,
            ];

            $fileList = request()->file("qualication_detail.{$index}.qualication_file_list", []);
            if ($id) { // 批量更新
                $detailParam['id'] = $id;
                $insertId = $id;
                $editDetailDatas[] = $detailParam;
            } else {
                $insertId = ChannelQualicationDetail::add($detailParam, true);
            }

            if ($fileList && $insertId) {
                $aDetailFiles[$insertId] = $fileList;
            }
        }
        if ($editDetailDatas) { // 更新
            ChannelQualicationDetail::batchUpdateData($editDetailDatas);
        }

        if ($aDetailFiles) { // 插入文件
            $this->saveQualicationDetailFile($aDetailFiles);
        }
    }
}
