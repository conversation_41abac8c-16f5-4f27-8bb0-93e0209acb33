<?php

namespace App\Http\Repository;

use App\Models\BillCost;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Models\CustomerRealValid;
use App\Providers\RedisCache\RedisCache;
use App\Http\Controllers\StatProductController;
use App\Utils\Helpers\Func;
use Hamcrest\Util;
use App\Models\Account;
use App\Models\BillProductIncomeV2;
use App\Models\CustomerExpend;
use App\Models\ClickHouse\RequestProductLog;

class StatCustomerRepository extends StatBaseRepository
{
    /**
     * 数据统计-主产品统计-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
            if (!$params['father_id']) throw new \Exception("缺少主产品ID");
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $show_money = $this->checkUserProductAuth($params['father_id'],'money');  //是否有权限查看产品金额
        $show_cost = $this->checkUserProductAuth($params['father_id'],'cost');  //是否有权限查看产品金额
        $show_cache = $this->checkUserProductAuth($params['father_id'],'cache');  //是否有权限查看产品缓存

        $usage_data = $this->getUsage($params); //调用量
        //var_dump($usage_data);
        $data_main = $this->getMainUsage($params);  //主接口调用量
        //dd($usage_data);
        //号码分,贷后风险指数 去ck查询一下真实查得量,计算真实查得率
        //且运营商暂时不作为条件 因为号码分等产品没渠道 如果是邦信分等产品记得在model里面补充运营商转化条件
        if(in_array($params['father_id'], [10000, 20000])){
            $ck_data = $this->getDataFromCk($params);
            $data = $this->formatUsageDataAndCk($usage_data, $data_main, $show_money,$show_cache, $ck_data);  //格式化数据
        }else{
            $data = $this->formatUsageData($usage_data, $data_main, $show_money,$show_cache);  //格式化数据
        }

        $income_data = $this->getIncome($params);   //收入
        $cost_data = $this->getCost($params);   //成本
        $customer_adjust_cost = $this->getAdjustCost($params); //客户成本调整
        $fixed_cost = $this->getFixedCost($params); //固定费用成本
        $channel_adjust_cost = $this->getAdjustChannelCost($params); //渠道成本调整(其实叫成本调整更符合业务,由于历史原因，命名的语义不太符合业务)
        $expend_data = $this->getExpend($params);   //特殊消耗
        $data = $this->formatData($data, $income_data, $cost_data, $expend_data, compact('show_money','show_cost','show_cache'), $customer_adjust_cost, $fixed_cost, $channel_adjust_cost);

        return $data;
    }

    //固定费用成本
    protected function getFixedCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountFixedFee::getChannelCostByCustomer($where, $params);
        $result = [];
        foreach($cost_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $total_money  = $result[$customer_id]['money'] ?? 0;
            $result[$customer_id]['money'] = bcadd($total_money, $item['money'], $this->degree);
            $result[$customer_id]['customer_id'] = $customer_id;
        }
        return $result;
    }

    //渠道成本调整
    protected function getAdjustChannelCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);

        $cost_data = ChannelAccountAdjust::getChannelCostByCustomer($where, $params);
        return $cost_data;

    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @param $show_money
     * @return mixed
     * @throws \Exception
     */
    private function formatData($usage_data, $income_data, $cost_data, $expend_data, $show_auth, $customer_adjust_cost = [], $fixed_cost = [], $channel_adjust_cost = [])
    {

        //信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
        $income_data = $this->transProduct($income_data);
        foreach ($income_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['number'] = $item['number'];
            $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['money'] = $show_auth['show_money'] ? $item['money'] : '-';
            $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cost'] = $show_auth['show_cost'] ? 0 : '-';
            if(isset($item['cache'])) {
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cache'] = $show_auth['show_cache'] ? $item['cache'] : '-';
            }
        }
        if ($show_auth['show_cost']) {
            $cost_data = $this->transProduct($cost_data);
            foreach ($cost_data as $item){
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cost'] = $item['money'];
            }
        }
        foreach ($expend_data['add'] as $item) {
            if (!isset($usage_data[$item['customer_id']]['特殊消耗'])) {
                $usage_data[$item['customer_id']]['特殊消耗'][0][0]['number'] = 0;
                $usage_data[$item['customer_id']]['特殊消耗'][0][0]['money'] = $show_auth['show_money'] ? 0 : '-';
            }
            $usage_data[$item['customer_id']]['特殊消耗'][0][0]['number'] += $item['fee_number'];
            if($show_auth['show_money']) $usage_data[$item['customer_id']]['特殊消耗'][0][0]['money'] += $item['money'];
        }
        foreach ($expend_data['sub'] as $item) {
            if (!isset($usage_data[$item['customer_id']]['特殊消耗'])) {
                $usage_data[$item['customer_id']]['特殊消耗'][0][0]['number'] = 0;
                $usage_data[$item['customer_id']]['特殊消耗'][0][0]['money'] = $show_auth['show_money'] ? 0 : '-';
            }
            $usage_data[$item['customer_id']]['特殊消耗'][0][0]['number'] -= $item['fee_number'];
            if($show_auth['show_money']) $usage_data[$item['customer_id']]['特殊消耗'][0][0]['money'] -= $item['money'];
        }

            foreach ($customer_adjust_cost as $item) {
                $apikey = 'customer_adjust';//在前端字典已经做了该key映射
                $product_id = 'NO';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['cost'] = $show_auth['show_cost']?$item['money']:'-';
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['money'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['operator'] = $operator;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['product_id'] = $product_id;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['success'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['total'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['valid'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['cache'] = $show_auth['show_cost']?0:'-';
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['apikey'] = $apikey;
            }

            //固定费用成本
            foreach ($fixed_cost as $item) {
                $apikey = 'fixed_adjust';//在前端字典已经做了该key映射
                $product_id = 'NO';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['cost'] = $show_auth['show_cost']?$item['money']:'-';
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['money'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['operator'] = $operator;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['product_id'] = $product_id;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['success'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['total'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['valid'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['cache'] = $show_auth['show_cost']?0:'-';
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['apikey'] = $apikey;
            }

            //渠道成本调整
            foreach ($channel_adjust_cost as $item) {
                $apikey = 'channel_adjust';//在前端字典已经做了该key映射
                $product_id = 'NO';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['cost'] = $show_auth['show_cost']?$item['money']:'-';
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['money'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['operator'] = $operator;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['product_id'] = $product_id;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['success'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['total'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['valid'] = 0;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['cache'] = $show_auth['show_cost']?0:'-';;
                $usage_data[$item['customer_id']][$apikey][$product_id][$operator]['apikey'] = $apikey;
            }
        
        return $usage_data;
    }

    protected function formatUsageData($data_son, $data_father, $show_money, $show_cache)
    {

        $data_son = $this->transProduct($data_son);

        //dd($data_son);
        $usage_data = [];
        foreach ($data_son as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            //$item['product_id'] = $this->transProduct($customer_id, $item['product_id']);

            $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']] = $item;
            if (!$show_money) {
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['money'] = '-';
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cost'] = '-';
            }
            if (!$show_cache) {
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cache'] = '-';
            }
        }
        //dd($usage_data);
        foreach ($data_father as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $usage_data[$customer_id][$item['apikey']]['main'][$item['operator']] = $item;
        }
        return $usage_data;
    }


    /**
     * 月度数据统计-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statMonthList($params)
    {
        //设置返回表格的表头
        $arr['result']['header']['list'] = [
            "customer_name" => "客户名称",
            "rights_income" => "权责收入",
            "cost" => "数据成本",
            "rights_profit" => "权责毛利",
            "profit_rate" => "毛利率"
        ];

        $income_data = Account::getCustomerMonthIncome($params);//客户月度权责收入数据
        $cost_data = Account::getCustomerMonthCost($params);//客户月度成本数据
        //客户月度平账数据,分加减两部分
        $expend_data_sub = CustomerExpend::getCustomerMonthExpend(array_merge($params, ['type' => 1]));
        $expend_data_add = CustomerExpend::getCustomerMonthExpend(array_merge($params, ['type' => 2]));
        //返回全量且不重复的所有客户
        $customer_arr = $this->filterDataByColumn([$income_data, $cost_data, $expend_data_sub, $expend_data_add], 'customer_id');
        if(empty($customer_arr)){
            $arr['result']['items'] = [];
            return $arr;
        }

        //根据想要的key从新组装数据
        $income_data_tmp = $this->addDataKey($income_data, 'customer_id');
        $cost_data_tmp = $this->addDataKey($cost_data, 'customer_id');
        $expend_data_sub_tmp = $this->addDataKey($expend_data_sub, 'customer_id');
        $expend_data_add_tmp = $this->addDataKey($expend_data_add, 'customer_id');

        $total_rights_income = 0;
        $total_cost = 0;
        $total_rights_profit = 0;
        foreach ($customer_arr as $customer_id){
            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
            //权责收入 = 流水 + 消耗type=2 - 消耗type=1
            $income = $income_data_tmp[$customer_id]['s_money'] ?? 0;
            $expend_add = $expend_data_add_tmp[$customer_id]['s_money'] ?? 0;
            $expend_sub = $expend_data_sub_tmp[$customer_id]['s_money'] ?? 0;
            $rights_income = bcsub(bcadd($income, $expend_add, $this->degree), $expend_sub, $this->degree);
            $cost = $cost_data_tmp[$customer_id]['s_money'] ?? 0;
            $rights_profit = bcsub($rights_income, $cost, $this->degree);
            if($rights_income > 0){
                $profit_rate = bcdiv(bcmul($rights_profit, 100, $this->degree), $rights_income, 2) . '%';
            }else{
                $profit_rate = '0.00%';
            }
            $item = [
                'customer_name' => $customer_name,
                'rights_income_sort' => $rights_income,
                'rights_income' => number_format($rights_income, 2),
                'cost' => number_format($cost, 2),
                'rights_profit' => number_format($rights_profit, 2),
                'profit_rate' => $profit_rate,
            ];
            $arr['result']['items'][] = $item;

            $total_rights_income = bcadd($total_rights_income, $rights_income, $this->degree);
            $total_cost = bcadd($total_cost, $cost, $this->degree);
            $total_rights_profit = bcadd($total_rights_profit, $rights_profit, $this->degree);
        }

        $rights_income_sort = array_column($arr['result']['items'],'rights_income_sort');
        array_multisort($rights_income_sort,SORT_DESC, $arr['result']['items']);

        if(!empty($customer_arr)){
            if($total_rights_income > 0){
                $total_profit_rate = bcdiv(bcmul($total_rights_profit, 100, $this->degree), $total_rights_income, 2);
            }else{
                $total_profit_rate = '0.00';
            }
            array_unshift($arr['result']['items'], [
                'customer_name' => '总计',
                'rights_income' => number_format($total_rights_income, 2),
                'cost' => number_format($total_cost, 2),
                'rights_profit' => number_format($total_rights_profit, 2),
                'profit_rate' => $total_profit_rate . '%',
            ]);
        }

        return $arr;
    }


    public function getDataFromCk($params){

        $where['start_date'] = $params['start_date'];
        $where['end_date'] = $params['end_date'];
        $where['father_id'] = $params['father_id'];
        $where['product_id'] = $params['product_id'] ?? '';
        $where['apikey_list'] = $params['apikey_list'] ?? '';
        $where['filter_apikey'] = $params['filter_apikey'] ?? '';
        $where['product_list'] = $params['product_list'] ?? '';
        if(in_array($params['father_id'], [10000, 20000])){//号码分等产品没有渠道
            $where['operator'] = '';
        }else{
            $where['operator'] = $params['operator'] ?? '';
        }

        $tmp = [];
        $data = CustomerRealValid::getCustomerData($where);
        foreach ($data as $item){
            $tmp[$item['apikey']][$item['product_id']]['valid_real_total'] = $item['valid_real_total'];
            $tmp[$item['apikey']][$item['product_id']]['valid_real_success'] = $item['valid_real_success'];
        }

        return $tmp;
    }

    public function getDataFromCkBak($params){

        $where['start_date'] = $params['start_date'];
        $where['end_date'] = $params['end_date'];
        $where['father_id'] = $params['father_id'];
        $where['product_id'] = $params['product_id'] ?? '';
        $where['apikey_list'] = $params['apikey_list'] ?? '';
        $where['filter_apikey'] = $params['filter_apikey'] ?? '';
        $where['product_list'] = $params['product_list'] ?? '';
        if(in_array($params['father_id'], [10000, 20000])){//号码分等产品没有渠道
            $where['operator'] = '';
        }else{
            $where['operator'] = $params['operator'] ?? '';
        }

        $rpModel = new RequestProductLog();
        $real_total = $rpModel->getDataRealValid($where, 'valid_real_total');
        $real_success = $rpModel->getDataRealValid($where, 'valid_real_success');
        $tmp = [];
        foreach($real_total as $item){
            $tmp[$item['apikey']][$item['pids']]['valid_real_total'] = $item['valid_real_total'];
        }

        foreach($real_success as $item){
            $tmp[$item['apikey']][$item['pids']]['valid_real_success'] = $item['valid_real_success'];
        }

        return $tmp;

    }

    protected function formatUsageDataAndCk($data_son, $data_father, $show_money, $show_cache, $ck_data)
    {
        $usage_data = [];
        foreach ($data_son as &$item){
            $item['valid_real_total'] = $ck_data[$item['apikey']][$item['product_id']]['valid_real_total']??0;
            $item['valid_real_success'] = $ck_data[$item['apikey']][$item['product_id']]['valid_real_success']??0;

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']] = $item;
            if (!$show_money) {
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['money'] = '-';
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cost'] = '-';
            }
            if (!$show_cache) {
                $usage_data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cache'] = '-';
            }
        }
        foreach ($data_father as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $usage_data[$customer_id][$item['apikey']]['main'][$item['operator']] = $item;
        }
        return $usage_data;
    }


    //客户产品首次产生权责收入
    public function customerFirstIncome()
    {
        //set_time_limit(180);
        $params = request()->post();

        try {
            if (!$params['month']) throw new \Exception("缺少查看日期");
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        [$start_date, $end_date] = $this->getDateRange($params['limit_month'], $params['month']);

        $list = BillProductIncomeV2::getMonthIncome($start_date, $end_date, $params);

        $customer_tmp = [];
        $customer_month_tmp = [];
        //$customer_id_apikey = [];
        foreach($list as $key => $val){
            if($val['s_money'] == 0){
                continue;
            }
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
            //$customer_id_apikey[$customer_id][] = $val['apikey'];
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($val['product_id']);
            $total_money = $customer_tmp[$customer_id][$father_id][$val['product_id']][$val['s_month']]['total_money']??0;
            $customer_tmp[$customer_id][$father_id][$val['product_id']][$val['s_month']]['total_money'] = bcadd($total_money, $val['s_money'], $this->degree);
            $unique_key = $customer_id.'_'.$father_id.'_'.$val['product_id'];
            if(isset($customer_month_tmp[$unique_key]['month']) && $customer_month_tmp[$unique_key]['month'] != $val['s_month']){
                continue;//去除月不一致的数据 如$unique_key有3月的了,后面$unique_key在有5月的不需要
            }else{
                $customer_month_tmp[$unique_key]['month'] = $val['s_month'];
            }
        }

        //dd($customer_month_tmp);
        $first_arr = [];
        $sort_key = 0;
        foreach($customer_month_tmp as $key => $val){
            $item = explode('_', $key);
            $customer_id = $item[0];
            $father_id = $item[1];
            $product_id = $item[2];
            $month = $val['month'];

            //判断是否首次全责收入
            $customer_apikeys = [];//Account::getApikeysByCustomerIdsNew([$customer_id]);
            $firstIncome = BillProductIncomeV2::checkFirstIncome($start_date, $customer_apikeys);
            if(empty($firstIncome)){//首次
                //$sort_key = $customer_id.'_'.$product_id;
                $first_arr[$sort_key]['customer_id'] = $customer_id;
                $first_arr[$sort_key]['father_id'] = $father_id;
                $first_arr[$sort_key]['product_id'] = $product_id;

                $first_arr[$sort_key]['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
                $first_arr[$sort_key]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
                $first_arr[$sort_key]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($product_id);
                $first_arr[$sort_key]['s_month'] = $month;
                //正常收入
                $income = $customer_tmp[$customer_id][$father_id][$product_id][$month]['total_money'];
                $condition['customer_id'] = $customer_id;
                $condition['father_id'] = $father_id;
                $condition['product_id'] = $product_id;
                $condition['s_date'] = $month.'01';
                $condition['e_date'] = $this->getLastDay($month.'01');
                //特殊消耗
                $incomesub = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 1]));
                $incomeadd = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 2]));
                $incomesub = $incomesub[0]['s_money'] ?? 0;
                $incomeadd = $incomeadd[0]['s_money'] ?? 0;
                $first_arr[$sort_key]['income'] = bcsub(bcadd($income, $incomeadd, $this->degree), $incomesub, $this->degree);

                $condition2['apikey'] = $customer_apikeys;
                $condition2['product_id'] = $product_id;
                $condition2['s_date'] = $condition['s_date'];
                $condition2['e_date'] = $condition['e_date'];
                //普通成本
                $cost = BillCost::getBetweenDate($condition2);
                $cost = $cost[0]['s_money'] ?? 0;

                $condition3['customer_id'] = $customer_id;
                $condition3['product_id'] = $product_id;
                $condition3['s_date'] = $condition['s_date'];
                $condition3['e_date'] = $condition['e_date'];
                //客户成本调整
                $adjust_cost = CustomerBillAdjust::getBetweenDate($condition3);
                $adjust_cost = $adjust_cost[0]['s_money'] ?? 0;
                $first_arr[$sort_key]['cost'] = bcadd($cost, $adjust_cost, $this->degree);

                /*
                $customer_info = Customer::getCustomerInfo($customer_id);
                $first_arr[$sort_key]['salesman'] = $customer_info['salesman'];
                $sys_user = SystemUser::getPhoneByUserName($customer_info['salesman']);
                $dept_info = SystemDept::getInfoByDeptId($sys_user['dept_id']);
                $first_arr[$sort_key]['area'] = $dept_info['dept_name'];

                */
                $sort_key++;
            }

        }
        //dd($first_arr);

        return ['data' => $first_arr, 'count' => count($first_arr)];
    }

    public function getDateRange($limit = 1, $month){
        $end_date = $this->getLastDay($month.'-01');//查看日期所在月的最后一天
        if($limit == 1){//限定月份
            $start_date = date('Ymd', strtotime($month.'-01'));
        }else{
            $start_date = $this->getMonth(11, str_replace('-', '', $month)).'01';
        }

        return [$start_date, $end_date];
    }



}
