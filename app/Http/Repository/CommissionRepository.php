<?php

namespace App\Http\Repository;

use App\Models\SystemUser;
use App\Models\SalesCommissionRunRecord;

class CommissionRepository
{
    public function getCommissionTaskList()
    {
        $where = $this->getParams();
        $page = intval(request()->post('page', 1));
        $limit = intval(request()->post('limit', 10));
        $data = SalesCommissionRunRecord::getList($where, (($page-1)*$limit), $limit);
        $user_data = SystemUser::where(['disabled' => 1])->pluck('realname', 'username');
        try {
            $list = [];
            foreach ($data['list'] as $value) {
                $info = [];
                $info['run_id'] = $value['run_id'];
                $info['month']=$value['month'];
                $info['run_status']=$value['run_status'];
                $info['limit_month'] = $value['limit_month'];
                $info['remarks']=$value['remarks'];
                $info['admin'] = isset($user_data[$value['admin']]) ? $user_data[$value['admin']] : '';
                $info['create_at']=$value['create_at'];
                $info['update_at']=$value['update_at'];
                $list[] = $info;
            }
        } catch (\Exception $e) {
        }
        $count = $data['count'];
        return compact('list', 'count');
    }

    /**
     * 获取查询条件
     * @return array
     */
    public function getParams()
    {
        $parmas = [];
        $month = request()->post('month', null);
        $run_status = request()->post('run_status', null);
        if (!empty($month)) {
            $parmas['month'] = $month;
        }
        if ($run_status>0) {
            $parmas['run_status'] = $run_status;
        }
        $parmas['is_delete'] = 0;
        return $parmas;
    }


    public function saveCommissionTask($sysName = null)
    {
        $params = request()->post();
        if (!isset($sysName)) {
            return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
        }

        return SalesCommissionRunRecord::addRecord($params, $sysName);
    }
    
    public function deleteCommissionTask($sysName = null)
    {
        $run_id = request()->post('run_id', '');
        if (!isset($sysName)) {
            return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
        }

        return SalesCommissionRunRecord::delRecord($run_id, $sysName);
    }

    public function getRunStatus()
    {
        $run_id = request()->post('run_id', '');
        if (empty($run_id)) {
            return false;
        } else {
            return  SalesCommissionRunRecord::where('run_id', $run_id)->value('run_status');
        }
    }


    public function getRunFile()
    {
        $run_id = request()->post('run_id', '');
        $type = request()->post('type', 0);//0总表 1明细表
        $month = SalesCommissionRunRecord::where('run_id', $run_id)->value('month');

        if ($type==0) {
            $name = "客户提成总表_".$month.".csv";
        } else {
            $name ="客户提成明细_".$month.".csv";
        }
        try {
            $fileCtx = file_get_contents(storage_path().'/logs/'.$name);
            header("Content-type:application/csv");
            header('Content-Disposition:attachment; filename="'.$name.'"');
            header("Content-Type: application/force-download");
            header("Content-Type: application/octet-stream");
            header("Content-Type: application/download");
            echo $fileCtx;
            exit;
        } catch (Exception $e) {
            echo $e->getMessage();
            exit;
        }
    }
}
