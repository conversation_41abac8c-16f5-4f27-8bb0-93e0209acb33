<?php

namespace App\Http\Repository;


use App\Exports\UpstreamShortcutDayExport;
use App\Exports\UpstreamShortcutDetailExport;
use App\Exports\UpstreamShortcutExport;
use App\Exports\UpstreamVerificationDayExport;
use App\Exports\UpstreamVerificationDetailsExport;
use App\Models\MongoUpstreamShortcutStatistic;
use App\Models\MongoUpstreamVerificationStatistic;
use App\Models\Product;
use App\Support\CustomException;
use Maatwebsite\Excel\Facades\Excel;

class UpstreamRepository
{
    /** @var int 邦信分快捷版的产品ID */
    private $product_id_shortcut = 210;

    /** @var array 邦信分快捷版的渠道统计配置 */
    private $shrotcut_channel_stat = [];

    /** @var int 邦妙验的产品ID */
    private $product_id_verification = 200;

    /** @var array  邦妙验的渠道统计配置 */
    private $verification_channel_stat = [];

    /**
     * 上游 邦妙验详情下载
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function verificationDetailsExcel()
    {
        $list = request()->post('list');

        return Excel::download(new UpstreamVerificationDetailsExport($list), 'export.xlsx');
    }

    /**
     * 上游 邦妙验列表按天下载
     * @throws CustomException
     */
    public function verificationDayExcel()
    {
        // 获取列表
        $list = $this->_genListForVerificationDayExcel();

        // 生成excel
        return Excel::download(new UpstreamVerificationDayExport($list), 'export.xlsx');
    }

    /**
     * 获取列表
     * @throws CustomException
     * @return array
     */
    private function _genListForVerificationDayExcel(): array
    {
        // 校验参数
        $this->_validateParamsForDayVerificationExcel();

        // 生成pipeline参数
        $pipeline = $this->_genParamsForVerificationDayExcel();

        // 获取列表
        return $this->_getStatListForVerificationDayExcel($pipeline);
    }

    /**
     * 获取列表
     * @param array $pipeline
     * @return array
     */
    private function _getStatListForVerificationDayExcel(array $pipeline): array
    {
        // 总计
        $total = $this->_genTotalForVerification();

        // 总量
        $total['success'] = 0;

        $lists = MongoUpstreamVerificationStatistic::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->map(function ($item) use (&$total) {
            $item = $this->_formatStatItemForVerificationDayExcel($item);

            // 计算总量
            $total = $this->_sumTotalForVerification($item, $total);

            // 格式化数据
            return $item;
        })->all();

        //
        $total['product_name'] = '总计';
        array_unshift($lists, $total);
        return $lists;
    }


    /**
     * 格式化单元
     * @param $item
     * @return mixed
     */
    private function _formatStatItemForVerificationDayExcel($item)
    {
        // 产品
        $item->product_id = $item->_id->product_id;
        $item->product_name = $this->_getProductNameById($item->product_id);

        // 格式化upstream
        $item->upstream = $item->_id->upstream;
        $item->upstream_cn = $this->verification_channel_stat['upstream'][$item->upstream];

        // 计算有效调用量
        list($dx_succ, $yd_succ, $lt_succ) = [
            $item->dx_succ ?? 0,
            $item->yd_succ ?? 0,
            $item->lt_succ ?? 0
        ];
        $item->success = $dx_succ + $lt_succ + $yd_succ;

        // 日期
        $item->day = $item->_id->day;

        unset($item->_id);
        return $item;
    }


    /**
     * 生成pipeline参数
     * @return array
     */
    private function _genParamsForVerificationDayExcel(): array
    {
        return [
            ['$match' => $this->_genMatchPipelineForVerification()],
            ['$group' => $this->_genGroupPipelineVerificationDayExcel()]
        ];
    }

    /**
     * 分组
     * @return array
     */
    private function _genGroupPipelineVerificationDayExcel()
    {
        $pipeline_group = [
            '_id' => ['product_id' => '$product_id', 'upstream' => '$upstream', 'day' => '$day'],
        ];

        // 获取当前统计字段
        $product_id = $this->product_id_verification;
        $product = Product::findOneItem(compact('product_id'), 'channel_stat');
        $this->verification_channel_stat = $channel_stat = json_decode($product['channel_stat'], true);

        array_walk($channel_stat['statistic_field'], function ($intro, $field) use (&$pipeline_group) {
            $pipeline_group[$field] = [
                '$sum' => '$data.' . $field
            ];
        });

        return $pipeline_group;
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamsForDayVerificationExcel()
    {
        $this->_validateDate();
    }

    /**
     *  上游 邦妙验详情
     * @param int $product_id
     * @return array
     * @throws CustomException
     */
    public function verificationDetails(int $product_id): array
    {
        // 参数校验
        $this->_validateParamsForVerificationDetails();

        // 生成参数
        $pipeline_group = $this->_genPipelineForVerificationDetails($product_id);

        // 获取列表
        return $this->_genStatListForVerificationDetail($pipeline_group);
    }


    /**
     * 获取列表
     * @param array $pipeline_group
     * @return array
     */
    private function _genStatListForVerificationDetail(array $pipeline_group): array
    {
        // 总计
        $total = $this->_genTotalForVerification();

        // 总量
        $total['success'] = 0;

        $lists = MongoUpstreamVerificationStatistic::raw(function ($collection) use ($pipeline_group) {
            return $collection->aggregate($pipeline_group);
        })->map(function ($item) use (&$total) {
            $item = $this->_formatItemForVerificationDetails($item);

            // 计算总量
            $total = $this->_sumTotalForShortcuts($item, $total);
            return $item;
        })->all();

        $total['day'] = '总计';
        array_unshift($lists, $total);
        return $lists;
    }

    /**
     * 格式化
     * @param $item
     * @return mixed
     */
    private function _formatItemForVerificationDetails($item)
    {
        $item->day = $item->_id;

        // 计算有效调用量
        list($dx_succ, $yd_succ, $lt_succ) = [
            $item->dx_succ ?? 0,
            $item->yd_succ ?? 0,
            $item->lt_succ ?? 0
        ];
        $item->success = $dx_succ + $lt_succ + $yd_succ;
        unset($item->_id);
        return $item;
    }

    /**
     * 生成参数
     * @param string $field
     * @return array
     */
    private function _genPipelineForVerificationDetails(string $field): array
    {
        return [
            [
                '$match' => $this->_genMatchForVerificationDetails($field)
            ],
            [
                '$group' => $this->_genGroupPipelineForVerificationDetail()
            ]
        ];
    }

    /**
     * 分组依据
     */
    private function _genGroupPipelineForVerificationDetail()
    {
        $pipeline_group = ['_id' => '$day'];

        // 获取当前统计字段
        $product_id = $this->product_id_verification;
        $product = Product::findOneItem(compact('product_id'), 'channel_stat');
        $this->verification_channel_stat = $channel_stat = json_decode($product['channel_stat'], true);

        array_walk($channel_stat['statistic_field'], function ($intro, $field) use (&$pipeline_group) {
            $pipeline_group[$field] = [
                '$sum' => '$data.' . $field
            ];
        });

        return $pipeline_group;
    }

    /**
     * 限制
     * @param string $product_id
     * @return array
     */
    private function _genMatchForVerificationDetails(string $product_id): array
    {
        list($day, $upstream, $node, $product_id) = [
            $this->_genDayParamsForShortcutsDetail(),
            trim(request()->get('upstream')),
            trim(request()->get('node')),
            (int)$product_id
        ];

        return array_filter(compact('day', 'product_id', 'upstream', 'node'), function ($item) {
            return $item;
        });
    }

    /**
     * 参数校验
     * @throws CustomException
     */
    private function _validateParamsForVerificationDetails()
    {
        // 校验时间
        $this->_validateDate();
    }


    /**
     * 上游 邦妙验列表
     * @throws CustomException
     * @return array
     */
    public function verification(): array
    {
        // 校验参数
        $this->_validateParamsForVerification();

        // 生成参数
        $params = $this->_genPipelineParamsForVerification();

        // 获取列表
        return $this->_getStatListForVerification($params);
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     */
    private function _getStatListForVerification(array $params): array
    {
        // 总计
        $total = $this->_genTotalForVerification();

        // 总量
        $total['success'] = 0;

        $lists = MongoUpstreamVerificationStatistic::raw(function ($collection) use ($params) {
            return $collection->aggregate($params);
        })->map(function ($item) use (&$total) {
            $item = $this->_formatStatItemForVerification($item);

            // 计算总量
            $total = $this->_sumTotalForVerification($item, $total);

            // 格式化数据
            return $item;
        })->all();

        //
        $total['product_name'] = '总计';
        array_unshift($lists, $total);
        return $lists;
    }

    /**
     * 邦妙验合计
     * @param $item
     * @param array $total
     * @return array
     */
    private function _sumTotalForVerification($item, array $total): array
    {
        return $this->_sumTotalForUpstream($item, $total);
    }


    /**
     * 格式化单元
     * @param $item
     * @return mixed
     */
    private function _formatStatItemForVerification($item)
    {
        // 产品
        $item->product_id = $item->_id->product_id;
        $item->product_name = $this->_getProductNameById($item->product_id);

        // 格式化upstream
        $item->upstream = $item->_id->upstream;
        $item->upstream_cn = $this->verification_channel_stat['upstream'][$item->upstream];

        // 计算有效调用量
        list($dx_succ, $yd_succ, $lt_succ) = [
            $item->dx_succ ?? 0,
            $item->yd_succ ?? 0,
            $item->lt_succ ?? 0
        ];
        $item->success = $dx_succ + $lt_succ + $yd_succ;

        unset($item->_id);
        return $item;
    }

    /**
     * 产品名称
     * @param int $product_id
     * @return string
     */
    private function _getProductNameById(int $product_id): string
    {
        $product = Product::findOneItem(compact('product_id'), 'product_name');
        return $product['product_name'] ?? '没有找到对应的product_id';

    }

    /**
     * 总量
     * @return array
     */
    private function _genTotalForVerification(): array
    {
        $interface_statistic_field = array_keys($this->verification_channel_stat['statistic_field']);
        $total = array_fill_keys($interface_statistic_field, 0);
        return $total;
    }

    /**
     * 生成参数
     * @return array
     */
    private function _genPipelineParamsForVerification(): array
    {
        return [
            ['$match' => $this->_genMatchPipelineForVerification()],
            ['$group' => $this->_genGroupPipelineVerification()]
        ];
    }

    /**
     * 分组的依据
     */
    private function _genGroupPipelineVerification(): array
    {
        $pipeline_group = [
            '_id' => ['product_id' => '$product_id', 'upstream' => '$upstream'],
        ];

        // 获取当前统计字段
        $product_id = $this->product_id_verification;
        $product = Product::findOneItem(compact('product_id'), 'channel_stat');
        $this->verification_channel_stat = $channel_stat = json_decode($product['channel_stat'], true);

        array_walk($channel_stat['statistic_field'], function ($intro, $field) use (&$pipeline_group) {
            $pipeline_group[$field] = [
                '$sum' => '$data.' . $field
            ];
        });

        return $pipeline_group;
    }


    /**
     * 限制条件
     * @return array
     */
    private function _genMatchPipelineForVerification(): array
    {
        list($day, $upstream, $product_id, $node) = [
            $this->_genDayParamsForVerification(),
            trim(request()->get('upstream')),
            (int)trim(request()->get('product_id')),
            trim(request()->get('node')),
        ];

        return array_filter(compact('day', 'product_id', 'upstream', 'node'), function ($item) {
            return $item;
        });
    }

    /**
     * 生成参数
     */
    private function _genDayParamsForVerification(): array
    {
        return $this->_genDayLimitForUpstream();
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamsForVerification()
    {
        // 校验时间
        $this->_validateDate();
    }


    /**
     * 邦信分快捷版上游数据通详情导出excel
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function shortcutsDetailsExcel()
    {
        $list = request()->post('list');
        return Excel::download(new UpstreamShortcutDetailExport($list), 'export.xlsx');
    }

    /**
     * 按天导出excel
     * @throws CustomException
     */
    public function shortcutsDayExcel()
    {
        // 获取列表
        $list = $this->_genListForDayShortcutExcel();

        // 导出excel
        return Excel::download(new UpstreamShortcutDayExport($list), 'export.xlsx');
    }

    /**
     * 获取列表
     * @return array
     * @throws CustomException
     */
    private function _genListForDayShortcutExcel(): array
    {
        // 校验参数
        $this->_validateParamsForDayShortcutExcel();

        // 生成参数
        $pipeline = $this->_genPipelineParamsForShortcutsDayExcel();

        // 获取列表
        return $this->_getStatListForShortcutDayExcel($pipeline);
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     */
    private function _getStatListForShortcutDayExcel(array $params): array
    {
        // 总计
        $total = $this->_genTotalForShortcut();

        $lists = MongoUpstreamShortcutStatistic::raw(function ($collection) use ($params) {
            return $collection->aggregate($params);
        })->map(function ($item) use (&$total) {
            // 计算总量
            $total = $this->_sumTotalForShortcuts($item, $total);

            // 格式化数据
            return $this->_formatStatItemForShortcutDayExcel($item);
        })->all();

        //
        $total['field_cn'] = '总计';
        array_unshift($lists, $total);
        return $lists;
    }

    /**
     * 格式化
     */
    private function _formatStatItemForShortcutDayExcel($item)
    {
        // 格式化upstream
        $item->upstream = $item->_id->upstream;
        $item->upstream_cn = $this->shrotcut_channel_stat['upstream'][$item->upstream];
        $item->day = $item->_id->day;

        // 格式化field
        $item->field = $item->_id->field;
        $item->field_cn = $this->shrotcut_channel_stat['interface_item'][$item->upstream][$item->field] ?? '没有找到对应的字段';

        unset($item->_id);
        return $item;
    }

    /**
     * 生成参数
     * @return array
     */
    private function _genPipelineParamsForShortcutsDayExcel()
    {
        return [
            ['$match' => $this->_genMatchPipelineForShortcutsDayExcel()],
            ['$group' => $this->_genGroupPipelineForShortcutsDayExcel()]
        ];
    }

    /**
     * 分组的依据
     */
    private function _genGroupPipelineForShortcutsDayExcel(): array
    {
        $pipeline_group = [
            '_id' => ['field' => '$field', 'upstream' => '$upstream', 'day' => '$day'],
        ];

        // 获取当前统计字段
        $product_id = $this->product_id_shortcut;
        $product = Product::findOneItem(compact('product_id'), 'channel_stat');
        $this->shrotcut_channel_stat = $channel_stat = json_decode($product['channel_stat'], true);

        array_walk($channel_stat['interface_statistic_field'], function ($intro, $field) use (&$pipeline_group) {
            $pipeline_group[$field] = [
                '$sum' => '$data.' . $field
            ];
        });

        return $pipeline_group;
    }

    /**
     * 限制条件
     * @return array
     */
    private function _genMatchPipelineForShortcutsDayExcel(): array
    {
        list($day, $upstream, $field, $node) = [
            $this->_genDayParamsForShortcuts(),
            trim(request()->get('upstream')),
            trim(request()->get('field')),
            trim(request()->get('node')),
        ];

        return array_filter(compact('day', 'field', 'upstream', 'node'), function ($item) {
            return $item;
        });
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamsForDayShortcutExcel()
    {
        $this->_validateDate();
    }

    /**
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function shortcutsExcel()
    {
        $lists = request()->get('lists');

        return Excel::download(new UpstreamShortcutExport($lists), 'export.xlsx');
    }

    /**
     * 邦信分快捷版上游数据通计详情
     * @param string $field
     * @throws CustomException
     * @return array
     */
    public function shortcutDetails(string $field): array
    {
        // 参数校验
        $this->_validateParamsForShortcutDetails();

        // 生成参数
        $pipeline_group = $this->_genPipelineForShortcutDetails($field);

        // 获取列表
        return $this->_genStatListForShortcutDetail($pipeline_group);
    }

    /**
     * 获取列表
     * @param array $pipeline_group
     * @return array
     */
    private function _genStatListForShortcutDetail(array $pipeline_group): array
    {
        // 总量
        $total = $this->_genTotalForShortcut();

        $lists = MongoUpstreamShortcutStatistic::raw(function ($collection) use ($pipeline_group) {
            return $collection->aggregate($pipeline_group);
        })->map(function ($item) use (&$total) {
            $item->day = $item->_id;

            // 计算总量
            $total = $this->_sumTotalForShortcuts($item, $total);
            unset($item->_id);
            return $item;
        })->all();

        $total['day'] = '总计';
        array_unshift($lists, $total);
        return $lists;
    }

    /**
     * 快捷版总计
     * @param $item
     * @param array $total
     * @return array
     */
    public function _sumTotalForShortcuts($item, array $total): array
    {
        return $this->_sumTotalForUpstream($item, $total);
    }

    /**
     * 总计
     * @param $item
     * @param array $total
     * @return array
     */
    private function _sumTotalForUpstream($item, array $total): array
    {
        return collect($total)->map(function ($item_total, $item_field) use ($item) {
            $item_total += $item->$item_field ?? 0;
            return $item_total;
        })->all();
    }

    /**
     * 总量
     * @return array
     */
    private function _genTotalForShortcut(): array
    {
        $interface_statistic_field = array_keys($this->shrotcut_channel_stat['interface_statistic_field']);
        $total = array_fill_keys($interface_statistic_field, 0);
        return $total;
    }

    /**
     * 生成参数
     * @param string $field
     * @return array
     */
    private function _genPipelineForShortcutDetails(string $field): array
    {
        return [
            [
                '$match' => $this->_genMatchForShortcutDetails($field)
            ],
            [
                '$group' => $this->_genGroupPipelineForShortcutsDetail()
            ]
        ];
    }

    /**
     * 分组依据
     */
    private function _genGroupPipelineForShortcutsDetail()
    {
        $pipeline_group = ['_id' => '$day'];

        // 获取当前统计字段
        $product_id = $this->product_id_shortcut;
        $product = Product::findOneItem(compact('product_id'), 'channel_stat');
        $this->shrotcut_channel_stat = $channel_stat = json_decode($product['channel_stat'], true);

        array_walk($channel_stat['interface_statistic_field'], function ($intro, $field) use (&$pipeline_group) {
            $pipeline_group[$field] = [
                '$sum' => '$data.' . $field
            ];
        });

        return $pipeline_group;
    }

    /**
     * 限制
     * @param string $field
     * @return array
     */
    private function _genMatchForShortcutDetails(string $field): array
    {
        list($day, $upstream, $node) = [
            $this->_genDayParamsForShortcutsDetail(),
            trim(request()->get('upstream')),
            trim(request()->get('node')),
        ];

        return array_filter(compact('day', 'field', 'upstream', 'node'), function ($item) {
            return $item;
        });
    }

    /**
     * 日期
     * @return array
     */
    private function _genDayParamsForShortcutsDetail(): array
    {
        return $this->_genDayLimitForUpstream();
    }

    /**
     * 参数校验
     * @throws CustomException
     */
    private function _validateParamsForShortcutDetails()
    {
        // 校验时间
        $this->_validateDate();
    }

    /**
     * 邦信分快捷版上游数据通计列表
     * @return array
     * @throws CustomException
     */
    public function shortcuts(): array
    {
        // 校验参数
        $this->_validateParamsForShortcuts();

        // 生成参数
        $params = $this->_genPipelineParamsForShortcuts();

        // 获取列表
        return $this->_getStatListForShortcut($params);
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     */
    private function _getStatListForShortcut(array $params): array
    {
        // 总计
        $total = $this->_genTotalForShortcut();

        $lists = MongoUpstreamShortcutStatistic::raw(function ($collection) use ($params) {
            return $collection->aggregate($params);
        })->map(function ($item) use (&$total) {
            // 计算总量
            $total = $this->_sumTotalForShortcuts($item, $total);

            // 格式化数据
            return $this->_formatStatItemForShortcut($item);
        })->all();

        //
        $total['field_cn'] = '总计';
        array_unshift($lists, $total);
        return $lists;
    }

    /**
     * 格式化
     */
    private function _formatStatItemForShortcut($item)
    {
        // 格式化upstream
        $item->upstream = $item->_id->upstream;
        $item->upstream_cn = $this->shrotcut_channel_stat['upstream'][$item->upstream];

        // 格式化field
        $item->field = $item->_id->field;
        $item->field_cn = $this->shrotcut_channel_stat['interface_item'][$item->upstream] ? $this->shrotcut_channel_stat['interface_item'][$item->upstream] [$item->field] : '没有找到对应的字段';

        unset($item->_id);
        return $item;
    }

    /**
     * 生成参数
     * @return array
     */
    private function _genPipelineParamsForShortcuts(): array
    {
        return [
            ['$match' => $this->_genMatchPipelineForShortcuts()],
            ['$group' => $this->_genGroupPipelineForShortcuts()]
        ];
    }

    /**
     * 分组的依据
     */
    private function _genGroupPipelineForShortcuts(): array
    {
        $pipeline_group = [
            '_id' => ['field' => '$field', 'upstream' => '$upstream'],
        ];

        // 获取当前统计字段
        $product_id = $this->product_id_shortcut;
        $product = Product::findOneItem(compact('product_id'), 'channel_stat');
        $this->shrotcut_channel_stat = $channel_stat = json_decode($product['channel_stat'], true);

        array_walk($channel_stat['interface_statistic_field'], function ($intro, $field) use (&$pipeline_group) {
            $pipeline_group[$field] = [
                '$sum' => '$data.' . $field
            ];
        });

        return $pipeline_group;
    }


    /**
     * 限制条件
     * @return array
     */
    private function _genMatchPipelineForShortcuts(): array
    {
        list($day, $upstream, $field, $node) = [
            $this->_genDayParamsForShortcuts(),
            trim(request()->get('upstream')),
            trim(request()->get('field')),
            trim(request()->get('node')),
        ];

        return array_filter(compact('day', 'field', 'upstream', 'node'), function ($item) {
            return $item;
        });
    }

    /**
     * 生成参数
     */
    private function _genDayParamsForShortcuts(): array
    {
        return $this->_genDayLimitForUpstream();
    }

    /**
     * 日期限制
     * @return array
     */
    private function _genDayLimitForUpstream(): array
    {
        list($day_begin, $day_end) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
        ];

        return [
            '$gte' => date('Ymd', strtotime($day_begin)),
            '$lte' => date('Ymd', strtotime($day_end)),
        ];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamsForShortcuts()
    {
        // 校验时间
        $this->_validateDate();
    }

    /**
     * 校验时间
     * @throws CustomException
     */
    private function _validateDate()
    {
        list($day_begin, $day_end) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
        ];

        if (!$day_begin || !$day_end) {
            throw new CustomException('请传递day_begin和day_end');
        }

        if (strtotime($day_end) < strtotime($day_begin)) {
            throw new CustomException('抱歉,开始时间必须小于结束时间');
        }
    }
}