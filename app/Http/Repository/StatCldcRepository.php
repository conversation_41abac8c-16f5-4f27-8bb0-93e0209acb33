<?php

namespace App\Http\Repository;

use App\Models\Account;
use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\ChannelRemit;
use App\Models\CustomerExpend;
use App\Models\StatisticsInterfaceUsage;
use App\Models\UpstreamBillAdjust;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\UserChannelConfig;
use App\Providers\RedisCache\RedisCache;
use DateTime;


class StatCldcRepository extends StatBaseRepository
{

    private $type_product = [
        'ai' => [39001, 38001],
        'sms' => [39002, 39003, 38002, 38003],
    ];

    /**
     * 数据统计-存量洞察
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {

        $where = $this->getWhere();

        //查询数据
        $usage_data = StatisticsInterfaceUsage::getCldcStat($where);

        //查询花费数据
        $cost_data = BillCostV2::getCldcStat($where);


        $expend_where = $this->getWhere(2);

        //查询特殊消耗
        $expend = ChannelAccountAdjust::getCldcChannelAdjust($expend_where);


        if ($usage_data && $cost_data) {
            return $this->dealData($usage_data, $cost_data, $expend);
        }

        return [];
    }


    public function statListExport($export_type)
    {
        $where = $this->getWhere();

        //查询数据
        $usage_data = StatisticsInterfaceUsage::getExportCldcStat($where, $export_type);


        //查询花费数据
        $cost_data = BillCostV2::getExportCldcStat($where, $export_type);


        $expend_where = $this->getWhere(2);

        //查询特殊消耗
        $expend = ChannelAccountAdjust::getExportCldcChannelAdjust($expend_where, $export_type);


        if ($usage_data && $cost_data) {
            return $this->dealExportData($usage_data, $cost_data, $expend);
        }

        return [];
    }


    private function dealData($usage_data, $cost_data, $expend)
    {

        $optionsRepository = new OptionsRepository();
        //查询渠道
        $cldc_channel = $optionsRepository->getCldcChannelMap();


        //查询接口
        $cldc_interface = $optionsRepository->getCldcInterfaceMap();

        //查询渠道接口
        $cldc_channel_interface = $optionsRepository->getCldcInterfaceList();

        //查询账号
        $cldc_account = $optionsRepository->getAccountMap();


        //查询产品
        $cldc_product = $optionsRepository->getCldcProduct();

        $cost_deal_data = [];
        foreach ($cost_data as $v) {
            $apikey = $v['apikey'];
            $product_id = $v['product_id'];
            $interface_id = $v['interface_id'];
            $channel_id = isset($cldc_channel_interface[$interface_id]) ? $cldc_channel_interface[$interface_id] : '';
            if (!$channel_id) {
                continue;
            }

            $channel = isset($cldc_channel[$channel_id]) ? $cldc_channel[$channel_id] : '';
            $interface = isset($cldc_interface[$interface_id]) ? $cldc_interface[$interface_id] : '';
            $account = isset($cldc_account[$apikey]) ? $cldc_account[$apikey] : '';
            $product = isset($cldc_product[$product_id]) ? $cldc_product[$product_id] : '';

            $key = $channel . '_' . $interface . '_' . $account . '_' . $product;

            $dataDetail = ['cost' => $v['money']];


            $cost_deal_data[$key] = $dataDetail;
        }


        $usage_deal_data = [];
        foreach ($usage_data as $v) {


            $apikey = $v['apikey'];
            $product_id = $v['product_id'];
            $interface_id = $v['interface_id'];
            $channel_id = isset($cldc_channel_interface[$interface_id]) ? $cldc_channel_interface[$interface_id] : '';
            if (!$channel_id) {
                continue;
            }


            $channel = isset($cldc_channel[$channel_id]) ? $cldc_channel[$channel_id] : '';
            $interface = isset($cldc_interface[$interface_id]) ? $cldc_interface[$interface_id] : '';
            $account = isset($cldc_account[$apikey]) ? $cldc_account[$apikey] : '';
            $product = isset($cldc_product[$product_id]) ? $cldc_product[$product_id] : '';

            $key = $channel . '_' . $interface . '_' . $account . '_' . $product;

            $dataDetail = [];


            $dataDetail['channel'] = $channel;
            $dataDetail['interface'] = $interface;
            $dataDetail['account'] = $account;
            $dataDetail['product'] = $product;

            if (in_array($product_id, $this->type_product['ai'])) {
                $dataDetail['ai_total'] = $v['total_num'];
                $dataDetail['ai_success'] = $v['success_num'];
                $dataDetail['ai_valid'] = $v['valid_num'];
                $dataDetail['ai_charge'] = $v['charge_num'];
            } else {
                $dataDetail['sms_total'] = $v['total_num'];
                $dataDetail['sms_valid'] = $v['valid_num'];
                $dataDetail['sms_charge'] = $v['charge_num'];
            }

            $usage_deal_data[$key] = $dataDetail;
        }


        // 合并 花费和调用都有的数据
        $merged = array_map(function ($key) use ($usage_deal_data, $cost_deal_data) {
            if (array_key_exists($key, $usage_deal_data) && array_key_exists($key, $cost_deal_data)) {
                $result = [];
                $keys = explode('_', $key);
                $result[$keys[0]][$keys[1]][$keys[2]][$keys[3]] = array_merge($usage_deal_data[$key], $cost_deal_data[$key]);
                return $result;

            }
            if (array_key_exists($key, $usage_deal_data) && !array_key_exists($key, $cost_deal_data)) {
                $result = [];
                $keys = explode('_', $key);
                $result[$keys[0]][$keys[1]][$keys[2]][$keys[3]] = $usage_deal_data[$key];
                return $result;
            }
            return [];
        }, array_keys($usage_deal_data));


        $format_data = [];

        foreach ($merged as $v) {
            foreach ($v as $channel => $channel_v) {
                foreach ($channel_v as $interface => $interface_v) {
                    foreach ($interface_v as $apikey => $apikey_v) {
                        foreach ($apikey_v as $product => $product_v) {
                            $format_data[$channel][$interface][$apikey][$product] = $product_v;
                        }
                    }
                }
            }
        }


        if ($expend) {
            $spe_fix = '特殊消耗';
            foreach ($expend as $v) {
                $apikey = $v['apikey'];
                $product_id = $v['product_id'];
                $interface_id = $v['interface_id'];
                $channel_id = isset($cldc_channel_interface[$interface_id]) ? $cldc_channel_interface[$interface_id] : '';
                if (!$channel_id) {
                    continue;
                }

                $channel = isset($cldc_channel[$channel_id]) ? $cldc_channel[$channel_id] : '';
                $interface = isset($cldc_interface[$interface_id]) ? $cldc_interface[$interface_id] : '';
                $account = isset($cldc_account[$apikey]) ? $cldc_account[$apikey] : '';
                $product = isset($cldc_product[$product_id]) ? $cldc_product[$product_id] : '';

                if (isset($format_data[$channel][$spe_fix][$account][$product]['cost'])) {
                    $format_data[$channel][$interface][$account][$spe_fix]['cost'] += $v['money'];
                } else {
                    $format_data[$channel][$interface][$account][$spe_fix]['cost'] = $v['money'];
                }

            }
        }


        return $format_data;

    }


    private function dealExportData($usage_data, $cost_data, $expend)
    {

        $optionsRepository = new OptionsRepository();
        //查询渠道
        $cldc_channel = $optionsRepository->getCldcChannelMap();


        //查询接口
        $cldc_interface = $optionsRepository->getCldcInterfaceMap();

        //查询渠道接口
        $cldc_channel_interface = $optionsRepository->getCldcInterfaceList();

        //查询账号
        $cldc_account = $optionsRepository->getAccountMap();


        //查询产品
        $cldc_product = $optionsRepository->getCldcProduct();

        $cost_deal_data = [];
        foreach ($cost_data as $v) {
            $apikey = $v['apikey'];
            $product_id = $v['product_id'];
            $interface_id = $v['interface_id'];
            $date = $v['selectDate'];
            $channel_id = isset($cldc_channel_interface[$interface_id]) ? $cldc_channel_interface[$interface_id] : '';
            if (!$channel_id) {
                continue;
            }


            $channel = isset($cldc_channel[$channel_id]) ? $cldc_channel[$channel_id] : '';
            $interface = isset($cldc_interface[$interface_id]) ? $cldc_interface[$interface_id] : '';
            $account = isset($cldc_account[$apikey]) ? $cldc_account[$apikey] : '';
            $product = isset($cldc_product[$product_id]) ? $cldc_product[$product_id] : '';

            $key = $channel . '_' . $interface . '_' . $account . '_' . $product . '_' . $date;

            $costDataDetail = ['cost' => $v['money']];
            $costDataDetail['date'] = $this->getFormatDate($v['selectDate']);


            $cost_deal_data[$key] = $costDataDetail;
        }


        $usage_deal_data = [];
        foreach ($usage_data as $v) {


            $apikey = $v['apikey'];
            $product_id = $v['product_id'];
            $interface_id = $v['interface_id'];
            $date = $v['selectDate'];
            $channel_id = isset($cldc_channel_interface[$interface_id]) ? $cldc_channel_interface[$interface_id] : '';
            if (!$channel_id) {
                continue;
            }


            $channel = isset($cldc_channel[$channel_id]) ? $cldc_channel[$channel_id] : '';
            $interface = isset($cldc_interface[$interface_id]) ? $cldc_interface[$interface_id] : '';
            $account = isset($cldc_account[$apikey]) ? $cldc_account[$apikey] : '';
            $product = isset($cldc_product[$product_id]) ? $cldc_product[$product_id] : '';

            $key = $channel . '_' . $interface . '_' . $account . '_' . $product . '_' . $date;

            $dataDetail = [];

            $dataDetail['date'] = $this->getFormatDate($v['selectDate']);
            $dataDetail['channel'] = $channel;
            $dataDetail['interface'] = $interface;
            $dataDetail['account'] = $account;
            $dataDetail['product'] = $product;

            if (in_array($product_id, $this->type_product['ai'])) {
                $dataDetail['ai_total'] = $v['total_num'];
                $dataDetail['ai_success'] = $v['success_num'];
                $dataDetail['ai_valid'] = $v['valid_num'];
                $dataDetail['ai_charge'] = $v['charge_num'];
            } else {
                $dataDetail['sms_total'] = $v['total_num'];
                $dataDetail['sms_valid'] = $v['valid_num'];
                $dataDetail['sms_charge'] = $v['charge_num'];
            }

            $usage_deal_data[$key] = $dataDetail;
        }


        // 合并 花费和调用都有的数据
        $merged = array_map(function ($key) use ($usage_deal_data, $cost_deal_data) {
            if (array_key_exists($key, $usage_deal_data) && array_key_exists($key, $cost_deal_data)) {
                $result = array_merge($usage_deal_data[$key], $cost_deal_data[$key]);
                return $result;

            }
            if (array_key_exists($key, $usage_deal_data) && !array_key_exists($key, $cost_deal_data)) {
                $usage_deal_data[$key]['cost'] = 0;

                return $usage_deal_data[$key];
            }
            return [];
        }, array_keys($usage_deal_data));


        if ($expend) {
            $spe_fix = '特殊消耗';
            foreach ($expend as $v) {
                $apikey = $v['apikey'];
                $product_id = $v['product_id'];
                $interface_id = $v['interface_id'];
                $channel_id = isset($cldc_channel_interface[$interface_id]) ? $cldc_channel_interface[$interface_id] : '';
                if (!$channel_id) {
                    continue;
                }

                $channel = isset($cldc_channel[$channel_id]) ? $cldc_channel[$channel_id] : '';
                $interface = isset($cldc_interface[$interface_id]) ? $cldc_interface[$interface_id] : '';
                $account = isset($cldc_account[$apikey]) ? $cldc_account[$apikey] : '';


                $expendDetail['date'] = $v['selectDate'];
                $expendDetail['channel'] = $channel;
                $expendDetail['interface'] = $interface;
                $expendDetail['account'] = $account;
                $expendDetail['product'] = $spe_fix;
                $expendDetail['cost'] = $v['money'];


                $merged[] = $expendDetail;
            }
        }


        return $this->getFormatData($merged);

    }


    private function getFormatData($data)
    {
        if (count($data) == 0) {
            return $data;
        }


        foreach ($data as &$v) {
            $v['ai_total'] = isset($v['ai_total']) ? intval($v['ai_total']) : 0;
            $v['ai_success'] = isset($v['ai_success']) ? intval($v['ai_success']) : 0;
            $v['ai_valid'] = isset($v['ai_valid']) ? intval($v['ai_valid']) : 0;
            $v['ai_charge'] = isset($v['ai_charge']) ? intval($v['ai_charge']) : 0;
            $v['sms_total'] = isset($v['sms_total']) ? intval($v['sms_total']) : 0;
            $v['sms_valid'] = isset($v['sms_valid']) ? intval($v['sms_valid']) : 0;
            $v['sms_charge'] = isset($v['sms_charge']) ? intval($v['sms_charge']) : 0;
            $v['cost'] = isset($v['cost']) ? floatval($v['cost']) : 0;
        }


        $date = array_column($data, 'date');
        $channel = array_column($data, 'channel');
        $interface = array_column($data, 'interface');
        $account = array_column($data, 'account');
        $product = array_column($data, 'product');


        array_multisort($date, SORT_ASC, $channel, SORT_ASC, $interface, SORT_ASC, $account, SORT_ASC, $product, SORT_ASC, $data);

        return $data;
    }

    // $type  1 调用、消耗  2 特殊消耗
    private function getWhere($type = 1)
    {
        $where = [];
        $date_start = request()->post('start_date');
        $date_end = request()->post('end_date');
        $channel = request()->post('channel');
        $interface = request()->post('interface');
        $account = request()->post('account');
        $product = request()->post('product');
        $export_type = request()->post('export_type');


        if (isset($export_type) && $export_type == 'month') {
            $date_start = substr($date_start, 0, 6) . '01';
            $date_end = $this->getLastDay($date_end);
        }


        if ($type == 2) {
            isset($date_start) && isset($date_end)
            && $where = [['date', '>=', date('Y-m-d', strtotime($date_start))], ['date', '<=', date('Y-m-d', strtotime($date_end))]];
        } else {
            isset($date_start) && isset($date_end)
            && $where = [['date', '>=', date('Ymd', strtotime($date_start))], ['date', '<=', date('Ymd', strtotime($date_end))]];

        }


        if ($channel && !$interface) {
            $optionsRepository = new OptionsRepository();
            $cldc_interface = $optionsRepository->getCldcInterfaceMap($channel);
            $where['in']['interface_id'] = array_keys($cldc_interface);
        }
        if ($interface) {
            $where[] = ['interface_id', $interface];
        }

        if ($account) {
            $where[] = ['apikey', $account];
        }
        if ($product) { // 默认查询存量洞察全部产品
            $where[] = ['product_id', $product];
        } else {
            $where['in']['product_id'] = CLDC_PRODUCT;
        }


        return $where;

    }


    private function getFormatDate($date)
    {


        if (strlen($date) == 6) {
            $dates = DateTime::createFromFormat('Ym', $date);
            return $dates->format('Y-m');
        } elseif (strlen($date) == 8) {
            $dates = DateTime::createFromFormat('Ymd', $date);
            return $dates->format('Y-m-d');
        }
        return $date;
    }


}