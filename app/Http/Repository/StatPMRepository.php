<?php

namespace App\Http\Repository;

use App\Define\StatDefine;
use App\Exports\CustomerExpendExport;
use App\Exports\MainProductStatExport;
use App\Models\Account;
use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\ProductRel;
use App\Models\StatisticsCustomerUsage;
use App\Models\StatisticsInterfaceUsage;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Cost\ProductCostRepository;
use App\Repositories\Income\MainRepository;
use App\Repositories\Income\ProductIncomeRepository;
use App\Utils\Helpers\Func;
use App\Utils\Helpers\StatPMHelper;
use Maatwebsite\Excel\Facades\Excel;
use Exception;


class StatPMRepository
{

    private $rel_product_list = [];
    private $all_father_ids   = [];
    private $degree = 2;


    public function __construct() {
        $this->get_rel_product_map();
    }

    // ---------------------------- 通用方法


    /**
     * 获取配置后的产品id关系
     *
     * @return void
     * <AUTHOR> 2024-07-03 16:26:37
     *
     */
    private function get_rel_product_map(){
        $mainRep = new MainRepository();
        $this->rel_product_list = $mainRep->getSubPidsByFatherIdsWithAngle([],  StatDefine::PM_ANGLE);
        $this->all_father_ids = array_unique(array_values($this->rel_product_list));
    }

    /**
     * 参数验证
     *
     * @return StatPMHelper
     * @throws Exception
     */
    protected function getStatParams() {
        $father_id     = intval(request()->post('father_id'));
        $father_ids    = request()->post('father_ids', []);
        $customer_id   = request()->post('customer_id');
        $apikey        = request()->post('apikey');
        $product_id    = intval(request()->post('product_id'));
        $product_ids   = request()->post('product_ids');
        $operator      = request()->post('operator');
        $start_date    = request()->post('start_date');
        $end_date      = request()->post('end_date');
        $test_data     = request()->post('test_data');
        $channel       = request()->post('channel');
        $interface     = request()->post('interface');
        $export_type   = request()->post('export_type');
        $chart_type    = request()->post('chart_type');
        $dept_id       = request()->post('dept_id');
        $username      = request()->post('username');
        $father_type   = request()->post('father_type', '');
        $children_type = request()->post('children_type', '');
        $source        = request()->post('source', '');
        $user_cookie   = request()->post('user_cookie', '');

        if (!$start_date || !$end_date)
            throw new Exception('请选择时间');

        if (!preg_match('/^\d{8}$/', $start_date) || !preg_match('/^\d{8}$/', $end_date))
            throw new Exception('时间格式错误');

        // 替换产品人员级别关系
        if(!empty($dept_id)){
            [$dept_id_type,$dept_id_value] = explode("_", $dept_id[count($dept_id) - 1]);
            if($dept_id_type == 'dept'){
                $dept_id = $dept_id_value;
            }
            if($dept_id_type == 'salesman'){
                $username = $dept_id_value;
            }
        }

        $params = compact('start_date', 'end_date');
        $apikey && $params['apikey'] = $apikey;
        $product_id && $params['product_id'] = $product_id;
        $operator && $params['operator'] = $operator;
        $father_id && $params['father_id'] = $father_id;
        $father_ids && $params['father_ids'] = $father_ids;
        $customer_id && $params['customer_id'] = $customer_id;
        $channel && $params['channel'] = $channel;
        $interface && $params['interface'] = $interface;
        $export_type && $params['export_type'] = $export_type;//导出类型
        $chart_type && $params['chart_type'] = $chart_type;   //图形指标类型
        $dept_id && $params['dept_id'] = $dept_id;
        $username && $params['username'] = $username;
        $father_type && $params['father_type'] = $father_type;
        $children_type && $params['children_type'] = $children_type;
        $test_data && $params['test_data'] = $test_data;
        $source !== '' && $params['source'] = $source;
        $user_cookie !== '' && $params['user_cookie'] = $user_cookie;

        $params['apikey_list'] = $params['product_list'] = $params['filter_apikey'] = $params['filter_customer'] = $params['filter_product'] = false;
        //搜索条件中包含客户的话先取出所属apikey，避免重复查库
        if ($customer_id) {
            $account_list = Account::getListByCondition(['customer_id' =>$customer_id], 'apikey')->toArray();
            $apikey_list = array_filter(array_column($account_list,'apikey'));
            $params['apikey_list'] = $apikey_list;
            $params['customer_list'] = [$customer_id];
        }

        $products = array_keys($this->rel_product_list);

        //根据主产品查寻子产品,没有主产品则为所有主产品
        //后续不再根据主产品id进行查询

        $params['product_list'] = [];

        if ($father_ids && !empty(array_intersect($this->all_father_ids,$father_ids))) {
            foreach($this->rel_product_list as $pid => $fid){
                if(in_array($fid,$father_ids)) {
                    $params['product_list'][] = $pid;
                }
            }
        }
        if ($father_id && in_array($father_id,$this->all_father_ids)) {
            foreach($this->rel_product_list as $pid => $fid){
                if($fid == $father_id) {
                    $params['product_list'][] = $pid;
                }
            }
        }


        if($product_id) {
            if (in_array($product_id, $params['product_list'])) {
                $params['product_list'] = [$product_id];
            }
        }
        if($product_ids){
             $params['product_list'] = $product_ids;
        }

        if (empty($father_ids) && empty($father_id) && empty($product_id) && empty ($product_ids)) {
            $params['product_list'] = array_keys($this->rel_product_list);
        }

        if (!$test_data) {
            $params['filter_customer'] = ['C20180828LOCNMG', 'C20200622KF31GS'];//羽乐科技内部、售前测试
            $params['filter_apikey'] = array_column(Account::whereIn('customer_id', $params['filter_customer'])->get()->toArray(), 'apikey');
        }

        return new StatPMHelper($params);
    }


    /**
     * 获取配置后的子产品列表
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-07-11 14:34:19
     *
     */
    public function pm_sub_product_list(){
        $father_id = intval(request()->get('father_id'));

        $product_list = [];
        //获取查询主产品的配置子产品
        if($father_id) {
            foreach ($this->rel_product_list as $pid => $fid) {
                if ($fid == $father_id) {
                    $product_list[] = $pid;
                }
            }
        }else {
            $product_list = array_values($this->rel_product_list);
        }

        //获取产品名称
        $product_list = Product::getProductListByProductIds($product_list);
        return array_column($product_list,'product_name','product_id');
    }


    // ---------------------------- 通用方法


    // ---------------------------- 主产品统计数据

    /**
     * 数据统计-主产品统计
     *
     * @return array
     * @throws Exception
     */
    public function pm_main_stat_list() {
        $stat_pm_obj = $this->getStatParams();
        $usage_data       = $this->get_main_stat_usage($stat_pm_obj);       //获取调用量
        $income_data      = $this->get_main_stat_income($stat_pm_obj);      //获取收入
        $cost_data        = $this->get_main_stat_cost($stat_pm_obj);        //获取成本
        $cost_adjust_data = $this->get_main_stat_cost_adjust($stat_pm_obj);  //获取成本调整
        $cost_fixed_data  = $this->get_main_stat_fixed_cost($stat_pm_obj);   //获取固定费用成本
        $expend_data      = $this->get_main_stat_expend($stat_pm_obj);      //获取客户特殊消耗、特殊充值

        //将上面数据糅合到统一的父产品维度
        $data = $this->main_format_data($stat_pm_obj, $usage_data, $income_data, $cost_data, $expend_data, $cost_adjust_data, $cost_fixed_data);
        // $data = $this->filterAuthMoney($data); //过滤没有权限查看的产品（收入和成本）
        return array_values($data);
    }

    /**
     * 获取调用量
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     */
    protected function get_main_stat_usage($stat_pm_obj) {
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_usage_where();

        //获取子产品调用量
        return StatisticsCustomerUsage::getPMMainStatList($where,$ins,$filres);
    }

    /**
     * 获取营收
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     */
    protected function get_main_stat_income($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_income_where();
        return BillProductIncomeV2::getPMMainStatList($where,$ins,$filres);
    }

    /**
     * 获取成本
     *
     * @param $stat_pm_obj StatPMHelper
     * @return array
     */
    protected function get_main_stat_cost($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_where();

        return BillCostV2::getPMMainStatList($where,$ins,$filres);
    }

    /**
     *
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-03 17:25:16
     */
    protected function get_main_stat_cost_adjust($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_adjust_where();
        return ChannelAccountAdjust::getPMMainStatList($where,$ins,$filres);
    }

    /**
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-03 17:40:07
     */
    protected function get_main_stat_fixed_cost($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_fixed_where();
        return ChannelAccountFixedFee::getPMMainStatList($where,$ins,$filres);
    }

    /**
     * 获取营收特殊消耗
     *
     * @param $stat_pm_obj StatPMHelper
     * @return array
     */
    protected function get_main_stat_expend($stat_pm_obj){
        // $where = $this->getExpendWhere($params);
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_customer_expend_where();
        $where_sub = $where_add = $where;
        $where_sub[] = ['type', '=', 1];
        $expend['sub'] = CustomerExpend::getPMStatList($where_sub,$ins,$filres);
        $where_add[] = ['type', '=', 2];
        $expend['add'] = CustomerExpend::getPMStatList($where_add,$ins,$filres);

        return $expend;
    }


    private function get_fmort_init(){
        return [
            'father_id' => 0,
            'total' => 0,
            'success' => 0,
            'valid' => 0,
            'cache' => 0,
            'cost' => 0,
            'number' => 0,
            'money' => 0,
        ];
    }

    /**
     * 组装最终数据
     *
     * @param       $stat_pm_obj StatPMHelper
     * @param       $usage_data
     * @param       $income_data
     * @param       $cost_data
     * @param       $expend_data
     * @param array $cost_adjust_data
     * @param array $cost_fixed_data
     *
     * @return array
     */
    private function main_format_data($stat_pm_obj, $usage_data, $income_data, $cost_data, $expend_data, $cost_adjust_data = [], $cost_fixed_data = []) {

        $data = [];
        //糅合调用量数据
        foreach ($usage_data as $item){
            $father_id = $this->rel_product_list[$item['product_id']];
            if (!isset($data[$father_id])){
                $data[$father_id] = $this->get_fmort_init();
                $data[$father_id]['father_id'] = $father_id;
            }

            $data[$father_id]['total']   = $item['total']   + $data[$father_id]['total'];
            $data[$father_id]['success'] = $item['success'] + $data[$father_id]['success'];
            $data[$father_id]['valid']   = $item['valid']   + $data[$father_id]['valid'];
            $data[$father_id]['cache']   = $item['cache']   + $data[$father_id]['cache'];
        }

        //糅合营收数据
        foreach ($income_data as $item){
            $father_id = $this->rel_product_list[$item['product_id']];
            if (!isset($data[$father_id])){
                $data[$father_id] = $this->get_fmort_init();
                $data[$father_id]['father_id'] = $father_id;
            }
            $data[$father_id]['number'] = $item['number'] + $data[$father_id]['number'];
            $data[$father_id]['money']  = $item['money']  + $data[$father_id]['money'];
        }
        //糅合成本数据
        foreach ($cost_data as $item){
            $father_id = $this->rel_product_list[$item['product_id']];
            if (!isset($data[$father_id])){
                $data[$father_id] = $this->get_fmort_init();
                $data[$father_id]['father_id'] = $father_id;
            }
            $data[$father_id]['cost'] = $item['money'] + ($data[$father_id]['cost'] ?? 0);
        }

        //糅合成本调整数据
        foreach ($cost_adjust_data as $item){
            $father_id = $this->rel_product_list[$item['product_id']];
            if (!isset($data[$father_id])){
                $data[$father_id] = $this->get_fmort_init();
                $data[$father_id]['father_id'] = $father_id;
            }
            $total_cost = $data[$father_id]['cost'] ?? 0;
            $data[$father_id]['cost'] = bcadd($total_cost, $item['money'], $this->degree);
        }

        //糅合固定费用成本数据
        foreach ($cost_fixed_data as $item){
            $father_id = $this->rel_product_list[$item['product_id']];
            if (!isset($data[$father_id])){
                $data[$father_id] = $this->get_fmort_init();
                $data[$father_id]['father_id'] = $father_id;
            }
            $total_cost = $data[$father_id]['cost'] ?? 0;
            $data[$father_id]['cost'] = bcadd($total_cost, $item['money'], $this->degree);
        }

        //糅合营收特殊消耗
        if (isset($expend_data['add'])) {
            foreach ($expend_data['add'] as $item) {
                //获取fatherId（如果father_id不大于0说明是一级产品）
                $father_id = $this->rel_product_list[$item['product_id']];
                if (!isset($data[$father_id])) {
                    $data[$father_id] = $this->get_fmort_init();
                    $data[$father_id]['father_id'] = $father_id;
                }
                $data[$father_id]['number'] = isset($data[$father_id]['number']) ? $data[$father_id]['number'] + $item['fee_number'] : $item['fee_number'];
                $data[$father_id]['money'] = isset($data[$father_id]['money']) ? $data[$father_id]['money'] + $item['money'] : $item['money'];
            }
        }
        //糅合营收特殊赠送
        if (isset($expend_data['sub'])) {
            foreach ($expend_data['sub'] as $item) {
                //获取fatherId（如果father_id不大于0说明是一级产品）
                $father_id = $this->rel_product_list[$item['product_id']];

                if (!isset($data[$father_id])) {
                    $data[$father_id] = $this->get_fmort_init();
                    $data[$father_id]['father_id'] = $father_id;
                }
                $data[$father_id]['number'] = isset($data[$father_id]['number']) ? $data[$father_id]['number'] - $item['fee_number'] : 0 - $item['fee_number'];
                $data[$father_id]['money'] = isset($data[$father_id]['money']) ? $data[$father_id]['money'] - $item['money'] : 0 - $item['money'];
            }
        }


        //查看成本和收入权限
        foreach ($data as $father_id => &$info) {
            $show_money    = $stat_pm_obj->checkUserProductAuthWithProductID($father_id, 'money');  //是否有权限查看产品金额
            $show_cost     = $stat_pm_obj->checkUserProductAuthWithProductID($father_id, 'cost');   //是否有权限查看产品成本

            if (!$show_money) {
                $info['money'] = '-';
            }
            if (!$show_cost) {
                $info['cost'] = '-';
            }
        }


        return $data;
    }

    // ---------------------------- 主产品统计数据














    // ---------------------------- 客户维度统计数据


    /**
     * 数据统计-主产品统计-客户维度
     *
     * @return array
     * @throws Exception
     */
    public function pm_customer_stat_list(){
        $stat_pm_obj = $this->getStatParams();

        $usage_data           = $this->get_customer_stat_usage($stat_pm_obj);               //调用量
        $income_data          = $this->get_customer_stat_income($stat_pm_obj);              //收入
        $cost_data            = $this->get_customer_stat_cost($stat_pm_obj);                //成本
        $customer_adjust_cost = $this->get_customer_stat_adjust_cost($stat_pm_obj);         //客户成本调整
        $fixed_cost           = $this->get_customer_stat_fixed_cost($stat_pm_obj);          //固定费用成本
        $channel_adjust_cost  = $this->get_customer_stat_adjust_channel_cost($stat_pm_obj); //渠道成本调整(其实叫成本调整更符合业务,由于历史原因，命名的语义不太符合业务)
        $expend_data          = $this->get_customer_stat_expend($stat_pm_obj);              //特殊消耗

        return $this->format_customer_stat_data($stat_pm_obj,$usage_data, $income_data, $cost_data, $expend_data, $customer_adjust_cost, $fixed_cost, $channel_adjust_cost);
    }



    //客户调用量 按照客户分组
    protected function get_customer_stat_usage(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_usage_where();
        return StatisticsCustomerUsage::getPMCustomerStatList($where,$ins,$filres);
    }

    //客户收入 按照客户分组
    protected function get_customer_stat_income(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_income_where();
        return BillProductIncomeV2::getPMCustomerStatList($where,$ins,$filres);
    }

    //客户成本 按照客户分组
    protected function get_customer_stat_cost(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_where();
        return BillCostV2::getPMCustomerStatList($where,$ins,$filres);
    }

    //客户成本调整 按照客户分组
    protected function get_customer_stat_adjust_cost(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_customer_bill_adjust_where();
        return CustomerBillAdjust::getPMCustomerStatList($where,$ins,$filres);
    }

    //固定费用成本
    protected function get_customer_stat_fixed_cost(StatPMHelper $stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_account_fixed_fee_where();
        return ChannelAccountFixedFee::getPMCustomerStatList($where,$ins,$filres);
    }

    protected function get_customer_stat_adjust_channel_cost(StatPMHelper $stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_adjust_channel_cost_where();
        return ChannelAccountAdjust::getPMCustomerStatList($where,$ins,$filres);
    }

    protected function get_customer_stat_expend(StatPMHelper $stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_customer_expend_where();
        $where_sub = $where_add = $where;
        $where_sub[] = ['type', '=', 1];
        $expend['sub'] = CustomerExpend::getPMCustomerStatList($where_sub,$ins,$filres);
        $where_add[] = ['type', '=', 2];
        $expend['add'] = CustomerExpend::getPMCustomerStatList($where_add,$ins,$filres);

        return $expend;
    }

    /**
     * 格式化数据
     *
     * @param $stat_pm_obj StatPMHelper
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @param $customer_adjust_cost
     * @param $fixed_cost
     * @param $channel_adjust_cost
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-07-09 10:39:41
     */
    private function format_customer_stat_data($stat_pm_obj, $usage_data, $income_data, $cost_data, $expend_data, $customer_adjust_cost, $fixed_cost, $channel_adjust_cost) {
        $show_money = $stat_pm_obj->checkUserProductAuth('money');  //是否有权限查看产品金额
        $show_cost  = $stat_pm_obj->checkUserProductAuth('cost');   //是否有权限查看产品金额

        $data = [];
        foreach ($usage_data as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $info['success'] = $item['success'];
            $info['total']   = $item['total'];
            $info['valid']   = $item['valid'];
            $info['cache']   = $item['cache'];
            $info['money']   = $show_money ? 0 : '-';
            $info['cost']    = $show_cost ? 0 : '-';

            $data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']] = $info;
        }

        //信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
        foreach ($income_data as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['number'] = $item['number'];
            $data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['money']  = $show_money ? $item['money'] : '-';
            $data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cost']   = $show_cost ? 0 : '-';

        }
        if ($show_cost) {
            foreach ($cost_data as $item) {
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
                $data[$customer_id][$item['apikey']][$item['product_id']][$item['operator']]['cost'] = $item['money'];
            }
        }
        foreach ($expend_data['add'] as $item) {
            if (!isset($data[$item['customer_id']]['特殊消耗'])) {
                $data[$item['customer_id']]['特殊消耗'][0][0]['number'] = 0;
                $data[$item['customer_id']]['特殊消耗'][0][0]['money']  = $show_money ? 0 : '-';
            }
            $data[$item['customer_id']]['特殊消耗'][0][0]['number'] += $item['fee_number'];
            if ($show_money) $data[$item['customer_id']]['特殊消耗'][0][0]['money'] += $item['money'];
        }
        foreach ($expend_data['sub'] as $item) {
            if (!isset($data[$item['customer_id']]['特殊消耗'])) {
                $data[$item['customer_id']]['特殊消耗'][0][0]['number'] = 0;
                $data[$item['customer_id']]['特殊消耗'][0][0]['money']  = $show_money ? 0 : '-';
            }
            $data[$item['customer_id']]['特殊消耗'][0][0]['number'] -= $item['fee_number'];
            if ($show_money) $data[$item['customer_id']]['特殊消耗'][0][0]['money'] -= $item['money'];
        }

        foreach ($customer_adjust_cost as $item) {
            $apikey     = 'customer_adjust';//在前端字典已经做了该key映射
            $product_id = 'NO';             //在前端字典已经做了该key映射
            $operator   = 'NO';             //在前端字典已经做了该key映射
            $data[$item['customer_id']][$apikey][$product_id][$operator]['cost']       = $show_cost ? $item['money'] : '-';
            $data[$item['customer_id']][$apikey][$product_id][$operator]['money']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['operator']   = $operator;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['product_id'] = $product_id;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['success']    = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['total']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['valid']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['cache']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['apikey']     = $apikey;
        }

        //固定费用成本
        foreach ($fixed_cost as $item) {
            $apikey     = 'fixed_adjust';//在前端字典已经做了该key映射
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $product_id = 'NO';          //在前端字典已经做了该key映射
            $operator   = 'NO';          //在前端字典已经做了该key映射
            $data[$customer_id][$apikey][$product_id][$operator]['cost']       = $show_cost ? $item['money'] : '-';
            $data[$customer_id][$apikey][$product_id][$operator]['money']      = 0;
            $data[$customer_id][$apikey][$product_id][$operator]['operator']   = $operator;
            $data[$customer_id][$apikey][$product_id][$operator]['product_id'] = $product_id;
            $data[$customer_id][$apikey][$product_id][$operator]['success']    = 0;
            $data[$customer_id][$apikey][$product_id][$operator]['total']      = 0;
            $data[$customer_id][$apikey][$product_id][$operator]['valid']      = 0;
            $data[$customer_id][$apikey][$product_id][$operator]['cache']      = 0;
            $data[$customer_id][$apikey][$product_id][$operator]['apikey']     = $apikey;
        }

        //渠道成本调整
        foreach ($channel_adjust_cost as $item) {
            $apikey     = 'channel_adjust';//在前端字典已经做了该key映射
            $product_id = 'NO';            //在前端字典已经做了该key映射
            $operator   = 'NO';            //在前端字典已经做了该key映射
            $data[$item['customer_id']][$apikey][$product_id][$operator]['cost']       = $show_cost ? $item['money'] : '-';
            $data[$item['customer_id']][$apikey][$product_id][$operator]['money']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['operator']   = $operator;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['product_id'] = $product_id;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['success']    = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['total']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['valid']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['cache']      = 0;
            $data[$item['customer_id']][$apikey][$product_id][$operator]['apikey']     = $apikey;
        }

        return $data;
    }


    /**
     * 客户维度统计 详情
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2024-07-12 11:58:39
     *
     */
    public function pm_customer_stat_compare(){
        $stat_pm_obj = $this->getStatParams();

        $usage_data = $this->get_customer_compare_stat_usage($stat_pm_obj); //调用量
        $income_data = $this->get_customer_compare_stat_income($stat_pm_obj);   //收入
        $cost_data = $this->get_customer_compare_stat_cost($stat_pm_obj);   //成本
        $expend_data = $this->get_customer_compare_stat_expend($stat_pm_obj); //特殊消耗

        return $this->format_customer_compare_stat_data($stat_pm_obj, $usage_data, $income_data, $cost_data, $expend_data);
    }


    //客户维度每日调用量
    protected function get_customer_compare_stat_usage(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_usage_where();
        return StatisticsCustomerUsage::getPMCustomerStatCompareList($where,$ins,$filres);
    }

    //客户维度每日收入
    protected function get_customer_compare_stat_income(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_income_where();
        return BillProductIncomeV2::getPMCustomerStatCompareList($where,$ins,$filres);
    }

    //客户成本 按照客户分组 !!这里和客户维度统计数据相同
    protected function get_customer_compare_stat_cost(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_where();
        return BillCostV2::getPMCustomerStatCompareList($where,$ins,$filres);
    }

    //!!这里和客户维度统计数据相同
    protected function get_customer_compare_stat_expend(StatPMHelper $stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_customer_expend_where();
        $where_sub = $where_add = $where;
        $where_sub[] = ['type', '=', 1];
        $expend['sub'] = CustomerExpend::getPMCustomerStatCompareList($where_sub,$ins,$filres);
        $where_add[] = ['type', '=', 2];
        $expend['add'] = CustomerExpend::getPMCustomerStatCompareList($where_add,$ins,$filres);

        return $expend;
    }

    /**
     * 客户维度统计 每日详情 格式化
     *
     * @param $stat_pm_obj
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-07-12 12:18:30
     */
    private function format_customer_compare_stat_data($stat_pm_obj,$usage_data, $income_data, $cost_data, $expend_data) {
        $show_money = $stat_pm_obj->checkUserProductAuth('money');  //是否有权限查看产品金额
        $show_cost  = $stat_pm_obj->checkUserProductAuth('cost');   //是否有权限查看产品金额
        $show_money_agent = $stat_pm_obj->checkUserProductAuth('money_agent');

        $data = [];
        foreach ($usage_data as $item){
            $data[$item['date'].'_'.$item['product_id']] = $item;
            $father_id = $this->rel_product_list[$item['product_id']];
            $data[$item['date'].'_'.$item['product_id']]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($item['product_id']);
            $data[$item['date'].'_'.$item['product_id']]['father_id'] =$father_id;
            $data[$item['date'].'_'.$item['product_id']]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
        }
        foreach ($income_data as $item){
            $data[$item['date'].'_'.$item['product_id']]['number'] = $item['number'];
            $data[$item['date'].'_'.$item['product_id']]['money'] = $item['money'];
        }
        foreach ($cost_data as $item){
            $data[$item['date'].'_'.$item['product_id']]['cost'] = $item['money'];
        }
        foreach ($expend_data['sub'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date.'_'.$item['product_id']]['number'])){
                $data[$date.'_'.$item['product_id']]['number'] =  $data[$date.'_'.$item['product_id']]['money'] = 0;
                $data[$date.'_'.$item['product_id']]['date'] = $date;
            }
            $data[$date.'_'.$item['product_id']]['number'] -= $item['fee_number'];
            $data[$date.'_'.$item['product_id']]['money'] -= $item['money'];
        }
        foreach ($expend_data['add'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date.'_'.$item['product_id']]['number'])){
                $data[$date.'_'.$item['product_id']]['number'] =  $data[$date.'_'.$item['product_id']]['money'] = 0;
                $data[$date.'_'.$item['product_id']]['date'] = $date;
            }
            $data[$date.'_'.$item['product_id']]['number'] += $item['fee_number'];
            $data[$date.'_'.$item['product_id']]['money'] += $item['money'];
        }
        foreach ($data as $date => $value) {
            !isset($data[$date]['cost'])&&$data[$date]['cost']=0;
            !isset($data[$date]['money'])&&$data[$date]['money']=0;
        }


        foreach ($data as $demen1 => $items) {
             if(isset($items['money'])){
                if (!$show_money) {
                    $data[$demen1]['money']  = '-';
                }
             }
             if(isset($data[$demen1]['cost'])){
                if (!$show_cost) {
                    $data[$demen1]['cost']  = '-';
                }
             }
            if(isset($data[$demen1]['money_agent'])){
                if (!$show_money_agent) {
                    $data[$demen1]['money_agent']  = '-';
                }
            }
        }
        return $data;
    }

    // ---------------------------- 客户维度统计数据











    // ---------------------------- 产品维度统计数据

    /**
     * 产品视角 产品维度数据统计 列表
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-07-11 16:18:08
     *
     */
    public function pm_product_stat_list(){
        $stat_pm_obj = $this->getStatParams();

        $usage_data                = $this->get_product_stat_usage($stat_pm_obj);      //调用量
        $income_data               = $this->get_product_stat_income($stat_pm_obj);     //收入
        $cost_data                 = $this->get_product_stat_cost($stat_pm_obj);       //成本
        $fixed_cost_data           = $this->get_product_stat_fixed_cost($stat_pm_obj); //固定费用成本
        $customer_adjust_cost_data = $this->get_product_stat_adjust_cost($stat_pm_obj);//客户成本调整
        $channel_adjust_cost_data  = $this->get_product_stat_cost_adjust($stat_pm_obj);//渠道成本调整(由于历史原因名字起的不符合业务语义，其实应该叫成本调整表)
        $expend_data               = $this->get_product_stat_expend($stat_pm_obj);     //特殊消耗

        return $this->format_product_stat_data($stat_pm_obj,$usage_data, $income_data, $cost_data, $expend_data, $customer_adjust_cost_data, $fixed_cost_data, $channel_adjust_cost_data);

    }

    //客户调用量 按照客户分组 !!这里和客户维度统计数据相同
    protected function get_product_stat_usage(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_usage_where();
        return StatisticsCustomerUsage::getPMCustomerStatList($where,$ins,$filres);
    }

    //客户收入 按照客户分组 !!这里和客户维度统计数据相同
    protected function get_product_stat_income(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_income_where();
        return BillProductIncomeV2::getPMCustomerStatList($where,$ins,$filres);
    }

    //客户成本 按照客户分组 !!这里和客户维度统计数据相同
    protected function get_product_stat_cost(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_where();
        return BillCostV2::getPMCustomerStatList($where,$ins,$filres);
    }

    //客户成本 获取固定费用成本 !!这里和主产品统计数据相同
    protected function get_product_stat_fixed_cost($stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_fixed_where();
        return ChannelAccountFixedFee::getPMMainStatList($where,$ins,$filres);
    }

    //客户成本调整 按照客户分组
    protected function get_product_stat_adjust_cost(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_customer_bill_adjust_where();
        return CustomerBillAdjust::getPMProductStatList($where,$ins,$filres);
    }


    //!!这里和主产品统计数据相同
    protected function get_product_stat_cost_adjust($stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_fixed_where();
        return ChannelAccountAdjust::getPMMainStatList($where,$ins,$filres);
    }

    //!!这里和客户维度统计数据相同
    protected function get_product_stat_expend(StatPMHelper $stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_customer_expend_where();
        $where_sub = $where_add = $where;
        $where_sub[] = ['type', '=', 1];
        $expend['sub'] = CustomerExpend::getPMCustomerStatList($where_sub,$ins,$filres);
        $where_add[] = ['type', '=', 2];
        $expend['add'] = CustomerExpend::getPMCustomerStatList($where_add,$ins,$filres);

        return $expend;
    }

    /**
     * 格式化产品维度数据
     *
     * @param $stat_pm_obj StatPMHelper 用于获取权限
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @param $customer_adjust_cost_data
     * @param $fixed_cost_data
     * @param $channel_adjust_cost_data
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-07-11 16:23:46
     */
    private function format_product_stat_data($stat_pm_obj, $usage_data, $income_data, $cost_data, $expend_data, $customer_adjust_cost_data, $fixed_cost_data, $channel_adjust_cost_data) {
        $show_money = $stat_pm_obj->checkUserProductAuth('money');  //是否有权限查看产品金额
        $show_cost  = $stat_pm_obj->checkUserProductAuth('cost');   //是否有权限查看产品金额
        $data       = [];

        foreach ($usage_data as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $info['success'] = $item['success'];
            $info['total']   = $item['total'];
            $info['valid']   = $item['valid'];
            $info['cache']   = $item['cache'];
            $info['money']   = $show_money ? 0 : '-';
            $info['cost']    = $show_cost ? 0 : '-';

            $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']] = $info;
        }

        foreach ($income_data as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['number'] = $item['number'];
            $show_money && $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['money'] = $item['money'];
        }
        if ($show_cost) {
            foreach ($cost_data as $item) {
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
                $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['cost'] = $item['money'];
            }

            foreach ($fixed_cost_data as $item) {
                if ($item['money'] == 0) {
                    continue;
                }
                $customer_id = 'fixed_adjust';//在前端字典已经做了该key映射
                $apikey      = 'NO';          //在前端字典已经做了该key映射
                $operator    = 'NO';          //在前端字典已经做了该key映射
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cost']       = $item['money'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['apikey']     = $apikey;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['operator']   = $operator;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['product_id'] = $item['product_id'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['success']    = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['total']      = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['valid']      = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cache']      = 0;
            }

            //客户成本调整
            foreach ($customer_adjust_cost_data as $item) {
                if ($item['money'] == 0) {
                    continue;
                }
                $customer_id = 'customer_adjust';//在前端字典已经做了该key映射
                $apikey      = 'NO';             //在前端字典已经做了该key映射
                $operator    = 'NO';             //在前端字典已经做了该key映射
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cost']       = $item['money'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['apikey']     = $apikey;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['operator']   = $operator;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['product_id'] = $item['product_id'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['success']    = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['total']      = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['valid']      = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cache']      = 0;
            }

            //渠道成本调整
            foreach ($channel_adjust_cost_data as $item) {
                if ($item['money'] == 0) {
                    continue;
                }
                $customer_id = 'channel_adjust';//在前端字典已经做了该key映射
                $apikey      = 'NO';            //在前端字典已经做了该key映射
                $operator    = 'NO';            //在前端字典已经做了该key映射
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cost']       = $item['money'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['apikey']     = $apikey;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['operator']   = $operator;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['product_id'] = $item['product_id'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['success']    = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['total']      = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['valid']      = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cache']      = 0;
            }

        }
        foreach ($expend_data['add'] as $item) {
            if (!isset($data[$item['product_id']][$item['customer_id']]['特殊消耗'])) {
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] = 0;
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money']  = $show_money ? 0 : '-';
            }
            $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] += $item['fee_number'];
            if ($show_money) $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money'] += $item['money'];
        }
        foreach ($expend_data['sub'] as $item) {
            if (!isset($data[$item['product_id']][$item['customer_id']]['特殊消耗'])) {
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] = 0;
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money']  = $show_money ? 0 : '-';
            }
            $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] -= $item['fee_number'];
            if ($show_money) $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money'] -= $item['money'];
        }


        return $data;
    }



    /**
     * 产品维度统计 详情
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2024-07-12 11:58:39
     *
     */
    public function pm_product_stat_compare(){
        $stat_pm_obj = $this->getStatParams();

        $usage_data = $this->get_product_compare_stat_usage($stat_pm_obj); //调用量
        $income_data = $this->get_product_compare_stat_income($stat_pm_obj);   //收入
        $cost_data = $this->get_product_compare_stat_cost($stat_pm_obj);   //成本
        $expend_data = $this->get_product_compare_stat_expend($stat_pm_obj); //特殊消耗

        return $this->format_product_compare_stat_data($stat_pm_obj, $usage_data, $income_data, $cost_data, $expend_data);
    }

    //客户调用量
    protected function get_product_compare_stat_usage(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_usage_where();
        return StatisticsCustomerUsage::getPMProductStatCompareList($where,$ins,$filres);
    }

    protected function get_product_compare_stat_income(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_income_where();
        return BillProductIncomeV2::getPMProductStatCompareList($where,$ins,$filres);
    }

    protected function get_product_compare_stat_cost(StatPMHelper $stat_pm_obj) {
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_where();
        return BillCostV2::getPMProductStatCompareList($where,$ins,$filres);
    }


    protected function get_product_compare_stat_expend($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_customer_expend_where();
        $where_sub = $where_add = $where;
        $where_sub[] = ['type', '=', 1];
        $expend['sub'] = CustomerExpend::getPMProductStatCompareList($where_sub,$ins,$filres);
        $where_add[] = ['type', '=', 2];
        $expend['add'] = CustomerExpend::getPMProductStatCompareList($where_add,$ins,$filres);

        return $expend;
    }

    private function format_product_compare_stat_data($stat_pm_obj, $usage_data, $income_data, $cost_data, $expend_data) {
        $show_money = $stat_pm_obj->checkUserProductAuth('money');  //是否有权限查看产品金额
        $show_cost  = $stat_pm_obj->checkUserProductAuth('cost');   //是否有权限查看产品金额
        $show_money_agent = $stat_pm_obj->checkUserProductAuth('money_agent');

        $data = [];
        foreach ($usage_data as $item){
            $data[$item['date']] = $item;
        }
        foreach ($income_data as $item){
            $data[$item['date']]['number'] = $item['number'];
            $data[$item['date']]['money'] = $item['money'];
        }
        foreach ($cost_data as $item){
            $data[$item['date']]['cost'] = $item['money'];
        }
        foreach ($expend_data['sub'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date]['number'])){
                $data[$date]['number'] = $data[$date]['money'] = 0;
                $data[$date]['date'] = $date;
            }
            $data[$date]['number'] -= $item['fee_number'];
            $data[$date]['money'] -= $item['money'];
        }
        foreach ($expend_data['add'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date]['number'])){
                $data[$date]['number'] = $data[$date]['money'] = 0;
                $data[$date]['date'] = $date;
            }
            $data[$date]['number'] += $item['fee_number'];
            $data[$date]['money'] += $item['money'];
        }
        foreach ($data as $date => $value) {
            !isset($value['cost']) && $data[$date]['cost']=0;
            !isset($data[$date]['money'])&&$data[$date]['money']=0;
        }

        foreach ($data as $demen1 => $items) {
             if(isset($items['money'])){
                if (!$show_money) {
                    $data[$demen1]['money']  = '-';
                }
             }
             if(isset($data[$demen1]['cost'])){
                if (!$show_cost) {
                    $data[$demen1]['cost']  = '-';
                }
             }
            if(isset($data[$demen1]['money_agent'])){
                if (!$show_money_agent) {
                    $data[$demen1]['money_agent']  = '-';
                }
            }
        }
        return $data;
    }

    // ---------------------------- 产品维度统计数据











    // ---------------------------- 渠道维度统计数据

    /**
     * 渠道维度
     *
     * @return mixed
     * @throws Exception
     * <AUTHOR> 2024-07-11 18:26:34
     *
     */
    public function pm_channel_stat_list() {
        $stat_pm_obj = $this->getStatParams();

        // $channel_ids = $this->get_channel_ids($stat_pm_obj); //主产品对应的所有渠道

        // $interface_ids = $this->get_interface_ids($stat_pm_obj);   //渠道下所有的iid(接口id)
        $usage_data = $this->get_channel_stat_usage($stat_pm_obj);   //调用量
        $cost_data = $this->get_channel_stat_cost($stat_pm_obj);   //成本
        $expend_cost_data = $this->get_channel_stat_cost_adjust($stat_pm_obj); //特殊消耗成本(成本调整)
        $fixed_cost_data = $this->get_channel_stat_fixed_cost($stat_pm_obj); //固定费用成本

        return $this->format_channel_stat_data($stat_pm_obj, $usage_data, $cost_data, $expend_cost_data, $fixed_cost_data);
    }


    //!!这里和主产品统计数据相同

    /**
     *
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-11 19:23:37
     */
    protected function get_channel_stat_usage($stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_statistics_interface_usage_where();
        return StatisticsInterfaceUsage::getPMChannelStatList($where,$ins,$filres);
    }

    /**
     *
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-11 19:23:37
     */
    protected function get_channel_stat_cost($stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_cost_where();
        return BillCostV2::getPMChannelStatList($where,$ins,$filres);
    }

    /**
     *
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-03 17:25:16
     */
    protected function get_channel_stat_cost_adjust($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_cost_adjust_where();
        return ChannelAccountAdjust::getPMChannelStatList($where,$ins,$filres);
    }

    /**
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-03 17:40:07
     */
    protected function get_channel_stat_fixed_cost($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_fixed_where();
        return ChannelAccountFixedFee::getPMChannelStatList($where,$ins,$filres);
    }


    /**
     *
     *
     * @param $stat_pm_obj
     * @param $usage_data
     * @param $cost_data
     * @param $expend_cost_data
     * @param $fixed_cost_data
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-07-11 19:41:04
     */
    protected function format_channel_stat_data($stat_pm_obj, $usage_data, $cost_data, $expend_cost_data, $fixed_cost_data){
        $show_money = $stat_pm_obj->checkUserProductAuth('money');  //是否有权限查看产品金额

        $data = [];
        foreach ($usage_data as $item) {
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            $data[$channel_id][$item['interface_id']][$item['operator']] = $item;
            $show_money || $data[$channel_id][$item['interface_id']][$item['operator']]['cost'] = '-';
        }
        if($show_money) {
            foreach ($cost_data as $item){
                $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
                if (isset($data[$channel_id][$item['interface_id']][$item['operator']])){
                    $data[$channel_id][$item['interface_id']][$item['operator']]['cost'] = $item['money'];
                }
            }

            //特殊消耗成本
            foreach ($expend_cost_data as &$item) {
                if($item['interface_id'] == 0){
                    continue;
                }
                $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
                $interface_id = 'expend';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射

                $total_cost = $data[$channel_id][$interface_id][$operator]['cost'] ?? 0;
                $total_cost = bcadd($total_cost, $item['money'], 6);
                $data[$channel_id][$interface_id][$operator]['cost'] = $total_cost;
                $data[$channel_id][$interface_id][$operator]['success'] = 0;
                $data[$channel_id][$interface_id][$operator]['total'] = 0;
                $data[$channel_id][$interface_id][$operator]['valid'] = 0;
            }

            //固定费用成本
            foreach ($fixed_cost_data as &$item) {
                $channel_id = $item['channel_id'];
                $interface_id = 'fixed';//在前端字典已经做了该key映射
                $operator     = 'NO';//在前端字典已经做了该key映射
                $total_cost = $data[$channel_id][$interface_id][$operator]['cost'] ?? 0;
                $total_cost = bcadd($total_cost, $item['money'], 6);
                $data[$channel_id][$interface_id][$operator]['cost'] = $total_cost;
                $data[$channel_id][$interface_id][$operator]['success'] = 0;
                $data[$channel_id][$interface_id][$operator]['total'] = 0;
                $data[$channel_id][$interface_id][$operator]['valid'] = 0;
            }
        }
        return $data;
    }


    /**
     *
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-07-19 16:11:30
     *
     */
    public function pm_channel_stat_compare() {
        $stat_pm_obj = $this->getStatParams();



        $usage_data = $this->get_channel_compare_stat_usage($stat_pm_obj);   //调用量
        $cost_data = $this->get_channel_compare_stat_cost($stat_pm_obj);   //成本
        $expend_cost_data = $this->get_channel_compare_stat_cost_adjust($stat_pm_obj); //特殊消耗成本(成本调整)
        $fixed_cost_data = $this->get_channel_compare_stat_fixed_cost($stat_pm_obj); //固定费用成本

        return $this->format_channel_compare_stat_data($stat_pm_obj, $usage_data,$cost_data, $expend_cost_data, $fixed_cost_data);
    }


    //!!这里和主产品统计数据相同

    /**
     *
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-11 19:23:37
     */
    protected function get_channel_compare_stat_usage($stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_statistics_interface_usage_where();
        return StatisticsInterfaceUsage::getPMChannelStatCompareList($where,$ins,$filres);
    }

    /**
     * 获取渠道日明细 这个和产品的方法一样,不再重写一个新的方法
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-11 19:23:37
     */
    protected function get_channel_compare_stat_cost($stat_pm_obj){
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_where();
        return BillCostV2::getPMProductStatCompareList($where,$ins,$filres);
    }

    /**
     *
     *
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-03 17:25:16
     */
    protected function get_channel_compare_stat_cost_adjust($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_cost_adjust_where();
        return ChannelAccountAdjust::getPMChannelStatCompareList($where,$ins,$filres);
    }

    /**
     * @param $stat_pm_obj StatPMHelper
     *
     * @return array
     * <AUTHOR> 2024-07-03 17:40:07
     */
    protected function get_channel_compare_stat_fixed_cost($stat_pm_obj){
        //获取条件
        [$where,$ins,$filres] = $stat_pm_obj->get_channel_fixed_where();
        return ChannelAccountFixedFee::getPMChannelStatCompareList($where,$ins,$filres);
    }


    /**
     * 渠道每日详情
     *
     * @param $stat_pm_obj StatPMHelper
     * @param $usage_data
     * @param $cost_data
     * @param $expend_cost_data
     * @param $fixed_cost_data
     *
     * @return array
     * <AUTHOR> 2024-07-25 11:11:00
     */
    private function format_channel_compare_stat_data($stat_pm_obj, $usage_data,$cost_data, $expend_cost_data, $fixed_cost_data) {
        $show_money = $stat_pm_obj->checkUserProductAuth('money');  //是否有权限查看产品金额

        $t = [
            'total' => 0,
            'success' => 0,
            'valid' => 0,
            'cost' => 0,
            'number' => 0,
            'money' => 0,
        ];

        $data = [];
        foreach ($usage_data as $item){
            $da = $item['date'];
            if(key_exists($da, $data)) {
                $data[$da]['total'] = $data[$da]['total'] + $item['total'];
                $data[$da]['success'] = $data[$da]['success'] + $item['success'];
                $data[$da]['valid'] = $data[$da]['valid'] + $item['valid'];
            }else{
                $data[$da] = $item;
            }
        }
        foreach ($cost_data as $item){
            $da = $item['date'];
            if(!key_exists($da, $data)) {
                $data[$da] = $t;
            }
            if(!key_exists('cost', $data[$da])){
                $data[$da]['cost'] = 0;
            }
            $data[$da]['cost'] = $data[$da]['cost'] + $item['money'];
        }
        foreach ($expend_cost_data as $item){
            $da = $item['date'];
            if(!key_exists($da, $data)) {
                $data[$da] = $t;
            }
            if(!key_exists('number', $data[$da])){
                $data[$da]['number'] = $item['number'];
                $data[$da]['money'] = $item['money'];
            }
            if(!key_exists('money', $data[$da])){
                $data[$da]['money'] = $item['money'];
            }
            $data[$da]['number'] = $data[$da]['number'] + $item['number'];
            $data[$da]['money'] = $data[$da]['money'] + $item['money'];
        }
        foreach ($fixed_cost_data as $item){
            $da = $item['date'];
            if(!key_exists($da, $data)) {
                $data[$da] = $t;
            }
            if(!key_exists('number', $data[$da])){
                $data[$da]['number'] = $item['number'];
                $data[$da]['money'] = $item['money'];
            }
            if(!key_exists('money', $data[$da])){
                $data[$da]['money'] = $item['money'];
            }
            $data[$da]['number'] = $data[$da]['number'] + $item['number'];
            $data[$da]['money'] = $data[$da]['money'] + $item['money'];
        }
        foreach ($data as $date => $value) {
            !isset($value['cost']) && $data[$date]['cost']=0;
            !isset($data[$date]['money'])&&$data[$date]['money']=0;
        }

        if (!$show_money) {
            foreach ($data as $demen => $items) {
                if (isset($data[$demen]['cost'])) {
                    $data[$demen]['cost'] = '-';
                }
            }
        }
        return $data;
    }
    // ---------------------------- 渠道维度统计数据
}