<?php

namespace App\Http\Repository;


use App\Models\Account;
use App\Models\ClickHouse\BaseModel;
use App\Models\ClickHouse\ImportProduct;
use App\Models\Customer;

class ImportantProductRepository extends PublicRepository
{
    private  $importproduct;
    private  $product = [
          200=>'邦秒验',
          210=>'邦信分-通信字段',
          615=>'号码风险等级',
          1000=>'邦信分-通信评分',
          10000=>'号码分',
    ];
    //确定标红的行
    private  $row_index = [10000=>0,1000=>1,615=>2,210=>3,200=>4];
    //查得率 下限
   // private  $chad_rate = [200=>90,210=>80,615=>98,1000=>50,10000=>99];
    private  $chad_rate = [10000=>0,1000=>0,615=>0,210=>0,200=>0];
    //超1s 占比 上限
    private  $more_one_up_rate = [10000=>100,1000=>100,615=>100,210=>100,200=>100];
    //0.8-0.5 占比 范围
    private  $more_five_up_rate = [10000=>[0,100],1000=>[0,100],615=>[0,100],210=>[0,100],200=>[0,100]];
    //0.5-0.3 占比 范围
    private  $more_three_up_rate = [10000=>[0,100],1000=>[0,100],615=>[0,100],210=>[0,100],200=>[0,100]];
    //异常 占比 上限
    private  $abnorma_rate = [10000=>100,1000=>100,615=>100,210=>100,200=>100];

    public function __construct()
    {
       $this->importproduct = new ImportProduct();
    }

    public function getImportProductInfo($start_time,$end_time,$test_data)
	{
	    //测试账号
        if ($test_data){
            $apikey =  Account::getApikeyByCustomId(['C20200622KF31GS','C20180828LOCNMG']);
            $apikeys = array_column($apikey,'apikey');

            $apikeys = implode("','",$apikeys);
            $apikeys = "'" . $apikeys ."'";
        }else{
            $apikeys = "''";
        }


	    //总调用量
         $total_amount = $this->importproduct->getProductAmountGroupPid($start_time,$end_time,$apikeys);
         $total_amount = array_column($total_amount,'count','pid');
         //昨日调用量
         $yestoday_start_time = $start_time - 86400;
         $yestoday_end_time = $end_time - 86400;
         $total_yestoday_amount = $this->importproduct->getProductAmountGroupPid($yestoday_start_time,$yestoday_end_time,$apikeys);
         $total_yestoday_amount = array_column($total_yestoday_amount,'count','pid');
         //查的量
         $check_num = $this->importproduct->getProductValidGroupPid($start_time,$end_time,$apikeys);
         $check_num = array_column($check_num,'count','pid');

         $check_yestoday_num = $this->importproduct->getProductValidGroupPid($yestoday_start_time,$yestoday_end_time,$apikeys);
         $check_yestoday_num = array_column($check_yestoday_num,'count','pid');

         //平均响应时间
         $avg_time = $this->importproduct->getAvgRequestTime($start_time,$end_time,$apikeys);
         $avg_time = array_column($avg_time,'time','pid');

        $avg_yestoday_time = $this->importproduct->getAvgRequestTime($yestoday_start_time,$yestoday_end_time,$apikeys);
        $avg_yestoday_time = array_column($avg_yestoday_time,'time','pid');
         //notinapikey customid  in C20200622KF31GS  C20180828LOCNMG


         //北京机房数
         $beijing = $this->importproduct->getBjRequestNum($start_time,$end_time,$apikeys);
         $beijing = array_column($beijing,'count','pid');
         //北京机房昨日
         $beijing_yestoday = $this->importproduct->getBjRequestNum($yestoday_start_time,$yestoday_end_time,$apikeys);
         $beijing_yestoday = array_column($beijing_yestoday,'count','pid');

         //异常数量
         $abnorma_num = $this->importproduct->abnorma($start_time,$end_time,$apikeys);
         $abnorma_num = array_column($abnorma_num,'count','pid');

         //tp90
        $tp = $this->importproduct->tp($start_time,$end_time,$apikeys);
        $tp = array_column($tp,null,'pid');

        $yestoday_tp = $this->importproduct->tp($yestoday_start_time,$yestoday_end_time,$apikeys);
        $yestoday_tp = array_column($yestoday_tp,null,'pid');


        $result_data =[];
        $backgroud = [];
        foreach ($total_amount as $k=>$v){
            $product_name = $this->product[$k];
            $total_yestoday_amount_count = isset($total_yestoday_amount[$k]) ? $total_yestoday_amount[$k] : 0;
            $total_yestoday_amount_count  = $total_yestoday_amount_count>1000 ? sprintf('%.2f',$total_yestoday_amount_count/10000).'万' : $total_yestoday_amount_count;
            $total_amount_count = $v > 1000 ? sprintf('%.2f',$v/10000).'万' : $v;
            $total_amount = "$total_amount_count($total_yestoday_amount_count)";
            $check_yestoday_rate = bcdiv($check_yestoday_num[$k]*100,$total_yestoday_amount[$k],2).'%';
            $check_rate = bcdiv($check_num[$k]*100,$v,2).'%'."($check_yestoday_rate)";
             //查得率
            if ($check_rate < $this->chad_rate[$k]){
                $backgroud[] = [2,$this->row_index[$k]];
            }
            //北京机房
            $beijing_num = isset($beijing[$k]) ? $beijing[$k] : 0;
            $beijing_yestoday_num = isset($beijing_yestoday[$k]) ? $beijing_yestoday[$k] : 0;

            $beijing_amount = "$beijing_num($beijing_yestoday_num)";

            //异常数占比
            $abnorma_num[$k] = isset($abnorma_num[$k]) ? $abnorma_num[$k] : 0;
            $abnorma_num_num = $abnorma_num[$k] > 1000 ? sprintf('%.2f',$abnorma_num[$k]/10000).'万' : $abnorma_num[$k];
            $abnorma_num_rate =bcdiv($abnorma_num[$k]*100,$v,2).'%'.'/'.$abnorma_num_num;
            if (bcdiv($abnorma_num[$k]*100,$v,2) > $this->abnorma_rate[$k]){
                $backgroud[] = [8,$this->row_index[$k]];
            }

            $tp90 = $tp[$k]['tp90'].'('.$yestoday_tp[$k]['tp90'].')';
            $tp95 = $tp[$k]['tp95'].'('.$yestoday_tp[$k]['tp95'].')';
            $tp99 = $tp[$k]['tp99'].'('.$yestoday_tp[$k]['tp99'].')';

            $result_data[] = ['name'=>$product_name,'total_amount'=>$total_amount,'check_num'=>$check_num[$k],'check_rate'=>$check_rate,'avg_time'=>round($avg_time[$k]).'('. round($avg_yestoday_time[$k]).')','beijing_amount'=>$beijing_amount
            ,'abnorma_num_rate'=>$abnorma_num_rate,'tp90'=>$tp90,'tp95'=>$tp95,'tp99'=>$tp99];

        }
            return ['data'=>$result_data,'backgroud'=>$backgroud ];
	}


}