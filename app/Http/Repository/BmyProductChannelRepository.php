<?php

namespace App\Http\Repository;

use App\Models\Account;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\ConfigInterfaceShuntLog;
use App\Models\ConfigPriceInterface;
use App\Models\StatisticsInterfaceUsage;
use App\Models\ConfigInterfaceShunt;
use App\Models\ConfigChannelAccount;
use App\Models\Product;
use App\Models\AccountProduct;

class BmyProductChannelRepository
{
    private $operator = [
        'ALL' => '不区分',
        'CMCC' => '移动',
        'CUCC' => '联通',
        'CTCC' => '电信',
        'CBN' => '广电',
    ];

    private $encrypt_way = [
        'ALL' => '不区分',
        'CLEAR' => '明文',
        'SHA256' => 'SHA256',
        'MD5' => 'MD5',
    ];

    private $shunt_v2_fatherIds = [200,210,1000,30000];
    private $shunt_v2_filter_channelIds = [501,502,503,142];

    public function getConfigPriceInterfaceList()
    {
        $channel_id = intval(request()->post('channel_id', null));
        $interface_name = request()->post('interface_name', null);
        // 获取邦秒验所有的channel_id
        $channel_info = ChannelProduct::getChannelInfoByProductId(200, $channel_id);
        $channel_info = array_column($channel_info, null, 'channel_id');
        $channel_ids = $channel_info ? array_column($channel_info, 'channel_id') : [];
        $channel_ids = $channel_ids ? implode(',', $channel_ids) : '';

        // 获取渠道、接口关联信息
        $channel_interface_info = ChannelInterface::getInterfaceChannelInfo($channel_ids, $interface_name);
        $channel_interface_info = array_column($channel_interface_info, null, 'interface_id');
        $interface_ids = $channel_interface_info ? array_column($channel_interface_info, 'interface_id') : [];
        $interface_ids = implode(',', $interface_ids);

        $price_info = ConfigPriceInterface::getLatestPriceByIids($interface_ids);

        $end = date('Ymd');
        $start = date('Ymd', strtotime('-1 days'));
        $stat_all = StatisticsInterfaceUsage::getInterfaceValidStat($interface_ids, $start, $end);
        $stat_list = [];
        foreach ($stat_all as $item) {
            if (in_array($item['encrypt'], ['MD5', 'SHA256'])) {
                $stat_list[$item['interface_id']][$item['operator']]['md5_sha256'] = isset($stat_list[$item['interface_id']][$item['operator']]['md5_sha256']) ? $stat_list[$item['interface_id']][$item['operator']]['md5_sha256'] + $item['valid'] : $item['valid'];
            } elseif ($item['encrypt'] == 'CLEAR') {
                $stat_list[$item['interface_id']][$item['operator']]['clear'] = $item['valid'];
            }
        }
        try {
            $list = $channel_list = [];
            foreach ($price_info as $value) {
                $data = [];
                $price = json_decode($value['price'], true);
                $channel_interface = $channel_interface_info[$value['interface_id']];
                $stat = isset($stat_list[$value['interface_id']]) ? $stat_list[$value['interface_id']] : [];
                $channel_name = $channel_interface['channel_name'];
                $channel_id = $channel_interface['channel_id'];
                $data['interface_label'] = $channel_interface['interface_label'];
                $data['operator'] = '';
                $operator = $this->getPriceInfo($price);

                foreach ($operator as $key => $item) {
                    $form_key = $channel_interface['interface_name'] . '_' . $key;
                    foreach ($channel_info as $val) {
                        $data[$val['name']] = isset($list[$form_key][$val['name']]) ? $list[$form_key][$val['name']] : '';
                    }

                    $valid_md5_sha256 = isset($stat[$key]['md5_sha256']) ? intval($stat[$key]['md5_sha256']) : 0;
                    $valid_clear = isset($stat[$key]['clear']) ? intval($stat[$key]['clear']) : 0;

                    $data['operator'] = $this->operator[$key];
                    $data[$channel_name] = $item . '/' . $valid_md5_sha256 . '_' . $valid_clear;
                    $data['channel_num'] = isset($list[$form_key]['channel_num']) ? $list[$form_key]['channel_num'] + 1 : 1;
                    $data['operator_en'] = $key;
                    $data['stat_num_md5_sha256'] = isset($list[$form_key]['stat_num_md5_sha256']) ? $list[$form_key]['stat_num_md5_sha256'] + $valid_md5_sha256 : $valid_md5_sha256;
                    $data['stat_num_clear'] = isset($list[$form_key]['stat_num_clear']) ? $list[$form_key]['stat_num_clear'] + $valid_clear : $valid_clear;
                    $list[$form_key] = $data;
                }
                $channel_list[$channel_interface['interface_name']][$channel_id] = $channel_info[$channel_id];
            }

            $sort_key = array_column($list, 'channel_num');
            //$operator_key = array_column($list, 'operator');
            array_multisort($sort_key, SORT_DESC, $list);
            //array_multisort($sort_key, SORT_DESC, $operator_key, SORT_DESC, $list);


            foreach ($list as &$value) {
                foreach ($value as $kk => &$vv) {
                    $exp = explode('/', $vv);
                    if (isset($exp[1])) {
                        $stat = explode('_', $exp[1]);
                        $md5_sha256 = $value['stat_num_md5_sha256'] > 0 ? round($stat[0] / $value['stat_num_md5_sha256'] * 100, 1) : '0';
                        $clear = $value['stat_num_clear'] > 0 ? round($stat[1] / $value['stat_num_clear'] * 100, 1) : '0';
                        $vv = $exp[0] . PHP_EOL . $md5_sha256 . '(' . $clear . ')%';
                    }
                }
            }

            if ($interface_name) {
                $channel_info = $channel_list[$interface_name];
            }

            return ['channel_info' => array_values($channel_info), 'list' => array_values($list)];
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    protected function getPriceInfo($price)
    {
        $operator = [];
        foreach ($price as $val) {
            if ($val['encrypt_way'] == 'MD5') {
                if (isset($operator[$val['operator']])) {
                    preg_match('/^\d*\.?\d*/', $operator[$val['operator']], $matches);
                    $p = isset($matches[0]) && $matches[0] ? str_replace($matches[0], $val['price'], $operator[$val['operator']]) : $val['price'] . $operator[$val['operator']];
                } else {
                    $p = $val['price'];
                }
                $operator[$val['operator']] = $p;
            } elseif ($val['encrypt_way'] == 'SHA256') {
                if (isset($operator[$val['operator']])) {
                    $flag = preg_match('/^\d/', $operator[$val['operator']]);
                    $p = $flag ? $operator[$val['operator']] : $val['price'] . $operator[$val['operator']];
                } else {
                    $p = $val['price'];
                }
                $operator[$val['operator']] = $p;
            } elseif ($val['encrypt_way'] == 'CLEAR') {
                $operator[$val['operator']] = isset($operator[$val['operator']]) ? $operator[$val['operator']] . '(' . $val['price'] . ')' : '(' . $val['price'] . ')';
            } elseif ($val['encrypt_way'] == 'ALL' && $val['operator'] == 'ALL') {
                $p = $val['price'] . '(' . $val['price'] . ')';
                $operator['CMCC'] = $operator['CUCC'] = $operator['CTCC'] = $operator['CBN'] = $p;
            } elseif ($val['encrypt_way'] == 'ALL' && $val['operator'] != 'ALL') {
                $p = $val['price'] . '(' . $val['price'] . ')';
                $operator[$val['operator']] = $p;
            }
        }

        if(!isset($operator['CBN'])){
            $operator['CBN'] = '0(0)';
        }

        return $operator;
    }

    public function getChannelList()
    {
        $type = request()->post('type', null);
        if ($type == 'all') {
            $product_info = request()->post('product_name', null);
            $product_info = $product_info ? explode('_', $product_info) : [];
            $product_name = isset($product_info[1]) ? $product_info[1] : '';
            // 获取邦秒验所有的channel_id 包括自有渠道
            $channel_info = ChannelProduct::getChannelListByProductId(200, $product_name);
            $channel_info = array_column($channel_info, 'label', 'channel_id');
        } else {
            // 获取邦秒验所有的channel_id
            $channel_info = ChannelProduct::getChannelInfoByProductId(200);
            $channel_info = array_column($channel_info, 'label', 'channel_id');
        }

        return $channel_info;
    }

    public function getChannelListV2()
    {
       $channel_ids = ChannelProduct::getChannelInfoByProductIdsFilter($this->shunt_v2_fatherIds,$this->shunt_v2_filter_channelIds);
       $channel_info = Channel::getChannelInfoByChannelIds($channel_ids);
       $channel_info = array_column($channel_info, 'label', 'channel_id');
       return $channel_info;
    }

    public function getInterfaceList()
    {
        $channel_info = ChannelProduct::getChannelInfoByProductId(200);
        $channel_ids = array_column($channel_info, 'channel_id');
        $channel_ids = $channel_ids ? implode(',', $channel_ids) : '';
        $interface_list = ChannelInterface::getInterfaceNameList($channel_ids);
        return array_column($interface_list, 'label', 'name');
    }

    public function getProductListV2()
    {
        $channel_info = ChannelProduct::getChannelInfoByProductId(200);
        $channel_info = array_column($channel_info, null, 'channel_id');
        $channel_ids = $channel_info ? array_column($channel_info, 'channel_id') : [];
        $channel_ids = $channel_ids ? implode(',', $channel_ids) : '';
        // 获取渠道、接口关联信息
        $channel_interface_info = ChannelInterface::getInterfaceChannelInfo($channel_ids);
        $bmy_product_info = [];
        foreach ($channel_interface_info as $item){
            if(!isset($bmy_product_info[$item['interface_name']])){
                $bmy_product_info[$item['interface_name']] = ['product_id'=>$item['interface_id'],'product_name'=>$item['interface_label']];
            }
        }
        $products = array_merge(array_values($bmy_product_info),[['product_id'=>210,'product_name'=>'通信指数'],['product_id'=>1000,'product_name'=>'通信评分'],['product_id'=>30000,'product_name'=>'AI创新']]);
        return $products;
    }

    public function getAccountProductList()
    {
        $account_info = request()->get('account_id', null);
        $account_id = explode('_', $account_info)[0];
        $product_list = Product::getListByCondition(['father_id' => 200], ['product_id', 'product_name'])->toArray();
        $product_list = array_column($product_list, null, 'product_id');
        $product_ids = array_column($product_list, 'product_id');
        $account_product = AccountProduct::getList(['account_id' => $account_id, 'product_id' => $product_ids], ['product_id']);
        $list = [];
        foreach ($account_product as $value) {
            $list[$value['product_id']] = $product_list[$value['product_id']]['product_name'];
        }
        return $list;
    }

    public function getShuntList()
    {
        $channel_id = intval(request()->post('channel_id', null));
        $interface_name = request()->post('interface_name', null);
        // 获取邦秒验所有的channel_id
        $channel_info = ChannelProduct::getChannelInfoByProductId(200, $channel_id);
        $channel_info = array_column($channel_info, null, 'channel_id');
        $channel_ids = $channel_info ? array_column($channel_info, 'channel_id') : [];
        $channel_ids = $channel_ids ? implode(',', $channel_ids) : '';

        // 获取渠道、接口关联信息
        $channel_interface_info = ChannelInterface::getInterfaceChannelInfo($channel_ids, $interface_name);
        $channel_interface_info = array_column($channel_interface_info, null, 'interface_id');
        $interface_ids = $channel_interface_info ? array_column($channel_interface_info, 'interface_id') : [];
        $interface_ids = implode(',', $interface_ids);

        $price_list = ConfigPriceInterface::getLatestPriceByIids($interface_ids);

        $shunt_all = ConfigInterfaceShunt::getAll($interface_ids);
        $shunt_list = [];
        foreach ($shunt_all as $value) {
            $shunt_list[$value['operator']][$value['encrypt']][$value['interface_id']] = $value['ratio'];
        }

        $list = $channel_list = [];
        foreach ($price_list as $value) {
            $data = [];
            $price_info = json_decode($value['price'], true);
            $channel_interface = $channel_interface_info[$value['interface_id']];
            $channel_name = $channel_interface['channel_name'];
            $channel_id = $channel_interface['channel_id'];
            $data['interface_label'] = $channel_interface['interface_label'];
            $data['operator'] = '';
            $data['encrypt'] = '';
            $encrypt = $this->getOperatorEncryptPrice($price_info);
            foreach ($encrypt as $key => $item) {
                foreach ($item as $kk => $price) {
                    $form_key = $channel_interface['interface_name'] . '_' . $key . '_' . $kk;
                    foreach ($channel_info as $val) {
                        $data[$val['name']] = isset($list[$form_key][$val['name']]) ? $list[$form_key][$val['name']] : ['price' => '', 'ratio' => ''];
                    }

                    $ratio = isset($shunt_list[$key][$kk][$value['interface_id']]) ? $shunt_list[$key][$kk][$value['interface_id']] . '%' : '0%';
                    $data['operator'] = $this->operator[$key];
                    $data['encrypt'] = $this->encrypt_way[$kk];
                    $data[$channel_name]['price'] = $price ? $price : '';
                    $data[$channel_name]['ratio'] = $price ? $ratio : '';
                    $data[$channel_name]['interface_id'] = $value['interface_id'];
                    $data[$channel_name]['channel_id'] = $channel_id;
                    $data['channel_num'] = isset($list[$form_key]['channel_num']) ? $list[$form_key]['channel_num'] + 1 : 1;
                    $data['operator_en'] = $key;
                    $data['encrypt_en'] = $kk;
                    $list[$form_key] = $data;
                }
            }
            $channel_list[$channel_interface['interface_name']][$channel_id] = $channel_info[$channel_id];
        }

        $sort_key = array_column($list, 'channel_num');
        array_multisort($sort_key, SORT_DESC, $list);

        if ($interface_name) {
            $channel_info = $channel_list[$interface_name];
        }

        return ['channel_info' => array_values($channel_info), 'list' => array_values($list)];
    }

    public function getShuntListV2()
    {

        $channel_id = intval(request()->post('channel_id', ''));
        $product_id = request()->post('product_id', '');
        $start_date = request()->post('start_date', '');
        $end_date = request()->post('end_date', '');
         //获取产品信息
        $products = Product::getAll([['father_id','in',$this->shunt_v2_fatherIds]]);
        $product_ids = array_column($products,'product_id');
        $product_father_map = array_column($products,'father_id','product_id');
        // 获取渠道信息
        $channel_info = $this->getChannelInfo($channel_id, $product_id);
        $channel_id = $channel_info['ids'];
        // 接口关联信息
        $interface_info = $this->getInterfaceInfo($product_id,$channel_id,$channel_info);
        $interface_idstr = $interface_info['ids'];
        $channel_interface_info = $interface_info['channel_interface_info'];
        $interface_ids = explode(',',$interface_idstr);
         //价格列表
        $price_list = ConfigPriceInterface::getLatestPriceByIids($interface_idstr);
        // 分流配置
        $shunt_all = ConfigInterfaceShunt::getAll($interface_idstr);
        // 调用量
        $where = $this->getShuntListV2Where($start_date,$end_date);
        $use_data = StatisticsInterfaceUsage::getStatListByProductChannel($where,$product_ids,$interface_ids);
        // 格式化数据
        $format_price_data = $this->formatChannelPriceData($price_list,$channel_interface_info);
        $format_use_data = $this->formatUseData($use_data,$product_father_map,$channel_interface_info);
        $format_shunt_data = $this->formatShuntData($shunt_all,$channel_interface_info);
        $merge_data = $this->mergeData($format_price_data,$format_use_data,$format_shunt_data);
        $format_data = $this->formatData($merge_data);

        return $format_data;
    }

    /**
     * 获取接口信息
     */
    protected function getInterfaceInfo($productId, $channelIds, array $channelInfo): array
    {
        $channelInterfaceInfo = ChannelInterface::getInterfaceChannelInfo($channelIds);
        $res = ['channel_interface_info' => array_column($channelInterfaceInfo, null, 'interface_id')];
        if (!$productId) {
            // 获取所有接口ID
            $interfaceIds = $channelInterfaceInfo ? array_column($channelInterfaceInfo, 'interface_id') : [];
        }else{
            if (in_array($productId, $this->shunt_v2_fatherIds)) {
                // 获取父产品关联的渠道接口
                $channelIds = ChannelProduct::getChannelInfoByProductIdsFilter(
                    [$productId],
                    $this->shunt_v2_filter_channelIds
                );
                $channelInterfaceInfo = ChannelInterface::getInterfaceChannelInfo(implode(',', $channelIds));
                $interfaceIds = $channelInterfaceInfo ? array_column($channelInterfaceInfo, 'interface_id') : [];
            }else{
                // 获取单个产品接口
                $interfaceName = ChannelInterface::getInterfaceInfo(['id' => $productId])->name;
                $interfaceIds = ChannelInterface::getListByCondition(
                    ['name' => $interfaceName],
                    ['id']
                )->pluck('id')->toArray();
            }
        }
        return  array_merge($res,['ids' => implode(',', $interfaceIds)]);
    }

    protected function getChannelInfo($channelId, $productId): array
    {
        // 如果有指定渠道ID，直接获取该渠道信息
        if ($channelId) {
            $channelInfo = Channel::getChannelInfoById($channelId);
            return [
                'info' => array_column($channelInfo, null, 'channel_id'),
                'ids' => $channelId
            ];
        }
        // 获取产品关联的渠道ID
        $channelIds = ChannelProduct::getChannelInfoByProductIdsFilter(
            $this->shunt_v2_fatherIds,
            $this->shunt_v2_filter_channelIds
        );
        $channelInfo = Channel::getChannelByChannelIdS($channelIds);
        $channelInfo = array_column($channelInfo, null, 'channel_id');
        return [
            'info' => $channelInfo,
            'ids' => $channelInfo ? implode(',', array_column($channelInfo, 'channel_id')) : ''
        ];
    }

    private function formatData($merge_data){
        $format_data = [];
        $opeartor_order =  ['移动' => 1, '联通' => 2, '电信' => 3, '广电'=>4];
        $encrypt_order =  ['明文' => 1, 'MD5' => 2, 'SHA256' => 3];

        $sums = [];
        foreach ($merge_data as $item) {
            $interface = trim($item['interface_label']);
            $channel = $item['channel_label'];

            if (!isset($sums[$interface])) {
                $sums[$interface] = [
                    'total' => 0,
                    'channels' => []
                ];
            }
            $sums[$interface]['total'] += (int)$item['total'];
            if (!isset($sums[$interface]['channels'][$channel])) {
                $sums[$interface]['channels'][$channel] = 0;
            }
            $sums[$interface]['channels'][$channel] += (int)$item['total'];
        }

        // 先按 interface_label 分组
        usort($merge_data, function($a, $b) use($sums,$opeartor_order,$encrypt_order)  {
            $aInterface = trim($a['interface_label']);
            $bInterface = trim($b['interface_label']);
            //按照总量排序
            if ($sums[$aInterface]['total'] != $sums[$bInterface]['total']) {
                return $sums[$bInterface]['total'] <=> $sums[$aInterface]['total'];
            }
            //相同的产品排在一起
            $interfaceCmp = strcmp($aInterface, $bInterface);
            if ($interfaceCmp !== 0) {
                return $interfaceCmp;
            }
            $aChannelSum = $sums[$aInterface]['channels'][$a['channel_label']];
            $bChannelSum = $sums[$bInterface]['channels'][$b['channel_label']];
            if ($aChannelSum != $bChannelSum) {
                return $bChannelSum <=> $aChannelSum;
            }
             //渠道相同的排在一起
            $channelCmp = strcmp($a['channel_label'], $b['channel_label']);
            if ($channelCmp !== 0) {
                return $channelCmp;
            }
            // 3. 运营商按指定顺序排序
            $operatorOrderA = $opeartor_order[$a['operator_label']] ?? PHP_INT_MAX;
            $operatorOrderB = $opeartor_order[$b['operator_label']] ?? PHP_INT_MAX;
            if ($operatorOrderA != $operatorOrderB) {
                return $operatorOrderA - $operatorOrderB;
            }
            // 4. 最后按加密方式排序
            $encryptOrderA = $encrypt_order[$a['encrypt_label']] ?? PHP_INT_MAX;
            $encryptOrderB = $encrypt_order[$b['encrypt_label']] ?? PHP_INT_MAX;
            return $encryptOrderA - $encryptOrderB;
        });

        foreach ($merge_data as $item){
            $interface = trim($item['interface_label']);
            $channel = $item['channel_label'];
            $interface_total = $sums[$interface]['total'];
            $channel_total = $sums[$interface]['channels'][$channel];
            $real_total = $item['total'];
            $item['total_ratio'] = $interface_total > 0 ? number_format(($channel_total / $interface_total) * 100, 2) . '%' : '0%';
            $item['real_ratio'] = $interface_total > 0 ? number_format(($real_total / $interface_total) * 100, 2) . '%' : '0%';
            $item['total'] =  number_format($item['total']);
            $format_data[] = $item;
        }

        return $format_data;
    }
    private function mergeData($format_price_datas,$format_use_data,$format_shunt_data){
        $merge_data = [];
        foreach ($format_price_datas as $format_price_data){
            foreach ($format_price_data as $key => $item){
                $use_data = $format_use_data[$key] ?? [];
                $shunt_data = $format_shunt_data[$key] ?? [];
                $res = [
                    'interface_label' => $item['interface_label'],
                    'channel_id' => $item['channel_id'],
                    'channel_label' => $item['channel_label'],
                    'operator' => $item['operator'],
                    'operator_label' => $this->operator[$item['operator']] ?? $item['operator'],
                    'encrypt_way' => $item['encrypt_way'],
                    'encrypt_label' => $this->encrypt_way[$item['encrypt_way']] ?? $item['encrypt_way'],
                    'price' => $item['price'],
                    'total' => (int)($use_data['total'] ?? 0),
                    'config_ratio' => isset($shunt_data['ratio']) ? $shunt_data['ratio']. '%' :  '0%',
                ];
                $merge_data[] = $res;
            }
        }
        return $merge_data;
    }

    private  function formatShuntData($shunt_data,$channel_interface_info){
        $new_shunt_data = [];
        foreach ($shunt_data as $item){
            $channel_interface = $channel_interface_info[$item['interface_id']];
            $key = $item['channel_id'] . '_' . $channel_interface['interface_name'] . '_' .$item['operator'] . '_' .$item['encrypt'];
            if (!isset($new_shunt_data[$key])){
                $new_shunt_data[$key]['ratio'] = $item['ratio'];
            }
        }
        return $new_shunt_data;
    }


    private  function formatUseData($use_data,$product_father_map,$channel_interface_info){

        $res = [];
        foreach ($use_data as $item){
             $father_id = $product_father_map[$item['product_id']] ?? '';
             $channel_interface = $channel_interface_info[$item['interface_id']];
             $product_id = $item['product_id'];

             if (strpos($item['operator'],'CMCC') !== false){
                 $operator = 'CMCC';
             }elseif (strpos($item['operator'],'CUCC') !== false){
                 $operator = 'CUCC';
             }elseif (strpos($item['operator'],'CTCC') !== false){
                 $operator = 'CTCC';
             }elseif (strpos($item['operator'],'CBN') !== false){
                 $operator = 'CBN';
             }else{
                 continue;
            }
             if ($item['encrypt'] == ''){
                 $encrypt = 'CLEAR';
             }else{
                 $encrypt = $item['encrypt'];
             }
             $key = $channel_interface['channel_id']. '_' . $channel_interface['interface_name'] . '_' .$operator. '_' .$encrypt;
            if (!isset($res[$key])){
                $interface_label = '';
                if (isset($merge_father_ids[$product_id])){
                    $interface_label = $merge_father_ids[$product_id];
                }
                 $res[$key] = [
                      'father_id'=>$father_id,
                      'product_id'=>$item['product_id'],
                      'product_name'=>$item['product_id'],
                      'channel_id'=>$channel_interface['channel_id'],
                      'interface_id'=>$item['interface_id'],
                      'interface_label'=> $interface_label,
                      'operator'=>$operator,
                      'encrypt_way'=>$encrypt,
                      'total'=>$item['total'],
                 ];
            }else{
                $res[$key]['total'] += $item['total'];
            }
        }
        return $res;
    }

    private function formatChannelPriceData($price_list,$channel_interface_info){

        $bmy_product_info = [];
        foreach ($channel_interface_info as $item){
            if(!isset($bmy_product_info[$item['interface_name']])){
                $bmy_product_info[$item['interface_name']] = $item['interface_label'];
            }
        }

        $new_price_list = [];
        foreach ($price_list as $item) {
            $price_info = json_decode($item['price'], true);
            $channel_interface = $channel_interface_info[$item['interface_id']] ?? [];
            if (!$channel_interface){
                continue;
            }
            $split_price_info = $this->getSplitPriceConf($price_info); // 三个运营商、三个加密方式
            foreach ($split_price_info as $v){
                $key = $channel_interface['channel_id'] . '_' . $channel_interface['interface_name'] . '_' .$v['operator']. '_' .$v['encrypt_way'];
                if (!isset($channel_interface['interface_name'][$key])){
                    $new_price_list[$channel_interface['interface_name']][$key] = [
                        'channel_id' => $channel_interface['channel_id'],
                        'interface_id' => $item['interface_id'],
                        'interface_label' => $bmy_product_info[$channel_interface['interface_name']] ?? $channel_interface['interface_label'],
                        'channel_label' => $channel_interface['channel_label'],
                        'operator' => $v['operator'],
                        'encrypt_way' => $v['encrypt_way'],
                        'price' => $v['price'],
                    ];
                }
            }
        }
        return $new_price_list;
    }

    private function getSplitPriceConf($price_info){
        $opeators = ['CMCC','CUCC','CTCC','CBN'];
        $encrypts = ['CLEAR', 'SHA256', 'MD5'];
        $new_operator_price_info = [];
        foreach ($price_info as $item){
              if ($item['operator'] == 'ALL'){
                  foreach ($opeators as $opeator){
                       $item['operator'] = $opeator;
                       $new_operator_price_info[] = $item;
                  }
              }else{
                  $new_operator_price_info[] = $item;
              }
        }
        foreach ($opeators as $opeator){
            if (!in_array($opeator,array_column($new_operator_price_info,'operator'))){
                $new_operator_price_info[] = [
                   'operator'=>$opeator,
                   'encrypt_way'=> 'ALL',
                   'price'=> '0',
                   'price_model'=> '2',
                ];
            }
        }
        
        $new_price_info = [];
        foreach ($new_operator_price_info as $item){
            if ($item['encrypt_way'] == 'ALL'){
                foreach ($encrypts as $encrypt){
                    $item['encrypt_way'] = $encrypt;
                    $new_price_info[] = $item;
                }
            }else{
                $new_price_info[] = $item;
            }
        }
        return $new_price_info;
    }
    private function getShuntListV2Where($start_date,$end_date){
        $where = [];
        $start_date  && $where[] = ['date', '>=', date('Ymd',strtotime($start_date))];
        $end_date  && $where[] = ['date', '<=', date('Ymd',strtotime($end_date))];
        return $where;
    }

    public function getInterfacePriceChange()
    {
        $price_list = ConfigPriceInterface::getLatestPriceByFatherId(200);
        $shunt_list = ConfigInterfaceShuntLog::getAll();
        $interface_info = ChannelInterface::getAllChannel();
        $interface_map = array_column($interface_info,'label','id');
        foreach ($price_list as $item){
           $price_info =   json_decode($item['price'],true);
           foreach ($price_info as $v){
                $res = [
                  'inteface_id' => $item['interface_id'],
                  'interface_label' => $interface_map[$item['interface_id']] ?? '',
                  'operator' => $v['operator'],
                  'encrypt_way' => $v['encrypt_way'],
                  'ratio' => '',
                  'price' => $v['price'],
                  'date' => date('Y-m-d',strtotime($item['start_date'])),
                ];
                $new_price_list[] = $res;
           }
        }
        $new_shunt_list = [];
        foreach ($shunt_list as $item){
            $res = [
                'inteface_id' => $item['interface_id'],
                'interface_label' => $interface_map[$item['interface_id']] ?? '',
                'operator' => $item['operator'],
                'encrypt_way' => $item['encrypt'],
                'ratio' => $item['ratio'] . '%',
                'price' => '',
                'date' => date('Y-m-d',$item['create_time']),
            ];
            $new_shunt_list[] = $res;
        }
       $result =  array_merge($new_price_list,$new_shunt_list);
        usort($result, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });
        return $result;
    }

    protected function getOperatorEncryptPrice($price)
    {
        $encrypt = [];
        foreach ($price as $val) {
            if ($val['encrypt_way'] == 'ALL' && $val['operator'] == 'ALL') {
                foreach ($this->operator as $k => $v) {
                    if ($k == 'ALL') continue;
                    foreach ($this->encrypt_way as $kk => $vv) {
                        if ($kk == 'ALL') continue;
                        $encrypt[$k][$kk] = $val['price'];
                    }
                }
            } elseif ($val['encrypt_way'] == 'ALL' && $val['operator'] != 'ALL') {
                foreach ($this->encrypt_way as $k => $v) {
                    if ($k == 'ALL') continue;
                    $encrypt[$val['operator']][$k] = $val['price'];
                }
            } else {
                $encrypt[$val['operator']][$val['encrypt_way']] = $val['price'];
            }
        }

        return $encrypt;
    }

    public function saveShunt()
    {
        $channel_id = intval(request()->post('channel_id', null));
        $interface_id = intval(request()->post('interface_id', null));
        $operator = request()->post('operator', null);
        $encrypt = request()->post('encrypt', null);
        $ratio = request()->post('ratio', null);

        $ratio = floatval(str_replace('%', '', $ratio));

        if (!$channel_id || !$interface_id || !$operator || !$encrypt) {
            return false;
        }

        if ($ratio === '' || $ratio > 100 || strpos($ratio,".") !== false) {
            throw new \Exception('分流占比设置错误');
        }

        $res = ConfigInterfaceShunt::getOneData($interface_id, $channel_id, $operator, $encrypt);
        $log_data =  $where = compact('channel_id', 'interface_id', 'operator', 'encrypt');
        $log_data['create_time'] = time();
        $log_data['ratio'] = $ratio;
        if ($res) {
            $data = [
                'ratio' => $ratio,
                'update_time' => time()
            ];
            $result =  ConfigInterfaceShunt::updateData($where, $data);
            $log_data['type'] = 2;
        } else {
            $data = [
                'interface_id' => $interface_id,
                'channel_id' => $channel_id,
                'operator' => $operator,
                'encrypt' => $encrypt,
                'ratio' => $ratio,
                'create_time' => time(),
                'update_time' => time()
            ];
             $result =  ConfigInterfaceShunt::addData($data);
            $log_data['type'] = 1;
        }
        ConfigInterfaceShuntLog::addData($log_data);
        return  $result;
    }



    public function getConfigChannelAccountList()
    {
        $channel_id = intval(request()->post('channel_id', null));
        $product_id = request()->post('product_id', null);
        $account_id = request()->post('account_id', null);
        $limit = intval(request()->post('limit', 20));
        $page = intval(request()->post('page', 1));

        $where = ['channel_id' => $channel_id, 'product_id' => $product_id, 'account_id' => $account_id];
        $config_all = ConfigChannelAccount::getAll($where, $limit, $page);

        $product_info = Product::getListByCondition(['father_id' => 200], ['product_id', 'product_name'])->toArray();
        $product_info = array_column($product_info, 'product_name', 'product_id');

        $channel_info = ChannelProduct::getChannelListByProductId(200);
        $channel_info = array_column($channel_info, 'label', 'channel_id');

        $account_ids = $config_all ? array_column($config_all, 'account_id') : [];
        $account_info = Account::getList($account_ids);
        $account_info = array_column($account_info, 'account_name', 'account_id');

        foreach ($config_all as &$value) {
            $value['account_name'] = $account_info[$value['account_id']];
            $value['product_name'] = $product_info[$value['product_id']];
//            $operator = $value['operator'] ? explode(',', $value['operator']) : [];
//            $operator_name = [];
//            foreach ($operator as $val) {
//                $operator_name[$val] = $this->operator[$val];
//            }
//
//            $value['operator_list'] = $operator_name;
//            $value['operator_name'] = $operator_name ? implode(' | ', array_values($operator_name)) : '';
            $value['operator_name'] = $value['operator'] ? $this->operator[$value['operator']] : '';
            $channel_need = $value['channel_need'] ? explode(',', $value['channel_need']) : [];
            $channel_name_need = [];
            foreach ($channel_need as $val) {
                $channel_name_need[$val] = $channel_info[$val];
            }

            $value['channel_name_need_list'] = $channel_name_need;
            $value['channel_name_need'] = $channel_name_need ? implode(' | ', array_values($channel_name_need)) : '';
            $channel_no = $value['channel_no'] ? explode(',', $value['channel_no']) : [];
            $channel_name_no = [];
            foreach ($channel_no as $val) {
                $channel_name_no[$val] = $val == 'all' ? '全部' : $channel_info[$val];
            }
            $value['channel_name_no_list'] = $channel_name_no;
            $value['channel_name_no'] = $channel_name_no ? implode(' | ', array_values($channel_name_no)) : '';
        }

        $count = ConfigChannelAccount::getCount($where);
        return ['list' => $config_all, 'count' => $count];
    }

    public function saveConfigChannelAccount()
    {
        $id = intval(request()->post('id', null));
        $account_id = request()->post('account_id', null);
        $product_info = request()->post('product_id', null);
        $operator = request()->post('operator', null);
        $channel_need = request()->post('channel_need', null);
        $channel_no = request()->post('channel_no', null);
        $remark = request()->post('remark', null);

        if (!$account_id) {
            throw new \Exception('请选择账号');
        }
        if (!$product_info) {
            throw new \Exception('请选择产品');
        }
        if (!$operator) {
            throw new \Exception('请选择运营商');
        }
        if (!$channel_need && !$channel_no) {
            throw new \Exception('要走的渠道或不走的渠道，请至少选择一项');
        }
        $product_id = intval(explode('_', $product_info)[0]);
        $where = ['account_id' => $account_id, 'product_id' => $product_id, 'delete_time' => 0];

        $res = ConfigChannelAccount::getData($where);
        if ($res && !$id) {
            $operator_name = [];
            foreach ($res as $value) {
                if ($value['operator'] == $operator) {
                    throw new \Exception('该客户-产品-运营商已添加');
                }
                if ($value['operator'] == 'ALL' && $operator != 'ALL') {
                    throw new \Exception('该客户-产品已添加不区分运营商的渠道');
                }
                if ($value['operator'] != 'ALL' && $operator == 'ALL') {
                    $operator_name[] = $this->operator[$value['operator']];
                }
            }
            if ($operator_name) {
                $operator_name = implode(',', $operator_name);
                throw new \Exception('该客户-产品已添加' . $operator_name . '的渠道');
            }
        }

        $data['operator'] = $operator;
        $data['channel_need'] = $channel_need ? implode(',', $channel_need) : '';
        $data['channel_no'] = $channel_no ? implode(',', $channel_no) : '';
        $data['remark'] = trim($remark);
        $data['update_time'] = time();

        if ($id) {
            return ConfigChannelAccount::updateData(['id' => $id], $data);
        } else {
            $data['create_time'] = time();
            $data = array_merge($where, $data);
            return ConfigChannelAccount::addData($data);
        }
    }

    public function delConfig()
    {
        $id = intval(request()->post('id', null));
        if (!$id) {
            throw new \Exception('参数错误');
        }
        $where = ['id' => $id];
        $data = ['delete_time' => time()];
        return ConfigChannelAccount::updateData($where, $data);
    }
}