<?php

namespace App\Http\Repository;

use App\Models\Monitor\PeriodSwitchRecord;
use App\Models\Monitor\PeriodUseRecord;
use App\Models\PeriodCompareResult;
use App\TraitUpgrade\CurlTrait;
use App\Util\Func;

class StatPeroidRepository extends StatBaseRepository
{
    use CurlTrait;

    //定义各个项目组请求地址

    public $curl_list = [
        ['node' => 'beijing', 'domain' => 'http://bj-opdata.dianhua.cn', 'interface' => '/opdata/getDate', 'pid' => 200, 'enable' => 'OPDATA_BJ_PEROID_API_ENABLE'],
        ['node' => 'shenzhen', 'domain' => 'http://opdata.dianhua.cn', 'interface' => '/opdata/getDate', 'pid' => 200, 'enable' => 'OPDATA_SZ_PEROID_API_ENABLE'],
        ['node' => 'beijing', 'domain' => 'http://bj-shield.dianhua.cn', 'interface' => '/backend/getPeriodInfo', 'pid' => 615, 'enable' => 'SHIELD_BJ_PEROID_API_ENABLE'],
        ['node' => 'shenzhen', 'domain' => 'http://shield.dianhua.cn', 'interface' => '/backend/getPeriodInfo', 'pid' => 615, 'enable' => 'SHIELD_BJ_PEROID_API_ENABLE'],
        ['node' => 'beijing', 'domain' => 'http://bj-hmf.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 10000, 'enable' => 'HMF_BJ_PEROID_API_ENABLE'],
        ['node' => 'shenzhen', 'domain' => 'http://hmf.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 10000, 'enable' => 'HMF_BJ_PEROID_API_ENABLE'],
        //['node' => 'beijing', 'domain' => 'http://bj-dh.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 20000, 'enable' => 'DH_BJ_PEROID_API_ENABLE'],
        ['node' => 'shenzhen', 'domain' => 'http://dh.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 20000, 'enable' => 'DH_BJ_PEROID_API_ENABLE'],
    ];
     /*
    public $curl_list = [
        ['node' => 'beijing', 'domain' => 'http://opdata-jianye.dianhua.cn:800', 'interface' => '/opdata/getDate', 'pid' => 200],
        ['node' => 'shenzhen', 'domain' => 'http://opdata-jianye.dianhua.cn:8080', 'interface' => '/opdata/getDate', 'pid' => 200],
        ['node' => 'shenzhen', 'domain' => 'http://shield-dan.dianhua.cn', 'interface' => '/backend/getPeriodInfo', 'pid' => 615],
        ['node' => 'shenzhen', 'domain' => 'http://shield-dan.dianhua.cn', 'interface' => '/backend/getPeriodInfo', 'pid' => 615],
        ['node' => 'beijing', 'domain' => 'http://bj-hmf.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 10000],
        ['node' => 'shenzhen', 'domain' => 'http://hmf.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 10000],
        ['node' => 'beijing', 'domain' => 'http://bj-dh.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 20000],
        ['node' => 'shenzhen', 'domain' => 'http://dh.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 20000],
    ];
    */

    public $apikey = '76EE8FA57AE0bb6E78c9364163d62944';
    public $appsecret = '76EE8FA57AE0bb76EE8FA57AE0bb6E78c9364163d629446E78c9364163d62944';
    public $type_map = [
        "hmf" => "号码分",
        "hmf_dxm" => "号码分度小满",
        "dh" => "贷后风险指数",
        "variables" => "变量",
        "score" => "分值",
        "opdata" => "活跃指数",
        "opdata_net_time" => "在网时长",
    ];
    public $node_map = [
        'beijing' => '北京',
        'shenzhen' => '深圳',
    ];

    public $data = [];
    public $tmp = [];


    public function getPeroidList($params){

        try{
            foreach($this->curl_list as $val){
                $enable = env($val['enable'], true);
                //如果在env配置中设置对应接口为false时，则跳过
                if(!$enable){
                    continue;
                }
                $postData = $this->setParams($val);//设置请求参数
                $url = $val['domain'] . $val['interface'];
                $result = $this->post($url, $postData);
                if($val['pid'] == 200){
                    //dd($result);
                }

                $this->checkResult($result, $val);
                $this->formatResult($result, $val);//根据各个项目组返回的数据，整理成统一格式

            }

            //说明是来自console中的 ProductPeroidList参数
            if(isset($params['save']) && $params['save']){
                $this->saveResult();
                return true;
            }

            $this->mergedCell();//把数据合并组装成前端需要的格式
            return ['list' => $this->data, 'count' => count($this->data)];

        }catch(\Exception $e){
            //dd($e->getMessage(), $e->getLine());
            sendExceptionNotice($this, $e);
        }

    }

    //获取对应条件的历史账期
    public function getHistoryPeroidList($params){

        $limit = request()->post('limit', 10);
        $page = request()->post('page', 1);
        $father_id = request()->post('father_id', '');
        $node = request()->post('node', '');
        $type = request()->post('type', '');
        $offset = ($page - 1) * $limit;
        $where = ['status' => 1];
        $where['father_id'] = $father_id;
        $where['node'] = $node;
        $where['type'] = $type;
        try{
            $res = PeriodUseRecord::getList($where, $offset, $limit);

            foreach($res['list'] as &$item){
                $use_date_arr = explode(',', $item['use_date']);
                $use_date_tmp = [];
                foreach($use_date_arr as $u_date){
                    $use_date_tmp[] = date('Y-m-d', strtotime($u_date));
                }
                $item['use_date'] = implode(',', $use_date_tmp);
                $item['date'] = date('Y-m-d', strtotime($item['date']));
            }

        }catch(\Exception $e){
            //dd($e->getMessage(), $e->getLine());
            sendExceptionNotice($this, $e);
        }


        return $res;

    }

    public function saveResult(){
        foreach($this->tmp as $val){
            $add = [
                'father_id' => $val['father_id'],
                'node' => $val['node'],
                'type' => $val['type'],
                'use_date' => $val['use_date'],
                'date' => str_replace('-', '', $val['date']),
            ];

            PeriodUseRecord::addRecord($add);
        }

        return true;
    }


    public function mergedCell(){
        $col_arr = [];
        foreach($this->tmp as $item){
            $pid_num = $col_arr[$item['father_id']]['num'] ?? 0;
            $col_arr[$item['father_id']]['num'] = $pid_num + 1;

            $node_num = $col_arr[$item['father_id']][$item['node']]['num'] ?? 0;
            $col_arr[$item['father_id']][$item['node']]['num'] = $node_num + 1;

        }

        //dd($this->tmp);
        $unique_father_id = [];
        $unique_node = [];
        foreach($this->tmp as $item){

            if(!in_array($item['father_id'], $unique_father_id)){
                $item['father_name_col'] = $col_arr[$item['father_id']]['num']??0;
                $unique_father_id[] = $item['father_id'];
            }else{
                $item['father_name_col'] = 0;
            }

            if(!in_array($item['father_id'].'_'.$item['node'], $unique_node)){
                $item['node_col'] = $col_arr[$item['father_id']][$item['node']]['num']??0;
                $unique_node[] = $item['father_id'].'_'.$item['node'];
            }else{
                $item['node_col'] = 0;
            }

            $item['type_name'] = $this->type_map[$item['type']] ?? 'unknow';
            $item['node_name'] = $this->node_map[$item['node']] ?? 'unknow';

            $this->data[] = $item;
        }


        return true;
    }


    public function formatResult($result, $val){

        if($val['pid'] == 200){
            //dd($result);
            $father_name = '邦秒验';
            foreach ($result['data'] as $item){

                $arr = [
                    'node' => $val['node'],
                    'father_id' => $val['pid'],
                    'father_name' => $father_name,
                    'type' => $item['type'],
                    'use_date' => $this->formatUseDate($item['use_date']),
                    'date' => date('Y-m-d'),
                    'father_name_col' => 0,
                    'node_col' => 0,
                ];

                $this->tmp[] = $arr;

            }


        }

        if($val['pid'] == 615){
            $father_name = '号码风险等级';
            //dd($result);
            foreach ($result['data'] as $item){

                $arr = [
                    'node' => $val['node'],
                    'father_id' => $val['pid'],
                    'father_name' => $father_name,
                    'type' => $item['type'],
                    'use_date' => $this->formatUseDate($item['use_date']),
                    'date' => date('Y-m-d'),
                    'father_name_col' => 0,
                    'node_col' => 0,
                ];

                $this->tmp[] = $arr;

            }

        }

        if(in_array($val['pid'], [10000, 20000])){
            $father_name = ($val['pid'] == 20000) ? '贷后风险指数' : '号码分';
            foreach ($result['data']['result'] as $item){

                $arr = [
                    'node' => $val['node'],
                    'father_id' => $val['pid'],
                    'father_name' => $father_name,
                    'type' => $item['type'],
                    'use_date' => $this->formatUseDate($item['use_date']),
                    'date' => date('Y-m-d'),
                    'father_name_col' => 0,
                    'node_col' => 0,
                ];

                $this->tmp[] = $arr;

            }

        }


        return true;
    }


    public function getPeriodListFromMongo(){

        $url = "http://172.18.32.205:8081/hmf/getDateList?check=asf3fgl";
        $result = $this->get($url);
        $result = isset($result['data']) ? $result['data'] : [];
        $limit = count($result);

        $mongo_result = [];
        foreach ($result as $date){
            $date = date('Ymd', strtotime($date));
            $item = [
                'father_id' => 10000,
                'node' => 'shenzhen',
                'type' => '号码分',
                'use_date' => $date
            ];
            $mongo_result[] = $date;
            PeriodSwitchRecord::addRecord($item);
        }

        //获取当前账期
        $node_info = ['node' => 'shenzhen', 'domain' => 'http://hmf.dianhua.cn', 'interface' => '/tabdate/get', 'pid' => 10000];
        $postData = $this->setParams();//设置请求参数
        $url = $node_info['domain'] . $node_info['interface'];
        $result = $this->post($url, $postData);
        $this->checkResult($result, $node_info);
        $current_use_date = '';//当前使用账期
        foreach ($result['data']['result'] as $val){
            if($val['type'] == 'hmf'){
                $current_use_date = $this->formatUseDate($val['use_date']);
                break;
            }
        }

        if($current_use_date){
            $date = date('Ymd', strtotime($current_use_date));
            $mongo_result[] = $date;
            PeriodSwitchRecord::setCurrentPeriod($date);
        }

        $list = PeriodSwitchRecord::getList([], 0, 10);
        $return['list'] = [];
        $return['count'] = 10;
        foreach ($list['list'] as &$item){
            $diff_time = time() - $item['update_time'];
            if($item['status'] == 2 && $diff_time > 150){
                $flag = PeriodSwitchRecord::where(['id' => $item['id']])->update(['status' => 1, 'update_time' => time()]);
                if($flag){
                    $item['status'] = 1;
                }
            }

            $use_date = $item['use_date'];
            $item['use_date'] = date('Y-m-d', strtotime($use_date));

            if(in_array($use_date, $mongo_result)){
                $return['list'][] = $item;
            }

        }

        return $return;
    }

    public function setHmfPeriod($params){
        $id = isset($params['id']) ? $params['id'] : 0;
        if(!$id){
            throw new \Exception('参数id异常');
        }

        $info = PeriodSwitchRecord::where($params)->first();
        if(!isset($info['use_date']) || empty($info['use_date'])){
            throw new \Exception('参数id异常-1');
        }
        $date = date('Y-m-d', strtotime($info['use_date']));
        $url = "http://172.18.52.149:9091/tabdate/hmf/changeTabdate?tabDate=".$date."&node=shenzhen";
        $result = $this->get($url);
        $result = is_array($result) ? json_encode($result) : $result;
        $updata = ['status' => 2, 'update_time' => time(), 'note' => $result];
        PeriodSwitchRecord::where(['id' => $info['id']])->update($updata);

        $list = PeriodSwitchRecord::getList([], 0, 6);
        foreach ($list['list'] as $item){
            if($item['id'] == $info['id']){
                continue;
            }

            $diff_time = time() - $item['update_time'];
            if($item['status'] == 2 && $diff_time > 150){
                PeriodSwitchRecord::where(['id' => $item['id']])->update(['status' => 1, 'update_time' => time()]);
            }
        }

        return [];

    }



    public function getShieldPeriod($params){
        $date = !empty($params['period']) ? $params['period'] : 0;
        if(!$date){
            throw new \Exception('参数period异常');
        }

        $type = !empty($params['type']) ? $params['type'] : '';
        if(!$type){
            throw new \Exception('参数type异常');
        }

        $where = ['period' => date('Y-m-d', strtotime($date))];
        $where['type'] = $type;
        $list = PeriodCompareResult::getListByDate($where);
        $data = [];
        foreach ($list as $item){
            $content = json_decode($item['content'], true);
            $data[$item['pids']]['new'] = isset($content['new']) ? $content['new'] : [];
            $data[$item['pids']]['old'] = isset($content['old']) ? $content['old'] : [];
        }

        $template = [
            "count", "avg", "variance", "min",
            "quantile_1", "quantile_2", "quantile_3", "quantile_4",
            "quantile_5", "quantile_6", "quantile_7", "quantile_8",
            "quantile_9", "quantile_10", "max"
        ];
        //type=>count,new_666=>xxx,old_666=>xxxx,new_667=>xxxx,old_667=>xxxx
        $result = [];
        foreach($template as $type){
            $item = [
                'type' => $type,
                'new_666' => isset($data['666']['new'][$type]) ? $data['666']['new'][$type] : 0,
                'old_666' => isset($data['666']['old'][$type]) ? $data['666']['old'][$type] : 0,                'new_666' => isset($data['666']['new'][$type]) ? $data['666']['new'][$type] : 0,
                'new_667' => isset($data['667']['new'][$type]) ? $data['667']['new'][$type] : 0,
                'old_667' => isset($data['667']['old'][$type]) ? $data['667']['old'][$type] : 0,
            ];

            $result[] = $item;
        }

        return $result;
    }

    public function setShieldPeriod($params){
        $date = !empty($params['period']) ? $params['period'] : 0;
        if(!$date){
            throw new \Exception('参数period异常');
        }

        $type = !empty($params['type']) ? $params['type'] : '';
        if(!$type){
            throw new \Exception('参数type异常');
        }

        $postData = $this->setParams();
        $postData['period'] = date('Ymd', strtotime($date));
        $postData['type'] = $type;
        //$url = "http://guangli-shield.dianhua.cn/backend/setPeriod";
        $url = "http://shield.dianhua.cn/backend/setPeriod";
        $result = $this->post($url, $postData);
        //dd($result);
        if(isset($result['status']) && $result['status'] == 0 && isset($result['sid'])){
            return true;
        }

        return false;
    }


    /**
     *号码风险等级获取帐期 V2
     */
    public function getShieldPeriodV2()
    {
        //获取可用帐期列表(大账期)
        $url = "http://172.18.32.203:8081/period/getAvailablePeriodList?check=asf3fgl&project=risk";
//        $url = "http://ning-test-bigData.dianhua.cn/period/getAvailablePeriodList?check=asf3fgl&project=risk";
        $result = $this->get($url);
        $result = $result['data'] ?? [];
        if (!$result) {
            return [];
        }

        //获取可用帐期列表(微众T+1)
        $url = "http://172.18.32.203:8081/period/getAvailablePeriodList?check=asf3fgl&project=riskwz_v1";
//        $url = "http://ning-test-bigData.dianhua.cn/period/getAvailablePeriodList?check=asf3fgl&project=riskwz_v1";
        $resultWz = $this->get($url);
        $resultWz = $resultWz['data'] ?? [];
        if (!$resultWz) {
            return [];
        }

        $result = array_merge($result, $resultWz);

        //获取当前正在使用帐期
        $postData = $this->setParams();//设置请求参数
        $shieldUrl = 'http://shield.dianhua.cn/backend/getPeriodInfoV2';
//        $shieldUrl = 'http://ning-test-shield.dianhua.cn/backend/getPeriodInfoV2';
        $usePeriodRes = $this->post($shieldUrl, $postData);
        $usePeriodRes = $usePeriodRes['data'] ?? [];
        if (!$usePeriodRes) {
            return [];
        }
        $usePeriodRes = array_column($usePeriodRes, 'usePeriod', 'type');
        $commonUsePeriod = $usePeriodRes['common'][0];
        $supplementUsePeriod = $usePeriodRes['supplement'][0];

        $availablePeriodList = [];
        foreach ($result as $table => $periods) {
            $periodType = 'common';
            $periodTypeTxt = '普通帐期';
            if ($table == 'telrisk_wz') {
                $periodType = 'supplement';
                $periodTypeTxt = 'T+1帐期';
            }
            foreach ($periods as $item) {
                $status = 1;
                if (
                    ($periodType == 'common' && $commonUsePeriod == $item) ||
                    ($periodType == 'supplement' && $supplementUsePeriod == $item)
                ) {
                    $status = 3;
                }
                $availablePeriodList[] = [
                    'period' => $item,
                    'periodType' => $periodType,
                    'periodTypeTxt' => $periodTypeTxt,
                    'status' => $status
                ];
            }
        }
        $periodTypeArray = array_column($availablePeriodList, 'periodType');
        $periodArray = array_column($availablePeriodList, 'period');
        array_multisort($periodTypeArray, SORT_ASC,$periodArray,SORT_DESC,$availablePeriodList);
        return $availablePeriodList;
    }

    public function setShieldPeriodV2($params){
        $date = !empty($params['period']) ? $params['period'] : 0;
        if(!$date){
            throw new \Exception('参数period异常');
        }
        preg_match("/^\d{8}$/", $date, $tmp);
        if (!$tmp) {
            throw new \Exception('参数period异常');
        }

        $type = !empty($params['periodType']) ? $params['periodType'] : '';
        if(!$type){
            throw new \Exception('参数periodType异常');
        }

        //todo 临时逻辑，普通帐期：0；补充帐期：671
        $postData = $this->setParams();
        $postData['period'] = date('Ymd', strtotime($date));
        if ($type == 'supplement') {
            $postData['productId'] = 671;
            $postData['periodType'] = 2;
            // 补充账期只灌group1
            $postData['group'] = 'group1';
        } else {
            $postData['productId'] = 'default';
            $postData['periodType'] = 1;
            $groupurl = "http://172.18.32.203:8081/tool/getPeriodGroup?check=asf3fgl";
            $groupresult = $this->post($groupurl, ['product'=>'risk_v1','date'=>$date]);
            if(isset($groupresult['code']) && $groupresult['code']==0 && isset($groupresult['data']['group'])){
                $postData['group'] = $groupresult['data']['group'];
            }else{
                throw new \Exception('未获取到账期组');
            }
        }

        $shieldUrl = env('SHIELD_URL', '');
        $url = $shieldUrl . "/backend/setPeriodV2";
        $result = $this->post($url, $postData);

        if(isset($result['status']) && $result['status'] == 0 && isset($result['sid'])){
            return true;
        }
        return false;
    }


    public function setParams($val = ''){
        $nonce = mt_rand(1000, 9999);
        $timestamp = time();
        $sign = $this->makeSign($this->apikey, $this->appsecret, $nonce, $timestamp);

        return ['timestamp' => $timestamp, 'nonce' => $nonce,
            'apikey' => $this->apikey, 'signature' => $sign];
    }

    public function makeSign($apikey, $apiscrect, $nonce, $timestamp)
    {
        if(!$apikey || !$apiscrect || !$nonce || !$timestamp){
            return false;
        }
        $tmpArr = array($timestamp, $apikey, $apiscrect, $nonce);
        sort($tmpArr, SORT_STRING);
        $sign = sha1( implode( $tmpArr ) );
        return $sign;
    }

    public function checkResult($result, $params){
        if(isset($result['status'])){
            if($result['status'] == 1748){
                throw new \Exception('产品'.$params['pid'].'节点'.$params['node'].'请求报错:'.$result['msg']);
            }else if($result['status'] == 0){
                return;
            }else{
                throw new \Exception('产品'.$params['pid'].'节点'.$params['node'].'请求返回状态码未知');
            }

        }else{
            throw new \Exception('产品'.$params['pid'].'节点'.$params['node'].'数据返回状态码不存在');
        }

    }

    public function formatUseDate($use_date){
        $use_date_arr = [];
        foreach($use_date as $u_date){
            $use_date_arr[] = date('Y-m-d', strtotime($u_date));
        }

        return implode(',', $use_date_arr);
    }




}