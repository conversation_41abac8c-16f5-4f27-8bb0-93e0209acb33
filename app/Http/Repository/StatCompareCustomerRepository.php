<?php

namespace App\Http\Repository;

use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\CustomerBillAdjust;
use App\Models\CustomerExpend;
use App\Models\StatisticsCustomerUsage;
use App\Models\Product;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Support\Facades\DB;

class StatCompareCustomerRepository extends StatBaseRepository
{
    /**
     * 数据对比-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $product_ids = request()->post('product_ids', []);

        if($product_ids){
            $params['product_list'] = $product_ids;
        }
        $usage_data = $this->getUsage($params); //调用量
        $income_data = $this->getIncome($params);   //收入
        $expend_data = $this->getDateExpend($params); //特殊消耗

        $cost_data = $this->getCost($params);   //成本

        $channel_adjust_cost = $this->getAdjustChannelCost($params); //渠道成本调整(其实叫成本调整更符合业务,由于历史原因，命名的语义不太符合业务)
        $customer_adjust_cost = $this->getAdjustCustomerCost($params); //客户成本调整
        $fixed_cost = $this->getFixedCost($params); //固定费用成本

        $data = $this->formatData($usage_data, $income_data, $cost_data, $expend_data, $channel_adjust_cost, $customer_adjust_cost, $fixed_cost);
        $father_id = $params['father_id'];
        $data = $this->filterAuthMoneyByFatherid($data,$father_id);
        return $data;
    }

    protected function getUsage($params){
        $where = $this->getWhere($params);
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        $usage_data = StatisticsCustomerUsage::getCompareListByDateProduct($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey'], $params['filter_product']);
        return $usage_data;
    }

    protected function getIncome($params)
    {
        $where = $this->getWhere($params);
        $income_data = BillProductIncomeV2::getCompareListDateProduct($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey']);
        return $income_data;
    }

    protected function getCost($params)
    {
        $where = $this->getWhere($params);
        $cost_data = BillCostV2::getCompareListDateProduct($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey']);
        return $cost_data;
    }

    //渠道成本调整
    protected function getAdjustChannelCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);

        $cost_data = ChannelAccountAdjust::where($where)
            ->select([
                DB::raw("DATE_FORMAT(date, '%Y%m%d') as date"),
                "product_id",
                DB::raw('sum(money) AS money'),
                DB::raw('sum(fee_number) AS number')
            ])
            ->when(isset($params['product_list']) && $params['product_list'], function($query) use ($params) {
                return $query->whereIn('product_id', $params['product_list']);
            })
            ->when(isset($params['apikey_list']) && $params['apikey_list'], function ($query) use ($params) {
                return $query->whereIn('apikey', $params['apikey_list']);
            })
            ->when(isset($params['filter_apikey']) && $params['filter_apikey'], function ($query) use ($params) {
                return $query->whereNotIn('apikey', $params['filter_apikey']);
            })
            ->groupBy(['date', 'product_id'])
            ->get()->toArray();

        return $cost_data;
    }

    //客户成本调整 按照客户分组
    protected function getAdjustCustomerCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Ymd', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Ymd', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);

        $cost_data = CustomerBillAdjust::where($where)
            ->where('delete_at', 0)
            ->when(isset($params['filter_customer']) && !empty($params['filter_customer']), function($query) use($params) {
                return $query->whereNotIn('customer_id', $params['filter_customer']);
            })
            ->select([
                'date',
                'product_id',
                DB::raw('sum(money) AS money'),
                DB::raw('sum(fee_number) AS number'),
            ])
            ->when(isset($params['product_list']) && $params['product_list'], function($query) use ($params) {
                return $query->whereIn('product_id', $params['product_list']);
            })
            ->when(isset($params['customer_id']) && $params['customer_id'], function($query) use ($params) {
                return $query->where('customer_id', $params['customer_id']);
            })
            ->groupBy(['date', 'product_id'])
            ->get()->toArray();

        return $cost_data;
    }

    //固定费用成本
    protected function getFixedCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);

        $cost_data = ChannelAccountFixedFee::where($where)
            ->select([
                DB::raw("DATE_FORMAT(date, '%Y%m%d') as date"),
                "product_id",
                DB::raw('sum(money) AS money'),
                DB::raw('0 AS number')
            ])
            ->when(isset($params['father_ids']) && $params['father_ids'], function($query) use ($params){
                return $query->whereIn('father_id', $params['father_ids']);
            })
            ->when(isset($params['father_id']) && $params['father_id'], function($query) use ($params){
                return $query->where('father_id', $params['father_id']);
            })
            ->when(isset($params['product_list']) && $params['product_list'], function($query) use ($params) {
                return $query->whereIn('product_id', $params['product_list']);
            })
            ->when(isset($params['apikey_list']) && $params['apikey_list'], function ($query) use ($params) {
                return $query->whereIn('apikey', $params['apikey_list']);
            })
            ->when(isset($params['filter_apikey']) && $params['filter_apikey'], function ($query) use ($params) {
                return $query->whereNotIn('apikey', $params['filter_apikey']);
            })
            ->groupBy(['date', 'product_id'])
            ->get()->toArray();

        return $cost_data;
    }

    /**
     * 对比页面特殊消耗
     * @param $params
     * @return mixed
     */
    protected function getDateExpend($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getDateExpendByCustomerProduct(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer']);
        $expend_data['add'] = CustomerExpend::getDateExpendByCustomerProduct(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer']);
        return $expend_data;
    }

    protected function getWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        return $where;
    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @return array
     */
    private function formatData($usage_data, $income_data, $cost_data, $expend_data, $channel_adjust_cost, $customer_adjust_cost, $fixed_cost)
    {
        $data = [];
        foreach ($usage_data as $item){
            $data[$item['date'].'_'.$item['product_id']] = $item;
            $father_id =  RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $data[$item['date'].'_'.$item['product_id']]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($item['product_id']);
            $data[$item['date'].'_'.$item['product_id']]['father_id'] =$father_id;
            $data[$item['date'].'_'.$item['product_id']]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
        }
        foreach ($income_data as $item){
            $data[$item['date'].'_'.$item['product_id']]['number'] = $item['number'];
            $data[$item['date'].'_'.$item['product_id']]['money'] = $item['money'];
        }
        foreach ($cost_data as $item){
            $data[$item['date'].'_'.$item['product_id']]['cost'] = $item['money'];
        }
        foreach ($expend_data['sub'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date.'_'.$item['product_id']]['number'])){
                $data[$date.'_'.$item['product_id']]['number'] =  $data[$date.'_'.$item['product_id']]['money'] = 0;
                $data[$date.'_'.$item['product_id']]['date'] = $date;
            }
            $data[$date.'_'.$item['product_id']]['number'] -= $item['fee_number'];
            $data[$date.'_'.$item['product_id']]['money'] -= $item['money'];
        }
        foreach ($expend_data['add'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date.'_'.$item['product_id']]['number'])){
                $data[$date.'_'.$item['product_id']]['number'] =  $data[$date.'_'.$item['product_id']]['money'] = 0;
                $data[$date.'_'.$item['product_id']]['date'] = $date;
            }
            $data[$date.'_'.$item['product_id']]['number'] += $item['fee_number'];
            $data[$date.'_'.$item['product_id']]['money'] += $item['money'];
        }

        //渠道成本调整
        foreach ($channel_adjust_cost as $item) {
            $sum_cost = $data[$item['date'].'_'.$item['product_id']]['cost'] ?? 0;

            $data[$item['date'].'_'.$item['product_id']]['cost'] = bcadd($sum_cost, $item['money'], $this->degree);
        }

        //客户成本调整
        foreach ($customer_adjust_cost as $item) {
            $sum_cost = $data[$item['date'].'_'.$item['product_id']]['cost'] ?? 0;

            $data[$item['date'].'_'.$item['product_id']]['cost'] = bcadd($sum_cost, $item['money'], $this->degree);
        }

        //固定费用成本调整
        foreach ($fixed_cost as $item) {
            $sum_cost = $data[$item['date'].'_'.$item['product_id']]['cost'] ?? 0;

            $data[$item['date'].'_'.$item['product_id']]['cost'] = bcadd($sum_cost, $item['money'], $this->degree);
        }

        foreach ($data as $date => $value) {
            !isset($data[$date]['cost'])&&$data[$date]['cost']=0;
            !isset($data[$date]['money'])&&$data[$date]['money']=0;
        }
        return $data;
    }
}
