<?php

namespace App\Http\Repository;



use App\Exports\WeeklyStatisticExport;
use App\Models\WeeklyStatisticData;
use App\Utils\Helpers\Func;
use Maatwebsite\Excel\Facades\Excel;
use Exception;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class WeeklyStatisticRepository
{

    /**
     * 脚本添加任务方法
     *
     * 每周添加上周的任务 本周周一添加
     * 开始时间为当年1月1日
     * 结束时间为本周一 执行时不会包括本周一的数据
     *
     * @return bool
     * <AUTHOR> 2024-01-25 11:57:55
     */
    public static function batch_add_task(): bool {
        $start_time  = date('Y0101');
        //上周一
        $this_monday = date('Ymd', strtotime('this monday', time()));

        //计算上周回款数据,这个是开始时间 上上周一
        $last_monday = date('Ymd', strtotime('-1 monday', time()));

        $now = date('Y-m-d H:i:s');

        $insert_data    = [];
        $task_type_list = WeeklyStatisticData::getTaskTypeList();
        foreach ($task_type_list as $_type => $type_name) {
            $task_info = [];

            $params = [
                'start' => $start_time,
                'end'   => $this_monday,
            ];
            if (in_array($_type,WeeklyStatisticData::NEED_POINT_START)) {
                $params['start'] = $last_monday;
            }

            $task_info['type']       = $_type;
            $task_info['status']     = WeeklyStatisticData::TASK_STATUS_INIT;
            $task_info['params']     = json_encode($params);
            $task_info['admin']      = 'admin';
            $task_info['remark']     = '自动添加';
            $task_info['created_at'] = $now;
            $task_info['updated_at'] = $now;

            $insert_data[] = $task_info;
        }

        return WeeklyStatisticData::insert($insert_data);
    }


    /**
     * 页面添加任务方法
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-01-25 15:16:23
     */
    public static function add_task(): bool {
        $end_time        = request()->get('end_time');       //截止时间 Ymd
        $week_start_time = request()->get('week_start_time');//上周回款数据 开始时间 Ymd 如果不是上周回款数据类型无校
        $type            = request()->get('type');           //任务类型
        $remark          = request()->get('remark');         //备注
        $user_cookie     = request()->post('user_cookie');

        $username = Func::getUserNameFromCookie($user_cookie);

        $task_type_list = WeeklyStatisticData::getTaskTypeList();
        if(!key_exists($type,$task_type_list)){
            throw new Exception('任务类型错误!');
        }

        if(empty($end_time)){
            throw new Exception('请传入截止时间!');
        }

        $end_time = $end_time/1000;
        $today = strtotime('today');
        if($end_time >= $today){
            throw new Exception('请选择今天之前的时间!');
        }


        // 需要指定开始时间
        if (in_array($type ,WeeklyStatisticData::NEED_POINT_START)) {
            if(empty($week_start_time)){
                throw new Exception('请传入开始时间!');
            }

            $week_start_time = $week_start_time/1000;
            if($end_time < $week_start_time){
                throw new Exception('截止时间应在开始时间之后!');
            }
            $week_start_time = date('Ymd',$week_start_time);
        }

        $end_time = $end_time + 86400;
        $end_time = date('Ymd',$end_time);

        $start_time  = date('Y0101');

        $now = date('Y-m-d H:i:s');

        $params = [
            'start' => $start_time,
            'end'   => $end_time,
        ];

        // 需要指定开始时间
        if (in_array($type ,WeeklyStatisticData::NEED_POINT_START)) {
            $params['start'] = $week_start_time;//计算上周回款数据,这个是开始时间
        }

        $task_info = [];
        $task_info['type']       = $type;
        $task_info['status']     = WeeklyStatisticData::TASK_STATUS_INIT;
        $task_info['params']     = json_encode($params);
        $task_info['admin']      = $username;
        $task_info['remark']     = $remark;
        $task_info['created_at'] = $now;
        $task_info['updated_at'] = $now;

        return WeeklyStatisticData::insert($task_info);
    }

    /**
     * 获取策略列表
     *
     * @return array
     * <AUTHOR> 2024-01-08 11:36:41
     *
     */
    public function task_list(): array {
        $page   = intval(request()->post('page', 1));
        $limit  = intval(request()->post('limit', 20));
        $status = intval(request()->post('status'));
        $type   = intval(request()->post('type'));

        $list  = WeeklyStatisticData::getWeeklyStatisticDataList($status,$type,$page,$limit);
        $count = WeeklyStatisticData::getWeeklyStatisticDataCount($status,$type);

        $type_list   = WeeklyStatisticData::getTaskTypeList();
        $status_list = WeeklyStatisticData::getTaskStatusList();


        foreach($list as &$info){
            $params = json_decode($info['params'],true);
            $info['start'] = date("Y-m-d",strtotime($params['start']));
            $info['end']   = date("Y-m-d",strtotime($params['end']) - 86400);
            $info['status_name'] = $status_list[$info['status']];
            $info['type_name'] =   $type_list[$info['type']];
        }

        return [
            'list'  => $list,
            'count' => $count
        ];
    }

    /**
     * 获取任务类型列表
     *
     * @return array
     * <AUTHOR> 2024-01-25 16:10:16
     *
     */
    public function task_type_list(): array {
        $_type_list = WeeklyStatisticData::getTaskTypeList();

        $type_list = [];
        foreach ($_type_list as $type => $type_name){
            $type_list[] = [
                'name' => $type_name,
                'type' => $type,
            ];
        }

        return $type_list;
    }


    /**
     * 获取状态列表
     *
     * @return array
     * <AUTHOR> 2024-01-25 17:43:00
     *
     */
    public function task_status_list(): array {
        $_status_list = WeeklyStatisticData::getTaskStatusList();

        $status_list = [];
        foreach ($_status_list as $status => $status_name){
            $status_list[] = [
                'name'   => $status_name,
                'status' => $status,
            ];
        }

        return $status_list;
    }

    /**
     *
     *
     * @param $id
     *
     * @return BinaryFileResponse
     * @throws Exception
     * <AUTHOR> 2024-01-25 16:31:21
     */
    public function export($id): BinaryFileResponse {
        // $id = request()->get('id');
        $task_info = WeeklyStatisticData::getTaskInfoById($id);
        if($task_info['status'] != WeeklyStatisticData::TASK_STATUS_DONE){
            throw new Exception($id." 任务未执行完成");
        }
        $type = $task_info['type'];

        $info_list = json_decode($task_info['result'],true);
        $task_info_list = $this->get_info_list($type,$info_list);

        $task_type_list = WeeklyStatisticData::getTaskTypeList();

        $params = json_decode($task_info['params'],true);

        $excel_name = '领导周报统计数据导出-'.$task_type_list[$type].'-'.$params['start'].'-'.$params['end'].'.xlsx';

        return Excel::download(new WeeklyStatisticExport($task_info_list,$type), $excel_name);
    }


    /**
     * 任务结果详情
     *
     * @param $id
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-25 16:59:33
     */
    public function info($id): array {
        $task_info = WeeklyStatisticData::getTaskInfoById($id);
        if($task_info['status'] != WeeklyStatisticData::TASK_STATUS_DONE){
            throw new Exception($id." 任务未执行完成");
        }

        $info_list = json_decode($task_info['result'],true);
        $task_info['result'] = $this->get_info_list($task_info['type'], $info_list);

        return $task_info['result'];
    }


    /**
     * 格式化任务结果列表
     *
     * @param $type
     * @param $info_list
     *
     * @return array
     * <AUTHOR> 2024-01-25 17:02:47
     */
    private function get_info_list($type, $info_list): array {
        $res = [];

        switch ($type) {
            case WeeklyStatisticData::TASK_TYPE_DEPT:
            case WeeklyStatisticData::TASK_TYPE_BANK:
            case WeeklyStatisticData::TASK_TYPE_REMIT:
            case WeeklyStatisticData::TASK_TYPE_DEPT_LAST_WEEK:
            case WeeklyStatisticData::TASK_TYPE_SALEMAN_LAST_WEEK:
                $month_list = array_keys($info_list);
                $res['-'] = $month_list;
                foreach($info_list as $month => $product_data){
                    foreach($product_data as $name => $money){
                        if(!isset($res[$name])){
                            foreach($month_list as $mo){
                                $res[$name][$mo] = 0;
                            }
                        }
                        $res[$name][$month] = $money;
                    }
                }
                uksort($res,function($i,$j){
                    if($i != '-' && $j == '-'){
                        return -1;
                    }
                    if($i == '合计' && $j != '合计'){
                        return 1;
                    }
                    return 0;
                });
                break;
            case WeeklyStatisticData::TASK_TYPE_PRODUCT:
            case WeeklyStatisticData::TASK_TYPE_REMIT_LAST_WEEK:
            case WeeklyStatisticData::TASK_TYPE_PRODUCT_LAST_WEEK:
            case WeeklyStatisticData::TASK_TYPE_CUSTOMER_LAST_WEEK:
                $res = $info_list;
                break;
            case WeeklyStatisticData::TASK_TYPE_COST:
            default:
                //其他情况 返回空
                break;
        }
        return $res;
    }

}
