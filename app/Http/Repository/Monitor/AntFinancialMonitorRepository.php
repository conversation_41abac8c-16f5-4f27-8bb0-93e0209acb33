<?php

namespace App\Http\Repository\Monitor;

use App\Http\Repository\PublicRepository;
use Illuminate\Support\Facades\Redis;

class AntFinancialMonitorRepository extends PublicRepository
{
	private $redisHashName = 'antFinancialValueSpreadCache';
	
	/**
	 * 获取监控明细页面
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12/4 10:18
	 *
	 * @return array|integer
	 */
	public function getInfoForDetail()
	{
		$res = $this->getParamsForDetail();
		if (!is_array($res)) {
			return $res;
		}
		
		return $this->getRedisCache($res);
	}
	
	/**
	 * 获取缓存数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12
	 *
	 * @param $params array 参数
	 *
	 * @return array|integer
	 */
	protected function getRedisCache($params)
	{
		//在redis中的key
		$key  = $params['config_id'] . '_' . $params['product_id'];
		$data = Redis::connection('default')
					 ->hGet($this->redisHashName, $key);
		
		if (empty($data)) {
			return 20003;
		}
		$data = json_decode($data, true);
		if (!$data) {
			return 20004;
		}
		
		return $data;
	}
	
	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12/4 10:19
	 *
	 * @return array|integer
	 */
	protected function getParamsForDetail()
	{
		$product_id = $this->request->get('product_id');
		if (!$product_id) {
			return 20001;
		}
		
		$config_id = $this->request->get('config_id');
		if (!$config_id) {
			return 20002;
		}
		
		return compact('product_id', 'config_id');
	}
}