<?php

namespace App\Http\Repository\Monitor;

use App\Http\Repository\PublicRepository;
use App\Models\AccountProductModel;
use App\Models\Monitor\LogProduct;
use App\Models\Monitor\ProductStatis;
use App\Models\Monitor\ProductStatisExceptionPeriod;
use App\Models\Monitor\ProductStatisPeriod;
use App\Models\Monitor\ProductStatus;
use App\Models\Monitor\ProductValueSpreadPeriod;
use App\Models\Product;
use App\Providers\Monitor\ProductStatusService;
use App\Providers\Monitor\ValueSpreadGoalValueService;
use App\Providers\RedisCache\RedisCache;

class ProductStatisRepository extends PublicRepository
{
    public function getProductAndApikeyByFatherId($fatherId)
    {
        //获取父产品下的 子产品id
        $where = [
            ['father_id','=', $fatherId],
        ];
        $accountWhere['product_id'] = Product::getListByCondition($where, ['product_id'])->pluck('product_id')->toArray();
        $accountProductModel = new AccountProductModel([]);
        $apikeyInfoResult = $accountProductModel->getList($accountWhere);

        $haveApikeys = [];
        $result = [];

        foreach ( $apikeyInfoResult as &$v){

            if( in_array($v['apikey'], $haveApikeys) ){
                continue;
            }else{
                $item = [
                    'name' => $v['account_name'],
                    'apikey' => $v['apikey'],
                ];

                $result[] = $item;
                $haveApikeys[] = $v['apikey'];
            }
        }

        return $result;
    }

    public function getProductIdByFatherId($fatherId)
    {
        //获取父产品下的 子产品id
        $where = [
            ['father_id','=', $fatherId],
        ];
        $productList = Product::getListByCondition($where, ['product_id','product_name'])->toArray();
        foreach ( $productList as &$v ){
            $v['name'] = $v['product_name'];
        }
        return $productList;
    }

    public function getTotalInfo($where)
    {
        $where[] = ['channel_id', "=", 0];
        $totalData = ProductStatisPeriod::getTotalCountByCondition($where);
        $totalData = $this->ratioAndTimeToTotalInfo($totalData);

        $ids = ProductStatisPeriod::getIds($where);
        $ids = array_column($ids, 'id');
        $totalException = ProductStatisExceptionPeriod::getStatusCountByIds($ids);
        $totalData = $this->mergeTotalException($totalData, $totalException);
        $totalData['channel'] = '合计';

        return $totalData;
    }

    public function getChannelInfo($where)
    {
        $whereAllChannel = $where;
        $whereAllChannel[] = ['channel_id', ">", 0];
        $channelInfoArray = ProductStatisPeriod::getCountGroupbyChannel($whereAllChannel);
        foreach ( $channelInfoArray as $k=>$v ){
            $channelInfo = $this->ratioAndTimeToTotalInfo($v);
            //获取异常信息
            $whereException = $where;
            $whereException[] = ['channel_id','=',$v['channel_id']];
            $ids = ProductStatisPeriod::getIds($whereException);
            $ids = array_column($ids, 'id');
            $channelException = ProductStatisExceptionPeriod::getStatusCountByIds($ids);
            $channelInfo = $this->mergeTotalException($channelInfo, $channelException);

            //获取渠道名称
            $channelInfo['channel'] = RedisCache::instance('channelId_label_mapping')->get($v['channel_id']);
            $channelInfoArray[$k] = $channelInfo;
        }

        return $channelInfoArray;
    }

    public function getTotalValueSpreadInfo($where)
    {
        $where[] = ['channel_id', "=", 0];
        $totalData = ProductValueSpreadPeriod::getValueTotalCountByCondition($where);
        $totalData = $this->getTotalValueWithValue($totalData);
        $totalData['channel'] = '合计';
        $totalData['source'] = '合计';

        return $totalData;
    }

    public function getChannelValueSpreadInfo($where)
    {
        $whereAllChannel = $where;
        $whereAllChannel[] = ['channel_id', ">", 0];
        $channelInfoArray = ProductValueSpreadPeriod::getCountGroupbyChannel($whereAllChannel);

        foreach ( $channelInfoArray as $k=>$v ){
            $whereDetail = $where;
            $whereDetail[] = ['channel_id','=',$v['channel_id']];
            $channelInfo = ProductValueSpreadPeriod::getValueTotalCountByCondition($whereDetail);
            $channelInfo = $this->getTotalValueWithValue($channelInfo);
            //获取渠道名称
            $channelInfo['channel'] = RedisCache::instance('channelId_label_mapping')->get($v['channel_id']);
            $channelInfoArray[$k] = $channelInfo;
        }

        return $channelInfoArray;
    }

    public function getSourceValueSpreadInfo($where)
    {
        $whereAllSource = $where;
        $whereAllSource[] = ['source', ">", 0];
        $sourceInfoArray = ProductValueSpreadPeriod::getCountGroupbySource($whereAllSource);

        foreach ( $sourceInfoArray as $k=>$v ){
            $whereDetail = $where;
            $whereDetail[] = ['source','=',$v['source']];
            $sourceInfo = ProductValueSpreadPeriod::getValueTotalCountByCondition($whereDetail);
            $sourceInfo = $this->getTotalValueWithValue($sourceInfo);
            //获取渠道名称
            $sourceInfo['source'] = $this->getSourceName($v['source']);
            $sourceInfoArray[$k] = $sourceInfo;
        }

        return $sourceInfoArray;
    }

    private function getSourceName($source)
    {
        $name = "";

        switch ($source){
            case 99:
                $name = '数据源';
                break;
            case 1:
                $name = '规则补数';
                break;
            case 2:
                $name = '邦信分补数';
                break;
            case 3:
                $name = '缓存';
                break;
        }

        return $name;
    }

    public function ratioAndTimeToTotalInfo($totalInfo)
    {
        if( !$totalInfo['all'] ){
            $totalInfo = [
                'all' => "",
                'ratio' => "",
                'run_time' => "",
                'success' => "",
            ];

            return $totalInfo;
        }

        if( $totalInfo ){

            $totalInfo['ratio'] = divGetRatio($totalInfo['success'], $totalInfo['all'], 4);
            $totalInfo['run_time'] = intval($totalInfo['run_time']);
            return $totalInfo;
        }else{
            return [];
        }
    }

    public function mergeTotalException($totalInfo, $exception)
    {
        $totalInfo['exception'] = "";
        foreach ( $exception as $v ){
            $totalInfo['exception'] .= $v['status'].":".$v['num']."<br/>";
        }

        return $totalInfo;
    }

    public function getStatisHeader()
    {
        $header = [
            "channel" => '渠道',
            "all" => '总量',
            "success" => '成功量',
            "ratio" => '成功率',
            "run_time" => '平均响应时间',
            "exception" => '错误统计',
        ];

        return $header;
    }

    public function getValueSpreadHeader($productId)
    {
        $valueSpreadService = new ValueSpreadGoalValueService();

        $values = $valueSpreadService->getGoalValues($productId);

        $head = [
            'channel' => '渠道',
        ];
        foreach ( $values as $v ){
            $head['value_'.$v] = $v;
        }

        return $head;
    }

    public function getSourceValueSpreadHeader($productId)
    {
        $valueSpreadService = new ValueSpreadGoalValueService();

        $values = $valueSpreadService->getGoalValues($productId);

        $head = [
            'source' => '数据源',
        ];
        foreach ( $values as $v ){
            $head['value_'.$v] = $v;
        }

        return $head;
    }

    public function getTotalValueWithValue($data)
    {
        $result = [];
        $counts = array_column($data, 'num');
        $total = array_sum($counts);
        foreach ( $data as $v ){
            if( !$v['num'] ){
                continue;
            }
            $ratio = divGetRatio($v['num'], $total, 4);
            $result['value_'.$v['value']] = $ratio.'（'.$v['num'].'）';
        }

        return $result;
    }
}