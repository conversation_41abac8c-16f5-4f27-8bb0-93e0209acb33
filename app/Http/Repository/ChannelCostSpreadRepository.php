<?php

namespace App\Http\Repository;

use App\Models\Account;
use App\Models\Approval;
use App\Models\Channel;
use App\Models\ChannelAccountAdjust;
use App\Models\Customer;
use App\Models\Product;
use App\Models\SystemUser;
use App\Models\ChannelCostMinconsumeSpread;
use Illuminate\Support\Facades\DB;

class ChannelCostSpreadRepository
{
    public function getSpreadList()
    {
        $where = $this->getParams();
        $page = intval(request()->post('page', 1));
        $limit = intval(request()->post('limit', 10));
        $data = ChannelCostMinconsumeSpread::getList($where, (($page-1)*$limit), $limit);
        $user_data = SystemUser::pluck('realname', 'username');
        try {
            $list = [];
            foreach ($data['list'] as $value) {
                $info = [];
                $info['run_id'] = $value['run_id'];
                $info['month']=$value['month'];
                $info['money']=number_format($value['money'],2);
                $info['run_status']=$value['run_status'];
                $info['operator'] = $value['operator'];
                $info['category'] = $value['category'];
                $info['remarks']=$value['remarks'];
                $info['admin'] = isset($user_data[$value['admin']]) ? $user_data[$value['admin']] : '';
                $info['create_at']=$value['create_at'];
                $info['update_at']=$value['update_at'];
                $list[] = $info;
            }
        } catch (\Exception $e) {
        }
        // $list = $data['list'];
        $count = $data['count'];
        return compact('list', 'count');
    }

    /**
     * 获取查询条件
     * @return array
     */
    public function getParams()
    {
        $parmas = [];
        $month = request()->post('month', null);
        $operator = request()->post('operator', null);

        $run_status = request()->post('run_status', null);
        $category = request()->post('category', null);
        if (!empty($month)) {
            $parmas['month'] = $month;
        }
        if ($run_status>0) {
            $parmas['run_status'] = $run_status;
        }
        if (!empty($operator)) {
            $parmas['operator'] = $operator;
        }
        if (!empty($category)) {
            $parmas['category'] = $category;
        }
        $parmas['is_delete'] = 0;
        return $parmas;
    }


    public function saveSpreadTask($sysName = null)
    {
        $params = request()->post();
        $user_cookie = request()->post('user_cookie');
        if (!isset($sysName)) {
            return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
        }
        if (1000 > abs($params['money'])) { //审批通过 或者 调整金额不超过1000 直接插入
            // month 2022-10 转为 202210
            $month = str_replace('-', '', $params['month']);
            $params['month'] = $month;
            ChannelCostMinconsumeSpread::addRecord($params, $sysName);
           return $this->autoBottomChannelCost($month, $params['operator']);
        }else{
            return $this->costSpreadToApproval([$params], $user_cookie, $sysName);
        }
    }


    /**
     * @param $aDatas
     * @param $sUserCookie
     * @return bool
     */
    private function costSpreadToApproval($aDatas = [], $sUserCookie = '' ,$sysName = '')
    {
        $aChannel = Channel::getAllChannel(true);
        $aChanelMap = array_column($aChannel, 'label', 'channel_id');

        $approval_data = [];
        foreach ($aDatas as $aData) {
            $iOperator = $aData['operator'];
            $sMoney = $aData['money'];
            $sRemark = $aData['remarks'];
            $sMonth = $aData['month'];
            $aData['admin'] = $sysName;

            $sApplyContent = sprintf('渠道成本保底分摊, 调整月份: %s, 渠道: %s, 原因:「%s」,  金额: 「%s」',
                $sMonth, $aChanelMap[$iOperator] ?? '', $sRemark, $sMoney);

            $approval_data[] = [
                'uri' => Approval::URL_CHANNEL_COST_SPREAD,
                'data' => $aData,
                'apply_content' => $sApplyContent,
            ];
        }
        if ($approval_data){
            return ApprovalRepository::BatchAddApproval($approval_data, $sUserCookie);
        }
        return true;
    }

    #自动执行保底分摊
    public function autoBottomChannelCost($month, $channel_id)
    {
        #不需要保底分摊的渠道不执行
        $need_bottom_cost = Channel::getNeedBottomCostChannel($channel_id);
        if (empty($need_bottom_cost)) {
            return true;
        }

        $where = [['month', '=', $month], ['operator', '=', $channel_id]];
        $historyRecord = ChannelCostMinconsumeSpread::getList($where, 0, 100, 'asc');

        $duiZhangAllMoney = 0; #后台对账调整总金额
        $firstBottomMoney = 0; #每月2号自动执行保底金额
        $lastDuiZhangMoney = 0; #最后一次 对账调整金额
        $bottomMoney = 0; #对账调整总额 大于 月初首次保底金额时候  应该将之前的保底 填平
        $firstBottom = 0;

        foreach ($historyRecord['list'] as $item) {
            #对账调整总额
            if ($item['category'] == 2) {
                $lastDuiZhangMoney = $item['money'];
                $duiZhangAllMoney = bcadd($duiZhangAllMoney, $item['money'], 6);
            }
            #只有当 对账调整总额 大于 月初首次保底金额时候 才会用到
            if ($item['category'] == 1) {
                $bottomMoney = bcadd($bottomMoney, $item['money'], 6);
                $firstBottom == 0 && $firstBottomMoney = $item['money'];
                $firstBottom = 1;
            }
        }

        $record['category'] = 1;
        $record['remarks'] = '后台添加对账调整后自动执行保底';
        $record['operator'] = $channel_id;
        $record['month'] = $month;

        #对账调整总额 小于 月初首次保底金额时
        if ($duiZhangAllMoney < $firstBottomMoney) {
            $record['money'] = -$lastDuiZhangMoney;
            ChannelCostMinconsumeSpread::addRecord($record, 'auto_bottom_channel_cost');
        } elseif ($duiZhangAllMoney > $firstBottomMoney && $bottomMoney != 0) {
            #对账调整总额 大于 月初首次保底金额时
            $record['money'] = -$bottomMoney;
            ChannelCostMinconsumeSpread::addRecord($record, 'auto_bottom_channel_cost');
        }
        return true;
    }

    public function deleteSpreadRecord($sysName = null)
    {
        $run_id = request()->post('run_id', '');
        if (!isset($sysName)||empty($run_id)) {
            return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
        }
        $run_status = $this->getRunStatus($run_id);
        if($run_status==3){
            ChannelAccountAdjust::where('run_id',$run_id)->delete();
        }

        return ChannelCostMinconsumeSpread::delRecord($run_id, $sysName);
    }

    public function getRunStatus()
    {
        $run_id = request()->post('run_id', '');
        if (empty($run_id)) {
            return false;
        } else {
            return  ChannelCostMinconsumeSpread::where('run_id', $run_id)->value('run_status');
        }
    }

    public function getSpreadDetail()
    {
        $run_id = request()->post('run_id', '');
        if (empty($run_id)) {
            return false;
        } else {
            $sum = 0;
            $list =   ChannelAccountAdjust::where('run_id', $run_id)
            ->select([
                'customer_id',
                'date',
                DB::raw('sum(money) as money')
            ])
            ->groupBy([
                'customer_id',
            ])->get()->toArray();
            array_walk($list,function($item) use(&$sum){
                $sum +=$item['money'];
            });
            $sum = number_format($sum,2);
            return compact('list','sum');
        }
    }

}
