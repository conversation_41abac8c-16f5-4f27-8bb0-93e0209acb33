<?php

namespace App\Http\Repository;


use App\Models\ConfigChannelStatis as StatisModel;


class ConfigChannelStatis
{
    /**
     * 配置管理-查得率 列表
     * @return mixed
     * @throws \Exception
     */
    public function statisList()
    {
        $params = $this->getStatisParams();
        $where = [];
        $channel = $params['channel'] ? $params['channel'] : null;

        $offset = intval($params['offset']) ? intval($params['offset']) : null;
        $limit = intval($params['limit']) ? intval($params['limit']) : null;

        $count = StatisModel::getCountByCondition($where, $channel);
        $data = StatisModel::getListByCondition($where, null,  'id desc', $channel, $offset, $limit);
        return compact('count', 'data');
    }
    /**
     * 配置管理-查得率 详情
     * @return mixed
     * @throws \Exception
     */
    public function statisInfo()
    {
        $id = intval(request()->post('id', 0));
        if (!$id) {
            return false;
        }
        $data = StatisModel::findOneItem(['id' => $id]);
        return $data;
    }

    /**
     * 渠道查得率配置 唯一性校验
     * @param $cid
     * @param $custom_key
     * @param $type
     * @return mixed
     */
    public function checkStatisUnique($cid, $custom_key, $type)
    {
        return StatisModel::getCountByCondition(compact('cid', 'custom_key', 'type'));
    }

    /**
     * 配置管理-查得率 修改
     * @return mixed
     * @throws \Exception
     */
    public function statisEdit()
    {
        $id = intval(request()->post('id', 0));
        $field = trim(request()->post('field', ''));
        $value = floatval(request()->post('value', 0));

        $auto_add = StatisModel::$auto_add['OPERATION_TYPE']; //默认将type字段改为 已配置 状态
        if (!$id || !$field || !$value
            || !in_array($field, ['th_one', 'th_two',])) {
            throw new \Exception("参数错误");
        }
        $data = StatisModel::editItemById($id, [$field => $value, 'auto_add'=>$auto_add]);
        return $data;
    }

    /**
     * 配置管理-查得率 添加
     * @return mixed
     * @throws \Exception
     */
    public function statisAdd()
    {
        $cid = intval(request()->post('cid', 0));
        $type = intval(request()->post('type', 0));
        $custom_key = trim(request()->post('custom_key', 0));
        $th_one = floatval(request()->post('th_one', 0));
        $th_two = floatval(request()->post('th_two', 0));
        $auto_add = StatisModel::$auto_add['OPERATION_TYPE']; //默认将type字段改为 已配置 状态
        $count = $this->checkStatisUnique($cid, $custom_key, $type);
        if ($count > 0) {
            throw new \Exception("该条记录已经存在");
        }

        $data = StatisModel::addItem( compact('custom_key', 'cid', 'type', 'th_two', 'th_one', 'auto_add'));
        return $data;
    }

    /**
     * 校验/获取 查询的参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getStatisParams()
    {
        $channel = request()->post('channel');
        $offset = request()->post('offset');
        $limit = request()->post('limit');

        return compact('channel', 'offset', 'limit');
    }
}
