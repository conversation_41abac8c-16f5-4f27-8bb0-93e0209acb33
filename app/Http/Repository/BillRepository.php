<?php

namespace App\Http\Repository;

use App\Define\Common;
use App\Exports\{
    BillHistoryExport,
    CustomerMonthBillExport,
    CustomerProductBillExport,
    DayBillExport,
    EmailBill,
    ProductBillExport,
    ProductCustomerBillExport,
    ProductMonthBillExport
};
use App\Models\{
    Account, AccountProduct, Customer, MoneyRecharge, MongoBillDay, MongoBillDayLogs, MongoBillMonth, MongoLog, Product
};
use App\Mail\BillMonth;
use App\Support\CustomException;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\WechatExceptionTrait;
use function GuzzleHttp\Psr7\str;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Matrix\Exception;
use PharIo\Manifest\ManifestDocumentException;

class BillRepository
{
    use CurlTrait, WechatExceptionTrait;
    /** @var string 客户id */
    private $customer_id;

    /** @var array 按照算法场景分组的账单片段 */
    private $list_bill_section_group_alg;

    /** @var MongoBillMonth 正在操作的账单单元 */
    private $section_bill_item;

    /** @var array 产品的消费明细属性 */
    private $list_product_alo_consumptions;

    /** @var array 账单配置冲突的产品ID */
    private $list_product_ids_which_bill_conflict = [];

    /** @var string 催收分析快捷版的key */
    private $product_key_cuishou = '99bbdb8426f8b4e8d0cc3ebd92484590';

    /** @var int 一年按照固定的365计算 */
    private $days_in_years = 365;


    /** @var array 不能输出excel的product_ids */
    private $list_disabled_product_ids = [];

    /** @var array 催收快捷版的产品ID列表 */
    private $list_cuishou_short_product_ids = [];

    /** @var array 账单时间映射关系 */
    private $list_fee_time_rule_mapping = [
        1 => '日',
        2 => '月',
        3 => '年'
    ];

    /** @var string 余额百分比标记的前置key */
    private $prefix_key_balance_percent = 'customer_bill_warning_balance_percent_';

    /** @var string 剩余天数标记的前置key */
    private $prefix_key_days_left = 'customer_bill_warning_days_left_';

    /** @var string redis链接 */
    private $redis_bill_connection = 'db_backend';

    /** @var bool 是否需要生成催收分分析快捷版的明细 */
    private $_determine_need_create_shortcut = false;

    /** @var array 今天当前客户快捷版计费规则有冲突的账单 */
    private $_list_shortcut_conflict_bill_this_year = [];

    /** @var array 快捷版整合成的属性 */
    private $_list_shortcut_alo_computed = [];

    /** @var array 今年快捷版生成账单列表 */
    private $_list_shortcut_bill_this_year = [];

    /** @var array  有冲突的快捷版算法 */
    private $list_shortcut_useless_alog = [];

    /**
     * 日账单日志列表
     * @return array
     */
    public function getDayLogLists(): array
    {
        // 条件
        $where = $this->_genParamsForDayLogLists();

        // 列表
        // 列表
        return [
            'total' => $this->_genTotalNumberForDayLogList($where),
            'data' => $this->_getDayLogListDo($where),
            'page' => (int)trim(request()->get('page', 1)),
            'page_size' => (int)trim(request()->get('page_size', 50)),
        ];
    }

    /**
     * @param array $where
     * @return array
     */
    private function _getDayLogListDo(array $where)
    {
        // 参数
        list ($page_size, $page) = [
            (int)trim(request()->get('page_size', 50)),
            (int)trim(request()->get('page', 1))
        ];

        // 偏移
        $offset = $page_size * ($page - 1);
        $list = MongoBillDayLogs::where($where)
            ->orderBy('_id', 'desc')
            ->offset($offset)
            ->limit($page_size)
            ->get();
        return $list ? $list->toArray() : [];
    }

    /**
     * 总数
     * @param array $where
     * @return mixed
     */
    private function _genTotalNumberForDayLogList(array $where)
    {
        return MongoBillDayLogs::where($where)
            ->count();
    }


    /**
     * 条件
     * @return array
     */
    private function _genParamsForDayLogLists(): array
    {
        list($date, $customer_id, $account_id, $product_id, $month_begin, $month_end, $uuid) = [
            trim(request()->get('date')),
            trim(request()->get('customer_id')),
            trim(request()->get('account_id')),
            (int)trim(request()->get('product_id')),
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
            trim(request()->get('uuid')),
        ];
        if ($date) {
            $date = date('Ymd', strtotime($date));
        }

        $where = array_filter(compact('date', 'customer_id', 'account_id', 'product_id', 'uuid'), function ($item) {
            return $item;
        });

        if (!$month_begin && !$month_end) {
            return $where;
        }

        if ($month_begin && $month_end) {
            $where['month'] = [
                '$lte' => $this->_formatMonth($month_end),
                '$gte' => $this->_formatMonth($month_begin),
            ];
            return $where;
        }

        if ($month_begin) {
            $where['month'] = [
                '$gte' => $this->_formatMonth($month_begin),
            ];
            return $where;
        }
        $where['month'] = [
            '$lte' => $this->_formatMonth($month_end),
        ];
        return $where;
    }


    /**
     * 日账单列表
     * @return array
     */
    public function getDayLists(): array
    {
        // 条件
        $where = $this->_genParamsForDayLists();

        // 列表
        return [
            'total' => $this->_genTotalNumberForDayList($where),
            'data' => $this->_getDayListDo($where),
            'page' => (int)trim(request()->get('page', 1)),
            'page_size' => (int)trim(request()->get('page_size', 50)),
        ];
    }

    /**
     * @param array $where
     * @return array
     */
    private function _getDayListDo(array $where)
    {
        // 参数
        list ($page_size, $page) = [
            (int)trim(request()->get('page_size', 50)),
            (int)trim(request()->get('page', 1))
        ];

        // 偏移
        $offset = $page_size * ($page - 1);
        $list = MongoBillDay::where($where)
            ->orderBy('_id', 'desc')
            ->offset($offset)
            ->limit($page_size)
            ->get();
        return $list ? $list->toArray() : [];
    }

    /**
     * 总数
     * @param array $where
     * @return mixed
     */
    private function _genTotalNumberForDayList(array $where)
    {
        return MongoBillDay::where($where)
            ->count();
    }

    /**
     * 条件
     * @return array
     */
    private function _genParamsForDayLists(): array
    {
        list($date, $customer_id, $account_id, $product_id, $month_begin, $month_end, $slug_id) = [
            trim(request()->get('date')),
            trim(request()->get('customer_id')),
            trim(request()->get('account_id')),
            (int)trim(request()->get('product_id')),
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
            trim(request()->get('slug_id')),
        ];
        if ($date) {
            $date = date('Ymd', strtotime($date));
        }

        $where = array_filter(compact('date', 'customer_id', 'account_id', 'product_id', 'slug_id'), function ($item) {
            return $item;
        });

        if (!$month_begin && !$month_end) {
            return $where;
        }

        if ($month_begin && $month_end) {
            $where['month'] = [
                '$lte' => $this->_formatMonth($month_end),
                '$gte' => $this->_formatMonth($month_begin),
            ];
            return $where;
        }

        if ($month_begin) {
            $where['month'] = [
                '$gte' => $this->_formatMonth($month_begin),
            ];
            return $where;
        }
        $where['month'] = [
            '$lte' => $this->_formatMonth($month_end),
        ];
        return $where;
    }

    /**
     * @param string $month
     * @return string
     */
    private function _formatMonth(string $month): string
    {
        if (strpos($month, '-') === false) {
            return $month;
        }
        return date('Ym', strtotime($month));
    }

    /**
     * 各个客户对应账单
     * @return array
     * @throws CustomException
     */
    public function getCustomerBill(): array
    {
        // 校验参数
        $this->validateParamsForCustomer();

        // 生成聚合条件
        $pipeline = $this->genPipelineForCustomerBill();

        // 列表
        return $this->getCustomerBillDo($pipeline);
    }

    /**
     * 生成列表
     * @param array $pipeline
     * @return array
     */
    private function getCustomerBillDo(array $pipeline): array
    {
        return MongoBillMonth::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->reduce(function ($carry, $item) {
            $carry[] = [
                'customer_id' => $item->_id,
                'section_number_sum' => $item->section_number_sum,
                'money_sum' => $item->money_sum,
            ];
            return $carry;
        }, []);
    }

    /**
     * @return array
     */
    private function genPipelineForCustomerBill(): array
    {
        list($group, $match) = [
            $this->genGroupParamsForCustomer(),
            $this->genMatchParamsForCustomerBill()
        ];

        return [
            [
                '$match' => $match
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 条件
     * @return array
     */
    private function genMatchParamsForCustomerBill(): array
    {
        return [
            'customer_id' => [
                '$in' => request()->get('customer_id')
            ]
        ];
    }

    /**
     * 分组的依据
     * @return array
     */
    private function genGroupParamsForCustomer(): array
    {
        return [
            '_id' => '$customer_id',
            'section_number_sum' => [
                '$sum' => '$section_invoked_number'
            ],
            'money_sum' => [
                '$sum' => '$money'
            ]
        ];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForCustomer()
    {
        $customer_ids = request()->get('customer_id');
        if (!$customer_ids || !is_array($customer_ids)) {
            throw new CustomException('请输入合法的客户ID');
        }
    }

    /**
     * 特定计费配置对应的总调用量,金额消费信息
     * @return array
     * @throws CustomException
     */
    public function getSectionList(): array
    {
        // 校验参数
        $this->validateParamsForSection();

        // 生成聚合条件
        $pipeline = $this->genPipelineParamsForSection();

        // 列表
        return $this->getSectionListDo($pipeline);
    }

    /**
     * 列表
     * @param array $pipeline
     * @return array
     */
    private function getSectionListDo(array $pipeline): array
    {
        return MongoBillMonth::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->reduce(function ($carry, $item) {
            $carry[] = [
                'section_id' => $item->_id,
                'section_number_sum' => $item->section_number_sum,
                'money_sum' => $item->money_sum,
            ];
            return $carry;
        }, []);
    }

    /**
     * 生成聚合条件
     * @return array
     */
    private function genPipelineParamsForSection(): array
    {
        list($group, $match) = [
            $this->genGroupParamsForAggregateSection(),
            $this->genMatchParamsForAggregateSection()
        ];

        return [
            [
                '$match' => $match
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 条件
     * @return array
     */
    private function genMatchParamsForAggregateSection(): array
    {
        $list_section_ids = array_map(function ($item) {
            return (int)$item;
        }, request()->get('section_id'));

        return [
            'section_source.id' => [
                '$in' => $list_section_ids
            ]
        ];
    }

    /**
     * 分组的依据
     * @return array
     */
    private function genGroupParamsForAggregateSection(): array
    {
        return [
            '_id' => '$section_source.id',
            'section_number_sum' => [
                '$sum' => '$section_invoked_number'
            ],
            'money_sum' => [
                '$sum' => '$money'
            ]
        ];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForSection()
    {
        $list_section = request()->get('section_id');
        if (!$list_section || !is_array($list_section)) {
            throw new CustomException('请传递合法的section_id');
        }

        array_walk($list_section, function ($item) {
            if (!is_numeric($item)) {
                throw new CustomException('请传递合法的section_id');
            }
        });
    }

    /**
     * 账单列表
     * @throws CustomException
     */
    public function sourceList()
    {
        // 参数校验
        $this->validateParamsForSource();

        // 列表
        return [
            'total' => $this->genTotalNumberForSource(),
            'data' => $this->getSourceListDo(),
            'page' => (int)trim(request()->get('page', 1)),
            'page_size' => (int)trim(request()->get('page_size', 50)),
        ];
    }

    /**
     * 获取列表
     * @return array
     */
    private function getSourceListDo(): array
    {
        // 参数
        list ($where, $page_size, $page) = [
            $this->genParamsForSourceList(),
            (int)trim(request()->get('page_size', 50)),
            (int)trim(request()->get('page', 1))
        ];

        // 偏移
        $offset = $page_size * ($page - 1);
        $list = MongoBillMonth::where($where)
            ->orderBy('_id', 'desc')
            ->offset($offset)
            ->limit($page_size)
            ->get();
        return $list ? $list->toArray() : [];
    }

    /**
     * @return int
     */
    private function genTotalNumberForSource(): int
    {
        // 参数
        $where = $this->genParamsForSourceList();

        return MongoBillMonth::where($where)
            ->count();
    }

    /**
     * 生成参数
     * @return array
     */
    private function genParamsForSourceList(): array
    {
        list($uuid, $month_begin, $month_end, $customer_id, $account_id, $product_id, $bill_config_id) = [
            trim(request()->get('uuid')),
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
            trim(request()->get('customer_id')),
            trim(request()->get('account_id')),
            (int)trim(request()->get('product_id')),
            trim(request()->get('bill_config_id'))
        ];

        $where = array_filter(compact('uuid', 'customer_id', 'account_id', 'product_id'), function ($item) {
            return $item;
        });

        // 如果限制了计费配置的ID
        if ($bill_config_id) {
            $where['section_source.id'] = (int)$bill_config_id;
        }

        // 没有做月份的限制
        if (!$month_end && !$month_begin) {
            return $where;
        }

        // 同时限制了两个
        if ($month_begin && $month_begin) {
            $where['month'] = [
                '$gte' => date('Ym', strtotime($this->monthFormatChange($month_begin))),
                '$lte' => date('Ym', strtotime($this->monthFormatChange($month_end))),
            ];
            return $where;
        }

        if ($month_begin) {
            $where['month'] = [
                '$gte' => date('Ym', strtotime($this->monthFormatChange($month_begin))),
            ];
            return $where;
        }

        $where['month'] = [
            '$lte' => date('Ym', strtotime($this->monthFormatChange($month_end))),
        ];
        return $where;
    }

    /**
     * 参数校验
     * @throws CustomException
     */
    private function validateParamsForSource()
    {
        list($month_begin, $month_end) = [
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
        ];

        // 如果开始月份大于结束月份
        if (!$month_end || !$month_begin) {
            return;
        }

        $month_begin = $this->monthFormatChange($month_begin);
        $month_end = $this->monthFormatChange($month_end);
        if (strtotime($month_begin) > strtotime($month_end)) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }

    /**
     * 日账单生成excel
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadDay()
    {
        $list_bill = request()->post('list_bills');

        return Excel::download(new DayBillExport($list_bill), 'customer_product.xlsx');
    }

    /**
     * 每天的账单信息
     * @return array
     * @throws CustomException
     */
    public function dayList(): array
    {
        // 校验参数
        $this->validateParamsForDay();

        // 生成聚合条件
        $pipeline = $this->genParamsForAggregateDay();

        // 聚合操作
        return $this->getAggregateDayBill($pipeline);
    }

    /**
     * 聚合操作
     * @param array $pipeline
     * @return array
     */
    private function getAggregateDayBill(array $pipeline): array
    {
        // 总量
        $total = [
            'date' => '总计',
            'money_sum' => 0,
            'section_number_sum' => 0
        ];

        $lists = MongoBillDay::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->reduce(function ($carry, $item) use (&$total) {
            $item->date = $item->_id;
            $total['money_sum'] += $item['money_sum'];
            $total['section_number_sum'] += $item['section_number_sum'];

            $carry[] = $item->toArray();
            return $carry;
        }, []);

        array_unshift($lists, $total);
        return $lists;
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForDay()
    {
        // 校验月份参数
        $this->validateMonthForDay();

        // 对日期的校验
        $this->validateDayParamsForDay();

        // 校验客户ID
        $this->validateCustoemrParamsForDay();
    }

    /**
     * @throws CustomException
     */
    private function validateCustoemrParamsForDay()
    {
        if (!trim(request()->get('customer_id'))) {
            throw new CustomException('请输入客户ID');
        }
    }

    /**
     * 对日期的校验
     * @throws CustomException
     */
    private function validateDayParamsForDay()
    {
        list($day_begin, $day_end, $day) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
            trim(request()->get('day')),
        ];

        if (!$day_begin && !$day_end) {
            return;
        }

        if ($day_end && !$day_begin) {
            throw new CustomException('请输入day_begin');
        }

        if ($day_begin && !$day_end) {
            throw new CustomException('请输入 day_end');
        }

        // 大小比较
        if (strtotime($day_begin) > strtotime($day_end)) {
            throw new CustomException('开始日期必须小于结束日期');
        }

        if (strtotime($day_end) > strtotime(date('Y-m-d'))) {
            throw new CustomException('日期参数不能超过当前时间');
        }

        // 不需要三者比较
        if (!$day) {
            return;
        }

        if (strtotime($day) < strtotime($day_begin) || strtotime($day) > strtotime($day_end)) {
            throw new CustomException('选中的day和day_begin day_en冲突');
        }

        if (strtotime($day) > strtotime(date('Y-m-d'))) {
            throw new CustomException('日期参数不能超过当前时间');
        }
    }

    /**
     * 对月份的校验
     * @throws CustomException
     */
    private function validateMonthForDay()
    {
        list($month_begin, $month_end, $month) = [
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
            trim(request()->get('month')),
        ];

        if (!$month_end && !$month_begin) {
            return;
        }

        // 对月份的校验
        if (!$month_begin && $month_end) {
            throw new CustomException('请输入 month_begin');
        }

        if ($month_begin && !$month_end) {
            throw new CustomException('请输入 month_end');
        }

        // 月份比较
        $month_begin = $this->monthFormatChange($month_begin);
        $month_end = $this->monthFormatChange($month_end);
        if (strtotime($month_begin) > strtotime($month_end)) {
            throw new CustomException('开始月份必须小于结束月份');
        }

        // 不能超过当前月份
        if (strtotime($month_end) > strtotime(date('Y-m'))) {
            throw new CustomException('月份参数不能超过当前的月份');
        }

        // 选中的月份是否在合法区间
        if (!$month) {
            return;
        }
        $month = $this->monthFormatChange($month);
        if (strtotime($month) < strtotime($month_begin) || strtotime($month) > strtotime($month_end)) {
            throw new CustomException('选中的month和month_begin,month_end冲突');
        }

        if (strtotime($month) > strtotime(date('Y-m'))) {
            throw new CustomException('月份参数不能超过当前的月份');
        }
    }

    /**
     * 月份统一成Y-m格式
     * @param $month
     * @return string
     */
    private function monthFormatChange(string $month): string
    {
        // 判断是否是Y-m格式的月份
        if (date('Y-m', strtotime($month)) == $month) {
            return $month;
        }

        return date('Y-m', strtotime($month . '01'));
    }

    /**
     * 生成聚合条件
     * @return array
     */
    private function genParamsForAggregateDay(): array
    {
        list($group, $match) = [
            $this->genGroupParamsForAggregateDay(),
            $this->genMatchParamsForAggregateDay()
        ];

        return [
            [
                '$match' => $match
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     *
     * @return array
     */
    private function genMatchParamsForAggregateDay(): array
    {
        list($customer_id, $account_id, $product_id) = [
            trim(request()->get('customer_id')),
            trim(request()->get('account_id')),
            trim(request()->get('product_id'))
        ];

        // 选填参数
        $where = array_filter(compact('customer_id', 'account_id'), function ($item) {
            return $item;
        });

        // 限制产品
        if ($product_id) {
            $where['product_id'] = (int)$product_id;
        }

        // 设置月份
        $where = $this->setMonthParamsForDay($where);

        // 设置日期
        $where = $this->setDayParamsForDay($where);

        return $where;
    }

    /**
     * 设置日期
     * @param array $where
     * @return array
     */
    private function setDayParamsForDay(array $where): array
    {
        list($date, $date_begin, $date_end) = [
            trim(request()->get('day')),
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
        ];


        // 如果只限制了date_begin $date_end
        if ($date_begin && $date_end && !$date) {
            $where['date'] = [
                '$gte' => date('Ymd', strtotime($date_begin)),
                '$lte' => date('Ymd', strtotime($date_end))
            ];
        }

        // 如果限制了date
        if ($date) {
            $where['date'] = date('Ymd', strtotime($date));
        }
        return $where;
    }

    /**
     * 设置月份
     * @param array $where
     * @return array
     */
    private function setMonthParamsForDay(array $where): array
    {
        list($month_begin, $month_end, $month) = [
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
            trim(request()->get('month')),
        ];

        // 如果只是限定了month_begin month_end
        if ($month_begin && $month_end && !$month) {
            $month_begin = $this->monthFormatChange($month_begin);
            $month_end = $this->monthFormatChange($month_end);
            $where['month'] = [
                '$gte' => date('Ym', strtotime($month_begin)),
                '$lte' => date('Ym', strtotime($month_end))
            ];
        }

        // 只要存在month 则做这样的限定
        if ($month) {
            $month = $this->monthFormatChange($month_end);
            $where['month'] = date('Ym', strtotime($month));
        }

        return $where;
    }

    /**
     * 分组的依据
     * @return array
     */
    private function genGroupParamsForAggregateDay(): array
    {
        return [
            '_id' => '$date',
            'section_number_sum' => [
                '$sum' => '$section_invoked_number'
            ],
            'money_sum' => [
                '$sum' => '$money'
            ]
        ];
    }

    /**
     * 删除客户余额监控标记
     * @param string $customer_id
     * @throws CustomException
     */
    public function warningFlag(string $customer_id)
    {
        // 校验条件
        $this->validateParamsForFlag($customer_id);

        // 清理标记
        $this->flushFlag($customer_id);
    }

    /**
     * 清理标记
     * @param string $customer_id
     */
    private function flushFlag(string $customer_id)
    {
        app('redis')->connection($this->redis_bill_connection)
            ->del($this->prefix_key_days_left . $customer_id);

        app('redis')->connection($this->redis_bill_connection)
            ->del($this->prefix_key_balance_percent . $customer_id);
    }

    /**
     * 校验条件
     * @param string $customer_id
     * @throws CustomException
     */
    private function validateParamsForFlag(string $customer_id)
    {
        if (!Customer::getOneItemByCondition(compact('customer_id'))) {
            throw new CustomException('customer_id : ' . $customer_id . ' 对应的客户不存在');
        }
    }

    /** @var array 时间计费规则映射 */
    private $list_time_rule_mapping = [
        1 => 'day',
        2 => 'month',
        3 => 'year',
    ];

    /** @var string 调用量驱动的前缀 */
    private $driver_prefix = 'BillStat';

    /**
     * 客户每月的账单excel
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function genCustomerMonthExcel()
    {
        $list_bill = request()->post('list_bills');

        return Excel::download(new CustomerMonthBillExport($list_bill), 'customer_product.xlsx');
    }

    /**
     * 客户每月的账单
     * @throws CustomException
     */
    public function getCustomerBillGroupMonth(): array
    {
        // 校验参数
        $this->validateParamsForCustomerMonth();

        // 获取列表
        return $this->getCustomerBillGroupMonthList();
    }

    /**
     * 获取列表
     * @return array
     */
    private function getCustomerBillGroupMonthList(): array
    {
        // 生成pipeline
        $pipeline = $this->genPipelineForCustomerMonth();

        // 生成月账单列表(聚合)
        return $this->genMonthBillAggregateList($pipeline);
    }

    /**
     * 生成pipeline
     */
    private function genPipelineForCustomerMonth(): array
    {
        list($match, $group) = [
            $this->genMatchPartForCustomerMonth(),
            $this->genGroupPartForBillMonth()
        ];

        return [
            [
                '$match' => $match
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 生成match
     * @return array
     */
    private function genMatchPartForCustomerMonth(): array
    {
        list($customer_id, $product_id, $month_begin, $month_end, $account_id) = [
            trim(request()->get('customer_id')),
            (int)request()->get('product_id'),
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
            trim(request()->get('account_id')),
        ];

        $month = [
            '$gte' => date('Ym', strtotime($month_begin)),
            '$lte' => date('Ym', strtotime($month_end)),
        ];

        if ($account_id) {
            return compact('customer_id', 'month', 'account_id', 'product_id');
        }

        return compact('customer_id', 'month', 'product_id');
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForCustomerMonth()
    {
        // 产品id && 客户id 校验
        if (!trim(request()->get('customer_id'))) {
            throw new CustomException('customer_id不可以为空');
        }

        if (!trim(request()->get('product_id'))) {
            throw new CustomException('product_id不可以为空');
        }

        // 时间校验
        $month_begin = trim(request()->get('month_begin'));
        $month_end = trim(request()->get('month_end'));

        if (!$month_begin) {
            throw new CustomException('开始月份不可以为空');
        }

        if (!$month_end) {
            throw new CustomException('结束月份不可以为空');
        }

        $time_diff = strtotime($month_end) - strtotime($month_begin);
        if ($time_diff < 0) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }

    /**
     * 产品每月账单excel
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function genProductMonthExcel()
    {
        $list_bill = request()->post('list_bills');

        return Excel::download(new ProductMonthBillExport($list_bill), 'file.xlsx');
    }

    /**
     * 产品每月账单
     * @throws CustomException
     * @return array
     */
    public function getProductBillGroupMonth(): array
    {
        //  校验参数
        $this->validateParamsForProductMonth();

        //  生成列表
        return $this->getProductBillGroupMonthList();
    }

    /**
     * 生成列表
     * @return array
     */
    private function getProductBillGroupMonthList(): array
    {
        // 生成pipeline
        $pipeline = $this->genPipelineForProductMonth();

        return $this->genMonthBillAggregateList($pipeline);
    }

    /**
     * 生成月账单列表(聚合)
     * @return array
     */
    private function genMonthBillAggregateList(array $pipeline): array
    {
        // 生成结果
        $total = [
            'month' => '总计',
            'money_sum' => 0,
            'section_number_sum' => 0
        ];

        $list = MongoBillMonth::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->map(function ($item) use (&$total) {
            $item->month = $item->_id;

            $total['money_sum'] += $item->money_sum;
            $total['section_number_sum'] += $item->section_number_sum;
            return $item;
        })->toArray();

        array_unshift($list, $total);
        return $list;
    }

    /**
     * 生成pipeline
     * @return array
     */
    private function genPipelineForProductMonth(): array
    {
        list($match, $group) = [
            $this->genMatchPartForProductMonth(),
            $this->genGroupPartForBillMonth()
        ];

        return [
            [
                '$match' => $match
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 生成group part
     * @return array
     */
    private function genGroupPartForBillMonth(): array
    {
        return [
            '_id' => '$month',
            'section_number_sum' => [
                '$sum' => '$section_invoked_number'
            ],
            'money_sum' => [
                '$sum' => '$money'
            ]
        ];
    }

    /**
     * 生成match
     * @return array
     */
    private function genMatchPartForProductMonth(): array
    {
        list($product_id, $month_begin, $month_end) = [
            (int)request()->get('product_id'),
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
        ];

        // 快捷版特殊处理
        if ($product_id == 210) {
            $product_id = [
                '$in' => $this->getSubCuishouShortProductIds()
            ];
        }

        $month = [
            '$gte' => date('Ym', strtotime($month_begin)),
            '$lte' => date('Ym', strtotime($month_end)),
        ];

        return compact('product_id', 'month');
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForProductMonth()
    {
        // 产品id && 客户id 校验
        if (!trim(request()->get('product_id'))) {
            throw new CustomException('product_id不可以为空');
        }

        // 时间校验
        $month_begin = trim(request()->get('month_begin'));
        $month_end = trim(request()->get('month_end'));

        if (!$month_begin) {
            throw new CustomException('开始月份不可以为空');
        }

        if (!$month_end) {
            throw new CustomException('结束月份不可以为空');
        }

        $time_diff = strtotime($month_end) - strtotime($month_begin);
        if ($time_diff < 0) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }

    public function genProductCustomerExcel()
    {
        $list_bill = request()->post('list_bills');

        return Excel::download(new ProductCustomerBillExport($list_bill), 'file.xlsx');
    }

    /**
     * 产品客户对账单
     * @return array
     * @throws CustomException
     */
    public function getProductCustomerList(): array
    {
        // 校验参数
        $this->validateParamsForProductCustomer();

        // 获取列表
        return $this->getProductCustomerListDo();
    }

    /**
     * 产品客户对账单列表
     * @return array
     */
    private function getProductCustomerListDo(): array
    {
        // 聚合的条件
        $pipeline = $this->aggregateProductCustomerBillList();

        // 客户列表
        $list_customers = Customer::getListByCondition([], ['customer_id', 'name', 'company'])->toArray();
        $list_customers = array_column($list_customers, null, 'customer_id');
        $total = [
            'company' => '总计',
            'money_sum' => 0,
            'section_number_sum' => 0
        ];

        $lists = MongoBillMonth::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->map(function ($item) use ($list_customers, &$total) {
            $item->customer_id = $item->_id;
            $item->name = array_key_exists($item->_id, $list_customers) ? $list_customers[$item->_id]['name'] : '没有对应的客户';
            $item->company = array_key_exists($item->_id, $list_customers) ? $list_customers[$item->_id]['company'] : '没有对应的客户';

            // 计算总值
            $total['money_sum'] += $item->money_sum;
            $total['section_number_sum'] += $item->section_number_sum;

            return $item;
        })->all();

        array_unshift($lists, $total);
        return $lists;
    }

    /**
     * 聚合的条件
     * @return array
     */
    private function aggregateProductCustomerBillList()
    {
        // 条件 && 分组
        list($match, $group) = [
            $this->genConditionForAggregate(),
            $this->genParamsForAggregateGroup()
        ];

        return [
            [
                '$match' => $match
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 分组参数
     * @return array
     */
    private function genParamsForAggregateGroup(): array
    {
        return [
            '_id' => '$customer_id',
            'money_sum' => [
                '$sum' => '$money'
            ],
            'section_number_sum' => [
                '$sum' => '$section_invoked_number'
            ]
        ];
    }

    /**
     * 条件限制
     * @return array
     */
    private function genConditionForAggregate(): array
    {
        list($product_id, $customer_id, $month_begin, $month_end) = [
            (int)request()->get('product_id'),
            trim(request()->get('customer_id')),
            date('Ym', strtotime(request()->get('month_begin'))),
            date('Ym', strtotime(request()->get('month_end'))),
        ];

        $month = [
            '$gte' => $month_begin,
            '$lte' => $month_end,
        ];

        // 如果是210的话 需要转换成下辖的所有子产品
        if ($product_id == 210) {
            $product_id = [
                '$in' => $this->getSubCuishouShortProductIds()
            ];
        }

        if ($customer_id) {
            return compact('product_id', 'month', 'customer_id');
        }

        return compact('product_id', 'month');
    }

    /**
     * 获取催收快捷版的子集的产品ID
     * @return array
     */
    private function getSubCuishouShortProductIds(): array
    {
        $father_id = 210;
        return Product::getListByCondition(compact('father_id'))->pluck('product_id')->toArray();
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForProductCustomer()
    {
        // 产品id && 客户id 校验
        list($product_id, $customer_id) = [
            request()->get('product_id'),
            request()->get('customer_id'),
        ];
        if (!$product_id) {
            throw new CustomException('product_id不可以为空');
        }

        if ($customer_id && !is_string($customer_id)) {
            throw new CustomException('customer_id必须是一个合法的字符串');
        }

        // 时间校验
        $month_begin = trim(request()->get('month_begin'));
        $month_end = trim(request()->get('month_end'));

        if (!$month_begin) {
            throw new CustomException('开始月份不可以为空');
        }

        if (!$month_end) {
            throw new CustomException('结束月份不可以为空');
        }

        $time_diff = strtotime($month_end) - strtotime($month_begin);
        if ($time_diff < 0) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }


    /**
     * 产品对账单
     * @return array
     * @throws CustomException
     */
    public function getBillProductList(): array
    {
        $where = [];
        if ($product_id = request()->get('product_id')) {
            $where['product_id'] = (int)$product_id;
        }

        return $this->getSectionOfCustomer($where);
    }

    /**
     * 产品对账单到处excel
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function productBillDownload()
    {
        $list_bill = request()->post('list_bills');

        return Excel::download(new ProductBillExport($list_bill), 'customer_product.xlsx');
    }

    /**
     * 客户产品对账单excel
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function CPExcel()
    {
        $list_bill = request()->post('list_bills');

        return Excel::download(new CustomerProductBillExport($list_bill), 'customer_product.xlsx');
    }

    /**
     * 获取一个客户下的产品账单信息
     * @param string $customer_id
     * @return array
     * @throws CustomException
     */
    public function getProductBillOfCustomer(string $customer_id): array
    {
        // 校验参数
        $this->validateParamsForBill($customer_id);

        // 条件除了时间的条件
        $where = compact('customer_id');

        // 获取客户产品列表
        return $this->getSectionOfCustomer($where);
    }

    /**
     * 校验参数
     * @param string $customer_id
     * @throws CustomException
     */
    private function validateParamsForBill(string $customer_id)
    {
        if (!Customer::getOneItemByCondition(compact('customer_id'))) {
            throw new CustomException('customer_id未找到对应的客户');
        }

        $month_begin = trim(request()->get('month_begin'));
        $month_end = trim(request()->get('month_end'));

        if (!$month_begin) {
            throw new CustomException('开始月份不可以为空');
        }

        if (!$month_end) {
            throw new CustomException('结束月份不可以为空');
        }

        $time_diff = strtotime($month_end) - strtotime($month_begin);
        if ($time_diff < 0) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }

    /**
     * 获取客户产品列表
     * @param array $where
     * @return array
     */
    private function getSectionOfCustomer(array $where): array
    {
        // 条件
        list($month_begin, $month_end, $list_products, $list_accounts) = [
            date('Ym', strtotime(request()->get('month_begin'))),
            date('Ym', strtotime(request()->get('month_end'))),
            Product::getListByCondition([], ['product_id', 'product_name'])->toArray(),
            Account::getListByCondition([], ['account_id', 'account_name'])->toArray(),
        ];
        $list_accounts = array_column($list_accounts, null, 'account_id');
        $list_products = array_column($list_products, null, 'product_id');


        $total = [
            'product_name' => ' ',
            'account_name' => '总计',
            'section_number' => 0,
            'money' => 0,
        ];

        $list_bills = MongoBillMonth::where($where)
            ->whereBetween('month', [$month_begin, $month_end])
            ->get()->reduce(function ($carry, $item) use ($list_accounts, $list_products, &$total) {
                $item->section_source = (object)$item->section_source;

                // 总计更新
                $total['section_number'] += $item->section_invoked_number ?? 0;
                $total['money'] += $item->money;
                // 整合成产品ID为维度的产品
                return $this->aggregateToProduct($carry, $list_products, $list_accounts, $item);
            }, []);

        array_unshift($list_bills, $total);
        return $list_bills;
    }

    /**
     * @param array $carry
     * @param array $list_products
     * @param array $list_accounts
     * @param  MongoBillMonth $item
     * @return array
     */
    private function aggregateToProduct(array $carry, array $list_products, array $list_accounts, $item): array
    {
        //  获取对应的产品ID, 主要针对的是催收分析快捷版,它们需要整合到一起
        $product_id = $this->getAggrateProductId($item->product_id);

        // 如果这个产品之前是第一次轮询
        if (!isset($carry[$product_id])) {
            return $this->tidyWhenFirstTime($carry, $list_products, $list_accounts, $item);
        }

        // 轮询到其他次数
        return $this->tidyWhenNotFirstTime($carry, $list_accounts, $item);
    }

    /**
     * 获取对应的产品ID, 主要针对的是催收分析快捷版,它们需要整合到一起
     * @param int $product_id
     * @return int
     */
    private function getAggrateProductId(int $product_id): int
    {
        // 如果不是催收分析快捷版本的，则返回本身
        if (!$this->list_cuishou_short_product_ids) {
            $product_key = $this->product_key_cuishou;
            $this->list_cuishou_short_product_ids = Product::getListByCondition(compact('product_key'), ['product_id'])->pluck('product_id')->all();
        }

        // 如果是催收分析快捷版 统一返回210
        if (in_array($product_id, $this->list_cuishou_short_product_ids)) {
            return 210;
        }
        return $product_id;
    }


    /**
     * @param array $carry
     * @param array $list_products
     * @param array $list_accounts
     * @param $item
     * @return array
     */
    private function tidyWhenFirstTime(array $carry, array $list_products, array $list_accounts, $item): array
    {
        list($product_id, $account_id, $uuid) = [
            $this->getAggrateProductId($item->product_id),
            $item->account_id,
            $item->uuid,
        ];
        $carry[$product_id] = [
            'list_uuids' => [$uuid],
            'list_account_ids' => [$account_id],
            'product_name' => !!$list_products[$product_id] ? $list_products[$product_id]['product_name'] : $product_id . ' 缺少对应的产品',
            'account_name' => !!$list_accounts[$account_id] ? $list_accounts[$account_id]['account_name'] : $account_id . ' 缺少对应的账号',
            'money' => $item->money,
            'section_number' => $item->section_invoked_number ?? 0,
            'product_id' => $product_id,
        ];
        return $carry;
    }


    /**
     * // 轮询到其他次数
     * @param array $carry
     * @param array $list_accounts
     * @param $item
     * @return array
     */
    private function tidyWhenNotFirstTime(array $carry, array $list_accounts, $item): array
    {
        list($product_id, $account_id, $uuid) = [
            $this->getAggrateProductId($item->product_id),
            $item->account_id,
            $item->uuid,
        ];

        // 如果账号ID没有存在 则继续
        if (!in_array($account_id, $carry[$product_id]['list_account_ids'])) {
            $carry[$product_id]['list_account_ids'][] = $account_id;
            $account = $list_accounts[$account_id] ?? [];
            $account_name = $account ? $account['account_name'] : $account_id . ' 缺少对应的账号';
            $carry[$product_id]['account_name'] .= ' ' . $account_name;
        }

        $carry[$product_id]['list_uuids'][] = $uuid;
        $carry[$product_id]['money'] += $item->money;
        $carry[$product_id]['section_number'] += $item->section_invoked_number ?? 0;
        return $carry;
    }


    /**
     * 账单列表下载excel
     */
    public function download()
    {
        $list_bill = request()->post('bill_history');

        return Excel::download(new BillHistoryExport($list_bill), 'history.xlsx');
    }

    /**
     *
     * @return array
     */
    public function bills(): array
    {
        // 生成条件
        $where = $this->genParamsForBill();

        // 获取列表
        $list = MongoBillMonth::getListByCondition($where);
        return $list ? $list->all() : [];
    }

    /**
     * 生成参数
     * @return array
     */
    private function genParamsForBill(): array
    {
        $uuid = trim(request()->get('uuid'));
        $account_id = trim(request()->get('account_id'));
        $product_id = (int)(request()->get('product_id'));
        $customer_id = trim(request()->get('customer_id'));
        $month_begin = trim(request()->get('month_begin'));
        $month_end = trim(request()->get('month_end'));

        $where = compact('uuid', 'account_id', 'product_id', 'customer_id');
        $where = array_filter($where, function ($item) {
            return $item;
        });

        if ($month_begin && $month_end) {
            $where['month'] = [
                '$lte' => $month_end,
                '$gte' => $month_begin
            ];
        } elseif ($month_begin && !$month_end) {
            $where['month'] = [
                '$gte' => $month_begin
            ];
        } elseif ($month_end && !$month_begin) {
            $where['month'] = [
                '$lte' => $month_end
            ];
        }

        return $where;
    }

    /**
     * 对账单进行排序
     * @throws CustomException
     */
    public function sorts(): array
    {
        // 校验参数
        $this->validateParamsForSort();

        // 排序
        return $this->sortDo();
    }

    /**
     * 排序
     * @return array
     */
    private function sortDo(): array
    {
        list($list_source, $sort_by, $sort_field) = [
            request()->post('list_source'),
            request()->post('field_sort_base')['sort_by'],
            request()->post('field_sort_base')['field'],
        ];

        // 总计部分不参与排序
        $total = array_shift($list_source);
        $sort_flag = in_array($sort_by, ['asc', 'ASC']) ? SORT_ASC : SORT_DESC;
        $item_sub_fields = array_column($list_source, $sort_field);
        array_multisort($item_sub_fields, $sort_flag, SORT_NUMERIC, $list_source);

        array_unshift($list_source, $total);
        return $list_source;
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForSort()
    {
        $list_source = request()->post('list_source');
        if (!$list_source || !is_array($list_source)) {
            throw new CustomException('请传递要排序的合法数组list_source');
        }

        $list_base_sort = request()->post('field_sort_base');
        if (!$list_base_sort) {
            throw new CustomException('请传递要排序的依据field_sort_base');
        }

        if (!array_key_exists('field', $list_base_sort) || !$list_base_sort['field']) {
            throw new CustomException('请传递合法的field_sort_base.field');
        }

        if (!array_key_exists('sort_by', $list_base_sort) || !in_array($list_base_sort['sort_by'], ['desc', 'asc', 'DESC', 'ASC'])) {
            throw new CustomException('请传递合法的field_sort_base.sort_by');
        }
    }

    /**
     * 客户账单列表
     * @throws CustomException
     * @return array
     */
    public function customers(): array
    {
        // 校验参数
        $this->validateParamsForCustomers();

        // 选中的客户
        $list_customers = $this->getListCustomer();

        // 追加选中时间内的消费金额 && 到目前为止剩下的金额
        $list_customers = $this->appendBillToCustomer($list_customers);

        // 查看是否需要进一度的过滤
        $list_customers = $this->filterInAnotherStep($list_customers);

        // 追加总计
        return $this->appendTotalToCustomer($list_customers);
    }

    /**
     * 追加总计
     * @param $list_customers
     * @return array
     */
    private function appendTotalToCustomer($list_customers): array
    {
        $name = $customer_id = ' ';
        $company = '总计';
        $money_consumption_in_dates = $list_customers->sum('money_consumption_in_dates');
        $money_residue_now = $list_customers->sum('money_residue_now');

        $list_customers = $list_customers->all();
        array_unshift($list_customers, compact('name', 'customer_id', 'company', 'money_residue_now', 'money_consumption_in_dates'));
        return $list_customers;
    }

    /**
     * 进一步进行过滤
     * @param $list_customers
     * @return mixed
     */
    private function filterInAnotherStep($list_customers)
    {
        // 是否需要进一步的过滤
        if (!$this->determineNeedAnotherFilter()) {
            return $list_customers;
        }

        $residue_left = trim(request()->get('residue_left'));
        $residue_right = trim(request()->get('residue_right'));
        return $list_customers->filter(function ($item) use ($residue_right, $residue_left) {
            return $item->money_residue_now >= $residue_left && $item->money_residue_now <= $residue_right;
        });
    }

    /**
     * 是否需要进一步的过滤
     * @return bool
     */
    private function determineNeedAnotherFilter(): bool
    {
        $residue_left = trim(request()->get('residue_left'));
        $residue_right = trim(request()->get('residue_right'));
        return $residue_left && $residue_right;
    }


    /**
     * 追加选中时间内的消费金额 && 到目前为止剩下的金额
     * @param $list_customers
     * @return mixed
     */
    private function appendBillToCustomer($list_customers)
    {
        //  计算选中客户的余额
        $list_customers = $this->getResidueOfCustomer($list_customers);

        // 计算选中时间范围的消费金额
        return $this->getConsumptionBetWeenDates($list_customers);
    }

    /**
     * 时间范围内的消费金额
     * @param $list_customers
     * @return mixed
     */
    private function getConsumptionBetWeenDates($list_customers)
    {
        // 消费条件
        $pipeline = $this->getAggregationBetweenDates($list_customers);

        // 聚合
        $list_consumption_in_dates = MongoBillMonth::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->pluck('money_residue', '_id')->all();

        return $list_customers->map(function ($item) use ($list_consumption_in_dates) {
            $customer_id = $item->customer_id;
            $item->money_consumption_in_dates = $list_consumption_in_dates[$customer_id] ?? 0;
            return $item;
        });
    }

    /**
     * 一段时间范围内的消费聚合条件
     * @param $list_customers
     * @return array
     */
    private function getAggregationBetweenDates($list_customers): array
    {
        // 条件
        $list_customer_ids = $list_customers->pluck('customer_id')->all();
        $customer_id = [
            '$in' => $list_customer_ids
        ];

        // 时间限制
        $month_begin = trim(request()->get('month_begin'));
        $month_end = trim(request()->get('month_end'));
        $month = [
            '$lte' => date('Ym', strtotime($month_end)),
            '$gte' => date('Ym', strtotime($month_begin))
        ];

        // 分组
        $group = [
            '_id' => '$customer_id',
            'money_residue' => [
                '$sum' => '$money'
            ]
        ];

        return [
            [
                '$match' => compact('customer_id', 'month')
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 计算选中客户的余额
     * @param $list_customers
     * @return mixed
     */
    private function getResidueOfCustomer($list_customers)
    {
        // 各个客户累计消费
        $list_history_consumptions = $this->getHistoryConsumptionForCustomer($list_customers);

        //  各个客户累计充值
        $list_history_recharge = $this->getHistoryRechargeForCustomer($list_customers);

        // 计算各个客户的余额
        return $list_customers->map(function ($item) use ($list_history_consumptions, $list_history_recharge) {
            $customer_id = $item->customer_id;
            $item->money_history_consumption = $money_history_consumption = $list_history_consumptions[$customer_id] ?? 0;
            $item->money_history_recharge = $money_history_recharge = $list_history_recharge[$customer_id] ?? 0;
            $item->money_residue_now = $money_history_recharge - $money_history_consumption;
            return $item;
        });
    }

    /**
     * 各个客户累计充值
     * @param $list_customers
     * @return array
     */
    private function getHistoryRechargeForCustomer($list_customers): array
    {
        // 条件
        list($list_customer_ids, $where) = $this->getConditionForHistoryRechargeOfCustomers($list_customers);

        return MoneyRecharge::when($list_customer_ids, function ($query, $list_customer_ids) {
            return $query->whereIn('customer_id', $list_customer_ids);
        })
            ->where($where)
            ->get()
            ->reduce(function ($carry, $item) {
                if (!isset($carry[$item->customer_id])) {
                    $carry[$item->customer_id] = 0;
                }

                $carry[$item->customer_id] += $item->money;
                return $carry;
            }, []);
    }

    /**
     * 条件
     * @param $list_customers
     * @return array
     */
    private function getConditionForHistoryRechargeOfCustomers($list_customers): array
    {
        $list_customer_ids = $list_customers->pluck('customer_id')->all();
        $status = 3;
        return [$list_customer_ids, compact('status')];
    }

    /**
     * 各个客户累计消费
     * @param $list_customers
     * @return array
     */
    private function getHistoryConsumptionForCustomer($list_customers): array
    {
        // 聚合条件
        $pipeline = $this->getAggregationForHistory($list_customers);

        // 聚合
        return MongoBillMonth::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->pluck('money_residue', '_id')->all();
    }

    /**
     * 获取聚合条件
     * @param $list_customers
     * @return array
     */
    private function getAggregationForHistory($list_customers): array
    {
        // 条件
        $list_customer_ids = $list_customers->pluck('customer_id')->all();
        $customer_id = [
            '$in' => $list_customer_ids
        ];

        // 分组
        $group = [
            '_id' => '$customer_id',
            'money_residue' => [
                '$sum' => '$money'
            ]
        ];

        return [
            [
                '$match' => compact('customer_id')
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 获取选中的客户列表
     */
    private function getListCustomer()
    {
        // 条件
        $where = $this->getConditionForCustomer();

        return Customer::getListByCondition($where);
    }

    /**
     * 条件
     * @return array
     */
    private function getConditionForCustomer(): array
    {
        $customer_id = trim(request()->get('customer_id'));
        return $customer_id ? compact('customer_id') : [];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForCustomers()
    {
        // 校验月份
        $this->validateTimeParamsForCustomers();

        // 校验余额范围
        $this->validateResidueParamsForCustomers();
    }

    /**
     * 校验余额范围
     * @throws CustomException
     */
    private function validateResidueParamsForCustomers()
    {
        $residue_left = trim(request()->get('residue_left'));
        $residue_right = trim(request()->get('residue_right'));
        if (!$residue_left && $residue_right) {
            throw new CustomException('请同时输入月额的左右边界');
        }

        if ($residue_left && !$residue_right) {
            throw new CustomException('请同时输入月额的左右边界');
        }

        if ($residue_left && $residue_right) {
            $diff = $residue_right - $residue_left;
            if ($diff < 0) {
                throw new CustomException('余额右边界不可以小于左边界');
            }
        }
    }

    /**
     * 校验月份
     * @throws CustomException
     */
    private function validateTimeParamsForCustomers()
    {
        $month_begin = trim(request()->get('month_begin'));
        $month_end = trim(request()->get('month_end'));

        if (!$month_begin) {
            throw new CustomException('开始月份不可以为空');
        }

        if (!$month_end) {
            throw new CustomException('结束月份不可以为空');
        }

        $time_diff = strtotime($month_end) - strtotime($month_begin);
        if ($time_diff < 0) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }

    /**
     * @param MongoBillMonth $section_bill_item
     */
    public function setSectionBillItem($section_bill_item)
    {
        $section_bill_item->section_source = (object)$section_bill_item->section_source;
        $section_bill_item->start_day = date('Ymd', strtotime($section_bill_item->section_source->start_date));
        $this->section_bill_item = $section_bill_item;
    }

    /**
     * 邮件发送对账单
     * @throws CustomException
     */
    public function email()
    {
        // 校验参数
        $this->validateParamsForEmail();

        // 生成excel
        $this->genExcelForEmail();

        // 发送邮件
        $this->sendEmailThenDel();
    }

    /**
     * 发送邮件
     */
    private function sendEmailThenDel()
    {
        // 邮件主题和内容
        list($theme, $content) = [
            trim(request()->post('theme')),
            trim(request()->post('content')),
        ];
        $path_file = storage_path() . '/app/' . $this->getFileName();

        // 格式化邮箱
        list($email_received, $email_copy) = $this->formatEmail();

        Mail::to($email_received)
            ->cc($email_copy)
            ->send(new BillMonth($theme, $content, $path_file));

        if (file_exists($path_file)) {
            @unlink($path_file);
        }
    }

    /**
     * 格式化邮箱
     * @return array
     */
    private function formatEmail(): array
    {
        list($email_received, $email_copy) = [
            trim(request()->post('email_received'), ';'),
            trim(request()->post('email_copy'), ';'),
        ];

        // 邮件接收人
        $email_received = explode(';', $email_received);
        $email_received = array_map(function ($item) {
            return trim($item);
        }, $email_received);

        // 邮件抄送人
        $email_copy = explode(';', $email_copy);
        $email_copy = array_map(function ($item) {
            return trim($item);
        }, $email_copy);

        return [$email_received, $email_copy];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForEmail()
    {
        // 进行基础校验
        $this->validateBaseParamsForEmail();

        // 校验邮箱
        $this->validateEmail();
    }

    /**
     * 校验邮箱
     * @throws CustomException
     */
    private function validateEmail()
    {
        list($email_received, $email_copy) = [
            request()->post('email_received'),
            request()->post('email_copy'),
        ];

        // 校验收件人邮件
        $this->validateOneEmail($email_received, '接收人邮箱');

        // 抄送人邮箱
        $this->validateOneEmail($email_copy, '抄送人邮箱');
    }

    /**
     * 校验邮箱
     * @param $email_str
     * @param $intro_field
     * @throws CustomException
     */
    private function validateOneEmail($email_str, $intro_field)
    {
        if (!is_string($email_str)) {
            throw new CustomException($intro_field . '参数必须是字符串');
        }

        // 防止在后面添加';'结尾
        $email_str = trim($email_str, ';');
        if (!$email_str) {
            throw new CustomException($intro_field . '不可以为空');
        }


        $list_emails = explode(';', $email_str);
        array_walk($list_emails, function ($email) use ($intro_field) {
            // 过滤下
            $email = trim($email);
            if (filter_var($email, FILTER_VALIDATE_EMAIL) == false) {
                throw new CustomException($intro_field . ' : ' . $email . '不是合法的邮箱');
            }
        });

    }

    /**
     * 进行基础校验
     * @throws CustomException
     */
    private function validateBaseParamsForEmail()
    {
        list($customer_info, $theme, $content, $history, $list_product_alo_consumptions) = [
            request()->post('customer_info'),
            request()->post('theme'),
            request()->post('content'),
            request()->post('history'),
            request()->post('list_product_alo_consumptions')
        ];

        // 校验客户信息
        if (!$customer_info) {
            throw new CustomException('customer_info参数不可以为空');
        }

        // 校验主题
        if (!$theme) {
            throw new CustomException('theme参数不可以为空');
        }

        // 检查邮件内容
        if (!$content) {
            throw new CustomException('content参数不可以为空');
        }

        // 校验结算单数据
        if (!$history) {
            throw new CustomException('history参数不可以为空');
        }

        // 校验消费明细数据
        if (!$list_product_alo_consumptions) {
            throw new CustomException('list_product_alo_consumptions参数不可以为空');
        }
    }

    /**
     * 生成excel
     */
    private function genExcelForEmail()
    {
        $file_name = $this->getFileName();
        list($history, $customer_info) = [
            request()->post('history'),
            request()->post('customer_info'),
        ];

        // 各个产品的算法账单
        $list_product_bill_alo = $this->formatProductBill();

        // 生成excel
        Excel::store(new EmailBill($customer_info, $history, $list_product_bill_alo), $file_name);
    }

    /**
     * 格式化产品的数据
     * @return array
     */
    private function formatProductBill(): array
    {
        // 容器
        $list_container = [];
        $list_product_alo_consumptions = request()->post('list_product_alo_consumptions');

        collect($list_product_alo_consumptions)->each(function ($item, $product_alo_key) use (&$list_container) {
            $product_id = strstr($product_alo_key, '_', true);
            $export_consumption_details = $item['export_consumption_details'] ?? '';
            if ($export_consumption_details === false) {
                $this->list_disabled_product_ids[] = $product_id;
                return;
            }

            if (!in_array($product_id, $this->list_disabled_product_ids)) {
                $list_container[$product_id][$product_alo_key] = $item;
            }
        });
        return $list_container;
    }

    /**
     * @return string
     */
    private function getFileName()
    {
        $customer_info = request()->post('customer_info');
        return $customer_info['name'] . '-结算单.xlsx';
    }

    /**
     * 文件名
     * @return string
     */
    private function genExcelGbkFileName(): string
    {
        $file_name = $this->getFileName();
        return iconv('UTF-8', 'GBK', $file_name);
    }

    /**
     * 获取excel所需要的数据
     * @param string $customer_id
     * @return array
     * @throws CustomException
     */
    public function excel($customer_id): array
    {
        // 校验然后设置customer_id
        $this->validateAndSetCustomerId($customer_id);

        // 设置是否需要生成催收快捷版的明细
        $this->_setDetermineCreateShortcut();

        // 按照账单算法聚合账单片段
        $this->aggregateSectionGroupBillAloAndProductId();

        // 获取账单算法对应的消费明细
        return $this->_genConsumptionDetails();
    }

    /**
     * 设置是否需要生成催收快捷版的明细
     */
    private function _setDetermineCreateShortcut()
    {
        // 重置环境(类属性被重复修改)
        $this->_resetEnv();

        // 获取关于快捷版所有账单
        $list_shortcut_items = $this->_getShortcutBill();

        // 判断这些账单的算法是否一致
        $this->determineShortcutOnlyOneAlo($list_shortcut_items);
    }

    /**
     * 设置生曾快捷半的方式
     * @param array $list_shortcut_items 快捷版产品按照算法和月份进行聚合
     */
    private function determineShortcutOnlyOneAlo(array $list_shortcut_items)
    {
        // 如果没有对应的单元
        if (count($list_shortcut_items) == 0) {
            $this->_list_shortcut_conflict_bill_this_year = [
                '没有快捷版的账单'
            ];
            return;
        }

        array_walk($list_shortcut_items, function ($item_months, $alo) {
            array_walk($item_months, function ($items, $month) use ($alo) {
                // 当计费规则冲突的时候， 设置相关的数据
                if ($this->_determineThisMonthConflict(collect($items), $alo)) {
                    // 如果冲突
                    $this->list_shortcut_useless_alog[] = $alo;
                } else {
                    // 如果没有冲突 则设置需要整合催收快捷版
                    $this->_determine_need_create_shortcut = true;
                }
            });
        });
    }

    /**
     * 当前的算法是否有冲突
     * @param Collection $list_items
     * @param string $alo
     * @return bool
     */
    private function _determineThisMonthConflict(Collection $list_items, string $alo): bool
    {
        // 作为基础的一个
        $base_one = $list_items->first();

        // 是否配置冲突
        return $list_items->contains(function ($bill_section_item) use ($base_one, $alo) {

            // 判断计费规则是否一致
            $bool = $bill_section_item->section_source->fee_basis != $base_one->section_source->fee_basis
                || $bill_section_item->section_source->fee_method != $base_one->section_source->fee_method
                || $bill_section_item->section_source->fee_time_rule != $base_one->section_source->fee_time_rule
                || $bill_section_item->section_source->fee_amount_rule != $base_one->section_source->fee_amount_rule
                || $bill_section_item->section_source->fee_step_rule != $base_one->section_source->fee_step_rule
                || $bill_section_item->section_source->fee_price_rule != $base_one->section_source->fee_price_rule
                || $bill_section_item->section_source->fee_price != $base_one->section_source->fee_price;

            // 设置冲突标识
            $bool && $this->_setShortcutDataWhenAloConflict($alo, $base_one, $bill_section_item);
            return $bool;
        });
    }

    /**
     * 当快捷版冲突的时候, 设置快捷版相关的数据
     * @param string $alo
     * @param $base_one
     * @param $bill_section_item
     */
    private function _setShortcutDataWhenAloConflict(string $alo, $base_one, $bill_section_item)
    {
        $this->_list_shortcut_conflict_bill_this_year[$alo] = [
            'base_one' => $base_one->toArray(),
            'another_one' => $bill_section_item->toArray()
        ];

        $this->list_product_alo_consumptions['210_' . $alo] = [
            'export_consumption_details' => false,
            'msg' => '此产品不同账号计费配置规则不同，暂时无法输出账单',
            'product_info' => Product::findOneItem(['product_id' => 210], ['product_id', 'product_name'])
        ];
    }

    /**
     * 获取快捷版的账单
     * @return mixed
     */
    private function _getShortcutBill(): array
    {
        // 快捷版产品列表
        $product_id = $this->_getShortcutProductIds();

        // 客户 && 时间限制
        $where = $this->genConsumeParamsForThisYear();
        $this->_list_shortcut_bill_this_year = MongoBillMonth::where($where)
            ->whereIn('product_id', $product_id)
            ->get();

        // 设置算法
        $this->_list_shortcut_bill_this_year->each(function ($section_item) {
            // 设置正在操作的账单片段
            $this->setSectionBillItem($section_item);

            // 设置算法场景的账单片段
            $this->setListBillSectionGroupAlg();
        });

        // 按照算法分类
        $list_container = [];
        collect($this->list_bill_section_group_alg)->each(function ($items, $product_alo) use (&$list_container) {
            $alo = strstr($product_alo, '_');
            collect($items)->each(function ($item, $month) use ($alo, &$list_container) {
                $alo_months = $list_container[$alo] ?? [];
                $month_items = $alo_months[$month] ?? [];
                $list_container[$alo][$month] = array_merge($month_items, $item);
            });
        });

        return $list_container;
    }

    /**
     * 获取催收快捷版的所有产品ID
     * @return array
     */
    private function _getShortcutProductIds(): array
    {
        // 静态变量
        static $list_shortcut_product_ids;
        if ($list_shortcut_product_ids) {
            return $list_shortcut_product_ids;
        }

        $product_key = $this->product_key_cuishou;
        return $list_shortcut_product_ids = Product::where('father_id', '<>', 0)
            ->where(compact('product_key'))
            ->select(['product_id'])
            ->get()
            ->pluck('product_id')
            ->all();
    }

    /**
     * 某个客户的历史数据
     * @param string $customer_id
     * @return array
     * @throws CustomException
     */
    public function history($customer_id): array
    {
        // 校验然后设置customer_id
        $this->validateAndSetCustomerId($customer_id);

        // 结算单
        return $this->getTotalStatement();
    }

    /**
     * 这种算法是否是无效的
     * @param $product_alo_key
     * @return bool
     */
    private function _determineThisAloUselessForShortcut($product_alo_key): bool
    {
        $alo = strstr($product_alo_key, '_');
        return in_array($alo, $this->list_shortcut_useless_alog);
    }

    /**
     * 生成产品的算法消费明细
     */
    private function _genProductALogConsumption()
    {
        collect($this->list_bill_section_group_alg)->each(function ($list_month_group_sections, $product_alo_key) {

            // 如果不需要生成催收快捷版
            if ($this->determineItIsCuishouShort($product_alo_key) && $this->_determineThisAloUselessForShortcut($product_alo_key)) {
                return;
            }

            // 如果这个产品的这个算法不可以生成对账单
            if ($this->determineCanExportConsumptionForAloProduct($list_month_group_sections)) {
                $this->list_product_alo_consumptions[$product_alo_key] = [
                    'export_consumption_details' => false,
                    'msg' => '此产品不同账号计费配置规则不同，暂时无法输出账单',
                    'product_info' => $this->getProductFromAlo($product_alo_key)
                ];
                return;
            }

            // 生成各种算法的账单明细
            $this->writeProductAloConsumptionDetail($list_month_group_sections, $product_alo_key);
        });
    }

    /**
     * 生成对应的消费明细
     */
    private function _genConsumptionDetails(): array
    {
        // 生成产品的算法消费明细
        $this->_genProductALogConsumption();

        // 整合快捷版的属性
        $this->_determine_need_create_shortcut && $this->_appendShortcutToGroupConsumption();

        list($list_product_alo_consumptions, $list_bill_section_group_alg, $_determine_need_create_shortcut,
            $_list_shortcut_conflict_bill_this_year, $_list_shortcut_alo_computed, $_list_shortcut_bill_this_year) = [
            $this->list_product_alo_consumptions, // 原始的账单数据
            $this->list_bill_section_group_alg, // 区分产品算法之后得到的待渲染数据
            $this->_determine_need_create_shortcut, // 是否需要生成快捷版数据
            $this->_list_shortcut_conflict_bill_this_year, // 快捷版本年冲突的数据
            $this->_list_shortcut_alo_computed, // 快捷版聚合成的数据
            $this->_list_shortcut_bill_this_year ? $this->_list_shortcut_bill_this_year->pluck('uuid') : [],  // 今年快捷版生成账单列表
        ];

        return compact('list_product_alo_consumptions', 'list_bill_section_group_alg',
            '_determine_need_create_shortcut', '_list_shortcut_conflict_bill_this_year',
            '_list_shortcut_alo_computed', '_list_shortcut_bill_this_year');
    }

    /**
     * 将催收快捷版的数据附加消费明细中
     */
    private function _appendShortcutToGroupConsumption()
    {
        // 计算快捷版的消费明细
        $this->_computedShortcutForExcel();

        // 如果可以的话 那是要给快捷版加上210有效调用量的
        $this->_determine_need_create_shortcut && $this->_genShortcutNumberWhenNecessary();

        // 附加
        $this->_appendShortcutToGroupConsumptionDo();
    }

    /**
     * 如果可以的话 那是要给快捷版加上210有效调用量的
     */
    private function _genShortcutNumberWhenNecessary()
    {
        // 如果需要生成有效调用量 则生成
        $this->_determineNeedCreateShortcutNumber() && $this->_appendShortcutNumber();
    }

    /**
     * 生成调用量
     */
    private function _appendShortcutNumber()
    {
        $this->_list_shortcut_alo_computed = array_map(function ($shortcut_alog) {

            return array_map(function ($shortcut_month) {
                // 生成调用量
                $shortcut_month['shortcut_number'] = $this->_getShortcutNumber($shortcut_month);
                return $shortcut_month;
            }, $shortcut_alog);
        }, $this->_list_shortcut_alo_computed);
    }

    /**
     *
     * @param array $shortcut_month
     * @return int
     * @throws \Exception
     */
    private function _getShortcutNumber(array $shortcut_month): int
    {
        // 生成参数
        $params = $this->_genParamsForShortcutNumber($shortcut_month);

        // 获取调用量
        return $this->_getShortcutNumberDo($params);
    }

    /**
     * 获取调用量
     * @param array $params
     * @return int
     * @throws \Exception
     */
    private function _getShortcutNumberDo(array $params): int
    {
        $url = env('LIST_STAT_COMMON_API');
        for ($i = 0; $i < 3; $i++) {
            $response = $this->post($url, $params);
            if ($response['status'] == 0) {
                break;
            }
        }

        // 校验返回接口
        $this->validateApiResponse($response, $params, $url);

        // 调用量
        $list_stat = $response['list_stat'] ?? [];
        $cuishouShort210 = $list_stat['cuishouShort210'] ?? [];
        $total = $cuishouShort210['total'] ?? [];
        list($yd_succ, $dx_succ, $lt_succ, $lt_cache, $yd_cache, $dx_cache) = [
            $total['yidong_success'] ?? 0,
            $total['dianxin_success'] ?? 0,
            $total['liantong_success'] ?? 0,
            $total['liantong_cache'] ?? 0,
            $total['yidong_cache'] ?? 0,
            $total['dianxin_cache'] ?? 0
        ];
        return $yd_succ + $dx_succ + $lt_succ + $lt_cache + $yd_cache + $dx_cache;
    }

    /**
     * @param array $response
     * @param array $params
     * @param string $url
     * @throws \Exception
     */
    private function validateApiResponse(array $response, array $params, string $url)
    {
        // 记录日志
        MongoLog::create([
            'msg' => '账单邮件发送在生成快捷版210有效调用量的结果',
            'success' => $response['status'] == 0 ? true : false,
            'url' => $url,
            'response' => $response,
            'params' => $params
        ]);

        // 抛出异常
        if ($response['status'] != 0) {
            $msg = ' 账单邮件发送在生成快捷版210有效调用量的时候失败，详情请查看日志  msg:' . json_encode(compact('url', 'params', 'response'), JSON_UNESCAPED_UNICODE);
            $this->wechatException($msg);
            throw new CustomException($msg);
        }
    }

    /**
     * 生成参数
     * @param array $shortcut_month
     * @return array
     */
    private function _genParamsForShortcutNumber(array $shortcut_month): array
    {
        list($start_time, $end_time, $product_id, $key) = [
            $shortcut_month['section_begin'],
            $shortcut_month['section_end'],
            210,
            $this->product_key_cuishou
        ];

        // 可能的apikey
        $apikey = $this->getApikeyOfShortcutForNumber();
        return compact('apikey', 'start_time', 'end_time', 'product_id', 'key');
    }


    /**
     * 获取当前账单对应账号apikey
     * @return array
     */
    private function getApikeyOfShortcutForNumber(): array
    {
        $list_account_ids = $this->_list_shortcut_bill_this_year->pluck('account_id')->unique()->all();

        return Account::whereIn('account_id', $list_account_ids)
            ->select(['apikey'])
            ->get()
            ->pluck('apikey')
            ->all();
    }

    /**
     * 是否需要生成有效调用量
     * @return bool
     */
    private function _determineNeedCreateShortcutNumber(): bool
    {
        if (!$this->_list_shortcut_alo_computed) {
            return false;
        }

        // 如果没有命中算法
        $list_alos = array_keys($this->_list_shortcut_alo_computed);
        return (bool)array_intersect($list_alos, ['210_1', '210_2_1']);
    }

    /**
     * 附加
     */
    private function _appendShortcutToGroupConsumptionDo()
    {
        // 过滤掉快捷版的产品
        $this->list_product_alo_consumptions = collect($this->list_product_alo_consumptions)->filter(function ($product_sections, $product_alo_key) {
            return !$this->determineItIsCuishouShort($product_alo_key);
        })->merge($this->_list_shortcut_alo_computed)->all();
    }

    /**
     * 计算快捷版的的明细
     */
    private function _computedShortcutForExcel()
    {
        collect($this->list_product_alo_consumptions)->each(function ($product_consumption, $product_alo_key) {
            // 如果不是快捷版
            if (!$this->determineItIsCuishouShort($product_alo_key)) {
                return true;
            }

            // 当前算法是否可以生成
            if ($this->_determineThisAloUselessForShortcut($product_alo_key)) {
                return true;
            }


            // 如果计费规则冲突 则结束循环
            $export_consumption_details = $product_consumption['export_consumption_details'] ?? true;
            if (!$export_consumption_details) {
                $this->_list_shortcut_alo_computed = [
                    'export_consumption_details' => false,
                    'msg' => '此产品不同账号计费配置规则不同，暂时无法输出账单',
                    'product_alo_key' => $product_alo_key,
                    'product_info' => Product::findOneItem(['product_id' => 210], ['product_id', 'product_name'])->toArray()
                ];
                return true;
            }

            // 如果是快捷版 则按照算法进行整合
            $this->_computedShortcutWhenDiffAlo($product_consumption, $product_alo_key);
        });
    }

    /**
     * 不同算法之间，催收快捷版的整合
     * @param array $product_consumption
     * @param string $product_alo_key
     */
    private function _computedShortcutWhenDiffAlo(array $product_consumption, string $product_alo_key)
    {
        // 算法
        $alo = $this->_getAlog($product_alo_key);

        switch ($alo) {
            case '_1':
                // 通用 && 按时间
                $this->_computedShortcutWhenCommonDateAndNum($product_consumption, '210_1');
                break;
            case '_2_1':
                // 如果通用的按用量 && 固定价格
                $this->_computedShortcutWhenCommonDateAndNum($product_consumption, '210_2_1');
                break;
            case "_2_2":
                // 通用的按用量 && 累进阶梯
                $this->_computedShortcutWhenCommonProgression($product_consumption, '210_2_2');
                break;
            case "_2_3":
                // 通用的按用量 && 到达阶梯
                $this->_computedShortcutWhenCommonReach($product_consumption, '210_2_3');
                break;
            case "_1_2":
                // 区分运营商按时间
                $this->_computedShortcutWhenOperatorDate($product_consumption, '210_1_2');
                break;
            case "_2_1_2":
                //  区分运营商按用量 && 固定价格
                $this->_computedShortcutWhenOperatorFixed($product_consumption, '210_2_1_2');
                break;
            case "_2_2_2":
                //  区分运营商按用量 && 累进阶梯
                $this->_computedShortcutWhenOperatorProgressionAndReach($product_consumption, '210_2_2_2');
                break;
            case '_2_3_2':
                // 区分运营商按用量 && 到达阶梯
                $this->_computedShortcutWhenOperatorProgressionAndReach($product_consumption, '210_2_3_2');
                break;
        }
    }

    /**
     * 区分运营商 按累进
     * @param array $product_consumption
     * @param string $product_alo
     */
    private function _computedShortcutWhenOperatorProgressionAndReach(array $product_consumption, string $product_alo)
    {
        collect($product_consumption)->each(function ($product_consumption_month, $month) use ($product_alo) {
            // 如果之前没有这个月份的数据
            if (!$this->_computedShortcutWhenInit($product_alo, $product_consumption_month, $month)) {
                return;
            }

            // 累加各个区间的金额和调用量
            $this->_list_shortcut_alo_computed[$product_alo][$month]['list_distribute'] = $this->_computedDistributeWhenOperatorProgressionForShortcutAndReach(
                $this->_list_shortcut_alo_computed[$product_alo][$month], $product_consumption_month
            );

            // 设置快捷版的常规属性
            $this->_setCommonAttrForShortcut($month, $product_alo, $product_consumption_month);
        });
    }

    /**
     * 快捷版运营商 固定单价
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     * @return array
     */
    private function _computedDistributeWhenOperatorProgressionForShortcutAndReach(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 格式化数据
        $list_distribute_new = array_column($list_distribute_new, null, 'operator');
        $list_distribute_old = array_column($list_distribute_old, null, 'operator');
        $list_operators = array_unique(array_merge(array_keys($list_distribute_new), array_keys($list_distribute_old)));

        // 累加
        return array_map(function ($operator) use ($list_distribute_old, $list_distribute_new) {
            list($number_old, $number_new, $money_old, $money_new) = [
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['number'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['number'] : 0,
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['money'] : 0,
            ];
            return [
                'number' => $number_old + $number_new,
                'money' => $money_old + $money_new,
                'operator' => $operator,
            ];
        }, $list_operators);
    }

    /**
     * 区分运营商按固定价格
     * @param array $product_consumption
     * @param string $product_alo
     */
    private function _computedShortcutWhenOperatorFixed(array $product_consumption, string $product_alo)
    {
        collect($product_consumption)->each(function ($product_consumption_month, $month) use ($product_alo) {
            // 如果之前没有这个月份的数据
            if (!$this->_computedShortcutWhenInit($product_alo, $product_consumption_month, $month)) {
                return;
            }

            // 累加各个区间的金额和调用量
            $this->_list_shortcut_alo_computed[$product_alo][$month]['list_distribute'] = $this->_computedDistributeWhenOperatorFixedForShortcut(
                $this->_list_shortcut_alo_computed[$product_alo][$month], $product_consumption_month
            );

            // 设置快捷版的常规属性
            $this->_setCommonAttrForShortcut($month, $product_alo, $product_consumption_month);
        });
    }

    /**
     * 快捷版运营商 固定单价
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     * @return array
     */
    private function _computedDistributeWhenOperatorFixedForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 格式化数据
        $list_distribute_new = array_column($list_distribute_new, null, 'operator');
        $list_distribute_old = array_column($list_distribute_old, null, 'operator');
        $list_operators = array_unique(array_merge(array_keys($list_distribute_new), array_keys($list_distribute_old)));

        // 累加
        return array_map(function ($operator) use ($list_distribute_old, $list_distribute_new) {
            list($number_old, $number_new, $money_old, $money_new, $price) = [
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['number'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['number'] : 0,
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['price'] : (isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['price'] : 0),
            ];
            return [
                'number' => $number_old + $number_new,
                'money' => $money_old + $money_new,
                'operator' => $operator,
                'price' => $price
            ];
        }, $list_operators);
    }


    /**
     * 区分运营商按时间
     * @param array $product_consumption
     * @param string $product_alo
     */
    private function _computedShortcutWhenOperatorDate(array $product_consumption, string $product_alo)
    {
        collect($product_consumption)->each(function ($product_consumption_month, $month) use ($product_alo) {
            // 如果之前没有这个月份的数据
            if (!$this->_computedShortcutWhenInit($product_alo, $product_consumption_month, $month)) {
                return;
            }

            // 累加各个区间的金额和调用量
            $this->_list_shortcut_alo_computed[$product_alo][$month]['list_distribute'] = $this->_computedDistributeWhenOperatorDataForShortcut(
                $this->_list_shortcut_alo_computed[$product_alo][$month], $product_consumption_month
            );

            // 设置快捷版的常规属性
            $this->_setCommonAttrForShortcut($month, $product_alo, $product_consumption_month);
        });
    }

    /**
     * 快捷版通用按累进 计算分布
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     * @return array
     */
    private function _computedDistributeWhenOperatorDataForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 格式化数据
        $list_distribute_new = array_column($list_distribute_new, null, 'operator');
        $list_distribute_old = array_column($list_distribute_old, null, 'operator');
        $list_operators = array_unique(array_merge(array_keys($list_distribute_new), array_keys($list_distribute_old)));

        // 累加
        return array_map(function ($operator) use ($list_distribute_old, $list_distribute_new) {
            list($number_old, $number_new, $money_old, $money_new) = [
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['number'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['number'] : 0,
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['money'] : 0,
            ];
            return [
                'number' => $number_old + $number_new,
                'money' => $money_old + $money_new,
                'operator' => $operator
            ];
        }, $list_operators);
    }

    /**
     * 初始化快捷版的属性
     * @param string $product_alo
     * @param array $product_consumption_month
     * @param string $month
     * @return bool
     */
    private function _computedShortcutWhenInit(string $product_alo, array $product_consumption_month, string $month): bool
    {
        // 如果不是初始化
        if (isset($this->_list_shortcut_alo_computed[$product_alo]) && isset($this->_list_shortcut_alo_computed[$product_alo][$month])) {
            return true;
        }

        $this->_list_shortcut_alo_computed[$product_alo][$month] = $product_consumption_month;
        $this->_list_shortcut_alo_computed[$product_alo][$month]['product_info'] = Product::findOneItem(['product_id' => 210], ['product_id', 'product_name'])->toArray();
        return false;
    }

    /**
     * 设置快捷版的常规属性
     * @param string $month
     * @param string $product_alo
     * @param array $product_consumption_month
     */
    private function _setCommonAttrForShortcut(string $month, string $product_alo, array $product_consumption_month)
    {
        // 累加计算属性
        $this->_list_shortcut_alo_computed[$product_alo][$month]['money'] += $product_consumption_month['money'];

        // 使开始--结束时间包含的范围最大化
        if ($product_consumption_month['section_begin'] < $this->_list_shortcut_alo_computed[$product_alo][$month]['section_begin']) {
            $this->_list_shortcut_alo_computed[$product_alo][$month]['section_begin'] = $product_consumption_month['section_begin'];
        }

        if ($product_consumption_month['section_end'] > $this->_list_shortcut_alo_computed[$product_alo][$month]['section_end']) {
            $this->_list_shortcut_alo_computed[$product_alo][$month]['section_end'] = $product_consumption_month['section_end'];
        }
    }

    /**
     * 通用的按用量 && 到达阶梯计算快捷版的消费明细
     * @param array $product_consumption
     * @param string $product_alo
     */
    private function _computedShortcutWhenCommonReach(array $product_consumption, string $product_alo)
    {
        collect($product_consumption)->each(function ($product_consumption_month, $month) use ($product_alo) {
            // 如果之前没有这个月份的数据
            if (!$this->_computedShortcutWhenInit($product_alo, $product_consumption_month, $month)) {
                return;
            }

            // 累加各个区间的金额和调用量
            $this->_list_shortcut_alo_computed[$product_alo][$month]['list_distribute'] = $this->_computedDistributeWhenCommonReachForShortcut(
                $this->_list_shortcut_alo_computed[$product_alo][$month], $product_consumption_month
            );

            // 设置快捷版的常规属性
            $this->_setCommonAttrForShortcut($month, $product_alo, $product_consumption_month);
        });
    }

    /**
     * 快捷版通用按累进 计算分布
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     * @return array
     */
    private function _computedDistributeWhenCommonReachForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 获取key的聚合
        $section_keys_old = array_keys($list_distribute_old);
        $section_keys_new = array_keys($list_distribute_new);
        $section_keys = array_unique(array_merge($section_keys_old, $section_keys_new));


        return array_reduce($section_keys, function ($carry, $section_key) use ($list_distribute_old, $list_distribute_new) {
            list ($money_old, $money_new, $section_number_old, $section_number_new, $price) = [
                isset($list_distribute_old[$section_key]) ? $list_distribute_old[$section_key]['money'] : 0,
                isset($list_distribute_new[$section_key]) ? $list_distribute_new[$section_key]['money'] : 0,
                isset($list_distribute_old[$section_key]) ? $list_distribute_old[$section_key]['section_number'] : 0,
                isset($list_distribute_new[$section_key]) ? $list_distribute_new[$section_key]['section_number'] : 0,
                isset($list_distribute_old[$section_key]['price']) ? $list_distribute_old[$section_key]['price'] : $list_distribute_new[$section_key]['price']
            ];

            $carry[$section_key] = [
                'money' => $money_new + $money_old,
                'section_number' => $section_number_old + $section_number_new,
                'price' => $price,
                'section_key' => $section_key
            ];
            return $carry;
        }, []);
    }

    /**
     * 通用的按用量 && 累进阶梯计算快捷版的消费明细
     * @param array $product_consumption
     * @param string $product_alo
     */
    private function _computedShortcutWhenCommonProgression(array $product_consumption, string $product_alo)
    {
        collect($product_consumption)->each(function ($product_consumption_month, $month) use ($product_alo) {
            // 如果之前没有这个月份的数据
            if (!$this->_computedShortcutWhenInit($product_alo, $product_consumption_month, $month)) {
                return;
            }

            // 累加各个区间的金额和调用量
            $this->_list_shortcut_alo_computed[$product_alo][$month]['list_distribute'] = $this->_computedDistributeWhenCommonProgressionForShortcut(
                $this->_list_shortcut_alo_computed[$product_alo][$month], $product_consumption_month
            );

            // 设置快捷版的常规属性
            $this->_setCommonAttrForShortcut($month, $product_alo, $product_consumption_month);
        });
    }

    /**
     * 快捷版通用按累进 计算分布
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     * @return array
     */
    private function _computedDistributeWhenCommonProgressionForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($distribute_old, $distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? [],
        ];

        // 累计
        $distribute_base = count($distribute_old) > count($distribute_new) ? $distribute_old : $distribute_new;
        $distribute_another = count($distribute_old) > count($distribute_new) ? $distribute_new : $distribute_old;

        return array_map(function ($item) use ($distribute_another) {
            $section_key = $item['section_key'] ?? 'none';
            $item['money'] += isset($distribute_another[$section_key]) ? $distribute_another[$section_key]['money'] : 0;
            $item['section_number'] += isset($distribute_another[$section_key]) ? $distribute_another[$section_key]['section_number'] : 0;
            return $item;
        }, $distribute_base);
    }

    /**
     * 通用按时间 && 固定单价 算法计算快捷版数据
     * @param array $product_consumption
     * @param string $product_alo
     */
    private function _computedShortcutWhenCommonDateAndNum(array $product_consumption, string $product_alo)
    {
        collect($product_consumption)->each(function ($product_consumption_month, $month) use ($product_alo) {
            // 如果之前没有这个月份的数据
            if (!$this->_computedShortcutWhenInit($product_alo, $product_consumption_month, $month)) {
                return;
            }

            // 累加计算属性
            $this->_list_shortcut_alo_computed[$product_alo][$month]['fee_number'] += $product_consumption_month['fee_number'];

            // 设置快捷版的常规属性
            $this->_setCommonAttrForShortcut($month, $product_alo, $product_consumption_month);
        });
    }

    /**
     * 获取算法
     * @param string $product_alo_key
     * @return string
     */
    private function _getAlog(string $product_alo_key): string
    {
        return strstr($product_alo_key, '_');
    }

    /**
     * 是否是催收分析快捷版
     * @param string $product_alo_key
     * @return bool
     */
    private function determineItIsCuishouShort(string $product_alo_key): bool
    {
        $product_id = strstr($product_alo_key, '_', true);
        $list_shortcut_product_ids = $this->_getShortcutProductIds();
        return in_array($product_id, $list_shortcut_product_ids);
    }

    /**
     * @param string $product_alo_key
     * @return array
     */
    private function getProductFromAlo(string $product_alo_key): array
    {
        $product_id = strstr($product_alo_key, '_', true);
        return Product::findOneItem(compact('product_id'), ['product_name', 'product_id'])->toArray();
    }


    /**
     * 生成各种算法的账单明细
     * @param array $list_month_group_sections
     * @param string $product_alo_key 产品ID和算法组合成的唯一健
     */
    private function writeProductAloConsumptionDetail(array $list_month_group_sections, string $product_alo_key)
    {
        collect($list_month_group_sections)->each(function ($list_group_sections, $month) use ($product_alo_key) {
            // 按月生成各种算法的账单明细
            $this->writeProductAloConsumptionDetailByMonth($list_group_sections, $product_alo_key, $month);
        });
    }

    /**
     * 按月生成各种算法的账单明细
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeProductAloConsumptionDetailByMonth(array $list_group_sections, string $product_alo_key, string $month)
    {
        // 算法
        $alg = strstr($product_alo_key, '_');
        switch ($alg) {
            case '_1':
                // 通用 && 按时间
                $this->writeConsumptionDetailByMonthWhenCommonDate($list_group_sections, $product_alo_key, $month);
                break;
            case '_2_1':
                // 如果通用的按用量 && 固定价格
                $this->writeConsumptionDetailByMonthWhenCommonNumberFixed($list_group_sections, $product_alo_key, $month);
                break;
            case "_2_2":
                // 通用的按用量 && 累进阶梯
                $this->writeConsumptionDetailByMonthWhenCommonProgression($list_group_sections, $product_alo_key, $month);
                break;
            case "_2_3":
                // 通用的按用量 && 到达阶梯
                $this->writeConsumptionDetailByMonthWhenCommonReach($list_group_sections, $product_alo_key, $month);
                break;
            case "_1_2":
                // 区分运营商按时间
                $this->writeConsumptionDetailByMonthWhenOperatorDate($list_group_sections, $product_alo_key, $month);
                break;
            case "_2_1_2":
                //  区分运营商按用量 && 固定价格
                $this->writeConsumptionDetailByMonthWhenOperatorFixed($list_group_sections, $product_alo_key, $month);
                break;
            case "_2_2_2":
                //  区分运营商按用量 && 累进阶梯
                $this->writeConsumptionDetailByMonthWhenOperatorProgression($list_group_sections, $product_alo_key, $month);
                break;
            case '_2_3_2':
                // 区分运营商按用量 && 到达阶梯
                $this->writeConsumptionDetailByMonthWhenOperatorReach($list_group_sections, $product_alo_key, $month);
                break;
        }
    }

    /**
     * 区分运营商按用量 && 到达阶梯
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeConsumptionDetailByMonthWhenOperatorReach(array $list_group_sections, string $product_alo_key, string $month)
    {
        $list_container = collect($list_group_sections)->reduce(function ($carry, $item) {
            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price;
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 各个运营商的调用量
            $carry = $this->setNumberOperatorWhenOperator($carry, $item);

            // 调用量在价格区间的分布分布
            $carry['list_money'][] = $this->distributeNumberOnPriceWhenOperatorReach($item);
            return $carry;
        }, []);

        // 格式化区间分布
        $list_container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenOperatorProgression($list_container);
        $this->list_product_alo_consumptions[$product_alo_key][$month] = $list_container;
    }

    /**
     * 价格的分布 && 区分运营商 && 到达阶梯
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function distributeNumberOnPriceWhenOperatorReach($section_item): array
    {
        // 阶梯周期
        switch ($section_item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                return $this->getMoneyInfoWhenOperatorReachDayCycle($section_item);
                break;
            case 2:
                // 月周期
                return $this->genMoneyWhenOperatorByNumberReach($section_item);
                break;
            case 3:
                // 年周期
                return $this->genMoneyWhenOperatorByNumberReachAndYearCycle($section_item);
                break;
            default:
                // 无周期
                return $this->genMoneyWhenOperatorByNumberReachAndNoCycle($section_item);
        }
    }

    /**
     * 无周期 && 通用 && 达到阶梯
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenOperatorByNumberReachAndNoCycle($section_item): array
    {
        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenOperatorReachAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenOperatorReachAndYearNoCycleSecond($section_item);

        // 格式化
        return $this->formatMoneyWhenOperatorReachAndNoCycle($money_first, $money_second);
    }

    /**
     * 无周期 && 运营商 && 按用
     * @param array $money_info_first
     * @param array $money_info_second
     * @return array
     */
    private function formatMoneyWhenOperatorReachAndNoCycle(array $money_info_first, array $money_info_second): array
    {
        return $this->formatMoneyWhenOperatorAndYearCycleInSameYear($money_info_first, $money_info_second);
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillMonth $section_item
     * @return float
     * @throws CustomException
     */
    private function getMoneyWhenOperatorReachAndYearNoCycleSecond($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearNoCycleSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计算
        return app('bill.number.reach.operator')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段的结束时间(第二部分)
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function genParamsWhenOperatorReachAndYearNoCycleSecond($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorReachAndYearNoCycleFirst($section_item): array
    {
        // 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            return [
                'money_yd' => 0,
                'money_lt' => 0,
                'money_dx' => 0,
            ];
        }

        return $this->getMoneyWhenOperatorReachAndYearNoCycleFirstAndTimeDiff($section_item);
    }


    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）时间不同
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorReachAndYearNoCycleFirstAndTimeDiff($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearNoCycleFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach.operator')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorReachAndYearNoCycleFirst($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 运营商 && 达到阶梯
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenOperatorByNumberReachAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {
            return $this->genMoneyWhenOperatorReachAndYearCycleInTwoYear($section_item);
        }

        // 片段在同一年
        return $this->genMoneyWhenOperatorReachAndYearCycleInSameYear($section_item);
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在同一年
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenOperatorReachAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            return $this->genMoneyWhenOperatorByNumberReach($section_item);
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.excel.reach.operator')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.excel.reach.operator')->genMoney($params_money_before);

        // 格式化数据
        return $this->formatMoneyWhenOperatorReachAndYearCycleInSameYear($money_before, $money_end);
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在同一年 格式化金钱
     * @param array $money_info_first
     * @param array $money_info_second
     * @return array
     */
    private function formatMoneyWhenOperatorReachAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
    {
        return $this->formatMoneyWhenOperatorAndYearCycleInSameYear($money_info_first, $money_info_second);
    }

    /**
     * 格式化 运营商 && 年周期 && 在同一周期
     * @param array $money_info_first
     * @param array $money_info_second
     * @return array
     */
    private function formatMoneyWhenOperatorAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
    {
        list($money_first_yd, $money_first_lt, $money_first_dx) = [
            $money_info_first['money_yd'] ?? 0, $money_info_first['money_lt'] ?? 0, $money_info_first['money_dx'] ?? 0
        ];

        list($money_second_yd, $money_second_lt, $money_second_dx) = [
            $money_info_second['money_yd'] ?? 0, $money_info_second['money_lt'] ?? 0, $money_info_second['money_dx'] ?? 0
        ];

        return [
            'money_yd' => $money_second_yd - $money_first_yd,
            'money_lt' => $money_second_lt - $money_first_lt,
            'money_dx' => $money_second_dx - $money_first_dx,
        ];

    }

    /**
     * 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenOperatorReachAndYearCycleInTwoYear($section_item): array
    {
        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenOperatorReachAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenOperatorReachAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenOperatorReachAndYearInTwoYearThird($section_item);

        // 格式化
        return $this->formatMoneyWhenOperatorReachAndYearCycleInTwoYear($money_first, $money_second, $money_third);
    }

    /**
     * 格式化
     * @param array $money_info_first
     * @param array $money_info_second
     * @param array $money_info_third
     * @return array
     */
    private function formatMoneyWhenOperatorReachAndYearCycleInTwoYear(array $money_info_first, array $money_info_second, array $money_info_third): array
    {
        list($money_first_yd, $money_first_lt, $money_first_dx) = [
            $money_info_first['money_yd'] ?? 0, $money_info_first['money_lt'] ?? 0, $money_info_first['money_dx'] ?? 0
        ];

        list($money_second_yd, $money_second_lt, $money_second_dx) = [
            $money_info_second['money_yd'] ?? 0, $money_info_second['money_lt'] ?? 0, $money_info_second['money_dx'] ?? 0
        ];

        list($money_third_yd, $money_third_lt, $money_third_dx) = [
            $money_info_third['money_yd'] ?? 0, $money_info_third['money_lt'] ?? 0, $money_info_third['money_dx'] ?? 0
        ];

        return [
            'money_yd' => $money_second_yd - $money_first_yd + $money_third_yd,
            'money_lt' => $money_second_lt - $money_first_lt + $money_third_lt,
            'money_dx' => $money_second_dx - $money_first_dx + $money_third_dx,
        ];
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function getMoneyWhenOperatorReachAndYearInTwoYearThird($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearInTwoYearThird($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach.operator')->genMoney($params_money);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorReachAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorReachAndYearInTwoYearSecond($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearInTwoYearSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach.operator')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorReachAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorReachAndYearInTwoYearFirst($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 数量
        $params = $this->genParamsWhenOperatorReachAndYearInTwoYearFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach.operator')->genMoney($params_money);
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorReachAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 生成Money
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenOperatorByNumberReach($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费
        return app('bill.excel.reach.operator')->genMoney($params_money);
    }

    /**
     * 天周期金额计算
     * @param $item
     * @return array
     * @throws CustomException
     */
    private function getMoneyInfoWhenOperatorReachDayCycle($item): array
    {
        $list_day_moneys = $this->genMoneyWhenOperatorByNumberReachAndDayCycle($item);

        // 格式化
        return $this->formatMoneyInfoWhenOperatorReachDayCycle($list_day_moneys);
    }

    /**
     * @param array $list_day_money
     * @return array
     */
    private function formatMoneyInfoWhenOperatorReachDayCycle(array $list_day_money): array
    {
        return array_reduce($list_day_money, function ($carry, $item) {
            $carry['money_yd'] += $item['money_yd'];
            $carry['money_lt'] += $item['money_lt'];
            $carry['money_dx'] += $item['money_dx'];
            return $carry;
        }, ['money_yd' => 0, 'money_lt' => 0, 'money_dx' => 0]);
    }

    /**
     * 天周期 && 通用 && 达到阶梯
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenOperatorByNumberReachAndDayCycle($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 计算这里每天的量
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 容器
        $list_container = [];

        // 计算每天的费用
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $list_container[$day] = app('bill.excel.reach.operator')->genMoney($params_money);
        });
        return $list_container;
    }

    /**
     * 区分运营商按用量 && 累进阶梯
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeConsumptionDetailByMonthWhenOperatorProgression(array $list_group_sections, string $product_alo_key, string $month)
    {
        $list_container = collect($list_group_sections)->reduce(function ($carry, $item) {
            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price;
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 各个运营商的调用量
            $carry = $this->setNumberOperatorWhenOperator($carry, $item);

            // 调用量在价格区间的分布分布
            $carry['list_money'][] = $this->distributeNumberOnPriceWhenOperatorProgression($item);

            return $carry;
        }, []);

        // 格式化区间分布
        $list_container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenOperatorProgression($list_container);
        $this->list_product_alo_consumptions[$product_alo_key][$month] = $list_container;
    }

    /**
     * 格式化区间分布
     * @param array $list_container
     * @return array
     */
    private function formatDistributeNumberOnPriceWhenOperatorProgression(array $list_container): array
    {
        // 格式化金额
        $list_money = array_reduce($list_container['list_money'], function ($carry, $item) {
            $carry['money_yd'] += $item['money_yd'] ?? 0;
            $carry['money_lt'] += $item['money_lt'] ?? 0;
            $carry['money_dx'] += $item['money_dx'] ?? 0;
            return $carry;
        }, ['money_yd' => 0, 'money_lt' => 0, 'money_dx' => 0]);

        return [
            [
                'operator' => '移动',
                'money' => $list_money['money_yd'],
                'number' => $list_container['list_number']['yd'] ?? 0,
            ],
            [
                'operator' => '联通',
                'money' => $list_money['money_lt'],
                'number' => $list_container['list_number']['lt'] ?? 0,
            ],
            [
                'operator' => '电信',
                'money' => $list_money['money_dx'],
                'number' => $list_container['list_number']['dx'] ?? 0,
            ],
        ];
    }

    /**
     * 通用 && 累进阶梯
     * @param MongoBillMonth $item
     * @return array
     * @throws CustomException
     */
    private function distributeNumberOnPriceWhenOperatorProgression($item): array
    {
        // 阶梯周期
        switch ($item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                return $this->getMoneyInfoWhenOperatorProgressionDayCycle($item);
                break;
            case 2:
                // 月周期
                return $this->getMoneyInfoWhenOperatorProgressionMonthCycle($item);
                break;
            case 3:
                // 年周期
                return $this->genMoneyWhenOperatorByNumberProgressionAndYearCycle($item);
                break;
            default:
                // 无周期
                return $this->genMoneyWhenOperatorByNumberProgressionAndNoCycle($item);
        }
    }

    /**
     * 无周期 && 运营商 && 累进阶梯
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenOperatorByNumberProgressionAndNoCycle($section_item): array
    {
        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenOperatorProgressAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenOperatorProgressAndYearNoCycleSecond($section_item);

        // 计费
        return $this->formatMoneyWhenOperatorByNumberProgressionAndNoCycle($money_first, $money_second);
    }


    /**
     * 年周期 && 运营商 && 按用量 && 在同一年 格式化金钱
     * @param array $money_info_first
     * @param array $money_info_second
     * @return array
     */
    private function formatMoneyWhenOperatorByNumberProgressionAndNoCycle(array $money_info_first, array $money_info_second): array
    {
        list($money_first_yd, $money_first_lt, $money_first_dx) = [
            $money_info_first['money_yd'] ?? 0, $money_info_first['money_lt'] ?? 0, $money_info_first['money_dx'] ?? 0
        ];

        list($money_second_yd, $money_second_lt, $money_second_dx) = [
            $money_info_second['money_yd'] ?? 0, $money_info_second['money_lt'] ?? 0, $money_info_second['money_dx'] ?? 0
        ];

        return [
            'money_yd' => $money_second_yd - $money_first_yd,
            'money_lt' => $money_second_lt - $money_first_lt,
            'money_dx' => $money_second_dx - $money_first_dx,
        ];
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorProgressAndYearNoCycleSecond($section_item): array
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonProgressAndYearNoCycleSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression.operator')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorProgressAndYearNoCycleFirst($section_item): array
    {
        // 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            return [
                'money_yd' => 0,
                'money_lt' => 0,
                'money_dx' => 0,
            ];
        }

        return $this->getMoneyWhenOperatorProgressAndYearNoCycleFirstAndTimeDiff($section_item);
    }

    /**
     * 计费开始时间和片段开始时间不同
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorProgressAndYearNoCycleFirstAndTimeDiff($section_item): array
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearNoCycleFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression.operator')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorProgressAndYearNoCycleFirst($section_item): array
    {
        $section_begin = date('Ymd', strtotime($section_item->section_source->start_date));
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 运营商 && 累进阶梯
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenOperatorByNumberProgressionAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {
            return $this->genMoneyWhenOperatorProgressionAndYearCycleInTwoYear($section_item);
        }

        return $this->genMoneyWhenOperatorProgressionAndYearCycleInSameYear($section_item);
    }


    /**
     * 年周期 && 运营商 && 按用量 && 在同一年
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenOperatorProgressionAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            return $this->getMoneyInfoWhenOperatorProgressionMonthCycle($section_item);
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.excel.progression.operator')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.excel.progression.operator')->genMoney($params_money_before);

        // 设置属性
        return $this->formatMoneyWhenOperatorProgressionAndYearCycleInSameYear($money_before, $money_end);
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在同一年 格式化金钱
     * @param array $money_info_first
     * @param array $money_info_second
     * @return array
     */
    private function formatMoneyWhenOperatorProgressionAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
    {
        return $this->formatMoneyWhenOperatorAndYearCycleInSameYear($money_info_first, $money_info_second);
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在不同的两年
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenOperatorProgressionAndYearCycleInTwoYear($section_item): array
    {
        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenOperatorProgressAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenOperatorProgressAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenOperatorProgressAndYearInTwoYearThird($section_item);

        // 格式化
        return $this->formatMoneyWhenOperatorProgressionAndYearCycleInTwoYear($money_first, $money_second, $money_third);
    }

    /**
     * 格式化
     * @param array $money_info_first
     * @param array $money_info_second
     * @param array $money_info_third
     * @return array
     */
    private function formatMoneyWhenOperatorProgressionAndYearCycleInTwoYear(array $money_info_first, array $money_info_second, array $money_info_third): array
    {
        list($money_first_yd, $money_first_lt, $money_first_dx) = [
            $money_info_first['money_yd'] ?? 0, $money_info_first['money_lt'] ?? 0, $money_info_first['money_dx'] ?? 0
        ];

        list($money_second_yd, $money_second_lt, $money_second_dx) = [
            $money_info_second['money_yd'] ?? 0, $money_info_second['money_lt'] ?? 0, $money_info_second['money_dx'] ?? 0
        ];

        list($money_third_yd, $money_third_lt, $money_third_dx) = [
            $money_info_third['money_yd'] ?? 0, $money_info_third['money_lt'] ?? 0, $money_info_third['money_dx'] ?? 0
        ];

        return [
            'money_yd' => $money_second_yd - $money_first_yd + $money_third_yd,
            'money_lt' => $money_second_lt - $money_first_lt + $money_third_lt,
            'money_dx' => $money_second_dx - $money_first_dx + $money_third_dx,
        ];
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function getMoneyWhenOperatorProgressAndYearInTwoYearThird($section_item): array
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearInTwoYearThird($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression.operator')->genMoney($params_money);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorProgressAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorProgressAndYearInTwoYearSecond($section_item): array
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearInTwoYearSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression.operator')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorProgressAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenOperatorProgressAndYearInTwoYearFirst($section_item): array
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearInTwoYearFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);
        return app('bill.excel.progression.operator')->genMoney($params_money);
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenOperatorProgressAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 运营商 && 累进阶梯 && 月周期
     * @param MongoBillMonth $bill_section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyInfoWhenOperatorProgressionMonthCycle($bill_section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($bill_section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($bill_section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费
        return app('bill.excel.progression.operator')->genMoney($params_money);
    }

    /**
     * 天周期金额计算
     * @param $item
     * @return array
     * @throws CustomException
     */
    private function getMoneyInfoWhenOperatorProgressionDayCycle($item): array
    {
        $list_day_moneys = $this->genMoneyWhenOperatorByNumberProgressionAndDayCycle($item);

        // 格式化
        return $this->formatMoneyInfoWhenOperatorProgressionDayCycle($list_day_moneys);
    }

    /**
     * 格式化数据
     * @param array $list_day_money
     * @return array
     */
    private function formatMoneyInfoWhenOperatorProgressionDayCycle(array $list_day_money): array
    {
        return array_reduce($list_day_money, function ($carry, $item) {
            $carry['money_yd'] += $item['money_yd'];
            $carry['money_lt'] += $item['money_lt'];
            $carry['money_dx'] += $item['money_dx'];
            return $carry;
        }, ['money_yd' => 0, 'money_lt' => 0, 'money_dx' => 0]);
    }

    /**
     * 天周期 && 运营商 && 累进阶梯
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenOperatorByNumberProgressionAndDayCycle($section_item): array
    {
        // 格式化价格 && 计算这里每天的量
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 容器
        $list_container = [];
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $list_container[$day] = app('bill.excel.progression.operator')->genMoney($params_money);
        });

        return $list_container;
    }

    /**
     * 格式化通用累进阶梯的价格
     * @param MongoBillMonth $section_item
     * @return array
     */
    private function formatPriceForNumberProgressionOperator($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'left' => $item[0],
                'right' => $item[1],
                'price_yd' => $item[2],
                'price_lt' => $item[3],
                'price_dx' => $item[4],
            ];
            return $carry;
        }, []);
    }

    /**
     * 设置各个运营商的调用量
     * @param array $carry
     * @param MongoBillMonth $item
     * @return array
     * @throws CustomException
     */
    private function setNumberOperatorWhenOperator(array $carry, $item): array
    {
        // 计费片段的调用量
        $fee_number = $this->numberInTheSection($item);
        list($number_yd, $number_lt, $number_dx) = [$fee_number['fee_number']['yd'] ?? 0, $fee_number['fee_number']['lt'] ?? 0, $fee_number['fee_number']['dx'] ?? 0];

        if (!isset($carry['list_number'])) {
            $carry['list_number'] = [
                'yd' => 0,
                'lt' => 0,
                'dx' => 0,
            ];
        }

        $carry['list_number']['yd'] += $number_yd;
        $carry['list_number']['lt'] += $number_lt;
        $carry['list_number']['dx'] += $number_dx;
        return $carry;
    }

    /**
     * 区分运营商 按日期
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeConsumptionDetailByMonthWhenOperatorDate(array $list_group_sections, string $product_alo_key, string $month)
    {
        $list_container = collect($list_group_sections)->reduce(function ($carry, $item) {
            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price;
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 各个运营商的调用量
            $carry = $this->setNumberOperatorWhenOperator($carry, $item);

            // 设置各个运营商的消费金额
            return $this->setMoneyOperatorWhenOperatorDate($carry, $item);
        }, []);

        // 格式化区间分布
        $list_container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenOperatorDate($list_container);
        $this->list_product_alo_consumptions[$product_alo_key][$month] = $list_container;
    }

    /**
     * 格式化区间分布
     * @param array $list_container
     * @return array
     */
    private function formatDistributeNumberOnPriceWhenOperatorDate(array $list_container): array
    {
        return [
            [
                'operator' => '移动',
                'money' => $list_container['list_money']['money_yd'] ?? 0,
                'number' => $list_container['list_number']['yd'] ?? 0,
            ],
            [
                'operator' => '联通',
                'money' => $list_container['list_money']['money_lt'] ?? 0,
                'number' => $list_container['list_number']['lt'] ?? 0,
            ],
            [
                'operator' => '电信',
                'money' => $list_container['list_money']['money_dx'] ?? 0,
                'number' => $list_container['list_number']['dx'] ?? 0,
            ],
        ];
    }

    /**
     * 各个运营商的累加
     * @param array $carry
     * @param MongoBillMonth $item
     * @return array
     * @throws CustomException
     */
    private function setMoneyOperatorWhenOperatorDate(array $carry, $item): array
    {
        if (!isset($carry['list_money'])) {
            $carry['list_money'] = [
                'money_yd' => 0,
                'money_lt' => 0,
                'money_dx' => 0
            ];
        }
        $fee_number = $this->getFeeNumberWhenOperatorDate($item);
        $carry['list_money']['money_yd'] += $fee_number['money_yd'] ?? 0;
        $carry['list_money']['money_lt'] += $fee_number['money_lt'] ?? 0;
        $carry['list_money']['money_dx'] += $fee_number['money_dx'] ?? 0;
        return $carry;
    }

    /**
     * 运营商 && 按时间 当前片段的各个运营商的金额
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function getFeeNumberWhenOperatorDate($section_item): array
    {
        // 获取这段时间消费的金额
        $params_money = $this->genParamsForOperatorByDate($section_item);
        return app('bill.excel.date.operator')->genMoney($params_money);
    }

    /**
     * 通用按日期生成参数
     * @param object $section_item
     * @return array
     */
    private function genParamsForOperatorByDate($section_item): array
    {
        // 价格格式化
        $fee_price = $this->formatPriceWhenOperatorDate($section_item);

        $fee_time_rule = $section_item->section_source->fee_time_rule;
        return [
            'section_begin' => $section_item->section_begin,
            'section_end' => $section_item->section_end,
            'fee_price' => $fee_price,
            'fee_time_rule' => $this->list_time_rule_mapping[$fee_time_rule] ?? '异常的fee_time_rule：' . $fee_time_rule,
            'month' => $section_item->month
        ];
    }

    /**
     * 格式化价格
     * @param $section_item
     * @return array
     */
    private function formatPriceWhenOperatorDate($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return [
            'price_yd' => $fee_price[0],
            'price_lt' => $fee_price[1],
            'price_dx' => $fee_price[2],
        ];
    }

    /**
     * 区分运营商的金额
     * @param $list_group_sections
     * @param $product_alo_key
     * @param $month
     */
    private function writeConsumptionDetailByMonthWhenOperatorFixed($list_group_sections, $product_alo_key, $month)
    {
        $list_container = collect($list_group_sections)->reduce(function ($carry, $item) {
            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price;
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 调用量总量
            if (!isset($carry['fee_number'])) {
                $carry['fee_number'] = [
                    'yd' => 0,
                    'dx' => 0,
                    'lt' => 0
                ];
            }

            $carry['fee_number']['yd'] += $item->section_number['fee_number']['yd'] ?? 0;
            $carry['fee_number']['dx'] += $item->section_number['fee_number']['dx'] ?? 0;
            $carry['fee_number']['lt'] += $item->section_number['fee_number']['lt'] ?? 0;
            return $carry;
        }, []);

        // 格式化区间分布
        $list_container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenOperatorFixed($list_container);
        $this->list_product_alo_consumptions[$product_alo_key][$month] = $list_container;
    }

    /**
     * 运营商 && 固定价格
     * @param array $list_container
     * @return array
     */
    private function formatDistributeNumberOnPriceWhenOperatorFixed(array $list_container): array
    {
        list($list_fee_number, $list_price) = [
            $list_container['fee_number'],
            json_decode($list_container['price'], true)
        ];

        // 计算金额
        list($price_yd, $price_lt, $price_dx) = [$list_price[0], $list_price[1], $list_price[2]];
        list($number_yd, $number_lt, $number_dx) = [$list_fee_number['yd'], $list_fee_number['lt'], $list_fee_number['dx']];
        $money_yd = $price_yd * $number_yd;
        $money_lt = $price_lt * $number_lt;
        $money_dx = $price_dx * $number_dx;

        return [
            ['price' => $price_yd, 'money' => $money_yd, 'number' => $number_yd, 'operator' => '移动'],
            ['price' => $price_lt, 'money' => $money_lt, 'number' => $number_lt, 'operator' => '联通'],
            ['price' => $price_dx, 'money' => $money_dx, 'number' => $number_dx, 'operator' => '电信'],
        ];
    }

    /**
     *  通用的按用量 && 到达阶梯
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeConsumptionDetailByMonthWhenCommonReach(array $list_group_sections, string $product_alo_key, string $month)
    {
        $list_container = collect($list_group_sections)->reduce(function ($carry, $item) {
            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price;
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 调用量在价格区间的分布分布
            $carry['list_distribute'][] = $this->distributeNumberOnPriceWhenCommonReach($item);

            return $carry;
        }, []);

        // 格式化区间分布
        $list_container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenCommonReach($list_container['list_distribute']);
        $this->list_product_alo_consumptions[$product_alo_key][$month] = $list_container;
    }

    /**
     * 格式化区间分布
     * @param array $list_distribute
     * @return array
     */
    private function formatDistributeNumberOnPriceWhenCommonReach(array $list_distribute): array
    {
        return array_reduce($list_distribute, function ($carry, $item_distribute) {
            array_walk($item_distribute, function ($item, $section_key) use (&$carry) {
                // 初始化数值
                if (!isset($carry[$section_key])) {
                    $carry[$section_key] = $item;
                    return;
                }
                $carry[$section_key]['money'] += $item['money'];
                $carry[$section_key]['section_number'] += $item['section_number'];
            });
            return $carry;
        }, []);
    }

    /**
     * 账单金额,调用量和单价的分布
     * @param  MongoBillMonth $item
     * @return array
     * @throws CustomException
     */
    private function distributeNumberOnPriceWhenCommonReach($item)
    {
        switch ($item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                return $this->getMoneyInfoWhenCommonReachDayCycle($item);
                break;
            case 2:
                // 月周期
                return $this->genMoneyWhenCommonByNumberReach($item);
                break;
            case 3:
                // 年周期
                return $this->genMoneyWhenCommonByNumberReachAndYearCycle($item);
                break;
            default:
                // 无周期
                return $this->genMoneyWhenCommonByNumberReachAndNoCycle($item);
        }
    }

    /**
     * 无周期 && 通用 && 达到阶梯
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenCommonByNumberReachAndNoCycle($section_item): array
    {
        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenCommonReachAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenCommonReachAndYearNoCycleSecond($section_item);

        // 格式化
        return $this->formatMoneyWhenCommonByNumberReachAndNoCycle($money_second, $money_first);
    }

    /**
     * 格式化
     * @param array $money_info_second
     * @param array $money_info_first
     * @return array
     */
    private function formatMoneyWhenCommonByNumberReachAndNoCycle(array $money_info_second, array $money_info_first): array
    {
        // 容器
        $list_container = [];

        list($money_first, $section_number_first) = $money_info_first ? [$money_info_first['money'], $money_info_first['section_number']] : [0, 0];
        list($money_second, $section_number_second, $section_key_second, $price_second) = $money_info_second ?
            [$money_info_second['money'], $money_info_second['section_number'], $money_info_second['section_key'], $money_info_second['price']]
            : [0, 0, 0, 0];

        $list_container[$section_key_second] = [
            'price' => $price_second,
            'section_key' => $section_key_second,
            'section_number' => $section_number_second - $section_number_first,
            'money' => $money_second - $money_first
        ];

        return $list_container;
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenCommonReachAndYearNoCycleSecond($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearNoCycleSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);
        return app('bill.excel.reach')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段的结束时间(第二部分)
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function genParamsWhenCommonReachAndYearNoCycleSecond($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenCommonReachAndYearNoCycleFirst($section_item): array
    {
        // 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            return [];
        }

        return $this->getMoneyWhenCommonReachAndYearNoCycleFirstAndTimeDiff($section_item);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分） 时间不同
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenCommonReachAndYearNoCycleFirstAndTimeDiff($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearNoCycleFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonReachAndYearNoCycleFirst($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 通用 && 达到阶梯
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenCommonByNumberReachAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {

            return $this->genMoneyWhenCommonReachAndYearCycleInTwoYear($section_item);
        }

        // 片段在同一年
        return $this->genMoneyWhenCommonReachAndYearCycleInSameYear($section_item);
    }

    /**
     * 年周期 && 通用 && 按用量 && 在同一年
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenCommonReachAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            return $this->genMoneyWhenCommonByNumberReach($section_item);
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.excel.reach')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.excel.reach')->genMoney($params_money_before);

        // 格式化
        return $this->formatMoneyWhenCommonReachAndYearCycleInSameYear($money_before, $money_end);
    }

    /**
     * 格式化
     * @param array $money_info_first
     * @param array $money_info_second
     * @return array
     */
    private function formatMoneyWhenCommonReachAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
    {
        // 容器
        $list_container = [];

        list($money_first, $section_number_first) = $money_info_first ? [$money_info_first['money'], $money_info_first['section_number']] : [0, 0];
        list($money_second, $section_number_second, $section_key_second, $price_second) = $money_info_second ?
            [$money_info_second['money'], $money_info_second['section_number'], $money_info_second['section_key'], $money_info_second['price']]
            : [0, 0, 0, 0];

        $list_container[$section_key_second] = [
            'price' => $price_second,
            'section_key' => $section_key_second,
            'section_number' => $section_number_second - $section_number_first,
            'money' => $money_second - $money_first
        ];

        return $list_container;
    }

    /**
     * 年周期 && 通用 && 按用量 && 在不同的两年 && 到达
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenCommonReachAndYearCycleInTwoYear($section_item): array
    {
        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenCommonReachAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenCommonReachAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenCommonReachAndYearInTwoYearThird($section_item);

        // 格式化
        return $this->formatMoneyWhenCommonReachAndYearCycleInTwoYear($money_first, $money_second, $money_third);
    }

    /**
     * 格式化
     * @param array $money_info_first
     * @param array $money_info_second
     * @param array $money_info_third
     * @return mixed
     */
    private function formatMoneyWhenCommonReachAndYearCycleInTwoYear(array $money_info_first, array $money_info_second, array $money_info_third)
    {
        // 容器
        $list_container = [];

        list($money_first, $section_number_first) = $money_info_first ? [$money_info_first['money'], $money_info_first['section_number']] : [0, 0];
        list($money_second, $section_number_second, $section_key_second, $price_second) = $money_info_second ?
            [$money_info_second['money'], $money_info_second['section_number'], $money_info_second['section_key'], $money_info_second['price']]
            : [0, 0, 0, 0];

        $list_container[$section_key_second] = [
            'price' => $price_second,
            'section_key' => $section_key_second,
            'section_number' => $section_number_second - $section_number_first,
            'money' => $money_second - $money_first
        ];

        // 如果第三个和第二个命中是同一个阶梯 则需要合并
        if ($section_key_second == $money_info_third['section_key']) {
            $list_container[$section_key_second]['section_number'] += $money_info_third['section_number'];
            $list_container[$section_key_second]['money'] += $money_info_third['money'];
        } else {
            $list_container[$money_info_third['section_key']] = $money_info_third;
        }

        return $list_container;
    }

    /**
     * 格式化累进阶梯的参数
     * @param MongoBillMonth $section_item
     * @return array
     */
    private function formatReachPrice($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price);
        return array_reduce($fee_price, function ($carry, $item) {
            list($section_key, $price) = [$item[0], $item[1], $item[2]];
            array_push($carry, compact('price', 'section_key'));
            return $carry;
        }, []);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function getMoneyWhenCommonReachAndYearInTwoYearThird($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearInTwoYearThird($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach')->genMoney($params_money);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonReachAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenCommonReachAndYearInTwoYearSecond($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearInTwoYearSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonReachAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }


    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenCommonReachAndYearInTwoYearFirst($section_item): array
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 数量
        $params = $this->genParamsWhenCommonReachAndYearInTwoYearFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.reach')->genMoney($params_money);
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonReachAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }


    /**
     * 生成Money
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenCommonByNumberReach($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费
        $money_info = app('bill.excel.reach')->genMoney($params_money);

        // 如果没有section_key 说明调用量为0,这时候返回[]
        return isset($money_info['section_key']) ? [$money_info['section_key'] => $money_info] : [];
    }

    /**
     * 通用 && 到达 && 日周期
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyInfoWhenCommonReachDayCycle($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 计算这里每天的量
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 容器
        $list_container = [];

        // 计算每天的费用
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $list_container[$day] = app('bill.excel.reach')->genMoney($params_money);
        });

        // 格式化金额
        return $this->formatMoneyInfoWhenCommonReachDayCycle($list_container);
    }

    /**
     * 格式化金额
     * @param array $list_container
     * @return array
     */
    private function formatMoneyInfoWhenCommonReachDayCycle(array $list_container): array
    {

        return array_reduce($list_container, function ($carry, $item) use ($list_container) {
            // 如果这天没有命中的section_key  (区间)
            if (!array_key_exists('section_key', $item)) {
                return $carry;
            }

            $section_key = $item['section_key'];

            // 如果之前没有这个阶梯的效果 则初始化
            if (!isset($carry[$section_key])) {
                $carry[$section_key] = $item;
                return $carry;
            }

            $carry[$section_key]['money'] += $item['money'];
            $carry[$section_key]['section_number'] += $item['section_number'];
            return $carry;
        }, []);
    }

    /**
     * 格式化通用达到阶梯的价格
     * @param MongoBillMonth $section_item
     * @return array
     */
    private function formatPriceForNumberReach($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'reach_standard' => $item[0],
                'price' => $item[1],
            ];
            return $carry;
        }, []);
    }

    /**
     * 格式化运营商达到阶梯的价格
     * @param MongoBillMonth $section_item
     * @return array
     */
    private function formatPriceForNumberReachOperator($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'reach_standard' => $item[0],
                'price_yd' => $item[1],
                'price_lt' => $item[2],
                'price_dx' => $item[3],
            ];
            return $carry;
        }, []);
    }

    /**
     * 通用的按用量 && 累进阶梯
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeConsumptionDetailByMonthWhenCommonProgression(array $list_group_sections, string $product_alo_key, string $month)
    {
        $list_container = collect($list_group_sections)->reduce(function ($carry, $item) {
            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price;
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 调用量在价格区间的分布分布
            $carry['list_distribute'][] = $this->distributeNumberOnPriceWhenCommonProgression($item);

            return $carry;
        }, []);

        // 格式化区间分布
        $list_container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenCommonProgression($list_container['list_distribute']);

        $this->list_product_alo_consumptions[$product_alo_key][$month] = $list_container;
    }

    /**
     * 格式化区间分布
     * @param array $list_distributes
     * @return array
     */
    private function formatDistributeNumberOnPriceWhenCommonProgression(array $list_distributes): array
    {
        // 容器
        $list_container = [];
        array_walk($list_distributes, function ($items) use (&$list_container) {
            array_walk($items, function ($section_money, $section_key) use (&$list_container) {
                if (!isset($list_container[$section_key])) {
                    $list_container[$section_key] = $section_money;
                    return;
                }
                $list_container[$section_key]['section_number'] += $section_money['section_number'];
                $list_container[$section_key]['money'] += $section_money['money'];
            });
        });
        return $list_container;
    }

    /**
     * 通用 && 累进阶梯
     * @param MongoBillMonth $item
     * @return array
     * @throws CustomException
     */
    private function distributeNumberOnPriceWhenCommonProgression($item): array
    {
        switch ($item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                return $this->getMoneyInfoWhenCommonProgressionDayCycle($item);
                break;
            case 2:
                // 月周期
                return $this->getMoneyInfoWhenCommonProgressionMonthCycle($item);
                break;
            case 3:
                // 年周期
                return $this->genMoneyWhenCommonByNumberProgressionAndYearCycle($item);
                break;
            default:
                // 无周期
                return $this->genMoneyWhenCommonByNumberProgressionAndNoCycle($item);
        }
    }

    /**
     * 无周期 && 通用 && 累进阶梯
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenCommonByNumberProgressionAndNoCycle($section_item): array
    {
        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenCommonProgressAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenCommonProgressAndYearNoCycleSecond($section_item);

        // 格式化
        return $this->formatMoneyWhenCommonByNumberProgressionAndNoCycle($section_item, $money_first, $money_second);
    }

    /**
     * @param $section_item
     * @param array $money_info_first
     * @param array $money_info_second
     * @return array
     */
    private function formatMoneyWhenCommonByNumberProgressionAndNoCycle($section_item, array $money_info_first, array $money_info_second): array
    {
        // key列表
        $list_section_keys = $this->formatProgressionPrice($section_item);

        // 各个区间的金额和用量的分布
        $list_section_distribute = array_reduce($list_section_keys, function ($carry, $item) use ($money_info_first, $money_info_second) {
            list($section_key, $price) = [$item['section_key'], $item['price']];
            list($money_first, $section_number_first) = isset($money_info_first[$section_key]) ?
                [$money_info_first[$section_key]['money'], $money_info_first[$section_key]['section_number']] : [0, 0];

            list($money_second, $section_number_second) = isset($money_info_second[$section_key]) ?
                [$money_info_second[$section_key]['money'], $money_info_second[$section_key]['section_number']] : [0, 0];


            // 三者的聚合
            $money = $money_second - $money_first;
            $section_number = $section_number_second - $section_number_first;

            $carry[$section_key] = compact('money', 'section_number', 'section_key', 'price');
            return $carry;
        }, []);

        // 如果每个区间都是0的话 则只保留第一个区间的信息
        if ($this->_determineEverySpaceIsZero($list_section_distribute)) {
            $key_current = key($list_section_distribute);
            return [$key_current => $list_section_distribute[$key_current]];
        }

        return $list_section_distribute;
    }

    /**
     * 累进每个区间的金额是否每个区间都是0
     * @param array $list_section_distribute
     * @return bool
     */
    private function _determineEverySpaceIsZero(array $list_section_distribute): bool
    {
        // 默认全是0
        $zero = true;
        collect($list_section_distribute)->each(function($item) use (&$zero){
            if ($item['section_number']) {
                $zero = false;
                return false;
            }
        });
        return $zero;
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillMonth $section_item
     * @return float
     * @throws CustomException
     */
    private function getMoneyWhenCommonProgressAndYearNoCycleSecond($section_item): array
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearNoCycleSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段的结束时间(第二部分)
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function genParamsWhenCommonProgressAndYearNoCycleSecond($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return float
     * @throws CustomException
     */
    private function getMoneyWhenCommonProgressAndYearNoCycleFirst($section_item): array
    {
        // 如果开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            return [];
        }

        return $this->getMoneyWhenCommonProgressAndYearNoCycleFirstAndTimeDiff($section_item);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）时间不同
     * @param $section_item
     * @return float
     * @throws CustomException
     */
    private function getMoneyWhenCommonProgressAndYearNoCycleFirstAndTimeDiff($section_item): array
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearNoCycleFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonProgressAndYearNoCycleFirst($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 通用 && 累进阶梯
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenCommonByNumberProgressionAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {
            return $this->genMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item);
        }

        return $this->genMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item);
    }

    /**
     * 年周期 && 通用 && 按用量 && 在同一年
     * @param $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            return $this->genMoneyWhenCommonByNumberProgression($section_item);
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgression($section_item);

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.excel.progression')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.excel.progression')->genMoney($params_money_before);

        // 设置属性
        return $this->formatMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item, $money_end, $money_before);
    }

    /**
     * @param MongoBillMonth $section_item
     * @param array $money_info_second
     * @param array $money_info_first
     * @return mixed
     */
    private function formatMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item, array $money_info_second, array $money_info_first): array
    {
        // key列表
        $list_section_keys = $this->formatProgressionPrice($section_item);

        return array_reduce($list_section_keys, function ($carry, $item) use ($money_info_first, $money_info_second) {
            list($section_key, $price) = [$item['section_key'], $item['price']];
            list($money_first, $section_number_first) = isset($money_info_first[$section_key]) ?
                [$money_info_first[$section_key]['money'], $money_info_first[$section_key]['section_number']] : [0, 0];

            list($money_second, $section_number_second) = isset($money_info_second[$section_key]) ?
                [$money_info_second[$section_key]['money'], $money_info_second[$section_key]['section_number']] : [0, 0];


            // 三者的聚合
            $money = $money_second - $money_first;
            $section_number = $section_number_second - $section_number_first;

            $carry[$section_key] = compact('money', 'section_number', 'section_key', 'price');
            return $carry;
        }, []);
    }

    /**
     * 开始计费到节点开始时候的参数 && 同一年
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsFromBeginWhenSameYear($section_item): array
    {
        //  在同一年的时候,获取第一阶段的开始时间
        $section_begin = $this->genBeginDayWhenSameYear($section_item);

        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 在同一年的时候,获取第一阶段的开始时间
     * @param MongoBillMonth $section_item
     * @return string
     */
    private function genBeginDayWhenSameYear($section_item): string
    {
        list($section_begin, $start_day) = [$section_item->section_begin, $section_item->start_day];

        // 如果开始计费时间和片段时间是在一年
        $days_from_begin = (strtotime($section_begin) - strtotime($start_day)) / 86400;

        // 和365天求余数
        $times = floor($days_from_begin / $this->days_in_years);
        $diff_days = $this->days_in_years * $times;

        return date('Ymd', strtotime('+' . $diff_days . ' days', strtotime($start_day)));
    }

    /**
     * 开始计费到节点开始时候的参数 && 同一年
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsFromEndWhenSameYear($section_item): array
    {
        // 同一年的开始时间
        $section_begin = $this->genBeginDayWhenSameYear($section_item);

        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 生成Money
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenCommonByNumberProgression($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgression($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费
        return app('bill.excel.progression')->genMoney($params_money);
    }


    /**
     * 年周期 && 通用 && 按用量 && 在不同的两年
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item): array
    {
        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenCommonProgressAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenCommonProgressAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenCommonProgressAndYearInTwoYearThird($section_item);

        // 格式化计费
        return $this->formatMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item, $money_first, $money_second, $money_third);
    }

    /**
     * 格式化
     * @param MongoBillMonth $section_item
     * @param array $money_info_first
     * @param array $money_info_second
     * @param array $money_info_third
     * @return mixed
     */
    private function formatMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item, array $money_info_first, array $money_info_second, array $money_info_third)
    {
        // key列表
        $list_section_keys = $this->formatProgressionPrice($section_item);

        return array_reduce($list_section_keys, function ($carry, $item) use ($money_info_first, $money_info_second, $money_info_third) {
            list($section_key, $price) = [$item['section_key'], $item['price']];
            list($money_first, $section_number_first) = isset($money_info_first[$section_key]) ?
                [$money_info_first[$section_key]['money'], $money_info_first[$section_key]['section_number']] : [0, 0];

            list($money_second, $section_number_second) = isset($money_info_second[$section_key]) ?
                [$money_info_second[$section_key]['money'], $money_info_second[$section_key]['section_number']] : [0, 0];

            list($money_third, $section_number_third) = isset($money_info_third[$section_key]) ?
                [$money_info_third[$section_key]['money'], $money_info_third[$section_key]['section_number']] : [0, 0];

            // 三者的聚合
            $money = $money_second - $money_first + $money_third;
            $section_number = $section_number_second - $section_number_first + $section_number_third;

            $carry[$section_key] = compact('money', 'section_number', 'section_key', 'price');
            return $carry;
        }, []);
    }

    /**
     * 格式化累进阶梯的参数
     * @param MongoBillMonth $section_item
     * @return array
     */
    private function formatProgressionPrice($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price);
        return array_reduce($fee_price, function ($carry, $item) {
            list($left, $right, $price) = [$item[0], $item[1], $item[2]];
            if ($right != -1) {
                $section_key = $left . '-' . $right;
            } else {
                $section_key = $left . '+';
            }

            array_push($carry, compact('price', 'section_key'));
            return $carry;
        }, []);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function getMoneyWhenCommonProgressAndYearInTwoYearThird($section_item): array
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearInTwoYearThird($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression')->genMoney($params_money);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonProgressAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenCommonProgressAndYearInTwoYearSecond($section_item): array
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearInTwoYearSecond($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonProgressAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyWhenCommonProgressAndYearInTwoYearFirst($section_item): array
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearInTwoYearFirst($section_item);
        $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        return app('bill.excel.progression')->genMoney($params_money);
    }

    /**
     * 计费片段的产品调用量
     * @param MongoBillMonth $section_item
     * @param array $params
     * @return array
     * @throws CustomException
     */
    private function numberForSectionByParams($section_item, array $params): array
    {
        // 驱动名称
        $driver_name = $this->genStatDriverName($section_item);
        return app('bill.statistic')->driver($driver_name)->billStat($params);
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsWhenCommonProgressAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 获取某一轮询的结束日期
     * @param MongoBillMonth $section_item
     * @return array
     */
    private function getTheLastDayOfThisSection($section_item): array
    {
        list($section_begin, $section_end, $start_day) = [$section_item->section_begin,
            $section_item->section_end, $section_item->start_day];

        // 这里一定不会出现等于的情况，如果出现出现的话,那么这个片段是在一个循环
        $timestamp_end = strtotime($start_day) + 86400 * $this->days_in_years;
        $day_end = date('Ymd', $timestamp_end);
        $day_begin = $start_day;
        while (true) {
            if ($day_end > $section_begin && $day_end < $section_end) {
                break;
            }

            $day_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
            $timestamp_end = strtotime($day_end) + 86400 * $this->days_in_years;
            $day_end = date('Ymd', $timestamp_end);
        }

        return [$day_begin, $day_end];
    }

    /**
     * 本次计费片段是否包含了两年的时间
     * @param $section_item
     * @return bool
     */
    private function determineInTwoYearFromStart($section_item): bool
    {
        // 距离开始日期的天数
        list($section_begin, $section_end, $start_day) = [$section_item->section_begin, $section_item->section_end,
            $section_item->section_source->start_date];

        $days_from_begin = (strtotime($section_begin) - strtotime($start_day)) / 86400;
        $days_from_end = (strtotime($section_end) - strtotime($start_day)) / 86400;

        // 和365天求余数
        $remainder_from_begin = $days_from_begin % $this->days_in_years;
        $remainder_from_end = $days_from_end % $this->days_in_years;

        // 因为section_begin && section_end最多间隔一个月， 所以 如果结束的余数小于了开始的余数 则说明进入两个周期
        return $remainder_from_end < $remainder_from_begin;
    }

    /**
     * 通用 && 累进阶梯 && 月周期
     * @param MongoBillMonth $bill_section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyInfoWhenCommonProgressionMonthCycle($bill_section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgression($bill_section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($bill_section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费
        return app('bill.excel.progression')->genMoney($params_money);
    }

    /**
     * 格式化通用累进阶梯的价格
     * @param MongoBillMonth $section_item
     * @return array
     */
    private function formatPriceForNumberProgression($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'left' => $item[0],
                'right' => $item[1],
                'price' => $item[2],
            ];
            return $carry;
        }, []);
    }

    /**
     * 通用 && 累进阶梯 && 天周期
     * @param MongoBillMonth $bill_section_item
     * @return array
     * @throws CustomException
     */
    private function getMoneyInfoWhenCommonProgressionDayCycle($bill_section_item): array
    {
        $list_container = $this->genMoneyWhenCommonByNumberProgressionAndDayCycle($bill_section_item);

        // 格式化
        return $this->formatMoneyInfoWhenCommonProgressionDayCycle($list_container);
    }

    /**
     * 格式化数据
     * @param array $list_day_money
     * @return array
     */
    private function formatMoneyInfoWhenCommonProgressionDayCycle(array $list_day_money): array
    {
        // 容器
        $list_container = [];

        array_walk($list_day_money, function ($info_day, $day) use (&$list_container) {
            array_walk($info_day, function ($section_money, $section_key) use (&$list_container) {
                if (!isset($list_container[$section_key])) {
                    $list_container[$section_key] = $section_money;
                    return;
                }
                $list_container[$section_key]['section_number'] += $section_money['section_number'];
                $list_container[$section_key]['money'] += $section_money['money'];
            });
        });

        return $list_container;
    }

    /**
     * 天周期 && 通用 && 累进阶梯
     * @param MongoBillMonth $section_item
     * @throws CustomException
     * @return array
     */
    private function genMoneyWhenCommonByNumberProgressionAndDayCycle($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgression($section_item);

        // 计算这里每天的量
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 总价格
        $list_container = [];

        // 计算每天的费用
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $list_container[$day] = app('bill.excel.progression')->genMoney($params_money);
        });

        return $list_container;
    }

    /**
     * 日调用量
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function numberSectionWhenDayCycle($section_item): array
    {
        // 驱动名称
        $driver_name = $this->genStatDriverName($section_item);

        // 参数
        $params = $this->genParamsForStatDay($section_item);

        // 容器
        $list_container = [];

        array_walk($params, function ($param) use (&$list_container, $driver_name) {
            $day = $param['section_begin'];
            $list_container[$day] = app('bill.statistic')->driver($driver_name)->billStat($param);
        });

        return $list_container;
    }

    /**
     * 生成天周期的参数
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsForStatDay($section_item)
    {
        $begin = $section_item->section_begin;
        $end = $section_item->section_end;

        // 基础信息
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        $base_item = compact('apikey', 'uuid', 'fee_basis', 'fee_price_rule');

        // 容器
        $list_container = [];
        while ($begin <= $end) {
            $time_item = [
                'section_begin' => $begin,
                'section_end' => $begin
            ];
            $list_container[] = array_merge($base_item, $time_item);
            $begin = date('Ymd', strtotime('+1 day', strtotime($begin)));
        }

        return $list_container;
    }

    /**
     * 通用 && 累进 && 获取调用量
     * @param $section_item
     * @throws CustomException
     * @return int
     */
    private function getFeeNumberWhenCommonProgression($section_item)
    {
        $fee_number = $this->numberInTheSection($section_item);
        return $fee_number['fee_number'] ?? 0;
    }

    /**
     * 如果通用的按用量 && 固定价格 则生成特定月分明的消费明细
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeConsumptionDetailByMonthWhenCommonNumberFixed(array $list_group_sections, string $product_alo_key, string $month)
    {
        $this->list_product_alo_consumptions[$product_alo_key][$month] = collect($list_group_sections)->reduce(function ($carry, $item) {

            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price;
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 调用量总量
            $carry['fee_number'] = ($carry['fee_number'] ?? 0) + ($item->section_number['fee_number'] ?? 0);
            return $carry;
        }, []);
    }

    /**
     * 按月生成各种算法的账单明细 && 通用按时间
     * @param array $list_group_sections
     * @param string $product_alo_key
     * @param string $month
     */
    private function writeConsumptionDetailByMonthWhenCommonDate(array $list_group_sections, string $product_alo_key, string $month)
    {
        $this->list_product_alo_consumptions[$product_alo_key][$month] = collect($list_group_sections)->reduce(function ($carry, $item) {
            // 设置价格
            if (!isset($carry['price'])) {
                $carry['price'] = $item->section_source->fee_price . '/' .
                    $this->list_fee_time_rule_mapping[$item->section_source->fee_time_rule];
            }

            // 产品信息
            if (!isset($carry['product_id'])) {
                $product_id = $item->product_id;
                $carry['product_info'] = Product::findOneItem(compact('product_id'))->toArray();
            }

            // 产品开始时间
            if (!isset($carry['section_begin'])) {
                $carry['section_begin'] = $item->section_begin;
            }

            if ($carry['section_begin'] > $item->section_begin) {
                $carry['section_begin'] = $item->section_begin;
            }

            // 产品结束时间
            if (!isset($carry['section_end'])) {
                $carry['section_end'] = date('Ymd', strtotime($carry['section_begin']));
            }

            if ($carry['section_end'] < $item['section_end']) {
                $carry['section_end'] = $item['section_end'];
            }

            // 消耗金额
            $carry['money'] = ($carry['money'] ?? 0) + $item->money;

            // 调用量总量
            $fee_number = $this->getFeeNumberWhenCommonDate($item);
            $carry['fee_number'] = ($carry['fee_number'] ?? 0) + $fee_number;
            return $carry;
        }, []);
    }

    /**
     * 通用 && 按日期 && 获取调用量
     * @param $section_item
     * @throws CustomException
     * @return int
     */
    private function getFeeNumberWhenCommonDate($section_item)
    {
        $fee_number = $this->numberInTheSection($section_item);
        return $fee_number['fee_number'] ?? 0;
    }

    /**
     * 计费片段的产品调用量
     * @param MongoBillMonth $section_item
     * @return array
     * @throws CustomException
     */
    private function numberInTheSection($section_item): array
    {
        // 驱动名称
        $driver_name = $this->genStatDriverName($section_item);

        // 参数
        $params = $this->genParamsForStatDriver($section_item);

        return app('bill.statistic')->driver($driver_name)->billStat($params);
    }

    /**
     * 获取调用量驱动的参数
     * @param $section_item
     * @return array
     * @throws CustomException
     */
    private function genParamsForStatDriver($section_item): array
    {
        $section_begin = $section_item->section_begin;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * @param MongoBillMonth $section_item
     * @return string
     * @throws CustomException
     */
    private function getApikeyForSection($section_item): string
    {
        $account_id = $section_item->account_id;
        $account = Account::getOneItemByCondition(compact('account_id'), 'apikey');
        if (!$account || !$account->apikey) {
            throw new CustomException('uuid : ' . $section_item->uuid . ' account_id:' . $section_item->account_id . '没有找到合法的账号');
        }
        return $account->apikey;
    }

    /**
     * 获取调用量驱动的名称
     * @param MongoBillMonth $section_item
     * @return string
     */
    private function genStatDriverName($section_item): string
    {
        $product_id = $section_item->product_id;
        return $this->driver_prefix . $product_id;
    }

    /**
     * 某个产品的某种算法不可以生成对账单
     * @param array $list_month_group_sections
     * @return bool
     */
    private function determineCanExportConsumptionForAloProduct(array $list_month_group_sections): bool
    {
        $export_cant = false;
        collect($list_month_group_sections)->each(function ($list_group_sections) use (&$export_cant) {
            // 判断选中月份是否是相同的配置, 如果是配置不同，则不出消费明细
            return !($export_cant = $this->determineCantExportConsumptionDetails($list_group_sections));
        });

        return $export_cant;
    }

    /**
     * 不可以导出消费明细?
     * @param array $list_group_sections
     * @return bool
     */
    private function determineCantExportConsumptionDetails(array $list_group_sections): bool
    {
        // 作为基础的一个
        $base_one = collect($list_group_sections)->first();

        // 如果在配置冲突的产品里面 则直接返回
        if (in_array($base_one->product_id, $this->list_product_ids_which_bill_conflict)) {
            return true;
        }

        // 是否配置冲突
        $if_conflict = collect($list_group_sections)->contains(function ($bill_section_item) use ($base_one) {
            // 计费片段
            return $bill_section_item->section_begin != $base_one->section_begin
                || $bill_section_item->section_end != $base_one->section_end
                || $bill_section_item->section_source->fee_basis != $base_one->section_source->fee_basis
                || $bill_section_item->section_source->fee_method != $base_one->section_source->fee_method
                || $bill_section_item->section_source->fee_time_rule != $base_one->section_source->fee_time_rule
                || $bill_section_item->section_source->fee_amount_rule != $base_one->section_source->fee_amount_rule
                || $bill_section_item->section_source->fee_step_rule != $base_one->section_source->fee_step_rule
                || $bill_section_item->section_source->fee_price_rule != $base_one->section_source->fee_price_rule
                || $bill_section_item->section_source->fee_price != $base_one->section_source->fee_price;
        });

        if ($if_conflict === true) {
            array_push($this->list_product_ids_which_bill_conflict, $base_one->product_id);
        }

        return $if_conflict;
    }


    /**
     * 根据账单算法 && 产品ID来聚合账单片段
     */
    private function aggregateSectionGroupBillAloAndProductId()
    {
        // 重置环境
        $this->_resetEnv();

        // 条件
        $where = $this->genConsumeParamsForThisYear();
        MongoBillMonth::getListByCondition($where, null, '_id asc')->filter(function ($item) {
            $remarks = $item['section_source']['remarks'] ?? '';
            return trim($remarks) != '系统归零设置';
        })->each(function ($section_item) {
            // 设置正在操作的账单片段
            $this->setSectionBillItem($section_item);

            // 设置算法场景的账单片段
            $this->setListBillSectionGroupAlg();
        });
    }

    /**
     * 重置环境变量
     */
    private function _resetEnv()
    {
        $this->section_bill_item = null;
        $this->list_bill_section_group_alg = [];
    }

    /**
     * 设置算法场景的账单片段determineShortcutOnlyOneAlo
     */
    public function setListBillSectionGroupAlg()
    {
        // 通用 && 按时间
        if ($this->determineIfSectionIsCommonByDate($this->section_bill_item)) {
            // 设置算法分组
            $this->setBillGroupAlgWhenCommonByDate();
            return;
        }


        // 如果通用的按用量 && 固定价格
        if ($this->determineIfSectionIdCommonByNumberFixed($this->section_bill_item)) {
            // 设置算法分组
            $this->setBillGroupAlgWhenCommonByNumberFixed();
            return;
        }

        // 通用的按用量 && 累进阶梯
        if ($this->determineIfSectionIsCommonByNumberProgression($this->section_bill_item)) {
            // 设置算法分组
            $this->setBillGroupAlgWhenCommonByNumberProgression();
            return;
        }

        // 通用的按用量 && 到达阶梯
        if ($this->determineSectionIsCommonByNumberReach($this->section_bill_item)) {
            $this->setBillGroupAlgWhenCommonNumberReach();
            return;
        }

        // 区分运营商按时间
        if ($this->determineSectionIsDateOperator($this->section_bill_item)) {
            $this->setBillGroupAlgWhenDateOperator();
            return;
        }

        // 区分运营商按用量 && 固定价格
        if ($this->determineIfSectionIdOperatorByNumberFixed($this->section_bill_item)) {
            $this->setBillGroupAlgWhenOperatorByNumberFixed();
            return;
        }

        // 区分运营商按用量 && 累进阶梯
        if ($this->determineIfSectionIsOperatorByNumberProgression($this->section_bill_item)) {
            $this->setBillGroupAlgWhenOperatorByNumberProgression();
            return;
        }

        // 区分运营商按用量 && 到达阶梯
        if ($this->determineSectionIsOperatorByNumberReach($this->section_bill_item)) {
            $this->setBillGroupAlgWhenOperatorNumberReach();
            return;
        }
    }

    /**
     * 区分运营商按用量 && 到达阶梯算法服务
     */
    private function setBillGroupAlgWhenOperatorNumberReach()
    {
        list($product_id, $fee_method, $fee_amount_rule, $fee_price_rule, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->section_source->fee_amount_rule,
            $this->section_bill_item->section_source->fee_price_rule,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method, $fee_amount_rule, $fee_price_rule);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * 片段是否是(运营商的按用量 && 到达阶梯)
     * @param object $section_item
     * @return bool
     */
    private function determineSectionIsOperatorByNumberReach($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule == 2;
    }

    /**
     * 区分运营商按用量 && 累进阶梯分组算法
     */
    private function setBillGroupAlgWhenOperatorByNumberProgression()
    {
        list($product_id, $fee_method, $fee_amount_rule, $fee_price_rule, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->section_source->fee_amount_rule,
            $this->section_bill_item->section_source->fee_price_rule,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method, $fee_amount_rule, $fee_price_rule);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * @param object $section_item
     * @return bool
     */
    private function determineIfSectionIsOperatorByNumberProgression($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule == 2;
    }

    /**
     * 区分运营商按用量 && 固定价格算法分组
     */
    private function setBillGroupAlgWhenOperatorByNumberFixed()
    {
        list($product_id, $fee_method, $fee_amount_rule, $fee_price_rule, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->section_source->fee_amount_rule,
            $this->section_bill_item->section_source->fee_price_rule,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method, $fee_amount_rule, $fee_price_rule);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * 计费片段是否是运营商固定价格模式
     * @param $section_item
     * @return bool
     */
    private function determineIfSectionIdOperatorByNumberFixed($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule == 2;
    }

    /**
     * 区分运营商按时间算法分组
     */
    private function setBillGroupAlgWhenDateOperator()
    {
        list($product_id, $fee_method, $fee_price_rule, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->section_source->fee_price_rule,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method, $fee_price_rule);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * 是否是区分运营商按时间的场景
     * @param object $section_item
     * @return bool
     */
    private function determineSectionIsDateOperator($section_item): bool
    {
        // 按时间
        $fee_method = $section_item->section_source->fee_method;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return 1 == $fee_method && $fee_price_rule == 2;
    }

    /**
     * 通用的按用量 && 到达阶梯设置算法分组
     */
    private function setBillGroupAlgWhenCommonNumberReach()
    {
        list($product_id, $fee_method, $fee_amount_rule, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->section_source->fee_amount_rule,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method, $fee_amount_rule);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * 片段是否是(通用的按用量 && 到达阶梯)
     * @param object $section_item
     * @return bool
     */
    private function determineSectionIsCommonByNumberReach($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule != 2;
    }

    /**
     * 通用的按用量 && 累进阶梯设置算法分组
     */
    private function setBillGroupAlgWhenCommonByNumberProgression()
    {
        list($product_id, $fee_method, $fee_amount_rule, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->section_source->fee_amount_rule,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method, $fee_amount_rule);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * @param  $section_item
     * @return bool
     */
    private function determineIfSectionIsCommonByNumberProgression($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule != 2;
    }

    /**
     * 通用的按用量 && 固定价格设置算法分组
     */
    private function setBillGroupAlgWhenCommonByNumberFixed()
    {
        list($product_id, $fee_method, $fee_amount_rule, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->section_source->fee_amount_rule,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method, $fee_amount_rule);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * 计费片段是否是通用固定价格模式
     * @param $section_item
     * @return bool
     */
    private function determineIfSectionIdCommonByNumberFixed($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule != 2;
    }

    /**
     * 通用 && 按时间设置
     */
    private function setBillGroupAlgWhenCommonByDate()
    {
        list($product_id, $fee_method, $month) = [
            $this->section_bill_item->product_id,
            $this->section_bill_item->section_source->fee_method,
            $this->section_bill_item->month
        ];

        // 生成算法分组key
        $key_alo = $this->genBillGroupAlgKey($product_id, $fee_method);
        $this->list_bill_section_group_alg[$key_alo][$month][] = $this->section_bill_item;
    }

    /**
     * 生成算法分组key
     * @return mixed
     */
    private function genBillGroupAlgKey()
    {
        $arg_list = func_get_args();
        return array_reduce($arg_list, function ($carry, $item) {
            if ($item) {
                $carry .= $carry ? ('_' . $item) : $item;
            }
            return $carry;
        }, '');
    }

    /**
     * 是否是通用时间
     * @param object $section_item
     * @return bool
     */
    private function determineIfSectionIsCommonByDate($section_item)
    {
        // 按时间
        $fee_method = $section_item->section_source->fee_method;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;
        return 1 == $fee_method && $fee_price_rule != 2;
    }

    /**
     * 对账结算单
     */
    private function getTotalStatement(): array
    {
        // 去年剩余的金额
        $money_last_year = $this->getResidueOfLastYear();

        // 客户相关信息
        $customer_info = $this->getCustomerInfo();

        // 今年各个月份金额分组的情况
        $month_month_this_year = $this->getMoneyGroupByMonthInThisYear();

        // 合计(结算单)
        $list_aggregate_money = $this->aggregateMoneyForTotalShow($money_last_year, $month_month_this_year);

        // 获取基本信息
        list($today, $theme, $content) = $this->getBaseInfoForHistory($list_aggregate_money, $customer_info);

        return compact('money_last_year', 'customer_info', 'month_month_this_year', 'list_aggregate_money', 'today', 'theme', 'content');
    }

    /**
     * 追加一些基本信息
     * @param array $list_aggregate_money
     * @param array $customer_info
     * @return array
     */
    private function getBaseInfoForHistory(array $list_aggregate_money, array $customer_info): array
    {
        // 操作时间
        $today = date('d日n月Y年');

        // 主题
        $theme = $this->getHistoryTheme($customer_info);

        // 上个月客户消耗的金额
        $money_last_month = $this->getConsumptionLastMonth();

        // 是否余额不足
        $str_when_residue_not_much = ($list_aggregate_money['money_residue_now'] <= $money_last_month) ? "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;剩余金额预计可用不足一个月，为保证账号正常使用，请及时充值" : "";

        // 默认内容
        $content = "<p>尊敬的客户,您好：</p><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;感谢贵司一直以来对我司产品的支持与信赖！ 
贵司" . date('m', strtotime('first day of last month')) . "月份消耗总额为：" . number_format($money_last_month, 2) . "元，
剩余金额为：" . number_format($list_aggregate_money['money_residue_now'], 2) . "元。 消耗详情请查看附件结算单！
<br/>$str_when_residue_not_much
</p><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;如与贵司记录相符，请回复邮件确认。
如不符，也请随时告知我司，我们会尽快排查，再次核对。 若3个工作日内未回复，我司默认贵司本月调用与我司一致，感谢贵司的理解与支持，祝好！</p>";
        return [$today, $theme, $content];
    }

    /**
     * 上个月消费的金额
     * @return float
     */
    private function getConsumptionLastMonth(): float
    {
        $customer_id = $this->customer_id;
        $month = date('Ym', strtotime('first day of last month'));
        return MongoBillMonth::where(compact('month', 'customer_id'))
            ->sum('money');
    }

    /**
     * 主题
     * @param array $customer_info
     * @return string
     */
    private function getHistoryTheme(array $customer_info): string
    {
        // XXXX客户YYYY年MM月对账单，其中XXXX取值当前客户名，YYYY取当年，MM取值当月-1
        return Common::COMPANY_CN_NAME.'--' . $customer_info['company'] . date('Y年m月', strtotime('first day of last month')) . '对账单';
    }

    /**
     *
     * @param array $money_last_year
     * @param array $month_month_this_year
     * @return array
     */
    private function aggregateMoneyForTotalShow(array $money_last_year, array $month_month_this_year): array
    {
        // 去年剩余  今年充值金额  今年消费金额
        list($money_residue, $list_month_recharge, $list_month_consume) = [$money_last_year['money_residue'],
            $month_month_this_year['list_month_recharge'], $month_month_this_year['list_month_consume']];

        // 今年充值金额累加
        $money_recharge_aggregate = array_reduce($list_month_recharge, function ($carry, $item) {
            $carry += $item;
            return $carry;
        }, $money_residue);

        // 今年消费金额累加
        $money_consume_aggregate = array_reduce($list_month_consume, function ($carry, $item) {
            $carry += $item;
            return $carry;
        }, 0);

        // 今年剩余的金额
        $money_residue_now = $money_recharge_aggregate - $money_consume_aggregate;
        return compact('money_recharge_aggregate', 'money_consume_aggregate', 'money_residue_now');
    }

    /**
     * 今年各个月份金额分组的情况
     * @return array
     */
    private function getMoneyGroupByMonthInThisYear(): array
    {
        // 今年充值金额每月的分布
        $list_month_recharge = $this->getRechargeMonthOfThisYear();

        // 今年消费金额每月的分布
        $list_month_consume = $this->getConsumeMonthOfThisYear();

        // 区间分割
        $list_show_date = $this->getDateSectionWhichMappingMonth();

        return compact('list_month_consume', 'list_month_recharge', 'list_show_date');
    }

    /**
     * 获取日期分割的片段 && 已经对应的映射关系
     * @return array
     */
    private function getDateSectionWhichMappingMonth(): array
    {
        $where = $this->genConsumeParamsForThisYear();

        // 获取开始时间
        $item_begin = MongoBillMonth::getOneItemByCondition($where, null, 'section_begin asc');
        $section_begin = $item_begin->section_begin ?? '';
        if (!$section_begin) {
            return ['该客户没有对应的计费片段'];
        }

        // 获取结束时间
        $section_end = MongoBillMonth::getOneItemByCondition($where, null, 'section_end desc')->section_end;

        // 结算单关于展示日期的切割
        return $this->cutDateSectionWhenTotalShow($section_begin, $section_end);
    }

    /**
     * 结算单关于展示日期的切割
     * @param string $section_begin
     * @param string $section_end
     * @return array
     */
    private function cutDateSectionWhenTotalShow(string $section_begin, string $section_end): array
    {
        // 容器
        $list_container = [];
        while ($section_begin <= $section_end) {
            $month = date('Ym', strtotime($section_begin));
            $last_day_of_month = date('Ymd', strtotime('last day of this month', strtotime($section_begin)));
            $list_container[$month] = compact('section_begin', 'last_day_of_month', 'month');
            $section_begin = date('Ymd', strtotime('first day of next month', strtotime($section_begin)));
        }

        return $list_container;
    }

    /**
     * 今年消费参数
     * @return array
     */
    private function genConsumeParamsForThisYear(): array
    {
        $customer_id = $this->customer_id;
        $month = [
            '$gte' => date('Y' . '01')
        ];
        return compact('customer_id', 'month');
    }

    /**
     * 今年消费金额每月的分布
     * @return array
     */
    private function getConsumeMonthOfThisYear(): array
    {
        $where = $this->genConsumeParamsForThisYear();
        return MongoBillMonth::getListByCondition($where)->reduce(function ($carry, $item) {
            $month = $item->month;
            $carry[$month] = ($carry[$month] ?? 0) + $item->money;
            return $carry;
        }, []);
    }

    /**
     * 充值金额每月的分布
     * @return array
     */
    private function getRechargeMonthOfThisYear(): array
    {
        $where = [
            ['customer_id', $this->customer_id],
            ['remit_date', '>=', strtotime(date('Y') . '-01-01')],
            ['status', 3]
        ];

        return MoneyRecharge::getListByCondition($where)->reduce(function ($carry, $item) {
            $month = date('Ym', $item->remit_date);
            if (!isset($carry[$month])) {
                $carry[$month] = 0;
            }
            $carry[$month] += $item->money;
            return $carry;
        }, []);
    }

    /**
     * @return array
     */
    private function getCustomerInfo(): array
    {
        $customer_id = $this->customer_id;
        return Customer::getOneItemByCondition(compact('customer_id'))->toArray();
    }

    /**
     * 校验然后设置customer_id
     * @param string $customer_id
     * @throws CustomException
     */
    private function validateAndSetCustomerId($customer_id)
    {
        if (!Customer::getOneItemByCondition(compact('customer_id'))) {
            throw new CustomException('传入的customer_id未找到对应的客户');
        }

        // 校验然后设置customer_id
        $this->customer_id = $customer_id;
    }

    /**
     * 获取去年剩余的金额
     * @return array
     */
    private function getResidueOfLastYear(): array
    {
        // 截止到去年的历史充值金until
        $money_recharge = $this->getRechargeUntilLastYear();

        // 截至到去年的历史消费金额
        $money_consume = $this->getConsumeUntilLastYear();

        // 去年剩余的金额
        $money_residue = $money_recharge - $money_consume;

        return compact('money_residue', 'money_consume', 'money_recharge');
    }

    /**
     * 获取客户截至到去年消费的金额
     * @return float
     */
    private function getConsumeUntilLastYear(): float
    {
        $month = ['$lt' => date('Y' . '01')];
        $customer_id = $this->customer_id;
        return MongoBillMonth::where(compact('month', 'customer_id'))
            ->sum('money');
    }

    /**
     * 截止到去年的历史充值金until
     * @return float
     */
    private function getRechargeUntilLastYear(): float
    {
        // 到去年为止
        $timestamp_this_year = strtotime(date('Y' . '-01-01'));
        $where = [
            ['remit_date', '<', $timestamp_this_year],
            ['status', 3],
            ['customer_id', $this->customer_id]
        ];

        return MoneyRecharge::where($where)->sum('money');
    }
}