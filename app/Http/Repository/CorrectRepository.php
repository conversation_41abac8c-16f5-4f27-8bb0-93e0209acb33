<?php

namespace App\Http\Repository;

use App\Exports\CorrectExport;
use App\Models\Account;
use App\Models\Customer;
use App\Models\MongoCorrectBillNumbers;
use App\Models\MongoCorrectNumberComment;
use App\Models\Product;
use App\Support\CustomException;
use Maatwebsite\Excel\Facades\Excel;
use Ramsey\Uuid\Uuid;

class CorrectRepository
{

    /**
     * 备注列表
     *
     * @param string $uuid
     *
     * @return array
     */
    public function comments(string $uuid): array
    {
        return MongoCorrectNumberComment::getListByCondition(compact('uuid'))
            ->all();
    }

    /**
     * 增加备注
     * @throws CustomException
     */
    public function addComment()
    {
        // 校验条件
        $this->_validateParamsForComment();

        // 生成条件
        $params = $this->_genParamsForComment();

        // 写入
        MongoCorrectNumberComment::create($params);
    }

    /**
     * 生成条件
     * @return array
     */
    private function _genParamsForComment(): array
    {
        list($uuid, $comment, $operator) = [
            trim(request()->post('uuid')),
            trim(request()->post('comment')),
            trim(request()->post('operator')),
        ];
        return compact('uuid', 'comment', 'operator');
    }

    /**
     * 校验条件
     * @throws CustomException
     */
    private function _validateParamsForComment()
    {
        if (!request()->post('uuid')) {
            throw new CustomException('请输入uuid');
        }

        if (!request()->post('comment')) {
            throw new CustomException('请输入备注');
        }
        if (!request()->post('operator')) {
            throw new CustomException('请输入登陆用户');
        }
    }

    /**
     * 校正日期列表下载
     */
    public function excel()
    {
        $list = request()->post('list');

        return Excel::download(new CorrectExport($list), 'export.xlsx');
    }

    /**
     * 操作用户列表
     * @return array
     */
    public function operators(): array
    {
        return MongoCorrectBillNumbers::distinct('operator')
            ->get()
            ->reduce(
                function ($carry, $item) {
                    array_unshift($carry, $item->toArray()[0]);
                    return $carry;
                },
                []
            );
    }

    /**
     * 校正日期列表
     * @return array
     * @throws CustomException
     */
    public function lists(): array
    {
        // 校验参数
        $this->_validateParamsForList();

        // 生成参数
        $where = $this->_genParamsForLists();

        // 获取列表
        return $this->_genFormatList($where);
    }

    /**
     * 获取列表
     *
     * @param array $where
     *
     * @return array
     */
    private function _genFormatList(array $where): array
    {
        $total = [
            'source_number'  => 0,
            'updated_number' => 0
        ];

        $list = MongoCorrectBillNumbers::getListByCondition($where)
            ->map(
                function ($item) use (&$total) {
                    // 计算总计
                    $total = $this->_computedTotalForList($total, $item);

                    return $this->formatItemForList($item);
                }
            )
            ->all();

        return compact('list', 'total');
    }

    /**
     * 计算总量
     *
     * @param array $total
     * @param       $item
     *
     * @return array
     */
    private function _computedTotalForList(array $total, $item): array
    {
        $item->data              = (object)$item->data;
        $total['source_number']  += $item->type == 'common' ? $item->data->source_number : ($item->data->yd_source_number + $item->data->lt_source_number + $item->data->dx_source_number);
        $total['updated_number'] += $item->type == 'common' ? $item->data->updated_number : ($item->data->yd_updated_number + $item->data->lt_updated_number + $item->data->dx_updated_number);
        return $total;
    }

    /**
     * 格式列表单元
     *
     * @param $item
     *
     * @return mixed
     */
    private function formatItemForList($item)
    {
        $item->customer_name = $this->_getNameByCustoemrId($item->customer_id);
        $item->account_name  = $this->_getNameByAccountId($item->account_id);
        $item->apikey        = $this->_getApikeyByAccountId($item->account_id);
        $item->product_name  = $this->_getNameByProductId($item->product_id);

        unset($item->_id);
        return $item;
    }

    /**
     * 获取产品名
     *
     * @param string $product_id
     *
     * @return string
     */
    private function _getNameByProductId(string $product_id): string
    {
        $product = Product::findOneItem(compact('product_id'), 'product_name');
        return $product ? $product->product_name : '';
    }

    /**
     * 获取客户的name
     *
     * @param string $account_id
     *
     * @return string
     */
    private function _getNameByAccountId(string $account_id): string
    {
        $account = Account::getOneItemByCondition(compact('account_id'), 'account_name');
        return $account ? $account->account_name : '';
    }

    /**
     * 获取客户的apikey
     *
     * @param string $account_id
     *
     * @return string
     */
    private function _getApikeyByAccountId(string $account_id): string
    {
        $account = Account::getOneItemByCondition(compact('account_id'), 'apikey');
        return $account ? $account->apikey : '';
    }

    /**
     * @param string $customer_id
     *
     * @return string
     */
    private function _getNameByCustoemrId(string $customer_id): string
    {
        $customer = Customer::getOneItemByCondition(compact('customer_id'), 'name');
        return $customer ? $customer->name : '';
    }

    /**
     * 生成参数
     * @return array
     */
    private function _genParamsForLists(): array
    {
        // 月份限制
        $param_month = $this->_genMonthParamsForList();

        // 基本限制
        $param_base = $this->_genBaseParamsForList();

        return array_merge($param_base, $param_month);
    }

    /**
     * 月份限制
     * @return array
     */
    protected function _genMonthParamsForList(): array
    {
        list($month_begin, $month_end) = [
            $this->_formatMonth(request()->get('month_begin')),
            $this->_formatMonth(request()->get('month_end')),
        ];

        return [
            'month' => [
                '$gte' => $month_begin,
                '$lte' => $month_end,
            ]
        ];
    }

    /**
     * 基本限制
     * @return array
     */
    protected function _genBaseParamsForList(): array
    {
        list($customer_id, $product_id, $operator) = [
            trim(request()->get('customer_id')),
            (int)trim(request()->get('product_id')),
            (int)trim(request()->get('operator')),
        ];

        return array_filter(
            compact('customer_id', 'product_id', 'operator'),
            function ($item) {
                return $item;
            }
        );
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamsForList()
    {
        // 校验时间
        list($month_begin, $month_end) = [
            trim(request()->get('month_begin')),
            trim(request()->get('month_end')),
        ];

        if (!$month_end || !$month_begin) {
            throw new CustomException('请输入开始和结束月份');
        }

        list($month_begin_format, $month_end_format) = [
            (int)$this->_formatMonth($month_begin),
            (int)$this->_formatMonth($month_end),
        ];

        if ($month_begin_format > $month_end_format) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }

    /**
     * 格式化月份
     *
     * @param string $month
     *
     * @return string
     */
    private function _formatMonth(string $month): string
    {
        // 是否是Ymd的格式
        if (strpos($month, '-') === false) {
            return $month;
        }

        return date('Ym', strtotime($month));
    }

    /**
     * 新建矫正数据
     * @throws CustomException
     * @throws \Exception
     */
    public function create()
    {
        // 校验参数
        $this->_validateParamsForCreate();

        // 生成参数
        $params = $this->_genParamsForCreate();

        // 入库
        MongoCorrectBillNumbers::create($params);
    }

    /**
     * 生成参数
     * @return array
     * @throws \Exception
     */
    private function _genParamsForCreate(): array
    {
        // 基本参数
        $params_base = $this->_genBaseParamsForCreate();

        // 调用量参数
        $params_amount = $this->_genAmountParamsForCreate();

        return array_merge($params_amount, $params_base);
    }

    /**
     * 调用量参数
     * @return array
     */
    private function _genAmountParamsForCreate(): array
    {
        list(
            $type, $updated_number, $source_number, $yd_updated_number, $yd_source_number, $lt_source_number, $lt_updated_number, $dx_source_number, $dx_updated_number
            ) = [
            trim(request()->post('type')),
            (int)trim(request()->post('updated_number')),
            (int)trim(request()->post('source_number')),
            (int)trim(request()->post('yd_updated_number')),
            (int)trim(request()->post('yd_source_number')),
            (int)trim(request()->post('lt_source_number')),
            (int)trim(request()->post('lt_updated_number')),
            (int)trim(request()->post('dx_source_number')),
            (int)trim(request()->post('dx_updated_number')),
        ];

        // 如果不区分运营商
        if ($type === 'common') {
            $data = compact('updated_number', 'source_number');
        } else {
            $data = compact(
                'dx_source_number',
                'dx_updated_number',
                'yd_source_number',
                'yd_updated_number',
                'lt_source_number',
                'lt_updated_number'
            );
        }

        return compact('type', 'data');
    }

    /**
     * 基本参数
     * @return array
     * @throws \Exception
     */
    private function _genBaseParamsForCreate(): array
    {
        list($customer_id, $account_id, $product_id, $standard_day, $details, $reason, $operator) = [
            trim(request()->post('customer_id')),
            trim(request()->post('account_id')),
            (int)trim(request()->post('product_id')),
            trim(request()->post('standard_day')),
            trim(request()->post('details')),
            trim(request()->post('reason')),
            trim(request()->post('operator')),
        ];

        // 日期格式化
        $month        = date('Ym', strtotime($standard_day));
        $standard_day = date('Ymd', strtotime($standard_day));

        // 唯一标识
        $uuid = Uuid::uuid4()
            ->toString();

        return compact(
            'uuid',
            'month',
            'standard_day',
            'operator',
            'customer_id',
            'account_id',
            'product_id',
            'details',
            'reason'
        );
    }

    /**
     * @throws CustomException
     */
    private function _validateParamsForCreate()
    {
        // 校验修正日期
        $this->_validateDateForCreate();

        // 校验客户 账号  产品是否选择
        $this->_validateBaseForCreate();

        // 校验是否实际用量 && 校正后用量是否合法
        $this->_validateAmountForCreate();

    }

    /**
     *  校验是否实际用量 && 校正后用量是否合法
     * @throws CustomException
     */
    private function _validateAmountForCreate()
    {
        // 是否区分运营商
        if (request()->post('type') === 'common') {
            $this->_validateCommonAmountForCreate();
            return;
        }

        // 区分运营商校验用量
        $this->_validateOperatorAmountForCreate();
    }

    /**
     * 区分运营商校验用量
     * @throws CustomException
     */
    private function _validateOperatorAmountForCreate()
    {
        list($yd_updated_number, $yd_source_number, $lt_source_number, $lt_updated_number, $dx_source_number, $dx_updated_number) = [
            trim(request()->post('yd_updated_number')),
            trim(request()->post('yd_source_number')),
            trim(request()->post('lt_source_number')),
            trim(request()->post('lt_updated_number')),
            trim(request()->post('dx_source_number')),
            trim(request()->post('dx_updated_number')),
        ];
        $not_exists = $yd_updated_number === '' || $yd_source_number === '' || $lt_source_number == '' || $lt_updated_number == '' || $dx_source_number == '' || $dx_updated_number == '';
        if ($not_exists) {
            throw new CustomException('请输入合法的实际用量和校正后用量');
        }
        if ((int)$yd_updated_number != $yd_updated_number) {
            throw new CustomException('移动校正后用量必须是整数');
        }

        if ((int)$yd_source_number != $yd_source_number) {
            throw new CustomException('移动实际用量必须是整数');
        }

        if ((int)$lt_updated_number != $lt_updated_number) {
            throw new CustomException('联通校正后用量必须是整数');
        }

        if ((int)$lt_source_number != $lt_source_number) {
            throw new CustomException('联通实际用量必须是整数');
        }

        if ((int)$dx_source_number != $dx_source_number) {
            throw new CustomException('电信实际用量必须是整数');
        }

        if ((int)$dx_updated_number != $dx_updated_number) {
            throw new CustomException('电信校正后用量必须是整数');
        }
    }

    /**
     * 不区分运营商校验用量
     * @throws CustomException
     */
    private function _validateCommonAmountForCreate()
    {
        list($updated_number, $source_number) = [
            trim(request()->post('updated_number')),
            trim(request()->post('source_number')),
        ];
        if ($updated_number == '' || $source_number == '') {
            throw new CustomException('请输入合法的实际用量和校正后用量');
        }

        if ((int)$updated_number != $updated_number) {
            throw new CustomException('校正后用量必须是整数');
        }

        if ((int)$source_number != $source_number) {
            throw new CustomException('实际用量必须是整数');
        }
    }

    /**
     * 校验客户 账号  产品是否选择
     * @throws CustomException
     */
    private function _validateBaseForCreate()
    {
        list($operator, $customer_id, $account_id, $product_id) = [
            trim(request()->post('operator')),
            trim(request()->post('customer_id')),
            trim(request()->post('account_id')),
            (int)trim(request()->post('product_id')),
        ];

        if (!$product_id || !$account_id || !$customer_id) {
            throw new CustomException('请输入合法的产品ID，账号ID, 客户ID');
        }

        if (!$operator) {
            throw new CustomException('请传递操作人');
        }
    }

    /**
     * 校验修正日期
     * @throws CustomException
     */
    private function _validateDateForCreate()
    {
        $standard_day = trim(request()->post('standard_day'));
        if (!$standard_day) {
            throw new CustomException('请选择校正日期');
        }

        // 是否选中的是上个月
        $month_standard = date('Ym', strtotime($standard_day));
        $month_last     = date('Ym', strtotime('first day of last month'));

        if ($month_last != $month_standard) {
            throw new CustomException('校正日期只是可以选择上个月的某一天');
        }
    }
}