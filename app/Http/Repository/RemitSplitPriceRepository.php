<?php

namespace App\Http\Repository;

use App\Models\Customer;
use App\Models\SystemUser;
use App\Models\RemitSplitPrice;
use App\Exports\RemitSplitPriceExport;
use App\Models\Common\CommonEnumModel;
use Exception;
use Maatwebsite\Excel\Facades\Excel;
use App\Repositories\AutoSplitPriceRepository;

class RemitSplitPriceRepository
{
    public function getSplitPriceList()
    {
        $where = $this->getParams();

        $page = intval(request()->post('page', 1));
        $limit = intval(request()->post('limit', 10));

        $data = RemitSplitPrice::getSplitPriceList($where, $page, $limit);

        $count_money = RemitSplitPrice::getSplitPriceCountAndTotalMoney($where);

        $count = isset($count_money['nums']) ? $count_money['nums'] : 0;
        $split_money = isset($count_money['total_money']) ? $count_money['total_money'] : 0;

        $costomer_list = Customer::select(['customer_id', 'name', 'company','salesman'])->get()->toArray();
        $costomer_list = array_column($costomer_list, null, 'customer_id');

        $user_data = SystemUser::select(['username', 'realname'])->where(['disabled' => 1])->get()->toArray();
        $user_data = array_column($user_data, 'realname', 'username');
        $sourceMap = CommonEnumModel::getListByType(1);
        $sourceMap = array_column($sourceMap, 'value', 'name');
        $user_dept_map =  $this->getUserDept();

        try {
            $list = [];
            foreach ($data as $value) {
                $info = [];
                $salesman = $costomer_list[$value['customer_id']]['salesman'];
                $info['receipt_serial'] = $value['receipt_serial'];
                $info['customer_name'] = $costomer_list[$value['customer_id']]['name'];
                $info['company'] = $costomer_list[$value['customer_id']]['company'];
                $info['product_name'] = $value['product_name'];
                $info['month'] = date('Y-m', strtotime($value['month'] . '01'));
                $info['money'] = $value['money'];
                $info['remit_date'] = date('Y-m-d', $value['remit_date']);
                $info['source'] = $value['source'];
                $info['source_name'] = isset($sourceMap[$value['source']]) ? $sourceMap[$value['source']] : '';
                $info['admin'] = isset($user_data[$value['admin']]) ? $user_data[$value['admin']] : '';

                $info['customer_id']=$value['customer_id'];
                $info['salesman'] = $salesman;
                $info['salesname'] = isset($user_data[$salesman]) ? $user_data[$salesman] : '';
                $info['area_person'] = isset($user_dept_map[$salesman])?$user_dept_map[$salesman]['dept_name']:'';
                $list[] = $info;
            }
        } catch (Exception $e) {
        }

        $total_money = RemitSplitPrice::getSlitPriceTotalMoney($where);

        return compact('list', 'count', 'split_money', 'total_money');
    }

    /**
     * 获取查询条件
     * @return array
     */
    public function getParams()
    {
        $receipt_serial = request()->post('receipt_serial', null);
        $product_id = intval(request()->post('product_id', null));
        $customer_id = request()->post('customer_id', null);
        $month = request()->post('month', null);
        $remit_date = request()->post('remit_date', null);
        $remit_date = is_string($remit_date) ? explode(',', $remit_date) : $remit_date;
        $source = request()->post('source', null);

        return compact('receipt_serial', 'product_id', 'customer_id', 'month', 'remit_date', 'source');
    }

    public function downloadSplitPrice()
    {
        $sourceMap = CommonEnumModel::getListByType(1);
        $sourceMap = array_column($sourceMap, 'value', 'name');
        $where = $this->getParams();
        $list = RemitSplitPrice::getSplitPriceList($where, 0, 0);
        $costomer_list = Customer::select(['customer_id', 'name', 'company','salesman'])->get()->toArray();
        $costomer_list = array_column($costomer_list, null, 'customer_id');
        $user_data = SystemUser::pluck('realname','username');
        $user_dept_map =  $this->getUserDept();
        foreach ($list as &$value) {
            $salesman = $costomer_list[$value['customer_id']]['salesman']??'';
            $value['customer_name'] = $costomer_list[$value['customer_id']]['name'];
            $value['company'] = $costomer_list[$value['customer_id']]['company'];
            $value['source_name'] = isset($sourceMap[$value['source']]) ? $sourceMap[$value['source']] : '';
            $value['customer_id']=$value['customer_id'];
            $value['salesman'] = $salesman;
            $value['salesname'] = isset($user_data[$salesman]) ? $user_data[$salesman] : '';
            $value['area_person'] = isset($user_dept_map[$salesman])?$user_dept_map[$salesman]['dept_name']:'';
        }

        return Excel::download(new RemitSplitPriceExport($list), 'remit_split_price.xlsx');
    }

    public function getUserDept()
    {
        $list = SystemUser::select(['*'])->leftJoin('crs_system_dept', 'crs_system_user.dept_id', '=', 'crs_system_dept.dept_id')->get()->toArray();
        return array_column($list, null, 'username');
    }
}
