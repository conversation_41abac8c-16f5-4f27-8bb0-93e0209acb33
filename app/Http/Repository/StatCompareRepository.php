<?php

namespace App\Http\Repository;

use App\Http\Controllers\StatProductController;
use App\Models\BillCost;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Models\Account;
use App\Models\BillProductIncomeV2;

class StatCompareRepository extends StatBaseRepository
{
    /**
     * 数据对比-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $usage_data = $this->getUsage($params); //调用量
        $income_data = $this->getIncome($params);   //收入
        $expend_data = $this->getExpend($params);   //特殊消耗
        $cost_data = $this->getCost($params);   //成本

        $data = $this->formatData($usage_data, $income_data, $expend_data, $cost_data);
        $data = $this->filterAuthMoneyByFatherid($data,$params['father_id']);
        return $data;
    }

    protected function getUsage($params){
        $where = $this->getWhere($params);
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        $usage_data = StatisticsCustomerUsage::getCompareList($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey'], $params['filter_product']);
        return $usage_data;
    }

    protected function getIncome($params)
    {
        $where = $this->getWhere($params);
        $income_data = BillProductIncomeV2::getCompareList($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey']);
        return $income_data;
    }

    protected function getExpend($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getCustomerExpendByDate(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer']);
        $expend_data['add'] = CustomerExpend::getCustomerExpendByDate(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer']);
        return $expend_data;
    }

    protected function getCost($params)
    {
        $where = $this->getWhere($params);
        $cost_data = BillCost::getCompareList($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey']);
        return $cost_data;
    }

    protected function getWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        return $where;
    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $show_money
     * @return array
     */
    private function formatData($usage_data, $income_data, $expend_data, $cost_data)
    {
        $data = [];
        foreach ($usage_data as $item){
            $data[$item['date']] = $item;
            $data[$item['date']]['money'] = $data[$item['date']]['number'] = $data[$item['date']]['cost'] = 0;
        }
        foreach ($income_data as $item){
            if (isset($data[$item['date']])){
                $data[$item['date']]['number'] += $item['number'];
                $data[$item['date']]['money'] = bcadd($data[$item['date']]['money'], $item['money'], $this->degree);
            }else{
                $data[$item['date']]['number'] = $item['number'];
                $data[$item['date']]['money'] = $item['money'];
            }
        }
        foreach ($expend_data['add'] as $item) {
            if (isset($data[$item['date']])){
                $data[$item['date']]['number'] += $item['number'];
                $data[$item['date']]['money'] = bcadd($data[$item['date']]['money'], $item['money'], $this->degree);
            }else{
                $data[$item['date']]['number'] = $item['number'];
                $data[$item['date']]['money'] = $item['money'];
            }

        }

        foreach ($expend_data['sub'] as $item) {
            if (isset($data[$item['date']])){
                $data[$item['date']]['number'] -= $item['number'];
                $data[$item['date']]['money'] = bcsub($data[$item['date']]['money'], $item['money'], $this->degree);
            }else{
                $data[$item['date']]['number'] = -$item['number'];
                $data[$item['date']]['money'] = bcsub(0, $item['money'], $this->degree);
            }

        }
        foreach ($cost_data as $item){
            if (isset($data[$item['date']])){
                $data[$item['date']]['cost'] = $item['money'];
            }
        }
        return $data;
    }
}
