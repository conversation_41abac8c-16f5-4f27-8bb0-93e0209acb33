<?php

namespace App\Http\Repository\Receiver;

use App\Models\Receipt;
use App\Models\Receiver\PushReceipt;
use App\Models\Receiver\PushReceiptLog;
use App\Repositories\FeishuRepository;
use App\Utils\Helpers\Func;
use Exception;


/**
 * 接受其他系统推送的数据
 */
class PushReceiptRepository
{

    /** @var string 用来加密解密的key 这个由财务后台提供 不变 */
    const SIGN = '5db5147d21268bdc4f9b89d3249a4be4f7e0602ada6bda1a05100f979e2b6d07';
    const OPERATE_REMIT  = 0;//0【需要对应业务线 认款】
    const OPERATE_DELETE = 1;//1【需要对应业务线 删除】

    /**
     * 获取推送收款单
     *
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-06-17 19:48:25
     */
    public static function get_push_receipt() {
        $task_id = request()->post('task_id');
        $data = request()->post('data');
        PushReceiptLog::insert_log($task_id,$data);

        // $data = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1tITv9AmH4I1lvD-dt0IA_Ob6x2kh9MLJvuyzXk9OCU';

        $data = self::decode_jwt($data);
        // $data['operate'] = 1;

        //查询task_id是否已经存在,查询 operate 状态是否相同
        $res = PushReceipt::getOneByTaskId($task_id);

        $now = time();
        $date_now = date("Y-m-d H:i:s",$now);
        $data['updated_at'] = $date_now;

        $data = PushReceipt::filter_column($data);
        if(!empty($res)){
            //更新数据
            self::update_data($task_id,$data);
        }else{
            //更新数据
            $data['created_at'] = $date_now;
            self::insert_data($task_id,$data);
        }

        if($data['operate'] == self::OPERATE_REMIT) {//新添加的企服收款单
            $receipt_serial = $data['trade_id'];
            $receipt_info = Receipt::getInfoByReceiptSerialAndStatus($receipt_serial);

            if(empty($receipt_info)) {
                $receiptdata                   = [];
                $receiptdata['receipt_serial'] = $receipt_serial;
                $receiptdata['name']           = $data['payer_name'];   //付方名称
                $receiptdata['account']        = $data['payer_account'];//付方账号
                $receiptdata['money']          = $data['trade_money'];
                $receiptdata['bank']           = $data['payer_open_bank'];  //付方开户行
                $receiptdata['remit_date']     = $data['trade_time'];       //交易时间
                $receiptdata['status']         = Receipt::$STATUS_INIT;     //初始化
                $receiptdata['create_time']    = $now;
                $receiptdata['admin']          = 'push_receipt';//添加
                $receiptdata['source']         = 0;
                Receipt::insert($receiptdata);
            }
        }else{
            //删除
            Receipt::updateWithCondition([['receipt_serial','=',$data['trade_id']]],['delete_time' => time()]);
        }

        return $task_id;
    }


    /**
     * 添加
     * 添加到收款单表中
     *
     * @param $task_id
     * @param $data
     *
     * @return void
     * <AUTHOR> 2024-06-17 19:59:19
     */
    private static function insert_data($task_id, $data){
        $data['task_id'] = $task_id;

        PushReceipt::insert($data);
    }

    /**
     * 添加
     * 添加到收款单表中
     *
     * @param $task_id
     * @param $data
     *
     * @return void
     * <AUTHOR> 2024-06-17 19:59:19
     */
    private static function update_data($task_id, $data){
        PushReceipt::where([['task_id','=',$task_id]])->update($data);
    }


    public static function getInfoByTradeId($trade_id){
        return PushReceipt::getInfoByTradeId($trade_id);
    }


    /**
     * @throws Exception
     */
    public static function push_remit($record_id, $receipt_serial, $money, $company, $salesman){
        $data = [
            'record_id'     => $record_id,// 上文推送接口数据
            'accept_number' => $receipt_serial,// 流水号/生成的流水号
            'charge_id'     => $receipt_serial,// 流水号/生成的流水号
            'money'         => $money,// 认款金额 (保证金额是字符串)
            'product_type'  => '金融数据',// 产品类型 固定值
            'accept_shop'   => $company,// 认款商户
            'sell_user'     => $salesman,// 销售
            'operate'       => 0,// int 0 认款时值为 0
        ];

        $jwt = self::encode_jwt($data);

        $url = config("push_receipt.accountant_api_domain")."/v1/bill_pay/bill_push_finanical";
        $post_data = [
            'record_id' => $record_id,
            'money'     => $money,
            'sign'      => $jwt,
        ];

        $retry_times = 0;

        do { //重试3次
            $post_res = self::curlRequestForPost($url, json_encode($post_data));
            if ($post_res['code'] != 200) {
                $retry_times ++;
            }else{
                break;
            }
        } while ($retry_times < 3);

        if($retry_times >= 3){
            $feishu = new FeishuRepository();
            $mess = [
                'data'      => $data,
                'post_data' => $post_data,
                'post_res'  => $post_res,
            ];
            $feishu->send_card_message_to_chat_group("财务同步认款失败! 流水号:".$receipt_serial,$mess);
            // 这里不抛出异常 完成后台功能优先
            // throw new Exception("财务同步认款失败! 流水号:".$receipt_serial);
        }
    }




    /**
     * 解析数据
     *
     * @param $jwt
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-06-18 18:13:32
     */
    private static function decode_jwt($jwt){

        // 用于签名的密钥
        $secretKey = self::SIGN;

        // 分割JWT
        [$headerBase64,$payloadBase64, $signatureBase64] = explode('.',$jwt);

        // 解码头部和有效载荷
        // $header = json_decode(Func::base64url_decode($headerBase64), true);
        $payload = json_decode(Func::base64url_decode($payloadBase64), true);

        // 重新计算签名
        $signature = Func::base64url_decode($signatureBase64);
        $expectedSignature = hash_hmac('sha256',$headerBase64.'.'.$payloadBase64,$secretKey,true);

        // dd(Func::base64url_encode($signature),Func::base64url_encode($expectedSignature));

        // 比较签名
        if (!self::hash_equals($signature,$expectedSignature)) {
            // echo "JWT验证失败!";
            throw new Exception("JWT验证失败!");
        }

        return $payload;
    }


    /**
     * jwt编码加密
     *
     * @return string
     * <AUTHOR> 2024-06-24 15:48:05
     *
     */
    private static function encode_jwt($payload){
        $header = '{"alg":"HS256","typ":"JWT"}';

        $payload = json_encode($payload);

        $secretKey = self::SIGN;

        $headerBase64 = Func::base64url_encode($header);
        $payloadBase64 = Func::base64url_encode($payload);

        $expectedSignature = hash_hmac('sha256',$headerBase64.'.'.$payloadBase64, $secretKey,true);

        $signBase64 = Func::base64url_encode($expectedSignature);

        return $headerBase64.'.'.$payloadBase64.'.'.$signBase64;
    }


    // 辅助函数来安全地比较哈希值
    private static function hash_equals($knownString,$userString) {
        if (strlen($knownString) !== strlen($userString)) {
            return false;
        }
        $result = 0;
        for ($i = 0;$i < strlen($knownString);$i++) {
            $result |= ord($knownString[$i]) ^ ord($userString[$i]);
        }
        return $result === 0;
    }


    /**
     * 发送post请求
     * @param $url
     * @param $post_data
     * @return array|mixed
     */
    protected static function curlRequestForPost($url, $post_data) {
        if (is_array($post_data)) {
            $post_data = http_build_query($post_data);
        }

        $headers = [
            'Content-Type: application/json',
        ];

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($curl, CURLOPT_TIMEOUT, 20);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $list_stat = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            //将返回结果和接口返回做相同接口处理
            return [
                'code' => -1,
                'msg'  => curl_error($curl)
            ];
        }
        curl_close($curl);

        if (is_string($list_stat)) {
            return json_decode($list_stat, true);
        }

        return $list_stat;
    }
}
