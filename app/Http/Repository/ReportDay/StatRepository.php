<?php

namespace App\Http\Repository\ReportDay;

use App\Define\StatDefine;
use App\Models\Common\CommonEnumModel;
use App\Models\Monitor\PeriodSwitchRecord;
use App\Models\Monitor\PeriodUseRecord;
use App\Models\PeriodCompareResult;
use App\Models\RemitSplitPrice;
use App\Models\ReportDay\StatisticsSummaryDailyKeyProductIncome;
use App\Models\ReportDay\StatisticsSummaryDailyMonthIncome;
use App\Models\ReportDay\StatisticsSummaryDailyProdcutCategory;
use App\Models\ReportDay\StatisticsSummaryDailySnapshotData;
use App\Models\SystemAccessLog;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Income\MainRepository;
use App\TraitUpgrade\CurlTrait;
use App\Http\Repository\StatBaseRepository;
use Illuminate\Support\Facades\DB;
use App\Util\Func;

class StatRepository extends StatBaseRepository
{
    use CurlTrait;

    public function getReportList($date){

        try{
            $list = StatisticsSummaryDailyProdcutCategory::getListByCondition(['date' => $date]);
            $base = [
                'category_name' => '合计', 'show_prodcut_name' => '合计', 'category_merge_count' => 1,
                'total' => 0, 'income' => 0, 'profit' => 0, 'year_income' => 0, 'year_profit' => 0
            ];

            $category_data = [];//产品分类收入表格数据
            $amount = $base;//合计信息
            $every_category_data = [];//每个产品分类的小记信息
            foreach ($list as $item){
                $amount['total'] = bcadd($amount['total'], $item['total'], 0);//日总调用量
                $amount['income'] = bcadd($amount['income'], $item['income'], 2);//日收入
                $amount['profit'] = bcadd($amount['profit'], $item['profit'], 2);//日毛利
                $amount['year_income'] = bcadd($amount['year_income'], $item['year_income'], 2);//当年收入
                $amount['year_profit'] = bcadd($amount['year_profit'], $item['year_profit'], 2);//当年毛利

                $category_name = $item['category_name'];
                $category_data[$category_name][] = [
                    'category_name' => $category_name,
                    'category_merge_count' => 0,
                    'show_prodcut_name' => $item['show_prodcut_name'],
                    'total' => $item['total'],
                    'income' => $item['income'],
                    'profit' => $item['profit'],
                    'year_income' => $item['year_income'],
                    'year_profit' => $item['year_profit'],
                ];

                $this->sumEveryCategoryData($every_category_data, $item);
            }

            unset($list);

            $category_table = [];
            $is_add_cat_merge = [];//是否添加分类小计
            foreach ($category_data as $cat_name => $cat_item){
                if(isset($every_category_data[$cat_name]) && !isset($is_add_cat_merge[$cat_name])){
                    $every_category_data[$cat_name]['category_merge_count'] += 1;//多了小计一行 记得加上
                    $category_table[] = $this->formatRow($every_category_data[$cat_name]);
                    $is_add_cat_merge[$cat_name] = true;
                }
                foreach ($cat_item as $item){
                    $category_table[] = $this->formatRow($item);
                }
            }
            unset($category_data);
            unset($is_add_cat_merge);

            //补充合计信息
            array_unshift($category_table, $this->formatRow($amount));

            //卡片数据
            $month_data = $this->getCurrentMonthIncome($date);//获取当前月收入数据
            //获取当前日收入变化量和变化率等指标
            $currentDateChange = $this->getCurrentDayIncomeChangeRate($date, $amount['income'], $amount['profit']);
            //获取当前月收入变化量和变化率等指标
            $currentMonthChange = $this->getCurrentMonthIncomeChangeRate($date, $month_data['income'], $month_data['profit']);
            //获取当前年收入变化量和变化率等指标
            $currentYearChange = $this->getCurrentYearIncomeChangeRate($date, $amount['year_income'], $amount['year_profit']);

            $cardData = [
                'income' => $this->formatNumStr($amount['income']),
                'profit' => $this->formatNumStr($amount['profit']),
                'currentDateChange' => $currentDateChange,
                'currentMonthChange' => $currentMonthChange,
                'currentYearChange' => $currentYearChange,
                'month_income' => $this->formatNumStr($month_data['income']),
                'month_profit' => $this->formatNumStr($month_data['profit']),
                'year_income' => $this->formatNumStr($amount['year_income']),
                'year_profit' => $this->formatNumStr($amount['year_profit'])
            ];

            //年度任务完成进度数据
            $task_data = $this->getTaskProgressInfo($date, $amount['year_income']);

            //回款完成进度数据
            $remit_data = $this->getRemitProgressInfo($date, $amount['year_income']);

            //top10客户收入情况
            $customerTOP10Income = $this->getCustomerIncomeTOP10TableData($date);

            //重点产品客户收入数据
            $key_product_data = $this->getKeyProductIncomData($date);

            //客户收入变化率数据
            $change_rate_data = $this->getCustomerIncomeChangeRateData($date);

            return ['cardData' => $cardData, 'task_data' => $task_data, 'remit_data' => $remit_data,
                'customer_top10_income' => $customerTOP10Income, 'key_product_data' => $key_product_data,
                'change_rate_data' => $change_rate_data, 'category_table' => $category_table
            ];

        }catch(\Exception $e){
            //dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendExceptionNotice($this, $e);
        }

    }


    public function getCustomerIncomeChangeRateData($date){

        $list = StatisticsSummaryDailySnapshotData::getListByCondition(['date' => $date, 'type' => StatisticsSummaryDailySnapshotData::TYPE_CUSTOMER_INCOME_CHANGE_RATE]);
        if(empty($list)){
            return [];
        }

        $every_category_count = [];
        $is_add_cat_merge = [];//是否合并分类计算
        foreach ($list as $item){
            if($item['extend3'] == 'up_topn'){
                $cate_name = '增长top3';
            }else{
                $cate_name = '减少top3';
            }

            if(isset($every_category_count[$cate_name])){
                $every_category_count[$cate_name] +=1;
            }else{
                $every_category_count[$cate_name] = 1;
            }
        }

        $result  = [];
        foreach ($list as $item){
            if($item['extend3'] == 'up_topn'){
                $cate_name = '增长top3';
            }else{
                $cate_name = '减少top3';
            }

            if(isset($is_add_cat_merge[$cate_name])){
                $category_merge_count = 0;
            }else{
                $is_add_cat_merge[$cate_name] = true;
                $category_merge_count = $every_category_count[$cate_name] ?? 1;
            }

            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($item['customer_id']) ?: '未知';
            $change_income = bcsub($item['income'], $item['extend1'], 2);
            if($change_income < 0){
                $change_income = bcsub(0, $change_income, 2);
            }
            if($item['extend2'] < 0){
                $change_ratio = bcsub(0, $item['extend2'], 2);
            }else{
                $change_ratio = $item['extend2'];
            }
            $result[] = [
                'cate_name' => $cate_name,//类别
                'category_merge_count' => $category_merge_count,
                'customer_name' => $customer_name,
                'current_income' => $this->formatNumStr($item['income']),//当前统计日收入
                'pre_income' => $this->formatNumStr($item['extend1']),//比对日收入
                'change_income' => $this->formatNumStr($change_income),//变化量
                'change_ratio' => $change_ratio.'%',//变化率
            ];
        }

        return $result;

    }

    public function getCustomerIncomeTOP10TableData($date){
        $list = StatisticsSummaryDailySnapshotData::getListByCondition(['date' => $date, 'type' => StatisticsSummaryDailySnapshotData::TYPE_CUSTOMER_TOP10_INCOME]);
        $result = [];
        foreach ($list as $item){
            //日top10客户
            if($item['customer_id'] == '合计'){
                $day_customer_name = '合计';
            }else{
                $day_customer_name = RedisCache::instance('customerId_customerName_mapping')->get($item['customer_id']);
            }
            //日top10客户收入
            $day_customer_income = $this->formatNumStr($item['income']);
            //日top10客户收入占比
            $day_customer_ratio = $item['extend1'];


            //年top10客户
            if($item['extend2'] == '合计'){
                $year_customer_name = '合计';
            }else{
                $year_customer_name = RedisCache::instance('customerId_customerName_mapping')->get($item['extend2']);
            }
            //年top10客户收入
            $year_customer_income = $this->formatNumStr($item['year_income']);
            //年top10客户收入占比
            $year_customer_ratio = $item['extend3'];

            $result[] = [
                'day_customer_name' => $day_customer_name,
                'day_customer_income' => $day_customer_income,
                'day_customer_ratio' => $day_customer_ratio,
                'year_customer_name' => $year_customer_name,
                'year_customer_income' => $year_customer_income,
                'year_customer_ratio' => $year_customer_ratio,
            ];
        }

        unset($list);
        return $result;
    }

    public function getKeyProductIncomData($date){
        $list = StatisticsSummaryDailyKeyProductIncome::getListByCondition(['date' => $date]);

        $base = [
            'show_prodcut_name' => '合计', 'customer_name' => '合计', 'prodcut_name_merge_count' => 1,
            'total' => 0, 'income' => 0, 'profit' => 0, 'month_income' => 0, 'month_profit' => 0
        ];

        $category_data = [];//重点产品分类收入表格数据
        $amount = $base;//合计信息
        $every_category_data = [];//重点产品每个产品分类的小记信息
        foreach ($list as $item){
            $amount['total'] = bcadd($amount['total'], $item['total'], 0);//日总调用量
            $amount['income'] = bcadd($amount['income'], $item['income'], 2);//日收入
            $amount['profit'] = bcadd($amount['profit'], $item['profit'], 2);//日毛利
            $amount['month_income'] = bcadd($amount['month_income'], $item['month_income'], 2);//当月收入
            $amount['month_profit'] = bcadd($amount['month_profit'], $item['month_profit'], 2);//当月毛利

            $show_prodcut_name = $item['show_prodcut_name'];
            $category_data[$show_prodcut_name][] = [
                'show_prodcut_name' => $show_prodcut_name,
                'prodcut_name_merge_count' => 0,
                'customer_name' => RedisCache::instance('customerId_customerName_mapping')->get($item['customer_id']),
                'total' => $item['total'],
                'income' => $item['income'],
                'profit' => $item['profit'],
                'month_income' => $item['month_income'],
                'month_profit' => $item['month_profit'],
            ];

            $this->sumEveryKeyProductData($every_category_data, $item);
        }

        unset($list);

        $category_table = [];
        $is_add_cat_merge = [];//是否添加分类小计
        foreach ($category_data as $cat_name => $cat_item){
            if(isset($every_category_data[$cat_name]) && !isset($is_add_cat_merge[$cat_name])){
                $every_category_data[$cat_name]['prodcut_name_merge_count'] += 1;//多了小计一行 记得加上
                $category_table[] = $this->formatRow($every_category_data[$cat_name]);
                $is_add_cat_merge[$cat_name] = true;
            }
            foreach ($cat_item as $item){
                $category_table[] = $this->formatRow($item);
            }
        }
        unset($category_data);
        unset($is_add_cat_merge);

        //补充合计信息
        array_unshift($category_table, $this->formatRow($amount));

        return $category_table;
    }

    public function getTaskProgressInfo($date, $finish_task = 0){
        //年度任务暂定固定值
        $year_task = config('params.year_task_money');
        //未完成任务 = 年度任务 - 完成任务(年权责收入)
        $unfinish_task = bcsub($year_task, $finish_task, 2);

        //计算完成任务占比
        $task_ratio = (float)bcmul(bcdiv($finish_task, $year_task, 4), 100, 1);

        //计算时间进度占比
        $start_date = substr($date, 0, 4).'0101';
        $start_time = strtotime($start_date);
        $end_time = strtotime('+1 years', $start_time);
        $current_time = strtotime($date);
        //已过完天数
        $past_days = ($current_time - $start_time)/86400;
        //一年总天数
        $total_days = ($end_time - $start_time)/86400;
        $time_ratio = (float)bcmul(bcdiv($past_days, $total_days, 4), 100, 1);

        return ['finish_task' => $this->formatNumStr($finish_task), 'unfinish_task' => $this->formatNumStr($unfinish_task), 'task_ratio' => $task_ratio, 'time_ratio' => $time_ratio];
    }

    public function getRemitProgressInfo($date, $year_income = 0){
        //回款数据中 企服产品回款也减半 和企服产品收入减半 保持一致
        $start_month = date('Y', strtotime($date)).'01';//开始月份
        $end_time =  strtotime('+1 days', strtotime($date));//开始月份

        $select = [
            'receipt.source as source',
            DB::raw('SUM(IF(remit_split_price.product_id=3100, remit_split_price.`money`/2, remit_split_price.`money`)) as money')
        ];

        $remit = RemitSplitPrice::select($select)
        ->leftJoin('receipt', 'receipt.receipt_serial', '=', 'remit_split_price.receipt_serial')
            ->where('remit_split_price.delete_time', '=' , 0)
            ->where('remit_split_price.create_time', '<' , $end_time)
            ->where('remit_split_price.month', '>=' , $start_month)
            ->where('receipt.delete_time', '=' , 0)
            ->groupBy(['receipt.source'])
            ->get()->toArray();
        $remit = array_column($remit, 'money', 'source');
        $remit_money = array_sum($remit);

        //未回款 = 当前年收入 - 已回款
        $unremit_money = bcsub($year_income, $remit_money, 2);
        //计算回款占比
        if($year_income <= 0){
            $remit_ratio = 0;
        }else{
            $remit_ratio = (float)bcmul(bcdiv($remit_money, $year_income, 4), 100, 1);
        }

        //获取所有来源配置
        $source_map = CommonEnumModel::getTypeMaps(1);

        $rep = new MainRepository();
        $res = $rep->getBaseIncome(StatDefine::INCOME_DIMENSION_SOURCE, 20240101, 20240903, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_qifu_income' => 1]);
        if(isset($res['status']) && $res['status']==0){
            $year_source_income = array_column($res['data'], 'income', 'source');
        }else{
            $year_source_income = [];
        }

        $unremit_source_array = [];
        foreach ($source_map as $source_number => $source_name){
            //羽乐科技重命名为直客
            if($source_number == 0){
                $source_name = '直客';
            }
            $income = $year_source_income[$source_number] ?? 0;//对应来源的收入
            $remit_income = $remit[$source_number] ?? 0;//对应来源的回款
            if($income == 0 && $remit_income == 0){
                continue;
            }
            //对应来源未回款 = 对应来源的收入 - 对应来源的回款
            $unremit_income = bcsub($income, $remit_income, 2);
            if($unremit_income < 0){
                #todo 报警
            }else{
                $unremit_source_array[$source_name] = $unremit_income;
            }
        }

        arsort($unremit_source_array);

        $unremit_detail = [];
        foreach ($unremit_source_array as $name => $money){
            $money = $this->formatNumStr($money);
            $unremit_detail[] = ['name' => $name, 'money' => $money];
        }

        unset($unremit_source_array);
        return ['remit_money' => $this->formatNumStr($remit_money), 'unremit_money' => $this->formatNumStr($unremit_money), 'remit_ratio' => $remit_ratio, 'unremit_detail' => $unremit_detail];
    }

    public function getCurrentMonthIncome($date){
        $current_month = date('Ym', strtotime($date));
        $list = StatisticsSummaryDailyMonthIncome::getListByCondition(['date' => $date]);
        $month = ['income' => 0, 'profit' => 0];
        foreach ($list as $item){
            if($item['month'] != $current_month){
                continue;
            }
            $month['income'] = bcadd($month['income'], $item['income'], 2);//月收入
            $month['profit'] = bcadd($month['profit'], $item['profit'], 2);//月毛利
        }

        return $month;
    }

    public function getCurrentDayIncomeChangeRate($date, $current_income = 0, $current_profit = 0){
        //获取前一日收入数据
        $res = StatisticsSummaryDailySnapshotData::where(['date' => $date, 'type' => StatisticsSummaryDailySnapshotData::TYPE_YESTERERDAY_INCOME])
            ->first();
        $yesterday_income = $res['income'] ?? 0;
        $yesterday_profit = $res['profit'] ?? 0;

        $diff_money = bcsub($current_income, $yesterday_income, 2);

        //如果为负 转化成正数
        if($diff_money <= 0){
            $diff_money = bcsub(0, $diff_money, 2);
            $income_diff_type = 'down';
        }else{
            $income_diff_type = 'up';
        }

        if($yesterday_income <= 0){
            $income_ratio = 0;
        }else{
            $income_ratio = bcdiv($diff_money, $yesterday_income, 4);
            $income_ratio = bcmul($income_ratio, 100, 2);
        }

        $diff_profit = bcsub($current_profit, $yesterday_profit, 2);
        //如果为负 转化成正数
        if($diff_profit <= 0){
            $diff_profit = bcsub(0, $diff_profit, 2);
            $profit_diff_type = 'down';
        }else{
            $profit_diff_type = 'up';
        }

        if($yesterday_profit <= 0){
            $profit_ratio = 0;
        }else{
            $profit_ratio = bcdiv($diff_profit, $yesterday_profit, 4);
            $profit_ratio = bcmul($profit_ratio, 100, 2);
        }

        return [
            'income_diff' => $this->formatNumStr($diff_money), 'income_ratio' => $income_ratio, 'income_diff_type' => $income_diff_type,
            'profit_diff' => $this->formatNumStr($diff_profit), 'profit_ratio' => $profit_ratio, 'profit_diff_type' => $profit_diff_type,
        ];
    }

    public function getCurrentMonthIncomeChangeRate($date, $current_income = 0, $current_profit = 0){
        //获取比较月收入数据
        $res = StatisticsSummaryDailySnapshotData::where(['date' => $date, 'type' => StatisticsSummaryDailySnapshotData::TYPE_COMPAREMONTH_INCOME])
            ->first();
        $month_income = $res['month_income'] ?? 0;
        $month_profit = $res['month_profit'] ?? 0;

        $diff_money = bcsub($current_income, $month_income, 2);

        //如果为负 转化成正数
        if($diff_money <= 0){
            $diff_money = bcsub(0, $diff_money, 2);
            $income_diff_type = 'down';
        }else{
            $income_diff_type = 'up';
        }

        if($month_income <= 0){
            $income_ratio = 0;
        }else{
            $income_ratio = bcdiv($diff_money, $month_income, 4);
            $income_ratio = bcmul($income_ratio, 100, 2);
        }

        $diff_profit = bcsub($current_profit, $month_profit, 2);
        //如果为负 转化成正数
        if($diff_profit <= 0){
            $diff_profit = bcsub(0, $diff_profit, 2);
            $profit_diff_type = 'down';
        }else{
            $profit_diff_type = 'up';
        }

        if($month_profit <= 0){
            $profit_ratio = 0;
        }else{
            $profit_ratio = bcdiv($diff_profit, $month_profit, 4);
            $profit_ratio = bcmul($profit_ratio, 100, 2);
        }

        return [
            'month_income_diff' => $this->formatNumStr($diff_money), 'month_income_ratio' => $income_ratio, 'month_income_diff_type' => $income_diff_type,
            'month_profit_diff' => $this->formatNumStr($diff_profit), 'month_profit_ratio' => $profit_ratio, 'month_profit_diff_type' => $profit_diff_type,
        ];
    }

    public function getCurrentYearIncomeChangeRate($date, $current_income = 0, $current_profit = 0){
        //获取比较月收入数据
        $res = StatisticsSummaryDailySnapshotData::where(['date' => $date, 'type' => StatisticsSummaryDailySnapshotData::TYPE_COMPAREYEAR_INCOME])
            ->first();
        $year_income = $res['year_income'] ?? 0;
        $year_profit = $res['year_profit'] ?? 0;

        $diff_money = bcsub($current_income, $year_income, 2);

        //如果为负 转化成正数
        if($diff_money <= 0){
            $diff_money = bcsub(0, $diff_money, 2);
            $income_diff_type = 'down';
        }else{
            $income_diff_type = 'up';
        }

        if($year_income <= 0){
            $income_ratio = 0;
        }else{
            $income_ratio = bcdiv($diff_money, $year_income, 4);
            $income_ratio = bcmul($income_ratio, 100, 2);
        }

        $diff_profit = bcsub($current_profit, $year_profit, 2);
        //如果为负 转化成正数
        if($diff_profit <= 0){
            $diff_profit = bcsub(0, $diff_profit, 2);
            $profit_diff_type = 'down';
        }else{
            $profit_diff_type = 'up';
        }

        if($year_profit <= 0){
            $profit_ratio = 0;
        }else{
            $profit_ratio = bcdiv($diff_profit, $year_profit, 4);
            $profit_ratio = bcmul($profit_ratio, 100, 2);
        }

        return [
            'year_income_diff' => $this->formatNumStr($diff_money), 'year_income_ratio' => $income_ratio, 'year_income_diff_type' => $income_diff_type,
            'year_profit_diff' => $this->formatNumStr($diff_profit), 'year_profit_ratio' => $profit_ratio, 'year_profit_diff_type' => $profit_diff_type,
        ];
    }

    public function sumEveryKeyProductData(&$data, $row){
        $show_prodcut_name = $row['show_prodcut_name'];
        if(!isset($data[$show_prodcut_name]['show_prodcut_name'])){
            $data[$show_prodcut_name]['show_prodcut_name'] = $show_prodcut_name;
        }

        if(!isset($data[$show_prodcut_name]['prodcut_name_merge_count'])){
            $data[$show_prodcut_name]['prodcut_name_merge_count'] = 1;
        }else{
            $data[$show_prodcut_name]['prodcut_name_merge_count'] +=1;
        }

        if(!isset($data[$show_prodcut_name]['customer_name'])){
            $data[$show_prodcut_name]['customer_name'] = '小计';
        }

        if(!isset($data[$show_prodcut_name]['total'])){
            $data[$show_prodcut_name]['total'] = $row['total'];
        }else{
            $data[$show_prodcut_name]['total'] += $row['total'];
        }

        if(!isset($data[$show_prodcut_name]['income'])){
            $data[$show_prodcut_name]['income'] = $row['income'];
        }else{
            $data[$show_prodcut_name]['income'] = bcadd($data[$show_prodcut_name]['income'], $row['income'], 2);
        }

        if(!isset($data[$show_prodcut_name]['profit'])){
            $data[$show_prodcut_name]['profit'] = $row['profit'];
        }else{
            $data[$show_prodcut_name]['profit'] = bcadd($data[$show_prodcut_name]['profit'], $row['profit'], 2);
        }

        if(!isset($data[$show_prodcut_name]['month_income'])){
            $data[$show_prodcut_name]['month_income'] = $row['month_income'];
        }else{
            $data[$show_prodcut_name]['month_income'] = bcadd($data[$show_prodcut_name]['month_income'], $row['month_income'], 2);
        }

        if(!isset($data[$show_prodcut_name]['month_profit'])){
            $data[$show_prodcut_name]['month_profit'] = $row['month_profit'];
        }else{
            $data[$show_prodcut_name]['month_profit'] = bcadd($data[$show_prodcut_name]['month_profit'], $row['month_profit'], 2);
        }

        return true;
    }

    public function sumEveryCategoryData(&$data, $row){
        $category_name = $row['category_name'];
        if(!isset($data[$category_name]['category_name'])){
            $data[$category_name]['category_name'] = $category_name;
        }

        if(!isset($data[$category_name]['category_merge_count'])){
            $data[$category_name]['category_merge_count'] = 1;
        }else{
            $data[$category_name]['category_merge_count'] +=1;
        }

        if(!isset($data[$category_name]['show_prodcut_name'])){
//            $data[$category_name]['show_prodcut_name'] = '小计';
            $data[$category_name]['show_prodcut_name'] = $category_name.'-小计';
        }

        if(!isset($data[$category_name]['total'])){
            $data[$category_name]['total'] = $row['total'];
        }else{
            $data[$category_name]['total'] += $row['total'];
        }

        if(!isset($data[$category_name]['income'])){
            $data[$category_name]['income'] = $row['income'];
        }else{
            $data[$category_name]['income'] = bcadd($data[$category_name]['income'], $row['income'], 2);
        }

        if(!isset($data[$category_name]['profit'])){
            $data[$category_name]['profit'] = $row['profit'];
        }else{
            $data[$category_name]['profit'] = bcadd($data[$category_name]['profit'], $row['profit'], 2);
        }

        if(!isset($data[$category_name]['year_income'])){
            $data[$category_name]['year_income'] = $row['year_income'];
        }else{
            $data[$category_name]['year_income'] = bcadd($data[$category_name]['year_income'], $row['year_income'], 2);
        }

        if(!isset($data[$category_name]['year_profit'])){
            $data[$category_name]['year_profit'] = $row['year_profit'];
        }else{
            $data[$category_name]['year_profit'] = bcadd($data[$category_name]['year_profit'], $row['year_profit'], 2);
        }

        return true;
    }


    public function formatRow($row){
        if(isset($row['total'])){
            $row['total'] = $this->formatNumStr($row['total']);
        }
        if(isset($row['income'])){
            $row['income'] = $this->formatNumStr($row['income']);
        }
        if(isset($row['profit'])){
            $row['profit'] = $this->formatNumStr($row['profit']);
        }
        if(isset($row['month_income'])){
            $row['month_income'] = $this->formatNumStr($row['month_income']);
        }
        if(isset($row['month_profit'])){
            $row['month_profit'] = $this->formatNumStr($row['month_profit']);
        }
        if(isset($row['year_income'])){
            $row['year_income'] = $this->formatNumStr($row['year_income']);
        }
        if(isset($row['year_profit'])){
            $row['year_profit'] = $this->formatNumStr($row['year_profit']);
        }

        return $row;
    }

    protected function formatNumStr($num)
    {
        $return = $num;
        if ($num >= 1000 || $num <= -1000) {
            $return = sprintf ( "%.1f", $num / 10000 ) . "万";
        }else{
            $return = round($return, 2);
        }
        return $return;
    }

    public function getDateType($date, $holiday){
        $weekdayNumber = date('w', strtotime($date));
        //0代表星期日，1代表星期一，以此类推，6代表星期六
        //$weekdays = ['日', '一', '二', '三', '四', '五', '六'];

        //判断统计日为工作日(work_date)还是休息日(free_date)
        if(in_array($date, $holiday['free_date'])){
            $type = 'free_date';
        }else if(in_array($date, $holiday['work_date'])){
            $type = 'work_date';
        }else if(in_array($weekdayNumber, [0, 6])){
            $type = 'free_date';
        }else if(in_array($weekdayNumber, [1, 2, 3, 4, 5])){
            $type = 'work_date';
        }else {
            $type = 'work_date';
        }

        return $type;
    }

    public function getHolidayConfig(){
        //假期配置包含假期休息日期、和假期调休工作日期
        $free_date = [
            //元旦
            20250101,
            //春节
            20250128, 20250129, 20250130, 20250131, 20250201, 20250202, 20250203, 20250204,
            //清明
            20250404, 20250405, 20250406,
            //五一
            20250501, 20250502, 20250503, 20250504, 20250505,
            //端午节
            20250531, 20250601, 20250602,
            //中秋国庆
            20251001, 20251002, 20251003, 20251004, 20251005, 20251006, 20251007, 20251008,
        ];

        $work_date = [
            //春节
            20250126, 20250208,
            //五一
            20250427,
            //中秋国庆
            20250928, 20251011,
        ];

        return ['free_date' => $free_date, 'work_date' => $work_date];
    }

    /*
    public function createJwtToken($secretKey, $userInfo, $exp = 3600*24*7){
        $payload = [
            'iss' => 'wgl',  // 签发者
            'iat' => time(),    // 签发时间
            'exp' => time() + $exp, // 过期时间（默认为7天）
            'data' => [         // 自定义数据
                'name' => $userInfo['name'],
                'email' => $userInfo['email']
            ]
        ];

        $token = $this->encodeJwt($payload, $secretKey);

        return $token;
    }

    public function encodeJwt($payload, $secretKey) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode($payload);
        $token = base64_encode($header) . '.' . base64_encode($payload);
        $signature = hash_hmac('sha256', $token, $secretKey, true);
        $token .= '.' . base64_encode($signature);

        return $token;
    }

    public function decodeJwt($token, $secretKey) {
        list($header64, $payload64, $signature64) = explode('.', $token);
        //$header = json_decode(base64_decode($header64), JSON_OBJECT_AS_ARRAY);
        $payload = json_decode(base64_decode($payload64), JSON_OBJECT_AS_ARRAY);
        $signature = base64_decode($signature64);
        $expectedSignature = hash_hmac('sha256', "$header64.$payload64", $secretKey, true);
        if (hash_equals($signature, $expectedSignature)) {
            return $payload;
        }

        return [];
    }


    public function addRequestLog($username, $uri, $content){
        $row['username'] = $username;
        $row['uri'] = $uri;
        $row['content'] = json_encode($content, JSON_UNESCAPED_UNICODE);
        $row['created_at'] = time();

        return SystemAccessLog::insert($row);
    }
    */

}