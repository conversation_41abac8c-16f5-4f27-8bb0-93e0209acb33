<?php

namespace App\Http\Repository;

//use App\Models\SystemUser;
use App\Models\ApprovalRole;
use App\Models\ChannelCostMinconsumeSpread;
use App\Models\Crs\SystemUser;
use App\Models\Approval;
use App\Models\ApprovalConfig;
use App\Utils\Helpers\Func;
use App\Models\ApprovalRequest;
use Exception;

class ApprovalRepository
{
    //1审批中，2审批通过，3审批驳回，4撤销
    const STATUS_APPROVAL = 1;
    const STATUS_PASS     = 2;
    const STATUS_REJECT   = 3;
    const STATUS_CANCEL   = 4;

    public function approvalList(): array {
        $where = $this->getParams();
        $page  = intval(request()->post('page', 1));
        $limit = intval(request()->post('limit', 10));

        $data = Approval::getList($where, (($page - 1) * $limit), $limit);
        $approval_role = ApprovalRole::getAll([]);
        $approval_role_map = array_column($approval_role,'approver','role');

        $users = [];
        try {
            $list = [];
            foreach ($data['list'] as $value) {
                if($value['applicant'] && !array_key_exists($value['applicant'],$users)){
                    $users[$value['applicant']] = $value['applicant'];
                }
                if($value['approver'] && !array_key_exists($value['approver'],$users)) {
                    $users[$value['approver']] = $value['approver'];
                }
                if (isset($approval_role_map[$value['approval_role']])){
                    $value['can_approval'] = $approval_role_map[$value['approval_role']];
                }else{
                    $value['can_approval'] = '';
                }

                $list[] = $value;
            }
        } catch (Exception $e) { }
        $user_infos = SystemUser::getInfosByUserName(array_merge($users));
        foreach($user_infos as $v){
            $users[$v['username']] = $v['realname'];
        }
        $count = $data['count'];
        return compact('list', 'count','users');
    }

    /**
     * 获取查询条件
     * @return array
     */
    public function getParams(): array {
        $parmas = [];
        $customer_id = request()->post('customer',  null);
        $account_id  = request()->post('account',  null);
        $product_id  = request()->post('product',   null);
        $op_type     = request()->post('op_type',   null);
        $status      = request()->post('status',    null);
        $create_at   = request()->post('create_at', null);
        $user_cookie = request()->post('user_cookie', null);

        $user_name = Func::getUserNameFromCookie($user_cookie);

        if (!empty($customer_id)) {
            $parmas[] = ['customer_id','=', $customer_id];
        }
        if (!empty($product_id)) {
            $parmas[] = ['product_id', '=', $product_id];
        }
        if (!empty($account_id)) {
            $parmas[] = ['account_id','=', $account_id];
        }
        if ($status > 0) {
            $parmas[] = ['status','=', $status];
        }
        if (!empty($create_at)) {
            $parmas[] = ['create_at', '>=', $create_at[0]];
            $parmas[] = ['create_at', '<=', $create_at[1]];
        }


        if($user_name !== ''){
            $tab_type = request()->post('tab_type', null);
            if($tab_type == 'approver'){//审批人
                //获取有权审批的审批人角色
                $approvalRoles = ApprovalRole::getRole($user_name);
                if ($approvalRoles){
                    $parmas[] = ['approval_role','in',array_column($approvalRoles,'role')];
                }else{
                    $parmas[] = ['approval_role','=', ''];
                }
            }else if($tab_type == 'applicant'){//申请人
                $parmas[] = ['applicant','=',$user_name];
            }
        }

        if (!empty($op_type)) {
            $parmas[] = ['op_type','=', $op_type];
        }

        return $parmas;
    }


    /**
     * 通过审批/驳回
     *
     * @param string|null $sysName 审批人
     *
     * @return array|bool
     * @throws Exception
     * @since 2024-01-09 10:36:11 修伟<<EMAIL>>
     */
    public function approvalDeal(string $sysName = null) {
        $params = request()->post();
        if (!isset($sysName)) {
            return ['msg' => '非法操作', 'code' => 50002, 'data' => []];
        }
        $where['id'] = $params['id'];
        unset($params['user_cookie']);
        $params['approve_at'] = time();
        $params['approver'] = $sysName;

        $bmp_urls = config("approval.bmp_urls");

        $info = Approval::find($params['id']);
        $requestInfo= ApprovalRequest::where('flow_no',$info['flow_no'])->first();
        //请求数据/审批通过待处理数据用于添加,更新等操作
        $data = json_decode($requestInfo['data'], true);


        // 审核通过 bmp请求重新处理 finance-manage-api调用相应审批通过方法
        if($params['status'] == self::STATUS_PASS){
            //需要请求bmp的审批
            if(in_array($requestInfo['url'], $bmp_urls)){
                // 先处理请求
                $yulore_finance_domain = config('approval.yulore_finance_domain');
                $url = $yulore_finance_domain . '/' . $requestInfo['url'];

                $data['approval_token'] = $params['id'];
                $res                    = $this->postData($url, $data);
            }else{
                $res = $this->finance_manage_api_deal($requestInfo['url'],$data);
            }

            ApprovalRequest::where('flow_no', $info['flow_no'])->update(['op_result' => $res]);
        }

        if($params['status'] == self::STATUS_REJECT) {
            if(!in_array($requestInfo['url'], $bmp_urls)){
                $res = $this->finance_manage_api_reject($requestInfo['url'],$data,$params['approval_comments']);
                ApprovalRequest::where('flow_no', $info['flow_no'])->update(['op_result' => $res]);
            }
        }

        if($params['status'] == self::STATUS_CANCEL){
            $info = Approval::find($params['id']);
            if($sysName != $info['applicant']){
                throw new Exception("操作异常!");
            }
            unset($params['approver']);
            unset($params['approve_at']);

            //撤销时需要更新
            if(!in_array($requestInfo['url'], $bmp_urls)){
                $res = $this->finance_manage_api_cancel($requestInfo['url'],$data);
                ApprovalRequest::where('flow_no', $info['flow_no'])->update(['op_result' => $res]);
            }
        }

        return Approval::updateData($where, $params);
    }

    public function getApprovalConfig($user_name): array {
        $data = ApprovalConfig::getConfig();

        $op_types = [];
        $approver = [];
        foreach($data as $info){
            $op_types[] = [
                'op_type' => $info['node_url'],
                'name' => $info['name'],
            ];
            $tmp_approver = explode(",",trim($info['approver'],", \t\n\r\0\x0B"));
            foreach($tmp_approver as $ap){
                if(!array_key_exists($ap, $approver)){
                    $approver[$ap] = $ap;
                }
            }
        }

        return ['op_types' => $op_types,'approver' => array_keys($approver),'whoami' => $user_name];
    }

    /**
     * 发送post请求
     *
     * @param string       $url
     * @param array|string $post_data
     * @param null         $header
     *
     * @return bool|string
     */
    public static function postData(string $url, array $post_data, $header=null)
    {
        if (is_array($post_data)) {
            $post_data = http_build_query($post_data);
        }

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        if($header){
            curl_setopt($curl, CURLOPT_HEADER, FALSE);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        // 20s 超时
        curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 3);
        curl_setopt($curl, CURLOPT_TIMEOUT, 3);

        //设置post方式提交
        curl_setopt($curl, CURLOPT_POST, 1);
        // post参数
        curl_setopt($curl, CURLOPT_POSTFIELDS, $post_data);
        $response = curl_exec($curl);

        // curl 出错
        if (curl_errno($curl)) {
            return json_encode([
                'success' => false,
                'status'  => 1748, // 设置出错的标识
                'code'    => 1378, // 设置出错标识
                'msg'     => curl_error($curl),
            ]);
        }
        curl_close($curl);

        return $response;
    }


    /**
     * 添加审批
     *
     * @param $customer_id
     * @param $account_id
     * @param $product_id
     * @param $uri
     * @param $data
     * @param $apply_content
     * @param $user_cookie
     *
     * @return bool
     * <AUTHOR> 2024-01-08 16:25:33
     *
     * @static
     */
    public static function addApproval($customer_id,$account_id,$product_id,$uri,$data,$apply_content,$user_cookie): bool {
        $user_name = Func::getUserNameFromCookie($user_cookie);

        $now = time();
        $flow_no = self::getFlowNo();
        $approval_role = 'tech'; // 默认由技术审批
        $res = ApprovalConfig::getApproverByNodeUrl($uri);
        if ($res) {
            $approval_role = $res['approver'];
        }

        $appr['flow_no']       = $flow_no;
        $appr['applicant']     = $user_name;
        $appr['approver']      = '';//审批人
        $appr['op_type']       = $uri;
        $appr['status']        = self::STATUS_APPROVAL;
        $appr['apply_content'] = $apply_content;//比较差异的文本 这个值需要自定义
        $appr['customer_id']   = $customer_id;
        $appr['account_id']    = $account_id;
        $appr['product_id']    = $product_id;
        $appr['approval_role']    = $approval_role;
        $appr['create_at']     = $now;
        $appr['update_at']     = $now;

        Approval::insert($appr);

        $apprequest['flow_no']   = $flow_no;
        $apprequest['url']       = $uri;
        $apprequest['status']    = self::STATUS_APPROVAL;
        $apprequest['op_result'] = '';

        //审批通过后需要根据该数据进行相应操作 不转义中文字符和斜线
        $apprequest['data']      = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $apprequest['create_at'] = $now;
        $apprequest['update_at'] = $now;

        return ApprovalRequest::insert($apprequest);
    }


    /**
     * 批量添加审批
     *
     * @param $approval_data
     * @param $user_cookie
     * @return bool
     */
    public static function batchAddApproval($approval_data,$user_cookie): bool {
        if (!$approval_data){
            return  true;
        }
        $now = time();
        $user_name = Func::getUserNameFromCookie($user_cookie);
        $appr = [];
        $apprequest = [];
        foreach ($approval_data as $data){
            $flow_no = self::getFlowNo();
            $approval_role = 'tech'; // 默认由技术审批
            $res = ApprovalConfig::getApproverByNodeUrl($data['uri']);
            if ($res) {
                $approval_role = $res['approver'];
            }
            $appr[] = [
                'flow_no'=>$flow_no,
                'applicant'=>$user_name,
                'approver'=>'',
                'op_type'=>$data['uri'],
                'status'=>self::STATUS_APPROVAL,
                'apply_content'=>$data['apply_content'] ?? '',
                'customer_id'=>$data['customer_id'] ?? '',
                'account_id'=>$data['account_id'] ?? '',
                'product_id'=>$data['product_id'] ?? '',
                'approval_role'=> $approval_role,
                'create_at'=>$now,
                'update_at'=>$now,
            ];

            $apprequest[] = [
                'flow_no' => $flow_no,
                'url' => $data['uri'] ?? '',
                'status' => self::STATUS_APPROVAL,
                'op_result' => '',
                'data' => json_encode($data['data'] ?? '', JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                'create_at'=>$now,
                'update_at'=>$now,
            ];
        }
        Approval::insert($appr);

        return ApprovalRequest::insert($apprequest);
    }



    /**
     * 审批流水号 11位字符+13位时间戳
     *
     * 按原规则生成,将相关的函数从bmp迁移过来
     *
     * @return string
     * <AUTHOR> 2024-01-08 18:13:50
     */
    private static function getFlowNo(): string {
        [$t1, $t2] = explode(' ', microtime());
        return  Func::randString(11) . (float) sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
    }

    /**
     * 非bmp审批通过执行该操作,调用相应方法
     *
     * @param $url
     * @param $data
     *
     * @return bool
     * <AUTHOR> 2024-01-09 11:44:31
     */
    private function finance_manage_api_deal($url, $data): bool {
        switch ($url){
            case Approval::URL_BXF_STRATEGY_ADD://邦信分添加策略
                $res = BxfStrategyRepository::approval_deal_add($data);
                break;
            case Approval::URL_BXF_STRATEGY_EDIT://邦信分添加策略
                $res = BxfStrategyRepository::approval_deal_edit($data);
                break;
            case Approval::URL_BXF_STRATEGY_DEL://邦信分删除策略
                $res = BxfStrategyRepository::approval_deal_del($data);
                break;
            case Approval::URL_BXF_STRATEGY_CONFIG_ADD://邦信分添加策略
                $res = BxfStrategyConfigRepository::approval_deal_add($data);
                break;
            case Approval::URL_BXF_STRATEGY_CONFIG_EDIT://邦信分添加策略
                $res = BxfStrategyConfigRepository::approval_deal_edit($data);
                break;
            case Approval::URL_BXF_STRATEGY_CONFIG_DEL://邦信分添加策略
                $res = BxfStrategyConfigRepository::approval_deal_del($data);
                break;
            case Approval::URL_CONTRACT_FILE_DOWNLOAD://合同管理 文件下载
                $res = ContractRepositorie::deal_download_permission($data);
                break;
            case Approval::URL_BILL_EXPEND_SAVE:
                $res = BillV3Repository::saveExpendByApproval($data);
                break;
            case Approval::URL_CHANNEL_COST_ADJUST: //渠道成本调整
                $res = ChannelCostAdjustRepository::saveCostAdjustByApproval($data);
                break;
            case Approval::URL_CHANNEL_COST_SPREAD: //渠道成本保底分摊
                $res = ChannelCostMinconsumeSpread::addRecordByApproval($data);
                break;
            case Approval::URL_CONTRACT_DEL: //合同管理 删除合同
                $res = ContractRepositorie::realDel($data);
                break;
            default:
                break;
        }

        return $res;
    }


    /**
     * 非bmp审批通过执行该操作,调用相应方法
     *
     * @param $url
     * @param $data
     *
     * @return bool
     * <AUTHOR> 2024-01-09 11:44:31
     */
    private function finance_manage_api_reject($url, $data, $approval_comments): bool {
        switch ($url){
            case Approval::URL_CONTRACT_FILE_DOWNLOAD://合同管理 文件下载
                $res = ContractRepositorie::reject_download_permission($data,$approval_comments);
                break;
            case Approval::URL_BILL_EXPEND_SAVE:
                $res = true;
                break;
            default:
                break;
        }

        return $res;
    }


    /**
     * 非bmp审批取消执行该操作,调用相应方法
     *
     * @param $url
     * @param $data
     *
     * @return bool
     * <AUTHOR> 2024-06-04 16:46:14
     */
    private function finance_manage_api_cancel($url, $data): bool {
        switch ($url){
            case Approval::URL_CONTRACT_FILE_DOWNLOAD://合同管理 文件下载
                $res = ContractRepositorie::cancel_download_permission($data);
                break;
            case Approval::URL_BILL_EXPEND_SAVE:
                $res = true;
                break;
            default:
                break;
        }

        return $res;
    }
}
