<?php

namespace App\Http\Repository;

use App\Jobs\UpdateProductDayAmountJob;
use App\Models\MongoInvokedLog;
use App\Models\ProductAmount;

class ProductAmountRepository
{
    /*
     * 缓存的天数
     *
     * */
    private $cache_days = 1;
    private $data;

    /*
     * 放入的队列
     * */
    private $queue_update_day_amount = 'queue-day-amount-update';

    /*
     * 连接
     * */
    private $queue_connection = 'redis_backend';

    /*
     * redis连接
     * */
    private $redis_connection = 'db_backend';

    /**
     * 统计数据
     *
     * @param array $data
     *
     * @throws \Exception
     * @return array
     */
    public function stat(array $data): array
    {
        $this->setData($data);

        // 校验
        $this->validateParams();

        //请求日志

        // 返回
        return $this->responseClient();
    }

    /**
     * 返回
     */
    private function responseClient(): array
    {
        // 容器
        $list_container = [];

        $product_id = $this->data['product_id'];
        $day        = date('Ymd', strtotime($this->data['amount_date']));
        collect($this->data['data'])->each(function ($increment, $apikey) use ($product_id, $day, &$list_container) {
            // 如果在缓存中
            if ($this->determineIfCache($apikey, $product_id, $day)) {
                $list_container[$apikey] = $this->dealAmountWhenHasCache($apikey, $product_id, $day, $increment);

                return;
            }
            $list_container[$apikey] = $this->dealAmountWithOutCache($apikey, $product_id, $day, $increment);
        });

        // job
        dispatch((new UpdateProductDayAmountJob($this->data))
            ->onConnection($this->queue_connection)
            ->onQueue($this->queue_update_day_amount)
        );

        return $list_container;
    }

    /**
     * 当没有缓存的时候处理计数
     *
     * @param string $apikey
     * @param string $product_id
     * @param string $day
     * @param int    $increment
     *
     * @return array
     */
    private function dealAmountWithOutCache(string $apikey, string $product_id, string $day, int $increment): array
    {
        $cache_key = 'product_amount_'.$product_id.'_'.$apikey.'_'.$day;

        // 获取数据库对应的数据
        $cache_data = $this->getCacheData(compact('apikey', 'product_id'), $day);

        // 处理缓存信息
        $cache_add_increment_data = $this->dealCacheDo($increment, $cache_key, $cache_data);

        // 设置生成周期
        app('redis')->connection($this->redis_connection)->expire($cache_key, $this->cache_days * 86400);

        return $cache_add_increment_data;
    }

    /**
     * 获取缓存信息
     *
     * @param array  $where
     * @param string $day 传递的日期
     *
     * @return array
     */
    private function getCacheData(array $where, string $day): array
    {
        // 日期限制
        $day_search   = date('Y-m-d', strtotime($day));
        $month_search = date('Y-m', strtotime($day));
        $year_search  = date('Y', strtotime($day));

        // 组合条件
        $where_day   = [$day_search, $day_search];
        $where_month = [$month_search.'-01', $month_search.'-31'];
        $where_year  = [$year_search.'-01-01', $year_search.'-12-31'];

        $daily_amount = $this->aggregateSumAmount($where, $where_day);
        $month_amount = $this->aggregateSumAmount($where, $where_month);
        $year_amount  = $this->aggregateSumAmount($where, $where_year);
        $total_amount = $this->aggregateSumAmount($where, [date('0000-01-01'), '9999-12-31']);

        return compact('daily_amount', 'month_amount', 'year_amount', 'total_amount');
    }

    /**
     * 聚合获取某段时间的总和
     *
     * @param array $where
     * @param array $limit_days
     *
     * @return int
     */
    private function aggregateSumAmount(array $where, array $limit_days): int
    {
        return (int)ProductAmount::where($where)
            ->whereBetween('amount_date', $limit_days)
            ->sum('daily_amount');
    }

    /**
     * 处理有缓存的时候的计数
     *
     * @param string $apikey
     * @param string $product_id
     * @param string $day
     * @param int    $increment
     *
     * @return array
     */
    private function dealAmountWhenHasCache(string $apikey, string $product_id, string $day, int $increment): array
    {
        // 获取缓存信息
        $cache_key  = 'product_amount_'.$product_id.'_'.$apikey.'_'.$day;
        $cache_data = app('redis')->connection($this->redis_connection)->hGetAll($cache_key);

        // 处理缓存信息
        return $this->dealCacheDo($increment, $cache_key, $cache_data);
    }

    /**
     * 对cache的处理
     *
     * @param int    $increment
     * @param string $cache_key
     * @param        $cache_data
     *
     * @return array
     */
    private function dealCacheDo(int $increment, string $cache_key, $cache_data)
    {
        $cache_add_increment_data = array_map(function ($old_number) use ($increment) {
            return $old_number + $increment;
        }, $cache_data);

        // 重置
        app('redis')->connection($this->redis_connection)->hMset($cache_key, $cache_add_increment_data);

        return $cache_add_increment_data;
    }

    /**
     * 是否在缓存中
     *
     * @param string $apikey
     * @param string $product_id
     * @param string $day
     *
     * @return bool
     */
    private function determineIfCache(string $apikey, string $product_id, string $day): bool
    {
        $cache_key = 'product_amount_'.$product_id.'_'.$apikey.'_'.$day;

        return app('redis')->connection($this->redis_connection)->exists($cache_key);
    }

    /**
     * @throws \Exception
     */
    private function validateParams()
    {
        $node_area   = $this->getNodeArea();
        $product_id  = $this->data['product_id'] ?? '';
        $amount_date = $this->data['amount_date'] ?? '';
        $data        = $this->data['data'] ?? '';

        if (!$node_area || !$product_id || !$amount_date || !$data) {
            throw new \Exception('缺少必填参数');
        }
    }

    /**
     * 获取节点
     * @return string
     */
    private function getNodeArea(): string
    {
        $node_area = $this->data['node_area'] ?? '';

        return $node_area ?: node_area();
    }

    /**
     * @param mixed $data
     */
    private function setData(array $data)
    {
        $this->data = $data;
    }
}
