<?php

namespace App\Http\Repository;

use App\Models\Account;
use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\ChannelRemit;
use App\Models\CldcRouteAiCallback;
use App\Models\CldcRouteAiImport;
use App\Models\CustomerExpend;
use App\Models\StatisticsInterfaceUsage;
use App\Models\UpstreamBillAdjust;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\UserChannelConfig;
use App\Providers\RedisCache\RedisCache;
use DateTime;


class StatCldcAiRepository extends StatBaseRepository
{

    /**
     *
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        // 查询导入数据
        $import_where = $this->getWhere();

        $import_lists = CldcRouteAiImport::getListByOptions($import_where);

        if ($import_lists) {

            //查询回调数据
            $callback_where = $this->getWhere(true);
            $callback_lists = CldcRouteAiCallback::getListByOptions($callback_where);

            // 回调数据
            $callback_data = [];
            if ($callback_lists) {
                foreach ($callback_lists as $item) {
                    $key = $item['task_id'] . '_' . $item['channel_tag'] . '_' . $item['apikey']; // 拼接多个字段
                    if (isset($callback_data[$key])) {
                        // 如果键已存在，合并需要相加的字段
                        $callback_data[$key]['call_count'] += $item['call_count'];
                        $callback_data[$key]['through'] += $item['through'];
                        $callback_data[$key]['ab_count'] += $item['ab_count'];
                        $callback_data[$key]['duration'] += $item['duration'];
                        $callback_data[$key]['user_hung_up_count'] += $item['user_hung_up_count'];
                        $callback_data[$key]['bill_minutes'] += $item['bill_minutes'];
                        $callback_data[$key]['send_msg_count'] += $item['send_msg_count'];
                        $callback_data[$key]['send_msg_success_count'] += $item['send_msg_success_count'];

                    } else {
                        // 如果键不存在，直接添加
                        $callback_data[$key] = $item;
                    }
                }
            }


            $channels = $this->getChannelInfos();
            $accounts = $this->getAccountInfos();

            $channel_map = array_column($channels, 'label', 'channel_tag');
            $account_map = array_column($accounts, 'account_name', 'apikey');

            $data_list = [];
            $res_data = [];
            foreach ($import_lists as $item) {
                $key = $item['task_id'] . '_' . $item['channel_tag'] . '_' . $item['apikey']; // 拼接多个字段

                $res_data[$key]['id'] = $item['id'];
                $res_data[$key]['task_id'] = $item['task_id'];
                $res_data[$key]['task_name'] = $item['task_name'];
                $res_data[$key]['apikey'] = $item['apikey'];
                $res_data[$key]['account_name'] = isset($account_map[$item['apikey']]) ? $account_map[$item['apikey']] : '';
                $res_data[$key]['channel_label'] = isset($channel_map[$item['channel_tag']]) ? $channel_map[$item['channel_tag']] : '';
                $res_data[$key]['task_create_time'] = $item['task_create_time'];
                $res_data[$key]['total'] = $item['total'];

                //回调数据
                $res_data[$key]['call_count'] = isset($callback_data[$key]) ? $callback_data[$key]['call_count'] : 0;
                $res_data[$key]['through'] = isset($callback_data[$key]) ? $callback_data[$key]['through'] : 0;
                $res_data[$key]['ab_count'] = isset($callback_data[$key]) ? $callback_data[$key]['ab_count'] : 0;
                $res_data[$key]['duration'] = isset($callback_data[$key]) ? $callback_data[$key]['duration'] : 0;
                $res_data[$key]['user_hung_up_count'] = isset($callback_data[$key]) ? $callback_data[$key]['user_hung_up_count'] : 0;
                $res_data[$key]['bill_minutes'] = isset($callback_data[$key]) ? $callback_data[$key]['bill_minutes'] : 0;
                $res_data[$key]['send_msg_count'] = isset($callback_data[$key]) ? $callback_data[$key]['send_msg_count'] : 0;
                $res_data[$key]['send_msg_success_count'] = isset($callback_data[$key]) ? $callback_data[$key]['send_msg_success_count'] : 0;

                //计算占比
                // 进度
                $res_data[$key]['progress'] = $res_data[$key]['call_count'] . '/' . $res_data[$key]['total'];
                //接通率
                $res_data[$key]['call_rate'] = $this->getRatio($res_data[$key]['through'], $res_data[$key]['total']) . '%';
                //A/B意向率
                $res_data[$key]['ab_rate'] = $this->getRatio($res_data[$key]['ab_count'], $res_data[$key]['through']) . '%';
                // 短信触达率
                $res_data[$key]['sms_rate'] = $this->getRatio($res_data[$key]['send_msg_success_count'], $res_data[$key]['send_msg_count']) . '%';
                //平均通话时长
                $res_data[$key]['avg_duration'] = $this->getAvgRatio($res_data[$key]['duration'], $res_data[$key]['through']);
                //挂机率
                $res_data[$key]['hung_rate'] = $this->getRatio($res_data[$key]['user_hung_up_count'], $res_data[$key]['call_count']) . '%';

                $data_list[] = $res_data[$key];
            }

            return  $data_list;

        } else { // 没有导入数据
            return [];
        }

    }


    private function getRatio($through, $total)
    {
        // 安全计算比率
        if ($total != 0) {
            $ratio = round($through * 100 / $total, 2);  // 四舍五入保留2位小数
            // 或者使用 number_format 会补全小数位
            // $ratio = number_format($through / $total, 2, '.', '');
        } else {
            $ratio = 0;  // 或者其他默认值如 null, 'N/A' 等
        }

        return $ratio;
    }


    private function getAvgRatio($through, $total)
    {
        // 安全计算比率
        if ($total != 0) {
            $ratio = round($through/ $total, 2);  // 四舍五入保留2位小数
            // 或者使用 number_format 会补全小数位
            // $ratio = number_format($through / $total, 2, '.', '');
        } else {
            $ratio = 0;  // 或者其他默认值如 null, 'N/A' 等
        }

        return $ratio;
    }


    // 获取options
    public function getOptions()
    {
        // 获取任务信息
        $tasks = CldcRouteAiImport::getAllTask();

        // 获取渠道信息
        $channels = $this->getChannelInfos();

        // 获取账号信息
        $accounts = $this->getAccountInfos();

        $res = ['tasks' => $tasks, 'channels' => $channels, 'accounts' => $accounts];

        return $res;

    }


    // 获取渠道信息
    private function getChannelInfos()
    {
        $channel_tags = CldcRouteAiImport::getUniqueChannelTag();

        $channels = Channel::getChannelByNames($channel_tags);

        return $channels;
    }

    // 获取账号信息
    private function getAccountInfos()
    {
        $apikeys = CldcRouteAiImport::getUniqueApikey();

        $accounts = Account::getAccountInfosByApikey($apikeys);

        return $accounts;
    }


    // $contain_callback  是否包含callback_date 参数
    private function getWhere($contain_callback = false)
    {
        $where = [];
        $task_id = request()->post('task_id');
        $apikey = request()->post('apikey');
        $channel_tag = request()->post('channel_tag');
        $start_import_date = request()->post('start_import_date');
        $end_import_date = request()->post('end_import_date');
        $start_callback_date = request()->post('start_callback_date');
        $end_callback_date = request()->post('end_callback_date');


        if ($task_id) {
            $where[] = ['task_id', $task_id];
        }

        if ($apikey) {
            $where[] = ['apikey', $apikey];
        }

        if ($channel_tag) {
            $where[] = ['channel_tag', $channel_tag];
        }

        if ($start_import_date) {
            $where[] = ['import_date', '>=', date('Y-m-d', strtotime($start_import_date))];
        }

        if ($end_import_date) {
            $where[] = ['import_date', '<=', date('Y-m-d', strtotime($end_import_date))];
        }

        if ($contain_callback) {

            if ($start_callback_date) {
                $where[] = ['callback_date', '>=', date('Y-m-d', strtotime($start_callback_date))];
            }

            if ($end_callback_date) {
                $where[] = ['callback_date', '<=', date('Y-m-d', strtotime($end_callback_date))];
            }
        }

        return $where;

    }



}