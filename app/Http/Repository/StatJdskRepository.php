<?php

namespace App\Http\Repository;

use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;
use App\Utils\Helpers\Func;

class StatJdskRepository extends StatBaseRepository
{
    /**

     */
    public function getJdsk()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        //排除掉主接口调用量
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');

        $usage_data = $this->getJdskUsage($params); //调用量

        $chad_rate = $this->getChadRate($params); //查得率

        $data = $this->formatData($usage_data,$chad_rate,$params);

        return $data;
    }


    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @param $customerIds
     * @return mixed
     * @throws \Exception
     */
    private function formatData($usage_data,$chad_rate,$params)
    {
        $data = [];
       // $out_of_the_field = config('params.out_of_the_field');
        foreach ($usage_data as $item){
            $father_id = Product::getDisplayFatherId($item['product_id']);
            if ($father_id == $item['product_id']){
                continue;
            }
            //临时处理,wangning-0921
            if ($father_id == 200) {
                continue;
            }
            if (!isset($data['main_product'][$father_id])) {
                $data['main_product'][$father_id]['date'] = substr($params['start_date'],-4).'-'.substr($params['end_date'],-4);
                $data['main_product'][$father_id]['total'] = 0;
                $data['main_product'][$father_id]['valid'] = 0;
                $data['main_product'][$father_id]['product_name'] = Product::getProductNameByid($father_id);
            }


            $data['main_product'][$father_id]['total'] += $item['total'];
            $data['main_product'][$father_id]['valid'] += $item['valid'];

            if (!isset($data['total'])){
                $data['total']['total'] = 0;
                $data['total']['success'] = 0;
                $data['total']['valid'] = 0;
            }
            $data['total']['total'] += $item['total'];
            $data['total']['valid'] += $item['valid'];


            $data['product'][$item['product_id']]['date'] = substr($params['start_date'],-4).'-'.substr($params['end_date'],-4);
            $data['product'][$item['product_id']]['product_name'] = Product::getProductNameByid($father_id);
            $data['product'][$item['product_id']]['out_of_the_field'] = Product::getProductEnname($item['product_id']);
            $data['product'][$item['product_id']]['total'] = $item['total'];
            $data['product'][$item['product_id']]['valid'] = $item['valid'];
            $data['product'][$item['product_id']]['notvalid'] = ($item['total'] - $item['valid']);
            $data['product'][$item['product_id']]['valid_rate'] = round(($item['valid']/$item['total']), 2)*100 .'%';

        }


        $data['total']['valid_rate'] = round( ($data['total']['valid']/$data['total']['total']), 2)*100 .'%';

        foreach ($data['main_product'] as &$v){
            $v['notvalid'] = ($v['total'] - $v['valid']);
            $v['valid_rate'] = round(($v['valid']/$v['total']), 2)*100 .'%';
        }


        foreach ($chad_rate as $ch){
            $father_id = Product::getDisplayFatherId($ch['product_id']);
            if ($father_id == $ch['product_id']){
                continue;
            }
            //临时处理,wangning-0921
            if ($father_id == 200) {
                continue;
            }
            if (!isset($temp[$father_id]['data'][$ch['date']])){
                $temp[$father_id]['product_name'] = Product::getProductNameByid($father_id);
                $temp[$father_id]['data'][$ch['date']]['total'] = 0;
                $temp[$father_id]['data'][$ch['date']]['valid'] = 0;
            }
            $temp[$father_id]['data'][$ch['date']]['total'] += $ch['total'];
            $temp[$father_id]['data'][$ch['date']]['valid'] += $ch['valid'];
        }

        foreach( $temp as &$value){
            foreach ($value['data'] as $k=> &$da){
                $da['date'] = substr($k,-4);
                $da['valid_rate'] = round(($da['valid']/$da['total']), 2)*100;
            }
        }

        foreach ($temp as &$te){
            $te['date'] = array_column($te['data'],'date');
            $te['valid_rate'] =  array_column($te['data'],'valid_rate');
        }
        $data['valid_rate'] = $temp;

        return $data;
    }

    //查询主产品及旗下子产品
     private function dealwith($productIds)
     {
         $data = [];
         foreach ($productIds as $v) {
             $father_id = Product::getDisplayFatherId($v['product_id']);
             if ($father_id == $v['product_id']) {
                 continue;
             }
             $data[$father_id][] = $v['product_id'];
         }

         return $data;
     }

}

