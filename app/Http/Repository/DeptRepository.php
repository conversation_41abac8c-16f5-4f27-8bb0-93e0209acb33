<?php

namespace App\Http\Repository;

use App\Models\SystemUser;
use App\Models\DeptGrade;
use App\Models\Dept;
use App\Utils\Helpers\Func;

class DeptRepository
{

    /** @var array 所有商务部门id */
    private static $sale_dept_ids = [];

    /** @var array 所有商务 */
    private static $all_salesman  = [];

    /** @var array 某个商务的下属商务,辅助递归存储商务,不需要处理返回值 */
    private static $underling_salesman = [];
    private static $underling_realname = [];
    private static $underling_dept = [];
    /** @var array 商务 => ['realname' => 商务名字,'area' => 部门名字] */
    private static $area_person = [];


    /**
     * 获取商务的下属
     *
     * @param $salesman
     *
     * @static
     * @return array
     * <AUTHOR> 2024-03-22 16:23:42
     */
    public static function getUnderLing($salesman){
        $userInfo = SystemUser::getUserInfoByName($salesman);
        $dept = self::getSaleDeptRecursion($userInfo['dept_id'],$userInfo['is_leader']);
        $dept = $dept['dept_infos'];

        self::$underling_salesman = [];
        self::$underling_realname = [];
        self::$underling_dept     = [];
        self::get_underling_salesman($dept);

        return [
            'salesman' => self::$underling_salesman,
            'dept'     => self::$underling_dept,
            'realname' => self::$underling_realname,
        ];
    }

    /**
     * 获取部门的商务
     *
     * @param $dept_id
     *
     * @return array
     * @static
     * <AUTHOR> 2024-03-22 16:23:42
     */
    public static function getDeptSalesmans($dept_id){
        $dept = self::getSaleDeptRecursion($dept_id,true);
        $dept = $dept['dept_infos'];

        self::$underling_salesman = [];
        self::$underling_dept     = [];
        self::get_underling_salesman($dept);

        return self::$underling_salesman;
    }


    /**
     * 获取全部销售
     *
     * @return array
     * <AUTHOR> 2024-03-26 14:41:29
     *
     * @static
     */
    public static function getAllSalesman(){
        return self::getDeptSalesmans(Dept::SALE_DEPT_ID);
    }

    /**
     * 获取商务的下属
     *
     * @return array
     * @static
     * <AUTHOR> 2024-03-22 16:23:42
     */
    public static function getSalesDept(){
        $dept = self::getSaleDeptRecursion(Dept::SALE_DEPT_ID,true);
        $dept = $dept['dept_infos'];

        self::$underling_salesman = [];
        self::$underling_dept     = [];
        self::get_underling_salesman($dept);

        return self::$underling_dept;
    }

    /**
     * 获取商务对应的名字和区域信息
     *
     * @return array
     * @static
     * <AUTHOR> 2024-03-22 16:23:42
     */
    public static function getSalesmanDeptMap(){
        $dept = self::getSaleDeptRecursion(Dept::SALE_DEPT_ID,true);
        $dept = $dept['dept_infos'];

        self::$underling_salesman = [];
        self::$underling_dept     = [];
        self::$area_person        = [];
        self::get_underling_salesman($dept);

        return self::$area_person;
    }

    private static function get_underling_salesman($dept_infos,$father_info = []){
        if(empty($dept_infos)){
            return;
        }

        usort($dept_infos, function($f,$s){
            $fv = $f['type'] == 'dept' ? -1 : 1;
            $sv = $s['type'] == 'dept' ? -1 : 1;
            return $sv <=> $fv;
        });

        foreach($dept_infos as $dept_info){
            if($dept_info['type'] == 'dept'){
                self::$underling_dept[] = $dept_info['value'];
            }
            if(isset($dept_info['children'])) {
                $father_info = $dept_info;
                unset($father_info['children']);
                self::get_underling_salesman($dept_info['children'],$father_info);
            }

            if($dept_info['type'] == 'salesman'){
                self::$underling_salesman[] = $dept_info['value'];
                self::$underling_realname[] = $dept_info['label'];

                //获取商务对应的名字和区域信息
                self::$area_person[$dept_info['value']] = [
                    'realname' => $dept_info['label'],
                    'area'     => $father_info['label'],
                    'dept_id'  => $father_info['value'],
                ];
            }
        }
    }

    public static function getSaleDeptStruct($salesman){
        $userInfo = SystemUser::getUserInfoByName($salesman);
        $dept = self::getSaleDeptRecursion($userInfo['dept_id'],$userInfo['is_leader'], true);
        return $dept['dept_infos'];
    }

    /**
     * 获取所有商务部门数据
     *
     * @param      $user_dept_id
     * @param      $is_leader
     * @param bool $disable_salesman
     *
     * @return array
     * <AUTHOR> 2024-03-18 17:33:04
     *
     * @static
     */
    public static function getSaleDeptRecursion($user_dept_id,$is_leader,$disable_salesman = false): array {
        $all_depts       = DeptGrade::getAllDept();//所有部门及其上级部门
        $all_depts_infos = Dept::getDeptsInfo();//所有部门信息

        $dept_user_map = self::get_dept_user_map();

        self::$sale_dept_ids = [];
        self::$sale_dept_ids[Dept::SALE_DEPT_ID] = Dept::SALE_DEPT_ID;
        $dept_infos = self::get_child_dept(Dept::SALE_DEPT_ID,$all_depts,$all_depts_infos,$dept_user_map,$disable_salesman);

        $is_sale = false;
        $dept_data = $dept_infos;
        // 如果是商务
        if(key_exists($user_dept_id, self::$sale_dept_ids)){
            $is_sale = true;
            if($is_leader == 1){//返回部门以及下属部门
                $dept_data = self::get_dept_info_from_dept_id($user_dept_id,$dept_infos);
            }else{
                $dept_data = [];
            }
        }

        return [
            'is_sale'    => $is_sale,
            'dept_infos' => $dept_data,
        ];
    }

    /**
     * 获取部门id 包含的用户数据
     *
     * @return array
     * <AUTHOR> 2024-03-22 15:29:11
     *
     * @static
     */
    private static function get_dept_user_map(){
        $all_users = SystemUser::getAllUsers();

        $dept_user_map = [];
        foreach($all_users as $user_info){
            if(!empty($user_info['dept_id'])){
                $dept_user_map[$user_info['dept_id']][] = [
                    'username'  => $user_info['username'],
                    'realname'  => $user_info['realname'],
                    'is_leader' => $user_info['is_leader'],
                ];
            }
        }

        return $dept_user_map;
    }


    /**
     *
     * @param      $dept_id
     * @param      $all_depts
     * @param      $all_depts_infos
     * @param      $dept_user_map
     * @param bool $disable_salesman
     * @param bool $root_dept
     *
     * @return array
     * @static
     * <AUTHOR> 2024-03-18 17:11:21
     */
    private static function get_child_dept($dept_id, $all_depts, $all_depts_infos, $dept_user_map, bool $disable_salesman = false, bool $root_dept = true): array {
        //如果没有子部门 直接返回
        $child_dept = [];

        if($root_dept){
            $salesmans = $dept_user_map[$dept_id] ?? [];
            $children_salesman = [];
            if(!$disable_salesman) {
                foreach ($salesmans as $salesman) {
                    $children_salesman[] = [
                        'value' => $salesman['username'],
                        'key'   => Func::get_tmp_key("salesman", $salesman['username']),
                        'label' => $salesman['realname'],
                        'type'  => 'salesman',
                    ];
                    self::$all_salesman[$salesman['username']] = $salesman['username'];
                }
            }
            $child_dept[$dept_id][] = [
                'value' => $dept_id,//区域id
                'key'   => Func::get_tmp_key("dept",$dept_id),
                'label' => $all_depts_infos[$dept_id]['dept_name'],//区域名称
                'type'  => 'dept',
                'children' => $children_salesman,
            ];

            self::$sale_dept_ids[$dept_id] = $dept_id;
        }else{
            foreach ($all_depts as $dept_info) {
                if ($dept_info['grade_father_id'] == $dept_id) {
                    $salesmans = $dept_user_map[$dept_info['grade_dept_id']] ?? [];
                    $children_salesman = [];
                    if(!$disable_salesman) {
                        foreach ($salesmans as $salesman) {
                            $children_salesman[] = [
                                'value' => $salesman['username'],
                                'key'   => Func::get_tmp_key("salesman", $salesman['username']),
                                'label' => $salesman['realname'],
                                'type'  => 'salesman',
                            ];
                            self::$all_salesman[$salesman['username']] = $salesman['username'];
                        }
                    }

                    $grade_dept_id = $dept_info['grade_dept_id'];
                    $child_dept[$grade_dept_id][] = [
                        'value' => $grade_dept_id,
                        'key'   => Func::get_tmp_key("dept",$grade_dept_id),
                        'label' => $all_depts_infos[$dept_info['grade_dept_id']]['dept_name'],
                        'type'  => 'dept',
                        'children' => $children_salesman,
                    ];
                    self::$sale_dept_ids[$grade_dept_id] = $grade_dept_id;
                }
            }
        }
        if(!empty($child_dept)) {
            foreach ($child_dept as $dept_id => &$c_depts) {
                foreach ($c_depts as &$c_dept){
                    if(isset($c_dept['type']) && $c_dept['type'] == 'dept') {
                        $children = self::get_child_dept($c_dept['value'], $all_depts, $all_depts_infos, $dept_user_map, $disable_salesman, false);
                        if(!empty($children)) {
                            foreach($children as $child_info){
                                foreach($child_info as $chi) {
                                    //将部门放置在销售上方
                                    array_unshift($c_dept['children'] , $chi);
                                }
                            }
                        }
                    }
                }
            }
        }
        if($root_dept) {
            return $child_dept[$dept_id];
        }else {
            return $child_dept;
        }
    }


    /**
     * 获取登录用户区域数据
     *
     * @param $user_dept_id
     * @param $dept_infos
     *
     * @static
     * @return array
     * <AUTHOR> 2024-03-19 14:38:56
     */
    private static function get_dept_info_from_dept_id($user_dept_id,$dept_infos){
        if(empty($dept_infos)){
            return [];//终止条件
        }

        foreach ($dept_infos as $dept_info) {
                $dept_id = $dept_info['value'];
                if ($user_dept_id == $dept_id) {
                    return [$dept_info];//如果存在则返回 为了前端格式 返回使用数组包裹
                } else {
                    if (!empty($dept_info['children'])) {
                        //递归调用
                        $_dept_info = self::get_dept_info_from_dept_id($user_dept_id, $dept_info['children']);
                        if (!empty($_dept_info)) {
                            return $_dept_info;
                        }
                    }
                }

        }
        return [];
    }
}