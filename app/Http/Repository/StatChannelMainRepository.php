<?php

namespace App\Http\Repository;

use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\ChannelRemit;
use App\Models\ConfigPriceInterface;
use App\Models\StatisticsInterfaceUsage;
use App\Models\UpstreamBillAdjust;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\UserChannelConfig;
use App\Providers\RedisCache\RedisCache;


class StatChannelMainRepository extends StatBaseRepository
{


    // 存储渠道产品关系
    protected $channel_product_map = [];

    /**
     * 数据统计-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        $channel_ids = $this->getChannelIds($params); //主产品对应的所有渠道

        $interface_ids = $this->getIids($params, $channel_ids);   //渠道下所有的iid(接口id)
        //$usage_data = $this->getUsage($params, $interface_ids);   //调用量
        //$cost_data = $this->getCost($params,$interface_ids);   //成本
        //$data = $this->formatData($usage_data, $cost_data);
        $usage_data = $this->getUsageV2($params, $interface_ids);   //调用量
        $cost_data = $this->getCostV2($params,$interface_ids);   //成本
        $expend_cost_data = $this->getExpendCost($params,$interface_ids); //特殊消耗成本(成本调整)
        $fixed_cost_data = $this->getFixedCost($params,$channel_ids); //固定费用成本

        $channel_real_balance = Channel::getChannelBalance();//渠道余额
        //获取渠道接口对应的最新价格列表
        $price_list = $this->getLatestPriceList($interface_ids);

        $data = $this->formatDataV2($usage_data, $channel_real_balance, $price_list, $cost_data, $expend_cost_data, $fixed_cost_data);

        return $this->filterAuthMoney($data); //过滤没有权限查看的产品（收入和成本）
    }

    /**
     * 客户维度统计过滤没有权限查看的产品（收入和成本）
     * @param $data
     * @return mixed
     */
    private function filterAuthMoney($data){
        if(empty($data)){
            return [];
        }
        foreach ($data as $chanel_id => $items_channel) {
            //判断用户是否有展示金额相关的权限，没有则用-标识没权限，跟0区分开
            foreach ($items_channel as $interface_id => $item_interface) {
                foreach ($item_interface as $operator => $item_opr) {
                    foreach ($item_opr as $encrypt => $value) {
                        $father_ids = $this->channel_product_map[$chanel_id];
                        if (!$this->checkProductsAuth($father_ids,'cost')) {
                            $data[$chanel_id][$interface_id][$operator][$encrypt]['cost'] = '-';
                        }
                    }
                }
            }
        }
        return $data;
    }

    // 检查所有产品是否都有权限
    private function checkProductsAuth($ids,$type){
        if(empty($ids)){
            return false;
        }
        $res = true;;
        foreach ($ids as $father_id ) {
           $res = $res && $this->checkUserProductAuth($father_id,$type);
        }
        return $res;
    }

    public function statListExport()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        if(isset($params['export_type']) && $params['export_type'] == 'month'){
            $params['start_date'] = substr($params['start_date'], 0, 6).'01';
            $params['end_date'] = $this->getLastDay($params['end_date']);
        }

        $channel_ids = $this->getChannelIds($params); //主产品对应的所有渠道
        $interface_ids = $this->getIids($params, $channel_ids);   //渠道下所有的iid(接口id)
        $usage_data = $this->getUsageV3($params, $interface_ids);   //调用量
        $cost_data = $this->getCostV3($params,$interface_ids);   //成本
        $expend_cost_data = $this->getExpendCostV3($params,$interface_ids); //特殊消耗成本
        $fixed_cost_data = $this->getFixedCostV3($params,$channel_ids); //固定费用成本
        $channel_real_balance = Channel::getChannelBalance();//渠道余额
        //获取渠道接口对应的最新价格列表
        $price_list = $this->getLatestPriceList($interface_ids);

        $data = $this->formatDataV3($usage_data, $channel_real_balance, $price_list, $cost_data, $expend_cost_data, $fixed_cost_data);
        $data = $this->filterAuthMoneyExport($data); //过滤没有权限查看的产品（收入和成本）
        return $data;
    }

        /**
     * 客户维度统计过滤没有权限查看的产品（收入和成本）
     * @param $data
     * @return mixed
     */
    private function filterAuthMoneyExport($data){
        if(empty($data)){
            return [];
        }
        foreach ($data as $key => $value) {
            //判断用户是否有展示金额相关的权限，没有则用-标识没权限，跟0区分开
         
            $father_ids = $this->channel_product_map[$value['channel']];
            if (!$this->checkProductsAuth($father_ids,'cost')) {
                $data[$key]['cost'] = '-';
            }
            
        }
        return $data;
    }
    protected function getChannelIds($params){
        $where = [];
        isset($params['father_id']) && $where['product_id'] = $params['father_id'];

        if(isset($params['channel'])){
            $where['channel_id'] = $params['channel'];
        }else{
            $channel_ids = UserChannelConfig::getChannelIdsByUser(self::$user_name);
            $channel_ids = array_column($channel_ids,'channel_id');
            if(in_array(-1 ,$channel_ids)) {
                $channel_ids = Channel::select(['label', 'channel_id'])->get()->toArray();
                $channel_ids = array_column($channel_ids,'channel_id');
            }
            if(!empty($channel_ids)){
                $where['channel_id'] = $channel_ids;
            }
        }

        if(empty($where['channel_id'])) {
            return [];
        }

        $list = ChannelProduct::getListByCondition($where, ['channel_id','product_id'])->toArray();

        // 取渠道产品关系 用于后续权限判断
        if($list){
            foreach ($list as  $value) {
                if(!isset($this->channel_product_map[$value['channel_id']])){
                    $this->channel_product_map[$value['channel_id']]=[];
                }
                $this->channel_product_map[$value['channel_id']][]=$value['product_id'];
            }
        }
        return array_column($list , 'channel_id');
    }

    protected function getIids($params, $channel_ids) {

        $where = [];
        isset($params['interface']) && $where['id'] = $params['interface'];
        if(empty($channel_ids)){
            return [];
        }
        return array_column(ChannelInterface::getListByCondition($where, ['id'], $channel_ids)->toArray(), 'id');
    }

    protected function getUsage($params, $interface_ids=null) {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsInterfaceUsage::getStatList($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $usage_data;
    }

    protected function getUsageV2($params, $interface_ids=null) {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsInterfaceUsage::getStatListV2($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $usage_data;
    }

    protected function getUsageV3($params, $interface_ids=null) {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsInterfaceUsage::getStatListV3($where, $params['product_list'], $interface_ids, $params['filter_apikey'], null, $params);
        return $usage_data;
    }

    protected function getCost($params, $interface_ids=null){
        $where = $this->getCostWhere($params);
        $cost_data = BillCostV2::getChannelStatList($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected function getCostV2($params, $interface_ids=null){
        $where = $this->getCostWhere($params);
        $cost_data = BillCostV2::getChannelStatListV2($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected function getCostV3($params, $interface_ids=null){
        $where = $this->getCostWhere($params);
        $cost_data = BillCostV2::getChannelStatListV3($where, $params['product_list'], $interface_ids, $params['filter_apikey'], null, $params);
        return $cost_data;
    }

    protected function getExpendCost($params, $interface_ids=null){
        //由于ChannelAccountAdjust中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelStatList($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected function getExpendCostV3($params, $interface_ids=null){
        //由于ChannelAccountAdjust中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }

        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelStatListV3($where, $params['product_list'], $interface_ids, $params['filter_apikey'], null, $params);
        return $cost_data;
    }

    protected  function getFixedCost($params,$channel_ids){
        if(empty($channel_ids)){
            return [];
        }else{
            $params['channel_id'] = $channel_ids;
        }
        //由于ChannelAccountFixedFee中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        if(isset($params['channel']) && !empty($params['channel'])){
            $where[] = ['channel_id', '=', $params['channel']];
        }

        if(isset($params['operator']) && !empty($params['operator'])){
            $where[] = ['operator', '=', $params['operator']];
        }

        $cost_data = ChannelAccountFixedFee::getChannelStatList($where, $params);
        return $cost_data;

    }

    protected  function getFixedCostV3($params,$channel_ids){
        //由于ChannelAccountFixedFee中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }

        return ChannelAccountFixedFee::getChannelStatListV3($params,$channel_ids);

    }

//    protected function getUsageWhere($params)
//    {
//        $where = [];
//        isset($params['start_date']) && isset($params['end_date'])
//        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
//        isset($params['operator']) && $where[] = ['operator', $params['operator']];
//        return $where;
//    }
//    protected function getCostWhere($params)
//    {
//        $where = [];
//        isset($params['start_date']) && isset($params['end_date'])
//        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
//        isset($params['operator']) && $where[] = ['operator', $params['operator']];
//        return $where;
//    }


    /**
     * 组装最终数据
     * @param $usage_data
     * @param $cost_data
     * @param $show_money
     * @return mixed
     * @throws \Exception
     */
    private function formatData($usage_data, $cost_data)
    {
        $data = [];
        foreach ($usage_data as $item) {
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            $data[$channel_id][$item['interface_id']][$item['operator']] = $item;
        }
        foreach ($cost_data as $item){
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            if (isset($data[$channel_id][$item['interface_id']][$item['operator']])){
                $data[$channel_id][$item['interface_id']][$item['operator']]['cost'] = $item['money'];
            }
        }
        return $data;
    }

    private function formatDataV2($usage_data, $channel_real_balance, $price_list, $cost_data, $expend_cost_data = [], $fixed_cost_data = [])
    {
        $data = [];
        foreach ($usage_data as &$item) {
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            if(empty($item['encrypt'])){//不是所有产品调用接口时都会传该参数的，因此存在为空的
                $encrypt = 'NO';
            }else{
                $encrypt = $item['encrypt'];
            }
            $item['encrypt'] = $encrypt;
            $item['balance'] = $channel_real_balance[$channel_id] ?? '-';
            $info = $this->handleSpecialOperatorAndEncrypt($item['operator'], $encrypt);
            $item['price'] = $price_list[$item['interface_id']][$info['operator']][$info['encrypt']] ?? '-';

            $data[$channel_id][$item['interface_id']][$item['operator']][$encrypt] = $item;
        }

        foreach ($cost_data as &$item) {
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            if(empty($item['encrypt'])){//不是所有产品调用接口时都会传该参数的，因此存在为空的
                $encrypt = 'NO';
            }else{
                $encrypt = $item['encrypt'];
            }
            //$item['encrypt'] = $encrypt;
            if (isset($data[$channel_id][$item['interface_id']][$item['operator']][$encrypt])) {
                $data[$channel_id][$item['interface_id']][$item['operator']][$encrypt]['cost'] = $item['money'];
                $data[$channel_id][$item['interface_id']][$item['operator']][$encrypt]['cost_part'] = $item['money'];
            }
        }

        //特殊消耗成本
        foreach ($expend_cost_data as &$item) {
            if($item['interface_id'] == 0){
                continue;
            }

            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);

            $interface_id = $item['interface_id'];
            $operator     = 'expend';//在前端字典已经做了该key映射
            $encrypt      = 'NO';//在前端字典已经做了该key映射
            
            $total_cost = $data[$channel_id][$interface_id][$operator][$encrypt]['cost'] ?? 0;
            $total_cost = bcadd($total_cost, $item['money'], 6);

            if ($item['category'] == 2) {
                $total_cost_part = $data[$channel_id][$interface_id][$operator][$encrypt]['cost_part'] ?? 0;
                $total_cost_part = bcadd($total_cost_part, $item['money'], 6);
                $data[$channel_id][$interface_id][$operator][$encrypt]['cost_part'] = $total_cost_part;
            }

            $data[$channel_id][$interface_id][$operator][$encrypt]['cost'] = $total_cost;
            $data[$channel_id][$interface_id][$operator][$encrypt]['success'] = 0;
            $data[$channel_id][$interface_id][$operator][$encrypt]['total'] = 0;
            $data[$channel_id][$interface_id][$operator][$encrypt]['valid'] = 0;

        }

        //固定费用成本
        foreach ($fixed_cost_data as &$item) {
            $channel_id = $item['channel_id'];

            $interface_id = 'fixed';//在前端字典已经做了该key映射
            $operator = 'NO';//在前端字典已经做了该key映射
            $encrypt = 'NO';//在前端字典已经做了该key映射

            $total_cost = $data[$channel_id][$interface_id][$operator][$encrypt]['cost'] ?? 0;
            $total_cost = bcadd($total_cost, $item['money'], 6);
            $data[$channel_id][$interface_id][$operator][$encrypt]['cost'] = $total_cost;
            $data[$channel_id][$interface_id][$operator][$encrypt]['cost_part'] = $total_cost;
            $data[$channel_id][$interface_id][$operator][$encrypt]['success'] = 0;
            $data[$channel_id][$interface_id][$operator][$encrypt]['total'] = 0;
            $data[$channel_id][$interface_id][$operator][$encrypt]['valid'] = 0;

        }

        return $data;
    }

    private function formatDataV3($usage_data, $channel_real_balance, $price_list, $cost_data, $expend_cost_data = [], $fixed_cost_data = [])
    {
        $data = [];
        foreach ($cost_data as $item) {
            if(empty($item['encrypt'])){//不是所有产品调用接口时都会传该参数的，因此存在为空的
                $encrypt = 'NO';
            }else{
                $encrypt = $item['encrypt'];
            }

            $data[$item['interface_id']][$item['operator']][$encrypt][$item['date']]['cost'] = $item['money'];
            $data[$item['interface_id']][$item['operator']][$encrypt][$item['date']]['cost_part'] = $item['money'];
        }

        unset($item);
        foreach ($usage_data as &$item) {
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            if(empty($item['encrypt'])){//不是所有产品调用接口时都会传该参数的，因此存在为空的
                $encrypt = 'NO';
            }else{
                $encrypt = $item['encrypt'];
            }
            $item['encrypt'] = $encrypt;
            $item['interface'] = $item['interface_id'];
            $item['channel'] = $channel_id;

            if($item['total'] > 0){
                $item['success_ratio'] = bcdiv(bcmul($item['success'], 100, 2), $item['total'], 2);
                $item['valid_ratio'] = bcdiv(bcmul($item['valid'], 100, 2), $item['total'], 2);;
            }else{
                $item['success_ratio'] = 0;
                $item['valid_ratio'] = 0;
            }

            $cost  = $data[$item['interface_id']][$item['operator']][$encrypt][$item['date']]['cost'] ?? 0;
            $item['cost'] = number_format($cost, 2);
            $item['cost_part'] = number_format($cost, 2);
            $item['balance'] = $channel_real_balance[$channel_id] ?? '-';

            $info = $this->handleSpecialOperatorAndEncrypt($item['operator'], $encrypt);
            $item['price'] = $price_list[$item['interface_id']][$info['operator']][$info['encrypt']] ?? '-';
        }
        //dd($expend_cost_data,$usage_data);

        //特殊消耗成本
        $expend_cost_data_tmp = [];
        unset($item);
        foreach ($expend_cost_data as &$item) {
            if($item['interface_id'] == 0){
                continue;
            }

            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            $date = str_replace('-', '', $item['date']);

            $total_cost = $expend_cost_data_tmp[$channel_id][$date]['cost'] ?? 0;
            $total_cost = bcadd($total_cost, $item['money'], 6);
            $expend_cost_data_tmp[$channel_id][$date]['cost'] = $total_cost;
            $expend_cost_data_tmp[$channel_id][$date]['date'] = $date;

            #不含保底
            if ($item['category'] == 2) {
                $total_cost_part = $expend_cost_data_tmp[$channel_id][$date]['cost_part'] ?? 0;
                $total_cost_part = bcadd($total_cost_part, $item['money'], 6);
                $expend_cost_data_tmp[$channel_id][$date]['cost_part'] = $total_cost_part;
            }

        }

        foreach($expend_cost_data_tmp as $channel_id => $val){
            unset($item);
            foreach($val as $date => $item){
                $item['interface_id'] = 'expend';
                $item['operator'] = 'NO';
                $item['encrypt'] = 'NO';
                $item['interface'] = 'expend';
                $item['channel'] = $channel_id;

                $item['success'] = 0;
                $item['total'] = 0;
                $item['valid'] = 0;
                $item['success_ratio'] = 0;
                $item['valid_ratio'] = 0;

                $usage_data[] = $item;
            }

        }


        //固定费用成本
        $fixed_cost_data_tmp = [];
        unset($item);
        foreach ($fixed_cost_data as $item) {
            $channel_id = $item['channel_id'];

            $date = str_replace('-', '', $item['s_date']);

            $row['interface_id'] = 'fixed';
            $row['operator'] = 'NO';
            $row['encrypt'] = 'NO';
            $row['interface'] = 'fixed';
            $row['channel'] = $channel_id;
            $row['date'] = $date;
            $row['cost'] = $item['s_money'];
            $row['cost_part'] = $item['s_money'];

            $row['success'] = 0;
            $row['total'] = 0;
            $row['valid'] = 0;
            $row['success_ratio'] = 0;
            $row['valid_ratio'] = 0;

            $usage_data[] = $row;
        }

        return $usage_data;
    }

    //获取渠道接口对应的最新价格列表
    public function getLatestPriceList($interface_ids = []){
        $result = [];
        ConfigPriceInterface::whereNull('delete_time')
            ->when($interface_ids, function($query) use ($interface_ids) {
                return $query->whereIn('interface_id', $interface_ids);
            })
            ->orderBy('start_date', 'desc')
            ->get()
            ->each(function ($item) use (&$result) {
                $interface_id = $item['interface_id'];
                $price = json_decode($item['price'], true);
                if (json_last_error() != JSON_ERROR_NONE) {
                    return;
                }

                $key = $interface_id;
                if (!array_key_exists($key, $result)) {
                    $result[$key] = $price;
                }
            });

        //对接口价格列表进一步处理
        $op_map = ['CMCC', 'CUCC', 'CTCC', 'CBN'];
        $encrypt_map = ['CLEAR', 'MD5', 'SHA256'];
        $price_list = [];

        foreach ($result as $iid => $item){
            foreach ($item as $price_info){
                $operator = [];
                $encrypt_way = [];
                $money = $price_info['price'];
                if($price_info['operator'] == 'ALL' ){
                    $operator = $op_map;
                }else{
                    $operator[] = in_array($price_info['operator'], $op_map) ? $price_info['operator'] : '';
                }

                if($price_info['encrypt_way'] == 'ALL' ){
                    $encrypt_way = $encrypt_map;
                }else{
                    $encrypt_way[] = in_array($price_info['encrypt_way'], $encrypt_map) ? $price_info['encrypt_way'] : '';
                }

                //运营商和加密方式的组合方式有多少中
                foreach ($operator as $op){
                    foreach ($encrypt_way as $encrypt){
                        $price_list[$iid][$op][$encrypt] = $money;
                    }
                }
            }

        }

        unset($result);
        return $price_list;
    }

    public function handleSpecialOperatorAndEncrypt($operator, $encrypt){
        if(in_array($operator, ['CMCC', 'CUCC', 'CTCC', 'CBN'])){
            $new_operator = $operator;
        }else if(strpos($operator, 'CMCC') !== false){
            $new_operator = 'CMCC';
        }else if(strpos($operator, 'CUCC') !== false){
            $new_operator = 'CUCC';
        }else if(strpos($operator, 'CTCC') !== false){
            $new_operator = 'CTCC';
        }else if(strpos($operator, 'CBN') !== false){
            $new_operator = 'CBN';
        }else{
            $new_operator = 'CMCC';
        }

        if(in_array($encrypt, ['CLEAR', 'MD5', 'SHA256'])){
            $new_encrypt = $encrypt;
        }else{
            $new_encrypt = 'CLEAR';
        }

        return ['operator' => $new_operator, 'encrypt' => $new_encrypt];
    }

    /**
     * 返回两个日期相差月份数
     * @param $date1
     * @param $date2
     * @return float|int
     */
    public function getDiffMonthNum($date1, $date2)
    {

        $year1 = substr($date1, 0, 4);
        $month1 = substr($date1, 4, 2);

        $year2 = substr($date2, 0, 4);
        $month2 = substr($date2, 4, 2);

        $month_num_1 = $year1*12 + $month1;
        $month_num_2 = $year2*12 + $month2;
        return abs($month_num_1 - $month_num_2);
    }









}