<?php

namespace App\Http\Repository;

use Account\Model\CompanytypeModel;
use App\Models\Account;
use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\CompanyType;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Models\Common\CommonEnumModel;
use App\Models\CustomerExpend;
use App\Models\StatisticsCustomerUsage;
use App\Models\Product;
use App\Models\SystemSession;
use App\Models\SystemUser;
use App\Models\SystemUserProduct;
use App\Providers\Auth\DataAuth;
use App\Utils\Helpers\Func;
use Hamcrest\Util;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Providers\RedisCache\RedisCache;

class StatBaseRepository
{
    public static $user_product = [];           // 存储当前用户显示收入列产品  
    public static $user_money_product = [];      // 存储当前用户显示成本列产品
    public static $user_money_finance_product = [];  // 存储当前用户显示金融收入列产品
    public static $user_cost_product = [];      // 存储当前用户显示成本列产品
    public static $user_agent_product = [];     // 存储当前用户显示征信机构收入列产品
    public static $user_cache_product = [];     // 存储当前用户显示缓存产品
    public $base_customerid_source_map = [];         // 存储当前用户权限客户和可查看的来源
    public $base_customerid_source_map_page = [];
    private $apikey_customer_id_map = [];
    public static $user_name;
    public $degree = 6;//定义默认计算精度

    public function __construct()
    {
        //根据用户cookie取出所属主产品
        $user_cookie = request()->post('user_cookie', null);
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id'=>$user_cookie])->first()->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)){
                self::$user_name = $match[1];
                $this->renderAuth(self::$user_name);
            }
        }
    }

    public function renderAuth($username){
        $products = SystemUserProduct::where(['username'=>$username])->get()->toArray();
        self::$user_product =  self::$user_money_product = self::$user_cost_product = [];
        self::$user_money_finance_product = self::$user_agent_product = self::$user_cache_product = [];
        array_walk($products,function ($item){

            self::$user_product = array_merge(self::$user_product,[$item['father_id']]);

            if ($item['is_show_money']==1) {
                self::$user_money_product = array_merge(self::$user_money_product,[$item['father_id']]);
            }
            //金融收入
            if ($item['is_show_money_finance']==1) {
                self::$user_money_finance_product = array_merge(self::$user_money_finance_product,[$item['father_id']]);
            }
            if ($item['is_show_cost']==1) {
                self::$user_cost_product = array_merge(self::$user_cost_product,[$item['father_id']]);
            }
            if ($item['is_show_money_agent']==1) {
                self::$user_agent_product = array_merge(self::$user_agent_product,[$item['father_id']]);
            }
            if ($item['is_show_cache']==1) {
                self::$user_cache_product = array_merge(self::$user_cache_product,[$item['father_id']]);
            }
        }
        );
    }

    public function getFinanceAuthProduct(){
        return [
            'user_product'=>self::$user_product,
            'user_money_product'=>self::$user_money_product,
            'user_money_finance_product'=>self::$user_money_finance_product,
            'user_cost_product'=>self::$user_cost_product,
            'user_agent_product'=>self::$user_agent_product,
            'user_cache_product'=>self::$user_cache_product,
        ];
    }

    /**
     * 参数验证
     * @return array
     * @throws \Exception
     */
    protected function getStatParams()
    {
        $group_id = request()->post('group_id', null);
        $father_id = intval(request()->post('father_id', null));
        $father_ids = request()->post('father_ids', []);
        $customer_id = request()->post('customer_id', null);
        $apikey = request()->post('apikey', null);
        $product_id = intval(request()->post('product_id', null));
        $operator = request()->post('operator', null);
        $start_date = request()->post('start_date', null);
        $end_date   = request()->post('end_date', null);
        $test_data  = request()->post('test_data', null);
        $channel  = request()->post('channel', null);
        $channel_id  = request()->post('channel_id', null);
        $interface  = request()->post('interface', null);
        $export_type  = request()->post('export_type', null);
        $chart_type  = request()->post('chart_type', null);
        $dept_id = request()->post('dept_id',null);
        $username = request()->post('username',null);
        $father_type = request()->post('father_type', '');
        $children_type = request()->post('children_type', '');
        $channel_type = request()->post('channel_type', '');
        $source = request()->post('source', '');
        $view_angle = request()->post('view_angle', '');
//        if (!$father_id) throw new \Exception('缺少主产品ID');
        if (!$start_date || !$end_date) throw new \Exception('请选择时间');
        if (!preg_match('/^\d{8}$/', $start_date) || !preg_match('/^\d{8}$/', $end_date))
            throw new \Exception('时间格式错误');

        if(!empty($dept_id)){
            [$dept_id_type,$dept_id_value] = explode("_", $dept_id[count($dept_id) - 1]);
            if($dept_id_type == 'dept'){
                $dept_id = $dept_id_value;
            }
            if($dept_id_type == 'salesman'){
                $username = $dept_id_value;
            }
        }

        $params = compact('start_date', 'end_date');
        $apikey && $params['apikey'] = $apikey;
        $product_id && $params['product_id'] = $product_id;
        $operator && $params['operator'] = $operator;
        $father_id && $params['father_id'] = $father_id;
        $father_ids && $params['father_ids'] = $father_ids;
        $customer_id && $params['customer_id'] = $customer_id;
        $channel && $params['channel'] = $channel;
        $channel_id && $params['channel_id'] = $channel_id;
        $interface && $params['interface'] = $interface;
        $export_type && $params['export_type'] = $export_type;//导出类型
        $chart_type && $params['chart_type'] = $chart_type;//图形指标类型
        $dept_id && $params['dept_id'] = $dept_id;
        $username && $params['username'] = $username;
        $father_type && $params['father_type'] = $father_type;
        $children_type && $params['children_type'] = $children_type;
        $channel_type && $params['channel_type'] = $channel_type;
        $test_data && $params['test_data'] = $test_data;
        $source !== '' && $params['source'] = $source;
        $view_angle !== '' && $params['view_angle'] = $view_angle;

        $params['apikey_list'] = $params['product_list'] = $params['filter_apikey'] = $params['filter_customer'] = $params['filter_product'] = false;
        //搜索条件中包含客户的话先取出所属apikey，避免重复查库
        if ($group_id) {
            $_customer_ids = Customer::getListByGroupIds([$group_id]);
            $_customer_ids = array_column($_customer_ids, 'customer_id');
            $_apikey_list = Account::getApikeysByCustomerIdsNew($_customer_ids);
            $_apikey_list = array_filter($_apikey_list, function ($api_item) {return !empty($api_item);});
            $params['customer_ids'] = $_customer_ids;
            $params['apikey_list'] = $_apikey_list;
        }
        if ($customer_id) {
            $params['apikey_list'] = array_column(Account::getListByCondition(['customer_id' =>$customer_id], 'apikey')->toArray(), 'apikey');
        }
        if ($father_ids) {
            $products = Product::getChildProduct($father_ids);
            $params['product_list'] = array_column($products, 'product_id');
        }
        if ($father_id) {
            $products = Product::getListByCondition(['father_id' =>$father_id], 'product_id')->toArray();
            if (!$products) {
                $products = Product::getListByCondition(['product_id' =>$father_id], 'product_id')->toArray();
            }
            $params['product_list'] = array_column($products, 'product_id');
        }
        if (!$test_data) {
            $params['filter_customer'] = ['C20180828LOCNMG', 'C20200622KF31GS'];//羽乐科技内部、售前测试
            $params['filter_apikey'] = array_column(Account::whereIn('customer_id', $params['filter_customer'])->get()->toArray(), 'apikey');
        }
        return $params;
    }
    /**
     * 客户调用量源数据
     * @param $params
     * @return mixed
     */
    protected function getUsage($params)
    {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsCustomerUsage::getStatList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product'])->toArray();
        return $usage_data;
    }

    protected function getStatUsageSource($params)
    {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsCustomerUsage::getStatListSource($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product'])->toArray();
        return $usage_data;
    }

    protected function getUsageSource($params)
    {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsCustomerUsage::getStatListSourceMonthly($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product'])->toArray();
        
        return $usage_data;
    }

    protected function getJdskUsage($params)
    {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsCustomerUsage::getData($where,$params['apikey_list'])->toArray();
        return $usage_data;
    }

    protected function getProductIds($params)
    {
        $where = $this->getUsageWhere($params);
        $productIds = StatisticsCustomerUsage::getProductIds($where,$params['apikey_list'])->toArray();
        return $productIds;
    }

    protected function getChadRate($params)
    {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsCustomerUsage::getChadRate($where,$params['apikey_list'],'date')->toArray();
        return $usage_data;
    }

    protected function getFatherIds($params)
    {
        $where = $this->getUsageWhere($params);
        $data = StatisticsCustomerUsage::getProductIds($params['apikey_list']);
    }

    /**
     * 主接口调用量源数据
     * @param $params
     * @return mixed
     */
    protected function getMainUsage($params)
    {
        $where = $this->getMainUsageWhere($params);
        $data_main = StatisticsCustomerUsage::getStatList($where, null, $params['apikey_list'], $params['filter_apikey'])->toArray();
        return $data_main;
    }


    /**
     * 客户收入源数据
     * @param $params
     * @return mixed
     */
    protected function getIncome($params)
    {
        $where = $this->getIncomeWhere($params);
        if (isset($params['source']) && $params['source'] == BillProductIncomeV2::$source['朴道']) {
            $income_data = BillProductIncomeV2::getStatList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey']);
        } else {
            $income_data = BillProductIncomeV2::getStatList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey']);
        }

        return $income_data;
    }

    /**
     * 客户收入源数据
     * @param $params
     * @return mixed
     */
    protected function getIncomeV2($params)
    {
        $where = $this->getIncomeWhere($params);
        $income_data = BillProductIncomeV2::getStatList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey']);
        return $income_data;
    }
        /**
     * 客户收入源数据
     * @param $params
     * @return mixed
     */
    protected function getIncomeV2Source($params)
    {
        $where = $this->getIncomeWhere($params);
        $income_data = BillProductIncomeV2::getStatListSourceMonthly($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey']);
        return $income_data;
    }

    /**
     * 客户成本源数据
     * @param $params
     * @return mixed
     */
    protected function getCost($params)
    {
        $where = $this->getCostWhere($params);
        $cost_data = BillCostv2::getStatList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'])->toArray();
        return $cost_data;
    }

    /**
     * 客户成本源数据 v2表
     * @param $params
     * @return mixed
     */
    protected function getCostV2($params)
    {
        $where = $this->getCostWhere($params);
        $cost_data = BillCostV2::getStatList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'])->toArray();
        return $cost_data;
    }
    /**
     * 客户成本源数据 v2表 按照客户来源分组
     * @param $params
     * @return mixed
     */
    protected function getCostV2Source($params)
    {
        $where = $this->getCostWhere($params);
        $cost_data = BillCostV2::getStatListSourceMonthly($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'])->toArray();
        return $cost_data;
    }

    //客户成本调整 按照客户分组
    protected function getAdjustCost($params){
        $cost_data = CustomerBillAdjust::getStatList($params);
        return $cost_data;
    }

    //客户成本调整 按照客户,来源分组
    protected function getAdjustCostSource($params){
        $cost_data = CustomerBillAdjust::getStatListSourceMonthly($params);
        return $cost_data;
    }

    //客户成本调整 按照客户,来源分组
    protected function getAdjustCostMix($params){
        $cost_data = CustomerBillAdjust::getStatListMix($params);
        return $cost_data;
    }

    /**
     * 客户维度特殊消耗
     * @param $params
     * @return mixed
     */
    protected function getExpend($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getCustomerExpendBySourceMonthly(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer'], $params['customer_ids']??null);
        $expend_data['add'] = CustomerExpend::getCustomerExpendBySourceMonthly(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer'], $params['customer_ids']??null);
        return $expend_data;
    }

    /**
     * 客户维度特殊消耗
     * @param $params
     * @return mixed
     */
    protected function getExpendMix($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data = CustomerExpend::getCustomerExpendBySourceMix($where, $params['product_list'], $params['filter_customer'], $params['customer_ids']??null);
        return $expend_data;
    }

    protected function getExpendWhere($params)
    {
        $where = [];
        if (isset($params['start_date']) && isset($params['end_date'])){
            $start = date('Y-m-d', strtotime($params['start_date']));
            $end = date('Y-m-d', strtotime($params['end_date']));
            $where = [['profile_show_date', '>=', $start],['profile_show_date', '<=', $end]];
        }

        isset($params['customer_id']) && $where[] = ['customer_id', $params['customer_id']];
        isset($params['product_id']) && $where[] = ['product_id', $params['product_id']];
        isset($params['operator']) && $where[] = ['operator', $params['operator']];
        isset($params['source']) && $where[] = ['source', $params['source']];
        return $where;
    }

    protected function getUsageWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        isset($params['apikey']) && $where[] = ['apikey', $params['apikey']];
        isset($params['product_id']) && $where[] = ['product_id', $params['product_id']];
        isset($params['operator']) && $where[] = ['operator', $params['operator']];
        isset($params['source']) && $where[] = ['source', $params['source']];
        return $where;
    }

    protected function getMainUsageWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        isset($params['apikey']) && $where[] = ['apikey', $params['apikey']];
        if(isset($params['product_id'])){
            $where[] = ['product_id', $params['product_id']];
        }else{
            isset($params['father_id']) && $where[] = ['product_id', $params['father_id']];
        }

        isset($params['operator']) && $where[] = ['operator', $params['operator']];
        return $where;
    }

    protected function getIncomeWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        isset($params['apikey']) && $where[] = ['apikey', $params['apikey']];
        isset($params['product_id']) && $where[] = ['product_id', $params['product_id']];
        isset($params['father_id']) && $where[] = ['father_id', $params['father_id']];
        isset($params['operator']) && $where[] = ['operator', $params['operator']];
        isset($params['source']) && $where[] = ['source', $params['source']];
        return $where;
    }

    protected function getCostWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        isset($params['apikey']) && $where[] = ['apikey', $params['apikey']];
        isset($params['product_id']) && $where[] = ['product_id', $params['product_id']];
        isset($params['operator']) && $where[] = ['operator', $params['operator']];
        isset($params['source']) && $where[] = ['source', $params['source']];
        return $where;
    }

    /**
     * 校验用户的产品权限
     * @param $father_id
     * @param $type
     * @return bool
     */
    protected function checkUserProductAuth($father_id,$type='default'){
        if($type=='default'){
            if(in_array(-1, self::$user_product) || in_array($father_id, self::$user_product)) {
                return true;
            }
            return false;
        }
        if($type=='money'){
            if(in_array(-1, self::$user_money_product) || in_array($father_id, self::$user_money_product)) {
                return true;
            }
            return false;
        }
        if($type=='money_finance'){
            if(in_array(-1, self::$user_money_finance_product) || in_array($father_id, self::$user_money_finance_product)) {
                return true;
            }
            return false;
        }
        if($type=='cost'){
            if(in_array(-1, self::$user_cost_product) || in_array($father_id, self::$user_cost_product)) {
                return true;
            }
            return false;
        }
        if($type=='money_agent'){
            if(in_array(-1, self::$user_agent_product) || in_array($father_id, self::$user_agent_product)) {
                return true;
            }
            return false;
        }
        if($type=='cache'){
            if(in_array(-1, self::$user_cache_product) || in_array($father_id, self::$user_cache_product)) {
                return true;
            }
            return false;
        }
        return false;
    }

    /**
     * 过滤没有权限查看的产品（收入和成本）
     * @param $data
     * @return mixed
     */
    protected function filterAuthMoneyByFatherid($data,$father_id){
        $show_money = $this->checkUserProductAuth($father_id,'money');
        $show_cache = $this->checkUserProductAuth($father_id,'cache');
        $show_cost = $this->checkUserProductAuth($father_id,'cost');
        $show_money_agent = $this->checkUserProductAuth($father_id,'money_agent');
        foreach ($data as $demen1 => $items) {
             if(isset($data[$demen1]['money'])){
                if (!$show_money) {
                    $data[$demen1]['money']  = '-';
                }
             }
             if(isset($data[$demen1]['cost'])){
                if (!$show_cost) {
                    $data[$demen1]['cost']  = '-';
                }
             }
            if(isset($data[$demen1]['money_agent'])){
                if (!$show_money_agent) {
                    $data[$demen1]['money_agent']  = '-';
                }
            }
            if(isset($data[$demen1]['cache'])){
                if (!$show_cache) {
                    $data[$demen1]['cache']  = '-';
                }
            }
        }
        return $data;
    }

   /**
     * 客户维度统计过滤没有权限查看的产品（收入和成本）
     * @param $data
     * @return mixed
     */
    protected function filterAuthMoneyDimension($data){
        foreach ($data as $customer_id => $items_father) {
            //判断用户是否有展示金额相关的权限，没有则用-标识没权限，跟0区分开
            foreach ($items_father as $father_id => $item_product) {
                foreach ($item_product as $product_id => $item_opr) {
                    foreach ($item_opr as $operator => $value) {
                        if (!$this->checkUserProductAuth($father_id,'money')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['money'] = '-';
                        }
                        if (!$this->checkUserProductAuth($father_id,'money_agent')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['money_agent'] = '-';
                        }
                        if (!$this->checkUserProductAuth($father_id,'cost')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['cost'] = '-';
                        }
                    }
                }
            }
        }
        return $data;
    }

    /**
     * 获取商务跟进人所属的客户
     * @param string $username
     * @param string $dept_id
     * @return array|void
     */
    protected function getAuthCustomerIds($username = '',$dept_id = ''){
        //获取权限服务对象
        //区域与人员选择为空  保持不变
        if(empty($username) && empty($dept_id)){
            $dataAuthService = new DataAuth(self::$user_name);
            //获取这个用户的所能查看的所有客户ID
            return $dataAuthService->getCustomerIds();
        }
        //人员选择不为空 以人员为标注过滤客户
        if(!empty($username)){
            $dataAuthService = new DataAuth($username);
            return  $dataAuthService->getCustomerIdsByUserName();
        }
        //人员为空 部门不为空 以部门领导所能查看的客户数据
        if(!empty($dept_id) && empty($username)){
            $salesman_arr = DeptRepository::getDeptSalesmans($dept_id);
            return  DataAuth::getCustomerIdsBySalesmans($salesman_arr);
        }
    }

    /**
     * 获取商务跟进人所属的客户 页面查询接口
     * @param string $username
     * @param string $dept_id
     * @return array
     */
    public function getDataAndSourceAuthCustomerIds($username = '',$dept_id = '',$start_date = '2018-01',$end_date = '2999-12'){
        //获取渠道跟进人及配置的渠道
        //区域与人员选择为空  保持不变
        $salesman_arr = [];
        $source_auth = [];
        $data_auth = [];
        $is_show_all_salesman = false;
        $need_filler_source_auth = true;

        if(!empty($dept_id)) {
            $all_sale_dept = DeptRepository::getSalesDept();
            if(!in_array($dept_id,$all_sale_dept)) {
                $need_filler_source_auth = false;
                $dept_id = '';
            }
        }

        if(empty($username) && empty($dept_id)){
            $dataAuthService = new DataAuth(self::$user_name);
            //获取这个用户的所能查看的所有客户ID
            $data_auth    = $dataAuthService->getCustomerIds();//商务跟进人和运营跟进人为这个用户的客户
            //如果拥有所有权限或者部门权限 获取相应的商务列表

            $salesman_arr = [self::$user_name];
            $auth = $dataAuthService->getUserAuth();
            if (in_array(0, $auth)) {
                $is_show_all_salesman = true;//所有商务
            }else{
                $need_filler_source_auth = false;
                $salesman_arr = $dataAuthService->getBelongUsers();
                $salesman_arr = array_unique(array_merge($salesman_arr, [self::$user_name]));
            }

            $source_auth  = $dataAuthService->getCustomerIdsSource();//渠道
        }
        if(!empty($username)){//人员选择不为空 以人员为标注过滤客户
            $dataAuthService = new DataAuth($username);

            $data_auth    = $dataAuthService->getCustomerIdsByUserName();
            $salesman_arr = [$username];
            $source_auth  = $dataAuthService->getCustomerIdsSourceByUserName();
        }
        if(!empty($dept_id) && empty($username)){//人员为空 部门不为空 以部门领导所能查看的客户数据
            $dataAuthService = new DataAuth('',$dept_id);
            $data_auth = $dataAuthService->getCustomerIdsBydeptId();

            //$salesman_arr = SystemUser::getUserInfoByDeptId($dept_id);
            //$salesman_arr = array_column($salesman_arr,'username');
            $salesman_arr = DeptRepository::getDeptSalesmans($dept_id);
            $source_auth  = $dataAuthService->getCustomerIdsSourceBydeptId();
            $need_filler_source_auth = false;
        }

        $salesman_array = array_flip($salesman_arr);
        //如果选择区域或用户或者只有查看自己客户权限 需要过滤历史数据中其他商务的数据
        //获取历史商务数据
        $cshr = new CustomerSalesmanHistoryRepository();
        $customer_id_with_old = $cshr->getSalesmanAllCustomers($salesman_arr);
        $customer_id_with_old = array_unique(array_merge($customer_id_with_old,$data_auth,array_keys($source_auth)));
        $cshr_month = $cshr->getListMonthly($customer_id_with_old,date("Y-m",strtotime($start_date)),date("Y-m",strtotime($end_date)));

        $result = [];
        array_walk($customer_id_with_old, function($customer_id) use (&$result,$cshr_month,$salesman_array,$is_show_all_salesman) {
            if (key_exists($customer_id, $cshr_month)) {
                $monthly_salesman = $cshr_month[$customer_id];
                foreach ($monthly_salesman as $month => $salesman) {
                    if ($is_show_all_salesman) {//默认没有筛选区域与商务,则查询出来的 $cshr_month 不需要进行过滤
                        $result[$customer_id][$month] = [-1];
                    } else {
                        if (key_exists($salesman, $salesman_array)) {
                            $result[$customer_id][$month] = [-1];
                        }
                    }
                }
            }
        });

        if(!isset($customer_id_with_old[0]) || !isset($cshr_month[$customer_id_with_old[0]])){
            return $result;
        }
        //只使用月份数据 ["2023-02","2023-03","2023-04"]
        $month_arr = array_keys($cshr_month[$customer_id_with_old[0]]);

        //合并渠道跟进人可查看的来源
        array_walk($source_auth, function($item,$customer_id) use (&$result,$month_arr){
            if(!key_exists($customer_id,$result)){
                $can_check_sources = explode(',',$item);
                array_walk($can_check_sources,function(&$item){
                    $item = intval($item);
                });

                foreach($month_arr as $month){
                    $result[$customer_id][$month] = $can_check_sources;
                }
            }
        });

        // $need_filler_cource_auth = true;
        //用户限定了征信机构查看权限
        if($need_filler_source_auth){
            foreach ($salesman_arr as $salesman) {
                $dataAuthServiceMy = new DataAuth($salesman);
                $userSourceAuth    = $dataAuthServiceMy->getUserSourceAuth();
                if ($userSourceAuth !== '-1') {
                    array_walk($result, function (&$item) use ($userSourceAuth) {
                        foreach ($item as &$info) {
                            $info = [$userSourceAuth];
                        }
                    });
                }
            }
        }

        $this->base_customerid_source_map_page = $result;
        return $result;
    }

    /**
     * 获取商务跟进人所属的客户 邮件报表
     * @param $username
     */
    public function getDataAndSourceAuthCustomerIdsForReport($username = ''){
        $dataAuthService = new DataAuth($username);
        //获取这个用户的所能查看的所有客户ID
        $data_auth =  $dataAuthService->getCustomerIds();
        //获取渠道跟进人及配置的渠道
        $source_auth = $dataAuthService->getCustomerIdsSource();
        $result = [];
        array_walk($data_auth,function($item)use(&$result){
            $result[$item]=[-1];
        });
        //并上渠道跟进人可查看的来源
        array_walk($source_auth,function($item,$key)use(&$result){
            if(!isset($result[$key])){
                $result[$key]=explode(',',$item);
            }
        });
        //用户限定了征信机构查看权限
        $userSourceAuth = $dataAuthService->getUserSourceAuth();
        if($userSourceAuth!=='-1'){
            array_walk($result,function(&$item)use($userSourceAuth){
                $item = [$userSourceAuth];
            });
        }
        $this->base_customerid_source_map[$username] = $result;
        return $result;
    }

    // 按照客户及客户来源过滤数据
    public function filterDataSourceBase($customer_id,$source){
        $auth = $this->base_customerid_source_map;
        if(!isset($auth[$customer_id])){
            return true;// 被过滤掉
        }
        if(in_array(-1,$auth[$customer_id])||in_array($source,$auth[$customer_id])){
            return false;// 有权限，不用过滤
        }else{
            return true;// 无权限，被过滤掉
        }
    }

    //符合公司类型条件的客户
    protected function getTypeCustomerIds($params){
        $type = [];
        if(isset($params['father_type']) && !empty($params['father_type'])){
            $type = CompanyType::getChildType($params['father_type']);
        }

        if(isset($params['children_type']) && !empty($params['children_type'])){
            $type = [$params['children_type']];
        }

        $cutomer_id = Customer::getListByCondition(['type' => $type], ['customer_id'])->toArray();
        $cutomer_id = array_column($cutomer_id, 'customer_id');

        return $cutomer_id;
    }

    protected function hasAgentIncomePriv(){
        $users = CommonEnumModel::getTypeColumn('user_agent_auth','name');
        return in_array(self::$user_name,$users);
    }


    /**
     *只根据客户维度计算调用量等指标
     * @param $params
     * @return mixed
     */
    protected function getUsageByCustomer($params)
    {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsCustomerUsage::getStatListByCustomer($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product']);
        return $usage_data;
    }

    /**
     * 只根据客户维度计算客户收入
     * @param $params
     * @return mixed
     */
    protected function getIncomeByCustomer($params)
    {
        $where = $this->getIncomeWhere($params);
        $income_data = BillProductIncomeV2::getStatListByCustomer($where, $params['apikey_list'], $params['filter_apikey']);
        return $income_data;
    }

    /**
     * 只根据客户维度计算特殊消耗
     * @param $params
     * @return mixed
     */
    protected function getExpendByCustomer($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getCustomerExpendByCustomer(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer']);
        $expend_data['add'] = CustomerExpend::getCustomerExpendByCustomer(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer']);
        return $expend_data;
    }


    /**
     * 根据某列合并多个数组，返回该列全量的数组
     *
     * @param array $data
     * @param string $column
     * @return array
     */
    protected function filterDataByColumn($data = [], $column = ''){
        if(empty($data) || empty($column)){
            return [];
        }

        $tmp = [];
        foreach ($data as $arr){
            if(!is_array($arr)){
                continue;
            }
            $column_arr = array_column($arr, $column);
            $tmp = array_merge($tmp, $column_arr);
        }

        return array_unique($tmp);
    }

    /**
     * 给原数组添加key
     *
     * @param array $data
     * @param string $key
     * @return array
     */
    protected function addDataKey($data = [], $addkey = ''){
        if(empty($data) || empty($addkey) || !is_array($data)){
            return [];
        }
        $tmp = [];
        foreach ($data as  $key => $val){
            $tmp[$val[$addkey]] = $val;
        }

        return $tmp;
    }


    /**
     * 获取指定日期段内每一天的日期
     * @param  Date  $startdate 开始日期
     * @param  Date  $enddate   结束日期
     * @return Array
     */
    public function getDateFromRange($startdate, $enddate){

        $stimestamp = strtotime($startdate);
        $etimestamp = strtotime($enddate);

        // 计算日期段内有多少天
        $days = ($etimestamp-$stimestamp)/86400+1;

        // 保存每天日期
        $date = array();

        for($i=0; $i<$days; $i++){
            $date[] = date('Ymd', $stimestamp+(86400*$i));
        }

        return $date;
    }

    /***
     * 根据日期获取所在年份的第几周
     * @param $tm
     * @return false|string
     */
    public function getDateWeek($date)
    {
        $tm = strtotime($date);
        $w = date('w', $tm) ==0 ? 7 : date('w', $tm);
        if(date('W', $tm) >= 52) // 用周一
        {
            $date = $tm -($w-1)*24*3600 ;
        }
        else // 用周日
        {
            $date = $tm + (7-$w)*24*3600;
        }
        return date("YW", $date);

    }

    /**
     * 获取某个日期所在月的最后一天
     * @param $date
     * @return false|string
     */
    public function getLastDay($date)
    {
        $firstday = date('Y-m-01', strtotime($date));
        $lastday = date('Ymd', strtotime("$firstday +1 month -1 day"));
        return $lastday;
    }

    //获取指定日期的前后几个月
    public function getMonth($sign, $month = '')
    {
        //得到系统的年月
        $tmp_date = empty($month) ? date("Ym") : $month;
        //切割出年份
        $tmp_year = substr($tmp_date,0,4);
        //切割出月份
        $tmp_mon = substr($tmp_date,4,2);
        // 得到当前月份的下几月
        $tmp_nextmonth = mktime(0,0,0,$tmp_mon+$sign,1,$tmp_year);
        // 得到当前月份的前几月
        $tmp_forwardmonth = mktime(0,0,0,$tmp_mon-$sign,1,$tmp_year);
        return $fm_next_month = date("Ym",$tmp_forwardmonth);
    }


    //临时逻辑 信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
    public function checkTransProduct($customer_id, $product_id){
        $customer_ids = ['C20211014HWQSKY', 'C20220228TBQW3U'];//信飞 蓝海银行
        if(in_array($customer_id, $customer_ids) && in_array($product_id,[711, 712])){
            //$product_id = 712;
            return true;
        }

        return false;
    }

    public function transProduct($data){
        $tmp = [];
        $data_712 = [];

        foreach ($data as $key => $item){
            $customer_id = $this->cache_get_customer_id($item['apikey']);
            $flag = $this->checkTransProduct($customer_id, $item['product_id']);
            if($flag){
                $item['product_id'] = 712;
                $total_num = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['total'] ?? 0;
                $item['total'] = $item['total'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['total'] = $total_num + $item['total'];

                $success_num = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['success'] ?? 0;
                $item['success'] = $item['success'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['success'] = $success_num + $item['success'];

                $valid_num = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['valid'] ?? 0;
                $item['valid'] = $item['valid'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['valid'] = $valid_num + $item['valid'];

                $cache_num = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['cache'] ?? 0;
                $item['cache'] = $item['cache'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['cache'] = $cache_num + $item['cache'];

                $total_number = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['number'] ?? 0;
                $item['number'] = $item['number'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['number'] = $total_number + $item['number'];

                $total_money = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money'] ?? 0;
                $item['money'] = $item['money'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money'] = bcadd($total_money, $item['money'], $this->degree);

                $total_money_finance = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money_finance'] ?? 0;
                $item['money_finance'] = $item['money_finance'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money_finance'] = bcadd($total_money_finance, $item['money_finance'], $this->degree);

                $total_money_agent = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money_agent'] ?? 0;
                $item['money_agent'] = $item['money_agent'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money_agent'] = bcadd($total_money_agent, $item['money_agent'], $this->degree);

                $total_cost = $tmp[$item['apikey']][$item['product_id']][$item['operator']]['cost'] ?? 0;
                $item['cost'] = $item['cost'] ??0;
                $tmp[$item['apikey']][$item['product_id']][$item['operator']]['cost'] = bcadd($total_cost, $item['cost'], $this->degree);

                $unique_key = $item['apikey'].'_'.$item['product_id'].'_'.$item['operator'];

                $data_712[$unique_key] = [
                    'apikey' => $item['apikey'],
                    'product_id' => $item['product_id'],
                    'operator' => $item['operator'],
                    'source' => isset($item['source'])?$item['source']:'',
                    'total' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['total'],
                    'success' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['success'],
                    'valid' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['valid'],
                    'cache' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['cache'],
                    'number' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['number'],
                    'money' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money'],
                    'money_agent' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money_agent'],
                    'money_finance' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['money_finance'],
                    'cost' => $tmp[$item['apikey']][$item['product_id']][$item['operator']]['cost'],
                ];
                if(key_exists('month', $item)){
                    $data_712[$unique_key]['month'] = $item['month'];
                }

                unset($data[$key]);
            }

        }

        $data = array_merge($data, $data_712);
        return $data;
    }

    /**
     * 暂存从redis中获取的apikey -> customer_id 优化遍历时间
     * @param $apikey
     * @return mixed
     * @throws \Exception
     */
    private function cache_get_customer_id($apikey){
        if(key_exists($apikey, $this->apikey_customer_id_map)){
            $customer_id = $this->apikey_customer_id_map[$apikey];
        }else{
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
            $this->apikey_customer_id_map[$apikey] = $customer_id;
        }
        return $customer_id;
    }
}
