<?php

namespace App\Http\Repository;

use App\Jobs\ProductInvokedJob;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\MongoInvokedLog;
use App\Models\MongoProductInvoked;
use App\TraitUpgrade\WechatExceptionTrait;
use Symfony\Component\Cache\Tests\Adapter\AbstractRedisAdapterTest;

class ProductInvokedRepository
{
    use WechatExceptionTrait;
    /*
     * 缓存信息的前缀
     * product_invoked_{$product_id}_{$apikey}_{$day}
     * */
    private $cache_key_invoked_prefix = 'product_invoked_';

    /*
     * 缓存使用的连接
     * */
    private $cache_connection = 'db_backend';

    /*
     * job队列
     * */
    private $queue_product_invoked_job = 'product-invoked-amount-into-mongo';

    /*
     * queue使用的连接
     * */
    private $queue_connection = 'redis_backend';

    /*
     * 缓存产品信息
     * */
    private $prefix_product_limit_cache = 'product_limit_';

    /** @var string 账号产品限制日期的关系key */
    private $key_account_product_limit_start_day = 'key_account_product_limit_start_day';

    /*
     * 缓存1小时
     * */
    private $cache_day = 1;

    /**
     * 产品调用量预警flag被重置
     * @throws \Exception
     */
    public function clearWarningFlag()
    {
        // 校验参数
        $this->validateParamsForFlag();

        // 清除标记
        $this->clearFlag();
    }

    /**
     * 清除标记
     */
    private function clearFlag()
    {
        $list_items_wait = [
            'daily_limit', 'month_limit', 'year_limit', 'total_limit',
        ];
        $after           = request()->post('after');
        $before          = request()->post('before');

        $flag = false;
        array_walk($list_items_wait, function ($item) use ($after, $before, &$flag) {
            // 字段发生了变化
            if ($after[$item] != $before[$item]) {
                // 清除标记
                $this->clearFlagWhenLimitChange($item);
                $flag = true;
            }
        });

        // 清理在用量通知JOB缓存的数据
        if ($flag) {
            $this->clearProductCacheWhenWarning();
        }
    }

    /**
     * 清理在用量通知JOB缓存的数据
     */
    private function clearProductCacheWhenWarning()
    {
        $cache_key = $this->getCacheKeyOfProductLimit();
        $this->delCacheKey($cache_key);
    }

    /**
     * 限额缓存的key
     * @return string
     */
    private function getCacheKeyOfProductLimit(): string
    {
        $apikey     = trim(request()->post('apikey'));
        $product_id = trim(request()->post('product_id'));

        return $this->prefix_product_limit_cache.$apikey.'_'.$product_id;
    }

    /**
     * @param string $type
     */
    private function clearFlagWhenLimitChange(string $type)
    {
        $list_limit_standard = config('params.list_limit_standard');

        // 清除签约部分
        $this->clearFlagWhenLimitChangeDo($list_limit_standard['sign_a_contract'], $type);

        // 清除未签约部分
        $this->clearFlagWhenLimitChangeDo($list_limit_standard['not_sign_a_contract'], $type);

        // 清除超过100%的部分
        $this->clearFlagWhenTooMuch($type, $list_limit_standard['too_much']);
    }

    /**
     * 清除超过100%的部分
     *
     * @param string $type
     * @param array  $list_wait
     */
    private function clearFlagWhenTooMuch(string $type, array $list_wait)
    {
        $list_mapping = [
            'daily_limit' => 'day',
            'month_limit' => 'month',
            'year_limit'  => 'year',
            'total_limit' => 'total',
        ];
        $another_type = $list_mapping[$type];
        $prefix_cache = $list_wait[$another_type]['prefix_cache'];
        $this->clearFlagDo($prefix_cache);
    }

    /**
     * 签约相关的删除
     *
     * @param array  $list_wait
     * @param string $type
     */
    private function clearFlagWhenLimitChangeDo(array $list_wait, string $type)
    {
        array_walk($list_wait[$type], function ($item) {
            $this->clearFlagDo($item['prefix_cache']);
        });
    }

    private function clearFlagDo(string $prefix_cache)
    {
        // 生成Key
        $product_id = trim(request()->post('product_id'));
        $apikey     = trim(request()->post('apikey'));
        $limit_key  = $prefix_cache.'_'.$apikey.'_'.$product_id;

        // 删除
        $this->delCacheKey($limit_key);
    }

    /**
     * @param string $cache_key
     */
    private function delCacheKey(string $cache_key)
    {
        if (app('redis')->connection($this->cache_connection)->exists($cache_key)) {
            app('redis')->connection($this->cache_connection)->del($cache_key);
        }
    }

    /**
     * 校验参数
     * @throws \Exception
     */
    private function validateParamsForFlag()
    {
        // before
        $before = request()->post('before', []);
        $this->validateParamsForFlagDetail($before, 'before');

        // after
        $after = request()->post('after', []);
        $this->validateParamsForFlagDetail($after, 'after');

        // 校验其他
        $this->validateOtherForFlag();
    }

    /**
     * 校验其他
     * @throws \Exception
     */
    private function validateOtherForFlag()
    {
        $apikey = request()->post('apikey', '');
        if (!$apikey) {
            throw new \Exception('请传入合法的apikey');
        }

        $product_id = request()->post('product_id', '');
        if (!$product_id || !is_numeric($product_id)) {
            throw new \Exception('请输入合法的product_id');
        }
    }

    /**
     * 校验参数
     *
     * @param array  $data_wait_validate
     * @param string $tip
     *
     * @throws \Exception
     */
    private function validateParamsForFlagDetail(array $data_wait_validate, string $tip = '')
    {
        // 外层校验
        if (!$data_wait_validate || !is_array($data_wait_validate)) {
            throw new \Exception('请传入合法的'.$tip);
        }

        // 详细的校验
        $list_items_wait = [
            'daily_limit', 'month_limit', 'year_limit', 'total_limit',
        ];
        array_walk($list_items_wait, function ($item_validate) use ($data_wait_validate, $tip) {
            $validate_value = $data_wait_validate[$item_validate] ?? '';
            if ($validate_value === '' || !is_numeric($validate_value)) {
                throw new \Exception('请输入合法的'.$tip.'.'.$item_validate);
            }
        });
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function dayInvoked(): array
    {
        // 校验参数
        $this->validateParamsForDayInvoked();

        // 计算当前值
        return $this->computedAndWriteAmount();
    }

    /**
     * 计算 && create or update 调用信息
     * @return array
     * @@throws \Exception
     */
    private function computedAndWriteAmount(): array
    {
        // 计算
        $list_computed = $this->computedAmount();

        //记录日志
        list($product_id, $node_area, $day, $invoked_amount) = $this->listCommonParamsForInvoked();
        $invoked_amount = json_encode($invoked_amount, JSON_UNESCAPED_UNICODE);
        if (!is_null($list_computed)) {
            $result = json_encode($list_computed, JSON_UNESCAPED_UNICODE);
        } else {
            $result = 'null';
        }
        $request_date = date('YmdHis');
        MongoInvokedLog::insert(compact('product_id', 'node_area', 'day', 'invoked_amount', 'result', 'request_date'));

        // 分发write事件
        $this->dispatchWriteJob();

        // 预警机制
        $this->warningIfNeed($list_computed);

        return $list_computed;
    }

    /**
     * 预警机制
     *
     * @param array $list_computed
     *
     * @throws \Exception
     */
    private function warningIfNeed(array $list_computed)
    {
        array_walk($list_computed, function ($item, $apikey) use ($list_computed) {
            list($total_amount, $year_amount, $month_amount, $daily_amount) = [
                $item['history_amount'],
                $item['year_amount'],
                $item['month_amount'],
                $item['daily_amount'],
            ];

            $exception = $total_amount < $year_amount || $total_amount < $month_amount || $total_amount < $daily_amount;
            if ($exception) {
                $input  = request()->post();
                $return = $list_computed;
                $msg    = 'apikey : '.$apikey.' product_id : '.request()->post('product_id').' 总调用量异常 '.json_encode(compact('input', 'return'), JSON_UNESCAPED_UNICODE);
                //$this->wechatException($msg);
            }
        });
    }

    /**
     * 分发write事件
     */
    private function dispatchWriteJob()
    {
        // 参数
        $params_job = $this->getParamsForJob();

        // 分发
        dispatch((new ProductInvokedJob($params_job))
            ->onQueue($this->queue_product_invoked_job)
            ->onConnection($this->queue_connection)
        );
    }

    /**
     * @return array
     */
    private function getParamsForJob(): array
    {
        list($product_id, $node_area, $day, $invoked_amount) = $this->listCommonParamsForInvoked();

        return compact('product_id', 'day', 'node_area', 'invoked_amount');
    }


    /**
     * 计算调用量
     * @return array
     */
    private function computedAmount(): array
    {
        // 容器
        $list_container = [];

        list($product_id, $node_area, $day, $invoked_amount) = $this->listCommonParamsForInvoked();
        collect($invoked_amount)->each(function ($amount, $apikey) use ($product_id, $day, $node_area, &$list_container) {
            $list_container[$apikey] = $this->computedAmountWithoutCache($product_id, $apikey, $node_area, $day, $amount);
        });

        return $list_container;
    }

    /**
     * 有缓存时计算当前值
     *
     * @param int    $product_id
     * @param string $apikey
     * @param string $node_area
     * @param string $day
     * @param int    $amount_day
     *
     * @return array
     */
    private function computedAmountWhenCached(int $product_id, string $apikey, string $node_area, string $day, int $amount_day): array
    {
        // 缓存中的数据
        $list_computed_data = $this->getCacheDataFromRedis($apikey, $product_id, $day);

        // 计算增量
        $list_computed_increment_data = $this->computedIncrementAmount($list_computed_data, $node_area, $amount_day);

        // 重写cache
        return $this->rewriteCache($apikey, $product_id, $day, $list_computed_increment_data);
    }

    /**
     * 重写cache
     *
     * @param string $apikey
     * @param int    $product_id
     * @param string $day
     * @param array  $list_computed_increment_data
     *
     * @return array
     */
    private function rewriteCache(string $apikey, int $product_id, string $day, array $list_computed_increment_data): array
    {
        $cache_key = $this->getCacheKey($apikey, $product_id, $day);
        $this->setCacheForInvoked($list_computed_increment_data, $cache_key);

        return $list_computed_increment_data;
    }

    /**
     * 获取缓存数据
     *
     * @param string $apikey
     * @param int    $product_id
     * @param string $day
     *
     * @return array
     */
    private function getCacheDataFromRedis(string $apikey, int $product_id, string $day): array
    {
        $cache_key  = $this->getCacheKey($apikey, $product_id, $day);
        $cache_data = app('redis')->connection($this->cache_connection)
            ->get($cache_key);

        return json_decode($cache_data, true);
    }


    /**
     * 当没有缓存的时候
     *
     * @param int    $product_id
     * @param string $apikey
     * @param string $node_area
     * @param string $day
     * @param int    $amount_day
     *
     * @return array
     */
    private function computedAmountWithoutCache(int $product_id, string $apikey, string $node_area, string $day, int $amount_day): array
    {
        // 计算
        $list_computed_data = $this->getComputedDataFromMongo(compact('product_id', 'apikey'), $day);

        // 计算增量
        $list_computed_increment_data = $this->computedIncrementAmount($list_computed_data, $node_area, $amount_day);

        // 缓存
        return $this->writeCache($apikey, $product_id, $day, $list_computed_increment_data);
    }

    /**
     * 写缓存
     *
     * @param string $apikey
     * @param int    $product_id
     * @param string $day
     * @param array  $list_computed_increment_data
     *
     * @return array
     */
    private function writeCache(string $apikey, int $product_id, string $day, array $list_computed_increment_data): array
    {
        // 写入缓存
        $cache_key = $this->getCacheKey($apikey, $product_id, $day);
        $this->setCacheForInvoked($list_computed_increment_data, $cache_key);

        return $list_computed_increment_data;
    }

    /**
     * 缓存
     *
     * @param array  $list_computed_data
     * @param string $cache_key
     */
    private function setCacheForInvoked(array $list_computed_data, string $cache_key)
    {
        $list_computed_data = json_encode($list_computed_data, JSON_UNESCAPED_UNICODE);

        app('redis')->connection($this->cache_connection)
            ->set($cache_key, $list_computed_data);

        app('redis')->connection($this->cache_connection)
            ->expire($cache_key, $this->cache_day * 3600);
    }

    /**
     * 计算增量
     *
     * @param array  $list_computed_data
     * @param string $node_area
     * @param int    $amount_day
     *
     * @return array
     */
    private function computedIncrementAmount(array $list_computed_data, string $node_area, int $amount_day): array
    {
        // 增量
        $increment = $this->getIncrement($list_computed_data, $node_area, $amount_day);

        // 增
        return $this->computedIncrementAmountDo($list_computed_data, $node_area, $amount_day, $increment);
    }

    /**
     * 计算增量
     *
     * @param array  $list_computed_data
     * @param string $node_area
     * @param int    $amount_day
     * @param int    $increment
     *
     * @return array
     */
    private function computedIncrementAmountDo(array $list_computed_data, string $node_area, int $amount_day, int $increment): array
    {
        // 设置新日调用
        $list_computed_data['list_day_node_amount'][$node_area] = $amount_day;

        // 新增总量(在历史总量和限定量之间取舍)
        $list_computed_data['total_amount'] = $list_computed_data['total_from_special_day'] !== null ? $list_computed_data['total_from_special_day'] : $list_computed_data['history_amount'];

        return collect($list_computed_data)->map(function ($item, $key) use ($increment) {
            // 如果key 是total_from_special_day 而且等于null 则不进行累加
            if ($key == 'total_from_special_day' && !$item) {
                return $item;
            }
            if ($key != 'list_day_node_amount') {
                $item += $increment;
            }

            return $item;
        })->toArray();
    }

    /**
     * 增量
     *
     * @param array  $list_computed_data
     * @param string $node_area
     * @param int    $amount_day
     *
     * @return int
     */
    private function getIncrement(array $list_computed_data, string $node_area, int $amount_day): int
    {
        $amount_node = $list_computed_data['list_day_node_amount'][$node_area] ?? 0;

        return $amount_day - $amount_node;
    }

    /**
     * 获取当前各个节点的信息
     *
     * @param array  $where
     * @param string $day
     *
     * @return array
     */
    private function getNodeDataFromMongo(array $where, string $day): array
    {
        $where['day'] = $day;

        return MongoProductInvoked::where($where)
            ->get()
            ->pluck('amount', 'node_area')
            ->toArray();
    }

    /**
     * 获取聚合信息
     *
     * @param array  $where
     * @param string $day 传递的日期
     *
     * @return array
     */
    private function getComputedDataFromMongo(array $where, string $day): array
    {
        // 获取大部分的统计信息
        $list_most_data = $this->getMostDataFromMongo($where, $day);

        // 各个节点的数量
        $list_most_data['list_day_node_amount'] = $this->getNodeDataFromMongo($where, $day);

        return $list_most_data;
    }

    /**
     * 获取统计信息(需要的绝大部分都可以获得)
     *
     * @param array  $where
     * @param string $day
     *
     * @return array
     */
    private function getMostDataFromMongo(array $where, string $day): array
    {
        // 日期限制
        $day_search   = date('Ymd', strtotime($day));
        $month_search = date('Ym', strtotime($day));
        $year_search  = date('Y', strtotime($day));

        // 组合条件
        $where_day   = [$day_search, $day_search];
        $where_month = [$month_search.'01', $month_search.'31'];
        $where_year  = [$year_search.'0101', $year_search.'1231'];

        $daily_amount   = $this->aggregateSumAmount($where, $where_day);
        $month_amount   = $this->aggregateSumAmount($where, $where_month);
        $year_amount    = $this->aggregateSumAmount($where, $where_year);
        $history_amount = $this->aggregateSumAmount($where, ['00000000', '99999999']);

        // 计算这个产品的限定日期内的调用量
        $total_from_special_day = $this->getTotalAmountFromSpecialDay($where, $day);

        return compact('daily_amount', 'month_amount', 'year_amount', 'history_amount', 'total_from_special_day');
    }

    /**
     * 计算这个产品的限定日期内的调用量
     *
     * @param array  $where
     * @param string $day
     *
     * @return null
     */
    private function getTotalAmountFromSpecialDay(array $where, string $day)
    {
        // 这个产品的日期
        $limit_start_date = $this->getTheLimitDayForTheProduct($where);

        // 如果产品日期没有设置
        if (!$limit_start_date) {
            return null;
        }

        $day_end   = date('Ymd', strtotime($day));
        $day_begin = date('Ymd', strtotime($limit_start_date));

        return $this->aggregateSumAmount($where, [$day_begin, $day_end]);
    }

    /**
     * 这个产品的先日期
     *
     * @param array $where
     *
     * @return mixed|null
     */
    private function getTheLimitDayForTheProduct(array $where)
    {
        // 获取全部的产品信息
        $list_relationship_account_product = $this->getAllAccountProduct();

        // 获取key
        $key = $this->getCacheKeyForAccountProduct($where['apikey'], $where['product_id']);

        return $list_relationship_account_product[$key] ?? null;
    }


    /**
     * 获取所有账号产品的信息
     * @return array
     */
    private function getAllAccountProduct(): array
    {
        // 如果存在缓存
        if ($this->determineHasCacheForAccountProduct()) {
            return app('redis')->connection($this->cache_connection)
                ->hGetAll($this->key_account_product_limit_start_day);
        }

        // 组装需要的格式
        $list_accounts        = Account::getListByCondition([], ['account_id', 'apikey'])->pluck('apikey', 'account_id')->all();
        $relationship_mapping = AccountProduct::getListByCondition([])->reduce(function ($carry, $item) use ($list_accounts) {
            list($account_id, $product_id) = [
                $item->account_id,
                $item->product_id,
            ];

            if (!array_key_exists($account_id, $list_accounts)) {
                $msg = '调用量推送接口 account_id : '.$account_id.'缺少对应的账号';
                $this->wechatException($msg);
                throw new \Exception($msg);
            }

            // 生成key
            $key         = $this->getCacheKeyForAccountProduct($list_accounts[$account_id], $product_id);
            $carry[$key] = $item->limit_start_date;

            return $carry;
        }, []);

        // 缓存
        return $this->setAccountProductCache($relationship_mapping);
    }

    /**
     * 缓存
     *
     * @param array $relationship_mapping
     *
     * @return array
     */
    private function setAccountProductCache(array $relationship_mapping): array
    {
        app('redis')->connection($this->cache_connection)
            ->hMset($this->key_account_product_limit_start_day, $relationship_mapping);

        // 缓存11分钟
        app('redis')->connection($this->cache_connection)
            ->expire($this->key_account_product_limit_start_day, 660);

        return $relationship_mapping;
    }

    /**
     * 生成唯一key
     *
     * @param string $apikey
     * @param string $product_id
     *
     * @return string
     */
    private function getCacheKeyForAccountProduct(string $apikey, string $product_id): string
    {
        return $apikey.'_'.$product_id;
    }

    /**
     * 是否存在cache
     * @return bool
     */
    private function determineHasCacheForAccountProduct(): bool
    {
        return (bool)app('redis')->connection($this->cache_connection)
            ->exists($this->key_account_product_limit_start_day);
    }

    /**
     * 聚合获取某段时间的总和
     *
     * @param array $where
     * @param array $limit_days
     *
     * @return int
     */
    private function aggregateSumAmount(array $where, array $limit_days): int
    {
        return (int)MongoProductInvoked::where($where)
            ->whereBetween('day', $limit_days)
            ->sum('amount');
    }

    /**
     * 是否有缓存
     *
     * @param string $apikey
     * @param string $product_id
     * @param string $day
     *
     * @return bool
     */
    private function determineInvokedHasCache(string $apikey, string $product_id, string $day): bool
    {
        $cache_key = $this->getCacheKey($apikey, $product_id, $day);

        return app('redis')
            ->connection($this->cache_connection)
            ->exists($cache_key);
    }

    /**
     * 获取缓存key
     *
     * @param string $apikey
     * @param int    $product_id
     * @param string $day
     *
     * @return string
     */
    private function getCacheKey(string $apikey, int $product_id, string $day): string
    {
        return $this->cache_key_invoked_prefix.$product_id.'_'.$apikey.'_'.$day;
    }

    /**
     * 共用参数
     * @return array
     */
    private function listCommonParamsForInvoked(): array
    {
        $product_id     = (int)trim(request()->post('product_id', ''));
        $node_area      = trim(request()->post('node_area', ''));
        $amount_date    = trim(request()->post('amount_date', ''));
        $day            = date('Ymd', strtotime($amount_date));
        $invoked_amount = request()->post('data', []);

        return [$product_id, $node_area, $day, $invoked_amount];
    }

    /**
     * 校验参数
     * @throws \Exception
     */
    private function validateParamsForDayInvoked()
    {
        $product_id = trim(request()->post('product_id', ''));
        if (!$product_id || !is_numeric($product_id)) {
            throw new \Exception('请输入合法的product_id');
        }

        $amount_date = trim(request()->post('amount_date', ''));
        if (!$amount_date) {
            throw new \Exception('请输入amount_date');
        }

        $node_area = trim(request()->post('node_area', ''));
        if (!$node_area) {
            throw new \Exception('请输入node_area');
        }
        $node_area = strtolower($node_area);
        if (!in_array($node_area, config('params.list_nodes'))) {
            $msg = 'product: '.$product_id.' 调用量推送的节点异常, 推送节点: '.$node_area.' msg : '.json_encode(request()->post(), JSON_UNESCAPED_UNICODE);
            $this->wechatException($msg);
            throw new \Exception('请输入合法的node_area');
        }

        $data = request()->post('data', []);
        if (!$data || !is_array($data)) {
            throw new \Exception('请传入合法的data');
        }
    }
}