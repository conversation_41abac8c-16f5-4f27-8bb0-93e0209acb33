<?php

namespace App\Http\Repository;

use App\Models\Account;
use App\Models\Approval;
use App\Models\BxfStrategy;
use App\Models\BxfStrategyConfig;
use App\Models\Customer;
use App\Models\Product;
use App\Providers\RedisCache\RedisCache;
use App\Utils\Helpers\Func;
use Exception;

/**
 *
 */
class BxfStrategyConfigRepository
{
    /**
     * @throws Exception
     */
    public function __construct() {
        $user_cookie = request()->post('user_cookie');

        if(empty($user_cookie)){
            throw new Exception("请登录后进行操作");
        }
    }


    /**
     * 获取策略列表
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-08 11:36:41
     */
    public function strategy_config_list(): array {
        $page        = intval(request()->post('page', 1));
        $limit       = intval(request()->post('limit', 20));
        $account_id  = request()->post('account_id');
        $product_id  = request()->post('product_id');
        $strategy_id = request()->post('strategy_id');//数组,多个
        $_status     = request()->post('status');

        $status = [];
        if(!empty($_status)){
            $status = [$_status];
        }

        $list  = BxfStrategyConfig::getStrategyConfigList($account_id, $product_id, $strategy_id, $page, $limit, $status);
        $count = BxfStrategyConfig::getStrategyConfigCount($account_id, $product_id, $strategy_id, $status);

        //补充客户,帐号,产品,策略名称
        $account_ids = $strategy_ids = [];
        foreach($list as &$info){
            $account_ids[] = $info['account_id'];
            $info['strategy_id'] = explode(",",$info['strategy_id']);
            $strategy_ids = array_merge($strategy_ids,$info['strategy_id']);
        }

        $account_list = Account::getListByAccountIds($account_ids);
        $account_customer_map = array_column($account_list, 'customer_id','account_id');
        $account_list = array_column($account_list, 'account_name','account_id');


        $strategy_list = BxfStrategy::getStrategyListByStrategyIds($strategy_ids);
        $strategy_list = array_column($strategy_list, null, 'strategy_id');
        foreach($strategy_list as &$s_info) {
            $s_info['pv_dict']             = json_encode(json_decode($s_info['pv_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $s_info['map_dict']            = json_encode(json_decode($s_info['map_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $s_info['pv_seg_innet_dict']   = json_encode(json_decode($s_info['pv_seg_innet_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $s_info['pv_seg_noinnet_dict'] = json_encode(json_decode($s_info['pv_seg_noinnet_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            $s_info['filter_dict']         = json_encode(json_decode($s_info['filter_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        }

        foreach($list as &$info){
            if($info['account_id'] == 'default') {
                $info['account_name']  = '默认';
                $info['customer_name'] = '默认';
            }else {
                $info['account_name']  = $account_list[$info['account_id']];
                $info['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($account_customer_map[$info['account_id']]);
            }
            $info['product_name'] = RedisCache::instance('productId_productName_mapping')->get($info['product_id']);
            $_strategy_list = [];
            foreach($info['strategy_id'] as $strategy_id){
                $_strategy_list[$strategy_id] = $strategy_list[$strategy_id];
            }
            $info['strategy_infos'] = $_strategy_list;
        }

        return [
            'list'  => $list,
            'count' => $count
        ];
    }


    /**
     * 添加,编辑策略校验参数
     *
     * @param $para_data
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-09 13:54:35
     */
    private function check_data_and_get_account_info($para_data): array {
        if(empty($para_data['user_cookie'])){
            throw new Exception("请登录后进行操作");
        }
        $username = Func::getUserNameFromCookie($para_data['user_cookie']);
        $strategy_config_data = [];
        if(empty($para_data['account_id'])){
            throw new Exception("请选择帐号");
        }
        if(empty($para_data['product_id'])){
            throw new Exception("请选择产品");
        }
        if(empty($para_data['strategy_id'])){
            throw new Exception("请选择策略");
        }
        $para_data['strategy_id'] = array_unique($para_data['strategy_id']);
        foreach($para_data['strategy_id'] as &$strategy_id){
            if(empty($strategy_id)){
                unset($strategy_id);
            }
        }
        if(count($para_data['strategy_id']) != 2){
            throw new Exception("请选择两个策略");
        }

        $id = isset($para_data['id'])?$para_data['id']:0;//这个id用来标记是否是编辑
        //校验唯一
        $is_unique = $this->check_unique($para_data['account_id'],$para_data['product_id'],$para_data['strategy_id'],$id);
        if(!$is_unique){
            throw new Exception("配置已经存在请勿重复添加或编辑!");
        }
        //校验是否在审批中

        $strategy_config_data['account_id']  = $para_data['account_id'];
        $strategy_config_data['product_id']  = $para_data['product_id'];
        $strategy_config_data['strategy_id'] = implode(',',$para_data['strategy_id']);
        $strategy_config_data['remark']      = $para_data['remark'];

        //处理默认值
        if($para_data['account_id'] == 'default') {
            $account_info = [
                'account_id'  => 'default',
                'customer_id' => 'default'
            ];
        } else {
            $account_info = Account::getAccountInfoByAccountId($para_data['account_id']);
        }


        return [
            $strategy_config_data,
            $username,
            $account_info,
        ];
    }


    /**
     * 添加策略
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-01-08 15:00:44
     *
     */
    public function add(): bool {
        $account_id  = request()->post('account_id');
        $product_id  = request()->post('product_id');
        $user_cookie = request()->post('user_cookie');

        [$strategy_config_data,$username,$account_info]= $this->check_data_and_get_account_info(request()->post());//参数校验 帐号和产品除了配置默认值外其余需要校验是否存在

        $now = date("Y-m-d H:i:s");

        $strategy_config_data['admin']      = $username;
        $strategy_config_data['status']     = BxfStrategyConfig::STATUS_AVAILABLE;
        $strategy_config_data['created_at'] = $now;
        $strategy_config_data['updated_at'] = $now;

        $apply_content = $this->add_diff($strategy_config_data);

        //添加到审批
        return ApprovalRepository::addApproval($account_info['customer_id'],$account_id,$product_id,Approval::URL_BXF_STRATEGY_CONFIG_ADD,$strategy_config_data,$apply_content,$user_cookie);
    }


    /**
     *
     *
     * @param $new
     *
     * @return string
     * <AUTHOR> 2024-01-18 18:32:34
     */
    private function add_diff($new): string {
        $apply_content = '添加策略配置:'.PHP_EOL;
        if($new['account_id'] == 'default') {
            $account_info = ['account_name' => '默认'];
        }else{
            $account_info = Account::getAccountInfoByAccountId($new['account_id']);
        }
        $apply_content .= '帐号: ' . $account_info['account_name'] . PHP_EOL;

        $product_info = Product::getProductInfoByProductId($new['product_id']);
        $apply_content .= '产品: ' . $product_info['product_name'] . PHP_EOL;

        $new_strategy_ids    = explode(",",$new['strategy_id']);
        $len = count($new_strategy_ids);
        $strategy_list = BxfStrategy::getStrategyListByStrategyIds($new_strategy_ids);
        $strategy_list = array_column($strategy_list, 'strategy_name', 'strategy_id');
        for ($i = 0; $i < $len; $i ++) {
            $ne_name = $strategy_list[$new_strategy_ids[$i]] ?? '';
            $apply_content .= '策略'.($i + 1).": " . $ne_name . PHP_EOL;
        }

        $apply_content .= '备注: ' . $new['remark'] . PHP_EOL;


        return $apply_content;
    }

    /**
     * 审批通过 - 添加策略
     *
     * @param $strategy_data
     *
     * @static
     * @return bool
     * <AUTHOR> 2024-01-09 11:47:11
     */
    public static function approval_deal_add($strategy_data): bool {
        return BxfStrategyConfig::add($strategy_data);
    }



    /**
     * 添加策略
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-01-08 15:00:44
     *
     */
    public function edit(): bool {
        $account_id  = request()->post('account_id');
        $product_id  = request()->post('product_id');
        $id          = request()->post('id');
        $user_cookie = request()->post('user_cookie');

        [$strategy_config_data,$username,$account_info]= $this->check_data_and_get_account_info(request()->post());//参数校验 帐号和产品除了配置默认值外其余需要校验是否存在

        $strategy_config_info = BxfStrategyConfig::getStrategyConfigInfoById($id);

        $strategy_config_data['id']         = $id;
        $strategy_config_data['admin']      = $username;
        $strategy_config_data['status']     = BxfStrategyConfig::STATUS_AVAILABLE;
        $strategy_config_data['updated_at'] = date("Y-m-d H:i:s");

        if(($strategy_config_info['account_id'] == 'default' && $account_info['account_id'] != 'default') ||
        ($strategy_config_info['account_id'] != 'default' && $account_info['account_id'] == 'default')){
            throw new Exception("默认配置不可更改帐号,其他配置不可更改为默认配置!");
        }

        // $apply_content = '编辑策略配置:'.json_encode($strategy_config_data,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $apply_content = $this->edit_diff($strategy_config_info,$strategy_config_data);

        //添加到审批
        return ApprovalRepository::addApproval($account_info['customer_id'],$account_id,$product_id,Approval::URL_BXF_STRATEGY_CONFIG_EDIT,$strategy_config_data,$apply_content,$user_cookie);
    }

    /**
     *
     *
     * @param $origin
     * @param $new
     *
     * @return string
     * <AUTHOR> 2024-01-18 17:53:00
     */
    private function edit_diff($origin,$new): string {
        $apply_content = '编辑策略配置:'.PHP_EOL;
        if($origin['account_id'] != $new['account_id']) {
            $account_list = Account::getListByAccountIds([$origin['account_id'],$new['account_id']]);
            $account_list = array_column($account_list, 'account_name', 'account_id');
            $apply_content .= '帐号: '. $account_list[$origin['account_id']] . ' -> ' . $account_list[$new['account_id']] . PHP_EOL;
        }
        if($origin['product_id'] != $new['product_id']) {
            $product_list = Product::getProductListByProductIds([$origin['product_id'],$new['product_id']]);
            $product_list = array_column($product_list, 'product_name', 'product_id');
            $apply_content .= '产品: '.$product_list[$origin['product_id']] . ' -> ' . $product_list[$new['product_id']] . PHP_EOL;
        }
        if($origin['strategy_id'] != $new['strategy_id']) {
            $origin_strategy_ids = explode(",",$origin['strategy_id']);
            $new_strategy_ids    = explode(",",$new['strategy_id']);

            $len = max(count($origin_strategy_ids),count($new_strategy_ids));

            $strategy_list = BxfStrategy::getStrategyListByStrategyIds(array_merge($origin_strategy_ids,$new_strategy_ids));
            $strategy_list = array_column($strategy_list, 'strategy_name', 'strategy_id');

            for ($i = 0; $i < $len; $i ++) {
                $os_name = $strategy_list[$origin_strategy_ids[$i]] ?? '';
                $ne_name = $strategy_list[$new_strategy_ids[$i]] ?? '';
                if($os_name == $ne_name){
                    continue;
                }
                $apply_content .= '策略'.($i + 1).": ".$os_name . ' -> ' . $ne_name . PHP_EOL;
            }
        }
        if($origin['remark'] != $new['remark']) {
            $apply_content .= '备注: '.$origin['remark'] . ' -> ' . $new['remark'] . PHP_EOL;
        }

        return $apply_content;
    }

    /**
     * 校验添加或者编辑配置是否唯一
     *
     * @param $account_id
     * @param $product_id
     * @param $strategy_ids
     * @param int $strategy_config_id
     *
     * @return bool
     * <AUTHOR> 2024-01-12 00:43:31
     */
    private function check_unique($account_id, $product_id, $strategy_ids, int $strategy_config_id = 0){
        $strategy_config_list = BxfStrategyConfig::getListByAccountIdAndProductId($account_id,$product_id,$strategy_config_id);
        sort($strategy_ids);
        $data_string = $account_id.$product_id.implode($strategy_ids,'');

        foreach($strategy_config_list as $sc_info){
            $_strategy_ids = explode(",",$sc_info['strategy_id']);
            sort($_strategy_ids);
            $_data_string = $sc_info['account_id'].$sc_info['product_id'].implode($_strategy_ids,'');
            if($data_string == $_data_string){
                return false;
            }
        }
        return true;
    }
    /**
     * 审批通过 - 添加策略配置
     *
     * @param $strategy_config_data
     *
     * @return bool
     * @static
     * <AUTHOR> 2024-01-09 11:47:11
     */
    public static function approval_deal_edit($strategy_config_data): bool {
        $strategy_config_id = $strategy_config_data['id'];
        unset($strategy_config_data['id']);
        return BxfStrategyConfig::edit($strategy_config_id,$strategy_config_data);
    }

    /**
     * 审批通过 - 禁用
     *
     * @param $strategy_config_data
     *
     * @static
     * @return bool
     * <AUTHOR> 2024-01-11 17:27:03
     */
    public static function approval_deal_del($strategy_config_data): bool {
        $strategy_config_id = $strategy_config_data['id'];
        unset($strategy_config_data['id']);
        return BxfStrategyConfig::edit($strategy_config_id,$strategy_config_data);
    }

    /**
     *
     *
     * @return array
     * <AUTHOR> 2024-01-09 18:14:47
     *
     * @static
     */
    public static function info(): array {
        $id = request()->post('id');
        return BxfStrategyConfig::getStrategyConfigInfoById($id);
    }


    /**
     * 禁用策略配置
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-01-12 00:04:54
     *
     */
    public function del(): bool {
        $id  = request()->post('id');
        $user_cookie = request()->post('user_cookie');

        $strategy_config_info = BxfStrategyConfig::getStrategyConfigInfoById($id);

        $strategy_ids = explode(",",$strategy_config_info['strategy_id']);

        if($strategy_config_info['account_id'] == 'default'){
            throw new Exception("默认配置不可禁用!");
        }

        if(!empty($strategy_config_info) && $strategy_config_info['status'] != BxfStrategyConfig::STATUS_AVAILABLE){
            throw new Exception("策略配置状态错误,禁用失败!");
        }

        $account_info = Account::getAccountInfoByAccountId($strategy_config_info['account_id']);
        $product_info = Product::getProductInfoByProductId($strategy_config_info['product_id']);

        $strategy_list = BxfStrategy::getStrategyListByStrategyIds($strategy_ids);
        $strategy_list = array_column($strategy_list, 'strategy_name');
        $strategy_names = implode(",",$strategy_list);

        $username = Func::getUserNameFromCookie($user_cookie);

        $strategy_config_data['id']         = $id;
        $strategy_config_data['admin']      = $username;
        $strategy_config_data['status']     = BxfStrategyConfig::STATUS_FORBIDDEN;
        $strategy_config_data['updated_at'] = date("Y-m-d H:i:s");

        $apply_content = '帐号: '.$account_info['account_name'].PHP_EOL.'产品: '.$product_info['product_name'].PHP_EOL.'策略: '.$strategy_names.PHP_EOL.'状态: 启用 -> 禁用';

        //添加到审批
        return ApprovalRepository::addApproval($account_info['customer_id'],$strategy_config_info['account_id'],$strategy_config_info['product_id'],Approval::URL_BXF_STRATEGY_CONFIG_DEL,$strategy_config_data,$apply_content,$user_cookie);
    }
}