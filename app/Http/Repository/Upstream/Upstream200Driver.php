<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/16 0016
 * Time: 18:30
 */

namespace App\Http\Repository\Upstream;

use App\Console\Commands\CacheDriver\CacheDriverFacade;

//邦秒验产品渠道统计
class Upstream200Driver extends Driver
{
    public function __construct()
    {
        $this->product_id        = 200;
        $cacheDriverFacade       = new CacheDriverFacade();
        $this->verify_product_id = $cacheDriverFacade->get200ChildrenProductId();
        //邦秒验的渠道都是一样的，所以只需要获取其中的一个子产品的就可以了
        $this->channel_config = $cacheDriverFacade->getUpstreamChannelByProductId(201);
    }

    /**
     * 整理数据
     *
     * @access protected
     *
     * @param $data array 用户数据
     *
     * @return void
     **/
    protected function disposeUpstreamData($data)
    {
        $this->data = [];
        foreach ($data as $item) {
            $item = $this->disposeItem($item);
            if ($item) {
                array_push($this->data, $item);
            }
        }
    }

    /**
     * 整理每一条数据
     *
     * @access protected
     *
     * @param $item array 每一个客户的数据
     *
     * @return array|false
     **/
    protected function disposeItem($item)
    {
        //channel
        if (!array_key_exists('channel', $item)) {
            return false;
        }
        $channel = $item['channel'];

        $yd = array_key_exists('yd', $item) ? intval($item['yd']) : 0;
        $lt = array_key_exists('lt', $item) ? intval($item['lt']) : 0;
        $dx = array_key_exists('dx', $item) ? intval($item['dx']) : 0;

        //计算各种量
        $succ   = $yd + $lt + $dx;
        $failed = 0;
        $all    = $succ + $failed;
        $data   = compact('all', 'succ', 'failed', 'yd', 'lt', 'dx');

        //补充数据
        $apikey      = $this->apikey;
        $node        = $this->node;
        $product_id  = $this->product_id;
        $date        = $this->date;
        $account_id  = $this->account_id;
        $customer_id = $this->customer_id;

        return compact('account_id', 'customer_id', 'apikey', 'node', 'channel', 'product_id', 'date', 'data');
    }
}