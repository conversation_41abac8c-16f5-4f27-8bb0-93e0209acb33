<?php

namespace App\Http\Repository\Upstream;

use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Models\MongoUpstream;

//每个产品的上游统计量入库的驱动
abstract class Driver
{
    protected $data = [];
    protected $date;
    protected $apikey;
    protected $node;
    protected $account_id;
    protected $customer_id;
    protected $channel_config;

    //当前的产品ID
    protected $product_id;

    //校验传递的产品product_id
    protected $verify_product_id;

    /**
     * 校验参数
     *
     * @access public
     *
     * @param $data array 数据
     *
     * @return true|string 返回true表示无错误，返回的字符串代表出现错误，字符串内容是错误信息
     **/
    public function verify($data)
    {
        //校验产品
        if (($res = $this->verifyProductId($data)) !== true) {
            return $res;
        }

        //校验日期
        if (($res = $this->verifyDate($data)) !== true) {
            return $res;
        }

        //校验apikey
        if (($res = $this->verifyApikey($data)) !== true) {
            return $res;
        }

        //校验节点
        if (($res = $this->verifyNode($data)) !== true) {
            return $res;
        }

        //校验是否存在data
        if (($res = $this->verifyData($data)) !== true) {
            return $res;
        }

        //设置每个数据中需要填充的数据 product_id apikey node date
        $this->setFillData($data);

        //处理数据
        $this->disposeUpstreamData($data['data']);

        return true;
    }

    abstract protected function disposeUpstreamData($data);

    /**
     * 校验产品
     *
     * @access protected
     *
     * @param $data array POST数据
     *
     * @return true|string
     **/
    protected function verifyProductId($data)
    {
        if (!array_key_exists('product_id', $data)) {
            return '缺少必须参数 [product_id]';
        }
        $product_id = $data['product_id'];
        if (!in_array($product_id, $this->verify_product_id)) {
            return "非法的product_id字段 [{$product_id}]";
        }
        return true;
    }

    /**
     * 校验日期
     *
     * @access protected
     *
     * @param $data array POST数据
     *
     * @return true|string
     **/
    protected function verifyDate($data)
    {
        if (!array_key_exists('date', $data)) {
            return '缺少必须参数 [date]';
        }
        return true;
    }

    /**
     * 校验apikey
     *
     * @access protected
     *
     * @param $data array POST数据
     *
     * @return true|string
     **/
    protected function verifyApikey($data)
    {
        if (!array_key_exists('apikey', $data)) {
            return '缺少必须参数 [apikey]';
        }
        return true;
    }

    /**
     * 校验节点
     *
     * @access protected
     *
     * @param $data array POST数据
     *
     * @return true|string
     **/
    protected function verifyNode($data)
    {
        if (!array_key_exists('node', $data)) {
            return '缺少必须参数 [node]';
        }
        $node = $data['node'];
        if (!in_array($node, config('params.upstream_nodes'))) {
            return "非法的node字段 [{$node}]";
        }
        return true;
    }

    /**
     * 校验data
     *
     * @access protected
     *
     * @param $data array POST数据
     *
     * @return true|string
     **/
    protected function verifyData($data)
    {
        //校验data
        if (!array_key_exists('data', $data)) {
            return '缺少必须参数 [data]';
        }
        $data = $data['data'];
        if (empty($data)) {
            return '上游统计量数据为空';
        }

        //校验渠道是否包含不存在的渠道
        $channel         = array_column($data, 'channel');
        $notAllowChannel = array_diff($channel, $this->channel_config);
        if (!empty($notAllowChannel)) {
            return '非法的channel字段 ' . implode('、', $notAllowChannel);
        }

        return true;
    }

    /**
     * 数据入库
     *
     * @access public
     *
     * @return void
     **/
    public function create()
    {
        foreach ($this->data as $item) {
            MongoUpstream::updateOrCreate(array_only($item, ['apikey', 'date', 'channel', 'node', 'product_id']),
                $item);
        }
    }

    /**
     * 设置数据的补充内容
     *
     * @access protected
     *
     * @param $data array 校验通过的数据
     *
     * @return void
     **/
    protected function setFillData($data)
    {
        $this->product_id  = intval($data['product_id']);
        $this->node        = $data['node'];
        $this->date        = intval($data['date']);
        $this->apikey      = $data['apikey'];
        $cacheDriverFacade = new CacheDriverFacade();
        $this->customer_id = $cacheDriverFacade->getCustomerIdByApikey($this->apikey);
        $this->account_id  = $cacheDriverFacade->getAccountIdByApikey($this->apikey);
    }
}