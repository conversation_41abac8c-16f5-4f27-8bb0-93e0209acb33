<?php

namespace App\Http\Repository\Upstream;

use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Models\Upstream;

//邦企查上游渠道统计
class Upstream401Driver extends Driver
{
    public function __construct()
    {
        $this->product_id        = 401;
        $this->verify_product_id = [$this->product_id];
        $this->channel_config    = (new CacheDriverFacade)->getUpstreamChannelByProductId(401);
    }

    /**
     * 整理数据
     *
     * @access protected
     *
     * @param $data array 用户数据
     *
     *
     * @return void
     **/
    protected function disposeUpstreamData($data)
    {
        $this->data = [];
        foreach ($data as $item) {
            $item = $this->disposeItem($item);
            if ($item) {
                array_push($this->data, $item);
            }
        }
    }

    /**
     * 整理每一条数据
     *
     * @access protected
     *
     * @param $item array 每一个客户的数据
     *
     * @return array|false
     **/
    protected function disposeItem($item)
    {
        //channel
        if (!array_key_exists('channel', $item)) {
            return false;
        }
        $channel = $item['channel'];

        $succ   = array_key_exists('succ', $item) ? intval($item['succ']) : 0;
        $failed = array_key_exists('failed', $item) ? intval($item['failed']) : 0;


        //计算各种量
        $all  = $succ + $failed;
        $yd   = 0;
        $lt   = 0;
        $dx   = 0;
        $data = compact('all', 'succ', 'failed', 'yd', 'lt', 'dx');

        //补充数据
        $apikey      = $this->apikey;
        $node        = $this->node;
        $product_id  = $this->product_id;
        $date        = $this->date;
        $customer_id = $this->customer_id;
        $account_id  = $this->account_id;

        return compact('account_id', 'customer_id', 'apikey', 'node', 'channel', 'product_id', 'date', 'data');
    }
}