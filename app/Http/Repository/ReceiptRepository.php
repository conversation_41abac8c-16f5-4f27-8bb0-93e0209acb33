<?php

namespace App\Http\Repository;

use App\Http\Repository\Receiver\PushReceiptRepository;
use App\Imports\ReceiptSplitImport;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\BillCustomerIncomeV2;
use App\Models\Common\CommonEnumModel;
use App\Models\CustomerExpend;
use App\Models\Receiver\PushReceipt;
use App\Models\Remit;
use App\Models\SystemUser;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Utils\Helpers\Func;
use App\Define\Common;

class ReceiptRepository
{
    protected $receipt_serial = '';
    protected $source = '';
    protected $start_date = '';
    protected $end_date = '';
    
    // 客户名称
    protected $name = '';

    // 根据客户名称模糊查询出来的客户id
    protected $customer_ids = [];
    public function getSplitCustomer()
    {
        $this->initParams();
        $data = $this->getCustomerMonthIncome($this->customer_ids);
        $count = count($data);

        //已拆金额
        $split_money = Receipt::where(['parent_serial'=>$this->receipt_serial,'delete_time'=>0])->sum('money');

        $costomer_list = Customer::select(['customer_id', 'name', 'company'])->get()->toArray();
        $costomer_list = array_column($costomer_list, null, 'customer_id');
        try {
            $list = [];
            $total_money= 0.0000 ;
            foreach ($data as $customer_id => $value) {
                $total_money = bcadd($total_money, $value['money'], 6);
                $info = [];
                $info['customer_id'] = $customer_id;
                $info['customer_name'] = $costomer_list[$customer_id]['name'];
                $info['company'] = $costomer_list[$customer_id]['company'];
                $info['month'] = date('Y-m', strtotime($value['month'] . '01'));
                $info['money'] = bcadd($value['money'],0,4);
                $list[] = $info;
            }
        } catch (Exception $e) {
            echo $e->getMessage();
        }
        $receiptdata = Receipt::where(['receipt_serial'=>$this->receipt_serial])->first()->toArray();
        
        $receipt_money = $receiptdata['money'];
        //待拆金额
        $unsplit_money = $receipt_money-$split_money;
        return compact('list', 'count', 'split_money', 'total_money', 'receipt_money','unsplit_money');
    }

    protected function getCustomerMonthIncome($customer_ids)
    {
        $result=[];
        //征信机构费率
        $sourceFeeMap = CommonEnumModel::getTypeMaps('agent_income_fee');   //征信机构扣除后收入费率
        $sourceFeeRate = isset($sourceFeeMap[$this->source])?$sourceFeeMap[$this->source]:1;
        BillCustomerIncomeV2::select(DB::raw('SUM(money) as money, SUM(number) as number, date, apikey'))
        ->where(function ($query) use ($customer_ids) {
            if ($customer_ids) {
                $apikeys = Account::whereIn('customer_id', $customer_ids)
                    ->pluck('apikey');
                if (!empty($apikeys)) {
                    $query->whereIn('apikey', $apikeys);
                }
            }
        })
        ->where(function ($query) {
            if ($this->source !== '') {
                $query->where('source', $this->source);
            }
        })
        ->where('date', '>=', $this->start_date)
        ->where('date', '<=', $this->end_date)
        ->groupBy('apikey')
        ->get()
        ->map(function ($item) use (&$result) {
            $apikey                            = $item['apikey'];
            $customer_id                       = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
            $month = date('Ym', strtotime($item['date']));
            $this->initItem($result, $customer_id, $month);
            $result[$customer_id]['money']     = bcadd($result[$customer_id]['money'], $item['money'], 6);
            $result[$customer_id]['number']    = bcadd($result[$customer_id]['number'], $item['number']);
            $result[$customer_id]['month']    = $month;

            $result[$customer_id][$month]['money']     = bcadd($result[$customer_id][$month]['money'], $item['money'], 6);
            $result[$customer_id][$month]['number']    = bcadd($result[$customer_id][$month]['number'], $item['number']);
            $result[$customer_id][$month]['customer_id'] = $customer_id;
        });

        //客户月度平账数据,分加减两部分
		$where = [
			['profile_show_date', '>=', $this->start_date],['profile_show_date', '<=', $this->end_date],
			['source',$this->source]
		];

		$sub = CustomerExpend::getCustomerDateExpend(array_merge($where, [['type', 1]]), []);
		$add = CustomerExpend::getCustomerDateExpend(array_merge($where, [['type', 2]]), []);
        foreach ($sub as $item) {
            $customer_id = $item['customer_id'];
			if(!isset($result[$customer_id])){
                $this->initItem($result, $customer_id, date('Ym', strtotime($item['profile_show_date'])));
                // continue;
			}
            $result[$customer_id]['number'] -= $item['fee_number'];
            $result[$customer_id]['money'] =  bcsub($result[$customer_id]['money'],$item['money']*$sourceFeeRate,6);
        }
        foreach ($add as $item) {
            $customer_id = $item['customer_id'];
            if(!isset($result[$customer_id])){
                $this->initItem($result, $customer_id, date('Ym', strtotime($item['profile_show_date'])));
                // continue;
            }
            $result[$customer_id]['number'] += $item['fee_number'];
            $result[$customer_id]['money'] = bcadd( $result[$customer_id]['money'],$item['money']*$sourceFeeRate,6);
        }
        return $result;
    }

    protected function initItem(&$data, $customer_id, $month)
    {
        $data[$customer_id]=[];
        $data[$customer_id]['money']=0.0000;
        $data[$customer_id]['number']=0;
        $data[$customer_id][$month]['money'] = 0.0000;
        $data[$customer_id][$month]['number'] = 0;
        $data[$customer_id]['month'] = $month;
    }

    /**
     * 获取查询条件
     * @return array
     */
    public function initParams()
    {
        $this->source= request()->post('source', '');
        // 客户名称过滤
        $this->name= request()->post('name', '');
        if($this->name){
            $this->customer_ids = Customer::where('name','like','%'.$this->name.'%')->pluck('customer_id');
        }
        $this->receipt_serial = request()->post('receipt_serial', null);
        $remit_date = request()->post('remit_date', null);

        if (empty($remit_date)) {
            $remit_date[0] = date('Ymd', strtotime('first day of last month'));
            $remit_date[1] = date('Ymd', strtotime('last day of last month'));
        }
        $remit_date = is_string($remit_date) ? explode(',', $remit_date) : $remit_date;
        //202207 ->20220701-20220731
        $this->start_date = $remit_date[0].'01';
        $this->end_date = date('Ymd', strtotime('last day of this month', strtotime($remit_date[1].'01')));
    }


    /**
     * @throws Exception
     *
     */
    public function split()
    {
        $this->initParams();
        //
        $receiptdata = Receipt::where(
            ['receipt_serial'=>$this->receipt_serial]
        )->first()->toArray();


        $customer_money_map = request()->post('customer_money_map',[]);
        unset($receiptdata['id']);
        unset($receiptdata['receipt_serial']);
        $needIdNum = count($customer_money_map);
        if($needIdNum){
            // 预生成ID 且保证唯一    
            $cids = $this->getRandSplitId($needIdNum);
            $exist = Receipt::where(['receipt_serial'=>['in',$cids]])->count();           
            if($exist){
                $cids = $this->getRandSplitId($needIdNum);
            }

            //获取推送的收款单数据
            $push_receipt_info = PushReceiptRepository::getInfoByTradeId($this->receipt_serial);
            $customer_ids = array_column($customer_money_map,'customer_id');
            $customer_infos = Customer::getCustomerListByCustomerIds(['*'], $customer_ids);
            $customer_infos = array_column($customer_infos,null,'customer_id');

            $all_salesman = SystemUser::getAllUsers();
            $all_salesman = array_column($all_salesman,'realname','username');

            $push_remit_data = [];

            try {

                DB::beginTransaction();

                foreach ($customer_money_map as $map) {
                    if($map['money'] == 0){//过滤金额为0的数据
                        continue;
                    }
                    // 新增朴道收款
                    $receiptdata['receipt_serial'] = array_pop($cids);
                    $receiptdata['source'] = $this->source;
                    $receiptdata['status'] = Receipt::$STATUS_REMIT;     // 已认款
                    $receiptdata['money'] = $map['money'];
                    $receiptdata['parent_serial'] = $this->receipt_serial;
                    Receipt::insert($receiptdata);

                    //新增认款
                    $data['customer_id'] = $map['customer_id'];
                    $data['name'] = $receiptdata['name'];
                    $data['money'] = $map['money'];
                    $data['bank'] = $receiptdata['bank'];
                    $data['remit_date'] = $receiptdata['remit_date'];
                    $data['remit_serial'] = $receiptdata['receipt_serial'];
                    $data['receipt_serial'] = $receiptdata['receipt_serial'];

                    $data['source']           = $this->source;
                    $data['status']           = 3;    //已认款
                    $data['parent_serial']    = $this->receipt_serial;
                    $data['admin']            = $receiptdata['admin'];
                    $data['create_time']      = time();
                    $data['remit_unconsumed'] = Func::mockMysqlEncrypt($map['money']);
                    $data['remit_balance']    = Func::mockMysqlEncrypt($map['money']);
                    Remit::insert($data);

                    //TODO 后付费对齐数据 可考虑自动拆单到当月

                    //调用财务系统,推送拆单数据
                    if(!empty($push_receipt_info)) {
                        $push_remit_data[] = [
                            'record_id'      => $push_receipt_info['record_id'],
                            'receipt_serial' => $receiptdata['receipt_serial'],
                            'money'          => $map['money'],
                            'company'        => $customer_infos[$map['customer_id']]['company'],
                            'salesman'       => $all_salesman[$customer_infos[$map['customer_id']]['salesman']],
                        ];
                    }
                }

                DB::commit();
            }catch(Exception $e){
                DB::rollBack();
            }

            foreach($push_remit_data as $push_remit_info){
                PushReceiptRepository::push_remit(
                    $push_remit_info['record_id'],
                    $push_remit_info['receipt_serial'],
                    $push_remit_info['money'],
                    $push_remit_info['company'],
                    $push_remit_info['salesman']
                );
            }
        }

    }


    /**
     * 根据getSplitCustomer方法改写,返回格式相同,
     * 添加上传文件,匹配其中的数据,
     * 不使用时间去查询客户的消耗进行返回,
     * 而是返回excel中的客户以及金额数据
     * 返回后前端会执行全选操作,方便运营操作
     *
     * @return array
     * @throws Exception
     *
     *<AUTHOR> 2024-04-23 19:53:33
     */
    public function split_by_excel(){
        $file = request()->file('file');
        $this->initParams();

        //查询收款单详情
        $receipt_info = Receipt::where(['receipt_serial'=>$this->receipt_serial])->first()->toArray();

        //校验文件
        if (!$file->isValid()) {
             throw new Exception('文件异常');
        }
        //校验文件后缀
        $ext = $file->getClientOriginalExtension();
        if (!in_array($ext, ['xls', 'xlsx'])) {
            throw new Exception('上传文件类型不对,请检查是否为xls或 xlsx类型');
        }
        $size = $file->getSize();//获取上传文件的大小
        if(1024 * 1024 < $size){
            throw new Exception('文件超过500KB');
        }

        //获取文件名
        $originalName = $file->getClientOriginalName();
        $originalName = explode('.', $originalName);
        $noOriginalName = $originalName[0];//不带扩展名的文件名
        $realPath = $file->getRealPath();
        $filename = $noOriginalName . '_' . date('YmdHi') . '_' . uniqid() . '.' . $ext;

        $bool = Storage::disk('excel')->put($filename, file_get_contents($realPath));
        if (!$bool) {
            throw new Exception('上传文件服务存储有误');
        }
        $saveFilePath = storage_path() . '/excel/upload/' . $filename;

        //获取文件内容
        $data = Excel::toArray(new ReceiptSplitImport, $saveFilePath);

        //去除表头
        $data = $data[0];
        unset($data[0]);

        $customer_list = Customer::select(['customer_id', 'name', 'company'])->get()->toArray();
        $customer_list = array_column($customer_list, null, 'name');

        $list = [];
        $count = count($data);
        $total_money= 0.0000 ;
        $receipt_money = $receipt_info['money'];
        $unsplit_money = $receipt_info['money'];
        foreach($data as $line => $info){
            $money = $info[1];
            $customer_name = $info[0];
            if(!isset($customer_list[$customer_name])){
                throw new Exception(($line + 1) .'行客户名称错误!');
            }

            $customer_info = $customer_list[$customer_name];
            $total_money = bcadd($total_money, $money, 6);
            $info = [];
            $info['customer_id'] = $customer_info['customer_id'];
            $info['customer_name'] = $customer_info['name'];
            $info['company'] = $customer_info['company'];
            $info['month'] = '';
            $info['money'] = bcadd($money,0,4);
            $list[] = $info;
        }
        $unsplit_money = $unsplit_money - $total_money;
        $split_money = $total_money;
        if($receipt_money > $total_money){
            throw new Exception('上传文件拆分金额小于收款单金额!');
        }
        if($receipt_money < $total_money){
            throw new Exception('上传文件拆分金额大于收款单金额!');
        }
        return compact('list', 'count', 'split_money', 'total_money', 'receipt_money','unsplit_money');
    }


    public function getRandSplitId($num){
        $ids = [];
        for ($i=0; $i < $num; $i++) { 
           $ids[] = 'autosplit'.str_random(10);
        }
        return $ids;
    }


    /**
     * 创建渠道预收款单
     *
     * @param $source
     * @param $money
     * @param $name string 开票公司名
     * @param $preset_list
     *
     * @return array
     * @static
     * <AUTHOR> 2024-08-28 14:13:51
     */
    public static function setPresetReceiptSource($source,$money,$name,$preset_list){
        $receipt_serial = Func::createUUid("PRESET_");
        $now = time();
        //父收款单
        $receiptdata['receipt_serial'] = $receipt_serial;
        $receiptdata['source']         = $source;
        $receiptdata['name']           = $name;
        $receiptdata['status']         = Receipt::$STATUS_REMIT;     // 已认款
        $receiptdata['money']          = $money;
        $receiptdata['remit_date']     = $now;
        $receiptdata['admin']          = 'preset';
        $receiptdata['from']           = 1;
        Receipt::insert($receiptdata);

        $sub_receipt_serials = [];
        //子收款单
        foreach($preset_list as $idx => $preset) {
            $sub_receipt_serial = Func::createUUid("PRESET_".$idx."_");
            // 新增朴道收款
            $receiptdata['receipt_serial'] = $sub_receipt_serial;
            $receiptdata['source']         = $source;
            $receiptdata['status']         = Receipt::$STATUS_REMIT;  // 已认款
            $receiptdata['money']          = $preset['money'];
            $receiptdata['parent_serial']  = $receipt_serial;
            $receiptdata['admin']          = 'preset';
            Receipt::insert($receiptdata);

            //新增认款
            $data['customer_id']      = $preset['customer_id'];
            $data['name']             = $name;
            $data['money']            = $preset['money'];
            $data['bank']             = '';
            $data['remit_date']       = $now;
            $data['remit_serial']     = $sub_receipt_serial;
            $data['receipt_serial']   = $sub_receipt_serial;
            $data['source']           = $source;
            $data['status']           = 3;    //已认款
            $data['parent_serial']    = $receipt_serial;
            $data['admin']            = 'preset';
            $data['create_time']      = $now;
            $data['invoice_status']   = Common::INVOICE_STATUS_APPLY;
            $data['remit_unconsumed'] = Func::mockMysqlEncrypt($preset['money']);
            $data['remit_balance']    = Common::AES_ENCRYPT_ZERO;
            Remit::insert($data);
            $sub_receipt_serials[$preset['customer_id']] = $sub_receipt_serial;
        }

        return $sub_receipt_serials;
    }

    /**
     * 创建电话帮收款单 先票后款
     *
     * @param $customer_id
     * @param $source
     * @param $money
     * @param $name
     *
     * @static
     * @return string
     * <AUTHOR> 2024-08-28 17:10:14
     */
    public static function setPresetReceipt($customer_id,$source,$money,$name){
        $receipt_serial = Func::createUUid("PRESET_");
        $now = time();
        //父收款单
        $receiptdata['receipt_serial'] = $receipt_serial;
        $receiptdata['source']         = $source;
        $receiptdata['name']           = $name;
        $receiptdata['status']         = Receipt::$STATUS_REMIT;     // 已认款
        $receiptdata['money']          = $money;
        $receiptdata['remit_date']     = $now;
        $receiptdata['from']           = 1;
        $receiptdata['admin']          = 'preset';
        Receipt::insert($receiptdata);

        //新增认款
        $data['customer_id']      = $customer_id;
        $data['name']             = $name;
        $data['money']            = $money;
        $data['bank']             = '';
        $data['remit_date']       = $now;
        $data['remit_serial']     = $receipt_serial;
        $data['receipt_serial']   = $receipt_serial;
        $data['source']           = $source;
        $data['status']           = 3;    //已认款
        $data['parent_serial']    = $receipt_serial;
        $data['admin']            = 'preset';
        $data['create_time']      = $now;
        $data['invoice_status']   = Common::INVOICE_STATUS_APPLY;
        $data['remit_unconsumed'] = Func::mockMysqlEncrypt($money);
        $data['remit_balance']    = Common::AES_ENCRYPT_ZERO;
        Remit::insert($data);

        return $receipt_serial;
    }

    public function split_price(){
        $sReceiptSerial = request()->post('receipt_serial', '');
        $admin = request()->post('admin', '');
        $aDataList = request()->post('data', []);

        if (!$sReceiptSerial) {
            throw new Exception('请求参数[收款流水单号]不能为空!');
        }
        if (empty($aDataList)) {
            throw new Exception('请求参数[拆单信息]不能为空!');
        }

        // 到款信息
        $oRemit = Remit::selectFormat(['*'])->where(['receipt_serial' => $sReceiptSerial])->first();
        if (!$oRemit) {
            throw new Exception(sprintf('到款单(%s)不存在!', $sReceiptSerial));
        }

        $sTime = date('Y-m-d H:i:s');
        // 历史全部删除
        $aWhere = [
            ['receipt_serial', '=', $sReceiptSerial],
            ['deleted_at', '=', null],
        ];
        $aHisRelList = \App\Models\Invoice\RelRemitConsume::aGetListByCond($aWhere);

        // 上次 拆单金额
        $fHisTotalRelMoney = 0;
        if ($aHisRelList) {
            foreach ($aHisRelList as $aItem) {
                $fHisTotalRelMoney = bcadd($fHisTotalRelMoney, $aItem['rel_money'], 6);
            }
            $aUpdate = [
                'deleted_at' => $sTime,
            ];
            \App\Models\Invoice\RelRemitConsume::where($aWhere)->update($aUpdate);
        }

        // 插入新关联关系
        $aProductId = array_column($aDataList, 'product_id');
        $aProductList = \App\Models\Product::getProductListByProductIds($aProductId);
        $aProductFatherMap = array_column($aProductList, 'father_id', 'product_id');

        $aInsertList = [];
        // 本次 拆单金额
        $fTotalRelMoney = 0;
        foreach ($aDataList as $aItem) {
            $fTotalRelMoney = bcadd($fTotalRelMoney, $aItem['money'], 6);
            $aInsertList[] = [
                'customer_id' => $oRemit['customer_id'],
                'source' => $oRemit['source'],
                'remit_serial' => $oRemit['remit_serial'],
                'receipt_serial' => $oRemit['receipt_serial'],
                'remit_money' => $oRemit['money'],
                'consume_month' => str_replace('-', '', $aItem['month']),
                'product_id' => $aItem['product_id'],
                'father_id' => $aProductFatherMap[$aItem['product_id']] ?? 0,
                'consume_money' => $aItem['consume_money'],
                'rel_money' => $aItem['money'],
                'admin' => $admin,
                'created_at' => $sTime,
            ];
        }
        \App\Models\Invoice\RelRemitConsume::batchInsert($aInsertList);

        // 更新到款余额
        // 加 上次拆分金额
        $fRemitUnconsumed = bcadd($oRemit->remit_unconsumed, $fHisTotalRelMoney, 6);
        // 减 本次拆单金额
        $fRemitUnconsumed = bcsub($fRemitUnconsumed, $fTotalRelMoney, 6);
        $oRemit->remit_unconsumed = $fRemitUnconsumed;
        $oRemit->save();

        //更新收款单状态
        $aWhere = [
            'status' => 2,
            'delete_time' => 0,
            'receipt_serial' => $sReceiptSerial,
        ];
        \App\Models\Receipt::where($aWhere)->update(['status' => 3]);

        return true;
    }

    public function split_price_test(){
        $sReceiptSerial = request()->post('receipt_serial', '');
        $admin = request()->post('admin', '');
        $aDataList = request()->post('data', []);

        if (!$sReceiptSerial) {
            throw new Exception('请求参数[收款流水单号]不能为空!');
        }
        if (empty($aDataList)) {
            throw new Exception('请求参数[拆单信息]不能为空!');
        }

        // 到款信息
        $oRemit = Remit::selectFormat(['*'])->where(['receipt_serial' => $sReceiptSerial])->first();
        if (!$oRemit) {
            throw new Exception(sprintf('到款单(%s)不存在!', $sReceiptSerial));
        }


        $sTime = date('Y-m-d H:i:s');
        $aWhere = [
            ['receipt_serial', '=', $sReceiptSerial],
            ['deleted_at', '=', null],
        ];
        $aHisRelList = \App\Models\Invoice\RelRemitConsume::aGetListByCond($aWhere);


        echo '<pre>';
        print_r($oRemit->toArray());
        echo '</pre>';
        echo '<pre>';
        print_r($aHisRelList);
        echo '</pre>';
        exit;
        die;

        // 数据还原

        if ($aHisRelList) {
            $aUpdate = [
                'deleted_at' => $sTime,
            ];
            \App\Models\Invoice\RelRemitConsume::where($aWhere)->update($aUpdate);
        }

        $oRemit->remit_unconsumed = $oRemit->money;
        $oRemit->remit_balance = $oRemit->money;
        $a = $oRemit->save();
        echo '<pre>';
        var_dump($a);
        echo '</pre>';

        $aWhere = [
            'status' => 3,
            'delete_time' => 0,
            'receipt_serial' => $sReceiptSerial,
        ];
        $b = \App\Models\Receipt::where($aWhere)->update(['status' => 2]);
        echo '<pre>';
        var_dump($b);
        echo '</pre>';


        $oRemit = Remit::selectFormat(['*'])->where(['receipt_serial' => $sReceiptSerial])->first();
        echo '<pre>';
        print_r($oRemit->toArray());
        echo '</pre>';

        exit;

        return true;
    }

    public function get_split_price(){
        $sReceiptSerial = request()->post('receipt_serial', '');
        $sCustomerId = request()->post('customer_id', '');
        $iSource = request()->post('source', '-1');
        $iPaymentType = request()->post('payment_type', '0');

        // 查询所有主产品
        $aFatherList = \App\Models\Product::getFatherProducts();
        $aFatherNameMap = array_column($aFatherList, 'product_name', 'product_id');


        // 查询 到款流水号 关联的消耗信息
        // 查询 客户+渠道 所有 到款-消耗 关联关系
        $fSerialRelMoney = 0;
        $aSerialRelList = [];
        $aOtherRelMap = [];

        $aWhere = [
            ['customer_id', '=', $sCustomerId],
            ['source', '=', $iSource],
            ['deleted_at', '=', null],
        ];
        $aRelList = \App\Models\Invoice\RelRemitConsume::aGetListByCond($aWhere);
        foreach ($aRelList as $aItem) {
            if ($sReceiptSerial == $aItem['receipt_serial']) {
                $iRelMonth = date('Y-m', strtotime($aItem['consume_month'] . '01'));
                $aRel = [
                    'id' => $aItem['id'],
                    'product_id' => $aItem['product_id'],
                    'product_name' => $aFatherNameMap[$aItem['product_id']] ?? $aItem['product_id'],
                    'money' => $aItem['rel_money'],
                    'consume_money' => $aItem['consume_money'], // todo 这个值可能是变化的
                    'month' => $iRelMonth,
                    'checked' => $aItem['product_id'] . '_' . $iRelMonth . '_' . round($aItem['rel_money'], 2),
                ];

                $aSerialRelList[] = $aRel;
                $fSerialRelMoney = bcadd($fSerialRelMoney, $aItem['rel_money'], 6);
            } else {
                $fRelMoney = $aOtherRelMap[$aItem['consume_month']][$aItem['product_id']] ?? null;
                if (isset($fRelMoney)) {
                    $fRelMoney = bcadd($fRelMoney, $aItem['rel_money'], 6);
                } else {
                    $fRelMoney = $aItem['rel_money'];
                }

                $aOtherRelMap[$aItem['consume_month']][$aItem['product_id']] = $fRelMoney;
            }
        }

        // 查询 客户+渠道+时间段[202201, 昨天] 所有消耗
        // todo
        // 按 月份-主产品 聚合
        $sMock = '{"202305":{"money":2.8,"month":"2023-05","product":{"200":{"money":2.8,"father_id":"200"}}},"202306":{"money":131.52,"month":"2023-06","product":{"200":{"money":131.52,"father_id":"200"}}},"202307":{"money":2316.96,"month":"2023-07","product":{"200":{"money":2316.96,"father_id":"200"}}},"202308":{"money":9.68,"month":"2023-08","product":{"200":{"money":9.68,"father_id":"200"}}},"202309":{"money":96.72,"month":"2023-09","product":{"200":{"money":96.72,"father_id":"200"}}},"202310":{"money":0.64,"month":"2023-10","product":{"200":{"money":0.64,"father_id":"200"}}},"202311":{"money":706.24,"month":"2023-11","product":{"200":{"money":706.24,"father_id":"200"}}},"202312":{"money":711.36,"month":"2023-12","product":{"200":{"money":711.36,"father_id":"200"}}},"202401":{"money":3622.56,"month":"2024-01","product":{"200":{"money":3622.56,"father_id":"200"}}},"202402":{"money":2072.72,"month":"2024-02","product":{"200":{"money":2072.72,"father_id":"200"}}},"202403":{"money":1745.28,"month":"2024-03","product":{"200":{"money":1745.28,"father_id":"200"}}},"202404":{"money":1631.2,"month":"2024-04","product":{"200":{"money":1631.2,"father_id":"200"}}},"202405":{"money":1378.64,"month":"2024-05","product":{"200":{"money":1378.64,"father_id":"200"}}},"202406":{"money":942.08,"month":"2024-06","product":{"200":{"money":942.08,"father_id":"200"}}},"202407":{"money":764.08,"month":"2024-07","product":{"200":{"money":764.08,"father_id":"200"}}}}';
        $aConsumeData = json_decode($sMock, true);

        $is_checked = $aSerialRelList ? array_column($aSerialRelList, 'checked') : [];

        // 总数据处理
        foreach ($aConsumeData as $key => &$value) {
            $value['unremit_money'] = $value['money'];
            $check = false;
            foreach ($value['product'] as $kk => &$vv) {
                $vv['unremit_money'] = $vv['money'];
                if ($aOtherRelMap) { // 已认款的数据
                    $month = str_replace('-', '', $value['month']);
                    $remit_money = $aOtherRelMap[$month][$vv['father_id']] ?? 0;
                    // 产品未认款的
                    $vv['unremit_money'] = round(($vv['money'] - $remit_money), 2);
                    // 月份未认款
                    $value['unremit_money'] = round(($value['unremit_money'] - $remit_money), 2);
                }
                if ($vv['unremit_money'] == 0) {
                    unset($value['product'][$kk]);
                }
                $checked = $vv['father_id'] . '_' . $value['month'] . '_' . round($vv['unremit_money'], 2);
                $vv['checked'] = $is_checked && in_array($checked, $is_checked);
                $is_checked && in_array($checked, $is_checked) && $check = true;
                if ($value['unremit_money'] == 0) {
                    unset($aConsumeData[$key]);
                }
            }

            $value['checked'] = $check;
        }
        unset($value);

        $option = '';
        foreach ($aFatherNameMap as $value => $show) {
            $checked = (null == $value) ? 'selected' : '';
            $option  .= "<option value='{$value}' {$checked}>{$show}</option>";
        }


        // 响应
        return [
            'receipt_serial' => $sReceiptSerial,
            'payment_type' => $iPaymentType,
            'money' => $fSerialRelMoney,
            'money_data' => $aConsumeData,
            'list_data' => $aSerialRelList,
            'product_data' => $aFatherNameMap,
            'product_option' => $option,
        ];
    }


    function makeOption($data, $default = null, $extra = [])
    {
        $option = '';
        foreach ($data as $value => $show) {
            $checked = ($default == $value) ? 'selected' : '';
            $option  .= "<option value='{$value}' {$checked}>{$show}</option>";
        }

        return $option;
    }

    public function get_split_price_test(){


        $sMock = '{"202305":{"money":2.8,"month":"2023-05","product":{"200":{"money":2.8,"father_id":"200"}}},"202306":{"money":131.52,"month":"2023-06","product":{"200":{"money":131.52,"father_id":"200"}}},"202307":{"money":2316.96,"month":"2023-07","product":{"200":{"money":2316.96,"father_id":"200"}}},"202308":{"money":9.68,"month":"2023-08","product":{"200":{"money":9.68,"father_id":"200"}}},"202309":{"money":96.72,"month":"2023-09","product":{"200":{"money":96.72,"father_id":"200"}}},"202310":{"money":0.64,"month":"2023-10","product":{"200":{"money":0.64,"father_id":"200"}}},"202311":{"money":706.24,"month":"2023-11","product":{"200":{"money":706.24,"father_id":"200"}}},"202312":{"money":711.36,"month":"2023-12","product":{"200":{"money":711.36,"father_id":"200"}}},"202401":{"money":3622.56,"month":"2024-01","product":{"200":{"money":3622.56,"father_id":"200"}}},"202402":{"money":2072.72,"month":"2024-02","product":{"200":{"money":2072.72,"father_id":"200"}}},"202403":{"money":1745.28,"month":"2024-03","product":{"200":{"money":1745.28,"father_id":"200"}}},"202404":{"money":1631.2,"month":"2024-04","product":{"200":{"money":1631.2,"father_id":"200"}}},"202405":{"money":1378.64,"month":"2024-05","product":{"200":{"money":1378.64,"father_id":"200"}}},"202406":{"money":942.08,"month":"2024-06","product":{"200":{"money":942.08,"father_id":"200"}}},"202407":{"money":764.08,"month":"2024-07","product":{"200":{"money":764.08,"father_id":"200"}}}}';
        $aConsumeData = json_decode($sMock, true);

        $aConsumeMap = [];
        foreach ($aConsumeData as $iMonth => $aItem) {
            $aConsumeMap[$iMonth] = $aItem['money'];
        }

        $aReceiptSerial = ['C0646SF0008YKFZ', 'C0646LH0008WN5Z'];

        $aRemitList = \App\Models\Remit::select()->whereIn('receipt_serial', $aReceiptSerial)->where(['source' => 0])->get()->toArray();
        $aRemitMap = array_column($aRemitList, null,'receipt_serial');
//        echo '<pre>';
//        print_r($aRemitMap);
//        echo '</pre>';
//        exit;

        $aRemitSplitPrice = \App\Models\RemitSplitPrice::select()->whereIn('receipt_serial', $aReceiptSerial)->where(['delete_time' => 0])->get()->toArray();

        $sCustomerId = 'C20230517YCKXYE';
        $iSource = 0;


        $aProductMap = Product::getAllParentIdMap();
        $sTime = date('Y-m-d H:i:s');
        $aInsertList = [];
        foreach ($aRemitSplitPrice as $aItem) {
            $aInsertList[] = [
                'customer_id' => $sCustomerId,
                'source' => $iSource,
                'remit_serial' => $aItem['receipt_serial'],
                'receipt_serial' => $aItem['receipt_serial'],
                'remit_money' => $aRemitMap[$aItem['receipt_serial']]['money'],
                'consume_month' => $aItem['month'],
                'product_id' => $aItem['product_id'],
                'father_id' => $aProductMap[$aItem['product_id']],
                'consume_money' => $aConsumeMap[$aItem['month']],
                'rel_money' => $aItem['money'],
                'admin' => $aItem['admin'],
                'created_at' => $sTime,
            ];
        }
        //$a = \App\Models\Invoice\RelRemitConsume::batchInsert($aInsertList);

        echo '<pre>';
        var_dump('end');
        echo '</pre>';
        exit;
    }


    /**
     * 认票时拆单 渠道认票
     *
     * @param $customer_money_map array [[客户id,金额],[客户id2,金额2]...]
     * @param $receipt_serial
     * @param $source
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-09-08 17:19:28
     */
    public static function relate_remit($customer_money_map,$receipt_serial,$source){
        if($source == 0) {
            throw new Exception('仅渠道认票！');
        }
        $push_receipt_info = PushReceiptRepository::getInfoByTradeId($receipt_serial);

        $receiptdata = Receipt::getInfoByReceiptSerialAndStatus($receipt_serial);
        unset($receiptdata['id']);
        unset($receiptdata['receipt_serial']);

        $source_company_map = [
            1  => '朴道征信有限公司',
            2  => '郑州数据交易中心有限公司',
            10 => '浙江大数据交易中心有限公司',
        ];

        $receipt_serial_customer_map = [];
        $idx = 0;
        $total_money = 0;
        foreach ($customer_money_map as $map) {
            if($map['money'] == 0){//过滤金额为0的数据
                continue;
            }
            $idx++;
            // 新增朴道收款
            $child_receipt_serial = 'autosplit_' . $receipt_serial . '_' . $idx;

            $receiptdata['receipt_serial'] = $child_receipt_serial;
            $receiptdata['source']         = $source;
            $receiptdata['status']         = 3; //拆单
            $receiptdata['money']          = $map['money'];
            $receiptdata['admin']          = 'push_receipt';//认票
            $receiptdata['from']           = 0;
            $receiptdata['parent_serial']  = $receipt_serial;
            Receipt::insert($receiptdata);

            //新增认款
            $data['customer_id']      = $map['customer_id'];
            $data['name']             = $receiptdata['name'];
            $data['money']            = $map['money'];
            $data['bank']             = $receiptdata['bank'];
            $data['remit_date']       = $receiptdata['remit_date'];
            $data['remit_serial']     = $child_receipt_serial;
            $data['receipt_serial']   = $child_receipt_serial;
            $data['source']           = $source;
            $data['status']           = 3;    //已认款
            $data['parent_serial']    = $receipt_serial;
            $data['admin']            = 'push_receipt';//认票
            $data['invoice_status']   = Common::INVOICE_STATUS_INVOICE;
            $data['remit_unconsumed'] = Func::mockMysqlEncrypt($map['money']);
            $data['remit_balance']    = Common::AES_ENCRYPT_ZERO;
            $data['create_time']      = time();
            Remit::insert($data);

            $receipt_serial_customer_map[$map['customer_id']] = $child_receipt_serial;

            //调用财务系统,推送拆单数据
            $total_money = bcadd($total_money,$map['money'],2);
        }

        $company = $source_company_map[$source];

        //推送财务
        PushReceiptRepository::push_remit(
            $push_receipt_info['record_id'],
            $receipt_serial,
            $total_money,
            $company,
            '周静'//'jing.zhou'
        );
        Receipt::deleteByReceiptSerials([$receipt_serial]);

        return $receipt_serial_customer_map;
    }
}
