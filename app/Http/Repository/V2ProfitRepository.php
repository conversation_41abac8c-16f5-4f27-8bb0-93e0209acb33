<?php
/**
 * Created by PhpStorm.
 * User: gang8
 * Date: 2020/5/19
 * Time: 17:22
 */

namespace App\Http\Repository;


use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\MoneyRecharge;
use App\Models\MongoBillDay;
use App\Models\MongoBillMonth;
use App\Models\MongoTransferBill;
use App\Models\MongoUpstreamBill;
use App\Models\Product;
use App\Models\ProductType;
use App\Models\Upstream;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Class V2ProfitRepository
 * @package App\Http\Repository
 */
class V2ProfitRepository
{
	protected $customerModel;
	protected $billDayMongoModel;
	protected $billMonthMongoModel;
	protected $customerExpendModel;
	protected $upstreamBillMongoModel;
	protected $productModel;
	protected $rechargeModel;
	
	//615子产品ID
	protected $children615ProductIds = [];
	//邦秒验子产品ID
	protected $children200ProductIds = [];
	//评分类字段产品ID
	protected $storeProductIds = [];
	//统计类字段产品ID
	protected $normalProductIds = [];
	//615非分流子产品的统计
	protected $noneDisperseProductIds = [];
	//产品ID->产品名称映射关系表
	protected $productId2ProductNameMapping = [];
	
	//邦信分详单版包含的产品ID
	protected $bxfDetailProductIds = [
		101,
		105,
	];
	//邦秒配包含的产品ID
	protected $bmpProductIds = [
		601,
		604,
		104,
	];
	//邦秒验特殊的产品ID
	protected $bmyExtraProductIds = [801];
	//金盾特殊的产品ID
	protected $goldenShieldExtraProductIds = [
		603,
		612,
		613,
		614,
		616,
		664,
	];
	
	public function __construct()
	{
		$this->customerModel          = new Customer();
		$this->billDayMongoModel      = new MongoBillDay();
		$this->billMonthMongoModel    = new MongoBillMonth();
		$this->customerExpendModel    = new CustomerExpend();
		$this->upstreamBillMongoModel = new MongoUpstreamBill();
		$this->productModel           = new Product();
		$this->rechargeModel          = new MoneyRecharge();
	}
	
	/**
	 * 获取客户维度的利润统计表
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/5/20 11:22
	 *
	 * @throws \Exception
	 * @return array
	 **/
	public function getCustomerProfit()
	{
		//获取请求的参数
		$params = $this->getParams();
		
		//按客户维度获取营收数据
		$income = $this->getCustomerIncome($params);
		
		//按客户维度获取成本数据
		$cost = $this->getCustomerCost($params);
		
		//按客户维度获取余额数据
		$balance = $this->getCustomerBalance($params);
		
		//融合数据
		return $this->mergeCustomerProfit($income, $cost, $balance);
		
	}
	
	/**
	 * 通过客户维度整合数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 15:55
	 *
	 * @param $income  array 营收数据
	 * @param $cost    array 成本数据
	 * @param $balance array 余额数据
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function mergeCustomerProfit($income, $cost, $balance)
	{
		$result = array_map(function ($incomeItem) {
			$customer_id = $incomeItem['customer_id'];
			$money       = $incomeItem['money'];
			$number      = $incomeItem['number'];
			$income_arr  = compact('money', 'number');
			
			$money    = $number = 0;
			$cost_arr = compact('money', 'number');
			
			$money      = $income_arr['money'] - $cost_arr['money'];
			$number     = $income_arr['number'] - $cost_arr['number'];
			$profit_arr = compact('money', 'number');
			
			$recharge    = $income = $balance = $special = 0;
			$balance_arr = compact('recharge', 'income', 'balance', 'special');
			
			return compact('customer_id', 'income_arr', 'cost_arr', 'profit_arr', 'balance_arr');
		}, $income);
		
		
		array_walk($cost, function ($costItem) use (&$result) {
			$customer_id = $costItem['customer_id'];
			$money       = $costItem['money'];
			$number      = $costItem['number'];
			$cost_arr    = compact('money', 'number');
			if (array_key_exists($customer_id, $result)) {
				$result[$customer_id]['cost_arr'] = $cost_arr;
			} else {
				$money      = $number = 0;
				$income_arr = compact('money', 'number');
				
				$recharge    = $income = $balance = $special = 0;
				$balance_arr = compact('recharge', 'income', 'balance', 'special');
				
				$result[$customer_id] = compact('customer_id', 'income_arr', 'cost_arr', 'balance_arr');
			}
			
			$money                              = $result[$customer_id]['income_arr']['money'] - $result[$customer_id]['cost_arr']['money'];
			$number                             = $result[$customer_id]['income_arr']['number'] - $result[$customer_id]['cost_arr']['number'];
			$profit_arr                         = compact('money', 'number');
			$result[$customer_id]['profit_arr'] = $profit_arr;
			
		});
		
		array_walk($balance, function ($balanceItem) use (&$result) {
			$customer_id = $balanceItem['customer_id'];
			$recharge    = $balanceItem['recharge'];
			$income      = $balanceItem['income'];
			$balance     = $balanceItem['balance'];
			$special     = array_get($balanceItem, 'special', 0);
			$balance_arr = compact('recharge', 'income', 'balance', 'special');
			
			if (array_key_exists($customer_id, $result)) {
				$result[$customer_id]['balance_arr'] = $balance_arr;
			} else {
				$money      = $number = 0;
				$income_arr = compact('money', 'number');
				$cost_arr   = compact('money', 'number');
				$profit_arr = compact('money', 'number');
				
				$result[$customer_id] = compact('income_arr', 'cost_arr', 'profit_arr', 'balance_arr');
			}
		});
		
		return $result;
	}
	
	/**
	 * 按客户维度获取余额统计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 15:18
	 *
	 * @param $params array 请求的参数
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function getCustomerBalance($params)
	{
		//查询合计的营收数据
		$params['start_date'] = 20170101;
		$params['end_date']   = intval(date('Ymd', strtotime('-1 days')));
		$params['productIds'] = [];
		$income               = $this->getCustomerIncome($params);
		
		//查询合计的充值数据
		$recharge = $this->getCustomerRecharge($params);
		
		//查询每个客户的特殊消耗
		//$special = $this->getCustomerSpecialTotal($params);
		$special = [];
		
		//计算余额
		return $this->computeCustomerBalance($income, $recharge, $special);
	}
	
	
	/**
	 * 查询每个客户的特殊消耗费用
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/25 14:32
	 *
	 * @param $params array 请求的数据
	 *
	 * @return array
	 **/
	protected function getCustomerSpecialTotal($params)
	{
		$result = [];
		
		$data = CustomerExpend::select([
			'customer_id',
			'money',
			'type',
		])
							  ->where(function ($query) use ($params) {
								  $customerIds = $params['customerIds'];
								  if (!empty($customerIds)) {
									  $query->whereIn('customer_id', $customerIds);
								  }
							  })
							  ->where('start_date', '>=', date('Ym', strtotime($params['start_date'])))
							  ->where('start_date', '<=', date('Ym', strtotime($params['end_date'])))
							  ->get()
							  ->map(function ($item) use (&$result) {
								  $type        = $item['type'];
								  $customer_id = $item['customer_id'];
								  $money       = $item['money'];
								  if (!array_key_exists($customer_id, $result)) {
									  $result[$customer_id] = 0;
								  }
			
								  switch ($type) {
									  case 1:
										  $result[$customer_id] += $money;
										  break;
									  case 2:
										  $result[$customer_id] -= $money;
										  break;
								  }
			
								  return $item;
							  })
							  ->toArray();
		
		return $result;
	}
	
	/**
	 * 计算客户维度的余额
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 15:44
	 *
	 * @param $income   array 消费统计
	 * @param $recharge array 充值统计
	 * @param $special  array 特殊消费
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function computeCustomerBalance($income, $recharge, $special)
	{
		$result = array_map(function ($item) {
			$customer_id = $item['customer_id'];
			$recharge    = $balance = $item['money'];
			$income      = 0;
			
			return compact('customer_id', 'recharge', 'income', 'balance');
		}, $recharge);
		
		array_walk($income, function ($item) use (&$result) {
			$customer_id = $item['customer_id'];
			$income      = $item['money'];
			
			if (array_key_exists($customer_id, $result)) {
				$result[$customer_id]['income']  = $income;
				$result[$customer_id]['balance'] = $result[$customer_id]['recharge'] - $income;
			} else {
				$result[$customer_id] = [
					'customer_id' => $customer_id,
					'recharge'    => 0,
					'income'      => $income,
					'balance'     => 0 - $income,
				];
			}
		});
		
		foreach ($special as $customer_id => $money) {
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id] = [
					'customer_id' => $customer_id,
					'recharge'    => 0,
					'income'      => 0,
					'balance'     => 0,
				];
			}
			$result[$customer_id]['special'] = $money;
			$result[$customer_id]['balance'] += $money;
		}
		
		return $result;
	}
	
	/**
	 * 按客户维度获取充值统计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 15:33
	 *
	 * @param $params array 请求的参数
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function getCustomerRecharge($params)
	{
		$customerIds = array_get($params, 'customerIds');
		$data        = $this->rechargeModel->select(DB::raw('sum(`money`) as money, customer_id'))
										   ->where('status', 3)
										   ->where(function ($query) use ($customerIds) {
											   if (!empty($customerIds)) {
												   $query->whereIn('customer_id', $customerIds);
											   }
										   })
										   ->groupBy('customer_id')
										   ->get()
										   ->toArray();
		
		return array_column($data, null, 'customer_id');
	}
	
	/**
	 * 按客户维度获取成本统计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 14:59
	 *
	 * @param $params array 请求的参数
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function getCustomerCost($params)
	{
		$result = $this->upstreamBillMongoModel->query()
											   ->raw(function ($collection) use ($params) {
												   $aggregate = [];
			
												   //查询条件
												   $aggregate[0]['$match'] = [
													   'date' => [
														   '$gte' => array_get($params, 'start_date'),
														   '$lte' => array_get($params, 'end_date'),
													   ],
												   ];
												   $productIds             = array_get($params, 'productIds', []);
												   if (!empty($productIds)) {
													   $aggregate[0]['$match']['product_id'] = ['$in' => $productIds];
												   }
												   $customerIds = array_get($params, 'customerIds', []);
												   if (!empty($customerIds)) {
													   $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
												   }
			
												   //分组查询
												   $aggregate[1]['$group'] = [
													   '_id'         => '$customer_id',
													   'customer_id' => ['$first' => '$customer_id'],
													   'money'       => ['$sum' => '$money'],
													   'number'      => ['$sum' => '$fee_number'],
												   ];
			
												   //过滤字段
												   $aggregate[2]['$project'] = [
													   '_id'         => 0,
													   'customer_id' => 1,
													   'money'       => 1,
													   'number'      => 1,
												   ];
			
												   return $collection->aggregate($aggregate);
			
											   })
											   ->toArray();
		
		return array_column($result, null, 'customer_id');
	}
	
	/**
	 * 按客户维度获取营收数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 11:24
	 *
	 * @param $params array 请求的参数
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function getCustomerIncome($params)
	{
		//分割请求的时间(本月的收入需要请求日账单、本月之前的收入需要请求月账单)
		$start_date           = array_get($params, 'start_date');
		$end_date             = array_get($params, 'end_date');
		$this_month_first_day = intval(date('Ymd', strtotime('first day of this month')));
		
		$billMonthStartMonth = null;
		$billMonthEndMonth   = null;
		$billDayStartDate    = null;
		$billDayEndDate      = null;
		
		//判断请求的时间范围
		if ($end_date < $this_month_first_day) {
			//查询的时间范围是本月之前的数据,需要判断的是
			//开始时间是否为某月的月初
			if (!$this->isMonthFirstDate($start_date)) {
				throw new \Exception('start_date需要为月初日期');
			}
			//截止时间是否为某月的月末
			if (!$this->isMonthLastDate($end_date)) {
				throw new \Exception('end_date需要为月末日期');
			}
			
			$billMonthStartMonth = intval(date('Ym', strtotime($start_date)));
			$billMonthEndMonth   = intval(date('Ym', strtotime($end_date)));
		} else if ($start_date < $this_month_first_day) {
			//查询的时间包含本月，但同时包含本月之前的时间
			if (!$this->isMonthFirstDate($start_date)) {
				throw new \Exception('start_date需要为月初日期');
			}
			$billMonthStartMonth = intval(date('Ym', strtotime($start_date)));
			$billMonthEndMonth   = intval(date('Ym', strtotime('last day of last month')));
			$billDayStartDate    = intval(date('Ymd', strtotime('first day of this month')));
			$billDayEndDate      = $end_date;
		} else {
			//查询的时间在本月之内
			$billDayStartDate = $start_date;
			$billDayEndDate   = $end_date;
		}
		
		
		//查询月账单
		$billMonthData = [];
		if (!is_null($billMonthStartMonth)) {
			
			$billMonthData = $this->billMonthMongoModel->query()
													   ->raw(function ($collection) use ($billMonthStartMonth, $billMonthEndMonth, $params) {
														   $aggregate = [];
				
														   //判断条件
														   $aggregate[0]['$match'] = [
															   'month' => [
																   '$gte' => strval($billMonthStartMonth),
																   '$lte' => strval($billMonthEndMonth),
															   ],
														   ];
														   $customerIds            = array_get($params, 'customerIds', []);
														   if (!empty($customerIds)) {
															   $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
														   }
				
														   $productIds = array_get($params, 'productIds', []);
														   if (!empty($productIds)) {
															   $aggregate[0]['$match']['product_id'] = ['$in' => $productIds];
														   }
				
														   //分组查询
														   $aggregate[1]['$group'] = [
															   '_id'         => '$customer_id',
															   'customer_id' => ['$first' => '$customer_id'],
															   'money'       => ['$sum' => '$money'],
															   'number'      => ['$sum' => '$section_invoked_number'],
														   ];
				
														   //过滤字段
														   $aggregate[2]['$project'] = [
															   '_id'         => 0,
															   'customer_id' => 1,
															   'money'       => 1,
															   'number'      => 1,
														   ];
				
														   return $collection->aggregate($aggregate);
													   })
													   ->filter(function ($item) {
														   return $item->money != 0 || $item->number != 0;
													   })
													   ->toArray();
			$billMonthData = array_column($billMonthData, null, 'customer_id');
		}
		
		//查询日账单
		$billDayData = [];
		if (!is_null($billDayEndDate)) {
			$billDayData = $this->billDayMongoModel->query()
												   ->raw(function ($collection) use ($billDayStartDate, $billDayEndDate, $params) {
													   $aggregate = [];
				
													   //查询条件
													   $aggregate[0]['$match'] = [
														   'date' => [
															   '$gte' => strval($billDayStartDate),
															   '$lte' => strval($billDayEndDate),
														   ],
													   ];
													   $customerIds            = array_get($params, 'customerIds', []);
													   if (!empty($customerIds)) {
														   $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
													   }
				
													   $productIds = array_get($params, 'productIds', []);
													   if (!empty($productIds)) {
														   $aggregate[0]['$match']['product_id'] = ['$in' => $productIds];
													   }
				
													   //分组查询
													   $aggregate[1]['$group'] = [
														   '_id'         => '$customer_id',
														   'customer_id' => ['$first' => '$customer_id'],
														   'money'       => ['$sum' => '$money'],
														   'number'      => ['$sum' => '$section_invoked_number'],
													   ];
				
													   //过滤字段
													   $aggregate[2]['$project'] = [
														   '_id'         => 0,
														   'customer_id' => 1,
														   'money'       => 1,
														   'number'      => 1,
													   ];
				
													   return $collection->aggregate($aggregate);
												   })
												   ->filter(function ($item) {
													   return $item->money != 0 || $item->number != 0;
												   })
												   ->toArray();
			$billDayData = array_column($billDayData, null, 'customer_id');
		}
		
		//账单转移数据
		$transfer = $this->getTransferForCustomer($params);
		
		//特殊收入明细
		$special = $this->getSpecial($params);
		
		//融合日账单、月账单、账单转移数据
		return $this->mergeCustomerIncome($billDayData, $billMonthData, $transfer, $special);
	}
	
	/**
	 * 获取本年内的特殊费用数据
	 *
	 * @access protected
	 *
	 * @param $params array 请求参数
	 *
	 * @return array
	 **/
	protected function getSpecial($params)
	{
		$start_date = date('Y-m-d', strtotime($params['start_date']));
		$end_date   = date('Y-m-d', strtotime($params['end_date']));
		
		
		return CustomerExpend::where('profile_show_date', '>=', $start_date)
							 ->where('profile_show_date', '<=', $end_date)
							 ->where(function ($query) use ($params) {
								 $customerIds = array_get($params, 'customerIds', []);
								 if (!empty($customerIds)) {
									 $query->whereIn('customer_id', $customerIds);
								 }
								 $productIds = array_get($params, 'productIds', []);
								 if (!empty($productIds)) {
									 $query->whereIn('product_id', $productIds);
								 }
							 })
							 ->orderBy('start_date')
							 ->get()
							 ->map(function ($item) {
								 $customer_id = $item['customer_id'];
								 $product_id  = $item['product_id'];
								 $money       = 0;
								 $number      = 0;
								 if ($item['type'] == 1) {
									 $number -= $item['fee_number'];
									 $money  -= $item['money'];
								 } else if ($item['type'] == 2) {
									 $number += $item['fee_number'];
									 $money  += $item['money'];
								 }
			
								 return compact('customer_id', 'product_id', 'money', 'number');
							 })
							 ->toArray();
	}
	
	protected function getTransferForCustomer($params)
	{
		$data = MongoTransferBill::query()
								 ->raw(function ($collection) use ($params) {
									 $aggregate = [
										 [
											 '$match' => [
												 'date' => [
													 '$gte' => intval($params['start_date']),
													 '$lte' => intval($params['end_date']),
												 ],
											 ],
										 ],
										 [
											 '$group' => [
												 '_id'                 => [
													 'original_product_id' => '$original_product_id',
													 'product_id'          => '$product_id',
													 'customer_id'         => '$customer_id',
												 ],
												 'money'               => ['$sum' => '$money'],
												 'fee_number'          => ['$sum' => '$fee_number'],
												 'original_product_id' => [
													 '$first' => '$original_product_id',
												 ],
												 'product_id'          => [
													 '$first' => '$product_id',
												 ],
												 'customer_id'         => [
													 '$first' => '$customer_id',
												 ],
											 ],
										 ],
										 [
											 '$project' => [
												 '_id' => 0,
											 ],
										 ],
									 ];
			
									 //删选客户
									 $customerIds = array_get($params, 'customerIds', []);
									 if (!empty($customerIds)) {
										 $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
									 }
			
									 return $collection->aggregate($aggregate);
								 })
								 ->filter(function ($item) use ($params) {
									 $productIds          = array_get($params, 'productIds', []);
									 $product_id          = $item['product_id'];
									 $original_product_id = $item['original_product_id'];
			
									 return empty($productIds) || in_array($product_id, $productIds) || in_array($original_product_id, $productIds);
								 })
								 ->toArray();
		
		$result     = [];
		$productIds = $params['productIds'];
		
		array_walk($data, function ($item) use (&$result, $productIds) {
			$product_id = $item['product_id'];
			if (empty($productIds) || in_array($product_id, $productIds)) {
				$result[] = [
					'customer_id' => $item['customer_id'],
					'money'       => $item['money'],
					'number'      => $item['fee_number'],
				];
			}
			$original_product_id = $item['original_product_id'];
			if (empty($productIds) || in_array($original_product_id, $productIds)) {
				$result[] = [
					'customer_id' => $item['customer_id'],
					'money'       => bcsub(0, $item['money'], 4),
					'number'      => bcsub(0, $item['fee_number']),
				];
			}
		});
		
		return $result;
	}
	
	
	/**
	 * 融合客户的日账单、月账单
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 14:54
	 *
	 * @param $dayData   array 客户的日账单
	 * @param $monthData array 客户的月账单
	 * @param $transfer  array 客户维度的账单转移数据
	 * @param $special   array 特殊消耗
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function mergeCustomerIncome($dayData, $monthData, $transfer, $special)
	{
		if (empty($monthData)) {
			$result = $dayData;
		} else if (empty($dayData)) {
			$result = $monthData;
		} else {
			$result = $dayData;
			
			foreach ($monthData as $customer_id => $item) {
				if (!array_key_exists($customer_id, $result)) {
					$result[$customer_id] = $item;
					continue;
				}
				$result[$customer_id]['money']  += $item['money'];
				$result[$customer_id]['number'] += $item['number'];
			}
		}
		
		
		foreach ($transfer as $item) {
			$customer_id = $item['customer_id'];
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id] = $item;
				continue;
			}
			$result[$customer_id]['money']  += $item['money'];
			$result[$customer_id]['number'] += $item['number'];
		}
		
		foreach ($special as $item) {
			$customer_id = $item['customer_id'];
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id] = $item;
				continue;
			}
			$result[$customer_id]['money']  += $item['money'];
			$result[$customer_id]['number'] += $item['number'];
		}
		
		return $result;
	}
	
	/**
	 * 判断某个日期是否为月初
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 11:34
	 *
	 * @param $date integer 日期Ymd
	 *
	 * @throws \Exception
	 * @return boolean
	 **/
	protected function isMonthFirstDate($date)
	{
		$thisMonthFirstDate = date('Ymd', strtotime('first day of this month', strtotime($date)));
		
		return $thisMonthFirstDate == $date;
	}
	
	/**
	 * 判断某个日期是否为月末
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 13:57
	 *
	 * @param $date integer 日期Ymd
	 *
	 * @throws \Exception
	 * @return boolean
	 **/
	protected function isMonthLastDate($date)
	{
		$thisMonthLastDate = date('Ymd', strtotime('last day of this month', strtotime($date)));
		
		return $date == $thisMonthLastDate;
	}
	
	
	/**
	 * 获取查询的参数数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 11:21
	 *
	 * @param name string this is params
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function getParams()
	{
		$start_date = request()->post('start_date');
		$end_date   = request()->post('end_date');
		
		if (empty($start_date)) {
			throw new \Exception('不存在start_date参数');
		}
		
		if (empty($end_date)) {
			throw new \Exception('不存在end_date参数');
		}
		
		if (!preg_match('/^\d{4}\-\d{2}\-\d{2}$/', $start_date)) {
			throw new \Exception('start_date参数格式不正确');
		}
		
		if (!preg_match('/^\d{4}\-\d{2}\-\d{2}$/', $end_date)) {
			throw new \Exception('end_date参数格式不正确');
		}
		
		$start_date = intval(date('Ymd', strtotime($start_date)));
		$end_date   = intval(date('Ymd', strtotime($end_date)));
		
		if ($end_date < $start_date) {
			list($start_date, $end_date) = [
				$end_date,
				$start_date,
			];
		}
		
		$customerIds = request()->post('customerIds', []);
		
		if (!is_array($customerIds)) {
			throw new \Exception('customerIds参数格式不正确');
		}
		
		$productIds = request()->post('productIds', []);
		if (!is_array($productIds)) {
			throw new \Exception('productIds参数格式不正确');
		}
		
		if (!empty($productIds)) {
			$productIds = array_map('intval', $productIds);
		}
		
		$isExtra = request()->post('isExtra', 0);
		if ($isExtra != 1 && $isExtra != 0) {
			throw new \Exception('isExtra参数格式不正确');
		}
		
		return compact('start_date', 'end_date', 'customerIds', 'productIds', 'isExtra');
	}
	
	/**
	 * 查询产品维度的营收、成本、利润、余额数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/21 10:21
	 *
	 * @throws \Exception
	 * @return array
	 **/
	public function getProductProfit()
	{
		//获取请求的参数
		$params = $this->getParams();
		
		//缓存金盾、邦秒验、邦信分快捷版等子产品的相关数据
		$this->initCache();
		
		//产品维度的统计数据
		$container = $this->getProductProfitContainer();
		
		//按产品维度获取营收数据
		$this->getProductIncome($params, $container);
		
		//按产品维度获取成本数据
		$this->getProductCost($params, $container);
		
		//融合数据
		return $this->computeProductProfit($container);
	}
	
	/**
	 * 计算产品的利润
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/21 17:47
	 *
	 * @param $container array 产品营收、成本数据
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function computeProductProfit($container)
	{
		array_walk($container, function (&$containerItem) {
			if (array_key_exists('children', $containerItem)) {
				$data       = array_column($containerItem['children'], 'data');
				$income_arr = array_column($data, 'income_arr');
				//统计营收
				$money                               = array_sum(array_column($income_arr, 'money'));
				$number                              = array_sum(array_column($income_arr, 'number'));
				$containerItem['data']['income_arr'] = compact('money', 'number');
				
				//成本统计
				$cost_arr                          = array_column($data, 'cost_arr');
				$money                             = array_sum(array_column($cost_arr, 'money'));
				$number                            = array_sum(array_column($cost_arr, 'number'));
				$containerItem['data']['cost_arr'] = compact('money', 'number');
				
				array_walk($containerItem['children'], function (&$childrenItem) {
					//计算利润
					$money                              = $childrenItem['data']['income_arr']['money'] - $childrenItem['data']['cost_arr']['money'];
					$number                             = $childrenItem['data']['income_arr']['number'] - $childrenItem['data']['cost_arr']['number'];
					$childrenItem['data']['profit_arr'] = compact('money', 'number');
				});
			}
			//计算利润
			$money                               = $containerItem['data']['income_arr']['money'] - $containerItem['data']['cost_arr']['money'];
			$number                              = $containerItem['data']['income_arr']['number'] - $containerItem['data']['cost_arr']['number'];
			$containerItem['data']['profit_arr'] = compact('money', 'number');
		});
		
		//过滤数据
		return array_filter($container, function (&$parentItem) {
			
			$income_arr = $parentItem['data']['income_arr'];
			$cost_arr   = $parentItem['data']['cost_arr'];
			
			if (0 == $income_arr['money'] && 0 == $income_arr['number'] && 0 == $cost_arr['money'] && 0 == $cost_arr['number']) {
				return false;
			}
			
			if (array_key_exists('children', $parentItem)) {
				array_filter($parentItem['children'], function ($childrenItem) {
					$income_arr = $childrenItem['data']['income_arr'];
					$cost_arr   = $childrenItem['data']['cost_arr'];
					
					if (0 == $income_arr['money'] && 0 == $income_arr['number'] && 0 == $cost_arr['money'] && 0 == $cost_arr['number']) {
						return false;
					}
					
					return true;
				});
			}
			
			return true;
		});
	}
	
	/**
	 * 对产品维度统计所需要的数据进行缓存
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/21 15:10
	 *
	 * @return void
	 **/
	protected function initCache()
	{
		$this->children615ProductIds = get615ChildrenProductIds();
		$this->children200ProductIds = get200ChildrenProductIds();
		
		$this->storeProductIds              = ProductType::where('type', 1)
														 ->pluck('product_id')
														 ->toArray();
		$this->normalProductIds             = ProductType::where('type', 2)
														 ->pluck('product_id')
														 ->toArray();
		$this->noneDisperseProductIds       = ProductType::where('type', 3)
														 ->pluck('product_id')
														 ->toArray();
		$this->productId2ProductNameMapping = Product::pluck('product_name', 'product_id')
													 ->toArray();
	}
	
	/**
	 * 按客户维度获取成本统计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 14:59
	 *
	 * @param $params     array 请求的参数
	 * @param &$container array 保存数据的容器
	 *
	 * @throws \Exception
	 * @return void
	 **/
	protected function getProductCost($params, &$container)
	{
		$result = $this->upstreamBillMongoModel->query()
											   ->raw(function ($collection) use ($params) {
												   $aggregate = [];
			
												   //查询条件
												   $aggregate[0]['$match'] = [
													   'date' => [
														   '$gte' => array_get($params, 'start_date'),
														   '$lte' => array_get($params, 'end_date'),
													   ],
												   ];
												   $productIds             = array_get($params, 'productIds', []);
												   if (!empty($productIds)) {
													   $aggregate[0]['$match']['product_id'] = [
														   '$in' => $productIds,
														   '$ne' => 210,
													   ];
												   }
												   $customerIds = array_get($params, 'customerIds', []);
												   if (!empty($customerIds)) {
													   $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
												   }
			
												   //分组查询
												   $aggregate[1]['$group'] = [
													   '_id'        => '$product_id',
													   'product_id' => ['$first' => '$product_id'],
													   'money'      => ['$sum' => '$money'],
													   'number'     => ['$sum' => '$fee_number'],
												   ];
			
												   //过滤字段
												   $aggregate[2]['$project'] = [
													   '_id'        => 0,
													   'product_id' => 1,
													   'money'      => 1,
													   'number'     => 1,
												   ];
			
												   return $collection->aggregate($aggregate);
			
											   })
											   ->toArray();
		
		
		//将成本数据插入到容器中
		array_walk($result, function ($item) use (&$container) {
			$product_id   = array_get($item, 'product_id', 0);
			$containerKey = $this->getCostContainerKey($product_id);
			if (null == $containerKey) {
				return;
			}
			$money  = array_get($item, 'money', 0);
			$number = array_get($item, 'number', 0);
			if (0 == $money && 0 == $number) {
				return;
			}
			
			if (is_string($containerKey)) {
				$container[$containerKey]['data']['cost_arr']['money']  += $money;
				$container[$containerKey]['data']['cost_arr']['number'] += $number;
			} else if (is_array($containerKey)) {
				list($first, $last) = $containerKey;
				$container[$first]['children'][$last]['data']['cost_arr']['money']  += $money;
				$container[$first]['children'][$last]['data']['cost_arr']['number'] += $number;
			}
		});
		
		//评分类字段的成本统计
		$store_channel = Upstream::where('product_id', 210)
								 ->where('type', 1)
								 ->pluck('channel')
								 ->toArray();
		$productIds    = array_get($params, 'productIds', []);
		if (!empty($store_channel) && (in_array(251, $productIds) || empty($productIds))) {
			$store_channel_cost = $this->upstreamBillMongoModel->query()
															   ->raw(function ($collection) use ($store_channel, $params) {
																   $aggregate = [];
				
																   //查询条件
																   $aggregate[0]['$match'] = [
																	   'date'       => [
																		   '$gte' => array_get($params, 'start_date'),
																		   '$lte' => array_get($params, 'end_date'),
																	   ],
																	   'product_id' => 210,
																	   'channel'    => ['$in' => $store_channel],
																   ];
																   $customerIds            = array_get($params, 'customerIds', []);
																   if (!empty($customerIds)) {
																	   $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
																   }
				
																   //分组查询
																   $aggregate[1]['$group'] = [
																	   '_id'        => '$product_id',
																	   'product_id' => ['$first' => '$product_id'],
																	   'money'      => ['$sum' => '$money'],
																	   'number'     => ['$sum' => '$fee_number'],
																   ];
				
																   //过滤字段
																   $aggregate[2]['$project'] = [
																	   '_id'        => 0,
																	   'product_id' => 1,
																	   'money'      => 1,
																	   'number'     => 1,
																   ];
				
																   return $collection->aggregate($aggregate);
				
															   })
															   ->toArray();
			
			$store_channel_cost = array_get($store_channel_cost, 0, []);
			
			$container['bxf_short']['children']['bxf_score']['data']['cost_arr']['number'] += array_get($store_channel_cost, 'number', 0);
			$container['bxf_short']['children']['bxf_score']['data']['cost_arr']['money']  += array_get($store_channel_cost, 'money', 0);
		}
		
		//统计类字段的成本统计
		$normal_channel = Upstream::where('product_id', 210)
								  ->where('type', 2)
								  ->pluck('channel')
								  ->toArray();
		if (!empty($normal_channel) && (in_array(241, $productIds) || empty($productIds))) {
			$normal_channel_cost = $this->upstreamBillMongoModel->query()
																->raw(function ($collection) use ($normal_channel, $params) {
																	$aggregate = [];
				
																	//查询条件
																	$aggregate[0]['$match'] = [
																		'date'       => [
																			'$gte' => array_get($params, 'start_date'),
																			'$lte' => array_get($params, 'end_date'),
																		],
																		'product_id' => 210,
																		'channel'    => ['$in' => $normal_channel],
																	];
																	$customerIds            = array_get($params, 'customerIds', []);
																	if (!empty($customerIds)) {
																		$aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
																	}
				
																	//分组查询
																	$aggregate[1]['$group'] = [
																		'_id'        => '$product_id',
																		'product_id' => ['$first' => '$product_id'],
																		'money'      => ['$sum' => '$money'],
																		'number'     => ['$sum' => '$fee_number'],
																	];
				
																	//过滤字段
																	$aggregate[2]['$project'] = [
																		'_id'        => 0,
																		'product_id' => 1,
																		'money'      => 1,
																		'number'     => 1,
																	];
				
																	return $collection->aggregate($aggregate);
				
																})
																->toArray();
			
			$normal_channel_cost = array_get($normal_channel_cost, 0, []);
			
			$container['bxf_short']['children']['bxf_normal']['data']['cost_arr']['number'] += array_get($normal_channel_cost, 'number', 0);
			$container['bxf_short']['children']['bxf_normal']['data']['cost_arr']['money']  += array_get($normal_channel_cost, 'money', 0);
		}
	}
	
	/**
	 * 生成一个产品维度利润数据的维度字段
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/21 11:39
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function getProductProfitContainer()
	{
		$data = [
			'income_arr' => [
				'money'  => 0,
				'number' => 0,
			],
			'cost_arr'   => [
				'money'  => 0,
				'number' => 0,
			],
			'profit_arr' => [
				'money'  => 0,
				'number' => 0,
			],
		];
		
		$container = [];
		//邦信分快捷版
		$label                  = 'bxf_short';
		$product_name           = '邦信分快捷版';
		$productType            = (new ProductType())->select(DB::raw('GROUP_CONCAT(`product_id`) as product_id, `type`'))
													 ->groupBy('type')
													 ->get()
													 ->toArray();
		$productType            = array_column($productType, 'product_id', 'type');
		$product_id             = $productType[1] . $productType[2] . ',210';
		$children               = [
			'bxf_score'  => [
				'label'        => 'bxf_score',
				'product_id'   => $productType[1],
				'product_name' => '评分类字段',
				'data'         => $data,
			],
			'bxf_normal' => [
				'label'        => 'bxf_normal',
				'product_id'   => $productType[2] . ',210',
				'product_name' => '统计类字段',
				'data'         => $data,
			],
		
		];
		$container['bxf_short'] = compact('label', 'product_id', 'product_name', 'children', 'data');
		
		//邦信分详单版
		$label        = 'bxf_detail';
		$product_name = '邦信分详单版';
		$product_id   = implode(',', $this->bxfDetailProductIds);
		$children     = array_column(array_map(function ($label) use ($data) {
			$product_name = $this->productId2ProductNameMapping[$label];
			$product_id   = $label;
			
			return compact('label', 'product_id', 'product_name', 'data');
		}, $this->bxfDetailProductIds), null, 'label');
		
		$container['bxf_detail'] = compact('label', 'product_id', 'product_name', 'children', 'data');
		
		//邦信分私有云
		$label                        = 'bxf_price_cloud';
		$product_name                 = '邦信分私有云';
		$product_id                   = 501;
		$container['bxf_price_cloud'] = compact('label', 'product_id', 'product_name', 'data');
		
		//邦秒配
		$label        = 'bmp';
		$product_name = '邦秒配';
		$children     = array_column(array_map(function ($label) use ($data) {
			$product_name = $this->productId2ProductNameMapping[$label];
			$product_id   = $label;
			
			return compact('label', 'product_id', 'product_name', 'data');
		}, [
			601,
			604,
			104,
		]), null, 'label');
		
		$product_id       = implode(',', array_column($children, 'product_id'));
		$container['bmp'] = compact('label', 'product_id', 'product_name', 'children', 'data');
		
		//邦企查
		$label            = 'bqc';
		$product_name     = '邦企查';
		$product_id       = 401;
		$container['bqc'] = compact('label', 'product_id', 'product_name', 'data');
		
		//邦秒验
		$children         = array_column(array_map(function ($label) use ($data) {
			$product_name = $this->productId2ProductNameMapping[$label];
			$product_id   = $label;
			
			return compact('label', 'product_id', 'product_name', 'data');
		}, array_merge($this->children200ProductIds, $this->bmyExtraProductIds)), null, 'label');
		$label            = 'bmy';
		$product_name     = '邦秒验';
		$product_id       = implode(',', array_column($children, 'product_id'));
		$container['bmy'] = compact('label', 'product_id', 'product_name', 'children', 'data');
		
		//金盾
		$label                    = 'gold_shield';
		$product_name             = '金盾';
		$children                 = array_column(array_map(function ($label) use ($data) {
			$product_name = $this->productId2ProductNameMapping[$label];
			$product_id   = $label;
			
			return compact('label', 'product_id', 'product_name', 'data');
		}, array_merge($this->children615ProductIds, $this->goldenShieldExtraProductIds)), null, 'label');
		$product_id               = implode(',', array_column($children, 'product_id'));
		$container['gold_shield'] = compact('label', 'product_id', 'product_name', 'children', 'data');
		
		return $container;
	}
	
	/**
	 * 按产品维度获取营收数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 11:24
	 *
	 * @param $params     array 请求的参数
	 * @param &$container array 数据存储的容器
	 *
	 * @throws \Exception
	 * @return void
	 **/
	protected function getProductIncome($params, &$container)
	{
		//分割请求的时间(本月的收入需要请求日账单、本月之前的收入需要请求月账单)
		$start_date           = array_get($params, 'start_date');
		$end_date             = array_get($params, 'end_date');
		$this_month_first_day = intval(date('Ymd', strtotime('first day of this month')));
		
		$billMonthStartMonth = null;
		$billMonthEndMonth   = null;
		$billDayStartDate    = null;
		$billDayEndDate      = null;
		
		//判断请求的时间范围
		if ($end_date < $this_month_first_day) {
			//查询的时间范围是本月之前的数据,需要判断的是
			//开始时间是否为某月的月初
			if (!$this->isMonthFirstDate($start_date)) {
				throw new \Exception('start_date需要为月初日期');
			}
			//截止时间是否为某月的月末
			if (!$this->isMonthLastDate($end_date)) {
				throw new \Exception('end_date需要为月末日期');
			}
			
			$billMonthStartMonth = intval(date('Ym', strtotime($start_date)));
			$billMonthEndMonth   = intval(date('Ym', strtotime($end_date)));
		} else if ($start_date < $this_month_first_day) {
			//查询的时间包含本月，但同时包含本月之前的时间
			if (!$this->isMonthFirstDate($start_date)) {
				throw new \Exception('start_date需要为月初日期');
			}
			$billMonthStartMonth = intval(date('Ym', strtotime($start_date)));
			$billMonthEndMonth   = intval(date('Ym', strtotime('last day of last month')));
			$billDayStartDate    = intval(date('Ymd', strtotime('first day of this month')));
			$billDayEndDate      = $end_date;
		} else {
			//查询的时间在本月之内
			$billDayStartDate = $start_date;
			$billDayEndDate   = $end_date;
		}
		
		
		//查询月账单
		$billMonthData = [];
		if (!is_null($billMonthStartMonth)) {
			
			$billMonthData = $this->billMonthMongoModel->query()
													   ->raw(function ($collection) use ($billMonthStartMonth, $billMonthEndMonth, $params) {
														   $aggregate = [];
				
														   //判断条件
														   $aggregate[0]['$match'] = [
															   'month' => [
																   '$gte' => strval($billMonthStartMonth),
																   '$lte' => strval($billMonthEndMonth),
															   ],
														   ];
														   $productIds             = array_get($params, 'productIds', []);
														   if (!empty($productIds)) {
															   $aggregate[0]['$match']['product_id'] = ['$in' => $productIds];
														   }
														   $customerIds = array_get($params, 'customerIds', []);
														   if (!empty($customerIds)) {
															   $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
														   }
				
														   //分组查询
														   $aggregate[1]['$group'] = [
															   '_id'        => '$product_id',
															   'product_id' => ['$first' => '$product_id'],
															   'money'      => ['$sum' => '$money'],
															   'number'     => ['$sum' => '$section_invoked_number'],
														   ];
				
														   //过滤字段
														   $aggregate[2]['$project'] = [
															   '_id'        => 0,
															   'product_id' => 1,
															   'money'      => 1,
															   'number'     => 1,
														   ];
				
														   return $collection->aggregate($aggregate);
													   })
													   ->filter(function ($item) {
														   return $item->money != 0 || $item->number != 0;
													   })
													   ->toArray();
			$billMonthData = array_column($billMonthData, null, 'product_id');
		}
		
		
		//查询日账单
		$billDayData = [];
		if (!is_null($billDayEndDate)) {
			$billDayData = $this->billDayMongoModel->query()
												   ->raw(function ($collection) use ($billDayStartDate, $billDayEndDate, $params) {
													   $aggregate = [];
				
													   //查询条件
													   $aggregate[0]['$match'] = [
														   'date' => [
															   '$gte' => strval($billDayStartDate),
															   '$lte' => strval($billDayEndDate),
														   ],
													   ];
													   $productIds             = array_get($params, 'productIds', []);
													   if (!empty($productIds)) {
														   $aggregate[0]['$match']['product_id'] = ['$in' => $productIds];
													   }
													   $customerIds = array_get($params, 'customerIds', []);
													   if (!empty($customerIds)) {
														   $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
													   }
				
													   //分组查询
													   $aggregate[1]['$group'] = [
														   '_id'        => '$product_id',
														   'product_id' => ['$first' => '$product_id'],
														   'money'      => ['$sum' => '$money'],
														   'number'     => ['$sum' => '$section_invoked_number'],
													   ];
				
													   //过滤字段
													   $aggregate[2]['$project'] = [
														   '_id'        => 0,
														   'product_id' => 1,
														   'money'      => 1,
														   'number'     => 1,
													   ];
				
													   return $collection->aggregate($aggregate);
												   })
												   ->filter(function ($item) {
													   return $item->money != 0 || $item->number != 0;
												   })
												   ->toArray();
			$billDayData = array_column($billDayData, null, 'product_id');
		}
		
		//融合日账单、月账单
		$result = $this->mergeProductIncome($billDayData, $billMonthData);
		
		//查询账单转移数据
		$transfer = $this->getTransfer($params);
		
		//特殊消耗
		$special = $this->getSpecial($params);
		
		//将得到的营收数据放置到容器中
		$this->pushProductIncome($result, $container);
		$this->pushProductIncome($transfer, $container);
		$this->pushProductIncome($special, $container);
	}
	
	/**
	 * 获取每个产品维度的转移账单数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/9 10:08
	 *
	 * @params   array 请求参数
	 *
	 * @return array
	 **/
	protected function getTransfer($params)
	{
		$data = MongoTransferBill::query()
								 ->raw(function ($collection) use ($params) {
									 $aggregate = [
										 [
											 '$match' => [
												 'date' => [
													 '$gte' => intval($params['start_date']),
													 '$lte' => intval($params['end_date']),
												 ],
											 ],
										 ],
										 [
											 '$group' => [
												 '_id'                 => [
													 'original_product_id' => '$original_product_id',
													 'product_id'          => '$product_id',
													 'customer_id'         => '$customer_id',
													 'account_id'          => '$account_id',
												 ],
												 'money'               => ['$sum' => '$money'],
												 'fee_number'          => ['$sum' => '$fee_number'],
												 'original_product_id' => [
													 '$first' => '$original_product_id',
												 ],
												 'product_id'          => [
													 '$first' => '$product_id',
												 ],
												 'customer_id'         => [
													 '$first' => '$customer_id',
												 ],
												 'account_id'          => [
													 '$first' => '$account_id',
												 ],
											 ],
										 ],
										 [
											 '$project' => [
												 '_id' => 0,
											 ],
										 ],
									 ];
			
									 //删选客户
									 $customerIds = array_get($params, 'customerIds', []);
									 if (!empty($customerIds)) {
										 $aggregate[0]['$match']['customer_id'] = ['$in' => $customerIds];
									 }
			
									 return $collection->aggregate($aggregate);
								 })
								 ->filter(function ($item) use ($params) {
									 $productIds          = array_get($params, 'productIds', []);
									 $product_id          = $item['product_id'];
									 $original_product_id = $item['original_product_id'];
			
									 return empty($productIds) || in_array($product_id, $productIds) || in_array($original_product_id, $productIds);
								 })
								 ->toArray();
		
		$result     = [];
		$productIds = $params['productIds'];
		array_walk($data, function ($item) use (&$result, $productIds) {
			$product_id = $item['product_id'];
			if (empty($productIds) || in_array($product_id, $productIds)) {
				$result[] = [
					'product_id' => $product_id,
					'money'      => $item['money'],
					'number'     => $item['fee_number'],
				];
			}
			$original_product_id = $item['original_product_id'];
			if (empty($productIds) || in_array($original_product_id, $productIds)) {
				$result[] = [
					'product_id' => $original_product_id,
					'money'      => bcsub(0, $item['money'], 4),
					'number'     => bcsub(0, $item['fee_number']),
				];
			}
		});
		
		return $result;
	}
	
	/**
	 * 将生成的营收数据按照一定的规则放置到数据容器中
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/21 13:54
	 *
	 * @param $data       array 数据
	 * @param &$container array 容器
	 *
	 * @throws \Exception
	 * @return void
	 **/
	protected function pushProductIncome($data, &$container)
	{
		array_walk($data, function ($item) use (&$container) {
			$product_id   = array_get($item, 'product_id', 0);
			$containerKey = $this->getIncomeContainerKey($product_id);
			$money        = array_get($item, 'money', 0);
			$number       = array_get($item, 'number', 0);
			if (0 == $money && 0 == $number) {
				return;
			}
			
			if (is_string($containerKey)) {
				$container[$containerKey]['data']['income_arr']['money']  += $money;
				$container[$containerKey]['data']['income_arr']['number'] += $number;
			} else if (is_array($containerKey)) {
				list($first, $last) = $containerKey;
				$container[$first]['children'][$last]['data']['income_arr']['money']  += $money;
				$container[$first]['children'][$last]['data']['income_arr']['number'] += $number;
			}
		});
	}
	
	/**
	 * 根据产品ID，确认数据需要展示的位置(成本)
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/21 15:29
	 *
	 * @param $product_id integer 产品ID
	 *
	 * @throws \Exception
	 *
	 * @return array|string
	 **/
	protected function getCostContainerKey($product_id)
	{
		if (in_array($product_id, $this->children200ProductIds)) {
			return [
				'bmy',
				$product_id,
			];
		}
		
		if (801 == $product_id) {
			return [
				'bmy',
				801,
			];
		}
		
		switch ($product_id) {
			case 210:
				return 'bxf_short';
				break;
			case 401:
				return 'bqc';
				break;
			default :
				return null;
				break;
			
		}
	}
	
	/**
	 * 根据产品ID，确认数据需要展示的位置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/21 15:29
	 *
	 * @param $product_id integer 产品ID
	 *
	 * @throws \Exception
	 *
	 * @return array|string
	 **/
	protected function getIncomeContainerKey($product_id)
	{
		//邦信分快捷版--评分类字段
		if (in_array($product_id, $this->storeProductIds)) {
			return [
				'bxf_short',
				'bxf_score',
			];
		}
		
		//邦信分快捷版--普通类字段
		if (in_array($product_id, $this->normalProductIds) || $product_id == 210) {
			return [
				'bxf_short',
				'bxf_normal',
			];
		}
		
		//邦秒验
		if (in_array($product_id, $this->children200ProductIds) || in_array($product_id, $this->bmyExtraProductIds)) {
			return [
				'bmy',
				$product_id,
			];
		}
		
		//金盾
		if (in_array($product_id, $this->children615ProductIds) || in_array($product_id, $this->goldenShieldExtraProductIds)) {
			return [
				'gold_shield',
				$product_id,
			];
		}
		
		//邦秒配
		if (in_array($product_id, $this->bmpProductIds)) {
			return [
				'bmp',
				$product_id,
			];
		}
		
		//邦信分详单版
		if (in_array($product_id, $this->bxfDetailProductIds)) {
			return [
				'bxf_detail',
				$product_id,
			];
		}
		
		//特殊情况
		switch ($product_id) {
			case 501:
				return 'bxf_price_cloud';
				break;
			case 401:
				return 'bqc';
				break;
			case 615:
				return [
					'gold_shield',
					661,
				];
				break;
			default:
				return null;
				break;
		}
	}
	
	/**
	 * 融合产品的日账单、月账单
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/5/20 14:54
	 *
	 * @param $dayData   array 客户的日账单
	 * @param $monthData array 客户的月账单
	 *
	 * @throws \Exception
	 * @return array
	 **/
	protected function mergeProductIncome($dayData, $monthData)
	{
		if (empty($monthData)) {
			return $dayData;
		} else if (empty($dayData)) {
			return $monthData;
		}
		
		$result = $dayData;
		foreach ($monthData as $product_id => $item) {
			if (!array_key_exists($product_id, $result)) {
				$result[$product_id] = $item;
				continue;
			}
			$result[$product_id]['money']  += $item['money'];
			$result[$product_id]['number'] += $item['number'];
		}
		
		return $result;
	}
}