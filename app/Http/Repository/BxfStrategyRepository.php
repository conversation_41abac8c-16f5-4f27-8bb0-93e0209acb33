<?php

namespace App\Http\Repository;

use App\Models\Approval;
use App\Models\BxfStrategy;
use App\Models\BxfStrategyConfig;
use App\Utils\Helpers\Func;
use Exception;

/**
 * 邦信分-策略管理
 */
class BxfStrategyRepository
{

    /**
     * @throws Exception
     */
    public function __construct() {
        $user_cookie = request()->post('user_cookie');

        if(empty($user_cookie)){
            throw new Exception("请登录后进行操作");
        }
    }


    /**
     * 获取策略列表
     *
     * @return array
     * <AUTHOR> 2024-01-08 11:36:41
     *
     */
    public function strategy_list(): array {
        $page        = intval(request()->post('page', 1));
        $limit       = intval(request()->post('limit', 20));
        $status      = intval(request()->post('status'));
        $strategy_id = request()->post('strategy_id');

        $list  = BxfStrategy::getStrategyListByStrategyName($strategy_id,$status,$page,$limit);
        $count = BxfStrategy::getStrategyCountByStrategyName($strategy_id,$status);

        // foreach($list as &$info){
        //     $info['map_dict'] = json_encode(json_decode($info['map_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        // }

        return [
            'list'  => $list,
            'count' => $count
        ];
    }


    /**
     * 策略名称列表
     *
     * @return array
     * <AUTHOR> 2024-01-11 16:00:46
     *
     */
    public function strategy_name_list(): array {
        $status = BxfStrategy::STATUS_AVAILABLE;

        $list = BxfStrategy::getStrategyListByStrategyName('',$status,1,100000);

        $res = [];

        foreach($list as $info){
            $res[] = [
                'strategy_name' => $info['strategy_name'],
                'strategy_id' => $info['strategy_id'],
            ];
        }

        return $res;
    }


    /**
     * 添加,编辑策略校验参数
     *
     * @param $strategy_data
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-01-09 13:54:35
     */
    private function check_data($strategy_data){
        if(empty($strategy_data['strategy_name'])){
            throw new Exception("策略名称不可为空");
        }

        if(empty($strategy_data['score_id'])){
            throw new Exception("策略产品不可为空");
        }

        $strategy_id = isset($strategy_data['strategy_id'])?$strategy_data['strategy_id']:'';
        //校验策略名,不可重复
        $has_strategy_name = BxfStrategy::strategyNameHasExists($strategy_data['strategy_name'],$strategy_id);
        if($has_strategy_name){
            throw new Exception("策略名称已存在,不可重复");
        }
        self::check_json_pv_dict($strategy_data['pv_dict']);
        self::check_json_range_map($strategy_data['map_dict'],'map_dict');
        self::check_json_range_map($strategy_data['pv_seg_innet_dict'],'pv_seg_innet_dict');
        self::check_json_range_map($strategy_data['pv_seg_noinnet_dict'],'pv_seg_noinnet_dict');
        self::check_json_filter_dict($strategy_data['filter_dict']);
    }

    /**
     *
     * 校验map_dict,pv_seg_innet_dict,pv_seg_noinnet_dict
     *
     * $map_dict = '{"1":[462.64,481],"2":[450.4,462.64],"3":[439.18,450.4],"4":[431.02,439.18],"5":[423.88,431.02],"6":[417.76,423.88],"7":[411.64,417.76],"8":[406.54,411.64],"9":[402.46,406.54],"10":[398.38,402.46],"11":[391.24,398.38],"12":[389.2,391.24],"13":[384.1,389.2],"14":[381.04,384.1],"15":[379,381.04],"16":[374.92,379],"17":[369.82,374.92],"18":[367.78,369.82],"19":[366.76,367.78],"20":[364.72,366.76],"21":[363.7,364.72],"22":[358.6,363.7],"23":[355.54,358.6],"25":[350.44,355.54],"26":[349.42,350.44],"28":[342.28,349.42],"29":[340.24,342.28],"32":[338.2,340.24],"34":[335.14,338.2],"35":[334.12,335.14],"36":[332.08,334.12],"37":[329.02,332.08],"40":[324.94,329.02],"45":[323.92,324.94],"46":[321.88,323.92],"47":[317.8,321.88],"51":[305.56,317.8],"58":[303.52,305.56],"66":[289.24,303.52],"74":[0,289.24]}';
     * $pv_seg_innet_dict = '{"-3":[323.906,851.101],"-4":[206.331,323.906],"-6":[136.544,206.331],"-5":[91.789,136.544]}';
     * $pv_seg_noinnet_dict = '{"-3":[276.25,344.656],"-4":[252.252,276.25],"-6":[236.104,252.252],"-5":[165.051,236.104]}';
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-01-15 16:49:59
     *
     */
    private static function check_json_range_map($json_str,$json_name){
        $json_arr = json_decode($json_str);

        if(json_last_error() != JSON_ERROR_NONE){
            throw new Exception($json_name.' 不是一个json字符串');
        }

        $last_info = [];
        $last_idx = '';
        foreach ($json_arr as $idx => $info) {
            if($info[0] > $info[1]){
                throw new Exception($json_name.' 中的数据应升序排序:'.$idx);
            }
            if(!empty($last_info)){
                if($info[1] != $last_info[0]){
                    throw new Exception($json_name.' 范围应连续:'.$last_idx.'-'.$idx);
                }
            }
            $last_info = $info;
            $last_idx = $idx;
        }
    }

    /**
     * 校验 filter_dict 格式
     * {"A_ifnet":2,"A_innet_days":10,"A_cn_d360":2,"A_cn_d180":0,"B_cn_d360":50,"B_cn_d180":40,"B_cn_d90":5}
     *
     * @param $json_str
     *
     * @static
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-01-15 17:08:04
     */
    private static function check_json_filter_dict($json_str){
        $json_arr = json_decode($json_str,true);
        if(json_last_error() != JSON_ERROR_NONE){
            throw new Exception('filter_dict 不是一个json字符串');
        }

        if(count($json_arr) != 7){
            throw new Exception('filter_dict 数量错误');
        }
        $ori_keys = ['A_ifnet','A_innet_days','A_cn_d360','A_cn_d180','B_cn_d360','B_cn_d180','B_cn_d90'];
        $keys = array_keys($json_arr);

        if(!empty(array_diff($ori_keys, $keys)) || !empty(array_diff($keys,$ori_keys))){
            throw new Exception('filter_dict 请检查键名,数量是否正确');
        }
    }

    /**
     * 校验 pv_dict 格式
     * {"cn_d30":"23.651127819548872","cn_d60":"48.23759398496241","cn_d90":"72.69022556390978","cn_d180":"148.26466165413535","cn_d360":"304.1368421052632"}
     *
     * @param $json_str
     *
     * @static
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-01-15 17:08:04
     */
    private static function check_json_pv_dict($json_str){
        $json_arr = json_decode($json_str,true);
        if(json_last_error() != JSON_ERROR_NONE){
            throw new Exception('pv_dict 不是一个json字符串');
        }

        if(count($json_arr) != 5){
            throw new Exception('pv_dict 数量错误');
        }
        $ori_keys = ['cn_d30', 'cn_d60', 'cn_d90', 'cn_d180', 'cn_d360'];
        $keys = array_keys($json_arr);

        if(!empty(array_diff($ori_keys, $keys)) || !empty(array_diff($keys,$ori_keys))){
            throw new Exception('pv_dict 请检查键名,数量是否正确');
        }
    }


    /**
     * 添加策略
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-01-08 15:00:44
     *
     */
    public function add(): bool {
        $strategy_name       = request()->post('strategy_name');
        $score_id            = request()->post('score_id');
        $pv_dict             = request()->post('pv_dict');
        $map_dict            = request()->post('map_dict');
        $pv_seg_innet_dict   = request()->post('pv_seg_innet_dict');
        $pv_seg_noinnet_dict = request()->post('pv_seg_noinnet_dict');
        $filter_dict         = request()->post('filter_dict');
        $user_cookie         = request()->post('user_cookie');

        $username = Func::getUserNameFromCookie($user_cookie);

        $strategy_data = [];

        $strategy_data['strategy_name']       = $strategy_name;
        $strategy_data['score_id']            = $score_id;
        $strategy_data['pv_dict']             = $pv_dict;
        $strategy_data['map_dict']            = $map_dict;
        $strategy_data['pv_seg_innet_dict']   = $pv_seg_innet_dict;
        $strategy_data['pv_seg_noinnet_dict'] = $pv_seg_noinnet_dict;
        $strategy_data['filter_dict']         = $filter_dict;

        $this->check_data($strategy_data);//参数校验

        $strategy_data['pv_dict']             = json_encode(json_decode($pv_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['map_dict']            = json_encode(json_decode($map_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['pv_seg_innet_dict']   = json_encode(json_decode($pv_seg_innet_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['pv_seg_noinnet_dict'] = json_encode(json_decode($pv_seg_noinnet_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['filter_dict']         = json_encode(json_decode($filter_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $strategy_data['admin']               = $username;
        $strategy_data['status']              = BxfStrategy::STATUS_AVAILABLE;
        $strategy_data['strategy_id']         = Func::createUUid("BS");

        $now = date("Y-m-d H:i:s");
        $strategy_data['created_at']         = $now;
        $strategy_data['updated_at']         = $now;

        $apply_content = $this->add_diff($strategy_data);

        //添加到审批
        return ApprovalRepository::addApproval('','','',Approval::URL_BXF_STRATEGY_ADD,$strategy_data,$apply_content,$user_cookie);
    }


    /**
     *
     *
     * @param $strategy_data
     *
     * @return string
     * <AUTHOR> 2024-01-18 18:43:00
     */
    private function add_diff($strategy_data): string {
        $apply_content = '添加策略:'.PHP_EOL;
        $apply_content .= '策略名: ' . $strategy_data['strategy_name'] . PHP_EOL;
        $apply_content .= 'pv_dict: ' .PHP_EOL. json_encode(json_decode($strategy_data['pv_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL;
        $apply_content .= 'map_dict: ' .PHP_EOL. json_encode(json_decode($strategy_data['map_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL;
        $apply_content .= 'pv_seg_innet_dict: ' .PHP_EOL. json_encode(json_decode($strategy_data['pv_seg_innet_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL;
        $apply_content .= 'pv_seg_noinnet_dict: ' .PHP_EOL. json_encode(json_decode($strategy_data['pv_seg_noinnet_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL;
        $apply_content .= 'filter_dict: ' .PHP_EOL. json_encode(json_decode($strategy_data['filter_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . PHP_EOL;

        return $apply_content;
    }

    /**
     * 审批通过 - 添加策略
     *
     * @param $strategy_data
     *
     * @static
     * @return bool
     * <AUTHOR> 2024-01-09 11:47:11
     */
    public static function approval_deal_add($strategy_data): bool {
        return BxfStrategy::add($strategy_data);
    }


    /**
     * 添加策略
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-01-08 15:00:44
     *
     */
    public function edit(): bool {
        $user_cookie         = request()->post('user_cookie');
        $score_id            = request()->post('score_id');
        $strategy_name       = request()->post('strategy_name');
        $pv_dict             = request()->post('pv_dict');
        $map_dict            = request()->post('map_dict');
        $pv_seg_innet_dict   = request()->post('pv_seg_innet_dict');
        $pv_seg_noinnet_dict = request()->post('pv_seg_noinnet_dict');
        $filter_dict         = request()->post('filter_dict');
        $strategy_id         = request()->post('strategy_id');

        $username = Func::getUserNameFromCookie($user_cookie);
        $strategy_data = [];

        $strategy_data['pv_dict']             = $pv_dict;
        $strategy_data['map_dict']            = $map_dict;
        $strategy_data['pv_seg_innet_dict']   = $pv_seg_innet_dict;
        $strategy_data['pv_seg_noinnet_dict'] = $pv_seg_noinnet_dict;
        $strategy_data['filter_dict']         = $filter_dict;

        $strategy_data['strategy_name']       = $strategy_name;
        $strategy_data['strategy_id']         = $strategy_id;
        $strategy_data['score_id']            = $score_id;

        $this->check_data($strategy_data);//参数校验

        $strategy_data['pv_dict']             = json_encode(json_decode($pv_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['map_dict']            = json_encode(json_decode($map_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['pv_seg_innet_dict']   = json_encode(json_decode($pv_seg_innet_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['pv_seg_noinnet_dict'] = json_encode(json_decode($pv_seg_noinnet_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $strategy_data['filter_dict']         = json_encode(json_decode($filter_dict),JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $strategy_data['admin'] = $username;
        $strategy_data['updated_at'] = date("Y-m-d H:i:s");

        $strategy_info = BxfStrategy::info($strategy_id);


        // $apply_content = '修改策略:'.json_encode($strategy_data,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $apply_content = $this->edit_diff($strategy_info,$strategy_data);

        //添加到审批
        return ApprovalRepository::addApproval('','','',Approval::URL_BXF_STRATEGY_EDIT,$strategy_data,$apply_content,$user_cookie);
    }



    private function edit_diff($origin,$new): string {
        $apply_content = '';
        if($origin['strategy_name'] != $new['strategy_name']){
            $apply_content .= '策略名: ' . $origin['strategy_name']. ' -> '. $new['strategy_name'] . PHP_EOL;
        }
        if($origin['score_id'] != $new['score_id']){
            $apply_content .= '策略产品id: ' . $origin['score_id']. ' -> '. $new['score_id'] . PHP_EOL;
        }
        if($origin['pv_dict'] != $new['pv_dict']){
            $apply_content .= $this->diff_pv_filter($origin['pv_dict'], $new['pv_dict'], 'pv_dict');
        }
        if($origin['filter_dict'] != $new['filter_dict']){
            $apply_content .= $this->diff_pv_filter($origin['filter_dict'], $new['filter_dict'], 'filter_dict');
        }
        if($origin['map_dict'] != $new['map_dict']){
            $apply_content .= $this->diff_map_seg($origin['map_dict'], $new['map_dict'], 'map_dict');
        }
        if($origin['pv_seg_innet_dict'] != $new['pv_seg_innet_dict']){
            $apply_content .= $this->diff_map_seg($origin['pv_seg_innet_dict'], $new['pv_seg_innet_dict'], 'pv_seg_innet_dict');
        }
        if($origin['pv_seg_noinnet_dict'] != $new['pv_seg_noinnet_dict']){
            $apply_content .= $this->diff_map_seg($origin['pv_seg_noinnet_dict'], $new['pv_seg_noinnet_dict'], 'pv_seg_noinnet_dict');
        }

        return '修改策略:' . PHP_EOL . $apply_content;
    }

    /**
     * pv_dict filter_dict diff对比
     *
     * @param $origin
     * @param $new
     * @param $name
     *
     * @return string
     * <AUTHOR> 2024-01-18 19:40:04
     */
    private function diff_pv_filter($origin,$new,$name): string {
        $apply_content = '';
        $o_pd = json_decode($origin,true);
        $n_pd = json_decode($new,true);
        // $apply_content .= $name.': '.PHP_EOL;
        foreach($o_pd as $ok => $ov){
            if($ov != $n_pd[$ok]){
                $apply_content .= $name.'['.$ok.']: ' . $ov. ' -> '.$n_pd[$ok] . PHP_EOL;
            }
        }
        return $apply_content;
    }

    /**
     *  map_dict,pv_seg_innet_dict,pv_seg_noinnet_dict diff对比
     *
     * @param $origin
     * @param $new
     * @param $name
     *
     * @return string
     * <AUTHOR> 2024-01-18 19:39:40
     */
    private function diff_map_seg($origin,$new,$name): string {
        $apply_content = '';
        $o_pd = json_decode($origin,true);
        $n_pd = json_decode($new,true);
        // $apply_content .= $name.': '.PHP_EOL;
        foreach($o_pd as $ok => $ov){
            if(!isset($n_pd[$ok])){
                $apply_content .= $name.'['.$ok.']: ' . $ov[0]. ','.$ov[1].' -> ' . PHP_EOL;
                continue;
            }
            if($ov[0] != $n_pd[$ok][0]){
                $apply_content .=  $name.'['.$ok.'][0]: ' . $ov[0]. ' -> '.$n_pd[$ok][0] . PHP_EOL;
            }
            if($ov[1] != $n_pd[$ok][1]){
                $apply_content .= $name.'['.$ok.'][1]: ' . $ov[1]. ' -> '.$n_pd[$ok][1] . PHP_EOL;
            }
        }
        return $apply_content;
    }

    /**
     * 审批通过 - 修改策略
     *
     * @param $strategy_data
     *
     * @return bool
     * @static
     * <AUTHOR> 2024-01-09 11:47:11
     */
    public static function approval_deal_edit($strategy_data): bool {
        $strategy_id = $strategy_data['strategy_id'];
        unset($strategy_data['strategy_id']);
        return BxfStrategy::edit($strategy_id,$strategy_data);
    }


    /**
     * 禁用策略
     *
     * @param $strategy_data
     *
     * @static
     * @return bool
     * <AUTHOR> 2024-01-11 17:29:38
     */
    public static function approval_deal_del($strategy_data): bool {
        $strategy_id = $strategy_data['strategy_id'];
        unset($strategy_data['strategy_id']);
        return BxfStrategy::edit($strategy_id,$strategy_data);
    }

    /**
     * 获取策略详情
     *
     * @return array
     * <AUTHOR> 2024-01-09 15:11:50
     *
     */
    public function info(): array {
        $strategy_id = request()->post('strategy_id');
        $info = BxfStrategy::info($strategy_id);

        $info['pv_dict'] = json_encode(json_decode($info['pv_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $info['map_dict'] = json_encode(json_decode($info['map_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $info['pv_seg_innet_dict'] = json_encode(json_decode($info['pv_seg_innet_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $info['pv_seg_noinnet_dict'] = json_encode(json_decode($info['pv_seg_noinnet_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $info['filter_dict'] = json_encode(json_decode($info['filter_dict']), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

        return $info;
    }


    /**
     *
     * 删除策略
     *
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-01-09 16:47:53
     *
     */
    public function del(): bool {
        $user_cookie         = request()->post('user_cookie');
        $strategy_id         = request()->post('strategy_id');

        $strategy_info = BxfStrategy::info($strategy_id);

        //校验是否存在策略配置使用该策略
        $res = BxfStrategyConfig::getListLikeStrategyId($strategy_id);
        if(!empty($res)){
            throw new Exception("该策略正在被使用,不可禁用");
        }

        $username = Func::getUserNameFromCookie($user_cookie);

        $strategy_data = [];
        $strategy_data['admin']       = $username;
        $strategy_data['updated_at']  = date("Y-m-d H:i:s");
        $strategy_data['status']      = BxfStrategy::STATUS_FORBIDDEN;
        $strategy_data['strategy_id'] = $strategy_id;

        $apply_content = '禁用策略:'.$strategy_info['strategy_name'].PHP_EOL.'状态: 启用 -> 禁用';;

        //添加到审批
        return ApprovalRepository::addApproval('','','',Approval::URL_BXF_STRATEGY_DEL,$strategy_data,$apply_content,$user_cookie);
    }
}