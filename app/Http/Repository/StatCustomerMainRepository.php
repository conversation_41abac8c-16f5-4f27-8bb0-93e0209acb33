<?php

namespace App\Http\Repository;

use App\Models\Account;
use App\Models\Product;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Common\CommonEnumModel;
use App\Models\Customer;
use App\Models\SystemUser;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\Cost\ProductCostRepository;
use App\Repositories\Income\CustomerIncomeRepository;
use App\Repositories\Income\ProductIncomeRepository;
use App\Providers\Auth\DataAuth;
use App\Utils\Helpers\Func;

class StatCustomerMainRepository extends StatBaseRepository
{
    public $customerIds_type = [];

    protected $customerid_source_map = [];

    private $dept_salesmans = [];

    private $cshr_month = [];

    private $product_id_father_id_map = [];

    private $apikey_customer_id_map = [];

    /**
     * 数据统计-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        //获取历史商务数据
        $cshr = new CustomerSalesmanHistoryRepository();
        $all_customer_ids = Customer::getRealAllCustomerIds();
        //customer_id => [[month => salesman]...];
        $cshr_month = $cshr->getListMonthly($all_customer_ids,date("Y-m",strtotime($params['start_date'])));
        $this->cshr_month = $cshr_month;

        $this->customerid_source_map = $this->getDataAndSourceAuthCustomerIds($params['username']??"",$params['dept_id']??"",$params['start_date'],$params['end_date']);
        //排除掉主接口调用量
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        $usage_data = $this->getUsageSource($params); //调用量
        $income_data = $this->getIncomeV2Source($params);   //收入
        $cost_data = $this->getCostV2Source($params);   //成本 普通成本
        $adjust_cost_data = $this->getAdjustCostSource($params);   //客户成本调整
        $channel_adjust_cost_data = $this->getChannelAdjustCostSource($params);//渠道成本调整
        $fixed_cost_data = $this->getChannelFixedCostSource($params);//固定费用成本调整
        $expend_data = $this->getExpend($params);   //特殊消耗
        $params['username'] = isset($params['username']) ? $params['username'] : null;
        $params['dept_id'] = isset($params['dept_id']) ? $params['dept_id'] : null;

        $this->customerIds_type = $this->getTypeCustomerIds($params);//符合公司类型条件的客户

        $data = $this->formatData($usage_data, $income_data, $cost_data, $expend_data, $adjust_cost_data, $channel_adjust_cost_data, $fixed_cost_data);
        $data = $this->filterAuthMoney($data); //过滤没有权限查看的产品（收入和成本）

        return $data;
    }

    /**
     * 客户维度统计过滤没有权限查看的产品（收入和成本）
     * @param $data
     * @return mixed
     */
    private function filterAuthMoney($data){
        foreach ($data as $customer_id => $items_father) {
            //判断用户是否有展示金额相关的权限，没有则用-标识没权限，跟0区分开
            foreach ($items_father as $father_id => $item_product) {
                foreach ($item_product as $product_id => $item_opr) {
                    foreach ($item_opr as $operator => $value) {
                        if (!$this->checkUserProductAuth($father_id,'money')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['money'] = '-';
                        }
                        if (!$this->checkUserProductAuth($father_id,'money_finance')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['money_finance'] = '-';
                        }
                        if (!$this->checkUserProductAuth($father_id,'money_agent')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['money_agent'] = '-';
                        }
                        if (!$this->checkUserProductAuth($father_id,'cost')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['cost'] = '-';
                        }
                        if (!$this->checkUserProductAuth($father_id,'cache')) {
                            $data[$customer_id][$father_id][$product_id][$operator]['cache'] = '-';
                        }
                    }
                }
            }
        }
        return $data;
    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @param array $adjust_cost_data
     * @param array $channel_adjust_cost_data
     * @param array $fixed_cost_data
     * @return array
     * @throws \Exception
     */
    private function formatData($usage_data, $income_data, $cost_data, $expend_data, $adjust_cost_data = [], $channel_adjust_cost_data = [], $fixed_cost_data = []){
        //获取各征信机构扣除后收入费率
        $sourceFeeMap = CommonEnumModel::getTypeMaps('agent_income_fee');

        //优化时间 21000+条数据 16.6s -> 0.32
        // $customer_id = $this->cache_get_customer_id($item['apikey']);
        // $father_id = $this->cache_get_father_id($item['product_id']);

        $tpl = [
            'total'       => 0,
            'success'     => 0,
            'valid'       => 0,
            'cache'       => 0,
            'number'      => 0,
            'money_finance' => 0,
            'money'       => 0,
            'cost'        => 0,
            'money_agent' => 0,
            'father_id'   => 0,
        ];
        $data = [];
        $usage_data = $this->transProduct($usage_data);

        foreach ($usage_data as $item){
            $customer_id = $this->cache_get_customer_id($item['apikey']);
            $father_id = $this->cache_get_father_id($item['product_id']);

            if($this->filterDataSource($customer_id,$item['source'],$item['month'])){continue;}
            if (!isset($data[$customer_id][$father_id][$item['product_id']][$item['operator']])) {
                $data[$customer_id][$father_id][$item['product_id']][$item['operator']] = $tpl;
                $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['father_id'] = $father_id;
            }
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['total'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['total'],$item['total'],6);
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['success'] =
                bcadd( $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['success'],$item['success'],6);
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['valid'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['valid'],$item['valid'],6);
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['cache'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['cache'],$item['cache'],6);
        }

        $income_data = $this->transProduct($income_data);
        foreach ($income_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $father_id = Product::getDisplayFatherId($item['product_id']);
            if($this->filterDataSource($customer_id,$item['source'],$item['month'])){continue;}

            if (!isset($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['number'])) {
                $data[$customer_id][$father_id][$item['product_id']][$item['operator']] = $tpl;
                $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['father_id'] = $father_id;
            }
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['number'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['number'],$item['number'],6);
            //客户正常真实收入
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['money'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['money'],$item['money'],6);

            //金融收入
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['money_finance'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['money_finance'], $item['money_finance'],6);

            //征信收入
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['money_agent'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['money_agent'],isset($item['money_agent'])?$item['money_agent']:0,6);

        }

        $cost_data = $this->transProduct($cost_data);
        foreach ($cost_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $father_id = Product::getDisplayFatherId($item['product_id']);
            if($this->filterDataSource($customer_id,$item['source'],$item['month'])){continue;}

            if (!isset($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['cost'])) {
                $data[$customer_id][$father_id][$item['product_id']][$item['operator']] = $tpl;
                $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['father_id'] = $father_id;
            }
            $data[$customer_id][$father_id][$item['product_id']][$item['operator']]['cost'] =
                bcadd($data[$customer_id][$father_id][$item['product_id']][$item['operator']]['cost'],$item['money'],6);
        }
        foreach ($expend_data['add'] as $item) {
            $father_id = Product::getDisplayFatherId($item['product_id']);
            if($this->filterDataSource($item['customer_id'],$item['source'],$item['month'])){continue;}

            if (!isset($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗'])) {
                $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗'] = $tpl;
            }
            $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['number'] =
                bcadd($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['number'],$item['fee_number'],6);
            $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money'] =
                bcadd($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money'],$item['money'],6);

            //金融收入
            $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_finance'] =
                bcadd($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_finance'], $item['money_finance'],6);

            if($item['source']>0&&$item['source']<100){
                // 运营填写的特殊消耗，未扣除服务费，需要乘以费率 未配置为1 配置值 0-1 之间，20221101之前需要扣除 之后特殊消耗就是扣点后金额
                if ($item['profile_show_date']<date('Y-m-d',strtotime('2022-11-01'))) {
                    $rate = isset($sourceFeeMap[$item['source']]) ? $sourceFeeMap[$item['source']] : 1;
                    $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'] =
                        bcadd($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'],bcmul($item['money'],$rate,6),6);
                }else{
                    $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'] =
                        bcadd($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'],$item['money'],6);
                }
            }
        }
        foreach ($expend_data['sub'] as $item) {
            $father_id = Product::getDisplayFatherId($item['product_id']);
            if($this->filterDataSource($item['customer_id'],$item['source'],$item['month'])){continue;}

            if (!isset($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗'])) {
                $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗'] = $tpl;
            }
            $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['number'] =
                bcsub($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['number'],$item['fee_number'],6);
            $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money'] =
                bcsub($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money'],$item['money'],6);

            //金融收入
            $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_finance'] =
                bcsub($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_finance'], $item['money_finance'],6);

            if($item['source']>0&&$item['source']<100){
                // 运营填写的特殊消耗，未扣除服务费，需要乘以费率 未配置为1 配置值 0-1 之间 ,20221101之前需要扣除 之后特殊消耗就是扣点后金额
                if ($item['profile_show_date']<date('Y-m-d',strtotime('2022-11-01'))) {
                    $rate = isset($sourceFeeMap[$item['source']])?$sourceFeeMap[$item['source']]:1;
                    $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'] =
                        bcsub($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'],bcmul($item['money'],$rate,6),6);
                }else{
                    $data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'] =
                        bcsub($data[$item['customer_id']][$father_id][$item['product_id']]['特殊消耗']['money_agent'],$item['money'],6);
                }
           }
        }

        foreach($adjust_cost_data as $item){
            if($this->filterDataSource($item['customer_id'],$item['source'],$item['month'])){continue;}
            $father_id = 'adjust';//在前端字典已经做了该key映射
            $product_id = 'NO';//在前端字典已经做了该key映射
            $operator = 'NO';//在前端字典已经做了该key映射
            if(!isset($data[$item['customer_id']][$father_id][$product_id][$operator])){
                $data[$item['customer_id']][$father_id][$product_id][$operator] = $tpl;
                $data[$item['customer_id']][$father_id][$product_id][$operator]['father_id'] = $father_id;
            }

            $data[$item['customer_id']][$father_id][$product_id][$operator]['cost'] += $item['money'];
        }

        foreach($channel_adjust_cost_data as $item){
//            $father_id = 'channel_adjust';//在前端字典已经做了该key映射
//            $product_id = 'NO';//在前端字典已经做了该key映射
//            $operator = 'NO';//在前端字典已经做了该key映射

            $product_id = $item['product_id'];
            $father_id = Product::getDisplayFatherId($product_id);
            $operator = '渠道成本调整';
            if(!isset($data[$item['customer_id']][$father_id][$product_id][$operator])){
                $data[$item['customer_id']][$father_id][$product_id][$operator] = $tpl;
                $data[$item['customer_id']][$father_id][$product_id][$operator]['father_id'] = $father_id;
            }
            $sum_channel_adjust_cost = $data[$item['customer_id']][$father_id][$product_id][$operator]['cost'];

            $data[$item['customer_id']][$father_id][$product_id][$operator]['cost'] = bcadd($sum_channel_adjust_cost, $item['money'], $this->degree);
        }

        foreach($fixed_cost_data as $customer_id => $item){
            $father_id = 'fixed_adjust';//在前端字典已经做了该key映射
            $product_id = 'NO';//在前端字典已经做了该key映射
            $operator = 'NO';//在前端字典已经做了该key映射
            if(!isset($data[$customer_id][$father_id][$product_id][$operator])){
                $data[$customer_id][$father_id][$product_id][$operator] = $tpl;
                $data[$customer_id][$father_id][$product_id][$operator]['father_id'] = $father_id;
            }
            $data[$customer_id][$father_id][$product_id][$operator]['cost'] = $item['money'];
        }

        //过滤公司类型限定的客户
        $data = array_filter($data, function ($key) {
            return  in_array($key,  $this->customerIds_type);
        }, ARRAY_FILTER_USE_KEY);

        return $data;
    }

    // 按照客户及客户来源过滤数据
    public function filterDataSource($customer_id,$source,$month){
        if(empty($customer_id)){
            return true;// f60c38ddb9fde408786bd6591eaddfe1 在statistics_customer_usage中存在数据 在account表中查询不到
        }

        $auth = $this->customerid_source_map;
        if(!isset($auth[$customer_id][$month])){
            return true;// 被过滤掉
        }
        if(in_array(-1, $auth[$customer_id][$month]) || in_array($source, $auth[$customer_id][$month])){
            return false;// 有权限，不用过滤
        }else{
            return true;// 无权限，被过滤掉
        }
    }

    protected function getChannelAdjustCostSource($params){
        //由于ChannelAccountAdjust中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelCostByCustomerSourceMonthly($where, $params);

        $result = [];
        foreach($cost_data as $item){
            // 客户来源权限过滤
            $customer_id = $item['customer_id'];
            if($this->filterDataSource($customer_id,$item['source'],$item['month'])){
                continue;
            }
//            if(!isset($result[$customer_id])){
//                $result[$customer_id]['money'] = 0;
//                $result[$customer_id]['number'] = 0;
//                $result[$customer_id]['customer_id'] = $customer_id;
//            }
//            $result[$customer_id]['money'] = bcadd($result[$customer_id]['money'], $item['money'], $this->degree);
//            $result[$customer_id]['number'] +=$item['number'];
            $result[] = $item;
        }
        return $result;
    }

    protected  function getChannelFixedCostSource($params){
        //由于ChannelAccountFixedFee中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);

        $cost_data = ChannelAccountFixedFee::getChannelCostByCustomerSourceMonthly($where, $params);
        $result = [];
        foreach($cost_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            // 客户来源权限过滤
            if($this->filterDataSource($customer_id,$item['source'],$item['month'])){
                continue;
            }
            $total_money  = $result[$customer_id]['money'] ?? 0;
            $result[$customer_id]['money'] = bcadd($total_money, $item['money'], $this->degree);
        }
        return $result;

    }


    /**
     * 客户统计趋势图
     * @return mixed
     * @throws \Exception
     */
    public function statChartList()
    {
        try {
            $params = $this->getStatParams();
            //getStatParams方法中的参数大都是共用的 故统一在在该方法中处理参数,为了保证该方法比较干净简洁,
            //故一些新参数可以在下面在单独添加进$params, 当然不是必须的
            $params['day_type'] = request()->post('day_type', 'day');
            if($params['day_type'] == 'month'){
                $params['start_date'] = $params['start_date'];
                $params['end_date'] = $this->getLastDay($params['end_date']);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        //排除掉主接口调用量
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        $customerIds = $this->getAuthCustomerIds(); //过滤不属于自己的客户
        if($params['chart_type'] == 'valid' || $params['chart_type'] == 'total'){
            $usage_data = $this->getUsageByCustomer($params); //调用量
            $data = $this->formatDataUsage($usage_data, $customerIds, $params);
        }
        if($params['chart_type'] == 'money'){
            $income_data = $this->getIncomeByCustomer($params); //收入
            $expend_data = $this->getExpendByCustomer($params);   //特殊消耗
            $data = $this->formatDataIncome($income_data, $expend_data, $customerIds, $params);
        }

        return $data;
    }


    private function formatDataUsage($usage_data, $customerIds, $params)
    {

        $data = [];
        $top5 = [];
        $result = [];
        foreach ($usage_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $data[$customer_id]['total'] = $data[$customer_id]['total'] ?? 0;
            $data[$customer_id]['total'] += $item['total'];
            $data[$customer_id]['valid'] = $data[$customer_id]['valid'] ?? 0;
            $data[$customer_id]['valid'] += $item['valid'];

            $result[$customer_id][$item['date']]['total'] = $result[$customer_id][$item['date']]['total'] ?? 0;
            $result[$customer_id][$item['date']]['total'] += $item['total'];
            $result[$customer_id][$item['date']]['valid'] = $result[$customer_id][$item['date']]['valid'] ?? 0;
            $result[$customer_id][$item['date']]['valid'] += $item['valid'];
        }

        $chart_column = array_column($data, $params['chart_type']);
        array_multisort($chart_column, SORT_DESC, $data);//根据查看的指标类型排序
        $top5 = array_slice($data, 0, 5);//获取排名前5名客户
        $top5_customer_id = array_keys($top5);

        $dateRange = $this->getDateFromRange($params['start_date'], $params['end_date']);//获取区间时间
        if($params['day_type'] == 'day'){
            $res = $this->getDayData($top5_customer_id, $dateRange, $result);
        }

        if($params['day_type'] == 'month'){
            $res = $this->getMonthData($top5_customer_id, $dateRange, $result);
        }

        if($params['day_type'] == 'week'){
            $res = $this->getWeekData($top5_customer_id, $dateRange, $result);
        }

        return $res;

        //过滤掉不属于自己的客户
        $data = array_filter($data, function ($key) use ($customerIds) {
            return in_array($key, $customerIds);
        }, ARRAY_FILTER_USE_KEY);

        return $data;
    }

    public function formatDataIncome($income_data, $expend_data, $customerIds, $params){

        $data = [];
        $top5 = [];
        $result = [];
        foreach($income_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $data[$customer_id]['money'] = $data[$customer_id]['money'] ?? 0;
            $data[$customer_id]['money'] = bcadd($data[$customer_id]['money'], $item['money'], $this->degree);

            $result[$customer_id][$item['date']]['money'] = $result[$customer_id][$item['date']]['money'] ?? 0;
            $result[$customer_id][$item['date']]['money'] = bcadd($result[$customer_id][$item['date']]['money'], $item['money'], $this->degree);
        }

        foreach($expend_data['add'] as $item){
            $data[$item['customer_id']]['money'] = $data[$item['customer_id']]['money'] ?? 0;
            $data[$item['customer_id']]['money'] = bcadd($data[$item['customer_id']]['money'], $item['money'], $this->degree);

            $result[$item['customer_id']][$item['date']]['money'] = $result[$item['customer_id']][$item['date']]['money'] ?? 0;
            $result[$item['customer_id']][$item['date']]['money'] = bcadd($result[$item['customer_id']][$item['date']]['money'], $item['money'], $this->degree);
        }

        foreach($expend_data['sub'] as $item){
            $data[$item['customer_id']]['money'] = $data[$item['customer_id']]['money'] ?? 0;
            $data[$item['customer_id']]['money'] = bcsub($data[$item['customer_id']]['money'], $item['money'], $this->degree);

            $result[$item['customer_id']][$item['date']]['money'] = $result[$item['customer_id']][$item['date']]['money'] ?? 0;
            $result[$item['customer_id']][$item['date']]['money'] = bcsub($result[$item['customer_id']][$item['date']]['money'], $item['money'], $this->degree);
        }

        $chart_column = array_column($data, 'money');
        array_multisort($chart_column, SORT_DESC, $data);//根据查看的指标类型排序
        $top5 = array_slice($data, 0, 5);//获取排名前5名客户
        $top5_customer_id = array_keys($top5);

        $dateRange = $this->getDateFromRange($params['start_date'], $params['end_date']);//获取区间时间
        if($params['day_type'] == 'day'){
            $res = $this->getDayData($top5_customer_id, $dateRange, $result, $params['chart_type']);
        }

        if($params['day_type'] == 'month'){
            $res = $this->getMonthData($top5_customer_id, $dateRange, $result, $params['chart_type']);
        }

        if($params['day_type'] == 'week'){
            $res = $this->getWeekData($top5_customer_id, $dateRange, $result, $params['chart_type']);
        }

        //dd($res);
        return $res;


        //过滤掉不属于自己的客户
        $data = array_filter($data, function ($key) use ($customerIds) {
            return in_array($key, $customerIds);
        }, ARRAY_FILTER_USE_KEY);

        return $data;


    }




    public function getDayData($top5_customer_id, $dateRange, $result, $chart_type = ''){

        $arr = [];//把总时间段内的前5名客户,每个时间段(每天或每周或每月等)的指标值放该数组里(或者理解为该前5名客户在每个时间段内,他们的指标值情况)
        //为了在echart中容易遍历数据，数据组装成每个客户在各个时间段内的指标值
        foreach($top5_customer_id as $customer_id){
            foreach($dateRange as $date){
                if($chart_type == 'money'){
                    $arr[$customer_id][$date]['money'] = $result[$customer_id][$date]['money'] ?? 0;
                }else{
                    $arr[$customer_id][$date]['total'] = $result[$customer_id][$date]['total'] ?? 0;
                    $arr[$customer_id][$date]['valid'] = $result[$customer_id][$date]['valid'] ?? 0;
                }
            }
        }

        return ['x_val' => $dateRange, 'y_val' => $arr];
    }


    public function getWeekData($top5_customer_id, $dateRange, $result, $chart_type = ''){
        $arr = [];
        $week_arr = [];
        foreach($dateRange as $date){
            $week = $this->getDateWeek($date);//根据日期获取所在年份的第几周
            $week_arr[$week][] = $date;
        }

        foreach($top5_customer_id as $customer_id){
            foreach($week_arr as $week => $date_arr){
                foreach($date_arr as $date){
                    if($chart_type == 'money'){
                        $arr[$customer_id][$week]['money'] = $arr[$customer_id][$week]['money'] ?? 0;
                        $total = $result[$customer_id][$date]['money'] ?? 0;
                        $arr[$customer_id][$week]['money'] += $total;
                    }else{
                        $arr[$customer_id][$week]['total'] = $arr[$customer_id][$week]['total'] ?? 0;
                        $total = $result[$customer_id][$date]['total'] ?? 0;
                        $arr[$customer_id][$week]['total'] += $total;

                        $arr[$customer_id][$week]['valid'] = $arr[$customer_id][$week]['valid'] ?? 0;
                        $valid = $result[$customer_id][$date]['valid'] ?? 0;
                        $arr[$customer_id][$week]['valid'] += $valid;
                    }

                }

            }
        }

        $x_val = [];
        foreach($week_arr as $week=>$date_arr){
            $week_num = 'W'.substr($week, -2);//取出周数
            $start_date = date('Y.m.d', strtotime(reset($date_arr)));
            $end_date = date('Y.m.d', strtotime(end($date_arr)));
            $date_str = $start_date."\n".$end_date."\n".'('.$week_num.')';
            $x_val[] = $date_str;
        }

        return ['x_val' => $x_val, 'y_val' => $arr];
    }


    public function getMonthData($top5_customer_id, $dateRange, $result, $chart_type = ''){

        $arr = [];
        $month_arr = [];
        foreach($dateRange as $date){
            $month = substr($date, 0, 6);
            $month_arr[$month][] = $date;
        }

        foreach($top5_customer_id as $customer_id){
            foreach($month_arr as $month => $date_arr){
                foreach($date_arr as $date){
                    if($chart_type == 'money'){
                        $arr[$customer_id][$month]['money'] = $arr[$customer_id][$month]['money'] ?? 0;
                        $total = $result[$customer_id][$date]['money'] ?? 0;
                        $arr[$customer_id][$month]['money'] += $total;
                    }else{
                        $arr[$customer_id][$month]['total'] = $arr[$customer_id][$month]['total'] ?? 0;
                        $total = $result[$customer_id][$date]['total'] ?? 0;
                        $arr[$customer_id][$month]['total'] += $total;

                        $arr[$customer_id][$month]['valid'] = $arr[$customer_id][$month]['valid'] ?? 0;
                        $valid = $result[$customer_id][$date]['valid'] ?? 0;
                        $arr[$customer_id][$month]['valid'] += $valid;
                    }

                }
            }

        }

        $x_val = array_keys($month_arr);

        return ['x_val' => $x_val, 'y_val' => $arr];

    }

    public function getDownlandCustomerDatasForMonth()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        $this->customerid_source_map = $this->getDataAndSourceAuthCustomerIds($params['username']??"",$params['dept_id']??"",$params['start_date'],$params['end_date']);

        $customerIds = array_keys($this->customerid_source_map);
        //符合公司类型条件的客户
        $tmpCustomerIds = $this->getTypeCustomerIds($params);
        $customerIds = array_intersect($customerIds, $tmpCustomerIds);
        $apikeys = (new Account())->select('apikey')->whereIn('customer_id', $customerIds)->get()->toArray();
        $apikeys = array_unique(array_filter(array_column($apikeys, 'apikey')));
        if (empty($params['apikey_list'])) {
            $params['apikey_list'] = $apikeys;
        } else {
            $params['apikey_list'] = array_intersect($params['apikey_list'], $apikeys);
        }
        if (!$params['apikey_list']) {
            return [];
        }
        //排除掉主接口调用量
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        $params['customer'] = $params['customer_ids']??[];

        $incomeRepository = new ProductIncomeRepository();
        $costRepository = new ProductCostRepository();
        $costDatas = $costRepository->getProductCostTotalForMonth($params, $costRepository::$type['客户']);
        $incomeDatas = $incomeRepository->getProductIncomeTotalForMonth($params);
        $usageWhere = [
            ['date', '>=', $params['start_date']],
            ['date', '<=', $params['end_date']]
        ];
        isset($params['apikey']) && $usageWhere[] = ['apikey', $params['apikey']];
        isset($params['product_id']) && $usageWhere[] = ['product_id', $params['product_id']];
        isset($params['operator']) && $usageWhere[] = ['operator', $params['operator']];
        isset($params['source']) && $usageWhere[] = ['source', $params['source']];

        $usage = StatisticsCustomerUsage::getStatListForMonth($usageWhere, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product']);
        $usage = count($usage) ? $usage->toArray() : [];

        $data = $this->formatDataForMonth($usage, $costDatas, $incomeDatas,$params['source']??-1);
        $data = $this->filterAuthMoneyDownload($data);
        return array_values($data);
    }

    /**
     * 客户维度统计的按月导出过滤没有权限查看的产品（收入和成本）
     * @param $data
     * @return mixed
     */
    private function filterAuthMoneyDownload($data){
        foreach ($data as $uk => $item) {
            //判断用户是否有展示金额相关的权限，没有则用-标识没权限，跟0区分开
            if (!$this->checkUserProductAuth($item['father_id'],'money')) {
                $data[$uk]['money']  = '-';
                $data[$uk]['money_finance']  = '-';
            }
            if(!$this->checkUserProductAuth($item['father_id'],'cost')){
                $data[$uk]['cost'] = '-';
            }
            if (!$this->checkUserProductAuth($item['father_id'],'money_agent')) {
                $data[$uk]['money_agent'] = '-';
            }
        }
        return $data;
    }


    private function formatDataForMonth($usage_data, $cost_data, $income_data,$source)
    {
        $productIds = array_unique(array_merge(array_column($usage_data,'product_id'), array_column($cost_data,'product_id'),array_column($income_data,'product_id')));
        $fatherMap = [];
        foreach ($productIds as $id) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($id) ?: $id;
            $fatherMap[$id]['father_id'] = $father_id;
            $fatherMap[$id]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
            $fatherMap[$id]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($id);
        }

        $apikeys = array_unique(array_column($usage_data,'apikey'));
        $apikeyMap = [];
        foreach ($apikeys as $id) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($id);
            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
            $apikeyMap[$id]['customer_id'] = $customer_id;
            $apikeyMap[$id]['customer_name'] = $customer_name;
        }

        $customerIds = array_unique(array_merge(array_column($income_data, 'customer_id'),array_column($cost_data, 'customer_id'),array_column($apikeyMap, 'customer_id')));
        $customerIdMap = [];
        foreach ($customerIds as $id) {
            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($id);
            $customerIdMap[$id] = $customer_name;
        }
        //customer_type salesman
        $customerMap = Customer::whereIn("customer_id", $customerIds)
        ->select(['customer_id','customer_type','salesman'])
        ->get()
        ->toArray();
        $customerTypeMap = [];
        $customerSalemanMap = [];
        foreach ($customerMap as  $value) {
            $customerTypeMap[$value['customer_id']] = $value['customer_type'];
            $customerSalemanMap[$value['customer_id']] = $value['salesman'];
        }
        $data = [];
        foreach ($usage_data as $item){
            $customer_id = $apikeyMap[$item['apikey']]['customer_id'];
            $father_id = $fatherMap[$item['product_id']]['father_id'];
            //临时逻辑 信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
            $checkRes = $this->checkTransProduct($customer_id, $item['product_id']);
            $item['product_id'] = $checkRes ? '712' : $item['product_id'];

            //过滤商务
            if($this->filterDataSource($customer_id,$source,$item['month'])){continue;}

            $uniqueKey = $item['month'] . $customer_id . $father_id. $item['product_id'] . $item['operator'];
            if (!isset($data[$uniqueKey])) {
                $data[$uniqueKey]['month'] = $item['month'];
                $data[$uniqueKey]['operator'] = $item['operator'];
                $data[$uniqueKey]['father_id'] = $fatherMap[$item['product_id']]['father_id'];
                $data[$uniqueKey]['father_name'] = $fatherMap[$item['product_id']]['father_name'];
                $data[$uniqueKey]['product_id'] = $item['product_id'];
                $data[$uniqueKey]['product_name'] = $fatherMap[$item['product_id']]['product_name'];
                $data[$uniqueKey]['customer_id'] = $apikeyMap[$item['apikey']]['customer_id'];
                $data[$uniqueKey]['customer_name'] = $apikeyMap[$item['apikey']]['customer_name'];
                $data[$uniqueKey]['total'] = 0;
                $data[$uniqueKey]['success'] = 0;
                $data[$uniqueKey]['valid'] = 0;
                $data[$uniqueKey]['number'] = 0;
                $data[$uniqueKey]['money'] = 0;
                $data[$uniqueKey]['money_finance'] = 0;
                $data[$uniqueKey]['cost'] = 0;
            }
            $data[$uniqueKey]['total'] += $item['total'];
            $data[$uniqueKey]['success'] += $item['success'];
            $data[$uniqueKey]['valid'] += $item['valid'];
        }

        foreach ($income_data as $item) {
            //临时逻辑 信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
            $checkRes = $this->checkTransProduct($item['customer_id'], $item['product_id']);
            $item['product_id'] = $checkRes ? '712' : $item['product_id'];

            //过滤商务
            if($this->filterDataSource($item['customer_id'],$source,$item['month'])){continue;}

            $uniqueKey = $item['month'] . $item['customer_id'] . $item['father_id']. $item['product_id'] . $item['operator'];
            if (!isset($data[$uniqueKey])) {
                $data[$uniqueKey]['month'] = $item['month'];
                $data[$uniqueKey]['operator'] = $item['operator'];
                $data[$uniqueKey]['father_id'] = $item['father_id'];
                $data[$uniqueKey]['father_name'] = $fatherMap[$item['product_id']]['father_name'];
                $data[$uniqueKey]['product_id'] = $item['product_id'];
                $data[$uniqueKey]['product_name'] = $fatherMap[$item['product_id']]['product_name'];
                $data[$uniqueKey]['customer_id'] = $item['customer_id'];
                $data[$uniqueKey]['customer_name'] = $customerIdMap[$item['customer_id']];
                $data[$uniqueKey]['total'] = 0;
                $data[$uniqueKey]['success'] = 0;
                $data[$uniqueKey]['valid'] = 0;
                $data[$uniqueKey]['number'] = 0;
                $data[$uniqueKey]['money'] = 0;
                $data[$uniqueKey]['money_finance'] = 0;
                $data[$uniqueKey]['cost'] = 0;
            }

            $data[$uniqueKey]['number'] += $item['number'];
            $data[$uniqueKey]['money'] = bcadd($data[$uniqueKey]['money'], $item['money'], $this->degree);
            $data[$uniqueKey]['money_finance'] = bcadd($data[$uniqueKey]['money_finance'], $item['money_finance'], $this->degree);
        }

        foreach ($cost_data as $item) {
            //临时逻辑 信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
            $checkRes = $this->checkTransProduct($item['customer_id'], $item['product_id']);
            $item['product_id'] = $checkRes ? '712' : $item['product_id'];

            //过滤商务
            if($this->filterDataSource($item['customer_id'],$source,$item['month'])){continue;}

            $uniqueKey = $item['month'] . $item['customer_id'] . $item['father_id']. $item['product_id'] . $item['operator'];

            if (!isset($data[$uniqueKey])) {
                $data[$uniqueKey]['month'] = $item['month'];
                $data[$uniqueKey]['operator'] = $item['operator'];
                $data[$uniqueKey]['father_id'] = $item['father_id'];
                $data[$uniqueKey]['father_name'] = $fatherMap[$item['product_id']]['father_name'];
                $data[$uniqueKey]['product_id'] = $item['product_id'];
                $data[$uniqueKey]['product_name'] = $fatherMap[$item['product_id']]['product_name'];
                $data[$uniqueKey]['customer_id'] = $item['customer_id'];
                $data[$uniqueKey]['customer_name'] = $customerIdMap[$item['customer_id']];
                $data[$uniqueKey]['total'] = 0;
                $data[$uniqueKey]['success'] = 0;
                $data[$uniqueKey]['valid'] = 0;
                $data[$uniqueKey]['number'] = 0;
                $data[$uniqueKey]['money'] = 0;
                $data[$uniqueKey]['money_finance'] = 0;
                $data[$uniqueKey]['cost'] = 0;
            }

            $data[$uniqueKey]['cost'] = bcadd($data[$uniqueKey]['cost'], $item['cost'], $this->degree);
        }

        foreach ($data as $uniqueKey => $value) {
            $data[$uniqueKey]['customer_type'] = $customerTypeMap[$value['customer_id']];
            $data[$uniqueKey]['customer_type_name'] = $customerTypeMap[$value['customer_id']]==1?'金融用户':'企服用户';
            $data[$uniqueKey]['salesman'] = $customerSalemanMap[$value['customer_id']];
        }

        return $data;
    }


    /**
     * 获取客户维度统计数据 包括不同的商务数据 按月份区分
     * @return array
     * @throws \Exception
     */
    public function getDownlandCustomerDatasForMonthAndSalesman(){
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        //获取当前用户可以查看的客户
        $username = $params['username'] ?? null;
        $dept_id = $params['dept_id'] ?? null;
        // $customerIds = $this->getAuthCustomerIds($username,$dept_id);
        $this->customerid_source_map = $this->getDataAndSourceAuthCustomerIds($params['username']??"",$params['dept_id']??"",$params['start_date'],$params['end_date']);
        $customerIds = array_keys($this->customerid_source_map);

        //符合公司类型条件的客户
        $tmpCustomerIds = $this->getTypeCustomerIds($params);
        $customerIds = array_intersect($customerIds, $tmpCustomerIds);

        $params['customer'] = $customerIds;
        if(!empty($params['customer_ids'])){
            $params['customer'] = array_intersect($params['customer'],$params['customer_ids']);
        }
        if(!empty($params['customer_id'])){
            $params['customer'] = [$params['customer_id']];
        }

        $apikeys = Account::getApikeysByCustomerIdsNew($params['customer']);
        $apikeys = array_unique($apikeys);
        $apikeys = array_filter($apikeys);

        $params['apikey_list'] = $apikeys;

        if (!$params['apikey_list']) {
            return [];
        }

        //获取历史商务数据
        $cshr = new CustomerSalesmanHistoryRepository();
        $cshr_month = $cshr->getListMonthly($params['customer'],date('Y-m',strtotime($params['start_date'])));

        //排除掉主接口调用量
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');

        $incomeRepository = new ProductIncomeRepository();
        $costRepository = new ProductCostRepository();

        $costDatas = $costRepository->getProductCostTotalForMonthAndSalesman($params);
        $incomeDatas = $incomeRepository->getProductIncomeTotalForMonthAndSalesman($params);

        $usageWhere = [
            ['date', '>=', $params['start_date']],
            ['date', '<=', $params['end_date']]
        ];

        // isset($params['apikey_list']) && $usageWhere[] = ['apikey', $params['apikey_list']];
        isset($params['product_id']) && $usageWhere[] = ['product_id', $params['product_id']];
        isset($params['operator']) && $usageWhere[] = ['operator', $params['operator']];
        isset($params['source']) && $usageWhere[] = ['source', $params['source']];

        $usage = StatisticsCustomerUsage::getStatListForMonthWithSource($usageWhere, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product']);
        $usage = count($usage) ? $usage->toArray() : [];

        $data = $this->formatDataForMonthAndSalesman($usage, $costDatas, $incomeDatas,$cshr_month, $params['apikey_list']);

        return $data;
    }


    /**
     * 根据商务与月份关系整理数据
     * @param $usage_data
     * @param $cost_data
     * @param $income_data
     * @param $cshr_month
     * @return array
     * @throws \Exception
     */
    private function formatDataForMonthAndSalesman($usage_data, $cost_data, $income_data,$cshr_month, $apikey_list = []){
        //收集数据
        $apikey_map = [];
        $customer_ids = [];
        $product_ids = [];
        foreach($usage_data as $info){
            if(!empty($apikey_list) && !in_array($info['apikey'], $apikey_list)){
                continue;
            }

            $product_ids[$info['product_id']] = $info['product_id'];

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($info['apikey']);
            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
            $apikey_map[$info['apikey']]['customer_id'] = $customer_id;
            $apikey_map[$info['apikey']]['customer_name'] = $customer_name;
            $customer_ids[$customer_id] = $customer_id;
        }

        unset($apikey_list);

        foreach($cost_data as $info){
            $product_ids[$info['product_id']] = $info['product_id'];
            $customer_ids[$info['customer_id']] = $info['customer_id'];
        }

        foreach($income_data as $info){
            $product_ids[$info['product_id']] = $info['product_id'];
            $customer_ids[$info['customer_id']] = $info['customer_id'];
        }

        $father_map = [];
        foreach ($product_ids as $product_id) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id) ?: $product_id;
            $father_map[$product_id]['father_id'] = $father_id;
            $father_map[$product_id]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
            $father_map[$product_id]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($product_id);
        }

        $customer_infos = Customer::whereIn('customer_id', array_keys($customer_ids))
            ->select(['customer_id','customer_type','salesman','name'])
            ->get()
            ->toArray();

        $customer_map = array_column($customer_infos, null,'customer_id');
        $data = [];
        foreach ($usage_data as $item){
            $customer_id = $apikey_map[$item['apikey']]['customer_id'] ?? (RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']) ?: $item['apikey']);
            $father_id = $father_map[$item['product_id']]['father_id'] ?? (RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']) ?: $item['product_id']);
            if($this->filterDataSource($customer_id,-1,$item['month'])){continue;}
            //临时逻辑 信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
            $checkRes = $this->checkTransProduct($customer_id, $item['product_id']);
            $item['product_id'] = $checkRes ? '712' : $item['product_id'];

            $uk = implode('_',[$item['month'], $customer_id, $father_id, $item['product_id'], $item['operator'],$item['source']]);

            if (!key_exists($uk, $data)) {
                $data[$uk]['month'] = $item['month'];
                $data[$uk]['father_id'] = $father_id;
                $data[$uk]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
                $data[$uk]['product_id'] = $item['product_id'];
                $data[$uk]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($item['product_id']);;
                $data[$uk]['customer_id'] = $apikey_map[$item['apikey']]['customer_id'];
                $data[$uk]['customer_name'] = $apikey_map[$item['apikey']]['customer_name'];
                $data[$uk]['customer_type'] = $customer_map[$customer_id]['customer_type'];
                $data[$uk]['customer_type_name'] = $customer_map[$customer_id]['customer_type'] == 1 ? '金融用户' : '企服用户';
                $data[$uk]['salesman'] = $cshr_month[$customer_id][$item['month']] ?? $customer_map[$item['customer_id']]['salesman'];
                $data[$uk]['operator'] = $item['operator'];
                $data[$uk]['source'] = $item['source'];
                $data[$uk]['total'] = 0;
                $data[$uk]['success'] = 0;
                $data[$uk]['valid'] = 0;
                $data[$uk]['number'] = 0;
                $data[$uk]['money'] = 0;
                $data[$uk]['money_finance'] = 0;
                $data[$uk]['cost'] = 0;
            }
            $data[$uk]['total'] += $item['total'];
            $data[$uk]['success'] += $item['success'];
            $data[$uk]['valid'] += $item['valid'];
        }

        foreach ($income_data as $item) {
            if($this->filterDataSource($item['customer_id'],-1,$item['month'])){continue;}

            //临时逻辑 信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
            $checkRes = $this->checkTransProduct($item['customer_id'], $item['product_id']);
            $item['product_id'] = $checkRes ? '712' : $item['product_id'];

            $uk = implode('_',[$item['month'], $item['customer_id'], $item['father_id'], $item['product_id'], $item['operator'], $item['source']]);

            if (!key_exists($uk, $data)) {
                $data[$uk]['month'] = $item['month'];
                $data[$uk]['father_id'] = $item['father_id'];
                $data[$uk]['father_name'] = $father_map[$item['product_id']]['father_name'];
                $data[$uk]['product_id'] = $item['product_id'];
                $data[$uk]['product_name'] = $father_map[$item['product_id']]['product_name'];
                $data[$uk]['customer_id'] = $item['customer_id'];
                $data[$uk]['customer_name'] = $customer_map[$item['customer_id']]['name'];
                $data[$uk]['customer_type'] = $customer_map[$item['customer_id']]['customer_type'];
                $data[$uk]['customer_type_name'] = $customer_map[$item['customer_id']]['customer_type'] == 1 ? '金融用户' : '企服用户';
                $data[$uk]['salesman'] = $cshr_month[$item['customer_id']][$item['month']] ?? $customer_map[$item['customer_id']]['salesman'];
                $data[$uk]['operator'] = $item['operator'];
                $data[$uk]['source'] = $item['source'];
                $data[$uk]['total'] = 0;
                $data[$uk]['success'] = 0;
                $data[$uk]['valid'] = 0;
                $data[$uk]['number'] = 0;
                $data[$uk]['money'] = 0;
                $data[$uk]['money_finance'] = 0;
                $data[$uk]['cost'] = 0;
            }

            $data[$uk]['number'] += $item['number'];
            $data[$uk]['money'] = bcadd($data[$uk]['money'],$item['money'],6);
            $data[$uk]['money_finance'] = bcadd($data[$uk]['money_finance'],$item['money_finance'],6);
        }

        foreach ($cost_data as $item) {
            if($this->filterDataSource($item['customer_id'],-1,$item['month'])){continue;}
            //临时逻辑 信飞 蓝海银行 两个客户的产品 评分Y01(711)的量和钱合并到评分Y02(712)产品上
            $checkRes = $this->checkTransProduct($item['customer_id'], $item['product_id']);
            $item['product_id'] = $checkRes ? '712' : $item['product_id'];

            // $uk = implode("_",[$item['month'], $item['customer_id'], $item['father_id'], $item['product_id'], $item['operator']]);
            $uk = implode('_',[$item['month'], $item['customer_id'], $item['father_id'], $item['product_id'], $item['operator'],$item['source']]);
            if (!key_exists($uk, $data)) {
                $data[$uk]['month'] = $item['month'];
                $data[$uk]['father_id'] = $item['father_id'];
                $data[$uk]['father_name'] = $father_map[$item['product_id']]['father_name'];
                $data[$uk]['product_id'] = $item['product_id'];
                $data[$uk]['product_name'] = $father_map[$item['product_id']]['product_name'];
                $data[$uk]['customer_id'] = $item['customer_id'];
                $data[$uk]['customer_name'] = $customer_map[$item['customer_id']]['name'];
                $data[$uk]['customer_type'] = $customer_map[$item['customer_id']]['customer_type'];
                $data[$uk]['customer_type_name'] = $customer_map[$item['customer_id']]['customer_type'] == 1 ? '金融用户' : '企服用户';
                $data[$uk]['salesman'] = $cshr_month[$item['customer_id']][$item['month']] ?? $customer_map[$item['customer_id']]['salesman'];
                $data[$uk]['operator'] = $item['operator'];
                $data[$uk]['source'] = $item['source'];
                $data[$uk]['total'] = 0;
                $data[$uk]['success'] = 0;
                $data[$uk]['valid'] = 0;
                $data[$uk]['number'] = 0;
                $data[$uk]['money'] = 0;
                $data[$uk]['money_finance'] = 0;
                $data[$uk]['cost'] = 0;
            }

            $data[$uk]['cost'] = bcadd($data[$uk]['cost'],$item['cost'],6);
        }

        $ret = [];

        foreach($data as $info){
            // $info['data']['month'] = $this->get_month_area($info['area']);
            if (!$this->checkUserProductAuth($info['father_id'],'money')) {
                $info['money']  = '-';
                $info['money_finance']  = '-';
            }
            if(!$this->checkUserProductAuth($info['father_id'],'cost')){
                $info['cost'] = '-';
            }
            if (!$this->checkUserProductAuth($info['father_id'],'money_agent')) {
                $info['money_agent'] = '-';
            }
            $ret[$info['customer_id']][] = $info;
        }

        foreach($ret as &$info) {
            usort($info, function ($first, $second) {
                return $first['father_id'].' '.$first['product_id'] > $second['father_id'].' '.$second['product_id'] ? 1 : -1;
            });
        }

        $result = [];
        foreach($ret as $infos) {
            foreach($infos as $item){
                $result[] = $item;
            }
        }

        return $result;
    }

    private function get_month_area($months){
        if(count($months) == 1){
            return $months[0];
        }else{
            return $months[0]."~".$months[count($months)-1];
        }
    }


    /**
     * 暂存从redis中获取的apikey -> customer_id 优化遍历时间
     * @param $apikey
     * @return mixed
     * @throws \Exception
     */
    private function cache_get_customer_id($apikey){
        if(key_exists($apikey, $this->apikey_customer_id_map)){
            $customer_id = $this->apikey_customer_id_map[$apikey];
        }else{
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
            $this->apikey_customer_id_map[$apikey] = $customer_id;
        }
        return $customer_id;
    }

    /**
     * 暂存从redis中获取的product_id -> father_id 优化遍历时间
     * @param $product_id
     * @return mixed
     * @throws \Exception
     */
    private function cache_get_father_id($product_id){
        if(key_exists($product_id, $this->product_id_father_id_map)){
            $father_id = $this->product_id_father_id_map[$product_id];
        }else {
            $father_id  = Product::getDisplayFatherId($product_id);
            $this->product_id_father_id_map[$product_id] = $father_id;
        }
        return $father_id;
    }
}
