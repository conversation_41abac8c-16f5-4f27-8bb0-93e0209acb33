<?php
namespace App\Http\Repository;

use App\Imports\Contract\CustomerTypeImport;
use App\Models\Approval;
use App\Models\Contract;
use App\Models\ContractDownloadPermission;
use App\Models\Customer;
use App\Models\Product;
use App\Models\SystemUser;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Func;
use Exception;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Carbon\Carbon;

/**
 * 合同
 */
class ContractRepositorie extends BaseRepositorie
{
    const NEW_CONTRACT = '新合同';

    /** @var int 合同类别 客户类别 */
    const CONTRACT_CATEGORY_CUSTOMER = 1;
    /** @var int 合同类别 渠道类别 */
    const CONTRACT_CATEGORY_SOURCE   = 2;
    // /** @var int 合同类别 三方类别 */
    // const CONTRACT_CATEGORY_THRID    = 3;


    /** @var int 合同类型 收入 */
    const CONTRACT_TYPE_INCOME  = 1;
    /** @var int 合同类型 支出 */
    const CONTRACT_TYPE_EXPEND  = 2;
    /** @var int 合同类型 保密 */
    const CONTRACT_TYPE_SECRECY = 3;
    /** @var int 合同类型 其他 */
    const CONTRACT_TYPE_OTHER   = 4;
    /** @var int 合同类型 补充协议 */
    const CONTRACT_TYPE_CHANNEL_FILL   = 51;
    /** @var int 合同类型 测试协议 */
    const CONTRACT_TYPE_CHANNEL_TEST   = 52;
    /** @var int 合同类型 保密协议 */
    const CONTRACT_TYPE_CHANNEL_KEEP   = 53;
    /** @var int 合同类型 代理协议 */
    const CONTRACT_TYPE_CHANNEL_PROXY  = 54;
    /** @var int 合同类型 合作协议 */
    const CONTRACT_TYPE_CHANNEL_CONN   = 55;
    /** @var int 合同类型 三方协议 */
    const CONTRACT_TYPE_CHANNEL_THIRD  = 56;
    /** @var int 合同类型 终止协议 */
    const CONTRACT_TYPE_CHANNEL_TERM  = 57;
    /** @var int 合同类型 承诺函 */
    const CONTRACT_TYPE_CHANNEL_COMM  = 58;



    /** @var int 签约类型 新签 */
    const CONTRACT_SIGN_TYPE_NEW_SIGN  = 1;
    /** @var int 签约类型 续签 */
    const CONTRACT_SIGN_TYPE_RENEW_SIGN  = 2;


    /** @var int 渠道类型 代运营 */
    const CONTRACT_CHANNEL_TYPE_AGENCY  = 1;
    /** @var int 渠道类型 核验类 */
    const CONTRACT_CHANNEL_TYPE_CHECK    = 2;
    /** @var int 渠道类型 运营商 */
    const CONTRACT_CHANNEL_TYPE_OPERATOR = 3;
    /** @var int 渠道类型 征信类 */
    const CONTRACT_CHANNEL_TYPE_CREDIT   = 4;
    /** @var int 渠道类型 支撑类 */
    const CONTRACT_CHANNEL_TYPE_SUPPORT  = 5;
    /** @var int 渠道类型 代理类 */
    const CONTRACT_CHANNEL_TYPE_PROXY  = 6;
    /** @var int 渠道类型 其他 */
    const CONTRACT_CHANNEL_TYPE_OTHER  = 7;



    /** @var int 签约主体 北京德宝天辰 */
    const CONTRACT_SIGN_GROUP_DBTC = 1;
    /** @var int 签约主体 创新  */
    const CONTRACT_SIGN_GROUP_CX   = 2;
    /** @var int 签约主体 互通 */
    const CONTRACT_SIGN_GROUP_HT  = 3;
    /** @var int 签约主体 北京尚昕科技 */
    const CONTRACT_SIGN_GROUP_SXKJ  = 4;


    /** @var int 是否归档 未归档  */
    const CONTRACT_ARCHIVE_STATUS_NOT  = 0;
    /** @var int 是否归档 已归档 */
    const CONTRACT_ARCHIVE_STATUS_DONE  = 1;


    /** @var int 上传合同未选择类别 */
    const RESP_CONTRACT_UPLOAD_CATEGORY_EMPTY = '上传合同未选择类别';//2200;
    /** @var int 上传合同未选择excel文件 */
    const RESP_CONTRACT_UPLOAD_EXCEL_EMPTY = '上传合同未选择excel文件';//2201;
    /** @var int 上传合同未选择pdf文件 */
    const RESP_CONTRACT_UPLOAD_PDF_EMPTY = '上传合同未选择pdf文件';//2202;
    /** @var int excel文件获取失败 */
    const RESP_CONTRACT_LOAD_EXCEL_FAIL = 'excel文件获取失败';//2203;
    /** @var int 选择的类别与excel内容符,请检查 */
    const RESP_CONTRACT_CATEGORY_EXCEL_NOT_MATCH = '选择的类别与excel内容不符';//2204;
    /** @var int 合同类型错误 */
    const RESP_CONTRACT_TYPE_ERR = '合同类型错误';//2205;
    /** @var int 签约类型错误 */
    const RESP_CONTRACT_SIGN_ERR = '签约类型错误';//2206;
    /** @var int 合同编号缺失 */
    const RESP_CONTRACT_MISSING_CONTRACT_NO = '合同编号缺失';//2207;
    /** @var int 合同id缺失 */
    const RESP_CONTRACT_MISSING_CONTRACT_ID = '合同id缺失';//2207;
    /** @var int 合同文件缺失 */
    const RESP_CONTRACT_MISSING_CONTRACT_FILE = '合同文件缺失';//2208;
    /** @var int 合同文件数量大于数据行数 */
    const RESP_CONTRACT_CONTRACT_FILE_COUNT_GREATER_THAN_ROWS = '合同文件数量大于数据行数';//2209;
    /** @var int 合同文件数量小于数据行数 */
    const RESP_CONTRACT_CONTRACT_FILE_COUNT_LESS_THAN_ROWS = '合同文件数量小于数据行数';//2210;
    /** @var int 合同编号重复 */
    const RESP_CONTRACT_CONTRACT_NO_DUPLICATION = '合同编号重复';//2211;
    /** @var int 合同编号重复 */
    const RESP_CONTRACT_EMPTY = '无合同内容';//2212;

    /** @var array 合同类别 */
    private static $category_arr = [
        '客户类别' => self::CONTRACT_CATEGORY_CUSTOMER,
        '渠道类别' => self::CONTRACT_CATEGORY_SOURCE,
        // '三方类别' => self::CONTRACT_CATEGORY_THRID
    ];

    /** @var array 合同类型 */
    private static $type = [
        '收入' => self::CONTRACT_TYPE_INCOME,
        '支出' => self::CONTRACT_TYPE_EXPEND,
        '保密' => self::CONTRACT_TYPE_SECRECY,
        '其他' => self::CONTRACT_TYPE_OTHER,

        '补充协议' => self::CONTRACT_TYPE_CHANNEL_FILL,
        '测试协议' => self::CONTRACT_TYPE_CHANNEL_TEST,
        '保密协议' => self::CONTRACT_TYPE_CHANNEL_KEEP,
        '代理协议' => self::CONTRACT_TYPE_CHANNEL_PROXY,
        '合作协议' => self::CONTRACT_TYPE_CHANNEL_CONN,
        '三方协议' => self::CONTRACT_TYPE_CHANNEL_THIRD,
        '终止协议' => self::CONTRACT_TYPE_CHANNEL_TERM,
        '承诺函'   => self::CONTRACT_TYPE_CHANNEL_COMM,
    ];

    /** @var array 合同归档状态 */
    private static $archive_status = [
        '未归档' => self::CONTRACT_ARCHIVE_STATUS_NOT,
        '已归档' => self::CONTRACT_ARCHIVE_STATUS_DONE,
    ];

    /** @var array 签约类型 */
    private static $sign_type = [
        '新签' => self::CONTRACT_SIGN_TYPE_NEW_SIGN,
        '续约' => self::CONTRACT_SIGN_TYPE_RENEW_SIGN,//兼容导入时的错误文本
        '续签' => self::CONTRACT_SIGN_TYPE_RENEW_SIGN,//这个需要放在下面,在获取签约类型时会覆盖"续约"
    ];

    private static $channel_type = [
        '代运营' => self::CONTRACT_CHANNEL_TYPE_AGENCY,
        '核验类' => self::CONTRACT_CHANNEL_TYPE_CHECK,
        '运营商' => self::CONTRACT_CHANNEL_TYPE_OPERATOR,
        '征信类' => self::CONTRACT_CHANNEL_TYPE_CREDIT,
        '支撑类' => self::CONTRACT_CHANNEL_TYPE_SUPPORT,
        '代理类' => self::CONTRACT_CHANNEL_TYPE_PROXY,
        '其他'   => self::CONTRACT_CHANNEL_TYPE_OTHER,
    ];

    private static $sign_group = [
        '北京德宝天辰' => self::CONTRACT_SIGN_GROUP_DBTC,
        '创新'         => self::CONTRACT_SIGN_GROUP_CX,
        '互通'         => self::CONTRACT_SIGN_GROUP_HT,
        '北京尚昕科技' => self::CONTRACT_SIGN_GROUP_SXKJ,
    ];


    //客户类别表头
    private static $contract_category_customer_header = ['OA编号', '合同编号', '审批通过时间', '合同名', '客户名称', '合同类型', '公司名称', '合同开始日期', '合同结束日期', '签约类型', '签约产品（主产品）', '签约产品（子产品）', '商务跟进人', '备注', '归档状态', '归档份数'];
    private static $contract_category_channel_header  = ['OA编号', '合同编号', '审批通过时间', '合同名', '渠道类型', '渠道简称', '渠道全称', '签约主体', '签约年份', '合同类型', '合同开始日期', '合同结束日期', '商务跟进人', '备注', '归档状态'];

    // private static $contract_category_thrid_header    = ['合同编号','客户名称','合同类型','公司名称','第三方公司名称','合同开始日期','合同结束日期','签约类型','产品','商务跟进人','备注','归档份数'];

    /**
     * 构造函数
     */
    public function __construct(){
    }


    public static function options(){
        $res['category'] = [[
                'value' => -1,
                'label' => '全部',
            ], [
                'value' => self::CONTRACT_CATEGORY_CUSTOMER,
                'label' => '客户类别',
            ], [
                'value' => self::CONTRACT_CATEGORY_SOURCE,
                'label' => '渠道类别',
            // ], [
            //     'value' => self::CONTRACT_CATEGORY_THRID,
            //     'label' => '三方类别',
        ],];

        $res['type'] = [
            self::CONTRACT_CATEGORY_CUSTOMER => [
                [
                    'value' => - 1,
                    'label' => '全部',
                ], [
                    'value' => self::CONTRACT_TYPE_INCOME,
                    'label' => '收入',
                ], [
                    'value' => self::CONTRACT_TYPE_EXPEND,
                    'label' => '支出',
                ], [
                    'value' => self::CONTRACT_TYPE_SECRECY,
                    'label' => '保密',
                ], [
                    'value' => self::CONTRACT_TYPE_OTHER,
                    'label' => '其他',
                ],
            ],
            self::CONTRACT_CATEGORY_SOURCE   => [
                [
                    'value' => - 1,
                    'label' => '全部',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_FILL,
                    'label' => '补充协议',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_TEST,
                    'label' => '测试协议',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_KEEP,
                    'label' => '保密协议',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_PROXY,
                    'label' => '代理协议',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_CONN,
                    'label' => '合作协议',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_THIRD,
                    'label' => '三方协议',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_TERM,
                    'label' => '终止协议',
                ], [
                    'value' => self::CONTRACT_TYPE_CHANNEL_COMM,
                    'label' => '承诺函',
                ],
            ],
        ];


        $res['sign'] = [[
                'value' => -1,
                'label' => '全部',
            ], [
                'value' => self::CONTRACT_SIGN_TYPE_NEW_SIGN,
                'label' => '新签',
            ], [
                'value' => self::CONTRACT_SIGN_TYPE_RENEW_SIGN,
                'label' => '续签',
        ],];

        $res['channel_type'] = [[
                'value' => self::CONTRACT_CHANNEL_TYPE_AGENCY,
                'label' => '代运营',
            ], [
                'value' => self::CONTRACT_CHANNEL_TYPE_CHECK,
                'label' => '核验类',
            ], [
                'value' => self::CONTRACT_CHANNEL_TYPE_OPERATOR,
                'label' => '运营商',
            ], [
                'value' => self::CONTRACT_CHANNEL_TYPE_CREDIT,
                'label' => '征信类',
            ], [
                'value' => self::CONTRACT_CHANNEL_TYPE_SUPPORT,
                'label' => '支撑类',
            ], [
                'value' => self::CONTRACT_CHANNEL_TYPE_PROXY,
                'label' => '代理营',
            ], [
                'value' => self::CONTRACT_CHANNEL_TYPE_OTHER,
                'label' => '其他',
        ],];

        $res['sign_group'] = [[
                'value' => -1,
                'label' => '全部',
            ], [
                'value' => self::CONTRACT_SIGN_GROUP_DBTC,
                'label' => '北京德宝天辰',
            ], [
                'value' => self::CONTRACT_SIGN_GROUP_CX,
                'label' => '创新',
            ], [
                'value' => self::CONTRACT_SIGN_GROUP_HT,
                'label' => '互通',
            ], [
                'value' => self::CONTRACT_SIGN_GROUP_SXKJ,
                'label' => '北京尚昕科技',
        ],];

        $res['archive_status'] = [[
                'value' => - 1,
                'label' => '全部',
            ], [
                'value' => self::CONTRACT_ARCHIVE_STATUS_NOT,
                'label' => '未归档',
            ], [
                'value' => self::CONTRACT_ARCHIVE_STATUS_DONE,
                'label' => '已归档',
        ],];

        $res['channel_name'] = [];
        $channel_name_list = Contract::getAllChannelNames();
        foreach($channel_name_list as $info) {
            $res['channel_name'][] = [
                'value' => $info['channel_name'],
                'label' => $info['channel_name'],
            ];
        }
        // 渠道合同
        $channel_category_list = Contract::getCategoryContracts();
        $channel_full_name_hash = [];
        $channel_salesman_name_hash = [];
        foreach($channel_category_list as $info) {
            if (!isset($channel_full_name_hash[$info['channel_full_name']])) {
                $channel_full_name_hash[$info['channel_full_name']] = $info['channel_full_name'];
                $res['channel_full_name'][] = [
                    'value' => $info['channel_full_name'],
                    'label' => $info['channel_full_name'],
                ];
            }

            if (!empty($info['salesman_name']) && !isset($channel_salesman_name_hash[$info['salesman_name']])) {
                $channel_salesman_name_hash[$info['salesman_name']] = $info['salesman_name'];
                $res['channel_salesman'][] = [
                    'value' => $info['salesman_name'],
                    'label' => $info['salesman_name'],
                ];
            }
        }
        $res['main_product'] = Product::getMainProduct();
        return $res;
    }


    /**
     * 列表条件
     *
     * @param $params
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-03-04 14:19:33
     */
    private static function get_list_where($params){
        $where = [];

        $contract_name        = $params['contract_name'];
        $serial_number        = $params['serial_number'];
        $contract_no          = $params['contract_no'];
        $contract_category    = $params['contract_category'];
        $contract_type        = $params['contract_type'];
        $contract_start_start = $params['contract_start_start'];
        $contract_start_end   = $params['contract_start_end'];
        $contract_end_start   = $params['contract_end_start'];
        $contract_end_end     = $params['contract_end_end'];
        $created_at_start     = $params['created_at_start'];
        $created_at_end       = $params['created_at_end'];
        $company_name         = $params['company_name'];
        $salesman_name        = $params['salesman_name'];
        $salesman_arr         = $params['salesman_arr'];
        $channel_type         = $params['channel_type'];
        $channel_name         = $params['channel_name'];
        $channel_full_name    = $params['channel_full_name'];
        $archive_status       = $params['archive_status'];
        $sign_year            = $params['sign_year'];

        $customer_name        = $params['customer_name'];


        if(!empty($contract_start_start) && !empty($contract_start_end) && $contract_start_start > $contract_start_end){
            throw new Exception("合同开始时间错误!!");
        }
        if(!empty($contract_end_start) && !empty($contract_end_end) && $contract_end_start > $contract_end_end){
            throw new Exception("合同结束时间错误!!");
        }
        if(!empty($contract_start_end) && !empty($contract_end_start) && $contract_start_end > $contract_end_start){
            throw new Exception("合同结束时间不可小于合同开始时间错误!!");
        }


        if(!empty($serial_number)){
            $where[] = ['serial_number', 'like', '%'.$serial_number.'%'];
        }
        if(!empty($contract_no)){
            $where[] = ['contract_no', 'like', '%'.$contract_no.'%'];
        }
        if(!empty($contract_name)){
            $where[] = ['contract_name', 'like', '%'.$contract_name.'%'];
        }
        if(!empty($contract_category)){
            $where[] = ['contract_category', '=', $contract_category];
        }
        if(!empty($contract_type) && $contract_type != -1){
            $where[] = ['contract_type', '=', $contract_type];
        }
        if(!empty($contract_start_start)){
            $contract_start_start = date('Y-m-d H:i:s', $contract_start_start/1000);
            $where[] = ['contract_start', '>=', $contract_start_start];
        }
        if(!empty($contract_start_end)){
            $contract_start_end = date('Y-m-t 23:59:59', $contract_start_end/1000);
            $where[] = ['contract_start', '<=', $contract_start_end];
        }
        if(!empty($contract_end_start)){
            $contract_end_start = date('Y-m-d H:i:s', $contract_end_start/1000);
            $where[] = ['contract_end', '>=', $contract_end_start];
        }
        if(!empty($contract_end_end)){
            $contract_end_end = date('Y-m-t 23:59:59', $contract_end_end/1000);
            $where[] = ['contract_end', '<=', $contract_end_end];
        }

        if(!empty($contract_end)){
            $where[] = ['contract_end', '<', $contract_end];
        }
        if(!empty($created_at_start)){
            $where[] = ['created_at', '>=', $created_at_start];
        }
        if(!empty($created_at_end)){
            $where[] = ['created_at', '<', $created_at_end];
        }

        if(!empty($company_name)){
            $where[] = ['company_name', '=', $company_name];
        }

        if(!empty($channel_type)){
            $where[] = ['channel_type', '=', $channel_type];
        }
        if(!empty($channel_name)){
            $where[] = ['channel_name', '=', $channel_name];
        }
        if(!empty($channel_full_name)){
            $where[] = ['channel_full_name', '=', $channel_full_name];
        }
        if(!empty($sign_year)){
            $where[] = ['sign_year', '=', $sign_year];
        }
        if($archive_status != -1 && $archive_status !== ''){
            $where[] = ['archive_status', '=', $archive_status];
        }
        // if(!empty($salesman_name)){
        //     $where[] = ['salesman_name', '=', $salesman_name];
        // }
        $customer_name_arr = [];
        if(!empty($customer_name)){
            $customer_name_arr = Customer::getListByName($customer_name);
            $customer_name_arr = array_column($customer_name_arr, 'customer_id');
        }

        // $where[] = ['delated_at', 'is null', ''];

        return [$where,$salesman_arr,$salesman_name,$customer_name_arr,$customer_name];
    }


    /**
     * 获取列表
     *
     * @param $params
     * @param $username
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2024-02-21 19:15:40
     */
    public static function get_list($params,$username) {
        $page              = $params['page'];
        $page_size         = $params['page_size'];

        [$where,$salesman_arr,$salesman_name,$customer_id_arr,$customer_name] = self::get_list_where($params);

        $order_by = ['id','desc'];
        if($params['contract_category'] == self::CONTRACT_CATEGORY_SOURCE){
            $order_by = ['contract_start','desc'];
        }
        $father_id = $params['father_id'];
        $_list = Contract::getListByCondition($where,$father_id,$salesman_arr,$salesman_name,$customer_id_arr,$customer_name,'*',$page,$page_size,$order_by);

        if(empty($_list)){
            return $_list;
        }

        $category_map = array_flip(self::$category_arr);
        $type_map = array_flip(self::$type);
        $channel_type_map = array_flip(self::$channel_type);
        $sign_group_map = array_flip(self::$sign_group);
        $sign_map = array_flip(self::$sign_type);
        $archive_status_map = array_flip(self::$archive_status);

        $all_salesman = SystemUser::getAllUsers();
        // $all_salesman = SystemUser::getUserInfoByNames($all_salesman);
        $all_salesman = array_column($all_salesman,'realname','username');

        $product_ids  = [];
        $contract_nos = [];
        $father_ids   = [];
        foreach($_list as &$_info){
            $_product_ids = explode(",",$_info['product_id']);
            $_info['product_ids'] = $_product_ids;
            foreach($_product_ids as $_pid){
                $product_ids[$_pid] = $_pid;
            }

            $_father_ids = explode(",",$_info['father_id']);
            $_info['product_ids'] = $_product_ids;
            foreach($_father_ids as $_fid){
                $father_ids[$_fid] = $_fid;
            }
            // unset($_info['file_url']);
            $contract_nos[] = $_info['contract_no'];
        }
        $product_ids = array_values($product_ids);

        $_product_infos = Product::getProductListByProductIds($product_ids);

        $product_infos = [];
        $product_name_map = [];
        foreach ($_product_infos as $product_info) {
            $father_id = $product_info['father_id'] == 0 ? $product_info['product_id'] : $product_info['father_id'];
            $father_ids[$product_info['product_id']] = $father_id;
            $product_infos[$product_info['product_id']] = $product_info['product_name'];
            $product_name_map[$product_info['product_name']] = $product_info['product_id'];
        }

        $_father_infos = Product::getProductListByProductIds(array_values($father_ids));
        $father_infos = [];
        $father_name_map = [];
        foreach($_father_infos as $info){
            $father_infos[$info['product_id']]      = $info['product_name'];
            $father_name_map[$info['product_name']] = $info['product_id'];
        }

        //获取子产品
        //1 获取product_id
        //2 获取product_name
        //3 去重
        //4 查询下载权限

        $download_permission_list = ContractDownloadPermission::get_download_permission_list($username,$contract_nos);
        $dpc_nos = [];
        // $dpc_nos = array_column($download_permission_list, null, 'contract_no');//用户可以下载的合同
        foreach($download_permission_list as $dp_info){
            if(key_exists($dp_info['contract_no'], $dpc_nos)){
                if($dp_info['status'] == ContractDownloadPermission::STATUS_DEAL) {
                    $dpc_nos[$dp_info['contract_no']] = $dp_info;
                }else if($dp_info['status'] == ContractDownloadPermission::STATUS_APPLY){
                    $dpc_nos[$dp_info['contract_no']] = $dp_info;
                }else if($dpc_nos[$dp_info['contract_no']]['status'] < $dp_info['status'] ){
                    $dpc_nos[$dp_info['contract_no']] = $dp_info;
                }
            }else {
                $dpc_nos[$dp_info['contract_no']] = $dp_info;
            }
        }

        $list = [];
        foreach($_list as $info){
            $main_product_name = [];
            $product_name = [];
            $product_name_arr = [];
            foreach ($info['product_ids'] as $info_product_id) {
                if(empty($info_product_id)) {
                    continue;
                }
                $info_father_id      = $father_ids[$info_product_id];
                $product_name[]      = $product_infos[$info_product_id];
                $main_product_name[] = $father_infos[$info_father_id];
            }
            $father_name_arr   = !empty($info['father_name']) ? explode("\n",$info['father_name']) : [];
            $main_product_name = array_merge($main_product_name,$father_name_arr);

            $product_name_arr  = !empty($info['product_name']) ? explode("\n",$info['product_name']) : [];
            $product_name      = array_merge($product_name,$product_name_arr);

            $info['contract_category'] = $category_map[$info['contract_category']];
            $info['contract_start']    = !empty($info['contract_start'])?substr($info['contract_start'],0,10):'';
            $info['contract_end']      = !empty($info['contract_end'])?substr($info['contract_end'],0,10):'';
            $info['created_at']        = substr($info['created_at'],0,10);
            // $info['salesman']          = isset($all_salesman[$info['salesman']])?$all_salesman[$info['salesman']]:'';
            // $info['salesman_name']     = isset($all_salesman[$info['salesman']])?$all_salesman[$info['salesman']]:'';
            $info['salesman_name']     = empty($info['salesman']) ? $info['salesman_name'] : (isset($all_salesman[$info['salesman']])?$all_salesman[$info['salesman']]:'');


            $_product_ids = empty($info['product_id']) ? [] : explode(",",trim($info['product_id'],','));
            $_father_id  = empty($info['father_id'])  ? [] : explode(",",trim($info['father_id'],','));

            //添加子产品,主产品 id对应的名称
            foreach($_product_ids as $_pid){
                $product_name[] = $product_infos[$_pid];
            }
            foreach($_father_id as $_fid){
                if(isset($father_infos[$_fid])) {
                    $main_product_name[] = $father_infos[$_fid];
                }
            }

            $info['main_product_name'] = array_unique($main_product_name);
            $info['product_name']      = array_unique($product_name);

            $info['contract_start_timestamp'] = strtotime($info['contract_start']);
            $info['contract_start_timestamp'] = $info['contract_start_timestamp'] !== false ? $info['contract_start_timestamp'] * 1000 : 0;
            $info['contract_end_timestamp']   = strtotime($info['contract_end']);
            $info['contract_end_timestamp']   = $info['contract_end_timestamp'] !== false ? $info['contract_end_timestamp'] * 1000 : 0;
            $info['sign_year_timestamp']   = strtotime($info['sign_year'].'0101');
            $info['sign_year_timestamp']   = $info['sign_year_timestamp'] !== false ? $info['sign_year_timestamp'] * 1000 : 0;

            $info['contract_type']      = empty($info['contract_type']) ? '' : $info['contract_type'];
            $info['contract_type_text'] = $type_map[$info['contract_type']] ?? '';
            $info['sign_type_text']     = $sign_map[$info['sign_type']]??'';
            $info['sign_group_text']    = $sign_group_map[$info['sign_group']]??'';
            $info['channel_type_text']  = $channel_type_map[$info['channel_type']] ?? '';
            $info['archive_status_text']= $archive_status_map[$info['archive_status']] ?? '';
            $info['father_name_arr']    = $main_product_name;
            $info['product_name_arr']   = $product_name_arr;

            //拆分product_id,father_id
            $info['product_id'] = $_product_ids;
            $info['father_id']  = $_father_id;

            $_father_ids = [];
            foreach($_product_ids as $_pid){
                $_father_ids[] = "" . $father_ids[$_pid];//前端需要一个字符串的key
            }
            $info['father_id'] = array_unique(array_merge($info['father_id'],$_father_ids));

            //删除重复名称 在循环中使用引用删除数组元素,可能会在转为json时出现错误,将引用元素转为空数组
            $_father_name_arr = [];
            foreach ($father_name_arr as $father_name) {
                if (!empty($father_name)) {
                    if (key_exists($father_name, $father_name_map)) {
                        $f_id = $father_name_map[$father_name];
                        if (!in_array($f_id, $info['father_id'])) {
                            $_father_name_arr[] = $father_name;
                        }
                    } else {
                        $_father_name_arr[] = $father_name;
                    }
                }
            }
            $father_name_arr = $_father_name_arr;

            $_product_name_arr = [];
            foreach ($product_name_arr as $product_name) {
                if (!empty($product_name)) {
                    if (key_exists($product_name, $product_name_map)) {
                        $p_id = $product_name_map[$product_name];
                        if (!in_array($p_id, $info['product_id'])) {
                            $_product_name_arr[] = $product_name;
                        }
                    } else {
                        $_product_name_arr[] = $product_name;
                    }
                }
            }
            $product_name_arr = $_product_name_arr;

            $info['father_id_arr']  = array_merge($info['father_id'],$father_name_arr);
            $info['product_id_arr'] = array_merge($info['product_id'],$product_name_arr);
            $info['permission_status'] = key_exists($info['contract_no'], $dpc_nos) ? $dpc_nos[$info['contract_no']]['status'] : 0;
            $info['reject_comments']   = key_exists($info['contract_no'], $dpc_nos) ? $dpc_nos[$info['contract_no']]['reject_comments'] : "";
            $info['approval_time'] = substr($info['approval_time'],0,10);

            $list[] = $info;
        }

        return $list;
    }


    /**
     *
     *
     * @param $params
     *
     * @static
     * @return int
     * @throws Exception
     * <AUTHOR> 2024-03-04 14:19:54
     */
    public static function get_count($params) {
        [$where,$salesman_arr,$salesman_name,$customer_id_arr,$customer_name] = self::get_list_where($params);
        $father_id = $params['father_id'];
        return Contract::getCountByCondition($where,$father_id,$salesman_arr,$salesman_name,$customer_id_arr,$customer_name);
    }


    /**
     * 校验文件
     * 是否存在excel
     * excel中数据是否与上传的pdf数量相同,名称是否匹配
     * 合同编号与excel中相关字段是否匹配
     *
     * @param $contract_category
     * @param $excel
     * @param $pdf
     * @param $admin
     *
     * @return true
     * @throws Exception
     * @static
     * <AUTHOR> 2024-02-29 18:52:53
     */
    public static function archive($contract_category,$excel,$pdf,$admin){
        if(empty($contract_category)){
            throw new Exception(self::RESP_CONTRACT_UPLOAD_CATEGORY_EMPTY,-100);
        }
        if(empty($excel)){
            throw new Exception(self::RESP_CONTRACT_UPLOAD_EXCEL_EMPTY,-100);
        }
        if(empty($pdf)){
            throw new Exception(self::RESP_CONTRACT_UPLOAD_PDF_EMPTY,-100);
        }

        $pdf_name_map = [];
        foreach($pdf as $p_file){
            $p_file_name = explode('_',$p_file);
            $p_file_name = substr($p_file_name[3], 0, -4);
            $pdf_name_map[$p_file_name] = $p_file;
        }

        //获取excel中行数是
        //下载excel文件
        //解析excel
        Log::info('excel文件名称：' . $excel);
        $excel_url = config('contract.finance_contract_domain').$excel;
        Log::info('excel url：' . $excel_url);

        // 初始化 cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL,$excel_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception(self::RESP_CONTRACT_LOAD_EXCEL_FAIL,-100);
        }
        curl_close($ch);

        // 将响应保存到文件
        $excel_path = '/tmp/' . $excel;
        Log::info('excel文件路径：' . $excel_path);

        file_put_contents($excel_path, $response);

        Log::info('文件是否存在：' . (file_exists($excel_path)? 'true' : 'false'));


        Log::info('文件权限：' . decoct(fileperms($excel_path)));

        $excel_data = Excel::toArray(new CustomerTypeImport(), $excel_path);
        $excel_data = $excel_data[0];//第一个sheet
        Log::info('excel数据：' . json_encode($excel_data,JSON_UNESCAPED_UNICODE));

        foreach($excel_data as $idx => &$row){
            $is_line_empty = self::is_line_empty($row);

            if($is_line_empty) {
                unset($excel_data[$idx]);
            }
        }
        $excel_data = array_merge($excel_data);

        unlink($excel_path);

        //行数
        $excel_rows = count($excel_data);
        if($excel_rows - 1 > count($pdf_name_map)){
            throw new Exception(self::RESP_CONTRACT_CONTRACT_FILE_COUNT_GREATER_THAN_ROWS,-100);
        }
        if($excel_rows - 1 < count($pdf_name_map)){
            throw new Exception(self::RESP_CONTRACT_CONTRACT_FILE_COUNT_LESS_THAN_ROWS,-100);
        }
        // 读取单元格数据

        $excel_headers = [
            //客户类别表头
            self::CONTRACT_CATEGORY_CUSTOMER => self::$contract_category_customer_header,
            //渠道类别
            self::CONTRACT_CATEGORY_SOURCE   => self::$contract_category_channel_header,
            //三方类别
            // self::CONTRACT_CATEGORY_THRID    => self::$contract_category_thrid_header,
        ];
        //判断传入类别与定义的表头是否匹配
        if(count($excel_headers[$contract_category]) != count($excel_data[0])){
            throw new Exception(self::RESP_CONTRACT_CATEGORY_EXCEL_NOT_MATCH,-100);
        }
        //校验表头单元格
        foreach($excel_data[0] as $idx => $cell_value){
            $header_value = $excel_headers[$contract_category][$idx];
            if($cell_value != $header_value){
                throw new Exception('请检查: ' . $idx . ':1,应为: ' . $header_value,self::RESP_CONTRACT_CATEGORY_EXCEL_NOT_MATCH);
            }
        }

        //根据类型判断excel格式是否正确
        $update_data = [];
        $insert_data = [];
        for ($r = 1; $r < $excel_rows; $r ++) {
            $row_info = [];
            $col_range = $excel_data[$r];
            foreach ($col_range as $idx => $cell_value) {
                $head            = $excel_headers[$contract_category][$idx];
                $row_info[$head] = $cell_value;
            }
            $update_item = self::check_row_and_return_data($contract_category, $row_info, $r + 1, $pdf_name_map, $admin);
            $serial_number = $update_item['serial_number'];
            unset($update_item['serial_number']);

            if($serial_number == self::NEW_CONTRACT){
                Log::info('新增合同：' . json_encode($update_item,JSON_UNESCAPED_UNICODE));
                $insert_data[] = $update_item;
            }else {
                $update_item = array_filter($update_item);
                Log::info('更新合同：' . json_encode($update_item,JSON_UNESCAPED_UNICODE));
                $update_data[$serial_number] = $update_item;
            }
        }

        //校验数据库中是否存在
        $contract_nos = array_column($update_data,'contract_no');
        $has_contract_no = Contract::hasContract($contract_nos);
        if($has_contract_no !== false){
            throw new Exception(self::RESP_CONTRACT_CONTRACT_NO_DUPLICATION.$has_contract_no);
        }
        // Contract::insert($insert_data);
        if (!empty($insert_data)) {
            Contract::insert($insert_data);
        }

        if (!empty($update_data)) {
            foreach ($update_data as $serial_number => $update_item) {
                Contract::where([['serial_number', '=', $serial_number]])->update($update_item);
            }
        }

        return true;
    }


    private static function is_line_empty($row){
        foreach($row as $cell){
            if($cell !== null){
                return false;
            }
        }
        return true;
    }


    /**
     * 校验数据并返回行数据
     *
     * @param $contract_category
     * @param $row_info
     * @param $row
     * @param $pdf_name_map
     * @param $admin
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2024-03-01 15:23:35
     */
    private static function check_row_and_return_data($contract_category,$row_info,$row,$pdf_name_map,$admin){

        if(empty($row_info['OA编号'])){
            throw new Exception('数据异常: 第' . $row . '行, OA编号 缺失,如添加新合同请填写"新合同"在OA编号列',100);
        }
        if(empty($row_info['合同编号'])){
            throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同编号 缺失',100);
        }
        if(empty($row_info['合同名'])){
            throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同名 缺失',100);
        }
        if(!in_array($row_info['合同类型'],array_keys(self::$type))){
            throw new Exception('合同类型错误: ' . $row,100);
        }

        // if(!in_array($row_info['签约类型'],array_keys(self::$sign_type),true)){
        //     throw new Exception("签约类型错误: ".$row." ".$row_info['签约类型'],100);
        // }
        if(!isset($pdf_name_map[$row_info['合同编号']])){
            throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同文件 缺失',100);
        }

        $url = $pdf_name_map[$row_info['合同编号']];

        $now = date('Y-m-d H:i:s');

        $contract_start_time = null;
        $contract_end_time = null;
        // if(!empty($row_info['合同开始日期'])) {
        //     try {
        //         $contract_start_time = Carbon::instance(Date::excelToDateTimeObject($row_info['合同开始日期']))->timestamp;
        //         $contract_start_time = date('Y-m-d 00:00:00', $contract_start_time);
        //     }catch (Exception $e){
        //         throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同开始日期 错误',100);
        //     }
        // }
        //
        // if(!empty($row_info['合同结束日期'])) {
        //     try {
        //         $contract_end_time = Carbon::instance(Date::excelToDateTimeObject($row_info['合同结束日期']))->timestamp;
        //         $contract_end_time = date('Y-m-d 23:59:59', $contract_end_time);
        //     }catch (Exception $e){
        //         throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同结束日期 错误',100);
        //     }
        // }


        if(empty($row_info['合同开始日期'])){
            throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同开始日期 为空',100);
        }
        if(empty($row_info['合同结束日期'])){
            throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同结束日期 为空',100);
        }

        $regex = '/^\d{4}-\d{2}-\d{2}$/';

        if (!preg_match($regex, trim($row_info['合同开始日期']))) {
            throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同开始日期 格式错误',100);
        }
        if (!preg_match($regex, trim($row_info['合同开始日期']))) {
            throw new Exception('数据异常: 第' . $row . '行( '.$row_info['OA编号'].' ), 合同结束日期 格式错误',100);
        }

        $contract_start_time = $row_info['合同开始日期'];
        $contract_end_time   = $row_info['合同结束日期'];

        if ($row_info['OA编号'] != self::NEW_CONTRACT) {
            $contract_info = Contract::where([['serial_number', '=', $row_info['OA编号']],])->whereNull('delated_at')->first();
            if (empty($contract_info)) {
                throw new Exception('数据异常: 第' . $row . '行( ' . $row_info['OA编号'] . ' ), 合同审批不存在', 100);
            }

            if ($contract_info['archive_status'] == self::CONTRACT_ARCHIVE_STATUS_DONE) {
                throw new Exception('数据异常: 第' . $row . '行( ' . $row_info['OA编号'] . ' ), 合同已归档', 100);
            }
        // } else {
        //     // 新合同不添加OA编号, OA编号可在编辑时添加
        //     $row_info['OA编号'] = '';
        }

        $customer_id = '';
        $customer_name = '';
        $company_name = '';
        $channel_name = '';
        $channel_full_name = '';
        $third_party_company_name = '';
        $father_id = '';
        $father_name = '';
        $product_name = '';
        $product_id = '';
        $third_product_name = '';
        $channel_product_name = '';
        $salesman = '';
        $channel_type = '';
        $sign_group = '';
        $sign_year = '';
        if($contract_category == self::CONTRACT_CATEGORY_CUSTOMER){
            $customer_name = empty($row_info['客户名称'])? '' : $row_info['客户名称'];
            //根据客户名称查询客户id
            $customer_info = Customer::getCustomerInfoByName($customer_name);
            if(!empty($customer_info)){
                $customer_id = $customer_info['customer_id'];
                $salesman = $customer_info['salesman'];
            }
            $company_name = $row_info['公司名称'];
            $product_name = empty($row_info['签约产品（子产品）'])? '' : $row_info['签约产品（子产品）'];
            $_product_name = explode("\n",$row_info['签约产品（子产品）']);

            $product_list = Product::getListByProductNames($_product_name);
            $product_ids = array_column($product_list, 'product_id');
            $product_id  = implode(',',$product_ids);

            $father_name = empty($row_info['签约产品（主产品）'])? '' : $row_info['签约产品（主产品）'];
            $_father_name = explode("\n",$row_info['签约产品（主产品）']);

            $father_list = Product::getListByProductNames($_father_name);
            $father_ids = array_column($father_list, 'product_id');
            $father_id  = implode(',',$father_ids);
        }

        if($contract_category == self::CONTRACT_CATEGORY_SOURCE){
            $channel_name         = $row_info['渠道简称'];
            $channel_full_name    = $row_info['渠道全称'];
            // $channel_product_name = $row_info['签约渠道产品'];
            $channel_type         = self::$channel_type[$row_info['渠道类型']];
            $sign_group           = self::$sign_group[$row_info['签约主体']];
            $sign_year            = $row_info['签约年份'];
            $salesman             = '';
            $salesman_name        = $row_info['商务跟进人'];
            //根据商务名字(汉字)查询商务名字(拼音)
            if(!empty($salesman_name)) {
                $salesman_info = SystemUser::getUserInfoByRealname($salesman_name);
                $salesman      = $salesman_info['username']??'';
            }
        }

        // if($contract_category == self::CONTRACT_CATEGORY_THRID){
        //     $customer_name = empty($row_info['客户名称'])?"":$row_info['客户名称'];
        //     //根据客户名称查询客户id
        //     $customer_info = Customer::getCustomerInfoByName($customer_name);
        //     if(!empty($customer_info)){
        //         $customer_id = $customer_info['customer_id'];
        //         $salesman = $customer_info['salesman'];
        //     }
        //     $company_name = empty($row_info['公司名称'])?"":$row_info['公司名称'];
        //     $third_party_company_name = empty($row_info['第三方公司名称'])?"":$row_info['第三方公司名称'];
        //     $third_product_name = empty($row_info['产品'])?"":$row_info['产品'];
        // }

        // 审批通过时间 合同名 归档状态

        return [
            'serial_number'            => $row_info['OA编号'],
            'contract_no'              => $row_info['合同编号'],
            'contract_name'            => $row_info['合同名'],
            'contract_end'             => $contract_end_time,
            'contract_start'           => $contract_start_time,
            'contract_category'        => $contract_category,
            'contract_type'            => self::$type[$row_info['合同类型']],
            'sign_type'                => isset($row_info['签约类型'])?self::$sign_type[$row_info['签约类型']]:'',
            'customer_id'              => $customer_id,
            'customer_name'            => $customer_name,
            'company_name'             => $company_name,
            'channel_name'             => $channel_name,
            'channel_full_name'        => $channel_full_name,
            'third_party_company_name' => $third_party_company_name,
            'father_id'                => $father_id,
            'father_name'              => $father_name,
            'product_id'               => $product_id,
            'product_name'             => $product_name,
            'third_product_name'       => $third_product_name,
            // 'channel_product_name'     => $channel_product_name,
            'salesman'                 => $salesman,
            'salesman_name'            => $row_info['商务跟进人'],
            'archive_num'              => $row_info['归档份数'] ?? -1,
            'archive_status'           => self::CONTRACT_ARCHIVE_STATUS_DONE,
            'file_url'                 => $url,
            'remark'                   => empty($row_info['备注']) ? '' : $row_info['备注'],
            'admin'                    => $admin,
            'channel_type'             => $channel_type,
            'sign_group'               => $sign_group,
            'sign_year'                => $sign_year,
            'archive_time'             => date('Y-m-d H:i:s'),//归档时间
            // 'delated_at'               => '',
            'created_at'               => $now,
            'updated_at'               => $now,
        ];
    }


    /**
     * 编辑
     *
     * @param $contract_no
     * @param $contract_info
     * @param $admin
     *
     * @return true
     * @throws Exception
     * @static
     * <AUTHOR> 2024-02-29 18:52:53
     */
    public static function edit($id,$contract_info,$admin){
        if(empty($id)){
            throw new Exception(self::RESP_CONTRACT_EMPTY,-100);
        }

        $update_data = self::check_info($id, $contract_info, $admin);

        Contract::editById($id,$update_data);
        return true;
    }


    /**
     * 校验数据并返回行数据
     *
     * @param $contract_no
     * @param $contract_info
     * @param $admin
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2024-03-01 15:23:35
     */
    private static function check_info($id, $contract_info, $admin){
        if(!in_array($contract_info['contract_type'],self::$type)){
            throw new Exception('合同类型错误',100);
        }

        // if(!in_array($contract_info['sign_type'],self::$sign_type,true)){
        //     throw new Exception("签约类型错误",100);
        // }

        if(empty($contract_info['salesman'])){
            throw new Exception('请选择商务',100);
        }


        $now = date('Y-m-d H:i:s');

        $contract_start_time = null;
        $contract_end_time = null;
        if(!empty($contract_info['contract_start_timestamp'])) {
            $contract_start_time = date('Y-m-d 00:00:00', $contract_info['contract_start_timestamp'] / 1000);
        }
        if(!empty($contract_info['contract_end_timestamp'])) {
            $contract_end_time = date('Y-m-d 23:59:59', $contract_info['contract_end_timestamp'] / 1000);
        }

        $contract_category = self::$category_arr[$contract_info['contract_category']];

        $customer_id = '';
        $customer_name = '';
        $company_name = '';
        $channel_name = '';
        $channel_full_name = '';
        $third_party_company_name = '';
        $product_name = '';
        $product_id = '';
        $third_product_name = '';
        $channel_product_name = '';
        $channel_type         = '';
        $sign_group           = '';
        $sign_year            = '';
        $salesman = $contract_info['salesman'];
        if($contract_category == self::CONTRACT_CATEGORY_CUSTOMER){
            $customer_id = $contract_info['customer_id'];
            $customer_info = Customer::getCustomerInfo($customer_id);
            $customer_name = $customer_info['name'];
            $company_name = $contract_info['company_name'];
        }

        if($contract_category == self::CONTRACT_CATEGORY_SOURCE){
            $channel_name         = $contract_info['channel_name'];
            $channel_full_name    = $contract_info['channel_full_name'];
            $channel_product_name = $contract_info['channel_product_name'];
            $channel_type         = $contract_info['channel_type'];
            $sign_group           = $contract_info['sign_group'];
            $sign_year            = $contract_info['sign_year'];
        }

        // if($contract_category == self::CONTRACT_CATEGORY_THRID){
        //     $customer_id = $contract_info['customer_id'];
        //     $customer_info = Customer::getCustomerInfo($customer_id);
        //     $customer_name = $customer_info['name'];
        //
        //     $third_party_company_name = $contract_info['third_party_company_name'];
        //     $company_name = $contract_info['company_name'];
        //     $third_product_name = $contract_info['third_product_name'];
        // }

        $current_contract_info = Contract::getInfoById($id);

        $product_ids = [];
        foreach($contract_info['product_id_arr'] as $pid){
            if(intval($pid)){
                $product_ids[] = $pid;
            }
        }
        $father_product_map = Product::getProductFatherMap($product_ids);
        //处理产品id
        // father_id_arr
        // product_id_arr
        $product_id   = [];
        $product_name = [];
        $father_id    = [];
        $father_name  = [];

        if(!empty($contract_info['product_id_arr'])) {
            foreach ($contract_info['product_id_arr'] as $_product_id) {
                if(intval($_product_id)){
                    $product_id[] = $_product_id;
                    $contract_info['father_id_arr'][] = $father_product_map[$_product_id];
                }else{
                    $product_name[] = $_product_id;
                }
            }
        }

        $contract_info['father_id_arr'] = array_unique($contract_info['father_id_arr']);
        if(!empty($contract_info['father_id_arr'])) {
            foreach ($contract_info['father_id_arr'] as $_father_id) {
                if(intval($_father_id)){
                    $father_id[] = $_father_id;
                }else{
                    $father_name[] = $_father_id;
                }
            }
        }


        $serial_number = $contract_info['serial_number'];

        $product_id   = implode(',',$product_id);
        $product_name = implode(PHP_EOL,$product_name);
        $father_id    = implode(',',$father_id);
        $father_name  = implode(PHP_EOL,$father_name);

        $update_info = [
            'contract_end'             => $contract_end_time,
            'contract_start'           => $contract_start_time,
            'contract_category'        => $contract_category,
            'contract_type'            => $contract_info['contract_type'],
            'sign_type'                => $contract_info['sign_type'],
            'customer_id'              => $customer_id,
            'customer_name'            => $customer_name,
            'company_name'             => $company_name,
            'channel_name'             => $channel_name,
            'channel_full_name'        => $channel_full_name,
            'third_party_company_name' => $third_party_company_name,
            'father_id'                => $father_id,//父产品id
            'father_name'              => $father_name,//不存在的父产品名称
            'product_id'               => $product_id,//子产品id
            'product_name'             => $product_name,//不存在的子产品名称
            'third_product_name'       => $third_product_name,
            'channel_product_name'     => $channel_product_name,
            'salesman'                 => $salesman,
            'archive_num'              => $contract_info['archive_num'],
            'remark'                   => $contract_info['remark'],
            'channel_type'             => $channel_type,
            'sign_group'               => $sign_group,
            'sign_year'                => $sign_year,
        ];
        if(empty($current_contract_info['serial_number']) && !empty($serial_number)){
            $update_info['serial_number'] =$serial_number;
        }

        foreach($update_info as $key => &$info){
            if($current_contract_info[$key] === $info){
                unset($update_info[$key]);
            }
        }

        if(empty($update_info)){
            throw new Exception('没有修改',100);
        }

        $update_info['updated_at'] = $now;
        $update_info['admin']      = $admin;
        return $update_info;
    }


    /**
     * 删除
     *
     * @throws Exception
     * <AUTHOR> 2024-02-22 13:57:08
     *
     * @static
     */
    public static function del($contract_id,$user_cookie){
        if(empty($contract_id)){
            throw new Exception('',self::RESP_CONTRACT_MISSING_CONTRACT_ID);
        }

        $contract_info = Contract::getInfoById($contract_id);
        self::delContractToApproval($contract_info,$user_cookie);
    }

    /**
     * @param $data
     * @throws Exception
     */
    public static function realDel($data){
        if(empty($data)){
            throw new Exception('',self::RESP_CONTRACT_MISSING_CONTRACT_ID);
        }

        $contract_id = $data['id'] ?? '';
        $admin = $data['admin'] ?? '';
        if (!$contract_id || !$admin){
            return  false;
        }
        $now = date('Y-m-d H:i:s');

        $res = Contract::deleteByID($contract_id,['delated_at' => $now,'admin' => $admin]);

        Log::info(  '合同删除:', ['contract_id' => $contract_id,'OA编号' => $data['serial_number'] ?? '','user_name' => $admin]);

        return $res;
    }

    private static function delContractToApproval($contract_info = [], $user_cookie = '') {
        $sCustomerId = $contract_info['customer_id'];
        $iProductId = $contract_info['product_id'];
        $contract_name = $contract_info['contract_name'];
        $company_name = $contract_info['company_name'];
        $contract_category = $contract_info['contract_category'];
        $category = $contract_category == 1 ? '客户' : '渠道';
        $user_name   = Func::getUserNameFromCookie($user_cookie);
        $contract_info['admin'] = $user_name;
        $sApplyContent = sprintf('合同删除, 合同名称:<%s>, 公司名称:「%s」, 合同分类:「%s」',
            $contract_name,$company_name,$category);

        return ApprovalRepository::addApproval($sCustomerId,'',$iProductId,Approval::URL_CONTRACT_DEL, $contract_info, $sApplyContent, $user_cookie);
    }


    /**
     * 获取公司名称列表
     *
     * @throws Exception
     * <AUTHOR> 2024-02-22 13:59:57
     *
     * @static
     */
    public static function company_list($company_name){
        $res = Contract::getListByCompanyName($company_name);

        //转为数组(下标数组),如果下标不连续则在转换json的时候会转换为对象
        return array_merge(array_unique(array_column($res, 'company_name')));
    }


    /**
     * 获取商务名称列表
     *
     * @throws Exception
     * <AUTHOR> 2024-02-22 13:59:57
     *
     * @static
     */
    public static function salesman_list($salesman_name){
        $res = Contract::getListBySalesmanName($salesman_name);

        //转为数组(下标数组),如果下标不连续则在转换json的时候会转换为对象
        return array_merge(array_unique(array_column($res, 'salesman_name')));
    }


    /**
     * 申请 下载合同文件
     *
     * @param $contract_no
     * @param $user_cookie
     *
     * @static
     * @return bool
     * @throws Exception
     * <AUTHOR> 2024-06-03 18:02:51
     */
    public static function get_download_permission($contract_no,$user_cookie,$apply_remark){
        if(empty($contract_no)){
            throw new Exception("申请下在和合同编号为空!!");
        }
        if(empty($user_cookie)){
            throw new Exception("请登陆后进行操作!!");
        }
        if(empty($apply_remark)){
            throw new Exception("申请理由为空!!");
        }

        $user = Func::getUserNameFromCookie($user_cookie);

        //查看触发已经存在审批请求
        $has_permission = ContractDownloadPermission::get_download_permission_list($user,[$contract_no],[ContractDownloadPermission::STATUS_APPLY,ContractDownloadPermission::STATUS_DEAL]);
        if(!empty($has_permission)){
            if($has_permission['status'] == ContractDownloadPermission::STATUS_APPLY){
                throw new Exception("申请正在审批中,不可重复申请");
            }
            // if($has_permission['status'] == ContractDownloadPermission::STATUS_DEAL){
            //     throw new Exception("申请已经通过审批,不可重复申请");
            // }
        }

        $user_info = SystemUser::getUserInfoByName($user);

        $contract_info = Contract::getInfoByContractNo($contract_no);
        $customer_info = Customer::getCustomerInfo($contract_info['customer_id']);

        $data = [
            'user'        => $user,
            'contract_no' => $contract_no,
        ];

        $apply_content = $user_info['realname'].'请求下载合同,客户:《'.$customer_info['name'].'》, 合同编号:「'.$contract_no.'」, 申请理由: '.$apply_remark;

        ContractDownloadPermission::add_download_permission($contract_no,$user,$apply_remark);

        return ApprovalRepository::addApproval('','','',Approval::URL_CONTRACT_FILE_DOWNLOAD,$data,$apply_content,$user_cookie);
    }


    /**
     * 通过 下载权限申请
     *
     * @param $data
     *
     * @return bool
     * @static
     * <AUTHOR> 2024-06-03 18:08:22
     */
    public static function deal_download_permission($data){
        return ContractDownloadPermission::deal_download_permission($data['contract_no'],$data['user']);
    }


    /**
     * 驳回 下载权限申请
     *
     * @param $data
     * @param $reject_comments
     *
     * @return bool
     * @static
     * <AUTHOR> 2024-06-03 18:08:22
     */
    public static function reject_download_permission($data,$reject_comments){
        return ContractDownloadPermission::reject_download_permission($data['contract_no'],$data['user'],$reject_comments);
    }


    /**
     * 取消
     *
     * @param $data
     *
     * @static
     * @return bool
     * <AUTHOR> 2024-06-04 16:47:53
     */
    public static function cancel_download_permission($data){
        return ContractDownloadPermission::cancel_download_permission($data['contract_no'],$data['user']);
    }

    /**
     * 下载后废弃下下载许可
     * 获取许可详情
     * 返回合同下载地址
     *
     * @param $contract_no
     * @param $user_cookie
     *
     * @return bool
     * @static
     * @throws Exception
     * <AUTHOR> 2024-06-04 15:50:57
     */
    public static function abandon_download_permission($contract_no,$user_cookie){
        $user = Func::getUserNameFromCookie($user_cookie);
        //贾舒语下载合同不需要走审批流程、可直接下载通过，除她之外其他人仍需走审批流程
        if(in_array($user, ['ren.zhang','chuanchuan.yu','tianyi.li'])){
            $contract_info = Contract::getInfoByContractNo($contract_no);
            //添加下载成功日志
            ContractDownloadPermission::add_success_download_permission($contract_no,$user);

            return $contract_info['file_url'];
        }

        $contract_download_permission_info = ContractDownloadPermission::get_download_permission_list($user,[$contract_no],[ContractDownloadPermission::STATUS_DEAL]);
        if(empty($contract_download_permission_info)){
            throw new Exception("当前合同不可下载!!!");
        }

        $contract_info = Contract::getInfoByContractNo($contract_no);

        ContractDownloadPermission::abandon_download_permission($contract_no,$user);

        return $contract_info['file_url'];
    }
}
