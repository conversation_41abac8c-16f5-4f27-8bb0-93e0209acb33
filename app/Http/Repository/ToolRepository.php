<?php

namespace App\Http\Repository;

use App\Providers\Tool\SendMailService;

/**
 * Class ToolRepository 工具控制代码仓库
 * @package App\Http\Repository
 */
class ToolRepository extends PublicRepository
{
	/**
	 * 发送邮件接口
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/12 15:37
	 *
	 * @return integer
	 */
	public function sendMail()
	{
		$params            = $this->getJsonByRaw();
		$sendMailerService = new SendMailService();
		
		//设置主题
		$sendMailerService->setSubject(array_get($params, 'subject', '无主题'));
		
		//设置内容
		$sendMailerService->setContent(array_get($params, 'content', ''));
		
		//收件人
		$addressee = array_get($params, 'addressee', []);
		if (empty($addressee)) {
			return 15001;
		}
		$sendMailerService->setAddressee($addressee);
		
		//抄送人
		$cc = array_get($params, 'cc');
		if (!empty($cc)) {
			$sendMailerService->setCC($cc);
		}
		
		//设置发送人
		$from = array_get($params, 'from');
		if ($from) {
			$sendMailerService->setFrom($from);
		}
		
		//设置发件人名称
		$from_name = array_get($params, 'from_name');
		if ($from_name) {
			$sendMailerService->setFromName($from_name);
		}
		
		//设置回复人地址
		$replay = array_get($params, 'replay');
		if ($replay) {
			$sendMailerService->setReplay($replay);
		}
		
		//回复人名称
		$replay_name = array_get($params, 'replay_name');
		if ($replay_name) {
			$sendMailerService->setReplayName($replay_name);
		}
		
		//附件
		$attachment = array_get($params, 'attachment');
		if ($attachment) {
			$sendMailerService->setAttachment($attachment);
		}
		
		$sendMailerService->sendByAsync();
		
		return 0;
	}
}