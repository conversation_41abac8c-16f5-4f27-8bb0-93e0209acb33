<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/8 0008
 * Time: 16:10
 */

namespace App\Http\Repository;


//v3版本- 数据源相关的处理方案
use App\Models\MongoUpstream;
use App\Models\MongoUpstreamBill;

class V3UpstreamRepository
{
    /**
     * 获取统计数据
     *
     * @access public
     *
     * @return array
     **/
    public function getStatisticsData()
    {
        //校验必填参数
        $params = $this->validQueryParams();


        return MongoUpstream::query()->raw(function ($collction) use ($params) {
            $aggregate = [];

            //查询条件
            $aggregate[0]['$match'] = [
                'date' => [
                    '$gte' => intval(date('Ymd', strtotime($params['start_date']))),
                    '$lte' => intval(date('Ymd', strtotime($params['end_date'])))
                ]
            ];
            $customer_id            = request()->post('customer_id');
            if (!empty($customer_id)) {
                $aggregate[0]['$match']['customer_id'] = $customer_id;
            }
            $product_id = request()->post('product_id', '');
            if (!empty($product_id)) {
                $aggregate[0]['$match']['product_id'] = intval($product_id);
            }
            $channel = request()->post('channel');
            if (!empty($channel)) {
                $aggregate[0]['$match']['channel'] = ['$in' => $channel];
            }

            //分组查询
            $aggregate[1]['$group'] = [
                '_id'         => [
                    'product_id'  => '$product_id',
                    'customer_id' => '$customer_id',
                    'channel'     => '$channel'
                ],
                'product_id'  => ['$first' => '$product_id'],
                'customer_id' => ['$first' => '$customer_id'],
                'channel'     => ['$first' => '$channel'],
                'all'         => ['$sum' => '$data.all'],
                'failed'      => ['$sum' => '$data.failed'],
                'succ'        => ['$sum' => '$data.succ'],
                'yd'          => ['$sum' => '$data.yd'],
                'lt'          => ['$sum' => '$data.lt'],
                'dx'          => ['$sum' => '$data.dx'],
            ];

            //过滤字段
            $aggregate[2]['$project'] = [
                '_id'         => 0,
                'product_id'  => 1,
                'customer_id' => 1,
                'channel'     => 1,
                'all'         => 1,
                'failed'      => 1,
                'succ'        => 1,
                'yd'          => 1,
                'lt'          => 1,
                'dx'          => 1
            ];
            return $collction->aggregate($aggregate);
        })->toArray();
    }


    /**
     * 获取账单统计数据
     *
     * @access public
     *
     * @return array
     **/
    public function getBillData()
    {
        //校验必填参数
        $params = $this->validQueryParams();

        return MongoUpstreamBill::query()->raw(function ($collction) use ($params) {
            $aggregate = [];

            //查询条件
            $aggregate[0]['$match'] = [
                'date' => [
                    '$gte' => intval(date('Ymd', strtotime($params['start_date']))),
                    '$lte' => intval(date('Ymd', strtotime($params['end_date'])))
                ]
            ];
            $customer_id            = request()->post('customer_id');
            if (!empty($customer_id)) {
                $aggregate[0]['$match']['customer_id'] = $customer_id;
            }
            $product_id = request()->post('product_id', '');
            if (!empty($product_id)) {
                $aggregate[0]['$match']['product_id'] = intval($product_id);
            }
            $channel = request()->post('channel');
            if (!empty($channel)) {
                $aggregate[0]['$match']['channel'] = ['$in' => $channel];
            }

            //分组查询
            $aggregate[1]['$group'] = [
                '_id'         => [
                    'product_id'  => '$product_id',
                    'customer_id' => '$customer_id',
                    'channel'     => '$channel'
                ],
                'product_id'  => ['$first' => '$product_id'],
                'customer_id' => ['$first' => '$customer_id'],
                'channel'     => ['$first' => '$channel'],
                'money'       => ['$sum' => '$money'],
                'number'      => ['$sum' => '$fee_number']
            ];

            //过滤字段
            $aggregate[2]['$project'] = [
                '_id'         => 0,
                'product_id'  => 1,
                'customer_id' => 1,
                'channel'     => 1,
                'money'       => 1,
                'number'      => 1
            ];

            return $collction->aggregate($aggregate);
        })->toArray();
    }


    /**
     * 校验查询的参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function validQueryParams()
    {
        $start_date = request()->post('start_date', '');
        $end_date   = request()->post('end_date', '');

        $date_regex = '/^\d{4}\-\d{2}\-\d{2}$/';
        if (!preg_match($date_regex, $start_date)) {
            throw new \Exception('开始时间不存在或格式不正确');
        }

        if (!preg_match($date_regex, $end_date)) {
            throw new \Exception('截止时间不存在或格式不正确');
        }

        $start_date = intval(date('Ymd', strtotime($start_date)));
        $end_date   = intval(date('Ymd', strtotime($end_date)));
        return compact('start_date', 'end_date');
    }


}