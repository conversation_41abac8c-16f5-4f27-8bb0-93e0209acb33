<?php

namespace App\Http\Repository;

use App\Define\StatDefine;
use App\Exports\MixDataExport;
use App\Exports\PretestManageListExport;
use App\Models\Account;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\ConfigPriceInterface;
use App\Models\Product;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Common\CommonEnumModel;
use App\Models\Customer;
use App\Models\StatisticsInterfaceUsage;
use App\Models\UserChannelConfig;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Income\MainRepository;
use Maatwebsite\Excel\Facades\Excel;

class StatMixRepository extends StatBaseRepository
{

    protected $channel_map = [];
    protected $account_customer_map = [];
    protected $customer_map = [];
    protected $channel_interface_map = [];
    protected $product_map = [];
    protected $product_father_map = [];
    protected $self_channel_id = [124, 801, 802];
    protected $pudao_map = [['father_id' => 70000, 'channel_id' => [803, 804, 805, 806, 808]], ['father_id' => 210, 'channel_id' => [41]]];

    /**
     * 数据统计-融合页面
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        $params = $this->getStatParams();
        $control_mainProduct = request()->post('control_mainProduct', '0');
        $control_subProduct = intval(request()->post('control_subProduct', '0'));
        $control_channel = request()->post('control_channel', '0');
        $father_id = request()->post('father_id', '');
        $controls = $control_mainProduct . $control_subProduct . $control_channel;
        //排除掉主接口调用量
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        //获取渠道信息
        $channel_info = Channel::getAllChannel();
        $this->channel_map = array_column($channel_info, null, 'channel_id');
        //获取账号信息
        $account_info = Account::getAllAccount();
        $this->account_customer_map = array_column($account_info, 'customer_id', 'value');
        // 获取客户信息
        $customer_info = Customer::aGetListByCond();
        $this->customer_map = array_column($customer_info, 'name', 'customer_id');
        // 获取渠道接口信息
        $channel_interface_info = ChannelInterface::getAllChannel();
        $this->channel_interface_map = array_column($channel_interface_info, null, 'id');
        // 产品信息
        $product_info = Product::getAllProduct();
        $this->product_map = array_column($product_info, null, 'value');
        if (StatDefine::PM_ANGLE == $params['view_angle']) {
            $rep = new MainRepository();
            $this->product_father_map = $rep->getSubPidsByFatherIdsWithAngle([], StatDefine::PM_ANGLE);
            if ($father_id) {
                $father_product_map = [];
                foreach ($this->product_father_map as $id => $value) {
                    if (!isset($father_product_map[$value])) {
                        $father_product_map[$value] = [];
                    }
                    $father_product_map[$value][] = $id;
                }
                $params['product_list'] = $father_product_map[$father_id];
            }
        } else {
            $this->product_father_map = array_column($product_info, 'father_id', 'value');
        }

        // 客户相关数据
        $customer_usage_data = $this->getStatUsageSource($params); //客户调用量
        $customer_income_data = $this->getIncomeV2Mix($params); // 客户收入
        $customer_cost_data = $this->getCostV2Minx($params);   // 客户成本
        $customer_adjust_cost_data = $this->getAdjustCostMix($params);   //客户成本调整
        $customer_channel_adjust_cost_data = $this->getChannelAdjustCostSource($params);//渠道成本调整
        $customer_fixed_cost_data = $this->getChannelFixedCostSource($params);//固定费用成本调整
        $customer_expend_data = $this->getExpendMix($params);   //特殊消耗
        // 渠道相关数据
        $channel_ids = $this->getChannelIds($params); //主产品对应的所有渠道
        $interface_ids = $this->getIids($channel_ids);
        $channel_usage_data = $this->getChannelUsageMix($params, $interface_ids);   //调用量
        $channel_cost_data = $this->getChannelCostV2($params, $interface_ids);   //成本
        $channel_expend_cost_data = $this->getChannelExpendCost($params, $interface_ids); //特殊消耗成本
        $Channel_fixed_cost_data = $this->getChannelFixedCost($params, $channel_ids); //固定费用成本

        // 格式化客户数据
        $customer_data = $this->formatCustomerData($customer_usage_data, $customer_income_data, $customer_cost_data, $customer_adjust_cost_data, $customer_channel_adjust_cost_data, $customer_fixed_cost_data, $customer_expend_data);
        // 格式化渠道数据
        $channel_data = $this->formatChannelData($channel_usage_data, $channel_cost_data, $channel_expend_cost_data, $Channel_fixed_cost_data, $customer_data);

        // 合并数据
        $data = $this->mergeData($customer_data, $channel_data, $controls, $params);

        $data = $this->formatData($data);

        return $data;
    }

    /**
     * @param $data
     */
    protected function formatData($data)
    {
        foreach ($data as &$item) {
            $item['call_number'] = number_format($item['call_number']);
            $item['success_number'] = number_format($item['success_number']);
            $item['cache_number'] = number_format($item['cache_number']);
            $item['valid_number'] = number_format($item['valid_number']);
            $item['charge_number'] = number_format($item['charge_number']);
            $item['number'] = number_format($item['number']);
            $item['uncache_number'] = number_format($item['uncache_number']);
            $item['income_money'] = number_format($item['income_money'], 2);
            $item['cost_money'] = number_format($item['cost_money'], 2);
            $item['profit_money'] = number_format($item['profit_money'], 2);
            foreach ($item['channels'] as &$values) {
                $values['channel_call_number'] = number_format($values['channel_call_number']);
                $values['channel_success_number'] = number_format($values['channel_success_number']);
                $values['channel_valid_number'] = number_format($values['channel_valid_number']);
                $values['channel_charge_number'] = number_format($values['channel_charge_number']);
                $values['channel_number'] = number_format($values['channel_number']);
                $values['channel_cost_money'] = number_format($values['channel_cost_money'], 2);
            }
            unset($values);
        }
        unset($item);
        return $data;
    }

    /**
     * @param $customer_data
     * @param $channel_data
     * @param $controls
     * @return array
     */
    protected function mergeData($customer_data, $channel_data, $controls, $params)
    {
        if (isset($params['channel_id'])) {
            $customer_data = $this->getChannelCutomerData($channel_data, $customer_data);
        }
        //
        $self_channel_ids = $this->self_channel_id;
        // 使用array_flip优化in_array查找
        $self_channel_map = array_flip($self_channel_ids);
        foreach ($customer_data as $index => &$item) {
            // 处理有效数据
            if (isset($channel_data[$index])) {
                $item['channels'] = array_values($channel_data[$index]);
            } else {
                $item['channels'] = [];
            }
            // 优化后的排序逻辑
            usort($item['channels'], function ($a, $b) use ($self_channel_map) {
                $aInSelf = isset($self_channel_map[$a['channel_id']]);
                $bInSelf = isset($self_channel_map[$b['channel_id']]);
                if ($aInSelf !== $bInSelf) {
                    return $aInSelf ? -1 : 1;
                }
                return 0;
            });
        }
        unset($item);
        $customer_data = array_values($customer_data);
        usort($customer_data, function ($a, $b) {
            $nameCompare = strcmp($a['customer_id'], $b['customer_id']);
            if ($nameCompare !== 0) {
                return $nameCompare;
            }
            // 如果customer_id相同，再比较father_id
            return $a['father_id'] - $b['father_id'];
        });

        $customer_data = $this->getControlData($customer_data, $controls);
        $total_customer = $this->initializeBaseData([], ['customer_id' => '-', 'customer_name' => '合计', 'father_id' => '-', 'father_name' => '-', 'product_id' => '-', 'product_name' => '-']);
        $total_customer['channels'][0] = $this->initChannelData();

        //过滤不存在渠道或者三方渠道的数据
        foreach ($customer_data as $index => $item) {
            if (empty($item['channels'])) {
                unset($customer_data[$index]);
                continue;
            }

            $current_channels = $item['channels'];
            $channel_ids = array_column($current_channels, 'channel_id');
            if (!array_diff($channel_ids, $self_channel_ids)) {
                unset($customer_data[$index]);
                continue;
            }


            $total_customer['call_number'] += $item['call_number'];
            $total_customer['success_number'] += $item['success_number'];
            $total_customer['cache_number'] += $item['cache_number'];
            $total_customer['valid_number'] += $item['valid_number'];
            $total_customer['charge_number'] += $item['charge_number'];
            $total_customer['number'] += $item['number'];
            $income_money = $item['income_money'];
            $cost_money = $item['cost_money'];

            $profit_money = $item['profit_money'];
            $total_customer['income_money'] = bcadd($total_customer['income_money'], $income_money, 2);
            $total_customer['cost_money'] = bcadd($total_customer['cost_money'], $cost_money, 2);
            $total_customer['profit_money'] = bcadd($total_customer['profit_money'], $profit_money, 2);
            $total_customer['channels'][0]['channel_name'] = '-';
            foreach ($current_channels as $key => $itemChannel) {
                $channel_cost_money = $itemChannel['channel_cost_money'];
                $total_customer['channels'][0]['channel_call_number'] += $itemChannel['channel_call_number'];
                $total_customer['channels'][0]['channel_success_number'] += $itemChannel['channel_success_number'];
                $total_customer['channels'][0]['channel_valid_number'] += $itemChannel['channel_valid_number'];
                $total_customer['channels'][0]['channel_charge_number'] += $itemChannel['channel_charge_number'];
                $total_customer['channels'][0]['channel_number'] += $itemChannel['channel_number'];
                $total_customer['channels'][0]['channel_cache_number'] = 0;
                $total_customer['channels'][0]['channel_cost_money'] = bcadd($total_customer['channels'][0]['channel_cost_money'], $channel_cost_money, 2);
            }
        }

        $total_customer_data = [$total_customer];
        $this->calculateFinalRates($total_customer_data);
        array_unshift($customer_data, $total_customer_data[0]);
        return array_values($customer_data);
    }


    protected function getChannelCutomerData($channel_data, $customer_data)
    {
        $new_customer_data = [];
        foreach ($channel_data as $key => $item) {
            $channelitem = array_values($item)[0] ?? [];
            if ($channelitem) {
                $new_customer_data[$key] = [
                    'customer_id' => $customer_data[$key]['customer_id'],
                    'customer_name' => $customer_data[$key]['customer_name'],
                    'father_id' => $customer_data[$key]['father_id'],
                    'father_name' => $customer_data[$key]['father_name'],
                    'product_id' => $customer_data[$key]['product_id'],
                    'product_name' => $customer_data[$key]['product_name'],
                    'call_number' => $channelitem['channel_call_number'],
                    'success_number' => $channelitem['channel_success_number'],
                    'cache_number' => $channelitem['channel_cache_number'],
                    'valid_number' => $channelitem['channel_valid_number'],
                    'charge_number' => $channelitem['channel_charge_number'],
                    'success_rate' => $channelitem['channel_success_rate'],
                    'uncache_number' => ($channelitem['channel_call_number'] - $channelitem['channel_cache_number']),
                    'valid_rate' => $channelitem['channel_valid_rate'],
                    'charge_rate' => ($channelitem['channel_valid_number'] > 0 ? $this->formatPercentage($channelitem['channel_number'] / $channelitem['channel_valid_number']) : '0%'),
                    'income_money' => 0,
                    'cost_money' => $channelitem['channel_cost_money'],
                    'profit_money' => 0,
                    'number' => $channelitem['channel_number'],
                    'profit_rate' => '-',
                ];
            }
        }
        return $new_customer_data;
    }

    /**
     * @param array $customer_data
     * @param $controls
     * @return array
     */
    protected function getControlData(array $customer_data, $controls): array
    {
        $strategies = [
            '000' => ['processAllMerged', 'initializeCustomerData'],
            '001' => ['processProductMergedChannelsExpanded', 'initializeCustomerData'],
            '010' => ['processMainProductChannelsMerged', 'initializeCustomerProductData'],
            '011' => ['processMainProductMerged', 'initializeCustomerProductData'],
            '100' => ['processCustomerMainProductMerged', 'initializeCustomerMainProductData'],
            '101' => ['processCustomerMainProductChannelsExpanded', 'initializeCustomerMainProductData'],
            '110' => ['processCustomerAllProductsMerged', 'initializeCustomerAllProductData'],
            '111' => ['processAllExpanded', 'initializeCustomerAllProductData']
        ];

        if (!isset($strategies[$controls])) {
            throw new InvalidArgumentException("Invalid control code: {$controls}");
        }

        $method = $strategies[$controls][0];
        $initDataMethod = $strategies[$controls][1];
        return $this->$method($customer_data, $initDataMethod);
    }


    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processAllMerged(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'];
            },
            false,
            $initDataMethod
        );
    }

    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processProductMergedChannelsExpanded(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'];
            },
            true,
            $initDataMethod
        );
    }

    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processMainProductChannelsMerged(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'] . '_' . $item['product_id'];
            },
            false,
            $initDataMethod
        );
    }

    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processMainProductMerged(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'] . '_' . $item['product_id'];
            },
            true,
            $initDataMethod
        );
    }

    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processCustomerMainProductMerged(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'] . '_' . $item['father_id'];
            },
            false,
            $initDataMethod
        );
    }

    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processCustomerMainProductChannelsExpanded(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'] . '_' . $item['father_id'];
            },
            true,
            $initDataMethod
        );
    }


    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processCustomerAllProductsMerged(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'] . '_' . $item['father_id'] . '_' . $item['product_id'];
            },
            false,
            $initDataMethod
        );
    }

    /**
     * @param array $customer_data
     * @param $initDataMethod
     * @return array
     */
    private function processAllExpanded(array $customer_data, $initDataMethod): array
    {
        return $this->processDataWithStrategy(
            $customer_data,
            function ($item) {
                return $item['customer_id'] . '_' . $item['father_id'] . '_' . $item['product_id'];
            },
            true,
            $initDataMethod
        );
    }

    /**
     * @param array $customer_data
     * @param callable $indexGenerator
     * @param bool $expandChannels
     * @param $initDataMethod
     * @return array
     */
    private function processDataWithStrategy(array $customer_data, callable $indexGenerator, bool $expandChannels = false, $initDataMethod): array
    {
        $resData = [];

        foreach ($customer_data as $item) {
            $index = $indexGenerator($item);

            if (!isset($resData[$index])) {
                $resData[$index] = $this->$initDataMethod($item);
            } else {
                $this->sumCustomerData($resData[$index], $item);
            }

            $expandChannels
                ? $this->processExpandedChannels($resData[$index], $item['channels'])
                : $this->processChannelData($resData[$index], $item['channels']);
        }

        if ($expandChannels) {
            foreach ($resData as &$customer) {
                $customer['channels'] = array_values($customer['channels']);
            }
        }

        $this->calculateFinalRates($resData);
        return array_values($resData);
    }


    /**
     * @param array $customerData
     * @param array $channels
     */
    private function processExpandedChannels(array &$customerData, array $channels)
    {
        foreach ($channels as $channel) {
            $channelId = $channel['channel_id'];

            if (!isset($customerData['channels'][$channelId])) {
                $customerData['channels'][$channelId] = $this->initializeChannelData($channel);
            } else {
                $this->sumChannelData($customerData['channels'][$channelId], $channel);
            }
        }
    }


    /**
     * @param array $item
     * @param array $overrides
     * @return array
     */
    private function initializeBaseData(array $item, array $overrides = []): array
    {
        $defaults = [
            'customer_id' => $item['customer_id'] ?? '',
            'customer_name' => $item['customer_name'] ?? '',
            'father_id' => '-',
            'father_name' => '合计',
            'product_id' => '-',
            'product_name' => '合计',
            'call_number' => $item['call_number'] ?? 0,
            'success_number' => $item['success_number'] ?? 0,
            'cache_number' => $item['cache_number'] ?? 0,
            'valid_number' => $item['valid_number'] ?? 0,
            'charge_number' => $item['charge_number'] ?? 0,
            'number' => $item['number'] ?? 0,
            'success_rate' => 0,
            'uncache_number' => 0,
            'valid_rate' => 0,
            'charge_rate' => 0,
            'income_money' => $item['income_money'] ?? 0,
            'cost_money' => $item['cost_money'] ?? 0,
            'profit_money' => $item['profit_money'] ?? 0,
            'profit_rate' => '',
            'channels' => []
        ];

        return array_merge($defaults, $overrides);
    }


    /**
     * @param array $item
     * @return array
     */
    private function initializeCustomerData(array $item): array
    {
        return $this->initializeBaseData($item);
    }

    /**
     * @param array $channel
     * @return array
     */
    private function initializeChannelData(array $channel): array
    {
        return [
            'channel_id' => $channel['channel_id'],
            'channel_name' => $channel['channel_name'],
            'channel_code' => $channel['channel_code'],
            'channel_call_number' => $channel['channel_call_number'],
            'channel_success_number' => $channel['channel_success_number'],
            'channel_valid_number' => $channel['channel_valid_number'],
            'channel_charge_number' => $channel['channel_charge_number'],
            'channel_number' => $channel['channel_number'],
            'channel_cost_money' => $channel['channel_cost_money'],
            'channel_success_rate' => 0,
            'channel_valid_rate' => 0,
            'channel_cost_rate' => 0
        ];
    }


    /**
     * @param array $item
     * @return array
     */
    private function initializeCustomerProductData(array $item): array
    {
        return $this->initializeBaseData($item, [
            'product_id' => $item['product_id'],
            'product_name' => $item['product_name']
        ]);
    }

    /**
     * @param array $item
     * @return array
     */
    private function initializeCustomerMainProductData(array $item): array
    {
        return $this->initializeBaseData($item, [
            'father_id' => $item['father_id'],
            'father_name' => $item['father_name']
        ]);
    }

    /**
     * @param array $item
     * @return array
     */
    private function initializeCustomerAllProductData(array $item): array
    {
        return $this->initializeBaseData($item, [
            'father_id' => $item['father_id'],
            'father_name' => $item['father_name'],
            'product_id' => $item['product_id'],
            'product_name' => $item['product_name']
        ]);
    }

    /**
     * @param array $target
     * @param array $source
     */
    private function sumCustomerData(array &$target, array $source)
    {
        $fields = ['call_number', 'success_number', 'cache_number', 'valid_number', 'charge_number', 'number', 'income_money', 'cost_money', 'profit_money'];
        foreach ($fields as $field) {
            if (isset($source[$field])) {
                $target[$field] += $source[$field];
            }
        }
    }

    /**
     * @param array $customerData
     * @param array $channels
     */
    private function processChannelData(array &$customerData, array $channels)
    {
        if (!isset($customerData['channels'][0])) {
            $customerData['channels'][0] = $this->initChannelData();
        }

        foreach ($channels as $channel) {
            $this->sumChannelData($customerData['channels'][0], $channel);
        }
    }

    /**
     * @return array
     */
    private function initChannelData()
    {
        return [
            'channel_id' => '-',
            'channel_name' => '合计',
            'channel_code' => '-',
            'channel_call_number' => 0,
            'channel_success_number' => 0,
            'channel_valid_number' => 0,
            'channel_charge_number' => 0,
            'channel_number' => 0,
            'channel_cache_number' => 0,
            'channel_success_rate' => 0,
            'channel_valid_rate' => 0,
            'channel_cost_money' => 0,
            'channel_cost_rate' => 0
        ];
    }

    /**
     * @param array $target
     * @param array $source
     */
    private function sumChannelData(array &$target, array $source)
    {
        $fields = ['channel_call_number', 'channel_success_number', 'channel_valid_number', 'channel_charge_number', 'channel_number', 'channel_cost_money'];

        foreach ($fields as $field) {
            $target[$field] += $source[$field];
        }
    }

    /**
     * @param array $data
     */
    private function calculateFinalRates(array &$data)
    {
        foreach ($data as &$customer) {

            // 确保所有数值字段都是float类型
            $callNumber = (float)($customer['call_number'] ?? 0);
            $successNumber = (float)($customer['success_number'] ?? 0);
            $cacheNumber = (float)($customer['cache_number'] ?? 0);
            $validNumber = (float)($customer['valid_number'] ?? 0);
            $chargeNumber = (float)($customer['charge_number'] ?? 0);
            $number = (float)($customer['number'] ?? 0);
            $incomeMoney = (float)($customer['income_money'] ?? 0);
            $costMoney = (float)($customer['cost_money'] ?? 0);
            // 计算各项指标
            $customer['success_rate'] = $callNumber > 0 ? $this->formatPercentage($successNumber / $callNumber) : '0%';
            $customer['uncache_number'] = $successNumber - $cacheNumber;
            $customer['valid_rate'] = $successNumber > 0 ? $this->formatPercentage($validNumber / $successNumber) : '0%';
            $customer['charge_rate'] = $validNumber > 0 ? $this->formatPercentage($number / $validNumber) : '0%';
            $profitMoney = $incomeMoney - $costMoney;
            $customer['profit_money'] = $profitMoney;
            $customer['profit_rate'] = ($incomeMoney > 0 && $profitMoney > 0 )? $this->formatPercentage($profitMoney / $incomeMoney) : '0%';

            // 渠道比率计算
            if (isset($customer['channels'][0])) {
                $channel = &$customer['channels'];
                foreach ($channel as &$row) {
                    $callNumber = (float)($row['channel_call_number'] ?? 0);
                    $successNumber = (float)($row['channel_success_number'] ?? 0);
                    $validNumber = (float)($row['channel_valid_number'] ?? 0);
                    $rowCostMoney = (float)($row['channel_cost_money'] ?? 0);

                    $row['channel_cost_money'] = $rowCostMoney;
                    $row['channel_success_rate'] = $callNumber > 0 ? $this->formatPercentage($successNumber / $callNumber) : '0%';
                    $row['channel_valid_rate'] = $successNumber > 0 ? $this->formatPercentage($validNumber / $successNumber) : '0%';
                    $row['channel_cost_rate'] = $costMoney > 0 ? $this->formatPercentage($rowCostMoney / $costMoney) : '0%';
                }
            }
        }
    }

    /**
     * @param $customer_usage_data
     * @param $customer_income_data
     * @param $customer_cost_data
     * @param $customer_adjust_cost_data
     * @param array $customer_channel_adjust_cost_data
     * @param array $customer_fixed_cost_data
     * @param array $customer_expend_data
     * @return array
     * @throws \Exception
     */
    private
    function formatCustomerData($customer_usage_data, $customer_income_data, $customer_cost_data, $customer_adjust_cost_data, $customer_channel_adjust_cost_data = [], $customer_fixed_cost_data = [], $customer_expend_data = [])
    {
        $data = [];
        // 调用相关
        foreach ($customer_usage_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            $customer_id = $this->account_customer_map[$item['apikey']];
            $product_id = $item['product_id'];
            $father_id = $this->product_father_map[$product_id] ?? '';
            if (isset($data[$index])) {
                // 只累加数值字段（比率字段最后统一计算）
                $data[$index]['call_number'] += $item['total'];
                $data[$index]['success_number'] += $item['success'];
                $data[$index]['cache_number'] += $item['cache'];
                $data[$index]['valid_number'] += $item['valid'];
                $data[$index]['charge_number'] += $item['charge'];
            } else {
                // 初始化模板（不计算比率字段）
                $data[$index] = [
                    'customer_id' => $customer_id,
                    'customer_name' => $this->customer_map[$customer_id] ?? '',
                    'father_id' => $father_id,
                    'father_name' => $this->product_map[$father_id == 0 ? $product_id : $father_id]['label'] ?? '',
                    'product_id' => $product_id,
                    'product_name' => $this->product_map[$product_id]['label'] ?? '',
                    'call_number' => $item['total'],
                    'success_number' => $item['success'],
                    'cache_number' => $item['cache'],
                    'valid_number' => $item['valid'],
                    'charge_number' => $item['charge'],

                    // 比率字段先占位，最后统一计算
                    'success_rate' => 0,
                    'uncache_number' => 0,
                    'valid_rate' => 0,
                    'charge_rate' => 0,
                    'income_money' => 0, // 需补充逻辑
                    'cost_money' => 0,  // 需补充逻辑
                    'profit_money' => 0,// 需补充逻辑
                    'number' => 0, // 计费量
                    'profit_rate' => '', // 需补充逻辑
                    'channels' => [],
                ];
            }
        }

        // 收入
        foreach ($customer_income_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            $income_father_id = $this->product_map[$item['product_id']]['father_id'] ?? '';
            $source = $item['source'];
            $operator = $item['operator'];
            if (isset($data[$index])) {
                $income_money = $data[$index]['income_money'];
                $item_income_money = $item['money'];
                if (($income_father_id == 70000 && $source == 1) || ($income_father_id == 210 && $source == 1 && $operator == 'ZYCMCC')) { // 号码融朴道测
                    $item_income_money = $item['money_original'];
                }
                $data[$index]['income_money'] = bcadd(
                    $income_money ?? '0',
                    $item_income_money,
                    2  // 保留2位小数
                );
                $data[$index]['number'] += $item['number'];
            }
        }
        // 成本
        foreach ($customer_cost_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            if (isset($data[$index]) && $item['money']) {
                $channel_id = $this->channel_interface_map[$item['interface_id']]['channel_id'] ?? '';
                $father_id = $this->product_map[$item['product_id']]['father_id'] ?? '';
                $isOriginal = $this->isOriginal($father_id, $channel_id);
                $cost_money = $data[$index]['cost_money'];
                if ($isOriginal) {
                    $item_cost_money = $item['money_original'];
                } else {
                    $item_cost_money = $item['money'];
                }
                $data[$index]['cost_money'] = bcadd(
                    $cost_money ?? '0',
                    $item_cost_money,
                    2  // 保留2位小数
                );
            }
        }
        // 客户成本调整
        foreach ($customer_adjust_cost_data as $item) {
            $index = $item['customer_id'] . '_' . $item['product_id'];
            if (isset($data[$index])) {
                $cost_money = $data[$index]['cost_money'];
                $data[$index]['cost_money'] = bcadd(
                    $cost_money ?? '0',
                    $item['money'],
                    2  // 保留2位小数
                );
            }
        }
        // 渠道成本调整
        foreach ($customer_channel_adjust_cost_data as $item) {
            $index = $item['customer_id'] . '_' . $item['product_id'];
            if (isset($data[$index])) {
                $cost_money = $data[$index]['cost_money'];
                $data[$index]['cost_money'] = bcadd(
                    $cost_money ?? '0',
                    $item['money'],
                    2  // 保留2位小数
                );
            }
        }
        // 渠道固定成本
        foreach ($customer_fixed_cost_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            if (isset($data[$index])) {
                $cost_money = $data[$index]['cost_money'];
                $data[$index]['cost_money'] = bcadd(
                    $cost_money ?? '0',
                    $item['money'],
                    2  // 保留2位小数
                );
            }
        }
        // 特殊消耗
        foreach ($customer_expend_data as $item) {
            $index = $item['customer_id'] . '_' . $item['product_id'];
            if (isset($data[$index])) {
                $scale = 2;
                if ($item['type'] == 1) {
                    // 减法计算：cost_money = cost_money - (money * rate)
                    // 乘法保留更多位数
                    $income_money = $data[$index]['income_money'];
                    $data[$index]['income_money'] = bcsub(
                        $income_money ?? '0',
                        $item['money'],
                        $scale
                    );
                } else {
                    // 加法计算：cost_money = cost_money + (money * rate);
                    $income_money = $data[$index]['income_money'];
                    $data[$index]['income_money'] = bcadd(
                        $income_money ?? '0',
                        $item['money'],
                        $scale
                    );
                }
            }
        }

        return $data;
    }


    private function formatChannelData($channel_usage_data, $channel_cost_data, $channel_expend_cost_data, $Channel_fixed_cost_data, $customer_data)
    {
        $data = [];
        foreach ($channel_usage_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            $channel_id = $this->channel_interface_map[$item['interface_id']]['channel_id'] ?? '';
            if (!$channel_id) {
                continue;
            }
            if (isset($data[$index][$channel_id])) {
                // 只累加数值字段（比率字段最后统一计算）
                $data[$index][$channel_id]['channel_call_number'] += $item['total'];
                $data[$index][$channel_id]['channel_success_number'] += $item['success'];
                $data[$index][$channel_id]['channel_valid_number'] += $item['valid'];
                $data[$index][$channel_id]['channel_charge_number'] += $item['charge'];
            } else {
                $data[$index][$channel_id] = [
                    'channel_id' => $channel_id,
                    'channel_name' => $this->channel_map[$channel_id]['label'] ?? '',
                    'channel_code' => $this->channel_map[$channel_id]['name'] ?? '',
                    'channel_call_number' => $item['total'],
                    'channel_success_number' => $item['success'],
                    'channel_valid_number' => $item['valid'],
                    'channel_charge_number' => $item['charge'],
                    'channel_cache_number' => 0, //  接口调用量不存在缓存值 默认为0
                    // 比率字段先占位，最后统一计算
                    'channel_success_rate' => 0,
                    'channel_number' => 0, //渠道计费量
                    'channel_valid_rate' => 0,
                    'channel_cost_money' => 0, // 后续补充
                    'channel_cost_rate' => 0, // 后续补充 渠道成本占比
                ];
            }
        }

        foreach ($channel_cost_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            $channel_id = $this->channel_interface_map[$item['interface_id']]['channel_id'] ?? '';
            if (!$channel_id) {
                continue;
            }
            if (isset($data[$index][$channel_id])) {
                $father_id = $this->product_map[$item['product_id']]['father_id'] ?? '';
                $isOriginal = $this->isOriginal($father_id, $channel_id);
                $channel_cost_money = $data[$index][$channel_id]['channel_cost_money'];
                if ($isOriginal) {
                    $item_channel_cost_money = $item['money_original'];
                } else {
                    $item_channel_cost_money = $item['money'];
                }
                $data[$index][$channel_id]['channel_cost_money'] = bcadd($channel_cost_money, $item_channel_cost_money, 2);
                $data[$index][$channel_id]['channel_number'] += $item['number'];
            }
        }

        foreach ($channel_expend_cost_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            $channel_id = $this->channel_interface_map[$item['interface_id']]['channel_id'] ?? '';
            if (!$channel_id) {
                continue;
            }
            if (isset($data[$index][$channel_id])) {
                $channel_cost_money = $data[$index][$channel_id]['channel_cost_money'];
                $data[$index][$channel_id]['channel_cost_money'] = bcadd($channel_cost_money, $item['money'], 2);
            }
        }

        foreach ($Channel_fixed_cost_data as $item) {
            $index = $this->getIndex($item);
            if (!$index) {
                continue;
            }
            $channel_id = $item['channel_id'];
            if (isset($data[$index][$channel_id])) {
                $channel_cost_money = $data[$index][$channel_id]['channel_cost_money'];
                $data[$index][$channel_id]['channel_cost_money'] = bcadd($channel_cost_money, $item['money'], 2);
            }
        }
        return $data;
    }

    private
    function formatPercentage(float $ratio): string
    {
        $percentage = $ratio * 100;

        // 如果是整数百分比
        if ($percentage == (int)$percentage) {
            return (int)$percentage . '%';
        }

        // 否则保留2位小数
        return number_format($percentage, 2) . '%';
    }

    protected
    function getIndex($item)
    {
        $customer_id = $this->account_customer_map[$item['apikey']] ?? null;
        if (!$customer_id) return null;
        return $customer_id . '_' . $item['product_id'];
    }

    protected
    function getIids($channel_ids)
    {
        $channel_ids = implode(',', $channel_ids);
        return array_column(ChannelInterface::getInterfaceIdBychannel($channel_ids), 'id');
    }

    protected
    function getChannelIds($params)
    {
        $channelIds = [];
        if (isset($params['channel_id']) && $params['channel_id']) {
            $channelIds[] = $params['channel_id'];
        } else {
            $channelIds = array_keys($this->channel_map);
        }

        return $channelIds;
    }

    protected
    function getChannelAdjustCostSource($params)
    {
        //由于ChannelAccountAdjust中date是带'-'格式的,在这里提前转化一下
        if (isset($params['start_date'])) {
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if (isset($params['end_date'])) {
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelCostByCustomerMix($where, $params);

        return $cost_data;
    }

    protected
    function getChannelFixedCostSource($params)
    {
        //由于ChannelAccountFixedFee中date是带'-'格式的,在这里提前转化一下
        if (isset($params['start_date'])) {
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if (isset($params['end_date'])) {
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountFixedFee::getChannelCostByMix($where, $params);

        return $cost_data;
    }

    protected
    function getChannelUsageMix($params, $interface_ids = null)
    {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsInterfaceUsage::getStatListMix($where, $params['product_list'], $params['apikey_list'], $interface_ids, $params['filter_apikey']);
        return $usage_data;
    }

    protected
    function getChannelCostV2($params, $interface_ids = null)
    {
        $where = $this->getCostWhere($params);
        $cost_data = BillCostV2::getChannelStatListMix($where, $params['product_list'], $params['apikey_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected
    function getChannelExpendCost($params, $interface_ids = null)
    {
        //由于ChannelAccountAdjust中date是带'-'格式的,在这里提前转化一下
        if (isset($params['start_date'])) {
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if (isset($params['end_date'])) {
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelStatListMix($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected
    function getChannelFixedCost($params, $channel_ids)
    {
        if (empty($channel_ids)) {
            return [];
        } else {
            $params['channel_id'] = $channel_ids;
        }
        //由于ChannelAccountFixedFee中date是带'-'格式的,在这里提前转化一下
        if (isset($params['start_date'])) {
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if (isset($params['end_date'])) {
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = [['date', '>=', $params['start_date']], ['date', '<=', $params['end_date']]];
        if (isset($params['channel_id']) && !empty($params['channel_id'])) {
            $where[] = ['channel_id', '=', $params['channel_id']];
        }


        $cost_data = ChannelAccountFixedFee::getChannelStatListMix($where, $params);
        return $cost_data;

    }

    protected function getCostV2Minx($params)
    {
        $where = $this->getCostWhere($params);
        $cost_data = BillCostV2::getStatListMix($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'])->toArray();
        return $cost_data;
    }

    /**
     * 测试明细列表下载excel
     */
    public
    function listDownload()
    {
        $aDataList = $this->statList();
        return Excel::download(new MixDataExport($aDataList), '融合数据.xlsx');
    }

    /**
     * @param $father_id
     * @param $channel_id
     * @return bool
     */
    private function isOriginal($father_id, $channel_id)
    {
        foreach ($this->pudao_map as $pudaoMap) {
            if ($father_id == $pudaoMap['father_id'] && in_array($channel_id,$pudaoMap['channel_id'])){
                return  true;
            }
        }
        return  false;
    }

    /**
     * 客户收入源数据
     * @param $params
     * @return mixed
     */
    protected function getIncomeV2Mix($params)
    {
        $where = $this->getIncomeWhere($params);
        $income_data = BillProductIncomeV2::getStatListMix($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey']);
        return $income_data;
    }

}
