<?php

namespace App\Http\Repository;

class ProductAuthRepository
{
    /*
    * product key 列表 && 和产品ID的映射关系
    * */
    private $key_cache_list_product_key_relation = 'backend_api_cache_product_relation';

    /*
     * 存储的连接
     * */
    private $redis_connection = 'db_backend';

    /**
     * 清理权限认证产生的缓存
     */
    public function clearAuthCache()
    {
        $exists_cache = app('redis')->connection($this->redis_connection)
            ->exists($this->key_cache_list_product_key_relation);
        if ($exists_cache){
            app('redis')->connection($this->redis_connection)
                ->del($this->key_cache_list_product_key_relation);
        }
    }
}