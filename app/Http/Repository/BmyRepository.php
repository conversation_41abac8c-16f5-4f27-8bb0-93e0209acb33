<?php

namespace App\Http\Repository;


use App\Models\BillConfig;
use App\Models\ClickHouse\RequestProductLog;
use App\Models\NetTimeRecheck;
use App\Models\Opdata\SelfChannelRuleMonitorLog;
use App\Models\Product;
use App\Models\SystemSession;
use App\Models\Upstream;
use App\Models\UpstreamChannelPrice;
use App\Models\UserRequestLog;
use App\Providers\RedisCache\RedisCache;
use App\TraitUpgrade\CurlTrait;
use App\Utils\Helpers\Func;

class BmyRepository extends PublicRepository
{
	protected $father_id = 200;
	
	
	/**
	 * 获取邦秒验的客户单价、成本单价的接口所需的参数值
	 **/
	protected $operatorForPrice  = [
		0 => '移动',
		1 => '联通',
		2 => '电信',
	];
	protected $startDateForPrice = null;

    protected $net_status_pids = [216, 41002, 225, 203, 41012, 228];

    protected $net_time_pids = [202, 224, 41001];

    const DEFAULT_NET_STATUS_THRESHOD = 0.03;
    const DEFAULT_NET_TIME_THRESHOD = 0.024;

    const SELF_CHANNEL_RULE_NO_THRESHOLD = 0;
    const SELF_CHANNEL_RULE_THRESHOLD_ONE = 1;
    const SELF_CHANNEL_RULE_THRESHOLD_TWO = 2;

    protected $self_channel_rule_map = [
        self::SELF_CHANNEL_RULE_NO_THRESHOLD => '默认规则',
        self::SELF_CHANNEL_RULE_THRESHOLD_ONE => '最严规则',
        self::SELF_CHANNEL_RULE_THRESHOLD_TWO => '普通规则'
    ];
	
	/**
	 * 校验参数
	 *
	 * @access protected
	 *
	 * @return void
	 **/
	protected function checkParamsForPrice()
	{
		$date = request()->get('date', date('Ymd'));
		if (!preg_match('/^\d{8}$/', $date)) {
			throw new \Exception('日期格式不正确');
		}
		$this->startDateForPrice = $date;
	}
	
	/**
	 * 获取全量的客户价格
	 *
	 * @access public
	 *
	 * @return array
	 **/
	public function getAllCustomerPrice()
	{
		$this->checkParamsForPrice();
		$data = BillConfig::select([
			'account.apikey',
			'account.account_id',
			'fee_config.fee_price',
			'fee_config.start_date',
			'fee_config.product_id',
			'fee_config.fee_method',
			'fee_config.fee_time_rule',
			'fee_config.fee_amount_rule',
			'fee_config.fee_price_rule',
			'fee_config.is_reset',
		])
						  ->join('account', 'account.account_id', '=', 'fee_config.account_id')
						  ->join('account_product', 'account.account_id', '=', 'account_product.account_id')
						  ->join('product', 'fee_config.product_id', '=', 'product.product_id')
						  ->where('product.father_id', '=', $this->father_id)
						  ->where('fee_config.is_delete', '=', 0)
						  ->where('fee_config.start_date', '<=', date('Y-m-d', strtotime($this->startDateForPrice)))
						  ->orderBy('fee_config.start_date', 'desc')
						  ->get()
						  ->toArray();
		
		//对每条数据进行单独处理
		
		$chadResultUniqueKey = [];
		$result              = [];
		
		array_walk($data, function ($item) use (&$result, &$chadResultUniqueKey) {
			$unique_key = $item['product_id'] . '_' . $item['apikey'];
			if (in_array($unique_key, $chadResultUniqueKey)) {
				return;
			}
			//因为数据是按照计费开始时间倒序的，将当前所处理的数据记录一下，如果后面还有相同的【apikey-product_id】，则不会处理
			//最终的数据一定是最新的
			$chadResultUniqueKey[] = $unique_key;
			
			$apikey = $item['apikey'];
			$pid    = $item['product_id'];
			
			//如果是归零设置
			if ($item['is_reset'] == 1) {
				$price = 0;
				foreach ($this->operatorForPrice as $operator_type => $name) {
					array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
				}
				
				return;
			}
			
			switch ($item['fee_method']) {
				case 1:
					//按时间计费，因为逻辑上来说，不存在此类计费，则返回-1
					$price = -1;
					foreach ($this->operatorForPrice as $operator_type => $name) {
						array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
					}
					break;
				case 2:
					//按用量计费
					switch ($item['fee_amount_rule']) {
						case 1:
							//固定价格
							switch ($item['fee_price_rule']) {
								case 1:
									//不区分运营商
									$price = $item['fee_price'];
									foreach ($this->operatorForPrice as $operator_type => $name) {
										array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
									}
									break;
								case 2:
									//区分运营商
									$priceArr = json_decode($item['fee_price'], true);
									foreach ($this->operatorForPrice as $operator_type => $name) {
										$price = $priceArr[$operator_type];
										array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
									}
									break;
								default :
									throw new \Exception("存在未知的价格规则【{$item['product_id']}_{$item['account_id']}_{$item['start_date']}】");
									break;
							}
							break;
						case 2:
							//累进阶梯
							$price = json_decode($item['fee_price'], true);
							switch ($item['fee_price_rule']) {
								case 1:
									//不区分运营商
									$price = min(array_column($price, 2));
									foreach ($this->operatorForPrice as $operator_type => $name) {
										array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
									}
									break;
								case 2:
									//区分运营商
									$priceArr   = [];
									$priceArr[] = min(array_column($price, 2));
									$priceArr[] = min(array_column($price, 3));
									$priceArr[] = min(array_column($price, 4));
									foreach ($this->operatorForPrice as $operator_type => $name) {
										$price = $priceArr[$operator_type];
										array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
									}
									break;
								default :
									throw new \Exception("存在未知的价格规则【{$item['product_id']}_{$item['account_id']}_{$item['start_date']}】");
									break;
							}
							break;
						case 3:
							//到达阶梯
							$price = json_decode($item['fee_price'], true);
							switch ($item['fee_price_rule']) {
								case 1:
									//不区分运营商
									$price = min(array_column($price, 1));
									foreach ($this->operatorForPrice as $operator_type => $name) {
										array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
									}
									break;
								case 2:
									//区分运营商
									$priceArr   = [];
									$priceArr[] = min(array_column($price, 1));
									$priceArr[] = min(array_column($price, 2));
									$priceArr[] = min(array_column($price, 3));
									foreach ($this->operatorForPrice as $operator_type => $name) {
										$price = $priceArr[$operator_type];
										array_unshift($result, compact('apikey', 'pid', 'operator_type', 'price'));
									}
									break;
								default :
									throw new \Exception("存在未知的价格规则【{$item['product_id']}_{$item['account_id']}_{$item['start_date']}】");
									break;
							}
							break;
						default :
							throw new \Exception("存在未知的计费方式【{$item['product_id']}_{$item['account_id']}_{$item['start_date']}】");
							break;
					}
			}
		});
		
		return $result;
	}
	
	/**
	 * 获取全量的成本价格
	 *
	 * @access public
	 *
	 * @return array
	 **/
	public function getAllUpstreamPrice()
	{
		$this->checkParamsForPrice();
		
		$product_id = Product::where('father_id', '=', $this->father_id)
							 ->pluck('product_id')
							 ->toArray();
		
		$data = UpstreamChannelPrice::select([
			'upstream_channel.name',
			'upstream_channel_price.product_id',
			'upstream_channel_price.channel',
			'upstream_channel_price.price',
		])
									->leftJoin('upstream_channel', 'upstream_channel_price.upstream_channel_id', '=', 'upstream_channel.id')
									->where('upstream_channel_price.start_date', '<=', date('Y-m-d', strtotime($this->startDateForPrice)))
									->whereIn('upstream_channel.product_id', $product_id)
									->orderBy('start_date', 'desc')
									->get()
									->toArray();
		
		$result = [];
		//用于校验是否计费记录了这个渠道的计费，如果已经记录的，则不应该重复记录，因为是按照开始时间倒序的，这会将所有的价格都固定为最近的价格
		$chadResultUniqueKey = [];
		
		array_walk($data, function ($item) use (&$result, &$chadResultUniqueKey) {
			
			$unique_key = "{$item['channel']}_{$item['product_id']}";
			if (in_array($unique_key, $chadResultUniqueKey)) {
				return;
			}
			$chadResultUniqueKey[] = $unique_key;
			
			$name         = $item['name'];
			$statis_field = $item['channel'];
			$pid          = $item['product_id'];
			
			$priceArr = json_decode($item['price'], true);
			/**
			 * 'yd' => 0,
			 * 'lt' => 1,
			 * 'dx' => 2
			 **/
			
			//移动的价格
			$succ   = array_get($priceArr, 'succ', 0);
			$all    = array_get($priceArr, 'all', 0);
			$yd     = array_get($priceArr, 'yd', 0) + $succ + $all;
			$lt     = array_get($priceArr, 'lt', 0) + $succ + $all;
			$dx     = array_get($priceArr, 'dx', 0) + $succ + $all;
			$prices = [$yd, $lt, $dx];
			
			foreach ($prices as $operator_type => $price) {
				array_unshift($result, compact('name', 'statis_field', 'pid', 'operator_type', 'price'));
			}
		});
		
		return $result;
	}
	
	/**
	 * 获取当前数据源的状态
	 *
	 * @access public
	 *
	 * @return array
	 **/
	public function getUpstreamStatus()
	{
		$product_id = Product::where('father_id', '=', $this->father_id)
							 ->pluck('product_id')
							 ->toArray();
		$data       = Upstream::select(['name', 'channel', 'yd_status', 'lt_status', 'dx_status', 'product_id'])
							  ->whereIn('product_id', $product_id)
							  ->get()
							  ->toArray();
		
		$result = [];
		
		array_walk($data, function ($item) use (&$result) {
			$pid          = $item['product_id'];
			$statis_field = $item['channel'];
			$name         = $item['name'];
			
			$operator_type = 0;
			$status        = $item['yd_status'];
			array_unshift($result, compact('pid', 'name', 'statis_field', 'operator_type', 'status'));
			
			$operator_type = 1;
			$status        = $item['lt_status'];
			array_unshift($result, compact('pid', 'name', 'statis_field', 'operator_type', 'status'));
			
			$operator_type = 2;
			$status        = $item['dx_status'];
			array_unshift($result, compact('pid', 'name', 'statis_field', 'operator_type', 'status'));
			
		});
		
		return $result;
	}

	protected function init(){
        $user_cookie = request()->post('user_cookie', null);
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id'=>$user_cookie])->first();
            if(empty($user_name)){
                return ['msg' => '登录已过期，请重新登录', 'code'=>50001 , 'data'=>[]];
            }
            $user_name = $user_name->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)){
                $sysName = $match[1];
            }
        }

        if(!isset($sysName)){
            return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
        }

        return ['code'=>0 , 'sysName'=>$sysName];
    }


	public function recheckSave(){
        $sids = request()->post('sids', null);
        $channel_id = request()->post('channel_id', 0);
        $check = $this->init();
        if($check['code'] > 0){
            return $check;
        }

        $sysName = $check['sysName'];
        $id = request()->post('id', null);
        try{
            if(intval($id) > 0){//编辑
               #todo

            }else{
                $sids = explode(PHP_EOL, $sids);
                foreach($sids as &$sid){
                    $sid = trim($sid);
                }
                $rpModel = new RequestProductLog();
                $list = $rpModel->getListBySids($sids);
                if(empty($list)){
                    return ['msg' => 'sid有误或时间过长', 'code'=>50008 , 'data'=>[]];
                }

                $insert = NetTimeRecheck::BatchAddRecord($list, $sysName, $channel_id);
                if($insert){
                    return ['msg' => '记录生成成功,复核结果需刷新等待', 'code'=>0 , 'data'=>[]];
                }else{
                    return ['msg' => '记录生成失败，请联系技术', 'code'=>50006 , 'data'=>[]];
                }
            }

        }catch(Exception $e){
            return ['msg' => "任务记录操作失败:".$e->getMessage()."，请联系技术", 'code'=>50007 , 'data'=>[]];
        }

    }

    public function recheck(){
        $sid = request()->post('sid', null);
        $channel_id = request()->post('channel_id', 0);//指定走的渠道
        $json_str = request()->post('json_str', '');
        $check = $this->init();
        if($check['code'] > 0){
            return $check;
        }

        $sysName = $check['sysName'];

        $json_arr = json_decode($json_str, true);
        if(empty($json_arr) || !is_array($json_arr)){
            return ['msg' => 'input参数获取有误', 'code'=>50009 , 'data'=>[]];
        }
        if(!isset($json_arr['products']) || empty($json_arr['products'])){
            return ['msg' => 'input参数products获取有误', 'code'=>50009 , 'data'=>[]];
        }

        $pid = $check_pid = $json_arr['products'];
        if($pid == 225){
            $check_pid = 216;//状态前筛使用216核验
        }
        if($pid == 224){
            $check_pid = 202;//时长前筛使用202核验
        }
        //要排除走的渠道
        $channel_no = isset($json_arr['channel_no']) ? $json_arr['channel_no'] : 0;
        if($channel_no > 100){//转换成bmy对应的channel_type
            $not_call_channel = $channel_no - 100;
        }else{
            $not_call_channel = '';
        }

        if($channel_id > 100){//转换成bmy对应的channel_type
            //如果指定渠道了,其它渠道必须排除了，要不然不一定走指定渠道
            $call_channel = $channel_id - 100;
            $not_call_channel = 'all';
        }else{
            $call_channel = '';
        }

        $apikey = 'b98b0449291f435141a0e58b9952ede2';
        $secret = 'ce5b34fb96baf51c857939b982e5236c0baacde8aa62f8e741b0c1133412f28c';
        $url = "http://opdata.dianhua.cn/index/index";

        try{

            $nonce = mt_rand(1000, 9999);
            $timestamp = time();

            //$pid = $val['product_id'];
            $postData = [
                'products' => $check_pid,
                'phone' => isset($json_arr['phone']) ? $json_arr['phone'] : "",
                'idnum' => isset($json_arr['idnum']) ? $json_arr['idnum'] : "",
                'name' => isset($json_arr['name']) ? $json_arr['name'] : "",
                'backDate' => isset($json_arr['backDate']) ? $json_arr['backDate'] : "",
                'activeDate' => isset($json_arr['activeDate']) ? $json_arr['activeDate'] : "",
                'readCache' => 2,
                'callChannels' => $call_channel ? $call_channel : "",
                'notCallChannels' => $not_call_channel ? $not_call_channel : "",
                'timestamp' => $timestamp,
                'apikey' => $apikey,
                'nonce' => $nonce,
                'signature' => Func::sign($apikey, $secret, $nonce, $timestamp)
            ];

            $curl_res = CurlTrait::postData($url, $postData);
            if(isset($curl_res['data']['result']['result_'.$check_pid]['status'])){//说明成功调用
                $curl_data = $curl_res['data']['result']['result_'.$check_pid]['data']['rescode'] ?? 4003;
                $curl_sid = $curl_res['sid'] ?? '';
            }else{
                //调用失败
                $curl_data = '-2';
                $curl_sid = '';
            }

            $add = [
                'check_sid' => $sid,
                'check_product_id' => $pid,
                'check_channel_id' => $channel_id,
                'sid' => $curl_sid,
                'data' => $curl_data,
                'create_at' => time(),
                'admin' => $sysName,
            ];

            $insert = NetTimeRecheck::insert($add);
            if($insert){
                return ['msg' => '复核成功', 'code'=>0 , 'data'=>[]];
            }else{
                return ['msg' => '复核失败，请联系技术', 'code'=>50006 , 'data'=>[]];
            }


        }catch(Exception $e){
            return ['msg' => "任务记录操作失败:".$e->getMessage()."，请联系技术", 'code'=>50007 , 'data'=>[]];
        }

    }

    public function recheckList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $sid = request()->post('sid', '');
        $tel = request()->post('tel', '');
        $offset = ($page - 1) * $limit;
        $where = [];
        if($sid){
            $sids = explode(PHP_EOL, $sid);
            foreach($sids as &$sid){
                $sid = trim($sid);
            }
            $where['sids'] = $sids;
        }
        if($tel){
            $where['tel'] = $tel;
        }

        $rpModel = new RequestProductLog();
        $res = $rpModel->getBmyList($where, $offset, $limit);
        foreach ($res['data'] as &$val){
            if($val['channel_id'] == 0){
                $val['channel_name'] = '缓存';
            }else{
                $val['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($val['channel_id']);
            }
            $val['product_name'] = RedisCache::instance('productId_productName_mapping')->get($val['pids']);
            $val['product_id'] = $val['pids'];
            $val['phone'] = $val['tel'];
            $input = json_decode($val['input'], true);
            $arr['products'] = $val['product_id'];
            $arr['channel_no'] = $val['channel_id'];
            $arr['phone'] = $val['tel'];
            $arr['idnum'] = $input['idnum'] ?? '';
            $arr['name'] = $input['name'] ?? '';
            $arr['backDate'] = $input['backDate'] ?? '';
            $arr['activeDate'] = $input['activeDate'] ?? '';
            $val['json_str'] = json_encode($arr);
            unset($val['input']);
            $val['tel'] = substr($val['tel'], 0, 24);
        }

        return $res;
    }

    public function history(){
        $sid = request()->post('sid', '');
        $product_id = request()->post('product_id', 0);
        $where['delete_at'] = 0;
        $where['check_sid'] = $sid;
        $where['check_product_id'] = $product_id;

        $res = NetTimeRecheck::getList($where, 0, 100);
        $rpModel = new RequestProductLog();
        foreach ($res['data'] as &$val){
            $val['create_at'] = date('Y-m-d H:i:s', $val['create_at']);

            if(empty($val['sid']) && $val['channel_id'] ==0){//异常
                $val['channel_name'] = '异常';
                continue;
            }
            if($val['data'] == 4003 && $val['channel_id'] ==0 && $val['check_channel_id'] > 0){
                //当指定渠道返回4003时，request_product_log中channel_id为0且request_channel_log不写入
                $val['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($val['check_channel_id']);
                continue;
            }
            if($val['channel_id'] ==0 ){
                $info = $rpModel->getBmyList(['sids' => [$val['sid']] ], 0, 1);
                $channel_id = $info['data'][0]['channel_id'] ?? 0;
                if($channel_id==0){
                    $val['channel_name'] = '同步中...';
                }else{
                    $val['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($channel_id);
                    NetTimeRecheck::where(['id' => $val['id']])->update(['channel_id' => $channel_id]);
                }
            }else{

                $val['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($val['channel_id']);

            }

        }

        return $res;
    }

    public function recheckListBak(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $batch_no = request()->post('batch_no', '');
        $sid = request()->post('sid', '');
        $tel = request()->post('tel', '');
        $offset = ($page - 1) * $limit;
        $where = ['delete_at' => 0];
        if($batch_no){
            $where['batch_no'] = $batch_no;
        }
        if($sid){
            $where['sid'] = $sid;
        }
        if($tel){
            $where['tel'] = $tel;
        }

        $res = NetTimeRecheck::getList($where, $offset, $limit);
        foreach ($res['data'] as &$val){
            $val['channel_name'] = RedisCache::instance('channelId_label_mapping')->get($val['channel_id']);
            $val['product_name'] = RedisCache::instance('productId_productName_mapping')->get($val['product_id']);
            if($val['status'] == 1){
                $val['status'] = '已复核';
            }else{
                $val['status'] = '未复核';
            }
            $val['tel'] = '';
        }

        return $res;
    }

    public function recheckDel(){
        $id = request()->get('id', '');
        $check = $this->init();
        if($check['code'] > 0){
            return $check;
        }

        $sysName = $check['sysName'];

        if(intval($id) == 0){
            return ['msg' => '非法操作', 'code'=>50003 , 'data'=>[]];
        }

        try{
            $up = NetTimeRecheck::where(['id' => $id])->update(['delete_at' => time(), 'admin' => $sysName]);
            if($up){
                return ['msg' => '删除成功', 'code'=> 0 , 'data'=>[]];
            }

            return ['msg' => '删除失败', 'code'=> 5005 , 'data'=>[]];
        }catch (Exception $e){
            $this->createBaseResponse("删除数据失败:".$e->getMessage(), 5006);
        }

    }

    public function getSelfRuleMonitorList($params, $page, $size)
    {
        $account_name = $params['account_name'] ?? '';
        $apikey = $params['apikey'] ?? '';
        $delay_days = $params['delay_days'] ?? '';
        $customer_id = $params['customer_id'] ?? '';
        $self_channel_rule = $params['self_channel_rule'] ?? '';
        $product_id = $params['product_id'] ?? '';

        $where = [];
        $where[] = ['status', '=', 1];
        if ($account_name) {
            $where[] = ['account_name', 'like', "%$account_name%"];
        }

        if ($apikey) {
            $where[] = ['apikey', 'like', "%$apikey%"];
        }

        if ($delay_days) {
            $delay_day_arr = explode("_", $delay_days);
            $min_day = $delay_day_arr[0];
            $max_day = $delay_day_arr[1];
            if ($min_day) {
                $where[] = ['delay_day', '>=', $min_day];
            }

            if ($max_day) {
                $where[] = ['delay_day', '<=', $max_day];
            }
        }

        if ($customer_id) {
            $where[] = ['customer_id', '=', $customer_id];
        }

        if ($product_id) {
            $where[] = ['pids', '=', $product_id];
        }

        $where_in_list = [];
        if ($self_channel_rule) {
            $where_in_list[] = ['self_channel_rule', $self_channel_rule];
        }

        $offset = ($page - 1) * $size;
        $res = SelfChannelRuleMonitorLog::getList($where, $where_in_list, $offset, $size);

        // 批量获取产品名称
        $product_name_map = [];
        $product_ids = array_column($res['data'], 'pids');
        if ($product_ids) {
            $product_name_list = Product::getProductListByProductIds($product_ids);
            foreach ($product_name_list as $product_name_item) {
                $product_name_map[$product_name_item['product_id']] = $product_name_item['product_name'];
            }
        }

        $list = [];
        foreach ($res['data'] as $datum) {
            $item = [];
            $item['apikey'] = $datum['apikey'];
            $item['account_name'] = $datum['account_name'];
            $product_id = $datum['pids'];
            $item['self_channel_rule'] = $this->self_channel_rule_map[$datum['self_channel_rule']] ?? '';
            $item['product'] = $product_id . '-' . ($product_name_map[$product_id] ?? '');
            $item['product_id'] = $datum['pids'];
            $item['lastest_change_time'] = $datum['lastest_change_time'] ? date("Y-m-d", $datum['lastest_change_time']) : '无';
            $item['opening_time'] = $datum['opening_time'] ? date("Y-m-d", $datum['opening_time']) : '无';
            $item['delay_day'] = $datum['delay_day'];
            if ($datum['self_channel_rule'] == self::SELF_CHANNEL_RULE_THRESHOLD_TWO) {
                $item['delay_day'] = '--';
            }
            
            $account_id = $datum['account_id'];
            $item['modify_url'] = "https://fin-manage.dianhua.cn/#/show/userProduct/config?account_id=${account_id}&product_id=${product_id}&is_father=0";
            if (empty($datum['shutdown_ratio'])) {
                if (in_array($product_id, $this->net_time_pids)) {
                    $item['shutdown_ratio'] = '--';
                } else {
                    $item['shutdown_ratio'] = '0%';
                }
            } else {
                $item['shutdown_ratio'] = $datum['shutdown_ratio'] . "%";
            }

            if (empty($datum['self_channel_call_ratio'])) {
                $item['self_channel_call_ratio'] = '0%';
            } else {
                $item['self_channel_call_ratio'] = $datum['self_channel_call_ratio'] . '%';
            }

            $item['two_week_call_num'] = $datum['two_week_call_num'];
            $list[] = $item;
        }

        return ['list' => $list , 'count' => $res['count']];
    }
}