<?php

namespace App\Http\Repository;


use App\Define\Common;
use App\Models\AccountProductModel;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\CompanyType;
use App\Models\Customer\CustomerGroup;
use App\Models\Monitor\ConfigAntFinancialBatch;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Account;
use App\Models\ConfigOperator;
use App\Models\SystemUser;
use App\Models\DeptGrade;
use App\Models\Dept;
use App\Models\UserChannelConfig;
use App\Providers\RedisCache\RedisCache;

class OptionsRepository extends PublicRepository
{
	/**
	 * 获取可用产品的选择栏
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/11/19 14:50
	 *
	 * @return string
	 */
	public function getProductOption()
	{
		return $this->createOption($this->getProductForOption(), $this->request->get('default', ''));
	}

    public function getApikeyOption()
    {
        $options = $this->request->get('options', true);
        if ($options === true) {
            return $this->createOption($this->getApikeyFroOption(), $this->request->get('default', ''));
        } else {
            $is_with_id = $this->request->get('is_with_id', false);
            if ($is_with_id) {
                return $this->getAccountIdFroOption();
            }
            return $this->getApikeyFroOption();
        }

    }

    /**
     * 获取主产品选择栏
     *
     * @access   public
     * <AUTHOR>
     * @datetime 2020/11/19 14:50
     *
     * @return string
     */
    public function getMainProductOption()
    {
        $options = $this->request->get('options', true);
        if($options === true){
            return $this->createOption($this->getMainProductForOption(), $this->request->get('default', ''));
        }else{
            return $this->getMainProductForOption();//获取不带options数据
        }
    }

	/**
	 * 获取产品的选择栏数据并处理
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/19 17:04
	 *
	 * @return array
	 */
	protected function getProductForOption()
	{
		$data = Product::select(['product_id', 'product_name'])
					   ->where(function ($query) {
						   $is_include_disabled = $this->request->get('is_include_disabled', false);
						   if (!$is_include_disabled) {
							   $query->where('status', '=', 1);
						   }
						   $father_id = $this->request->get('father_id', false);
						   if ($father_id) {
						       $query->where('father_id', '=', $father_id);
                           }
					   })
					   ->where(function ($query) {
						   $type = $this->request->get('type');
						   if ($type) {
							   $query->whereIn('type', explode(',', $type));
						   }
					   })
					   ->get()
					   ->toArray();

		$is_with_id = $this->request->get('is_with_id');
		if (!$is_with_id) {
			return array_column($data, 'product_name', 'product_id');
		}

		$data = array_map(function ($item) {
			$item['product_name'] = "【{$item['product_id']}】{$item['product_name']}";

			return $item;
		}, $data);

		return array_column($data, 'product_name', 'product_id');
	}

	public function getApikeyFroOption()
    {
        $fatherId = $this->request->get('father_id', 0);
        if( !$fatherId ){
            return [];
        }

        //获取父产品下的 子产品id
        $where = [
            ['father_id','=', $fatherId],
        ];
        $accountWhere['product_id'] = Product::getListByCondition($where, ['product_id'])->pluck('product_id')->toArray();
        $accountProductModel = new AccountProductModel([]);
        $apikeyInfoResult = $accountProductModel->getList($accountWhere);

        $haveApikeys = [];
        $result = [];

        foreach ( $apikeyInfoResult as &$v){

            if( in_array($v['apikey'], $haveApikeys) ){
                continue;
            }else{
                $item = [
                    'name' => $v['account_name'],
                    'apikey' => $v['apikey'],
                ];

                $result[] = $item;
                $haveApikeys[] = $v['apikey'];
            }
        }

        return array_column($result, 'name', 'apikey');
    }

    protected function getAccountIdFroOption()
    {
        $fatherId = $this->request->get('father_id', 0);
        if( !$fatherId ){
            return [];
        }

        //获取父产品下的 子产品id
        $where = ['status' => 1];
        $where['product_id'] = Product::getListByCondition([['father_id','=', $fatherId]], ['product_id'])->pluck('product_id')->toArray();
        $result = (new AccountProductModel([]))->getList($where);
        $list = [];
        foreach ($result as $value) {
            $list[$value['account_id']] = $value['account_name'];
        }
        return $list;
    }

    /**
     * 获取可用客户的选择栏
     * @return string
     */
    public function getCustomerOption()
    {
        return $this->createOption($this->getCustomerForOption(), $this->request->get('default', ''));
    }

    /**
     * 获取客户的选择栏数据并处理
     * @return array
     */
    protected function getCustomerForOption()
    {
        $data = Customer::select(['customer_id', 'name'])->get()->toArray();

        if (!$this->request->get('is_with_id')) {
            return array_column($data, 'name', 'customer_id');
        }
        $data = array_map(function ($item) {
            $item['name'] = "【{$item['customer_id']}】{$item['name']}";
            return $item;
        }, $data);
        return array_column($data, 'name', 'customer_id');
    }

    /**
     * 获取可用账号的选择栏
     * @return string
     */
    public function getAccountOption()
    {
        $is_width_id = $this->request->get('is_width_id', false);
        if($is_width_id==true){
            return $this->getAccountIdOption();
        }
        $options = $this->request->get('options', true);//默认带options
        if($options === true){
            return $this->createOption($this->getAccountForOption(), $this->request->get('default', ''));
        }else{

            $where = [
                ['status', '=', 1],
                ['is_delete', '=', 0],
                ['apikey', '!=', ''],
            ];
            return $this->getAccountForOption($where);
        }
    }

    /**
     * 返回account_id 和name的对应关系
     *
     * @param array $where
     * @return void
     */
    protected function getAccountIdOption($where = [])
    {
        return Account::where($where)->pluck('account_name','account_id')->toArray();
    }

    /**
     * 获取账号的选择栏数据并处理
     * @return array
     */
    protected function getAccountForOption($where = [])
    {
        $data = Account::where($where)->select(['apikey', 'account_name'])->get()->toArray();

        if (!$this->request->get('is_with_id')) {
            return array_column($data, 'account_name', 'apikey');
        }
        $data = array_map(function ($item) {
            $item['account_name'] = "【{$item['apikey']}】{$item['account_name']}";
            return $item;
        }, $data);
        return array_column($data, 'account_name', 'apikey');
    }
    /**
     * 获取主产品的选择栏数据并处理
     * @return array
     */
    protected function getMainProductForOption()
    {
        $data = Product::select(['product_id', 'product_name'])->where([['search_show', 1],['father_id',0]])->orWhere('father_id',401)->orderByRaw('sort desc')->get()->toArray();

        if (!$this->request->get('is_with_id')) {
            return array_column($data, 'product_name', 'product_id');
        }
        $data = array_map(function ($item) {
            $item['product_name'] = "【{$item['product_id']}】{$item['product_name']}";
            return $item;
        }, $data);
        return array_column($data, 'product_name', 'product_id');
    }

    /**
     * 获取渠道的选择栏
     * @return string
     */
    public function getChannelOption()
    {
        $bxf = $this->request->get('bxf', '');
        if(empty($bxf)){
            return $this->createOption($this->getChannelForOption(), $this->request->get('default', ''));
        }else{
            return $this->geBxftChannelForOption();//只获取获取邦信分渠道列表
        }
    }

    ////只获取获取邦信分渠道列表
    protected function geBxftChannelForOption()
    {

        $data = ChannelProduct::getBxfChannelList([210, 1000]);
        return $data;
    }

    /**
     * 获取渠道的选择栏数据并处理
     * @return array
     */
    protected function getChannelForOption()
    {
        $product_id = $this->request->get('father_id', false);
        if ($product_id) {
            $data = ChannelProduct::getChannelInfoByFidNew($product_id);
        } else {
            $data = Channel::select(['label', 'channel_id'])->get()->toArray();
        }
        return array_column($data, 'label', 'channel_id');
    }
    /**
     * 获取接口的选择栏
     * @return string
     */
    public function getInterfaceOption()
    {
        return $this->createOption($this->getInterfaceForOption(), $this->request->get('default', ''));
    }

    /**
     * 获取接口的选择栏数据并处理
     * @return array
     */
    protected function getInterfaceForOption()
    {
        $product_id = $this->request->get('father_id', false);
        $data = [];
        if ($product_id) {
            $channel = ChannelProduct::getChannelInfoByFidNew($product_id);
            $channel_id = array_column($channel, 'channel_id');
            if ($channel_id) {
                $data = ChannelInterface::getListByCondition([], ['id','label'], $channel_id)->toArray();
            }
        } else {
            $data = ChannelInterface::getListByCondition([], ['id','label'])->toArray();
        }
        return array_column($data, 'label', 'id');
    }
    /**
     * 获取可用运营商的选择栏
     * @return string
     */
    public function getOperatorOption()
    {
        return $this->createOption($this->getOperatorForOption(), $this->request->get('default', ''));
    }

    /**
     * 获取运营商的选择栏数据并处理
     * @return array
     */
    protected function getOperatorForOption()
    {
        $data = ConfigOperator::select('operator', 'name')->get()->toArray();
        return array_column($data, 'name', 'operator');
    }

	/**
	 * 获取产品类型的选择栏
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/11/19 14:50
	 *
	 * @return string
	 */
	public function getProductTypeOption()
	{
		return $this->createOption(config('params.product_type'), $this->request->get('default', ''));
	}

	/**
	 * 获取蚂蚁跑批监控、批次选择框数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12/4 14:10
	 *
	 * @return string
	 */
	public function getAntFinancialMonitorBatchOption()
	{
		//按ID倒序
		$data = ConfigAntFinancialBatch::orderBy('id', 'desc')
									   ->pluck('name', 'id')
									   ->toArray();

		return $this->createOption($data, $this->request->get('default', ''));
	}

	/**
	 * 获取蚂蚁跑批监控、产品选择框数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12/4 14:10
	 *
	 * @return string
	 */
	public function getAntFinancialMonitorProductOption()
	{
		$data = [
			280 => '【280】近30天催收号码数',
			281 => '【281】近30天被叫催收次数',
		];

		return $this->createOption($data, $this->request->get('default', ''));
	}

	/**
	 * 获取蚂蚁跑批监控、渠道选择框数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12/7 15:04
	 *
	 * @return string
	 */
	public function getAntFinancialMonitorChannelOption()
	{
		$data = Channel::select(['channel.channel_id', 'channel.label'])
					   ->leftJoin('channel_product', 'channel_product.channel_id', '=', 'channel.channel_id')
					   ->where('channel_product.product_id', 210)
					   ->get()
					   ->toArray();

		return $this->createOption(array_column($data, 'label', 'channel_id'), $this->request->get('default', ''));
	}

    public function getCustomerMap($params = '',$include_group_id)
    {
        $millisecond1 = $this->getMillisecond();
        $data = Customer::select(['customer_id', 'name','group_id'])->where([['is_delete','=','0']])->get()->toArray();
        $millisecond2 = $this->getMillisecond();
        if($params == 'test'){
            echo $millisecond2 - $millisecond1;exit;
        }

        if($include_group_id){
            return array_column($data, null, 'customer_id');
        }else{
            return array_column($data, 'name', 'customer_id');
        }
    }


    /**
     * 根据group_id获取客户
     *
     * @param $group_id
     *
     * @return array
     * <AUTHOR> 2025-02-13 17:11:23
     */
    public function getcustomerbygroupid($group_id)
    {
        $where =[['is_delete','=','0']];
        if(!empty($group_id)){// group_id 如果为空 查询所有
            $where[] = ['group_id','=',$group_id];
        }
        $data = Customer::select(['customer_id', 'name','group_id'])->where($where)->get()->toArray();

        return array_column($data, 'name', 'customer_id');
    }

    public function getCustomerGroupMap(){

        $where =[
            ['is_delete','=','0'],
            ['group_id','!=',''],
        ];
        $data = Customer::select(['customer_id', 'group_id'])->where($where)->get()->toArray();
        return array_column($data, 'group_id', 'customer_id');
    }


    /**
     * 获取客户id和客户类型及名称的映射
     * @return array
     */
    public function getCustomerIdTypeMap()
    {
        $data = Customer::select(['customer_id', 'customer_type'])->get()->toArray();

        $map = [];
        array_walk($data, function($val) use(&$map){
            $map[$val['customer_id']]['customer_type'] = $val['customer_type'];
            //TODO enum
            if($val['customer_type']==1){
                $map[$val['customer_id']]['type_name'] = '金融用户';
            }elseif($val['customer_type']==2){
                $map[$val['customer_id']]['type_name'] = '企服用户';
            }
        });
        return $map;
    }

    /**
     * 客户所属销售和销售人名
     *
     * @return void
     */
    public function getCustomerIdSalesmanMap(){
        $data = Customer::select(['customer_id', 'salesman'])->get()->toArray();
        //salesman 有可能为空
        $user_data = SystemUser::pluck('realname','username')
        ->toArray();
        $map = [];
        array_walk($data, function($val) use(&$map,$user_data){
            $map[$val['customer_id']]['salesman'] = !empty($val['salesman'])?$val['salesman']:'';
            $map[$val['customer_id']]['realname'] = isset($user_data[$val['salesman']])?$user_data[$val['salesman']]:'';
        });
        return $map;
    }

    public function getMillisecond()
    {
        [$t1, $t2] = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
    }

    public function getProductMap()
    {
        $data = Product::select(['product_id', 'product_name'])->get()->toArray();
        return array_column($data, 'product_name', 'product_id');
    }
    public function getAccountMap()
    {
        $data = Account::select(['apikey', 'account_name'])->get()->toArray();
        return array_column($data, 'account_name', 'apikey');
    }
    public function getAccountIdMap() {
        $data = Account::select(['account_id', 'account_name'])->get()->toArray();
        return array_column($data, 'account_name', 'account_id');
    }

    public function getGroupMap() {
        $data = CustomerGroup::select(['group_id', 'group_name'])->get()->toArray();
        return array_column($data, 'group_name', 'group_id');
    }

    public function getCldcAccountMap()
    {
        $accountProductModel = new AccountProductModel([]);
        $data = $accountProductModel->getAccountByProduct(CLDC_PRODUCT);
        $account_ids = array_column($data, 'account_id');
        $data = Account::select(['apikey', 'account_name'])->whereIn('account_id', $account_ids)->get()->toArray();

        return array_column($data, 'account_name', 'apikey');
    }

    public function getOperatorMap()
    {
        $data = ConfigOperator::select(['operator', 'name'])->get()->toArray();
        return array_column($data, 'name', 'operator');
    }

    public function getCompanyTypeMap($company_type){
        $data = CompanyType::getCompanyTypeMap($company_type);
        return $data;
    }

    public function getChannelMap($user = '', $is_all_channel = '')
    {
        $data = [];
        if(!empty($user)) {
            $data = UserChannelConfig::getChannelIdsByUser($user);
            $channel_ids = array_column($data,'channel_id');
            if(in_array(-1 ,$channel_ids)) {
                $data = Channel::select(['label', 'channel_id'])->get()->toArray();
            }else {
                $data = Channel::getChannelByChannelIdS($channel_ids);
            }
        }else {
            $data = Channel::getAllChannel($is_all_channel);
        }
        return array_column($data, 'label', 'channel_id');

    }

    public function getCldcChannelMap()
    {

        $data = ChannelProduct::getCldcChannelInfoByProductId([30000, 1200]);


        return array_column($data, 'label', 'channel_id');

    }

    public function getInterfaceMap()
    {
        $data = ChannelInterface::select(['id', 'label'])->get()->toArray();
        return array_column($data, 'label', 'id');
    }


    public function getCldcInterfaceMap($channel_id = '')
    {
        if ($channel_id && $channel_id != 'null') {
            $data = ChannelInterface::select(['id', 'label'])->where('channel_id', $channel_id)->get()->toArray();
            return array_column($data, 'label', 'id');
        } else {
            $data = ChannelProduct::getCldcChannelInfoByProductId([30000, 1200]);
            $channel_ids = array_column($data, 'channel_id');
            $data = ChannelInterface::getInterfaceByChannelIds($channel_ids);
        }
        return array_column($data, 'label', 'id');
    }


    public function getCldcInterfaceList()
    {

        $data = ChannelProduct::getListByPids([30000, 1200]);
        $channel_ids = array_column($data, 'channel_id');
        $data = ChannelInterface::getInterfaceByChannelIds($channel_ids);
        return array_column($data, 'channel_id', 'id');
    }

    public function getMainProductMap()
    {
        $data = Product::select(['product_id', 'product_name'])->where([['father_id', '=', 0]])->orWhere('father_id', 401)->get()->toArray();
        return array_column($data, 'product_name', 'product_id');
    }
    public function getMainProductList()
    {
        $data = Product::select(['product_id', 'product_name'])->where([['father_id', '=', 0],['search_show', '=', 1]])->orWhere('father_id', 401)->orderByRaw('sort desc')->get()->toArray();
        return $data;
    }

    public function getProductList()
    {
        return Product::select(['product_id', 'product_name'])->where([['status', '=', 2]])->orWhere('father_id', 401)->orderByRaw('sort desc')->get()->toArray();
    }

    public function getCldcProduct()
    {
        $data = Product::select(['product_id', 'product_name'])->whereIn('product_id', CLDC_PRODUCT)->orderByRaw('sort desc')->get()->toArray();
        return array_column($data, 'product_name', 'product_id');
    }

    public function getSourceList()
    {
        $data = [['name' => Common::COMPANY_CN_NAME, 'source' => 0]];
        $list = Account::select(['name', 'user_agent_number as source'])->join('customer', 'customer.customer_id', 'account.customer_id')->where([['user_agent_number', '>' , 0], ['account.customer_id', '!=', 'C20180828LOCNMG']])->get()->toArray();
        $data = $list ? array_merge($data, $list) : $data;
        return $data;
    }

    //获取登录用户的姓名获取用户的相关数据
    public function getUserAuth($sysName)
    {
        $userData = [];
        //获取销售的所有部门
        $userInfo = SystemUser::getUserInfoByName($sysName);
        //获取所有的销售部门
        //$dept = DeptGrade::getSaleDept();
        $dept = DeptRepository::getSaleDeptRecursion($userInfo['dept_id'],$userInfo['is_leader']);

        //判断是否是销售人员
        $userData['is_sale'] = $dept['is_sale'];

        //是否是领导
        $userData['is_leader'] = $userInfo['is_leader'];
        $userData['dept_id'] = $userInfo['dept_id'];
        //$userData['uid'] = $userInfo['id'];
        //数据权限
        $userData['data_auth'] = $userInfo['data_auth'];

        //获取销售地域及相关销售人员
        $userData['dept_info'] = $dept['dept_infos'];
        $userData['area_person'] = DeptRepository::getSalesmanDeptMap();
        $userData['user_name'] = $sysName;

        //勇哥只查看金融收入 和对应的金融毛利；其他人根据权限能看到收入、金融收入、毛利
        if($userInfo['id'] == 40){
        //if($userInfo['id'] == 128){
            $userData['finance_income_auth'] = true;
        }else{
            $userData['finance_income_auth'] = false;
        }

        return $userData;
    }

    /**
     *
     *
     * @return array
     * <AUTHOR> 2024-03-20 21:35:03
     *
     */
    public function getDeptList()
    {
        $userData = [];
        //获取销售的所有部门
        //获取所有的销售部门
        //$dept = DeptGrade::getSaleDept();
        $dept = DeptRepository::getSaleDeptRecursion('',false,true);

        $userData['dept_list'] = $dept['dept_infos'];

        return $userData;
    }

    public function getFinanceAuth(){
        return ( new StatBaseRepository())->getFinanceAuthProduct();
    }

    /**
     * 获取公司名称
     *
     * @return array
     * <AUTHOR> 2025-03-11 14:53:48
     *
     */
    public function getCompanyMap() {
        $data = Customer::select(['company'])->get()->toArray();
        return array_unique(array_column($data, 'company'));
    }

    private function getAreaperson($dept_ids)
    {
        $area_person = [];
        array_walk($dept_ids,function ($dept_id)use(&$area_person){
              $area =Dept::getDeptById($dept_id);
              $person = SystemUser::getUserInfoByDeptId($dept_id);
              array_push($area_person,['area'=>$area,'person'=>$person]);
        });

        return $area_person;
    }

    //主产品下对应的子产品列表
    public function getChildrenProductList(){
        $father_id = $this->request->get('father_id', '');
        $father_ids = Product::getGeneralProduct();//获取所有没有子产品的产品(父产品)

        $father_ids = array_column($father_ids, 'product_id');
        if(in_array($father_id, $father_ids)){//如果在这个里面，则子产品就是父产品本身
            $p_name = RedisCache::instance('productId_productName_mapping')->get($father_id);
            $data = [['product_id' => $father_id, 'product_name' => $p_name ]];
            return array_column($data, 'product_name', 'product_id');

        }

        $data = Product::getListByCondition(['father_id' => $father_id], ['product_id', 'product_name'])->toArray();
        return array_column($data, 'product_name', 'product_id');
    }


}