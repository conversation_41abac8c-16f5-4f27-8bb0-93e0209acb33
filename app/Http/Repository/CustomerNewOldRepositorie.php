<?php
namespace App\Http\Repository;

use App\Define\Common;
use App\Models\Customer;
use App\Models\Customer\CustomerGroup;
use App\Models\Customer\CustomerNewOldCollect;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\BaseRepositorie;
use App\Utils\Helpers\Func;
use Exception;


/**
 * 新老客户
 */
class CustomerNewOldRepositorie extends BaseRepositorie
{


    /**
     * 构造函数
     */
    public function __construct(){

    }

    private static $collect_type_map = [
        Common::CUSTOMER_NEW_OLD_COLLECT_TYPE_CUSTOMER => '客户',
        Common::CUSTOMER_NEW_OLD_COLLECT_TYPE_GROUP    => '主体',
    ];

    private static $is_new_customer_map = [
        Common::CUSTOMER_NEW_OLD_NEW_CUSTOMER => '新客户',
        Common::CUSTOMER_NEW_OLD_OLD_CUSTOMER => '老客户',
    ];

    private static $is_new_product_map = [
        Common::CUSTOMER_NEW_OLD_NEW_PRODUCT => '新产品',
        Common::CUSTOMER_NEW_OLD_OLD_PRODUCT => '老产品',
    ];

    private static $customer_disable_map = [
        Common::CUSTOMER_NEW_OLD_CUSTOMER_DISABLE => '流失',
        Common::CUSTOMER_NEW_OLD_CUSTOMER_ABLE    => '续约',
    ];

    public static function options(){
        $res['collect_type'] = [[
                'value' => -1,
                'label' => '全部',
            ], [
                'value' => Common::CUSTOMER_NEW_OLD_COLLECT_TYPE_CUSTOMER,
                'label' => '客户',
            ], [
                'value' => Common::CUSTOMER_NEW_OLD_COLLECT_TYPE_GROUP,
                'label' => '主体',
            ],
        ];

        $res['is_new_customer'] = [
            [
                'value' => - 1,
                'label' => '全部',
            ], [
                'value' => Common::CUSTOMER_NEW_OLD_NEW_CUSTOMER,
                'label' => '新客户',
            ], [
                'value' => Common::CUSTOMER_NEW_OLD_OLD_CUSTOMER,
                'label' => '老客户',
            ],
        ];

        // $res['is_new_product'] = [[
        //         'value' => -1,
        //         'label' => '全部',
        //     ], [
        //         'value' => Common::CUSTOMER_NEW_OLD_NEW_PRODUCT,
        //         'label' => '新产品',
        //     ], [
        //         'value' => Common::CUSTOMER_NEW_OLD_OLD_PRODUCT,
        //         'label' => '老产品',
        // ],];

        $customer_list = Customer::getAllCustomer(['customer_id','name']);
        $group_list = CustomerGroup::getGroupList();
        $res['customer_group_list'] = [];
        $res['customer_group_list'][] = [
            'value' => -1,
            'label' => '全部',
        ];
        foreach($customer_list as $c_info) {
            $res['customer_group_list'][] = [
                'value' => $c_info['customer_id'],
                'label' => $c_info['name'] .'-客户',
            ];
        }
        foreach($group_list as $g_info) {
            $res['customer_group_list'][] = [
                'value' => $g_info['group_id'],
                'label' => $g_info['group_name'] . '-主体',
            ];
        }

        return $res;
    }


    /**
     * 列表条件
     *
     * @param $params
     *
     * @static
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-03-04 14:19:33
     */
    private static function get_list_where($params){
        $where = [];

        $collect_type    = $params['collect_type'];
        $rel_id          = $params['rel_id'];
        $month           = $params['month'];
        $is_new_customer = $params['is_new_customer'];


        if(!empty($collect_type) && $collect_type != -1){
            $where[] = ['collect_type', '=', $collect_type];
        }
        if(!empty($rel_id) && $rel_id != -1){
            $where[] = ['rel_id', '=', $rel_id];
        }
        if(!empty($is_new_customer) && $is_new_customer != -1){
            $where[] = ['is_new_customer', '=', $is_new_customer];
        }

        if(!empty($month)){
            $where[] = ['month', '=', $month];
        }

        //按主产品查询
        $where[] = ['father_id', '=', 0];

        return $where;
    }


    /**
     * 获取列表
     *
     * @param $params
     *
     * @return array
     * @throws Exception
     * @static
     * <AUTHOR> 2024-02-21 19:15:40
     */
    public static function get_list($params) {
        $page              = $params['page'];
        $page_size         = $params['page_size'];

        $where = self::get_list_where($params);

        $_list = CustomerNewOldCollect::getListByCondition('*',$where,$page,$page_size);

        $rel_id_arr = array_column($_list,'rel_id');
        $month_arr = array_column($_list,'month');
        $product_id_arr = array_column($_list,'product_id');

        $children_list = CustomerNewOldCollect::getChildrenList($params,$rel_id_arr,$month_arr,$product_id_arr);
        $children_list = Func::column_with_multiple_fields($children_list,['collect_type','rel_id','month','father_id']);

        $list = [];
        foreach ($_list as $item) {
            $tmp_key = Func::get_tmp_key($item['collect_type'], $item['rel_id'], $item['month'], $item['product_id']);
            if (key_exists($tmp_key, $children_list)) {
                foreach ($children_list[$tmp_key] as &$info) {
                    if ($item['is_new_product'] == Common::CUSTOMER_NEW_OLD_NEW_PRODUCT) {
                        if ($info['is_new_product'] == Common::CUSTOMER_NEW_OLD_OLD_PRODUCT) {
                            $item['is_new_product'] = Common::CUSTOMER_NEW_OLD_OLD_PRODUCT;
                            break;
                        }
                    }
                    $info['collect_type']        = self::$collect_type_map[$info['collect_type']];
                    $info['is_new_customer']     = self::$is_new_customer_map[$info['is_new_customer']];
                    $info['is_new_product']      = self::$is_new_product_map[$info['is_new_product']];
                    $info['product_name']        = RedisCache::instance('productId_productName_mapping')->get($info['product_id']);
                    $info['customer_start_date'] = date('Y-m-d', strtotime($item['customer_start_date']));
                    $info['product_start_date']  = date('Y-m-d', strtotime($item['product_start_date']));
                    $info['customer_disable']    = '';
                    $info['disable_date']        = empty($info['disable_date'])?'':date('Y-m-d', strtotime($info['disable_date']));
                    $info['disable_status_time'] = $item['disable_status_time'];
                }
                $item['children'] = $children_list[$tmp_key];
            }

            //客户开通后一年后才是续约客户
            if (($item['customer_disable'] == Common::CUSTOMER_NEW_OLD_CUSTOMER_ABLE) &&
                ($item['month'] - date('Ym', strtotime($item['customer_start_date'])) <= 100)) {
                $item['customer_disable'] = '';
            } else {
                $item['customer_disable'] = self::$customer_disable_map[$item['customer_disable']];
            }

            $item['collect_type']        = self::$collect_type_map[$item['collect_type']];
            $item['is_new_customer']     = self::$is_new_customer_map[$item['is_new_customer']];
            $item['is_new_product']      = self::$is_new_product_map[$item['is_new_product']];
            $item['product_name']        = RedisCache::instance('productId_productName_mapping')->get($item['product_id']);
            $item['customer_start_date'] = date('Y-m-d', strtotime($item['customer_start_date']));
            $item['product_start_date']  = date('Y-m-d', strtotime($item['product_start_date']));
            $item['disable_date']        = empty($item['disable_date'])?'':date('Y-m-d', strtotime($item['disable_date']));
            $item['disable_status_time'] = $item['disable_status_time']??'';

            $list[] = $item;
        }

        return $list;
    }


    /**
     *
     *
     * @param $params
     *
     * @static
     * @return int
     * @throws Exception
     * <AUTHOR> 2024-03-04 14:19:54
     */
    public static function get_count($params) {
        $where = self::get_list_where($params);
        return CustomerNewOldCollect::getCountByCondition($where);
    }

}
