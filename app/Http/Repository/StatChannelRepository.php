<?php

namespace App\Http\Repository;

use App\Http\Controllers\StatProductController;
use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\ChannelInterface;
use App\Models\ChannelProduct;
use App\Models\StatisticsInterfaceUsage;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Providers\RedisCache\RedisCache;
use App\Utils\Helpers\Func;


class StatChannelRepository extends StatBaseRepository
{
    /**
     * 数据统计-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $show_money = $this->checkUserProductAuth($params['father_id'],'cost');  //是否有权限查看产品成本金额
        $channel_ids = $this->getChannelIds($params); //主产品对应的所有渠道
        $interface_ids = $this->getIids($params, $channel_ids);   //渠道下所有的iid(接口id)
        $usage_data = $this->getUsage($params, $interface_ids);   //调用量
        $cost_data = $this->getCost($params,$interface_ids);   //成本
        $expend_cost_data = $this->getExpendCost($params,$interface_ids); //特殊消耗成本(成本调整)
        $fixed_cost_data = $this->getFixedCost($params,$interface_ids); //固定费用成本

        $data = $this->formatData($usage_data, $cost_data, $show_money, $expend_cost_data, $fixed_cost_data);
        return $data;
    }

    protected function getChannelIds($params){
        $where['product_id'] = $params['father_id'];
        isset($params['channel']) && $where['channel_id'] = $params['channel'];
        return array_column(ChannelProduct::getListByCondition($where, ['channel_id'])->toArray(), 'channel_id');
    }

    protected function getIids($params, $channel_ids) {
        $where = [];
        isset($params['interface']) && $where['id'] = $params['interface'];
        return array_column(ChannelInterface::getListByCondition($where, ['id'], $channel_ids)->toArray(), 'id');
    }

    protected function getUsage($params, $interface_ids=null) {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsInterfaceUsage::getStatList($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $usage_data;
    }

    protected function getCost($params, $interface_ids=null){
        $where = $this->getCostWhere($params);
        $cost_data = BillCostv2::getChannelStatList($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected function getExpendCost($params, $interface_ids=null){
        //由于ChannelAccountAdjust中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelStatList($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected  function getFixedCost($params){
        //由于ChannelAccountFixedFee中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        if(isset($params['channel']) && !empty($params['channel'])){
            $where[] = ['channel_id', '=', $params['channel']];
        }

        if(isset($params['operator']) && !empty($params['operator'])){
            $where[] = ['operator', '=', $params['operator']];
        }

        $cost_data = ChannelAccountFixedFee::getChannelStatList($where, $params);
        return $cost_data;

    }

//    protected function getUsageWhere($params)
//    {
//        $where = [];
//        isset($params['start_date']) && isset($params['end_date'])
//        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
//        isset($params['operator']) && $where[] = ['operator', $params['operator']];
//        return $where;
//    }
//    protected function getCostWhere($params)
//    {
//        $where = [];
//        isset($params['start_date']) && isset($params['end_date'])
//        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
//        isset($params['operator']) && $where[] = ['operator', $params['operator']];
//        return $where;
//    }


    /**
     * 组装最终数据
     * @param $usage_data
     * @param $cost_data
     * @param $show_money
     * @return mixed
     * @throws \Exception
     */
    private function formatData($usage_data, $cost_data, $show_money, $expend_cost_data = [], $fixed_cost_data = [])
    {
        $data = [];
        foreach ($usage_data as $item) {
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
            $item['channel_id'] = $channel_id;
            $data[$channel_id][$item['interface_id']][$item['operator']] = $item;
            $show_money || $data[$channel_id][$item['interface_id']][$item['operator']]['cost'] = '-';
        }
        if($show_money) {
            foreach ($cost_data as $item){
                $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);
                if (isset($data[$channel_id][$item['interface_id']][$item['operator']])){
                    $data[$channel_id][$item['interface_id']][$item['operator']]['cost'] = $item['money'];
                }
            }

            //特殊消耗成本
            foreach ($expend_cost_data as &$item) {
                if($item['interface_id'] == 0){
                    continue;
                }

                $channel_id = RedisCache::instance('iid_channelId_mapping')->get($item['interface_id']);

                $interface_id = 'expend';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射

                $total_cost = $data[$channel_id][$interface_id][$operator]['cost'] ?? 0;
                $total_cost = bcadd($total_cost, $item['money'], 6);
                $data[$channel_id][$interface_id][$operator]['cost'] = $total_cost;
                $data[$channel_id][$interface_id][$operator]['success'] = 0;
                $data[$channel_id][$interface_id][$operator]['total'] = 0;
                $data[$channel_id][$interface_id][$operator]['valid'] = 0;

            }

            //固定费用成本
            foreach ($fixed_cost_data as &$item) {
                $channel_id = $item['channel_id'];

                $interface_id = 'fixed';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射

                $total_cost = $data[$channel_id][$interface_id][$operator]['cost'] ?? 0;
                $total_cost = bcadd($total_cost, $item['money'], 6);
                $data[$channel_id][$interface_id][$operator]['cost'] = $total_cost;
                $data[$channel_id][$interface_id][$operator]['success'] = 0;
                $data[$channel_id][$interface_id][$operator]['total'] = 0;
                $data[$channel_id][$interface_id][$operator]['valid'] = 0;

            }

        }

        return $data;
    }


    /**
     * 月度数据统计-渠道维度
     *
     * @param $params
     */
    public function statMonthList($params)
    {
        //设置返回表格的表头
        $arr['result']['header']['list'] = [
            "channel_name" => "渠道名称",
            "cost" => "数据成本",
        ];

        $cost_data = BillCost::getChannelMonthCost($params);//渠道月成本数据
        if(empty($cost_data)){
            $arr['result']['items'] = [];
            return $arr;
        }

        $total_cost = 0;//总计成本
        foreach ($cost_data as $key => $val){
            $channel_label = RedisCache::instance('channelId_label_mapping')->get($val['channel_id']);
            $cost = bcadd($val['s_money'], 0, $this->degree);
            $total_cost = bcadd($total_cost, $cost, $this->degree);
            $arr['result']['items'][] = [
                'channel_name' => $channel_label,
                'cost_sort' => $cost,
                'cost' => number_format($cost, 2),
            ];
        }

        $cost_sort = array_column($arr['result']['items'], 'cost_sort');
        array_multisort($cost_sort,SORT_DESC, $arr['result']['items']);

        if(!empty($cost_data)){
            array_unshift($arr['result']['items'], ['channel_name' => '总计', 'cost' => number_format($total_cost, 2)]);
        }

        return $arr;
    }




    /**
     * 数据统计-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statCompareList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $show_money = $this->checkUserProductAuth($params['father_id'],'cost');  //是否有权限查看产品成本金额
        $channel_ids = $this->getChannelIds($params); //主产品对应的所有渠道
        $interface_ids = $this->getIids($params, $channel_ids);   //渠道下所有的iid(接口id)

        $usage_data = $this->getCompareUsage($params, $interface_ids);   //调用量
        $cost_data = $this->getCompareCost($params,$interface_ids);   //成本
        $expend_cost_data = $this->getCompareExpendCost($params,$interface_ids); //特殊消耗成本(成本调整)
        $fixed_cost_data = $this->getCompareFixedCost($params,$interface_ids); //固定费用成本

        return $this->formatCompareData($usage_data, $cost_data, $show_money, $expend_cost_data, $fixed_cost_data);
    }

    protected function getCompareUsage($params, $interface_ids=null) {
        $where = $this->getUsageWhere($params);
        $usage_data = StatisticsInterfaceUsage::getStatListV4($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $usage_data;
    }

    protected function getCompareCost($params, $interface_ids=null){
        $where = $this->getCostWhere($params);
        $cost_data = BillCostv2::getChannelStatListV4($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected function getCompareExpendCost($params, $interface_ids=null){
        //由于ChannelAccountAdjust中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelStatListV4($where, $params['product_list'], $interface_ids, $params['filter_apikey']);
        return $cost_data;
    }

    protected  function getCompareFixedCost($params){
        //由于ChannelAccountFixedFee中date是带'-'格式的,在这里提前转化一下
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        if(isset($params['channel']) && !empty($params['channel'])){
            $where[] = ['channel_id', '=', $params['channel']];
        }

        if(isset($params['operator']) && !empty($params['operator'])){
            $where[] = ['operator', '=', $params['operator']];
        }

        $cost_data = ChannelAccountFixedFee::getChannelStatListV4($where, $params);
        return $cost_data;

    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $cost_data
     * @param $show_money
     * @return mixed
     * @throws \Exception
     */
    private function formatCompareData($usage_data, $cost_data, $show_money, $expend_cost_data = [], $fixed_cost_data = [])
    {
        // dd($expend_cost_data);
        $data = [];
        foreach ($usage_data as $item) {
            $date = $item['date'];
            $data[$date][$item['interface_id']][$item['operator']] = $item;
            $show_money || $data[$date][$item['interface_id']][$item['operator']]['cost'] = '-';
        }
        if($show_money) {
            foreach ($cost_data as $item){
                $date = $item['date'];
                if (isset($data[$date][$item['interface_id']][$item['operator']])){
                    $data[$date][$item['interface_id']][$item['operator']]['cost'] = $item['money'];
                }
            }

            //特殊消耗成本
            foreach ($expend_cost_data as &$item) {
                if($item['interface_id'] == 0){
                    continue;
                }

                $date = str_replace('-', '', $item['date']);

                $interface_id = 'expend';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射

                $total_cost = $data[$date][$interface_id][$operator]['cost'] ?? 0;
                $total_cost = bcadd($total_cost, $item['money'], 6);
                $data[$date][$interface_id][$operator]['cost'] = $total_cost;
                $data[$date][$interface_id][$operator]['success'] = 0;
                $data[$date][$interface_id][$operator]['total'] = 0;
                $data[$date][$interface_id][$operator]['valid'] = 0;

            }

            //固定费用成本
            foreach ($fixed_cost_data as &$item) {
                $date = $item['date'];

                $interface_id = 'fixed';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射

                $total_cost = $data[$date][$interface_id][$operator]['cost'] ?? 0;
                $total_cost = bcadd($total_cost, $item['money'], 6);
                $data[$date][$interface_id][$operator]['cost'] = $total_cost;
                $data[$date][$interface_id][$operator]['success'] = 0;
                $data[$date][$interface_id][$operator]['total'] = 0;
                $data[$date][$interface_id][$operator]['valid'] = 0;
            }
        }

        ksort($data);

        return $data;
    }

}