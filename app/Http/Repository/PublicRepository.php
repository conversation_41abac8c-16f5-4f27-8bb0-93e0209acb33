<?php

namespace App\Http\Repository;


class PublicRepository
{
	/**
	 * @var array|\Illuminate\Http\Request|string 请求对象
	 */
	protected $request;
	
	/**
	 * @var array 以raw格式通过POST方式传递过来的数据
	 */
	protected $rawJson;
	
	public function __construct()
	{
		$this->request = request();
	}
	
	/**
	 * 获取RAW方式传递的JSON数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/8/26 15:01
	 *
	 * @return array|false
	 */
	protected function getJsonByRaw()
	{
		if (is_null($this->rawJson)) {
			$data = file_get_contents("php://input");
			if (!$data) {
				return false;
			}
			if (!is_json($data)) {
				return false;
			}
			$this->rawJson = json_decode($data, true);
		}
		
		return $this->rawJson;
	}
	
	/**
	 * 创建一个下拉选择框
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/19 16:59
	 *
	 * @param $data    array 数据
	 * @param $default string 默认值
	 *
	 * @return string
	 */
	protected function createOption($data, $default = '')
	{
		$option = '';
		foreach ($data as $value => $name) {
			if (strlen($default) == strlen($value) && $value == $default) {
				$option .= "<option value='{$value}' selected>{$name}</option>";
			} else {
				$option .= "<option value='{$value}'>{$name}</option>";
			}
		}
		
		return $option;
	}
	
	/**
	 * 完整的数据分页,并组装好layui所需的数据格式
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/20 11:38
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	protected function splitPageForCompleteInfo($data)
	{
		$total = count($data);
		$page  = $this->request->post('page');
		$limit = $this->request->post('limit');
		if ($page && $limit) {
			$data  = array_chunk($data, $limit);
			$items = array_get($data, $page - 1, []);
		} else {
			$items = $data;
		}
		
		return compact('items', 'total');
	}
}