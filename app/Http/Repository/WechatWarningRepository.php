<?php

namespace App\Http\Repository;

use EasyWeChat\Work\Application;
use Illuminate\Support\Arr;

class WechatWarningRepository
{
    /*
     * 微信报警队列
     * */
    private $queue_report = 'wechat_warning_report';

    /**
     * 微信报警
     * @throws \Exception
     */
    public function warning()
    {
        $redis = app('redis')->connection('wechat');
        while ($item_warning = $redis->rpop($this->queue_report)) {

            // 检查元素是否合法
            $this->validateWarning($item_warning);

            // 触发报警
            $this->wechatWarning($item_warning);
            usleep(200);
        }
    }

    /**
     * 发送微信报警
     * @param $wechat_warning
     */
    private function wechatWarning($wechat_warning)
    {
        $wechat_warning = json_decode($wechat_warning, true);

        // 实例化wechat对象
        $wechat_object = $this->instanceWechat($wechat_warning);

        // 发送报警
        $wechat_object->messenger
            ->message($wechat_warning['msg'])
            ->toTag($wechat_warning['itag_id'])
            ->ofAgent($wechat_warning['agent_id'])
            ->send();
    }

    /**
     * 获取work wechat实例
     * @param array $wechat_warning 微信报警的中配置
     * @return object
     */
    private function instanceWechat(array $wechat_warning)
    {
        $config_wechat = Arr::only($wechat_warning, ['corp_id', 'agent_id', 'secret']);

        // 检测存储的配置是否合法
        $config_default = [
            'response_type' => 'array',
            'use_laravel_cache' => true,
            'log' => [
                'level' => env('WECHAT_LOG_LEVEL', 'debug'),
                'file' => env('WECHAT_LOG_FILE', storage_path('logs/wechat.log'))
            ]
        ];

        return new Application(array_merge($config_default, $config_wechat));
    }

    /**
     * 检查元素是否合法
     * @param string $item_queue 队列单元
     * @throws \Exception
     */
    private function validateWarning($item_queue)
    {
        // 是否是合法json字符串
        $json_validate = $this->jsonValidate($item_queue);
        if ($json_validate === false) {
            throw new \Exception('填充不是合法的json, 原始数据： ' . $item_queue);
        }

        // 是否函数必要的元素
        $item_warning = json_decode($item_queue, true);
        if (!array_key_exists('itag_id', $item_warning) || !$item_warning['itag_id']) {
            throw new \Exception('缺少必选参数itag_id，原始数据：' . $item_queue);
        }

        // 检测是否含有必须的企业微信的配置
        if (!array_key_exists('corp_id', $item_warning) || !$item_warning['corp_id']) {
            throw new \Exception('缺少必选参数corp_id，原始数据：' . $item_queue);
        }

        if (!array_key_exists('agent_id', $item_warning) || !$item_warning['agent_id']) {
            throw new \Exception('缺少必选参数agent_id，原始数据：' . $item_queue);
        }

        if (!array_key_exists('secret', $item_warning) || !$item_warning['secret']) {
            throw new \Exception('缺少必选参数secret，原始数据：' . $item_queue);
        }

        // 是否函数msg元素
        if (!array_key_exists('msg', $item_warning) || !$item_warning['msg']) {
            throw new \Exception('缺少必选参数msg，原始数据：' . $item_queue);
        }
    }

    /**
     * 判断变量是否是json字符串
     * @param $string
     * @return bool
     */
    private function jsonValidate($string)
    {
        if (is_string($string)) {
            @json_decode($string);
            return (json_last_error() === JSON_ERROR_NONE);
        }
        return false;
    }
}
