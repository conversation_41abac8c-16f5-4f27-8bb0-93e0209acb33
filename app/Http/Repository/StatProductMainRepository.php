<?php

namespace App\Http\Repository;

use App\Define\Common;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\CustomerBillAdjust;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;

class StatProductMainRepository extends StatBaseRepository
{
    protected $source = [0 => Common::COMPANY_CN_NAME, 1 => '朴道', 2 => '百行'];

    public function statList()
    {
        try {
            $params = $this->getStatParams();
            $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        $usage_data = $this->getUsage($params); //调用量
        $income_data = $this->getIncomeV2($params);   //收入
        $cost_data = $this->getCostV2($params);   //成本
        $fixed_cost_data = $this->getFixedCost($params); //固定费用成本
        $customer_cost_data = $this->getCustomerAdjustCost($params);//客户成本调整
        $channel_cost_data = $this->getChannelAdjustCost($params);  //渠道成本调整(由于历史原因名字起的不符合业务语义，其实应该叫成本调整表)
        $expend_data = $this->getExpend($params);   //特殊消耗

        $data = $this->formatData($usage_data, $income_data, $cost_data, $expend_data, $fixed_cost_data, $customer_cost_data, $channel_cost_data);
        return $this->filterAuthMoney($data);
    }

    protected function getUsage($params)
    {
        $where = $this->getUsageWhere($params);
        return StatisticsCustomerUsage::getStatList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product']);
    }

    protected function getExpend($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getCustomerExpendByOperator(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer']);
        $expend_data['add'] = CustomerExpend::getCustomerExpendByOperator(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer']);
        return $expend_data;
    }

    protected function getFixedCost($params)
    {
        $where = $this->getCostWhere($params);
        return ChannelAccountFixedFee::getCostList($where, $params['product_list'], $params['apikey_list'], $params['filter_apikey'])->toArray();
    }

    protected function getCustomerAdjustCost($params)
    {
        $where = $this->getCustomerAdjustCostWhere($params);
        return CustomerBillAdjust::getCostList($where, $params['product_list'], $params['filter_customer'])->toArray();
    }

    protected function getCustomerAdjustCostWhere($params)
    {
       
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        isset($params['customer_id']) && $where[] = ['customer_id', $params['customer_id']];
        isset($params['product_id']) && $where[] = ['product_id', $params['product_id']];
        isset($params['father_id']) && $where[] = ['father_id', $params['father_id']];
        isset($params['operator']) && $where[] = ['operator', $params['operator']];
        isset($params['source']) && $where[] = ['source', $params['source']];
        $where[] = ['delete_at', 0];
        return $where;
    }

    protected function getChannelAdjustCost($params)
    {
        $where = $this->getChannelAdjustCostWhere($params);

        return ChannelAccountAdjust::getCostList($where, $params['product_list'], $params['filter_customer'])->toArray();
    }

    protected function getChannelAdjustCostWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        isset($params['customer_id']) && $where[] = ['customer_id', $params['customer_id']];
        isset($params['apikey']) && $where[] = ['apikey', $params['apikey']];
        isset($params['product_id']) && $where[] = ['product_id', $params['product_id']];
        isset($params['operator']) && $where[] = ['operator', $params['operator']];
        isset($params['source']) && $where[] = ['source', $params['source']];
        return $where;
    }

    protected function formatData($usage_data, $income_data, $cost_data, $expend_data, $fixed_cost_data, $customer_cost_data, $channel_cost_data)
    {
        $main_product_stat = $product_stat = [];
        foreach ($usage_data as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';
            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['total'] += $item['total'];
            $main_product_stat[$father_id]['success'] += $item['success'];
            $main_product_stat[$father_id]['valid'] += $item['valid'];
            $main_product_stat[$father_id]['cache'] += $item['cache'];


            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['total'] += $item['total'];
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['success'] += $item['success'];
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['valid'] += $item['valid'];
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['cache'] += $item['cache'];
        }

        foreach ($income_data as $item) {
            $father_id = $item['father_id'];
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';
            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['number'] += $item['number'];
            $sum_main_product_money = $main_product_stat[$father_id]['money'] ?? 0;
            $main_product_stat[$father_id]['money'] = bcadd($sum_main_product_money, $item['money'], $this->degree);
            //金融收入
            $sum_main_product_money_finance = $main_product_stat[$father_id]['money_finance'] ?? 0;
            $main_product_stat[$father_id]['money_finance'] = bcadd($sum_main_product_money_finance, $item['money_finance'], $this->degree);

            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['number'] += $item['number'];
            $sum_money = $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money'] ?? 0;
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money'] = bcadd($sum_money, $item['money'], $this->degree);

            //金融收入
            $sum_money_finance = $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money_finance'] ?? 0;
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money_finance'] = bcadd($sum_money_finance, $item['money_finance'], $this->degree);

        }

        foreach ($expend_data['sub'] as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $customer_id = $item['customer_id'];
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';

            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['number'] -= $item['number'];
            $sum_main_product_money = $main_product_stat[$father_id]['money'] ?? 0;
            $main_product_stat[$father_id]['money'] = bcsub($sum_main_product_money, $item['money'], $this->degree);
            //金融收入
            $sum_main_product_money_finance = $main_product_stat[$father_id]['money_finance'] ?? 0;
            $main_product_stat[$father_id]['money_finance'] = bcsub($sum_main_product_money_finance, $item['money_finance'], $this->degree);

            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['number'] -= $item['number'];
            $sum_money = $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money'] ?? 0;
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money'] = bcsub($sum_money, $item['money'], $this->degree);

            //金融收入
            $sum_money_finance = $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money_finance'] ?? 0;
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money_finance'] = bcsub($sum_money_finance, $item['money_finance'], $this->degree);
        }

        foreach ($expend_data['add'] as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $customer_id = $item['customer_id'];
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';

            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['number'] += $item['number'];
            $sum_main_product_money = $main_product_stat[$father_id]['money'] ?? 0;
            $main_product_stat[$father_id]['money'] = bcadd($sum_main_product_money, $item['money'], $this->degree);
            //金融收入
            $sum_main_product_money_finance = $main_product_stat[$father_id]['money_finance'] ?? 0;
            $main_product_stat[$father_id]['money_finance'] = bcadd($sum_main_product_money_finance, $item['money_finance'], $this->degree);

            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['number'] += $item['number'];
            $sum_money = $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money'] ?? 0;
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money'] = bcadd($sum_money, $item['money'], $this->degree);

            //金融收入
            $sum_money_finance = $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money_finance'] ?? 0;
            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['money_finance'] = bcadd($sum_money_finance, $item['money_finance'], $this->degree);

        }

        // 成本
        foreach ($cost_data as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';

            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['cost'] += $item['money'];

            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['cost'] += $item['money'];
        }
        // 固定费用成本调整
        foreach ($fixed_cost_data as $item) {
            $father_id = $item['father_id'];
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';
            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['cost'] += $item['money'];

            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['cost'] += $item['money'];
        }

        // 客户成本调整
        foreach ($customer_cost_data as $item) {
            $father_id = $item['father_id'];
            $customer_id = $item['customer_id'];
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';
            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['cost'] += $item['money'];

            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['cost'] += $item['money'];
        }

        foreach ($channel_cost_data as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $customer_id = $item['customer_id'];
            $item['operator'] = $item['operator'] ? $item['operator'] : '-';

            if (!isset($main_product_stat[$father_id])) {
                $main_product_stat[$father_id] = $this->getTpl();
                $main_product_stat[$father_id]['father_id'] = $father_id;
            }

            $main_product_stat[$father_id]['cost'] += $item['money'];

            if (!isset($product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']])) {
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']] = $this->getTpl();
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['father_id'] = $father_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['product_id'] = $item['product_id'];
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['customer_id'] = $customer_id;
                $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['operator'] = $item['operator'];
            }

            $product_stat[$father_id][$item['product_id']][$customer_id][$item['operator']]['cost'] += $item['money'];
        }

        $main_product_stat = array_values($main_product_stat);

        return compact('main_product_stat','product_stat');
    }

    protected function getTpl()
    {
        return [
            'father_id' => '',
            'product_id' => '',
            'customer_id' => '',
            'operator' => '',
            'total' => 0,
            'success' => 0,
            'valid' => 0,
            'cache' => 0,
            'number' => 0,
            'money' => 0,
            'money_finance' => 0,
            'cost' => 0
        ];
    }

    protected function filterAuthMoney($data)
    {
        foreach ($data['main_product_stat'] as $father_id => &$value) {
            if (!$this->checkUserProductAuth($value['father_id'],'money')) {
                $value['money'] = '-' ;
                $value['money_finance'] = '-' ;
            }
            if(!$this->checkUserProductAuth($value['father_id'],'cost')){
                $value['cost'] = '-';
            }
        }

        foreach ($data['product_stat'] as $father_id => &$value) {
                foreach ($value as $product_id => &$val) {
                    foreach ($val as $customer_id => &$vv) {
                        foreach ($vv as $operator => &$vvv) {
                            if (!$this->checkUserProductAuth($father_id,'money')) {
                                $vvv['money'] = '-';
                                $vvv['money_finance'] = '-';
                            }
                            if(!$this->checkUserProductAuth($father_id,'cost')){
                                $vvv['cost'] = '-';
                            }
                        }
                    }
                }
        }

        return $data;
    }
}