<?php

namespace App\Http\Repository;

use App\Define\Common;
use App\Exports\EmailBillV2;
use App\Illuminate\BillDetailsV2\SettelBillMonth;
use App\Mail\BillMonth;
use App\Models\{
    Account, CustomerExpend, MoneyRecharge, MongoBillMonth, Customer, MongoBillSection, Product
};
use App\Support\CustomException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;

class BillV2Repository
{
    /** @var string  当前操作的客户的ID */
    private $customer_id;

    /** @var string 催收分析快捷版的key */
    private $product_key_cuishou = '99bbdb8426f8b4e8d0cc3ebd92484590';

    private $product_key_golden = '973cfad2f2b36e7c8c0a33b959efb2bb';

    /** @var array 按照算法场景分组的账单片段 */
    private $list_bill_section_group_alg;

    /** @var array 今天当前客户快捷版计费规则有冲突的账单 */
    private $_list_shortcut_conflict_bill_this_year = [];
    private $_list_golden_conflict_bill_this_year   = [];

    /** @var array  有冲突的快捷版算法 */
    private $list_shortcut_useless_account_ids = [];
    private $list_golden_useless_account_ids   = [];


    /** @var MongoBillMonth 正在操作的账单单元 */
    private $section_bill_item;

    /**
     * 邮件发送对账单
     * @throws CustomException
     */
    public function email()
    {
        // 校验参数
        $this->validateParamsForEmail();

        // 设置请求customer_id
        $this->sendCustomerIdForEmail();

        // 生成excel
        $this->genExcelForEmail();

        // 发送邮件
        $this->sendEmailThenDel();
    }

    /**
     * 设置请求customer_id
     */
    private function sendCustomerIdForEmail()
    {
        $customer_Info     = request()->post("customer_info");
        $this->customer_id = $customer_Info["customer_id"];
    }

    /**
     * 发送邮件
     */
    private function sendEmailThenDel()
    {
        // 邮件主题和内容
        list($theme, $content) = [
            trim(request()->post('theme')),
            trim(request()->post('content')),
        ];
        $path_file = storage_path() . '/app/' . $this->getFileName();

        // 格式化邮箱
        list($email_received, $email_copy) = $this->formatEmail();

        Mail::to($email_received)
            ->cc($email_copy)
            ->send(new BillMonth($theme, $content, $path_file));

        if (file_exists($path_file)) {
            @unlink($path_file);
        }
    }

    /**
     * 格式化邮箱
     * @return array
     */
    private function formatEmail(): array
    {
        list($email_received, $email_copy) = [
            trim(request()->post('email_received'), ';'),
            trim(request()->post('email_copy'), ';'),
        ];

        // 邮件接收人
        $email_received = explode(';', $email_received);
        $email_received = array_map(
            function ($item) {
                return trim($item);
            },
            $email_received
        );

        // 邮件抄送人
        $email_copy = explode(';', $email_copy);
        $email_copy = array_map(
            function ($item) {
                return trim($item);
            },
            $email_copy
        );

        return [$email_received, $email_copy];
    }

    /**
     * 生成excel
     */
    private function genExcelForEmail()
    {
        $file_name = $this->getFileName();
        list($history, $customer_info, $list_product_bill_alo) = [
            request()->post('history'),
            request()->post('customer_info'),
            $this->_formatAccountBillSection(), // 各个账号产品的算法的情况
        ];

        // 常规客户生成excel
        if (!$this->_determineIsSepcialCustomer() && !$this->_determineIsPostfixSepcialCustomer()) {
            Excel::store(new EmailBillV2($customer_info, $history, $list_product_bill_alo), $file_name);
            return;
        }

        // 特殊客户生成excel
        app("bill.special.customer.email")->_genExcel(
            $this->customer_id,
            [$customer_info, $history, $list_product_bill_alo, $file_name]
        );
    }

    /**
     * 格式化账单对应的计费片段
     * @return array
     */
    private function _formatAccountBillSection(): array
    {
        $list_product_bill_alo = request()->post('list_product_alo_consumptions');

        // 如果是特殊的客户  则直接返回
        if ($this->_determineIsSepcialCustomer() && !$this->_determineIsPostfixSepcialCustomer()) {
            return $list_product_bill_alo;
        }

        return array_map(
            function ($item_account) {
                $item_account['list_bills'] = array_filter(
                    $item_account['list_bills'],
                    function ($item_bill) {
                        $export_consumption_details = $item_bill['export_consumption_details'] ?? true;
                        return $export_consumption_details;
                    }
                );
                return $item_account;
            },
            $list_product_bill_alo
        );
    }

    /**
     * @return string
     */
    private function getFileName()
    {
        $customer_info = request()->post('customer_info');
        return $customer_info['name'] . '-结算单.xlsx';
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForEmail()
    {
        // 进行基础校验
        $this->validateBaseParamsForEmail();

        // 校验邮箱
        $this->validateEmail();
    }

    /**
     * 校验邮箱
     * @throws CustomException
     */
    private function validateEmail()
    {
        list($email_received, $email_copy) = [
            request()->post('email_received'),
            request()->post('email_copy'),
        ];

        // 校验收件人邮件
        $this->validateOneEmail($email_received, '接收人邮箱');

        // 抄送人邮箱
        $this->validateOneEmail($email_copy, '抄送人邮箱');
    }

    /**
     * 校验邮箱
     *
     * @param $email_str
     * @param $intro_field
     *
     * @throws CustomException
     */
    private function validateOneEmail($email_str, $intro_field)
    {
        if (!is_string($email_str)) {
            throw new CustomException($intro_field . '参数必须是字符串');
        }

        // 防止在后面添加';'结尾
        $email_str = trim($email_str, ';');
        $email_str = trim($email_str);
        if (!$email_str) {
            throw new CustomException($intro_field . '不可以为空');
        }

        $list_emails = explode(';', $email_str);
        array_walk(
            $list_emails,
            function ($email) use ($intro_field) {
                // 过滤下
                $email = trim($email);
                if (filter_var($email, FILTER_VALIDATE_EMAIL) == false) {
                    throw new CustomException($intro_field . ' : ' . $email . '不是合法的邮箱');
                }
            }
        );

    }

    /**
     * 进行基础校验
     * @throws CustomException
     */
    private function validateBaseParamsForEmail()
    {
        list($customer_info, $theme, $content, $history) = [
            request()->post('customer_info'),
            request()->post('theme'),
            request()->post('content'),
            request()->post('history'),
        ];

        // 校验客户信息
        if (!$customer_info) {
            throw new CustomException('customer_info参数不可以为空');
        }

        if (!($customer_info["customer_id"] ?? "")) {
            throw new CustomException('customer_info缺少customer_id的信息');
        }

        // 校验主题
        if (!$theme) {
            throw new CustomException('theme参数不可以为空');
        }

        // 检查邮件内容
        if (!$content) {
            throw new CustomException('content参数不可以为空');
        }

        // 校验结算单数据
        if (!$history) {
            throw new CustomException('history参数不可以为空');
        }
    }

    /**
     * 获取excel所需要的数据
     *
     * @param string $customer_id
     *
     * @return array
     * @throws CustomException
     * @throws \Exception
     */
    public function excel($customer_id): array
    {
        // 校验然后设置customer_id
        $this->_validateAndSetCustomerId($customer_id);

        // 生成计费消费
        return [
            'list_account_show'                      => $this->_genMoneyDetail(),
            'list_shortcut_conflict_bill_this_month' => $this->_list_shortcut_conflict_bill_this_year,
            'list_golden_conflict_bill_this_month'   => $this->_list_golden_conflict_bill_this_year,
            'list_shortcut_useless_account_ids'      => $this->list_shortcut_useless_account_ids,
            'list_golden_useless_account_ids'        => $this->list_golden_useless_account_ids,
        ];
    }

    /**
     * 生成计费消费
     * @return array
     */
    private function _genMoneyDetail(): array
    {
        // 如果是特殊客户(前置的特殊客户)
        if ($this->_determineIsSepcialCustomer()) {
            return $this->_getSpeicalCustomerMoneyInfo();
        }

        $data = $this->_getCommonCustomerMoney();

        //如果是后置的特殊客户
        if ($this->_determineIsPostfixSepcialCustomer()) {
            $data = $this->_getPostfixSepcialCustomer($data);
        }
        return $data;
    }

    /**
     * 是否是特殊客户（后置）
     * @return bool
     */
    private function _determineIsPostfixSepcialCustomer(): bool
    {
        return in_array($this->customer_id, config("params.postfix_special_customer"));
    }

    /**
     * 获取特殊客户（后置）的账单信息
     *
     * @return array
     **/
    private function _getPostfixSepcialCustomer($data): array
    {
        return app('bill.special.postfix.customer')
            ->driver($this->customer_id)
            ->_getMoneyInfo($data);
    }

    /**
     * 是否是特殊客户
     * @return bool
     */
    private function _determineIsSepcialCustomer(): bool
    {
        return in_array($this->customer_id, config("params.list_special_customer"));
    }

    /**
     * 特殊客户获取账单信息
     * @return array
     */
    private function _getSpeicalCustomerMoneyInfo(): array
    {
        return app("bill.special.customer")
            ->driver($this->customer_id)
            ->_getMoneyInfo();
    }

    /**
     * 通用的客户excel money设置
     * @return array
     */
    private function _getCommonCustomerMoney(): array
    {
        //本月的月份日期
//        $month = $this->getBillMonth();
//        //获取本月内的所有计费账单
//        $this_month_bill_month = MongoBillMonth::where('month', $month)
//            ->where('customer_id', $this->customer_id)
//            ->get()
//            ->toArray();
//
//        //获取产品数据
//        $product_info = Product::select(['product_name', 'product_id', 'father_id'])
//            ->get()
//            ->toArray();
//        $product_info = array_column($product_info, null, 'product_id');
//
//        //整理账单信息
//        $data = [];
//        //210产品备份数组(210产品需要特殊处理)
//        $product210_bak = [];
//
//        /**
//         * 遍历所有查询月份的账单数据，遍历中将会做两件事情
//         *      1. 过滤掉所有的210产品的子产品，保存在product210_bak中
//         *      2. 将其他产品的账单数据进行组装，并插入到data中
//         **/
//        array_map(
//            function ($item) use ($product_info, &$product210_bak, &$data) {
//                //过滤掉不需要计费的
//                $section_source = $item['section_source'];
//                if ($section_source['is_reset'] == 1) {
//                    return true;
//                }
//                $product_id = $item['product_id'];
//                if ($product_info[$product_id]['father_id'] == 210) {
//                    $product210_bak[] = $item;
//                    return true;
//                }
//                $data[] = $this->settleBillMonth($item, $product_info);
//                return true;
//            },
//            $this_month_bill_month
//        );
//
//        halt($data);

        // 初步整理计费片段
        $list_account_money_details = $this->_getMoneyDetailForGroup();

        // 计算各种算法需要详细数据
        $list_account_common_products = $this->_genBillDetailsForAlo($list_account_money_details);

        // 设置是否需要生成催收快捷版的明细
        $list_shortcut_products = $this->_getDetermineCreateShortcut($list_account_money_details);

        // 设置金盾的明细
        $list_goldenShield_products = $this->_getDetermineCreateGoldenShield($list_account_money_details);


        // 快捷版和通用部分进行聚合
        return $this->_aggregateCommonAndShortcut(
            $list_account_common_products,
            $list_shortcut_products,
            $list_goldenShield_products
        );
    }

    /**
     * 创建settelBillMonth的对象
     *
     * @access private
     *
     * @param $fee_method      integer 计费方式 （1-按时间计费 2-按用量计费）
     * @param $fee_time_rule   integer 时间计费规则（0- 未设置 1-包日 2-包月 3-包年）
     * @param $fee_amount_rule integer 用量计费规则（0-未设置 1-固定单价 2-累进阶梯 3-到达阶梯）
     * @param $fee_price_rule  integer 是否区分运营商（0-未设置 1-不区分 2-区分）
     * @param $fee_step_rule   integer 计费周期（0-未设置 1-日 2-月 3-年 4-无周期）
     *
     * @return SettelBillMonth
     **/
    private function getSettelBillMonth($fee_method, $fee_time_rule, $fee_amount_rule, $fee_price_rule, $fee_step_rule)
    {
        /**
         *  用量 && 固定单价
         **/
        if ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 1 && $fee_price_rule == 0 && $fee_step_rule == 0) {
            //按用量 && 固定单价 && 无区分运营商配置项 && 无周期配置
            return new SettelBillMonth\SettelBillMonth20100();
        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 1 && $fee_price_rule == 1 && $fee_step_rule == 0) {
            //按用量 && 固定单价 && 不区分运营商 && 无周期配置

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 1 && $fee_price_rule == 2 && $fee_step_rule == 0) {
            //按用量 && 固定单价 && 区分运营商 && 无周期配置
            //return new SettelBillMonth\SettelBillMonth20120();
        }

        /**
         * 用量 && 累进阶梯
         **/
        if ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 0 && $fee_step_rule == 1) {
            //按用量 && 累进阶梯 && 无区分运营商配置项 && 周期为 日
            return new SettelBillMonth\SettelBillMonth20201();
        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 0 && $fee_step_rule == 2) {
            //按用量 && 累进阶梯 && 无区分运营商配置项 && 周期为 月
            return new SettelBillMonth\SettelBillMonth20202();
        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 0 && $fee_step_rule == 3) {
            //按用量 && 累进阶梯 && 无区分运营商配置项 && 周期为 年
            return new SettelBillMonth\SettelBillMonth20203();
        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 0 && $fee_step_rule == 4) {
            //按用量 && 累进阶梯 && 无区分运营商配置项 && 周期为 无周期

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 1 && $fee_step_rule == 1) {
            //按用量 && 累进阶梯 && 不区分运营商 && 周期为 日
            //return new SettelBillMonth\SettelBillMonth20211();
        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 1 && $fee_step_rule == 2) {
            //按用量 && 累进阶梯 && 不区分运营商 && 周期为 月

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 1 && $fee_step_rule == 3) {
            //按用量 && 累进阶梯 && 不区分运营商 && 周期为 年

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 1 && $fee_step_rule == 4) {
            //按用量 && 累进阶梯 && 不区分运营商 && 周期为 无周期

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 2 && $fee_step_rule == 1) {
            //按用量 && 累进阶梯 && 区分运营商 && 周期为 日

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 2 && $fee_step_rule == 2) {
            //按用量 && 累进阶梯 && 区分运营商 && 周期为 月

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 2 && $fee_step_rule == 3) {
            //按用量 && 累进阶梯 && 区分运营商 && 周期为 年

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 2 && $fee_price_rule == 2 && $fee_step_rule == 4) {
            //按用量 && 累进阶梯 && 区分运营商 && 周期为 无周期
            //return new SettelBillMonth\SettelBillMonth20224();

        }

        /**
         * 用量 && 到达阶梯
         **/
        if ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 0 && $fee_step_rule == 1) {
            //按用量 && 到达阶梯 && 无区分运营商配置项 && 周期为 日

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 0 && $fee_step_rule == 2) {
            //按用量 && 到达阶梯 && 无区分运营商配置项 && 周期为 月

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 0 && $fee_step_rule == 3) {
            //按用量 && 到达阶梯 && 无区分运营商配置项 && 周期为 年

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 0 && $fee_step_rule == 4) {
            //按用量 && 到达阶梯 && 无区分运营商配置项 && 周期为 无周期

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 1 && $fee_step_rule == 1) {
            //按用量 && 到达阶梯 && 不区分运营商 && 周期为 日

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 1 && $fee_step_rule == 2) {
            //按用量 && 到达阶梯 && 不区分运营商 && 周期为 月

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 1 && $fee_step_rule == 3) {
            //按用量 && 到达阶梯 && 不区分运营商 && 周期为 年

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 1 && $fee_step_rule == 4) {
            //按用量 && 到达阶梯 && 不区分运营商 && 周期为 无周期

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 2 && $fee_step_rule == 1) {
            //按用量 && 到达阶梯 && 区分运营商 && 周期为 日

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 2 && $fee_step_rule == 2) {
            //按用量 && 到达阶梯 && 区分运营商 && 周期为 月

        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 2 && $fee_step_rule == 3) {
            //按用量 && 到达阶梯 && 区分运营商 && 周期为 年
            //return new SettelBillMonth\SettelBillMonth20323();
        } elseif ($fee_method == 2 && $fee_time_rule == 0 && $fee_amount_rule == 3 && $fee_price_rule == 2 && $fee_step_rule == 4) {
            //按用量 && 到达阶梯 && 区分运营商 && 周期为 无周期

        }

        /**
         * 周期 && 按日
         **/
        if ($fee_method == 1 && $fee_time_rule == 1 && $fee_amount_rule == 0 && $fee_price_rule == 0 && $fee_step_rule == 0) {
            //按周期 && 包日 && 无区分运营商配置项 && 无周期配置
            return new SettelBillMonth\SettelBillMonth11000();
        } elseif ($fee_method == 1 && $fee_time_rule == 1 && $fee_amount_rule == 0 && $fee_price_rule == 1 && $fee_step_rule == 0) {
            //按周期 && 包日 && 不区分运营商 && 无周期配置
        } elseif ($fee_method == 1 && $fee_time_rule == 1 && $fee_amount_rule == 0 && $fee_price_rule == 2 && $fee_step_rule == 0) {
            //按周期 && 包日 && 区分运营商 && 无周期配置
        } elseif ($fee_method == 1 && $fee_time_rule == 2 && $fee_amount_rule == 0 && $fee_price_rule == 0 && $fee_step_rule == 0) {
            //按周期 && 包月 && 无区分运营商配置项 && 无周期配置
            return new SettelBillMonth\SettelBillMonth12000();
        } elseif ($fee_method == 1 && $fee_time_rule == 2 && $fee_amount_rule == 0 && $fee_price_rule == 1 && $fee_step_rule == 0) {
            //按周期 && 包月 && 不区分运营商 && 无周期配置
        } elseif ($fee_method == 1 && $fee_time_rule == 2 && $fee_amount_rule == 0 && $fee_price_rule == 2 && $fee_step_rule == 0) {
            //按周期 && 包月 && 区分运营商 && 无周期配置
        } elseif ($fee_method == 1 && $fee_time_rule == 3 && $fee_amount_rule == 0 && $fee_price_rule == 0 && $fee_step_rule == 0) {
            //按周期 && 包年 && 无区分运营商配置项 && 无周期配置
            return new SettelBillMonth\SettelBillMonth13000();
        } elseif ($fee_method == 1 && $fee_time_rule == 3 && $fee_amount_rule == 0 && $fee_price_rule == 1 && $fee_step_rule == 0) {
            //按周期 && 包年 && 不区分运营商 && 无周期配置
        } elseif ($fee_method == 1 && $fee_time_rule == 3 && $fee_amount_rule == 0 && $fee_price_rule == 2 && $fee_step_rule == 0) {
            //按周期 && 包年 && 区分运营商 && 无周期配置
        }


        halt('未发现驱动', $fee_method, $fee_time_rule, $fee_amount_rule, $fee_price_rule, $fee_step_rule);
    }

    /**
     * 组装账单信息
     *
     * @access private
     *
     * @param $bill_month_item array 账单信息
     * @param $product_info    array 产品信息
     *
     * @return array
     **/
    private function settleBillMonth($bill_month_item, $product_info)
    {
        //计费方式 （1-按时间计费 2-按用量计费）
        $fee_method = $bill_month_item['section_source']['fee_method'];
        //时间计费规则 1-包日 2-包月 3-包年
        $fee_time_rule = $bill_month_item['section_source']['fee_time_rule'];
        //用量计费规则 1-固定单价 2-累进阶梯 3-到达阶梯
        $fee_amount_rule = $bill_month_item['section_source']['fee_amount_rule'];
        //是否区分运营商 （1-不区分 2-区分）
        $fee_price_rule = $bill_month_item['section_source']['fee_price_rule'];
        //计费周期
        $fee_step_rule = $bill_month_item['section_source']['fee_step_rule'];

        //产品ID
        //$product_id = $bill_month_item['product_id'];

        $data['money_info'] = $this->getSettelBillMonth(
            $fee_method,
            $fee_time_rule,
            $fee_amount_rule,
            $fee_price_rule,
            $fee_step_rule
        )
            ->settel($bill_month_item);
        $data['label']      = $label = $fee_method . '-' . $fee_time_rule . '-' . $fee_amount_rule . '-' . $fee_price_rule . '-' . $fee_step_rule;

        return $data;
    }


    /**
     * 获取账单月份
     *
     * @access private
     *
     * @return string
     **/
    private function getBillMonth()
    {
        $month = request()->get('month', date('Ym', strtotime('first day of last month')));
        if (!preg_match('/^20[12]\d[01]\d$/', $month)) {
            throw new \Exception('不正确的month参数');
        }
        return strval($month);
    }

    /**
     * 快捷版和通用部分进行聚合
     *
     * @param       $list_account_common_products
     * @param array $list_shortcut_products
     *
     * @return array
     */
    private function _aggregateCommonAndShortcut($list_account_common_products, array $list_shortcut_products, array $list_goldenShield_products): array
    {

        // 过滤掉非快捷版的子产品 615子产品同样过滤
        $list_account_common_products = $this->_filterCommonProductMoney($list_account_common_products);


        // 将快捷版添加到普通产品中去
        return $list_aggregate = $this->_appendShortcutToCommon(
            $this->_appendShortcutToCommon(
                $list_account_common_products,
                $list_shortcut_products
            ),
            $list_goldenShield_products
        );

    }

    /**
     * 将快捷版添加到普通产品中去
     *
     * @param array $list_account_common_products
     * @param array $list_shortcut_products
     *
     * @return array
     */
    private function _appendShortcutToCommon(array $list_account_common_products, array $list_shortcut_products): array
    {
        //halt($list_account_common_products);

        // 容器
        $list_container = [];
        array_walk(
            $list_account_common_products,
            function ($item_account, $account_id) use ($list_shortcut_products, &$list_container) {
                // 对应的快捷版
                $account_shortcut    = $list_shortcut_products[$account_id] ?? [];
                $list_bills_shortcut = $account_shortcut['list_bills'] ?? [];
                $list_bills_common   = $item_account['list_bills'] ?? [];

                if ($list_bills_shortcut) {
                    $item_account['list_bills'] = array_merge($list_bills_shortcut, $list_bills_common);
                }

                $list_container[$account_id] = $item_account;
            }
        );

        return $list_container;
    }

    /**
     * 过滤掉非快捷版的子产品
     *
     * @param array $list_account_common_products
     *
     * @return array
     */
    private function _filterCommonProductMoney(array $list_account_common_products): array
    {
        $product_id = array_merge($this->_getShortcutProductIds(), $this->_getGoldenShieldProductIds());
        return array_map(
            function ($item_account) use ($product_id) {
                $item_account['list_bills'] = array_filter(
                    $item_account['list_bills'],
                    function ($item_product) use ($product_id) {
                        return !in_array($item_product->product_id, $product_id);
                    }
                );
                return $item_account;
            },
            $list_account_common_products
        );
    }

    /**
     * 设置是否需要生成催收快捷版的明细
     *
     * @param array $list_account_money_details
     *
     * @return array
     */
    private function _getDetermineCreateShortcut(array $list_account_money_details): array
    {
        // 获取关于快捷版所有账单
        $list_shortcut_items = $this->_getShortcutBill($list_account_money_details);

        // 判断这些账单的算法是否一致
        $this->determineShortcutOnlyOneAlo($list_shortcut_items);

        // 获取整合后的参数
        return $this->_aggregateShortcutMoney($list_shortcut_items);
    }

    private function _getDetermineCreateGoldenShield(array $list_account_money_details): array
    {
        $list_shortcut_items = $this->_getGoldenShieldBill($list_account_money_details);

        $this->determineGoldenShieldOnlyOneAlo($list_shortcut_items);

        return $this->_aggregateGoldenShieldMoney($list_shortcut_items);
    }

    /**
     * 获取整合后的参数
     *
     * @param array $list_shortcut_items
     *
     * @return array
     */
    private function _aggregateShortcutMoney(array $list_shortcut_items): array
    {
        // 容器
        $list_container = [];
        array_walk(
            $list_shortcut_items,
            function ($item_account, $account_id) use (&$list_container) {
                // 如果当前的账号不能生成对应的快捷版的产品 则
                if (in_array($account_id, $this->list_shortcut_useless_account_ids)) {
                    $item_account['list_bills'] = [
                        [
                            "product_name"               => "邦信分快捷版",
                            "export_consumption_details" => false,
                            "export_info"                => "计费配置冲突"
                        ]
                    ];
                } else {
                    $alo                        = collect($item_account['list_bills'])->first()->product_alo;
                    $list_bills                 = app('bill.detail.v2.shortcut')->_aggregateInAccount(
                        $item_account['list_bills'],
                        $alo
                    );
                    $item_account['list_bills'] = [$list_bills];
                }
                $list_container[$account_id] = $item_account;
            }
        );

        return $list_container;
    }

    private function _aggregateGoldenShieldMoney(array $list_shortcut_items): array
    {
        // 容器
        $list_container = [];
        array_walk(
            $list_shortcut_items,
            function ($item_account, $account_id) use (&$list_container) {
                // 如果当前的账号不能生成对应的快捷版的产品 则
                if (in_array($account_id, $this->list_shortcut_useless_account_ids)) {
                    $item_account['list_bills'] = [
                        [
                            "product_name"               => "金盾",
                            "export_consumption_details" => false,
                            "export_info"                => "金盾"
                        ]
                    ];
                } else {
                    $alo                        = collect($item_account['list_bills'])->first()->product_alo;
                    $list_bills                 = app('bill.detail.v2.golden')->_aggregateInAccount(
                        $item_account['list_bills'],
                        $alo
                    );
                    $item_account['list_bills'] = [$list_bills];
                }
                $list_container[$account_id] = $item_account;
            }
        );

        return $list_container;
    }


    /**
     * 设置生曾快捷半的方式
     *
     * @param array $list_shortcut_items 快捷版产品按照算法和月份进行聚合
     */
    private function determineShortcutOnlyOneAlo(array $list_shortcut_items)
    {
        // 如果没有对应的单元
        if (count($list_shortcut_items) == 0) {
            $this->_list_shortcut_conflict_bill_this_year = [
                '没有快捷版的账单'
            ];
            return;
        }

        array_walk(
            $list_shortcut_items,
            function ($item_account, $account_id) {
                // 当计费规则冲突的时候， 设置相关的数据
                if ($this->_determineThisMonthConflict($item_account['list_bills'], $account_id)) {
                    // 如果冲突
                    $this->list_shortcut_useless_account_ids[] = $account_id;
                }
            }
        );
    }

    private function determineGoldenShieldOnlyOneAlo(array $list_shortcut_items)
    {
        // 如果没有对应的单元
        if (count($list_shortcut_items) == 0) {
            $this->_list_golden_conflict_bill_this_year = [
                '没有金盾的账单'
            ];
            return;
        }

        array_walk(
            $list_shortcut_items,
            function ($item_account, $account_id) {
                // 当计费规则冲突的时候， 设置相关的数据
                if ($this->_determineThisMonthConflict($item_account['list_bills'], $account_id)) {
                    // 如果冲突
                    $this->list_golden_useless_account_ids[] = $account_id;
                }
            }
        );
    }

    /**
     * 当前的算法是否有冲突
     *
     * @param array  $list_items
     * @param string $account_id
     *
     * @return bool
     */
    private function _determineThisMonthConflict(array $list_items, string $account_id): bool
    {
        $list_items = collect($list_items);

        // 作为基础的一个
        $base_one = $list_items->first();

        // 是否配置冲突
        return $list_items->contains(
            function ($bill_section_item) use ($base_one, $account_id) {

                // 判断计费规则是否一致
                $bool = $bill_section_item->section_source->fee_basis != $base_one->section_source->fee_basis || $bill_section_item->section_source->fee_method != $base_one->section_source->fee_method || $bill_section_item->section_source->fee_time_rule != $base_one->section_source->fee_time_rule || $bill_section_item->section_source->fee_amount_rule != $base_one->section_source->fee_amount_rule || $bill_section_item->section_source->fee_step_rule != $base_one->section_source->fee_step_rule || $bill_section_item->section_source->fee_price_rule != $base_one->section_source->fee_price_rule || $bill_section_item->section_source->fee_price != $base_one->section_source->fee_price;

                // 设置冲突标识
                if ($bool) {
                    $this->_list_shortcut_conflict_bill_this_year[$account_id] = [
                        'base_one'    => $base_one->toArray(),
                        'another_one' => $bill_section_item->toArray()
                    ];
                }
                return $bool;
            }
        );
    }

    /**
     * 获取快捷版的账单
     *
     * @param array $list_account_money_details
     *
     * @return mixed
     */
    private function _getShortcutBill(array $list_account_money_details): array
    {
        // 快捷版产品列表
        $product_id = $this->_getShortcutProductIds();

        // 过滤掉非快捷版的子产品
        $list_accounts = array_map(
            function ($item_account) use ($product_id) {

                $item_account['list_bills'] = array_filter(
                    $item_account['list_bills'],
                    function ($item_product) use ($product_id) {
                        return in_array($item_product->product_id, $product_id);
                    }
                );
                return $item_account;
            },
            $list_account_money_details
        );

        // 过滤掉没有子产品的账号
        return array_filter(
            $list_accounts,
            function ($item) {
                return $item['list_bills'];
            }
        );
    }

    private function _getGoldenShieldBill(array $list_account_money_details): array
    {
        // 快捷版产品列表
        $product_id = $this->_getGoldenShieldProductIds();

        // 过滤掉非快捷版的子产品
        $list_accounts = array_map(
            function ($item_account) use ($product_id) {

                $item_account['list_bills'] = array_filter(
                    $item_account['list_bills'],
                    function ($item_product) use ($product_id) {
                        return in_array($item_product->product_id, $product_id);
                    }
                );
                return $item_account;
            },
            $list_account_money_details
        );

        // 过滤掉没有子产品的账号
        return array_filter(
            $list_accounts,
            function ($item) {
                return $item['list_bills'];
            }
        );
    }

    /**
     * @param MongoBillMonth $section_bill_item
     */
    public function setSectionBillItem($section_bill_item)
    {
        $section_bill_item->section_source = (object)$section_bill_item->section_source;
        $section_bill_item->start_day      = date('Ymd', strtotime($section_bill_item->section_source->start_date));
        $this->section_bill_item           = $section_bill_item;
    }

    /**
     * 今年消费参数
     * @return array
     */
    private function genConsumeParamsForThisMonth(): array
    {
        $customer_id = $this->customer_id;
        $month       = [
            '$gte' => date('Ym', strtotime('last day of last month'))
        ];
        return compact('customer_id', 'month');
    }

    /**
     * 获取催收快捷版的所有产品ID
     * @return array
     */
    private function _getShortcutProductIds(): array
    {
        // 静态变量
        static $list_shortcut_product_ids;
        if ($list_shortcut_product_ids) {
            return $list_shortcut_product_ids;
        }

        $product_key               = $this->product_key_cuishou;
        $product_id                = Product::where('father_id', '<>', 0)
            ->where(compact('product_key'))
            ->select(['product_id'])
            ->get()
            ->pluck('product_id')
            ->all();
        $product_id[]              = 210;
        $list_shortcut_product_ids = $product_id;
        return $product_id;
    }

    private function _getGoldenShieldProductIds(): array
    {
        // 静态变量
        static $list_golden_product_ids;
        if ($list_golden_product_ids) {
            return $list_golden_product_ids;
        }

        $product_key             = $this->product_key_golden;
        $product_id              = Product::where('father_id', '<>', 0)
            ->where(compact('product_key'))
            ->select(['product_id'])
            ->get()
            ->pluck('product_id')
            ->all();
        $product_id[]            = 615;
        $list_golden_product_ids = $product_id;
        return $product_id;
    }

    /**
     * 重置环境变量
     */
    private function _resetEnv()
    {
        $this->section_bill_item           = null;
        $this->list_bill_section_group_alg = [];
    }

    /**
     * 计算各种算法需要详细数据
     *
     * @param array $list_account_money_details
     *
     * @return array
     */
    private function _genBillDetailsForAlo(array $list_account_money_details): array
    {
        return array_map(
            function ($item_account) {
                $item_account['list_bills'] = array_map(
                    function ($item_bill) {
                        // 获取当前算法类型树妖的数据相关
                        $item_bill['details'] = $this->_getDetailInfoForAlo($item_bill);
                        return $item_bill;
                    },
                    $item_account['list_bills']
                );

                return $item_account;
            },
            $list_account_money_details
        );
    }

    /**
     * 获取当前算法类型的相关数据
     *
     * @param MongoBillMonth $item
     *
     * @return array
     * @throws CustomException
     */
    private function _getDetailInfoForAlo(MongoBillMonth $item): array
    {
        return app('bill.detail.v2.common')->_getDetailInfoForAlo($item);
    }

    /**
     * 按照账号分组计费配置
     * @return array
     */
    private function _getMoneyDetailForGroup(): array
    {
        // 产品列表
        $list_products = Product::getListByCondition([], ['product_id', 'product_name'])
            ->pluck('product_name', 'product_id')
            ->all();
        $list_accounts = Account::getListByCondition([], ['account_id', 'account_name'])
            ->pluck('account_name', 'account_id')
            ->all();

        return MongoBillMonth::where(['customer_id' => $this->customer_id, 'month' => $this->getBillMonth()])
            ->get()
            ->filter(
                function ($item) {
                    $remarks = $item['section_source']['remarks'] ?? '';
                    return trim($remarks) != '系统归零设置';
                }
            )
            ->reduce(
                function ($carry, $item) use ($list_products, $list_accounts) {

                    if (empty($item->section_source)) {
                        return $carry;
                    }
                    // 充值属性类型,方便操作
                    $item = $this->_setObjectAttr($item);

                    //  设置算法 && 其他的基本属性
                    $item = $this->_setAloAttr($item, $list_products);

                    // 返回赋值
                    return $carry = $this->_setValueForReturn($carry, $item, $list_accounts);
                },
                []
            );
    }

    /**
     * 返回赋值
     *
     * @param array          $carry
     * @param MongoBillMonth $item
     *
     * @return array
     */
    private function _setValueForReturn(array $carry, MongoBillMonth $item, array $list_accounts): array
    {
        $carry[$item->account_id]['list_bills'][] = $item;
        if (!isset($carry[$item->account_id]) || !isset($carry[$item->account_id]['account_name'])) {
            $carry[$item->account_id]['account_name'] = $list_accounts[$item->account_id] ?? "没有找到对应的账号";
        }
        return $carry;
    }

    /**
     * 设置算法
     *
     * @param MongoBillMonth $item
     * @param array          $list_products
     *
     * @return MongoBillMonth
     */
    private function _setAloAttr(MongoBillMonth $item, array $list_products): MongoBillMonth
    {
        list($product_alo, $product_alo_cn) = $this->_getProductAlo($item);
        $item->product_alo    = $product_alo;
        $item->product_alo_cn = $product_alo_cn;
        $item->product_name   = $list_products[$item->product_id] ?? '没有找到对应的产品';
        return $item;
    }

    /**
     * 重置属性类型,方便操作
     *
     * @param MongoBillMonth $item
     *
     * @return MongoBillMonth
     */
    private function _setObjectAttr(MongoBillMonth $item): MongoBillMonth
    {
        $item->section_source = (object)$item->section_source;
        $item->start_day      = date('Ymd', strtotime($item->section_source->start_date));
        return $item;
    }

    /**
     * 获取计费片段对应的算法
     *
     * @param MongoBillMonth $item_bill
     *
     * @return array
     */
    public function _getProductAlo(MongoBillMonth $item_bill): array
    {
        // 通用 && 按时间
        if ($this->determineIfSectionIsCommonByDate($item_bill)) {
            // 设置算法分组
            return [
                $this->getBillGroupAlgWhenCommonByDate($item_bill),
                "通用 && 按时间"
            ];
        }

        // 如果通用的按用量 && 固定价格
        if ($this->determineIfSectionIdCommonByNumberFixed($item_bill)) {
            // 设置算法分组
            return [
                $this->getBillGroupAlgWhenCommonByNumberFixed($item_bill),
                "通用的按用量 && 固定价格"
            ];
        }

        // 通用的按用量 && 累进阶梯
        if ($this->determineIfSectionIsCommonByNumberProgression($item_bill)) {
            // 设置算法分组
            return [
                $this->getBillGroupAlgWhenCommonByNumberProgression($item_bill),
                "通用的按用量 && 累进阶梯"
            ];
        }

        // 通用的按用量 && 到达阶梯
        if ($this->determineSectionIsCommonByNumberReach($item_bill)) {
            return [
                $this->getBillGroupAlgWhenCommonNumberReach($item_bill),
                "通用的按用量 && 到达阶梯"
            ];
        }

        // 区分运营商按时间
        if ($this->determineSectionIsDateOperator($item_bill)) {
            return [
                $this->getBillGroupAlgWhenDateOperator($item_bill),
                "区分运营商按时间"
            ];
        }

        // 区分运营商按用量 && 固定价格
        if ($this->determineIfSectionIdOperatorByNumberFixed($item_bill)) {
            return [
                $this->getBillGroupAlgWhenOperatorByNumberFixed($item_bill),
                "区分运营商按用量 && 固定价格"
            ];
        }

        // 区分运营商按用量 && 累进阶梯
        if ($this->determineIfSectionIsOperatorByNumberProgression($item_bill)) {
            return [
                $this->getBillGroupAlgWhenOperatorByNumberProgression($item_bill),
                "区分运营商按用量 && 累进阶梯"
            ];
        }

        // 区分运营商按用量 && 到达阶梯
        if ($this->determineSectionIsOperatorByNumberReach($item_bill)) {
            return [
                $this->getBillGroupAlgWhenOperatorNumberReach($item_bill),
                "区分运营商按用量 && 到达阶梯"
            ];
        }
    }

    /**
     * 区分运营商按用量 && 到达阶梯算法服务
     *
     * @param MongoBillMonth $item
     *
     * @return string
     */
    private function getBillGroupAlgWhenOperatorNumberReach(MongoBillMonth $item): string
    {
        list($fee_method, $fee_amount_rule, $fee_price_rule) = [
            $item->section_source->fee_method,
            $item->section_source->fee_amount_rule,
            $item->section_source->fee_price_rule,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule, $fee_price_rule);
    }

    /**
     * 片段是否是(运营商的按用量 && 到达阶梯)
     *
     * @param object $section_item
     *
     * @return bool
     */
    private function determineSectionIsOperatorByNumberReach($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule == 2;
    }

    /**
     * 区分运营商按用量 && 累进阶梯分组算法
     *
     * @param MongoBillMonth $item
     *
     * @return string
     */
    private function getBillGroupAlgWhenOperatorByNumberProgression(MongoBillMonth $item): string
    {
        list($fee_method, $fee_amount_rule, $fee_price_rule) = [
            $item->section_source->fee_method,
            $item->section_source->fee_amount_rule,
            $item->section_source->fee_price_rule,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule, $fee_price_rule);
    }

    /**
     * @param object $section_item
     *
     * @return bool
     */
    private function determineIfSectionIsOperatorByNumberProgression($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule == 2;
    }


    /**
     * 区分运营商按用量 && 固定价格算法分组
     *
     * @param MongoBillMonth $item
     *
     * @return string
     */
    private function getBillGroupAlgWhenOperatorByNumberFixed(MongoBillMonth $item): string
    {
        list($fee_method, $fee_amount_rule, $fee_price_rule) = [
            $item->section_source->fee_method,
            $item->section_source->fee_amount_rule,
            $item->section_source->fee_price_rule,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule, $fee_price_rule);
    }

    /**
     * 计费片段是否是运营商固定价格模式
     *
     * @param $section_item
     *
     * @return bool
     */
    private function determineIfSectionIdOperatorByNumberFixed($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule == 2;
    }


    /**
     * 区分运营商按时间算法分组
     *
     * @param MongoBillMonth $item
     *
     * @return string
     */
    private function getBillGroupAlgWhenDateOperator(MongoBillMonth $item): string
    {
        list($fee_method, $fee_price_rule) = [
            $item->section_source->fee_method,
            $item->section_source->fee_price_rule,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method, $fee_price_rule);
    }

    /**
     * 是否是区分运营商按时间的场景
     *
     * @param object $section_item
     *
     * @return bool
     */
    private function determineSectionIsDateOperator($section_item): bool
    {
        // 按时间
        $fee_method = $section_item->section_source->fee_method;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return 1 == $fee_method && $fee_price_rule == 2;
    }

    /**
     * 通用的按用量 && 到达阶梯设置算法分组
     *
     * @param MongoBillMonth $item
     *
     * @return string
     */
    private function getBillGroupAlgWhenCommonNumberReach(MongoBillMonth $item): string
    {
        list($fee_method, $fee_amount_rule) = [
            $item->section_source->fee_method,
            $item->section_source->fee_amount_rule,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule);
    }

    /**
     * 片段是否是(通用的按用量 && 到达阶梯)
     *
     * @param object $section_item
     *
     * @return bool
     */
    private function determineSectionIsCommonByNumberReach($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule != 2;
    }

    /**
     * 通用的按用量 && 累进阶梯设置算法分组
     *
     * @param MongoBillMonth $item
     *
     * @return string
     */
    private function getBillGroupAlgWhenCommonByNumberProgression(MongoBillMonth $item): string
    {
        list($fee_method, $fee_amount_rule) = [
            $item->section_source->fee_method,
            $item->section_source->fee_amount_rule,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule);
    }

    /**
     * @param  $section_item
     *
     * @return bool
     */
    private function determineIfSectionIsCommonByNumberProgression($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule != 2;
    }

    /**
     * 通用的按用量 && 固定价格设置算法分组
     *
     * @param MongoBillMonth $item_bill
     *
     * @return mixed
     */
    private function getBillGroupAlgWhenCommonByNumberFixed(MongoBillMonth $item_bill)
    {
        list($fee_method, $fee_amount_rule) = [
            $item_bill->section_source->fee_method,
            $item_bill->section_source->fee_amount_rule,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule);
    }

    /**
     * 计费片段是否是通用固定价格模式
     *
     * @param $section_item
     *
     * @return bool
     */
    private function determineIfSectionIdCommonByNumberFixed($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule != 2;
    }

    /**
     * 通用 && 按时间设置
     *
     * @param MongoBillMonth $section_bill_item
     *
     * @return mixed
     */
    private function getBillGroupAlgWhenCommonByDate(MongoBillMonth $section_bill_item)
    {
        list($fee_method) = [
            $section_bill_item->section_source->fee_method,
        ];

        // 生成算法分组key
        return $this->genBillGroupAlgKey($fee_method);
    }

    /**
     * 生成算法分组key
     * @return mixed
     */
    private function genBillGroupAlgKey()
    {
        $arg_list = func_get_args();
        return array_reduce(
            $arg_list,
            function ($carry, $item) {
                if ($item) {
//                $carry .= $carry ? ('_' . $item) : $item;
                    $carry .= ('_' . $item);
                }
                return $carry;
            },
            ''
        );
    }

    /**
     * 是否是通用时间
     *
     * @param object $section_item
     *
     * @return bool
     */
    private function determineIfSectionIsCommonByDate($section_item)
    {
        // 按时间
        $fee_method = $section_item->section_source->fee_method;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;
        return 1 == $fee_method && $fee_price_rule != 2;
    }

    /**
     * 获取客户的历史账单信息
     *
     * @param string $customer_id
     *
     * @throws CustomException
     * @return array
     */
    public function history(string $customer_id): array
    {
        // 校验然后设置customer_id
        $this->_validateAndSetCustomerId($customer_id);

        // 结算单
        return $this->getTotalStatement();
    }

    /**
     * 校验然后设置customer_id
     *
     * @param string $customer_id
     *
     * @throws CustomException
     */
    private function _validateAndSetCustomerId($customer_id)
    {
        if (!Customer::getOneItemByCondition(compact('customer_id'))) {
            throw new CustomException('传入的customer_id未找到对应的客户');
        }

        // 校验然后设置customer_id
        $this->customer_id = $customer_id;
    }

    /**
     * 对账结算单
     */
    private function getTotalStatement(): array
    {
        // 客户相关信息
        $customer_info = $this->getCustomerInfo();

        //充值金额 （账单月与历史的充值）
        $recharge = $this->getRechargeForHistory($customer_info['customer_id']);

        //消费金额（账单月与历史的消费）
        $consume = $this->getConsumeForHistory($customer_info['customer_id']);

        //特殊费用金额（账单月与历史的消费）
        $special = $this->getSpecialForHistory($customer_info['customer_id']);

        //计算数据
        $calculated = $this->computeMoneyForHistory($recharge, $consume, $special);

        //将所有数据进行整合，然后将需要显示的金额保留两位小数
        $result = $this->tidyHistoryMoney($recharge, $consume, $special, $calculated);
//        // 特殊消费
//        //变量一次代表的是 所有特殊消费数据|账单月的特殊消费数据|账单月之前的特殊消费数据
//        list(
//            $money_special_all_history, $money_special_of_this_month, $money_special_last_before_this_month
//            ) = $this->_getSpeicalMoneyInfo();
//
//        //需要展示的特殊消费数据
//        $money_special_which_show = $money_special_last_before_this_month;
//
//        // 账单月之前的剩余金额
//        $money_last_before_this_month = $this->_getMoneyBeforeThisMonth($money_special_last_before_this_month);
//
//
//        // 账单月消费的金额
//        $money_detail_this_month = $this->_getMoneyOfThisMonth();
//
//
//        //  合计(结算单)
//        //  合计充值金额 = 账单月充值 + 账单月特殊充值
//        //  合计消费金额 = 账单月消费 + 账单月特殊消费
//        //  余额 = 账单月充值金额 + 历史充值金额 + 特殊消费充值金额 - 账单月
//        $list_aggregate_money = $this->aggregateMoneyForTotalList(
//            $money_last_before_this_month,
//            $money_detail_this_month,
//            $money_special_of_this_month
//        );

        // 获取基本信息
        list($today, $theme, $content) = $this->getBaseInfoForHistory($result, $special , $customer_info);

        return compact(
            'today',
            'theme',
            'content',
            'result',
            'calculated',
            'special',
            'recharge',
            'consume',
            'customer_info'
        );
    }

    /**
     * 将结算单所需的内容进行整理，重装为一个新数组，
     * 将所需展示的金额保留两位小数
     *
     * @access protected
     *
     * @param $recharge   array 充值数据
     * @param $consume    array 消费数据
     * @param $special    array 特殊金额数据
     * @param $calculated array 计算结果数据
     *
     * @return array
     **/
    protected function tidyHistoryMoney($recharge, $consume, $special, $calculated)
    {
        //账单月开始结转余额
        $bill_month_start_residue_money = number_format($calculated['bill_month_start_residue_money'], 2);

        //账单月结算单
        $bill_month_start_date = date('Ymd', strtotime('first day of last month'));
        $bill_month_end_date   = date('Ymd', strtotime('last day of last month'));
        $bill_month_data       = [
            'name'     => $bill_month_start_date . ' -- ' . $bill_month_end_date,
            'recharge' => number_format($recharge['bill_month_recharge_money'], 2),
            'consume'  => number_format($consume['bill_month_consume_money'], 2)
        ];

        //特殊金额
        $bill_month_special_data = array_map(
            function ($item) {
                $item['recharge'] = $item['recharge'] != '--' ? number_format($item['recharge'], 2) : '--';
                $item['consume']  = $item['consume'] != '--' ? number_format($item['consume'], 2) : '--';
                return $item;
            },
            $special['bill_month_special']
        );

        //合计
        $total = [
            'recharge' => number_format($calculated['total_recharge'], 2),
            'consume'  => number_format($calculated['total_consume'], 2)
        ];

        //余额
        $residue_money = number_format($calculated['residue_money'], 2);
        return compact(
            'bill_month_start_residue_money',
            'bill_month_data',
            'bill_month_special_data',
            'total',
            'residue_money'
        );
    }

    /**
     * 计算结算单数据
     *  1.账单月开始结转金额
     *  2.账单月截止余额
     *
     * @access protected
     *
     * @param $recharge array 充值数组数据
     * @param $consume  array 消费数组数据
     * @param $special  array 特殊金额
     *
     * @return array
     **/
    protected function computeMoneyForHistory($recharge, $consume, $special)
    {
        //上月结转金额
        $bill_month_start_residue_money = $recharge['history_recharge_money'] - $consume['history_consume_money'] + $special['history_special_recharge_money'] - $special['history_special_consume_money'];

        //账单月余额 = 总充值金额 - 总消费金额 + 账单月特殊充值金额 + 历史特殊充值金额 - 账单月特殊消费金额 - 历史特殊充值金额
        $residue_money = array_sum(
                array_column(
                    $recharge['recharge_list'],
                    'money'
                )
            ) - array_sum(
                array_column(
                    $consume['consume_list'],
                    'money'
                )
            ) + $special['history_special_recharge_money'] + $special['bill_month_special_recharge_money'] - $special['bill_month_special_consume_money'] - $special['history_special_consume_money'];

        //合计栏
        //  1.充值合计 = 上月结转金额 + 账单月充值金额 + 账单月特殊消费金额
        $total_recharge = $bill_month_start_residue_money + $recharge['bill_month_recharge_money'] + $special['bill_month_special_recharge_money'];
        //  2.消费合计 = 账单月消费金额 + 账单月特殊消费金额
        $total_consume = $consume['bill_month_consume_money'] + $special['bill_month_special_consume_money'];

        return compact('bill_month_start_residue_money', 'residue_money', 'total_consume', 'total_recharge');
    }

    /**
     * 获取特殊金额
     *  1.历史特殊消费之和
     *  2.历史特殊充值之和
     *  3.账单月特殊消费之和
     *  4.账单月特殊充值之和
     *  5.账单月特殊金额明细
     *  6.特殊金额明细
     *
     * @access protected
     *
     * @param $customer_id string 客户ID
     *
     * @return array
     **/
    protected function getSpecialForHistory($customer_id)
    {
        //账单月开始日期
        $bill_month_start = date('Ym', strtotime('first day of last month'));

        //账单月截止日期之前所有的特殊金额
        $special_list = CustomerExpend::where('customer_id', $customer_id)
            ->where('start_date', '<=', $bill_month_start)
            ->get()
            ->toArray();

        //历史特殊消费之和
        $history_special_consume_money = 0;
        //历史特殊充值之和
        $history_special_recharge_money = 0;
        //账单月特殊消费之和
        $bill_month_special_consume_money = 0;
        //账单月特殊充值之和
        $bill_month_special_recharge_money = 0;
        //账单月特殊金额明细
        $bill_month_special = [];

        array_walk(
            $special_list,
            function ($item) use (&$history_special_consume_money, &$history_special_recharge_money, &$bill_month_special_consume_money, &$bill_month_special_recharge_money, &$bill_month_special, $bill_month_start) {
                if ($item['start_date'] == $bill_month_start) {
                    if ($item['type'] == 1) {
                        $bill_month_special_recharge_money += $item['money'];
                        $bill_month_special[]              = [
                            'name'     => $item['name'],
                            'month'    => $item['start_date'],
                            'recharge' => $item['money'],
                            'consume'  => '--'
                        ];
                    } elseif ($item['type'] == 2) {
                        $bill_month_special_consume_money += $item['money'];
                        $bill_month_special[]             = [
                            'name'     => $item['name'],
                            'month'    => $item['start_date'],
                            'recharge' => '--',
                            'consume'  => $item['money']
                        ];
                    }
                } else {
                    if ($item['type'] == 1) {
                        $history_special_recharge_money += $item['money'];
                    } elseif ($item['type'] == 2) {
                        $history_special_consume_money += $item['money'];
                    }
                }


            }
        );

        return compact(
            'history_special_consume_money',
            'history_special_recharge_money',
            'bill_month_special_consume_money',
            'bill_month_special_recharge_money',
            'bill_month_special',
            'special_list'
        );
    }

    /**
     * 获取消费金额
     *  1.历史消费金额之和
     *  2.账单月历史消费金额之和
     *  3.消费明细
     *
     * @access protected
     *
     * @param $customer_id string 客户ID
     *
     * @return array
     **/
    protected function getConsumeForHistory($customer_id)
    {
        //账单月开始日期
        $bill_month_start = date('Ym', strtotime('first day of last month'));

        //账单月截止日期之前的所有消费数据
        $consume_list = MongoBillMonth::where('month', '<=', $bill_month_start)
            ->where('customer_id', $customer_id)
            ->get()
            ->toArray();

        //历史消费金额
        $history_consume_money = 0;
        //账单月消费金额
        $bill_month_consume_money = 0;
        //重构消费明细信息
        $consume_list = array_map(
            function ($item) use (&$history_consume_money, &$bill_month_consume_money, $bill_month_start) {
                if ($item['month'] == $bill_month_start) {
                    $bill_month_consume_money += $item['money'];
                } else {
                    $history_consume_money += $item['money'];
                }
                return [
                    'month'       => $item['month'],
                    'money'       => $item['money'],
                    'customer_id' => $item['customer_id'],
                    'account_id'  => $item['account_id'],
                    'product_id'  => $item['product_id'],
                    'uuid'        => $item['uuid'],
                    '_id'         => $item['_id']
                ];
            },
            $consume_list
        );

        return compact('bill_month_consume_money', 'history_consume_money', 'consume_list');
    }

    /**
     * 获取充值金额
     *  1. 历史充值金额之和
     *  2. 账单月充值金额之和
     *  3. 充值明细
     *
     * @access protected
     *
     * @param $customer_id string 客户ID
     *
     * @return array
     **/
    protected function getRechargeForHistory($customer_id)
    {
        //账单月开始日期
        $bill_month_start_time = strtotime('first day of last month midnight');
        //账单月截止日期
        $bill_month_end_time = strtotime('first day of this month midnight') - 1;

        //账单月截止日期之前的所有充值数据
        $recharge_list = MoneyRecharge::where('remit_date', '<=', $bill_month_end_time)
            ->where('status', 3)
            ->where('customer_id', $customer_id)
            ->get()
            ->toArray();

        //历史充值金额
        $history_recharge_money = 0;
        //账单月充值金额
        $bill_month_recharge_money = 0;
        array_walk(
            $recharge_list,
            function ($item) use (&$history_recharge_money, &$bill_month_recharge_money, $bill_month_start_time) {
                $remit_date = $item['remit_date'];
                if ($remit_date >= $bill_month_start_time) {
                    $bill_month_recharge_money += $item['money'];
                } else {
                    $history_recharge_money += $item['money'];
                }
            }
        );

        return compact('history_recharge_money', 'bill_month_recharge_money', 'recharge_list');
    }


    /**
     * 获取客户特殊的充值或者赠送的情况
     * @return array
     */
    private function _getSpeicalMoneyInfo(): array
    {
        // 历史特殊费用
        $money_special_all_history = CustomerExpend::where(['customer_id' => $this->customer_id])
            ->where('start_date', '<=', date('Ym', strtotime('last day of last month')))
            ->get()
            ->reduce(
                function ($carry, $item) {

                    $carry[] = [
                        'month'    => formatMonth($item->start_date),
                        'title'    => $item->start_date . "月" . $item->name,
                        'recharge' => ($item->money < 0) ? -$item->money : 0,
                        'consume'  => ($item->money > 0) ? $item->money : 0,
                    ];

                    return $carry;
                },
                []
            );

        //    本月特殊的费用
        $money_special_of_this_month = array_filter(
            $money_special_all_history,
            function ($item) {
                return $item['month'] == date('Ym', strtotime('first day of last month'));
            }
        );

        // 账单月之前特殊的消费
        $money_special_last_before_this_month = array_filter(
            $money_special_all_history,
            function ($item) {
                return $item['month'] < date('Ym', strtotime('first day of last month'));
            }
        );

        return [
            $money_special_all_history,
            $money_special_of_this_month,
            $money_special_last_before_this_month
        ];
    }

    /**
     * 总结
     *
     * @param array $money_last_before_this_month 账单月之前的金额情况
     * @param array $money_detail_this_month      本月的消费情况
     * @param array $money_special_of_this_month  本月特殊费用
     *
     * @return array
     */
    private function aggregateMoneyForTotalList(array $money_last_before_this_month, array $money_detail_this_month, array $money_special_of_this_month): array
    {
        // 账单月之前的剩余金额 账单月之前充值的金额  账单月之前消费金额
        list(
            $money_recharge_before_this_month,  // 账单月之前充值的金额
            $money_consume_before_this_month, // 账单月之前消费的金额
            $money_consume_of_this_month, // 账单月的消费金额
            $money_recharge_of_this_month, // 账单月的充值金额
            ) = [
            $money_last_before_this_month['money_recharge'],
            $money_last_before_this_month['money_consume'],
            $money_detail_this_month['money_consume_of_this_month'],
            $money_detail_this_month['money_recharge_of_this_month'],
        ];

        // 合计充值金额 = 账单月充值+账单月特殊充值
        $money_recharge_aggregate = $money_recharge_of_this_month;

        // 合计消费金额 = 账单月消费+账单月特殊消费
        $money_consume_aggregate = $money_consume_of_this_month;

        //  处理特殊的金额
        foreach ($money_special_of_this_month as $item_special) {
            $money_recharge_aggregate += $item_special['recharge'];
            $money_consume_aggregate  += $item_special['consume'];
        }

        // 总的剩余金额
        $money_residue_now = $money_recharge_before_this_month - $money_consume_before_this_month + $money_recharge_aggregate - $money_consume_aggregate;

        return compact('money_recharge_aggregate', 'money_consume_aggregate', 'money_residue_now');
    }


    /**
     * 账单月的消费和重置
     * @return array
     */
    private function _getMoneyOfThisMonth(): array
    {
        // 账单月充值金额
        $money_recharge_of_this_month = $this->_getRechargeMonthOfThisMonth();

        // 账单月消费
        $money_consume_of_this_month = $this->_getConsumeOfThisMonth();

        // 账单月的特殊消耗

        // 本月开始和结束的时间
        $month_begin = date('Ym01', strtotime('-1 month'));
        $month_end   = date('Ymd', strtotime('last day of last month'));

        return compact('money_consume_of_this_month', 'money_recharge_of_this_month', 'month_begin', 'month_end');
    }

    /**
     * 今年消费金额每月的分布
     * @return float
     */
    private function _getConsumeOfThisMonth(): float
    {
        $customer_id = $this->customer_id;
        $month       = [
            '$gte' => date('Ym', strtotime('-1 month'))
        ];

        return MongoBillMonth::where(compact('customer_id', 'month'))
            ->sum('money');
    }

    /**
     * 账单月充值金额
     * @return array
     */
    private function _getRechargeMonthOfThisMonth(): float
    {
        $where = [
            ['customer_id', $this->customer_id],
            ['remit_date', '>=', strtotime(date('Y-m-01', strtotime('-1 month')))],
            ['status', 3]
        ];

        return MoneyRecharge::where($where)
            ->sum('money');
    }

    /**
     * 账单月之前的剩余金额
     *
     * @param array $money_special_last_before_this_month
     *
     * @return array
     */
    private function _getMoneyBeforeThisMonth(array $money_special_last_before_this_month): array
    {
        // 截止到上月（-1 month 之前）的历史充值金until
        $money_recharge = $this->_getRechargeBeforeThisMonth();

        // 截至到上月（-1 month 之前）的历史消费金额
        $money_consume = $this->getConsumeBeforeThisMonth();

        //  处理特殊的金额
        foreach ($money_special_last_before_this_month as $item_special) {
            $money_recharge += $item_special['recharge'];
            $money_consume  += $item_special['consume'];
        }

        // 上月结转的金额
        $money_residue = $money_recharge - $money_consume;
        return compact('money_residue', 'money_consume', 'money_recharge');
    }

    /**
     * 获取客户截至到上上个月的消费金额
     * @return float
     */
    private function getConsumeBeforeThisMonth(): float
    {
        $customer_id = $this->customer_id;
        $money       = MongoBillMonth::where('month', '<', date('Ym', strtotime('last day of -1 month')))
            ->where('customer_id', $customer_id)
            ->sum('money');
        return $money;
    }

    /**
     * 截止到上上个月的历史充值金until
     * @return float
     */
    private function _getRechargeBeforeThisMonth(): float
    {
        // 到去年为止
        $timestamp_this_month = strtotime(date('Y-m-01', strtotime('last day of -1 month')));
        $where                = [
            ['remit_date', '<', $timestamp_this_month],
            ['status', 3],
            ['customer_id', $this->customer_id]
        ];

        return MoneyRecharge::where($where)
            ->sum('money');
    }


    /**
     * 追加一些基本信息
     *
     * @param array $result 计算结果
     * @param array $customer_info
     *
     * @return array
     */
    private function getBaseInfoForHistory(array $result, array $special, array $customer_info): array
    {
        //余额
        $residue_money = implode(explode(',', $result['residue_money']), '');

        // 操作时间
        $today = date('Y年n月d日');

        // 主题
        $theme = $this->getHistoryTheme($customer_info);

        // 上个月客户消耗的金额
        $money_last_month = implode(explode(',', $result['bill_month_data']['consume'])) + $special['bill_month_special_consume_money'] - $special['bill_month_special_recharge_money'];

        // 是否余额不足
        $is_arrearage  = $residue_money < 0;
        $residue_money = number_format(abs($residue_money), 2);

        $month = date('m', strtotime('first day of last month'));

        if ($is_arrearage) {
            $content = <<<HTML
<p>尊敬的客户,您好：</p>
<p>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;感谢贵司一直以来对我司产品的支持与信赖！ 
    贵司{$month}月份消耗总额为：{$money_last_month}元， 
    <strong style="color: rgb(230, 0, 0);">欠费总金额为: {$residue_money}元</strong>。
    消耗详情请查看附件结算单！ 
</p>
<p>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;如与贵司记录相符，请回复邮件确认。 如不符，也请随时告知我司，我们会尽快排查，
    再次核对。 若3个工作日内未回复，我司默认贵司本月调用与我司一致，感谢贵司的理解与支持，祝好！
</p>
HTML;
        } else {
            $consume       = implode(explode(',', $result['total']['consume']), '');
            $remindContent = $consume >= implode(explode(',', $residue_money), '') ? <<<HTML
<p>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;剩余金额预计可用不足一个月，为保证账号正常使用，请及时充值
</p>
HTML
                : '';

            $content = <<<HTML
<p>尊敬的客户,您好：</p>
<p>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;感谢贵司一直以来对我司产品的支持与信赖！ 
    贵司{$month}月份消耗总额为：{$money_last_month}元， 
    剩余总金额为：{$residue_money}元。 消耗详情请查看附件结算单！
</p>
{$remindContent}
<p>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;如与贵司记录相符，请回复邮件确认。 
    如不符，也请随时告知我司，我们会尽快排查，再次核对。 
    若3个工作日内未回复，我司默认贵司本月调用与我司一致，感谢贵司的理解与支持，祝好！
</p>
HTML;
        }
        return [$today, $theme, $content];
    }

    /**
     * 剩余费用转换语句
     *
     * @param float $money_last
     *
     * @return string
     */
    private function _getTheLastMoneyStr(float $money_last): string
    {
        return $money_last > 0 ? "剩余金额为：" . number_format($money_last, 2) : "欠费金额为: " . number_format(-$money_last, 2);
    }

    /**
     * 上个月消费的金额
     * @return float
     */
    private function getConsumptionLastMonth(): float
    {
        $customer_id = $this->customer_id;
        $month       = date('Ym', strtotime('first day of last month'));
        return MongoBillMonth::where(compact('month', 'customer_id'))
            ->sum('money');
    }

    /**
     * 主题
     *
     * @param array $customer_info
     *
     * @return string
     */
    private function getHistoryTheme(array $customer_info): string
    {
        //特殊主题
        $month         = date('m', strtotime('last day of last month'));
        $special_theme = [
            'C20181101YRXUX2' => Common::COMPANY_CN_NAME.'-每月余额表-卡牛',
            'C20181101LHVREG' => Common::COMPANY_CN_NAME.'-每月余额表-随手记',
            'C20181101G14P0P' => '北京羽乐创新科技有限公司-爱财-' . $month . '月账单'
        ];

        if (array_key_exists($customer_info['customer_id'], $special_theme)) {
            return $special_theme[$customer_info['customer_id']];
        }
        return $customer_info['company'] . date('Y年m月', strtotime('first day of last month')) . '对账单--'.Common::COMPANY_CN_NAME;
    }

    /**
     * @return array
     */
    private function getCustomerInfo(): array
    {
        $customer_id = $this->customer_id;
        return Customer::getOneItemByCondition(
            compact('customer_id'),
            [
                'bill_cc_email',
                'bill_email',
                'c_type',
                'name',
                'company',
                'customer_id',
                'id'
            ]
        )
            ->toArray();
    }

    /**
     * 客户账单列表
     *
     * @access public
     *
     * @return array
     **/
    public function customers()
    {
        //$v1Repository = new BillRepository();

        //校验参数
        $this->validateParamsForCustomers();

        //获取客户的消费、余额，并进行过滤
        $data = $this->getCustomerMoneyList()
            ->toArray();

        //总计
        $total = [
            'customer_id'                => '',
            'name'                       => '',
            'company'                    => '总计',
            'money_consumption_in_dates' => array_sum(array_column($data, 'money_consumption_in_dates')),
            'money_residue_now'          => array_sum(array_column($data, 'money_residue_now'))
        ];

        array_unshift($data, $total);
        return $data;

        //$data         = $v1Repository->customers();
//        halt($data);
//
//
//        $expend = $this->getCustomerExpend();
//
//        //合计处理
//        $total = array_shift($data);
//
//        //合计消耗
//        $total_consume = $expend['total_consume'];
//        //合计充值
//        $total_recharge = $expend['total_recharge'];
//        //查询月消耗
//        $consume = $expend['consume'];
//
//
//        //每个客户处理
//        $data = array_map(
//            function ($item) use ($total_consume, $total_recharge, $consume) {
//                $customer_id         = $item['customer_id'];
//                $total_consume_item  = array_key_exists(
//                    $customer_id,
//                    $total_consume
//                ) ? $total_consume[$customer_id] : 0;
//                $total_recharge_item = array_key_exists(
//                    $customer_id,
//                    $total_recharge
//                ) ? $total_recharge[$customer_id] : 0;
//                $consume_item        = array_key_exists($customer_id, $consume) ? $consume[$customer_id] : 0;
//
//                $item['money_consumption_in_dates'] = $item['money_consumption_in_dates'] + $consume_item;
//                $item['money_residue_now']          = $item['money_residue_now'] + $total_recharge_item - $total_consume_item;
//                return $item;
//            },
//            $data
//        );
//
//        $total['money_consumption_in_dates'] = $total['money_consumption_in_dates'] + array_sum($consume);
//        $total['money_residue_now']          = $total['money_residue_now'] + array_sum($total_recharge) - array_sum(
//                $total_consume
//            );
//
//        array_unshift($data, $total);
//
//        return $data;
    }

    /**
     * 获取客户余额
     *
     * @access protected
     *
     * @return array
     **/
    protected function getCustomerMoney()
    {
        //校验参数
        ///$this->validateParamsForCustomers();

        //获取客户的消费、余额，并进行过滤
        //return $this->getCustomerMoneyList();
    }

    /**
     * 计算客户的消费、余额，并进行过滤
     *
     * @access private
     *
     * @return array
     **/
    private function getCustomerMoneyList()
    {
        //获取客户
        $customer_list = $this->getListCustomer();
        $customer_ids  = $customer_list->pluck('customer_id')
            ->toArray();

        //获取客户的消费金额
        $consume_list = $this->getCustomerConsume($customer_ids);

        //获取上月及历史的全部充值金额
        $recharge_list = $this->getCustomerRecharge($customer_ids);

        //获取上月及历史的全部特殊消费
        $special_list = $this->getCustomerSpecial($customer_ids);

        $result = $customer_list->filter(
            function ($item) use ($consume_list, $recharge_list, $special_list) {
                $customer_id = $item->customer_id;
                return array_key_exists($customer_id, $consume_list) || array_key_exists(
                        $customer_id,
                        $consume_list
                    ) || array_key_exists(
                        $customer_id,
                        $consume_list
                    );
            }
        )
            ->map(
                function ($item) use ($consume_list, $recharge_list, $special_list) {
                    $customer_id   = $item->customer_id;
                    $name          = $item->name;
                    $company       = $item->company;
                    $customer_info = $item->toArray();
                    $result        = compact('customer_id', 'name', 'company', 'customer_info');

                    $recharge = 0;
                    if (array_key_exists($customer_id, $recharge_list)) {
                        $recharge = $recharge_list[$customer_id]['recharge'];
                        //$result['recharge_list'] = $recharge_list[$customer_id];
                    }

                    $total_consume  = 0; //总消费 用于计算余额
                    $search_consume = 0;    //查询区间消费

                    if (array_key_exists($customer_id, $consume_list)) {
                        $total_consume  = $consume_list[$customer_id]['total_consume'];
                        $search_consume = $consume_list[$customer_id]['search_consume'];
                        //$result['consume_list'] = $consume_list[$customer_id];

                    }

                    $total_special_recharge  = 0;
                    $total_special_consume   = 0;
                    $search_special_recharge = 0;
                    $search_special_consume  = 0;
                    if (array_key_exists($customer_id, $special_list)) {
                        $total_special_recharge  = $special_list[$customer_id]['total_special_recharge'];
                        $total_special_consume   = $special_list[$customer_id]['total_special_consume'];
                        $search_special_recharge = $special_list[$customer_id]['search_special_recharge'];
                        $search_special_consume  = $special_list[$customer_id]['search_special_consume'];
                    }

                    //余额
                    $balance                              = $recharge + $total_special_recharge - $total_consume - $total_special_consume;
                    $cost                                 = $search_consume + $search_special_consume - $search_special_recharge;
                    $result['money_residue_now']          = round($balance, 2);
                    $result['money_consumption_in_dates'] = round($cost, 2);
                    return $result;
                }
            );

        //余额条件
        $residue_left  = request()->get('residue_left');
        $residue_right = request()->get('residue_right');

        if (empty($residue_left) && empty($residue_right)) {
            return $result;
        }

        return $result->filter(
            function ($item) use ($residue_left, $residue_right) {
                return $item['money_residue_now'] >= $residue_left && $item['money_residue_now'] <= $residue_right;
            }
        );
    }

    /**
     * 获取特殊消费金额
     *
     * @access private
     *
     * @param $customer_ids array 客户ID列表
     *
     * @return array
     **/
    private function getCustomerSpecial($customer_ids)
    {
        $result      = [];
        $resultItem  = [
            'customer_id'                 => '',
            'total_special_recharge'      => 0,     //合计特殊充值
            'total_special_consume'       => 0,     //合计特殊消费
            'search_special_recharge'     => 0,     //查询区间特殊充值
            'search_special_consume'      => 0,     //查询区间特殊消费
            'bill_month_special_recharge' => 0,     //账单月特殊充值
            'bill_month_special_consume'  => 0,     //账单月特殊消费
            'special_recharge_list'       => [],
            'special_consume_list'        => []
        ];
        $month_begin = date('Ym', strtotime(request()->get('month_begin')));
        $month_end   = date('Ym', strtotime(request()->get('month_end')));
        $bill_month  = date('Ym', strtotime('last day of last month'));

        CustomerExpend::whereIn('customer_id', $customer_ids)
            ->where('start_date', '<=', $bill_month)
            ->get()
            ->map(
                function ($item) use (&$result, $resultItem, $month_begin, $month_end, $bill_month) {
                    $customer_id = $item->customer_id;
                    $money       = $item->money;
                    $type        = $item->type;
                    $month       = $item->start_date;

                    if (!array_key_exists($customer_id, $result)) {
                        $result[$customer_id]                = $resultItem;
                        $result[$customer_id]['customer_id'] = $customer_id;
                    }

                    // 为2 代表消耗
                    // 为1 代表充值
                    switch ($type) {
                        case 1:
                            $result[$customer_id]['total_special_recharge'] += $money;

                            if ($month >= $month_begin && $month <= $month_end) {
                                $result[$customer_id]['search_special_recharge'] += $money;
                            }

                            if ($month == $bill_month) {
                                $result[$customer_id]['bill_month_special_recharge'] += $money;
                            }

                            $result[$customer_id]['special_recharge_list'][] = $item->toArray();
                            break;
                        case 2:
                            $result[$customer_id]['total_special_consume'] += $money;

                            if ($month >= $month_begin && $month <= $month_end) {
                                $result[$customer_id]['search_special_consume'] += $money;
                            }

                            if ($month == $bill_month) {
                                $result[$customer_id]['bill_month_special_consume'] += $money;
                            }

                            $result[$customer_id]['special_consume_list'][] = $item->toArray();
                            break;
                    }
                }
            );
        return $result;
    }

    /**
     * 获取充值金额
     *
     * @access private
     *
     * @param $customer_ids array 客户ID列表
     *
     * @return array
     **/
    private function getCustomerRecharge($customer_ids)
    {
        $result     = [];
        $remit_date = strtotime('first day of this month midnight') - 1;

        MoneyRecharge::whereIn('customer_id', $customer_ids)
            ->where('remit_date', '<=', $remit_date)
            ->where('status', '=', 3)
            ->get()
            ->map(
                function ($item) use (&$result) {
                    $customer_id    = $item->customer_id;
                    $receipt_serial = $item->receipt_serial;
                    $remit_serial   = $item->remit_serial;
                    $money          = $item->money;
                    $remit_date     = date('Y-m-d', $item->remit_date);
                    $recharge_list  = compact('customer_id', 'receipt_serial', 'remit_serial', 'money', 'remit_date');
                    if (array_key_exists($customer_id, $result)) {
                        $result[$customer_id]['recharge'] += $money;
                    } else {
                        $result[$customer_id]['recharge'] = $money;
                    }
                    $result[$customer_id]['recharge_list'][] = $recharge_list;
                }
            );
        return $result;
    }


    /**
     * 获取消费金额
     *
     * @access private
     *
     * @param $customer_ids array 客户ID列表
     *
     * @return array
     **/
    private function getCustomerConsume($customer_ids)
    {
        $result      = [];
        $resultItem  = [
            'customer_id'        => '',     //客户ID
            'total_consume'      => 0,      //合计消耗
            'search_consume'     => 0,      //查询消费
            'bill_month_consume' => 0,      //账单月消耗
            'consume_list'       => []      //消耗数据列表
        ];
        $month_begin = date('Ym', strtotime(request()->get('month_begin')));
        $month_end   = date('Ym', strtotime(request()->get('month_end')));
        $bill_month  = date('Ym', strtotime('last day of last month'));
        $model       = MongoBillMonth::whereIn('customer_id', $customer_ids)
            ->get()
            ->map(
                function ($item) use (&$result, $month_end, $month_begin, $bill_month, $resultItem) {
                    $customer_id  = $item->customer_id;
                    $month        = $item->month;
                    $money        = $item->money;
                    $product_id   = $item->product_id;
                    $account_id   = $item->account_id;
                    $uuid         = $item->uuid;
                    $consume_list = compact('customer_id', 'month', 'money', 'product_id', 'account_id', 'uuid');

                    if (!array_key_exists($customer_id, $result)) {
                        $result[$customer_id]                = $resultItem;
                        $result[$customer_id]['customer_id'] = $customer_id;
                    }

                    //合计消耗
                    $result[$customer_id]['total_consume'] += $money;

                    //查询消费
                    if ($month >= $month_begin && $month <= $month_end) {
                        $result[$customer_id]['search_consume'] += $money;
                    }

                    //账单月消费
                    if ($month == $bill_month) {
                        $result[$customer_id]['bill_month_consume'] += $money;
                    }

                    //消费列表
                    $result[$customer_id]['consume_list'][] = $consume_list;
                }
            );
        return $result;
    }

    /**
     * 获取选中的客户列表
     */
    private function getListCustomer()
    {
        // 条件
        $where = $this->getConditionForCustomer();

        return Customer::getListByCondition($where);
    }

    /**
     * 条件
     * @return array
     */
    private function getConditionForCustomer(): array
    {
        $customer_id = trim(request()->get('customer_id'));
        return $customer_id ? compact('customer_id') : [];
    }

    private function getCustomerConsume1()
    {
        // 条件


        // $list_customer_ids = $list_customers->pluck('customer_id')->all();
//        $customer_id = [
//            '$in' => $list_customer_ids
//        ];

        // 分组
        $group = [
            '_id'           => '$customer_id',
            'money_residue' => [
                '$sum' => '$money'
            ]
        ];

        return [
            [
                '$match' => compact('customer_id')
            ],
            [
                '$group' => $group
            ]
        ];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForCustomers()
    {
        // 校验月份
        $this->validateTimeParamsForCustomers();

        // 校验余额范围
        $this->validateResidueParamsForCustomers();
    }

    /**
     * 校验余额范围
     * @throws CustomException
     */
    private function validateResidueParamsForCustomers()
    {
        $residue_left  = trim(request()->get('residue_left'));
        $residue_right = trim(request()->get('residue_right'));
        if (!$residue_left && $residue_right) {
            throw new CustomException('请同时输入月额的左右边界');
        }

        if ($residue_left && !$residue_right) {
            throw new CustomException('请同时输入月额的左右边界');
        }

        if ($residue_left && $residue_right) {
            $diff = $residue_right - $residue_left;
            if ($diff < 0) {
                throw new CustomException('余额右边界不可以小于左边界');
            }
        }
    }

    /**
     * 校验月份
     * @throws CustomException
     */
    private function validateTimeParamsForCustomers()
    {
        $month_begin = trim(request()->get('month_begin'));
        $month_end   = trim(request()->get('month_end'));

        if (!$month_begin) {
            throw new CustomException('开始月份不可以为空');
        }

        if (!$month_end) {
            throw new CustomException('结束月份不可以为空');
        }

        $time_diff = strtotime($month_end) - strtotime($month_begin);
        if ($time_diff < 0) {
            throw new CustomException('开始月份必须小于结束月份');
        }
    }

    /**
     * 获取客户的特殊消耗数据
     *
     * @access public
     *
     * @return array
     **/
    private function getCustomerExpend()
    {
        $prevMonthDate = date('Ym', strtotime('last day of last month'));
        $month_begin   = date('Ym', strtotime(request('month_begin')));
        $month_end     = date('Ym', strtotime(request('month_end')));
        //总特殊消费
        $total_consume = CustomerExpend::selectRaw('SUM(money) as money, customer_id')
            ->where('start_date', '<=', $prevMonthDate)
            ->where('type', '=', 2)
            ->groupBy('customer_id')
            ->pluck('money', 'customer_id')
            ->toArray();

        //总特殊充值
        $total_recharge = CustomerExpend::selectRaw('SUM(money) as money, customer_id')
            ->where('start_date', '<=', $prevMonthDate)
            ->where('type', '=', 1)
            ->groupBy('customer_id')
            ->pluck('money', 'customer_id')
            ->toArray();

        //查询范围内的特殊消费
        $consume = CustomerExpend::selectRaw('SUM(money) as money, customer_id')
            ->where('start_date', '>=', $month_begin)
            ->where('start_date', '<=', $month_end)
            ->where('type', '=', 2)
            ->groupBy('customer_id')
            ->pluck('money', 'customer_id')
            ->toArray();

        return compact('consume', 'total_recharge', 'total_consume');
    }

    /**
     * 获取一个客户下的产品账单信息
     *
     * @param string $customer_id
     *
     * @return array
     * @throws CustomException
     */
    public function getProductBillOfCustomer(string $customer_id): array
    {

        $v1Repository = new BillRepository();
        $data         = $v1Repository->getProductBillOfCustomer($customer_id);
        $expend       = $this->getOneCustomerExpend($customer_id);

        $data = array_map(
            function ($item) use ($expend) {
                $item['operator'] = $item['account_name'] != '总计';
                $item['money']    = $item['account_name'] != '总计' ? $item['money'] : round(
                    $item['money'] + array_sum(
                        array_column(
                            $expend,
                            'money'
                        )
                    ),
                    2
                );
                return $item;
            },
            $data
        );


        array_walk(
            $expend,
            function ($item) use (&$data) {
                $data[] = [
                    'account_name'   => $item['start_date'] . $item['name'],
                    'product_name'   => '--',
                    'section_number' => 0,
                    'money'          => $item['money'],
                    'operator'       => false
                ];
            }
        );
        return $data;
    }

    /**
     * 获取某客户的特殊消耗数据
     *
     * @access public
     *
     * @return array
     **/
    private function getOneCustomerExpend($customer_id)
    {
        $month_begin = date('Ym', strtotime(trim(request()->get('month_begin'))));
        $month_end   = date('Ym', strtotime(trim(request()->get('month_end'))));

        return CustomerExpend::where('start_date', '<=', $month_end)
            ->where('start_date', '>=', $month_begin)
            ->where('customer_id', $customer_id)
            ->where('type', '=', 2)
            ->select('money', 'start_date', 'name')
            ->get()
            ->toArray();
    }

    /**
     * 产品对账单
     * @return array
     * @throws CustomException
     */
    public function getBillProductList(): array
    {
        $itemProductBill = [
            'product_id'     => null,
            'product_name'   => null,
            'operator'       => 1,
            'section_number' => 0,
            'money'          => 0
        ];

        //获取产品ID => 产品名称对应列表
        $product_id_name_map = Product::pluck('product_name', 'product_id')
            ->toArray();

        //获取产品ID => 父产品ID的对应列表
        $product_id_father_id_map = Product::pluck('father_id', 'product_id')
            ->map(
                function ($item, $product_id) {
                    return $item && $item != 200 ? $item : $product_id;
                }
            )
            ->toArray();

        $result = [];


        //汇总每个产品的计费用量、费用
        $month_begin = request()->get('month_begin', date('Y-m', strtotime('first day of last month')));
        $month_begin = date('Ym', strtotime($month_begin));
        $month_end   = request()->get('month_end', date('Y-m', strtotime('first day of last month')));
        $month_end   = date('Ym', strtotime($month_end));
        MongoBillMonth::where('month', '>=', $month_begin)
            ->where('month', '<=', $month_end)
            ->get()
            ->map(
                function ($item) use (&$result, $itemProductBill, $product_id_name_map, $product_id_father_id_map) {
                    $product_id = $item->product_id;
                    $father_id  = $product_id_father_id_map[$product_id];
                    if (!array_key_exists($father_id, $result)) {
                        $result[$father_id]                 = $itemProductBill;
                        $result[$father_id]['product_id']   = $father_id;
                        $result[$father_id]['product_name'] = $product_id_name_map[$father_id];
                    }
                    $result[$father_id]['section_number'] += $item['section_invoked_number'];
                    $result[$father_id]['money']          += $item['money'];
                    return $item;
                }
            );

        //获取特殊消耗的合计
        $money = CustomerExpend::where('start_date', '<=', $month_end)
            ->where('start_date', '>=', $month_begin)
            ->where('start_date', '<=', $month_end)
            ->where('type', '=', 2)
            ->sum('money');
        
        $money1 = CustomerExpend::where('start_date', '<=', $month_end)
								->where('start_date', '>=', $month_begin)
								->where('start_date', '<=', $month_end)
								->where('type', '=', 1)
								->sum('money');
        

        $expend = [
            'product_name'   => '特殊消耗',
            'operator'       => 2,
            'section_number' => 0,
            'money'          => ($money - $money1),
            'product_id'     => ''
        ];
        //合计数据
        $total = [
            'product_name'   => '合计',
            'operator'       => 0,
            'section_number' => array_sum(array_column($result, 'section_number')),
            'money'          => round(array_sum(array_column($result, 'money')) + $money - $money1, 2),
            'product_id'     => ''
        ];


        return array_merge([$total], [$expend], array_values($result));
    }
}