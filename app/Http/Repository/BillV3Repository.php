<?php

namespace App\Http\Repository;


use App\Define\Common;
use App\Define\SourceDefine;
use App\Jobs\Invoice\UpdateInvoiceConsumeAssembleJob;
use App\Models\Account;
use App\Models\Approval;
use App\Models\BillCustomerIncomeV2;
use App\Models\BillOperatorMonthCheck;
use App\Models\BillOperatorMonthLogs;
use App\Models\BillProductIncome;
use App\Models\ChannelInterface;
use App\Models\ChannelInterfaceMapping;
use App\Models\ChannelMapping;
use App\Models\Common\CommonEnumModel;
use App\Models\Crs\SystemDept;
use App\Models\Customer\CustomerGroup;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\ProductIdConversion;
use App\Models\BillCustomerIncome;
use App\Models\Customer;
use App\Models\Remit;
use App\Models\RemitSplitPrice;
use App\Models\LogsMailCustomerIncome;
use App\Models\SystemUser;
use App\Providers\BillIncome\BillStatistics\BalanceService;
use App\Providers\BillIncome\BillStatistics\IncomeService;
use App\Providers\BillIncome\BillStatistics\RechargeService;
use App\Providers\BillIncome\CreateBillIncome;
use App\Providers\BillIncomeV2\CalculateProductIncomeService;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\Income\CustomerIncomeRepository;
use App\Repositories\Income\ProductIncomeRepository;
use App\TraitUpgrade\ExcelTrait;
use App\TraitUpgrade\CurlTrait;
use App\Models\SystemSession;
use App\Models\RerunBillRecord;
use App\Models\CustomerBillAdjust;
use App\Utils\Helpers\Func;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Exports\CustomerExpendExport;
use App\Models\BillProductIncomeV2;
use App\Models\ConfigOperator;
use App\Models\ConfigPriceCustomer;
use Maatwebsite\Excel\Facades\Excel;

class BillV3Repository extends PublicRepository
{
	use ExcelTrait;
    use CurlTrait;


	/**
	 * 重跑营收账单
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 13:32
	 *
	 * @return integer
	 */
	public function reCreateBillIncome()
	{
		$status = $this->checkReCreateBillIncomeParams();
		if ($status) {
			return $status;
		}
		
		$params = $this->getReCreateBillIncomeParams();
		(new CreateBillIncome($params['date'], $params['days'], $params['apikeys'], $params['product_ids']))->run();
		
		return 0;
	}
	
	/**
	 * 获取客户对账单列表数据
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/11/4 14:18
	 *
	 * @return array
	 */
	public function customerStatementList()
	{
		//校验参数
		$result = $this->checkCustomerStatementListParams();
		if (!is_array($result)) {
			return $result;
		}

		//参数
		$params = $result;
		// 导出结算单--朴道对账需求
		if ($params['export_statement']=='1') {
			//$params['source']=1;
		}

		//获取消费数据
		$consume = $this->getConsumeService($params['start_date'], $params['end_date'], $params['source'])
						->getCustomerIncomeV2($params['customer_ids']);

		//获取余额数据
		$balance = $this->getBalanceService($params['end_date'], null, $params['source'])
						->getCustomerBalanceV2($params['customer_ids']);

		// 获取客户列表
        $customer_list = Customer::select(['customer_id', 'name', 'email_type', 'status', 'operator', 'salesman','company','agent_company','group_id'])->get()->toArray();
        //$company_list = array_column($customer_list, 'company', 'customer_id');
        $company_list = [];
        foreach ($customer_list as $item){
            $company_list[$item['customer_id']] = ['company' => $item['company'], 'agent_company' => $item['agent_company']];
        }

		$customer_list = array_column($customer_list, null, 'customer_id');

		//组装两部分数据,并整理
		$list = $this->combineCustomerStatementList($consume, $balance, $customer_list, $params['source']);
		//过滤数据
		$list = $this->filterCustomerStatementList($list, $params);
		
		//求合计数据
		$total = $this->getCustomerStatementListTotal($list);
		
		//对数据进行排序、分割、格式化
		$result = $this->sortCustomerStatementList($list, $params);
		
		// 导出结算单--朴道对账需求
		if($params['export_statement']=='1'){
			$result['total'] = $total;
			
			// 获取客户-父产品的计费标准
			//$accordMap = $this->getAccountFatheridAccord($params['end_date'],[]);

			// 查询客户-产品维度
			$details = $this->CustomerBillIncomeV2MonthForPudao($params['start_date'],$params['end_date'],[],$params['source']);

			$sourceFeeMap = CommonEnumModel::getTypeMaps('agent_income_fee');   //征信机构扣除后收入费率
            //********之前 朴道比例为0.94
            if($params['end_date']<'********'){
                if(isset($sourceFeeMap[1])){
                    $sourceFeeMap[1]=0.94;
                }
            }
            $sourceFeeRate = isset($sourceFeeMap[$params['source']])?$sourceFeeMap[$params['source']]:1;

            $sourceMap = CommonEnumModel::getListByType(1);
            $sourceMap = array_column($sourceMap, 'value', 'name');

            $source_name = $sourceMap[$params['source']];

            //合并数据
			$result['details'] = $this->formaterPudaoStatement($details,$company_list,$sourceFeeRate, $params['source']);
			$result['total']['money_source']=array_sum(array_column($result['details'], 'money_source'));
			// 绘制xlsx ajax 返回
			return  $this->createExcelForPudaoStatementMail($source_name,$result,$params['start_date'],$params['end_date']);
			
		}
		//计算汇总数据
		
		array_unshift($result['items'], $total);
		
		return $result;
	}

	/**
	 * 账号产品维度的计费配置字段 如计费标准
	 *
	 * @param [type] $start_date
	 * @param [type] $customer_ids
	 * @return void
	 */
	public function getAccountFatheridAccord($start_date,$customer_ids,$column = 'accord'){
		$result=[];
		ConfigPriceCustomer::where('start_date', '<=', $start_date)
		->where(function ($query) use ($customer_ids) {
			$apikeys = [];
			if (!empty($customer_ids)) {
				$apikeys = Account::whereIn('customer_id', $customer_ids)
								  ->pluck('apikey');
			}
			if (!empty($apikeys)) {
				$query->whereIn('apikey', $apikeys);
			}
		})
		->orderBy('start_date', 'desc')
		->get()
		->map(function ($item) use (&$result,$column) {
			$apikey    = $item['apikey'];
			$father_id = $item['father_id'];
			$key       = $apikey . '_' . $father_id;
			if (!array_key_exists($key, $result)) {
				$result[$key] = $item->$column;
			}
		});

		return $result;
	}

    public function getConfigPriceByIds($config_price_ids = []){

        $config = ConfigPriceCustomer::select(['id', 'product_ids', 'accord', 'mode', 'diff_operator', 'methods'])
            ->whereIn('id', $config_price_ids)
            ->get()->toArray();

        $config = array_column($config, null, 'id');

        return $config;
    }

	public function formaterPudaoStatement($data,$company_list,$agent_income_fee=1, $source=0)
	{
		$operatorMap = ConfigOperator::pluck('name','operator')->toArray();
        $result = [];
		foreach ($data as $uk => $value) {
			[$customerid] = explode('_',$uk);
			if(isset($company_list[$customerid])){
				$value['company'] = $company_list[$customerid]['company'];//羽乐科技对应的签约公司名称
                $agent_company = json_decode($company_list[$customerid]['agent_company'], true);
                //按来源对应的签约公司名称
                $value['pd_company'] = $agent_company[$source] ?? '';
			}else{
                $value['company'] = '';
                $value['pd_company'] = '';
            }
			$value['price'] = array_unique($value['price']);
			$value['money_source'] = bcdiv($value['money'],$agent_income_fee,2);
			$value ['agent_income_fee'] = $agent_income_fee;
			$value['operator'] = isset($operatorMap[$value['operator']])?$operatorMap[$value['operator']]:$value['operator'];
			$result[]=$value;
		}
		return $result;
	}
	/**
	 * 朴道对账单 客户产品维度
	 *
	 * @param array $customer_ids
	 * @return void
	 */
	public function CustomerBillIncomeV2MonthForPudao($start_date,$end_date,$customer_ids = [],$source='',$accordMap=[])
    {
        $config_price_ids = [];
		$result = [];
        //BillProductIncomeV2::select(DB::raw('SUM(bill_product_income_v2.money) as money, SUM(bill_product_income_v2.number) as number, bill_product_income_v2.apikey, bill_product_income_v2.father_id, bill_product_income_v2.call_product_id as product_id,bill_product_income_v2.operator,bill_customer_income_id,bill_customer_income_v2.price,config_price_customer.accord,config_price_customer.mode,config_price_customer.diff_operator'))
        $list = BillProductIncomeV2::select(DB::raw('SUM(bill_product_income_v2.money) as money, SUM(bill_product_income_v2.number) as number, bill_product_income_v2.apikey, bill_product_income_v2.father_id, bill_product_income_v2.call_product_id as product_id,bill_product_income_v2.operator,bill_customer_income_id,bill_customer_income_v2.price,config_price_customer.id as config_id'))
            ->where(function ($query) use ($customer_ids) {
                if ($customer_ids) {
                    $apikeys = Account::whereIn('customer_id', $customer_ids)
                        ->pluck('apikey');
                    if (!empty($apikeys)) {
                        $query->whereIn('apikey', $apikeys);
                    }
                }
            })
            ->where('bill_product_income_v2.source',$source)
            ->where('bill_product_income_v2.date', '>=', $start_date)
            ->where('bill_product_income_v2.date', '<=', $end_date)
			->leftJoin('bill_customer_income_v2', 'bill_customer_income_v2.id', '=', 'bill_product_income_v2.bill_customer_income_id')
			->leftJoin('config_price_customer', 'config_price_customer.id', '=', 'bill_customer_income_v2.config_price_customer_id')

            ->groupBy('bill_product_income_v2.apikey','bill_product_income_v2.father_id','bill_product_income_v2.call_product_id','bill_product_income_v2.operator','bill_customer_income_v2.price','config_price_customer.id')
			->orderBy(DB::raw('bill_product_income_v2.apikey,bill_product_income_v2.call_product_id,bill_product_income_v2.operator,bill_customer_income_v2.price'), 'desc')
			->get()
            ->toArray();

        foreach ($list as $item){
            if(!in_array($item['config_id'], $config_price_ids)){
                $config_price_ids[] = $item['config_id'];
            }
        }

        //根据计费配置id，一次性批量获取
        $config_price_list = $this->getConfigPriceByIds($config_price_ids);

        foreach ($list as $item){
            $apikey                            = $item['apikey'];
            $customer_id                       = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
            $truefid = $father_id = $item['father_id'];
            $product_id = $item['product_id'];

            // 区分运营商只区分到移动 联通 电信
            $operator = $this->convertOperator($item['operator']);

            //下面几个 ?? 都是处理没有计费配置的情况
            //计费模式：1-独立子产品 2-打包 3-汇总子产品
            $mode = $config_price_list[$item['config_id']]['mode'] ?? -1;
            //是否区分运营商（0-不区分 1-区分）
            $diff_operator = $config_price_list[$item['config_id']]['diff_operator'] ?? 0;
            //计费依据（1-成功调用量，2-查得量， 3-计费量）
            $accord = $config_price_list[$item['config_id']]['accord'] ?? -1;
            //计费方式（0-不计费 1-包年 2-固定价格 3-累进阶梯 4-到达阶梯）
            $methods = $config_price_list[$item['config_id']]['methods'] ?? 0;

            $price = $item['price'];
            if($mode == 2){// 打包计费 展示打包子产品
                $product_id = $config_price_list[$item['config_id']]['product_ids'] ?: $father_id;
            }

            if($diff_operator == 0){// 不区分运营商
                $operator = '不区分';
            }

            //因为不为同一条计费配置的话可能会出现价格没变，计费方式、计费依据等变了，因此要把这些维度也放进来才能体现差异
            $unique_key = $customer_id.'_'.$product_id.'_'.$operator.'_'.$price.'_'.$mode.'_'.$diff_operator.'_'.$accord.'_'.$methods;

            //初始化结果集
            if(!isset($result[$unique_key])){
                $this->initResult($result,$unique_key);
            }
            $result[$unique_key]['father_id'] =$father_id;
            $result[$unique_key]['customer_id'] =$customer_id;

            $result[$unique_key]['company'] ='';
            $result[$unique_key]['customer_name'] =  RedisCache::instance('customerId_customerName_mapping')->get($customer_id);

            $result[$unique_key]['father_name'] =  RedisCache::instance('productId_productName_mapping')->get($father_id);

            $product_name = '';
            $product_ids = explode(',', $product_id);
            foreach ($product_ids as $pid){
                $product_name = $product_name.','.RedisCache::instance('productId_productName_mapping')->get($pid);
            }
            $result[$unique_key]['product_name'] = ltrim($product_name, ',');

            $result[$unique_key]['product_ids'] =  $product_id;
            $result[$unique_key]['pd_product_ids'] = '';
            $result[$unique_key]['operator'] =  $operator;

            $result[$unique_key]['price'][]  = $item['price'];

            if($mode == 0){
                $result[$unique_key]['mode'] = '无';
            }else if($mode == 1){
                $result[$unique_key]['mode'] = '独立子产品';
            }else if($mode == 2){
                $result[$unique_key]['mode'] = '打包';
            }else if($mode == 3){
                $result[$unique_key]['mode'] = '汇总子产品';
            }else{
                $result[$unique_key]['mode'] = '未知';
            }

            if($accord == 1){
                $result[$unique_key]['fee_type'] = '查询';
            }else if($accord == 2){
                $result[$unique_key]['fee_type'] = '查得';
            }else if($accord == 3){
                $result[$unique_key]['fee_type'] = '计费量';
            }else{
                $result[$unique_key]['fee_type'] = '未知';
            }

            //0-不计费 1-包年 2-固定价格 3-累进阶梯 4-到达阶梯
            if($methods == 0){
                $result[$unique_key]['methods'] = '不计费';
            }else if($methods == 1){
                $result[$unique_key]['methods'] = '包年';
            }else if($methods == 2){
                $result[$unique_key]['methods'] = '固定价格';
            }else if($methods == 3){
                $result[$unique_key]['methods'] = '累进阶梯';
            }else if($methods == 4){
                $result[$unique_key]['methods'] = '到达阶梯';
            }else{
                $result[$unique_key]['methods'] = '未知';
            }

            $result[$unique_key]['money']     = bcadd($result[$unique_key]['money'], $item['money'], 4);
            $result[$unique_key]['number']    = bcadd($result[$unique_key]['number'], $item['number'],0);
        }

		//客户月度平账数据,分加减两部分
		$where = [
			['profile_show_date', '>=', $start_date],['profile_show_date', '<=', $end_date],
			['source',$source]
		];
		$sub = CustomerExpend::getDateExpendByCustomerProduct(array_merge($where, [['type', 1]]), []);
		$add = CustomerExpend::getDateExpendByCustomerProduct(array_merge($where, [['type', 2]]), []);
		
		foreach ($sub as $item) {
			
			$operator='特殊消耗';
			$unique_key = $item['customer_id'].'_'.$item['product_id'].'_'.$operator;
			if(!isset($result[$unique_key])){
				$this->initResult($result,$unique_key);
			}

			$product_id = $item['product_id'];
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
			$father_id = $father_id?$father_id:$item['product_id'];
			$customer_id = $item['customer_id'];
			$result[$unique_key]['customer_name'] =  RedisCache::instance('customerId_customerName_mapping')->get($customer_id);

			$result[$unique_key]['father_id'] =$father_id;	
			$result[$unique_key]['customer_id'] =$customer_id;	

			$result[$unique_key]['father_name'] =  RedisCache::instance('productId_productName_mapping')->get($father_id);
			$result[$unique_key]['product_name'] =  RedisCache::instance('productId_productName_mapping')->get($product_id);
			$result[$unique_key]['operator'] =  $operator;

            $result[$unique_key]['product_ids'] =  $product_id;
            $result[$unique_key]['pd_product_ids'] = '';

			$result[$unique_key]['number'] -= $item['fee_number'];
            $result[$unique_key]['money'] -= round($item['money'],3);
        }
        foreach ($add as $item) {
			$operator='特殊消耗';
			$unique_key = $item['customer_id'].'_'.$item['product_id'].'_'.$operator;
			if(!isset($result[$unique_key])){
				$this->initResult($result,$unique_key);
			}

            $product_id = $item['product_id'];
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
			$father_id = $father_id?$father_id:$item['product_id'];
			$customer_id = $item['customer_id'];
	
			$result[$unique_key]['customer_name'] =  RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
			$result[$unique_key]['father_id'] =$father_id;	
			$result[$unique_key]['customer_id'] =$customer_id;	

			$result[$unique_key]['father_name'] =  RedisCache::instance('productId_productName_mapping')->get($father_id);
			$result[$unique_key]['product_name'] =  RedisCache::instance('productId_productName_mapping')->get($product_id);
			$result[$unique_key]['operator'] =  $operator;

            $result[$unique_key]['product_ids'] =  $product_id;
            $result[$unique_key]['pd_product_ids'] = '';

            $result[$unique_key]['number'] += $item['fee_number'];
            $result[$unique_key]['money'] += $item['money'];
        }
		$customer_id_sort=array_column($result,'customer_id');
		array_multisort($customer_id_sort,SORT_DESC,$result);
        return $result;
    }

	// 区分运营商只区分到移动 联通 电信，为空或者其他值则原样返回
	public function convertOperator($operator){
		if(empty($operator)){
			return $operator;
		}
		if(strpos($operator,'CMCC')){
			return 'CMCC';
		}
		if(strpos($operator,'CTCC')){
			return 'CTCC';
		}		
		if(strpos($operator,'CUCC')){
			return 'CUCC';
		}
		return $operator;
	}
	
	public function initResult(&$result,$unique_key){
		$result[$unique_key]['father_id'] ='';	
		$result[$unique_key]['customer_id'] ='';	
		$result[$unique_key]['company'] ='';	
		$result[$unique_key]['customer_name'] =  '';
		$result[$unique_key]['father_name'] =  '';
		$result[$unique_key]['product_name'] =  '';
		$result[$unique_key]['product_ids'] =  '';//子产品id
		$result[$unique_key]['pd_product_ids'] =  '';//朴道产品代码
		$result[$unique_key]['operator'] =  '';
		$result[$unique_key]['methods']= '';//计费方式
		$result[$unique_key]['mode']= '';//计费模式
		$result[$unique_key]['fee_type']= '';
		$result[$unique_key]['money']     =  number_format(0,2);
		$result[$unique_key]['number']    =  number_format(0,2);
		$result[$unique_key]['agent_income_fee']    =  number_format(1,2);
		$result[$unique_key]['money_source']     =  number_format(0,2);
		$result[$unique_key]['price']    = [];

	}
	
	/**
	 * 计算合计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/20 11:32
	 *
	 * @param $list array 列表数据
	 *
	 * @return array
	 */
	protected function getCustomerStatementListTotal($list)
	{
		return [
			'customer_id'   => '',
			'customer_name' => '合计',
			'consume'       => array_sum(array_column($list, 'consume')),
			'balance'       => '',
		];
	}
	
	/**
	 * 获取每个客户最近发送邮件的日期，并计算到当前时间的距离
	 *
	 * @access   protected
	 * @param $customer_id string 客户ID
	 * @param string $source string 客户类型
	 *
	 * @return array
	 */
	protected function getLatelySendTime($customer_id = null, $source = '')
	{
		$current = time();
		$data    = LogsMailCustomerIncome::select(DB::raw('MAX(send_time) as time, customer_id'))
										 ->where(function ($query) use ($customer_id) {
											 if ($customer_id) {
												 $query->where('customer_id', $customer_id);
											 }
										 })
                                        ->where(function ($query) use ($source) {
                                            if ($source !== '') {
                                                $query->where('source', $source);
                                            }
                                        })
										 ->groupBy('customer_id')
										 ->get()
										 ->map(function ($item) use ($current) {
											 $time        = $item->time;
											 $interval    = $this->intervalToString(abs($current - $time));
											 $customer_id = $item->customer_id;
			
											 return compact('time', 'interval', 'customer_id');
										 })
										 ->toArray();
		
		return array_column($data, null, 'customer_id');
	}
	
	/**
	 * 将一个时间间隔转化为一个汉字提示
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/16 14:07
	 *
	 * @param $interval string 间隔时间
	 *
	 * @return string
	 */
	protected function intervalToString($interval)
	{
		if ($interval <= 60) {
			return '刚刚';
		} else if ($interval <= 3600) {
			return floor($interval / 60) . '分钟前';
		} else if ($interval <= 86400) {
			return floor($interval / 3600) . '小时前';
		} else {
			return floor($interval / 86400) . '天前';
		}
	}
	
	/**
	 * 对客户对账单列表数据进行排序
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 18:14
	 *
	 * @param $list   array 列表数据
	 * @param $params array 参数
	 *
	 * @return array
	 */
	protected function sortCustomerStatementList($list, $params)
	{
		//默认使用消费排序
		$field = $params['field'];
		$type  = 'asc' == $params['order'] ? SORT_ASC : SORT_DESC;
		
		array_multisort(array_column($list, $field), $type, $list);
		
		//分页
		//return $list;
		return $this->splitPageForCompleteInfo($list);
	}
	
	/**
	 * 过滤客户对账单数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 18:12
	 *
	 * @param $list   array 列表数据
	 * @param $params array 参数
	 *
	 * @return array
	 */
	protected function filterCustomerStatementList($list, $params)
	{
		$min_balance = array_get($params, 'min_balance');
		$max_balance = array_get($params, 'max_balance');
		$min_consume = array_get($params, 'min_consume');
		$max_consume = array_get($params, 'max_consume');

		$email_type = isset($params['email_type']) ? $params['email_type'] : '';
		$status = isset($params['status']) ? $params['status'] : '';

		$area_person = isset($params['area_person']) ? $params['area_person'] : [];

		return array_filter($list, function ($item) use ($min_balance, $max_balance, $min_consume, $max_consume, $email_type, $status, $area_person) {
			$balance = $item['balance'];
			$consume = $item['consume'];

			if (0 == $balance && 0 == $consume) {
				return false;
			}
			
			if (!is_null($min_consume) && $consume < $min_consume) {
				return false;
			}
			
			if (!is_null($max_consume) && $consume > $max_consume) {
				return false;
			}
			
			if (!is_null($min_balance) && $balance < $min_balance) {
				return false;
			}
			
			if (!is_null($max_balance) && $balance > $max_balance) {
				return false;
			}

			if ($email_type && $email_type != $item['email_type']) {
			    return false;
			}

			if ($status !== '' && $status != $item['status']) {
			    return false;
            }

			if ($area_person && !in_array($item['salesman'], $area_person)) {
			    return false;
            }
			return true;
		});
	}
	
	/**
	 * 组装客户对账单列表数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 17:57
	 *
	 * @param $consume array 消费数据
	 * @param $balance array 余额数据
	 *
	 * @return array
	 */
	protected function combineCustomerStatementList($consume, $balance, $customer_list, $source = '')
	{
		//获取每个客户的历史最近发送邮件日期，并计算到当前时间的距离
		//每个客户都需要补充这个数据，如果不存在，则记录“未发送”
		$latelySendTime = $this->getLatelySendTime(null, $source);
		//获取所有客户的公司名称
		$companyInfo = $this->getCompanyInfo();

        //获取客户主体
        $group_list = CustomerGroup::getGroupList();
        $group_list = array_column($group_list,'group_name','group_id');

		$result = array_map(function ($item) use ($latelySendTime, $companyInfo) {
			$customer_id   = $item['customer_id'];
			$customer_name = RedisCache::instance('customerId_customerName_mapping')
									   ->get($customer_id);
			$company       = array_get($companyInfo, $customer_id, '未知');
			$balance       = Func::round_number_keep_2($item['money']);
			$consume       = '0.00';
			
			$interval = array_get(array_get($latelySendTime, $customer_id, []), 'interval', '未发送过');

			return compact('customer_id', 'customer_name', 'consume', 'balance', 'interval', 'company');
		}, $balance);
		
		array_walk($consume, function ($item) use (&$result, $customer_list,$group_list) {
			$customer_id = $item['customer_id'];
			if (!array_key_exists($customer_id, $result)) {
				$customer_name        = RedisCache::instance('customerId_customerName_mapping')
												  ->get($customer_id);
				$balance              = '0.00';
				$consume              = Func::round_number_keep_2($item['money']);
				$result[$customer_id] = compact('customer_id', 'customer_name', 'consume', 'balance');
			} else {
				$result[$customer_id]['consume'] = Func::round_number_keep_2($item['money']);
			}
			// 邮件类型
            $email_type = isset($customer_list[$customer_id]['email_type']) ? $customer_list[$customer_id]['email_type'] : 1;
            $result[$customer_id]['email_type'] = $email_type;
            $result[$customer_id]['status'] = $customer_list[$customer_id]['status'];
            $result[$customer_id]['operator'] = $customer_list[$customer_id]['operator'];
            $result[$customer_id]['salesman'] = $customer_list[$customer_id]['salesman'];
			$result[$customer_id]['group_name'] = $group_list[$customer_list[$customer_id]['group_id']]??'';
		});
		
		return $result;
	}
	
	/**
	 * 获取所有客户->公司的映射数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/2/1 10:23
	 *
	 * @return array
	 */
	private function getCompanyInfo()
	{
		return Customer::where('is_delete', 0)
					   ->pluck('company', 'customer_id')
					   ->toArray();
	}
	
	/**
	 * 校验并获取客户对账单列表的参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 14:19
	 *
	 * @return array|integer integer代表校验失败
	 */
	protected function checkCustomerStatementListParams()
	{
		//参数
		$params = [];
		
		//月份区间
		$month_section = $this->request->post('month_section');
		if (!$month_section) {
			return 13010;
		}
		if (!preg_match('/^\d{4}\-\d{2}\s\-\s\d{4}\-\d{2}$/', $month_section)) {
			return 13011;
		}
		[$start_month, $end_month] = explode(' - ', $month_section);
		$params['start_date'] = date('Ymd', strtotime('first day of this month', strtotime($start_month . '-01')));
		$params['end_date']   = date('Ymd', strtotime('last day of this month', strtotime($end_month . '-01')));
		//签约主体
		$group_id               = $this->request->post('group_id');
		$sign_corp_customerids=[];
		if(!empty($group_id)){
            $dsc_where = [];
            if(!empty($group_id)){
                $dsc_where[] = ['group_id',$group_id];
            }
			$sign_corp_customerids = Customer::where($dsc_where)->pluck('customer_id');
		}
		//客户ID
		$customer_id            = $this->request->post('customer_id');
		// 两种条件都有的场景
		if($customer_id&&$sign_corp_customerids){
			$customer_id = in_array($customer_id,$sign_corp_customerids)?$customer_id:null;
		}
		$params['customer_ids'] = $customer_id ? [$customer_id] : $sign_corp_customerids;

		//来源
        $source = $this->request->post('source', '');

        $sourceMap = CommonEnumModel::getListByType(1);
        $sourceMap = array_column($sourceMap, 'name');
        if (!in_array($source, $sourceMap)) {
            return 13012;
        }
        $params['source'] = $source;

		$params['export_statement'] = $this->request->post('export_statement', '0');

		//需要过滤的余额范围
		$min_balance = trim($this->request->post('min_balance'));
		$max_balance = trim($this->request->post('max_balance'));
		if (preg_match('/^\-?\d+(\.\d+)?$/', $min_balance)) {
			$params['min_balance'] = $min_balance;
		}
		if (preg_match('/^\-?\d+(\.\d+)?$/', $max_balance)) {
			$params['max_balance'] = $max_balance;
		}
		
		if (array_key_exists('min_balance', $params) && array_key_exists('max_balance', $params)) {
			//如果都存在的话，需要校验一下谁大谁小,重新设置变量
			[$params['min_balance'], $params['max_balance']] = [
				min($params['min_balance'], $params['max_balance']),
				max($params['min_balance'], $params['max_balance']),
			];
		}
		
		//需要过滤的消费范围
		$min_consume = trim($this->request->post('min_consume'));
		$max_consume = trim($this->request->post('max_consume'));
		if (preg_match('/^\-?\d+(\.\d+)?$/', $min_consume)) {
			$params['min_consume'] = $min_consume;
		}
		if (preg_match('/^\-?\d+(\.\d+)?$/', $max_consume)) {
			$params['max_consume'] = $max_consume;
		}
		if (array_key_exists('min_consume', $params) && array_key_exists('max_consume', $params)) {
			//如果都存在的话，需要校验一下谁大谁小
			[$params['min_consume'], $params['max_consume']] = [
				min($params['min_consume'], $params['max_consume']),
				max($params['min_consume'], $params['max_consume']),
			];
		}
		
		//排序字段
		$field           = $this->request->post('field', 'consume');
		$params['field'] = in_array($field, ['consume', 'balance']) ? $field : 'consume';
		
		//排序方式
		$order           = $this->request->post('order', 'desc');
		$params['order'] = in_array($order, ['asc', 'desc']) ? $order : 'desc';

		// 邮件标准类型
        $params['email_type'] = $this->request->post('email_type', '');

        $params['status'] = $this->request->post('status', '');

        $area = $this->request->post('area', '');
        if ($area) {
            $person = SystemUser::getUserInfoByDeptId($area);
            $params['area_person'] = $person ? array_column($person, 'username') : [];
        }

		return $params;
	}
	
	/**
	 * 校验重跑账单参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 13:37
	 *
	 * @return integer
	 */
	protected function checkReCreateBillIncomeParams()
	{
		$apikeys = $this->request->post('apikeys');
		if (empty($apikeys)) {
			return 13001;
		}
		if (!is_array($apikeys)) {
			return 13002;
		}
		$product_ids = $this->request->post('product_ids');
		if (empty($product_ids)) {
			return 13003;
		}
		if (!is_array($product_ids)) {
			return 13004;
		}
		$date = $this->request->post('date');
		if (empty($date)) {
			return 13005;
		}
		if (empty(strtotime($date)) || !preg_match('/^\d{8}$/', $date)) {
			return 13006;
		}
		
		return 0;
	}
	
	/**
	 * 获取重跑账单参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 13:52
	 *
	 * @return array
	 */
	protected function getReCreateBillIncomeParams()
	{
		$apikeys     = $this->request->post('apikeys');
		$product_ids = $this->request->post('product_ids');
		$date        = $this->request->post('date');
		$today       = date('Ymd');
		$days        = $this->request->post('days', (strtotime($today) - strtotime($date)) / 86400);
		
		return compact('apikeys', 'product_ids', 'date', 'days');
	}
	
	/**
	 * 获取消费服务
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 15:05
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 * @param $source     string 客户来源
	 *
	 * @return IncomeService
	 */
	protected function getConsumeService($start_date, $end_date, $source = '')
	{
		return new IncomeService($end_date, $start_date, $source);
	}
	
	/**
	 * 获取余额服务
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/4 15:40
	 *
	 * @param $date   integer 余额查看日期
	 *
	 * @return BalanceService
	 */
	protected function getBalanceService($date, $startDate = '', $source = '')
	{
		return new BalanceService($date, $startDate, $source );
	}
	
	/**
	 * 获取充值服务
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 17:37
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return
	 */
	protected function getRechargeService($start_date, $end_date, $source = '')
	{
		return new RechargeService($end_date, $start_date, $source);
	}
	
	/**
	 * 获取客户对账单明细
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 14:19
	 *
	 * @return mixed
	 */
	public function customerStatementDetails()
	{
		//获取并校验参数
		$result = $this->checkCustomerStatementDetailsParams();
		if (!is_array($result)) {
			return $result;
		}
		$params = $result;
		
		//获取客户的收件人、抄送人、主题、客户名称等信息
		$email_config = $this->getCustomerStatementDetailsEmailConfig($params);
		if (!is_array($email_config)) {
			return $email_config;
		}
		
		//获取该客户的余额
		$balance = $this->getCustomerStatementDetailsBalance($params);

		//获取该客户的总消费
		$consume_array = $this->getCustomerStatementDetailsConsume($params);
		
		//获取客户邮件内容
		$content = $this->getCustomerStatementDetailsContent($consume_array['money'], $balance, $params);
		
		//基础数据
		$base_info = array_merge($email_config, compact('content'));
		
		//获取结算单数据
		$final_bill_info = $this->getCustomerStatementDetailsFinalBill($params, $consume_array, $balance);
		
		//获取账单明细数据
		$details_bill_info = $this->getCustomerConsumeDetails($params);
		
		//获取客户的产品日计费用量
		$customer_usage = $this->getCustomerBillsUsageNumberV2($params);

         //处理产品日计费
		$customer_usage = $this->dealwithCustomerBillsUsageNumber($customer_usage,$params['customer_id']);

		//获取其他辅助数据
		$other = $this->getCustomerConsumeDetailsOther($params);
		
		
		return compact('base_info', 'final_bill_info', 'details_bill_info', 'customer_usage', 'other');
	}
	
	/**
	 * 获取其他辅助数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/16 14:24
	 *
	 * @param $params array 参数
	 *
	 * @return array
	 */
	protected function getCustomerConsumeDetailsOther($params)
	{
		return $this->getCustomerConsumeDetailsRemindAndInterval($params);
	}
	
	/**
	 * 获取上次客户发送的时间，并判断是否需要进行提醒
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/16 14:29
	 *
	 * @param $params array 参数
	 *
	 * @return array
	 */
	protected function getCustomerConsumeDetailsRemindAndInterval($params)
	{
		$isRemind       = false;
		$interval       = '';
		$latelySendTime = $this->getLatelySendTime($params['customer_id'], $params['source']);
		if (empty($latelySendTime)) {
			return compact('isRemind', 'interval');
		}
        $latelySendTime = $latelySendTime[$params['customer_id']];
//		$latelySendTime = $this->getLatelySendTime($params['customer_id'])[$params['customer_id']];
		$isRemind       = abs(time() - array_get($latelySendTime, 'time', 0)) <= 86400; //是否提醒
		$interval       = $latelySendTime['interval'];
		
		
		return compact('isRemind', 'interval');
	}
	
	/**
	 * 获取客户的产品日计费用量
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/10 10:13
	 *
	 * @param $params array 参数
	 *
	 * @return array
	 * 此方法已废弃
	 */
	protected function getCustomerBillsUsageNumber($params)
	{
		//区分子产品计费模式获取子产品计费用量
		$result_modeIs1 = BillCustomerIncome::select([
			'bill_customer_income.product_ids as product_id',
			'bill_customer_income.date',
			'product.product_name',
			DB::raw('SUM(`bill_customer_income`.`number`) as number'),
		])
											->leftJoin('config_price_customer', 'config_price_customer.id', '=', 'bill_customer_income.config_price_customer_id')
											->leftJoin('product', 'product.product_id', '=', 'bill_customer_income.product_ids')
											->where('config_price_customer.mode', '1')
											->where(function ($query) use ($params) {
												$query->whereIn('bill_customer_income.apikey', Account::where('customer_id', $params['customer_id'])
																									  ->pluck('apikey')
																									  ->toArray());
											})
											->where('date', '>=', date('Ymd', strtotime('first day of this month', strtotime($params['month'] . '01'))))
											->where('date', '<=', date('Ymd', strtotime('last day of this month', strtotime($params['month'] . '01'))))
											->groupBy(['bill_customer_income.date', 'bill_customer_income.product_ids'])
											->get()
											->toArray();
		
		//其他计费模式都以父产品为准
		$result_modeIsOther = BillCustomerIncome::select([
			'bill_customer_income.father_id as product_id',
			'bill_customer_income.date',
			'product.product_name',
			'bill_customer_income.number',
		])
												->leftJoin('config_price_customer', 'config_price_customer.id', '=', 'bill_customer_income.config_price_customer_id')
												->leftJoin('product', 'product.product_id', '=', 'bill_customer_income.father_id')
												->where('config_price_customer.mode', '<>', '1')
												->where(function ($query) use ($params) {
													$query->whereIn('bill_customer_income.apikey', Account::where('customer_id', $params['customer_id'])
																										  ->pluck('apikey')
																										  ->toArray());
												})
												->where('date', '>=', date('Ymd', strtotime('first day of this month', strtotime($params['month'] . '01'))))
												->where('date', '<=', date('Ymd', strtotime('last day of this month', strtotime($params['month'] . '01'))))
												->groupBy([
													'bill_customer_income.date',
													'bill_customer_income.father_id',
												])
												->get()
												->toArray();
		
		//合并两部分数据并补充父级产品数据
		$result = array_merge(array_map([
			$this,
			'fillFatherInfo',
		], $result_modeIs1), array_map([
			$this,
			'fillFatherInfo',
		], $result_modeIsOther));
		
		
		//使用产品ID、日期进行排序
		array_multisort(array_column($result, 'father_id'), SORT_ASC, array_column($result, 'product_id'), SORT_ASC, array_column($result, 'date'), SORT_ASC, $result);
		
		return $result;
	}

    /**
     * 获取客户的产品日计费用量
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/11/10 10:13
     *
     * @param $params array 参数
     *
     * @return array
     */
    protected function getCustomerBillsUsageNumberV2($params)
    {
        //区分子产品计费模式获取子产品计费用量
        $result_modeIs1 = BillCustomerIncomeV2::select([
            'bill_customer_income_v2.product_ids as product_id',
            'bill_customer_income_v2.date',
            'product.product_name',
            DB::raw('SUM(`bill_customer_income_v2`.`number`) as number'),
        ])
            ->leftJoin('config_price_customer', 'config_price_customer.id', '=', 'bill_customer_income_v2.config_price_customer_id')
            ->leftJoin('product', 'product.product_id', '=', 'bill_customer_income_v2.product_ids')
            ->where('config_price_customer.mode', '1')
            ->where(function ($query) use ($params) {
                $query->whereIn('bill_customer_income_v2.apikey', Account::where('customer_id', $params['customer_id'])
                    ->pluck('apikey')
                    ->toArray());
            })
            ->where(function ($query) use ($params) {
                if ($params['source'] !== '') {
                    $query->where('bill_customer_income_v2.source', $params['source']);
                }
            })
            ->where('date', '>=', date('Ymd', strtotime('first day of this month', strtotime($params['month'] . '01'))))
            ->where('date', '<=', date('Ymd', strtotime('last day of this month', strtotime($params['month'] . '01'))))
            ->groupBy(['bill_customer_income_v2.date', 'bill_customer_income_v2.product_ids'])
            ->get()
            ->toArray();

        //其他计费模式都以父产品为准
        $result_modeIsOther = BillCustomerIncomeV2::select([
            'bill_customer_income_v2.father_id as product_id',
            'bill_customer_income_v2.date',
            'product.product_name',
            'bill_customer_income_v2.number',
        ])
            ->leftJoin('config_price_customer', 'config_price_customer.id', '=', 'bill_customer_income_v2.config_price_customer_id')
            ->leftJoin('product', 'product.product_id', '=', 'bill_customer_income_v2.father_id')
            ->where('config_price_customer.mode', '<>', '1')
            ->where(function ($query) use ($params) {
                $query->whereIn('bill_customer_income_v2.apikey', Account::where('customer_id', $params['customer_id'])
                    ->pluck('apikey')
                    ->toArray());
            })
            ->where(function ($query) use ($params) {
                if ($params['source'] !== '') {
                    $query->where('bill_customer_income_v2.source', $params['source']);
                }
            })
            ->where('date', '>=', date('Ymd', strtotime('first day of this month', strtotime($params['month'] . '01'))))
            ->where('date', '<=', date('Ymd', strtotime('last day of this month', strtotime($params['month'] . '01'))))
            ->groupBy([
                'bill_customer_income_v2.date',
                'bill_customer_income_v2.father_id',
            ])
            ->get()
            ->toArray();

        //合并两部分数据并补充父级产品数据
        $result = array_merge(array_map([
            $this,
            'fillFatherInfo',
        ], $result_modeIs1), array_map([
            $this,
            'fillFatherInfo',
        ], $result_modeIsOther));


        //使用产品ID、日期进行排序
        array_multisort(array_column($result, 'father_id'), SORT_ASC, array_column($result, 'product_id'), SORT_ASC, array_column($result, 'date'), SORT_ASC, $result);

        return $result;
    }
	
	/**
	 * 对调用明细补充父级产品ID及名称
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/11 11:13
	 *
	 * @param $detail array
	 *
	 * @return array
	 */
	protected function fillFatherInfo($detail)
	{
		$product_id            = $detail['product_id'];
		$father_id             = RedisCache::instance('productId_fatherId_mapping')
										   ->get($product_id) ?: $product_id;
		$father_name           = RedisCache::instance('productId_productName_mapping')
										   ->get($father_id);
		$detail['father_id']   = $father_id;
		$detail['father_name'] = $father_name;
		
		$detail['number'] = number_format($detail['number']);
		
		return $detail;
	}
	
	
	/**
	 * 获取客户消费明细
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 20:53
	 *
	 * @param $params array 参数
	 *
	 * @return array
	 */
	protected function getCustomerConsumeDetails($params)
	{
		//获取该客户的在这个月份的计费配置数据
		$bills = $this->getCustomerConsumeDetailsBillsV2($params);

		// 特殊消耗
        $month   = $params['month'];
        $start_date = date('Ymd', strtotime('first day of this month', strtotime($month . '01')));
        $end_date   = date('Ymd', strtotime('last day of this month', strtotime($month . '01')));
		$customer_expend = $this->getCustomerSpecialList([$params['customer_id']], $start_date, $end_date, $params['source']);
		$customer_expend = isset($customer_expend[$params['customer_id']]) ? $customer_expend[$params['customer_id']] : [];

		//分组统计
		$bills = $this->mergeCustomerConsumeDetailsBills($bills, $customer_expend);

		//更改部分子产品名称
        $bills = $this->changeProductName($bills,$params['customer_id']);

		//对每一个父产品数据做一个小计
		$bills = $this->makeCustomerConsumeDetailsSubtotal($bills);


		//过滤数据，看看是否需要展示部分字段
		$hide_fields = $this->hideCustomerConsumeDetailsFields($bills);
		
		return compact('bills', 'hide_fields');
	}
	
	/**
	 * 隐藏那些字段
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 16:25
	 *
	 * @param $bills array
	 *
	 * @return array
	 */
	protected function hideCustomerConsumeDetailsFields($bills)
	{
		$children_product_name = true;
		$operator_name         = true;
		array_walk($bills, function ($bill) use (&$children_product_name, &$operator_name) {
			foreach ($bill['data'] as $item) {
				//如果所有的子产品名称都是'--'就隐藏
				if ('--' != $item['children_product_name']) {
					$children_product_name = false;
				}
				//如果所有的运营商名称都是'--'就隐藏
				if ('--' != $item['operator_name']) {
					$operator_name = false;
				}
			}
		});
		
		return compact('children_product_name', 'operator_name');
	}
	
	/**
	 * 汇总每一个父产品的小计
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 16:10
	 *
	 * @param $bills array 账单数据
	 *
	 * @return array
	 */
	protected function makeCustomerConsumeDetailsSubtotal($bills)
	{
		return array_values(array_map(function ($bill) {
			$money  = 0;
			$number = 0;
			
			$bill['data'] = array_values(array_map(function ($item) use (&$money, &$number) {
				$money  = bcadd($money, $item['money'], 6);
				$number = bcadd($number, $item['number']);
				
				$item['price']  = strlen(array_last(explode('.', rtrim($item['price'], 0)))) > 2 ? rtrim($item['price'], 0) : number_format($item['price'], 2);
				$item['money']  = number_format($item['money'], 2);
				$item['number'] = number_format($item['number']);
				
				return $item;
			}, $bill['data']));
			
			//求小计
			if (count($bill['data']) > 1) {
				$total = [
					'children_product_id'   => '',
					'children_product_name' => '小计',
					'operator'              => '',
					'operator_name'         => '--',
					'price'                 => '--',
					'number'                => number_format($number),
					'money'                 => number_format($money, 2),
				];
				
				array_unshift($bill['data'], $total);
			}
			
			return $bill;
		}, $bills));
	}
	
	/**
	 * 对账单进行分组统计
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 15:40
	 *
	 * @param $bills array 账单数据
	 *
	 * @return array
	 */
	protected function mergeCustomerConsumeDetailsBills($bills, $customer_special)
	{
		$result = [];
		
		array_walk($bills, function ($bill) use (&$result) {
			$mode = $bill['mode'];
			switch ($mode) {
				case 0:
					//无计费模式
					$this->mergeCustomerConsumeDetailsModel0($bill, $result);
					break;
				case 1:
					//子产品计费
					$this->mergeCustomerConsumeDetailsModel1($bill, $result);
					break;
				case 2:
					//打包计费
					$this->mergeCustomerConsumeDetailsModel2($bill, $result);
					break;
				case 3:
					//汇总子产品计费
					$this->mergeCustomerConsumeDetailsModel3($bill, $result);
					break;
			}
		});

        // 特殊消耗处理
        $is_flag = [];
		if ($customer_special) {
            foreach ($result as &$item) {
                $father_id = $item['father_id'];
                foreach ($item['data'] as &$value) {
                    $product_id = strpos($value['children_product_id'],',') !== false ? $father_id : $value['children_product_id'];
                    $operator = $value['operator'];

                    $expend_number = isset($customer_special[$product_id][$operator]['number']) ? $customer_special[$product_id][$operator]['number'] : 0;
                    $expend_money = isset($customer_special[$product_id][$operator]['money']) ? $customer_special[$product_id][$operator]['money'] : 0;

                    if (!$expend_money && !$expend_number && $operator && !isset($is_flag[$product_id])) {
                        $expend_number = isset($customer_special[$product_id]['']['number']) ? $customer_special[$product_id]['']['number'] : 0;
                        $expend_money = isset($customer_special[$product_id]['']['money']) ? $customer_special[$product_id]['']['money'] : 0;
                        $is_flag[$product_id] = true;
                    }

                    $value['number'] += $expend_number;
                    $value['money'] = bcadd($value['money'], $expend_money, 6);

                }
            }
        }

		return $result;
	}
	
	/**
	 * 合并【无计费模式】的数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 15:43
	 *
	 * @param $bill   array 单个账单数据
	 * @param $result array 结果集
	 *
	 * @return void
	 */
	protected function mergeCustomerConsumeDetailsModel0($bill, &$result)
	{
		$father_id  = $bill['father_id'];
		$father_key = $father_id . '_0';
		if (!array_key_exists($father_key, $result)) {
			$result[$father_key] = [
				'father_id'   => $father_id,
				'father_name' => $this->getProductName($father_id),
				'data'        => [],
			];
		}
		
		$price        = $bill['price'];
		$operator     = $bill['operator'];
		$children_key = $operator . '_' . $price;
		
		if (!array_key_exists($children_key, $result[$father_key]['data'])) {
			$result[$father_key]['data'][$children_key] = [
				'children_product_id'   => $father_id,
				'children_product_name' => '--',
				'operator'              => $operator,
				'operator_name'         => $this->getOperatorName($operator),
				'price'                 => $price,
				'number'                => 0,
				'money'                 => 0,
			];
		}
		
		$result[$father_key]['data'][$children_key]['money']  = bcadd($result[$father_key]['data'][$children_key]['money'], $bill['money'], 6);
		$result[$father_key]['data'][$children_key]['number'] = bcadd($result[$father_key]['data'][$children_key]['number'], $bill['number']);
	}
	
	/**
	 * 合并【子产品计费】的数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 15:43
	 *
	 * @param $bill   array 单个账单数据
	 * @param $result array 结果集
	 *
	 * @return void
	 */
	protected function mergeCustomerConsumeDetailsModel1($bill, &$result)
	{
		$father_id  = $bill['father_id'];
		$father_key = $bill['father_id'] . '_1';
		
		if (!array_key_exists($father_key, $result)) {
			$result[$father_key] = [
				'father_id'   => $father_id,
				'father_name' => $this->getProductName($father_id),
				'data'        => [],
			];
		}
		
		$price        = $bill['price'];
		$operator     = $bill['operator'];
		$product_ids  = $bill['product_ids'];
		$children_key = $product_ids . '_' . $operator . '_' . $price;
		
		if (!array_key_exists($children_key, $result[$father_key]['data'])) {
			$result[$father_key]['data'][$children_key] = [
				'children_product_id'   => $product_ids,
				'children_product_name' => $this->getProductName($product_ids),
				'operator'              => $operator,
				'operator_name'         => $this->getOperatorName($operator),
				'price'                 => $price,
				'number'                => 0,
				'money'                 => 0,
			];
		}

		$result[$father_key]['data'][$children_key]['money']  = bcadd($result[$father_key]['data'][$children_key]['money'], $bill['money'], 6);
		$result[$father_key]['data'][$children_key]['number'] = bcadd($result[$father_key]['data'][$children_key]['number'], $bill['number']);
	}
	
	/**
	 * 合并【打包计费】的数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 15:43
	 *
	 * @param $bill   array 单个账单数据
	 * @param $result array 结果集
	 *
	 * @return void
	 */
	protected function mergeCustomerConsumeDetailsModel2($bill, &$result)
	{
		$father_id  = $bill['father_id'];
		$father_key = $bill['father_id'] . '_2';
		
		if (!array_key_exists($father_key, $result)) {
			$result[$father_key] = [
				'father_id'   => $father_id,
				'father_name' => $this->getProductName($father_id),
				'data'        => [],
			];
		}
		
		$price                = $bill['price'];
		$operator             = $bill['operator'];
		$together_call_number = $bill['together_call_number'];
		$children_key         = $together_call_number . '_' . $operator . '_' . $price;
		
		if (!array_key_exists($children_key, $result[$father_key]['data'])) {
			$result[$father_key]['data'][$children_key] = [
				'children_product_id'   => $bill['product_ids'],
				'children_product_name' => '>=' . $together_call_number,
				'operator'              => $operator,
				'operator_name'         => $this->getOperatorName($operator),
				'price'                 => $price,
				'number'                => 0,
				'money'                 => 0,
			];
		}
		
		$result[$father_key]['data'][$children_key]['money']  = bcadd($result[$father_key]['data'][$children_key]['money'], $bill['money'], 6);
		$result[$father_key]['data'][$children_key]['number'] = bcadd($result[$father_key]['data'][$children_key]['number'], $bill['number']);
	}
	
	/**
	 * 合并【汇总子产品计费】的数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 15:43
	 *
	 * @param $bill   array 单个账单数据
	 * @param $result array 结果集
	 *
	 * @return void
	 */
	protected function mergeCustomerConsumeDetailsModel3($bill, &$result)
	{
		$father_id  = $bill['father_id'];
		$father_key = $bill['father_id'] . '_3';
		
		if (!array_key_exists($father_key, $result)) {
			$result[$father_key] = [
				'father_id'   => $father_id,
				'father_name' => $this->getProductName($father_id),
				'data'        => [],
			];
		}
		
		$price        = $bill['price'];
		$operator     = $bill['operator'];
		$children_key = $operator . '_' . $price;
		
		if (!array_key_exists($children_key, $result[$father_key]['data'])) {
			$result[$father_key]['data'][$children_key] = [
				'children_product_id'   => $bill['product_ids'],
				'children_product_name' => '--',
				'operator'              => $operator,
				'operator_name'         => $this->getOperatorName($operator),
				'price'                 => $price,
				'number'                => 0,
				'money'                 => 0,
			];
		}
		
		$result[$father_key]['data'][$children_key]['money']  = bcadd($result[$father_key]['data'][$children_key]['money'], $bill['money'], 6);
		$result[$father_key]['data'][$children_key]['number'] = bcadd($result[$father_key]['data'][$children_key]['number'], $bill['number']);
	}
	
	/**
	 * 获取产品名称
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 15:47
	 *
	 * @param $product_id integer 产品ID
	 *
	 * @return string
	 */
	protected function getProductName($product_id)
	{
		return RedisCache::instance('productId_productName_mapping')
						 ->get($product_id);
	}
	
	/**
	 * 获取运营商的名称
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/9 15:48
	 *
	 * @param $operator string 运营商
	 *
	 * @return string
	 */
	protected function getOperatorName($operator)
	{
		if (empty($operator)) {
			return '--';
		}
		
		return RedisCache::instance('operator_bill_mapping')
						 ->get($operator);
	}
	
	/**
	 * 获取各产品账单明细数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 20:49
	 *
	 * @param $params array 参数
	 *
	 * @return array
	 * 此方法已废弃
	 */
	protected function getCustomerConsumeDetailsBills($params)
	{
		$apikeys = Account::where('customer_id', $params['customer_id'])
						  ->pluck('apikey')
						  ->toArray();
		$month   = $params['month'];
		
		$start_date = date('Ymd', strtotime('first day of this month', strtotime($month . '01')));
		$end_date   = date('Ymd', strtotime('last day of this month', strtotime($month . '01')));
		
		return BillCustomerIncome::select([
			'bill_customer_income.father_id',
			'bill_customer_income.product_ids',
			'bill_customer_income.operator',
			'bill_customer_income.together_call_number',
			'bill_customer_income.date',
			'bill_customer_income.price',
			'bill_customer_income.number',
			'bill_customer_income.money',
			'config_price_customer.mode',
		])
								 ->leftJoin('config_price_customer', 'bill_customer_income.config_price_customer_id', '=', 'config_price_customer.id')
								 ->where('date', '>=', $start_date)
								 ->where('date', '<=', $end_date)
								 ->whereIn('bill_customer_income.apikey', $apikeys)
								 ->where('methods', '<>', 1)//包年的不展示
								 ->orderBy('date')
								 ->get()
								 ->toArray();
	}

    /**
     * 获取各产品账单明细数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/11/6 20:49
     *
     * @param $params array 参数
     *
     * @return array
     */
    protected function getCustomerConsumeDetailsBillsV2($params)
    {
        $apikeys = Account::where('customer_id', $params['customer_id'])
            ->pluck('apikey')
            ->toArray();
        $month   = $params['month'];

        $start_date = date('Ymd', strtotime('first day of this month', strtotime($month . '01')));
        $end_date   = date('Ymd', strtotime('last day of this month', strtotime($month . '01')));

        return BillCustomerIncomeV2::select([
            'bill_customer_income_v2.father_id',
            'bill_customer_income_v2.product_ids',
            'bill_customer_income_v2.operator',
            'bill_customer_income_v2.together_call_number',
            'bill_customer_income_v2.date',
            'bill_customer_income_v2.price',
            'bill_customer_income_v2.number',
            'bill_customer_income_v2.money',
            'config_price_customer.mode',
        ])
            ->leftJoin('config_price_customer', 'bill_customer_income_v2.config_price_customer_id', '=', 'config_price_customer.id')
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->where('bill_customer_income_v2.source', $params['source'])
            ->whereIn('bill_customer_income_v2.apikey', $apikeys)
            ->where('methods', '<>', 1)//包年的不展示
            ->orderBy('date')
            ->get()
            ->toArray();
    }
	
	/**
	 * 获取客户的结算单数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 15:15
	 *
	 * @param $params       array 参数
	 * @param $consume      array 消费数据
	 * @param $balance      integer 余额
	 *
	 * @return array
	 */
	protected function getCustomerStatementDetailsFinalBill($params, $consume, $balance)
	{
		$customer_info = $params['customer_info'];

		$result = [];
		//本公司名称、简称  
		$result = $this->getBillMailHeaderAndFooter();

		//客户信息
		$result['customer_name'] = $customer_info['name'];
		$result['company']       = $customer_info['company'];
		
		//上月剩余金额
		$prev_month_date              = date('Ymd', strtotime('last day of last month', strtotime($params['month'] . '01')));
		$result['prev_month_balance'] = $this->getBalanceService($prev_month_date, null, $params['source'])
											 ->getCustomerBalanceV2([$params['customer_id']])[$params['customer_id']]['money'];
		$result['prev_month_balance'] = number_format($result['prev_month_balance'], 2);
		
		$result['details'] = $this->getCustomerStatementDetailsFinalBillDetails($params, $consume);

		$result['balance'] = number_format($balance, 2);
		
		
		return $result;
	}
	
	/**
	 * 获取结算单明细数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 17:46
	 *
	 * @param $params       array 参数
	 * @param $consume      array 消费数据
	 *
	 * @return array
	 */
	protected function getCustomerStatementDetailsFinalBillDetails($params, $consume)
	{
		$result = [];
		
		//获取当月的充值的数据
		$start_date = date('Ymd', strtotime('first day of this month', strtotime($params['month'] . '01')));
		$end_date   = date('Ymd', strtotime('last day of this month', strtotime($params['month'] . '01')));
		$recharge   = $this->getRechargeService($start_date, $end_date, $params['source'])
						   ->getCustomerIdRechargeMoney($params['customer_id']);

		//账单消费
		$bill_consume = $consume['bill_consume'];
		$title        = $start_date . ' -- ' . $end_date;
		
		//$result[] = [$title, $recharge, $bill_consume];
		//特殊消费数据
		$special_consume_details = $consume['special_consume_details'];
		foreach ($special_consume_details as $item) {
			switch ($item['type']) {
				case 1:
					//赠送
					//$result[] = [$item['title'], $item['money'], 0,];
                    $bill_consume = $bill_consume - $item['money'];
					break;
				case 2:
					//消费
					//$result[] = [$item['title'], 0, $item['money'],];
                    $bill_consume = $bill_consume + $item['money'];
					break;
			}
		}
        $result[] = [$title, $recharge, $bill_consume];
		//补充合计数据并整理数据
		$recharge_money_total = 0;
		$consume_money_total  = 0;
		$result               = array_map(function ($item) use (&$recharge_money_total, &$consume_money_total) {
			$recharge_money_total = bcadd($item[1], $recharge_money_total, 6);
			$consume_money_total  = bcadd($item[2], $consume_money_total, 6);
            if($item[1]){
                $item[1] = number_format($item[1], 2);
            }else{
                $item[1] = 0;
            }

            if($item[2]){
                $item[2] = number_format($item[2], 2);
            }else{
                $item[2] = 0;
            }

			return $item;
		}, $result);
		
		$result[] = ['合计', number_format($recharge_money_total, 2), number_format($consume_money_total, 2)];
		
		return $result;
	}
	
	/**
	 * 获取客户邮件的内容
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 14:45
	 *
	 * @param $consume integer 消费
	 * @param $balance integer 余额
	 * @param $params  array 参数
	 *
	 * @return
	 */
	protected function getCustomerStatementDetailsContent($consume, $balance, $params)
	{
		$extra_string = '';
		//剩余金额是否充足
		if ($balance < $consume) {
			$extra_string = '<p>&nbsp; &nbsp; &nbsp; &nbsp;贵司剩余金额预计可用不足一个月，请及时充值。</p>';
		}
		
		//欠费、与不欠费的说辞不一样
		if ($balance >= 0) {
			$balance        = number_format($balance, 2);
			$balance_string = "剩余总金额为：{$balance}元。";
		} else {
			$balance        = number_format($balance, 2);
			$balance_string = "<span style=\"color:#c0392b\">欠费总金额为: {$balance}元</span>。";
		}
		
		$month   = date('Y年m月份', strtotime($params['month'] . '01'));
		$consume = number_format($consume, 2);
		
		return <<<CONTENT
<p>尊敬的客户：</p>
<p>
&nbsp; &nbsp; &nbsp; &nbsp;
您好！感谢贵司一直以来对我司产品的支持与信赖！
贵司{$month}消耗总金额为：{$consume}元，
{$balance_string}
消耗详情请查看附件结算单。
<p>
&nbsp; &nbsp; &nbsp; &nbsp;
<span style="font-weight:bold">如与贵司记录相符，请回复邮件确认或对账单盖章邮寄我司。</span>如不符，也请随时告知我司， 我们会尽快排查，再次核对。


</p>
{$extra_string}
&nbsp; &nbsp; &nbsp; &nbsp;感谢贵司的理解与支持，顺祝商祺！
</p>
CONTENT;
	}
	
	/**
	 * 获取客户的收件人、抄送人、主题
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 14:37
	 *
	 * @param $params        array 参数
	 *
	 * @return array|integer
	 */
	protected function getCustomerStatementDetailsEmailConfig($params)
	{
		$customer_info = $params['customer_info'];
        $addressee     = '';
        $cc            = '';
        // 只有羽乐科技来源的返回邮件地址和抄送地址, 防止其他渠道误发送邮件
        if($params['source'] == SourceDefine::CUSTOMER_SOURCE_DIANHUA){
            $addressee     = $customer_info['bill_email'];
            $cc            = $customer_info['bill_cc_email'];
        }
		$subject = $this->getMailSubjectByCustomer($params['customer_info'],$params['month']);
		return compact('addressee', 'cc', 'subject');
	}
	
	/**
	 * 获取某客户的消费
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 14:50
	 *
	 * @param $param array 参数
	 *
	 * @return array
	 */
	protected function getCustomerStatementDetailsConsume($params)
	{
		$customer_id = $params['customer_id'];
		$month       = $params['month'];
		$end_date    = date('Ymd', strtotime('last day of this month', strtotime($month . '01')));
		$start_date  = date('Ymd', strtotime('first day of this month', strtotime($month . '01')));
		$consume     = $this->getConsumeService($start_date, $end_date, $params['source'])
							->getCustomerIncomeV2([$customer_id]);

		//特殊消耗
		$special_consume_details = [];
		if (!empty($consume)) {
			foreach ($consume[$customer_id]['details']['special'] as $item) {
				$special_consume_details[] = [
					'title' => $item['title'],
					'money' => $item['money'],
					'type'  => $item['type'],
				];
			}
		}

		
		//账单消耗
		$bill_consume = 0;
		$money        = 0;
		if (!empty($consume)) {
			foreach ($consume[$customer_id]['details']['bill'] as $item) {
				$bill_consume = bcadd($bill_consume, $item['money'], 6);
			}
			$money = $consume[$customer_id]['money'];
		}
		
		return compact('special_consume_details', 'bill_consume', 'money');
	}
	
	/**
	 * 获取某个客户的余额
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 14:32
	 *
	 * @param $params array 参数
	 *
	 * @return integer
	 */
	protected function getCustomerStatementDetailsBalance($params)
	{
		$customer_id = $params['customer_id'];
		$month       = $params['month'];
		$date        = date('Ymd', strtotime('last day of this month', strtotime($month . '01')));
		$balances    = $this->getBalanceService($date, null, $params['source'])
							->getCustomerBalanceV2([$customer_id]);
		
		return $balances[$customer_id]['money'];
	}
	
	/**
	 * 获取客户对账单明细参数并校验
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/6 14:19
	 *
	 * @return array|integer
	 */
	protected function checkCustomerStatementDetailsParams()
	{
		//客户ID
		$customer_id = $this->request->post('customer_id');
		if (empty($customer_id)) {
			return 13030;
		}

        $source = $this->request->post('source', '');

        $sourceMap = CommonEnumModel::getListByType(1);
        $sourceMap = array_column($sourceMap, 'name');
        if (!in_array($source, $sourceMap)) {
            return 13012;
        }
		
		//月份
		$month = $this->request->post('month');
		if (!preg_match('/^\d{6}$/', $month)) {
			return 13031;
		}
		if ($month < 202010) {
			return 13032;
		}
		
		//获取客户信息
		$customer_info = Customer::select(['bill_email', 'bill_cc_email', 'company', 'name'])
								 ->where('customer_id', $customer_id)
								 ->where('is_delete', 0)
								 ->first()
								 ->toArray();
		
		if (empty($customer_info)) {
			return 13033;
		}
		
		return compact('customer_id', 'month', 'customer_info', 'source');
	}
	
	/**
	 * 客户对账单发送邮件
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/11 14:10
	 *
	 * @return integer
	 */
	public function sendStatementMail()
	{
		$data = $this->getJsonByRaw();
		if (false === $data) {
			return 13040;
		}

		//发送邮件(同步执行)
		$sendMailService = new SendMailService();
        //设置邮箱配置(该对账单功能发送邮箱和默认邮箱不一样，且和默认邮箱的用户名密码也不一致，因此需要设置指定一下)
        $username = config('params.yun_ying_mail.mail_username');
        $password = config('params.yun_ying_mail.mail_password');
        $sendMailService->setConfig(['username' => $username, 'password' => $password]);
        //设置发送人地址
        $sendMailService->setFrom(config('params.yun_ying_mail.mail_from_address'));


		$sendMailService->setSubject($data['base_info']['subject'])
						->setContent($data['base_info']['content']);

		//收件人
		$addressee = array_map(function ($email) {
			return compact('email');
		}, explode(';', $data['base_info']['addressee']));
		$sendMailService->setAddressee($addressee);

		//抄送人
		$cc = $data['base_info']['cc'];
		if (!empty($cc)) {
			$cc = array_map(function ($email) {
				return compact('email');
			}, explode(';', $data['base_info']['cc']));
			$sendMailService->setCC($cc);
		}

		//设置附件
		$filename = $this->createExcelForStatementMail($data);
		// 附件文件名
		$name     = $this->getAttachmentFileName($data);
		$sendMailService->setAttachment([['file_path' => $filename, 'name' => $name]]);


		//设置发件人
		$sendMailService->setFromName("金融数据客户服务");

		//发送
		$sendMailService->send();

		//记录发送日志
		$this->writeSendMailLog(array_get($data, 'customer_id'), $sendMailService->getUuid(),array_get($data, 'source'));

		return 0;
	}
	
	/**
	 * 创建excel附件
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/13 16:28
	 *
	 * @param $data array 数据
	 *
	 * @return string
	 */
	protected function createExcelForStatementMail($data)
	{
		$this->file_out_init();
		//第一个sheet
		$this->createSheet1ForStatementMail($data);
		
		//第二个sheet
		$this->createSheet2ForStatementMail($data);
		
		//第三个sheet
		$this->createSheet3ForStatementMail($data);
		
		//下载到本地
		$filename = app()->storagePath() . '/' . $data['customer_id'] . '.xlsx';
		$this->save($filename);

		return $filename;
	}
	
	/**
	 * 创建朴道对账单
	 *
	 * @param $data array 数据
	 *
	 * @return string
	 */
    protected function createExcelForPudaoStatementMail($source_name,$data,$startDate=null,$endDate=null) {
		$this->file_out_init();
		//第一个sheet
		$this->createSheet1ForPudaoStatementMail($source_name,$data,$startDate,$endDate);

		//第二个sheet
		$this->createSheet2ForPudaoStatementMail($source_name,$data);
		
		$filename = Common::COMPANY_CN_NAME."【-{$source_name}】{$startDate}-{$endDate}对账单.xlsx";
        ob_start();
        $this->download($filename,false);
        $xlsData = ob_get_contents();
        ob_end_clean();
		return ['filename' => $filename, 'file' => "data:application/vnd.ms-excel;base64," . base64_encode($xlsData)];
	}
	/**
	 * 创建第一个sheet(朴道对账)
	 *
	 * @access   protected
	 * @param $data array 数据
	 *
	 * @return void
	 */
	protected function createSheet1ForPudaoStatementMail($source_name,$data,$start,$end) {
        $company_name_map = [
            '朴道'   => '朴道征信有限公司',
            '浙数交' => '浙江大数据交易中心有限公司',
        ];
        $source_company_name = $company_name_map[$source_name] ?? '';

		$sheetTitle = '结算单';
		$data['header'][0] = '数据源-'.$source_name.'侧服务结算单';
        $data['header'][1] = '甲方：' . $source_company_name;
		$data['header'][2] = '乙方：北京羽乐创新科技有限公司';
		$tableHeader = ['调用详情','服务周期','客户名称','商户结算金额'];

		$dateRange = $start.'-'.$end;
		
		$this->setSheetTitle($sheetTitle);
		$this->setWidth([10, 20, 30,20]);
		$this->setCenter('A1:C30');
		
		//头部
		$this->setCellValue('A1', $data['header'][0]);
		$this->mergeCell('A1:D1');
		$this->setCellValue('A2', $data['header'][1]);
		$this->mergeCell('A2:D2');
		$this->setLeft('A2');

		$this->setCellValue('A3', $data['header'][2]);
		$this->mergeCell('A3:D3');
		$this->setLeft('A3');
		$this->setBold('A1:D1');

		
		// 中间表头
		$this->setCellValue('A4',$tableHeader[0]);
		$this->setCellValue('B4',$tableHeader[1]);
		$this->setCellValue('C4',$tableHeader[2]);
		$this->setCellValue('D4',$tableHeader[3]);

		//中间主体
		$this->col = 5;
		foreach ($data['items'] as $item) {
			$this->setCellValue('B' . $this->col, $dateRange);
			$this->setCellValue('C' . $this->col, $item['company']);
			$this->setCellValue('D' . $this->col, $item['consume']);
			$this->col++;
		}

		$this->setCellValue('B' . $this->col, '总计');
		$this->mergeCell('B' . $this->col . ':C' . $this->col);
		$this->setCellValue('D' . $this->col, $data['total']['consume']);

		// 画边框
		$this->setBorder('A1:D' . $this->col);
		// 第一列 调用详情合并
		$this->mergeCell('A4:A' . $this->col);

		//设置每行高度
		for ($i = 0; $i < $this->col; $i++) {
			// 第一行要高
			if($i==1){
				$this->setHeight(40, $i);
			}else{
				$this->setHeight(20, $i);
			}
		}
	}

	/**
	 * 创建第二个sheet(朴道对账)
	 *
	 * @access   protected
	 *
	 * @param $data array 数据
	 *
	 * @return void
	 */
	protected function createSheet2ForPudaoStatementMail($source_name,$data)
	{

		$sheetTitle = '明细-产品';
		$header = '数据源-'.$source_name.'侧月度账单';
		$tableHeader = [
			'A'=>'公司名称',
			'B'=>$source_name.'公司名称',
			'C'=>'调用账号',
			'D'=>'主产品名称',
			'E'=>'子产品名称',
			'F'=>'子产品id',
			'G'=>'运营商名称',
			'H'=>'朴道产品代码',
			'I'=>'计费方式',
			'J'=>'计费标准（查得/查询）',
			'K'=>'计费模式',
			'L'=>'计费次数',
		];

		//预设
		$this->php_excel->createSheet(1);
		$this->cutSheet(1);
		$this->setSheetTitle($sheetTitle);
        //A到P列，每列对应的宽度
		$this->setWidth([30, 30, 10, 20, 30, 20, 20, 20, 15, 20, 15, 15, 15, 15, 15, 15, 30]);

		//头部
		$this->setCellValue('A1', $header);
		$this->mergeCell('A1:P1');
		$this->setHeight(40, 1);
		// 1-12 列
		foreach ($tableHeader as $key => $value) {
			$this->setCellValue($key.'2',$value);
			$this->mergeCell($key.'2:'.$key.'3');
		}
		// 13-14列
		$this->setCellValue('M2','商户结算金额');
		$this->mergeCell('M2:N2');
		$this->setCellValue('M3','单价（元）');
		$this->setCellValue('N3','金额（人民币）');
		// 15-16
		$this->setCellValue('O2','朴道结算金额');
		$this->mergeCell('O2:P2');
		$this->setCellValue('O3','分润比');
		$this->setCellValue('P3','金额（人民币）');
		// 备注
		$this->setCellValue('Q2','备注');
		$this->mergeCell('Q2:Q3');

		//header style
		$this->setBold('A1:Q3');
		$this->setCenter('A1:Q3');

		// 记录总行数
		$this->col = 4;
		//主体部分
		foreach ($data['details'] as $item) {
			//每一行的数据
			$every_data  = [];
			$every_data[] = $item['company'];
			$every_data[] = $item['pd_company'];
			$every_data[] = $item['customer_name'];
			$every_data[] = $item['father_name'];
			$every_data[] = $item['product_name'];
			$every_data[] = $item['product_ids'];
			$every_data[] = $item['operator'];
			$every_data[] = $item['pd_product_ids'];//朴道产品代码
			$every_data[] = $item['methods'];//计费方式
			$every_data[] = $item['fee_type'];// 查得 or 查询
            $every_data[] = $item['mode'];//计费模式
			$every_data[] = $item['number'];
			$every_data[] = implode(',',$item['price']);
			$every_data[] = $item['money_source'];
			$every_data[] = $item['agent_income_fee'];
			$every_data[] = $item['money'];
			$this->addRowContent($every_data, 18);

		}
		// 总计
		$this->setCellValue('A'.$this->col,'总计');
		$this->mergeCell('A'.$this->col.':M'.$this->col);
		$this->setBold('A'.$this->col.':M'.$this->col);
		$this->setCenter('A'.$this->col.':M'.$this->col);
		$this->setCellValue('N'.$this->col,$data['total']['money_source']);
		//$this->setCenter('K'.$this->col);
		$this->setCellValue('P'.$this->col,$data['total']['consume']);

	}

	/**
	 * 创建第一个sheet
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/13 16:30
	 *
	 * @param $data array 数据
	 *
	 * @return void
	 */
	protected function createSheet1ForStatementMail($data)
	{
		//预设
		$this->setSheetTitle('结算单');
		$this->setWidth([30, 20, 20]);
		$this->setCenter('A1:C30');
		
		//头部
		//$this->setCellValue('A1', $data['final_bill_info']['header'][0]);
		//$this->mergeCell('A1:C1');
		$this->setCellValue('A2', $data['final_bill_info']['header'][1]);
		$this->mergeCell('A2:C2');
		
		//隔断
		
		$this->setCellValue('A4', $data['final_bill_info']['header'][2]);
		$this->mergeCell('A4:C4');
		
		//隔断
		
		//中间主体
		$this->setCellValue('A6', '公司名称');
		$this->setCellValue('B6', $data['final_bill_info']['company']);
		$this->mergeCell('B6:C6');
		$this->setCellValue('A7', '客户名称');
		$this->setCellValue('B7', $data['final_bill_info']['customer_name']);
		$this->mergeCell('B7:C7');
		$this->setCellValue('A8', '上月剩余金额');
		$this->setCellValue('B8', $data['final_bill_info']['prev_month_balance']);
		$this->setRight('B8');
		$this->mergeCell('B8:C8');
		$this->setCellValue('A9', '期间');
		$this->setCellValue('B9', '充值金额');
		$this->setCellValue('C9', '消耗金额');
		$this->col = 10;
		foreach ($data['final_bill_info']['details'] as $item) {
			$this->setCellValue('A' . $this->col, $item[0]);
			$this->setCellValue('B' . $this->col, $item[1]);
			$this->setCellValue('C' . $this->col, $item[2]);
			$this->col++;
		}
		$this->setCellValue('A' . $this->col, '剩余金额');
		$this->setCellValue('B' . $this->col, $data['final_bill_info']['balance']);
		$this->setRight('B10:C' . $this->col);
		$this->mergeCell('B' . $this->col . ':C' . $this->col);
		$this->setBorder('A6:C' . $this->col);
		$this->col++;
		
		//隔断
		$this->mergeCell('A' . $this->col . ':C' . ($this->col + 1));
		$this->col += 2;

		/*
		$this->setRight('A' . $this->col . ':A' . ($this->col + 2));
		$this->setCellValue('A' . $this->col, $data['final_bill_info']['footer'][0]);
		$this->mergeCell('A' . $this->col . ':C' . $this->col);
		$this->col++;
		$this->setCellValue('A' . $this->col, $data['final_bill_info']['footer'][1]);
		$this->mergeCell('A' . $this->col . ':C' . $this->col);
		$this->col++;
		$this->setCellValue('A' . $this->col, $data['final_bill_info']['footer'][2]);
		$this->mergeCell('A' . $this->col . ':C' . $this->col);
		*/
        
        $this->setLeft('A' . $this->col);
        $this->setCellValue('A' . $this->col, '甲方信息确认：（签章）');
        $this->setLeft('C' . $this->col);
        $this->setCellValue('C' . $this->col, '乙方信息确认：（签章）');
        $this->col++;

        $this->setRight('A' . $this->col . ':A' . ($this->col + 2));
        $this->setRight('C' . $this->col . ':C' . ($this->col + 2));
        $this->setCellValue('A' . $this->col, $data['final_bill_info']['company']);
        $this->setCellValue('C' . $this->col, $data['final_bill_info']['footer'][0]);
        $this->col++;

        $this->setCellValue('A' . $this->col, '');
        $this->setCellValue('C' . $this->col, $data['final_bill_info']['footer'][1]);
        $this->col++;

        $this->setCellValue('A' . $this->col, $data['final_bill_info']['footer'][2]);
        $this->setCellValue('C' . $this->col, $data['final_bill_info']['footer'][2]);
		
		//设置每行高度
		for ($i = 0; $i < $this->col; $i++) {
			$this->setHeight(20, $i);
		}
	}
	
	/**
	 * 创建第二个sheet
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/13 17:19
	 *
	 * @param $data array 数据
	 *
	 * @return void
	 */
	protected function createSheet2ForStatementMail($data)
	{
		//预设
		$this->php_excel->createSheet(1);
		$this->cutSheet(1);
		$this->setSheetTitle('账单明细');
		$this->setWidth([20, 20, 20, 20, 20, 20]);
		$this->addRowContent($this->getTitleForSheet2($data), 20, true);
		
		//主体部分
		foreach ($data['details_bill_info']['bills'] as $item) {
			$father_start_col = $this->col;
			
			foreach ($item['data'] as $children_item) {
				//每一行的数据
				$every_data   = [];
				$every_data[] = $item['father_name'];
				
				//是否展示子产品
				if ($this->isShowChildrenProduct($data)) {
					$every_data[] = $children_item['children_product_name'];
				}
				
				//是否展示运营商
				if ($this->isShowOperator($data)) {
					$every_data[] = $children_item['operator_name'];
				}
				
				$every_data[] = $children_item['price'];
				$every_data[] = $children_item['number'];
				$every_data[] = $children_item['money'];
				
				$isBold = '小计' == $every_data[1];
				$this->addRowContent($every_data, 18, $isBold);
			}
			
			//将第一行合并单元格
			$this->setBold('A' . $father_start_col);
			$this->mergeCell('A' . $father_start_col . ':A' . ($this->col - 1));
		}
	}
	
	/**
	 * 获取第二个sheet中的标题数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/16 10:26
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	protected function getTitleForSheet2($data)
	{
		$title = [
			'产品名称',
		];
		if ($this->isShowChildrenProduct($data)) {
			$title[] = '子产品';
		}
		if ($this->isShowOperator($data)) {
			$title[] = '运营商';
		}
		$title[] = '单价';
		$title[] = '计费用量';
		$title[] = '消耗金额';
		
		return $title;
	}
	
	/**
	 * 是否展示子产品
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/13 17:34
	 *
	 * @param $data array 数据
	 *
	 * @return boolean
	 */
	protected function isShowChildrenProduct($data)
	{
		return !$data['details_bill_info']['hide_fields']['children_product_name'];
	}
	
	/**
	 * 是否展示运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/13 17:34
	 *
	 * @param $data array 数据
	 *
	 * @return boolean
	 */
	protected function isShowOperator($data)
	{
		return !$data['details_bill_info']['hide_fields']['operator_name'];
	}
	
	/**
	 * 生成第三个sheet
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/16 10:39
	 *
	 * @param $data array 数据
	 *
	 * @return void
	 */
	protected function createSheet3ForStatementMail($data)
	{
		//预设
		$this->php_excel->createSheet(2);
		$this->cutSheet(2);
		$this->setSheetTitle('产品日调用明细');
		$this->setWidth([20, 20, 20, 20]);
		$this->addRowContent(['主产品', '子产品', '日期', '计费用量'], 20, true);
		
		foreach ($data['customer_usage'] as $item) {
			$this->addRowContent([
				$item['father_name'],
				$item['product_name'],
				$item['date'],
				$item['number'],
			], 18, false);
		}
	}
	
	/**
	 * 记录邮件发送的日志
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/16 11:29
	 *
	 * @param $customer_id string 客户ID
	 * @param $uuid        string 邮件队列的唯一标识
	 *
	 * @return void
	 */
	protected function writeSendMailLog($customer_id, $uuid, $source = 0)
	{
		$send_time = time();
//        $source = $source ?: SourceDefine::$customerSourceMap['朴道'];
		LogsMailCustomerIncome::insert(compact('customer_id', 'uuid', 'send_time', 'source'));
	}

	protected function changeProductName($bills,$customer_id)
    {
        //mode = 1 且没有进行产品名称的替换
        $res1 = [];
        //所有的完成的产品名称替换的账单
        $res2 = [];
        array_walk($bills, function (&$bill,$key) use(&$res1,&$res2,$customer_id){
            $modearr = explode('_',$key);
            $mode = $modearr[1];

            //符合条件的完成 名称的替换
            if ($mode == 1){
                $billData = $bill['data'];
                foreach ($billData as $k =>$v){

                    $price        = $v['price'];
                    $operator        = $v['operator'];
                    $product_ids        = $v['children_product_id'];
                    $children_product_name        = $v['children_product_name'];

                    $father_key = $key;

                    $children_key = $product_ids . '_' . $operator . '_' . $price;

                    $NewProductId = $this->getNewProductId($customer_id,$product_ids);

                    if(!empty($NewProductId)){
                        $new_product_name = $this->getProductName($NewProductId['new_product_id']);
                        array_push($res2,['children_key' => $children_key,'new_product_name' => $new_product_name,'father_key' =>$father_key]);
                        //完成子产品名称的修改
                        $bill['data'][$children_key]['children_product_name'] = $new_product_name;
                    }else{
                        array_push($res1,['children_key' => $children_key,'children_product_name' => $children_product_name,'father_key'=>$father_key]);
                    }
                }

            }
        });

         //没有修改数据或没有重复数据不需要合并
        if (!empty($res1) && !empty($res2)) {
            $children_product_name_arr = array_column($res1, 'children_product_name');
            //判断是否有重复的子产品 合并金额和次数
            foreach ($res2 as $k => $v) {

                if (in_array($v['new_product_name'], $children_product_name_arr)) {
                    $index = $this->deep_get_key($v, $res1);

                    if ($index != false || $index === 0) {
                        $father_key2 = $v['father_key'];
                        $children_key2 = $v['children_key'];

                        $father_key1 = $res1[$index]['father_key'];
                        $children_key1 = $res1[$index]['children_key'];
                        //合并数据
                        $bills[$father_key1]['data'][$children_key1]['money'] = bcadd($bills[$father_key1]['data'][$children_key1]['money'], $bills[$father_key2]['data'][$children_key2]['money'], 6);
                        $bills[$father_key1]['data'][$children_key1]['number'] = bcadd($bills[$father_key1]['data'][$children_key1]['number'], $bills[$father_key2]['data'][$children_key2]['number']);

                        //删除被合并的子产品
                        unset($bills[$father_key2]['data'][$children_key2]);
                    }
                }
            }
        }

        return $bills;
    }

    //根据客户id和旧产品id 获取新的产品id
    protected function getNewProductId($customer_id,$old_product_id)
    {
        return  ProductIdConversion::getNewProductId($customer_id,$old_product_id);
    }
    //二维数组根据值获取键
    protected function  deep_get_key($search,$arr)
    {
        $index = false;
        if (is_array($arr)){
            foreach ($arr as $key => $value){
                if ($value['children_product_name'] == $search['new_product_name']){
                    //判断运营商是否相同 不同则跳出循环
                    $operator2 = explode('_',$search['children_key'])[1];
                    $operator1 = explode('_',$value['children_key'])[1];

                    if ($operator1 != $operator2){
                        continue;
                    }
                    $index = $key;
                }
            }
            return $index;
        }else{
            return  $index;
        }
    }

    protected function dealwithCustomerBillsUsageNumber($customer_usage,$customer_id)
    {
        if (is_array($customer_usage)){
            $product_ids = array_column($customer_usage,'product_id');
            $product_ids = array_unique($product_ids);


            array_walk($product_ids,function ($product_id) use (&$customer_usage,$customer_id,$product_ids){
                 $new_product_id = $this->getNewProductId($customer_id,$product_id);

                 if(!empty($new_product_id)){
                     //需要修改且数据有重复的情况
                     if(in_array($new_product_id['new_product_id'],$product_ids) &&!empty($new_product_id)){
                         $new_product_name = $this->getProductName($new_product_id['new_product_id']);

                         $this->mergeCustomerBillsUsageEditProductName($customer_usage,$product_id,$new_product_name);
                         //需要修改没有重复的数据
                     }elseif(!empty($new_product_id)){
                         $new_product_name = $this->getProductName($new_product_id['new_product_id']);
                         $this->editCustomerBillsUsageProductName($customer_usage,$product_id,$new_product_name);
                     }
                 }
            });
            sort($customer_usage);
            return $customer_usage;
        }

    }

    //没有重复数据 名称即可
    protected function  editCustomerBillsUsageProductName(&$customer_usage,$product_id,$new_product_name)
    {
        foreach ($customer_usage as &$v){
            if($v['product_id'] == $product_id){
              $v['product_name'] = $new_product_name;
            }
        }
    }

    //合并重复数据
   protected function mergeCustomerBillsUsageEditProductName(&$customer_usage,$product_id,$new_product_name)
   {
        foreach ($customer_usage as $k=>$v){
            if ($v['product_id'] == $product_id){
                $search['date'] = $v['date'];
                $search['product_name'] = $new_product_name;

                $index = $this->get_deep_index($customer_usage,$search);

                if ($index != false || $index === 0){
                    $customer_usage[$index]['number'] = bcadd($customer_usage[$index]['number'],$v['number']);
                    unset($customer_usage[$k]);
                }else{
                    $customer_usage[$k]['product_name'] = $new_product_name;
                }
            }
        }
   }

   //获取键值
   protected function  get_deep_index($customer_usage,$search)
   {
        $index = false;
        foreach ($customer_usage as $k=>$v){
            if ($v['date'] == $search['date'] && $v['product_name'] == $search['product_name']){
                return $k;
            }
        }
        return $index;
   }


   public function rerunBillRecord(){
       $params = $this->request->post();
       $user_cookie = request()->post('user_cookie', null);
       if ($user_cookie) {
           $user_name = SystemSession::where(['session_id'=>$user_cookie])->first();
           if(empty($user_name)){
               return ['msg' => '登录已过期，请重新登录', 'code'=>50001 , 'data'=>[]];
           }
           $user_name = $user_name->toArray();
           if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)){
               $sysName = $match[1];
           }
       }

       if(!isset($sysName)){
           return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
       }

       $start_date = str_replace('-', '', $params['start_date']);
       $end_date = str_replace('-', '', $params['end_date']);
       if($end_date>=date('Ymd')){
           return ['msg' => '结束日期不能大于等于今天的日期', 'code'=>50003 , 'data'=>[]];
       }

       if($start_date>$end_date){
           return ['msg' => '开始日期不能大于结束日期', 'code'=>50004 , 'data'=>[]];
       }

       if($sysName!='chang.liu'){//除了管理员刘畅外的其他人只能跑当月的
           //$limit_days = env('RERUN_LIMIT_DAYS', 7);
           //$start_date_limit = date('Ymd', strtotime("-".$limit_days."day"));
           $current_month = date('Ym');
           $compare_date = date('Ym', strtotime($start_date));
           if($current_month != $compare_date){
               return ['msg' => "管理员外的其他人只能跑当月之内的账单", 'code'=>50005 , 'data'=>[]];
           }
       }

       $stimestamp = strtotime($params['start_date']);
       $etimestamp = strtotime($params['end_date']);
       // 计算日期段内有多少天
       $days = ($etimestamp-$stimestamp)/86400+1;

       try{
           $insert = RerunBillRecord::addRecord($params, $sysName, $days);
           if($insert){
               $home = base_path();
               $logpath = $home.'/storage/logs/'.date('Ym').'_rerun_bill.log';
               $cmd = "cd {$home} && /data/ops/app/php7/bin/php artisan bill:create_bill_income --date={$start_date} ";
               if($params['customer_id'] != 'all'){
                   $cmd .= " --customer_id=".$params['customer_id'];
               }
               $cmd .= " --product_id=".$params['father_id'];
               $cmd .= " --days=".$days;
               file_put_contents($logpath, date('Y-m-d H:i:s').'执行命令:'.$cmd.PHP_EOL, FILE_APPEND);
               $output = exec($cmd." 2>&1>>".$logpath, $output, $status);
               file_put_contents($logpath, date('Y-m-d H:i:s').'执行命令结束'.$output.PHP_EOL, FILE_APPEND);

               $cmd_v2 = str_replace('create_bill_income', 'create_bill_income_v2', $cmd);
               file_put_contents($logpath, date('Y-m-d H:i:s').'执行v2命令:'.$cmd_v2.PHP_EOL, FILE_APPEND);
               $output_v2 = exec($cmd_v2." 2>&1>>".$logpath, $output_v2, $status_v2);
               file_put_contents($logpath, date('Y-m-d H:i:s').'执行v2命令结束'.$output_v2.PHP_EOL, FILE_APPEND);

               return ['msg' => '任务记录生成成功', 'code'=>0 , 'data'=>[]];
           }else{
               return ['msg' => '任务记录生成失败，请联系技术', 'code'=>50006 , 'data'=>[]];
           }

       }catch(Exception $e){
           return ['msg' => "任务记录生成失败:".$e->getMessage()."，请联系技术", 'code'=>50007 , 'data'=>[]];
       }


   }


    public function saveCustomerBillAdjust(){
        $params = $this->request->post();
        $user_cookie = request()->post('user_cookie', null);
        $id = request()->post('id', null);
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id'=>$user_cookie])->first();
            if(empty($user_name)){
                return ['msg' => '登录已过期，请重新登录', 'code'=>50001 , 'data'=>[]];
            }
            $user_name = $user_name->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)){
                $sysName = $match[1];
            }
        }

        if(!isset($sysName)){
            return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
        }

        try{
            if(intval($id) > 0){//编辑
                $up = CustomerBillAdjust::updateRecord(['id' => $id], $params, $sysName);
                if($up){
                    return ['msg' => '记录修改成功', 'code'=>0 , 'data'=>[]];
                }else{
                    return ['msg' => '记录修改失败，请联系技术', 'code'=>50006 , 'data'=>[]];
                }

            }else{
                $insert = CustomerBillAdjust::addRecord($params, $sysName);
                if($insert){
                    return ['msg' => '记录生成成功', 'code'=>0 , 'data'=>[]];
                }else{
                    return ['msg' => '记录生成失败，请联系技术', 'code'=>50006 , 'data'=>[]];
                }
            }

        }catch(Exception $e){
            return ['msg' => "任务记录操作失败:".$e->getMessage()."，请联系技术", 'code'=>50007 , 'data'=>[]];
        }

    }


    public function customerBillAdjustList(){
        $limit = request()->post('limit', 20);
        $page = request()->post('page', 1);
        $father_id = request()->post('father_id', '');
        $product_id = request()->post('product_id', '');
        $customer_id = request()->post('customer_id', '');
        $operator = request()->post('operator', '');
        $month = request()->post('month', '');
        $operate_date = request()->post('operate_date', '');
        $source = request()->post('source', '');
        $operate_type = request()->post('operate_type', '');
        $offset = ($page - 1) * $limit;
        $where = ['delete_at' => 0];
        if($father_id){
            $where['father_id'] = $father_id;
        }
        if($product_id){
            $where['product_id'] = $product_id;
        }
        if($customer_id){
            $where['customer_id'] = $customer_id;
        }
        if($operator){
            $where['operator'] = $operator;
        }

        if($month){
            $start_date = str_replace('-', '', $month[0]).'01';
            $end_date = str_replace('-', '', $month[1]).'01';
            $end_time = strtotime($end_date);
            $end_date = date('Ymd', strtotime("+1 month -1 day", $end_time));

            $where[] = ['date', '>=', $start_date];
            $where[] = ['date', '<=', $end_date];
        }
        if($operate_date){
            $start_date = $operate_date[0];
            $end_date = $operate_date[1]." 23:59:59";
            $start_time = strtotime($start_date);
            $end_time = strtotime($end_date);

            $where[] = ['create_at', '>=', $start_time];
            $where[] = ['create_at', '<=', $end_time];
        }

        if($source !=='' ){
            $where['source'] = $source;
        }

        //来源map
        $source_map = CommonEnumModel::getTypeMaps('1');
        if($operate_type == 'export'){
            $rep = new CustomerIncomeRepository();
            $res['data'] = CustomerBillAdjust::getListByCondition($where)->toArray();
        }else{
            $res = CustomerBillAdjust::getList($where, $offset, $limit);
        }

        $sum_number = 0;//计费用量
        $sum_money = 0;//调整费用
        foreach ($res['data'] as &$val){
            $sum_number = bcadd($val['fee_number'], $sum_number, 0);
            $sum_money = bcadd($val['money'], $sum_money, 4);
            //导出的时候在增加一列是否为新老成本
            if($operate_type == 'export'){
                $current_month = date('Ym', strtotime($val['date']));
                $is_new = $rep->checkCustomerFirstIncome($val['customer_id'], $val['product_id'], $current_month);
                $val['is_new'] = $is_new ? '新' : '老';
            }
            $val['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($val['customer_id']);
            $val['father_name'] = RedisCache::instance('productId_productName_mapping')->get($val['father_id']);
            $val['product_name'] = RedisCache::instance('productId_productName_mapping')->get($val['product_id']);
            $val['operator_name'] = RedisCache::instance('operator_name_mapping')->get($val['operator']);
            $val['source_name'] = $source_map[$val['source']] ?? '异常';
            $val['date'] = date('Y-m-d', strtotime($val['date']));
            $val['create_at'] = date('Y-m-d H:i:s', $val['create_at']);
        }

        $res['sum_number'] = $sum_number;
        $res['sum_money'] = round($sum_money, 3);

        return $res;
    }

    public function customerBillAdjustInfo(){
        $id = request()->get('id');

        try{
            $info = CustomerBillAdjust::where(['id' => $id])->first();
            if($info){
                $info['date'] = date('Y-m-d', strtotime($info['date']));
                $info['father_id'] = (string)$info['father_id'];
                $info['product_id'] = (string)$info['product_id'];
                return ['msg' => '获取数据成功', 'code' => 0 , 'data' => $info];
            }
        }catch (Exception $e){
            return ['msg' => '获取数据异常', 'code' => 5002, 'data' => []];
        }

        return ['msg' => '获取数据失败', 'code' => 5001, 'data' => []];
    }

    public function customerBillAdjustDel(){
        $id = request()->get('id', '');
        $user_cookie = request()->get('user_cookie', '');
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id'=>$user_cookie])->first();
            if(empty($user_name)){
                return ['msg' => '登录已过期，请重新登录', 'code'=>50001 , 'data'=>[]];
            }
            $user_name = $user_name->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)){
                $sysName = $match[1];
            }
        }

        if(!isset($sysName)){
            return ['msg' => '非法操作', 'code'=>50002 , 'data'=>[]];
        }

        if(intval($id) == 0){
            return ['msg' => '非法操作', 'code'=>50003 , 'data'=>[]];
        }

        try{
            $up = CustomerBillAdjust::where(['id' => $id])->update(['delete_at' => time(), 'admin' => $sysName]);
            if($up){
                return ['msg' => '删除成功', 'code'=> 0 , 'data'=>[]];
            }

            return ['msg' => '删除失败', 'code'=> 5005 , 'data'=>[]];
        }catch (Exception $e){
            $this->createBaseResponse("删除数据失败:".$e->getMessage(), 5006);
        }

    }

    public function getCustomerExpendList()
    {
        $limit = intval(request()->post('limit', 20));
        $page = intval(request()->post('page', 1));
        $father_id = intval(request()->post('father_id', ''));
        $product_id = intval(request()->post('product_id', ''));
        $customer_id = request()->post('customer_id', '');
        $start_date = request()->post('start_date', '');
        $update_time = request()->post('update_time', '');
        $source = request()->post('source', '');

        $params = compact('father_id', 'product_id', 'customer_id', 'start_date', 'update_time', 'source');
        $result = CustomerExpend::getList($params, $page, $limit);

        $source_map = CommonEnumModel::getListByType(1);
        $source_map = array_column($source_map, 'value', 'name');

        $list = [];
        foreach ($result as $value) {
            $info = [];
            $info['id'] = $value['id'];
            $info['customer_id'] = $value['customer_id'];
            $info['customer_name'] = $value['customer_name'];
            $info['name'] = $value['name'];
            $info['start_date'] = $value['start_date'];
            $info['type'] = $value['type'] == 1 ? '赠送' : '消费';
            $sub = $value['type'] == 1 ? '-' : '';
            $info['money'] = floatval($sub . $value['money']);
            $info['fee_number'] = $value['fee_number'];
            $info['remark'] = $value['remark'];
            $info['source'] = $value['source'];
            $info['source_name'] = $source_map[$value['source']];
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($value['product_id']);
            $info['product_name'] = RedisCache::instance('productId_productName_mapping')->get($value['product_id']);
            $info['father_name'] = $father_id ? RedisCache::instance('productId_productName_mapping')->get($father_id) : $info['product_name'];
            $info['operator_name'] = $value['operator'] ? RedisCache::instance('operator_name_mapping')->get($value['operator']) : '';
            $create_time = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
            $info['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : $create_time;
            $info['should_date'] = empty($value['should_date'])?'':date('Y-m', strtotime($value['should_date']));

            $info['father_id'] = $father_id;
            $info['product_id'] = $value['product_id'];
            $_time = strtotime($value['start_date']."01");
            $info['search_start_date'] = date("Ym01",$_time);
            $info['search_end_date']   = date("Ymt",$_time);
            $list[] = $info;
        }

        $result = CustomerExpend::getCountAndTotalMoney($params);
        $result = $result ? array_column($result, null, 'type') : [];
        $add_money = isset($result[2]['money']) ? floatval($result[2]['money']) : 0;
        $sub_money = isset($result[1]['money']) ? floatval($result[1]['money']) : 0;
        $total_money = floatval($add_money - $sub_money);
        $count = $result ? array_sum(array_column($result, 'nums')) : 0;

        return ['list' => $list, 'count' => $count, 'total_money' => $total_money, 'sub_money' => $sub_money, 'add_money' => $add_money];
    }

    public function getCustomerExpendInfo()
    {
        $id = intval(request()->get('id'));
        try{
            $info = CustomerExpend::where(['id' => $id])->first()->toArray();
            if ($info) {
                $info['type'] = (string)$info['type'];
                $info['start_date'] = date('Ym', strtotime($info['start_date'] . '01'));
                $father_id = RedisCache::instance('productId_fatherId_mapping')->get($info['product_id']);
                $info['father_id'] = $father_id ? intval($father_id) : intval($info['product_id']);
                $info['product_id'] = (string)$info['product_id'];
                $info['source'] = (string)$info['source'];
                unset($info['create_time'], $info['update_time']);
                return ['msg' => '获取数据成功', 'code' => 0 , 'data' => $info];
            }
        }catch (Exception $e){
            return ['msg' => '获取数据异常', 'code' => 5002, 'data' => []];
        }

        return ['msg' => '获取数据失败', 'code' => 5001, 'data' => []];
    }

    public function saveCustomerExpend()
    {
        $data = $this->request->post();
        $user_cookie = request()->post('user_cookie', null);
        $id = intval(request()->post('id', null));
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id' => $user_cookie])->first();
            if (empty($user_name)) {
                return ['msg' => '登录已过期，请重新登录', 'code' => 50001 , 'data' => []];
            }
            $user_name = $user_name->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)) {
                $sysName = $match[1];
            }
        }
        if (!isset($sysName)) {
            return ['msg' => '非法操作', 'code' => 50002 , 'data' => []];
        }
        $have_split = false;

        if ($id == 0) {
            //检查该客户是否是预付费客户
            $split_day    = [];
            $source_m     = [];
            $customer_ids = [];
            foreach ($data['data'] as $info) {
                $split_day[$info['customer_id']] = $info['profile_show_date'];
                $source_m[$info['source']][]     = $info['customer_id'];
                $customer_ids[]                  = $info['customer_id'];
            }
            if(isset($source_m[0])) {
                $customer_info     = Customer::whereIn('customer_id', $customer_ids)->where('payment_type', 1)->get()->toArray();
                $prepayed_customer = array_column($customer_info, 'customer_id');

                $_customer_id_arr      = array_intersect($source_m[0], $prepayed_customer);
                $split_receipt_serials = Remit::getListByCustomerIds($_customer_id_arr, null, 0);
                $split_receipt_serials = array_column($split_receipt_serials, 'receipt_serial');
                $split_day = array_shift($split_day);
                //检查当月是否存在自动拆单数据
                $rsp = RemitSplitPrice::getListWithRSandMonth($split_receipt_serials,date("Ym", strtotime($split_day)));

                if (count($rsp) > 0) {
                    $have_split = true;
                }
            }
        }


        try {
            $service = new CalculateProductIncomeService();
            if ($id > 0) { // 编辑
                $data['should_date'] = empty($data['should_date'])?null:$data['should_date'];
                $data['money_finance'] = $service->setIncomeByCondition('', $data['customer_id'], $data['product_id'], 0, $data['money']);
                $res = CustomerExpend::updateData($id, $data);
                if ($res) {
                    return ['msg' => '编辑成功', 'code' => 0 , 'data' => ['have_split' => $have_split]];
                } else {
                    return ['msg' => '编辑失败，请联系技术', 'code' => 50006 , 'data' => []];
                }
            } else {
                $add_list = $data['data'];
                foreach ($add_list as $value) {
                    // 超过10000元，走审批流程
                    $value['should_date'] = empty($value['should_date'])?null:$value['should_date'];
                    $value['money_finance'] = $service->setIncomeByCondition('', $value['customer_id'], $value['product_id'], 0, $value['money']);
                    if (10000 <= $value['money']) {
                        $this->expendToApproval($value, $user_cookie);
                        continue;
                    }
                    CustomerExpend::addData($value);
                }
                return ['msg' => '添加成功', 'code' => 0 , 'data' => ['have_split' => $have_split]];
            }
        } catch (Exception $e) {
            return ['msg' => "任务记录操作失败:" . $e->getMessage() . "，请联系技术", 'code' => 50007 , 'data' => []];
        }
    }

    /**
     * @param $aData
     * @param $sUserCookie
     * @param $sysName
     * @return bool
     */
    private function expendToApproval($aData = [], $sUserCookie = '') {
        $sCustomerId = $aData['customer_id'];
        $aCustomer = Customer::getCustomerInfo($sCustomerId);
        $iFatherId = $aData['father_id'];
        $iProductId = $aData['product_id'];
        $aProductList = Product::getProductListByProductIds([$iFatherId, $iProductId], ['product_id', 'product_name']);
        $aProductMap = array_column($aProductList, 'product_name', 'product_id');
        $sType = $aData['type'] == 1 ? '赠送' : '消费';
        $sMoney = $aData['money'];
        $sRemark = $aData['remark'];
        $sStartDate = $aData['start_date'];
        $sSourceName = $aData['source_name'];

        $sApplyContent = sprintf('特殊消耗, 客户:<%s>, 主产品:「%s」, 子产品:「%s」, 来源: %s, 调整月份: %s, 原因:「%s」, 类型: 「%s」, 金额: 「%s」',
             $aCustomer['name'], $aProductMap[$iFatherId] ?? '', $aProductMap[$iProductId] ?? '', $sSourceName, $sStartDate, $sRemark, $sType, $sMoney);

        return ApprovalRepository::addApproval($sCustomerId,'',$iProductId,Approval::URL_BILL_EXPEND_SAVE, $aData, $sApplyContent, $sUserCookie);
    }

    /**
     * @param $aData
     * @return mixed
     */
    public static function saveExpendByApproval($aData) {
        return CustomerExpend::addData($aData);
    }

    /**
     *
     *
     * @return array|void
     * @throws \Exception
     * <AUTHOR> 2024-02-02 15:15:00
     *
     */
    public function delCustomerExpend()
    {
        $id = intval(request()->get('id', ''));
        $user_cookie = request()->get('user_cookie', '');
        if ($user_cookie) {
            $user_name = SystemSession::where(['session_id'=>$user_cookie])->first();
            if(empty($user_name)){
                return ['msg' => '登录已过期，请重新登录', 'code'=>50001 , 'data'=>[]];
            }
            $user_name = $user_name->toArray();
            if (preg_match('/\"(.*\..*)\"/', $user_name['session_data'], $match)){
                $sysName = $match[1];
            }
        }

        if (!isset($sysName)) {
            return ['msg' => '非法操作', 'code' => 50002 , 'data' => []];
        }

        if (!in_array($sysName,['lili.liu','chang.liu','wei.xiu'])) {
            return ['msg' => '没有操作权限', 'code' => 50002 , 'data' => []];
        }

        if (!$id) {
            return ['msg' => '非法操作', 'code' => 50002 , 'data' => []];
        }

        try {
            $info = CustomerExpend::getInfoById($id);
            $del = CustomerExpend::where(['id' => $id])->delete();
            if($del){
                return ['msg' => '删除成功', 'code'=> 0 , 'data'=>[]];
            }

            return ['msg' => '删除失败', 'code'=> 5005 , 'data'=>[]];
        } catch (Exception $e) {
            // $this->createBaseResponse("删除数据失败:".$e->getMessage(), 5006);
            return ['msg' => "删除数据失败:" . $e->getMessage() . "，请联系技术", 'code' => 5006 , 'data' => []];
        }
    }

    /**
     * @throws Exception
     */
    public function downloadCustomerExpend(){
        $father_id = intval(request()->get('father_id', ''));
        $product_id = intval(request()->get('product_id', ''));
        $customer_id = request()->get('customer_id', '');
        $source = request()->get('source', '');
        $start_date = request()->get('start_date', '');
        $update_time = request()->get('update_time', '');

        $start_date = $start_date ? explode(',', $start_date) : [];
        $update_time = $update_time ? explode(',', $update_time) : [];

        $params = compact('father_id', 'product_id', 'customer_id', 'start_date', 'update_time', 'source');

        $sourceMap = CommonEnumModel::getListByType(1);
        $sourceMap = array_column($sourceMap, 'value', 'name');

        $list = CustomerExpend::getList($params, 0, 0);
        $customer_ids = array_column($list,'customer_id');
        $product_ids = array_column($list,'product_id');
        $father_ids = [];
        foreach ($product_ids as $_product_id) {
            $_father_id = RedisCache::instance('productId_fatherId_mapping')->get($_product_id);
            $father_ids[$_father_id] = $_father_id;
        }

        //获取客户信息
        $_customer_infos = Customer::getCustomerListByCustomerIds(['*'],$customer_ids);
        $customer_infos = [];
        foreach($_customer_infos as $v){
            $customer_infos[$v['customer_id']] = $v;
        }

        $api_key_customer_id_map = RedisCache::instance('apikey_customerId_mapping')->get();
        $api_keys = [];
        foreach ($api_key_customer_id_map as $_apikey => $_customer_id){
            if(in_array($_customer_id,$customer_ids)){
                $api_keys[] = $_apikey;
            }
        }

        $cshr = new CustomerSalesmanHistoryRepository();
        $cshr_month = $cshr->getListMonthly($customer_ids,0,date("Ym"),"Ym");
        // dd($cshr_month);

        $dept = SystemDept::getAllDeptInfo();
        $dept_map = array_column($dept, 'dept_name', 'dept_id');
        // dd($dept);

        $user_map = SystemUser::pluck('realname','username');
        $dept_user_map = SystemUser::pluck('dept_id','username');

        //202107之前的都算老收入
        //202107之后 开始有收入一年内为新收入 一年后为老收入
        // $start = $this->findStartMonth('202107',isset($start_date[0])?$start_date[0]:date("Ym"));
        // $start = '202207';
        $_haveIncome = BillProductIncomeV2::isHaveIncome(date("Ym").'01',$api_keys,$father_ids);

        $haveIncome = [];
        foreach($_haveIncome as $item){
            $_customer_id = $api_key_customer_id_map[$item['apikey']];
            $haveIncome[$_customer_id][$item['father_id']] = $item['ea_date'];
        }

        foreach ($list as &$value) {
            $month = date('Ym', strtotime($value['profile_show_date']));

            if(!isset($cshr_month[$value['customer_id']][$month])) {
                $value['salesman'] = '';
                $value['dept']     = '';
            }else {
                $salesman          = $cshr_month[$value['customer_id']][$month];
                $dept              = $dept_user_map[$salesman];
                $dept              = $dept_map[$dept];
                $salesman          = $user_map[$salesman];
                $value['salesman'] = $salesman;
                $value['dept']     = $dept;
            }

            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($value['product_id']);
            $value['product_name'] = RedisCache::instance('productId_productName_mapping')->get($value['product_id']);
            $value['father_name'] = $father_id ? RedisCache::instance('productId_productName_mapping')->get($father_id) : $value['product_name'];
            $value['operator_name'] = $value['operator'] ? RedisCache::instance('operator_name_mapping')->get($value['operator']) : '';
            $create_time = $value['create_time'] ? $value['create_time'] : '';
            $value['update_time'] = $value['update_time'] ? $value['update_time'] : $create_time;
            $sub = $value['type'] == 1 ? '-' : '';
            $value['money'] = floatval($sub . $value['money']);
            $value['sourceName'] = $sourceMap[$value['source']];
            //是否是老收入
            if(isset($haveIncome[$value['customer_id']][$father_id])) {
                //判断是否是一年以前
                $value['is_old_income'] = str_replace('-', '', $value['profile_show_date']) - 10000 > $haveIncome[$value['customer_id']][$father_id];
            }else {
                $value['is_old_income'] = false;
            }
            $value['channel_mode'] = isset($customer_infos[$value['customer_id']]['channel_mode'])?$customer_infos[$value['customer_id']]['channel_mode']:'';
        }
        return Excel::download(new CustomerExpendExport($list), 'customer_expend.xlsx');
    }


    public function bulkSendStatementMail()
    {
        $info = $this->checkParams();
        if (!is_array($info)) {
            return $info;
        }
        $customer_ids = $info['customer_ids'];
        $customer_list = $info['customer_list'];
        $customer_del = $info['customer_del'];
        $month = $info['month'];

        $start_date = date('Ymd', strtotime('first day of this month', strtotime($month . '01')));
        $end_date = date('Ymd', strtotime('last day of this month', strtotime($month . '01')));

        // 获取客户总余额
        $balance_list = $this->getBalanceService($end_date, '', $info['source'])->getCustomerBalanceV2($customer_ids);

        // 获取客户总消费
        $consume_list = $this->getCustomerConsumeList($start_date, $end_date, $customer_ids,$info['source']);

        // 获取客户上月剩余金额
        $prev_month_date = date('Ymd', strtotime('last day of last month', strtotime($month . '01')));
        $prev_month_balance_list = $this->getBalanceService($prev_month_date, '', $info['source'])->getCustomerBalanceV2($customer_ids);

        // 获取当月的充值数据
        $recharge_list = $this->getRechargeService($start_date, $end_date, $info['source'])->getCustomerRecharge($customer_ids);

        $apikeys = Account::whereIn('customer_id', $customer_ids)->pluck('apikey')->toArray();

        // 特殊消耗列表
        $customer_special_list = $this->getCustomerSpecialList($customer_ids, $start_date, $end_date, $info['source']);
        // 获取客户的在这个月份的计费配置数据
        $bill_list = $this->getCustomerConsumeBillList($apikeys, $start_date, $end_date,$info['source']);

        // 获取客户产品日计费用量
        $customer_usage_list = $this->getCustomerBillsUsageNumberList($apikeys, $start_date, $end_date, $info['source']);

        $other_list = $this->getLatelySendTimeList($customer_ids, $info['source']);
        $list = [];
        foreach ($customer_list as $customer_id => $item) {
            // 某客户余额
            $balance = $balance_list[$customer_id];
            // 某客户总消费
            $consume = $consume_list[$customer_id];
            // 某客户上个月余额
            $prev_month_balance = $prev_month_balance_list[$customer_id];
            // 某客户当月的充值数据
            $recharge = $recharge_list[$customer_id];
            // 某客户计费配置数据
            $bill = isset($bill_list[$customer_id]) ? $bill_list[$customer_id] : [];

            $customer_usage = isset($customer_usage_list[$customer_id]) ? $customer_usage_list[$customer_id] : [];

            $customer_special = isset($customer_special_list[$customer_id]) ? $customer_special_list[$customer_id] : [];

            // 获取客户的收件人、抄送人、主题、客户名称等信息
            $base_info = $this->getCustomerListEmailConfig($item, $month);
            // 获取客户邮件内容
            $base_info['content'] = $this->getCustomerStatementDetailsContent($consume['money'], $balance['money'], ['month' => $month]);

            // 获取结算单数据
            $final_bill_info = $this->getCustomerFinalBill($item, $prev_month_balance['money'], $balance['money']);
            // 获取结算单数据
            $final_bill_info['details'] = $this->getCustomerFinalBillDetails($recharge['money'], $consume, $month);

            // 获取账单明细数据
            $details_bill_info = $this->getCustomerConsumeDetailInfo($bill, $customer_id, $customer_special);

            // 处理产品日计费
            $customer_usage = $this->dealwithCustomerBillsUsageNumber($customer_usage, $customer_id);

            // 其他
            $other = isset($other_list[$customer_id]) ? $other_list[$customer_id] : ['is_remind' => false, 'interval' => ''];

            $source = $info['source'];
            $list[$customer_id] = compact('base_info', 'final_bill_info', 'details_bill_info', 'customer_usage', 'other', 'customer_id', 'source');
        }

        $customer_is_remind = $send_fail = [];
        $send_count = 0;
        $customer_desc = [];
        foreach ($list as $customer_id => $item) {
            $customer_name = $customer_list[$customer_id]['name'];
            if (!$item['other']['is_remind']) {
                $res = $this->sendEmail($item);
                if ($res === true) {
                    $send_count++;
                } else {
                    $send_fail[] = $customer_list[$customer_id]['name'];
                    $desc = '发送失败';
                    $customer_desc[] = [$customer_id, $customer_name, $desc];
                }
            } else {
                $customer_is_remind[] = $customer_list[$customer_id]['name'];
                $desc = '当月已发送过';
                $customer_desc[] = [$customer_id, $customer_name, $desc];
            }
        }

        return compact('customer_del', 'customer_is_remind', 'send_count', 'send_fail', 'customer_desc');
    }

    protected function checkParams()
    {
        // 客户id
        $customer_ids = $this->request->post('customer_id');
        if (!$customer_ids) {
            return 13030;
        }

        $sourceMap = CommonEnumModel::getListByType(1);
        $sourceMap = array_column($sourceMap, 'name');
        if (!in_array($source, $sourceMap)) {
            return 13012;
        }

        // 月份
        $month = $this->request->post('month');
        if (!preg_match('/^\d{6}$/', $month)) {
            return 13031;
        }

        if ($month < 202010) {
            return 13032;
        }

        // 获取客户信息
        $customer_list = Customer::select(['customer_id', 'bill_email', 'bill_cc_email', 'company', 'name'])
            ->where('is_delete', 0)
            ->whereIn('customer_id', $customer_ids)
            ->get()
            ->toArray();

        if (!$customer_list) {
            return 13033;
        }

        $customer_list = array_column($customer_list, null, 'customer_id');
        // 删除的客户
        $customer_del = array_diff($customer_ids, array_keys($customer_list));
        return compact('customer_ids', 'month', 'customer_list', 'customer_del', 'source');
    }

    protected function getCustomerConsumeList($start_date, $end_date, $customer_ids,$source)
    {
        $list = [];
        $consume_list = $this->getConsumeService($start_date, $end_date,$source)->getCustomerIncomeV2($customer_ids);
        foreach ($consume_list as $customer_id => $item) {
            $special_consume_details = [];
            // 特殊消耗
            foreach ($item['details']['special'] as $value) {
                $special_consume_details[] = [
                    'title' => $value['title'],
                    'money' => $value['money'],
                    'type' => $value['type']
                ];
            }

            $money = $item['money'];
            $bill_consume = $item['details']['bill'] ? array_sum(array_column($item['details']['bill'], 'money')) : 0;

            $list[$customer_id] = compact('special_consume_details', 'bill_consume', 'money');
        }

        return $list;
    }

    protected function getCustomerSpecialList($customer_ids, $start_date, $end_date, $source = '')
    {
        $special_list = CustomerExpend::select([
            'customer_id',
            'product_id',
            'operator',
            'type',
            DB::raw('sum(fee_number) AS number'),
            DB::raw('sum(money) AS money')
        ])
            ->where('profile_show_date', '>=', $start_date)
            ->where('profile_show_date', '<=', $end_date)
            ->where(function ($query) use ($customer_ids) {
                if ($customer_ids) {
                    $query->whereIn('customer_id', $customer_ids);
                }
            })
            ->where(function ($query) use ($source) {
                if ($source !== '') {
                    $query->where('source', $source);
                }
            })
            ->groupBy(['customer_id', 'product_id', 'operator', 'type'])
            ->get()->toArray();

        $list = [];
        foreach ($special_list as $item) {
            $customer_id = $item['customer_id'];
            $product_id = $item['product_id'];
            $operator = isset($item['operator']) ? $item['operator'] : '';
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
            $father_id = $father_id == 401 ? 0 : $father_id;
            if ($item['type'] == 1) { // 赠送
                $list[$customer_id][$product_id][$operator]['number'] = $list[$customer_id][$product_id][$operator]['number'] ?? 0;
                $list[$customer_id][$product_id][$operator]['number'] -= $item['number'];

                $list[$customer_id][$product_id][$operator]['money'] = $list[$customer_id][$product_id][$operator]['money'] ?? 0;
                $list[$customer_id][$product_id][$operator]['money'] = bcsub($list[$customer_id][$product_id][$operator]['money'], $item['money'], 6);

                // 父产品
                if ($father_id) {
                    $list[$customer_id][$father_id][$operator]['number'] = $list[$customer_id][$father_id][$operator]['number'] ?? 0;
                    $list[$customer_id][$father_id][$operator]['number'] -= $item['number'];

                    $list[$customer_id][$father_id][$operator]['money'] = $list[$customer_id][$father_id][$operator]['money'] ?? 0;
                    $list[$customer_id][$father_id][$operator]['money'] = bcsub($list[$customer_id][$father_id][$operator]['money'], $item['money'], 6);
                }
            } elseif ($item['type'] == 2) { // 消费
                $list[$customer_id][$product_id][$operator]['number'] = $list[$customer_id][$product_id][$operator]['number'] ?? 0;
                $list[$customer_id][$product_id][$operator]['number'] += $item['number'];

                $list[$customer_id][$product_id][$operator]['money'] = $list[$customer_id][$product_id][$operator]['money'] ?? 0;
                $list[$customer_id][$product_id][$operator]['money'] = bcadd($list[$customer_id][$product_id][$operator]['money'], $item['money'], 6);

                if ($father_id) {
                    $list[$customer_id][$father_id][$operator]['number'] = $list[$customer_id][$father_id][$operator]['number'] ?? 0;
                    $list[$customer_id][$father_id][$operator]['number'] += $item['number'];

                    $list[$customer_id][$father_id][$operator]['money'] = $list[$customer_id][$father_id][$operator]['money'] ?? 0;
                    $list[$customer_id][$father_id][$operator]['money'] = bcadd($list[$customer_id][$father_id][$operator]['money'], $item['money'], 6);
                }
            }
        }

        return $list;
    }

    protected function getCustomerConsumeBillList($apikeys, $start_date, $end_date, $source)
    {
        $list = [];
        $bill_list = BillCustomerIncomeV2::from('bill_customer_income_v2 as bci')
            ->select([
                'bci.apikey',
                'bci.father_id',
                'bci.product_ids',
                'bci.operator',
                'bci.together_call_number',
                'bci.date',
                'bci.price',
                'bci.number',
                'bci.money',
                'cpc.mode'
            ])->leftJoin('config_price_customer as cpc', 'bci.config_price_customer_id', '=', 'cpc.id')
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->where('bci.source', $source)
            ->whereIn('bci.apikey', $apikeys)
            ->where('methods', '<>', 1)//包年的不展示
            ->orderBy('date')->get()->toArray();

        foreach ($bill_list as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $list[$customer_id][] = $item;
        }

        return $list;
    }

    protected function getCustomerBillsUsageNumberList($apikeys, $start_date, $end_date, $source)
    {
        $result_modeIs1 = BillCustomerIncomeV2::from('bill_customer_income_v2 as bci')
            ->select([
                'bci.apikey',
                'bci.product_ids as product_id',
                'bci.date',
                'p.product_name',
                DB::raw('SUM(`bci`.`number`) as number')])
            ->leftJoin('config_price_customer as cpc', 'cpc.id', '=', 'bci.config_price_customer_id')
            ->leftJoin('product as p', 'p.product_id', '=', 'bci.product_ids')
            ->where('cpc.mode', '1')
            ->where('bci.source', $source)
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->whereIn('bci.apikey', $apikeys)
            ->groupBy(['bci.date', 'bci.product_ids'])
            ->get()
            ->toArray();

        $result_modeIsOther = BillCustomerIncomeV2::from('bill_customer_income_v2 as bci')
            ->select([
                'bci.apikey',
                'bci.father_id as product_id',
                'bci.date',
                'p.product_name',
                'bci.number'])
            ->leftJoin('config_price_customer as cpc', 'cpc.id', '=', 'bci.config_price_customer_id')
            ->leftJoin('product as p', 'p.product_id', '=', 'bci.product_ids')
            ->where('cpc.mode', '<>', '1')
            ->where('bci.source', $source)
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->whereIn('bci.apikey', $apikeys)
            ->groupBy(['bci.date', 'bci.father_id'])
            ->get()
            ->toArray();
        //合并两部分数据并补充父级产品数据
        $result = array_merge(
            array_map([$this, 'fillFatherInfo'], $result_modeIs1),
            array_map([$this, 'fillFatherInfo'], $result_modeIsOther)
        );

        $list = [];
        foreach ($result as $item) {
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $list[$customer_id][] = $item;
        }

        foreach ($list as &$item) {
            $father_ids = array_column($item, 'father_id');
            $product_ids = array_column($item, 'product_id');
            $date_list = array_column($item, 'date');
            array_multisort($father_ids, SORT_ASC, $product_ids, SORT_ASC, $date_list, SORT_ASC, $item);
        }

        return $list;
    }

    protected function getCustomerListEmailConfig($customer_info, $month)
    {
        $addressee = $customer_info['bill_email'];
        $cc = $customer_info['bill_cc_email'];
		$subject = $this->getMailSubjectByCustomer($customer_info,$month);
        return compact('addressee', 'cc', 'subject');
    }

    protected function getCustomerFinalBill($customer_info, $prev_month_balance, $balance)
    {
        $result = [];
        //签约公司名称、简称
		$result = $this->getBillMailHeaderAndFooter();

        //客户信息
        $result['customer_name'] = $customer_info['name'];
        $result['company']       = $customer_info['company'];

        //上月剩余金额
        $result['prev_month_balance'] = number_format($prev_month_balance, 2);

        $result['balance'] = number_format($balance, 2);

        return $result;
    }

    protected function getCustomerFinalBillDetails($recharge, $consume_info, $month)
    {
        $result = [];

        $start_date = date('Ymd', strtotime('first day of this month', strtotime($month . '01')));
        $end_date   = date('Ymd', strtotime('last day of this month', strtotime($month . '01')));

        //账单消费
        $bill_consume = $consume_info['bill_consume'];
        $title        = $start_date . ' -- ' . $end_date;

        //$result[] = [$title, $recharge, $bill_consume];
        // 特殊消费数据
        $special_consume_details = $consume_info['special_consume_details'];
        foreach ($special_consume_details as $item) {
            switch ($item['type']) {
                case 1:
                    //赠送
                    //$result[] = [$item['title'], $item['money'], 0];
                    $bill_consume = $bill_consume - $item['money'];
                    break;
                case 2:
                    //消费
                    //$result[] = [$item['title'], 0, $item['money']];
                    $bill_consume = $bill_consume + $item['money'];
                    break;
            }
        }

        $result[] = [$title, $recharge, $bill_consume];

        //补充合计数据并整理数据
        $recharge_money_total = 0;
        $consume_money_total  = 0;
        $result = array_map(function ($item) use (&$recharge_money_total, &$consume_money_total) {
            $recharge_money_total = bcadd($item[1], $recharge_money_total, 6);
            $consume_money_total  = bcadd($item[2], $consume_money_total, 6);

            $item[1] = $item[1] ? number_format($item[1], 2) : 0;
            $item[2] = $item[2] ? number_format($item[2], 2) : 0;

            return $item;
        }, $result);

        $result[] = ['合计', number_format($recharge_money_total, 2), number_format($consume_money_total, 2)];

        return $result;

    }

    protected function getCustomerConsumeDetailInfo($bills, $customer_id, $customer_special)
    {
        //分组统计
        $bills = $this->mergeCustomerConsumeDetailsBills($bills, $customer_special);

        //更改部分子产品名称
        $bills = $this->changeProductName($bills, $customer_id);

        //对每一个父产品数据做一个小计
        $bills = $this->makeCustomerConsumeDetailsSubtotal($bills);


        //过滤数据，看看是否需要展示部分字段
        $hide_fields = $this->hideCustomerConsumeDetailsFields($bills);

        return compact('bills', 'hide_fields');
    }

    protected function sendEmail($data)
    {
        try {
            //发送邮件(同步执行)
            $sendMailService = new SendMailService();
            //设置邮箱配置(该对账单功能发送邮箱和默认邮箱不一样，且和默认邮箱的用户名密码也不一致，因此需要设置指定一下)
            $username = config('params.yun_ying_mail.mail_username');
            $password = config('params.yun_ying_mail.mail_password');
            $sendMailService->setConfig(['username' => $username, 'password' => $password]);
            //设置发送人地址
            $sendMailService->setFrom(config('params.yun_ying_mail.mail_from_address'));

            $sendMailService->setSubject($data['base_info']['subject'])->setContent($data['base_info']['content']);

            //收件人
            $addressee = array_map(function ($email) {
                return compact('email');
            }, explode(';', $data['base_info']['addressee']));
            $sendMailService->setAddressee($addressee);

            //抄送人
            $cc = $data['base_info']['cc'];
            if (!empty($cc)) {
                $cc = array_map(function ($email) {
                    return compact('email');
                }, explode(';', $data['base_info']['cc']));
                $sendMailService->setCC($cc);
            }

            //设置附件
            $filename = $this->createExcelForStatementMail($data);
			//附件文件名
			$name = $this->getAttachmentFileName($data);

            $sendMailService->setAttachment([['file_path' => $filename, 'name' => $name]]);

            //设置发件人
            $sendMailService->setFromName("金融数据客户服务");

            //发送
            $res = $sendMailService->send();
            if ($res === true) {
                //记录发送日志
                $this->writeSendMailLog(array_get($data, 'customer_id'), $sendMailService->getUuid(),array_get($data, 'source'));
                return true;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }

    protected function getLatelySendTimeList($customer_ids, $source)
    {
        $current = time();
        $result_list = LogsMailCustomerIncome::select(DB::raw('MAX(send_time) as time, customer_id'))
            ->where('source', $source)
            ->whereIn('customer_id', $customer_ids)
            ->groupBy('customer_id')
            ->get()
            ->map(function ($item) use ($current) {
                $time = $item->time;
                $interval    = $this->intervalToString(abs($current - $time));
                $customer_id = $item->customer_id;
                return compact('time', 'interval', 'customer_id');
            })
            ->toArray();

        $list = [];
        foreach ($result_list as $item) {
            $is_remind = false;
            $send_month = date('Ym', $item['time']);
            if ($send_month == date('Ym')) {
                $is_remind = true;
            }
            $list[$item['customer_id']] = ['is_remind' => $is_remind, 'interval' => $item['interval']];
        }
        return $list;
    }

    /**
     * 客户对账单列表导出
     */
    public function downloadCustomerStatementList()
    {
        //校验参数
        $params = $this->checkCustomerStatementListParams();
        if (!is_array($params)) {
            return false;
        }

        $apikeys = [];
        if (!empty($params['customer_ids'])) {
            $apikeys = (new Account())->select('apikey')->whereIn('customer_id', $params['customer_ids'])->where([['apikey', '!=', '']])->get()->toArray();
            $apikeys = array_column($apikeys, 'apikey');
            if (!$apikeys) {
                return false;
            }
        }

        $incomeRepository = new ProductIncomeRepository();

        //获取用户当前时间段消耗
        $incomeParams = [
            'start_date' => $params['start_date'],
            'end_date' => $params['end_date'],
            'source' => $params['source'],
            'customer_id' => isset($params['customer_ids'][0])?$params['customer_ids'][0]:[],
        ];

        if ($apikeys) {
            $incomeParams['apikey_list'] = $apikeys;
        }
        $incomeDatasMonth = $incomeRepository->getProductIncomeTotalListV2($incomeParams);

        //获取用户总消耗
        $incomeParams['start_date'] = '********';
        $incomeDatasTotal = $incomeRepository->getProductIncomeTotalListV2($incomeParams);

        //获取用户总充值
        $rechargeServer = new RechargeService($params['end_date'], '********', $params['source']);
        $rechargeParams = [];
        if ($params['customer_ids']) {
            $rechargeParams = $params['customer_ids'];
        }
        $rechargeDatas = $rechargeServer->getCustomerRecharge($rechargeParams);

        //获取拆单到企服产品的充值金额
        $fields = 'r.customer_id as cid,sum(rsp.money) as money';
        $rspWhere = [
            ['r.source', $params['source']],
            ['r.status', 3],
            ['r.remit_date', '<=', strtotime($params['end_date']) + 86400],
        ];
        if ($params['customer_ids']) {
            $rspWhere[] = ['r.customer_id', $params['customer_ids'][0]];
        }

        $remitSplitPriceDatas = DB::table('remit_split_price as rsp')
                ->leftJoin('remit as r', 'r.receipt_serial', '=', 'rsp.receipt_serial')
                ->selectRaw($fields)
                ->where($rspWhere)
                ->whereIn('rsp.product_id', [3100, 664])
                ->groupBy('r.customer_id')
                ->get();
        $remitSplitPriceDatas = count($remitSplitPriceDatas) ? $remitSplitPriceDatas->toArray() : [];
        $remitSplitPriceDatas = array_column($remitSplitPriceDatas, null, 'cid');

        //获取customerNameMap
        $customerIds = array_unique(array_merge(array_column($incomeDatasTotal, 'customer_id'), array_column($remitSplitPriceDatas, 'cid'),array_column($rechargeDatas, 'customer_id')));
        // 获取客户列表
        $customerList = Customer::select(['customer_id', 'name', 'email_type', 'status', 'operator', 'salesman','company', 'customer_type'])->whereIn('customer_id', $customerIds)->get()->toArray();
        $customerList = array_column($customerList, null, 'customer_id');

        //product_father_map
        $productIds = array_column($incomeDatasTotal, 'product_id');
        $productIdMap = [];
        foreach ($productIds as $item) {
            $tmp = RedisCache::instance('productId_fatherId_mapping')->get($item);
            $productIdMap[$item] = $tmp ?: $item;
        }

        //计算用户余额
        $tmpExcelDatas = [];
        foreach ($incomeDatasTotal as $item) {
            if (!isset($tmpExcelDatas[$item['customer_id']])) {
                $tmpExcelDatas[$item['customer_id']]['dianhua'] = [
                    'monthConsume' => 0,
                    'consume' => 0,
                    'remit' => 0,
                ];
                $tmpExcelDatas[$item['customer_id']]['qifu'] = [
                    'monthConsume' => 0,
                    'consume' => 0,
                    'remit' => 0,
                ];
            }
            $fatherId = $productIdMap[$item['product_id']];
            if ($fatherId == 3100) {
                $tmpExcelDatas[$item['customer_id']]['qifu']['consume'] += $item['money'];
            } else {
                $tmpExcelDatas[$item['customer_id']]['dianhua']['consume'] += $item['money'];
            }
        }

        foreach ($rechargeDatas as $item) {
            if (!isset($tmpExcelDatas[$item['customer_id']])) {
                $tmpExcelDatas[$item['customer_id']]['dianhua'] = [
                    'monthConsume' => 0,
                    'consume' => 0,
                    'remit' => 0,
                ];
                $tmpExcelDatas[$item['customer_id']]['qifu'] = [
                    'monthConsume' => 0,
                    'consume' => 0,
                    'remit' => 0,
                ];
            }

            //企服用户所有充值为企服充值
            $cusInfo = $customerList[$item['customer_id']];
            $dianhuaMoney = $item['money'];
            $qifuMoney = 0;
            $flage = false;
            if ($cusInfo['customer_type'] == Customer::$customerType['企服用户']) {
                $qifuMoney = $item['money'];
                $dianhuaMoney = 0;
                $flage = true;
            }

            if (!$flage && !empty($remitSplitPriceDatas[$item['customer_id']]['money'])) {
                $dianhuaMoney -= $remitSplitPriceDatas[$item['customer_id']]['money'];
                $qifuMoney = $remitSplitPriceDatas[$item['customer_id']]['money'];
            }

            $tmpExcelDatas[$item['customer_id']]['qifu']['remit'] += $qifuMoney;
            $tmpExcelDatas[$item['customer_id']]['dianhua']['remit'] += $dianhuaMoney;
        }

        foreach ($incomeDatasMonth as $item) {
            $fatherId = $productIdMap[$item['product_id']];
            if ($fatherId == 3100) {
                $tmpExcelDatas[$item['customer_id']]['qifu']['monthConsume'] += $item['money'];
            } else {
                $tmpExcelDatas[$item['customer_id']]['dianhua']['monthConsume'] += $item['money'];
            }
        }
        $excelDatas = [];
        foreach ($tmpExcelDatas as $cusId => $arr) {
            $tmp = [
                'customerId' => $cusId,
                'customerName' => $customerList[$cusId]['name'],
                'companyName' => $customerList[$cusId]['company'],
                'email_type' => $customerList[$cusId]['email_type'],
                'status' => $customerList[$cusId]['status'],
                'salesman' => $customerList[$cusId]['salesman'],
            ];
            foreach ($arr as $k => $v) {
                if (!$v['consume'] && !$v['remit'] && !$v['monthConsume']) {
                    continue;
                }
                //todo 临时特殊处理 202008 记录异常，bill_customer_income 与bill_product_income 总数量异常（待查找）修正后，删除该处代码
                if ($cusId == 'C202002242GM436') {
                    $v['consume'] = $v['consume'] + 1768.8;
                }
                $tmp['balance'] = round($v['remit'] - $v['consume'], 2);
                $tmp['consume'] = round($v['monthConsume'], 2);
                $tmp['type'] = $k;
                $excelDatas[] = $tmp;
            }
        }
        $excelDatas = $this->filterCustomerStatementList($excelDatas, $params);
        $tmpSort = array_column($excelDatas, 'consume');
        array_multisort($tmpSort, SORT_DESC, $excelDatas);
        $excelDatas = array_values($excelDatas);
        return $excelDatas;
    }

	/**
	 * 返回邮件内容的头和尾
	 *
	 * @return array
     */
	public function getBillMailHeaderAndFooter(){

		$sign_corp_name = '北京羽乐创新科技有限公司';
		$brief_name  = Common::COMPANY_CN_NAME;
		$result = [];
		//本公司名称、简称
		$result['header'] = [
			$brief_name,
			$sign_corp_name,
			'对账单',
		];
		$result['footer'] = [
            $sign_corp_name,
            '运营组',
            date('Y年m月d日'),
        ];
		return $result;
	}

	/**
	 * 获取附件名称
	 *
	 * @param [type] $data
	 * @return void
	 */
	public function getAttachmentFileName($data){
        $brief_name  = Common::COMPANY_CN_NAME;

		return  RedisCache::instance('customerId_customerName_mapping')
							  ->get($data['customer_id']) . "-{$brief_name}-" . date('Y年m月', strtotime('first day of last month')) . '账单';

	}


	/**
	 * 返回邮件主题
	 *
	 * @param [type] $customer_info
	 * @param [type] $month
	 * @return void
	 */
	public function getMailSubjectByCustomer($customer_info,$month){
		$brief_name  = Common::COMPANY_CN_NAME . "（电话邦）";
		return $customer_info['company'] . date('Y年m月', strtotime($month . '01')) . "对账单--{$brief_name}";
		
	}

    /**
     * 获取开始月份
     *
     * @param $first_start_month string
     * @param $current_month string
     * @return string
     */
    public function findStartMonth($first_start_month,$current_month){
        //根据当前月份判断
        //如果统计的当前月份比首次开始月份日期都早 都按老项目处理;
        if($first_start_month > $current_month){
            return $first_start_month;
        }
        //获取一年前的月份
        $before_month = date("Ym",strtotime('-11 month',strtotime($current_month.'01')));
        if($first_start_month > $before_month){
            return $first_start_month;
        }
        return $before_month;
    }

    //运营商月账单获取核对
    public function checkBillOperator(){
        $params = $this->request->post();
        //检验部分参数
        if(!isset($params['channel_id']) || empty($params['channel_id'])){
            return ['msg' => '渠道信息异常', 'code' => 5001, 'data' => []];
        }

        if(!isset($params['date']) || empty($params['date'])){
            return ['msg' => '账单月份信息异常', 'code' => 5002, 'data' => []];
        }

        if(!isset($params['price_details']) || empty($params['price_details']) || !is_array($params['price_details'])){
            return ['msg' => '账单详情信息异常', 'code' => 5003, 'data' => []];
        }

        $channel_map = ChannelMapping::getOneItemByCondition(['operator_channel_id' => $params['channel_id']]);
        $channel_id = $channel_map['channel_id'] ?? 0;
        $month = $params['date'];
        //记录推送日志
        BillOperatorMonthLogs::addData(['channel_id' => $channel_id, 'month' => $month, 'result' => json_encode($params), 'create_time' => time()]);

        foreach ($params['price_details'] as $item){
            $list = ChannelInterfaceMapping::getListByCondition(['channel_id' => $channel_id, 'operator_pid' => $item['product_id']]);

            $iids = array_column($list, 'interface_id');
            $where = [
                ['month', '=', $month],
                ['channel_id', '=', $channel_id],
                ['status', '!=', 1],
            ];
            $up_product_data = [
                'operator_pid' => $item['product_id'],
                'operator_product_price' => $item['price_lowest_price'],
                'operator_product_cost' => $item['lowest_price'],
                'operator_product_count' => $item['call_statistics'],
            ];
            BillOperatorMonthCheck::updateDataByIids($where, $iids, $up_product_data);

            $up_channel_data = [
                'bill_method' => $params['billing_method'] ?? '',
                'operator_minimum_fee' => $params['minimum_price'] ?? 0,
                'operator_cost' => $params['product_cost_price'] ?? 0,
                'operator_total' => $params['call_volume'] ?? 0,
                'operator_success' => $params['call_volume'] ?? 0,
                'operator_valid' => $params['call_volume'] ?? 0,
                'status' => 2
            ];
            BillOperatorMonthCheck::updateData($where, $up_channel_data);
        }

        return ['msg' => '获取数据成功', 'code' => 0 , 'data' => []];
    }

    //运营商月账单对账列表
    public function billOperatorList(){
        $limit = request()->post('limit', 10);
        $page = request()->post('page', 1);
        $month = request()->post('month', '');
        $channel_id = request()->post('channel_id', '');
        $status = request()->post('status', '');
        $offset = ($page - 1) * $limit;
        $where = [];
        if($month){
            $where['month'] = $month;
        }
        if($channel_id){
            $where['channel_id'] = $channel_id;
        }
        if($status !== ''){
            $where['status'] = $status;
        }

        //一次10条 分页查询
        $list = BillOperatorMonthCheck::getListByChannelId($where, $offset, $limit);
        $result = [];
        foreach ($list['data'] as $item){
            $arr = $this->combineBillOperatorData($item['month'], $item['channel_id']);
            $result = array_merge($result, $arr);
        }

        return ['data' => $result, 'count' => $list['count']];
    }

    public function combineBillOperatorData($month, $channel_id){
        $list = BillOperatorMonthCheck::getListItemByCondition(['month' => $month, 'channel_id' => $channel_id]);
        $count = count($list);
        if($count == 0){
            return [];
        }

        $bill_method_map = ['total' => '总量', 'success' => '成功量', 'valid' => '查得量', 'unknow' => '未知'];
        $interface_product_map = ChannelInterfaceMapping::getPnameMap([]);
        $interface_map = ChannelInterface::getListByCondition(['channel_id' => $list[0]['channel_id']], ['id', 'label'])->toArray();
        $interface_map = array_column($interface_map, 'label', 'id');

        $result = [];
        $total = [];//合计信息
        $product = [];//产品信息

        foreach ($list as $item){
            $operator = RedisCache::instance('channelId_label_mapping')->get($item['channel_id']);
            $method_key = $item['bill_method'];
            if(!isset($bill_method_map[$method_key])){
                $method_key = 'unknow';
            }
            $base = [
                'month' => $item['month'],
                'channel_id' => $item['channel_id'],
                'operator' => $operator,
                'bill_method_key' => $method_key,
                'bill_method' => $bill_method_map[$method_key] ?? '异常'.$method_key,
                'operator_minimum_fee' => $item['operator_minimum_fee'],
                'operator_cost' => $item['operator_cost'],
                'month_merge_count' => 0,
                'operator_merge_count' => 0,
                'bill_method_merge_count' => 0,
                'operator_minimum_fee_merge_count' => 0,
                'operator_cost_merge_count' => 0,
                'operator_pname_merge_count' => 0,
                'operator_product_count_merge_count' => 0,
                'operator_product_cost_merge_count' => 0,
                'diff_number_merge_count' => 0,
                'diff_money_merge_count' => 0,
                'operate_merge_count' => 0,
            ];

            $pkey = $item['interface_id'].'_'.$item['operator_pid'];
            $operator_pname = $interface_product_map[$pkey] ?? '未知';
            $bill_number = $item[$method_key] ?? 0;//计费数量
            if(!isset($product[$operator_pname]['operator_product_count'])){
                $product[$operator_pname]['operator_product_count'] = $item['operator_product_count'];
                $product[$operator_pname]['operator_product_cost'] = $item['operator_product_cost'];
                $product[$operator_pname]['operator_product_count_num'] = 1;
                $product[$operator_pname][$method_key] = $bill_number;
                $product[$operator_pname]['cost'] = bcmul($bill_number, $item['operator_product_price'], 2);

            }else{
                $product[$operator_pname]['operator_product_count'] += $item['operator_product_count'];
                $product[$operator_pname]['operator_product_cost'] = bcadd($product[$operator_pname]['operator_product_cost'], $item['operator_product_cost'], 2);
                $product[$operator_pname]['operator_product_count_num'] += 1;
                $product[$operator_pname][$method_key] += $bill_number;
                $product[$operator_pname]['cost'] = bcadd($product[$operator_pname]['cost'], bcmul($bill_number, $item['operator_product_price'], 2),2);
            }

            $cost = bcmul($bill_number, $item['operator_product_price'], 2);
            if(!isset($total['interface_label'])){
                $total = $base;
                $total['interface_label'] = '合计';
                $total['operator_pname'] = '合计';
                $total['total'] = $item['total'];
                $total['success'] = $item['success'];
                $total['valid'] = $item['valid'];
                $total['cost'] = $cost;
            }else{
                $total['total'] += $item['total'];
                $total['success'] += $item['success'];
                $total['valid'] += $item['valid'];
                $total['cost'] = bcadd($total['cost'], $cost, 2);
            }

            $row = $base;
            //$pkey = $item['interface_id'].'_'.$item['operator_pid'];
            $row['interface_label'] = $interface_map[$item['interface_id']] ?? '未知';
            $row['operator_pname'] = $operator_pname;
            $row['operator_product_count'] = $item['operator_product_count'];
            $row['operator_product_cost'] = $item['operator_product_cost'];
            $row['total'] = $item['total'];
            $row['success'] = $item['success'];
            $row['valid'] = $item['valid'];
            $row['cost'] = $cost;
            $row['diff_number'] = '--';
            $row['diff_money'] = '--';

            $result[] = $row;
        }

        //按产品排序(运营商侧接口)
        array_multisort(array_column($result,'operator_pname'), $result);

        //dd($product);
        //补充合计信息
        foreach ($product as $pname => &$pitem){
            if(!isset($total['operator_product_count'])){
                $total['operator_product_count'] = bcdiv($pitem['operator_product_count'], $pitem['operator_product_count_num']);
            }else{
                $total['operator_product_count'] += bcdiv($pitem['operator_product_count'], $pitem['operator_product_count_num']);
            }

            if(!isset($total['operator_product_cost'])){
                $total['operator_product_cost'] = bcdiv($pitem['operator_product_cost'], $pitem['operator_product_count_num'],2);
            }else{
                $total['operator_product_cost'] = bcadd($total['operator_product_cost'], bcdiv($pitem['operator_product_cost'], $pitem['operator_product_count_num'],2), 2);
            }

            $pitem['operator_product_total_count'] = bcdiv($pitem['operator_product_count'], $pitem['operator_product_count_num']);
            $pitem['operator_product_total_cost'] = bcdiv($pitem['operator_product_cost'], $pitem['operator_product_count_num'], 2);
        }

        $method = $total['bill_method_key'];
        $total_bill_number = $total[$method] ?? 0;//金融侧总的计费用量(计费方式因为数据异常有可能不存在)
        $total['diff_number'] = $total['operator_product_count'] - $total_bill_number;
        $total['diff_money'] = $total['operator_product_cost'] - $total['cost'];

        array_unshift($result, $total);

        $month_count = count($result);
        $operator_pname_count = [];
        foreach ($result as $item){
            $pname = $item['operator_pname'];
            if(!isset($operator_pname_count[$pname])){
                $operator_pname_count[$pname]['count'] = 1;
                $operator_pname_count[$pname]['is_set'] = false;//默认未设置
            }else{
                $operator_pname_count[$pname]['count'] += 1;
            }
        }

        //单元格合并计算处理
        $month_merge_count_is_set = false;//未设置
        foreach ($result as &$item){
            if($item['month_merge_count'] == 0 && !$month_merge_count_is_set){
                $item['month_merge_count'] = $month_count;
                $item['operator_merge_count'] = $month_count;
                $item['bill_method_merge_count'] = $month_count;
                $item['operator_minimum_fee_merge_count'] = $month_count;
                $item['operator_cost_merge_count'] = $month_count;
                $item['operate_merge_count'] = $month_count;
                $month_merge_count_is_set = true;
            }

            if($item['operator_pname_merge_count'] == 0 && !$operator_pname_count[$item['operator_pname']]['is_set']){
                $item['operator_pname_merge_count'] = $operator_pname_count[$item['operator_pname']]['count'];
                $item['operator_product_count_merge_count'] = $operator_pname_count[$item['operator_pname']]['count'];
                $item['operator_product_cost_merge_count'] = $operator_pname_count[$item['operator_pname']]['count'];
                $item['diff_number_merge_count'] = $operator_pname_count[$item['operator_pname']]['count'];
                $item['diff_money_merge_count'] = $operator_pname_count[$item['operator_pname']]['count'];
                $operator_pname_count[$item['operator_pname']]['is_set'] = true;
            }

            if(isset($product[$item['operator_pname']])){
                $item['diff_number'] = $product[$item['operator_pname']]['operator_product_total_count'] - $product[$item['operator_pname']][$method];
                $item['diff_money'] = bcsub($product[$item['operator_pname']]['operator_product_total_cost'] , $product[$item['operator_pname']]['cost'], 2);
            }

        }

        unset($list);
        return $result;
    }

    //运营商月账单确认平账接口
    public function confirmOperatorBill(){
        $params = $this->request->post();
        //检验部分参数

        if(!isset($params['channel_id']) || empty($params['channel_id'])){
            return ['msg' => '渠道信息异常', 'code' => 5001, 'data' => []];
        }
        if(!isset($params['month']) || empty($params['month'])){
            return ['msg' => '账单月份信息异常', 'code' => 5002, 'data' => []];
        }

        $channel_map = ChannelMapping::getOneItemByCondition(['channel_id' => $params['channel_id']]);
        if(empty($channel_map)){
            return ['msg' => '无对应运营商侧渠道信息', 'code' => 5003, 'data' => []];
        }

        $bill_where = [
            ['channel_id', '=', $params['channel_id']],
            ['month', '=', $params['month']],
            ['status', '!=', 1],
        ];
        $bill_info = BillOperatorMonthCheck::getOneItemByCondition($bill_where);
        if(empty($bill_info)){
            return ['msg' => '对应渠道对应月份的运营商账单信息不存在', 'code' => 5004, 'data' => []];
        }

        $config = config('params.request_url');
        $url = $config['OPERATOR_BILL_CHECK_API'];
        $post = ['channel_id' => $channel_map['operator_channel_id'], 'date' => $params['month'], 'bill_revised' => true];
        $res = $this->postRawJson($url, $post);
        if(!isset($res['data']) || $res['data'] !== 'ok'){
            $msg = $res['error'] ?? '平账接口返回异常:'.json_encode($res);
            return ['msg' => $msg, 'code' => 5005, 'data' => []];
        }

        $res = BillOperatorMonthCheck::updateData($bill_where, ['status' => 1]);
        if($res){
            return ['msg' => '平账成功', 'code' => 0, 'data' => []];
        }
        return ['msg' => '平账失败', 'code' => 5006, 'data' => []];
    }


}
