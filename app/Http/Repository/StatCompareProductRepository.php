<?php

namespace App\Http\Repository;

use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\CustomerExpend;
use App\Models\StatisticsCustomerUsage;
use App\Models\Product;
use App\Providers\RedisCache\RedisCache;

class StatCompareProductRepository extends StatBaseRepository
{
    /**
     * 数据对比-客户维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $usage_data = $this->getUsage($params); //调用量
        $income_data = $this->getIncome($params);   //收入
        $cost_data = $this->getCost($params);   //成本
        $expend_data = $this->getDateExpend($params); //特殊消耗


        $data = $this->formatData($usage_data, $income_data, $cost_data, $expend_data);
        $father_id = Product::getDisplayFatherId($params['product_id']);
        $data = $this->filterAuthMoneyByFatherid($data,$father_id);
        return $data;
    }

    protected function getUsage($params){
        $where = $this->getWhere($params);
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        $usage_data = StatisticsCustomerUsage::getCompareList($where, null, null, $params['filter_apikey'], $params['filter_product']);
        return $usage_data;
    }

    protected function getIncome($params)
    {
        $where = $this->getWhere($params);
        $income_data = BillProductIncomeV2::getCompareList($where, null, null, $params['filter_apikey']);
        return $income_data;
    }

    protected function getCost($params)
    {
        $where = $this->getWhere($params);
        $cost_data = BillCostV2::getCompareList($where, null, null, $params['filter_apikey']);
        return $cost_data;
    }

    /**
     * 对比页面特殊消耗
     * @param $params
     * @return mixed
     */
    protected function getDateExpend($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getProductDateExpend(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer']);
        $expend_data['add'] = CustomerExpend::getProductDateExpend(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer']);
        return $expend_data;
    }

    protected function getWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        isset($params['product_id']) && $where['product_id'] = $params['product_id'];
        return $where;
    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @return array
     */
    private function formatData($usage_data, $income_data, $cost_data, $expend_data)
    {
        $data = [];
        foreach ($usage_data as $item){
            $data[$item['date']] = $item;
        }
        foreach ($income_data as $item){
            $data[$item['date']]['number'] = $item['number'];
            $data[$item['date']]['money'] = $item['money'];
        }
        foreach ($cost_data as $item){
            $data[$item['date']]['cost'] = $item['money'];
        }
        foreach ($expend_data['sub'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date]['number'])){
                $data[$date]['number'] = $data[$date]['money'] = 0;
                $data[$date]['date'] = $date;
            }
            $data[$date]['number'] -= $item['fee_number'];
            $data[$date]['money'] -= $item['money'];
        }
        foreach ($expend_data['add'] as $item){
            $date = date('Ymd', strtotime($item['profile_show_date']));
            if (!isset($data[$date]['number'])){
                $data[$date]['number'] = $data[$date]['money'] = 0;
                $data[$date]['date'] = $date;
            }
            $data[$date]['number'] += $item['fee_number'];
            $data[$date]['money'] += $item['money'];
        }
        foreach ($data as $date => $value) {
            !isset($data[$date]['cost'])&&$data[$date]['cost']=0;
            !isset($data[$date]['money'])&&$data[$date]['money']=0;
        }
        return $data;
    }



    /**
     * 产品统计趋势图
     * @return mixed
     * @throws \Exception
     */
    public function statChartList()
    {
        try {
            $params = $this->getStatParams();
            //getStatParams方法中的参数大都是共用的 故统一在在该方法中处理参数,为了保证该方法比较干净简洁,
            //故一些新参数可以在下面在单独添加进$params, 当然不是必须的
            $params['day_type'] = request()->post('day_type', 'day');
            if($params['day_type'] == 'month'){
                $params['start_date'] = $params['start_date'];
                $params['end_date'] = $this->getLastDay($params['end_date']);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        if(in_array($params['chart_type'], ['total', 'success', 'valid'])){
            $usage_data = $this->getUsageByProduct($params); //调用量
            $data = $this->formatDataUsage($usage_data, $params);
        }

        if(in_array($params['chart_type'], ['money'])){
            $income_data = $this->getIncomeByProduct($params);   //收入
            $expend_data = $this->getDateExpendByProduct($params); //特殊消耗
            $data = $this->formatDataIncome($income_data, $expend_data, $params);
        }

        return $data;
    }


    protected function getUsageByProduct($params){
        $where = $this->getWhere($params);
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        $usage_data = StatisticsCustomerUsage::getCompareListByProduct($where, null, null, $params['filter_apikey'], $params['filter_product']);
        return $usage_data;
    }

    private function formatDataUsage($usage_data, $params){
        $data = [];
        $result = [];
        $chart_type = $params['chart_type'];
        foreach($usage_data as $item){
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            //由于array_multisort进行二维数组排序时，如果关联（string）键名保持不变，但数字键名会被重新索引
            //且如果键值为数字,返回的json数据客户端会修改顺序,为了方便因此组装成string
            $father_id = 'p'.$father_id;

            $data[$father_id][$chart_type] = $data[$father_id][$chart_type] ?? 0;
            $data[$father_id][$chart_type] += $item[$chart_type];

            $result[$father_id][$item['date']][$chart_type] = $result[$father_id][$item['date']][$chart_type] ?? 0;
            $result[$father_id][$item['date']][$chart_type] += $item[$chart_type];
        }

        $chart_column = array_column($data, $chart_type);
        array_multisort($chart_column, SORT_DESC, $data);//根据查看的指标类型排序
        //$topn = array_slice($data, 0, 5);//获取排名前n名产品
        $topn_product_id = array_keys($data);
        $dateRange = $this->getDateFromRange($params['start_date'], $params['end_date']);//获取区间时间
        if($params['day_type'] == 'day'){
            $res = $this->getDayData($topn_product_id, $dateRange, $result, $chart_type);
        }

        if($params['day_type'] == 'month'){
            $res = $this->getMonthData($topn_product_id, $dateRange, $result, $chart_type);
        }

        if($params['day_type'] == 'week'){
            $res = $this->getWeekData($topn_product_id, $dateRange, $result, $chart_type);
        }

        return $res;

    }

    public function getDayData($topn_product_id, $dateRange, $result, $chart_type = ''){
        $arr = [];
        foreach($topn_product_id as $father_id){
            //$father_id = substr($father_id, 1);
            foreach($dateRange as $date){
                if($chart_type == 'money'){
                    $arr[$father_id][$date]['money'] = $result[$father_id][$date]['money'] ?? 0;
                }else{
                    $arr[$father_id][$date][$chart_type] = $result[$father_id][$date][$chart_type] ?? 0;
                }
            }
        }

        return ['x_val' => $dateRange, 'y_val' => $arr];
    }

    public function getWeekData($topn_product_id, $dateRange, $result, $chart_type = ''){
        $arr = [];
        $week_arr = [];
        foreach($dateRange as $date){
            $week = $this->getDateWeek($date);//根据日期获取所在年份的第几周
            $week_arr[$week][] = $date;
        }

        foreach($topn_product_id as $father_id){
            foreach($week_arr as $week => $date_arr){
                foreach($date_arr as $date){
                    if($chart_type == 'money'){
                        $arr[$father_id][$week]['money'] = $arr[$father_id][$week]['money'] ?? 0;
                        $total = $result[$father_id][$date]['money'] ?? 0;
                        $arr[$father_id][$week]['money'] += $total;
                    }else{
                        $arr[$father_id][$week][$chart_type] = $arr[$father_id][$week][$chart_type] ?? 0;
                        $total = $result[$father_id][$date][$chart_type] ?? 0;
                        $arr[$father_id][$week][$chart_type] += $total;

                    }

                }

            }
        }

        $x_val = [];
        foreach($week_arr as $week=>$date_arr){
            $week_num = 'W'.substr($week, -2);//取出周数
            $start_date = date('Y.m.d', strtotime(reset($date_arr)));
            $end_date = date('Y.m.d', strtotime(end($date_arr)));
            $date_str = $start_date."\n".$end_date."\n".'('.$week_num.')';
            $x_val[] = $date_str;
        }

        return ['x_val' => $x_val, 'y_val' => $arr];
    }

    public function getMonthData($topn_product_id, $dateRange, $result, $chart_type = ''){

        $arr = [];
        $month_arr = [];
        foreach($dateRange as $date){
            $month = substr($date, 0, 6);
            $month_arr[$month][] = $date;
        }

        foreach($topn_product_id as $father_id){
            foreach($month_arr as $month => $date_arr){
                foreach($date_arr as $date){
                    if($chart_type == 'money'){
                        $arr[$father_id][$month]['money'] = $arr[$father_id][$month]['money'] ?? 0;
                        $total = $result[$father_id][$date]['money'] ?? 0;
                        $arr[$father_id][$month]['money'] += $total;
                    }else{
                        $arr[$father_id][$month][$chart_type] = $arr[$father_id][$month][$chart_type] ?? 0;
                        $total = $result[$father_id][$date][$chart_type] ?? 0;
                        $arr[$father_id][$month][$chart_type] += $total;

                    }

                }
            }

        }

        $x_val = array_keys($month_arr);

        return ['x_val' => $x_val, 'y_val' => $arr];

    }


    protected function getIncomeByProduct($params)
    {
        $where = $this->getWhere($params);
        $income_data = BillProductIncomeV2::getCompareListByProduct($where, null, null, $params['filter_apikey']);
        return $income_data;
    }

    protected function getDateExpendByProduct($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getDateExpendByProduct(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer']);
        $expend_data['add'] = CustomerExpend::getDateExpendByProduct(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer']);
        return $expend_data;
    }


    protected function formatDataIncome($income_data, $expend_data, $params){
        $data = [];
        $result = [];
        foreach($income_data as $item){
            //由于array_multisort进行二维数组排序时，如果关联（string）键名保持不变，但数字键名会被重新索引
            //且如果键值为数字,返回的json数据客户端会修改顺序,为了方便因此组装成string
            $father_id = 'p'.$item['father_id'];
            $data[$father_id]['money'] = $data[$father_id]['money'] ?? 0;
            $data[$father_id]['money'] = bcadd($data[$father_id]['money'], $item['money'], $this->degree);

            $result[$father_id][$item['date']]['money'] = $result[$father_id][$item['date']]['money'] ?? 0;
            $result[$father_id][$item['date']]['money'] = bcadd($result[$father_id][$item['date']]['money'], $item['money'], $this->degree);
        }

        foreach($expend_data['add'] as $item){
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $father_id = 'p'.$father_id;

            $data[$father_id]['money'] = $data[$father_id]['money'] ?? 0;
            $data[$father_id]['money'] = bcadd($data[$father_id]['money'], $item['money'], $this->degree);

            $result[$father_id][$item['date']]['money'] = $result[$father_id][$item['date']]['money'] ?? 0;
            $result[$father_id][$item['date']]['money'] = bcadd($result[$father_id][$item['date']]['money'], $item['money'], $this->degree);
        }

        foreach($expend_data['sub'] as $item){
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $father_id = 'p'.$father_id;

            $data[$father_id]['money'] = $data[$father_id]['money'] ?? 0;
            $data[$father_id]['money'] = bcsub($data[$father_id]['money'], $item['money'], $this->degree);

            $result[$father_id][$item['date']]['money'] = $result[$father_id][$item['date']]['money'] ?? 0;
            $result[$father_id][$item['date']]['money'] = bcsub($result[$father_id][$item['date']]['money'], $item['money'], $this->degree);
        }

        $chart_column = array_column($data, 'money');
        array_multisort($chart_column, SORT_DESC, $data);//根据查看的指标类型排序
        //$topn = array_slice($data, 0, 5);//获取排名前n名客户
        $topn_product_id = array_keys($data);

        $dateRange = $this->getDateFromRange($params['start_date'], $params['end_date']);//获取区间时间
        if($params['day_type'] == 'day'){
            $res = $this->getDayData($topn_product_id, $dateRange, $result, $params['chart_type']);
        }

        if($params['day_type'] == 'month'){
            $res = $this->getMonthData($topn_product_id, $dateRange, $result, $params['chart_type']);
        }

        if($params['day_type'] == 'week'){
            $res = $this->getWeekData($topn_product_id, $dateRange, $result, $params['chart_type']);
        }

        return $res;
    }




}
