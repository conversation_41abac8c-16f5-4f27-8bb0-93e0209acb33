<?php

namespace App\Http\Repository;

use App\Models\ChannelInterface;
use App\Models\ClickHouse\RequestChannelLog;
use App\Models\ClickHouse\RequestLog;
use App\Models\ClickHouse\RequestProductLog;
use App\Models\Product;


class CheckDataRepository
{
    public function getCkList()
    {
        $where = $this->getParams();
        $this->checkParams($where);
        if (empty($where['sid']) && empty($where['apikey'])) {
            return ['list' => [], 'count' => 0];
        }
        $page = intval(request()->post('page', 1));
        $limit = intval(request()->post('limit', 10));
        $model = new RequestProductLog();
        $productMap = Product::pluck('product_name', 'product_id');
        $list = $model->getUsageList($where, 'data', (($page - 1) * $limit), $limit);
        if(empty($list)){
            $model = new RequestLog();
            $list = $model->getUsageList($where, 'data', (($page - 1) * $limit), $limit);
        }
        try {
            foreach ($list as &$value) {
                // 父产品名
                $value['pid_name'] = isset($productMap[$value['pid']]) ? $productMap[$value['pid']] : $value['pid'];
                $value['pids_name'] = $this->convertPids($value['pids'], $productMap);
                $value['data_str'] = $this->convertOutput($value['data'], $value['pid']);
                $value['input_str'] = $this->convertOutput($value['input'], $value['pid']);
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $count = (int) $model->getUsageList($where, 'page');
        return compact('list', 'count');
    }

    public function getLastestCall()
    {
        $apikey = request()->post('apikey', '');
        $pid = request()->post('pid', '');

        if (empty($apikey)) {
            throw new \Exception('请选择账号');
        } else {
            $pmodel = new RequestProductLog();
        
            return  $pmodel->getLastestData($apikey,strtotime('-1 year'), $pid);
        }
    }

    public function getChannelList()
    {
        $where = $this->getParams();
        $channelModel = new RequestChannelLog();
        $list = $channelModel->getListByCondition($where);
        $interfaceMap = ChannelInterface::pluck('label', 'name')->toArray();

        if ($list) {
            foreach ($list as &$value) {
                $intername = str_replace(['sha256', 'md5','MD5','SHA256', 'CMCC', 'CUCC', 'CTCC'], '', $value['interface_name']);
                // 银行卡四要素不一致
                if($intername=='CheckFourThree'){
                    $intername='CheckFour';
                }
                $value['interface_label'] = $interfaceMap[$intername] ?? $intername;
                $value['input_str'] = $this->convertOutput($value['input'], 0);
                $value['output_str'] = $this->convertOutput($value['output'], 0);
            }
        }else{
            $list=[];
        }

        return ['list' => $list];
    }

    /**
     * 获取查询条件
     * @return array
     */
    public function getParams()
    {
        $parmas = [];
        $month = request()->post('month', null);
        $sid = request()->post('sid', null);
        $apikey = request()->post('apikey', null);
        $pid = request()->post('pid', null);

        $phone = request()->post('phone', null);

        $name = request()->post('name', null);
        $idnum = request()->post('idnum', null);
        $bankcard = request()->post('bankcard', null);

        $request_at = request()->post('request_at', null);
        if (!empty($month)) {
            $parmas['start_date'] = $month[0];
            $parmas['end_date'] = $month[1];
        } else {
            // 默认今天
            $parmas['start_date'] = date('Y-m-d 00:00:00');
            $parmas['end_date'] = date('Y-m-d 23:59:59');
        }

        if ($request_at) {
            $parmas['start_time'] = $request_at - 10 * 60;
            $parmas['end_time'] = $request_at + 10 * 60;
        }

        if (!empty($sid)) {
            $parmas['sid'] = $sid;
        }


        if (!empty($apikey)) {
            $parmas['apikey'] = $apikey;
        }
        // 子产品id
        if (!empty($pid)) {
            $parmas['pid'] = $pid;
        }
        if (!empty($phone)) {
            $parmas['tel'] = hash('sha256', $phone);
        }

        if (!empty($name)) {
            $parmas['name'][0] = $name;
            $parmas['name'][1] = md5($name);
            $parmas['name'][2] = hash('sha256', $name);
        }
        if (!empty($idnum)) {
            $parmas['idnum'][0] = $idnum;
            $parmas['idnum'][1] = md5($idnum);
            $parmas['idnum'][2] = hash('sha256', $idnum);
        }
        if (!empty($bankcard)) {
            $parmas['bankcard'][0] = $bankcard;
            $parmas['bankcard'][1] = md5($bankcard);
            $parmas['bankcard'][2] = hash('sha256', $bankcard);
        }
        return $parmas;
    }


    public function convertOutput($out, $pid)
    {
        $str = '';
        if (!is_array(json_decode($out, true))) {
            return $out;
        }
        switch ($pid) {
            case '200':
                $arr = json_decode($out, true);
                foreach ($arr as $key => $value) {
                    $this->recure($key, $value, $str);
                }
                break;

            default:
                $arr = json_decode($out, true);
                foreach ($arr as $key => $value) {
                    $this->recure($key, $value, $str);
                }
                break;
        }

        return $str;
    }

    public function convertPids($pids, $map)
    {
        if (strpos($pids, ',') !== false) {
            $arr = explode(',', $pids);
            $str = '';
            foreach ($arr as $key => $value) {
                $str .= isset($map[$value]) ? $map[$value] : $value;
                $str .= ',';
            }
            return trim($str, ',');
        } else {
            return isset($map[$pids]) ? $map[$pids] : $pids;
        }
    }
    // 递归解析数组
    public function recure($key, $value, &$str)
    {
        if (!is_array($value)) {
            $str .= "{$key}:{$value}" . "<br>";
        } else {
            foreach ($value as $keyc => $valuec) {
                $this->recure($keyc, $valuec, $str);
            }
        }
    }

    public function checkParams($where)
    {

        // 
        $limit = 3600 * 24 * 3;
        if ((strtotime($where['end_date']) - strtotime($where['start_date'])) > $limit) {
            throw new \Exception('日期跨度不能超过3天');
        }
    }
}
