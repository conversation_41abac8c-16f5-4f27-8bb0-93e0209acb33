<?php

namespace App\Http\Repository;

use App\Models\SystemUser;
use App\Models\SystemUserToken;
use App\TraitUpgrade\CurlTrait;
use Exception;

/**
 * @package App\Http\Repository
 * @class UserRepository
 */
class UserRepository
{
    use CurlTrait;

    /**
     * @return array
     * @throws Exception
     */
    public function userTicket() {
        $sTicket = trim(request()->post('ticket', ''));

        $aTicketInfo = $this->getTicketInfo($sTicket);
        if (!$aTicketInfo['username']) {
            throw new Exception('ticket验证失败');
        }
        // 用ticket从cas获取登录信息
        $sUserName = $aTicketInfo['username'];
        $sUserMail = $aTicketInfo['email'];
        $iTime = time();
        $iExpire = $iTime + (86400 * 5);
        $sToken = $sSessionId = bin2hex($sUserName . '_' . $iTime);
        $aData = [
            'username' => $sUserName,
            'email' => $sUserMail,
            'token_time' => $iTime,
        ];
        SystemUserToken::saveToken($sSessionId, $iExpire, $aData);
        $aUserInfo = SystemUser::getUserInfoByName($sUserName);

        return [
            'avatar' => 'https://avatars.githubusercontent.com/u/44761321',
            'username' => $sUserName,
            'nickname' => $aUserInfo['realname'],
            'roles' => ["admin"], // todo
            'permissions' => ["*:*:*"],
            'accessToken' => $sToken,
            'tokenTime' => $iTime,
            'refreshToken' => 'eyJhbGciOiJIUzUxMiJ9.adminRefresh',
            'expires' => '2030/10/30 00:00:00',
        ];
    }

    /**
     * @param $sTicket
     * @return array|mixed
     */
    private function getTicketInfo($sTicket = ''){
//        return [
//            'username' => 'kai.lin',
//            'email' => '<EMAIL>',
//        ];

        $aParam = [
            'ticket' => $sTicket,
        ];
        $sUrl = 'https://www.secure.yulore.com:8443/cas/validate.php';
        return $this->get($sUrl, $aParam);
    }
}