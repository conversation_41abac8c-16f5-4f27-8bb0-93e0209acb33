<?php

namespace App\Http\Repository;

use App\Models\{
    MongoLog, MongoStatis,
    MongoStatLog
};
use App\Support\CustomException;
use MongoDB\BSON\UTCDateTime;

class LogRepository
{
    /**
     * 统计字段入库日志
     * @return array
     */
    public function logs(): array
    {
        // 校验参数
        $this->_validateParamsForLogs();

        // 生成列表
        return [
            'total' => $this->_genTotalNumberForLog(),
            'data' => $this->_getSearchForLog(),
            'page' => (int)trim(request()->get('page', 1)),
            'page_size' => (int)trim(request()->get('page_size', 30)),
        ];
    }

    /**
     * @return array
     */
    private function _getSearchForLog(): array
    {
        list($where, $offset, $limit) = $this->_genParamsForLog();

        // 获取列表
        $list = MongoStatLog::where($where)
            ->orderBy('_id', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();
        return $list ? $list->toArray() : [];
    }

    /**
     * 总量
     * @return int
     */
    private function _genTotalNumberForLog(): int
    {
        // 生成参数
        list( $where, $offset, $limit) = $this->_genParamsForLog();
        return MongoStatLog::where($where)
            ->count();
    }

    /**
     * 生成参数
     * @return array
     */
    private function _genParamsForLog(): array
    {
        list($day_begin, $day_end, $page_size, $page,$product_id, $apikey,
            $node_area, $success, $log_id, $province, $operator, $cralwer) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
            trim(request()->get('page_size', 30)),
            trim(request()->get('page', 1)),
            (int)trim(request()->get('product_id')),
            trim(request()->get('apikey')),
            trim(request()->get('node_area')),
            trim(request()->get('success')) == '' ? '' : (trim(request()->get('success')) == 'true' ? true : false),
            trim(request()->get('log_id')),
            trim(request()->get('province')),
            trim(request()->get('operator')),
            trim(request()->get('cralwer')),
        ];

        // 普通过滤
        $where = array_filter(compact('product_id', 'apikey', 'node_area', 'log_id', 'province', 'operator', 'cralwer'), function ($item) {
            return $item;
        });

        // 是否成功
        if ($success !== '') {
            $where['success'] = $success;
        }

        // 日期筛选
        if (!$day_begin && $day_end) {
            $where['amount_date'] = [
                '$lte' => date('Ymd', strtotime($day_end)),
            ];
        } elseif ($day_begin && !$day_end) {
            $where['amount_date'] = [
                '$gte' => date('Ymd', strtotime($day_begin)),
            ];
        } elseif ($day_begin && $day_end) {
            $where['amount_date'] = [
                '$gte' => date('Ymd', strtotime($day_begin)),
                '$lte' => date('Ymd', strtotime($day_end)),
            ];
        }

        return [
            $where,
            $page ? intval(($page - 1) * $page_size) : 0,
            (int)$page_size
        ];
    }

    /**
     * 检查条件
     * @throws CustomException
     */
    private function _validateParamsForLogs()
    {
        $this->_validateDate();
    }


    /**
     * 统计单元列表
     * @return array
     * @throws CustomException
     */
    public function statistic(): array
    {
        // 校验参数
        $this->_validateParamsForStatistic();

        // 生成列表
        return [
            'total' => $this->_genTotalNumberForStat(),
            'data' => $this->_getSearchForStat(),
            'page' => (int)trim(request()->get('page', 1)),
            'page_size' => (int)trim(request()->get('page_size', 30)),
        ];
    }

    /**
     * @return array
     */
    private function _getSearchForStat(): array
    {
        list($where, $offset, $limit) = $this->_genParamsForStat();

        // 获取列表
        $list = MongoStatis::where($where)
            ->orderBy('_id', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();
        return $list ? $list->toArray() : [];
    }

    /**
     * 总量
     * @return int
     */
    private function _genTotalNumberForStat(): int
    {
        // 生成参数
        list( $where, $offset, $limit) = $this->_genParamsForStat();

        return MongoStatis::where($where)
            ->count();
    }


    /**
     * 生成参数
     * @return array
     */
    private function _genParamsForStat(): array
    {
        list($day_begin, $day_end, $page_size, $page,$product_id, $apikey, $node_area) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
            trim(request()->get('page_size', 30)),
            trim(request()->get('page', 1)),
            (int)trim(request()->get('product_id')),
            trim(request()->get('apikey')),
            trim(request()->get('node_area')),
        ];

        // 普通过滤
        $where = array_filter(compact('product_id', 'apikey', 'node_area'), function ($item) {
            return $item;
        });

        // 日期筛选
        if (!$day_begin && $day_end) {
            $where['amount_date'] = [
                '$lte' => date('Ymd', strtotime($day_end)),
            ];
        } elseif ($day_begin && !$day_end) {
            $where['amount_date'] = [
                '$gte' => date('Ymd', strtotime($day_begin)),
            ];
        } elseif ($day_begin && $day_end) {
            $where['amount_date'] = [
                '$gte' => date('Ymd', strtotime($day_begin)),
                '$lte' => date('Ymd', strtotime($day_end)),
            ];
        }

        return [
            $where,
            $page ? intval(($page - 1) * $page_size) : 0,
            (int)$page_size
        ];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamsForStatistic()
    {
        $this->_validateDate();
    }

    /**
     * 日志列表查询
     * @return array
     * @throws CustomException
     */
    public function index(): array
    {
        // 校验参数
        $this->validateParamsForList();

        // 生成列表
        return [
            'total' => $this->genTotalNumber(),
            'data' => $this->getSearchList(),
            'page' => (int)trim(request()->get('page', 1)),
            'page_size' => (int)trim(request()->get('page_size', 30)),
        ];
    }

    /**
     * @return array
     */
    private function getSearchList(): array
    {
        list($day_begin, $day_end, $where, $offset, $limit) = $this->genParamsForList();

        // 获取列表
        $list = MongoLog::when($day_begin, function ($query) use ($day_begin) {
            $middle = new UTCDateTime(strtotime($day_begin) * 1000);
            return $query->where('created_at', '>=', $middle);
        })->when($day_end, function ($query) use ($day_end) {
            $middle = new UTCDateTime(strtotime($day_end) * 1000);
            return $query->where('updated_at', '<=', $middle);
        })->where($where)
            ->orderBy('_id', 'desc')
            ->offset($offset)
            ->limit($limit)
            ->get();
        return $list ? $list->toArray() : [];
    }

    /**
     * @return int
     */
    private function genTotalNumber(): int
    {
        // 生成参数
        list($day_begin, $day_end, $where, $offset, $limit) = $this->genParamsForList();

        return MongoLog::when($day_begin, function ($query) use ($day_begin) {
            $middle = new UTCDateTime(strtotime($day_begin) * 1000);
            return $query->where('created_at', '>=', $middle);
        })->when($day_end, function ($query) use ($day_end) {
            $middle = new UTCDateTime(strtotime($day_end) * 1000);
            return $query->where('updated_at', '<=', $middle);
        })->where($where)
            ->count();
    }

    /**
     * 生成参数
     * @return array
     */
    private function genParamsForList(): array
    {
        list($day_begin, $day_end, $uuid, $w_uuid, $page_size, $page, $action, $type) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
            trim(request()->get('uuid')),
            trim(request()->get('w_uuid')),
            trim(request()->get('page_size', 30)),
            trim(request()->get('page', 1)),
            trim(request()->get('action')),
            trim(request()->get('type')),
        ];

        $where = array_filter(compact('uuid', 'w_uuid', 'action', 'type'), function ($item) {
            return $item;
        });

        return [
            $day_begin,
            $day_end,
            $where,
            $page ? intval(($page - 1) * $page_size) : 0,
            (int)$page_size
        ];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamsForList()
    {
        $this->_validateDate();
    }

    /**
     * 校验日期
     * @throws CustomException
     */
    private function _validateDate()
    {
        list($day_begin, $day_end) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
        ];

        // 如果同时限制了开始和结束的日期
        if ($day_begin && $day_end && strtotime($day_begin) > strtotime($day_end)) {
            throw new CustomException('开始日期必须小于结束日期');
        }
    }
}