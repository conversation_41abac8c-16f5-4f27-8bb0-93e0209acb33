<?php

namespace App\Http\Repository;

use App\Models\Account;
use App\Models\BillCost;
use App\Models\BillProductIncomeV2;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;

class StatChartRepository extends StatBaseRepository
{
    public function statChartList()
    {
        try {
            $params = $this->getStatParams();
            $params['day_type'] = request()->post('day_type', 'day');
            if ($params['day_type'] == 'month') {
                $params['start_date'] = $params['start_date'];
                $params['end_date'] = $this->getLastDay($params['end_date']);
            }
            $params['chart_type'] = request()->post('chart_type', 'money');
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        // 该用户下的所有客户
        if (!isset($params['customer_id']) || empty($params['customer_id'])) {
            $customer_ids = $this->getAuthCustomerIds();
            $apikey_list = array_column(Account::getApikeyByCustomId($customer_ids), 'apikey');
            if (!$apikey_list) {
                throw new \Exception('无权限访问');
            }
            $params['apikey_list'] = $apikey_list;
        }

        $is_show_sub = $this->isShowSubproduct($params);

        if (in_array($params['chart_type'], ['total', 'valid'])) {
            $usage_data = $this->getUsage($params); //调用量
            $data = $this->formatDataUsage($usage_data, $is_show_sub);
        } else {
            // 判断收入显示权限 没有返回空数据
            if($this->checkUserProductAuth($params['father_id'],'money')){
                $income_data = $this->getIncome($params);   //收入
                $expend_data = $this->getDateExpend($params); //特殊消耗
                $data = $this->formatDataIncomeExpend($income_data, $expend_data, $is_show_sub);
            }else{
                return compact('customer_data', 'customer_sort', 'product_data', 'product_sort');
            }
        }

        return $this->formatData($data, $params, $is_show_sub);
    }

    protected function getUsage($params)
    {
        $where = $this->getUsageWhere($params);
        $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        return StatisticsCustomerUsage::getCompareListByCustomerProduct($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey'], $params['filter_product']);
    }

    protected function getIncome($params)
    {
        $where = $this->getIncomeWhere($params);
        return BillProductIncomeV2::getCompareListByCustomerProduct($where, $params['apikey_list'], $params['product_list'], $params['filter_apikey']);
    }

    /**
     * 对比页面特殊消耗
     * @param $params
     * @return mixed
     */
    protected function getDateExpend($params)
    {
        $where = $this->getExpendWhere($params);
        $expend_data['sub'] = CustomerExpend::getDateExpendByCustomerProduct(array_merge($where, [['type', 1]]), $params['product_list'], $params['filter_customer']);
        $expend_data['add'] = CustomerExpend::getDateExpendByCustomerProduct(array_merge($where, [['type', 2]]), $params['product_list'], $params['filter_customer']);
        return $expend_data;
    }

    protected function formatDataUsage($usage_data, $is_show_sub)
    {
        $product_sort = $customer_sort = [];
        $customer_data = $product_data = [];

        foreach ($usage_data as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);

            //由于array_multisort进行二维数组排序时，如果关联（string）键名保持不变，但数字键名会被重新索引
            //且如果键值为数字,返回的json数据客户端会修改顺序,为了方便因此组装成string
            $father_id = 'p' . $father_id;

            $product_id = 'p' . $item['product_id'];

            // 总量/查得量
            $product_sort[$father_id]['total'] = $product_sort[$father_id]['total'] ?? 0;
            $product_sort[$father_id]['total'] += $item['total'];
            $product_sort[$father_id]['valid'] = $product_sort[$father_id]['valid'] ?? 0;
            $product_sort[$father_id]['valid'] += $item['valid'];

            $product_data[$father_id][$item['date']]['total'] = $product_data[$father_id][$item['date']]['total'] ?? 0;
            $product_data[$father_id][$item['date']]['total'] += $item['total'];
            $product_data[$father_id][$item['date']]['valid'] = $product_data[$father_id][$item['date']]['valid'] ?? 0;
            $product_data[$father_id][$item['date']]['valid'] += $item['valid'];

            if ($is_show_sub && $product_id != $father_id) {
                $product_sort[$product_id]['total'] = $product_sort[$product_id]['total'] ?? 0;
                $product_sort[$product_id]['total'] += $item['total'];
                $product_sort[$product_id]['valid'] = $product_sort[$product_id]['valid'] ?? 0;
                $product_sort[$product_id]['valid'] += $item['valid'];

                $product_data[$product_id][$item['date']]['total'] = $product_data[$product_id][$item['date']]['total'] ?? 0;
                $product_data[$product_id][$item['date']]['total'] += $item['total'];
                $product_data[$product_id][$item['date']]['valid'] = $product_data[$product_id][$item['date']]['valid'] ?? 0;
                $product_data[$product_id][$item['date']]['valid'] += $item['valid'];
            }

            $customer_sort[$father_id][$customer_id]['total'] = $customer_sort[$father_id][$customer_id]['total'] ?? 0;
            $customer_sort[$father_id][$customer_id]['total'] += $item['total'];
            $customer_sort[$father_id][$customer_id]['valid'] = $customer_sort[$father_id][$customer_id]['valid'] ?? 0;
            $customer_sort[$father_id][$customer_id]['valid'] += $item['valid'];

            $customer_data[$father_id][$customer_id][$item['date']]['total'] = $customer_data[$father_id][$customer_id][$item['date']]['total'] ?? 0;
            $customer_data[$father_id][$customer_id][$item['date']]['total'] += $item['total'];
            $customer_data[$father_id][$customer_id][$item['date']]['valid'] = $customer_data[$father_id][$customer_id][$item['date']]['valid'] ?? 0;
            $customer_data[$father_id][$customer_id][$item['date']]['valid'] += $item['valid'];

            $customer_sort['p0'][$customer_id]['total'] = $customer_sort['p0'][$customer_id]['total'] ?? 0;
            $customer_sort['p0'][$customer_id]['total'] += $item['total'];
            $customer_sort['p0'][$customer_id]['valid'] = $customer_sort['p0'][$customer_id]['valid'] ?? 0;
            $customer_sort['p0'][$customer_id]['valid'] += $item['valid'];

            $customer_data['p0'][$customer_id][$item['date']]['total'] = $customer_data['p0'][$customer_id][$item['date']]['total'] ?? 0;
            $customer_data['p0'][$customer_id][$item['date']]['total'] += $item['total'];
            $customer_data['p0'][$customer_id][$item['date']]['valid'] = $customer_data['p0'][$customer_id][$item['date']]['valid'] ?? 0;
            $customer_data['p0'][$customer_id][$item['date']]['valid'] += $item['valid'];
        }

        return compact('customer_data', 'customer_sort', 'product_data', 'product_sort');
    }

    protected function formatDataIncomeExpend($income_data, $expend_data, $is_show_sub)
    {
        $product_sort = $customer_sort = [];
        $customer_data = $product_data = [];

        foreach ($income_data as $item) {
            //由于array_multisort进行二维数组排序时，如果关联（string）键名保持不变，但数字键名会被重新索引
            //且如果键值为数字,返回的json数据客户端会修改顺序,为了方便因此组装成string
            $father_id = 'p' . $item['father_id'];

            $product_id = 'p' . $item['product_id'];

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);

//            $product_sort[$father_id]['number'] = $product_sort[$father_id]['number'] ?? 0;
//            $product_sort[$father_id]['number'] += $item['number'];
            $product_sort[$father_id]['money'] = $product_sort[$father_id]['money'] ?? 0;
            $product_sort[$father_id]['money'] = bcadd($product_sort[$father_id]['money'], $item['money'], $this->degree);

//            $product_data[$father_id][$item['date']]['number'] = $product_data[$father_id][$item['date']]['number'] ?? 0;
//            $product_data[$father_id][$item['date']]['number'] += $item['number'];
            $product_data[$father_id][$item['date']]['money'] = $product_data[$father_id][$item['date']]['money'] ?? 0;
            $product_data[$father_id][$item['date']]['money'] = bcadd($product_data[$father_id][$item['date']]['money'], $item['money'], $this->degree);

            if ($is_show_sub && $product_id != $father_id) {
//                $product_sort[$product_id]['number'] = $product_sort[$product_id]['number'] ?? 0;
//                $product_sort[$product_id]['number'] += $item['number'];
                $product_sort[$product_id]['money'] = $product_sort[$product_id]['money'] ?? 0;
                $product_sort[$product_id]['money'] = bcadd($product_sort[$product_id]['money'], $item['money'], $this->degree);

//                $product_data[$product_id][$item['date']]['number'] = $product_data[$product_id][$item['date']]['number'] ?? 0;
//                $product_data[$product_id][$item['date']]['number'] += $item['number'];
                $product_data[$product_id][$item['date']]['money'] = $product_data[$product_id][$item['date']]['money'] ?? 0;
                $product_data[$product_id][$item['date']]['money'] = bcadd($product_data[$product_id][$item['date']]['money'], $item['money'], $this->degree);
            }

//            $customer_sort[$father_id][$customer_id]['number'] = $customer_sort[$father_id][$customer_id]['number'] ?? 0;
//            $customer_sort[$father_id][$customer_id]['number'] += $item['number'];
            $customer_sort[$father_id][$customer_id]['money'] = $customer_sort[$father_id][$customer_id]['money'] ?? 0;
            $customer_sort[$father_id][$customer_id]['money'] = bcadd($customer_sort[$father_id][$customer_id]['money'], $item['money'], $this->degree);

//            $customer_data[$father_id][$customer_id][$item['date']]['number'] = $customer_data[$father_id][$customer_id][$item['date']]['number'] ?? 0;
//            $customer_data[$father_id][$customer_id][$item['date']]['number'] += $item['number'];
            $customer_data[$father_id][$customer_id][$item['date']]['money'] = $customer_data[$father_id][$customer_id][$item['date']]['money'] ?? 0;
            $customer_data[$father_id][$customer_id][$item['date']]['money'] = bcadd($customer_data[$father_id][$customer_id][$item['date']]['money'], $item['money'], $this->degree);

//            $customer_sort['p0'][$customer_id]['number'] = $customer_sort['p0'][$customer_id]['number'] ?? 0;
//            $customer_sort['p0'][$customer_id]['number'] += $item['number'];
            $customer_sort['p0'][$customer_id]['money'] = $customer_sort['p0'][$customer_id]['money'] ?? 0;
            $customer_sort['p0'][$customer_id]['money'] = bcadd($customer_sort['p0'][$customer_id]['money'], $item['money'], $this->degree);

//            $customer_data['p0'][$customer_id][$item['date']]['number'] = $customer_data['p0'][$customer_id][$item['date']]['number'] ?? 0;
//            $customer_data['p0'][$customer_id][$item['date']]['number'] += $item['number'];
            $customer_data['p0'][$customer_id][$item['date']]['money'] = $customer_data['p0'][$customer_id][$item['date']]['money'] ?? 0;
            $customer_data['p0'][$customer_id][$item['date']]['money'] = bcadd($customer_data['p0'][$customer_id][$item['date']]['money'], $item['money'], $this->degree);
        }

        foreach ($expend_data['add'] as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $father_id = 'p' . $father_id;

            $product_id = 'p'.$item['product_id'];

//            $product_sort[$father_id]['number'] = $product_sort[$father_id]['number'] ?? 0;
//            $product_sort[$father_id]['number'] += $item['fee_number'];
            $product_sort[$father_id]['money'] = $product_sort[$father_id]['money'] ?? 0;
            $product_sort[$father_id]['money'] = bcadd($product_sort[$father_id]['money'], $item['money'], $this->degree);

//            $product_data[$father_id][$item['date']]['number'] = $product_data[$father_id][$item['date']]['number'] ?? 0;
//            $product_data[$father_id][$item['date']]['number'] += $item['fee_number'];
            $product_data[$father_id][$item['date']]['money'] = $product_data[$father_id][$item['date']]['money'] ?? 0;
            $product_data[$father_id][$item['date']]['money'] = bcadd($product_data[$father_id][$item['date']]['money'], $item['money'], $this->degree);

            if ($is_show_sub && $product_id != $father_id) {
//                $product_sort[$product_id]['number'] = $product_sort[$product_id]['number'] ?? 0;
//                $product_sort[$product_id]['number'] += $item['fee_number'];
                $product_sort[$product_id]['money'] = $product_sort[$product_id]['money'] ?? 0;
                $product_sort[$product_id]['money'] = bcadd($product_sort[$product_id]['money'], $item['money'], $this->degree);

//                $product_data[$product_id][$item['date']]['number'] = $product_data[$product_id][$item['date']]['number'] ?? 0;
//                $product_data[$product_id][$item['date']]['number'] += $item['fee_number'];
                $product_data[$product_id][$item['date']]['money'] = $product_data[$product_id][$item['date']]['money'] ?? 0;
                $product_data[$product_id][$item['date']]['money'] = bcadd($product_data[$product_id][$item['date']]['money'], $item['money'], $this->degree);
            }

//            $customer_sort[$father_id][$item['customer_id']]['number'] = $customer_sort[$father_id][$item['customer_id']]['number'] ?? 0;
//            $customer_sort[$father_id][$item['customer_id']]['number'] += $item['fee_number'];
            $customer_sort[$father_id][$item['customer_id']]['money'] = $customer_sort[$father_id][$item['customer_id']]['money'] ?? 0;
            $customer_sort[$father_id][$item['customer_id']]['money'] = bcadd($customer_sort[$father_id][$item['customer_id']]['money'], $item['money'], $this->degree);

//            $customer_data[$father_id][$item['customer_id']][$item['date']]['number'] = $customer_data[$father_id][$item['customer_id']][$item['date']]['number'] ?? 0;
//            $customer_data[$father_id][$item['customer_id']][$item['date']]['number'] += $item['fee_number'];
            $customer_data[$father_id][$item['customer_id']][$item['date']]['money'] = $customer_data[$father_id][$item['customer_id']][$item['date']]['money'] ?? 0;
            $customer_data[$father_id][$item['customer_id']][$item['date']]['money'] = bcadd($customer_data[$father_id][$item['customer_id']][$item['date']]['money'], $item['money'], $this->degree);

//            $customer_sort['p0'][$item['customer_id']]['number'] = $customer_sort['p0'][$item['customer_id']]['number'] ?? 0;
//            $customer_sort['p0'][$item['customer_id']]['number'] += $item['fee_number'];
            $customer_sort['p0'][$item['customer_id']]['money'] = $customer_sort['p0'][$item['customer_id']]['money'] ?? 0;
            $customer_sort['p0'][$item['customer_id']]['money'] = bcadd($customer_sort['p0'][$item['customer_id']]['money'], $item['money'], $this->degree);

//            $customer_data['p0'][$item['customer_id']][$item['date']]['number'] = $customer_data['p0'][$item['customer_id']][$item['date']]['number'] ?? 0;
//            $customer_data['p0'][$item['customer_id']][$item['date']]['number'] += $item['fee_number'];
            $customer_data['p0'][$item['customer_id']][$item['date']]['money'] = $customer_data['p0'][$item['customer_id']][$item['date']]['money'] ?? 0;
            $customer_data['p0'][$item['customer_id']][$item['date']]['money'] = bcadd($customer_data['p0'][$item['customer_id']][$item['date']]['money'], $item['money'], $this->degree);
        }

        foreach ($expend_data['sub'] as $item) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $father_id = 'p' . $father_id;

            $product_id = 'p'.$item['product_id'];

//            $product_sort[$father_id]['number'] = $product_sort[$father_id]['number'] ?? 0;
//            $product_sort[$father_id]['number'] -= $item['fee_number'];
            $product_sort[$father_id]['money'] = $product_sort[$father_id]['money'] ?? 0;
            $product_sort[$father_id]['money'] = bcsub($product_sort[$father_id]['money'], $item['money'], $this->degree);

//            $product_data[$father_id][$item['date']]['number'] = $product_data[$father_id][$item['date']]['number'] ?? 0;
//            $product_data[$father_id][$item['date']]['number'] -= $item['fee_number'];
            $product_data[$father_id][$item['date']]['money'] = $product_data[$father_id][$item['date']]['money'] ?? 0;
            $product_data[$father_id][$item['date']]['money'] = bcsub($product_data[$father_id][$item['date']]['money'], $item['money'], $this->degree);

            if ($is_show_sub && $product_id != $father_id) {
//                $product_sort[$product_id]['number'] = $product_sort[$product_id]['number'] ?? 0;
//                $product_sort[$product_id]['number'] -= $item['fee_number'];
                $product_sort[$product_id]['money'] = $product_sort[$product_id]['money'] ?? 0;
                $product_sort[$product_id]['money'] = bcsub($product_sort[$product_id]['money'], $item['money'], $this->degree);

//                $product_data[$product_id][$item['date']]['number'] = $product_data[$product_id][$item['date']]['number'] ?? 0;
//                $product_data[$product_id][$item['date']]['number'] -= $item['fee_number'];
                $product_data[$product_id][$item['date']]['money'] = $product_data[$product_id][$item['date']]['money'] ?? 0;
                $product_data[$product_id][$item['date']]['money'] = bcsub($product_data[$product_id][$item['date']]['money'], $item['money'], $this->degree);
            }

//            $customer_sort[$father_id][$item['customer_id']]['number'] = $customer_sort[$father_id][$item['customer_id']]['number'] ?? 0;
//            $customer_sort[$father_id][$item['customer_id']]['number'] -= $item['fee_number'];
            $customer_sort[$father_id][$item['customer_id']]['money'] = $customer_sort[$father_id][$item['customer_id']]['money'] ?? 0;
            $customer_sort[$father_id][$item['customer_id']]['money'] = bcsub($customer_sort[$father_id][$item['customer_id']]['money'], $item['money'], $this->degree);

//            $customer_data[$father_id][$item['customer_id']][$item['date']]['number'] = $customer_data[$father_id][$item['customer_id']][$item['date']]['number'] ?? 0;
//            $customer_data[$father_id][$item['customer_id']][$item['date']]['number'] -= $item['fee_number'];
            $customer_data[$father_id][$item['customer_id']][$item['date']]['money'] = $customer_data[$father_id][$item['customer_id']][$item['date']]['money'] ?? 0;
            $customer_data[$father_id][$item['customer_id']][$item['date']]['money'] = bcsub($customer_data[$father_id][$item['customer_id']][$item['date']]['money'], $item['money'], $this->degree);

//            $customer_sort['p0'][$item['customer_id']]['number'] = $customer_sort['p0'][$item['customer_id']]['number'] ?? 0;
//            $customer_sort['p0'][$item['customer_id']]['number'] -= $item['fee_number'];
            $customer_sort['p0'][$item['customer_id']]['money'] = $customer_sort['p0'][$item['customer_id']]['money'] ?? 0;
            $customer_sort['p0'][$item['customer_id']]['money'] = bcsub($customer_sort['p0'][$item['customer_id']]['money'], $item['money'], $this->degree);

//            $customer_data['p0'][$item['customer_id']][$item['date']]['number'] = $customer_data['p0'][$item['customer_id']][$item['date']]['number'] ?? 0;
//            $customer_data['p0'][$item['customer_id']][$item['date']]['number'] -= $item['fee_number'];
            $customer_data['p0'][$item['customer_id']][$item['date']]['money'] = $customer_data['p0'][$item['customer_id']][$item['date']]['money'] ?? 0;
            $customer_data['p0'][$item['customer_id']][$item['date']]['money'] = bcsub($customer_data['p0'][$item['customer_id']][$item['date']]['money'], $item['money'], $this->degree);
        }

        return compact('customer_sort', 'customer_data', 'product_sort', 'product_data');
    }


    protected function formatData($data, $params, $is_show_sub)
    {
        $product_sort = $data['product_sort'];
        $product_data = $data['product_data'];
        $customer_data = $data['customer_data'];
        $customer_sort = $data['customer_sort'];

        // 产品排序
        $topn_product_id = $this->getProductSort($product_sort, $params['chart_type'], $is_show_sub);
        // 日期
        $date_range = $this->getDateFromRange($params['start_date'], $params['end_date']);//获取区间时间
        // 客户排序
        $topn_customer = $this->getCustomerSort($customer_sort, $params['chart_type']);

        $data = [];
        if ($params['day_type'] == 'day') {
            $data = $this->getDayData($topn_product_id, $topn_customer, $date_range, $product_data, $customer_data);
        }

        if ($params['day_type'] == 'month') {
            $data = $this->getMonthData($topn_product_id, $topn_customer, $date_range, $product_data, $customer_data);
        }

        if ($params['day_type'] == 'week') {
            $data = $this->getWeekData($topn_product_id, $topn_customer, $date_range, $product_data, $customer_data);
        }

        return $data;
    }

    protected function getProductSort($product_sort, $sort_key, $is_show_sub)
    {
        // 产品排序
        $chart_column = array_column($product_sort, $sort_key);
        array_multisort($chart_column, SORT_DESC, $product_sort);//根据查看的指标类型排序
        $sort = [];
        if ($is_show_sub) {
            $total = max($chart_column);
            foreach ($product_sort as $key => $item) {
                if ($item[$sort_key] / $total * 100 > 5) {
                    $sort[$key] = $item;
                }
            }
        } else {
            $sort = $product_sort;
        }

        return array_keys($sort);
    }

    protected function getCustomerSort($customer_sort, $sort_key)
    {
        $sort = [];
        foreach ($customer_sort as $father_id => $item) {
            $info = array_filter($item, function ($value) use ($sort_key) {
                return isset($value[$sort_key]);
            });
            $chart_column = array_column($info, $sort_key);
            array_multisort($chart_column, SORT_DESC, $info);//根据查看的指标类型排序
            $customer_id = array_keys($info);
            $customer_id = array_splice($customer_id, 0, 10);
            $sort[$father_id] = $customer_id;
        }

        return $sort;
    }

    protected function getDayData($topn_product_id, $topn_customer, $date_range, $product_data, $customer_data)
    {
        $product_list = $customer_list = [];
        foreach ($topn_product_id as $father_id) {
            foreach ($date_range as $date) {
                $product_list[$father_id][$date] = $product_data[$father_id][$date] ?? [];
            }
        }

        foreach ($topn_customer as $father_id => $item) {
            foreach ($item as $customer_id) {
                foreach ($date_range as $date) {
                    $customer_list[$father_id][$customer_id][$date] = $customer_data[$father_id][$customer_id][$date] ?? [];
                }
            }
        }

        return ['product_data' => $product_list, 'customer_data' => $customer_list];
    }

    protected function getMonthData($topn_product_id, $topn_customer, $date_range, $product_data, $customer_data)
    {
        $product_list = $customer_list = [];
        $month_list = [];
        foreach ($date_range as $date) {
            $month = substr($date, 0, 6);
            $month_list[$month][] = $date;
        }

        foreach ($topn_product_id as $father_id) {
            foreach ($month_list as $month => $date_list) {
                foreach ($date_list as $date) {
                    $product_list[$father_id][$month]['total'] = $product_list[$father_id][$month]['total'] ?? 0;
                    $total = $product_data[$father_id][$date]['total'] ?? 0;
                    $product_list[$father_id][$month]['total'] += $total;

                    $product_list[$father_id][$month]['valid'] = $product_list[$father_id][$month]['valid'] ?? 0;
                    $valid = $product_data[$father_id][$date]['valid'] ?? 0;
                    $product_list[$father_id][$month]['valid'] += $valid;

//                    $product_list[$father_id][$month]['number'] = $product_list[$father_id][$month]['number'] ?? 0;
//                    $number = $product_data[$father_id][$date]['number'] ?? 0;
//                    $product_list[$father_id][$month]['number'] += $number;

                    $product_list[$father_id][$month]['money'] = $product_list[$father_id][$month]['money'] ?? 0;
                    $money = $product_data[$father_id][$date]['money'] ?? 0;
                    $product_list[$father_id][$month]['money'] += $money;
                }
            }
        }

        foreach ($topn_customer as $father_id => $item) {
            foreach ($item as $customer_id) {
                foreach ($month_list as $month => $date_list) {
                    foreach ($date_list as $date) {
                        $customer_list[$father_id][$customer_id][$month]['total'] = $customer_list[$father_id][$customer_id][$month]['total'] ?? 0;
                        $total = $customer_data[$father_id][$customer_id][$date]['total'] ?? 0;
                        $customer_list[$father_id][$customer_id][$month]['total'] += $total;

                        $customer_list[$father_id][$customer_id][$month]['valid'] = $customer_list[$father_id][$customer_id][$month]['valid'] ?? 0;
                        $valid = $customer_data[$father_id][$customer_id][$date]['valid'] ?? 0;
                        $customer_list[$father_id][$customer_id][$month]['valid'] += $valid;

//                        $customer_list[$father_id][$customer_id][$month]['number'] = $customer_list[$father_id][$customer_id][$month]['number'] ?? 0;
//                        $number = $customer_data[$father_id][$customer_id][$date]['number'] ?? 0;
//                        $customer_list[$father_id][$customer_id][$month]['number'] += $number;

                        $customer_list[$father_id][$customer_id][$month]['money'] = $customer_list[$father_id][$customer_id][$month]['money'] ?? 0;
                        $money = $customer_data[$father_id][$customer_id][$date]['money'] ?? 0;
                        $customer_list[$father_id][$customer_id][$month]['money'] += $money;
                    }
                }
            }
        }

        return ['product_data' => $product_list, 'customer_data' => $customer_list];
    }

    protected function getWeekData($topn_product_id, $topn_customer, $date_range, $product_data, $customer_data)
    {
        $product_list = $customer_list = [];

        $week_list = [];
        foreach ($date_range as $date) {
            $week = $this->getDateWeek($date);//根据日期获取所在年份的第几周
            $week_list[$week][] = $date;
        }

        foreach ($topn_product_id as $father_id) {
            foreach ($week_list as $week => $date_list) {
                foreach ($date_list as $date) {
                    $product_list[$father_id][$week]['total'] = $product_list[$father_id][$week]['total'] ?? 0;
                    $total = $product_data[$father_id][$date]['total'] ?? 0;
                    $product_list[$father_id][$week]['total'] += $total;

                    $product_list[$father_id][$week]['valid'] = $product_list[$father_id][$week]['valid'] ?? 0;
                    $valid = $product_data[$father_id][$date]['valid'] ?? 0;
                    $product_list[$father_id][$week]['valid'] += $valid;

//                    $product_list[$father_id][$week]['number'] = $product_list[$father_id][$week]['number'] ?? 0;
//                    $number = $product_data[$father_id][$date]['number'] ?? 0;
//                    $product_list[$father_id][$week]['number'] += $number;

                    $product_list[$father_id][$week]['money'] = $product_list[$father_id][$week]['money'] ?? 0;
                    $money = $product_data[$father_id][$date]['money'] ?? 0;
                    $product_list[$father_id][$week]['money'] += $money;
                }
            }
        }

        foreach ($topn_customer as $father_id => $item) {
            foreach ($item as $customer_id) {
                foreach ($week_list as $week => $date_list) {
                    foreach ($date_list as $date) {
                        $customer_list[$father_id][$customer_id][$week]['total'] = $customer_list[$father_id][$customer_id][$week]['total'] ?? 0;
                        $total = $customer_data[$father_id][$customer_id][$date]['total'] ?? 0;
                        $customer_list[$father_id][$customer_id][$week]['total'] += $total;

                        $customer_list[$father_id][$customer_id][$week]['valid'] = $customer_list[$father_id][$customer_id][$week]['valid'] ?? 0;
                        $valid = $customer_data[$father_id][$customer_id][$date]['valid'] ?? 0;
                        $customer_list[$father_id][$customer_id][$week]['valid'] += $valid;

                        $customer_list[$father_id][$customer_id][$week]['number'] = $customer_list[$father_id][$customer_id][$week]['number'] ?? 0;
                        $number = $customer_data[$father_id][$customer_id][$date]['number'] ?? 0;
                        $customer_list[$father_id][$customer_id][$week]['number'] += $number;

                        $customer_list[$father_id][$customer_id][$week]['money'] = $customer_list[$father_id][$customer_id][$week]['money'] ?? 0;
                        $money = $customer_data[$father_id][$customer_id][$date]['money'] ?? 0;
                        $customer_list[$father_id][$customer_id][$week]['money'] += $money;
                    }
                }
            }
        }

        return ['product_data' => $product_list, 'customer_data' => $customer_list];
    }


    protected function isShowSubproduct($params)
    {
        if (isset($params['father_id']) && $params['father_id'] || (isset($params['product_id']) && $params['product_id'])) {
            return true;
        }
        return false;
    }
}
