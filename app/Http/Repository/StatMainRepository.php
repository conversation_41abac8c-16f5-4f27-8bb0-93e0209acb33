<?php

namespace App\Http\Repository;

use App\Exports\CustomerExpendExport;
use App\Exports\MainProductStatExport;
use App\Models\Account;
use App\Models\BillCost;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Cost\ProductCostRepository;
use App\Repositories\Income\ProductIncomeRepository;
use App\Utils\Helpers\Func;
use Maatwebsite\Excel\Facades\Excel;


class StatMainRepository extends StatBaseRepository
{
    /**
     * 数据统计-客户维度
     * @param $user_products
     * @return array
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        $usage_data = $this->getUsage($params);     //获取调用量
        $income_data = $this->getIncome($params);   //获取收入
        $cost_data = $this->getCost($params);   //获取成本
        $cost_adjust_data = $this->getCostAdjust($params);  //获取成本调整
        $cost_fixed_data = $this->getFixedCost($params);  //获取固定费用成本

        $expend_data = $this->getExpend($params);   //获取客户特殊消耗、特殊充值

        //将上面数据糅合到统一的父产品维度
        $data = $this->formatData($usage_data, $income_data, $cost_data, $expend_data, $cost_adjust_data, $cost_fixed_data);
        $data = $this->filterAuthMoney($data); //过滤没有权限查看的产品（收入和成本）
        return array_values($data);
    }

    /**
     * 获取调用量
     * @param $params
     * @return array
     */
    protected function getUsage($params) {
        //获取子产品调用量
        $usage_data_child = StatisticsCustomerUsage::getChildProductStat($params);
        //获取普通产品调用量
        $params['general_pid'] = array_column(Product::getGeneralProduct(), 'product_id');
        $usage_data_general = StatisticsCustomerUsage::getGeneralProductStat($params);
        $usage_data_general = array_map(function($item){
            $item['father_id'] = $item['product_id'];
            return $item;
        }, $usage_data_general);

        return array_merge($usage_data_child, $usage_data_general);
    }

    /**
     * 获取营收
     * @param $params
     * @return mixed
     */
    protected function getIncome($params){
        $where = $this->getIncomeWhere($params);
        $need_apikey = empty($params['need_apikey']) ? [] : $params['need_apikey'];
        $filter_apikey = empty($params['filter_apikey']) ? [] : $params['filter_apikey'];
        $cost_data = BillProductIncomeV2::getMainStatList($where, $filter_apikey,$need_apikey);
        return $cost_data;
    }

    /**
     * 获取成本
     * @param $params
     * @return array
     */
    protected function getCost($params){
        //获取子产品成本
        $cost_data_child = BillCostV2::getChildProductStat($params);

        //按道理该查询其实没有作用 因为bill_cost中product_id没有父产品id,
        //但是还存在product_id=401或者501,601,604,612,617,664(特殊产品情况 产品id和父产品id一样或没有子产品的父产品)
        $cost_data_general = BillCostV2::getGeneralProductStat($params);
        $cost_data_general = array_map(function($item){
            $item['father_id'] = $item['product_id'];
            return $item;
        }, $cost_data_general);
        return array_merge($cost_data_child, $cost_data_general);
    }

    protected function getCostAdjust($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        //获取子产品的成本调整
        $cost_data_child = ChannelAccountAdjust::getChildProductCost($params);

        //按道理该查询其实没有作用 因为ChannelAccountAdjust中product_id没有父产品id,
        //但是还存在product_id=401或者501,601,604,612,617,664(特殊产品情况 产品id和父产品id一样或没有子产品的父产品)
        $cost_data_general = ChannelAccountAdjust::getGeneralProductCost($params);
        $cost_data_general = array_map(function($item){
            $item['father_id'] = $item['product_id'];
            return $item;
        }, $cost_data_general);
        return array_merge($cost_data_child, $cost_data_general);
    }

    protected function getFixedCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }
        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $fixed_cost = ChannelAccountFixedFee::getChannelCostByFatherId($params);
        return $fixed_cost;
    }

    /**
     * 获取营收特殊消耗
     * @param $params
     * @return array
     */
    protected function getExpend($params){
        $where = $this->getExpendWhere($params);

        $expend = [];
        //获取特殊充值
        $expend['sub'] = CustomerExpend::getProductExpend(array_merge($where, [['type', '=', 1]]), $params['filter_customer']);
        //获取特殊消费
        $expend['add'] = CustomerExpend::getProductExpend(array_merge($where, [['type', '=', 2]]), $params['filter_customer']);
        return $expend;
    }


    /**
     * 营收的where条件
     * @param $params
     * @return array|array[]
     */
    protected function getIncomeWhere($params)
    {
        $where = [];
        isset($params['start_date']) && isset($params['end_date'])
        && $where = [['date', '>=', $params['start_date']],['date', '<=', $params['end_date']]];
        return $where;
    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @param $adjust_data
     * @return array
     */
    private function formatData($usage_data, $income_data, $cost_data, $expend_data, $cost_adjust_data = [], $cost_fixed_data = [])
    {
        $data = [];
        //糅合调用量数据
        foreach ($usage_data as $item){
            $data[$item['father_id']] = $item;
        }
        //糅合营收数据
        foreach ($income_data as $item){
            if (!isset($data[$item['father_id']])){
                $data[$item['father_id']]['father_id'] = $item['father_id'];
            }
            $data[$item['father_id']]['number'] = $item['number'];
            $data[$item['father_id']]['money'] = $item['money'];
            $data[$item['father_id']]['money_finance'] = $item['money_finance'];
        }
        //糅合成本数据
        foreach ($cost_data as $item){
            if (!isset($data[$item['father_id']])){
                $data[$item['father_id']]['father_id'] = $item['father_id'];
            }
            $data[$item['father_id']]['cost'] = $item['money'];
        }

        //糅合成本调整数据
        foreach ($cost_adjust_data as $item){
            if (!isset($data[$item['father_id']])){
                $data[$item['father_id']]['father_id'] = $item['father_id'];
            }
            $total_cost = $data[$item['father_id']]['cost'] ?? 0;
            $data[$item['father_id']]['cost'] = bcadd($total_cost, $item['money'], $this->degree);
        }

        //糅合固定费用成本数据
        foreach ($cost_fixed_data as $item){
            if (!isset($data[$item['father_id']])){
                $data[$item['father_id']]['father_id'] = $item['father_id'];
            }
            $total_cost = $data[$item['father_id']]['cost'] ?? 0;
            $data[$item['father_id']]['cost'] = bcadd($total_cost, $item['money'], $this->degree);
        }

        //糅合营收特殊消耗
        if (isset($expend_data['add'])) {
            foreach ($expend_data['add'] as $item) {
                //获取fatherId（如果father_id不大于0说明是一级产品）
                $fatherId = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
                if ($fatherId <= 0) {
                    $fatherId = $item['product_id'];
                }
                if (!isset($data[$fatherId])) {
                    $data[$fatherId]['father_id'] = $fatherId;
                }
                $data[$fatherId]['number'] = isset($data[$fatherId]['number']) ? $data[$fatherId]['number'] + $item['fee_number'] : $item['fee_number'];
                //$data[$fatherId]['money'] = isset($data[$fatherId]['money']) ? $data[$fatherId]['money'] + $item['money'] : $item['money'];
                $sum_money = $data[$fatherId]['money'] ?? 0;
                $data[$fatherId]['money'] = bcadd($sum_money, $item['money'], $this->degree);

                //金融收入
                $sum_money_finance = $data[$fatherId]['money_finance'] ?? 0;
                $data[$fatherId]['money_finance'] = bcadd($sum_money_finance, $item['money_finance'], $this->degree);
            }
        }
        //糅合营收特殊赠送
        if (isset($expend_data['sub'])) {
            foreach ($expend_data['sub'] as $item) {
                //获取fatherId（如果father_id不大于0说明是一级产品）
                $fatherId = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
                if ($fatherId <= 0) {
                    $fatherId = $item['product_id'];
                }
                if (!isset($data[$fatherId])) {
                    $data[$fatherId]['father_id'] = $fatherId;
                }
                $data[$fatherId]['number'] = isset($data[$fatherId]['number']) ? $data[$fatherId]['number'] - $item['fee_number'] : 0 - $item['fee_number'];
                //$data[$fatherId]['money'] = isset($data[$fatherId]['money']) ? $data[$fatherId]['money'] - $item['money'] : 0 - $item['money'];
                $sum_money = $data[$fatherId]['money'] ?? 0;
                $data[$fatherId]['money'] = bcsub($sum_money, $item['money'], $this->degree);

                //金融收入
                $sum_money_finance = $data[$fatherId]['money_finance'] ?? 0;
                $data[$fatherId]['money_finance'] = bcsub($sum_money_finance, $item['money_finance'], $this->degree);
            }
        }

        return $data;
    }

    /**
     * 过滤没有权限查看的产品（收入和成本）
     * @param $data
     * @return mixed
     */
    private function filterAuthMoney($data){
        foreach ($data as $father_id => $items) {
            //判断用户是否有展示金额相关的权限，没有则用-标识没权限，跟0区分开
            if (!$this->checkUserProductAuth($father_id,'money')) {
                $data[$father_id]['money']  = '-';
                $data[$father_id]['money_finance']  = '-';
            }
            if(!$this->checkUserProductAuth($father_id,'cost')){
                $data[$father_id]['cost'] = '-';
            }
            if(!$this->checkUserProductAuth($father_id,'cache')){
               $data[$father_id]['cache'] = '-';
            }
        }
        return $data;
    }


    /**
     * 主产品统计数据导出
     */
    public function getDownlandMainProductDatas($special = 0)
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        $incomeParams = $params;
        //企服用户特殊处理
        if ($special) {
            $filterApikeys = [];
            $where = [
                ['customer_type', Customer::$customerType['企服用户']],
            ];
            $customerIds = (new Customer())->where($where)->pluck('customer_id');
            $customerIds = count($customerIds) > 0 ? $customerIds->toArray() : [];
            if ($customerIds) {
                $filterApikeys = (new Account())->whereIn('customer_id', $customerIds)->pluck('apikey');
                $filterApikeys = count($filterApikeys) > 0 ? $filterApikeys->toArray() : [];
            }
            $incomeParams['filter_apikey'] = $filterApikeys;
        }

        if (!empty($incomeParams['filter_apikey'])) {
            $income_data = $this->getIncome($incomeParams);   //获取收入
            unset($incomeParams['filter_apikey']);
            $incomeParams['need_apikey'] = $filterApikeys;
            $tmpIncomeData2 = $this->getIncome($incomeParams);   //获取收入
            $income_data = array_column($income_data, null,'father_id');
            foreach ($tmpIncomeData2 as $item) {
                $item['money'] = round($item['money']/2,2);
                if (!isset($income_data[$item['father_id']])) {
                    $income_data[$item['father_id']] = $item;
                    continue;
                }
                $income_data[$item['father_id']]['money'] += $item['money'];
                $income_data[$item['father_id']]['number'] += $item['number'];
            }
            $where = $this->getExpendWhere($params);

            $expend_data = [];
            //获取特殊充值
            $expend_data['sub'] = CustomerExpend::getCustomerExpend(array_merge($where, [['type', '=', 1]]), $params['filter_customer']);
            //获取特殊消费
            $expend_data['add'] = CustomerExpend::getCustomerExpend(array_merge($where, [['type', '=', 2]]), $params['filter_customer']);

            foreach ($expend_data['sub'] as $key => $tv) {
                if (!in_array($tv['customer_id'], $customerIds)) {
                    continue;
                }
                $expend_data['sub'][$key]['money'] = round($tv['money']/2,2);
            }

            foreach ($expend_data['add'] as $key => $tv) {
                if (!in_array($tv['customer_id'], $customerIds)) {
                    continue;
                }
                $expend_data['add'][$key]['money'] = round($tv['money']/2,2);
            }
        } else {
            $income_data = $this->getIncome($incomeParams);
            $expend_data = $this->getExpend($params);   //获取客户特殊消耗、特殊充值
        }

        $usage_data = $this->getUsage($params);     //获取调用量
        $cost_data = $this->getCost($params);   //获取成本
        $cost_adjust_data = $this->getCostAdjust($params);  //获取成本调整
        $cost_fixed_data = $this->getFixedCost($params);  //获取固定费用成本
        //将上面数据糅合到统一的父产品维度
        $data = $this->formatData($usage_data, $income_data, $cost_data, $expend_data, $cost_adjust_data, $cost_fixed_data);
        $fatherIds = array_keys($data);
        $productInfos = (new Product())->select(['product_id', 'product_name'])->whereIn('product_id', $fatherIds)->get()->toArray();
        $productInfos = array_column($productInfos, null, 'product_id');
        $data = $this->filterAuthMoney($data); //过滤没有权限查看的产品（收入和成本）
        foreach ($data as $key => &$item) {
            $item['product_name'] = $productInfos[$key]['product_name'];
        }
        return array_values($data);
    }

    public function getDownlandMainProductDatasForMonth()
    {
        try {
            $params = $this->getStatParams();
            $params['filter_product'] = array_column(Product::getHasProductFatherId(), 'product_id');
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        $incomeRepository = new ProductIncomeRepository();
        $costRepository = new ProductCostRepository();
        $costDatas = $costRepository->getProductCostTotalForMonth($params);
        $incomeDatas = $incomeRepository->getProductIncomeTotalForMonth($params);

        $usageWhere = [
            ['date', '>=', $params['start_date']],
            ['date', '<=', $params['end_date']]
        ];
        isset($params['apikey']) && $usageWhere[] = ['apikey', $params['apikey']];
        isset($params['product_id']) && $usageWhere[] = ['product_id', $params['product_id']];
        isset($params['operator']) && $usageWhere[] = ['operator', $params['operator']];
        isset($params['source']) && $usageWhere[] = ['source', $params['source']];

        $usage = StatisticsCustomerUsage::getStatListForMonth($usageWhere, $params['product_list'], $params['apikey_list'], $params['filter_apikey'], $params['filter_product']);
        $usage = count($usage) ? $usage->toArray() : [];
        $data = $this->formatDataForMonth($usage, $costDatas, $incomeDatas);
        //过滤没有权限查看的产品（收入和成本）
        foreach ($data as $month => $item) {
            $data[$month] = $this->filterAuthMoney($item);
        }
        $return = $this->getArrayValues($data);
        return $return;
    }

    protected function getArrayValues($datas)
    {
        $return = [];
        if (!$datas) {
            return $return;
        }
        foreach ($datas as $item) {
            if (!isset($item['month'])) {
                $return = array_merge($return, $this->getArrayValues($item));
                continue;
            }
            $return[] = $item;
        }

        return $return;
    }

    private function formatDataForMonth($usage_data, $cost_data, $income_data)
    {

        $productIds = array_unique(array_column($usage_data,'product_id'));
        $fatherMap = [];
        foreach ($productIds as $id) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($id) ?: $id;
            $fatherMap[$id] = $father_id;
        }
        $data = [];

        foreach ($usage_data as $item){
            $fatherId = $fatherMap[$item['product_id']];
            if (!isset($data[$item['month']][$fatherId])) {
                $data[$item['month']][$fatherId] = [
                    'month' => $item['month'],
                    'father_id' => $fatherId,
                    'total' => 0,
                    'success' => 0,
                    'valid' => 0,
                    'cache' => 0,
                    'number' => 0,
                    'money' => 0,
                    'money_finance' => 0,
                    'cost' => 0,
                ];
            }
            $data[$item['month']][$fatherId]['total'] += $item['total'];
            $data[$item['month']][$fatherId]['success'] += $item['success'];
            $data[$item['month']][$fatherId]['valid'] += $item['valid'];
            $data[$item['month']][$fatherId]['cache'] += $item['cache'];
        }
        //糅合营收数据
        foreach ($income_data as $item){
            if (!isset($data[$item['month']][$item['father_id']])) {
                $data[$item['month']][$item['father_id']] = [
                    'month' => $item['month'],
                    'father_id' => $item['father_id'],
                    'total' => 0,
                    'success' => 0,
                    'valid' => 0,
                    'cache' => 0,
                    'number' => 0,
                    'money' => 0,
                    'money_finance' => 0,
                    'cost' => 0,
                ];
            }
            $data[$item['month']][$item['father_id']]['number'] += $item['number'];
            $sum_money = $data[$item['month']][$item['father_id']]['money'] ?? 0;
            $data[$item['month']][$item['father_id']]['money'] = bcadd($sum_money, $item['money'], $this->degree);

            //金融收入
            $sum_money_finance = $data[$item['month']][$item['father_id']]['money_finance'] ?? 0;
            $data[$item['month']][$item['father_id']]['money_finance'] = bcadd($sum_money_finance, $item['money_finance'], $this->degree);
        }
        //糅合成本数据
        foreach ($cost_data as $item){
            if (!isset($data[$item['month']][$item['father_id']])) {
                $data[$item['month']][$item['father_id']] = [
                    'month' => $item['month'],
                    'father_id' => $item['father_id'],
                    'total' => 0,
                    'success' => 0,
                    'valid' => 0,
                    'cache' => 0,
                    'number' => 0,
                    'money' => 0,
                    'money_finance' => 0,
                    'cost' => 0,
                ];
            }
            $data[$item['month']][$item['father_id']]['cost'] += $item['cost'];
        }

        return $data;
    }
}