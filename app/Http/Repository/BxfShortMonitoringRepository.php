<?php

namespace App\Http\Repository;


use App\Models\MongoSecondaryProcessingScore;
use App\Models\MongoSecondaryProcessingStatis;
use App\Models\Monitor\ProductStatis;
use App\Models\Monitor\ChannelStatis;
use App\Models\Monitor\ProductValueSpread;
use App\Models\BxfPeriod;
use Illuminate\Support\Facades\Redis;



class BxfShortMonitoringRepository
{

    /**
     * 产品监控-值分布
     * @return mixed
     * @throws \Exception
     */
    public function valueSpreadInfo()
    {
        $params = $this->getSpreadParams();
        $where = [
            ['start_time', '>=', $params['start_date']],
            ['start_time', '<=', $params['end_date']]
        ];
        if ($params['product_id']) {
            $where[] = ['pid', $params['product_id']];
        }
        if ($params['apikey']) {
            $where[] = ['apikey', $params['apikey']];
        }

        $offset = intval($params['offset']) ? intval($params['offset']) : null;
        $limit = intval($params['limit']) ? intval($params['limit']) : null;

        $channel = $params['channel'] ? $params['channel'] : null;

        $count = ProductValueSpread::getCountByCondition($where, $channel);
        $data = ProductValueSpread::getListByCondition($where, null,  'id desc', $channel, $offset, $limit);
        return compact('count', 'data');
    }

    /**
     * 产品监控-查得率
     * @return mixed
     * @throws \Exception
     */
    public function successRatioInfo()
    {
        $params = $this->getSpreadParams();
        $where = [
            ['start_time', '>=', $params['start_date']],
            ['start_time', '<=', $params['end_date']]
        ];
        if ($params['product_id']) {
            $where[] = ['pid', $params['product_id']];
        }
        if ($params['apikey']) {
            $where[] = ['apikey', $params['apikey']];
        }

        $offset = intval($params['offset']) ? intval($params['offset']) : null;
        $limit = intval($params['limit']) ? intval($params['limit']) : null;

        $channel = $params['channel'] ? $params['channel'] : null;

        $count = ProductStatis::getCountByCondition($where, $channel);
        $data = ProductStatis::getListByCondition($where, null,  'id desc', $channel, $offset, $limit);
        return compact('count', 'data');
    }

    /**
     * 渠道查得率
     * @return mixed
     * @throws \Exception
     */
    public function channelStatisInfo()
    {
        $params = $this->getSpreadParams();
        $where = [
            ['end_time', '>=', $params['start_date']],
            ['end_time', '<=', $params['end_date']]
        ];

        $offset = intval($params['offset']) ? intval($params['offset']) : null;
        $limit = intval($params['limit']) ? intval($params['limit']) : null;

        $channel = $params['channel'] ? $params['channel'] : null;

        $count = ChannelStatis::getCountByCondition($where, $channel);
        $data = ChannelStatis::getListByCondition($where, null,  'id desc', $channel, $offset, $limit);
        return compact('count', 'data');
    }

    public function scoreInfo()
    {
        $params = $this->getParams();

        //获取参数区间内容分值统计数据
        $data = $this->getScoreInfo($params['start_date'], $params['end_date'], $params);

        //总数
        $total = array_sum(array_column($data, 'number'));
        //无效量
        $notMeanOValue = config('params.bxf_short.notMeanOValue');
        $data          = array_column($data, null, 'min');
        $failed        = array_get($data, $notMeanOValue) ?: [];
        $failed        = array_get($failed, 'number') ?: 0;
        //有销量
        $success = $total - $failed;
        //比率
        $failedRate  = $this->computeRate($failed, $total);
        $successRate = $this->computeRate($success, $total);

        //遍历数据，汇总到接口数据中
        $container = [
            '0~50'     => [
                'min'    => 0,
                'week'   => 0,
                'search' => 0
            ],
            '51~100'   => [
                'min'    => 51,
                'week'   => 0,
                'search' => 0
            ],
            '101~150'  => [
                'min'    => 101,
                'week'   => 0,
                'search' => 0
            ],
            '151~200'  => [
                'min'    => 151,
                'week'   => 0,
                'search' => 0
            ],
            '201~250'  => [
                'min'    => 201,
                'week'   => 0,
                'search' => 0
            ],
            '251~300'  => [
                'min'    => 251,
                'week'   => 0,
                'search' => 0
            ],
            '301~350'  => [
                'min'    => 301,
                'week'   => 0,
                'search' => 0
            ],
            '351~400'  => [
                'min'    => 351,
                'week'   => 0,
                'search' => 0
            ],
            '401~450'  => [
                'min'    => 401,
                'week'   => 0,
                'search' => 0
            ],
            '451~500'  => [
                'min'    => 451,
                'week'   => 0,
                'search' => 0
            ],
            '501~550'  => [
                'min'    => 501,
                'week'   => 0,
                'search' => 0
            ],
            '551~600'  => [
                'min'    => 551,
                'week'   => 0,
                'search' => 0
            ],
            '601~650'  => [
                'min'    => 601,
                'week'   => 0,
                'search' => 0
            ],
            '651~700'  => [
                'min'    => 651,
                'week'   => 0,
                'search' => 0
            ],
            '701~750'  => [
                'min'    => 701,
                'week'   => 0,
                'search' => 0
            ],
            '751~800'  => [
                'min'    => 751,
                'week'   => 0,
                'search' => 0
            ],
            '801~850'  => [
                'min'    => 801,
                'week'   => 0,
                'search' => 0
            ],
            '851~900'  => [
                'min'    => 851,
                'week'   => 0,
                'search' => 0
            ],
            '901~950'  => [
                'min'    => 901,
                'week'   => 0,
                'search' => 0
            ],
            '951~1000' => [
                'min'    => 951,
                'week'   => 0,
                'search' => 0
            ]
        ];

        $rubbish = [
            'search' => [],
            'week'   => []
        ];

        array_walk($data, function ($item) use (&$container, $success, &$rubbish) {
            $name   = $item['name'];
            $number = $item['number'];
            if (array_key_exists($name, $container)) {
                $container[$name]['search'] = $this->computeRate($number, $success);
            } else {
                $rubbish['search'][] = $item;
            }
        });

        //获取T-7到T-1的数据
        $start_date = date('Ymd', strtotime('-7 days'));
        $end_date   = date('Ymd', strtotime('-1 days'));
        $data       = $this->getScoreInfo($start_date, $end_date, $params);

        //计算7天内的成功量
        $weekTotal = array_sum(array_column($data, 'number'));
        //无效量
        $data       = array_column($data, null, 'min');
        $weekFailed = array_get((array_get($data, $notMeanOValue) ?: []), 'number') ?: 0;
        //有效量
        $weekSuccess = $weekTotal - $weekFailed;

        array_walk($data, function ($item) use (&$container, $weekSuccess, &$rubbish) {
            $name   = $item['name'];
            $number = $item['number'];
            if (array_key_exists($name, $container)) {
                $container[$name]['week'] = $this->computeRate($number, $weekSuccess);
            } else {
                $rubbish['week'][] = $item;
            }
        });

        //计算PSI
        $psi = round($this->getPsi($container), 4);

        //$x      = array_column($container, 'min');
        $x      = array_keys($container);
        $week   = array_column($container, 'week');
        $search = array_column($container, 'search');

        $successRate = $successRate == '--' ? '--' : $successRate . '%';
        $failedRate  = $failedRate == '--' ? '--' : $failedRate . '%';

        return compact('x', 'week', 'search', 'success', 'total', 'failed', 'successRate', 'failedRate', 'psi',
            'rubbish');
    }

    /**
     * 计算PSI
     *
     * @access protected
     *
     * @param $data
     *
     * @return float
     **/
    protected function getPsi($data)
    {
        $psi = 0;
        array_walk($data, function ($item) use (&$psi) {
            $search = array_key_exists('search', $item) ? bcdiv($item['search'], 100, 4) : 0;
            $week   = array_key_exists('week', $item) ? bcdiv($item['week'], 100, 4) : 0;
            if ($week != 0) {
                $psiItem = bcmul(bcsub($search, $week, 4), log(bcdiv($search, $week, 8)), 8);
                $psi     += $psiItem;
            }
        });
        return $psi;
    }

    /**
     * 获取分值的统计数据
     *
     * @access protected
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     * @param $params     array 查询参数
     *
     * @return array
     **/
    protected function getScoreInfo($start_date, $end_date, $params)
    {
        //获取制定区间内的分组数据
        return MongoSecondaryProcessingScore::raw(function ($collection) use ($start_date, $end_date, $params) {
            $pipeline    = [];
            $pipeline[0] = [
                '$match' => [
                    'day'        => [
                        '$lte' => intval($end_date),
                        '$gte' => intval($start_date)
                    ],
                    'product_id' => $params['product_id'],
                ]
            ];
            //是否存在客户条件
            if ($params['customer_id']) {
                $pipeline[0]['$match']['customer_id'] = $params['customer_id'];
            }
            //是否存在渠道条件
            if (!is_null($params['ctype'])) {
                $pipeline[0]['$match']['ctype'] = intval($params['ctype']);
            }

            //分组查询
            $pipeline[1] = [
                '$group' => [
                    '_id'    => '$min',
                    'number' => [
                        '$sum' => '$number'
                    ],
                    'name'   => [
                        '$first' => '$name'
                    ],
                    'min'    => [
                        '$first' => '$min'
                    ]
                ]
            ];

            //排序
            $pipeline[2] = [
                '$sort' => [
                    'min' => 1
                ]
            ];
            return $collection->aggregate($pipeline);
        })->toArray();
    }


    public function statisInfo()
    {
        $params = $this->getParams();

        //获取查询区间的数据
        $data = $this->getStatisInfo($params['start_date'], $params['end_date'], $params);

        //总数
        $total = array_sum(array_column($data, 'number'));
        //无效量
        $notMeanOValue = config('params.bxf_short.notMeanOValue');
        $data          = array_column($data, null, 'name');
        $failed        = array_get($data, $notMeanOValue) ?: [];
        $failed        = array_get($failed, 'number') ?: 0;
        //有销量
        $success = $total - $failed;
        //比率
        $failedRate  = $this->computeRate($failed, $total);
        $successRate = $this->computeRate($success, $total);

        //遍历数据，汇总到接口数据中
        $container = [
            -1 => [
                'week'   => 0,
                'search' => 0
            ],
            0  => [
                'week'   => 0,
                'search' => 0
            ],
            1  => [
                'week'   => 0,
                'search' => 0
            ],
            2  => [
                'week'   => 0,
                'search' => 0
            ],
            3  => [
                'week'   => 0,
                'search' => 0
            ],
            4  => [
                'week'   => 0,
                'search' => 0
            ],
            5  => [
                'week'   => 0,
                'search' => 0
            ],
            6  => [
                'week'   => 0,
                'search' => 0
            ]
        ];

        $rubbish = [
            'search' => [],
            'week'   => []
        ];

        array_walk($data, function ($item) use (&$container, $success, &$rubbish) {
            $name   = $item['name'];
            $number = $item['number'];
            if (array_key_exists($name, $container)) {
                $container[$name]['search'] = $this->computeRate($number, $success);
            } else {
                $rubbish['search'][] = $item;
            }
        });

        //获取T-7到T-1的数据
        $start_date = date('Ymd', strtotime('-7 days'));
        $end_date   = date('Ymd', strtotime('-1 days'));
        $data       = $this->getStatisInfo($start_date, $end_date, $params);

        //计算7天内的成功量
        $weekTotal = array_sum(array_column($data, 'number'));
        //无效量
        $data       = array_column($data, null, 'name');
        $weekFailed = array_get((array_get($data, $notMeanOValue) ?: []), 'number') ?: 0;
        //有效量
        $weekSuccess = $weekTotal - $weekFailed;

        array_walk($data, function ($item) use (&$container, $weekSuccess, &$rubbish) {
            $name   = $item['name'];
            $number = $item['number'];
            if (array_key_exists($name, $container)) {
                $container[$name]['week'] = $this->computeRate($number, $weekSuccess);
            } else {
                $rubbish['search'][] = $item;
            }
        });

        //计算PSI
        $psi = round($this->getPsi($container), 4);

        $x      = array_keys($container);
        $week   = array_column($container, 'week');
        $search = array_column($container, 'search');

        return compact('x', 'week', 'search', 'success', 'total', 'failed', 'successRate', 'failedRate', 'psi',
            'rubbish');
    }

    /**
     * 获取统计类值的统计数据
     *
     * @access protected
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     * @parmam $params array 查询参数
     *
     * @return array
     **/
    protected function getStatisInfo($start_date, $end_date, $params)
    {
        return MongoSecondaryProcessingStatis::raw(function ($collection) use ($start_date, $end_date, $params) {
            $pipeline    = [];
            $pipeline[0] = [
                '$match' => [
                    'day'        => [
                        '$lte' => intval($end_date),
                        '$gte' => intval($start_date)
                    ],
                    'product_id' => $params['product_id'],
                ]
            ];
            //是否存在客户条件
            if ($params['customer_id']) {
                $pipeline[0]['$match']['customer_id'] = $params['customer_id'];
            }
            //是否存在渠道条件
            if (!is_null($params['ctype'])) {
                $pipeline[0]['$match']['ctype'] = intval($params['ctype']);
            }

            //分组查询
            $pipeline[1] = [
                '$group' => [
                    '_id'    => '$o_value',
                    'number' => [
                        '$sum' => '$number'
                    ],
                    'name'   => [
                        '$first' => '$o_value'
                    ]
                ]
            ];

            //排序
            $pipeline[2] = [
                '$sort' => [
                    'name' => 1
                ]
            ];
            return $collection->aggregate($pipeline);
        })->toArray();

    }

    /**
     * 获取参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getParams()
    {
        $customer_id = request()->post('customer_id', null);

        $ctype = request()->post('ctype', null);

        $product_id = request()->post('product_id', null);
        if (empty($product_id)) {
            throw new \Exception('缺少产品ID');
        }
        $product_id = intval($product_id);


        $start_date = request()->post('start_date', null);
        $end_date   = request()->post('end_date', null);
        if (empty($start_date) || empty($end_date)) {
            throw new \Exception('日期参数错误');
        }

        $date_array = [
            strtotime($start_date),
            strtotime($end_date),
        ];
        $start_date = intval(date('Ymd', min($date_array)));
        $end_date   = intval(date('Ymd', max($date_array)));
        return compact('customer_id', 'ctype', 'start_date', 'end_date', 'product_id');
    }

    /**
     * 计算比率
     *
     * @access protected
     *
     * @param $left  integer 除数
     * @param $right integer 被除数
     * @param $scale integer 小数位数
     *
     * @return float
     **/
    protected function computeRate($left, $right, $scale = 2)
    {
        if ($right) {
            $scale += 4;
            return round(bcmul(bcdiv($left, $right, $scale), 100, $scale), 2);
        }
        return '--';
    }
    /**
     * 邦信分快捷版通知
     * 259200秒
     */
    public function receiveData($params = [])
    {
        $time = time();
        $where = ['channel'=>$params['channel'], 'period'=>$params['period']];
        $params['created_at'] = $time;
        $res = BxfPeriod::firstOrCreate($where, $params);
        if(!$res){
            return false;
        }
        return true;
    }

    /**
     * 发送邮件队列
     */
    protected function enQueue($title, $email_arr, $html)
    {
        $fromName = "金融服务部";
        $isSingle = count($email_arr);
        $project  = '邦信分快捷版账期通知';
        $subject  = $title;
        $address  = $email_arr;
        $content  = $html;

        $data = compact('fromName', 'isSingle', 'project', 'subject', 'content', 'address');
        $redis = Redis::connection('email_queue');
        $redis->lPush('MAIL_WARNING_QUEUE', json_encode($data, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 校验/获取 查询的参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getSpreadParams()
    {
        $start_date = request()->post('start_date', '');
        $end_date   = request()->post('end_date', '');


        $date_regex = '/^\d{4}\-\d{2}\-\d{2} \d{2}\:\d{2}\:\d{2}$/';
        if (!preg_match($date_regex, $start_date)) {
            throw new \Exception('开始时间不存在或格式不正确');
        }

        if (!preg_match($date_regex, $end_date)) {
            throw new \Exception('截止时间不存在或格式不正确');
        }

        $start_date = strtotime($start_date);
        $end_date   = strtotime($end_date);
        $apikey = request()->post('apikey');
        $product_id = request()->post('product_id');
        $channel = request()->post('channel');
        $offset = request()->post('offset');
        $limit = request()->post('limit');

        return compact('start_date', 'end_date', 'apikey', 'product_id', 'channel', 'offset', 'limit');
    }
}
