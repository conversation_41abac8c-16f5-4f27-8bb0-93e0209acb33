<?php

namespace App\Http\Repository;


use App\Models\Upload;

class UploadRepository
{

    public function getNewInfo($where = [], $fields = [])
    {
        if(empty($where)){
            return false;
        }
        $res = Upload::where($where)
            ->orderBy('created_at', 'asc')
            ->first($fields);
        if(empty($res)){
            return false;
        }
        $info = $res->toArray();
        return $info;
    }

    /**
     * 更新状态
     */
    public function updateInfo($where = [])
    {
        if(empty($where)){
            return false;
        }
        $time = time();
        $data = ['updated_at' => $time];
        if(isset($where['bj']) && !empty($where['bj'])){
            $data['bj'] = 1;
        }else{
            $data['status'] = 1;
        }
        $res = Upload::where(['id'=>$where['id']])->update($data);
        if(empty($res)){
            return false;
        }
        return true;
    }
}
