<?php

namespace App\Http\Repository;

use App\Http\Controllers\StatProductController;
use App\Models\BillCost;
use App\Models\BillCustomerIncome;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\CustomerExpend;
use App\Models\MoneyRecharge;
use App\Models\Product;
use App\Models\Account;
use App\Models\BillCustomerIncomeV2;

class StatProductRepository extends StatBaseRepository
{
    /**
     * 数据统计-产品维度
     * @return mixed
     * @throws \Exception
     */
    public function statList()
    {
        try {
            $params = $this->getStatParams();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        $show_money = $this->checkUserProductAuth($params['father_id'],'money');  //是否有权限查看产品金额
        $show_cost = $this->checkUserProductAuth($params['father_id'],'cost');  //是否有权限查看产品金额
        $show_cache = $this->checkUserProductAuth($params['father_id'],'cache');  //是否有权限查看产品缓存

        $usage_data = $this->getUsage($params); //调用量
        $income_data = $this->getIncome($params);   //收入
        $cost_data = $this->getCost($params);   //成本
        $fixed_cost_data = $this->getFixedCost($params); //固定费用成本
        $customer_cost_data = $this->getCustomerAdjustCost($params);//客户成本调整
        $channel_cost_data = $this->getChannelAdjustCost($params);  //渠道成本调整(由于历史原因名字起的不符合业务语义，其实应该叫成本调整表)
        $expend_data = $this->getExpend($params);   //特殊消耗
        
        $data = $this->formatData($usage_data, $income_data, $cost_data, $expend_data,compact('show_money','show_cost','show_cache'), $fixed_cost_data, $customer_cost_data, $channel_cost_data);
        return $data;
    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $income_data
     * @param $cost_data
     * @param $expend_data
     * @param $show_money
     * @return mixed
     * @throws \Exception
     */
    private function formatData($usage_data, $income_data, $cost_data, $expend_data, $show_auth, $fixed_cost_data = [], $customer_cost_data = [], $channel_cost_data = [])
    {
        $data = [];
        foreach ($usage_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']] = $item;
            if (!$show_auth['show_money']) {
                $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['money'] = '-';
            }
            if(!$show_auth['show_cost']){
                $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['cost'] = '-';
            }
            if(!$show_auth['show_cache']){
                $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['cache'] = '-';
            }
        }
        foreach ($income_data as $item){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['number'] = $item['number'];
            $show_auth['show_money'] && $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['money'] = $item['money'];
        }
        if ($show_auth['show_cost']) {
            foreach ($cost_data as $item){
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
                $data[$item['product_id']][$customer_id][$item['apikey']][$item['operator']]['cost'] = $item['money'];
            }

            foreach ($fixed_cost_data as $item){
                if($item['money'] == 0){
                    continue;
                }
                $customer_id = 'fixed_adjust';//在前端字典已经做了该key映射
                $apikey = 'NO';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cost'] = $item['money'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['apikey'] = $apikey;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['operator'] = $operator;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['product_id'] = $item['product_id'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['success'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['total'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['valid'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cache'] = $show_auth['show_cache'] ? 0 : '-';
            }

            //客户成本调整
            foreach ($customer_cost_data as $item){
                if($item['money'] == 0){
                    continue;
                }
                $customer_id = 'customer_adjust';//在前端字典已经做了该key映射
                $apikey = 'NO';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cost'] = $item['money'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['apikey'] = $apikey;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['operator'] = $operator;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['product_id'] = $item['product_id'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['success'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['total'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['valid'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cache'] = $show_auth['show_cache'] ? 0 : '-';
            }

            //渠道成本调整
            foreach ($channel_cost_data as $item){
                if($item['money'] == 0){
                    continue;
                }
                $customer_id = 'channel_adjust';//在前端字典已经做了该key映射
                $apikey = 'NO';//在前端字典已经做了该key映射
                $operator = 'NO';//在前端字典已经做了该key映射
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cost'] = $item['money'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['apikey'] = $apikey;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['operator'] = $operator;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['product_id'] = $item['product_id'];
                $data[$item['product_id']][$customer_id][$apikey][$operator]['success'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['total'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['valid'] = 0;
                $data[$item['product_id']][$customer_id][$apikey][$operator]['cache'] = $show_auth['show_cache'] ? 0 : '-';
            }

        }
        foreach ($expend_data['add'] as $item) {
            if (!isset($data[$item['product_id']][$item['customer_id']]['特殊消耗'])) {
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] = 0;
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money'] = $show_auth['show_money'] ? 0 : '-';
            }
            $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] += $item['fee_number'];
            if($show_auth['show_money']) $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money'] += $item['money'];
        }
        foreach ($expend_data['sub'] as $item) {
            if (!isset($data[$item['product_id']][$item['customer_id']]['特殊消耗'])) {
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] = 0;
                $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money'] = $show_auth['show_money'] ? 0 : '-';
            }
            $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['number'] -= $item['fee_number'];
            if($show_auth['show_money']) $data[$item['product_id']][$item['customer_id']]['特殊消耗'][0]['money'] -= $item['money'];
        }
        return $data;
    }

    public function getFixedCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }

        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);

        $cost_data = ChannelAccountFixedFee::getChannelCostByProduct($where, $params);

        return $cost_data;
    }

    public function getCustomerAdjustCost($params){
        $where = $this->getCostWhere($params);
        return CustomerBillAdjust::getCostByProduct($where, $params);
    }

    public function getChannelAdjustCost($params){
        if(isset($params['start_date'])){
            $params['start_date'] = date('Y-m-d', strtotime($params['start_date']));
        }

        if(isset($params['end_date'])){
            $params['end_date'] = date('Y-m-d', strtotime($params['end_date']));
        }
        $where = $this->getCostWhere($params);
        $cost_data = ChannelAccountAdjust::getChannelCostByProduct($where, $params);
        return $cost_data;
    }

    public function statProductWeekList()
    {
        $params = request()->post();

        $start_date = '2021-11-18';//周日 第46周
        $day = 0;//周日
        $dijizou = 46;
        $zhou_num = 0;
        $res = [];
        for($i = 322; $i >0; $i--){
            $yushu = $day % 7;
            if($yushu == 0){//说明是周日 新的一周 计算这是第几周
                $dijizhou = $dijizou - $zhou_num;
                $zhou_num++;
            }
            $week = 7 - $yushu;//算出这是周几
            //echo date('Y-m-d', strtotime($start_date) - $day * 86400);
            $res[$dijizhou][] = date('Ymd', strtotime($start_date) - $day * 86400);

            $day++;
        }

        $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];//羽乐科技内部、售前测试
        $filter_apikey = array_column(Account::whereIn('customer_id', $filter_customer)->get()->toArray(), 'apikey');

        $path = storage_path();
        $filePath = $path."/logs/".date('Ymd').'BMY.txt';
        $father_id = 200;
        foreach($res as $key => $val){

            $e_date = current($val);
            $s_date = end($val);
            $condition = [
                'father_id' => $father_id,
                's_date' => $s_date,
                'e_date' => $e_date
            ];

            $income_data = BillCustomerIncomeV2::getBetweenDate($condition, $filter_apikey);
            $income = $income_data[0]['s_money']??0;

            $cost_data = BillCost::getBetweenDate($condition, $filter_apikey);
            $cost = $cost_data[0]['s_money']??0;

            //产品月度平账数据,分加减两部分
            $expend_data_sub = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 1]), $filter_customer);
            $expend_data_add = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 2]), $filter_customer);
            //返回全量且不重复的所有产品

            $expend_sub = $expend_data_sub[0]['s_money']??0;
            $expend_add = $expend_data_add[0]['s_money']??0;

            $show_income = bcadd($income, $expend_add, $this->degree);
            $show_income = bcsub($show_income, $expend_sub, 2);

            $rights_income = bcsub($income, $cost, $this->degree);
            $rights_income = bcadd($rights_income, $expend_add, $this->degree);
            $rights_income = bcsub($rights_income, $expend_sub, 2);

            file_put_contents($filePath, "第".$key."周\t".$s_date.'-'.$e_date."\t".$show_income."\t".$rights_income.PHP_EOL, FILE_APPEND);

        }

        dd('ok');

    }

    /**
     * 月度数据统计-产品维度
     *
     * @param $params
     */
    public function statProductMonthList($params)
    {
        //设置返回表格的表头
        $arr['result']['header']['list'] = [
            "product_type" => "产品类型",
            "product_name" => "产品名称",
            "rights_income" => "权责收入",
            "cost" => "数据成本",
            "rights_profit" => "权责毛利",
            "total_income" => "总权责收入",
            "total_cost" => "总数据成本",
            "total_profit" => "总权责毛利",
        ];

        $income_data = BillCustomerIncomeV2::getProductMonthIncome($params);//产品月度权责收入数据
        $cost_data = BillCost::getProductMonthCost($params);//产品月度数据成本
        //产品月度平账数据,分加减两部分
        $expend_data_sub = CustomerExpend::getProductMonthExpend(array_merge($params, ['type' => 1]));
        $expend_data_add = CustomerExpend::getProductMonthExpend(array_merge($params, ['type' => 2]));
        //返回全量且不重复的所有产品
        $product_arr = $this->filterDataByColumn([$income_data, $cost_data, $expend_data_sub, $expend_data_add], 'father_id');
        if(empty($product_arr)){
            $arr['result']['items'] = [];
            return $arr;
        }

        //根据想要的key从新组装数据
        $income_data_tmp = $this->addDataKey($income_data, 'father_id');
        $cost_data_tmp = $this->addDataKey($cost_data, 'father_id');
        $expend_data_sub_tmp =  $this->addDataKey($expend_data_sub, 'father_id');
        $expend_data_add_tmp = $this->addDataKey($expend_data_add, 'father_id');

        //把所有基础数据先计算出来
        $result = [];
        foreach ($product_arr as $father_id){
            $product_name = RedisCache::instance('productId_productName_mapping')->get($father_id);
            $bill_income = $income_data_tmp[$father_id]['s_money'] ?? 0; //根据账单计算出的收入
            $expend_sub_income = $expend_data_sub_tmp[$father_id]['s_money'] ?? 0;//根据平账消耗计算出的收入
            $expend_add_income = $expend_data_add_tmp[$father_id]['s_money'] ?? 0;//根据平账消耗计算出的收入
            $rights_income = bcsub(bcadd($bill_income, $expend_add_income, $this->degree), $expend_sub_income, $this->degree);

            $cost = $cost_data_tmp[$father_id]['s_money'] ?? 0;//根据账单计算出的成本
            $rights_profit = bcsub($rights_income, $cost, $this->degree);
            $result[] =   [
                "product_type" => Product::getCategoryByPid($father_id),
                "product_name" => $product_name,
                "rights_income" => $rights_income,
                "cost" => $cost,
                "rights_profit" => $rights_profit,
            ];
        }

        //根据类型组装易获取的数据
        $tmp = [];
        foreach ($result as $key => $val){
            $total_income = $tmp[$val['product_type']]['total_income'] ?? 0;
            $total_income = bcadd($val['rights_income'] ,$total_income, $this->degree);
            $tmp[$val['product_type']]['total_income'] = $total_income;

            $total_cost = $tmp[$val['product_type']]['total_cost'] ?? 0;
            $total_cost = bcadd($val['cost'] , $total_cost,$this->degree);
            $tmp[$val['product_type']]['total_cost'] = $total_cost;

            $total_profit = $tmp[$val['product_type']]['total_profit'] ?? 0;
            $total_profit = bcadd($val['rights_profit'] , $total_profit,$this->degree);
            $tmp[$val['product_type']]['total_profit'] = $total_profit;

            $row_num = $tmp[$val['product_type']]['row_num'] ?? 0;
            $tmp[$val['product_type']]['row_num'] = $row_num + 1;

            $tmp[$val['product_type']]['detail'][] = $val;
        }

        //根据类型总权责收入排序
        $total_income_sort = array_column($tmp,'total_income');
        array_multisort($total_income_sort,SORT_DESC, $tmp);

        $arr['result']['items'] = [];
        foreach ($tmp as $t_key => $t_val){
            //同类型产品按收入排序
            $detail_rights_income_sort = array_column($t_val['detail'],'rights_income');
            array_multisort($detail_rights_income_sort,SORT_DESC, $t_val['detail']);
            $row_span_arr = [];
            foreach ($t_val['detail'] as $new_val){
                $item = [
                    'product_type' => $t_key,
                    'product_name' => $new_val['product_name'],
                    'rights_income' => number_format($new_val['rights_income'], 2),
                    'cost' => number_format($new_val['cost'], 2),
                    'rights_profit' => number_format($new_val['rights_profit'], 2),
                ];
                if( $t_val['row_num'] > 1 && !isset($row_span_arr[$t_key]) ){
                    $item['row_num'] = $t_val['row_num'];
                    $item['total_income'] = number_format($t_val['total_income'], 2);
                    $item['total_cost'] = number_format($t_val['total_cost'], 2);
                    $item['total_profit'] = number_format($t_val['total_profit'], 2);
                    $arr['result']['items'][] = $item;
                    $row_span_arr[$t_key] = true;
                }else if($t_val['row_num'] == 1){
                    $item['row_num'] = 1;
                    $item['total_income'] = number_format($new_val['rights_income'], 2);
                    $item['total_cost'] = number_format($new_val['cost'], 2);
                    $item['total_profit'] = number_format($new_val['rights_profit'],2);
                    $arr['result']['items'][] = $item;
                }else{
                    $arr['result']['items'][] = $item;
                }
            }

        }

        return $arr;
    }


    /**
     * 月度数据统计-按月所有数据
     *
     * @param $params
     */
    public function statMonthList($params)
    {
        //设置返回表格的表头
        $arr['result']['header']['list'] = [
            "month" => "月份",
            "rights_income" => "权责收入",
            "cost" => "数据成本",
            "cash_income" => "现金收入",
        ];

        $income_data = BillCustomerIncomeV2::getMonthIncome($params);//每月度权责收入数据
        $cost_data = BillCost::getMonthCost($params);//每月度数据成本
        //每月度平账数据,分加减两部分
        $expend_data_sub = CustomerExpend::getMonthExpend(array_merge($params, ['type' => 1]));
        $expend_data_add = CustomerExpend::getMonthExpend(array_merge($params, ['type' => 2]));
        $remit_data = MoneyRecharge::getMonthRemit($params);//每月现金收入
        //返回全量且不重复的所有月份
        $month_arr = $this->filterDataByColumn([$income_data, $cost_data, $expend_data_sub, $expend_data_add, $remit_data], 's_month');
        if(empty($month_arr)){
            $arr['result']['items'] = [];
            return $arr;
        }

        //根据想要的key从新组装数据
        $income_data_tmp = $this->addDataKey($income_data, 's_month');
        $cost_data_tmp = $this->addDataKey($cost_data, 's_month');
        $expend_data_sub_tmp = $this->addDataKey($expend_data_sub, 's_month');
        $expend_data_add_tmp = $this->addDataKey($expend_data_add, 's_month');
        $remit_data_tmp = $this->addDataKey($remit_data, 's_month');

        $total_rights_income = 0;
        $total_cost = 0;
        $total_cash_income = 0;
        foreach($month_arr as $month){
            $bill_income = $income_data_tmp[$month]['s_money'] ?? 0;//根据账单计算出的收入
            $expend_sub_income = $expend_data_sub_tmp[$month]['s_money'] ?? 0;//根据平账消耗计算出的收入
            $expend_add_income = $expend_data_add_tmp[$month]['s_money'] ?? 0;//根据平账消耗计算出的收入
            $rights_income = bcsub(bcadd($bill_income, $expend_add_income, $this->degree), $expend_sub_income, $this->degree);

            $cost = $cost_data_tmp[$month]['s_money'] ?? 0;//根据账单计算出的成本
            $cash_income = $remit_data_tmp[$month]['s_money'] ?? 0;//根据打款单计算出的现金收入

            $total_rights_income = bcadd($total_rights_income, $rights_income, $this->degree);
            $total_cost = bcadd($total_cost, $cost, $this->degree);
            $total_cash_income = bcadd($total_cash_income, $cash_income, $this->degree);
            $arr['result']['items'][] =
                [
                    "month" => $month,
                    'rights_income_sort' => $rights_income,
                    "rights_income" => number_format($rights_income, 2),
                    "cost" => number_format($cost, 2),
                    "cash_income" => number_format($cash_income, 2),
                ];
        }

        if(!empty($month_arr)){
            $month_sort = array_column($arr['result']['items'], 'month');
            array_multisort($month_sort,SORT_DESC, $arr['result']['items']);

            array_unshift($arr['result']['items'], [
                "month" => '总计',
                "rights_income" => number_format($total_rights_income, 2),
                "cost" => number_format($total_cost, 2),
                "cash_income" => number_format($total_cash_income, 2),
            ]);
        }

        return $arr;
    }


    /**
     * 季度数据统计-各产品各季度权责收入及毛利数据统计
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function statProductQuarterList($params)
    {
        //根据季度的起始动态返回表头
        $arr['result']['header']['list'] = $this->getQLables($params['start_q'], $params['end_q']);
        $common_where = ['year'=>$params['year'], 'father_id'=>$params['father_id']];
        //产品某年各个月权责收入数据
        $month_income_data = BillCustomerIncomeV2::getProductMonthIncome($common_where);
        //产品某年各个月月度平账数据,分加减两部分
        $month_expend_data_sub = CustomerExpend::getProductMonthExpend(array_merge($common_where, ['type' => 1]));
        $month_expend_data_add = CustomerExpend::getProductMonthExpend(array_merge($common_where, ['type' => 2]));
        $month_cost_data = BillCost::getProductMonthCost($common_where);//产品月度数据成本
        //返回全量且不重复的所有产品
        $product_arr = $this->filterDataByColumn([$month_income_data, $month_expend_data_sub, $month_expend_data_add, $month_cost_data], 'father_id');
        if(empty($product_arr)){
            $arr['result']['items'] = [];
            return $arr;
        }

        //根据产品father_id组装对应的季度数据
        $income_data_tmp = $this->formatQuarter($month_income_data, 'rights_income');
        $expend_data_sub_tmp =  $this->formatQuarter($month_expend_data_sub, 'rights_income');
        $expend_data_add_tmp = $this->formatQuarter($month_expend_data_add, 'rights_income');
        $cost_data_tmp = $this->formatQuarter($month_cost_data, 'cost');

        $result = [];
        foreach($product_arr as $father_id){
            $product_name = RedisCache::instance('productId_productName_mapping')->get($father_id);
            $show_field = $this->combineField($params, $father_id, $income_data_tmp, $expend_data_sub_tmp, $expend_data_add_tmp, $cost_data_tmp);
            $common_field = [
                "product_type" => Product::getCategoryByPid($father_id),
                "product_name" => $product_name,
            ];
            $result[] = array_merge($common_field, $show_field);
        }

        //根据类型组装易获取的数据
        $tmp = [];//相当于为每个类型添加一行总计行记录
        foreach ($result as $key => $val) {
            //某类型产品总权责收入(所有查看季度)
            $total_type_income = $tmp[$val['product_type']]['total_rights_income'] ?? 0;
            $tmp[$val['product_type']]['total_rights_income'] = bcadd($total_type_income, $val['total_rights_income'], $this->degree);

            //某类型产品总数据成本(所有查看季度)
            $total_type_cost = $tmp[$val['product_type']]['total_cost'] ?? 0;
            $tmp[$val['product_type']]['total_cost'] = bcadd($total_type_cost, $val['total_cost'], $this->degree);

            //某类型产品总权责毛利(所有查看季度)
            $total_type_rights_profit = $tmp[$val['product_type']]['total_rights_profit'] ?? 0;
            $tmp[$val['product_type']]['total_rights_profit'] = bcadd($total_type_rights_profit, $val['total_rights_profit'], $this->degree);

            for($q=$params['start_q']; $q<=$params['end_q']; $q++){
                //某类型产品某季度总权责收入
                $total_type_q_income = $tmp[$val['product_type']]['q'.$q.'_rights_income'] ?? 0;
                $total_type_q_income = bcadd($val['q'.$q.'_rights_income'] ,$total_type_q_income, $this->degree);
                $tmp[$val['product_type']]['q'.$q.'_rights_income'] = $total_type_q_income;

                //某类型产品某季度总数据成本
                $total_type_q_cost = $tmp[$val['product_type']]['q'.$q.'_cost'] ?? 0;
                $total_type_q_cost = bcadd($val['q'.$q.'_cost'] ,$total_type_q_cost, $this->degree);
                $tmp[$val['product_type']]['q'.$q.'_cost'] = $total_type_q_cost;

                //某类型产品某季度权责毛利
                $total_type_q_rights_profit = $tmp[$val['product_type']]['q'.$q.'_rights_profit'] ?? 0;
                $total_type_q_rights_profit = bcadd($val['q'.$q.'_rights_profit'] ,$total_type_q_rights_profit, $this->degree);
                $tmp[$val['product_type']]['q'.$q.'_rights_profit'] = $total_type_q_rights_profit;
            }

            $row_num = $tmp[$val['product_type']]['row_num'] ?? 0;
            $tmp[$val['product_type']]['row_num'] = $row_num + 1;

            $tmp[$val['product_type']]['detail'][] = $val;
        }

        //根据类型总权责收入排序
        $total_rights_income_sort = array_column($tmp,'total_rights_income');
        array_multisort($total_rights_income_sort,SORT_DESC, $tmp);
        $arr['result']['items'] = [];
        foreach ($tmp as $t_key => $t_val) {
            $item = [
                'product_type' => $t_key,
                'product_name' => '总计',
            ];

            for($q=$params['start_q']; $q<=$params['end_q']; $q++){
                if($t_val['q'.$q.'_rights_income'] > 0){
                    $q_profit_rate = bcdiv(bcmul($t_val['q'.$q.'_rights_profit'], 100, $this->degree), $t_val['q'.$q.'_rights_income'], $this->degree);
                }else{
                    $q_profit_rate = 0.00;
                }
                $item_q = [
                    'q'.$q.'_rights_income' => number_format($t_val['q'.$q.'_rights_income'], 2),
                    'q'.$q.'_cost' => number_format($t_val['q'.$q.'_cost'], 2),
                    'q'.$q.'_rights_profit' => number_format($t_val['q'.$q.'_rights_profit'], 2),
                    'q'.$q.'_profit_rate' => number_format($q_profit_rate, 2) . '%',
                ];
                $item = array_merge($item, $item_q);
            }

            if($t_val['total_rights_income'] > 0){
                $t_profit_rate = bcdiv(bcmul($t_val['total_rights_profit'], 100, $this->degree), $t_val['total_rights_income'], $this->degree);
            }else{
                $t_profit_rate = 0.00;
            }

           $item_end = [
               'total_rights_income' => number_format($t_val['total_rights_income'], 2),
               'total_cost' => number_format($t_val['total_cost'], 2),
               'total_rights_profit' => number_format($t_val['total_rights_profit'], 2),
               'total_profit_rate' => number_format($t_profit_rate, 2) . '%',
           ];
           $item = array_merge($item, $item_end);
           $item['row_num'] = 1 + $t_val['row_num'];
           $arr['result']['items'][] = $item;

           //同类型产品按收入排序
           $detail_rights_income_sort = array_column($t_val['detail'],'total_rights_income');
           array_multisort($detail_rights_income_sort,SORT_DESC, $t_val['detail']);
           $row_span_arr = [];
           foreach ($t_val['detail'] as $new_val){
               $new_item = [
                   'product_name' => $new_val['product_name'],
               ];
               for($q=$params['start_q']; $q<=$params['end_q']; $q++){
                   if($new_val['q'.$q.'_rights_income'] > 0){
                       $new_q_profit_rate = bcdiv(bcmul($new_val['q'.$q.'_rights_profit'], 100,$this->degree), $new_val['q'.$q.'_rights_income'], $this->degree);
                   }else{
                       $new_q_profit_rate = 0.00;
                   }
                   $new_item_q = [
                       'q'.$q.'_rights_income' => number_format($new_val['q'.$q.'_rights_income'], 2),
                       'q'.$q.'_cost' => number_format($new_val['q'.$q.'_cost'], 2),
                       'q'.$q.'_rights_profit' => number_format($new_val['q'.$q.'_rights_profit'],2),
                       'q'.$q.'_profit_rate' => number_format($q_profit_rate, 2) . '%',
                   ];
                   $new_item = array_merge($new_item, $new_item_q);
               }
               if($new_val['total_rights_income'] > 0){
                   $new_t_profit_rate = bcdiv(bcmul($new_val['total_rights_profit'], 100, $this->degree), $new_val['total_rights_income'], $this->degree);
               }else{
                   $new_t_profit_rate = 0.00;
               }
               $new_item_end = [
                   'total_rights_income' => number_format($new_val['total_rights_income'], 2),
                   'total_cost' => number_format($new_val['total_cost'], 2),
                   'total_rights_profit' => number_format($new_val['total_rights_profit'], 2),
                   'total_profit_rate' => number_format($new_t_profit_rate, 2) . '%',
               ];
               $new_item = array_merge($new_item, $new_item_end);

               $arr['result']['items'][] = $new_item;
           }

       }

       return $arr;
   }

   /**
    * 根据季度的起始动态返回表头
    *
    * @param $start
    * @param $end
    * @return string[]
    */
    public function getQLables($start, $end)
    {
        $lables = [
            'product_type' => '产品类型',
            'product_name' => '产品名称',
        ];
        for($q=$start; $q<=$end; $q++){
            $item = [
                'q'.$q.'_rights_income' => 'Q'.$q.'权责收入',
                'q'.$q.'_cost' => 'Q'.$q.'数据成本',
                'q'.$q.'_rights_profit' => 'Q'.$q.'权责毛利',
                'q'.$q.'_profit_rate' => 'Q'.$q.'毛利率',
            ];
            $lables = array_merge($lables, $item);
        }

        $labes_end = [
            'total_rights_income' => '总计权责收入',
            'total_cost' => '总计数据成本',
            'total_rights_profit' => '总计权责毛利',
            'total_profit_rate' => '总计毛利率',
        ];
        $lables = array_merge($lables, $labes_end);
        return $lables;
    }

    /**
     * 组装季度数据
     * @param $data
     * @param string $column
     * @return array
     */
    public function formatQuarter($data, $column = '')
    {
        if(empty($data) || empty($column)){
            return [];
        }
        $tmp = [];
        foreach($data as $key => $val){
            $q = $this->getQValue($val['s_month']);//获取季度值
            $q_value = $tmp[$val['father_id']]['q'.$q.'_'.$column] ?? 0;
            $tmp[$val['father_id']]['q'.$q.'_'.$column] = bcadd($q_value, $val['s_money'], $this->degree);
        }

        return $tmp;
    }

    public function combineField($params, $father_id, $income_data_tmp, $expend_data_sub_tmp, $expend_data_add_tmp, $cost_data_tmp)
    {
        $start = $params['start_q'];
        $end = $params['end_q'];
        $tmp = [];
        $total_rights_income = 0;
        $total_cost = 0;
        $total_rights_profit = 0;
        for($q=$start; $q<=$end; $q++){
            $q_bill_income = $income_data_tmp[$father_id]['q'.$q.'_rights_income'] ?? 0;
            $q_expend_sub_income = $expend_data_sub_tmp[$father_id]['q'.$q.'_rights_income'] ?? 0;
            $q_expend_add_income = $expend_data_add_tmp[$father_id]['q'.$q.'_rights_income'] ?? 0;
            $q_rights_income = bcsub(bcadd($q_bill_income, $q_expend_add_income, $this->degree), $q_expend_sub_income, $this->degree);

            $q_cost = $cost_data_tmp[$father_id]['q'.$q.'_cost'] ?? 0;//q1季度某产品的数据成本
            $q_rights_profit = bcsub($q_rights_income, $q_cost, $this->degree);//q1季度某产品的权责毛利
            if($q_rights_income > 0){//q1季度某产品的毛利率
                $q_profit_rate = bcdiv(bcmul($q_rights_profit, 100, $this->degree), $q_rights_income, $this->degree);
            }else{
                $q_profit_rate = 0.00;
            }

            $item = [
                'q'.$q.'_rights_income' => $q_rights_income,
                'q'.$q.'_cost' => $q_cost,
                'q'.$q.'_rights_profit' => $q_rights_profit,
                'q'.$q.'_profit_rate' => $q_profit_rate,
            ];

            $tmp = array_merge($tmp, $item);
            $total_rights_income = bcadd($total_rights_income, $q_rights_income, $this->degree);
            $total_cost = bcadd($total_cost, $q_cost, $this->degree);
            $total_rights_profit = bcadd($total_rights_profit, $q_rights_profit, $this->degree);
        }

        if($total_rights_income > 0 ){
            $total_profit_rate = bcdiv($total_rights_profit, $total_rights_income, $this->degree);
        }else{
            $total_profit_rate = 0.00;
        }
        $end_lables = [
            'total_rights_income' => $total_rights_income,
            'total_cost' => $total_cost,
            'total_rights_profit' => $total_rights_profit,
            'total_profit_rate' => $total_profit_rate,
        ];

        $tmp = array_merge($tmp, $end_lables);

        return $tmp;
    }

    /**
     * 获取季度值
     * @param $month
     * @return int
     */
    public function getQValue($month)
    {
        $m = (int)substr($month, -2);
        if(in_array($m, [1, 2, 3])){
            return 1;
        }
        if(in_array($m, [4, 5, 6])){
            return 2;
        }
        if(in_array($m, [7, 8, 9])){
            return 3;
        }
        if(in_array($m, [10, 11, 12])){
            return 4;
        }

    }


    /**
     * 季度数据统计-各季度权责收入及现金收入等统计
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function statQuarterList($params)
    {
        //设置返回表格的表头
        $arr['result']['header']['list'] = [
            "quarter" => "季度",
            "rights_income" => "权责收入",
            "cost" => "数据成本",
            "cash_income" => "现金收入",
        ];
        $common_where = ['year'=>$params['year']];
        $month_income_data = BillCustomerIncomeV2::getMonthIncome($common_where);//每月度权责收入数据
        $month_cost_data = BillCost::getMonthCost($common_where);//每月度数据成本
        //每月度平账数据,分加减两部分
        $month_expend_data_sub = CustomerExpend::getMonthExpend(array_merge($common_where, ['type' => 1]));
        $month_expend_data_add = CustomerExpend::getMonthExpend(array_merge($common_where, ['type' => 2]));
        $month_remit_data = MoneyRecharge::getMonthRemit($common_where);//每月现金收入

        //返回全量且不重复的所有月份
        $month_arr = $this->filterDataByColumn([$month_income_data, $month_cost_data, $month_expend_data_sub, $month_expend_data_add, $month_remit_data], 's_month');
        if(empty($month_arr)){
            $arr['result']['items'] = [];
            return $arr;
        }

        //根据想要的key从新组装数据
        $income_data_tmp = $this->addDataKey($month_income_data, 's_month');
        $cost_data_tmp = $this->addDataKey($month_cost_data, 's_month');
        $expend_data_sub_tmp = $this->addDataKey($month_expend_data_sub, 's_month');
        $expend_data_add_tmp = $this->addDataKey($month_expend_data_add, 's_month');
        $remit_data_tmp = $this->addDataKey($month_remit_data, 's_month');

        $total_rights_income = 0;
        $total_cost = 0;
        $total_cash_income = 0;
        $result = [];
        foreach($month_arr as $month){
            $bill_income = $income_data_tmp[$month]['s_money'] ?? 0;//根据账单计算出的收入
            $expend_sub_income = $expend_data_sub_tmp[$month]['s_money'] ?? 0;//根据平账消耗计算出的收入
            $expend_add_income = $expend_data_add_tmp[$month]['s_money'] ?? 0;//根据平账消耗计算出的收入
            $rights_income = bcsub(bcadd($bill_income, $expend_add_income, $this->degree), $expend_sub_income, $this->degree);

            $cost = $cost_data_tmp[$month]['s_money'] ?? 0;//根据账单计算出的成本
            $cash_income = $remit_data_tmp[$month]['s_money'] ?? 0;//根据打款单计算出的现金收入

            $total_rights_income = bcadd($total_rights_income, $rights_income, $this->degree);
            $total_cost = bcadd($total_cost, $cost, $this->degree);
            $total_cash_income = bcadd($total_cash_income, $cash_income, $this->degree);
            $result[] =
                [
                    "month" => $month,
                    "rights_income" => $rights_income,
                    "cost" => $cost,
                    "cash_income" => $cash_income,
                ];
        }

        //根据月数据分组成季度数据
        $tmp = [];
        foreach($result as $val){
            $q = $this->getQValue($val['month']);//根据月份获取季度值
            $q_rights_income = $tmp[$q]['rights_income'] ?? 0;
            $tmp[$q]['rights_income'] = bcadd($q_rights_income, $val['rights_income'], $this->degree);

            $q_cost = $tmp[$q]['cost'] ?? 0;
            $tmp[$q]['cost'] = bcadd($q_cost, $val['cost'], $this->degree);

            $q_cash_income = $tmp[$q]['cash_income'] ?? 0;
            $tmp[$q]['cash_income'] = bcadd($q_cash_income, $val['cash_income'], $this->degree);
        }

        $arr['result']['items'] = [];
        foreach($tmp as $t_key => $t_val){
            $arr['result']['items'][] = [
                "quarter" => 'Q'.$t_key,
                "rights_income" => number_format($t_val['rights_income'], 2),
                "cost" => number_format($t_val['cost'], 2),
                "cash_income" => number_format($t_val['cash_income'], 2),
            ];
        }

        $quarter_sort = array_column($arr['result']['items'], 'quarter');
        array_multisort($quarter_sort,SORT_ASC, $arr['result']['items']);
        array_unshift($arr['result']['items'], [
            "quarter" => '总计',
            "rights_income" => number_format($total_rights_income, 2),
            "cost" => number_format($total_cost, 2),
            "cash_income" => number_format($total_cash_income, 2),
        ]);

        return $arr;
    }






}