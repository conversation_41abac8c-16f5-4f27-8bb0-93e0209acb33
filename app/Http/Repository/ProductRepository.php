<?php

namespace App\Http\Repository;

use App\Models\Product;
use App\Providers\RedisCache\RedisCache;
use function foo\func;

/**
 * Class ProductRepository 产品配置代码仓库
 * @package App\Http\Repository
 */
class ProductRepository extends PublicRepository
{
	/**
	 * 获取产品列表
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/19 14:41
	 *
	 * @return array
	 */
	public function getInfo()
	{
		return Product::select([
			'product_id',
			'product_name',
			'product_enname',
			'product_key',
			'father_id',
			'admin',
			'mark',
			'create_at',
			'update_at',
			'status',
			'sort',
			'type',
		])
					  ->where(function ($query) {
						  $product_id = $this->request->get('product_id', '');
						  if (strlen($product_id) > 0) {
							  $query->where('product_id', '=', $product_id);
						  }
						  $status = $this->request->get('status', '');
						  if (strlen($status) > 0) {
							  $query->where('status', '=', $status);
						  }
						  $type = $this->request->get('type', '');
						  if (strlen($type) > 0) {
							  $query->whereIn('type', explode(',', $type));
						  }
						  $father_id = $this->request->get('father_id', '');
						  if (strlen($father_id) > 0) {
							  $query->where('father_id', $father_id);
						  }
					  })
					  ->orderBy('sort', 'desc')
					  ->get()
					  ->toArray();
	}
	
	/**
	 * 创建产品
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/25 16:30
	 *
	 * @return integer
	 */
	public function add()
	{
		//获取数据并校验
		$result = $this->getAddData();
		if (!is_array($result)) {
			return $result;
		}
		
		//创建数据
	}
	
	/**
	 * 获取创建产品的POST数据，并进行校验
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/25 16:32
	 *
	 * @return integer|array 如果出现错误，则返回int类型
	 */
	protected function getAddData()
	{
		//获取产品ID、产品名称（用于校验唯一性）
		$productInfo  = RedisCache::instance('productId_productName_mapping')
								  ->get();
		$productIds   = array_keys($productInfo);
		$productNames = array_values($productInfo);
		
		//产品类型
		$type = $this->request->post('type');
		if (!$type) {
			return 14001;
		}
		if (!in_array($type, config('params.product_type'))) {
			return 14002;
		}
		
		//父级产品ID
		$father_id = $this->request->post('father_id', 0);
		
		
		//产品ID
		$product_id = $this->request->post('product_id');
		if (!$product_id) {
			return 14003;
		}
		if (!filter_var($product_id, FILTER_SANITIZE_NUMBER_INT)) {
			return 14004;
		}
		if (false === array_search($product_id, $productIds)) {
			return 14005;
		}
		
		//产品名称
		$product_name = $this->request->post('product_name');
		if (!$product_name) {
			return 14006;
		}
		if (false === array_search($product_name, $productNames)) {
			return 14007;
		}
		
		//英文名称
		$product_enname = $this->request->post('product_enname', '');
		
		//产品key
		$product_key = $this->request->post('product_key');
		if (!$product_key) {
			return 14008;
		}
		
		//排序号
		$sort = $this->request->post('sort', 0);
		
		//备注
		$mark = $this->request->post('mark', '');
		
		return compact('product_id', 'product_name', 'product_key', 'type', 'father_id', 'product_enname', 'sort', 'mark');
	}
	
	
}