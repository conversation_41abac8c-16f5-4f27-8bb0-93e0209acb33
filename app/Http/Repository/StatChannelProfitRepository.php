<?php

namespace App\Http\Repository;

use App\Define\StatDefine;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\StatisticsInterfaceUsage;
use App\Models\UserChannelConfig;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Income\MainRepository;
use Illuminate\Support\Facades\DB;


class StatChannelProfitRepository extends StatBaseRepository
{

    protected $mainRep = null;
    protected $subToFatherMap = [];//全量的子、父产品map
    protected $searchChannelIds = [];
    protected $searchProductIds = [];
    protected $searchCustomerIds = [];
    protected $searchSourceIds = [];
    protected $startDate = '';
    protected $endDate = '';
    //渠道类别
    protected $channelTypeMap = [];
    //渠道类别和渠道id关系
    protected $channelIdToTypeMap = [];
    //用户拥有查看权限的渠道
    protected $authChannelList = [];


    /**
     * 数据统计-渠道收入统计
     * @return mixed
     * @throws \Exception
     */
    public function statChannelProfit()
    {
        try {
            $params = $this->getStatParams();
            $this->startDate = $params['start_date'];
            $this->endDate = $params['end_date'];
            //初始化渠道类别和渠道映射关系
            $this->initChannelTypeMap();
            //获取对应渠道权限配置
            $this->getAuthChannelList();

            $this->mainRep = new MainRepository();
            //根据产品视角，获取全量的所有的子、父产品map
            $this->subToFatherMap = $this->mainRep->getSubPidsByFatherIdsWithAngle([], StatDefine::PM_ANGLE);

            //根据查询条件获取要查询的渠道
            $this->searchChannelIds = $this->getChannelWhere($params);

            //根据查询条件获取要查询的产品视角产品
            $this->searchProductIds = $this->getProductWhere($params);

            //获取要查询的来源
            $this->searchCustomerIds = $this->getCustomerWhere($params);

            //获取要查询的来源
            $this->searchSourceIds = $this->getSourceWhere($params);

            //成本数据
            $cost_data = $this->getChannelInterfaceProductCost();

            //由于自有渠道没有计费配置，无法从bill_cost查，因此对自有渠道单独处理，其查得量默认为计费量
            $self_valid_data = $this->getSelfChannelInterfaceProduct();

            //用量数据
            $usage_data = $this->getChannelInterfaceProductUsage();

            //收入数据
            $income_data = $this->getProductCustomerIncome();

            $data = $this->formatData($cost_data, $self_valid_data, $usage_data, $income_data);

            return $data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage().';'.$e->getFile().';'.$e->getLine());
        }
    }

    private function getChannelInterfaceProductCost(){
        //过滤客户成本调整、过滤瀚境在网时长的携号转网
        $params = ['is_query_month_data' => 0, 'filter_customer_adjust' => 1, 'filter_hanJing_switchNetWork' => 1];
        if(!empty($this->searchSourceIds)){
            $params['source'] = $this->searchSourceIds;
        }
        $res = $this->mainRep->getBaseCost(StatDefine::COST_DIMENSION_CHANNEL_PRODUCT_CUSTOMER_SOURCE, $this->startDate, $this->endDate, [], [], [], [], $params);
        if($res['status'] != 0){
            throw new \Exception('成本信息获取异常:'.$res['msg']);
        }

        return $res['data'];
    }

    private function getSelfChannelInterfaceProduct(){
        $where = [
            ['date', '>=', $this->startDate],
            ['date', '<=', $this->endDate],
        ];

        $iids = ChannelInterface::getInterfaceByChannelIds([124]);
        $iids = array_column($iids, 'id');
        $where['in']['interface_id'] = $iids;

        if(!empty($this->searchSourceIds)){
            $where['in']['source'] = $this->searchSourceIds;
        }
        $list = StatisticsInterfaceUsage::getListGroupByApikeyPidIid($where);

        $result = [];
        foreach ($list as $item){
            $interface_id = $item['interface_id'];
            $product_id = $item['product_id'];
            $apikey = $item['apikey'];
            $source = $item['source'];
            $valid = $item['valid_num'];
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($interface_id);
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);

            //202的排除携号转网
            //if($product_id == 202  && $interface_id == 456){
            if($interface_id == 456){
                continue;
            }

            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
            //邦信分自有渠道不直接产生收入、邦秒验会
            if($father_id != 200){
                continue;
            }

            $result[] = [
                'channel_id' => $channel_id,
                'product_id' => $product_id,
                'customer_id' => $customer_id,
                'source' => $source,
                'cost' => 0,
                'cost_original' => 0,
                'number' => $valid,
            ];
        }

        return $result;
    }

    private function getChannelInterfaceProductUsage(){
        $where = [
            ['date', '>=', $this->startDate],
            ['date', '<=', $this->endDate],
        ];

        if(!empty($this->searchSourceIds)){
            $where['in'] = ['source' => $this->searchSourceIds];
        }
        $list = StatisticsInterfaceUsage::getListGroupByApikeyPidIid($where);

        $result = [];
        foreach ($list as $item){
            $interface_id = $item['interface_id'];
            $product_id = $item['product_id'];
            $apikey = $item['apikey'];
            //$source = $item['source'];
            $total = $item['total_num'];
            $success = $item['success_num'];
            $valid = $item['valid_num'];
            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($interface_id);
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
            $index = $channel_id.'_'.$product_id.'_'.$customer_id;
            if(isset($result[$index])){
                $result[$index]['total'] += $total;
                $result[$index]['success'] += $success;
                $result[$index]['valid'] += $valid;
            }else{
                $result[$index]['total'] = $total;
                $result[$index]['success'] = $success;
                $result[$index]['valid'] = $valid;
            }
        }

        return $result;
    }

    private function getProductCustomerIncome(){
        $params = ['is_query_month_data' => 0];
        if(!empty($this->searchSourceIds)){
            $params['source'] = $this->searchSourceIds;
        }

        $res = $this->mainRep->getBaseIncome(StatDefine::INCOME_DIMENSION_PRODUCT_CUSTOMER_OP_SOURCE, $this->startDate, $this->endDate, [], [], [], [], $params);
        if($res['status'] != 0){
            throw new \Exception('收入信息获取异常:'.$res['msg']);
        }

        return $res['data'];
    }

    /**
     * 组装最终数据
     * @param $usage_data
     * @param $cost_data
     * @param $show_money
     * @return mixed
     * @throws \Exception
     */
    private function formatData($cost_data, $self_valid_data, $usage_data, $income_data)
    {
        $product_customer_income = [];//产品、客户维度收入
        $product_customer_cost = [];//产品、客户维度成本
        $data = [];
        foreach ($income_data as $item) {
            $product_id = $item['product_id'];
            $customer_id = $item['customer_id'];
            $money = $item['income'];
            $money_finance = $item['income_finance'];

            if(($item['source'] == 1 && in_array($product_id, [41001, 41002, 41012]))
                || ($item['source'] == 1 && $product_id > 70000 && $product_id < 79999)
                || ($item['source'] == 1 && $item['operator'] == 'ZYCMCC')
            ){
                $money_original = $item['income_original'];

            }else{
                $money_original = $item['income'];
            }

            $index = $product_id.'_'.$customer_id;
            if(isset($product_customer_income[$index])){
                $product_customer_income[$index]['money'] = bcadd($product_customer_income[$index]['money'], $money, 6);
                $product_customer_income[$index]['money_finance'] = bcadd($product_customer_income[$index]['money_finance'], $money_finance, 6);
                $product_customer_income[$index]['money_original'] = bcadd($product_customer_income[$index]['money_original'], $money_original, 6);
            }else{
                $product_customer_income[$index]['money'] = $money;
                $product_customer_income[$index]['money_finance'] = $money_finance;
                $product_customer_income[$index]['money_original'] = $money_original;
            }

        }
        unset($income_data);

        $cost_data = array_merge($cost_data, $self_valid_data);
        $tmp_cost_data = [];
        foreach ($cost_data as $item){
            $channel_id = $item['channel_id'];
            $product_id = $item['product_id'];
            $customer_id = $item['customer_id'];
            $cost = $item['cost'];
            $number = $item['number'];

            if(in_array($channel_id, [501,502,503])){
                // 三数\诸天\雄源 排除
                continue;
            }

            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id) ?: $product_id;
            if($channel_id>100 && in_array($father_id, [210, 1000]) && !in_array($channel_id, [501,502,503])){
                //邦信分产品、邦秒验渠道
                continue;
            }

            //朴道侧号码融、前筛升级版产品、移动02产品成本使用原始成本，其他情况
            if(($item['source'] == 1 && in_array($product_id, [41001, 41002, 41012]))
                || ($item['source'] == 1 && $product_id > 70000 && $product_id < 79999)
                || ($item['source'] == 1 && $channel_id == 41)
            ){
                $cost_original = $item['cost_original'];

            }else{
                $cost_original = $item['cost'];
            }

            $index = $product_id.'_'.$customer_id;
            if(isset($product_customer_cost[$index])){
                $product_customer_cost[$index]['number'] += $number;
                $product_customer_cost[$index]['cost'] = bcadd($product_customer_cost[$index]['cost'], $cost, 6);
                $product_customer_cost[$index]['cost_original'] = bcadd($product_customer_cost[$index]['cost_original'], $cost_original, 6);
            }else{
                $product_customer_cost[$index]['number'] = $number;
                $product_customer_cost[$index]['cost'] = $cost;
                $product_customer_cost[$index]['cost_original'] = $cost_original;
            }

            $tmp_index = $channel_id.'_'.$product_id.'_'.$customer_id;
            if(isset($tmp_cost_data[$tmp_index])){
                $tmp_cost_data[$tmp_index]['number'] += $number;
                $tmp_cost_data[$tmp_index]['cost'] = bcadd($tmp_cost_data[$tmp_index]['cost'], $cost, 6);
                $tmp_cost_data[$tmp_index]['cost_original'] = bcadd($tmp_cost_data[$tmp_index]['cost_original'], $cost_original, 6);
            }else{
                $tmp_cost_data[$tmp_index]['number'] = $number;
                $tmp_cost_data[$tmp_index]['cost'] = $cost;
                $tmp_cost_data[$tmp_index]['cost_original'] = $cost_original;
            }

        }
        unset($self_valid_data);
        unset($cost_data);

        foreach ($tmp_cost_data as $key => $item){
            $arr = explode('_', $key);
            $channel_id = $arr[0] ?? 0;
            $product_id = $arr[1] ?? 0;
            $customer_id = $arr[2] ?? '';

            if(!empty($this->searchChannelIds) && !in_array($channel_id, $this->searchChannelIds)){
                continue;
            }

            if(!empty($this->searchProductIds) && !in_array($product_id, $this->searchProductIds)){
                continue;
            }

            if(!empty($this->searchCustomerIds) && !in_array($customer_id, $this->searchCustomerIds)){
                continue;
            }

            if(!in_array($channel_id, $this->authChannelList)){
                continue;
            }

            $father_id = $this->subToFatherMap[$product_id] ?? $product_id;

            $number = $item['number'];//渠道、产品、客户成本计费量
            $cost = $item['cost'];//渠道、产品、客户成本计费金额
            $cost_original = $item['cost_original'];//渠道、产品、客户成本计费金额(原始金额)

            $index = $product_id.'_'.$customer_id;
            $product_income_money = $product_customer_income[$index]['money'] ?? 0;//产品、客户对应收入金额
            $product_income_money_original = $product_customer_income[$index]['money_original'] ?? 0;//产品、客户对应收入金额(原始金额)
            $product_cost_number = $product_customer_cost[$index]['number'] ?? 0;//产品、客户对应总成本计费量
            $product_cost_money = $product_customer_cost[$index]['cost'] ?? 0;//产品、客户对应总成本计费金额
            $product_cost_original_money = $product_customer_cost[$index]['cost_original'] ?? 0;//产品、客户对应总成本计费金额(原始金额)

            if($product_cost_number <= 0){
                #todo 用成本金额做占比存在一个问题是用cost还是cost_original，这里用cost
                if($product_cost_money <= 0){
                    $ratio = 1;
                }else{
                    $ratio = bcdiv($cost, $product_cost_money, 6);
                }

            }else{
                $ratio = bcdiv($number, $product_cost_number, 6);
            }

            $money = bcmul($product_income_money, $ratio, 6);//渠道、产品、客户对应收入
            $money_original = bcmul($product_income_money_original, $ratio, 6);//渠道、产品、客户对应收入(原始金额)

            $total = $usage_data[$key]['total'] ?? 0;
            $success = $usage_data[$key]['success'] ?? 0;
            $valid = $usage_data[$key]['valid'] ?? 0;

            if(!isset($data[$channel_id][$father_id][$product_id][$customer_id])){
                $data[$channel_id][$father_id][$product_id][$customer_id] = $this->getTpl();
                $data[$channel_id][$father_id][$product_id][$customer_id]['total'] = $total;
                $data[$channel_id][$father_id][$product_id][$customer_id]['success'] = $success;
                $data[$channel_id][$father_id][$product_id][$customer_id]['valid'] = $valid;
                $data[$channel_id][$father_id][$product_id][$customer_id]['money'] = $money;
                $data[$channel_id][$father_id][$product_id][$customer_id]['cost'] = $cost;
                $data[$channel_id][$father_id][$product_id][$customer_id]['money_original'] = $money_original;
                $data[$channel_id][$father_id][$product_id][$customer_id]['cost_original'] = $cost_original;
            }else{
                $data[$channel_id][$father_id][$product_id][$customer_id]['total'] += $total;
                $data[$channel_id][$father_id][$product_id][$customer_id]['success'] += $success;
                $data[$channel_id][$father_id][$product_id][$customer_id]['valid'] += $valid;
                $data[$channel_id][$father_id][$product_id][$customer_id]['money'] = bcadd($data[$channel_id][$father_id][$product_id][$customer_id]['money'], $money, 6);
                $data[$channel_id][$father_id][$product_id][$customer_id]['cost'] = bcadd($data[$channel_id][$father_id][$product_id][$customer_id]['cost'], $cost, 6);
                $data[$channel_id][$father_id][$product_id][$customer_id]['money_original'] = bcadd($data[$channel_id][$father_id][$product_id][$customer_id]['money_original'], $money_original, 6);
                $data[$channel_id][$father_id][$product_id][$customer_id]['cost_original'] = bcadd($data[$channel_id][$father_id][$product_id][$customer_id]['cost_original'], $cost_original, 6);
            }
        }
        unset($usage_data);
        unset($tmp_cost_data);
        unset($product_customer_income);
        unset($product_customer_cost);

        return $data;
    }

    private function getTpl()
    {
        return [
            'total' => 0,
            'success' => 0,
            'valid' => 0,
            'money' => 0,//对应页面收入
            'cost' => 0,//对应页面成本
            //'money_finance' => 0,//对应页面金融收入
            'money_original' => 0,//对应页原始收入
            'cost_original' => 0//对应页原始成本
        ];
    }

    public function initChannelTypeMap(){
        //渠道类别
        $this->channelTypeMap = Channel::$channelTypeMap;
        unset($this->channelTypeMap[0]);
        //渠道类别和渠道对应关系
        $list = Channel::getChannelByCondition();
        $list = array_column($list, 'channel_type', 'channel_id');
        foreach ($list as $channel_id => $type){
            $this->channelIdToTypeMap[$channel_id] = $this->channelTypeMap[$type] ?? '其他';
        }
    }

    public function getAuthChannelList(){
        $channel_ids = UserChannelConfig::getChannelIdsByUser(self::$user_name);
        $channel_ids = array_column($channel_ids,'channel_id');
        if(in_array(-1 ,$channel_ids)) {
            $channel_ids = Channel::select(['label', 'channel_id'])->get()->toArray();
            $channel_ids = array_column($channel_ids,'channel_id');
        }

        $this->authChannelList = $channel_ids;
    }

    private function getChannelWhere($params){
        $channel_ids = [];
        if(isset($params['channel_type'])){
            $channel_type_name = $this->channelTypeMap[$params['channel_type']] ?? '';
            $channel_ids = array_keys($this->channelIdToTypeMap, $channel_type_name);
        }

        if(isset($params['channel'])){
            $channel_ids = [$params['channel']];
        }

        return $channel_ids;
    }

    private function getProductWhere($params){
        $product_ids = [];
        if(isset($params['father_id'])){
            $product_ids = array_keys($this->subToFatherMap, $params['father_id']);
        }

        if(isset($params['product_id'])){
            $product_ids = [$params['product_id']];
        }

        return $product_ids;
    }

    private function getCustomerWhere($params){
        $customer_ids = [];
        if(isset($params['customer_id'])){
            $customer_ids = [$params['customer_id']];
        }

        return $customer_ids;
    }

    private function getSourceWhere($params){
        $source_ids = [];
        if(isset($params['source'])){
            $source_ids = [$params['source']];
        }

        return $source_ids;
    }


    /**
     * 返回两个日期相差月份数
     * @param $date1
     * @param $date2
     * @return float|int
     */
    public function getDiffMonthNum($date1, $date2)
    {

        $year1 = substr($date1, 0, 4);
        $month1 = substr($date1, 4, 2);

        $year2 = substr($date2, 0, 4);
        $month2 = substr($date2, 4, 2);

        $month_num_1 = $year1*12 + $month1;
        $month_num_2 = $year2*12 + $month2;
        return abs($month_num_1 - $month_num_2);
    }

    public function getChannelTypeList(){
        $this->initChannelTypeMap();
        return ['channelToTypeMap' => $this->channelIdToTypeMap, 'channelTypeList' => $this->channelTypeMap];
    }

    public function getProductListByProductView($father_id = ''){
        $this->mainRep = new MainRepository();
        //根据产品视角，获取全量的所有的子、父产品map
        $this->subToFatherMap = $this->mainRep->getSubPidsByFatherIdsWithAngle([], StatDefine::PM_ANGLE);

        if(!empty($father_id)){
            $list = array_keys($this->subToFatherMap, $father_id);
        }else{
            $list = array_keys($this->subToFatherMap);
        }

        $data = [];
        foreach ($list as $pid){
            $p_name = RedisCache::instance('productId_productName_mapping')->get($pid);
            $data[$pid] = $p_name;
        }

        return $data;
    }

}