<?php

namespace App\Http\Repository;


use App\Models\Account;
use App\Models\Channel;
use App\Models\AlarmConfigProductValue;
use App\Models\MonitorCustom\ConfigProductValue;
use App\Models\AlarmConfigProductValueSpread as AlarmSpreadModel;
use App\Models\MonitorCustom\ConfigProductValueSpread as SpreadModel;
use App\Models\Product;
use DeepCopy\f001\A;
use Illuminate\Http\Request;


class ConfigProductValueSpread
{
    public function __construct($select)
    {
        /*
        if ($select){
            $this->SpreadModel = new SpreadModel();
            $this->ConfigProduct = new ConfigProductValue();
        }else{
            $this->SpreadModel = new AlarmSpreadModel();
            $this->ConfigProduct = new AlarmConfigProductValue();
        }
        */
        $this->SpreadModel = new AlarmSpreadModel();
        $this->ConfigProduct = new AlarmConfigProductValue();
    }
    /**
     * 配置管理-查得率 列表
     * @return mixed
     * @throws \Exception
     */
    public function spreadList()
    {
        $params = $this->getSpreadParams();
        $where = [];
        if ($params['product_id']) {
            $where[] = ['pid', $params['product_id']];
        }
        if ($params['apikey']) {
            $where[] = ['apikey', $params['apikey']];
        }
        $channel = $params['channel'] ? $params['channel'] : null;

        $offset = intval($params['offset']) ? intval($params['offset']) : null;
        $limit = intval($params['limit']) ? intval($params['limit']) : null;

        $count = SpreadModel::getCountByCondition($where, $channel);
        $data = SpreadModel::getListByCondition($where, null,  'id desc', $channel, $offset, $limit);
        return compact('count', 'data');
    }
    /**
     * 配置管理-查得率 详情
     * @return mixed
     * @throws \Exception
     */
    public function spreadInfo()
    {
        $id = intval(request()->post('id', 0));
        if (!$id) {
            return false;
        }
        $data = SpreadModel::findOneItem(['id' => $id]);
        return $data;
    }

    /**
     * 值分布配置 唯一性校验
     * @param $pid
     * @param $cid
     * @param $apikey
     * @param $value_id
     * @return mixed
     */
    public function checkSpreadUnique($pid, $cid, $apikey, $value_id)
    {
        return SpreadModel::getCountByCondition(compact('pid', 'cid', 'apikey', 'value_id'));
    }

    /**
     * 配置管理-查得率 修改
     * @return mixed
     * @throws \Exception
     */
    public function spreadEdit()
    {
        $id = intval(request()->post('id', 0));
        $field = trim(request()->post('field', ''));
        $value = floatval(request()->post('value', 0));

        $auto_add = SpreadModel::$auto_add['OPERATION_TYPE']; //默认将type字段改为 已配置 状态
        if (!$id || !$field || !$value
            || !in_array($field, ['th_one_max', 'th_one_min', 'th_two_max', 'th_two_min',])) {
            throw new \Exception("参数错误");
        }

        $data = SpreadModel::editItemById($id, [$field => $value, 'auto_add'=>$auto_add]);
        return $data;
    }

    /**
     * 配置管理-查得率 添加
     * @return mixed
     * @throws \Exception
     */
    public function spreadAdd()
    {
        $apikey = trim(request()->post('apikey', 0));
        $pid = intval(request()->post('pid', 0));
        $cid = intval(request()->post('cid', 0));
        $value_id = intval(request()->post('value_id', 0));
        $th_one_max = floatval(request()->post('th_one_max', 0));
        $th_one_min = floatval(request()->post('th_one_min', 0));
        $th_two_max = floatval(request()->post('th_two_max', 0));
        $th_two_min = floatval(request()->post('th_two_min', 0));
        $auto_add = SpreadModel::$auto_add['OPERATION_TYPE']; //默认将type字段改为 已配置 状态
        if(!$apikey || !$pid || !$cid || !$value_id) {
            throw new \Exception("缺少必要参数");
        }
        $count = $this->checkSpreadUnique($pid, $cid, $apikey, $value_id);
        if ($count > 0) {
            throw new \Exception("该条记录已经存在");
        }

        $data = SpreadModel::addItem( compact('apikey', 'pid', 'cid', 'value_id', 'th_two_max', 'th_two_min', 'th_one_max', 'th_one_min', 'auto_add'));
        return $data;
    }

    /**
     * 校验/获取 查询的参数
     *
     * @access protected
     *
     * @return array
     **/
    protected function getSpreadParams()
    {
        $apikey = request()->post('apikey');
        $product_id = request()->post('product_id');
        $channel = request()->post('channel');
        $offset = request()->post('offset');
        $limit = request()->post('limit');

        return compact('start_date', 'end_date', 'apikey', 'product_id', 'channel', 'offset', 'limit');
    }

    protected function getParams()
    {
        $apikey = request()->get('account','');
        $product_id = request()->get('product','');
        $channel = request()->get('channel','');
        $pagesize = request()->get('sizechange','');
        $currentchange = request()->get('currentchange','');
       // $select = request()->get('select','');
        return compact('apikey','product_id','channel','pagesize','currentchange');
    }

    //获取下拉选择框数据
    public function getOptions()
    {
        //获取账号 产品 渠道
        $data['accountData'] = Account::getAllAccount();
        $data['productData'] = Product::getAllProduct();
        $data['channelData'] = Channel::getAllChannelOptions();

        return $data;
    }

    //获取值分布阈值列表
    public function getConfigProductValueSpread()
    {
        $params = $this->getParams();
        $offset = ($params['currentchange'] - 1) * $params['pagesize'];
        $account = array_column(Account::getAllAccount(),'label','value');
        $product = array_column(Product::getAllProduct(),'label','value');
        $channel = array_column(Channel::getAllChannelOptions(),'label','value');

        //$value = $this->createConfigProductValue(ConfigProductValue::getConfigProductValue());
        $value = $this->createConfigProductValue($this->ConfigProduct::getConfigProductValue());

        $res = $this->SpreadModel::getConfigProductValueSpread($params['apikey'],$params['product_id'],$params['channel'],$offset,$params['pagesize']);
        $count =$this->SpreadModel::getConfigProductValueSpreadCount($params['apikey'],$params['product_id'],$params['channel']);

        foreach ($res as &$v){
            $v['account_name'] = isset($account[$v['apikey']]) ? $account[$v['apikey']] : '';
            $v['product_name'] = isset($product[$v['pid']]) ? $product[$v['pid']] : '';
            $v['channel_name'] = isset($channel[$v['cid']]) ? $channel[$v['cid']] : '';
            $v['value'] = isset($value[$v['value_id']]) ? $value[$v['value_id']] : '';
        }

        return ['configproductvaluespread'=>$res,'count'=>$count];

    }

    //根据产品查询值分布参数
    public function  getConfigProductValue()
    {
        $params = $this->getParams();
        $value = $this->createConfigProductValue($this->ConfigProduct::getConfigProductValueByPid($params['product_id']));
        $res = [];
        foreach ($value as $k=>$v){
            $res[] = ['value'=>$k,'label'=>$v];
        }
        return $res;
    }

    public function  createConfigProductValue($configproductvalue)
    {
        $res = [];
        $types = ConfigProductValue::$typeName;
        foreach ($configproductvalue as $v){
         $res[$v['id']] = $types[$v['type']] . ' ' .$v['label'];
        }
        return $res;
    }

    public function addConfigProductValueSpread()
    {
        $params = $this->getPostData();
        $res = $this->SpreadModel::insert($params);
        return $res;
    }

    public function updateConfigProductValueSpread()
    {
        $params = $this->getUpdatePostData();

        $data = $this->SpreadModel::find($params['id']);
        if ($data['th_one_max'] != $params['th_one_max'] || $data['th_one_min'] != $params['th_one_min'] || $data['th_two_max'] != $params['th_two_max'] || $data['th_two_min'] != $params['th_two_min']){
                $res = $this->SpreadModel::where('id',$params['id'])->update($params);
                return $res;
            }
        return  true;
    }

    private function getPostData()
    {
        $apikey = Request()->post('account','');
        $pid = Request()->post('product','');
        $cid = Request()->post('channel','');
        $value_id = Request()->post('value','');
        $th_one_max = Request()->post('one_max','');
        $th_one_min = Request()->post('one_min','');
        $th_two_max = Request()->post('two_max','');
        $th_two_min = Request()->post('two_min','');
        $type = Request()->post('type',2);

        return compact('apikey','pid','cid','value_id','th_one_max','th_one_min','th_two_max','th_two_min', 'type');
    }

    private function getUpdatePostData()
    {

        $id = Request()->post('id','');
        $th_one_max = Request()->post('th_one_max','');
        $th_one_min = Request()->post('th_one_min','');
        $th_two_max = Request()->post('th_two_max','');
        $th_two_min = Request()->post('th_two_min','');

        return compact('id','th_one_max','th_one_min','th_two_max','th_two_min');
    }
}
