<?php

namespace App\Http\Repository;

use App\Models\MongoCrawlerTels;
use App\Models\MongoProductInvoked;
use App\Models\MongoStatLog;
use App\Models\MongoUpstreamShortcutStatistic;
use App\Models\MongoUpstreamVerificationStatistic;
use App\Models\Product;
use App\Support\CustomException;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Support\Facades\DB;
use MongoDB\BSON\UTCDateTime;

class StatRepository
{
    use WechatExceptionTrait;

    /*
    * product key 列表 && 和产品ID的映射关系
    * */
    private $key_cache_list_product_key_relation = 'backend_api_cache_product_relation';

    /** @var int 催收快捷版的产品ID */
    private $product_id_cuishou_short = 210;

    /** @var int 数据核验父产品的ID */
    private $product_id_verify = 200;

    /** @var int 每月一号允许昨天传入的控制时间是20分钟 */
    const DELAY_CONTROL_TIME = 1200;

    /** @var string 如果修改历史数据 希望明白自己在做啥 */
    private $heading_what_doing = 'i am trying to fix bug data';

    /** @var string redis链接 */
    private $redis_bill_connection = 'db_backend';

    /** @var string 被遗弃的盐 */
    private $key_cacke_abandon_salt = 'key_abandon_salt_for_update_past_data';

    /** @var array 催收快捷版产品的渠道统计配置 */
    private $_channel_stat_cuishou_short = [];

    /*
     * product_id和代号之间的关系
     * */
    private $list_product_mapping = [
        '601' => 'matching', // 邦秒配（单号版）
        '604' => 'matchingBatch', // 邦秒配（详单版）
        '101' => 'cuishou', // 	催收分析详单
        '105' => 'cuishouVTwo', // 催收分析详单版V2
        '107' => 'cuishouVar', // 催收分析详内部模型变量
        '801' => 'telStatus', // 号码状态查询
        '401' => 'bang', // 邦企查
        '241' => 'cuishouShort241', // 催收快捷版241
        '242' => 'cuishouShort242', // 催收快捷版242
        '243' => 'cuishouShort243', // 催收快捷版243
        '244' => 'cuishouShort244', // 催收快捷版244
        '245' => 'cuishouShort245', // 催收快捷版245
        '246' => 'cuishouShort246', // 催收快捷版246
        '247' => 'cuishouShort247', // 催收快捷版247
        '251' => 'cuishouShort251', // 催收快捷版251
        '252' => 'cuishouShort252', // 催收快捷版252
        '253' => 'cuishouShort253', // 催收快捷版253
        '254' => 'cuishouShort254', // 催收快捷版254
        '255' => 'cuishouShort255', // 催收快捷版255
        '256' => 'cuishouShort256', // 催收快捷版256
        '257' => 'cuishouShort257', // 催收快捷版257
        '258' => 'cuishouShort258', // 催收快捷版258
        '259' => 'cuishouShort259', // 催收快捷版259
        '260' => 'cuishouShort260', // 催收快捷版260
        '261' => 'cuishouShort261', // 催收快捷版261
        '262' => 'cuishouShort262', // 催收快捷版262
        '263' => 'cuishouShort263', // 催收快捷版263
        '264' => 'cuishouShort264', // 催收快捷版264
        '265' => 'cuishouShort265', // 催收快捷版265
        '266' => 'cuishouShort266', // 催收快捷版266
        '267' => 'cuishouShort267', // 催收快捷版267
        '268' => 'cuishouShort268', // 催收快捷版268
        '269' => 'cuishouShort269', // 催收快捷版269
        '270' => 'cuishouShort270', // 催收快捷版270
        '271' => 'cuishouShort271', // 催收快捷版271
        '272' => 'cuishouShort272', // 催收快捷版272
        '273' => 'cuishouShort273', // 催收快捷版273
        '274' => 'cuishouShort274', // 催收快捷版274
        '275' => 'cuishouShort275', // 催收快捷版275
        '276' => 'cuishouShort276', // 催收快捷版276
        '277' => 'cuishouShort277', // 催收快捷版277
        '278' => 'cuishouShort278', // 催收快捷版278
        '279' => 'cuishouShort279', // 催收快捷版279
        '280' => 'cuishouShort280', // 催收快捷版280
        '281' => 'cuishouShort281', // 催收快捷版281
        '282' => 'cuishouShort282', // 催收快捷版282
        '283' => 'cuishouShort283', // 催收快捷版283
        '284' => 'cuishouShort284', // 催收快捷版284
        '285' => 'cuishouShort285', // 催收快捷版285
        '286' => 'cuishouShort286', // 催收快捷版286
        '287' => 'cuishouShort287', // 催收快捷版287
        '288' => 'cuishouShort288', // 催收快捷版288
        '289' => 'cuishouShort289', // 催收快捷版289
        '290' => 'cuishouShort290', // 催收快捷版290
        '291' => 'cuishouShort291', // 催收快捷版291
        '292' => 'cuishouShort292', // 催收快捷版292
        '293' => 'cuishouShort293', // 催收快捷版293
        '294' => 'cuishouShort294', // 催收快捷版294
        '295' => 'cuishouShort295', // 催收快捷版295
        '296' => 'cuishouShort296', // 催收快捷版296
        '297' => 'cuishouShort297', // 催收快捷版297
        '298' => 'cuishouShort298', // 催收快捷版298
        '299' => 'cuishouShort299', // 催收快捷版298
        '210' => 'cuishouShort210', //催收分析快捷版（210）总统计数据
		'711' => 'cuishouShort711',	//邦信分快捷版711
		'712' => 'cuishouShort712',		//邦信分快捷版712
        '201' => 'dataValidate201', // 数据核验201
        '202' => 'dataValidate202', // 数据核验202
        '203' => 'dataValidate203', // 数据核验203
        '204' => 'dataValidate204', // 数据核验204
        '205' => 'dataValidate205', // 数据核验205
        '206' => 'dataValidate206', // 数据核验206
        '207' => 'dataValidate207', // 数据核验207
        '208' => 'dataValidate208', // 数据核验208
        '209' => 'dataValidate209', // 数据核验209
        '211' => 'dataValidate211', // 数据核验211
        '212' => 'dataValidate212', // 数据核验212
        '213' => 'dataValidate213', // 数据核验213
        '214' => 'dataValidate214', // 数据核验214
        '215' => 'dataValidate215', // 数据核验215
        '216' => 'dataValidate216', // 数据核验216
        '217' => 'dataValidate217', // 数据核验217
        '218' => 'dataValidate218', // 数据核验218
        '219' => 'dataValidate219', // 数据核验219
        '220' => 'dataValidate220', // 数据核验220
        '221' => 'dataValidate221', // 数据核验221
        '222' => 'dataValidate222', // 数据核验222
        '230' => 'dataValidate230', // 数据核验230
        '231' => 'dataValidate231', // 数据核验231
        '232' => 'dataValidate232', // 数据核验232
        '233' => 'dataValidate233', // 数据核验233
        '234' => 'dataValidate234', // 数据核验234
        '235' => 'dataValidate235', // 数据核验235
        '237' => 'dataValidate237', // 数据核验237
        '238' => 'dataValidate238', // 数据核验238
        '239' => 'dataValidate239', // 数据核验238
        '240' => 'dataValidate240', // 数据核验238
        '310' => 'dataValidate310',
        '311' => 'dataValidate311',
        '312' => 'dataValidate312',
        '313' => 'dataValidate313',
        '314' => 'dataValidate314',
        '315' => 'dataValidate315',
        '316' => 'dataValidate316',
        '317' => 'dataValidate317',
		'318' => 'dataValidate318',
		'320' => 'dataValidate320',
		'321' => 'dataValidate321',
		'322' => 'dataValidate322',
		'323' => 'dataValidate323',
		'324' => 'dataValidate324',
		'325' => 'dataValidate325',
		'326' => 'dataValidate326',
		'327' => 'dataValidate327',
		'328' => 'dataValidate328',
		'329' => 'dataValidate329',
		'330' => 'dataValidate330',
		'331' => 'dataValidate331',
        '900' => 'dataValidate900', // 数据核验900
        '901' => 'dataValidate901', // 数据核验900
        '104' => 'peiXiangDan', // 邦秒配详单
        '501' => 'private', //  邦信分私有云
        '102' => 'report', // 邦报告
        '301' => 'crawler301', // 301 h5
        '302' => 'crawler302', // 302 api
        '603' => 'goldenShield603',
        '612' => 'goldenShield612',
        '613' => 'goldenShield613',
        '614' => 'goldenShield614',
        '615' => 'goldenShieldNew615',
        '641' => 'goldenShieldNew641',
        '642' => 'goldenShieldNew642',
        '643' => 'goldenShieldNew643',
        '644' => 'goldenShieldNew644',
        '645' => 'goldenShieldNew645',
        '646' => 'goldenShieldNew646',
        '647' => 'goldenShieldNew647',
        '648' => 'goldenShieldNew648',
        '649' => 'goldenShieldNew649',
        '651' => 'goldenShieldNew651',
        '652' => 'goldenShieldNew652',
        '661' => 'goldenShieldNew661',
        '662' => 'goldenShieldNew662',
        '663' => 'goldenShieldNew663',
        '681' => 'goldenShieldNew681',
        '682' => 'goldenShieldNew682',
        '683' => 'goldenShieldNew683',
        '684' => 'goldenShieldNew684',
        //风险符号
        '616' => 'riskSymbol616',
        '664' => 'telEarlyWarning664',
    ];

    /** @var 日志查询相关 */
    private $log_id;

    /** @var array 允许的爬虫 */
    private $list_crawler = [
        'yulore'    => '羽乐',
        'moxie'     => '魔蝎',
        'xinde'     => '信德',
        'shujumohe' => '聚信立',
        'juxinli'   => '数聚魔盒',
        'none'      => '未知',
    ];

    /** @var array 允许填充的运营商 */
    private $list_operator = [
        'yidong'   => '移动',
        'liantong' => '联通',
        'dianxin'  => '电信',
        'none'     => '未知'
    ];

    /** @var array 允许的省份 */
    private $list_province = [
        'shanghai'     => '上海',
        'yunnan'       => '云南',
        'neimenggu'    => '内蒙古',
        'beijing'      => '北京',
        'jilin'        => '吉林',
        'sichuan'      => '四川',
        'tianjin'      => '天津',
        'ningxia'      => '宁夏',
        'anhui'        => '安徽',
        'shandong'     => '山东',
        'shan1xi'      => '山西',
        'guangdong'    => '广东',
        'guangxi'      => '广西',
        'xinjiang'     => '新疆',
        'jiangsu'      => '江苏',
        'jiangxi'      => '江西',
        'hebei'        => '河北',
        'henan'        => '河南',
        'zhejiang'     => '浙江',
        'hainan'       => '海南',
        'hubei'        => '湖北',
        'hunan'        => '湖南',
        'gansu'        => '甘肃',
        'fujian'       => '福建',
        'xizang'       => '西藏',
        'guizhou'      => '贵州',
        'liaoning'     => '辽宁',
        'chongqing'    => '重庆',
        'shan3xi'      => '陕西',
        'qinghai'      => '青海',
        'heilongjiang' => '黑龙江',
        'none'         => '未知'
    ];

    /**
     * @throws \Exception
     */
    public function setLogId()
    {
        $this->log_id = \Ramsey\Uuid\Uuid::uuid4()->toString();
    }

    /**
     * 账号每天的爬取成功的具体的号码列表
     * @return array
     * @throws \Exception
     */
    public function crawlerTelList(): array
    {
        // 校验参数
        $this->_validateParamsForCrawlerTelList();

        // 生成列表
        return [
            'total'     => $this->_genTotalNumberForTelList(),
            'data'      => $this->_getSearchForTelList(),
            'page'      => (int)trim(request()->get('page', 1)),
            'page_size' => (int)trim(request()->get('page_size', 30)),
        ];
    }

    /**
     * @return array
     */
    private function _getSearchForTelList(): array
    {
        list($where, $offset, $limit) = $this->_genParamsForTelList();

        // 获取列表
        $list = MongoCrawlerTels::where($where)->orderBy('_id', 'desc')->offset($offset)->limit($limit)->get();
        return $list ? $list->toArray() : [];
    }


    /**
     * 总量
     * @return int
     */
    private function _genTotalNumberForTelList(): int
    {
        // 生成参数
        list($where, $offset, $limit) = $this->_genParamsForTelList();
        return MongoCrawlerTels::where($where)->count();
    }

    /**
     * 生成参数
     * @return array
     */
    private function _genParamsForTelList(): array
    {
        list($day_begin, $day_end, $page_size, $page, $product_id, $apikey, $tel) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
            trim(request()->get('page_size', 30)),
            trim(request()->get('page', 1)),
            (int)trim(request()->get('product_id')),
            trim(request()->get('apikey')),
            trim(request()->get('tel')),

        ];

        // 普通过滤
        $where = array_filter(compact('product_id', 'apikey', 'tel'), function ($item) {
            return $item;
        });

        // 日期筛选
        if (!$day_begin && $day_end) {
            $where['amount_date'] = [
                '$lte' => date('Ymd', strtotime($day_end)),
            ];
        } elseif ($day_begin && !$day_end) {
            $where['amount_date'] = [
                '$gte' => date('Ymd', strtotime($day_begin)),
            ];
        } elseif ($day_begin && $day_end) {
            $where['amount_date'] = [
                '$gte' => date('Ymd', strtotime($day_begin)),
                '$lte' => date('Ymd', strtotime($day_end)),
            ];
        }

        return [
            $where,
            $page ? intval(($page - 1) * $page_size) : 0,
            (int)$page_size
        ];
    }


    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamsForCrawlerTelList()
    {
        list($day_begin, $day_end) = [
            trim(request()->get('day_begin')),
            trim(request()->get('day_end')),
        ];

        // 如果同时限制了开始和结束的日期
        if ($day_begin && $day_end && strtotime($day_begin) > strtotime($day_end)) {
            throw new CustomException('开始日期必须小于结束日期');
        }
    }

    /**
     * 账号每天的爬取成功的具体的号码
     * @return array
     * @throws CustomException
     * @throws \Exception
     */
    public function crawlerTel(): array
    {
        $this->setLogId();

        // 校验参数
        $this->_validateParams();

        // 生成参数
        list($params, $list_tel) = $this->_genParams();

        return $this->_crawlerTelDo($params, $list_tel);
    }

    /**
     * 账号每天的爬取成功的具体的号码
     *
     * @param array $params
     * @param array $list_tels
     *
     * @return array
     * @throws CustomException
     * @throws \Exception
     */
    private function _crawlerTelDo(array $params, array $list_tels): array
    {
        // 写输入
        DB::transaction(function () use ($params, $list_tels) {
            if ($this->_determineIsUpdateForCrawler()) {
                DB::connection('mongodb_backend')
                    ->table('crawler_tels')
                    ->where(['hash_key' => $params['hash_key']])
                    ->delete();
            }

            array_walk($list_tels, function ($tel) use ($params) {
                $params['tel'] = $tel;
                DB::connection('mongodb_backend')->table('crawler_tels')->insert($params);
            });
        });

        // 是否更新了历史数据
        $this->_warningIfUpdateHistoryData();

        // 返回
        $this->_errorException('操作成功', true);
        return [
            'log_id'   => $this->log_id,
            'msg'      => '操作成功',
            'hash_key' => $params['hash_key']
        ];
    }

    /**
     * 修改历史数据 则预警
     * @throws \Exception
     */
    protected function _warningIfUpdateHistoryData()
    {
        if (!$this->_determineWechatWarning()) {
            return;
        }

        $msg = '爬取号码历史数据修改预警 product_id : ' . request()->post('product_id') . ' log_id : ' . $this->log_id . ' amount_date : ' . request()->post('amount_date');
        $this->wechatException($msg);
    }


    /**
     * 是否发送微信预警
     * @return bool
     */
    protected function _determineWechatWarning(): bool
    {
        // 要点参数
        list($date_amount, $date_today, $date_yesterday) = [
            trim(request()->post('amount_date')),
            date('Ymd'),
            date('Ymd', strtotime('-1 day')),
        ];
        $time_diff   = time() - strtotime($date_today);
        $date_amount = date('Ymd', strtotime($date_amount));

        // 是否推送的今天的数据
        if ($date_amount == $date_today) {
            return false;
        }
        // 如果是昨天的且在十分钟内的也可以
        if ($date_amount == $date_yesterday && $time_diff <= self::DELAY_CONTROL_TIME) {
            return false;
        }

        // 如果是在初始化数据也不需要预警
        return env('THE_STATUS_WHICH_CRAWLER_TEL_API') != 'init';
    }


    /**
     * 是否是更新操作
     * @return bool
     */
    private function _determineIsUpdateForCrawler(): bool
    {
        $type = trim(request()->post('type'));
        return $type == 'update';
    }

    /**
     * 生成参数
     * @return array
     */
    private function _genParams(): array
    {
        list($apikey, $product_id, $tel, $amount_date, $province, $crawler, $operator, $created_at, $updated_at) = [
            trim(request()->post('apikey')),
            (int)trim(request()->post('product_id')),
            request()->post('tel'),
            trim(request()->post('amount_date')),
            trim(request()->post('province')),
            trim(request()->post('crawler')),
            trim(request()->post('operator')),
            new UTCDateTime(time() * 1000),
            new UTCDateTime(time() * 1000),
        ];

        // 生成hash_key
        $amount_date = date('Ymd', strtotime($amount_date));
        $hash_key    = $this->_genHashKey(compact('apikey', 'product_id', 'amount_date', 'province', 'crawler',
                'operator'));
        return [
            compact('apikey', 'product_id', 'tel', 'amount_date', 'province', 'crawler', 'operator', 'hash_key',
                'created_at', 'updated_at'),
            is_string($tel) ? [$tel] : $tel
        ];
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParams()
    {
        list($apikey, $product_id, $tel, $amount_date, $province, $crawler, $operator, $type) = [
            trim(request()->post('apikey')),
            trim(request()->post('product_id')),
            request()->post('tel'),
            trim(request()->post('amount_date', '')),
            trim(request()->post('province')),
            trim(request()->post('crawler')),
            trim(request()->post('operator')),
            trim(request()->post('type')),
        ];

        !$apikey && $this->_errorException('缺少必须的参数 apikey');
        !$product_id && $this->_errorException('缺少必须的参数 product_id');
        !$tel && $this->_errorException('缺少必须的参数 tel');
        (!$province || !array_key_exists($province,
                $this->list_province)) && $this->_errorException('请输入正确的参数 province');
        (!$crawler || !array_key_exists($crawler, $this->list_crawler)) && $this->_errorException('请输入正确的参数 crawler');
        (!$operator || !array_key_exists($operator,
                $this->list_operator)) && $this->_errorException('请输入正确的参数 operator');

        // 时间校验
        $this->_validateDateForCrawlerTel($amount_date);

        //  校验type是否设置有异常
        $amount_date = date('Ymd', strtotime($amount_date));
        $this->_validateTypeForCrawlerTel($type,
            compact('apikey', 'product_id', 'amount_date', 'province', 'crawler', 'operator'));
    }

    /**
     * 校验时间是否合法
     *
     * @param string $amount_date
     *
     * @throws CustomException
     * @throws \Exception
     */
    private function _validateDateForCrawlerTel(string $amount_date)
    {
        // 是否存在
        !$amount_date && $this->_errorException('缺少必须的参数 amount_date');

        // 如果接口是初始化状态 则取消时间限制
        if (env('THE_STATUS_WHICH_CRAWLER_TEL_API') == 'init') {
            return;
        }

        // 推送的不是本月的数据得话
        if (!$this->_determineInsertDataOfThisMonth($amount_date)) {
            // 妄图修改历史数据
            if (!$this->determineUpdateYesterdayWhenFirstDayOfThisMonth($amount_date)) {
                $this->_errorException('不可以修改非本月数据');
            } else {
                // 如果是1号 但是如果不是在可控的时间范围内也是不可以的
                if (!$this->determineTheTimeInControl()) {
                    $this->_errorException('在每月1号的允许修改昨天的数据,但是请将时间控制在20分钟内');
                }
            }
        }

    }

    /**
     * 即使是一号在修改昨天的数据 那时间控制在了20分钟内了吗？
     * @return bool
     * @throws \Exception
     */
    private function determineTheTimeInControl(): bool
    {
        return (time() - strtotime(date('Y-m-d'))) < self::DELAY_CONTROL_TIME;
    }

    /**
     * 是在本月的一号修改昨天的信息吗
     *
     * @param string $amount_date
     *
     * @return bool
     */
    private function determineUpdateYesterdayWhenFirstDayOfThisMonth(string $amount_date): bool
    {
        if (date('d') != '01') {
            return false;
        }

        return strtotime($amount_date) == strtotime(date('Y-m-d', strtotime('last day of last month')));
    }

    /**
     * 是否插入的是今天的数据
     *
     * @param string $amount_date
     *
     * @return bool
     * @throws \Exception
     */
    private function _determineInsertDataOfThisMonth(string $amount_date): bool
    {
        // 是否在修改本月的数据
        $month_choose = date('Y-m', strtotime($amount_date));
        $month_now    = date('Y-m');
        return $month_choose == $month_now;
    }

    /**
     * 生成hash key
     *
     * @param array $data
     *
     * @return string
     */
    private function _genHashKey(array $data): string
    {
        ksort($data);
        return md5(implode(',', $data));
    }

    /**
     * 检验类型
     *
     * @param string $type
     * @param array  $params
     *
     * @throws CustomException
     * @throws \Exception
     */
    private function _validateTypeForCrawlerTel(string $type, array $params)
    {
        (!$type || !in_array($type, ['insert', 'update'])) && $this->_errorException('请输入正确的参数 type');

        // 是否已经推送过相同属性的数据
        $hash_key        = $this->_genHashKey($params);
        $hash_key_exists = $this->_determineHashKeyExists($hash_key);

        // 如果type时insert 但是hash_key 已经存在
        if ($type == 'insert' && $hash_key_exists) {
            $msg = 'insert模式对应的hash_key : ' . $hash_key . ' 已经存在 所以执行失败';
            $this->wechatException($msg . ' log_id :' . $this->log_id);
            $this->_errorException($msg);
        }

        if ($type == 'update' && !$hash_key_exists) {
            $msg = 'update模式对应的hash_key : ' . $hash_key . ' 不存在 所以执行失败';
            $this->wechatException($msg . ' log_id' . $this->log_id);
            $this->_errorException($msg);
        }
    }

    /**
     * hash key是否已经存在
     *
     * @param string $hash_key
     *
     * @return bool
     */
    private function _determineHashKeyExists(string $hash_key): bool
    {
        return (bool)MongoCrawlerTels::where(compact('hash_key'))->count();
    }

    /**
     * 异常处理
     *
     * @param string $msg
     * @param bool   $success
     *
     * @throws CustomException
     */
    private function _errorException(string $msg, $success = false)
    {
        MongoStatLog::create([
                'log_id'  => $this->log_id,
                'msg'     => $msg,
                'success' => $success,
                'action'  => 'crawler_tel',
                'source'  => request()->post()
            ]);
        if (!$success) {
            throw new CustomException(' 爬取成功号码入库失败 log_id : ' . $this->log_id . ' ' . $msg);
        }
    }

    /**
     * 邦妙验的上游数据入库
     * @throws CustomException
     */
    public function verification()
    {
        // 校验参数
        $this->_validateParamForVerification();

        //  入库
        $this->_createOrUpdateForVerification();
    }

    /**
     * 入库
     */
    private function _createOrUpdateForVerification()
    {
        // 条件 && 入库单元
        list($where, $item) = $this->_getWhereAndItemForVerification();

        MongoUpstreamVerificationStatistic::updateOrCreate($where, $item);
    }

    /**
     * 条件 && 入库单元
     * @return array
     */
    private function _getWhereAndItemForVerification(): array
    {
        list($day, $product_id, $upstream, $data, $node) = [
            $this->_getFormatDay(),
            (int)trim(request()->post('product_id')),
            trim(request()->post('upstream')),
            $this->_getDataParamsForVerify(),
            trim(request()->post('node'))
        ];

        return [
            compact('day', 'product_id', 'upstream', 'node'),
            compact('day', 'product_id', 'upstream', 'data', 'node'),
        ];
    }

    /**
     * 将string int
     *
     * @param array $data
     *
     * @return array
     */
    private function _formatDataFromStringToInt(array $data): array
    {
        return array_map(function ($item) {
            return (int)$item;
        }, $data);
    }

    /**
     * 获取data参数
     * @return array
     */
    private function _getDataParamsForVerify(): array
    {
        return $this->_formatDataFromStringToInt(request()->post('data'));
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function _validateParamForVerification()
    {
        // 校验product_id是否合法
        $this->_validateProductIdForVerification();

        // 校验数据源
        $this->_validateUpstreamForVerify();

        // 校验日期
        $this->_validateDayForVerification();

        // 校验传递的统计字段是否合法
        $this->_validateDataForVerification();

        // 校验节点是否合法
        $this->_validateNodeForVerfication();
    }

    /**
     * 校验节点是否合法
     * @throws CustomException
     */
    private function _validateNodeForVerfication()
    {
        $this->_validateNode('邦秒验');
    }

    /**
     * 校验传递的字段是否合法
     * @throws CustomException
     */
    private function _validateDataForVerification()
    {
        list($data, $interface_statistic_field) = [
            request()->post('data'),
            $this->_getStatisticFieldForVerification()
        ];

        if (!$data || !is_array($data)) {
            throw new CustomException('请传入合法的data');
        }

        array_walk($data, function ($item, $key) use ($interface_statistic_field) {
            if (!is_numeric($item) || $item < 0) {
                throw new CustomException('data.key ：' . $key . '对应的值' . $item . '不是一个合法的number');
            }

            // 统计字段不合法
            if (!array_key_exists($key, $interface_statistic_field)) {
                $this->wechatException('上游邦秒验数据入库异常 key:' . $key . '不在后台配置之中 lists:' . json_encode($interface_statistic_field,
                        JSON_UNESCAPED_UNICODE));
                throw new CustomException('data.key 对应的key' . $key . '不在后台配置之内');
            }
        });
    }

    /**
     * 获取统计字段
     * @return array
     */
    private function _getStatisticFieldForVerification(): array
    {
        $product_id      = $this->product_id_verify;
        $product_cuishou = Product::findOneItem(compact('product_id'), ['channel_stat', 'product_id']);
        $channel_stat    = json_decode($product_cuishou['channel_stat'], true);

        return $channel_stat['statistic_field'] ?? [];
    }

    /**
     * 校验日期是否合法
     * @throws CustomException
     */
    private function _validateDayForVerification()
    {
        $this->_determineDayIsValid();
    }

    /**
     * 校验数据源
     * @throws CustomException
     */
    private function _validateUpstreamForVerify()
    {
        $upstream = request()->post('upstream');
        if (!$upstream) {
            throw new CustomException('请传递upstream');
        }

        // 是否合法
        $product_id    = $this->product_id_verify;
        $product       = Product::findOneItem(compact('product_id'), 'channel_stat');
        $channel_stat  = json_decode($product['channel_stat'], true);
        $list_upstream = $channel_stat['upstream'] ?? [];
        if (!array_key_exists($upstream, $list_upstream)) {
            throw new CustomException('请传递合法的upstream');
        }
    }

    /**
     * 校验product_id是否合法
     * @throws CustomException
     */
    private function _validateProductIdForVerification()
    {
        $product_id = request()->post('product_id');
        if (!$product_id) {
            throw new CustomException('请传递product_id');
        }

        // 是否是数据合演子产品
        $father_id = $this->product_id_verify;
        $valid     = Product::getListByCondition(compact('father_id'), 'product_id')
            ->contains('product_id', $product_id);
        if (!$valid) {
            throw new CustomException('请传递合法的product_id');
        }
    }

    /**
     * 催收快捷版上游的数据入库
     * @throws CustomException
     */
    public function shortcuts()
    {
        // 校验参数
        $this->validateParamForShortcuts();

        // 入库
        $this->createOrUpdateShortcuts();
    }

    /**
     * 入库
     */
    private function createOrUpdateShortcuts()
    {
        // 条件 && 入库单元
        list($where, $item) = $this->_getWhereAndItem();

        MongoUpstreamShortcutStatistic::updateOrCreate($where, $item);
    }

    /**
     * 条件 && 入库单元
     * @return array
     */
    private function _getWhereAndItem(): array
    {
        list($day, $field, $upstream, $data, $node) = [
            $this->_getFormatDayForShortcuts(),
            trim(request()->post('field')),
            trim(request()->post('upstream')),
            $this->_getDataParamsForShortcuts(),
            trim(request()->post('node'))
        ];

        return [
            compact('day', 'field', 'upstream', 'node'),
            compact('day', 'field', 'upstream', 'data', 'node'),
        ];
    }

    /**
     * 获取data参数
     * @return array
     */
    private function _getDataParamsForShortcuts(): array
    {
        return $this->_formatDataFromStringToInt(request()->post('data'));
    }

    /**
     * 获取格式化的天
     * @return string
     */
    private function _getFormatDayForShortcuts(): string
    {
        return $this->_getFormatDay();
    }

    /**
     * 获取格式化的day
     * @return string
     */
    private function _getFormatDay(): string
    {
        return date('Ymd', strtotime(request()->post('day')));
    }

    /**
     * 校验参数
     * @throws CustomException
     */
    private function validateParamForShortcuts()
    {
        // 校验数据源
        $this->_validateUpstreamForShortcut();

        // 校验字段是否合法
        $this->validateFieldForShortcuts();

        // 校验日期
        $this->_determineDayIsValid();

        // 校验传递的统计字段是否合法
        $this->validateDataForShortcuts();

        // 校验节点是否合法
        $this->_validateNodeForShortcuts();
    }

    /**
     * @throws CustomException
     */
    private function _validateUpstreamForShortcut()
    {
        $upstream = request()->post('upstream');
        if (!$upstream) {
            throw new CustomException('请传递upstream');
        }

        // 是否合法
        $channel_stat  = $this->_getCuishouShortChannelStat();
        $list_upstream = $channel_stat['upstream'] ?? [];
        if (!array_key_exists($upstream, $list_upstream)) {
            throw new CustomException('请传递合法的upstream');
        }
    }

    /**
     * 获取催收快捷版产品的渠道统计配置
     * @return array
     */
    private function _getCuishouShortChannelStat(): array
    {
        if ($this->_channel_stat_cuishou_short) {
            return $this->_channel_stat_cuishou_short;
        }

        $product_id = $this->product_id_cuishou_short;
        $product    = Product::findOneItem(compact('product_id'), 'channel_stat');
        return $this->_channel_stat_cuishou_short = json_decode($product['channel_stat'], true);
    }

    /**
     * 校验节点是否合法
     * @throws CustomException
     */
    private function _validateNodeForShortcuts()
    {
        $this->_validateNode('邦信分快捷版');
    }

    /**
     * 校验节点
     *
     * @param string $action
     *
     * @throws CustomException
     * @throws \Exception
     */
    private function _validateNode($action = '')
    {
        $node = trim(request()->post('node'));

        // 如果节点不合法 则抛出异常
        if (!in_array($node, config('params.list_nodes'))) {
            $msg = '(' . $action . ')上游数据入库, 传递的节点 node:' . $node . '异常';
            $this->wechatException($msg);
            throw new CustomException($msg);
        }
    }

    /**
     * 校验传递的字段是否合法
     * @throws CustomException
     */
    private function validateDataForShortcuts()
    {
        list($data, $interface_statistic_field) = [
            request()->post('data'),
            $this->getStatisticFieldForCuishoucuts()
        ];
        if (!$data || !is_array($data)) {
            throw new CustomException('data异常');
        }

        array_walk($data, function ($item, $key) use ($interface_statistic_field) {
            if (!is_numeric($item) || $item < 0) {
                throw new CustomException('data.key ：' . $key . '对应的值' . $item . '不是一个合法的number');
            }

            // 统计字段不合法
            if (!array_key_exists($key, $interface_statistic_field)) {
                $this->wechatException('上游快捷版数据入库异常 key:' . $key . '不在后台配置之中 lists:' . json_encode($interface_statistic_field,
                        JSON_UNESCAPED_UNICODE));
                throw new CustomException('data.key 对应的key' . $key . '不在后台配置之内');
            }
        });
    }

    /**
     * 获取统计字段
     * @return array
     */
    private function getStatisticFieldForCuishoucuts(): array
    {
        $product_id      = $this->product_id_cuishou_short;
        $product_cuishou = Product::findOneItem(compact('product_id'), ['channel_stat', 'product_id']);
        $channel_stat    = json_decode($product_cuishou['channel_stat'], true);

        return $channel_stat['interface_statistic_field'];
    }

    /**
     * 校验日期
     * @throws CustomException
     * @throws \Exception
     */
    private function _determineDayIsValid()
    {
        $day = request()->post('day');

        // 是否不存在
        if (!$day) {
            throw new CustomException('day是必传参数');
        }
        return true;

        // 如果是本月的数据 可以直接操作
        if ($this->_determineInsertDataOfThisMonth($day)) {
            return;
        }

        // 如果是历史月份 则只可以修改上个月最后一天的且只能在1号的 00:20之前
        // 如果不是1号， 或者修改的不是上个月的最后一天的数据
        if (!$this->_determineUpdateYesterdayWhenFirstDayOfThisMonth($day)) {
            $msg = '抱歉,不可以修改历史月份的数据';
            throw new CustomException($msg);
        }

        // 如果是1号 但是如果不是在可控的时间范围内也是不可以的
        if (!$this->determineTheTimeInControl()) {
            $msg = '上游数据在推送上个月最后一天的数据, 但是时间超过了00:20, 所以无法入库';
            $this->wechatException($msg);
            throw new CustomException($msg);
        }
    }


    /**
     * 是在本月的一号修改昨天的信息吗
     *
     * @param string $amount_date
     *
     * @return bool
     */
    private function _determineUpdateYesterdayWhenFirstDayOfThisMonth(string $amount_date): bool
    {
        if (date('d') != '01') {
            return false;
        }

        return strtotime($amount_date) == strtotime(date('Y-m-d', strtotime('last day of last month')));
    }


    /**
     * @throws \Exception
     */
    private function _logAndReport()
    {
        list($product_id, $day) = [
            request()->post('product_id'),
            request()->post('day'),
        ];

        // 微信预警
        $msg = ($product_id ? 'product_id : ' . $product_id : '') . '上游数据入库 day : ' . $day . ' 正在通过后门修改历史数据';
        $this->wechatException($msg);
    }

    /**
     * 校验字段是否合法
     * @throws CustomException
     */
    private function validateFieldForShortcuts()
    {
        // 获取产品
        $product_id      = $this->product_id_cuishou_short;
        $product_cuishou = Product::findOneItem(compact('product_id'), ['channel_stat', 'product_id']);

        // 产品的配置是否完成
        $this->determineCuishouShortReady($product_cuishou);

        // 这个字段是否合法
        $this->determineFieldIsValid($product_cuishou);
    }

    /**
     * 这个字段是否合法
     *
     * @param Product $product_cuishou
     *
     * @throws CustomException
     * @throws \Exception
     */
    private function determineFieldIsValid(Product $product_cuishou)
    {
        list($field, $channel_stat, $upstream) = [
            trim(request()->post('field')),
            json_decode($product_cuishou['channel_stat'], true),
            trim(request()->post('upstream'))
        ];

        // 允许的渠道配置
        $list_fields = $channel_stat['interface_item'][$upstream] ?? [];
        if (!array_key_exists($field, $list_fields)) {
            $this->wechatException('上游快捷版数据入库异常 field:' . $field . '不在后台配置之中 lists:' . json_encode($channel_stat['interface_item'],
                    JSON_UNESCAPED_UNICODE));
            throw new CustomException('传入的field不合法');
        }
    }

    /**
     * 产品的配置是否完成
     *
     * @param Product $product_cuishou
     *
     * @throws CustomException
     */
    private function determineCuishouShortReady(Product $product_cuishou)
    {
        $msg = '邦信快捷版(210)产品还没有完成对上游的配置';

        if (!$product_cuishou) {
            throw new CustomException($msg);
        }

        // 校验配置
        $channel_stat    = json_decode($product_cuishou['channel_stat'], true);
        $something_wrong = !$channel_stat || !array_key_exists('interface_item',
                $channel_stat) || !array_key_exists('interface_statistic_field',
                $channel_stat) || !is_array($channel_stat['interface_item']) || !is_array($channel_stat['interface_statistic_field']);
        if ($something_wrong) {
            throw new CustomException($msg);
        }
    }

    /**
     * 产品调用量统计
     * @return int | array
     * @throws \Exception
     */
    public function amount()
    {
        // 校验参数
        $this->validateParamsForAmount();

        // 获取调用量
        return $this->getAmount();
    }

    /**
     * 获取调用量
     * @return int | array
     * @throws \Exception
     */
    private function getAmount()
    {
        // 是否需要分组
        if ($this->determineNeedGroup()) {
            return $this->getAmountWithGroup();
        }

        return $this->getAmountWithoutGroup();
    }

    /**
     *  分组获取调用量
     * @return array
     * @throws \Exception
     */
    private function getAmountWithGroup(): array
    {
        // 生成参数
        list($match, $group) = $this->genParamsForGroup();
        $pipeline = [
            ['$match' => $match],
            ['$group' => $group]
        ];

        // 聚合统计
        return MongoProductInvoked::raw(function ($collection) use ($pipeline) {
            return $collection->aggregate($pipeline);
        })->reduce(function ($carry, $item) {
                $carry[$item['_id']] = $item['amount'];
                return $carry;
            }, []);
    }

    /**
     * 生成参数
     * @return array
     * @throws \Exception
     */
    private function genParamsForGroup(): array
    {
        // 格式化时间范围
        $start_time = date('Ymd', strtotime(request()->post('start_time')));
        $end_time   = date('Ymd', strtotime(request()->post('end_time')));

        // 格式化需要检索的product_id
        $list_product_ids = $this->getProductIdByRequest();
        $where            = [
            'day'        => ['$gte' => $start_time, '$lte' => $end_time],
            'product_id' => ['$in' => $list_product_ids]
        ];

        // 格式化apikey
        $apikey = request()->post('apikey') ?? [];
        $apikey = is_string($apikey) ? [$apikey] : $apikey;

        if ($apikey) {
            $where['apikey'] = ['$in' => $apikey];
        }

        // 格式化group
        $group = ['_id' => '$' . trim(request()->post('group')), 'amount' => ['$sum' => '$amount']];

        return [$where, $group];
    }

    /**
     * 不需要分组的获取调用量
     * @return mixed
     * @throws \Exception
     */
    private function getAmountWithoutGroup()
    {
        // 格式化参数
        list($start_time, $end_time, $apikey, $list_product_ids) = $this->formatParamsForAmount();

        // 聚合
        return MongoProductInvoked::whereBetween('day', [$start_time, $end_time])
            ->when($apikey, function ($query, $apikey) {
                return $query->whereIn('apikey', $apikey);
            })
            ->whereIn('product_id', $list_product_ids)
            ->sum('amount');
    }


    /**
     * 是否需要分组
     * @return bool
     */
    private function determineNeedGroup(): bool
    {
        return !!trim(request()->post('group'));
    }

    /**
     * 格式化参数
     * @throws \Exception
     */
    private function formatParamsForAmount(): array
    {
        // 格式化时间范围
        $start_time = date('Ymd', strtotime(request()->post('start_time')));
        $end_time   = date('Ymd', strtotime(request()->post('end_time')));

        // 格式化apikey
        $apikey = request()->post('apikey') ?? [];
        $apikey = is_string($apikey) ? [$apikey] : $apikey;

        // 格式化需要检索的product_id
        $list_product_ids = $this->getProductIdByRequest();

        return [$start_time, $end_time, $apikey, $list_product_ids];
    }

    /**
     * 校验参数
     * @throws \Exception
     */
    private function validateParamsForAmount()
    {
        // 检查start_time参数
        $this->validateTimeParamsForAmount('start_time');

        // 检查end_time参数
        $this->validateTimeParamsForAmount('end_time');

        // 校验group参数
        $this->validateGroupParamsForAmount();
    }

    /**
     * 校验group参数
     * @throws \Exception
     */
    private function validateGroupParamsForAmount()
    {
        $group = trim(request()->post('group'));
        if ($group && !in_array($group, ['apikey', 'day'])) {
            throw new \Exception('请传入合法的group参数');
        }
    }

    /**
     * 检查时间参数
     *
     * @param string $field
     *
     * @throws \Exception
     */
    private function validateTimeParamsForAmount(string $field)
    {
        $time_params = trim(request()->post($field));
        if (!$time_params) {
            throw new \Exception('请传入合法的' . $field . '参数');
        }

        $time_format = date('Ymd', strtotime($time_params));
        if ($time_format == '19700101') {
            throw new \Exception('请传入合法的' . $field . '参数');
        }
    }

    /**
     * 每天的调用量
     * @return array
     * @throws \Exception
     */
    public function day(): array
    {
        // 下辖各个产品的信息
        $list_product_ids = $this->getProductIdByRequest();

        return array_reduce($list_product_ids, function ($carry, $product_id) {
            // 映射
            $mapping         = $this->list_product_mapping[$product_id] ?? 'NotExists' . $product_id;
            $carry[$mapping] = app('statistic')->driver($mapping)->day();
            return $carry;
        });
    }

    /**
     * 日报
     * @return array
     * @throws \Exception
     */
    public function report(): array
    {
        // 下辖各个产品的信息
        $list_product_ids = $this->getProductIdByRequest();

        return array_reduce($list_product_ids, function ($carry, $product_id) {
            // 映射
            $mapping         = $this->list_product_mapping[$product_id] ?? 'NotExists' . $product_id;
            $carry[$mapping] = app('statistic')->driver($mapping)->report();
            return $carry;
        });
    }

    /**
     * 列表
     * @return array
     * @throws \Exception
     */
    public function list(): array
    {
        // 下辖各个产品的信息
        $list_product_ids = $this->getProductIdByRequest();

        return array_reduce($list_product_ids, function ($carry, $product_id) {
            // 映射
            $mapping         = $this->list_product_mapping[$product_id] ?? 'NotExists' . $product_id;
            $carry[$mapping] = app('statistic')->driver($mapping)->list();
            return $carry;
        });
    }

    /**
     * 详情统计
     * @return array
     * @throws \Exception
     */
    public function detail(): array
    {
        // 下辖各个产品的信息
        $list_product_ids = $this->getProductIdByRequest();

        return array_reduce($list_product_ids, function ($carry, $product_id) {
            // 映射
            $mapping         = $this->list_product_mapping[$product_id] ?? 'NotExists' . $product_id;
            $carry[$mapping] = app('statistic')->driver($mapping)->detail();
            return $carry;
        });
    }

    /**
     * 当前请求下辖的product_id
     * @return array
     * @throws \Exception
     */
    private function getProductIdByRequest(): array
    {
        $product_key = trim(request()->input('key'));
        $product_id  = trim(request()->post('product_id', ''));

        //特殊产品
        if (($res = $this->validSpecialProduct($product_key, $product_id)) !== false) {
            return $res;
        }

        // key 下辖的产品id
        $product_id_string = app('redis')
            ->connection('db_backend')
            ->hGet($this->key_cache_list_product_key_relation, $product_key);


        $list_product_ids = json_decode($product_id_string, true);
        // 是否传递了product_id
        if ($product_id && !in_array($product_id, $list_product_ids)) {
            throw new \Exception('传递的product_id与key冲突');
        }

        // 如果同时限定了product_id 则返回[$product_id] 否则key限定的product_id列表
        return $product_id ? [(int)$product_id] : $list_product_ids;
    }

    /**
     * 特殊产品的校验
     *
     * @access protected
     *
     * @param $product_key string 产品的key
     * @param $product_id  int 产品的ID
     *
     * @return boolean|array
     **/
    private function validSpecialProduct($product_key, $product_id)
    {
        //催收分析快捷版总产品（210）
        if ($product_key == '99bbdb8426f8b4e8d0cc3ebd92484590' && $product_id == 210) {
            return [210];
        }

        //金盾V2
        if ($product_key == '973cfad2f2b36e7c8c0a33b959efb2bb' && $product_id == 615) {
            return [615];
        }

        //金盾相关产品
        $goldenShieldProductKey = [
            603 => '9eb88769808437cef9be2d19685dc013',
            612 => 'c2988efdbd8549d7aef058f3ef3fec1a',
            613 => 'a42cb915eeb843bea2cf59f21e2f87da',
            614 => 'e5cbd0a1a3474dde82dba6b11212855c'
        ];
        if (in_array($product_key, $goldenShieldProductKey)) {
            if ($product_id) {
                return [$product_id];
            }
            return array_keys($goldenShieldProductKey);
        }

        return false;
    }
}
