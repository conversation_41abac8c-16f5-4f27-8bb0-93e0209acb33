<?php

namespace App\Http\Repository;

use App\Imports\ChannelCostAdjustImport;
use App\Models\Account;
use App\Models\Approval;
use App\Models\Channel;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelInterface;
use App\Models\Common\CommonEnumModel;
use App\Models\ConfigOperator;
use App\Models\Customer;
use App\Models\Product;
use App\Models\SystemUser;
use App\Models\ChannelCostMinconsumeSpread;
use App\Utils\Helpers\Func;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

class ChannelCostAdjustRepository
{

    /**
     * 通过上传excel批量添加渠道成本调整
     *
     * 1 校验数据是否完成
     *  客户名称是否正确
     *  账号名称是否正确
     *  产品名称是否正确
     *  渠道名称是否正确
     *  接口名称是否正确
     *  运营商名称名称是否正确
     *
     *
     * @return boolean
     * <AUTHOR> 2024-05-15 19:18:01
     * @throws Exception
     */
    public function batch_add() {
        $file = request()->file('file');
        $user_cookie  = request()->post('user_cookie');
        if(empty($file)){
            exit('上传文件为空！');
        }
        if (!$file->isValid()) {
             throw new Exception('文件异常');
        }
        //扩展名
        $ext = $file->getClientOriginalExtension();
        if (!in_array($ext, ['xls', 'xlsx'])) {
            throw new Exception('上传文件类型不对,请检查是否为xls或 xlsx类型');
        }
        $size = $file->getSize();//获取上传文件的大小
        if(500 * 1024 < $size){
            throw new Exception('文件超过500KB');
        }
        //原文件名
        $originalName = $file->getClientOriginalName();
        $originalName = explode('.', $originalName);
        $noOriginalName = $originalName[0];//不带扩展名的文件名
        $realPath = $file->getRealPath();
        $filename = $noOriginalName . '_' . date('YmdHi') . '_' . uniqid() . '.' . $ext;

        $username = Func::getUserNameFromCookie($user_cookie);

        $bool = Storage::disk('excel')->put($filename, file_get_contents($realPath));
        if (!$bool) {
            throw new Exception('上传文件服务存储有误');
        }
        $saveFilePath = storage_path() . '/excel/upload/' . $filename;


        $data = Excel::toArray(new ChannelCostAdjustImport, $saveFilePath);

        //获取第一个sheet
        $data = $data[0];
        //去除说明文本和表头
        unset($data[0]);
        unset($data[1]);
        unset($data[2]);
        unset($data[3]);
        unset($data[4]);
        unset($data[5]);

        //这几个全部是 名字 => id 用于校验传入的excel名字是否正确
        $product_map = Product::getAllProduct();
        $product_map = array_column($product_map, 'value','label');

        $account_map = Account::getAllAccount();
        $apikey_map  = array_column($account_map, 'value','account_id');
        $account_map = array_column($account_map, 'account_id','label');

        $customer_map = Customer::getAllCustomer(['customer_id','name']);
        $customer_map = array_column($customer_map, 'customer_id','name');


        $channel_map = Channel::getAllChannel();
        $channel_map = array_column($channel_map, 'channel_id', 'label');


        $source_map = CommonEnumModel::getListByType(1);
        $source_map = array_column($source_map, 'name', 'value');

        $all_channel_interface = ChannelInterface::getAllChannel();
        $channel_interface_map = [];
        foreach($all_channel_interface as $ci_info){
            $channel_interface_name = $ci_info['label'];
            $channel_id = $ci_info['channel_id'];
            $channel_interface_map[$channel_interface_name][$channel_id] = $ci_info['id'];
        }

        $operator_map = ConfigOperator::getAllOperator();
        $operator_map = array_column($operator_map, 'operator', 'name');


        $cost_adjust_add_data = [];
        $approval_data = [];
        $row_no = 6;
        foreach ($data as $info) {
            $row_no = $row_no + 1;
            $invoice_id = $info[0];
            if (empty($invoice_id)) {
                continue;//过滤excel空白行
            }

            $time = Carbon::instance(Date::excelToDateTimeObject($info[8]))->timestamp - 28800;

            //excel中存储的是相关名称 这里的变量名直接为了方便使用最终的名改字,不再多次转换
            $title        = $info[0]; //标题
            $source       = $info[1]; //来源
            $customer_id  = $info[2]; //客户
            $account_id   = $info[3]; //账号
            $product_id   = $info[4]; //产品
            $channel_id   = $info[5]; //渠道
            $interface_id = $info[6]; //接口
            $operator     = $info[7]; //运营商
            $date         = date("Y-m-d",$time);//调整日期
            $fee_number   = $info[9]; //调整计费用量
            $money        = $info[10];//调整费用
            $remark       = $info[11];//备注信息

            if(!isset($source_map[$source])){
                throw new Exception("第".$row_no."行 来源名称错误!");
            }
            $source = $source_map[$source];

            if(!isset($customer_map[$customer_id])){
                throw new Exception("第".$row_no."行 客户名称错误!");
            }
            $customer_id = $customer_map[$customer_id];

            if(!isset($account_map[$account_id])){
                throw new Exception("第".$row_no."行 账号名称错误!");
            }
            $account_id = $account_map[$account_id];
            $apikey = $apikey_map[$account_id];

            if(!isset($product_map[$product_id])){
                throw new Exception("第".$row_no."行 产品名称错误!");
            }
            $product_id = $product_map[$product_id];

            if(!isset($channel_map[$channel_id])){
                throw new Exception("第".$row_no."行 渠道名称错误!");
            }
            $channel_id = $channel_map[$channel_id];

            if(!isset($channel_interface_map[$interface_id][$channel_id])){
                throw new Exception("第".$row_no."行 接口名称错误!");
            }
            $interface_id = $channel_interface_map[$interface_id][$channel_id];

            if(!empty($operator)) {
                if (!isset($operator_map[$operator])) {
                    throw new Exception("第" . $row_no . "行 运营商名称错误!");
                }
                $operator = $operator_map[$operator];
            }else{
                $operator = '';
            }

            $now  = time();

            $data = [
                'title' => $title,
                'source' => $source,
                'customer_id' => $customer_id,
                'account_id' => $account_id,
                'apikey' => $apikey,
                'product_id' => $product_id,
                'channel_id' => $channel_id,
                'interface_id' => $interface_id,
                'operator' => $operator,
                'date' => $date,
                'fee_number' => $fee_number,
                'money' => $money,
                'remark' => $remark,
                'admin' => $username,
                'create_time' => $now,
                'update_time' => $now,
                'category' => 2, #后台手动添加的默认为 【对账调整】
            ];

            if (1000 > abs($money)) {
                $cost_adjust_add_data[] = $data;
            } else {
                $approval_data[] = $data;
            }
        }
        if ($cost_adjust_add_data) {
            ChannelAccountAdjust::insert($cost_adjust_add_data);
        }
        if ($approval_data) {
            $this->costAdjustToApproval($approval_data,$user_cookie);
        }
        return true;
    }


    public function add()
    {
        $user_cookie = request()->post('user_cookie');
        $account_id = request()->post('account_id');
        list($customer_id, $account_id) = explode('_', $account_id);
        $title = request()->post('title');
        $source = request()->post('source'); //来源
        $product_id = request()->post('product_id'); //产品
        $channel_id = request()->post('channel'); //渠道
        $interface_id = request()->post('interface'); //接口
        $operator = request()->post('operator'); //运营商
        $date = request()->post('date');//调整日期
        $fee_number = request()->post('fee_number'); //调整计费用量
        $money = request()->post('money');//调整费用
        $remark = request()->post('remark');//备注信息
        $encrypt = request()->post('encrypt');//备注信息
        $category = 2;//后台手动添加的默认为 【对账调整】
        $id = request()->post('id', null);

        $username = Func::getUserNameFromCookie($user_cookie);
        if (!$username) {
            throw new Exception("登录已过期，请重新登录!");
        }
        $account_map = Account::getAllAccount();
        $apikey_map = array_column($account_map, 'value', 'account_id');

        if (!isset($apikey_map[$account_id])) {
            throw new Exception("账号id错误!");
        }
        $apikey = $apikey_map[$account_id];


        $cost_adjust_add_data = [
            'title' => $title,
            'source' => $source,
            'customer_id' => $customer_id,
            'account_id' => $account_id,
            'apikey' => $apikey,
            'product_id' => $product_id,
            'channel_id' => $channel_id,
            'interface_id' => $interface_id,
            'operator' => $operator,
            'date' => $date,
            'fee_number' => $fee_number,
            'money' => $money,
            'remark' => $remark,
            'admin' => $username,
            'encrypt' => $encrypt,
            'category' => $category, #后台手动添加的默认为 【对账调整】
        ];

        if (1000 > abs($money)) { //审批通过 或者 调整金额不超过1000 直接插入
            return ChannelAccountAdjust::addData($cost_adjust_add_data);
        } else { // 调整金额超过1000
            return $this->costAdjustToApproval([$cost_adjust_add_data], $user_cookie);
        }
    }


    /**
     * @param $aDatas
     * @param $sUserCookie
     * @return bool
     */
    private function costAdjustToApproval($aDatas = [], $sUserCookie = '')
    {
        $customer_map = Customer::getAllCustomer(['customer_id', 'name']);
        $customer_map = array_column($customer_map, 'name', 'customer_id');

        $account_map = Account::getAllAccount();
        $account_map = array_column($account_map, 'label', 'account_id');

        $aProductList = Product::getAllProduct();
        $aProductMap = array_column($aProductList, 'product_name', 'product_id');

        $approval_data = [];
        foreach ($aDatas as $aData) {
            $sCustomerId = $aData['customer_id'];
            $sAccountId = $aData['account_id'];
            $iProductId = $aData['product_id'];
            $sMoney = $aData['money'];
            $sRemark = $aData['remark'];
            $sdate = $aData['date'];
            $customer = $customer_map[$sCustomerId] ?? '';
            $account = $account_map[$sAccountId] ?? '';
            $product = $aProductMap[$iProductId] ?? '';

            $sApplyContent = sprintf('渠道成本调整, 客户:<%s>, 账号:<%s>, 子产品:「%s」,  调整日期: %s, 原因:「%s」,  金额: 「%s」',
                $customer, $account, $product, $sdate, $sRemark, $sMoney);

            $approval_data[] = [
                'customer_id' => $sCustomerId,
                'account_id' => $sAccountId,
                'product_id' => $iProductId,
                'uri' => Approval::URL_CHANNEL_COST_ADJUST,
                'data' => $aData,
                'apply_content' => $sApplyContent,
            ];
        }
        if ($approval_data){
            return ApprovalRepository::BatchAddApproval($approval_data, $sUserCookie);
        }
        return true;
    }


    /**
     * @param $aData
     * @return mixed
     */
    public static function saveCostAdjustByApproval($aData)
    {
        return ChannelAccountAdjust::addData($aData);
    }
}
