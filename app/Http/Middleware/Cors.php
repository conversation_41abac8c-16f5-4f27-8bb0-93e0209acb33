<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;

class Cors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        if ($request->getMethod() == "OPTIONS") {
            return response()->json(
                'ok',
                200,
                [
                    # 下面参数视request中header而定
                    'Access-Control-Allow-Origin'  => '*',
                    'Access-Control-Allow-Headers' => 'Authorization,x-csrf-token,x-requested-with,content-type,select,mobile-token',
                    'Access-Control-Allow-Methods' => 'GET,POST,PUT,OPTIONS,PATCH,DELETE,HEAD'
                ]
            );
        }

        $response = $next($request);
        ob_clean();
        $response->headers->set('Access-Control-Allow-Origin', '*');
        $response->headers->set('Access-Control-Allow-Methods', 'GET,POST,PUT,OPTIONS,PATCH,DELETE,HEAD');
        $response->headers->set('Access-Control-Allow-Headers', 'Authorization,x-csrf-token,x-requested-with,content-type,mobile-token');
        return $response;


//        $response = $next($request);
//
//
//        if ($response instanceof Response) {
//            return $next($request)
//                ->header('Access-Control-Allow-Origin', '*')
//                ->header('Access-Control-Allow-Methods', 'GET,POST,PUT,OPTIONS,PATCH,DELETE,HEAD')
//                ->header('Access-Control-Allow-Headers', 'x-csrf-token,x-requested-with,content-type');
//        }
//        //  如果是实列 \Symfony\Component\HttpFoundation\Response::class
//        $response->headers->set('Access-Control-Allow-Origin', '*');
//        $response->headers->set('Access-Control-Allow-Methods', 'GET,POST,PUT,OPTIONS,PATCH,DELETE,HEAD');
//        $response->headers->set('Access-Control-Allow-Headers', 'x-csrf-token,x-requested-with,content-type');
//        return $response;
    }
}
