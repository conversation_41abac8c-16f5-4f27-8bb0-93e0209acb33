<?php

namespace App\Http\Middleware;

use App\Models\SystemUserRole;
use App\Utils\Helpers\Func;
use Closure;
use Illuminate\Http\Request;

/**
 * 发票业务权限部分逻辑
 */
class InvoiceMiddleware
{
    const ROLE_ADMIN     = 1; //超级管理员
    const ROLE_OPERATE   = 3; //运营
    const ROLE_DEVELOPER = 5; //开发
    const ROLE_QA        = 6; //测试
    const ROLE_SALESMAN  = 16;//销售
    const ROLE_FINANCIAL = 21;//财务
    const ROLE_OPERATE_ASSISTANT = 45; //运营-助理
    const ROLE_OPERATE_CUSTOMER  = 49; //运营-客户
    const ROLE_OPERATE_CHANNEL   = 54; //运营-渠道

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next){
        $path = $request->path();
        $user_cookie = $request->get('user_cookie');

        if (empty($user_cookie)) {
            return response()->json(['status' => 2, 'msg' => '请登录后操作', 'data' => '']);
        }

        //获取登录客户的角色信息
        $username = Func::getUserNameFromCookie($user_cookie);
        if (empty($username)) {
            return response()->json(['status' => 2, 'msg' => '请登录后操作', 'data' => '']);
        }
        //全部权限
        $all = [
            self::ROLE_ADMIN,
            self::ROLE_OPERATE,
            self::ROLE_DEVELOPER,
            self::ROLE_QA,
            self::ROLE_SALESMAN,
            self::ROLE_FINANCIAL,
            self::ROLE_OPERATE_ASSISTANT,
            self::ROLE_OPERATE_CUSTOMER,
            self::ROLE_OPERATE_CHANNEL,
        ];
        $salesman  = [self::ROLE_ADMIN,self::ROLE_SALESMAN,self::ROLE_OPERATE,self::ROLE_OPERATE_CUSTOMER,self::ROLE_OPERATE_CHANNEL];
        $financial = [self::ROLE_ADMIN,self::ROLE_FINANCIAL];
        $operate   = [self::ROLE_ADMIN,self::ROLE_OPERATE,self::ROLE_OPERATE_ASSISTANT];
        $auth_map = [
            'invoice/invoice_list'          => ['role' => $all, 'msg' => '无查看该信息权限'],
            "invoice/options"               => ['role' => $all, 'msg' => '无查看该信息权限'],
            "invoice/customer_list"         => ['role' => $all, 'msg' => '无查看该信息权限'],
            "invoice/informations"          => ['role' => $all, 'msg' => '无查看该信息权限'],
            "invoice/address"               => ['role' => $all, 'msg' => '无查看该信息权限'],
            "invoice/invoice_info"          => ['role' => $all, 'msg' => '无查看发票详情权限'],
            "invoice/unapplied_list"        => ['role' => $all, 'msg' => '无查看该信息权限'],
            "invoice/apply"                 => ['role' => $salesman, 'msg' => '无申请开票权限'],
            "invoice/cancel"                => ['role' => $salesman, 'msg' => '无取消开票权限'],
            "invoice/reject"                => ['role' => $financial, 'msg' => '无驳回开票权限'],
            "invoice/pass"                  => ['role' => $financial, 'msg' => '无审核开票权限'],
            "invoice/batch_pass"            => ['role' => $financial, 'msg' => '无审核开票权限'],
            "invoice/issue"                 => ['role' => $financial, 'msg' => '无开票权限'],
            "invoice/issue_by_excel"        => ['role' => $financial, 'msg' => '无开票权限'],
            "invoice/add_express_no"        => ['role' => $operate,   'msg' => '无添加快递单号权限'],
            // "invoice/collect"               => ['role' => $financial, 'msg' => '无权限查看该数据'],
            // "invoice/uninvoice_money_count" => ['role' => $financial, 'msg' => '无权限查看该数据'],
            // "invoice/invoice_detail_list"   => ['role' => $financial, 'msg' => '无权限查看该数据'],
        ];

        $roles_map = key_exists($path, $auth_map);
        if(!$roles_map){
            return $next($request);
            // return response()->json(['status' => 2, 'msg' => $path.' 该操作未配置权限,请联系开发人员!', 'data' => '']);
        }

        $roles = SystemUserRole::geListByUsername($username,$auth_map[$path]['role']);

        //不为空即为有权限进行操作
        if(empty($roles)){
            return response()->json(['status' => 2, 'msg' => $auth_map[$path]['msg'], 'data' => '']);
        }

        return $next($request);
    }
}
