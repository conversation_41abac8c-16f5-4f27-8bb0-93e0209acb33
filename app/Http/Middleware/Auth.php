<?php

namespace App\Http\Middleware;

use App\Models\Product;
use Closure;

class Auth
{
    /*
     * product key 列表 && 和产品ID的映射关系
     * */
    private $key_cache_list_product_key_relation = 'backend_api_cache_product_relation';

    /*
     * 存储时长
     * */
    private $redis_cache_minutes = 60;

    /*
     * 存储的连接
     * */
    private $redis_connection = 'db_backend';

    /**
     * Handle an incoming request.
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure $next
     * @return mixed
     * @throws \Exception
     */
    public function handle($request, Closure $next)
    {
        // 校验是否合法
        $validate_handle = $this->validateHandle();

        if (!$validate_handle) {
            return response()->json(['status' => 1, 'msg' => '身份验证不通过', 'data' => '']);
        }
        return $next($request);
    }

    /**
     * 校验是否通过
     *
     */
    private function validateHandle(): bool
    {
        // 校验是否含有
        if (!$this->validateParamsForHandle()) {
            return false;
        }

        // 如果缓存信息则设置缓存信息
        $this->determineHasCache();

        // 是否有对应配置信息
        return $this->determineMatchingCache();
    }

    /**
     * 是否有对应配置信息
     * @return bool
     */
    private function determineMatchingCache(): bool
    {
        $product_key = trim(\request()->input('key'));
        if (!$product_key) {
            $request_json = file_get_contents("php://input");
            $data = json_decode($request_json, true);
            $product_key = $data['key'];
        }
        return (bool)app('redis')->connection($this->redis_connection)
            ->hExists($this->key_cache_list_product_key_relation, $product_key);
    }

    /**
     * 校验参数是否合法
     */
    private function validateParamsForHandle(): bool
    {
        $product_key = \request()->input('key', '');
        if (!$product_key) {
            $request_json = file_get_contents("php://input");
            $data = json_decode($request_json, true);
            $product_key = $data['key'] ? $data['key'] : '';
        }
        return trim($product_key);
    }

    /**
     * 是否有缓存信息
     */
    private function determineHasCache()
    {
        // 如果没有缓存信息的话 则刷新缓存
        $cache_exists = app('redis')->connection($this->redis_connection)
            ->exists($this->key_cache_list_product_key_relation);
        if (!$cache_exists) {
            $this->setListProductKeyRelation();
        }
    }

    /**
     * 设置list_product_key_relation属性
     */
    public function setListProductKeyRelation()
    {
        // 获取
        $list_products = $this->getProductByCondition([]);
        $list_father_ids = $list_products->pluck('father_id')->toArray();

        // 缓存
        $list_products_relation = $list_products->reduce(function ($carry, $item) use ($list_father_ids) {
            list($product_key, $product_id) = [$item['product_key'], $item['product_id']];

            // 舍弃没有product_key
            if (!trim($product_key)) {
                return $carry;
            }

            // 如果时father_id=0 && product_id是其他的产品的father_id 则pass
            if ($this->determineIsFatherLevel($item, $list_father_ids)) {
                return $carry;
            }

            $carry[$product_key][] = $product_id;
            return $carry;
        }, []);


        // 缓存
        $redis_connection = app('redis')->connection($this->redis_connection);
        collect($list_products_relation)->each(function ($list_product_ids, $product_key) use ($redis_connection) {
            $redis_connection->hSet($this->key_cache_list_product_key_relation, $product_key, json_encode($list_product_ids));
        });

        // 设置过期时间
        $redis_connection->expire($this->key_cache_list_product_key_relation, $this->redis_cache_minutes * 60);
    }

    /**
     * 是否是父级别的产品
     * @param $product
     * @param $list_father_ids
     * @return bool
     */
    private function determineIsFatherLevel($product, $list_father_ids): bool
    {
        if ($product['father_id'] != 0) {
            return false;
        }

        return in_array($product['product_id'], $list_father_ids);
    }

    /**
     * 获取产品列表
     * @param array $where
     * @return mixed
     */
    private function getProductByCondition(array $where)
    {
        return Product::where($where)
            ->get();
    }
}
