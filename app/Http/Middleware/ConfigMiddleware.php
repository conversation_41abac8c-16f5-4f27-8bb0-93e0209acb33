<?php

namespace App\Http\Middleware;

use Illuminate\Http\JsonResponse;

class ConfigMiddleware
{
	public function handle($request, \Closure $next)
	{
		//预检请求处理
		if ('OPTIONS' == $request->getMethod()) {
			$data   = ['status' => 0, 'msg' => 'OK', 'data' => []];
			$code   = 200;
			$header = [
				'Access-Control-Allow-Origin'  => '*',
				'Access-Control-Allow-Methods' => 'POST',
			];
			
			return new JsonResponse($data, $code, $header);
		}
		
		//允许跨域访问
		$response = $next($request);
		ob_clean();
		$response->headers->set('Access-Control-Allow-Origin', '*');
		$response->headers->set('Access-Control-Allow-Methods', 'POST');
		
		return $response;
	}
}