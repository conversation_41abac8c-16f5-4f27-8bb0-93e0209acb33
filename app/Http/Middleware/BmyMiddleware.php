<?php

namespace App\Http\Middleware;


use Closure;

class BmyMiddleware
{
    protected $product_key = 'fbaace1340a8706863ed6ae17560355c';

    public function handle($request, Closure $next)
    {
        $key = request()->get('key');
        if (empty($key)) {
            return response()->json(['status' => 1, 'msg' => '身份验证不通过', 'data' => '']);
        }

        if ($key != $this->product_key) {
            return response()->json(['status' => 2, 'msg' => '未知的产品KEY', 'data' => '']);
        }

        try {
            return $next($request);
        } catch (\Exception $exception) {
            $status = $exception->getCode();
            $msg    = $exception->getMessage();
            $data   = [];
            return response()->json(compact('status', 'msg', 'data'));
        }
    }
}