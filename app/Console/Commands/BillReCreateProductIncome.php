<?php

namespace App\Console\Commands;

use App\Jobs\CreateBillProductIncomeJob;
use App\Models\Account;
use App\Models\BillCustomerIncome;
use App\Models\ConfigPriceCustomer;
use Illuminate\Console\Command;

class BillReCreateProductIncome extends Command
{
	protected $signature = "bill:re_create_product_income
	{--start_date= : 账单日(起始)，默认昨日（格式Ymd）}
	{--end_date= : 账单日(截止)，默认昨日（格式Ymd）}
    {--customer_id= : 客户ID（多个客户ID以,隔开）}
    {--product_id= : 产品ID（父产品ID，多个产品以,隔开）}
	";
	
	protected $description = "重新生成产品营收账单";
	
	private $start_date;
	private $end_date;
	private $customer_ids;
	private $product_ids;
	
	public function __construct()
	{
		parent::__construct();
		
		$this->start_date = $this->end_date = date('Ymd', strtotime('-1 days'));
	}
	
	public function handle()
	{
		if (!$this->checkParams()) {
			return;
		}
		
		//获取所有符合条件的客户营收账单
		$bills = $this->getBillCustomerIncomes();
		
		//获取当前账单中使用的计费配置
		$configs = $this->getConfigs(array_values(array_unique(array_column($bills, 'config_price_customer_id'))));
		
		//将客户营收账单数据按照日期、计费配置进行分组,生成需要重新分割的配置数据
		$splitConfigs = $this->groupBillCustomerIncomes($bills, $configs);
		
		foreach ($splitConfigs as $splitConfig) {
			//(new CreateBillProductIncomeJob($splitConfig['config'], $splitConfig['date'], $splitConfig['bill_ids']))->handle();
			dispatch(new CreateBillProductIncomeJob($splitConfig['config'], $splitConfig['date'], $splitConfig['bill_ids']));
		}
		
		$this->output->success("账单任务已成功入队，请稍后确认账单是否成功执行");
	}
	
	/**
	 * 对客户营收账单进行分组
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/23 13:52
	 *
	 * @return array
	 */
	private function groupBillCustomerIncomes($bills, $configs)
	{
		if (empty($bills)) {
			return [];
		}
		
		$result = [];
		foreach ($bills as $bill) {
			$config_id = $bill['config_price_customer_id'];
			$date      = $bill['date'];
			$bill_id   = $bill['id'];
			$key       = $config_id . '_' . $date;
			if (!array_key_exists($key, $result)) {
				$result[$key] = [
					'config'   => $configs[$config_id],
					'date'     => $date,
					'bill_ids' => [],
				];
			}
			$result[$key]['bill_ids'][] = $bill_id;
		}
		
		return $result;
	}
	
	/**
	 * 获取所有符合条件的客户营收账单
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/22 15:57
	 *
	 * @return array
	 */
	private function getBillCustomerIncomes()
	{
		return BillCustomerIncome::where(function ($query) {
			if ($this->customer_ids) {
				$apikeys = Account::whereIn('customer_id', $this->customer_ids)
								  ->where('is_delete', 0)
								  ->where('father_id', '<>', '0')
								  ->pluck('apikey')
								  ->toArray();
				
				if ($apikeys) {
					$query->whereIn('apikeys', $apikeys);
				}
			}
			
			if ($this->product_ids) {
				$query->whereIn('father_id', $this->product_ids);
			}
		})
								 ->where('date', '>=', $this->start_date)
								 ->where('date', '<=', $this->end_date)
								 ->get()
								 ->toArray();
	}
	
	/**
	 * 获取计费配置
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/22 14:54
	 *
	 * @param $config_ids array 计费配置ID
	 *
	 * @return array
	 */
	private function getConfigs($config_ids)
	{
		//获取计费配置
		$data = ConfigPriceCustomer::whereIn('id', $config_ids)
								   ->get()
								   ->toArray();
		
		return array_column($data, null, 'id');
	}
	
	/**
	 * 校验参数
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/22 14:21
	 *
	 * @return boolean
	 */
	private function checkParams()
	{
		//开始日期
		$start_date = $this->input->getOption('start_date');
		if ($start_date) {
			$this->start_date = $start_date;
		}
		
		if (!preg_match('/^\d{8}$/', $this->start_date)) {
			$this->output->error("账单日(起始)格式不正确");
			
			return false;
		}
		
		//截止日期
		$end_date = $this->input->getOption('end_date');
		if ($end_date) {
			$this->end_date = $end_date;
		}
		
		if (!preg_match('/^\d{8}$/', $this->end_date)) {
			$this->output->error("账单日(截止)格式不正确");
			
			return false;
		}
		
		//客户ID
		$customer_ids = $this->input->getOption('customer_id');
		if ($customer_ids) {
			$this->customer_ids = explode(',', $customer_ids);
		}
		
		//产品ID
		$product_ids = $this->input->getOption('product_id');
		if ($product_ids) {
			$this->product_ids = explode(',', $product_ids);
		}
		
		return true;
	}
}