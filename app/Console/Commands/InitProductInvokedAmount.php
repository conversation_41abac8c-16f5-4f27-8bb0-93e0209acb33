<?php

namespace App\Console\Commands;

use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;
use App\Models\{
    Account, AccountProduct, MongoProductInvoked
};
use Illuminate\Support\Facades\DB;

class InitProductInvokedAmount extends Command
{
    use WechatExceptionTrait;
    /*
     * 缓存天数
     * */
    private $cache_days = 1;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'product:init-invoked';

    /*
     * redis连接
     * */
    private $redis_connection = 'db_backend';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = <<<EOF
    
                            这个新版本(全量)
                            执行时间: 每天凌晨3点执行
                            执行目的: 初始化每天每个待用产品的日月年调用量的redis属性
EOF;

    /*
     * 需要执行的总条数
     * */
    private $items_total;

    /*
     * 进度条对象
     * */
    private $progress_bar;

    /*
    * 缓存信息的前缀
    * product_invoked_{$product_id}_{$apikey}_{$day}
    * */
    private $cache_key_invoked_prefix = 'product_invoked_';

    /**
     * @throws \Exception
     */
    public function handle()
    {
        try {
            // 设置进程条
            $this->setProgressBar();

            // Do
            $this->handleDo();
        } catch (\Exception $e) {
            $msg = 'php artisan product:init-invoked 异常 msg:' . $e->getMessage();
            $this->wechatException($msg);
        }
    }

    /**
     * Do
     */
    private function handleDo()
    {
        $status = 1;
        Account::where(compact('status'))
            ->get(['apikey', 'account_id'])
            ->map(function ($item) {
                // 执行缓存
                $this->handleCache($item['apikey'], $item['account_id']);
            });
        $this->info(PHP_EOL . ' Well Done!');
    }

    /**
     * 设置进程条
     */
    private function setProgressBar()
    {
        // 设置总数
        $this->items_total = $this->computedTotalNumber();

        $this->progress_bar = $this->output->createProgressBar($this->items_total);
    }

    /**
     * 计算总数
     * @return int
     */
    private function computedTotalNumber(): int
    {
        $sql = 'select count(id) as total from account_product 
where account_id in(SELECT account_id FROM `account` where `status`=1) and status =1';
        $items_count = \DB::connection('mysql')
            ->select($sql);
        return $items_count[0]['total'] ?? 0;
    }

    /**
     * 执行缓存
     * @param string $apikey
     * @param string $account_id
     */
    private function handleCache(string $apikey, string $account_id)
    {
        $status = 1;
        AccountProduct::where(compact('status', 'account_id'))
            ->get(['product_id'])
            ->each(function ($item) use ($apikey) {
                // 解决缓存问题
                $this->handleCacheDo($apikey, $item['product_id']);

                $this->progress_bar->advance();
            });
    }

    /**
     * 缓存DO
     * @param string $apikey
     * @param string $product_id
     */
    private function handleCacheDo(string $apikey, string $product_id)
    {
        // 统计信息
        $cache_data = $this->getComputedDataFromMongo(compact('apikey', 'product_id'), date('Ymd'));

        // 写缓存
        $this->writeCache($apikey, $product_id, date('Ymd'), $cache_data);
    }


    /**
     * 写缓存
     * @param string $apikey
     * @param int $product_id
     * @param string $day
     * @param array $list_computed_increment_data
     */
    private function writeCache(string $apikey, int $product_id, string $day, array $list_computed_increment_data)
    {
        $cache_key = $this->getCacheKey($apikey, $product_id, $day);
        $list_computed_data = json_encode($list_computed_increment_data, JSON_UNESCAPED_UNICODE);

        // 写入缓存
        app('redis')->connection($this->redis_connection)
            ->set($cache_key, $list_computed_data);
        app('redis')->connection($this->redis_connection)
            ->expire($cache_key, $this->cache_days * 86400);
    }

    /**
     * 获取缓存key
     * @param string $apikey
     * @param int $product_id
     * @param string $day
     * @return string
     */
    private function getCacheKey(string $apikey, int $product_id, string $day): string
    {
        return $this->cache_key_invoked_prefix . $product_id . '_' . $apikey . '_' . $day;
    }

    /**
     * 获取聚合信息
     * @param array $where
     * @param string $day 传递的日期
     * @return array
     */
    private function getComputedDataFromMongo(array $where, string $day): array
    {
        // 获取大部分的统计信息
        $list_most_data = $this->getMostDataFromMongo($where, $day);

        // 各个节点的数量
        $list_most_data['list_day_node_amount'] = $this->getNodeDataFromMongo($where, $day);

        return $list_most_data;
    }

    /**
     * 获取统计信息(需要的绝大部分都可以获得)
     * @param array $where
     * @param string $day
     * @return array
     */
    private function getMostDataFromMongo(array $where, string $day): array
    {
        // 日期限制
        $day_search = date('Ymd', strtotime($day));
        $month_search = date('Ym', strtotime($day));
        $year_search = date('Y', strtotime($day));

        // 组合条件
        $where_day = [$day_search, $day_search];
        $where_month = [$month_search . '01', $month_search . '31'];
        $where_year = [$year_search . '0101', $year_search . '1231'];

        $daily_amount = $this->aggregateSumAmount($where, $where_day);
        $month_amount = $this->aggregateSumAmount($where, $where_month);
        $year_amount = $this->aggregateSumAmount($where, $where_year);
        $total_amount = $this->aggregateSumAmount($where, [date('00000000'), '99999999']);
        return compact('daily_amount', 'month_amount', 'year_amount', 'total_amount');
    }

    /**
     * 聚合获取某段时间的总和
     * @param array $where
     * @param array $limit_days
     * @return int
     */
    private function aggregateSumAmount(array $where, array $limit_days): int
    {
        return (int)MongoProductInvoked::where($where)
            ->whereBetween('day', $limit_days)
            ->sum('amount');
    }

    /**
     * 获取当前各个节点的信息
     * @param array $where
     * @param string $day
     * @return array
     */
    private function getNodeDataFromMongo(array $where, string $day): array
    {
        $where['day'] = $day;
        return MongoProductInvoked::where($where)
            ->get()
            ->pluck('amount', 'node_area')
            ->toArray();
    }
}