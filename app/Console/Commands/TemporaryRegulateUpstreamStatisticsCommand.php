<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/7 0007
 * Time: 13:57
 */

namespace App\Console\Commands;


use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Models\MongoUpstream;
use Illuminate\Console\Command;
use MongoDB\BSON\ObjectID;

//临时脚本：整理成本统计数据：将每条的成本统计（upstream_statistics）数据绑定到客户身上，就是在每条数据中增加一个字段（customer_id）
class TemporaryRegulateUpstreamStatisticsCommand extends Command
{
    protected $signature = 'temporary:regulate_upstream_statistics';

    protected $description = '整理成本统计数据';

    protected $cacheDriverFacade;

    public function __construct()
    {
        parent::__construct();
        $this->cacheDriverFacade = new CacheDriverFacade();
    }

    public function handle()
    {
        //从20200101号开始
        $firstDayTimestamp = strtotime('20200101');
        $todayTimestamp    = strtotime(date('Ymd'));

        for ($i = $todayTimestamp; $i >= $firstDayTimestamp; $i -= 86400) {
            $date = date('Ymd', $i);

            try {
                $this->regulateItem(intval($date));
            } catch (\Exception $exception) {
                $this->error("【{$date}】数据整理失败，信息：{$exception->getMessage()}");
                return;
            }

            $this->output->success("【{$date}】数据的执行成功");
        }
    }


    /**
     * 整理每天的数据
     *
     * @access protected
     *
     * @param $date integer 日期
     *
     * @return void
     **/
    protected function regulateItem($date)
    {
        //查询当天的所有数据
        MongoUpstream::where('date', '=', intval($date))->get()->map(function ($item) {
            $data = $item->toArray();

            $apikey = array_get($data, 'apikey');
            if (!$apikey) {
                $this->output->note('此条数据不存在apikey');
                return;
            }

            $customer_id = $this->cacheDriverFacade->getCustomerIdByApikey($apikey);
            if (empty($customer_id)) {
                $this->output->note('未发现此数据对应的客户ID，apikey为' . $apikey);
                return;
            }

            $account_id = $this->cacheDriverFacade->getAccountIdByApikey($apikey);
            if (empty($account_id)) {
                $this->output->note('未发现此数据对应的账号ID，apikey为' . $apikey);
                return;
            }

            $item->customer_id = $customer_id;
            $item->account_id  = $account_id;

            $item->save();

            $this->output->writeln(array_get($data, 'id') . ' -> 修改成功');
        });
    }
}