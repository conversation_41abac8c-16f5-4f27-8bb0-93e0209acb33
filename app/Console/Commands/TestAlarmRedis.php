<?php

namespace App\Console\Commands;

use App\Http\Repository\DeptRepository;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ChannelInterface;
use App\Models\ChannelInterfaceMapping;
use App\Models\ChannelMapping;
use App\Models\ClickHouse\DictDb;
use App\Models\ClickHouse\RequestChannelLog;
use App\Models\ClickHouse\RequestLog;
use App\Models\ClickHouse\RequestProductLog;
use App\Models\Common\CommonEnumModel;
use App\Models\ConfigPriceCustomer;
use App\Models\Customer;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\Income\MainRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * Class TestAlarmRedis 报警测试脚本
 * @package App\Console\Commands
 */
class TestAlarmRedis extends Command
{
	protected $signature = 'test:alarm_redis';
	
	protected $key = 'request_log';

    public function handle()
    {
        $show_start_time = '2025-04-08 14:00:00';
        $show_end_time = '2025-04-08 14:05:00';
        $start_time = strtotime($show_start_time);
        $end_time = strtotime($show_end_time);
        echo '测试统计时间范围:'.$show_start_time.'-'.$show_end_time.PHP_EOL;

        $rpModel = new RequestLog('ck_test61');
        $info = $rpModel->getCountByTime($start_time, $end_time);
        $call_num1 = $info[0]['num'] ?? 0;
        echo '61中user_request_log数据'.$call_num1."条".PHP_EOL;

        $rpModel = new RequestProductLog('ck_test61');
        $info = $rpModel->getCountByTime($start_time, $end_time);
        $call_num2 = $info[0]['num'] ?? 0;
        echo '61中user_request_product_log数据'.$call_num2."条".PHP_EOL;

        $rpModel = new RequestChannelLog('ck_test61');
        $info = $rpModel->getCountByTime($start_time, $end_time);
        $call_num3 = $info[0]['num'] ?? 0;
        echo '61中user_request_channel_log数据'.$call_num3."条".PHP_EOL;

        echo '=================================================='.PHP_EOL;

        $rpModel = new RequestLog('ck_test63');
        $info = $rpModel->getCountByTime($start_time, $end_time);
        $call_num1 = $info[0]['num'] ?? 0;
        echo '63中user_request_log数据'.$call_num1."条".PHP_EOL;

        $rpModel = new RequestProductLog('ck_test63');
        $info = $rpModel->getCountByTime($start_time, $end_time);
        $call_num2 = $info[0]['num'] ?? 0;
        echo '63中user_request_product_log数据'.$call_num2."条".PHP_EOL;

        $rpModel = new RequestChannelLog('ck_test63');
        $info = $rpModel->getCountByTime($start_time, $end_time);
        $call_num3 = $info[0]['num'] ?? 0;
        echo '63中user_request_channel_log数据'.$call_num3."条".PHP_EOL;

        echo '=================================================='.PHP_EOL;

        $list = Customer\CustomerGroup::getGroupList();
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['group_id'],
                $item['group_name'],
                $item['status']
            ];

            $insertData[] = $add;
        }

        //unset($account_map);
        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb('dictdb61');
        $ck->truncate('finance_customer_group');//先清
        $colmuns = ['group_id', 'group_name', 'status'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_customer_group', $addArr, $colmuns);
        }

        echo '61中dictdb库中finance_customer_group表插入成功'.PHP_EOL;
        unset($insertData);
        unset($addLogChunk);

        dd('ok');
        //延期账号统一处理
        $this->setDelay();

    }


    public function setDelay(){
        $cshr = new CustomerSalesmanHistoryRepository();
        $dept_struct = DeptRepository::getSalesmanDeptMap();

        $cid_map = Account::getAllAccount();
        $cid_map = array_column($cid_map, 'customer_id', 'cid');

        $result = [];
        $customer_ids = [];
        $write_file = storage_path().'/logs/stat_channel_beiyi.log';
        $file = storage_path().'/logs/channel_beiyi.log';
        $fp = fopen($file,"r");

        while(!feof($fp)) {
            $row = fgets($fp);//逐行读取。如果fgets不写length参数，默认是读取1k。
            $row = trim($row);

            if (empty($row)) {
                continue;
            }

            //线上文件直接explode即可
            $arr = explode("\t", $row);
            if(count($arr) != 3){
                dd('数据可能有异常:'.$row);
            }

            $month = trim($arr[0]);
            $cid = trim($arr[1]);
            $total = trim($arr[2]);
            $valid = 0;//trim($arr[3]);

            $customer_id = $cid_map[$cid] ?? '';
            if(empty($customer_id)){
                echo 'cid='.$cid."无对应客户".PHP_EOL;
                continue;
            }

            $index = $month.'_'.$customer_id;
            if(isset($result[$index])){
                $result[$index]['total'] += $total;
                $result[$index]['valid'] += $valid;
            }else{
                $result[$index]['total'] = $total;
                $result[$index]['valid'] = $valid;
            }

            if(!in_array($customer_id, $customer_ids)){
                $customer_ids[] = $customer_id;
            }

        }
        fclose($fp);

        $cshr_month = $cshr->getListMonthly($customer_ids, 202401, date("Ym"),"Ym");

        foreach ($result as $key => $item){
            $arr = explode('_', $key);
            $month = $arr[0];
            $customer_id = $arr[1];

            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);

            $saleman_en_name = $cshr_month[$customer_id][$month] ?? '未知';//销售英文名
            $saleman_ch_name = $dept_struct[$saleman_en_name]['realname'] ?? '未知';//销售中文名
            $saleman_area = $dept_struct[$saleman_en_name]['area'] ?? '未知';//销售区域
            $source_name = '朴道';

            $str = $month."\t".$customer_id."\t".$customer_name."\t".$saleman_ch_name."\t".$saleman_area."\t".
                $item['total']."\t".$item['valid']."\t".$source_name.PHP_EOL;

            file_put_contents($write_file, $str, FILE_APPEND);
        }

    }




}
