<?php

namespace App\Console\Commands;


use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\Product;
use App\Models\UpstreamChannelPrice;
use Illuminate\Console\Command;

class TempTransferConfigPriceInterface extends Command
{
	protected $signature = "temp:transfer_config_price_interface";
	
	protected $description = '转移成本单价';
	
	//原接口名称与现在的接口ID映射关系
	private $mapping = [];
	
	
	private $oldChannelName2NewChannelNameMapping = [
		"juhe"      => "JuHe",
		"datau"     => "DaYou",
		"xiaoan"    => "XiaoAn",
		"tanzhen"   => "Tan<PERSON>hen",
		"shudun"    => "ShuDun",
		"pintai"    => "PinTai",
		"czty"      => "CZTY",
		"dianxin"   => "DianXin",
		"shmf"      => "ShuHe",
		"lingdi"    => "LinDi",
		"dumiaov3"  => "PinTaiV3",
		"chuanglan" => "<PERSON>ang<PERSON>an",
		"jindun"    => "JinDun",
		"rongshu"   => "RongShu",
	];
	
	private $bmyProductId2NameMapping = [
		"203" => "手机号在网状态",
		"216" => "手机号实时在网状态",
		"201" => "手机号三要素验证详版",
		"204" => "手机号消费档次",
		"208" => "银行卡四要素验证",
		"214" => "手机号姓名二要素验证",
		"217" => "常用联系人",
		"238" => "通用信用分(高阶版)",
		"232" => "风险评估分",
		"202" => "手机号在网时长",
		"213" => "手机号三要素验证",
		"218" => "银行卡归属地及卡类型查询",
		"215" => "风险名单",
		"317" => "通用分4",
		"310" => "欺诈分",
		"311" => "APP分",
		"312" => "多头分",
		"313" => "消费分",
		"314" => "通用分1",
		"315" => "通用分2",
		"316" => "通用分3",
		"207" => "手机号近三个月流量查询",
		"206" => "手机号近三个月停机次数",
	];
	
	private $bxfProductId2InterfaceMapping = [
		['score_4', '251', 'CUCC'],
		['cs_latest_phone_days', '252', 'CUCC'],
		['cs_first_phone_days', '253', 'CUCC'],
		['cs_phone_numbers', '254', 'CUCC'],
		['cs_total_phone_times', '255', 'CUCC'],
		['cs_called_times', '256', 'CUCC'],
		['cs_phone_times_in_15s', '257', 'CUCC'],
		['cs_phone_times_between_15_30s', '258', 'CUCC'],
		['cs_phone_times_over_60s', '259', 'CUCC'],
		['yscs_latest_phone_days', '260', 'CUCC'],
		['yscs_first_phone_days', '261', 'CUCC'],
		['yscs_phone_numbers', '262', 'CUCC'],
		['yscs_total_phone_times', '263', 'CUCC'],
		['yscs_called_times', '264', 'CUCC'],
		['yscs_phone_times_in_15s', '265', 'CUCC'],
		['yscs_phone_times_between_15_30s', '266', 'CUCC'],
		['yscs_phone_times_over_60s', '267', 'CUCC'],
		['cs_phone_numbers_in_7_days', '268', 'CUCC'],
		['cs_called_times_in_7_days', '269', 'CUCC'],
		['yscs_phone_numbers_in_7_days', '270', 'CUCC'],
		['yscs_called_times_in_7_days', '271', 'CUCC'],
		['cs_phone_numbers_in_14_days', '272', 'CUCC'],
		['cs_called_times_in_14_days', '273', 'CUCC'],
		['yscs_phone_numbers_in_14_days', '274', 'CUCC'],
		['yscs_called_times_in_14_days', '275', 'CUCC'],
		['cs_phone_numbers_in_21_days', '276', 'CUCC'],
		['cs_called_times_in_21_days', '277', 'CUCC'],
		['yscs_phone_numbers_in_21_days', '278', 'CUCC'],
		['yscs_called_times_in_21_days', '279', 'CUCC'],
		['cs_phone_numbers_in_30_days', '280', 'CUCC'],
		['cs_called_times_in_30_days', '281', 'CUCC'],
		['yscs_phone_numbers_in_30_days', '282', 'CUCC'],
		['yscs_called_times_in_30_days', '283', 'CUCC'],
		['cs_phone_numbers_between_30_60_days', '284', 'CUCC'],
		['cs_called_times_between_30_60_days', '285', 'CUCC'],
		['yscs_phone_numbers_between_30_60_days', '286', 'CUCC'],
		['yscs_called_times_between_30_60_days', '287', 'CUCC'],
		['dm_score', '288', 'CUCC'],
		['total_call_count', '241', 'CUCC'],
		['ave_number_count', '242', 'CUCC'],
		['called_rate_str', '243', 'CUCC'],
		['max_block', '244', 'CUCC'],
		['morning_rate_str', '245', 'CUCC'],
		['first_day', '246', 'CUCC'],
		['last_day', '247', 'CUCC'],
		['score_3', '289', 'CUCC'],
		['score_1', '290', 'CUCC'],
		['score_2', '291', 'CUCC'],
		['score_5', '292', 'CUCC'],
		['score_6', '293', 'CUCC'],
		['score_7', '294', 'CUCC'],
		['score_8', '295', 'CUCC'],
		['score_9', '296', 'CUCC'],
		['score_10', '297', 'CUCC'],
		['score_11', '298', 'CUCC'],
		['score_4', '299', 'CUCC'],
		['c01_A_cs_history,c06_A_overdue_extent_D7', '252', 'BJCMCC'],
		['c09_A_various_borrow_D14,c10_A_various_borrow_D21', '253', 'BJCMCC'],
		['c03_A_payments_intention_two,c12_B_various_borrow_D60', '254', 'BJCMCC'],
		['c01_A_cs_history,c06_A_overdue_extent_D7', '255', 'BJCMCC'],
		['c07_A_overdue_extent_D14,c05_A_whole_overdue_extent', '256', 'BJCMCC'],
		['c03_A_payments_intention_two,c12_B_various_borrow_D60', '257', 'BJCMCC'],
		['c01_A_cs_history,c06_A_overdue_extent_D7', '258', 'BJCMCC'],
		['c07_A_overdue_extent_D14,c05_A_whole_overdue_extent', '259', 'BJCMCC'],
		['c07_A_overdue_extent_D14,c05_A_whole_overdue_extent', '260', 'BJCMCC'],
		['c17_B_payments_intention,c18_B_cs_history', '261', 'BJCMCC'],
		['c13_B_whole_various_borrow,c14_B_whole_overdue_extent', '262', 'BJCMCC'],
		['c13_B_whole_various_borrow,c14_B_whole_overdue_extent', '263', 'BJCMCC'],
		['c01_A_cs_history,c06_A_overdue_extent_D7', '264', 'BJCMCC'],
		['c17_B_payments_intention,c18_B_cs_history', '265', 'BJCMCC'],
		['c17_B_payments_intention,c18_B_cs_history', '266', 'BJCMCC'],
		['c03_A_payments_intention_two,c12_B_various_borrow_D60', '267', 'BJCMCC'],
		['c04_A_payments_intention_three,c08_A_whole_various_borrow', '268', 'BJCMCC'],
		['c04_A_payments_intention_three,c08_A_whole_various_borrow', '269', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '270', 'BJCMCC'],
		['c11_A_various_borrow_D30,c02_A_payments_intention_one', '271', 'BJCMCC'],
		['c03_A_payments_intention_two,c12_B_various_borrow_D60', '272', 'BJCMCC'],
		['c07_A_overdue_extent_D14,c05_A_whole_overdue_extent', '273', 'BJCMCC'],
		['c17_B_payments_intention,c18_B_cs_history', '274', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '275', 'BJCMCC'],
		['c09_A_various_borrow_D14,c10_A_various_borrow_D21', '276', 'BJCMCC'],
		['c09_A_various_borrow_D14,c10_A_various_borrow_D21', '277', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '278', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '279', 'BJCMCC'],
		['c11_A_various_borrow_D30,c02_A_payments_intention_one', '280', 'BJCMCC'],
		['c09_A_various_borrow_D14,c10_A_various_borrow_D21', '281', 'BJCMCC'],
		['c11_A_various_borrow_D30,c02_A_payments_intention_one', '282', 'BJCMCC'],
		['c13_B_whole_various_borrow,c14_B_whole_overdue_extent', '283', 'BJCMCC'],
		['c11_A_various_borrow_D30,c02_A_payments_intention_one', '284', 'BJCMCC'],
		['c13_B_whole_various_borrow,c14_B_whole_overdue_extent', '285', 'BJCMCC'],
		['c04_A_payments_intention_three,c08_A_whole_various_borrow', '286', 'BJCMCC'],
		['c04_A_payments_intention_three,c08_A_whole_various_borrow', '287', 'BJCMCC'],
		['c15_abnormal_score_1,c15_abnormal_score_1', '241', 'BJCMCC'],
		['c15_abnormal_score_1,c15_abnormal_score_1', '242', 'BJCMCC'],
		['c15_abnormal_score_1,c15_abnormal_score_1', '243', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '244', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '245', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '246', 'BJCMCC'],
		['c16_abnormal_score_2,c16_abnormal_score_2', '247', 'BJCMCC'],
		['ESR10000000181', '251', 'JSCMCC'],
		['ESR10000000183', '252', 'JSCMCC'],
		['ESR10000000184', '253', 'JSCMCC'],
		['ESR10000000185', '254', 'JSCMCC'],
		['ESR10000000186', '255', 'JSCMCC'],
		['ESR10000000187', '256', 'JSCMCC'],
		['ESR10000000188', '257', 'JSCMCC'],
		['ESR10000000189', '258', 'JSCMCC'],
		['ESR10000000190', '259', 'JSCMCC'],
		['ESR10000000191', '260', 'JSCMCC'],
		['ESR10000000192', '261', 'JSCMCC'],
		['ESR10000000193', '262', 'JSCMCC'],
		['ESR10000000194', '263', 'JSCMCC'],
		['ESR10000000195', '264', 'JSCMCC'],
		['ESR10000000196', '265', 'JSCMCC'],
		['ESR10000000197', '266', 'JSCMCC'],
		['ESR10000000198', '267', 'JSCMCC'],
		['ESR10000000199', '268', 'JSCMCC'],
		['ESR10000000200', '269', 'JSCMCC'],
		['ESR10000000201', '270', 'JSCMCC'],
		['ESR10000000202', '271', 'JSCMCC'],
		['ESR10000000203', '272', 'JSCMCC'],
		['ESR10000000204', '273', 'JSCMCC'],
		['ESR10000000205', '274', 'JSCMCC'],
		['ESR10000000206', '275', 'JSCMCC'],
		['ESR10000000207', '276', 'JSCMCC'],
		['ESR10000000208', '277', 'JSCMCC'],
		['ESR10000000209', '278', 'JSCMCC'],
		['ESR10000000210', '279', 'JSCMCC'],
		['ESR10000000211', '280', 'JSCMCC'],
		['ESR10000000212', '281', 'JSCMCC'],
		['ESR10000000213', '282', 'JSCMCC'],
		['ESR10000000214', '283', 'JSCMCC'],
		['ESR10000000215', '284', 'JSCMCC'],
		['ESR10000000216', '285', 'JSCMCC'],
		['ESR10000000217', '286', 'JSCMCC'],
		['ESR10000000218', '287', 'JSCMCC'],
		['cs_latest_phone_days', '252', 'SCCMCC'],
		['cs_first_phone_days', '253', 'SCCMCC'],
		['cs_phone_numbers', '254', 'SCCMCC'],
		['cs_total_phone_times', '255', 'SCCMCC'],
		['cs_called_times', '256', 'SCCMCC'],
		['cs_phone_times_in_15s', '257', 'SCCMCC'],
		['cs_phone_times_between_15_30s', '258', 'SCCMCC'],
		['cs_phone_times_over_60s', '259', 'SCCMCC'],
		['yscs_latest_phone_days', '260', 'SCCMCC'],
		['yscs_first_phone_days', '261', 'SCCMCC'],
		['yscs_phone_numbers', '262', 'SCCMCC'],
		['yscs_total_phone_times', '263', 'SCCMCC'],
		['yscs_called_times', '264', 'SCCMCC'],
		['yscs_phone_times_in_15s', '265', 'SCCMCC'],
		['yscs_phone_times_between_15_30s', '266', 'SCCMCC'],
		['yscs_phone_times_over_60s', '267', 'SCCMCC'],
		['cs_phone_numbers_in_7_days', '268', 'SCCMCC'],
		['cs_called_times_in_7_days', '269', 'SCCMCC'],
		['yscs_phone_numbers_in_7_days', '270', 'SCCMCC'],
		['yscs_called_times_in_7_days', '271', 'SCCMCC'],
		['cs_phone_numbers_in_14_days', '272', 'SCCMCC'],
		['cs_called_times_in_14_days', '273', 'SCCMCC'],
		['yscs_phone_numbers_in_14_days', '274', 'SCCMCC'],
		['yscs_called_times_in_14_days', '275', 'SCCMCC'],
		['cs_phone_numbers_in_21_days', '276', 'SCCMCC'],
		['cs_called_times_in_21_days', '277', 'SCCMCC'],
		['yscs_phone_numbers_in_21_days', '278', 'SCCMCC'],
		['yscs_called_times_in_21_days', '279', 'SCCMCC'],
		['cs_phone_numbers_in_30_days', '280', 'SCCMCC'],
		['cs_called_times_in_30_days', '281', 'SCCMCC'],
		['yscs_phone_numbers_in_30_days', '282', 'SCCMCC'],
		['yscs_called_times_in_30_days', '283', 'SCCMCC'],
		['cs_phone_numbers_between_30_60_days', '284', 'SCCMCC'],
		['cs_called_times_between_30_60_days', '285', 'SCCMCC'],
		['yscs_phone_numbers_between_30_60_days', '286', 'SCCMCC'],
		['yscs_called_times_between_30_60_days', '287', 'SCCMCC'],
		['main_score', '251', 'SCCMCC'],
		['dm_score', '288', 'SCCMCC'],
		['total_call_count', '241', 'SCCMCC'],
		['ave_number_count', '242', 'SCCMCC'],
		['called_rate', '243', 'SCCMCC'],
		['max_block', '244', 'SCCMCC'],
		['morning_rate', '245', 'SCCMCC'],
		['first_day', '246', 'SCCMCC'],
		['last_day', '247', 'SCCMCC'],
		['cs_latest_phone_days', '252', 'SDCMCC'],
		['cs_first_phone_days', '253', 'SDCMCC'],
		['cs_phone_numbers', '254', 'SDCMCC'],
		['cs_total_phone_times', '255', 'SDCMCC'],
		['cs_called_times', '256', 'SDCMCC'],
		['cs_phone_times_in_15s', '257', 'SDCMCC'],
		['cs_phone_times_between_15_30s', '258', 'SDCMCC'],
		['cs_phone_times_over_60s', '259', 'SDCMCC'],
		['yscs_latest_phone_days', '260', 'SDCMCC'],
		['yscs_first_phone_days', '261', 'SDCMCC'],
		['yscs_phone_numbers', '262', 'SDCMCC'],
		['yscs_total_phone_times', '263', 'SDCMCC'],
		['yscs_called_times', '264', 'SDCMCC'],
		['yscs_phone_times_in_15s', '265', 'SDCMCC'],
		['yscs_phone_times_between_15_30s', '266', 'SDCMCC'],
		['yscs_phone_times_over_60s', '267', 'SDCMCC'],
		['cs_phone_numbers_in_7_days', '268', 'SDCMCC'],
		['cs_called_times_in_7_days', '269', 'SDCMCC'],
		['yscs_phone_numbers_in_7_days', '270', 'SDCMCC'],
		['yscs_called_times_in_7_days', '271', 'SDCMCC'],
		['cs_phone_numbers_in_14_days', '272', 'SDCMCC'],
		['cs_called_times_in_14_days', '273', 'SDCMCC'],
		['yscs_phone_numbers_in_14_days', '274', 'SDCMCC'],
		['yscs_called_times_in_14_days', '275', 'SDCMCC'],
		['cs_phone_numbers_in_21_days', '276', 'SDCMCC'],
		['cs_called_times_in_21_days', '277', 'SDCMCC'],
		['yscs_phone_numbers_in_21_days', '278', 'SDCMCC'],
		['yscs_called_times_in_21_days', '279', 'SDCMCC'],
		['cs_phone_numbers_in_30_days', '280', 'SDCMCC'],
		['cs_called_times_in_30_days', '281', 'SDCMCC'],
		['yscs_phone_numbers_in_30_days', '282', 'SDCMCC'],
		['yscs_called_times_in_30_days', '283', 'SDCMCC'],
		['cs_phone_numbers_between_30_60_days', '284', 'SDCMCC'],
		['cs_called_times_between_30_60_days', '285', 'SDCMCC'],
		['yscs_phone_numbers_between_30_60_days', '286', 'SDCMCC'],
		['yscs_called_times_between_30_60_days', '287', 'SDCMCC'],
		['score_1', '251', 'SDCMCC'],
		['total_call_count', '241', 'SDCMCC'],
		['ave_number_count', '242', 'SDCMCC'],
		['called_rate', '243', 'SDCMCC'],
		['max_block', '244', 'SDCMCC'],
		['morning_rate', '245', 'SDCMCC'],
		['first_day', '246', 'SDCMCC'],
		['last_day', '247', 'SDCMCC'],
		['score_2', '289', 'SDCMCC'],
		['score_3', '290', 'SDCMCC'],
		['score_4', '292', 'SDCMCC'],
		['score_5', '295', 'SDCMCC'],
		['score_6', '296', 'SDCMCC'],
		['score_7', '298', 'SDCMCC'],
		['score_1', '299', 'SDCMCC'],
		['cs_latest_phone_days', '252', 'HBCMCC'],
		['cs_first_phone_days', '253', 'HBCMCC'],
		['cs_phone_numbers', '254', 'HBCMCC'],
		['cs_total_phone_times', '255', 'HBCMCC'],
		['cs_called_times', '256', 'HBCMCC'],
		['cs_phone_times_in_15s', '257', 'HBCMCC'],
		['cs_phone_times_between_15_30s', '258', 'HBCMCC'],
		['cs_phone_times_over_60s', '259', 'HBCMCC'],
		['yscs_latest_phone_days', '260', 'HBCMCC'],
		['yscs_first_phone_days', '261', 'HBCMCC'],
		['yscs_phone_numbers', '262', 'HBCMCC'],
		['yscs_total_phone_times', '263', 'HBCMCC'],
		['yscs_called_times', '264', 'HBCMCC'],
		['yscs_phone_times_in_15s', '265', 'HBCMCC'],
		['yscs_phone_times_between_15_30s', '266', 'HBCMCC'],
		['yscs_phone_times_over_60s', '267', 'HBCMCC'],
		['cs_phone_numbers_in_7_days', '268', 'HBCMCC'],
		['cs_called_times_in_7_days', '269', 'HBCMCC'],
		['yscs_phone_numbers_in_7_days', '270', 'HBCMCC'],
		['yscs_called_times_in_7_days', '271', 'HBCMCC'],
		['cs_phone_numbers_in_14_days', '272', 'HBCMCC'],
		['cs_called_times_in_14_days', '273', 'HBCMCC'],
		['yscs_phone_numbers_in_14_days', '274', 'HBCMCC'],
		['yscs_called_times_in_14_days', '275', 'HBCMCC'],
		['cs_phone_numbers_in_21_days', '276', 'HBCMCC'],
		['cs_called_times_in_21_days', '277', 'HBCMCC'],
		['yscs_phone_numbers_in_21_days', '278', 'HBCMCC'],
		['yscs_called_times_in_21_days', '279', 'HBCMCC'],
		['cs_phone_numbers_in_30_days', '280', 'HBCMCC'],
		['cs_called_times_in_30_days', '281', 'HBCMCC'],
		['yscs_phone_numbers_in_30_days', '282', 'HBCMCC'],
		['yscs_called_times_in_30_days', '283', 'HBCMCC'],
		['cs_phone_numbers_between_30_60_days', '284', 'HBCMCC'],
		['cs_called_times_between_30_60_days', '285', 'HBCMCC'],
		['yscs_phone_numbers_between_30_60_days', '286', 'HBCMCC'],
		['yscs_called_times_between_30_60_days', '287', 'HBCMCC'],
		['main_score', '251', 'HBCMCC'],
		['dm_score', '288', 'HBCMCC'],
		['total_call_count', '241', 'HBCMCC'],
		['ave_number_count', '242', 'HBCMCC'],
		['called_rate', '243', 'HBCMCC'],
		['max_block', '244', 'HBCMCC'],
		['morning_rate', '245', 'HBCMCC'],
		['first_day', '246', 'HBCMCC'],
		['last_day', '247', 'HBCMCC'],
		['CreditScore_gfdz_m0', '711', 'ALLCMCC'],
		['cs_latest_phone_days', '252', 'CTCC'],
		['cs_first_phone_days', '253', 'CTCC'],
		['cs_phone_numbers', '254', 'CTCC'],
		['cs_total_phone_times', '255', 'CTCC'],
		['cs_called_times', '256', 'CTCC'],
		['cs_phone_times_in_15s', '257', 'CTCC'],
		['cs_phone_times_between_15_30s', '258', 'CTCC'],
		['cs_phone_times_over_60s', '259', 'CTCC'],
		['yscs_latest_phone_days', '260', 'CTCC'],
		['yscs_first_phone_days', '261', 'CTCC'],
		['yscs_phone_numbers', '262', 'CTCC'],
		['yscs_total_phone_times', '263', 'CTCC'],
		['yscs_called_times', '264', 'CTCC'],
		['yscs_phone_times_in_15s', '265', 'CTCC'],
		['yscs_phone_times_between_15_30s', '266', 'CTCC'],
		['yscs_phone_times_over_60s', '267', 'CTCC'],
		['cs_phone_numbers_in_7_days', '268', 'CTCC'],
		['cs_called_times_in_7_days', '269', 'CTCC'],
		['yscs_phone_numbers_in_7_days', '270', 'CTCC'],
		['yscs_called_times_in_7_days', '271', 'CTCC'],
		['cs_phone_numbers_in_14_days', '272', 'CTCC'],
		['cs_called_times_in_14_days', '273', 'CTCC'],
		['yscs_phone_numbers_in_14_days', '274', 'CTCC'],
		['yscs_called_times_in_14_days', '275', 'CTCC'],
		['cs_phone_numbers_in_21_days', '276', 'CTCC'],
		['cs_called_times_in_21_days', '277', 'CTCC'],
		['yscs_phone_numbers_in_21_days', '278', 'CTCC'],
		['yscs_called_times_in_21_days', '279', 'CTCC'],
		['cs_phone_numbers_in_30_days', '280', 'CTCC'],
		['cs_called_times_in_30_days', '281', 'CTCC'],
		['yscs_phone_numbers_in_30_days', '282', 'CTCC'],
		['yscs_called_times_in_30_days', '283', 'CTCC'],
		['cs_phone_numbers_between_30_60_days', '284', 'CTCC'],
		['cs_called_times_between_30_60_days', '285', 'CTCC'],
		['yscs_phone_numbers_between_30_60_days', '286', 'CTCC'],
		['yscs_called_times_between_30_60_days', '287', 'CTCC'],
		['main_score', '251', 'CTCC'],
		['dumiao', '288', 'CTCC'],
		['callBasics', '241', 'CTCC'],
		['callBasics', '242', 'CTCC'],
		['callActive', '243', 'CTCC'],
		['callActive', '244', 'CTCC'],
		['callAbnormal', '245', 'CTCC'],
		['callAbnormal', '246', 'CTCC'],
		['callAbnormal', '247', 'CTCC'],
	];
	
	//结果SQL
	private $sql = '';
	
	public function handle()
	{
		//获取旧版渠道与新版接口的对应关系
		$this->initMapping();
		
		$fp = fopen('1.sql', 'w+');
		
		$result = $this->bmp();
		array_multisort(array_column($result, 'iid'), SORT_DESC, $result);
		foreach ($result as $item) {
			$price = json_encode($item['price'], JSON_UNESCAPED_UNICODE);
			$sql   = <<<SQL
INSERT INTO `config_price_interface`(`interface_id`, `father_id`, `price`, `start_date`) VALUE('{$item['iid']}','{$item['father_id']}','{$price}','{$item['date']}');\n
SQL;
			fwrite($fp, $sql);
		}
		
		//邦信分快捷版
		$result = $this->bxf();
		
		foreach ($result as $item) {
			$price = json_encode([$item['price']], JSON_UNESCAPED_UNICODE);
			$sql   = <<<SQL
INSERT INTO `config_price_interface`(`interface_id`, `father_id`, `price`, `start_date`) VALUE('{$item['iid']}','{$item['father_id']}','{$price}','{$item['date']}');\n
SQL;
			fwrite($fp, $sql);
		}
		
		
		fclose($fp);
	}
	
	private function bxf()
	{
		//计算出每个渠道所用了那些产品
		$oldChannelProductIds = [];
		foreach ($this->bxfProductId2InterfaceMapping as $item) {
			if (false === strpos($item[0], ',')) {
				$oldChannelProductIds[$item[2] . '_' . $item[0]][] = $item[1];
			} else {
				foreach (explode(',', $item[0]) as $interface) {
					$oldChannelProductIds[$item[2] . '_' . $interface][] = $item[1];
				}
			}
		}
		$result = [];
		UpstreamChannelPrice::where('product_id', 210)
							->get()
							->map(function ($item) use (&$result, $oldChannelProductIds) {
								$data = $item->toArray();
								//只有四川是查得、其他的都是查询计费
								$old_channel = $data['channel'];
			
								$operator    = 'ALL';
								$encrypt_way = 'ALL';
								//[{"operator":"ALL","encrypt_way":"ALL","price":0.1,"price_model":2}]
								if ('CUCC' == substr($old_channel, 0, 4)) {
									//中国联通
									$price_model     = 1;
									$price           = json_decode($data['price'], true);
									$price           = array_first($price);
									$channel_id      = 1;
									$interface_label = substr($old_channel, 5);
								} else if ('CTCC' == substr($old_channel, 0, 4)) {
									//中国电信
									//暂不处理
									return;
								} else if ('BJCMCC' == substr($old_channel, 0, 6)) {
									//北京移动
									$price_model     = 1;
									$price           = json_decode($data['price'], true);
									$price           = array_first($price);
									$channel_id      = 3;
									$interface_label = substr($old_channel, 7);
								} else if ('JSCMCC' == substr($old_channel, 0, 6)) {
									//江苏移动
									$price_model     = 1;
									$price           = json_decode($data['price'], true);
									$price           = array_first($price);
									$channel_id      = 4;
									$interface_label = substr($old_channel, 7);
								} else if ('SCCMCC' == substr($old_channel, 0, 6)) {
									//四川移动
									$price_model     = 2;
									$price           = json_decode($data['price'], true);
									$price           = array_first($price);
									$channel_id      = 5;
									$interface_label = substr($old_channel, 7);
								} else if ('SDCMCC' == substr($old_channel, 0, 6)) {
									//山东移动
									$price_model     = 1;
									$price           = json_decode($data['price'], true);
									$price           = array_first($price);
									$channel_id      = 6;
									$interface_label = substr($old_channel, 7);
								} else if ('HBCMCC' == substr($old_channel, 0, 6)) {
									//河北移动
									$price_model     = 1;
									$price           = json_decode($data['price'], true);
									$price           = array_first($price);
									$channel_id      = 7;
									$interface_label = substr($old_channel, 7);
								} else if ('ALLCMCC' == substr($old_channel, 0, 7)) {
									//江苏移动
									$price_model     = 1;
									$price           = json_decode($data['price'], true);
									$price           = array_first($price);
									$channel_id      = 8;
									$interface_label = substr($old_channel, 8);
								} else {
									throw new \Exception("not find channel: [{$old_channel}]");
								}
			
			
								//获取接口ID
								$iid = ChannelInterface::where('name', $interface_label)
													   ->where('channel_id', $channel_id)
													   ->pluck('id')
													   ->toArray();
								if (empty($iid)) {
									$this->output->warning("未找到对应的接口:{$channel_id}{$interface_label}");
				
									return 0;
								}
								$iid = $iid[0];
			
								$result[] = [
									'father_id' => 210,
									'iid'       => $iid,
									'price'     => compact('operator', 'encrypt_way', 'price', 'price_model'),
									'date'      => date('Ymd', strtotime($data['start_date'])),
								];
			
			
								//								$disabled_channels = [
								//									'CUCC_main_score',
								//									'CUCC_dhb_a_score',
								//									'CUCC_dhb_b_score',
								//									'CUCC_score_12',
								//									'CUCC_score_13',
								//									'CUCC_score_14',
								//									'CUCC_score_15',
								//									'CUCC_score_16',
								//									'CUCC_score_17',
								//									'CUCC_score_18',
								//									'CUCC_score_19',
								//									'CUCC_score_20',
								//									'BJCMCC_c01_A_cs_history',
								//									'BJCMCC_c02_A_payments_intention_one',
								//									'BJCMCC_c03_A_payments_intention_two',
								//									'BJCMCC_c04_A_payments_intention_three',
								//									'BJCMCC_c05_A_whole_overdue_extent',
								//									'BJCMCC_c06_A_overdue_extent_D7',
								//									'BJCMCC_c07_A_overdue_extent_D14',
								//									'BJCMCC_c08_A_whole_various_borrow',
								//									'BJCMCC_c09_A_various_borrow_D14',
								//									'BJCMCC_c10_A_various_borrow_D21',
								//									'BJCMCC_c11_A_various_borrow_D30',
								//									'BJCMCC_c12_B_various_borrow_D60',
								//									'BJCMCC_c13_B_whole_various_borrow',
								//									'BJCMCC_c14_B_whole_overdue_extent',
								//									'BJCMCC_c15_abnormal_score_1',
								//									'BJCMCC_c16_abnormal_score_2',
								//									'BJCMCC_c17_B_payments_intention',
								//									'BJCMCC_c18_B_cs_history',
								//									'BJCMCC_c19_total_csf_score',
								//									'BJCMCC_c20_A_cs_history_2',
								//									'BJCMCC_c21_A_overdue_extent_D21',
								//									'SDCMCC_total_stat_count',
								//									'SDCMCC_total_score_count',
								//								];
								//								if (!in_array($old_channel, $disabled_channels)) {
								//									$product_ids = $oldChannelProductIds[$old_channel];
								//
								//									foreach ($product_ids as $product_id) {
								//										$result[] = [
								//											'father_id'  => 210,
								//											'product_id' => $product_id,
								//											'iid'        => $iid,
								//											'price'      => compact('operator', 'encrypt_way', 'price', 'price_model'),
								//											'date'       => date('Ymd', strtotime($data['start_date'])),
								//										];
								//									}
								//								}
			
			
								//								try {
								//									$data = $item->toArray();
								//
								//									$price = json_decode($data['price'], true);
								//									if (array_key_exists('succ', $price)) {
								//										$yd = $lt = $dx = $price['succ'];
								//									} else if (array_key_exists('all', $price)) {
								//										$yd = $lt = $dx = $price['all'];
								//									} else {
								//										$yd = array_get($price, 'yd', 0);
								//										$lt = array_get($price, 'yd', 0);
								//										$dx = array_get($price, 'dx', 0);
								//									}
								//
								//
								//									$old_channel = $data['channel'];
								//									$product_id  = $data['product_id'];
								//									$start_date  = $data['start_date'];
								//									$encrypt     = '';
								//									if ('MD5' == substr($old_channel, -3)) {
								//										//MD5
								//										$encrypt = 'MD5';
								//									} else if ('SHA256' == substr($old_channel, -6)) {
								//										//SHA256
								//										$encrypt = 'SHA256';
								//									}
								//
								//									$iid = $this->getIidForBmp($old_channel, $product_id, $encrypt);
								//									if (!$iid) {
								//										$this->output->warning(json_encode($item, JSON_UNESCAPED_UNICODE));
								//									}
								//									$key = $iid . '_' . $start_date;
								//									if (!array_key_exists($key, $result)) {
								//										$result[$key] = [
								//											'father_id' => 200,
								//											'iid'       => $iid,
								//											'price'     => [],
								//											'date'      => $start_date,
								//										];
								//									}
								//
								//									//移动
								//									$result[$key]['price'][] = [
								//										'operator'    => 'CMCC',
								//										'encrypt_way' => $encrypt ?: 'CLEAR',
								//										'price'       => $yd,
								//										'price_model' => 2,
								//									];
								//
								//									//联通
								//									$result[$key]['price'][] = [
								//										'operator'    => 'CUCC',
								//										'encrypt_way' => $encrypt ?: 'CLEAR',
								//										'price'       => $lt,
								//										'price_model' => 2,
								//									];
								//
								//
								//									//电信
								//									$result[$key]['price'][] = [
								//										'operator'    => 'CTCC',
								//										'encrypt_way' => $encrypt ?: 'CLEAR',
								//										'price'       => $dx,
								//										'price_model' => 2,
								//									];
								//								} catch (\Exception $exception) {
								//									$this->output->error("错误信息");
								//									halt($exception->getLine(), $exception->getMessage(), $item->toArray());
								//								}
							});
		
		
		return $result;
	}
	
	//邦信分成本单价处理
	private function bmp()
	{
		$product_ids = Product::where('father_id', '200')
							  ->pluck('product_id')
							  ->toArray();
		
		$result = [];
		UpstreamChannelPrice::whereIn('product_id', $product_ids)
							->orderBy('start_date', 'desc')
							->get()
							->map(function ($item) use (&$result) {
								try {
									$data = $item->toArray();
				
									$price = json_decode($data['price'], true);
									if (array_key_exists('succ', $price)) {
										$yd = $lt = $dx = $price['succ'];
									} else if (array_key_exists('all', $price)) {
										$yd = $lt = $dx = $price['all'];
									} else {
										$yd = array_get($price, 'yd', 0);
										$lt = array_get($price, 'lt', 0);
										$dx = array_get($price, 'dx', 0);
									}
				
				
									$old_channel = $data['channel'];
									$product_id  = $data['product_id'];
									$start_date  = date('Ymd', strtotime($data['start_date']));
									$encrypt     = '';
									if ('MD5' == substr($old_channel, -3)) {
										//MD5
										$encrypt = 'MD5';
									} else if ('SHA256' == substr($old_channel, -6)) {
										//SHA256
										$encrypt = 'SHA256';
									}
				
									$iid = $this->getIidForBmp($old_channel, $product_id, $encrypt);
									if (!$iid) {
										$this->output->warning(json_encode($item, JSON_UNESCAPED_UNICODE));
					
										return;
									}
				
									if (!array_key_exists($iid, $result)) {
										$result[$iid] = [
											'father_id' => 200,
											'iid'       => $iid,
											'price'     => [],
											'date'      => 20210101,
										];
									}
				
									//确定是否存在这个运营商、这个加密方式的计费配置
									$encrypt = $encrypt ?: 'CLEAR';
				
									$yd_exists = false;
									$lt_exists = false;
									$dx_exists = false;
									foreach ($result[$iid]['price'] as $price) {
										if ($price['encrypt_way'] == $encrypt && $price['operator'] == 'CMCC') {
											$yd_exists = true;
										}
										if ($price['encrypt_way'] == $encrypt && $price['operator'] == 'CUCC') {
											$lt_exists = true;
										}
										if ($price['encrypt_way'] == $encrypt && $price['operator'] == 'CTCC') {
											$dx_exists = true;
										}
									}
									if (!$yd_exists) {
										$result[$iid]['price'][] = [
											'operator'    => 'CMCC',
											'encrypt_way' => $encrypt,
											'price'       => $yd,
											'price_model' => 2,
										];
									}
									
									if (!$lt_exists) {
										$result[$iid]['price'][] = [
											'operator'    => 'CUCC',
											'encrypt_way' => $encrypt,
											'price'       => $lt,
											'price_model' => 2,
										];
									}
									
									if (!$dx_exists) {
										$result[$iid]['price'][] = [
											'operator'    => 'CTCC',
											'encrypt_way' => $encrypt,
											'price'       => $dx,
											'price_model' => 2,
										];
									}
				
//
//									$result[$iid][$start_date] = [
//										'father_id' => 200,
//										'iid'       => $iid,
//										'price'     => [],
//										'date'      => $start_date,
//									];
//
//									//移动
//									$result[$iid][$start_date]['price'][] = [
//										'operator'    => 'CMCC',
//										'encrypt_way' => $encrypt ?: 'CLEAR',
//										'price'       => $yd,
//										'price_model' => 2,
//									];
//
//									//联通
//									$result[$iid][$start_date]['price'][] = [
//										'operator'    => 'CUCC',
//										'encrypt_way' => $encrypt ?: 'CLEAR',
//										'price'       => $lt,
//										'price_model' => 2,
//									];
//
//
//									//电信
//									$result[$iid][$start_date]['price'][] = [
//										'operator'    => 'CTCC',
//										'encrypt_way' => $encrypt ?: 'CLEAR',
//										'price'       => $dx,
//										'price_model' => 2,
//									];
				
								} catch (\Exception $exception) {
									$this->output->error("错误信息");
									halt($exception->getLine(), $exception->getMessage(), $item->toArray());
								}
							});
		
		//halt($result[361]);
		
		return $result;
	}
	
	private function initMapping()
	{
		$data = ChannelInterface::select([
			'channel.name as channel_name',
			'channel_interface.name as interface_name',
			'channel_interface.id as iid',
		])
								->leftJoin('channel', 'channel.channel_id', '=', 'channel_interface.channel_id')
								->get()
								->toArray();
		
		
		$this->mapping = [];
		foreach ($data as $item) {
			$this->mapping[$item['channel_name'] . '_' . $item['interface_name']] = $item['iid'];
		}
	}
	
	//获取邦秒验的接口ID
	private function getIidForBmp($old_channel, $product_id, $encrypt = '')
	{
		switch ($encrypt) {
			case 'MD5':
				$old_channel = substr($old_channel, 0, count($old_channel) - 4);
				break;
			case 'SHA256':
				$old_channel = substr($old_channel, 0, count($old_channel) - 7);
				break;
		}
		if (!array_key_exists($old_channel, $this->oldChannelName2NewChannelNameMapping)) {
			$this->output->warning("未找到对应的渠道");
			
			return 0;
		}
		$channel_name = $this->oldChannelName2NewChannelNameMapping[$old_channel];
		
		$channel_id = Channel::where('name', $channel_name)
							 ->pluck('channel_id')
							 ->toArray();
		
		$interface_label = $this->bmyProductId2NameMapping[$product_id];
		
		$iid = ChannelInterface::where('label', $interface_label)
							   ->where('channel_id', $channel_id[0])
							   ->pluck('id')
							   ->toArray();
		
		if (empty($iid)) {
			$this->output->warning("未找到对应的接口:{$old_channel},{$product_id},{$encrypt}");
			
			return 0;
		}
		
		return $iid[0];
	}
}