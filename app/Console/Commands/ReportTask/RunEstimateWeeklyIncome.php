<?php

namespace App\Console\Commands\ReportTask;

use App\Define\StatDefine;
use App\Models\Common\FlexConfig;
use App\Models\WeekIncomeEstimate;
use App\Repositories\Income\MainRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Command;
use App\Utils\Helpers\Func;
use Exception;


/**
 * 逐条校验发票数据
 *
 * php artisan report_task:run_estimate_weekly_income
 *
 * php artisan report_task:run_estimate_weekly_income --mode=2 --year=2025
 *
 */
class RunEstimateWeeklyIncome extends Command
{
    protected $signature = 'report_task:run_estimate_weekly_income
    {--mode= : 模式,1 默认为更新数据, 2 初始化周基础数据}
    {--date= : mode为1时,指定日期}
    {--year= : mode为2时,指定年}';

    protected $description = '更新周预估数据';


    /** 模式 更新*/
    public const MODE_UPDATE = 1;
    /** 模式 初始化*/
    public const MODE_INIT = 2;



    private $mode = 1;
    private $year = '';
    /** @var string 昨天或指定日期的前一天 */
    private $date = '';

    private $year_data = [];


    /** @var int 收入目标 */
    private $estimate_income_target = [];

    /**
     * @throws Exception
     */
    public function handle() {
        Log::info($this->description.' start.');

        $key  = 'dashboard_mobile_estimate_week_income';
        $data = FlexConfig::getConfig($key);
        $this->year_data = $data;

        $key  = 'dashboard_mobile_estimate_income_target';
        $data = FlexConfig::getConfig($key);
        $this->estimate_income_target = $data;

        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->set_param();


            switch ($this->mode) {
                case self::MODE_UPDATE:
                    $this->update_week_estimate_income();
                    break;
                case self::MODE_INIT:
                    $this->generate_all_year_weeks();
                    break;
            }


            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf($this->description . ', 执行开始时间:%s, 耗时:%s, 模式:%s, 年:%s', $sNow, $cost, $this->mode, $this->year);
            Log::info($msg);
            $this->output->success($msg);
        } catch (Exception $exception) {
            $msg = $exception->getFile() . ':' . $exception->getLine() . ' ' . $exception->getMessage();
            $this->output->warning($msg);

        }

    }

    /**
     * 获取参数
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2025-07-16 16:20:46
     *
     */
    private function set_param() {
        $this->mode = $this->input->getOption('mode') ?? 1;
        if($this->mode == self::MODE_INIT) {
            $this->year = $this->input->getOption('year');
            if(empty($this->year)) {
                throw new Exception('请指定参数 --year');
            }
        }

        if($this->mode == self::MODE_UPDATE) {
            $this->date = $this->input->getOption('date');
            if(empty($this->date)) {
                $this->date = date('Ymd',strtotime('-1 day'));
            }else {
                $this->date = date('Ymd',strtotime('-1 day',strtotime($this->date)));
            }
        }
    }


    /**
     *
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2025-07-16 16:25:48
     *
     */
    private function generate_all_year_weeks() {
        // 检查是否存在
        $is_have_this_year = WeekIncomeEstimate::haveThisYearData($this->year);
        if($is_have_this_year) {
            // 该年份数据已存在
            return;
        }

        $all_weeks = Func::get_week_ordinal(1, $this->year);

        $fmt_today = date('Ymd');
        $current_week = $all_weeks[$fmt_today]['week']??55;

        $weeks = [];
        foreach ($all_weeks as $info) {
            $week = $info['week'];
            $week_days = ($weeks[$week]['week_days'] ?? 0) + 1;
            $now = date('Y-m-d H:i:s');
            // echo $week ,' status : ',($week <=> ($current_week-1)) + 2,PHP_EOL;
            $weeks[$week] = [
                'year'           => $this->year,
                'week_number'    => $week,
                'start_date'     => min($info['fmt_day'], $weeks[$week]['start_date']??$info['fmt_day']),
                'end_date'       => max($info['fmt_day'], $weeks[$week]['end_date']??$info['fmt_day']),
                'week_days'      => $week_days,
                'status'         => ($week <=> ($current_week-1)) + 2,
                'avg_ratio'      => bcdiv(($this->year_data[$this->year][$week][0] ?? $this->year_data[2025][$week][0]),100,6),
                'real_money'     => $this->year_data[$this->year][$week][1] ?? 0,
                'estimate_money' => 0,
                'avg_money'      => bcdiv(bcadd(($weeks[$week]['real_money'] ?? 0),($weeks[$week]['estimate_money'] ?? 0),6),$week_days,6),
                'created_at'     => $now,
                'updated_at'     => $now,
            ];
        }

        $weeks = array_merge($weeks);

        WeekIncomeEstimate::initYearData($weeks);
    }

    /**
     * 更新每日数据
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2025-07-17 16:49:47
     */
    private function update_week_estimate_income() {
        $y = date('Y',strtotime($this->date));

        $is_have_this_year = WeekIncomeEstimate::haveThisYearData($y);
        if(!$is_have_this_year) {
            throw new Exception('无该年份数据!');
        }

        //获取是第周
        $all_weeks = Func::get_week_ordinal(1);
        $week = $all_weeks[$this->date]['week'];

        $week_info = WeekIncomeEstimate::getWeekInfo($week,$y);

        if(!$week_info){
            throw new Exception('无该周数据!');
        }

        //判断是否是周一 更新为当前周
        if($week_info->status != WeekIncomeEstimate::STATUS_CURRENT_WEEK){
            throw new Exception('该周状态异常!');
        }

        $week_info = WeekIncomeEstimate::getWeekInfo($week,$y);

        $dimension = StatDefine::DIMENSION_HUIZONG;
        $rep = new MainRepository();
        $income_data = $rep->getBaseIncome($dimension,$week_info->start_date, $this->date, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' => 1]);
        if($income_data['status'] != 0) {
            throw new Exception('消耗统计异常!');
        }
        $real_money = $income_data['data']['income'] ?? 0;
        $week_info->real_money = $real_money;
        // $week_info->estimate_money = $week_info->estimate_money - $real_money;

        //判断当天是否是周日 如果是周日更新未来周的预估数据,当前周预估数据更新为0,当前周更为过去周
        $w = date('N', strtotime($this->date));
        if($w == 0 || $w == 7) {
            //更新下周为当前周
            $week_info->status = WeekIncomeEstimate::STATUS_PAST_WEEK;
            $week_info->save();//这里需要先保存, 因为后面会计算未来周的预估数据

            //未来周的数据
            $this->update_future_week_estimate_income($y);

            //更新下一周为当前周
            $this->update_next_week_to_current_week($week_info->week_number,$y);
            // $this->update_next_week_to_current_week(55,$y);
        }else {
            $week_info->save();
        }
    }

    /**
     * @throws Exception
     */
    private function update_future_week_estimate_income($y) {
        //更新过去周的收入
        $this->update_past_week_real_income($y);

        //计算过去周的实际收入
        $sum_real_money = WeekIncomeEstimate::getAllRealMoney($y);
        //计算未来周的预估收入
        $estimate_income = $this->estimate_income_target[$y]??$this->estimate_income_target[2025];
        $estimate_income = bcsub($estimate_income, $sum_real_money,6);

        //计算未来周每周的占比
        $this->update_future_weeks($y,$estimate_income);
    }

    /**
     * @throws Exception
     */
    private function update_past_week_real_income($y) {
        $dimension = StatDefine::DIMENSION_HUIZONG;
        $rep = new MainRepository();

        $weeks = WeekIncomeEstimate::getPastWeeks($y);

        foreach($weeks as $week) {
            $income_data = $rep->getBaseIncome($dimension,$week->start_date, $week->end_date, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' => 1]);
            if($income_data['status'] != 0) {
                throw new Exception('消耗统计异常!'.$income_data['msg']);
            }
            $_real_money          = $income_data['data']['income'] ?? 0;
            $week->real_money     = $_real_money;
            $week->estimate_money = 0;
            $week->avg_money      = bcdiv($week->real_money, $week->week_days, 6);
            $week->save();
        }
    }

    /**
     *
     *
     * @param $y
     * @param $estimate_money
     *
     * @return void
     * <AUTHOR> 2025-07-22 19:06:01
     */
    private function update_future_weeks($y, $estimate_money) {
        //获取所有未来周
        $future_weeks = WeekIncomeEstimate::getFutureWeeks($y);
        $sum_ratio = 0;
        foreach ($future_weeks as $week) {
            $sum_ratio = bcadd($week->avg_ratio, $sum_ratio, 6);
        }

        $sum__ratio = 0;
        $count = count($future_weeks);
        $c = 1;
        $d = $estimate_money;

        //计算比例 更新所有未来周的预估数据
        foreach ($future_weeks as $week) {
            if($c == $count) {
                //最后一个 使用剩余的 不用乘法
                $_ratio = bcsub(1, $sum__ratio, 6);
                $week->estimate_money = $d;
            }else {
                $_ratio = bcdiv($week->avg_ratio, $sum_ratio, 6);
                $em     = bcmul($estimate_money, $_ratio, 6);
                $d      = bcsub($d, $em, 6);

                $week->estimate_money = $em;
            }

            $sum__ratio = bcadd($_ratio, $sum__ratio, 6);
            $week->real_money = 0;
            $week->avg_money  = bcdiv($week->estimate_money,$week->week_days,6);
            $week->save();
            $c++;
        }
    }

    /**
     * @throws Exception
     */
    private function update_next_week_to_current_week(int $week_number, int $y) {
        $next_week_number = $week_number + 1;
        $next_week = WeekIncomeEstimate::getWeekInfo($next_week_number,$y);
        if(is_null($next_week)) {
            $next_week_number = 1;
            $y = $y + 1;

            $next_week = WeekIncomeEstimate::getWeekInfo($next_week_number,$y);
            if(is_null($next_week)) {
                throw new Exception('无下周数据!');
            }
        }

        $next_week->status = WeekIncomeEstimate::STATUS_CURRENT_WEEK;
        $next_week->save();
    }
}


