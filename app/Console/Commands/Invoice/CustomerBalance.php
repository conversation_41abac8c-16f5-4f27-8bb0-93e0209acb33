<?php

namespace App\Console\Commands\Invoice;

use App\Models\Common\CommonEnumModel;
use App\Models\Invoice\CustomerFinanceSnapshot;
use App\Providers\BillIncome\BillStatistics\InvoiceBalanceService;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;
use App\Providers\BillIncome\BillStatistics\BalanceService;
use App\Models\Customer;
use App\Models\CustomerMonthlyBalance;


/**
 * 每月计算发票汇总数据
 *
 * php artisan invoice:customer_balance --month 202412 --customer_id C20210310TDXJ8J
 * php artisan invoice:customer_balance --month 202412
 * php artisan invoice:customer_balance
 */
class CustomerBalance extends Command
{
    protected $signature = 'invoice:customer_balance
    {--month= : 更新余额信息至该月份(格式Ym)}
    {--customer_id= : 客户ID(多个客户ID以,隔开)}';

    protected $description = '计算客户每月余额数据';

    /**
     * -1 为全部数据
     * 0 羽乐科技
     * 1 朴道
     * @var int[] 来源
     */
    protected $source_map = [];

    /**
     * 脚本入口
     *
     * @return void
     * <AUTHOR> 2025-03-06 15:17:23
     *
     */
    public function handle(){
        $this->output->success('计算用户每月余额数据脚本开始执行');

        $this->source_map = CommonEnumModel::getTypeMaps('1');
        // $this->source_map[''] = '全部';

        //获取参数
        $params = $this->get_params();

        $last_month = CustomerFinanceSnapshot::getLastMonth();
        // $first_month = CustomerFinanceSnapshot::getFirstMonth();

        $month = array_get($params, 'month');
        $min_start_time   = '202407';
        if($month < $min_start_time) {
            $this->output->error('小于最小月份!!!');
            die;
        }

        $last_month_plus_one = date('Ym',strtotime('+1 month',strtotime($last_month.'01')));

        if($month > $last_month_plus_one) {
            $this->output->error('大于最后月份+1!!!');
            die;
        }

        $start_time   = date('Ym01', strtotime($month . '01'));
        $end_time     = date('Ymt', strtotime($month . '01'));
        $customer_id  = array_get($params, 'customer_id');

        $prev_month_data = [];
        $_prev_month_data = CustomerFinanceSnapshot::getPrevMonthData($month.'01');
        foreach($_prev_month_data as $info) {
            $tmp_key = Func::get_tmp_key($info['customer_id'], $info['source']);
            $prev_month_data[$tmp_key] = $info;
        }

        try {
            foreach ($this->source_map as $source => $source_name){
                $bs = new InvoiceBalanceService($end_time,$start_time,$source);
                //分批次查询
                $customer_id_chunk = array_chunk($customer_id,200);
                foreach($customer_id_chunk as $customer_ids){
                    $res = $bs->getBalance($customer_ids);

                    foreach ($res as $cid => $item) {
                        //计算当月未回款 不需要考虑负数的情况
                        $tmp_key = Func::get_tmp_key($cid, $source);

                        if (!isset($prev_month_data[$tmp_key])) {
                            $prev_month_data[$tmp_key] = [
                                'consume_money'   => 0,
                                'remit_money'     => 0,
                                'invoice_money'   => 0,
                                'remit_balance'   => 0,
                                'invoice_balance' => 0,
                            ];
                        }

                        $where = [
                            'customer_id' => $cid,
                            'month'       => $month,
                            'source'      => $source === '' ? - 1 : $source,
                        ];

                        $now = date('Y-m-d H:i:s');
                        $data = [
                            'customer_id'     => $cid,
                            'month'           => $month,
                            'consume_money'   => bcadd($item['consume'], $prev_month_data[$tmp_key]['consume_money'], 6),
                            'remit_money'     => bcadd($item['recharge'], $prev_month_data[$tmp_key]['remit_money'], 6),
                            'invoice_money'   => bcadd($item['invoice'], $prev_month_data[$tmp_key]['invoice_money'], 6),
                            'remit_balance'   => bcadd($prev_month_data[$tmp_key]['remit_balance'], bcsub($item['recharge'], $item['consume'], 6), 6),
                            'invoice_balance' => bcadd($prev_month_data[$tmp_key]['invoice_balance'], bcsub($item['invoice'], $item['consume'], 6), 6),
                            'source'          => $source === '' ? - 1 : $source,
                            'created_at'      => $now,
                            'updated_at'      => $now,
                        ];
                        // echo $cid,' ',$month,' ',$source_name,' ',$data['consume_money'],PHP_EOL;
                        //更新或插入
                        CustomerFinanceSnapshot::updateOrCreate($where, $data);
                    }
                }
            }
        } catch (\Exception $e) {
            $this->output->error($e->getFile().':'.$e->getLine().' '.$e->getMessage());
        }
        $this->output->success('计算用户每月余额数据脚本执行完成');
    }



    /**
     * 获取命令参数
     *
     * @return array|void
     * <AUTHOR> 2025-03-10 10:39:10
     *
     */
    protected function get_params(){
        $params = [];

        //账单日
        $params['month'] = $this->input->getOption('month') ?? date('Ym',strtotime('-1 month'));

        if(!empty($params['month'])) {
            if (!preg_match('/^\d{6}$/', $params['month'])) {
                $this->output->error('日期格式不正确');
                die;
            }
        }

        //客户ID
        $customer_id = $this->input->getOption('customer_id');
        if (!empty($customer_id)) {
            $customer_id = explode(',', $customer_id);
            //过滤掉禁用用户
            $customer_infos = Customer::getCustomerListByCustomerIds(['customer_id','name','company','create_at'],$customer_id,1);
        }else{
            // 获取所有customer_id
            $customer_infos = Customer::getListByCondition([
                ['is_delete','=', 0],
                ['create_at','<', strtotime(date('Y-m-t 23:59:59', strtotime($params['month'] . '01')))]
            ],
                ['customer_id','name','company','create_at']);
        }
        $customer_ids = [];
        $customer_map = [];
        foreach($customer_infos as $info){
            $customer_ids[] = $info['customer_id'];
            $customer_map[$info['customer_id']]['create_month'] = date('Ym', $info['create_at']);
            $customer_map[$info['customer_id']]['name']         =$info['name'];
            $customer_map[$info['customer_id']]['company']      =$info['company'];
        }
        $params['customer_id'] = $customer_ids;
        $params['customer_map'] = $customer_map;

        return $params;
    }
}