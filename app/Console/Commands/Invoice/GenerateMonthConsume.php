<?php

namespace App\Console\Commands\Invoice;

use App\Define\Common;
use App\Define\StatDefine;
use App\Models\Invoice\CustomerConsume;
use App\Models\Invoice\CustomerInvoiceConfig;
use App\Models\Product;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 生成客户上个月消耗数据
 *
 * 仅初始化时执行
 * php artisan invoice:generate_month_consume --case=1 --month=202409
 *
 * 更新消耗金额时执行
 * php artisan invoice:generate_month_consume --case=3 --month=202409 --customer_id=C20230823BDEH5L,C20180831BZ7V8N  --source=0
 * php artisan invoice:generate_month_consume --case=3 --month=202405 --source=1
 * php artisan invoice:generate_month_consume --case=3 --month=202406 --source=1
 *
 * @uses GenerateMonthConsume
 */
class GenerateMonthConsume extends Command
{
    protected $signature = 'invoice:generate_month_consume
    {--case= : 统计(1:默认情况,统计上个月整体, 2:开票模式2,统计当月1号到昨天), 3:更新消耗时传入}
    {--month= : 开始月份}
    {--customer_id= : 指定客户 多个id使用","分割,不指定为全部客户}
    {--source= : 来源,仅在重跑账单后更新消耗数据时传入}';

    const CRON_NAME = '生成客户上个月消耗数据';

    /**
     * 产品维度中聚合维度
     * 1: 主产品维度
     * 2: 子产品维度
     */
    const FATHER = 1;
    const CHILD = 0;

    /**
     *  统计方式
     *  1: 默认情况,统计上个月整体
     *  2: 开票模式2,统计当月1号到昨天
     *  3: 消耗有改动时调用, 重跑账单, 修改特殊消耗等
     * @var int
     */
    const CASE_COMMON = 1;
    const CASE_MODEL_2 = 2;
    const CASE_MODEL_3 = 3;
    private $iCase = 0;

    private $iMonth = null;
    private $iSource = null;
    private $sCustomerId = '';//model 3使用的客户id
    private $aCustomerId = [];
    private $iStartDate = null;
    private $iEndDate = null;
    private $aProductFatherMap = [];
    private $aProductList = [];

    /**
     * @throws Exception
     */
    public function handle() {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            switch ($this->iCase) {
                case self::CASE_COMMON:
                    $this->main();
                    break;
                case self::CASE_MODEL_2:
                    $this->runModel2();
                    break;
                case self::CASE_MODEL_3:
                    $this->runModel3();
                    break;
                case 4:
                    $this->test(); // todo 测试用
                    break;
                default:
                    break;
            }

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s, 统计模式:%s, 月份:%s, 客户:%s",
                $sNow, $cost, $this->iCase, $this->iMonth, implode(',', $this->aCustomerId) ?: '(空)');
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $msg = $exception->getFile().":".$exception->getLine()." ".$exception->getMessage();
            $this->output->warning($msg);

            // $oFeishu = new FeishuRepository();
            // $aMessage = [
            //     'err_msg'     => $msg,
            //     'month'       => $this->iMonth,
            //     'customer_id' => $this->aCustomerId,
            // ];
            // $oFeishu->send_card_message_to_chat_group(self::CRON_NAME . '脚本执行失败!', $aMessage);
        }
    }

    /**
     * @return void
     */
    private function setParam() {
        $this->iCase = (int)($this->input->getOption('case')) ?: self::CASE_COMMON;

        switch ($this->iCase) {
            case self::CASE_COMMON:
                $this->iMonth = (int)($this->input->getOption('month') ?: date('Ym', strtotime('-1 month', time())));
                $this->iStartDate = $this->iMonth . '01';
                $iLastDay = strtotime("{$this->iStartDate} next month -1 day");
                if($iLastDay > time()){
                    $this->output->warning('模式1不可处理当月以及以后月份的消耗数据');
                    die;
                }
                $this->iEndDate = date("Ymd", $iLastDay);
                break;
            case self::CASE_MODEL_2:
                $this->iMonth = (int)date('Ym');
                $this->iStartDate = $this->iMonth . '01';
                $this->iEndDate = date("Ymd", strtotime('-1 day'));
                break;
            case self::CASE_MODEL_3:
                $this->iSource = $this->input->getOption('source');
                $customer_ids = $this->input->getOption('customer_id') ?: [];
                if(!empty($customer_ids)){
                    $this->aCustomerId = explode(",",$customer_ids);
                }

                $this->iMonth = $this->input->getOption('month');
                $this->iStartDate = $this->iMonth . '01';
                $this_month = date("Ym");
                if($this->iMonth == $this_month){//当月支持到当天的消耗
                    $this->iEndDate = date("Ymd");
                }else if($this->iMonth < $this_month){
                    $this->iEndDate = date("Ymt", strtotime($this->iStartDate));
                }else{
                    $this->output->warning('模式3不可处理以后月份的消耗数据');
                    die;
                }
                $this->iSource = $this->input->getOption('source');
                if(!is_null($this->iSource)){
                    // $this->output->warning('请传入渠道!!!');
                    // die;
                    $this->iSource = intval($this->iSource);
                }
                break;
            default:
                break;
        }

        $sCustomerId = $this->input->getOption('customer_id') ?: '';
        if ($sCustomerId) {
            $this->aCustomerId = explode(',', $sCustomerId);
        }

        $this->aProductFatherMap = Product::getOriginParentIdMap();
        $this->aProductList = Product::getAllSubProduct();

    }

    /**
     * @return void
     */
    private function main() {
        // 聚合产品维度 => 开票模式对照
        $aModelConfig = [
            self::FATHER => [CustomerInvoiceConfig::Model_1,CustomerInvoiceConfig::Model_2,CustomerInvoiceConfig::Model_5,CustomerInvoiceConfig::Model_6],
            self::CHILD => [CustomerInvoiceConfig::Model_3, CustomerInvoiceConfig::Model_4],
        ];
        // 按照模式聚合数据
        foreach ($aModelConfig as $iFatherFlag => $aModel) {
            $aModelConfigGroup = CustomerInvoiceConfig::getDataBySourceModel($aModel);

            foreach ($aModelConfigGroup as $iSource => $aAllCustomerId) {
                $aAllCustomerId = array_unique($aAllCustomerId);
                $aCustomerIdGroup = array_chunk($aAllCustomerId, 100);
                foreach ($aCustomerIdGroup as $aCustomerId) {
                    $where = [['consume_month' ,'=', $this->iMonth], ['source','=', $iSource]];
                    $check_has_consume_data = CustomerConsume::getListByCondition('*',$where,$aCustomerId,[],[],-1,-1);
                    $al_customer_ids = array_unique(array_column($check_has_consume_data,'customer_id'));

                    $_aCustomerId = array_diff($aCustomerId, $al_customer_ids);
                    if(empty($_aCustomerId)){
                        continue;
                    }

                    $aInsertList = $this->getData($iSource, $_aCustomerId, $iFatherFlag);
                    //todo 更新
                    if ($aInsertList) {
                        CustomerConsume::batchInsert($aInsertList);
                        usleep(100000);
                    }
                }
            }
        }
    }

    /**
     * 部分使用
     * @return void
     */
    private function runModel2() {
        $aModel = [CustomerInvoiceConfig::Model_2,CustomerInvoiceConfig::Model_4];

        $aModelConfigGroup = CustomerInvoiceConfig::getDataBySourceModel($aModel);

        foreach ($aModelConfigGroup as $iSource => $aAllCustomerId) {
            $aCustomerIdGroup = array_chunk($aAllCustomerId, 100);
            foreach ($aCustomerIdGroup as $aCustomerId) {
                $aCustomerInvoiceConfig = CustomerInvoiceConfig::getOne($aCustomerId,$iSource);
                $iFatherFlag = self::FATHER;
                if(in_array($aCustomerInvoiceConfig['model'],[Common::INVOICE_MODEL_3,Common::INVOICE_MODEL_4])){
                    $iFatherFlag = self::CHILD;
                }
                $aDataList = $this->getData($iSource, $aCustomerId, $iFatherFlag);

                $aCond = [
                    ['customer_id', 'in', $aCustomerId],
                    ['source', '=', $iSource],
                    ['consume_month', '=', $this->iMonth],
                ];
                $aExistDataList = CustomerConsume::getDataByCond($aCond);
                if (empty($aExistDataList)) {
                    CustomerConsume::batchInsert($aDataList);
                } else {
                    $aOldDataList = $this->formatForCase2($aExistDataList);
                    $aNewDataList = $this->formatForCase2($aDataList);

                    $aUpdateList = [];
                    $aInsertList = [];
                    $sTime = date('Y-m-d H:i:s');
                    foreach ($aNewDataList as $sKey => $aNewData) {
                        if (!isset($aOldDataList[$sKey])) {
                            $aInsertList[] = $aNewData;
                        } else {
                            $aOldData = $aOldDataList[$sKey];
                            if ($aOldData['consume_money'] == $aNewData['consume_money']) {
                                continue;
                                // } else if ($aOldData['consume_money'] < $aNewData['consume_money']) {
                            }else{
                                $x = bcsub($aNewData['consume_money'], $aOldData['consume_money'], 6);

                                $aUpdateList[] = [
                                    'id' => $aOldData['id'],
                                    'consume_money' => $aNewData['consume_money'],
                                    'consume_balance' => bcadd($aOldData['consume_balance'], $x, 6),
                                    'consume_unpaid' => bcadd($aOldData['consume_unpaid'], $x, 6),
                                    'invoice_status' => Common::INVOICE_STATUS_INVOICE == $aOldData['invoice_status'] ? Common::INVOICE_STATUS_PART : $aOldData['invoice_status'],
                                    'updated_at' => $sTime,
                                ];
                            // } else {
                            //     // todo  消耗变小, 记录异常
                            }
                        }
                    }

                    if ($aInsertList) {
                        CustomerConsume::batchInsert($aInsertList);
                    }
                    if ($aUpdateList) {
                        CustomerConsume::batchUpdate($aUpdateList);
                    }
                }
                usleep(100000);
            }
        }
    }


    /**
     * 更新数据用
     *
     * @return void
     * <AUTHOR> 2024-09-09 17:15:29
     *
     */
    private function runModel3() {
        //如果不传入 source $this->iSource为null 不过滤来源
        //传入source会转为数字
        $aCustomerInvoiceConfigs = CustomerInvoiceConfig::getCustomermListBySourceAndModels([], $this->iSource, $this->aCustomerId);//这里传入的是一个数组 不要和sCustomer搞混

        foreach ($aCustomerInvoiceConfigs as $aCustomerInvoiceConfigInfo) {
            $source      = $aCustomerInvoiceConfigInfo['source'];
            $model       = $aCustomerInvoiceConfigInfo['model'];
            $sCustomerId = $aCustomerInvoiceConfigInfo['customer_id'];

            $iFatherFlag = self::FATHER;

            if (in_array($model, [Common::INVOICE_MODEL_3, Common::INVOICE_MODEL_4])) {
                $iFatherFlag = self::CHILD;
            }
            $aDataList = $this->getData($source, [$sCustomerId], $iFatherFlag);
            $aCond = [
                ['customer_id', '=', $sCustomerId],
                ['source', '=', $source],
                ['consume_month', '=', $this->iMonth],
            ];
            $aExistDataList = CustomerConsume::getDataByCond($aCond);
            if (empty($aExistDataList)) {
                CustomerConsume::batchInsert($aDataList);
            } else {
                $aOldDataList = $this->formatForCase2($aExistDataList);
                $aNewDataList = $this->formatForCase2($aDataList);

                $aUpdateList = [];
                $aInsertList = [];
                $sTime       = date('Y-m-d H:i:s');
                foreach ($aNewDataList as $sKey => $aNewData) {
                    if (!isset($aOldDataList[$sKey])) {
                        $aInsertList[] = $aNewData;
                    } else {
                        $aOldData = $aOldDataList[$sKey];
                        if ($aOldData['consume_money'] == $aNewData['consume_money']) {
                            continue;
                        } else {
                            //变化量
                            $x = bcsub($aNewData['consume_money'], $aOldData['consume_money'], 6);

                            $aUpdateList[] = [
                                'id'              => $aOldData['id'],
                                'consume_money'   => round($aNewData['consume_money'],2),
                                'consume_balance' => round(bcadd($aOldData['consume_balance'], $x, 6),2),
                                'consume_unpaid'  => round(bcadd($aOldData['consume_unpaid'], $x, 6),2),
                                'invoice_status'  => Common::INVOICE_STATUS_INVOICE == $aOldData['invoice_status'] ? Common::INVOICE_STATUS_PART : $aOldData['invoice_status'],
                                'updated_at'      => $sTime,
                            ];
                        }
                    }
                }
                if ($aInsertList) {
                    CustomerConsume::batchInsert($aInsertList);
                }
                if ($aUpdateList) {
                    CustomerConsume::batchUpdate($aUpdateList);
                }
            }
        }
    }

    /**
     * @param $iSource
     * @param $aCustomerId
     * @param $iFatherFlag
     * @return array
     */
    private function getData($iSource = 0, $aCustomerId = [], $iFatherFlag = 0) {
        $aDataList = $this->getIncomeData($this->iStartDate, $this->iEndDate, $aCustomerId, $iSource);

        if (empty($aDataList)) {
            return [];
        }

        // 客户 => [月份 => 销售] 对照
        $aSalesmanMap = $this->getSalesmanMap($aCustomerId);

        if ($iFatherFlag) {
            // 产品 product_id => father_id
            $aProductMap = $this->aProductFatherMap;
        } else {
            // product_id => product_id
            $aProductMap = array_column($this->aProductList, 'product_id', 'product_id');
        }

        // 按照产品维度聚合
        $aCustomerProductConsume = [];
        foreach ($aDataList as $aData) {
            $iKey = $aProductMap[$aData['product_id']] ?: $aData['product_id'];

            $iIncome = $aCustomerProductConsume[$aData['customer_id']][$iKey] ?? 0;

            $aCustomerProductConsume[$aData['customer_id']][$iKey] = bcadd($iIncome, $aData['income'], 6);
        }
        // 转化为 客户消耗数据
        $aRet = [];
        $sTime = date('Y-m-d H:i:s');
        foreach ($aCustomerProductConsume as $sCustomerId => $aProductConsume) {
            foreach ($aProductConsume as $iProductId => $sConsume) {
                $aRet[] = [
                    'customer_id'     => $sCustomerId,
                    'source'          => $iSource,
                    'consume_month'   => $this->iMonth,
                    'product_id'      => $iProductId,
                    'father_id'       => $this->aProductFatherMap[$iProductId],
                    'consume_money'   => round($sConsume,2),
                    'consume_balance' => round($sConsume,2),
                    'consume_unpaid'  => round($sConsume,2),
                    'invoice_status'  => Common::INVOICE_STATUS_INIT,
                    'salesman'        => $aSalesmanMap[$sCustomerId][$this->iMonth] ?? '',
                    'created_at'      => $sTime,
                ];
            }
        }
        return $aRet;
    }

    /**
     * @param $aCustomerId
     * @return array
     */
    private function getSalesmanMap($aCustomerId = []) {
        $oSalesmanData = new CustomerSalesmanHistoryRepository();
        return $oSalesmanData->getListMonthly($aCustomerId, $this->iMonth, $this->iMonth,'Ym');
    }

    /**
     * @param $aDataList
     * @return array
     */
    private function formatForCase2($aDataList = []){
        $aRet = [];

        foreach ($aDataList as $aItem) {
            $sKey = implode('-', [$aItem['customer_id'], $aItem['source'], $aItem['consume_month'], $aItem['product_id']]);

            $aRet[$sKey] = $aItem;
        }

        return $aRet;
    }

    /**
     * @param $iStartDate
     * @param $iEndDate
     * @param $aCustomerId
     * @param $iSource
     * @return array|mixed
     */
    private function getIncomeData($iStartDate = 0, $iEndDate = 0 , $aCustomerId = [], $iSource = 0) {
        $iDimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;
        $aResult = (new MainRepository())->getBaseIncome($iDimension, $iStartDate, $iEndDate, $aCustomerId,[],[],[],['is_query_month_data' => 0]);

        if ($aResult['status']) {
            $this->output->warning($aResult['msg']);
            // todo 有报错
            return [];
        }

        $aOriginalDataList = $aResult['data'];
        foreach ($aOriginalDataList as $sKey => $aOriginalData) {
            if ($iSource != $aOriginalData['source']) {
                unset($aOriginalDataList[$sKey]);
            }
        }

        return array_values($aOriginalDataList);

//        $aDemoList = [
//            ['apikey' => 'a1111', 'product_id' => '201', 'operator' => 'CMCC', 'income' => 1234.567809, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
//            ['apikey' => 'a1111', 'product_id' => '202', 'operator' => 'CUCC', 'income' => 2345.123456, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
//            ['apikey' => 'a2222', 'product_id' => '216', 'operator' => 'CMCC', 'income' => 4321.876543, 'number' => 15000, 'cost' => 777, 'total' => 17000, 'success' => 16000, 'valid' => 15000],
//
//            ['apikey' => 'a1111', 'product_id' => '252', 'operator' => 'CMCC', 'income' => 1234.567809, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
//            ['apikey' => 'a1111', 'product_id' => '253', 'operator' => 'CUCC', 'income' => 2345.123456, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
//            ['apikey' => 'a2222', 'product_id' => '254', 'operator' => 'CMCC', 'income' => 4321.876543, 'number' => 15000, 'cost' => 777, 'total' => 17000, 'success' => 16000, 'valid' => 15000],
//        ];
//
//
////        $aDemoList = [
////
////            ['apikey' => 'a1111', 'product_id' => '201', 'operator' => 'CMCC', 'income' => 11234.567809, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
////            ['apikey' => 'a1111', 'product_id' => '202', 'operator' => 'CUCC', 'income' => 12345.123456, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
////            ['apikey' => 'a2222', 'product_id' => '216', 'operator' => 'CMCC', 'income' => 14321.876543, 'number' => 15000, 'cost' => 777, 'total' => 17000, 'success' => 16000, 'valid' => 15000],
////
////            ['apikey' => 'a1111', 'product_id' => '252', 'operator' => 'CMCC', 'income' => 11234.567809, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
////            ['apikey' => 'a1111', 'product_id' => '253', 'operator' => 'CUCC', 'income' => 12345.123456, 'number' => 12000, 'cost' => 666, 'total' => 15000, 'success' => 14000, 'valid' => 12000],
////            ['apikey' => 'a2222', 'product_id' => '254', 'operator' => 'CMCC', 'income' => 14321.876543, 'number' => 15000, 'cost' => 777, 'total' => 17000, 'success' => 16000, 'valid' => 15000],
////        ];
//
//        $aRet = [];
//        foreach ($aCustomerId as $sCustomerId) {
//            foreach ($aDemoList as $aDemo) {
//                $aDemo['customer_id'] = $sCustomerId;
//
//                $aRet[] = $aDemo;
//            }
//        }
//
//        return $aRet;
    }

    private function test(){
        $sCustomerId = 'C20180831BZ7V8N';
        $aConsumeId = [3,4];
        $sInvoiceId = 'IN20230801YJHG07';
        $sReceiptSerial = 'G6762400129072D';
        $sRemitSerial = $sReceiptSerial;
        $sCompanyName = '惠州融宝通投资有限公司';
        $sSalesman = 'heng.duo';

        $aConsumeList = CustomerConsume::selectFormat(['*'])->whereIn('id', $aConsumeId)->get();
        $fInvoiceMoney = 0;
        foreach ($aConsumeList as $oConsume) {
            $oConsume->invoice_status = Common::INVOICE_STATUS_INVOICE;
            //$oConsume->save();

            $fInvoiceMoney = bcadd($fInvoiceMoney, $oConsume->consume_money, 6);
        }


        foreach ($aConsumeList as $oConsume) {
            $oRelInvoiceConsume = new \App\Models\Invoice\RelInvoiceConsume();
            $oRelInvoiceConsume->customer_id = $sCustomerId;
            $oRelInvoiceConsume->source = 0;
            $oRelInvoiceConsume->invoice_id = $sInvoiceId;
            $oRelInvoiceConsume->invoice_money = $fInvoiceMoney;
            $oRelInvoiceConsume->consume_id = $oConsume->id;
            $oRelInvoiceConsume->consume_month = $oConsume->consume_month;
            $oRelInvoiceConsume->product_id = $oConsume->product_id;
            $oRelInvoiceConsume->father_id = $oConsume->father_id;
            $oRelInvoiceConsume->consume_money = $oConsume->consume_money;
            $oRelInvoiceConsume->rel_money = $oConsume->consume_money;
            $oRelInvoiceConsume->invoice_model = 1;
            //$oRelInvoiceConsume->save();
        }

        $oRelCustomerInvoice = new \App\Models\Invoice\InvoiceCustomerAssoc();
        $oRelCustomerInvoice->invoice_id = $sInvoiceId;
        $oRelCustomerInvoice->customer_id = $sCustomerId;
        $oRelCustomerInvoice->month = 202407;
        $oRelCustomerInvoice->source = 0;
        $oRelCustomerInvoice->salesman = $sSalesman;
        //$oRelCustomerInvoice->save();


        $oInvoice = new \App\Models\Invoice\CustomerInvoice();
        $oInvoice->invoice_id = $sInvoiceId;
        $oInvoice->salesman = $sSalesman;
        $oInvoice->company_name = $sCompanyName;
        $oInvoice->invoice_company = $sCompanyName;
        $oInvoice->date = date('Y-m-d H:i:s');
        $oInvoice->money = $fInvoiceMoney;
        $oInvoice->invoice_type = $sInvoiceId;
        $oInvoice->status = 30;
        $oInvoice->source = 0;
        $oInvoice->rel_type = 1;
        $oInvoice->invoice_model = 1;
        $oInvoice->invoice_balance = $fInvoiceMoney;
        $oInvoice->remit_status = 0;
        $oInvoice->flush_status = 0;
        //$oInvoice->save();


        $oRemit = new \App\Models\Remit();
        $oRemit->customer_id = $sCustomerId;
        $oRemit->receipt_serial = $sReceiptSerial;
        $oRemit->remit_serial = $sRemitSerial;
        $oRemit->name = $sCompanyName;
        $oRemit->money = $fInvoiceMoney;
        $oRemit->remit_date = time();
        $oRemit->status = 3;
        $oRemit->source = 0;
        $oRemit->invoice_status = 0;
        $oRemit->remit_unconsumed = $fInvoiceMoney;
        $oRemit->remit_balance = $fInvoiceMoney;
        //$oRemit->save();




    }
}