<?php

namespace App\Console\Commands\Invoice;

use App\Define\SourceDefine;
use App\Models\Customer;
use App\Models\Invoice\CustomerInvoiceConfig;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 初始化客户各渠道开票模式
 * php artisan invoice:init_customer_model_config
 * @uses InitCustomerModelConfig
 */
class InitCustomerModelConfig extends Command
{
    protected $signature = 'invoice:init_customer_model_config';

    const CRON_NAME = '初始化客户各渠道开票模式';

    /**
     * 渠道(0:羽乐科技,1:朴道,10:浙数交)
     */
    const SOURCE_0 = SourceDefine::CUSTOMER_SOURCE_DIANHUA;
    const SOURCE_1 = SourceDefine::CUSTOMER_SOURCE_PUDAO;
    const SOURCE_10 = SourceDefine::CUSTOMER_SOURCE_ZHESHUJIAO;

    /**
     * @throws Exception
     */
    public function handle() {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s,", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $this->output->success($exception->getMessage());
        }
    }

    private function main() {
        /**
         * 付款类型(1:预付款客户,2:后付款客户)
         *
         * 渠道(0:羽乐科技,1:朴道,10:浙数交)
         *
         * 开票模式:
         * 1: 按消耗(主产品)
         * 2: 按消耗(主产品,可拆金额)
         * 3: 按消耗(子产品)
         * 4: 按消耗(子产品,可拆金额)
         * 5: 按到款
         * 6: 按到款(先票后款后消耗)
         */

        $sTime = date('Y-m-d H:i:s');
        $aCustomerList = Customer::getListByCondition([]);

        // 初始化 渠道:羽乐科技 开票模式
        $aSource0 = [];
        foreach ($aCustomerList as $aCustomer) {
            $iModel = $aCustomer->payment_type == 1 ? CustomerInvoiceConfig::Model_5 : CustomerInvoiceConfig::Model_1;
            $aSource0[] = [
                'customer_id' => $aCustomer->customer_id,
                'source' => self::SOURCE_0,
                'model' => $iModel,
                'created_at' => $sTime,
            ];
        }
        if ($aSource0) {
            CustomerInvoiceConfig::insert($aSource0);
            usleep(10000);
        }

        // 初始化 渠道:朴道
        $aSource1 = [];
        foreach ($aCustomerList as $aCustomer) {
            $aSource1[] = [
                'customer_id' => $aCustomer->customer_id,
                'source' => self::SOURCE_1,
                'model' => CustomerInvoiceConfig::Model_1,
                'created_at' => $sTime,
            ];
        }
        if ($aSource1) {
            CustomerInvoiceConfig::insert($aSource1);
            usleep(10000);
        }

        // 初始化 渠道:浙数交
        $aSource10 = [];
        foreach ($aCustomerList as $aCustomer) {
            $aSource10[] = [
                'customer_id' => $aCustomer->customer_id,
                'source' => self::SOURCE_10,
                'model' => CustomerInvoiceConfig::Model_1,
                'created_at' => $sTime,
            ];
        }
        if ($aSource10) {
            CustomerInvoiceConfig::insert($aSource10);
            usleep(10000);
        }

        // 特殊case
        // 代运营客户, 渠道:羽乐科技, 开票模式: 2: 按消耗(主产品,可拆金额)
        $this->case1();
        usleep(10000);

        // 蚂蚁客户, 渠道:羽乐科技, 开票模式: 4: 按消耗(子产品,可拆金额)
        $this->case2();
        usleep(10000);

        // 美的客户, 渠道:羽乐科技, 开票模式: 5: 按到款
        $this->case3();
        usleep(10000);

        // 字节,浙商银行, 渠道:朴道, 开票模式: 5: 按到款
        $this->case4();

        return true;
    }

    /**
     * 代运营客户, 渠道:羽乐科技, 开票模式: 2: 按消耗(主产品,可拆金额)
     * @return true
     */
    private function case1(){
        $aCustomerId = [
            'C20240723E5TLEU',
            'C202407223FYQ5N',
            'C20240612GC5B0D',
            'C20240527GUSVAC',
            'C2024022970I7HJ',
            'C20231221Q2SO6Z',
            'C202312206YG1SU',
            'C202310317HRW07',
            'C20231010VQ7Q3G',
            'C20230829YBHWPZ',
            'C20230823BDEH5L',
            'C20230629N7XUP7',
            'C202306218X2QJX',
            'C20230616U76IJY',
            'C20230509YBK9VY',
            'C20230425DB5K46',
            'C202304235J1LCK',
            'C20230411IN3O9Y',
            'C20230209XB7XGO',
            'C20220527SP8RXB',
            'C20200722WMI2AF',
            'C201908143BJAH7',
        ];
        $iSource = self::SOURCE_0;
        $a = CustomerInvoiceConfig::where('source', $iSource)->whereIn('customer_id', $aCustomerId)->update(['model' => CustomerInvoiceConfig::Model_2]);

        return true;
    }

    /**
     * 蚂蚁客户, 渠道:羽乐科技, 开票模式: 4: 按消耗(子产品,可拆金额)
     * @return true
     */
    private function case2(){
        $aCustomerId = [
            'C20190614WWMKLO',
            'C202108258VWANY',
            'C20230113Z0USHH',
        ];
        $iSource = self::SOURCE_0;
        CustomerInvoiceConfig::where('source', $iSource)->whereIn('customer_id', $aCustomerId)->update(['model' => CustomerInvoiceConfig::Model_4]);

        return true;
    }

    /**
     * 美的客户, 渠道:羽乐科技, 开票模式: 5: 按到款
     * @return true
     */
    private function case3(){
        $aCustomerId = [
            'C20200713CMP5LK',
        ];
        $iSource = self::SOURCE_0;
        CustomerInvoiceConfig::where('source', $iSource)->whereIn('customer_id', $aCustomerId)->update(['model' => CustomerInvoiceConfig::Model_5]);

        return true;
    }

    /**
     * 字节,浙商银行, 渠道:朴道, 开票模式: 5: 按到款
     * @return true
     */
    private function case4(){
        $aCustomerId = [
            'C20220721ZMV1CH',
            'C20230728ITQ0OM',
        ];
        $iSource = self::SOURCE_1;
        CustomerInvoiceConfig::where('source', $iSource)->whereIn('customer_id', $aCustomerId)->update(['model' => CustomerInvoiceConfig::Model_5]);

        return true;
    }
}