<?php

namespace App\Console\Commands\Invoice;

use App\Models\Customer;
use App\Models\Invoice\CustomerInvoiceLog;
use App\Models\Invoice\InvoiceConsume;
use App\Models\Invoice\InvoiceCustomerAssoc;
use App\Models\Invoice\InvoiceRemit;
use App\Models\Receipt;
use App\Models\Remit;
use App\Models\RemitSplitPrice;
use App\Repositories\Invoice\InvoiceRepositorie;
use Illuminate\Console\Command;
use App\Models\Invoice\CustomerInvoice;
use Exception;
use Illuminate\Support\Facades\DB;


/**
 * 废弃 除了后付费 金额,公司与开票完全匹配的回款自动认款 其余运营手动认款
 *
 * 匹配拆单数据和发票
 *
 * php artisan invoice:match_remit_invoice --customer_id C2019070560JUMS
 */
class MatchRemitInvoice extends Command
{
    protected $signature = 'invoice:match_remit_invoice
    {--customer_id=  : 指定客户 多个id使用","分割,不指定为全部预付费客户}';


    protected $description = '匹配拆单数据和发票';

    private $start = '';
    private $end = '';
    /**
     * @var array|false|string[]
     */
    private $customer_ids;
    /**
     * @var mixed
     */
    private $need_update_customer_invoice = false;

    public function handle() {
        die;
        //设置参数
        try {
            $this->output->success("【{$this->description}】 开始");
            $this->setOptions();
            $this->match_remit_invoice();
            $this->output->success("【{$this->description}】 完成");
        } catch (Exception $e) {
            dd($e->getFile(),PHP_EOL,$e->getLine(),PHP_EOL,$e->getMessage());
        }
    }


    /**
     * 处理传入参数
     * @access protected
     * @return void
     * @throws Exception
     */
    protected function setOptions(){
        $customer_id = $this->input->getOption('customer_id');
        if (!empty($customer_id)) {//替换默认值
            $this->customer_ids = explode(',', $customer_id);
        } else {//所有客户
            $this->customer_ids = Customer::getAllCustomerIds();
        }
    }


    /**
     * 匹配回款和发票
     *
     * 1. 查询已经拆单数据 2023年以后
     * 2. 查询回款单
     * 3. 根据回款金额和公司查询发票
     * 4. 匹配 invoice_remit invoice_consume
     *
     * @throws Exception
     */
    private function match_remit_invoice(){
        // 1. 查询remit_split_price表,获取收款单号 排除2023年以前的消耗月份
        $receipt_serial_list = Remit::getListByCustomerIdsAndStatus($this->customer_ids);
        $receipt_serial_list = array_column($receipt_serial_list, 'receipt_serial');
        // $receipt_serial_list = ["C0646V400024JXZ"];
        $receipt_serial_list = array_chunk($receipt_serial_list, 20);

        foreach($receipt_serial_list as $receipt_serial_chunk) {

            $remit_split_list = RemitSplitPrice::getListByReceiptSerial($receipt_serial_chunk);
            if (empty($remit_split_list)) {
                // $this->output->note("【{$this->description}】 ".implode(",",$receipt_serial_chunk)." 未拆单 或 2023年以前的收款单");
                //未拆单 或 2023年以前的收款单
                continue;
            }

            $receipt_serial_arr = array_column($remit_split_list, 'receipt_serial');

            //2
            $remit_list = Remit::getListByReceiptSerial($receipt_serial_arr);

            foreach ($remit_list as $remit_info) {
                //3 根据金额和公司名称进行匹配 后付费客户按消耗金额开票
                $invoice_where = [
                    'money'           => $remit_info['money'],
                    'invoice_company' => $remit_info['name'],
                    'status'          => InvoiceRepositorie::INVOICE_STATUS_ISSUE,//已开票状态
                ];
                $invoice_list  = CustomerInvoice::getListByCondition('*', $invoice_where);

                if (empty($invoice_list)) {
                    continue;
                } else {
                    foreach($invoice_list as $invoice_info) {
                        $invoice_id = $invoice_info['invoice_id'];
                        $receipt_serial = $remit_info['receipt_serial'];
                        // $remit_date = date("Y-m-d H:i:s", $remit_info['remit_date']);
                        //4
                        $invoice_remit_info = InvoiceRemit::getListByReceiptSerialAndReceiptSeriial($invoice_id, $receipt_serial);

                        //查询拆分中是否存在invocie_cousome表中是否存在相同月份
                        $rsl_list = RemitSplitPrice::getListByReceiptSerial([$receipt_serial]);
                        $rsl_month_list = array_column($rsl_list,'month');
                        $rsl_month_list = array_map(function($value) {
                            return intval($value);
                        }, $rsl_month_list);

                        $ic_list = InvoiceConsume::getListByCondition('*',['invoice_id' => $invoice_id]);
                        $ic_month_list = array_column($ic_list,'month');

                        $month_intersect = array_intersect($rsl_month_list,$ic_month_list);
                        //添加发票与收款单关系
                        if (empty($invoice_remit_info)) {
                            if(empty($month_intersect)) {
                                //拆分金额没有和发票对应消耗的月份重叠,即这个收款不是这个发票流水号的回款
                                $this->output->note("【{$this->description}】 发票:" . $invoice_id . " 和 收款单:" . $receipt_serial . " 不相关");
                            }else{
                                InvoiceRemit::add($invoice_id, $receipt_serial);
                                $this->output->success("【{$this->description}】 发票:" . $invoice_id . " 和 收款单:" . $receipt_serial . " 匹配成功");
                            }
                        } else {
                            $this->output->note("【{$this->description}】 发票:" . $invoice_id . " 和 收款单:" . $receipt_serial . " 已匹配");
                        }
                    }
                }
            }
        }
    }
}

