<?php

namespace App\Console\Commands\Invoice;

use App\Models\Common\CommonEnumModel;
use App\Models\Customer;
use App\Models\CustomerMonthlyBalance;
use App\Models\Invoice\CustomerConsume;
use App\Models\Invoice\CustomerInvoice;
use App\Models\Invoice\CustomerInvoiceConfig;
use App\Models\Invoice\InvoiceConsume;
use App\Models\Invoice\InvoiceCustomerAssoc;
use App\Models\Invoice\InvoiceInfo;
use App\Models\Invoice\InvoiceRemit;
use App\Models\Invoice\RelInvoiceConsume;
use App\Models\Invoice\RelInvoiceRemit;
use App\Models\Invoice\RelRemitConsume;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\Remit;
use App\Models\RemitSplitPrice;
use App\Repositories\Customer\ConsumeRepositorie;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\Invoice\InvoiceRepositorie;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Define\Common;

/**
 * 转移当前发票数据到新表
 *
 *
 * php artisan invoice:transfer_invoice_data --type all
 * php artisan invoice:transfer_invoice_data --type remit
 * php artisan invoice:transfer_invoice_data --type customer_consume
 * php artisan invoice:transfer_invoice_data --type rel_invoice_consume
 * php artisan invoice:transfer_invoice_data --type rel_remit_consume
 */
class TransferInvoiceData extends Command
{
    protected $signature = 'invoice:transfer_invoice_data
    {--type= : 类型  all 所有}';
    // rel_invoice_consume 发票与消耗
    // rel_invoice_remit,发票与回款
    // rel_remit_consume,回款与消耗
    // remit 新增字段
    // receipt 新增字段
    // customer_invoice 新增字段

    protected $description = '发票数据转移脚本';

    private $type = 'all';

    /**
     * @throws Exception
     */
    public function handle() {
        //设置参数
        try {
            $this->output->success("【{$this->description}】 开始");
            $this->setParam();

            $this->trans_customer_consume();
            $this->trans_rel_invoice_consume();
            $this->trans_rel_invoice_remit();
            $this->trans_rel_remit_consume();
            $this->trans_remit();
            $this->trans_customer_invoice();

            $this->output->success("【{$this->description}】 完成");
        } catch (Exception $e) {
            throw new Exception($e->getFile().":".$e->getLine()." ".$e->getMessage());
        }
    }
    private function setParam() {
        $this->type = $this->input->getOption('type');
    }

    /**
     * 消耗统计数据
     *
     * @return void
     * <AUTHOR> 2024-09-04 17:38:48
     *
     */
    private function trans_customer_consume(){
        if($this->type != 'all' && $this->type != 'customer_consume'){
            $this->output->success("[客户消耗统计表] 跳过!");
            return;
        }

        $this->output->success("开始执行 [客户消耗统计表] 数据转移!");
        $keep_loop = true;
        $last_id = 0;
        do{
            $customer_consume_list = CustomerConsume::getListByCondition('*',[['id','>',$last_id]],[],[],[],1,200,['id','asc']);
            if(empty($customer_consume_list)){
                $keep_loop = false;
            }else{
                foreach($customer_consume_list as $customer_consume_info){
                    $last_id = $customer_consume_info['id'];

                    //更新小数未两位
                    // CustomerConsume::where('id', $last_id)->update([
                    //     'consume_balance' => Func::mockMysqlEncrypt($customer_consume_info['']),
                    //     'consume_unpaid'  => Func::mockMysqlEncrypt($customer_consume_info),
                    //     'invoice_status'  => Common::INVOICE_STATUS_INVOICE,
                    // ]);

                    //更新
                    //consume_balance 未开票消耗金额
                    //consume_unpaid  未到款消耗金额
                    //invoice_status  开票状态
                    //查询拆单表是否存在已经拆单数据,如果已经拆单
                    $remit_split_price_list = RemitSplitPrice::getRemitSplitPriceList($customer_consume_info['customer_id'],$customer_consume_info['product_id'],$customer_consume_info['consume_month'],$customer_consume_info['source']);
                    //回款金额 到款金额
                    $split_money = array_sum(array_column($remit_split_price_list, 'money'));
                    //如果存在回款金额更新相关金额状态
                    if($split_money != 0) {
                        if (bccomp($customer_consume_info['consume_money'], $split_money, 6) != 0) {

                            $_update_consume_balance = bcsub($customer_consume_info['consume_money'], $split_money, 6);
                            $_update_consume_unpaid = bcsub($customer_consume_info['consume_money'], $split_money, 6);

                            if(bcsub($customer_consume_info['consume_balance'],$_update_consume_balance,2) != 0 &&
                            bcsub($customer_consume_info['consume_unpaid'],$_update_consume_unpaid,2) != 0){
                                CustomerConsume::where('id', $last_id)->update([
                                    'consume_balance' => Func::mockMysqlEncrypt($_update_consume_balance),
                                    'consume_unpaid'  => Func::mockMysqlEncrypt($_update_consume_unpaid),
                                    'invoice_status'  => Common::INVOICE_STATUS_INVOICE,
                                ]);
                                // echo $last_id, " updated by remit_split_price.";
                                // echo "consume_money:\t", $customer_consume_info['consume_money'], "\t";
                                // echo "split_money:\t", $split_money, PHP_EOL;
                            }else{
                                // echo $last_id,"\t not need update!",PHP_EOL;
                            }
                        }else{
                            // echo $last_id,"\t not need update!!",PHP_EOL;
                        }
                    }else{
                        //如果没有回款金额查询消耗表invoice_consume
                        $where = [
                            ['customer_id', '=', $customer_consume_info['customer_id']],
                            ['father_id', '=', $customer_consume_info['product_id']],
                            ['month', '=', $customer_consume_info['consume_month']],
                            ['source', '=', $customer_consume_info['source']],
                        ];
                        $invoice_consume_info = InvoiceConsume::getInfoByCondition($where);
                        $rel_invoice_consume = RelInvoiceRemit::getListByConsumeId($last_id);

                        if(!empty($invoice_consume_info)){
                            $consume_balance = bcsub($customer_consume_info['consume_money'],$invoice_consume_info['consume'],6);

                            $invoice_status = Common::INVOICE_STATUS_INVOICE;

                            if(empty($invoice_consume_info['invoice_id']) && empty($rel_invoice_consume)) {
                                $consume_balance = $customer_consume_info['consume_money'];//未开票金额 为消耗金额
                                $invoice_status = Common::INVOICE_STATUS_INIT;
                            }

                            //四舍五入
                            $consume_balance = round($consume_balance,2);

                            $update_consume_data = [];
                            $update_consume_data['consume_balance'] = Func::mockMysqlEncrypt($consume_balance);
                            // if($consume_balance == 0){
                            //     echo  Func::mockMysqlEncrypt($consume_balance),PHP_EOL;
                            // }
                            if($customer_consume_info['invoice_status'] != $invoice_status) {
                                $update_consume_data['invoice_status'] = $invoice_status;
                            }

                            if(!empty($update_consume_data)){
                                CustomerConsume::where('id', $last_id)->update($update_consume_data);

                                // echo $last_id," updated customer_consume consume_balance: ";
                                // foreach($update_consume_data as $column_name => $value) {
                                //     if($column_name == 'consume_balance') {
                                //         echo $customer_consume_info['consume_balance'], " -> ", $consume_balance, ", ";
                                //     }
                                //     if($column_name == 'invoice_status') {
                                //         echo $customer_consume_info['invoice_status'], " -> ", $invoice_status, ", ";
                                //     }
                                // }
                                // echo PHP_EOL;
                            }else{
                                // echo $last_id,"\t not need update!!!",PHP_EOL;
                            }
                        }else{
                            // echo $last_id,"\t not need update!!!!",PHP_EOL;
                        }
                    }
                    $this->customer_consume_update_round_two_significant_figures($last_id);
                }
            }
        }while($keep_loop);

        $this->output->success("[客户消耗统计表] 执行完成!");
    }

    private function customer_consume_update_round_two_significant_figures($customer_consume_id){
        $cc_info = CustomerConsume::selectFormat('*')->where('id', $customer_consume_id)->first();
        // var_dump($cc_info);
        //
        // echo '---------------',PHP_EOL;

        $cc_info->consume_money   = Func::mockMysqlEncrypt(round($cc_info->consume_money,2));
        $cc_info->consume_balance = Func::mockMysqlEncrypt(round($cc_info->consume_balance,2));
        $cc_info->consume_unpaid  = Func::mockMysqlEncrypt(round($cc_info->consume_unpaid,2));

        $cc_info->save();

        // $cc_info_new = CustomerConsume::selectFormat('*')->where('id', $customer_consume_id)->first()->toArray();
        //
        // var_dump($cc_info_new);
        // echo '===============================',PHP_EOL;
        // //四舍五入2位小数
        // $round_money_data = [
        //     'consume_money'   => round($cc_info['consume_money'],2),
        //     'consume_balance' => round($cc_info['consume_balance'],2),
        //     'consume_unpaid'  => round($cc_info['consume_unpaid'],2),
        // ];
        // dd($round_money_data);
        // CustomerConsume::where('id', $customer_consume_id)->update($round_money_data);
    }

    /**
     *
     * rel_invoice_consume 发票与消耗关系
     * 1. 获取原消耗表数据 invoice_consume
     * 2. 更新 customer_consume
     * 3. 更新 rel_invoice_consume
     *
     * @return void
     * <AUTHOR> 2024-09-04 16:31:56
     *
     */
    private function trans_rel_invoice_consume(){
        if($this->type != 'all' && $this->type != 'rel_invoice_consume'){
            $this->output->success("[发票与消耗关系] 跳过!");
            return;
        }
        $this->output->success("开始执行 [发票与消耗关系] 数据转移!");
        $now = date("Y-m-d H:i:s");

        $keep_loop = true;
        $last_id = 0;
        do{
            $invoice_consume_list = InvoiceConsume::getListByCondition('*',[['id','>',$last_id]],[],[],[],1,200,['id','asc']);
            if(empty($invoice_consume_list)){
                $keep_loop = false;
            }else{
                foreach($invoice_consume_list as $invoice_consume_info){
                    $last_id = $invoice_consume_info['id'];
                    // echo $invoice_consume_info['invoice_id'],PHP_EOL;
                    // $where = [
                    //     ['customer_id', '=', $invoice_consume_info['customer_id']],
                    //     ['father_id', '=', $invoice_consume_info['father_id']],
                    //     ['month', '=', $invoice_consume_info['month']],
                    //     ['source', '=', $invoice_consume_info['source']],
                    // ];
                    $consume_info = CustomerConsume::getInfoByCondition([
                        ['consume_month','=',$invoice_consume_info['month']],
                        ['source','=',$invoice_consume_info['source']],
                        ['customer_id','=',$invoice_consume_info['customer_id']],
                        ['product_id','=',$invoice_consume_info['father_id']],
                    ]);
                    if(empty($consume_info)){
                        continue;
                    }
                    $customer_invoice_config_info = CustomerInvoiceConfig::getOne($invoice_consume_info['customer_id'],$invoice_consume_info['source']);
                    if(empty($invoice_consume_info['invoice_id'])){
                        continue;
                    }

                    $invoice_list = explode(",",$invoice_consume_info['invoice_id']);
                    foreach($invoice_list as $invoice_id) {
                        $invoice_info = CustomerInvoice::getOneCustomerInvoice($invoice_id);
                        if(empty($invoice_info)){
                            echo $invoice_id," are not fond in customer_invoice!!",PHP_EOL;
                            continue;
                        }
                        $_money = bccomp($invoice_info['money'],$consume_info['consume_money'],6);

                        $_money = $_money > 0 ? $consume_info['consume_money'] : $invoice_info['money'] ;

                        $ric_info = RelInvoiceConsume::getInfo($consume_info['id'],$invoice_id,$invoice_consume_info['customer_id'],$invoice_consume_info['source'],$invoice_consume_info['month']);

                        if(empty($ric_info)) {
                            // echo $_money,PHP_EOL;
                            RelInvoiceConsume::insert([
                                'consume_id'    => $consume_info['id'],
                                'customer_id'   => $invoice_consume_info['customer_id'],
                                'source'        => $invoice_consume_info['source'],
                                'invoice_id'    => $invoice_id,
                                'invoice_money' => Func::mockMysqlEncrypt($_money),
                                'consume_month' => $invoice_consume_info['month'],
                                'product_id'    => $invoice_consume_info['father_id'],
                                'father_id'     => 0,
                                'consume_money' => Func::mockMysqlEncrypt($consume_info['consume_money']),
                                'invoice_model' => $customer_invoice_config_info['model'],
                                'rel_money'     => Func::mockMysqlEncrypt($_money),
                                'created_at'    => $now,
                                'updated_at'    => $now,
                            ]);
                        }else{
                            echo "update rel_invoice_consume rel_invoice_consume id:",$ric_info['id'],PHP_EOL;
                            RelInvoiceConsume::where('id',$ric_info['id'])->update([
                                'invoice_money' => Func::mockMysqlEncrypt($_money),
                                'consume_money' => Func::mockMysqlEncrypt($consume_info['consume_money']),
                                'invoice_model' => $customer_invoice_config_info['model'],
                                'rel_money'     => Func::mockMysqlEncrypt($_money),
                                'updated_at'    => $now,
                            ]);
                        }
                    }
                }
            }
        }while($keep_loop);


        $this->output->success("[发票与消耗关系] 执行完成!");
    }

    /**
     * 发票与回款
     *
     * @return void
     * <AUTHOR> 2024-09-05 10:25:39
     *
     */
    private function trans_rel_invoice_remit(){
        if($this->type != 'all' && $this->type != 'rel_invoice_remit'){
            $this->output->success("[发票与回款] 跳过!");
            return;
        }
        $this->output->success("开始执行 [发票与回款] 数据转移!");
        $now = date("Y-m-d H:i:s");
        $keep_loop = true;
        $last_id = 0;
        do{
            $invoice_remit_list = InvoiceRemit::getListByCondition('*',[['id','>',$last_id]],1,200,['id','asc']);
            if(empty($invoice_remit_list)){
                $keep_loop = false;
            }else{
                foreach($invoice_remit_list as $invoice_remit_info){
                    $last_id = $invoice_remit_info['id'];

                    $invoice_id     = $invoice_remit_info['invoice_id'];
                    $receipt_serial = $invoice_remit_info['receipt_serial'];

                    $invoice_info = CustomerInvoice::getOneCustomerInvoice($invoice_id);
                    if(empty($invoice_info)){
                        echo $invoice_id," not exists!!",PHP_EOL;
                        continue;
                    }

                    $remit_info = Remit::getInfoByReceiptSerial($receipt_serial);
                    $customer_id    = $remit_info['customer_id'];
                    $source         = $remit_info['source'];

                    $customer_invoice_config_info = CustomerInvoiceConfig::getOne($customer_id,$source);


                    $invoice_money = min($invoice_info['money'], $remit_info['money']);
                    $rii_info = RelInvoiceRemit::getInfo($customer_id,$remit_info['source'],$invoice_id,$receipt_serial);

                    if(empty($rii_info)) {
                        // rel_invoice_remit 插入数据
                        RelInvoiceRemit::insert([
                            'customer_id'    => $customer_id,// 客户ID
                            'source'         => $remit_info['source'],// 来源(0:羽乐科技,1:朴道,10:浙数交)
                            'invoice_id'     => $invoice_id,//发票流水号
                            'invoice_money'  => Func::mockMysqlEncrypt($invoice_money),// 开票金额
                            'remit_serial'   => $receipt_serial,// 到款流水单号
                            'receipt_serial' => $receipt_serial,// 收款流水单号
                            'remit_money'    => Func::mockMysqlEncrypt($invoice_money),// 到款金额
                            'invoice_model'  => $customer_invoice_config_info['model'],// 开票模式
                            'rel_money'      => Func::mockMysqlEncrypt($invoice_money),// 关联金额
                            'created_at'     => $now,
                            'updated_at'     => $now,
                        ]);
                        echo "add rel invoice remit:\t", $invoice_id, "\t", $receipt_serial, PHP_EOL;
                    }else{
                        RelInvoiceRemit::where('id',$rii_info['id'])->update([
                            'invoice_money'  => Func::mockMysqlEncrypt($invoice_money),// 开票金额
                            'remit_money'    => Func::mockMysqlEncrypt($invoice_money),// 到款金额
                            'rel_money'      => Func::mockMysqlEncrypt($invoice_money),// 关联金额
                            'updated_at'     => $now,
                        ]);
                        echo "update rel invoice remit id:\t", $rii_info['id'], PHP_EOL;
                    }
                }
            }
        }while($keep_loop);

        $this->output->success("[发票与回款] 执行完成!");
    }

    //rel_remit_consume
    private function trans_rel_remit_consume(){
        if($this->type != 'all' && $this->type != 'rel_remit_consume'){
            $this->output->success("[回款与消耗] 跳过!");
            return;
        }
        $this->output->success("开始执行 [回款与消耗] 数据转移!");
        $now = date("Y-m-d H:i:s");
        $keep_loop = true;
        $last_id = 0;
        do{
            $remit_split_price_list = RemitSplitPrice::getListByCondition('*',[['id','>',$last_id]],1,200,['id','asc']);
            if(empty($remit_split_price_list)){
                $keep_loop = false;
            }else{
                foreach($remit_split_price_list as $remit_split_price_info){
                    $last_id    = $remit_split_price_info['id'];
                    $product_id = $remit_split_price_info['product_id'];
                    $month      = $remit_split_price_info['month'];

                    $receipt_serial = $remit_split_price_info['receipt_serial'];
                    $remit_info = Remit::getInfoByReceiptSerial($receipt_serial);
                    if(empty($remit_info)){
                        continue;
                    }

                    $customer_id = $remit_info['customer_id'];
                    $source      = $remit_info['source'];

                    $consume_info = CustomerConsume::getInfoByCondition([
                        ['customer_id', '=', $customer_id],
                        ['consume_month', '=', $month],
                        ['product_id', '=', $product_id],
                        ['source', '=', $source],
                    ]);

                    if(empty($consume_info)){
                        echo "没有相关消耗数据",PHP_EOL;
                        continue;
                    }

                    $rrc_info = RelRemitConsume::getListByConsumeIds([$consume_info['id']]);

                    if(!empty($rrc_info)){
                        echo "该消耗发票已经关联!",PHP_EOL;
                        continue;
                    }
                    RelRemitConsume::insert([
                        'customer_id'    => $customer_id,
                        'source'         => $source,
                        'remit_serial'   => $receipt_serial,
                        'receipt_serial' => $receipt_serial,
                        'remit_money'    => Func::mockMysqlEncrypt($consume_info['consume_money']),
                        'consume_id'     => $consume_info['id'],
                        'consume_month'  => $remit_split_price_info['month'],
                        'product_id'     => $remit_split_price_info['product_id'],
                        'father_id'      => 0,
                        'consume_money'  => Func::mockMysqlEncrypt($consume_info['consume_money']),
                        'rel_money'      => Func::mockMysqlEncrypt($consume_info['consume_money']),
                        'admin'          => $remit_split_price_info['admin'],
                        'created_at'     => $now,
                        'updated_at'     => $now,
                    ]);
                    echo "add rel consume remit:\t", $consume_info['id'], "\t", $receipt_serial, PHP_EOL;
                }
            }
        }while($keep_loop);

        $this->output->success("[回款与消耗] 执行完成!");
    }

    private function trans_remit(){
        if($this->type != 'all' && $this->type != 'remit'){
            $this->output->success("[回款单] 跳过!");
            return;
        }
        $this->output->success("开始执行 [回款单] 数据转移!");

        $keep_loop = true;
        $last_id = 0;
        do{
            $remit_list = Remit::getListByCondition('*',[['id','>',$last_id]],1,200,['id','asc']);
            if(empty($remit_list)){
                $keep_loop = false;
            }else{
                foreach($remit_list as $remit_info){
                    $last_id        = $remit_info['id'];
                    $receipt_serial = $remit_info['receipt_serial'];

                    $remit_split_list = RemitSplitPrice::getListByReceiptSerial([$receipt_serial]);//传入一个数组

                    $split_sum_money = array_sum(array_column($remit_split_list, 'money'));

                    $_money = bcsub($remit_info['money'], $split_sum_money,2);

                    Remit::where('id',$last_id)->update([
                        'invoice_status'   => Common::INVOICE_STATUS_INVOICE,//已开票
                        'remit_unconsumed' => Func::mockMysqlEncrypt($_money),
                        'remit_balance'    => Func::mockMysqlEncrypt($_money),
                    ]);

                    echo $receipt_serial," 已更新相关金额:\t",$remit_info['money'],"-",$split_sum_money,"=",$_money, PHP_EOL;
                }
            }
        }while($keep_loop);

        $this->output->success("[回款单] 执行完成!");
    }



    private function trans_customer_invoice(){
        if($this->type != 'all' && $this->type != 'customer_invoice'){
            $this->output->success("[发票表] 跳过!");
            return;
        }
        $this->output->success("开始执行 [发票表] 数据转移!");
        //更新
        $keep_loop = true;
        $last_id = 0;
        do{
            $customer_invoice_list = CustomerInvoice::getListByCondition('*',[['id','>',$last_id]],[],[],[],1,200,['id','asc']);

            if(empty($customer_invoice_list)){
                $keep_loop = false;
            }else{
                foreach($customer_invoice_list as $customer_invoice_info){
                    $last_id    = $customer_invoice_info['id'];
                    $invoice_id = $customer_invoice_info['invoice_id'];

                    $invoice_model = Common::INVOICE_MODEL_6;//朴道默认为先票后款
                    if($customer_invoice_info['source'] == 0){
                        $invoice_customer_assoc_info = InvoiceCustomerAssoc::getListByInvoiceId($invoice_id);
                        $invoice_model = Common::INVOICE_MODEL_1;
                        if(!empty($invoice_customer_assoc_info)) {
                            $customer_id   = $invoice_customer_assoc_info[0]['customer_id'];
                            $invoice_model = CustomerInvoiceConfig::getOne($customer_id, 0);
                            $invoice_model = $invoice_model['model'];
                        }
                    }

                    $invoice_list = InvoiceConsume::getListByInvoiceIds([$invoice_id]);
                    $rel_type = empty($invoice_list) ? 2 : 1;


                    $_money = 0;
                    if($customer_invoice_info['source'] == 0) {
                        //获取收款单
                        $remit_list         = InvoiceRemit::getListByInvoiceId($invoice_id);
                        $receipt_serial_arr = array_column($remit_list, 'receipt_serial');
                        $remit_list         = Remit::getListByReceiptSerial($receipt_serial_arr);
                        $remit_sum_money    = array_sum(array_column($remit_list, 'money'));

                        $comp_res = bccomp($remit_sum_money, $customer_invoice_info['money'], 2);

                        $_money = bcsub($customer_invoice_info['money'], $remit_sum_money, 2);

                        if ($comp_res > 0) {
                            if (count($receipt_serial_arr) == 1) {
                                $_money = 0;
                            } else {
                                echo $invoice_id, " 的回款金额大于发票金额,需要手动处理!", implode(',', $receipt_serial_arr), PHP_EOL;
                                continue;
                            }
                        }
                    }

                    CustomerInvoice::where('id', $last_id)->update([
                        'rel_type'        => $rel_type,//关联类型 1:关联消耗 2:关联到款
                        'invoice_model'   => $invoice_model,//票模式
                        'invoice_balance' => Func::mockMysqlEncrypt($_money),//票结余金额
                    ]);

                    echo $invoice_id," 已更新:\t",$rel_type,",\t",$invoice_model,"\t",$_money, PHP_EOL;

                }
            }
        }while($keep_loop);

        $this->output->success("[发票表] 执行完成!");
    }
}

