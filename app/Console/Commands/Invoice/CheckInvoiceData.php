<?php

namespace App\Console\Commands\Invoice;

use App\Define\Common;
use App\Models\Common\CommonEnumModel;
use App\Models\Customer;
use App\Models\CustomerMonthlyBalance;
use App\Models\Invoice\CustomerInvoice;
use App\Models\Invoice\InvoiceConsume;
use App\Models\Invoice\InvoiceCustomerAssoc;
use App\Models\Invoice\InvoiceInfo;
use App\Models\Invoice\InvoiceRemit;
use App\Models\Invoice\RelInvoiceConsume;
use App\Models\Invoice\RelInvoiceRemit;
use App\Models\Invoice\RelRemitConsume;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\Remit;
use App\Models\RemitSplitPrice;
use App\Repositories\Customer\ConsumeRepositorie;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\Invoice\InvoiceRepositorie;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;
use Exception;
use Illuminate\Support\Facades\DB;


/**
 * 逐条校验发票数据
 *
 * php artisan invoice:check_invoice_data --invoice_id IN20230925JC9N2A
 *
 */
class CheckInvoiceData extends Command
{
    protected $signature = 'invoice:check_invoice_data
                            {--invoice_id= : 传入的发票流水号,不传则为所有发票流水号}';

    protected $description = '逐条校验发票数据';

    //发票 => 收款单数组
    // 多个发票 => 多个收款单
    protected $invoice_ids = [
    ];


    /**
     * @throws Exception
     */
    public function handle() {
        //设置参数

        $para_invoice_id = $this->input->getOption('invoice_id') ?: '';

        if(!empty($para_invoice_id)) {
            $this->invoice_ids = explode(',', $para_invoice_id);
        }else {
            $all_invoice = CustomerInvoice::get()->toArray();
            $this->invoice_ids = array_column($all_invoice,'invoice_id');
        }

        try {
            $this->output->success("【{$this->description}】 开始");

            foreach($this->invoice_ids as $invoice_id) {

                $this->check($invoice_id);//修复

            }

            $this->output->success("【{$this->description}】 完成");
        } catch (Exception $e) {
            throw new Exception($e->getFile().":".$e->getLine()." ".$e->getMessage());
        }
    }

    /**
     * 修复
     *
     * @throws Exception
     */
    private function check($invoice_id){
        try {
            $this->output->success($invoice_id." 开始校验");
            // DB::beginTransaction();//开启事务

            //发票详情
            $invoice_info = CustomerInvoice::getOneCustomerInvoice($invoice_id);
            echo "invoice_id: ",$invoice_id,PHP_EOL;
            echo "status: ",$invoice_info['status'],PHP_EOL;
            echo "money: ",$invoice_info['money'],PHP_EOL;
            echo "invoice_balance: ",$invoice_info['invoice_balance'],PHP_EOL;
            echo "invoice_model: ",$invoice_info['invoice_model'],PHP_EOL;

            switch ($invoice_info['status']) {
                case Common::INVOICE_STATUS_PART_REMIT :
                case Common::INVOICE_STATUS_DONE_REMIT :
                    echo '校验发票与消耗: ';
                    $check_consume_result = $this->check_consume($invoice_id,$invoice_info['money']);
                    if($check_consume_result) {
                        echo "OK", PHP_EOL;
                    }else {
                        echo "NOT OK!!", PHP_EOL;
                        return;
                    }

                    echo '校验发票与回款: ';
                    $check_remit_result = $this->check_remit($invoice_id,$invoice_info['money']);
                    if($check_remit_result) {
                        echo "OK", PHP_EOL;
                    }else {
                        echo "NOT OK!!", PHP_EOL;
                        return;
                    }

                    break;
                default:
                    echo "该状态无需校验",PHP_EOL;
                    break;
            }

            //发票-消耗
            $rel_invoice_consume_list = RelInvoiceConsume::getListByInvoiceId($invoice_id);


            $this->output->success($invoice_id." 校验完成");

            // DB::commit();//提交事务
        } catch (Exception $e) {
            // DB::rollBack();//回滚事务
            throw new Exception('脚本异常:! '.$e->getFile().':'.$e->getLine().' '.$e->getMessage());
        }
    }


    private function check_consume($invoice_id,$invoice_money) {
        $consume_arr = RelInvoiceConsume::getListByInvoiceId($invoice_id);
        $sum_rel_money = array_reduce($consume_arr, function($sum_money,$consume_info){
            return bcadd($sum_money,$consume_info['rel_money'],6);
        },0);

        return bccomp($sum_rel_money,$invoice_money,6) == 0;
    }


    private function check_remit($invoice_id,$invoice_money) {
        $remit_arr = RelInvoiceRemit::getListByInvoice($invoice_id);
        $sum_rel_money = array_reduce($remit_arr, function($sum_money,$consume_info){
            return bcadd($sum_money,$consume_info['rel_money'],6);
        },0);

        return bccomp($sum_rel_money,$invoice_money,6) == 0;
    }
}


