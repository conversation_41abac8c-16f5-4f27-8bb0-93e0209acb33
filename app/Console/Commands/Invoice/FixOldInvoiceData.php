<?php

namespace App\Console\Commands\Invoice;

use App\Define\Common;
use App\Models\Common\CommonEnumModel;
use App\Models\Customer;
use App\Models\CustomerMonthlyBalance;
use App\Models\Invoice\CustomerConsume;
use App\Models\Invoice\CustomerInvoice;
use App\Models\Invoice\InvoiceConsume;
use App\Models\Invoice\InvoiceCustomerAssoc;
use App\Models\Invoice\InvoiceInfo;
use App\Models\Invoice\InvoiceRemit;
use App\Models\Invoice\RelInvoiceConsume;
use App\Models\Invoice\RelInvoiceRemit;
use App\Models\Invoice\RelRemitConsume;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\Remit;
use App\Models\RemitBalance;
use App\Models\RemitSplitPrice;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\AutoSplitPriceRepository;
use App\Repositories\Customer\ConsumeRepositorie;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\Invoice\InvoiceRepositorie;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use SebastianBergmann\CodeCoverage\Report\PHP;


/**
 * 发票数据修复脚本
 *
 *
 * php artisan invoice:fix_old_invoice_data
 */
class FixOldInvoiceData extends Command
{
    private $invoice_idx = 0;
    protected $signature = 'invoice:fix_old_invoice_data';

    protected $description = '发票历史数据修复脚本';

    private $user_name_map = [
        '多恒'   => 'heng.duo',
        '唐振鑫' => 'zhenxin.tang',
        '谢乔琳' => 'qiaolin.xie',
        '李鹏'   => 'peng.li',
        '彭馨瑶' => 'xinyao.peng',
        '刘强'   => 'qiang.liu',
        '赵志辉' => 'zhihui.zhao01',
        '王龙亮' => 'longliang.wang',
        '姜立庆' => 'liqing.jiang',
        '王梦思' => 'mengsi.wang',
        '万耀'   => 'yao.wan',
        '李芝明' => 'zhiming.li',
        '马彬芳' => 'binfang.ma',
        '余圣恩' => 'shengen.yu',
        '秦超'   => 'chao.qin',
        '赵伟'   => 'wei.zhao',
        '李磊'   => 'lei.li',
        '王成功' => 'chenggong.wang',
        '陈伟'   => 'wei.chen',
        '邱斌'   => 'bin.qiu',
        '郭励臻' => 'lizhen.guo',
        '郭利珍' => 'lizhen.guo',
        '俞圣恩' => 'shengen.yu',
        '王婷婷' => 'tingting.wang',
    ];

    private $invoice_type_map = [
        '专票'     => 1,
        '普票'     => 2,
        '电子普票' => 3,
    ];


    /** @var array 待修复数据 */
    private $data_to_be_repaired = [
        //['发票号', '发票类型', '开票日期', '请票人', '发票抬头', '发票总金额', 'customer_id', 'remit'],
        ['28577361', '普票', '2018-8-6', '王成功', '深圳光华普惠科技有限公司', '50000', 'C201901294TNIOR', 'historicalData00132'],
        ['03298975', '专票', '2017-8-31', '赵伟', '杭州圣数科技有限公司', '100000', 'C20181101CWTTBO', 'historicalData00018'],
        ['03298997', '专票', '2017-9-15', '刘强', '永仑车汇（上海）科技信息有限公司', '30000', 'C201811019FAXD9', 'historicalData00019'],
        ['05713090', '普票', '2017-10-19', '王梦思', '南京紫金普惠商务咨询有限公司', '50000', 'C20181101QBHHO0', 'historicalData00026'],
        ['28804349', '普票', '2017-10-24', '马彬芳', '上海重信企业信用征信服务有限公司', '100000', 'C20181101QZFBJQ', 'historicalData00023'],
        ['09589573', '专票', '2017-11-9', '邱斌', '北京恒诚千里征信有限公司', '100000', 'C2018110138JB60', 'historicalData00032'],
        ['09589576', '专票', '2017-11-9', '邱斌', '上海秦苍信息科技有限公司', '100000', 'C20181101VVPH6G', 'historicalData00038'],
        ['09589579', '专票', '2017-11-10', '马彬芳', '上海益芯金融信息服务有限公司', '100000', 'C20181101T548OF', 'historicalData00024'],
        ['06993091', '专票', '2017-11-15', '邱斌', '福州分啦网络科技有限公司', '12000', 'C20181101YPB9L7', 'historicalData00027'],
        ['28804372', '普票', '2017-11-22', '王婷婷', '厦门口子金融信息服务有限公司', '50000', 'C201811016QTZSB', 'historicalData00030'],
        ['06993130,06993131,06993132,06993133,06993134', '专票', '2017-11-23', '王梦思', '微额速达（集团）有限公司', '500000', 'C20190129VJZO2O', 'historicalData00037'],
        ['06993141', '专票', '2017-11-30', '邱斌', '北京云车信息咨询有限责任公司', '20000', 'C201811016LKRUP', 'historicalData00035'],
        ['04251061,04251062,04251063,04251064,04251065', '专票', '2017-12-20', '王梦思', '成都柠檬时光科技有限公司', '500000', 'C201811015ZS131', 'historicalData00054'],
        ['28804451', '普票', '2017-12-29', '王梦思', '北京有袋网络科技有限公司', '10000', 'C2018110130YKSX', 'historicalData00049'],
        ['28804461', '普票', '2018-1-9', '马彬芳', '暮霖金融信息服务（上海）有限公司', '20000', 'C20181101X507LE', 'historicalData00055'],
        ['28804465', '普票', '2018-1-12', '邱斌', '易车无忧（北京）汽车服务有限公司', '20000', 'C20181101FV5032', 'historicalData00052'],
        ['08989092', '专票', '2018-1-15', '王梦思', '天津今日头条科技有限公司', '100000', 'C20181101PBHX9M', 'historicalData00060'],
        ['26067711', '普票', '2018-1-18', '马彬芳', '杭州兜信数据技术有限公司', '50000', 'C20181101OT2IDD', 'historicalData00043'],
        ['08989110,08989111', '专票', '2018-1-23', '王成功', '北京天启智创信息技术有限公司', '150000', 'C201811015PKP5M', 'historicalData00056'],
        ['08989114', '专票', '2018-1-24', '邱斌', '徙木金融信息服务（上海）有限公司', '10000', 'C20181101N2RO9B', 'historicalData00058'],
        ['05614732', '专票', '2018-1-30', '王梦思', '上海欣禾量澄信息科技有限公司', '50000', 'C20181101A4313G', 'historicalData00064'],
        ['05614735', '专票', '2018-1-30', '秦超', '深圳赫美小额贷款股份有限公司', '100000', 'C20181101UYXHHX', 'historicalData00065'],
        ['26067750', '普票', '2018-2-27', '赵伟', '福州惠泰投资咨询有限公司', '50000', 'C20181101UDRGMY', 'historicalData00061'],
        ['09160840', '专票', '2018-3-1', '王梦思', '北京乐智汇科技有限公司', '100000', 'C20181101JI6H4L', 'historicalData00063'],
        ['09160851', '专票', '2018-3-7', '邱斌', '上海撷芳信息科技有限公司', '100000', 'C20181101KXPRKM', 'historicalData00073,historicalData00074'],
        ['09160852', '专票', '2018-3-12', '邱斌', '上海撷芳信息科技有限公司', '100000', 'C20181101KXPRKM', 'historicalData00076'],
        ['26067761', '普票', '2018-3-12', '秦超', '深圳市信用之家金融服务有限公司', '50000', 'C20181101FSHIF9', 'historicalData00066'],
        ['09160858', '专票', '2018-3-13', '秦超', '上海通善互联网金融信息服务有限公司', '100000', 'C201811012H5ERU', 'historicalData00069'],
        ['15849354', '专票', '2018-3-26', '赵伟', '量富征信管理有限公司', '10000', 'C201811010OWLVK', 'historicalData00072'],
        ['02409903,02409904', '专票', '2018-4-13', '陈伟', '人人贷商务顾问（北京）有限公司', '200000', 'C20181101QU9C0N', 'historicalData00077'],
        ['02409915', '专票', '2018-4-20', '俞圣恩', '深圳财富农场互联网金融服务有限公司', '20000', 'C201811012A06ZS', 'historicalData00075'],
        ['02409929', '专票', '2018-4-23', '赵伟', '上海久劲网络科技有限公司', '10000', 'C20181101A9XUFO', 'historicalData00080'],
        ['71172629', '普票', '2018-4-27', '俞圣恩', '厦门点时成金信息技术咨询有限公司', '10000', 'C20181101ZQMALK', 'historicalData00081'],
        ['71172638', '普票', '2018-5-3', '万耀', '深圳启元信息服务有限公司', '50000', 'C20181101CCE390', 'historicalData00083'],
        ['71172639', '普票', '2018-5-3', '邱斌', '宜农卓越投资管理（北京）有限公司', '10000', 'C201811014JOQPE', 'historicalData00086'],
        ['08500597', '专票', '2018-5-9', '王梦思', '上海翼倍信息科技有限公司', '20000', 'C20181101OU3082', 'historicalData00044'],
        ['08500616', '专票', '2018-5-12', '邱斌', '北京钱到到金服科技有限公司', '20000', 'C2018110139A8VG', 'historicalData00092'],
        ['71172684,71172685', '普票', '2018-6-7', '余圣恩', '湖南省钱多多商务咨询有限公司', '200000', 'C20181101107M4G', 'historicalData00098'],
        ['15137269', '专票', '2018-6-8', '万耀', '深圳市随手科技有限公司', '100000', 'C20181101LHVREG', 'historicalData00106'],
        ['15137275', '专票', '2018-6-11', '彭馨瑶', '上海骑呗信息技术有限公司', '100000', 'C20181101G8K9DA', 'historicalData00096'],
        ['15137280', '专票', '2018-6-12', '邱斌', '北京钱到到金服科技有限公司', '50000', 'C2018110139A8VG', 'historicalData00097'],
        ['15137305,15137306,15137307,15137308,15137309', '专票', '2018-6-19', '秦超', '万惠投资管理有限公司', '500000', 'C20181101SRJ7YR', 'historicalData00108'],
        ['20527961,20527962,20527964', '专票', '2018-6-22', '马彬芳', '杭州信易融科技有限公司', '300000', 'C201811014C10XD', 'historicalData00110'],
        ['22152495,22152496', '专票', '2018-7-1', '赵伟', '杭州信易融科技有限公司', '200000', 'C201811014C10XD', 'historicalData00116'],
        ['22152497', '专票', '2018-7-1', '彭馨瑶', '曲水好融车网络科技有限公司', '20000', 'C20181101BNWR79', 'historicalData00115'],
        ['22152504,22152505,22152506,22152507', '专票', '2018-7-3', '陈伟', '人人贷商务顾问（北京）有限公司', '400000', 'C20181101QU9C0N', 'historicalData00109'],
        ['23260276', '专票', '2018-7-9', '邱斌', '福州分啦网络科技有限公司', '10000', 'C20181101YPB9L7', 'historicalData00118'],
        ['71172709', '普票', '2018-7-10', '王成功', '西安友信金融外包服务有限公司', '50000', 'C20181101548QVP', 'historicalData00122'],
        ['23260288,23260289,23260290', '专票', '2018-7-12', '陈伟', '北京凤凰智信信息技术有限公司', '300000', 'C20181101D9VKRT', 'historicalData00121'],
        ['71172716,71172717,71172718', '普票', '2018-7-19', '万耀', '山西晟盾信息科技有限公司', '300000', 'C20181101JEGLAL', 'historicalData00117'],
        ['71172719', '普票', '2018-7-19', '李磊', '安徽永夯科技有限公司', '20000', 'C20181101MGKK4J', 'historicalData00114'],
        ['71172725', '普票', '2018-7-26', '多恒', '深圳派生天秤科技有限公司', '80000', 'C20181101RE6GD8', 'historicalData00140'],
        ['71172729', '普票', '2018-7-30', '多恒', '天银信息科技（广州）有限公司', '50000', 'C20181101Y1MUO3', 'historicalData00123'],
        ['71172733,71172734,71172735', '普票', '2018-8-3', '赵伟', '太和县即呗网络科技有限公司', '300000', 'C2018110109LGJV', 'historicalData00107'],
        ['75155045', '普票', '2018-8-17', '万耀', '深圳市巴斯光年信息技术开发有限公司', '100000', 'C20181101C5K5Y4', 'historicalData00139'],
        ['75155046', '普票', '2018-8-17', '陈伟', '杭州悦车网络科技有限公司', '10000', 'C201811010TFXLG', 'historicalData00142'],
        ['29967155', '专票', '2018-9-6', '邱斌', '福州分啦网络科技有限公司', '20000', 'C20181101YPB9L7', 'historicalData00146'],
        ['29967161', '专票', '2018-9-7', '王龙亮', '晋城银行股份有限公司', '100000', 'C20180903PZEF3D', 'historicalData00162'],
        ['54872879,54872880,54872881', '普票', '2018-9-12', '王龙亮', '临泉县慕斯电子商务有限公司', '300000', 'C20181101UB91OT', 'historicalData00150'],
        ['29967174', '专票', '2018-9-14', '陈伟', '人人贷商务顾问（北京）有限公司', '100000', 'C20181101QU9C0N', 'historicalData00163'],
        ['54872883', '普票', '2018-9-14', '多恒', '惠州融宝通投资有限公司', '100000', 'C20180831BZ7V8N', 'historicalData00149'],
        ['54872893', '普票', '2018-9-19', '王龙亮', '厦门同钱信息科技有限公司', '10000', 'C20181101R4Y8BO', 'historicalData00153'],
        ['54872894', '普票', '2018-9-19', '王龙亮', '厦门格隽金融信息服务有限公司', '50000', 'C20181016J5QX55', 'historicalData00154'],
        ['40053540', '专票', '2018-9-19', '邱斌', '北京想就拿信息科技有限公司', '30000', 'C20181101390VRS', 'historicalData00155'],
        ['54872904', '普票', '2018-9-28', '赵伟', '福州臻一亿网络科技有限公司', '30000', 'C20181101UDRGMY', 'historicalData00112'],
        ['54872908', '普票', '2018-10-10', '邱斌', '福州市易通行网络科技有限公司', '20000', 'C20181101G5LNRB', 'historicalData00160'],
        ['40053581', '专票', '2018-10-19', '彭馨瑶', '宝德融资租赁（上海）有限公司', '50000', 'C20181101RF4UG7', 'historicalData00171'],
        ['40053583', '专票', '2018-10-22', '邱斌', '福州分啦网络科技有限公司', '20000', 'C20181101YPB9L7', 'historicalData00164'],
        ['50711751', '专票', '2018-11-21', '李芝明', '广东鸿特普惠信息服务有限公司', '100000', 'C20190116SIMKBZ', 'historicalData00201'],
        ['50711692', '专票', '2018-11-8', '余圣恩', '广东粤财金融云科技股份有限公司', '100000', 'C2018112007WOWU', 'historicalData00191'],
        ['50711679', '专票', '2018-11-4', '邱斌', '广州市瑞蚨互联网小额贷款有限公司', '50000', 'C20181112S6CBPQ', 'historicalData00188'],
        ['50711680', '专票', '2018-11-5', '王成功', '霍尔果斯智融未来信息科技有限公司', '50000', 'C201810196KCVX0', 'historicalData00181'],
        ['44073534', '专票', '2018-10-29', '李磊', '浙江聚有财金融服务外包有限公司', '20000', 'C20181101CTH7AF', 'historicalData00167'],
        ['54872949,54872950,54872951', '普票', '2018-11-8', '王龙亮', '临泉县璟程商贸有限公司', '300000', 'C20181127HMJJS7', 'historicalData00187'],
        ['50711740,50711741,50711742,50711743', '专票', '2018-11-19', '陈伟', '人人贷商务顾问（北京）有限公司', '350000', 'C20181101QU9C0N', 'historicalData00184'],
        ['54872946,54872947,54872948', '普票', '2018-11-8', '万耀', '山西晟盾信息科技有限公司', '300000', 'C20181101JEGLAL', 'historicalData00179'],
        ['94193187', '普票', '2018-11-30', '王成功', '陕西沐金羊金融信息服务有限公司', '100000', 'C20190218KTCQLR', 'historicalData00210'],
        ['50711731', '专票', '2018-11-16', '万耀', '深圳市瑞信昌信息咨询有限公司', '20000', 'C20181101QF2Z1U', 'historicalData00190'],
        ['44073527,44073528,44073529', '专票', '2018-10-24', '李芝明', '深圳普惠快捷金融服务有限公司', '300000', 'C20181127JO35TD', 'historicalData00172'],
        ['50711712', '专票', '2018-11-12', '万耀', '深圳市后河出行科技有限公司', '100000', 'C201811012QMNCH', 'historicalData00183'],
        ['54872959', '普票', '2018-11-20', '王成功', '西安友信金融外包服务有限公司', '50000', 'C20181101548QVP', 'historicalData00199'],
        ['53971618,53971619', '专票', '2018-11-23', '李磊', '鑫涌算力信息科技（上海）有限公司', '150000', 'C20181101CKS5WI', 'historicalData00202'],
        ['56657609', '专票', '2018-11-29', '赵志辉', '广东致汇汽车服务有限责任公司', '5000', 'C201811010WPSW7', 'historicalData00204'],
        ['56657615', '专票', '2018-12-3', '彭馨瑶', '杭州大猷网络科技有限公司', '100000', 'C201811217JE09X', 'historicalData00195'],
        ['56657618', '专票', '2018-12-3', '万耀', '深圳市随手科技有限公司', '100000', 'C20181101LHVREG', 'historicalData00242'],
        ['56657631', '专票', '2018-12-5', '陈伟', '北京凤凰智信信息技术有限公司', '100000', 'C20181101D9VKRT', 'historicalData00220'],
        ['94193195,94193196,94193197', '普票', '2018-12-6', '赵伟', '太和县即呗网络科技有限公司', '300000', 'C2018110109LGJV', 'historicalData00185'],
        ['56657649', '专票', '2018-12-7', '邱斌', '福州分啦网络科技有限公司', '20000', 'C20181101YPB9L7', 'historicalData00208'],
        ['94193210,94193211,94193212,94193213,94193214,94193215,94193216,94193217,94193218,94193219,94193220,94193221', '普票', '2018-12-12', '多恒', '深圳派生天秤科技有限公司', '1200000', 'C20181101RE6GD8', 'historicalData00234'],
        ['94193225', '普票', '2018-12-14', '陈伟', '北京贷鱼科技有限公司', '50000', 'C20181217LBUPPX', 'historicalData00263,historicalData00228'],
        ['94193228', '普票', '2018-12-14', '王龙亮', '浙江融润科技有限公司', '50000', 'C20181224RPTY3Q', 'historicalData00241'],
        ['94193229', '普票', '2018-12-14', '多恒', '天银信息科技（广州）有限公司', '30000', 'C20181101Y1MUO3', 'historicalData00214'],
        ['58279298,58279299', '专票', '2018-12-17', '万耀', '深圳投哪金融服务有限公司', '200000', 'C20181228WS1IGI', 'historicalData00246'],
        ['58279303', '专票', '2018-12-19', '赵伟', '安徽皖新融资租赁有限公司', '10000', 'C20181210T25RSP', 'historicalData00231'],
        ['94193232', '普票', '2018-12-19', '王龙亮', '杭州小周到金融服务外包有限公司', '70000', 'C20181219TSASI7', 'historicalData00216'],
        ['94193235', '普票', '2018-12-21', '彭馨瑶', '浙江链机信息科技有限公司', '100000', 'C20181224443PZ3', 'historicalData00219'],
        ['63033589', '专票', '2018-12-21', '彭馨瑶', '杭州蜂融网络科技有限公司', '100000', 'C20181101F6RRLK', 'historicalData00078'],
        ['63033593', '专票', '2018-12-21', '李磊', '杭州惠风网络科技有限公司', '20000', 'C20181228DKFJWH', 'historicalData00235'],
        ['94193241', '普票', '2018-12-24', '王龙亮', '昊涵（杭州）金融信息服务有限公司', '30000', 'C201811272J8H1X', 'historicalData00222'],
        ['63033604,63033605,63033606', '专票', '2018-12-25', '万耀', '山西晟盾信息科技有限公司', '300000', 'C20181101JEGLAL', 'historicalData00226'],
        ['63033608,63033609,63033613,63033614,63033615', '专票', '2018-12-26', '王成功', '微额速达（集团）有限公司', '500000', 'C20190129VJZO2O', 'historicalData00236'],
        ['94193258', '普票', '2019-1-7', '多恒', '天银信息科技（广州）有限公司', '50000', 'C20181101Y1MUO3', 'historicalData00244'],
        ['65353173', '专票', '2019-1-8', '邱斌', '上海撷芳信息科技有限公司', '50000', 'C20181101KXPRKM', 'historicalData00248'],
        ['94193259,94193260', '普票', '2019-1-8', '王龙亮', '临泉县鸿贤商贸有限公司', '200000', 'C20181101UB91OT', 'historicalData00227'],
        ['65353175', '专票', '2019-1-8', '邱斌', '上海撷芳信息科技有限公司', '100000', 'C20181101KXPRKM', 'historicalData00253'],
        ['65353176', '专票', '2019-1-9', '陈伟', '轻易科技有限公司', '50000', 'C20190110II8XQA', 'historicalData00247'],
        ['65353202', '专票', '2019-1-23', '彭馨瑶', '杭州金团实业有限公司', '80000', 'C20181219I74RET', 'historicalData00240'],
        ['65353211', '专票', '2019-1-25', '邱斌', '福州分啦网络科技有限公司', '20000', 'C20181101YPB9L7', 'historicalData00250'],
        ['06435061', '普票', '2019-1-25', '余圣恩', '深圳市前海鼎昱通互联网金融服务有限公司', '50000', 'C20190219C2Q7CP', 'historicalData00264'],
        ['06435067', '普票', '2019-2-13', '赵伟', '深圳市中创金联互联网科技有限公司', '50000', 'C20190129IH5TCV', 'historicalData00256'],
        ['65353222', '专票', '2019-2-14', '王龙亮', '杭州领智文化创意有限公司', '10000', 'C20190115SQH1M5', 'historicalData00268'],
        ['65353236', '专票', '2019-2-20', '王成功', '无锡信成网络科技有限公司', '10000', 'C20190212LTI4GE', 'historicalData00266'],
        ['06435072', '普票', '2019-2-22', '邱斌', '沈阳佳禄宝科技有限公司', '50000', 'C20190215QBR25V', 'historicalData00262'],
        ['06435073', '普票', '2019-2-26', '赵伟', '杭州银榕汇金融信息服务有限公司', '50000', 'C201812184QDOWP', 'historicalData00270'],
        ['65353250', '专票', '2019-2-27', '多恒', '深圳市恒信永利金融服务有限公司', '50000', 'C2019032668WHR7', 'historicalData00269'],
        ['06435075', '普票', '2019-3-4', '陈伟', '北京极速云科技发展有限公司', '30000', 'C201903077LXWJM', 'historicalData00277'],
        ['06435079', '普票', '2019-3-5', '邱斌', '江西省明数网络科技有限公司', '20000', 'C201902288QNIU5', 'historicalData00271'],
        ['13418696', '专票', '2019-3-6', '王成功', '北京无相创利科技有限公司', '50000', 'C20190308HD6R3D', 'G7799700123989C'],
        ['13418697', '专票', '2019-3-6', '王成功', '北京星宁科技有限公司', '20000', 'C201902251G3O3Q', 'G9738900103164C'],
        ['13418709', '专票', '2019-3-7', '邱斌', '沐金农（北京）科技有限公司', '30000', 'C20181101390VRS', 'historicalData00275'],
        ['06435084', '普票', '2019-3-8', '多恒', '天银信息科技（广州）有限公司', '50000', 'C20181101Y1MUO3', 'historicalData00276'],
        ['06435085', '普票', '2019-3-8', '多恒', '深圳派生天秤科技有限公司', '96020.74', 'C20181101RE6GD8', 'historicalData00280'],
        ['06435086,06435087,06435088', '普票', '2019-3-11', '赵伟', '太和县博洛会商贸有限公司', '300000', 'C2018110109LGJV', 'historicalData00267'],
        ['13418730,13418731', '专票', '2019-3-13', '邱斌', '广州市瑞蚨互联网小额贷款有限公司', '200000', 'C20181112S6CBPQ', 'historicalData00288'],
        ['13418752', '专票', '2019-3-20', '邱斌', '北京博益普惠信息科技有限公司', '10000', 'C20190307XZ2NLF', 'historicalData00282'],
        ['13418756,13418757', '专票', '2019-3-21', '邱斌', '沣邦融资租赁（上海）有限公司', '110000', 'C20190103UFJ66U', 'G0932400039742C'],
        ['13418773', '专票', '2019-3-25', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'historicalData00283'],
        ['41504871', '普票', '2019-4-3', '余圣恩', '深圳美美网络科技有限公司', '10000', 'C201903080LBBSL', 'historicalData00287'],
        ['24652023', '专票', '2019-4-9', '邱斌', '福州分啦网络科技有限公司', '20000', 'C20181101YPB9L7', 'G3624500088617C'],
        ['24652034', '专票', '2019-4-15', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G1811700037486C'],
        ['24652055', '专票', '2019-4-22', '万耀', '山西晟盾信息科技有限公司', '70000', 'C20181101JEGLAL', 'G5713500059084C,G4518000014230C'],
        ['24652056,24652057', '专票', '2019-4-22', '陈伟', '人人贷商务顾问（北京）有限公司', '150000', 'C20181101QU9C0N', 'G0528800039271C'],
        ['24652060', '专票', '2019-4-23', '王成功', '深圳光华普惠科技有限公司', '100000', 'C201901294TNIOR', 'G5706000144455C'],
        ['24652063,24652064', '专票', '2019-4-23', '赵志辉', '微贷有限公司', '200000', 'C20181101L888MM', 'G0206600197441C'],
        ['24652073', '专票', '2019-4-26', '余圣恩', '重庆卓凡汽车销售有限公司', '30000', 'C20190314POHOQD', 'KK316600029059C'],
        ['24652075,24652076', '专票', '2019-4-28', '赵志辉', '凡普金科集团有限公司', '150000', 'C20190417PX262B', 'G3678500206380C'],
        ['24652077', '专票', '2019-4-28', '万耀', '山西晟盾信息科技有限公司', '100000', 'C20181101JEGLAL', 'G0206600173741C'],
        ['41504887', '普票', '2019-4-28', '余圣恩', '赤壁市肇鑫科技有限责任公司', '100000', 'C20190514Y00G9R', 'G5705800022530C'],
        ['24652093', '专票', '2019-5-5', '赵志辉', '启畅数据科技（上海）有限公司', '20000', 'C201906033JMXND', 'G4915900264364C'],
        ['24652100', '专票', '2019-5-6', '陈伟', '海尔消费金融有限公司', '100000', 'C20190505USNJG6', 'G4915800014638C'],
        ['41504896', '普票', '2019-5-8', '赵伟', '南京骄尧信息科技有限公司', '50000', 'C20190510U99AUS', 'KK027000255981C'],
        ['00220660,00220661', '专票', '2019-5-10', '赵志辉', '凡普金科集团有限公司', '200000', 'C20190417PX262B', 'G4359100167397C'],
        ['00220662', '专票', '2019-5-13', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G5532300356059C'],
        ['00220664', '专票', '2019-5-13', '邱斌', '北京钱到到金服科技有限公司', '10000', 'C2018110139A8VG', 'G8470200079349C'],
        ['41504903', '普票', '2019-5-14', '赵伟', '资趣（厦门）科技有限公司', '50000', 'C20181101ZHT6LR', 'historicalData00111'],
        ['00220669', '专票', '2019-5-14', '邱斌', '北京博益普惠信息科技有限公司', '10000', 'C20190307XZ2NLF', 'G0849200083584C'],
        ['00220680', '专票', '2019-5-17', '余圣恩', '湖南浩瀚汇通互联网小额贷款有限公司', '50000', 'C20181101QF0SXN', 'historicalData00091'],
        ['00220682', '专票', '2019-5-17', '邱斌', '北京捷越联合金融信息服务有限公司', '20000', 'C20190808JRW00J', 'G4248600042881C'],
        ['00220687', '专票', '2019-5-18', '邱斌', '深圳天道计然金融服务有限公司', '100000', 'C20181101KXPRKM', 'G2195800102418C'],
        ['00220688', '专票', '2019-5-18', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G9805000233176C'],
        ['00220705', '专票', '2019-5-23', '邱斌', '福州分啦网络科技有限公司', '20000', 'C20181101YPB9L7', 'G4369000164245C'],
        ['00220707', '专票', '2019-5-23', '李磊', '重庆无界领智普惠商务信息咨询有限公司', '20000', 'C20200323VLZPO1', 'G6057300115677C'],
        ['00220711,00220712,00220713,00220714', '专票', '2019-5-27', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G0159500044706C'],
        ['00220720', '专票', '2019-5-30', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G3195500174260C'],
        ['00220722,00220723', '专票', '2019-5-30', '余圣恩', '深圳微融信息科技有限公司', '150000', 'C20190529S9OYIW', 'G2195800057520C'],
        ['00220737', '专票', '2019-6-4', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G3532400053322C'],
        ['10207522', '专票', '2019-6-5', '王成功', '晋中市丰茂小额贷款有限公司', '20000', 'C201906103U0CGV', 'G7705500078077C'],
        ['10207534', '专票', '2019-6-11', '邱斌', '北京博益普惠信息科技有限公司', '20000', 'C20190307XZ2NLF', 'G8454800058128C'],
        ['10207538', '专票', '2019-6-11', '王成功', '郑州启志网络科技有限公司', '50000', 'C20190514N5IQ0H', 'G8270500114919C'],
        ['10207539', '专票', '2019-6-11', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G6940500685885C'],
        ['10207548,10207549,10207550,10207551', '专票', '2019-6-13', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G4835700181848C'],
        ['77110422', '普票', '2019-6-13', '余圣恩', '深圳复融数据科技有限公司', '50000', 'C20190923ZA9710', 'G9731900195461C'],
        ['77110428', '普票', '2019-6-18', '多恒', '深圳潮温新概念网络科技有限公司', '50000', 'C20180831BZ7V8N', 'G6762400129072C'],
        ['10207565,10207566', '专票', '2019-6-18', '王龙亮', '上海十露盘科技有限公司', '200000', 'C20190531MGLE27', 'G6299000025293C'],
        ['10207589', '专票', '2019-6-25', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G7512800030757C'],
        ['77110430', '普票', '2019-6-25', '多恒', '天银信息科技（广州）有限公司', '100000', 'C20181101Y1MUO3', 'G8513800059263C'],
        ['10207596', '专票', '2019-6-25', '赵志辉', '四川汇安融信息技术服务有限公司', '10000', 'C2018121479DEIP', 'historicalData00258'],
        ['04371091,04371092,04371093,04371094', '专票', '2019-7-4', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G6802700077554C'],
        ['04371095', '专票', '2019-7-4', '李磊', '杭州惠风网络科技有限公司', '50000', 'C20181228DKFJWH', 'historicalData00284'],
        ['04371099', '专票', '2019-7-4', '王成功', '北京众惠信创信息科技有限公司', '10000', 'C20190701UMV2GR', 'G0411700068527C'],
        ['04371114', '专票', '2019-7-4', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G9732100014214C'],
        ['77110441', '普票', '2019-7-10', '赵志辉', '义乌市手彻贸易有限公司', '10000', 'C2019062730ZU96', 'KK793200031137C'],
        ['04371142', '专票', '2019-7-12', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G2671300565593C'],
        ['04371146', '专票', '2019-7-15', '万耀', '深圳市随手科技有限公司', '50000', 'C20181101LHVREG', 'G8996600017710C'],
        ['04371147,04371148,04371149,04371150', '专票', '2019-7-15', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G0309200128342C'],
        ['04371167', '专票', '2019-7-22', '万耀', '山西晟盾信息科技有限公司', '50000', 'C20181101JEGLAL', 'G6508100164655C'],
        ['04371175,04371176', '专票', '2019-7-24', '陈伟', '人人贷商务顾问（北京）有限公司', '200000', 'C20181101QU9C0N', 'G0124600720980C'],
        ['04371179', '专票', '2019-7-25', '陈伟', '北京极速云科技发展有限公司', '30000', 'C201903077LXWJM', 'G0309200184351C'],
        ['04371190,04371191,04371192,04371193', '专票', '2019-7-30', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G5278500022229C'],
        ['77110465', '普票', '2019-7-24', '赵志辉', '义乌市手彻贸易有限公司', '50000', 'C2019062730ZU96', 'KK547800286141C'],
        ['04371206', '专票', '2019-8-5', '李鹏', '上海翰迪数据服务有限公司', '20000', 'C201908141OQUCU', 'G9064200193139C'],
        ['61518008', '普票', '2019-8-5', '邱斌', '江西省明数网络科技有限公司', '10000', 'C201902288QNIU5', 'G4385800036810C'],
        ['04371212,04371213,04371214,04371215', '专票', '2019-8-6', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G9290300074681C'],
        ['04371217', '专票', '2019-8-6', '赵伟', '杭州圣数科技有限公司', '20000', 'C20181101CWTTBO', 'G9404700116719C'],
        ['04371243', '专票', '2019-8-16', '邱斌', '福州分啦网络科技有限公司', '10000', 'C20181101YPB9L7', 'G7841600032842C'],
        ['04371257,04371258,04371259,04371260,04371261', '专票', '2019-8-19', '赵志辉', '凡普金科集团有限公司', '450000', 'C20190417PX262B', 'G5651800042351C'],
        ['04371277', '专票', '2019-8-26', '陈伟', '广州市智度互联网小额贷款有限公司', '50000', 'C201909104C7ADM', 'G0166400097423C'],
        ['04371290', '专票', '2019-9-3', '王成功', '北京无相创利科技有限公司', '20000', 'C20190308HD6R3D', 'G2680900066512C'],
        ['03100849,03100850,03100851,03100852', '专票', '2019-9-5', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G3191900044582C'],
        ['03100945,03100946,03100947,03100948', '专票', '2019-10-8', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G8914000070449C'],
        ['03100885,03100886,03100887,03100888', '专票', '2019-9-16', '赵志辉', '凡普金科集团有限公司', '400000', 'C20190417PX262B', 'G2885800153011C'],
        ['03100894,03100895,03100896,03100897,03100898,03100899,03100900,03100901', '专票', '2019-9-18', '赵志辉', '凡普金科集团有限公司', '800000', 'C20190417PX262B', 'G5782600034962C'],
        ['61518054', '普票', '2019-10-14', '王龙亮', '驻马店市大漠商贸有限公司', '100000', 'C20190716AIU5C7', 'G8607500152952C'],
        ['03101011', '专票', '2019-10-22', '王龙亮', '易沃思（深圳）科技有限公司', '20000', 'C20190912OCVRK2', 'G0314600047996C'],
        ['07503652', '专票', '2019-11-8', '多恒', '广州民盛互联网小额贷款有限公司', '30000', 'C20190813UD6HRS', 'G0047000096152C'],
        ['07503664,07503665', '专票', '2019-11-8', '余圣恩', '麦芒（深圳）互联网信息服务有限公司', '200000', 'C20191105DGN5Y0', 'G4004400072336C'],
        ['01362388', '普票', '2019-11-22', '多恒', '天银信息科技（广州）有限公司', '100000', 'C20181101Y1MUO3', 'G2919900229618C'],
        ['07503750', '专票', '2019-11-25', '姜立庆', '包头农村商业银行股份有限公司', '10000', 'C20191017H7O7W4', 'G7663200016038C'],
        ['07503809', '专票', '2019-12-6', '余圣恩', '万惠投资管理有限公司', '100000', 'C20181101SRJ7YR', 'C0339300019591C'],
        ['09708787', '专票', '2020-1-3', '陈伟', '人人贷商务顾问（北京）有限公司', '30000', 'C20181101QU9C0N', 'KK112700117371C'],
        ['09708791', '专票', '2020-1-7', '王龙亮', '上海骑呗信息技术有限公司', '100000', 'C20181101G8K9DA', 'G4902900079489C'],
        ['18309619', '专票', '2020-2-19', '余圣恩', '深圳市随手科技有限公司', '17213.6', 'C20181101LHVREG', 'G2071000161221C'],
        ['18309648', '专票', '2020-3-6', '陈伟', '福州分啦网络科技有限公司', '1000', 'C20191105AIT1VT', 'G0942800141759C'],
        ['18309660,18309661', '专票', '2020-3-13', '陈伟', '凤凰智信信息技术（海口）有限公司', '200000', 'C20181101D9VKRT', 'KK715900396629C'],
        ['01362430', '普票', '2020-3-17', '赵志辉', '广州市翼亮信息科技有限公司', '80000', 'C20200306OU66PG', 'KK207000188735C'],
        ['01362431', '普票', '2020-3-17', '赵志辉', '广州市翼亮信息科技有限公司', '20000', 'C20200306OU66PG', 'KK986900469617C'],
        ['18309752', '专票', '2020-3-20', '秦超', '深圳助保控股有限公司', '100000', 'C20191105DGN5Y0', 'G2071200146287C'],
        ['18309756', '专票', '2020-3-24', '秦超', '万惠投资管理有限公司', '100000', 'C20181101SRJ7YR', 'G9981400068515C'],
        ['18309778', '专票', '2020-3-27', '陈伟', '人人贷商务顾问（北京）有限公司', '40000', 'C20181101QU9C0N', 'KK064700761905C'],
        ['18309791,18309792', '专票', '2020-4-2', '陈伟', '友众信业金融信息服务（上海）有限公司', '200000', 'C202010153ZX8P0', 'G1799100008044C'],
        ['01362438', '普票', '2020-4-7', '赵志辉', '辽宁中明泰喆融资租赁有限公司', '50000', 'C202004071ZHWFR', 'G4056600180367C'],
        ['18309839,18309840,18309841', '专票', '2020-4-13', '陈伟', '凤凰智信信息技术（海口）有限公司', '300000', 'C20181101D9VKRT', 'historicalData00121'],
        ['01362444', '普票', '2020-4-16', '王龙亮', '甬汇小额贷款（东莞）有限公司', '50000', 'C20200331ST1LGX', 'KK712300067616C'],
        ['18309965,18309966,18309967,18309968,18309969,18309970', '专票', '2020-4-28', '赵志辉', '北京恒昌德盛信用管理有限公司', '600000', 'C2018110138JB60', 'G3453400033720C'],
        ['43207772', '专票', '2020-4-29', '王龙亮', '上海骑呗信息技术有限公司', '100000', 'C20181101G8K9DA', 'G8109500095483C'],
        ['43207774', '专票', '2020-4-29', '赵志辉', '北京恒诚千里征信有限公司', '100000', 'C2018110138JB60', 'G9277100253639C'],
        ['01362457', '普票', '2020-5-6', '秦超', '山东商为信息科技有限公司', '30000', 'C20200510WN0R3N', 'G6866100287395C'],
        ['01362462', '普票', '2020-5-13', '赵志辉', '广州市翼亮信息科技有限公司', '100000', 'C20200306OU66PG', 'KK228600136013C'],
        ['43207804,43207805,43207806', '专票', '2020-5-13', '陈伟', '凤凰智信信息技术（海口）有限公司', '300000', 'C20181101D9VKRT', 'KK138700203199C'],
        ['43207807,43207808', '专票', '2020-5-13', '陈伟', '凤凰智信信息技术（海口）有限公司', '200000', 'C20181101D9VKRT', 'KK715900396629C'],
        ['01362463', '普票', '2020-5-15', '王龙亮', '杭州企迅网络科技有限公司', '10000', 'C20200515SHW2BJ', 'K6114500058246C'],
        ['43207851', '专票', '2020-5-19', '陈伟', '北京极速云科技发展有限公司', '10000', 'C201903077LXWJM', 'G0926400095749C'],
        ['43207877', '专票', '2020-5-22', '王龙亮', '上海骑呗信息技术有限公司', '50000', 'C20181101G8K9DA', 'G9856600083240C'],
        ['43207982', '专票', '2020-6-16', '王龙亮', '上海骑呗信息技术有限公司', '100000', 'C20181101G8K9DA', 'G0027200291480C'],
        ['43208036', '专票', '2020-7-7', '陈伟', '北京极速云科技发展有限公司', '10000', 'C201903077LXWJM', 'G7229700014736C'],
        ['43208121', '专票', '2020-08-04', '王龙亮', '上海骑呗信息技术有限公司', '50000', 'C20181101G8K9DA', 'G8016700247636C'],
        ['43208148', '专票', '2020-08-11', '赵志辉', '苏州新焦点网络科技有限公司', '20000', 'C202008177E5P4L', 'G4244700177931C'],
        ['43208153', '专票', '2020-08-12', '王龙亮', '上海骑呗信息技术有限公司', '50000', 'C20181101G8K9DA', 'G6409600078393C'],
        ['43208298', '专票', '2020-9-16', '王龙亮', '上海骑呗信息技术有限公司', '50000', 'C20181101G8K9DA', 'G7285300108472C'],
        ['08891603', '专票', '2020-10-10', '王龙亮', '上海骑呗信息技术有限公司', '50000', 'C20181101G8K9DA', 'G9171700183888C'],
        ['08891621', '专票', '2020-10-14', '赵志辉', '苏州新焦点网络科技有限公司', '100000', 'C202008177E5P4L', 'G0560600190937C'],
        ['08891859', '专票', '2020-12-3', '陈伟', '友众信业金融信息服务（上海）有限公司', '50000', 'C202010153ZX8P0', 'G2780000059526C'],
        ['08891971', '专票', '2021-1-5', '秦超', '广州仁东互联网小额贷款有限公司', '10000', 'C20190813UD6HRS', 'G5308600032452C'],
        ['08892073', '专票', '2021-2-2', '赵志辉', '深圳市云信通达网络通信有限公司', '20000', 'C202102037PS5VL', 'KK604100785733C'],
        ['08036823', '专票', '2021-3-3', '陈伟', '海尔消费金融有限公司', '10317.6', 'C20190505USNJG6', 'G7306900010307C'],
        ['09909871', '专票', '2021-3-22', '赵志辉', '深圳众信网络科技有限公司', '20000', 'C20210318G0LOF8', 'G4784700110977C'],
        ['09909895', '专票', '2021-3-31', '陈伟', '君正智达（深圳）科技发展有限公司', '2000', 'C202009289K1B6L', 'G1999600231296C'],
        ['09909909', '专票', '2021-4-6', '陈伟', '海尔消费金融有限公司', '13398.29', 'C20190505USNJG6', 'G9657500068813C'],
        ['08157310', '专票', '2021-4-26', '王龙亮', '上海骑呗信息技术有限公司', '30000', 'C20181101G8K9DA', 'G3448500218003C'],
        ['08157311', '专票', '2021-4-26', '赵志辉', '苏州新焦点网络科技有限公司', '100000', 'C202008177E5P4L', 'G6285900203794C'],
        ['08157313', '专票', '2021-4-26', '王龙亮', '上海骑呗信息技术有限公司', '20000', 'C20181101G8K9DA', 'G5583600191107C'],
        ['08157348,08157578', '专票', '2021-5-8', '赵志辉', '北京融科君展信息技术有限公司', '37334.25', 'C20200813LX9Z2C', 'G2762400109156C'],
        ['19352540', '专票', '2021-6-21', '赵志辉', '海尔消费金融有限公司', '36593.36', 'C20190505USNJG6', 'G8752300168540C'],
        ['19352541', '专票', '2021-6-21', '赵志辉', '海尔消费金融有限公司', '31501.83', 'C20190505USNJG6', 'G8402100056051C'],
        ['19352549', '专票', '2021-6-23', '赵志辉', '深圳市云信通达网络通信有限公司', '30000', 'C202102037PS5VL', 'KK705300628803C'],
        ['19352618', '专票', '2021-7-5', '赵志辉', '海尔消费金融有限公司', '6461.2', 'C20190505USNJG6', 'G4690200050359C'],
        ['19352626', '专票', '2021-7-6', '赵志辉', '北京融科君展信息技术有限公司', '20396.75', 'C202008177E5P4L', 'G6840900090493C'],
        ['19352804', '专票', '2021-8-5', '赵志辉', '北京融科君展信息技术有限公司', '23152.25', 'C20200813LX9Z2C', 'G5667400125521C'],
        ['19352842', '专票', '2021-8-9', '赵志辉', '海尔消费金融有限公司', '5314.54', 'C20190505USNJG6', 'G6911700209532C'],
        ['14777500', '专票', '2021-9-24', '赵志辉', '海尔消费金融有限公司', '777.78', 'C20190505USNJG6', 'G2913600125250C'],
        ['14777814', '专票', '2021-11-4', '谢乔琳', '海尔消费金融有限公司', '6199.91', 'C20190505USNJG6', 'G3718800048237C'],
        ['14777815', '专票', '2021-11-4', '谢乔琳', '海尔消费金融有限公司', '7749.38', 'C20190505USNJG6', 'G3719000191315C'],
        ['11039993', '专票', '2021-12-8', '谢乔琳', '海尔消费金融有限公司', '5205.79', 'C20190505USNJG6', 'G4434200113551C'],
        ['11040102', '专票', '2021-12-27', '郭励臻', '深圳市云信通达网络通信有限公司', '22000', 'C202102037PS5VL', 'KK873600689331C'],
        ['11040261', '专票', '2022-1-7', '郭励臻', '君正智达（深圳）科技发展有限公司', '1500', 'C202009289K1B6L', 'G1829700056804C'],
        ['11040262', '专票', '2022-1-7', '谢乔琳', '海尔消费金融有限公司', '7372.67', 'C20190505USNJG6', 'G5760300147985C'],
        ['12849770', '专票', '2022-2-11', '谢乔琳', '海尔消费金融有限公司', '21112', 'C20190505USNJG6', 'G2707100137880C'],
        ['12849861', '专票', '2022-2-24', '郭励臻', '苏州新焦点网络科技有限公司', '50000', 'C202008177E5P4L', 'G4873300084207C'],
        ['12849872', '专票', '2022-2-28', '郭励臻', '苏州新焦点网络科技有限公司', '50000', 'C202008177E5P4L', 'G9172000136654C'],
        ['12849921', '专票', '2022-3-4', '谢乔琳', '海尔消费金融有限公司', '8323', 'C20190505USNJG6', 'G7964700039597C'],
        ['12850074', '专票', '2022-4-7', '谢乔琳', '海尔消费金融有限公司', '7779.83', 'C20190505USNJG6', 'G0292600017093C'],
        ['36159187', '专票', '2022-5-12', '谢乔琳', '海尔消费金融有限公司', '4424.24', 'C20190505USNJG6', 'G8552200241900C'],
        ['36159203', '专票', '2022-5-12', '郭励臻', '深圳市云信通达网络通信有限公司', '8000', 'C202102037PS5VL', 'KK924700354927C'],
        ['36159253', '专票', '2022-5-23', '秦超', '云治（杭州）互联网技术有限公司', '30000', 'C20220505VEY6KD', 'G6430500189829C'],
        ['36159452', '专票', '2022-6-10', '谢乔琳', '海尔消费金融有限公司', '3553.37', 'C20190505USNJG6', 'G0447600061961C'],
        ['00109034', '电子普票', '2022-6-10', '唐振鑫', '成都联百智荟金融服务外包有限公司', '10000', 'C2022051399FGKT', 'KK680600676999C'],
        ['00109040', '电子普票', '2022-7-5', '唐振鑫', '成都联百智荟金融服务外包有限公司', '10000', 'C2022051399FGKT', 'KK689000519543C,KK558800355606C'],
        ['36159679', '专票', '2022-7-11', '谢乔琳', '海尔消费金融有限公司', '2746.59', 'C20190505USNJG6', 'G9824300059154C'],
        ['00393857', '专票', '2022-8-8', '谢乔琳', '海尔消费金融有限公司', '3078.93', 'C20190505USNJG6', 'C0646EH000GSUCZ'],
        ['00394024', '专票', '2022-9-2', '谢乔琳', '海尔消费金融有限公司', '1057.92', 'C20190505USNJG6', 'C0646EO000J3NDZ'],
        ['14811202', '专票', '2022-9-13', '郭励臻', '北京浩旭承信科技有限公司', '5000', 'C202206295KE6JB', 'KK507000960360C'],
        ['14811452', '专票', '2022-10-13', '谢乔琳', '海尔消费金融有限公司', '1763.78', 'C20190505USNJG6', 'C0646FQ000EX0TZ'],
        ['26717432', '专票', '2022-11-2', '谢乔琳', '威海蓝海银行股份有限公司', '3877.96', 'C202301286QAWX5', 'C0646HU001M1ZWZ'],
        ['10539212', '专票', '2022-11-28', '谢乔琳', '海尔消费金融有限公司', '995.28', 'C20190505USNJG6', 'C0646GQ000ICRVZ'],
        ['10539305', '专票', '2022-12-6', '谢乔琳', '海尔消费金融有限公司', '494.16', 'C20190505USNJG6', 'C0646H7000FQZ5Z'],





        ['02792006','专票','2017-6-2','王梦思','百融（北京）金融信息服务股份有限公司','50000','C20200420EGIKGF','historicalData00011'],
        ['06993148','专票','2017-12-6','刘强','中远海运租赁有限公司','100000','C201811012A5B6W','historicalData00039'],
        ['58279253','专票','2018-12-10','赵伟','安徽省征信股份有限公司','100000','C201812177ARSF8','historicalData00245'],
        ['04251053,04251054,04251055,04251056,04251057','专票','2017-12-19','王梦思','安趣盈（上海）投资咨询有限公司','450000','C201811012TPQQ8','historicalData00045'],
        ['18309949','专票','2020-4-24','赵志辉','百融云创科技股份有限公司','50000','C20200420EGIKGF','G9855200214538C'],
        ['43208280','专票','2020-9-14','赵志辉','百融云创科技股份有限公司','50000','C20200420EGIKGF','G3358400024820C'],
        ['41504874','普票','2019-4-4','王龙亮','百维金科（上海）信息科技有限公司','50000','C20190228RN4GZ4','G4479700100386C'],
        ['43207915','专票','2020-5-28','王龙亮','百维金科（上海）信息科技有限公司','50000','C20190228RN4GZ4','G3177200218498C'],
        ['43207916','专票','2020-5-28','王龙亮','百维金科（上海）信息科技有限公司','50000','C20190228RN4GZ4','G4986200173627C'],
        ['43208037,43208038','专票','2020-7-7','王龙亮','百维金科（上海）信息科技有限公司','130581.75','C20190228RN4GZ4','G8399100125153C'],
        ['43208154,43208155','专票','2020-8-12','王龙亮','百维金科（上海）信息科技有限公司','114367.45','C20190228RN4GZ4','G1559800092825C'],
        ['43208270,43208271','专票','2020-9-10','王龙亮','百维金科（上海）信息科技有限公司','102369.6','C20190228RN4GZ4','G3355300198862C'],
        ['08891617,08891618','专票','2020-10-14','王龙亮','百维金科（上海）信息科技有限公司','102846.25','C20190228RN4GZ4','G2755600180670C'],
        ['08891748,08891749','专票','2020-11-10','王龙亮','百维金科（上海）信息科技有限公司','129437.8','C20190228RN4GZ4','G4649400319668C'],
        ['08891877,08891878','专票','2020-12-8','王龙亮','百维金科（上海）信息科技有限公司','178218.7','C20190228RN4GZ4','G3806100288783C'],
        ['02409916,02409917','专票','2018-4-20','陈伟','北京好还科技有限公司','200000','C20181101M41IR3','historicalData00085'],
        ['29967175,29967176','专票','2018-9-14','陈伟','北京好还科技有限公司','200000','C20181101M41IR3','historicalData00161'],
        ['50711754,50711755','专票','2018-11-22','陈伟','北京好还科技有限公司','200000','C20181101M41IR3','historicalData00194'],
        ['03100891','专票','2019-9-18','陈伟','北京好还科技有限公司','40000','C20181101M41IR3','G8602300226816C'],
        ['03100969,03100970','专票','2019-10-14','陈伟','北京好还科技有限公司','200000','C20181101M41IR3','G5830300043392C'],
        ['07503654','专票','2019-11-8','陈伟','北京好还科技有限公司','50000','C20181101M41IR3','G0604200024777C'],
        ['07503806','专票','2019-12-5','陈伟','北京好还科技有限公司','80000','C20181101M41IR3','G5981800058804C'],
        ['09708715,09708716,09708717,09708718,09708719,09708720,09708721,09708722,09708723,09708724','专票','2019-12-19','陈伟','北京好还科技有限公司','1000000','C20181101M41IR3','G4117700354030C'],
        ['09708786','专票','2020-1-3','陈伟','北京好还科技有限公司','80000','C20181101M41IR3','G3071300051966C'],
        ['18309600,18309601','专票','2020-2-12','陈伟','北京好还科技有限公司','150000','C20181101M41IR3','G1523500028140C'],
        ['18309779,18309780','专票','2020-3-27','陈伟','北京好还科技有限公司','200000','C20181101M41IR3','G1802700096791C'],
        ['43207789,43207790,43207791,43207792,43207793','专票','2020-5-11','陈伟','北京好还科技有限公司','500000','C20181101M41IR3','G0397500020370C'],
        ['43207905,43207906,43207907,43207908,43207909,43207910,43207911,43207912,43207913,43207914','专票','2020-5-28','陈伟','北京好还科技有限公司','1000000','C20181101M41IR3','G2977600023498C'],
        ['43208065,43208066,43208067','专票','2020-07-14','陈伟','北京好还科技有限公司','300000','C20181101M41IR3','G1097900210711C'],
        ['43208312','专票','2020-9-18','陈伟','北京好还科技有限公司','80000','C20181101M41IR3','G4119300067588C'],
        ['08891732,08891733,08891734,08891735,08891736','专票','2020-11-6','陈伟','北京好还科技有限公司','500000','C20181101M41IR3','G3652800094772C'],
        ['18309964','专票','2020-4-28','王龙亮','贝壳金科控股（北京）有限公司','65000','C202004201OFNPO','G5439100047576C'],
        ['18309824','专票','2020-4-8','赵志辉','光大永明人寿保险有限公司','1844.58','C201910188BMZEW','G3035800307340C'],
        ['43208028','专票','2020-7-7','赵志辉','光大永明人寿保险有限公司','2986.77','C201910188BMZEW','G9118400131185C'],
        ['08891626','专票','2020-10-15','赵志辉','光大永明人寿保险有限公司','6518.82','C201910188BMZEW','G6654400118140C'],
        ['13418790','专票','2019-3-29','余圣恩','广州广汽租赁有限公司','50000','C20190225VJOUQA','G9738800162149C'],
        ['08891782','专票','2020-11-18','秦超','广州广汽租赁有限公司','30000','C20190225VJOUQA','G8920300333503C'],
        ['65353206','专票','2019-1-24','李磊','广州市金诺小额贷款有限责任公司','20000','C20181229C50XT6','historicalData00278'],
        ['03101014','专票','2019-10-22','李磊','广州市网商小额贷款有限责任公司','100000','C201910247NQMT7','G0347200038617C'],
        ['04371277','专票','2019-8-26','陈伟','广州市智度互联网小额贷款有限公司','50000','C201909104C7ADM','G0166400097423C'],
        ['04371121','专票','2019-7-5','邱斌','国美小额贷款有限公司','50000','C2019070560JUMS','G0478700189268C'],
        ['08891605','专票','2020-10-12','赵志辉','国投（宁夏）互联网小额贷款股份有限公司','100000','C20200928I8C6PK','G7495800107261C'],
        ['07503707,07503708','专票','2019-11-13','邱斌','国政通科技有限公司','200000','C20191029RPKC4H','G5068500010511C'],
        ['43208035','专票','2020-7-7','赵志辉','国政通科技有限公司','100000','C20191029RPKC4H','C0520500015218C'],
        ['08891841','专票','2020-12-2','赵志辉','国政通科技有限公司','50000','C20191029RPKC4H','C0948400039123C'],
        ['03101024,03101025','专票','2019-10-28','李磊','杭银消费金融股份有限公司','122448.06','C20190801R97IIL','G2208800017411C'],
        ['07503703','专票','2019-11-12','李磊','杭银消费金融股份有限公司','104771.81','C20190801R97IIL','G2947500173062C'],
        ['07503807','专票','2019-12-6','李磊','杭银消费金融股份有限公司','61914.02','C20190801R97IIL','G7320000397853C'],
        ['18309635,18309636','专票','2020-2-27','李磊','杭银消费金融股份有限公司','82202.88','C20190801R97IIL','G5287700136213C'],
        ['43207955','专票','2020-6-9','李磊','杭银消费金融股份有限公司','269.84','C20190801R97IIL','G9567700144236C'],
        ['07503834,07503835','专票','2019-12-10','李磊','杭州蚂蚁上数信息技术有限公司','200000','C20190614WWMKLO','G2199000386065C'],
        ['18309745,18309746','专票','2020-3-20','李磊','杭州蚂蚁上数信息技术有限公司','119144.2','C20190614WWMKLO','G9889700018163C'],
        ['18309677,18309678,18309679,18309680,18309681,18309682,18309683,18309684,18309685,18309686,18309687,18309688,18309689,18309690,18309691,18309692,18309693,18309694,18309695,18309696,18309697,18309698,18309699,18309700,18309701,18309702,18309703,18309704,18309705,18309706,18309707,18309708,18309709,18309710,18309711,18309712,18309713,18309714,18309715,18309716,18309717,18309718,18309719,18309720,18309721,18309722,18309723,18309724,18309725,18309726','专票','2020-3-20','李磊','杭州蚂蚁上数信息技术有限公司','5000000','C20190614WWMKLO','G9889700018179C'],
        ['18309727,18309728,18309729,18309730,18309731,18309732,18309733,18309734,18309735,18309736,18309737,18309738,18309739,18309740,18309741,18309742,18309743,18309744','专票','2020-3-20','李磊','杭州蚂蚁上数信息技术有限公司','1800000','C20190614WWMKLO','G9889700018183C'],
        ['18309858,18309859,18309861,18309862,18309863,18309864,18309865,18309866,18309867,18309868,18309869,18309870,18309871,18309872,18309873,18309874,18309875,18309876,18309877,18309878,18309879,18309880,18309881,18309882,18309883,18309884,18309885,18309886,18309888,18309889,18309890,18309891,18309892,18309893,18309894,18309895,18309896,18309897,18309898,18309900,18309901,18309902,18309903,18309904,18309905,18309906,18309907,18309908,18309909,18309910','专票','2020-4-16','李磊','杭州蚂蚁上数信息技术有限公司','5000000','C20190614WWMKLO','G6115400007196C'],
        ['18309912,18309913,18309914,18309915,18309916,18309917,18309918,18309919,18309920,18309921,18309922,18309923,18309924','专票','2020-4-16','李磊','杭州蚂蚁上数信息技术有限公司','1230855.8','C20190614WWMKLO','G6115400007200C'],
        ['18309911,43207834,43207835,43207836,43207837,43207838,43207839,43207840,43207841,43207842,43207843,43207844,43207845,43207846,43207847,43207848,43207849,43207850','专票','2020-4-16','李磊','杭州蚂蚁上数信息技术有限公司','96818.7','C20190614WWMKLO','G6115400007290C'],
        ['43207834','专票','2020-5-19','李磊','杭州蚂蚁上数信息技术有限公司','1649982','C20190614WWMKLO','G5281400008596C'],
        ['43208081,43208089','专票','2020-07-20','王龙亮','河南中原消费金融股份有限公司','100000','C20200424SW3763','KK414700224059C'],
        ['43208090','专票','2020-07-22','王龙亮','河南中原消费金融股份有限公司','87793.01','C20200424SW3763','KK193800102992C'],
        ['43208184,43208185,43208186','专票','2020-08-19','王龙亮','河南中原消费金融股份有限公司','246815.29','C20200424SW3763','KK433100860175C'],
        ['08891570,08891571,08891572,08891573','专票','2020-9-27','王龙亮','河南中原消费金融股份有限公司','383183.77','C20200424SW3763','KK025900247223C'],
        ['08891700,08891701,08891702','专票','2020-11-3','王龙亮','河南中原消费金融股份有限公司','280192.53','C20200424SW3763','KK657000679258C'],
        ['08891903,08891904,08891905','专票','2020-12-11','王龙亮','河南中原消费金融股份有限公司','291635.43','C20200424SW3763','KK540800248707C'],
        ['08891836,08891837,08891838','专票','2020-12-2','王龙亮','河南中原消费金融股份有限公司','283601.48','C20200424SW3763','KK540900236621C'],
        ['03100912','专票','2019-9-23','赵志辉','恒智普惠（武汉）金融科技有限公司','30000','C20190923Q5G9UI','G5619800097873C'],
        ['08891914,08891915,08891916,08891917,08891918','专票','2020-12-14','赵志辉','湖南汇鑫融资担保有限公司','500000','C20181101XM4HQS','G3589200191702C'],
        ['13418725,13418726','专票','2019-3-12','李磊','联众融资租赁（上海）有限公司','200000','C20190312QBPS4K','G1431900082063C'],
        ['43208174,43208175,43208177,43208178','专票','2020-08-17','李磊','蚂蚁智信（杭州）信息技术有限公司','362295.5','C20190614WWMKLO','G5176000007520C'],
        ['43208282,43208283,43208284,43208285,43208286,43208287,43208288,43208289,43208290,43208291,43208292','专票','2020-9-15','李磊','蚂蚁智信（杭州）信息技术有限公司','1035536.69','C20190614WWMKLO','G9295800194735C'],
        ['08891592,08891593,08891594,08891595,08891596,08891597,08891598,08891599,08891600,08891601','专票','2020-10-10','李磊','蚂蚁智信（杭州）信息技术有限公司','1000000','C20190614WWMKLO','G0782800020469C'],
        ['08891602','专票','2020-10-10','李磊','蚂蚁智信（杭州）信息技术有限公司','95237.3','C20190614WWMKLO','G0782800020517C'],
        ['08891670,08891671,08891672,08891673,08891674,08891676,08891677,08891678,08891679,08891680,08891681,08891682,08891683,08891684,08891685','专票','2020-10-27','李磊','蚂蚁智信（杭州）信息技术有限公司','1497291.54','C20190614WWMKLO','G4504900007375C'],
        ['08891789,08891799,08891790,08891791,08891792,08891793,08891794,08891795,08891796,08891797','专票','2020-11-20','李磊','蚂蚁智信（杭州）信息技术有限公司','1000000','C20190614WWMKLO','G2778200223816C'],
        ['08891798,08891800,08891801','专票','2020-11-20','李磊','蚂蚁智信（杭州）信息技术有限公司','240066.85','C20190614WWMKLO','G2778200223986C'],
        ['09708735','专票','2019-12-20','王龙亮','南宁市金钱天下小额贷款有限公司','30000','C20191227JJ8XH2','KK170700531488C'],
        ['43207988','专票','2020-6-18','王龙亮','南宁市金钱天下小额贷款有限公司','30000','C20191227JJ8XH2','KK613800036681C'],
        ['18309612','专票','2020-2-19','王龙亮','南宁市金钱天下小额贷款有限公司','10000','C20191227JJ8XH2','KK100000785600C'],
        ['18309624','专票','2020-2-25','王龙亮','南宁市金钱天下小额贷款有限公司','20000','C20191227JJ8XH2','KK989400563271C'],
        ['18309663','专票','2020-3-13','王龙亮','南宁市金钱天下小额贷款有限公司','20000','C20191227JJ8XH2','KK030900234547C'],
        ['18309777','专票','2020-3-27','王龙亮','南宁市金钱天下小额贷款有限公司','30000','C20191227JJ8XH2','KK202000345853C'],
        ['08891882','专票','2020-12-8','王龙亮','南宁市金钱天下小额贷款有限公司','10000','C20191227JJ8XH2','KK656600505402C'],
        ['15137267','专票','2018-6-8','赵伟','宁波通商银行股份有限公司','100000','C20181101UQILA4','historicalData00104'],
        ['43208045','专票','2020-7-9','王龙亮','青岛汇岳信息科技有限公司','2000','C20200703MXQT1A','G9803100116331C'],
        ['08891873','专票','2020-12-8','王龙亮','青岛汇岳信息科技有限公司','1000','C20200703MXQT1A','G1823200093861C'],
        ['08891964','专票','2020-12-30','王龙亮','青岛汇岳信息科技有限公司','3000','C20200703MXQT1A','G0605600339527C'],
        ['65353213,10207546,03100902,09708696,18309669,43207952,43208213,08891811,08891872,08892135,09909880,08157473,14777375,14777376,14777705,04588261,12850269,36159697,36159698,36159699,36159700,00393794,14811187,26517238,26517239,26717465,10539281','专票','2019-1-25','秦超','厦门金美信消费金融有限公司','508307.11','C20181101983YOM','historicalData00170,C0646GX000E612Z'],
        ['58279275,58279276,58279277,58279278,58279279','专票','2018-12-13','邱斌','上海安趣盈科技有限公司','450000','C201811012TPQQ8','historicalData00229'],
        ['63033616,63033617,63033618,63033619,63033620','专票','2018-12-26','邱斌','上海安趣盈科技有限公司','500000','C201811012TPQQ8','historicalData00239'],
        ['43208084','专票','2020-07-20','王龙亮','上海分蛋信息科技有限公司','10000','C20200717CY26TG','G9299800093655C'],
        ['08891607','专票','2020-10-12','王龙亮','上海分蛋信息科技有限公司','5000','C20200717CY26TG','G9314400092011C'],
        ['40053524,40053525,40053526,40053527,40053528','专票','2018-9-17','彭馨瑶','上海数禾信息科技有限公司','500000','C20210519EJEXJO','historicalData00158'],
        ['10207527,10207528,10207529','专票','2019-6-6','李磊','上海数禾信息科技有限公司','239320','C20210519EJEXJO','G4363500109878C'],
        ['04371117,04371118,04371119','专票','2019-7-5','李磊','上海数禾信息科技有限公司','284154','C2018110104FJDR','G3133500202121C'],
        ['04371233,04371234,04371235,04371242','专票','2019-8-12','李磊','上海数禾信息科技有限公司','224755.5','C20210519EJEXJO','G9083300127918C'],
        ['03100855,03100856,03100857','专票','2019-9-5','李磊','上海数禾信息科技有限公司','217802.1','C2018110104FJDR','G6020000161307C'],
        ['03100964','专票','2019-10-11','李磊','上海数禾信息科技有限公司','47748.6','C2018110104FJDR','G9491300105551C'],
        ['08891758','专票','2020-11-12','王龙亮','上海数禾信息科技有限公司','100000','C2018110104FJDR','G2358300176787C'],
        ['04371183,04371184,04371185','专票','2019-7-25','李磊','上海维信荟智金融科技有限公司','300000','C201811018VUWII','G3161000121295C'],
        ['03101033,03101034,03101035,03101036,03101037,03101038,03101039,03101040,03101041,03101042,03101043,03101044,03101045,07503636,07503637','专票','2019-10-29','李磊','上海维信荟智金融科技有限公司','1500000','C201811018VUWII','G5149200279010C'],
        ['07503799','专票','2019-12-5','李磊','上海维信荟智金融科技有限公司','3825.68','C201811018VUWII','G8485300119164C'],
        ['18309617','专票','2020-2-19','李磊','上海维信荟智金融科技有限公司','52277.42','C201811018VUWII','G9981400039439C'],
        ['18309615','专票','2020-2-19','李磊','上海维信荟智金融科技有限公司','53358','C201811018VUWII','G8016600066268C'],
        ['18309848','专票','2020-4-14','李磊','上海维信荟智金融科技有限公司','27732.64','C201811018VUWII','G4487900347008C'],
        ['18309849','专票','2020-4-14','李磊','上海维信荟智金融科技有限公司','13129.4','C201811018VUWII','G4488000158900C'],
        ['43207787','专票','2020-5-9','李磊','上海维信荟智金融科技有限公司','16585.14','C201811018VUWII','G6313700075730C'],
        ['43207961','专票','2020-6-9','李磊','上海维信荟智金融科技有限公司','5582.69','C201811018VUWII','G8552000377186C'],
        ['43208022','专票','2020-7-3','李磊','上海维信荟智金融科技有限公司','2456.13','C201811018VUWII','G7037000091459C'],
        ['43208136','专票','2020-08-06','李磊','上海维信荟智金融科技有限公司','3988.8','C201811018VUWII','G2071300047015C'],
        ['43208261','专票','2020-9-8','李磊','上海维信荟智金融科技有限公司','5038.43','C201811018VUWII','G7040600047319C'],
        ['08891606','专票','2020-10-12','李磊','上海维信荟智金融科技有限公司','7654.19','C201811018VUWII','G6709200080334C'],
        ['08891705','专票','2020-11-4','李磊','上海维信荟智金融科技有限公司','7373.38','C201811018VUWII','G8654600078908C'],
        ['08891857','专票','2020-12-3','李磊','上海维信荟智金融科技有限公司','4057.44','C201811018VUWII','G6256900191796C'],
        ['18309818','专票','2020-4-7','李磊','上海源旅数据科技有限公司','8228.4','C202002242GM436','G9917400106044C'],
        ['43207876','专票','2020-5-21','李磊','上海源旅数据科技有限公司','20701.5','C202002242GM436','G0082600209477C'],
        ['43208117','专票','2020-08-03','李磊','上海源旅数据科技有限公司','56526.9','C202002242GM436','G7661900177551C'],
        ['43208259','专票','2020-9-8','李磊','上海源旅数据科技有限公司','31222.8','C202002242GM436','G3832200396164C'],
        ['08891591','专票','2020-10-10','李磊','上海源旅数据科技有限公司','23602.2','C202002242GM436','G9978400245962C'],
        ['08891704','专票','2020-11-4','李磊','上海源旅数据科技有限公司','10035','C202002242GM436','G7625800125478C'],
        ['08891858','专票','2020-12-3','李磊','上海源旅数据科技有限公司','15063.6','C202002242GM436','G0979400160288C'],
        ['02409913','专票','2018-4-20','王梦思','深圳萨摩耶互联网金融服务有限公司','100000','C20181101XM4HQS','historicalData00004'],
        ['40053584,40053585','专票','2018-10-22','陈伟','深圳萨摩耶互联网金融服务有限公司','200000','C20181101XM4HQS','historicalData00174'],
        ['44073511','专票','2018-10-22','陈伟','深圳萨摩耶互联网金融服务有限公司','100000','C20181101XM4HQS','historicalData00200'],
        ['04371251','专票','2019-8-16','赵志辉','深圳萨摩耶互联网金融服务有限公司','100000','C20181101XM4HQS','G6154600028152C'],
        ['03100980,03100981,03100982,03100983,03100984,03100985,03100986,03100987,03100988,03100989','专票','2019-10-15','赵志辉','深圳萨摩耶互联网金融服务有限公司','915200','C20181101XM4HQS','G0720900102302C'],
        ['43207887','专票','2020-5-25','秦超','深圳市帮客帮科技有限公司','10000','C20181210N8K0OB','G5200600160306C'],
        ['08891963','专票','2020-12-30','秦超','深圳市帮客帮科技有限公司','10000','C20181210N8K0OB','G1567900318883C'],
        ['43208151','专票','2020-08-12','秦超','深圳市飞泉云数据服务有限公司','50000','C20200807FQOQ3U','G7021500180347C'],
        ['58279261','专票','2018-12-11','余圣恩','深圳市人人聚财金融信息服务有限公司','20000','C20181210N8K0OB','historicalData00206'],
        ['03101023','专票','2019-10-24','余圣恩','深圳市人人聚财金融信息服务有限公司','10000','C20181210N8K0OB','KK361300115835C'],
        ['07503832,09708795,18309634,18309668,43207813,43208021,43208242,08891840,08892085','专票','2019-12-10','秦超','深圳市天下信息服务有限公司','208505.60','C20191115TBPAAU','G8567000798184C,KK576500803146C'],
        ['09909812','专票','2021-3-9','秦超','深圳市天下信息服务有限公司','7705','C20191116TBPAAU','KK135300680373C'],
        ['09909934','专票','2021-4-8','秦超','深圳市天下信息服务有限公司','11736.40','C20191117TBPAAU','KK037100222101C'],
        ['08157456','专票','2021-5-25','秦超','深圳市天下信息服务有限公司','11171.20','C20191118TBPAAU','KK202500475588C'],
        ['08157557','专票','2021-6-15','秦超','深圳市天下信息服务有限公司','8965.60','C20191119TBPAAU','KK893100663393C'],
        ['19352714','专票','2021-7-15','秦超','深圳市天下信息服务有限公司','18078.80','C20191120TBPAAU','KK280600085367C'],
        ['19352885','专票','2021-8-16','秦超','深圳市天下信息服务有限公司','7896','C20191121TBPAAU','KK149600434399C'],
        ['14777797','专票','2021-11-4','秦超','深圳市天下信息服务有限公司','15970.80','C20191122TBPAAU','KK253401864886C'],
        ['11040053','专票','2021-12-16','秦超','深圳市天下信息服务有限公司','5058.80','C20191123TBPAAU','G6647100240981C'],
        ['04588268','专票','2022-1-19','秦超','深圳市天下信息服务有限公司','4387.60','C20191124TBPAAU','G3324200133505C'],
        ['23260326,23260327','专票','2018-7-24','彭馨瑶','深圳市小赢普惠科技有限责任公司','200000','C20180904CEMD2W','historicalData00125'],
        ['23260328,23260329,23260330,23260331,23260332,23260333,23260334,23260335,23260336,23260337,23260338,23260339,23260340,23260341,23260342','专票','2018-7-24','彭馨瑶','深圳市小赢普惠科技有限责任公司','1500000','C20180904CEMD2W','historicalData00128'],
        ['29967177','专票','2018-9-14','彭馨瑶','深圳市小赢普惠科技有限责任公司','50000','C20180904CEMD2W','historicalData00166'],
        ['07503793,07503794','专票','2019-12-3','李磊','深圳市小赢普惠科技有限责任公司','106794','C20180904CEMD2W','G1943100315340C'],
        ['07503820,07503821,07503822,07503823,07503824,07503825,07503826,07503827,07503828','专票','2019-12-9','李磊','深圳市小赢普惠科技有限责任公司','850000','C20180904CEMD2W','G2107700226294C'],
        ['43208055,43208056','专票','2020-07-13','李磊','深圳市小赢普惠科技有限责任公司','150000','C20180904CEMD2W','G9241000122566C'],
        ['86416345','专票','2020-08-17','李磊','深圳市小赢普惠科技有限责任公司','150000','C20180904CEMD2W','G1163400171740C'],
        ['08891556,08891557','专票','2020-9-24','李磊','深圳市小赢普惠科技有限责任公司','150000','C20180904CEMD2W','G2071200345835C'],
        ['43207821,43207822','专票','2020-5-15','李磊','深圳市小赢普惠科技有限责任公司','150000','C20180904CEMD2W','G1016600156198C'],
        ['13418793','专票','2019-3-29','李芝明','四川世纪银通科技有限公司','10000','C20190401S7ASMU','G6419500137217C'],
        ['18309769','专票','2020-3-25','秦超','四川世纪银通科技有限公司','40000','C20190401S7ASMU','G1734900051153C'],
        ['40053551','专票','2018-9-28','王成功','随行付（北京）金融信息服务有限公司','100000','C201811014C5XLR','historicalData00159'],
        ['24652006','专票','2019-4-4','王成功','随行付（北京）金融信息服务有限公司','100000','C201811014C5XLR','G8450600019369C'],
        ['43207816','专票','2020-5-14','赵志辉','随行付（北京）金融信息服务有限公司','50000','C201811014C5XLR','G9277100309285C'],
        ['04371265,04371266','专票','2019-8-22','赵志辉','无锡源石云科技有限公司','200000','C201908074WYCOA','G0610800138912C'],
        ['43208197,43208198,43208199,43208200,43208201','专票','2020-8-25','赵志辉','无锡源石云科技有限公司','500000','C201908074WYCOA','G8130800386457C'],
        ['43207800','专票','2020-5-12','秦超','小花网络科技（深圳）有限公司','100000','C20200601JIIUZU','G4098800067092C'],
        ['43208272,43208273','专票','2020-9-10','秦超','小花网络科技（深圳）有限公司','102110.92','C20200601JIIUZU','G1227200068368C'],
        ['08891611','专票','2020-10-13','秦超','小花网络科技（深圳）有限公司','95136.38','C20200601JIIUZU','G2778000144600C'],
        ['08891709,08891710','专票','2020-11-5','秦超','小花网络科技（深圳）有限公司','178136.92','C20200601JIIUZU','G0612400036376C'],
        ['08891876','专票','2020-12-8','秦超','小花网络科技（深圳）有限公司','96169.92','C20200601JIIUZU','G1274900392946C'],
        ['00220681','专票','2019-5-17','陈伟','阳光财产保险股份有限公司','100000','C20190606B7M68O','G0120000058583C'],
        ['03100953','专票','2019-10-9','陈伟','阳光财产保险股份有限公司','80000','C20190606B7M68O','G5230400025912C'],
        ['07503650,07503651','专票','2019-11-8','陈伟','阳光财产保险股份有限公司','150000','C20190606B7M68O','G4540600075951C'],
        ['18309602','专票','2020-2-12','陈伟','阳光财产保险股份有限公司','43094.2','C20190606B7M68O','G8889700211141C'],
        ['18309671','专票','2020-3-17','陈伟','阳光财产保险股份有限公司','15336.73','C20190606B7M68O','G2875200052038C'],
        ['18309938','专票','2020-4-21','陈伟','阳光财产保险股份有限公司','41917.9','C20190606B7M68O','G0556500520496C'],
        ['43207810','专票','2020-5-13','陈伟','阳光财产保险股份有限公司','59379.12','C20190606B7M68O','G7726300021514C'],
        ['43207960','专票','2020-6-9','陈伟','阳光财产保险股份有限公司','15720.4','C20190606B7M68O','G0789500059454C'],
        ['43208086','专票','2020-07-21','陈伟','阳光财产保险股份有限公司','15840.4','C20190606B7M68O','G1097900230321C'],
        ['43208202','专票','2020-8-25','陈伟','阳光财产保险股份有限公司','16336','C20190606B7M68O','G9989200074634C'],
        ['08891559','专票','2020-9-24','陈伟','阳光财产保险股份有限公司','14949.2','C20190606B7M68O','G2309500066683C'],
        ['08891661','专票','2020-10-26','陈伟','阳光财产保险股份有限公司','7017.6','C20190606B7M68O','G4505600061179C'],
        ['08891786','专票','2020-11-19','陈伟','阳光财产保险股份有限公司','7142','C20190606B7M68O','G7568700016997C'],
        ['08891881','专票','2020-12-8','陈伟','阳光财产保险股份有限公司','8173.6','C20190606B7M68O','G9575500081144C'],
        ['04371137','专票','2019-7-10','余圣恩','壹融站信息技术（深圳）有限公司','50000','C20190626KC2N1U','G8523300039013C'],
        ['01362394','普票','2019-12-10','陈伟','亿保创元（北京）信息科技有限公司','10000','C20200106V6TRFZ','G7354600134617C'],
        ['29967114','专票','2018-8-17','王龙亮','浙江孚临科技有限公司','50000','C20181101IOLJH1','historicalData00138'],
        ['08891615,19352605','专票','2020-10-14','王龙亮','浙江孚临科技有限公司','30000','C20181101IOLJH1','KK641800342383C'],
        ['08891774','专票','2020-11-16','王龙亮','浙江孚临科技有限公司','37176.06','C20181101IOLJH1','KK969400348585C'],
        ['08891866','专票','2020-12-4','王龙亮','浙江孚临科技有限公司','38918.46','C20181101IOLJH1','KK562100098967C'],
        ['65353199','专票','2019-1-18','王龙亮','浙江霖梓科技有限公司','28725','C201908051UCYAT','G7235300193318C'],
        ['04371242','专票','2019-8-15','王龙亮','浙江霖梓科技有限公司','100000','C201908051UCYAT','G3164400187412C'],
        ['03100890','专票','2019-9-17','王龙亮','浙江霖梓科技有限公司','50000','C201908051UCYAT','G4831100205963C'],
        ['65353199','专票','2019-1-18','王龙亮','浙江霖梓科技有限公司',' 28725','C201908051UCYAT','G7235300193318C'],
        ['04371161','专票','2019-7-18','王龙亮','浙江霖梓科技有限公司',' 88000','C201908052UCYAT','G2294200182773C'],
        ['04371242','专票','2019-8-15','王龙亮','浙江霖梓科技有限公司',' 100000','C201908053UCYAT','G3164400187412C'],
        ['03100890','专票','2019-9-17','王龙亮','浙江霖梓科技有限公司',' 50000','C201908054UCYAT','G4831100205963C'],
        ['03100974','专票','2019-10-15','王龙亮','浙江霖梓科技有限公司',' 50000','C201908055UCYAT','G8441000188699C'],
        ['18309633','专票','2020-2-27','王龙亮','浙江霖梓科技有限公司',' 10000','C201908056UCYAT','G5049900197270C'],
        ['18309757','专票','2020-3-24','王龙亮','浙江霖梓科技有限公司',' 10000','C201908057UCYAT','G7838900150502C'],
        ['18309832','专票','2020-4-10','王龙亮','浙江霖梓科技有限公司',' 10000','C201908058UCYAT','G1544000174135C'],
        ['43207823','专票','2020-5-15','王龙亮','浙江霖梓科技有限公司',' 20000','C201908059UCYAT','G0082700220428C'],
        ['43207980','专票','2020-6-16','王龙亮','浙江霖梓科技有限公司',' 20000','C201908060UCYAT','G7953100225873C'],
        ['43208043','专票','2020-7-8','王龙亮','浙江霖梓科技有限公司',' 40000','C201908061UCYAT','G6682700314264C'],
        ['43208152','专票','2020-08-12','王龙亮','浙江霖梓科技有限公司',' 20000','C201908062UCYAT','G1922800156830C'],
        ['43208310','专票','2020-9-17','王龙亮','浙江霖梓科技有限公司',' 20000','C201908063UCYAT','G7253800371211C'],
        ['08891644','专票','2020-10-20','王龙亮','浙江霖梓科技有限公司',' 20000','C201908064UCYAT','G3600300200015C'],
        ['09909820','专票','2021-3-11','王龙亮','浙江霖梓科技有限公司',' 10000','C201908065UCYAT','G0384800216051C'],
        ['07503721','专票','2019-11-14','李磊','仲利国际租赁有限公司','100000','C20191121UO17KR','G2471200319039C'],
        ['43207968','专票','2020-6-12','王龙亮','众薪速达（上海）科技有限公司','10000','C202006160NE7RH','G3414900233901C'],
        ['04371272','专票','2019-8-23','李芝明','重庆海尔小额贷款有限公司','100000','C20200213A46X6K','G8435200175969C'],
        ['43208255','专票','2020-9-8','赵志辉','重庆美的小额贷款有限公司','10000','C20200713CMP5LK','G6972300052467C'],
        ['43208293,43208294','专票','2020-9-15','李磊','重庆万塘信息技术有限公司','144156.4','C20190614WWMKLO','G9292700054184C'],
        ['08891577,08891578','专票','2020-9-29','李磊','重庆万塘信息技术有限公司','188276.6','C20190614WWMKLO','G3971200008980C'],
        ['08891658,08891659,08891660','专票','2020-10-26','李磊','重庆万塘信息技术有限公司','266397.56','C20190614WWMKLO','G2526300018181C'],
        ['08891804','专票','2020-11-20','李磊','重庆万塘信息技术有限公司','100000','C20190614WWMKLO','G2774300016842C'],
        ['08891805','专票','2020-11-20','李磊','重庆万塘信息技术有限公司','100000','C20190614WWMKLO','G2774300016864C'],
        ['08891806','专票','2020-11-20','李磊','重庆万塘信息技术有限公司','90325.06','C20190614WWMKLO','G2774300016884C'],
        ['08891935,08891936,08891937','专票','2020-12-17','李磊','重庆万塘信息技术有限公司','257716.44','C20190614WWMKLO','G2356700020300C'],



        ['75155045','普票','2018-8-17','万耀','深圳市巴斯光年信息技术开发有限公司','100000','C20181101C5K5Y4','historicalData00139'],
        ['24652073','专票','2019-4-26','余圣恩','重庆卓凡汽车销售有限公司','30000','C20190314POHOQD','KK316600029059C'],
        ['04371277','专票','2019-8-26','陈伟','广州市智度互联网小额贷款有限公司','50000','C201909104C7ADM','G0166400097423C'],
        ['07503721','专票','2019-11-14','李磊','仲利国际租赁有限公司','100000','C20191121UO17KR','G2471200319039C'],
        ['07503750','专票','2019-11-25','姜立庆','包头农村商业银行股份有限公司','10000','C20191017H7O7W4','G7663200016038C'],
        ['01362394','普票','2019-12-10','陈伟','亿保创元（北京）信息科技有限公司','10000','C20200106V6TRFZ','G7354600134617C'],
        ['43208100','专票','2020-07-27','王龙亮',' 好东东融资租赁（云南）有限公司','10000','C202012179Z8GL8','G4738500032653C'],
        ['08891605','专票','2020-10-12','赵志辉','国投（宁夏）互联网小额贷款股份有限公司','100000','C20200928I8C6PK','G7495800107261C'],
        ['08891964','专票','2020-12-30','王龙亮','青岛汇岳信息科技有限公司','3000','C20200703MXQT1A','G0605600339527C'],
        ['09909871','专票','2021-3-22','赵志辉','深圳众信网络科技有限公司','20000','C20210318G0LOF8','G4784700110977C'],
        ['09909895','专票','2021-3-31','陈伟','君正智达（深圳）科技发展有限公司','2000','C202009289K1B6L','G1999600231296C'],
        ['08157310','专票','2021-4-26','王龙亮','上海骑呗信息技术有限公司','30000','C20181101G8K9DA','G3448500218003C'],
        ['50375817','普票','2021-5-8','赵志辉','佛山实现梦想科技有限公司','5000','C20210511H23S4S','G1346500199693C'],
        ['19352812','专票','2021-8-6','秦超','北京易拨通科技有限责任公司','50000','C202108059XQ07B','G6833500129381C'],
        ['14777718','专票','2021-10-22','秦超','广东云点科技有限公司','5000','C201910115CJOWO','G6907700162146C'],
        ['14777786','专票','2021-11-3','谢乔琳','贵州豆秒融资担保有限公司','50000','C2021031809FLG6','KK516000077916C'],
        ['14777796','专票','2021-11-4','王龙亮','上海分蛋信息科技有限公司','5000','C20200717CY26TG','G6189100417453C'],
        ['14777801','专票','2021-11-4','谢乔琳','百融云创科技股份有限公司','50000','C20200420EGIKGF','G0616300097997C'],
        ['14777852','专票','2021-11-11','谢乔琳','上海安迪泰信息技术有限公司','20000','C20210416U0B6ZQ','G7366400156041C'],
        ['11039978','专票','2021-12-7','王龙亮','众薪速达（上海）科技有限公司','30000','C202006160NE7RH','G8103300242984C'],
        ['11040004','专票','2021-12-9','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','G4434300314631C'],
        ['11040071','专票','2021-12-20','李磊','上海即科智能技术集团有限公司','100000','C20190823UTKFFH','G1121900135452C'],
        ['11040072','专票','2021-12-20','王龙亮','安徽科讯金服科技有限公司','50000','C20200807PZ9KU9','G2349700242416C'],
        ['11040077','专票','2021-12-21','谢乔琳','慧算账（深圳）商业保理有限公司','30000','C20210310TDXJ8J','G5055500101489C'],
        ['11040246','专票','2022-1-5','谢乔琳','百融云创科技股份有限公司','50000','C20200420EGIKGF','G9864400022251C'],
        ['11040283','专票','2022-1-11','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','G3324300827415C'],
        ['11040288','专票','2022-1-12','王龙亮','南宁市鼎荣小额贷款有限公司','10000','C20191227JJ8XH2','KK943500894747C'],
        ['11040397','专票','2022-1-17','谢乔琳','重庆美的小额贷款有限公司','3144.62','C20200713CMP5LK','G2884600929507C'],
        ['04588265,04588266','专票','2022-1-18','赵志辉','中国邮政速递物流股份有限公司上海市分公司','115000','C202209266W1QQZ','KK129700939164C'],
        ['04588267','专票','2022-1-18','谢乔琳','贵州豆秒融资担保有限公司','50000','C2021031809FLG6','KK247300409732C'],
        ['04588269','专票','2022-1-19','秦超','四川世纪银通科技有限公司','10000','C20190401S7ASMU','G5129200163194C'],
        ['04588276','专票','2022-1-19','秦超','广州广汽租赁有限公司','30000','C20190225VJOUQA','G2368800221222C'],
        ['04588296','专票','2022-1-21','秦超','广州九四智能科技有限公司','5000','C20211206J10EKB','G0562100111396C'],
        ['04588332','专票','2022-2-9','王龙亮','安徽科讯金服科技有限公司','40000','C20200807PZ9KU9','G3064300042732C'],
        ['12849753','专票','2022-2-10','谢乔琳','重庆美的小额贷款有限公司','4063.64','C20200713CMP5LK','G2884600929507C'],
        ['12849796','专票','2022-2-16','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','G1985400328538C'],
        ['12849808','专票','2022-2-17','王龙亮','南宁市鼎荣小额贷款有限公司','10000','C20191227JJ8XH2','KK504701151875C'],
        ['12849831','专票','2022-2-21','王龙亮','众薪速达（上海）科技有限公司','40000','C202006160NE7RH','G9338800156705C'],
        ['12849879','专票','2022-3-1','王龙亮','安徽科讯金服科技有限公司','50000','C20200807PZ9KU9','G2707100283296C'],
        ['12849930','专票','2022-3-8','谢乔琳','重庆美的小额贷款有限公司','2557.11','C20200713CMP5LK','G2884600929507C'],
        ['12849950','专票','2022-3-11','唐振鑫','广州啄木鸟数字科技有限公司','10000','C20220307QD4TBU','G5011800194567C'],
        ['12849960','专票','2022-3-14','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','G7964800160523C'],
        ['12850012','专票','2022-3-23','谢乔琳','慧算账（深圳）商业保理有限公司','20000','C20210310TDXJ8J','G2834300114109C'],
        ['12850028','专票','2022-3-28','王龙亮','安徽科讯金服科技有限公司','50000','C20200807PZ9KU9','G2834500189220C'],
        ['12850038,12850039,12850040,12850041','专票','2022-3-29','谢乔琳','湖南汇鑫融资担保有限公司','350000','C20181101XM4HQS','K8744300116116C'],
        ['12850058','专票','2022-4-1','谢乔琳','上海安迪泰信息技术有限公司','20000','C20210416U0B6ZQ','G5786400188187C'],
        ['12850083','专票','2022-4-7','郭励臻','北京音悦邦信息服务有限公司','5000','C20220406C6RWYH','K0864100174898C'],
        ['12850206','专票','2022-4-12','谢乔琳','重庆美的小额贷款有限公司','234.63','C20200713CMP5LK','G2884600929507C'],
        ['12850207','专票','2022-4-12','谢乔琳','重庆美的小额贷款有限公司','5224.3','C20200713CMP5LK','G4577100164298C'],
        ['12850208','专票','2022-4-12','谢乔琳','贵州豆秒融资担保有限公司','50000','C2021031809FLG6','KK590101089348C'],
        ['12850254','专票','2022-4-18','王龙亮','南宁市鼎荣小额贷款有限公司','10000','C20191227JJ8XH2','KK183701434648C'],
        ['12850297','专票','2022-4-25','王龙亮','安徽科讯金服科技有限公司','50000','C20200807PZ9KU9','G5073700166189C'],
        ['33624208','专票','2022-5-9','王龙亮','湖北消费金融股份有限公司','66000','C20220926PCYLU4','K2229300198601C'],
        ['36159203','专票','2022-5-12','郭励臻','深圳市云信通达网络通信有限公司','5000','C202102037PS5VL','KK924700354927C'],
        ['36159210,36159497','专票','2022-5-12','谢乔琳','南昌随行付网络小额贷款有限公司','40000','C201811014C5XLR','G6427100214191C'],
        ['36159222','专票','2022-5-18','谢乔琳','重庆美的小额贷款有限公司','4285.27','C20200713CMP5LK','G4577100164298C'],
        ['36159253','专票','2022-5-23','秦超','云治（杭州）互联网技术有限公司','30000','C20220505VEY6KD','G6430500189829C'],
        ['36159309','专票','2022-5-31','王龙亮','南宁市鼎荣小额贷款有限公司','10000','C20191227JJ8XH2','KK791100715932C'],
        ['36159310','专票','2022-5-31','谢乔琳','百融云创科技股份有限公司','100000','C20200420EGIKGF','K1355800123004C'],
        ['36159422','专票','2022-6-7','王龙亮','安徽科讯金服科技有限公司','50000','C20200807PZ9KU9','G4724200187322C'],
        ['36159424','专票','2022-6-7','唐振鑫','广州广汽租赁有限公司','30000','C20190225VJOUQA','G3642200149377C'],
        ['00109034','电子普票','2022-6-10','唐振鑫','成都联百智荟金融服务外包有限公司','5000','C2022051399FGKT','KK558800355606C'],
        ['36159455','专票','2022-6-10','唐振鑫','四川世纪银通科技有限公司','10000','C20190401S7ASMU','G2884801207225C'],
        ['36159495','专票','2022-6-16','谢乔琳','重庆美的小额贷款有限公司','4428.24','C20200713CMP5LK','G4577100164298C'],
        ['36159565','专票','2022-6-24','郭励臻','北京音悦邦信息服务有限公司','10000','C20220406C6RWYH','K6598800143513C'],
        ['36159630','专票','2022-7-5','李磊','南京铭智信息科技有限公司','3000','C20220531ZO08RQ','KK200200381053C'],
        ['36159657','专票','2022-7-6','李磊','南京铭智信息科技有限公司','10000','C20220531ZO08RQ','KK200900760028C'],
        ['00109040','电子普票','2022-7-5','唐振鑫','成都联百智荟金融服务外包有限公司','10000','C2022051399FGKT','KK680600676999C'],
        ['36159681','专票','2022-7-11','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','G8701800346952C'],
        ['36159708','专票','2022-7-12','谢乔琳','重庆美的小额贷款有限公司','5672.4','C20200713CMP5LK','G4577100164298C'],
        ['02230044','专票','2022-7-20','王龙亮','众薪速达（上海）科技有限公司','20000','C202006160NE7RH','G7935000045996C'],
        ['02230080','专票','2022-7-26','谢乔琳','百融云创科技股份有限公司','100000','C20200420EGIKGF','K4274800174877C'],
        ['00393853','专票','2022-8-8','王龙亮','安徽科讯金服科技有限公司','60000','C20200807PZ9KU9','G9261800309403C'],
        ['00393855','专票','2022-8-8','谢乔琳','贵州豆秒融资担保有限公司','50000','C2021031809FLG6','KK445100544438C'],
        ['00393870','专票','2022-8-10','唐振鑫','广州啄木鸟数字科技有限公司','10000','C20220307QD4TBU','G5713500304971C'],
        ['00393889','专票','2022-8-11','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','G7629600103357C'],
        ['00393901','专票','2022-8-15','李磊','广州市网商小额贷款有限责任公司','100000','C201910247NQMT7','G0986300171092C'],
        ['00393915','专票','2022-8-17','谢乔琳','重庆美的小额贷款有限公司','389.79','C20200713CMP5LK','G4577100164298C'],
        ['00393952','专票','2022-8-23','唐振鑫','广东云点科技有限公司','20000','C201910115CJOWO','G6956300130186C'],
        ['00393985','专票','2022-8-29','谢乔琳','百融云创科技股份有限公司','100000','C20200420EGIKGF','C0646EO000PRXMZ'],
        ['50376119','普票','2022-9-7','郭励臻','佛山实现梦想科技有限公司','5000','C20210511H23S4S','G8387300033456C'],
        ['14811184','专票','2022-9-8','唐振鑫','广东云点科技有限公司','20000','C201910115CJOWO','G9760900070250C'],
        ['14811201','专票','2022-9-13','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','C0646EQ000P7A1Z'],
        ['14811278','专票','2022-9-16','李磊','南京铭智信息科技有限公司','10000','C20220531ZO08RQ','KK075901058431C'],
        ['14811279','专票','2022-9-16','李磊','南京铭智信息科技有限公司','10000','C20220531ZO08RQ','KK451000774723C'],
        ['14811284','专票','2022-9-16','王龙亮','南宁市鼎荣小额贷款有限公司','10000','C20191227JJ8XH2','KK532501233458C'],
        ['14811286','专票','2022-9-16','谢乔琳','重庆美的小额贷款有限公司','4621.97','C20200713CMP5LK','G0123600224137C'],
        ['14811315','专票','2022-9-21','唐振鑫','四川世纪银通科技有限公司','15000','C20190401S7ASMU','G2036700231138C'],
        ['14811324','专票','2022-9-21','唐振鑫','四川世纪银通科技有限公司','15000','C20190401S7ASMU','KK532601265238C'],
        ['14811363','专票','2022-9-26','唐振鑫','四川世纪银通科技有限公司','10000','C20190401S7ASMU','KK708800445047C'],
        ['14811379','专票','2022-9-28','谢乔琳','百融云创科技股份有限公司','100000','C20200420EGIKGF','G6577700055132C'],
        ['14811471','专票','2022-10-14','谢乔琳','南昌随行付网络小额贷款有限公司','20000','C201811014C5XLR','C0646FI000QE9JZ'],
        ['26517246','专票','2022-10-17','王龙亮','安徽科讯金服科技有限公司','60000','C20200807PZ9KU9','C0646ES000LFEAZ'],
        ['26717302','专票','2022-10-19','王龙亮','南宁市鼎荣小额贷款有限公司','10000','C20191227JJ8XH2','KK012100351870C'],
        ['26717373','专票','2022-10-25','谢乔琳','重庆美的小额贷款有限公司','3902.78','C20200713CMP5LK','G0123600224137C'],
        ['10539214','专票','2022-11-28','王龙亮','安徽科讯金服科技有限公司','60000','C20200807PZ9KU9','C0646G9000QZIUZ'],
        ['10539227','专票','2022-11-28','谢乔琳','百融云创科技股份有限公司','100000','C20200420EGIKGF','C0646GC000TAUGZ'],
        ['10539548','专票','2022-12-19','谢乔琳','重庆美的小额贷款有限公司','4574.43','C20200713CMP5LK','G0123600224137C'],
        ['10539549','专票','2022-12-19','谢乔琳','重庆美的小额贷款有限公司','1340.1','C20200713CMP5LK','G0123600224137C'],
        ['10539734','专票','2023-1-9','唐振鑫','广东云点科技有限公司','10000','C201910115CJOWO','C0646G1000RJTZZ'],
    ];

    /** @var array 需要生成消耗的客户 */
    private $need_add_consume_data_customers = [
        'C20180831BZ7V8N',
        'C20180903PZEF3D',
        'C20180904CEMD2W',
        'C20181016J5QX55',
        'C201810196KCVX0',
        'C2018110104FJDR',
        'C2018110109LGJV',
        'C201811010OWLVK',
        'C201811010TFXLG',
        'C201811010WPSW7',
        'C20181101107M4G',
        'C201811012A06ZS',
        'C201811012A5B6W',
        'C201811012H5ERU',
        'C201811012QMNCH',
        'C201811012TPQQ8',
        'C2018110130YKSX',
        'C2018110138JB60',
        'C20181101390VRS',
        'C2018110139A8VG',
        'C201811014C10XD',
        'C201811014C5XLR',
        'C201811014JOQPE',
        'C20181101548QVP',
        'C201811015PKP5M',
        'C201811015ZS131',
        'C201811016LKRUP',
        'C201811016QTZSB',
        'C201811018VUWII',
        'C20181101983YOM',
        'C201811019FAXD9',
        'C20181101A4313G',
        'C20181101A9XUFO',
        'C20181101BNWR79',
        'C20181101C5K5Y4',
        'C20181101CCE390',
        'C20181101CKS5WI',
        'C20181101CTH7AF',
        'C20181101CWTTBO',
        'C20181101D9VKRT',
        'C20181101F6RRLK',
        'C20181101FSHIF9',
        'C20181101FV5032',
        'C20181101G5LNRB',
        'C20181101G8K9DA',
        'C20181101IOLJH1',
        'C20181101JEGLAL',
        'C20181101JI6H4L',
        'C20181101KXPRKM',
        'C20181101L888MM',
        'C20181101LHVREG',
        'C20181101M41IR3',
        'C20181101MGKK4J',
        'C20181101N2RO9B',
        'C20181101OT2IDD',
        'C20181101OU3082',
        'C20181101PBHX9M',
        'C20181101QBHHO0',
        'C20181101QF0SXN',
        'C20181101QF2Z1U',
        'C20181101QU9C0N',
        'C20181101QZFBJQ',
        'C20181101R4Y8BO',
        'C20181101RE6GD8',
        'C20181101RF4UG7',
        'C20181101SRJ7YR',
        'C20181101T548OF',
        'C20181101UB91OT',
        'C20181101UDRGMY',
        'C20181101UQILA4',
        'C20181101UYXHHX',
        'C20181101VVPH6G',
        'C20181101X507LE',
        'C20181101XM4HQS',
        'C20181101Y1MUO3',
        'C20181101YPB9L7',
        'C20181101ZHT6LR',
        'C20181101ZQMALK',
        'C20181112S6CBPQ',
        'C2018112007WOWU',
        'C201811217JE09X',
        'C201811272J8H1X',
        'C20181127HMJJS7',
        'C20181127JO35TD',
        'C20181210N8K0OB',
        'C20181210T25RSP',
        'C2018121479DEIP',
        'C201812177ARSF8',
        'C20181217LBUPPX',
        'C201812184QDOWP',
        'C20181219I74RET',
        'C20181219TSASI7',
        'C20181224443PZ3',
        'C20181224RPTY3Q',
        'C20181228DKFJWH',
        'C20181228WS1IGI',
        'C20181229C50XT6',
        'C20190103UFJ66U',
        'C20190110II8XQA',
        'C20190115SQH1M5',
        'C20190116SIMKBZ',
        'C201901294TNIOR',
        'C20190129IH5TCV',
        'C20190129VJZO2O',
        'C20190212LTI4GE',
        'C20190215QBR25V',
        'C20190218KTCQLR',
        'C20190219C2Q7CP',
        'C201902251G3O3Q',
        'C20190225VJOUQA',
        'C201902288QNIU5',
        'C20190228RN4GZ4',
        'C201903077LXWJM',
        'C20190307XZ2NLF',
        'C201903080LBBSL',
        'C20190308HD6R3D',
        'C20190312QBPS4K',
        'C20190314POHOQD',
        'C2019032668WHR7',
        'C20190401S7ASMU',
        'C20190417PX262B',
        'C20190505USNJG6',
        'C20190510U99AUS',
        'C20190514N5IQ0H',
        'C20190514Y00G9R',
        'C20190529S9OYIW',
        'C20190531MGLE27',
        'C201906033JMXND',
        'C20190606B7M68O',
        'C201906103U0CGV',
        'C20190614WWMKLO',
        'C20190626KC2N1U',
        'C2019062730ZU96',
        'C20190701UMV2GR',
        'C2019070560JUMS',
        'C20190716AIU5C7',
        'C20190801R97IIL',
        'C201908051UCYAT',
        'C201908052UCYAT',
        'C201908053UCYAT',
        'C201908054UCYAT',
        'C201908055UCYAT',
        'C201908056UCYAT',
        'C201908057UCYAT',
        'C201908058UCYAT',
        'C201908059UCYAT',
        'C201908060UCYAT',
        'C201908061UCYAT',
        'C201908062UCYAT',
        'C201908063UCYAT',
        'C201908064UCYAT',
        'C201908065UCYAT',
        'C201908074WYCOA',
        'C20190808JRW00J',
        'C20190813UD6HRS',
        'C201908141OQUCU',
        'C201909104C7ADM',
        'C20190912OCVRK2',
        'C20190923Q5G9UI',
        'C20190923ZA9710',
        'C20191017H7O7W4',
        'C201910188BMZEW',
        'C201910247NQMT7',
        'C20191029RPKC4H',
        'C20191105AIT1VT',
        'C20191105DGN5Y0',
        'C20191115TBPAAU',
        'C20191116TBPAAU',
        'C20191117TBPAAU',
        'C20191118TBPAAU',
        'C20191119TBPAAU',
        'C20191120TBPAAU',
        'C20191121TBPAAU',
        'C20191121UO17KR',
        'C20191122TBPAAU',
        'C20191123TBPAAU',
        'C20191124TBPAAU',
        'C20191227JJ8XH2',
        'C20200106V6TRFZ',
        'C20200213A46X6K',
        'C202002242GM436',
        'C20200306OU66PG',
        'C20200323VLZPO1',
        'C20200331ST1LGX',
        'C202004071ZHWFR',
        'C202004201OFNPO',
        'C20200420EGIKGF',
        'C20200424SW3763',
        'C20200510WN0R3N',
        'C20200515SHW2BJ',
        'C20200601JIIUZU',
        'C202006160NE7RH',
        'C20200703MXQT1A',
        'C20200713CMP5LK',
        'C20200717CY26TG',
        'C20200807FQOQ3U',
        'C20200813LX9Z2C',
        'C202008177E5P4L',
        'C202009289K1B6L',
        'C20200928I8C6PK',
        'C202010153ZX8P0',
        'C202102037PS5VL',
        'C20210318G0LOF8',
        'C20210519EJEXJO',
        'C20220505VEY6KD',
        'C2022051399FGKT',
        'C202206295KE6JB',
        'C202301286QAWX5',
    ];

    /** @var array 需要生成消耗的月份 */
    private $need_add_cnsume_month = [
        201806,
        201807,
        201808,
        201809,
        201810,
        201811,
        201812,
        201901,
        201902,
        201903,
        201904,
        201905,
        201906,
        201907,
        201908,
        201909,
        201910,
        201911,
        201912,
        202001,
        202002,
        202003,
        202004,
        202005,
        202006,
        202007,
        202008,
        202009,
        202010,
        202011,
        202012,
        202101,
        202102,
        202103,
        202104,
        202105,
        202106,
        202107,
        202108,
        202109,
        202110,
        202111,
        202112
    ];


    /** @var array 脚本执行前需要执行的sql */
    private $need_exec_sqls_before_script = [
        'update' => [
            "update customer_invoice_config set model = 1 where id = 431;",
            "update remit_split_price set delete_time = ********** where id in (22042,22044,22043);",
            "update remit_split_price set delete_time = ********** where receipt_serial in ('historicalData00032','G0309200184351C','KK112700117371C','C0646HU001M1ZWZ');",
            "update remit_split_price set delete_time = ********** where id in (25533);",
            "update remit_split_price set money = 16232.47 where id = 25347;",
            "update remit_balance set remit_balance = 0 where id = 2823;",
            "update receipt set status = 3 where id = 423;",
            "update receipt set status = 3 where id = 705;",
        ],
        'insert' => [
            "insert into remit (customer_id, receipt_serial, remit_serial, name, money, bank, remit_date,contract_no, proof_image, create_time, admin, status, remark, source,parent_serial, invoice_status, remit_unconsumed, remit_balance) values ('C201908051UCYAT', 'G2294200182773C', 'G2294200182773C', '浙江未讯科技有限公司', 88000,'上海浦东发展银行股份有限公司杭州分行营业部', '**********', '', '', **********, 'admin', 3, '', 0, '', 000, '', '');",
            "insert into remit (customer_id, receipt_serial, remit_serial, name, money, bank, remit_date, contract_no, proof_image, create_time, admin, status, remark, source, parent_serial, invoice_status, remit_unconsumed, remit_balance) values ('C201908051UCYAT', 'G6682700314264C', 'G6682700314264C', '浙江未讯科技有限公司', 20000, '上海浦东发展银行股份有限公司杭州分行营业部', '**********', '', '', **********, 'admin', 3, '', 0, '', 000, '','');",
        ],
        'delete' => [
            "delete from remit_split_price where admin = 'auto'  and delete_time = 0  and receipt_serial in ('historicalData00132', 'historicalData00018', 'historicalData00019', 'historicalData00026', 'historicalData00023', 'historicalData00032', 'historicalData00038', 'historicalData00024', 'historicalData00027', 'historicalData00030', 'historicalData00037', 'historicalData00035', 'historicalData00054', 'historicalData00049', 'historicalData00055', 'historicalData00052', 'historicalData00060', 'historicalData00043', 'historicalData00056', 'historicalData00058', 'historicalData00064', 'historicalData00065', 'historicalData00061', 'historicalData00063', 'historicalData00073', 'historicalData00074', 'historicalData00076', 'historicalData00066', 'historicalData00069', 'historicalData00072', 'historicalData00077', 'historicalData00075', 'historicalData00080', 'historicalData00081', 'historicalData00083', 'historicalData00086', 'historicalData00044', 'historicalData00092', 'historicalData00098', 'historicalData00106', 'historicalData00096', 'historicalData00097', 'historicalData00108', 'historicalData00110', 'historicalData00116', 'historicalData00115', 'historicalData00109', 'historicalData00118', 'historicalData00122', 'historicalData00121', 'historicalData00117', 'historicalData00114', 'historicalData00140', 'historicalData00123', 'historicalData00107', 'historicalData00139', 'historicalData00142', 'historicalData00146', 'historicalData00162', 'historicalData00150', 'historicalData00163', 'historicalData00149', 'historicalData00153', 'historicalData00154', 'historicalData00155', 'historicalData00112', 'historicalData00160', 'historicalData00171', 'historicalData00164', 'historicalData00201', 'historicalData00191', 'historicalData00188', 'historicalData00181', 'historicalData00167', 'historicalData00187', 'historicalData00184', 'historicalData00179', 'historicalData00210', 'historicalData00190', 'historicalData00172', 'historicalData00183', 'historicalData00199', 'historicalData00202', 'historicalData00204', 'historicalData00195', 'historicalData00242', 'historicalData00220', 'historicalData00185', 'historicalData00208', 'historicalData00234', 'historicalData00263', 'historicalData00228', 'historicalData00241', 'historicalData00214', 'historicalData00246', 'historicalData00231', 'historicalData00216', 'historicalData00219', 'historicalData00078', 'historicalData00235', 'historicalData00222', 'historicalData00226', 'historicalData00236', 'historicalData00244', 'historicalData00248', 'historicalData00227', 'historicalData00253', 'historicalData00247', 'historicalData00240', 'historicalData00250', 'historicalData00264', 'historicalData00256', 'historicalData00268', 'historicalData00266', 'historicalData00262', 'historicalData00270', 'historicalData00269', 'historicalData00277', 'historicalData00271', 'G7799700123989C', 'G9738900103164C', 'historicalData00275', 'historicalData00276', 'historicalData00280', 'historicalData00267', 'historicalData00288', 'historicalData00282', 'G0932400039742C', 'historicalData00283', 'historicalData00287', 'G3624500088617C', 'G1811700037486C', 'G5713500059084C', 'G4518000014230C', 'G0528800039271C', 'G5706000144455C', 'G0206600197441C', 'KK316600029059C', 'G3678500206380C', 'G0206600173741C', 'G5705800022530C', 'G4915900264364C', 'G4915800014638C', 'KK027000255981C', 'G4359100167397C', 'G5532300356059C', 'G8470200079349C', 'historicalData00111', 'G0849200083584C', 'historicalData00091', 'G4248600042881C', 'G2195800102418C', 'G9805000233176C', 'G4369000164245C', 'G6057300115677C', 'G0159500044706C', 'G3195500174260C', 'G2195800057520C', 'G3532400053322C', 'G7705500078077C', 'G8454800058128C', 'G8270500114919C', 'G6940500685885C', 'G4835700181848C', 'G9731900195461C', 'G6762400129072C', 'G6299000025293C', 'G7512800030757C', 'G8513800059263C', 'historicalData00258', 'G6802700077554C', 'historicalData00284', 'G0411700068527C', 'G9732100014214C', 'KK793200031137C', 'G2671300565593C', 'G8996600017710C', 'G0309200128342C', 'G6508100164655C', 'G0124600720980C', 'G0309200184351C', 'G5278500022229C', 'KK547800286141C', 'G9064200193139C', 'G4385800036810C', 'G9290300074681C', 'G9404700116719C', 'G7841600032842C', 'G5651800042351C', 'G0166400097423C', 'G2680900066512C', 'G3191900044582C', 'G8914000070449C', 'G2885800153011C', 'G5782600034962C', 'G8607500152952C', 'G0314600047996C', 'G0047000096152C', 'G4004400072336C', 'G2919900229618C', 'G7663200016038C', 'C0339300019591C', 'KK112700117371C', 'G4902900079489C', 'G2071000161221C', 'G0942800141759C', 'KK715900396629C', 'KK207000188735C', 'KK986900469617C', 'G2071200146287C', 'G9981400068515C', 'KK064700761905C', 'G1799100008044C', 'G4056600180367C', 'KK712300067616C', 'G3453400033720C', 'G8109500095483C', 'G9277100253639C', 'G6866100287395C', 'KK228600136013C', 'KK138700203199C', 'K6114500058246C', 'G0926400095749C', 'G9856600083240C', 'G0027200291480C', 'G7229700014736C', 'G8016700247636C', 'G6409600078393C', 'G7285300108472C', 'G9171700183888C', 'G2780000059526C', 'G5308600032452C', 'KK604100785733C', 'G7306900010307C', 'G4784700110977C', 'G1999600231296C', 'G9657500068813C', 'G3448500218003C', 'G5583600191107C', 'G2762400109156C', 'G8752300168540C', 'G8402100056051C', 'KK705300628803C', 'G4690200050359C', 'G5667400125521C', 'G6911700209532C', 'G2913600125250C', 'G3718800048237C', 'G3719000191315C', 'G4434200113551C', 'KK873600689331C', 'G1829700056804C', 'KK924700354927C', 'G6430500189829C', 'KK680600676999C', 'KK689000519543C', 'KK558800355606C', 'historicalData00011', 'historicalData00039', 'historicalData00045', 'G9855200214538C', 'G3358400024820C', 'G4479700100386C', 'G3177200218498C', 'G4986200173627C', 'G8399100125153C', 'G1559800092825C', 'G3355300198862C', 'G2755600180670C', 'G4649400319668C', 'G3806100288783C', 'historicalData00085', 'historicalData00161', 'historicalData00194', 'G8602300226816C', 'G5830300043392C', 'G0604200024777C', 'G5981800058804C', 'G4117700354030C', 'G3071300051966C', 'G1523500028140C', 'G1802700096791C', 'G0397500020370C', 'G2977600023498C', 'G1097900210711C', 'G4119300067588C', 'G3652800094772C', 'G3035800307340C', 'G9118400131185C', 'G6654400118140C', 'G9738800162149C', 'G8920300333503C', 'G0347200038617C', 'G0478700189268C', 'G7495800107261C', 'G5068500010511C', 'C0520500015218C', 'C0948400039123C', 'G2208800017411C', 'G2947500173062C', 'G7320000397853C', 'G5287700136213C', 'G9567700144236C', 'G2199000386065C', 'G9889700018163C', 'G9889700018179C', 'G9889700018183C', 'G6115400007196C', 'G6115400007200C', 'G6115400007290C', 'G5281400008596C', 'KK414700224059C', 'KK193800102992C', 'KK433100860175C', 'KK025900247223C', 'KK657000679258C', 'KK540800248707C', 'KK540900236621C', 'G5619800097873C', 'G3589200191702C', 'G5176000007520C', 'G9295800194735C', 'G0782800020469C', 'G0782800020517C', 'G4504900007375C', 'G2778200223816C', 'G2778200223986C', 'KK170700531488C', 'KK613800036681C', 'KK100000785600C', 'KK989400563271C', 'KK030900234547C', 'KK202000345853C', 'KK656600505402C', 'historicalData00104', 'G9803100116331C', 'G1823200093861C', 'G0605600339527C', 'historicalData00170', 'G9299800093655C', 'G9314400092011C', 'historicalData00158', 'G4363500109878C', 'G3133500202121C', 'G9083300127918C', 'G6020000161307C', 'G9491300105551C', 'G2358300176787C', 'G3161000121295C', 'G5149200279010C', 'G8485300119164C', 'G9981400039439C', 'G8016600066268C', 'G4487900347008C', 'G4488000158900C', 'G6313700075730C', 'G8552000377186C', 'G7037000091459C', 'G2071300047015C', 'G7040600047319C', 'G6709200080334C', 'G8654600078908C', 'G6256900191796C', 'G9917400106044C', 'G0082600209477C', 'G7661900177551C', 'G3832200396164C', 'G9978400245962C', 'G7625800125478C', 'G0979400160288C', 'historicalData00004', 'historicalData00174', 'historicalData00200', 'G6154600028152C', 'G0720900102302C', 'G5200600160306C', 'G1567900318883C', 'G7021500180347C', 'historicalData00206', 'KK361300115835C', 'G8567000798184C', 'KK576500803146C', 'KK135300680373C', 'KK037100222101C', 'KK202500475588C', 'KK893100663393C', 'KK280600085367C', 'KK149600434399C', 'KK253401864886C', 'G6647100240981C', 'historicalData00125', 'historicalData00128', 'historicalData00166', 'G1943100315340C', 'G2107700226294C', 'G9241000122566C', 'G1163400171740C', 'G2071200345835C', 'G1016600156198C', 'G6419500137217C', 'G1734900051153C', 'historicalData00159', 'G8450600019369C', 'G9277100309285C', 'G0610800138912C', 'G8130800386457C', 'G4098800067092C', 'G1227200068368C', 'G2778000144600C', 'G0612400036376C', 'G1274900392946C', 'G0120000058583C', 'G5230400025912C', 'G4540600075951C', 'G8889700211141C', 'G2875200052038C', 'G0556500520496C', 'G7726300021514C', 'G0789500059454C', 'G1097900230321C', 'G9989200074634C', 'G2309500066683C', 'G4505600061179C', 'G7568700016997C', 'G9575500081144C', 'G8523300039013C', 'G7354600134617C', 'historicalData00138', 'KK641800342383C', 'KK969400348585C', 'KK562100098967C', 'G7235300193318C', 'G3164400187412C', 'G4831100205963C', 'G2294200182773C', 'G8441000188699C', 'G5049900197270C', 'G7838900150502C', 'G1544000174135C', 'G0082700220428C', 'G7953100225873C', 'G6682700314264C', 'G1922800156830C', 'G7253800371211C', 'G3600300200015C', 'G0384800216051C', 'G2471200319039C', 'G3414900233901C', 'G6972300052467C', 'G9292700054184C', 'G3971200008980C', 'G2526300018181C', 'G2774300016842C', 'G2774300016864C', 'G2774300016884C', 'G2356700020300C');",
        ],
    ];

    /** @var array 脚本执行后需要执行的sql */
    private $need_exec_sqls_after_script = [
        'update' => [
            "update customer_invoice_config set model = 4 where id = 431;",
        ],
        'insert' => [],
        'delete' => [],
    ];


    private function exec_sql($sqls) {
        foreach ($sqls['update'] as $sql) {
            echo 'run sql :',$sql,PHP_EOL;
            DB::update($sql);
        }
        foreach ($sqls['insert'] as $sql) {
            echo 'run sql :',$sql,PHP_EOL;
            DB::insert($sql);
        }
        foreach ($sqls['delete'] as $sql) {
            echo 'run sql :',$sql,PHP_EOL;
            DB::delete($sql);
        }
    }

    /**
     * @throws Exception
     */
    public function handle() {
        //设置参数
        try {
            $this->output->success("【{$this->description}】 开始");

            $this->fix();//修复

            $this->output->success("【{$this->description}】 完成");
        } catch (Exception $e) {
            throw new Exception($e->getFile().":".$e->getLine().PHP_EOL.PHP_EOL.$e->getMessage());
        }
    }

    /**
     * 修复
     *
     * @throws Exception
     */
    private function fix(){
        try {
            DB::beginTransaction();//开启事务

            //执行sql
            $this->exec_sql($this->need_exec_sqls_before_script);

            $need_add_consume_customer_ids = implode(',', array_unique($this->need_add_consume_data_customers));
            //生成customer_consume
            foreach($this->need_add_cnsume_month as $month) {
                echo 'run command php artisan invoice:generate_month_consume --csae 1 --month '.$month.' --source 0 -- customer_id '.$need_add_consume_customer_ids.PHP_EOL;
                Artisan::call('invoice:generate_month_consume', [
                    '--case'        => 1,
                    '--month'       => $month,
                    '--customer_id' => $need_add_consume_customer_ids,
                    '--source'      => 0
                ]);
            }



            $source = 0;

            $customer_ids       = [];
            $receipt_serial_arr = [];
            $reparied_arr       = [];
            foreach($this->data_to_be_repaired as $repaired_info) {
                $_customer_id = $repaired_info[6];
                $_receipt_arr = explode(',',$repaired_info[7]);
                $customer_ids[$_customer_id] = $_customer_id;
                $receipt_serial_arr = array_unique(array_merge($receipt_serial_arr,$_receipt_arr));

                // echo $repaired_info[0],"--------",$repaired_info[5],PHP_EOL;
                // echo $repaired_info[5]%1000,PHP_EOL;

                $_info = [];
                $_info['invoice_no'] = explode(',',$repaired_info[0]);
                $_info['invoice_type'] = $this->invoice_type_map[$repaired_info[1]];
                $_info['invoice_data'] = date("Y-m-d H:i:s",strtotime($repaired_info[2]));
                $_info['salesman'] = $this->user_name_map[$repaired_info[3]];
                $_info['company_name'] = $repaired_info[4];
                $_info['money'] = $repaired_info[5];
                $_info['customer_id'] = $_customer_id;
                $_info['receipt_serial'] = $_receipt_arr;
                $_info['invoice_model'] = $repaired_info[5]%1000 == 0 ? Common::INVOICE_MODEL_5 : Common::INVOICE_MODEL_1;
                $_info['first_split_month'] = $this->first_split_month($_customer_id);

                $reparied_arr[] = $_info;
            }

            $customer_list = Customer::getCustomerListByCustomerIds(['customer_id','name','company'], $customer_ids);
            $receipt_serial_list = Remit::getListByReceiptSerial($receipt_serial_arr);
            $receipt_serial_list = array_column($receipt_serial_list,null,'receipt_serial');
            $splist_list = RemitSplitPrice::getListWithReceiptSerials($receipt_serial_arr);
            $splist_receipt_serials = array_column($splist_list,'receipt_serial');

            // 没有拆单的收款单
            $none_split_receipt_serial = array_diff($receipt_serial_arr,$splist_receipt_serials);

            $already_split_consume_ids = [];
            // dd($reparied_arr);
            // dd($customer_list);
            // dd($receipt_serial_list);
            // dd($splist_list);
            // dd($receipt_serial_arr);
            // dd($none_split_receipt_serial);

            //1 添加customer_invoice invoice_info invoice_customer_assoc
            //2 如果没有拆单数据 -> 创建拆单数据 remit_split_price
            //3 添加 rel_invoice_consume,rel_invoice_remit,rel_remit_consume
            //4 更新customer_consume remit customer_invoice
            foreach($reparied_arr as $reparied_info) {
                // dd($reparied_info);
                $customer_id = $reparied_info['customer_id'];
                echo "正在处理: ",$customer_id,PHP_EOL;
                // $customer_info = Customer::getCustomerInfo($customer_id);

                //1
                //customer_invoice
                $invoice_date = $reparied_info['invoice_data'];
                $username = $reparied_info['salesman'];
                $company_name = $reparied_info['company_name'];
                $invoice_money = $reparied_info['money'];
                $invoice_type = $reparied_info['invoice_type'];
                $invoice_model = $reparied_info['invoice_model'];
                $_receipt_create_time = $receipt_serial_list[$reparied_info['receipt_serial'][0]]['create_time'];
                $_invoice_id = $this->insert_customer_invoice($invoice_date,$username,$company_name,$invoice_money,$source,$invoice_type,$invoice_model,$_receipt_create_time);

                //invoice_info
                $left_money = $reparied_info['money'];
                foreach($reparied_info['invoice_no'] as $invoice_no) {
                    $_money = $left_money;
                    if($left_money > 100000) {
                        $left_money = $left_money - 100000;
                        $_money = 100000;
                    }
                    $this->insert_invoice_info($_invoice_id, $invoice_no, $invoice_date, $_money, $invoice_type);
                }
                $this->insert_invoice_customer_assoc($customer_id,$_invoice_id,date("Ymd",strtotime($invoice_date)),$username,$source);

                $rs_arr = $reparied_info['receipt_serial'];

                $need_run_2 = !empty(array_intersect($none_split_receipt_serial,$rs_arr));
                if($need_run_2){
                    $remit_list = Remit::getListByReceiptSerial($rs_arr);
                    //2
                    // $rsp_list = RemitSplitPrice::getListWithReceiptSerials($rs_arr);
                    $customer_consume_list = CustomerConsume::getListByCondition('*',[
                        ['source','=',$source],
                        ['customer_id','=',$customer_id],
                        ['consume_balance','!=',Common::AES_ENCRYPT_ZERO],
                    ],[],[],[
                        'id' => $already_split_consume_ids,
                    ],0,0,['consume_month','asc']);

                    if(empty($customer_consume_list)) {
                        echo $customer_id." 没有消耗!!!", PHP_EOL;
                    } else {
                        echo "生成拆单数据", PHP_EOL;

                        $current_consume = array_shift($customer_consume_list);
                        $current_remit = array_shift($remit_list);
                        $already_split_consume_ids[] = $current_consume['id'];
                        $current_remit_money = $current_remit['money'];

                        while(!is_null($current_consume) && !is_null($current_remit)){
                            if($current_consume['consume_month'] <= $reparied_info['first_split_month']) {
                                break;
                            }
                            echo $current_remit_money,"\t",$current_consume['consume_money'],"\t",$current_consume['consume_month'],PHP_EOL;
                            if($current_remit_money >= $current_consume['consume_money']) {
                                // echo "add split_remit_price data: money = ",$current_consume['consume_money'],PHP_EOL;
                                $this->insert_rsp($current_consume['consume_month'],$current_consume['product_id'],$current_remit['receipt_serial'],$current_consume['consume_money']);
                                $current_remit_money = $current_remit_money - $current_consume['consume_money'];
                                //next consume
                                $current_consume = array_shift($customer_consume_list);
                                $already_split_consume_ids[] = $current_consume['id'];
                                if($current_remit_money == 0) {
                                    $current_remit = array_shift($remit_list);
                                    if(is_null($current_remit)) {
                                        break;
                                    }
                                    $current_remit_money = $current_remit['money'];
                                }
                            } else {
                                // echo "add split_remit_price data: money = ",$current_remit_money,PHP_EOL;
                                $this->insert_rsp($current_consume['consume_month'],$current_consume['product_id'],$current_remit['receipt_serial'],$current_remit_money);
                                $current_consume['consume_money'] = $current_consume['consume_money'] - $current_remit_money;

                                $current_remit = array_shift($remit_list);
                                if(is_null($current_remit)) {
                                    break;
                                }
                                $current_remit_money = $current_remit['money'];
                            }
                        }
                    }
                }

                //3
                $remit_list = Remit::getListByReceiptSerial($rs_arr);
                $remit_list = array_column($remit_list,null,'receipt_serial');
                $_split_list = RemitSplitPrice::getListWithReceiptSerials($rs_arr);
                // $_split_list = array_column($_split_list,null,'receipt_serial');
                $_split_list = Func::column_with_multiple_fields($_split_list,['receipt_serial']);
                // 发票与消耗
                $now = date("Y-m-d H:i:s");
                foreach ($_split_list as $receipt_serial => $split_info_list) {
                    $split_money = 0;
                    foreach ($split_info_list as $split_info) {
                        $month = $split_info['month'];
                        $product_id = $split_info['product_id'];
                        $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
                        // $receipt_serial = $split_info['receipt_serial'];

                        $rel_money = $split_info['money'];
                        $split_money = $split_money + $rel_money;

                        $consume_info = CustomerConsume::getInfoByCondition([
                            ['consume_month','=',$month],
                            ['source','=',$source],
                            ['customer_id','=',$customer_id],
                            ['product_id','=',$product_id],
                        ]);

                        if(empty($consume_info)) {
                            // echo 'no consume!!!',PHP_EOL;
                            // var_dump([
                            //     ['consume_month','=',$month],
                            //     ['source','=',$source],
                            //     ['customer_id','=',$customer_id],
                            //     ['product_id','=',$product_id],
                            // ]);
                            // echo '___________',PHP_EOL;
                            // var_dump($split_info);
                            continue;
                        }


                        $consume_id = $consume_info['id'];

                        RelInvoiceConsume::where('consume_id',$consume_id)->update(['deleted_at' => $now,]);

                        $ric_data = [];
                        $ric_data[] = [
                            'customer_id'   => $customer_id,
                            'source'        => $source,
                            'invoice_id'    => $_invoice_id,
                            'invoice_money' => $invoice_money,
                            'consume_id'    => $consume_id,
                            'consume_month' => $month,
                            'product_id'    => $product_id,
                            'father_id'     => $father_id,
                            'consume_money' => $rel_money,
                            'rel_money'     => $rel_money,//开票金额
                            'invoice_model' => $invoice_model,
                            'created_at'    => $now,
                            'updated_at'    => $now,
                        ];

                        //添加发票与消耗的关系 rel_invoice_consume
                        RelInvoiceConsume::addData($ric_data);
                        // RelRemitConsume::where('consume_id',$consume_id)->update(['deleted_at' => $now,]);

                        // 消耗回款关系 rel_remit_consume
                        $irc_info = [];
                        $irc_info['customer_id']    = $customer_id;
                        $irc_info['source']         = $source;
                        $irc_info['remit_serial']   = $receipt_serial;
                        $irc_info['receipt_serial'] = $receipt_serial;
                        $irc_info['remit_money']    = Func::mockMysqlEncrypt($rel_money);
                        $irc_info['consume_id']     = $consume_id;
                        $irc_info['consume_month']  = $month;
                        $irc_info['product_id']     = $product_id;
                        $irc_info['father_id']      = $father_id;
                        $irc_info['consume_money']  = Func::mockMysqlEncrypt($consume_info['consume_money']);
                        $irc_info['rel_money']      = Func::mockMysqlEncrypt($rel_money);
                        $irc_info['admin']          = 'relate';
                        $irc_info['created_at']     = $now;
                        $irc_info['updated_at']     = $now;

                        RelRemitConsume::insert($irc_info);


                        // var_dump($consume_info);
                        // var_dump( $this->diff($consume_info['consume_balance'], $rel_money));
                        //4
                        CustomerConsume::where('id',$consume_id)->update(['consume_balance' => $this->diff($consume_info['consume_balance'], $rel_money),'consume_unpaid' => $this->diff($consume_info['consume_unpaid'], $rel_money),'invoice_status' => 30]);
                        $_invoice_info = CustomerInvoice::getOneCustomerInvoice($_invoice_id);
                        CustomerInvoice::where('invoice_id',$_invoice_id)->update(['invoice_balance' => $this->diff($_invoice_info['invoice_balance'] , $rel_money)]);

                        // $consume_info = CustomerConsume::getInfoByCondition([
                        //     ['consume_month','=',$month],
                        //     ['source','=',$source],
                        //     ['customer_id','=',$customer_id],
                        //     ['product_id','=',$product_id],
                        // ]);
                        // dd($consume_info);
                    }

                    $rir_info = [
                        'customer_id'    => $customer_id,
                        'source'         => $source,
                        'invoice_id'     => $_invoice_id,
                        'invoice_money'  => $invoice_money,
                        'remit_serial'   => $receipt_serial,
                        'receipt_serial' => $receipt_serial,
                        'remit_money'    => $remit_list[$receipt_serial]['money'],
                        'invoice_model'  => $invoice_model,
                        'rel_money'      => $split_money,
                    ];
                    RelInvoiceRemit::addOne($rir_info);
                    //4
                    $_remit_info = Remit::getInfoByReceiptSerial($receipt_serial);
                    Remit::where('receipt_serial',$receipt_serial)
                         ->update([
                             'remit_unconsumed' => $this->diff($_remit_info['remit_unconsumed'], $split_money),
                             'remit_balance'    => $this->diff($_remit_info['remit_balance'], $split_money),
                         ]);
                    // $_remit_info = Remit::getInfoByReceiptSerial($receipt_serial);
                    // dd($_remit_info);
                }

                echo $customer_id,"处理完成!",PHP_EOL;
            }




            //执行sql
            $this->exec_sql($this->need_exec_sqls_after_script);

            DB::commit();//提交事务
        } catch (Exception $e) {
            DB::rollBack();//回滚事务
            throw new Exception('脚本异常:! '.$e->getFile().':'.$e->getLine().' '.$e->getMessage());
        }
    }


    /**
     * @throws Exception
     */
    private function insert_rsp($month, $product_id, $receipt_serial, $money) {
        $list = [];

        $list['month']          = $month;
        $list['product_id']     = $product_id;
        $list['product_name']   = RedisCache::instance('productId_productName_mapping')->get($product_id);
        $list['receipt_serial'] = $receipt_serial;
        $list['money']          = $money;
        $list['admin']          = 'fix_old_invoice_data';
        $list['create_time']    = time();
        $list['update_time']    = time();

        // echo __LINE__,PHP_EOL;
        RemitSplitPrice::insert($list);
    }

    private function insert_customer_invoice($invoice_date,$username,$company_name,$money,$source,$invoice_type,$invoice_model,$remit_date){
        $now = date("Y-m-d H:i:s");

        $fmt_date = date("Ymd",strtotime($invoice_date));
        $this->invoice_idx ++;
        $idx =sprintf("%03d", $this->invoice_idx);
        $invoice_id = 'IN'.$fmt_date."FIX".$idx;
        $invoice_data = [
            'invoice_id'      => $invoice_id,
            'salesman'        => $username,
            'is_with_bill'    => 0,
            'company_name'    => $company_name,
            'invoice_company' => $company_name,
            'tax_number'      => '',
            'bank'            => '',
            'bank_account'    => '',
            'address'         => '',
            'phone'           => '',
            'post_receiver'   => '',
            'post_phone'      => '',
            'post_address'    => '',
            'date'            => date("Y-m-d", strtotime($invoice_date)),
            'money'           => $money,
            // 'balance_money'   => $payment_type == 2 ? 0 : $money,//后付费不计算余额
            'email'           => '',
            'invoice_type'    => $invoice_type,
            'invoice_content' => '',
            'remark'          => '历史开票数据',
            'invoice_remark'  => '',
            'status'          => Common::INVOICE_STATUS_DONE_REMIT,
            'source'          => $source,
            'invoice_model'   => $invoice_model,
            'invoice_balance' => $money,//Common::AES_ENCRYPT_ZERO,//后付费 发票余额为0
            'remit_status'    => Common::INVOICE_REMIT_STATUS_UNPAID,//未回款
            'rel_type'        => Common::INVOICE_REL_CONSUME,//关联消耗
            'remit_day'       => date("Y-m-d H:i:s", $remit_date),
            'created_at'      => $now,
            'updated_at'      => $now,
        ];


        // 插入数据 创建发票
        CustomerInvoice::insert($invoice_data);

        return $invoice_id;
    }

    private function first_split_month($customer_id){
        $remit_list = Remit::getListByCustomerIds([$customer_id]);
        $remit_list = array_column($remit_list,'receipt_serial');
        $split_list = RemitSplitPrice::getListWithReceiptSerials($remit_list);
        if(empty($split_list)) {
            return 0;
        }
        return min(array_column($split_list,'month'));
    }

    private function insert_invoice_info($invoice_id,$invoice_no,$date,$money,$invoice_type) {
        $now = date("Y-m-d H:i:s");

        $info = [
            'invoice_id'     => $invoice_id,
            'invoice_code'   => '',//空出现在没有发票代码 "1100224130"
            'invoice_no'     => $invoice_no,// 发票号码 "05138838"
            'date'           => $date,// 45051.*********
            'product_name'   => "*研发和技术服务*技术服务费",
            'number'         => 1,
            'price'          => $money * 0.94,// "94339.6226415094"
            'money'          => $money * 0.94,// "94339.62"
            'tax_rate'       => "0.06",
            'tax'            => $money * 0.06,// "5660.38"
            'total_money'    => $money,// 总金额
            'invoice_status' => 1,// "正常发票"
            'invoice_type'   => $invoice_type,// "专票"
            'created_at'     => $now,
            'updated_at'     => $now,
        ];

        InvoiceInfo::insert($info);
    }


    private function insert_invoice_customer_assoc($customer_id,$invoice_id,$month,$salesman,$source) {
        $now = date("Y-m-d H:i:s");
        $ica_data[] = [
            'customer_id' => $customer_id,
            'invoice_id'  => $invoice_id,
            'month'       => $month,
            'salesman'    => $salesman,
            'source'      => $source,
            'updated_at'  => $now,
            'created_at'  => $now,
        ];
        //添加发票与客户的关系
        InvoiceCustomerAssoc::insert($ica_data);
    }

    private function diff($num1,$num2) {
        // var_dump($num1,$num2);
        return Func::mockMysqlEncrypt($num1 - $num2);
    }


    // /**
    //  * @throws Exception
    //  */
    // private function insert_rel_invoice_consume($customer_id, $source, $invoice_id, $money, $month, $consume_id, $product_id, $consume_money, $rel_invoice_consume_money, $invoice_model) {
    //     $now = date("Y-m-d H:i:s");
    //
    //     $ric_data = [];
    //     $ric_data[] = [
    //         'customer_id'   => $customer_id,
    //         'source'        => $source,
    //         'invoice_id'    => $invoice_id,
    //         'invoice_money' => $money,
    //         'consume_id'    => $consume_id,
    //         'consume_month' => $month,
    //         'product_id'    => $product_id,
    //         'father_id'     => RedisCache::instance('productId_fatherId_mapping')->get($product_id),
    //         'consume_money' => $consume_money,
    //         'rel_money'     => $rel_invoice_consume_money,//开票金额
    //         'invoice_model' => $invoice_model,
    //         'created_at'    => $now,
    //         'updated_at'    => $now,
    //     ];
    //
    //     //添加发票与消耗的关系 rel_invoice_consume
    //     RelInvoiceConsume::addData($ric_data);
    // }
    //
    //
    // private function insert_rel_invoice_remit($customer_id,$source,$invoice_id,$invoice_money,$receipt_serial,$remit_money,$rel_invoice_remit_money,$invoice_model) {
    //     $rir_info = [
    //         'customer_id'    => $customer_id,
    //         'source'         => $source,
    //         'invoice_id'     => $invoice_id,
    //         'invoice_money'  => $invoice_money,
    //         'remit_serial'   => $receipt_serial,
    //         'receipt_serial' => $receipt_serial,
    //         'remit_money'    => $remit_money,
    //         'invoice_model'  => $invoice_model,
    //         'rel_money'      => $rel_invoice_remit_money,
    //     ];
    //     RelInvoiceRemit::addOne($rir_info);
    // }
    //
    // /**
    //  * @throws Exception
    //  */
    // private function insert_rel_remit_consume($customer_id, $source, $month, $consume_id, $product_id, $consume_money, $receipt_serial, $remit_money, $rel_remit_consume_money){
    //     $now = date("Y-m-d H:i:s");
    //
    //     $irc_info = [];
    //     $irc_info['customer_id']    = $customer_id;
    //     $irc_info['source']         = $source;
    //     $irc_info['remit_serial']   = $receipt_serial;
    //     $irc_info['receipt_serial'] = $receipt_serial;
    //     $irc_info['remit_money']    = Func::mockMysqlEncrypt($remit_money);
    //     $irc_info['consume_id']     = $consume_id;
    //     $irc_info['consume_month']  = $month;
    //     $irc_info['product_id']     = $product_id;
    //     $irc_info['father_id']      = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
    //     $irc_info['consume_money']  = Func::mockMysqlEncrypt($consume_money);
    //     $irc_info['rel_money']      = Func::mockMysqlEncrypt($rel_remit_consume_money);
    //     $irc_info['admin']          = 'relate';
    //     $irc_info['created_at']     = $now;
    //     $irc_info['updated_at']     = $now;
    //
    //     RelRemitConsume::insert($irc_info);
    // }
}

