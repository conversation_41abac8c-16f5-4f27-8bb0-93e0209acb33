<?php

namespace App\Console\Commands\Invoice;

use App\Define\Common;
use App\Models\Invoice\CustomerConsume;
use App\Models\Invoice\RelInvoiceConsume;
use Illuminate\Console\Command;
use Exception;
use Illuminate\Support\Facades\DB;


/**
 * 发票数据修复脚本
 *
 *
 * php artisan invoice:fix_customer_consume
 */
class FixCustomerConsumeData extends Command
{
    protected $signature = 'invoice:fix_customer_consume';

    protected $description = 'fix customer_consume status and consume_balance';


    /**
     * @throws Exception
     */
    public function handle() {
        //设置参数
        try {
            $this->output->success("【{$this->description}】 开始");

            $this->fix();//修复

            $this->output->success("【{$this->description}】 完成");
        } catch (Exception $e) {
            throw new Exception($e->getFile().":".$e->getLine()." ".$e->getMessage());
        }
    }

    /**
     * 修复
     *
     * @throws Exception
     */
    private function fix(){
        try {
            DB::beginTransaction();//开启事务

            $last_consume_id = 0;
            do {
                $customer_consume_list = CustomerConsume::selectFormat('*')
                    ->where('id','>',$last_consume_id)
                    ->whereNotIn('id',[3513,4136])
                    ->whereIn('invoice_status',[Common::INVOICE_STATUS_PART,Common::INVOICE_STATUS_INIT])
                    ->whereNull('deleted_at')
                    ->where('created_at','>','2024-01-01 00:00:00')
                    ->where('consume_month','>=','202401')
                    ->orderBy('id')
                    ->limit(100)
                    ->get()->toarray();
                $balance_map = array_column($customer_consume_list, 'consume_money','id');

                if(!empty($customer_consume_list)){
                    $last_consume_id = $customer_consume_list[count($customer_consume_list) - 1]['id'];
                    // $customer_consume_arr = array_column($customer_consume_list,null, 'id');

                    foreach ($customer_consume_list as $customer_consume) {
                        // echo 'update ',$customer_consume['id'].PHP_EOL;

                        $rel_invoice_list = RelInvoiceConsume::getListByConsumeId($customer_consume['id']);
                        if(empty($rel_invoice_list)){
                            // echo 'no rel_invoice_consume data!',$customer_consume['id'].PHP_EOL;
                            continue;
                        }

                        foreach($rel_invoice_list as $ris_info){
                            if(in_array($ris_info['id'],[4302,4303,4304,4253])) {
                                continue;
                            }
                            $_consume_id = $ris_info['consume_id'];
                            // $consume_info = $customer_consume_arr[$_consume_id];
                            $_balance = bcsub($balance_map[$_consume_id],$ris_info['rel_money'],2);
                            $_invoice_status = bccomp($_balance, 0, 6) == 0 ? Common::INVOICE_STATUS_INVOICE : Common::INVOICE_STATUS_PART;

                            // if($_balance != 0) {
                            //     echo 'consume_balance :' . $balance_map[$_consume_id], '-', $ris_info['rel_money'] . ' -> ' . $_balance, PHP_EOL;
                            //     echo 'invoice_status :' . $consume_info['invoice_status'] . ' -> ' . $_invoice_status, PHP_EOL;
                            // }

                            if($_balance < 0){
                                $_balance = 0;
                                $_invoice_status = Common::INVOICE_STATUS_INVOICE;
                            }

                            $balance_map[$_consume_id] = $_balance;
                            CustomerConsume::updateInvoiceStatusAndBalance($_consume_id,$_balance,$_invoice_status);
                        }
                    }
                }
            } while (!empty($customer_consume_list));


            DB::commit();//提交事务
        } catch (Exception $e) {
            DB::rollBack();//回滚事务
            throw new Exception('脚本异常:! '.$e->getFile().':'.$e->getLine().' '.$e->getMessage());
        }
    }

}

