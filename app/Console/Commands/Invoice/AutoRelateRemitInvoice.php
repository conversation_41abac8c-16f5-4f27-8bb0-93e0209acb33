<?php

namespace App\Console\Commands\Invoice;

use App\Define\Common;
use App\Models\Invoice\CustomerConsume;
use App\Models\Invoice\CustomerInvoice;
use App\Models\Invoice\RelInvoiceConsume;
use App\Models\Invoice\RelInvoiceRemit;
use App\Models\Remit;
use App\Repositories\FeishuRepository;
use App\Utils\Helpers\Func;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 自动关联[到款remit-票invoice]关系
 * 到款: 已认款, 未开票
 * 发票: 已开票, 按消耗开票, 未回款
 * php artisan invoice:AutoRelateRemitInvoice
 * @uses AutoRelateRemitInvoice
 */
class AutoRelateRemitInvoice extends Command
{
    protected $signature = 'invoice:AutoRelateRemitInvoice';

    const CRON_NAME = '自动关联[到款remit-票invoice]关系';

    /**
     * @throws Exception
     */
    public function handle() {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s,", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $this->output->success($exception->getMessage());

            $oFeishu = new FeishuRepository();
            $aMessage = [
                'err_msg' => $exception->getMessage(),
            ];
            $oFeishu->send_card_message_to_chat_group(self::CRON_NAME . '脚本执行失败!', $aMessage);
        }
    }

    /**
     * @return true
     */
    private function main() {
        // 所有 已认款, 未开票 到款信息
        $aCond = [
            ['invoice_status', '=', Common::INVOICE_STATUS_INIT],
            ['status', '=', Common::REMIT_STATUS_CONFIRM],
        ];
        $aRemitList = Remit::aGetListByCond($aCond);
        // 所有 发票已开票, 按消耗开票, 未回款 发票信息
        $aCond = [
            ['status', '=', Common::INVOICE_STATUS_INVOICE],
            ['rel_type', '=', Common::INVOICE_REL_CONSUME],
            ['remit_status', '=', Common::INVOICE_REMIT_STATUS_UNPAID],
        ];
        $aInvoiceList = CustomerInvoice::aGetListByCond($aCond);

        if (empty($aRemitList) || empty($aInvoiceList)) {
            return true;
        }

        $sTime = date('Y-m-d H:i:s');
        foreach ($aRemitList as $aRemit) {
            $sRemitCompanyName = trim(str_replace(['（', '）'], ['(', ')'], $aRemit['name']));
            $sRemitMoney = round($aRemit['money'], 2);

            foreach ($aInvoiceList as $aInvoice) {
                $sInvoiceCompanyName = trim(str_replace(['（', '）'], ['(', ')'], $aInvoice['invoice_company']));

                if ($sRemitCompanyName == $sInvoiceCompanyName
                    && bccomp($sRemitMoney, $aInvoice['money'], 2) === 0
                    && $aRemit['source'] == $aInvoice['source']
                ) {
                    // 关联关系
                    $oRelInvoiceRemit = new RelInvoiceRemit();
                    $oRelInvoiceRemit->customer_id = $aRemit['customer_id'];
                    $oRelInvoiceRemit->source = $aInvoice['source'];
                    $oRelInvoiceRemit->invoice_id = $aInvoice['invoice_id'];
                    $oRelInvoiceRemit->invoice_money = $aInvoice['money'];
                    $oRelInvoiceRemit->invoice_model = $aInvoice['invoice_model'];
                    $oRelInvoiceRemit->remit_serial = $aRemit['remit_serial'];
                    $oRelInvoiceRemit->receipt_serial = $aRemit['receipt_serial'];
                    $oRelInvoiceRemit->remit_money = $aRemit['money'];
                    $oRelInvoiceRemit->rel_money = $aRemit['money'];
                    $oRelInvoiceRemit->created_at = $sTime;
                    $oRelInvoiceRemit->save();

                    // 到款remit 改信息
                    $oRemit = Remit::find($aRemit['id']);
                    if ($oRemit) {
                        $oRemit->remit_balance = 0;
                        $oRemit->remit_unconsumed = 0;
                        $oRemit->invoice_status = Common::INVOICE_STATUS_INVOICE;
                        $oRemit->save();
                    }

                    // 发票invoice 改信息
                    $oCustomerInvoice = CustomerInvoice::find($aInvoice['id']);
                    if ($oCustomerInvoice) {
                        $oCustomerInvoice->remit_status = Common::INVOICE_REMIT_STATUS_PAID;
                        $oCustomerInvoice->invoice_balance = 0;
                        $oCustomerInvoice->remit_day = date('Y-m-d H:i:s', $aRemit['remit_date']);
                        $oCustomerInvoice->save();
                    }

                    // 通过 发票流水号 找到关联 的消耗
                    $aCond = [
                        ['invoice_id', '=', $aInvoice['invoice_id']],
                    ];
                    $aRelList = RelInvoiceConsume::aGetListByCond($aCond);
                    $aConsumeId = array_column($aRelList, 'consume_id');
                    $aConsumeRelMap = array_column($aRelList, null, 'consume_id');
                    $aCond = [
                        ['id', 'in', $aConsumeId],
                    ];
                    $aConsumeList = CustomerConsume::getDataByCond($aCond);

                    $aUpdateList = [];
                    foreach ($aConsumeList as $aConsume) {
                        $fConsumeUnpaid = bcsub($aConsume['consume_unpaid'], $aConsumeRelMap[$aConsume['id']]['rel_money'], 6);

                        $aUpdateList[] = [
                            'id' => $aConsume['id'],
                            'consume_unpaid' => $fConsumeUnpaid,
                        ];
                    }
                    if ($aUpdateList) {
                        CustomerConsume::batchUpdate($aUpdateList);
                    }
                }
            }

            usleep(10000);
        }

        return true;
    }
}