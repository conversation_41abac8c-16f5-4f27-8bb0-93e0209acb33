<?php

namespace App\Console\Commands;

use App\Models\MongoUpstreamBill;
use App\Models\Product;
use App\Models\StatisticsInterfaceUsage;
use Illuminate\Console\Command;

/**
 * Class TransStatInterface 将旧版成本调用量导入到新版中
 * 只导401的数据
 * 会导到 statistics_interface_usage 个表中
 * @package App\Console\Commands
 */
class TransInterfaceStat extends Command
{
    protected $signature   = 'trans:interface_stat
    {--date= : 数据开始日期Ymd}
    {--days= : 天数}';
    protected $date;
    protected $days =1;
    protected $product_ids = [];

	public function handle()
	{
        if ($this->setParams()) {
            for ($i = 0; $i < $this->days; $i++) {
                $curr_date = date('Ymd', strtotime("+{$i} days", strtotime($this->date)));
                //按天从mongo中拿旧账单
                $mongo_data = $this->getMongoBill($curr_date);
                foreach ($mongo_data as $bill) {
                    if (count($bill['details']) != 6) continue;//特殊消耗
                    if (in_array($bill['product_id'], $this->product_ids[401])) {
                        $this->transBQC($bill);  //邦企查
                    }
                }
                echo $curr_date."重跑完毕".PHP_EOL;
            }
        }
		$this->output->success('校验通过');
	}
    /**
     * 设置参数
     *
     * @access protected
     *
     * @return boolean
     **/
    protected function setParams()
    {
        $date       = $this->input->getOption('date');
        $this->date = empty($date) ? '20200101' : $date;
        if (!preg_match('/^\d{8}$/', $this->date)) {
            $this->output->error('日期格式不正确');
            return false;
        }

        $days = $this->input->getOption('days');
        $this->days = empty($days) ? 1 : intval($days);
        if ($days < 0) {
            $this->output->error('days不正确');
            return false;
        }
        $this->product_ids[401] = array_column(Product::getChildProduct([401]), 'product_id');
        return true;
    }
    /**
     * 转移邦企查数据
     * @param $bill
     */
    protected function transBQC($bill) {
        $iid = $this->getBQCIId($bill['channel']);
        $product_id = 401;
        if (!$bill['details']['all']['fee_number']) {
            return; //过滤无意义数据
        }
        $stat_data = [
            'apikey' => $bill['apikey'],
            'product_id' => $product_id,
            'date' => $bill['date'],
            'create_time' => time(),
            'update_time' => time(),
            'interface_id' => $iid,
            'iid' => $iid,
            'total' => $bill['details']['all']['fee_number'],
            'success' => $bill['details']['succ']['fee_number'],
            'valid' => $bill['details']['succ']['fee_number']
        ];
        $this->insertStat($stat_data);
    }

    protected function insertStat($item) {
        StatisticsInterfaceUsage::updateOrCreate(array_only($item, [
            'apikey',
            'product_id',
            'date',
            'interface_id',
        ]), $item);
    }

    protected function getMongoBill($date) {
        return  MongoUpstreamBill::query()
            ->raw(function ($collcetion) use ($date) {
                $aggregate = [['$match' => ['date' => intval($date)]]];
                return $collcetion->aggregate($aggregate);
            })
            ->toArray();
    }
    protected function getBQCIId($old_channel){
//        $channel = [
//            'tianyancha' => 201,
//            'chuanglan' => 120
//        ];
        $interface = [
            'tianyancha' => 383,
            'chuanglan' => 259
        ];
        return $interface[$old_channel];
    }
}