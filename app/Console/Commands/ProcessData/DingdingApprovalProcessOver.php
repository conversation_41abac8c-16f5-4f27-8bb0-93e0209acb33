<?php

namespace App\Console\Commands\ProcessData;

use App\Models\ChannelAccount;
use App\Utils\AliCall;
use App\Utils\DingDing;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Models\DingdingApprovalProcess as approvalprocess;
use App\TraitUpgrade\WechatExceptionTrait;

class DingdingApprovalProcessOver extends Command
{
    use  WechatExceptionTrait;
    protected $signature = 'ding:approval_process_over';

    protected $description = '钉钉报警审批超过8小时未处理';

    protected  $type = [
           1=>'限量预警',
           2=>'余额预警',
           3=>'合同日期预警'
    ];

    public function handle()
    {
        $dingding_approvel_process_data = $this->getApprovelProcessData();
        $this->getProcessInstanceStatus($dingding_approvel_process_data);
    }

    //获取最新的待审批数据
    private function getApprovelProcessData()
    {
        $dingding_approvel_process_data = approvalprocess::getDingProcessing();
        return $dingding_approvel_process_data;
    }

    //获取更新审批状态
    private function getProcessInstanceStatus($dingding_approvel_process_data)
    {

         foreach ($dingding_approvel_process_data as $v){
             if (time() - $v['create_time'] > 8 * 3600){
                 $type = $this->type[$v['type']];
                 $channel_account = ChannelAccount::find($v['channel_account_id'])['account'];

                 $str = "报警类型：运营商预警超过8小时未处理\n\n\n运营商：$channel_account\n类型：$type";

//                 $this->wechatException($str,'wechat.channel.monitor');
                 $this->post(SEND_WECHAT_ALARM_URL,['content'=>$str]);
                 $type = '运营商'.$type;
                 AliCall::call(ALARM_PHONE_LC,['type'=>$type],DINGTALK_PHONE_CODE);
             }
         }
    }

}