<?php

namespace App\Console\Commands\ProcessData;

use App\Models\CustomerAlarmProcessing;
use App\Models\MoneyRecharge;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ProcessCustomerAlarmData extends Command
{

    protected $signature = 'customer:process_alarm_data';

    protected $description = '处理客户报警状态';

    public function handle()
    {
        $this->customer_alarm_data = $this->getCustomerAlarmData();
        $this->setCustomerAlarmProcessingStatus($this->customer_alarm_data);

    }

    //查询客户分类型下的最新一条的报警处理情况
    private function getCustomerAlarmData()
    {
      return  CustomerAlarmProcessing::select(DB::raw('max(create_time) as create_time'),'customer_id')
                                        ->where('status','=',1)
                                        ->groupby('customer_id')
                                        ->get()->toArray();
    }

    //获取最新一条的报警处理后 有无最新客户认款单 如有最新的认款单 则修改报警处理状态 2
    private function setCustomerAlarmProcessingStatus($customer_alarm_data)
    {
        if (empty($customer_alarm_data)){
            return false;
        }
        array_walk($customer_alarm_data,function ($item){
                $res = $this->getCustomerRemit($item['customer_id'],$item['create_time']);
                if (!empty($res)){
                      $this->setCustomerAlarmStatus($item['customer_id']);
                }
        });
    }

    //查询有无最新的认款单
    private function getCustomerRemit($customer_id,$remit_date)
    {
          return MoneyRecharge::select()->where('customer_id','=',$customer_id)->where('remit_date','>=',$remit_date)->get()->toArray();
    }

    //把报警处理状态 置为2
    private  function setCustomerAlarmStatus($customer_id)
    {
        CustomerAlarmProcessing::where('customer_id',$customer_id)->update(['status'=>2]);
    }

}