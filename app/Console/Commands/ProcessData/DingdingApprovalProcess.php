<?php

namespace App\Console\Commands\ProcessData;

use App\Utils\DingDing;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Models\DingdingApprovalProcess as approvalprocess;

class DingdingApprovalProcess extends Command
{

    protected $signature = 'ding:approval_process';

    protected $description = '钉钉报警审批';

    public function handle()
    {
        $this->dingding_approvel_process_data = $this->getApprovelProcessData();
        $this->getProcessInstanceStatus($this->dingding_approvel_process_data);
    }

    //获取最新的待审批数据
    private function getApprovelProcessData()
    {
        $dingding_approvel_process_data = approvalprocess::getDingProcessing();
        return $dingding_approvel_process_data;
    }

    //获取更新审批状态
    private function getProcessInstanceStatus($dingding_approvel_process_data)
    {
        if (empty($dingding_approvel_process_data)){
            return false;
        }

        $dingding = new DingDing("dinga5hsi6g7dxqfthaj", "iVpM6SM1wDaYPms01TH3zVcPLHvLV6BVeHpxXmbg8aHw0QByl0oj4MW0rtH7z4UA");

        array_walk($dingding_approvel_process_data,function ($item) use($dingding){
            $res =  $dingding->getProcessInstance($item['process_instance_id']);


            if ($res['errcode'] == 0 && in_array($res['process_instance']['status'], ['COMPLETED', 'TERMINATED', 'CANCELED'])){
                $last_result = isset($res['process_instance']['operation_records']) ? array_column($res['process_instance']['operation_records'],null,'operation_type') : [];

                $updateData['mark'] = isset($last_result['EXECUTE_TASK_NORMAL']['remark']) ? '肜祥:'.$last_result['EXECUTE_TASK_NORMAL']['remark'] : '';
                $updateData['result'] = $res['process_instance']['result'] ?? '';
                $updateData['status'] = $res['process_instance']['status'] ?? '';
                $updateData['real_result'] = 2;  //审批结束
//
                approvalprocess::updateData(['id'=>$item['id']],$updateData);
            }
        });

    }

}