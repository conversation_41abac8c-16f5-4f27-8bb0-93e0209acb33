<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/13 0013
 * Time: 13:08
 */

namespace App\Console\Commands;

use App\Models\EmailConfig;
use App\Models\MongoStatis;
use App\TraitUpgrade\ExcelTrait;
use App\TraitUpgrade\MailTrait;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;
use App\Models\Account;
use App\Models\AccountProduct;

abstract class ProductDayReport extends Command
{
    use WechatExceptionTrait, ExcelTrait, MailTrait;


    protected $date;        //发送那日的数据
    protected $addressee = [];   //设置邮件收件人，如果设置了此项，将不会从数据库中读取收件人、抄送人

    protected $yuloreCustomerId  = 'C20180828LOCNMG';
    protected $emailConfigScene;  //邮件配置使用场景（通过此参数获取email_config表中的收件人）
    protected $productId;       //产品ID，可以为数组|int
    protected $productName;     //产品名称
    protected $statisticsField; //统计字段
    protected $compareDays       = 2;         //数据比较天数
    protected $sortField         = '';        //排序字段
    protected $excelRow          = [          //excel列
        ['name' => '客户ID', 'width' => 20, 'field' => 'customer_id'],
        //...
    ];
    protected $filterAccountIds  = [];  //过滤掉此处的账号ID数据
    protected $filterCustomerIds = [];  //过滤掉此处的客户ID数据

    protected $nodeArea              = ['shenzhen', 'beijing'];   //节点
    protected $contractStatusMapping = [
        1 => '已签约已付款',
        2 => '已签约未付款',
        3 => '未签约',
        4 => '特殊客户',
        5 => '其他',
    ];

    /**
     * 执行
     *
     * @access public
     *
     * @return void
     **/
    public function handle()
    {
        try {
            //设置参数
            $this->setOptions();

            //生成报表数据
            $data = $this->getReportInfo();

            //生成excel
            $filename = $this->createExcel($data);

            //生成html报表
            $html = $this->createHtml($data);

            //获取邮件的收件人、抄送人
            $emailConfigInfo = $this->getEmailConfig();

            //发送邮件
            $res = $this->setSubject("{$this->productName}日报表")->setRecipients(array_get($emailConfigInfo,
                'recipients'))->setCC(array_get($emailConfigInfo, 'cc'))->setAttachment([
                [
                    'filepath' => $filename,
                    'name'     => "{$this->productName}调用量统计_{$this->date}.xlsx"
                ]
            ])->sendMail($html);

            if (!$res) {
                throw new \Exception('邮件发送失败');
            }
        } catch (\Exception $exception) {
            $this->wechatException("【{$this->name}】任务执行失败，原因是：{$exception->getMessage()} In Line:{$exception->getFile()}");
            $this->output->error($exception->getMessage());
        }
    }

    /**
     * 获取邮件配置
     *
     * @access protected
     *
     * @return array
     **/
    protected function getEmailConfig()
    {
        if (empty($this->addressee)) {
            $emailConfig = new EmailConfig();
            return $emailConfig->getRecipientsAndCCByScene($this->emailConfigScene);
        }
        $recipients = array_map(function ($address) {
            list($name, $domain) = explode('@', $address);
            return compact('name', 'address');
        }, $this->addressee);
        $cc         = [];
        return compact('recipients', 'cc');
    }

    /**
     * 为html生成table内容盒子
     *
     * @access protected
     *
     * @param $data array 报表数据
     *
     * @return string
     **/
    protected function setHtmlBody($data)
    {
        //标题
        $table = '<tr><td><nobr>' . implode(array_column($this->excelRow, 'name'), '</nobr></td><td><nobr>') . '</nobr></td></tr>';

        //内容
        array_walk($data, function ($item) use (&$table) {
            $table .= '<tr>';
            foreach ($this->excelRow as $row) {
                $callback = array_get($row, 'callback', '');
                $field    = $row['field'];
                $value    = $item[$field];
                if (!empty($callback)) {
                    $value = $this->$callback($value);
                }
                $table .= $this->createTdHtml($value, $row['field'], $item);
            }
            $table .= '</tr>';
        });
        return $table;
    }

    /**
     * 设置html的每个TR数据
     *
     * @access protected
     *
     * @param $value mixed 展示的值
     * @param $field string 字段
     * @param $data  array 单个数据
     *
     * @return string
     **/
    protected function createTdHtml($value, $field, $data)
    {
        $td = "<td>{$value}</td>";
        switch ($field) {
            case 'contract_status_string':
                switch ($data['contract_status']) {
                    case 1:
                        $td = "<td class='td_green'><nobr>{$value}</nobr></td>";
                        break;
                    case 2:
                        $td = "<td class='td_red'><nobr>{$value}</nobr></td>";
                        break;
                }
                break;
        }
        return $td;
    }

    /**
     * 生成Html页面
     *
     * @access protected
     *
     * @param $data array 报表数据
     *
     * @return string
     **/
    protected function createHtml($data)
    {
        $title = '以下是' . date('Y-m-d', strtotime($this->date)) . $this->productName . '产品调用量统计，请查阅';
        $TBody = $this->setHtmlBody($data);
        $html  = <<<HTML
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>{$title}</title>
        <style>
            #tableArea { width:100%; height:auto; font-family: Arial, Helvetica, sans-serif; font-size: 14px; }
            .title { width:100%; height:26px; line-height:26px; color:#333; font-size:16px; font-weight:bold; text-align: center; }
            .table { width:98%; height:auto; overflow-x:auto; margin:20px auto; }
            .table table { height:auto; color:#333; border-top:1px solid #ccc; border-left:1px solid #ccc; min-width: 100%; }
            .table table tr:hover{ background:#eeeeee; }
            .table table th,.table table td { border-bottom: 1px solid #ccc; border-right: 1px solid #ccc; text-align:center; padding:4px 10px; box-sizing:border-box; white-space:nowrap; }
            .table table th { font-weight:bold; min-width:80px !important; }
            .td_red{color:red;}
            .td_green{color:green;}
        </style>
    </head>
    <body>
        <div id="tableArea">
            <div class="title">{$title}</div>
            <div class="table">
                <table cellpadding="0" cellspacing="0" border="1">
                    {$TBody}
                </table>
            </div>
        </div>
    </body>
</html>
HTML;
        return $html;
    }

    /**
     * 设置excel文件的内容
     *
     * @access protected
     *
     * @param $data array 报表数据
     *
     * @return void
     **/
    protected function setExcelBody($data)
    {
        //标题
        $this->setWidth(array_column($this->excelRow, 'width'));
        $this->addRowContent(array_column($this->excelRow, 'name'), 18, true);

        //内容
        array_walk($data, function ($item) {
            //设置本行样式
            $this->setColStyle($this->col, $item);
            $data = array_map(function ($row) use ($item) {
                $field    = $row['field'];
                $value    = array_get($item, $field, '--');
                $callback = array_get($row, 'callback', '');
                if (!empty($callback)) {
                    $value = $this->$callback($value);
                }
                return $value;
            }, $this->excelRow);
            $this->addRowContent($data, 16);

        });
    }

    /**
     * 为每个内容行设置样式
     *
     * @access protected
     *
     * @param $col  integer 行标
     * @param $data array 当前行数据
     *
     * @return void
     **/
    protected function setColStyle($col, $data)
    {
        $pCellCoordinate = "C{$col}";
        switch ($data['contract_status']) {
            case 1:
                $this->setColor($pCellCoordinate, 'green');
                break;
            case 2:
                $this->setColor($pCellCoordinate, 'red');
                break;
        }
    }

    /**
     * 生成Excel文件
     *
     * @access protected
     *
     * @param $data array 报表数据
     *
     * @return string
     **/
    protected function createExcel($data)
    {
        $this->file_out_init();

        //设置导出的内容
        $this->setExcelBody($data);

        //保存的文件名称
        $baseDir = app()->basePath() . '/storage/statisticsReportExcel';
        if (!is_dir($baseDir)) {
            mkdir($baseDir, 0755);
        }
        $time     = time();
        $filepath = "{$baseDir}/{$this->emailConfigScene}_{$this->date}_{$time}.xlsx";

        //设置sheet标题
        $this->setSheetTitle($this->productName . '调用量统计_' . $this->date);
        //保存文件
        $this->save($filepath);
        return $filepath;
    }

    /**
     * 设置参数
     *
     * @access protected
     *
     * @return void
     **/
    protected function setOptions()
    {
        $this->date = $this->input->getOption('date');
        $today      = date('Ymd');
        if ($this->date >= $today) {
            throw new \Exception('不可发送未来的报表');
        }

        $addressee = $this->input->getOption('addressee');

        if (!empty($addressee)) {
            $this->addressee = explode(',', $addressee);
        }
    }

    /**
     * 生成报表数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getReportInfo()
    {
        //获取客户信息
        $customerInfo = $this->getStoreCustomerInfo();

        $apikey = array_keys(array_get($customerInfo, 'apikeyCustomerIdMapping'));
        if (empty($apikey)) {
            return [];
        }

        //获取统计数据
        $statisticInfo = $this->getStatisticInfo($apikey);

        //与客户数据进行整合
        $result = $this->disposeReportInfo($customerInfo, $statisticInfo);

        //对数据进行过滤、计算
        $result = $this->filterResult($result);
        $result = $this->computeResult($result);

        //排序
        $result = $this->sortResult($result);
        return $result;
    }

    /**
     * 对结果数据进行排序
     *
     * @access protected
     *
     * @param $result array 数据集合
     *
     * @return array
     **/
    protected function sortResult($result)
    {
        $total          = array_shift($result);
        $notYuloreTotal = array_shift($result);
        $field          = $this->sortField;
        $asc            = SORT_DESC;
        array_multisort(array_map(function ($item) {
            return floatval($item);
        }, array_column($result, $field)), $asc, $result);

        array_unshift($result, $notYuloreTotal);
        array_unshift($result, $total);
        return $result;
    }

    /**
     * 过滤数据
     *
     * @access protected
     *
     * @param $result array 数据集合
     *
     * @return array
     **/
    protected function filterResult($result)
    {
        return array_filter($result, function ($item) {
            return $this->filterResultItem($item);
        });
    }

    /**
     * 计算数据
     *
     * @access protected
     *
     * @param $result array 数据集合
     *
     * @return array
     **/
    protected function computeResult($result)
    {
        return array_map(function ($item) {
            return $this->computeResultItem($item);
        }, $result);
    }

    /**
     * 过滤每一条数据
     *
     * @access protected
     *
     * @param $data array 每条数据[客户维度、包含合计、客户调用合计]
     *
     * @return boolean
     **/
    abstract protected function filterResultItem($data);

    /**
     * 计算每一条数据
     *
     * @access protected
     *
     * @param $data array 每条数据[客户维度、包含合计、客户调用合计]
     *
     * @return boolean
     **/
    abstract protected function computeResultItem($data);


    /**
     * 整合数据
     *
     * @access protected
     *
     * @param $customerInfo  array 客户信息
     * @param $statisticInfo array 统计数据
     *
     * @return array
     **/
    abstract protected function disposeReportInfo($customerInfo, $statisticInfo);

    /**
     * 获取统计数据
     *
     * @access protected
     *
     * @param $apikey array
     *
     * @return array
     **/
    protected function getStatisticInfo($apikey)
    {
        return MongoStatis::query()->raw(function ($collection) use ($apikey) {
            $aggregate = [];

            $amount_date = [];
            for ($i = 0; $i < $this->compareDays; $i++) {
                $amount_date[] = date('Ymd', strtotime("-{$i} days", strtotime($this->date)));
            }
            $product_id = is_array($this->productId) ? [
                '$in' => array_map('intval', $this->productId)
            ] : intval($this->productId);
            //查询条件
            $aggregate[0]['$match'] = [
                'apikey'      => ['$in' => $apikey],
                'node_area'   => ['$in' => $this->nodeArea],
                'product_id'  => $product_id,
                'amount_date' => ['$in' => $amount_date]
            ];

            //分组查询
            $aggregate[1]['$group'] = array_merge([
                '_id'        => [
                    'apikey'      => '$apikey',
                    'product_id'  => '$product_id',
                    'amount_date' => '$amount_date'
                ],
                'apikey'     => ['$first' => '$apikey'],
                'product_id' => ['$first' => '$product_id'],
                'date'       => ['$first' => '$amount_date']
            ], $this->statisticsField);

            //查询字段
            $aggregate[2]['$project'] = array_merge([
                '_id'        => 0,
                'apikey'     => 1,
                'product_id' => 1,
                'date'       => 1,
            ], array_combine(array_keys($this->statisticsField), array_fill(0, count($this->statisticsField), 1)));
            return $collection->aggregate($aggregate);
        })->toArray();
    }

    /**
     * 获取开通此产品的客户及账号信息
     *
     * @access protected
     *
     * @return array
     **/
    protected function getStoreCustomerInfo()
    {
        $field = [
            'customer.name',
            'customer.customer_id',
            'customer.contract_status',
            'account.apikey',
            'account.account_id',
            'account.account_name',
            'customer.status',
        ];
        $data  = AccountProduct::select($field)
            ->where(function ($query) {
                if (is_array($this->productId)) {
                    $query->whereIn('product_id', $this->productId);
                } elseif (is_numeric($this->productId)) {
                    $query->where('product_id', '=', $this->productId);
                }
                if (!empty($this->filterAccountIds)) {
                    $query->whereNotIn('account.account_id', $this->filterAccountIds);
                }
                if (!empty($this->filterCustomerIds)) {
                    $query->whereNotIn('account.customer_id', $this->filterCustomerIds);
                }
            })
            ->join('account', 'account.account_id', '=', 'account_product.account_id')
            ->join('customer', 'customer.customer_id', '=', 'account.customer_id')
            ->get()
            ->toArray();


        //将数据进行整理，整合成以下数据模式
        // [customer_id => [customer_id, customer_name, contract_status, status, account => [apikey, account_id, account_name]]]
        $customerInfo = [];

        array_walk($data, function ($item) use (&$customerInfo) {
            $customerId = $item['customer_id'];
            $accountId  = $item['account_id'];
            $apikey     = $item['apikey'];
            if ($this->isReckonIn($customerId, $accountId)) {
                if (!array_key_exists($customerId, $customerInfo)) {
                    $customerInfo[$customerId] = [
                        'customer_name'          => $item['name'],
                        'contract_status_string' => array_get($this->contractStatusMapping, $item['contract_status']),
                        'contract_status'        => $item['contract_status'],
                        'status'                 => $item['status'],
                        'account'                => []
                    ];
                }
                $customerInfo[$customerId]['account'][$apikey] = [
                    'apikey'       => $apikey,
                    'account_id'   => $item['account_id'],
                    'account_name' => $item['account_name']
                ];
            }
        });

        $apikeyCustomerIdMapping = array_column($data, 'customer_id', 'apikey');
        $yuloreApikey            = $this->getYuloreApikey();

        return compact('customerInfo', 'apikeyCustomerIdMapping', 'yuloreApikey');
    }

    /**
     * 是否计入到日报中
     *      日报中可能存在部分客户、账号不参与日报的统计，所以需要过滤掉
     *
     * @access protected
     *
     * @param $customerId string 客户ID
     * @param $accountId  string 账号ID
     *
     * @return boolean
     **/
    protected function isReckonIn($customerId, $accountId)
    {
        if (empty($this->filterCustomerIds) && empty($this->filterAccountIds)) {
            return true;
        } elseif (empty($this->filterCustomerIds)) {
            return !in_array($accountId, $this->filterAccountIds);
        } elseif (empty($this->filterAccountIds)) {
            return !in_array($customerId, $this->filterCustomerIds);
        } else {
            return !in_array($customerId, $this->filterCustomerIds) && !in_array($accountId, $this->filterAccountIds);
        }
    }

    /**
     * 获取羽乐内部的账号apikey
     *
     * @access protected
     *
     * @return array
     **/
    protected function getYuloreApikey()
    {
        return Account::where('customer_id', '=', $this->yuloreCustomerId)
            ->where('father_id', '!=', '0')
            ->pluck('apikey')
            ->toArray();
    }

    /**
     * 计算比率
     *
     * @access protected
     *
     * @param $left  integer 左侧数字
     * @param $right integer 右侧数字
     * @param $scale integer 保留小数位数
     *
     * @return float
     **/
    protected function computeRatio($left, $right, $scale = 6)
    {
        if (empty($right)) {
            return null;
        }

        return bcdiv($left, $right, $scale);
    }

    /**
     * 比率格式化的方法，可以通过callback调用
     *
     * @access protected
     *
     * @param $data mixed  数据
     *
     * @return string
     **/
    protected function ratioFormat($data)
    {
        if (is_null($data)) {
            return 'NA';
        }
        return round(bcmul($data, 100, 4), 2) . '%';
    }

    /**
     * 数值格式化方法，可以通过callback调用
     *
     * @access protected
     *
     * @param $data mixed  数据
     *
     * @return string
     **/
    protected function numberFormat($data)
    {
        return number_format($data);
    }
}