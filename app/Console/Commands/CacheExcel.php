<?php

namespace App\Console\Commands;


use App\Http\Repository\BillRepository;
use App\Http\Repository\BillV2Repository;
use App\Models\Customer;
use App\Models\MongoBillMonth;
use App\Models\MongoCacheExcel;
use App\Models\MongoCacheExcelV2;
use App\Models\Product;
use Illuminate\Console\Command;

class CacheExcel extends Command
{
    protected $signature = 'bill:cache-excel {--C|customer-id=default}';

    public function handle()
    {
        $customerIds = $this->getCustomerId();
        //创建进度条
        $bar = $this->output->createProgressBar(count($customerIds));
        $bar->start();

        foreach ($customerIds as $customerId) {

            $this->cacheV2($customerId);

            $bar->advance();
        }

        $bar->finish();
        $this->output->newLine();
        $this->output->writeln('缓存成功');
    }

    /**
     * v1版账单缓存
     *
     * @access private
     *
     * @param $customerId string 客户ID
     *
     * @return void
     **/
    private function cacheV1($customerId)
    {
        $repository = new BillRepository();
        $excel      = $repository->excel($customerId);

        $month      = intval(date('Ym', strtotime('last day of last month')));

        $created_at = time();
        $data       = compact('excel', 'customer_id', 'month', 'created_at');
        $mongo      = new MongoCacheExcel();
        $mongo->insert($data);
    }

    /**
     * v2版账单缓存
     *
     * @access private
     *
     * @param customer_id string 客户ID
     *
     * @return void
     **/
    private function cacheV2($customer_id)
    {
        $repository = new BillV2Repository();
        $excel      = $repository->excel($customer_id);

        $excel      = json_encode($this->formatData($excel), JSON_UNESCAPED_UNICODE);

        $month      = intval(date('Ym', strtotime('last day of last month')));
        $created_at = time();
        $data       = compact('excel', 'customer_id', 'month', 'created_at');
        $mongo      = new MongoCacheExcelV2();
        $mongo->insert($data);
    }

    /**
     * 格式化数据
     *
     * @access public
     *
     * @param $data mixed 需要格式化的数据
     *
     * @return array
     **/
    private function formatData($data)
    {
        if ($data instanceof MongoBillMonth) {
            return $data->toArray();
        } elseif (is_array($data)) {
            return array_map(
                function ($item) {
                    return $this->formatData($item);
                },
                $data
            );
        } else {
            return $data;
        }
    }

    /**
     * 获取客户ID
     *
     * @access private
     *
     * @return array
     **/
    private function getCustomerId()
    {
        $customerId = $this->option('customer-id');
        if ($customerId == 'default') {
            return Customer::where('is_delete', 'eq', '0')
                ->pluck('customer_id')
                ->toArray();
        } else {
            return [$customerId];
        }
    }

    /**
     * 保存生成的excel
     *
     * @access private
     *
     * @param $excel        array 用户的excel数据
     * @param $customer_id  string 客户
     *
     * @return void
     **/
    private function saveExcel($excel, $customer_id)
    {

        $month      = intval(date('Ym', strtotime('first day of last month')));
        $created_at = time();
        $data       = compact('excel', 'customer_id', 'month', 'created_at');
        $mongo      = new MongoCacheExcel();
        $mongo->insert($data);
}
}