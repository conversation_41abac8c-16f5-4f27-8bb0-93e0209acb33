<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\ChannelInterface;
use App\Models\StatisticsInterfaceUsage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Providers\RedisCache\RedisCache;
use App\Models\ChannelCostMinconsumeSpread as MyModel;
use App\Models\Customer;
use App\Models\ChannelAccountAdjust;

/**
 *
 *
 */
class ChannelCostMinConsumeSpread extends Command
{
    /** @var string 命令 */
    public $signature = 'spreaded_min_consume_cost
    {--month= : 账单日，默认今日（格式Ymd）}
    {--operator= : 运营商}
	{--charge= : 保底差额}
	{--del=0 : 计算是否清空数据，用于重新计算}
	{--filepath= : 计算是否清空数据，用于重新计算}


    ';

    protected $description = '计算保底差额并分摊至 渠道成本统计调整';


    /**
     * @var integer 计算月度（Ym）
     */
    protected $month = null;

    /**
     * @var int 运营商
     */
    protected $operator = '';

    /**
     * 保底差额
     *
     * @var integer
     */
    protected $charge = 0;

    /**
     * 计算是否先清空旧数据，用于重新计算
     *
     * @var integer
     */
    protected $del = 0;

    //上传文件路径
    protected $filepath = '';

    //
    protected $file = array();

    //待计算数据
    protected $calData = [];

    // 羽乐科技内部 售前测试
    public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS', 'C20231113E66N9F'];

    public $filter_apikeys = [];
    /**
     * 调用量占百分比多少以上的客户参与分摊成本
     *
     * @var float
     */
    protected $_allow_degree = 0.1;

    //计算小数位数
    protected $degree = 3;

    protected $spread_data = [];

    //
    protected $customerRepo='';


    public function handle()
    {
        try {
            $this->setData();
            $this->Calculate();
            $this->output->success("计算任务已执行，时间：".date('Y-m-d H:i:s'));
        } catch (Exception $e) {
            $this->output->error("计算失败：".$e->getMessage());
        }
    }

    /**
     * 获取未执行数据
     *
     * @access   protected
     */
    protected function setData()
    {
        $list   = MyModel::where('run_status',1)->where('is_delete',0)->get();

        $this->filter_apikeys = Account::whereIn('customer_id',$this->filter_customer)->pluck('apikey');
        if($list){
            $this->calData = $list->toArray();
        }
        $this->del = 1;
    }

    /**
     * 执行计算逻辑
     *
     * @access   public
     * @return void
     */
    public function Calculate()
    {
        $this->output->createProgressBar();
        $this->output->progressStart(count($this->calData));

        $this->customerIds = Customer::pluck('customer_id')->toArray();
        if (!empty($this->calData)) {
            foreach ($this->calData as  $value) {
                $this->del = $value['type'];
                $this->runItem($value['month'], $value['operator'], $value['money'],$value['run_id'],$value['category']);
            }
        } else {
            if($this->month){
                $this->runItem($this->month, $this->operator, $this->charge,0,1);
            }
        }
        // $this->mergeCustomerSpread();
        $this->output->progressFinish();
    }

    /**
     * 执行计算逻辑
     *
     * @param [type] $month
     * @param [type] $operator
     * @param [type] $charge
     * @return void
     */
    protected function runItem($month, $channel_id, $charge,$run_id,$category=0)
    {
        //如果是未来的时间，则不需要执行
        if (empty($month)||$month > date('Ym')) {
            return;
        }
        if ($channel_id == 1) {
            $end = $month . '27';
            $start = date('Ym27', strtotime('-1 months', strtotime($end)));
        } else {
            $start = $month . '01';
            $end = date('Ym01', strtotime('+1 months', strtotime($start)));
        }
        // Log::info('start:'.$start.' end:'.$end);

        // 如果是重跑 先删除
        if ($this->del===1) {
            ChannelAccountAdjust::where('run_id',$run_id)->delete();
        }
        try {
            $interface_ids = ChannelInterface::where('channel_id', $channel_id)->pluck('id')->toArray();
            
            if(empty($interface_ids)){
                return;
            }
            $where=[['date','>=',$start],['date','<',$end]];
            $sql = StatisticsInterfaceUsage::where($where)
            ->select([
                'apikey',
                'product_id',
                'interface_id',
                'encrypt',
                'operator',
                'source',
                // DB::raw('sum(total) AS total'),
                DB::raw('sum(success) AS total'),
                // DB::raw('sum(valid) AS valid'),
            ])

            ->whereIn('interface_id', $interface_ids)
            // 排除羽乐科技内部 售前测试调用
            ->whereNotIn('apikey', $this->filter_apikeys)
            ->groupBy(['apikey','product_id','interface_id', 'encrypt','source'])
            ->orderByRaw('apikey,product_id,interface_id,encrypt');

            $data = $sql->get()->toArray();

            if (empty($data)) {
                MyModel::where('run_id',$run_id)->update(['remarks'=>'未查询到调用数据','run_status'=>4]);
                return ;
            }
            // 计算总量
            $total=0;
            $apikeyAccountIdMap = $apikeys = [];
            array_walk($data, function ($item) use (&$total, &$apikeys) {
                $total +=$item['total'];
                $apikeys[]=$item['apikey'];
            });

            // 过滤数据 调用占比低于0.1%的客户不参与分摊
            $spread_data = [];
            foreach ($data as $value) {
                // Log::info('rate:'.bcdiv($value['total'], $total, 10).' 2:'.bcdiv($this->_allow_degree, 100, 10));
                if ((bcdiv($value['total'], $total, 10))>(bcdiv($this->_allow_degree, 100, 10))) {
                    $spread_data[]=$value;
                    // Log::info('pass');
                }
            }

            if(empty($spread_data)){
                MyModel::where('run_id',$run_id)->update(['remarks'=>'过滤后没有符合条件数据','run_status'=>4]);
                return ;
            }

            //重新计算总量
            $total=0;
            array_walk($spread_data, function ($item) use (&$total) {
                $total +=$item['total'];
            });

            $apikeyAccountIdMap =Account::whereIn('apikey', $apikeys)
            ->pluck('account_id', 'apikey')
            ->toArray();

            $sum = 0;
            $categoryName = $category==1?'保底分摊':'对账调整';
            foreach ($spread_data as $key =>$value) {
                // 先求百分比 再乘以总金额
                // $rate=bcdiv($value['total'], $total, 12);
                // $spread_data[$key]['money'] =round($rate*$charge, 2);

                // 先乘总金额 再除以总量
                $rate=($value['total']*$charge);
                $spread_data[$key]['money'] =round($rate/$total, 2);
                $spread_data[$key]['account_id'] = isset($apikeyAccountIdMap[$value['apikey']]) ? $apikeyAccountIdMap[$value['apikey']] : '';
                $spread_data[$key]['customer_id'] = RedisCache::instance('apikey_customerId_mapping')->get($value['apikey']);
                $spread_data[$key]['channel_id'] = RedisCache::instance('iid_channelId_mapping')->get($value['interface_id']);
                $spread_data[$key]['date'] = date('Y-m-d', strtotime($start));
                $spread_data[$key]['title'] = $start.$categoryName.'自动生成';
                $spread_data[$key]['remark'] = date('Y-m-d H:i:s').'自动生成记录';
                $spread_data[$key]['source'] = $value['source'];
                $spread_data[$key]['encrypt'] = $value['encrypt'];
                $spread_data[$key]['run_id'] = $run_id;
                $spread_data[$key]['category'] = $category;
                $spread_data[$key]['create_time'] = $spread_data[$key]['update_time'] = time();

                // 记录总额 
                $sum = bcadd($sum,$spread_data[$key]['money'],2);
                unset($spread_data[$key]['total']);
            }
            // 四舍五入可能差几分 放到第一个账户调到精确
            $spread_data[0]['money'] += bcsub($charge,$sum,2);
            array_walk($spread_data,function($item){
                ChannelAccountAdjust::insert($item);
            });


            $this->spread_data = array_merge_recursive($this->spread_data, $spread_data);

            // 计算完成
            MyModel::where('run_id',$run_id)->update(['run_status'=>3,'update_at'=>time()]);

        } catch (Exception $e) {
            MyModel::where('run_id',$run_id)->update(['run_status'=>4,'remarks'=>'计算错误：'.$e->getMessage()]);
        }
    }

    // 合并客户各个运营商分摊
    protected function mergeCustomerSpread()
    {
        if ($this->spread_data) {
            $data = [];
            foreach ($this->spread_data as $key => $value) {
                $key = $value['date'].'_'.$value['customer_id'];
                if (!isset($data[$key])) {
                    $data[$key]['customer_id']=$value['customer_id'];
                    $data[$key]['money']=0.000000;
                    $data[$key]['date']=$value['date'];
                }
                $data[$key]['money']=bcadd($data[$key]['money'], $value['money'], 6);
            }
            foreach ($data as $index => $customerData) {
                $data[$index]['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($customerData['customer_id']);
            }

            $this->writeCsv('', $data);
        }
    }

    protected function writeCsv($file='', $data=[])
    {
        $f = fopen(dir(__FILE__).'/b.csv', 'w');

        foreach ($data as $key => $value) {
            fputcsv($f, $value);
        }
        fclose($f);
    }
}
