<?php

namespace App\Console\Commands;


use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\MongoStatis;
use App\Models\Product;
use Illuminate\Console\Command;

class CreateTestStatisticsData extends Command
{
    protected $signature = 'bill:create-statistics {--P|product-id=default} {--C|customer-id=default} {--A|account-id=default} {--S|start-date=default} {--E|end-date=default}';

    private $node = [
        'shenzhen',
        'beijing'
    ];

    public function handle()
    {
        //获取需要生成统计的数据 ['apikey' => '', 'product_id' => '']
        $storeProductData = $this->getStoreProductData();

        //获取时间区间
        $amountDateData = $this->getAmountDateArea();

        //获取产品数据
        $productData = $this->getProductConfig();

        //获取节点配置
        $nodeData = $this->node;

        $total = count($storeProductData) * count($amountDateData) * count($nodeData);
        $bar   = $this->output->createProgressBar($total);
        //生成数据
        foreach ($storeProductData as $storeProductItem) {
            foreach ($amountDateData as $amountDateItem) {
                foreach ($nodeData as $nodeItem) {
                    $product_id = $storeProductItem['product_id'];
                    if (!array_key_exists($product_id, $productData)) {
                        $bar->advance();
                        continue;
                    }
                    $apikey      = $storeProductItem['apikey'];
                    $amount_date = $amountDateItem;
                    $update_at   = 0;
                    $updated_at  = 0;
                    $create_at   = time();
                    $stat_config = $productData[$product_id];
                    $node_area   = $nodeItem;
                    $stat_data   = array_map(function ($item) {
                        return rand(10, 345);
                    }, array_flip($stat_config));
                    $data        = compact('product_id', 'apikey', 'amount_date', 'update_at', 'updated_at', 'create_at', 'stat_data', 'node_area');
                    try {
                        $res = MongoStatis::insert($data);
                    } catch (\Exception $exction) {

                    }
                    $bar->advance();
                }
            }
        }

        $bar->finish();
        $this->output->newLine();
        $this->output->writeln('已完成');
    }

    /**
     * 获取所有开通了的产品数据
     *
     * @access private
     *
     * @return array
     **/
    private function getStoreProductData()
    {
        //获取apikey
        $data = $this->getApikeyAndAccountId();
        if (empty($data)) {
            $this->output->error('未命中任何账号');
        }

        $accountId = array_keys($data);
        //获取账号——产品表中的数据
        $accountProductModel = new AccountProduct();
        $accountProductData  = $accountProductModel->select('account_id', 'product_id')
            ->whereIn('account_id', $accountId)
            ->where('product_id', '<>', '301')
            ->where('product_id', '<>', '302')
            ->get()
            ->toArray();

        return array_map(function ($item) use ($data) {
            return [
                'apikey'     => $data[$item['account_id']],
                'product_id' => $item['product_id']
            ];
        }, $accountProductData);
    }

    /**
     * 获取产品的配置项
     *
     * @access private
     *
     * @return array
     **/
    private function getProductConfig()
    {
        //产品
        $product_id = $this->option('product-id') == 'default' ? null : $this->option('product-id');

        //获取所有产品的统计配置
        $productModel  = new Product();
        $productConfig = $productModel->select('product_id', 'stat_config')
            ->where(function ($query) use ($product_id) {
                if ($product_id) {
                    $query->whereIn('product_id', explode(',', $product_id));
                }
            })
            ->whereNotNull('stat_config')
            ->where('stat_config', '<>', '')
            ->pluck('stat_config', 'product_id')
            ->toArray();

        return array_map(function ($item) {
            $config = json_decode($item, true);
            return array_column($config, 'name');
        }, $productConfig);
    }

    /**
     * 获取生成数据的时间区间
     *
     * @access private
     *
     * @return array
     **/
    private function getAmountDateArea()
    {
        //时间区间
        $start_time = $this->option('start-date') == 'default' ? strtotime('first day of last month midnight') : strtotime($this->option('start-date') . ' midnight');
        $end_time   = $this->option('end-date') == 'default' ? strtotime('midnight') : strtotime($this->option('end-date') . ' midnight');
        if ($start_time > $end_time) {
            return [];
        }

        $result = [];
        for ($i = $start_time; $i <= $end_time; $i += 86400) {
            $result[] = date('Ymd', $i);
        }
        return $result;
    }

    /**
     * 根据选项获取apikey
     *
     * @access private
     *
     * @return array
     **/
    private function getApikeyAndAccountId()
    {
        //如果为default，则为所有的客户、账号
        $customerId = $this->option('customer-id') == 'default' ? null : $this->option('customer-id');
        $accountId  = $this->option('account-id') == 'default' ? null : $this->option('account-id');

        //账号的查询条件
        $accountId_array = null;
        //从Mysql中获取此客户的所有账号
        $accountModel = new Account();
        if ($customerId) {
            $accountId_array = $accountModel->where('is_delete', '=', 0)
                ->whereIn('customer_id', explode(',', $customerId))
                ->pluck('account_id')
                ->toArray();
        }

        //从账号中获取未被删除的账号ID
        if ($accountId) {
            $accountWhere = ['account_id', 'in', explode(',', $accountId)];
        }
        $accountWhere[] = ['is_delete', 'eq', 0];
        return $accountModel->where(function ($query) use ($accountId, $accountId_array) {
            if ($accountId) {
                $query = $query->whereIn('account_id', explode(',', $accountId));
            }
            if ($accountId_array) {
                $query->whereIn('account_id', $accountId_array);
            }
        })
            ->where('is_delete', 'eq', 0)
            ->pluck('apikey', 'account_id')
            ->toArray();
    }
}