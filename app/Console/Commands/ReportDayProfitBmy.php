<?php

namespace App\Console\Commands;

use App\Models\BillConfig;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\EmailConfig;
use App\Models\MongoBillDay;
use App\Models\MongoBillMonth;
use App\Models\MongoTransferBill;
use App\Models\MongoUpstreamBill;
use App\Models\Product;
use App\Models\Upstream;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\ExcelTrait;
use App\TraitUpgrade\MailTrait;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

//利润权责日报表(技术二部)
class ReportDayProfitBmy extends Command
{
	use CurlTrait;
	use WechatExceptionTrait;
	use MailTrait;
	
	protected $name = '利润权责日报表(技术二部)';
	//命令名称
	public $signature = 'reportDay:profit_bmy
    {--date= : 日期, 格式Ymd，默认昨日}
    {--address= : 设置收件人，如果设置了此项，系统将不会读取数据表中的配置项。多个收件人以,隔开}
    ';
	/** @var string 命令提示 */
	public $description = '收入、成本、利润日报表邮件发送(技术二部)';
	
	//允许查看的产品ID
	protected $productIds = [
		401,
		200,
		801,
	];
	protected $scene      = 'report_day_profit_bmy';
	
	
	//当日、本月累计数据使用的是日账单，本年累计非本月数据使用月账单
	//当日时间，即发送的账单日期
	protected $day;        //日期
	protected $address;        //收件人地址
	protected $month;       //所处的月份
	protected $monthFirstDay;   //当月第一天
	protected $yearFirstDay;   //本年第一天
	
	protected $incomeData;      //收入数据
	protected $costData;        //成本数据
	protected $bxfShortCostData;    //邦信分快捷版成本数据
	protected $specialData;     //特殊金额数据
	
	//排序字段
	protected $sortBy = 'this_day_income_money';
	
	//客户名称映射表
	protected $cacheCustomerMapping = null;
	
	
	public function __construct()
	{
		parent::__construct();
		
		//增加200子产品ID
		$this->fillProductIds();
		
	}
	
	/**
	 * 补充产品ID参数(615)
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/6/23 10:16
	 *
	 * @return void
	 **/
	protected function fillProductIds()
	{
		//增加615子产品ID
		$this->productIds = array_merge($this->productIds, Product::where('father_id', 200)
																  ->pluck('product_id')
																  ->toArray());
	}
	
	/**
	 * @throws \Exception
	 */
	public function handle()
	{
		#初始化设置
		$res = $this->setAttribute();
		
		#发送邮件
		if ($res) {
			$this->send();
		}
	}
	
	/**
	 * 设置参数
	 *
	 * @access protected
	 *
	 * @return boolean
	 **/
	protected function setAttribute()
	{
		
		//设置所需要的日期
		$this->day = empty($this->option('date')) ? date('Ymd', strtotime('-1 days')) : $this->option('date');
		
		if ($this->day >= date('Ymd') || !preg_match('/^\d{8}$/', $this->day)) {
			$this->output->error('日期格式不正确');
			
			return false;
		}
		
		if (!empty($this->option('address'))) {
			$this->address = $this->option('address');
			if (!filter_var($this->address, FILTER_VALIDATE_EMAIL)) {
				$this->output->error('邮箱地址格式不正确');
				
				return false;
			}
		}
		
		
		$this->month         = date('Ym', strtotime($this->day));
		$this->monthFirstDay = date('Ymd', strtotime('first day of this month', strtotime($this->day)));
		$this->yearFirstDay  = date('Y', strtotime($this->day)) . '0101';
		
		//获取每个账号、每个产品维度的成本统计
		$this->costData = $this->getAggregateCost();
		
		//获取每个账号、每个产品维度的收入统计
		$this->incomeData = $this->getAggregateIncome();
		
		//获取特殊费用数据
		//$this->specialData = $this->getSpecial();
		//邦信分快捷版成本数据
		
		return true;
	}
	
	/**
	 * 获取本年内的特殊费用数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getSpecial()
	{
		$customerMapping = $this->getCustomerMapping();
		
		return CustomerExpend::where('profile_show_date', '>=', date('Y-m-d', strtotime($this->yearFirstDay)))
							 ->where('profile_show_date', '<=', date('Y-m-d', strtotime($this->day)))
							 ->whereIn('product_id', $this->productIds)
							 ->orderBy('start_date')
							 ->get()
							 ->map(function ($item) use ($customerMapping) {
								 $customer_id = $item['customer_id'];
								 $account_id  = $item['customer_id'];
								 $product_id  = $item['product_id'];
								 $date        = intval(date('Ymd', strtotime($item['profile_show_date'])));
								 $money       = 0;
								 $fee_number  = 0;
								 if ($item['type'] == 1) {
									 $fee_number -= $item['fee_number'];
									 $money      -= $item['money'];
								 } else if ($item['type'] == 2) {
									 $fee_number += $item['fee_number'];
									 $money      += $item['money'];
								 }
			
								 return compact('customer_id', 'account_id', 'product_id', 'date', 'money', 'fee_number');
							 })
							 ->toArray();
	}
	
	/**
	 * 获取成本统计
	 *
	 * @access protected
	 *
	 * @return array    二维数组，每个单元的数据格式为
	 * [customer_id, product_id, account_id, day_money, day_number, month_money, month_number, year_money, month_number]
	 **/
	protected function getAggregateCost()
	{
		$result = [];
		$base   = [
			'customer_id'  => '',
			'product_id'   => '',
			'account_id'   => '',
			'day_money'    => 0,
			'day_number'   => 0,
			'month_money'  => 0,
			'month_number' => 0,
			'year_money'   => 0,
			'year_number'  => 0,
		];
		
		$dayData = MongoUpstreamBill::query()
									->raw(function ($collection) {
										$aggregate               = [];
										$aggregate[]['$match']   = [
											'date'       => intval($this->day),
											'product_id' => ['$in' => $this->productIds],
										];
										$aggregate[]['$group']   = [
											'_id'         => [
												'account_id' => '$account_id',
												'product_id' => '$product_id',
											],
											'money'       => ['$sum' => '$money',],
											'fee_number'  => ['$sum' => '$fee_number',],
											'customer_id' => ['$first' => '$customer_id',],
											'account_id'  => ['$first' => '$account_id',],
											'product_id'  => ['$first' => '$product_id',],
										];
										$aggregate[]['$project'] = [
											'_id'         => 0,
											'money'       => 1,
											'fee_number'  => 1,
											'customer_id' => 1,
											'product_id'  => 1,
											'account_id'  => 1,
										];
			
										return $collection->aggregate($aggregate);
									})
									->toArray();
		
		$monthData = MongoUpstreamBill::query()
									  ->raw(function ($collection) {
										  $aggregate               = [];
										  $aggregate[]['$match']   = [
											  'date'       => [
												  '$gte' => intval($this->monthFirstDay),
												  '$lte' => intval($this->day),
											  ],
											  'product_id' => ['$in' => $this->productIds],
										  ];
										  $aggregate[]['$group']   = [
											  '_id'         => [
												  'account_id' => '$account_id',
												  'product_id' => '$product_id',
											  ],
											  'money'       => ['$sum' => '$money',],
											  'fee_number'  => ['$sum' => '$fee_number',],
											  'customer_id' => ['$first' => '$customer_id',],
											  'account_id'  => ['$first' => '$account_id',],
											  'product_id'  => ['$first' => '$product_id',],
										  ];
										  $aggregate[]['$project'] = [
											  '_id'         => 0,
											  'money'       => 1,
											  'fee_number'  => 1,
											  'customer_id' => 1,
											  'product_id'  => 1,
											  'account_id'  => 1,
										  ];
			
										  return $collection->aggregate($aggregate);
									  })
									  ->toArray();
		
		$yearData = MongoUpstreamBill::query()
									 ->raw(function ($collection) {
										 $aggregate               = [];
										 $aggregate[]['$match']   = [
											 'date'       => [
												 '$gte' => intval($this->yearFirstDay),
												 '$lte' => intval($this->day),
											 ],
											 'product_id' => ['$in' => $this->productIds],
										 ];
										 $aggregate[]['$group']   = [
											 '_id'         => [
												 'account_id' => '$account_id',
												 'product_id' => '$product_id',
											 ],
											 'money'       => ['$sum' => '$money',],
											 'fee_number'  => ['$sum' => '$fee_number',],
											 'customer_id' => ['$first' => '$customer_id',],
											 'account_id'  => ['$first' => '$account_id',],
											 'product_id'  => ['$first' => '$product_id',],
										 ];
										 $aggregate[]['$project'] = [
											 '_id'         => 0,
											 'money'       => 1,
											 'fee_number'  => 1,
											 'customer_id' => 1,
											 'product_id'  => 1,
											 'account_id'  => 1,
										 ];
			
										 return $collection->aggregate($aggregate);
									 })
									 ->toArray();
		
		
		//日、月、年成本遍历数据，汇总到result变量中
		array_walk($dayData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['day_money']  = $money;
			$result[$key]['day_number'] = $number;
		});
		array_walk($monthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
		});
		array_walk($yearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = $money;
			$result[$key]['year_number'] = $number;
		});
		
		return array_values($result);
	}
	
	/**
	 * 获取邦信分快捷版的成本
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/6/9 10:03
	 *
	 * @return array
	 **/
	protected function getBxfShortCost()
	{
		$result = [];
		$base   = [
			'customer_id'  => '',
			'product_id'   => '',
			'account_id'   => '',
			'day_money'    => 0,
			'day_number'   => 0,
			'month_money'  => 0,
			'month_number' => 0,
			'year_money'   => 0,
			'year_number'  => 0,
		];
		
		
		//评分类字段的成本渠道
		$store_channel = Upstream::where('product_id', 210)
								 ->where('type', 1)
								 ->pluck('channel')
								 ->toArray();
		//统计类字段的成本渠道
		$normal_channel = Upstream::where('product_id', 210)
								  ->where('type', 2)
								  ->pluck('channel')
								  ->toArray();
		
		//mongo统计查询条件
		$aggregate = [
			['$match' => ['date' => '', 'product_id' => 210, 'channel' => '',],],
			[
				'$group' => [
					'_id'         => ['account_id' => '$account_id', 'product_id' => '$product_id',],
					'money'       => ['$sum' => '$money',],
					'fee_number'  => ['$sum' => '$fee_number',],
					'customer_id' => ['$first' => '$customer_id',],
					'account_id'  => ['$first' => '$account_id',],
					'product_id'  => ['$first' => '$product_id',],
				],
			],
			[
				'$project' => [
					'_id'         => 0,
					'money'       => 1,
					'fee_number'  => 1,
					'customer_id' => 1,
					'product_id'  => 1,
					'account_id'  => 1,
				],
			],
		];
		
		//评分类字段日查询条件
		$aggregate[0]['$match']['date']    = intval($this->day);
		$aggregate[0]['$match']['channel'] = ['$in' => $store_channel];
		$storeDayAggregate                 = $aggregate;
		//统计类字段日查询条件
		$aggregate[0]['$match']['channel'] = ['$in' => $normal_channel];
		$normalDayAggregate                = $aggregate;
		
		//月查询条件
		$aggregate[0]['$match']['date']    = ['$gte' => intval($this->monthFirstDay), '$lte' => intval($this->day),];
		$aggregate[0]['$match']['channel'] = ['$in' => $store_channel];
		$storeMonthAggregate               = $aggregate;
		$aggregate[0]['$match']['channel'] = ['$in' => $normal_channel];
		$normalMonthAggregate              = $aggregate;
		
		//年查询条件
		$aggregate[0]['$match']['date']    = ['$gte' => intval($this->yearFirstDay), '$lte' => intval($this->day),];
		$aggregate[0]['$match']['channel'] = ['$in' => $store_channel];
		$storeYearAggregate                = $aggregate;
		$aggregate[0]['$match']['channel'] = ['$in' => $normal_channel];
		$normalYearAggregate               = $aggregate;
		
		
		//日数据
		$storeDayData  = MongoUpstreamBill::query()
										  ->raw(function ($collection) use ($storeDayAggregate) {
											  return $collection->aggregate($storeDayAggregate);
										  })
										  ->toArray();
		$normalDayData = MongoUpstreamBill::query()
										  ->raw(function ($collection) use ($normalDayAggregate) {
											  return $collection->aggregate($normalDayAggregate);
										  })
										  ->toArray();
		
		//月数据
		$storeMonthData  = MongoUpstreamBill::query()
											->raw(function ($collection) use ($storeMonthAggregate) {
												return $collection->aggregate($storeMonthAggregate);
											})
											->toArray();
		$normalMonthData = MongoUpstreamBill::query()
											->raw(function ($collection) use ($normalMonthAggregate) {
												return $collection->aggregate($normalMonthAggregate);
											})
											->toArray();
		
		//年数据
		$storeYearData  = MongoUpstreamBill::query()
										   ->raw(function ($collection) use ($storeYearAggregate) {
											   return $collection->aggregate($storeYearAggregate);
										   })
										   ->toArray();
		$normalYearData = MongoUpstreamBill::query()
										   ->raw(function ($collection) use ($normalYearAggregate) {
											   return $collection->aggregate($normalYearAggregate);
										   })
										   ->toArray();
		
		
		//日、月、年成本遍历数据，汇总到result变量中
		array_walk($storeDayData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 251;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['day_money']  = $money;
			$result[$key]['day_number'] = $number;
		});
		array_walk($normalDayData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 241;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['day_money']  = $money;
			$result[$key]['day_number'] = $number;
		});
		array_walk($storeMonthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 251;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
		});
		array_walk($normalMonthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 241;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
		});
		array_walk($storeYearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 251;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = $money;
			$result[$key]['year_number'] = $number;
		});
		array_walk($normalYearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 241;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = $money;
			$result[$key]['year_number'] = $number;
		});
		
		return array_values($result);
	}
	
	
	/**
	 * 获取收入统计
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getAggregateIncome()
	{
		$result  = [];
		$base    = [
			'customer_id'  => '',
			'product_id'   => '',
			'account_id'   => '',
			'day_money'    => 0,
			'day_number'   => 0,
			'month_money'  => 0,
			'month_number' => 0,
			'year_money'   => 0,
			'year_number'  => 0,
		];
		$dayData = MongoBillDay::query()
							   ->raw(function ($collection) {
								   $aggregate               = [];
								   $aggregate[]['$match']   = [
									   'date'       => $this->day,
									   'product_id' => ['$in' => $this->productIds],
								   ];
								   $aggregate[]['$group']   = [
									   '_id'         => [
										   'account_id' => '$account_id',
										   'product_id' => '$product_id',
									   ],
									   'money'       => ['$sum' => '$money',],
									   'fee_number'  => ['$sum' => '$section_invoked_number',],
									   'customer_id' => ['$first' => '$customer_id',],
									   'account_id'  => ['$first' => '$account_id',],
									   'product_id'  => ['$first' => '$product_id',],
								   ];
								   $aggregate[]['$project'] = [
									   '_id'         => 0,
									   'money'       => 1,
									   'fee_number'  => 1,
									   'customer_id' => 1,
									   'product_id'  => 1,
									   'account_id'  => 1,
								   ];
			
								   return $collection->aggregate($aggregate);
							   })
							   ->toArray();
		
		$monthData = MongoBillDay::query()
								 ->raw(function ($collection) {
									 $aggregate               = [];
									 $aggregate[]['$match']   = [
										 'date'       => [
											 '$lte' => $this->day,
											 '$gte' => $this->monthFirstDay,
										 ],
										 'product_id' => ['$in' => $this->productIds],
									 ];
									 $aggregate[]['$group']   = [
										 '_id'         => [
											 'account_id' => '$account_id',
											 'product_id' => '$product_id',
										 ],
										 'money'       => ['$sum' => '$money',],
										 'fee_number'  => ['$sum' => '$section_invoked_number',],
										 'customer_id' => ['$first' => '$customer_id',],
										 'account_id'  => ['$first' => '$account_id',],
										 'product_id'  => ['$first' => '$product_id',],
									 ];
									 $aggregate[]['$project'] = [
										 '_id'         => 0,
										 'money'       => 1,
										 'fee_number'  => 1,
										 'customer_id' => 1,
										 'product_id'  => 1,
										 'account_id'  => 1,
									 ];
			
									 return $collection->aggregate($aggregate);
								 })
								 ->toArray();
		
		//年累计收入查询月账单
		$yearData = MongoBillMonth::query()
								  ->raw(function ($collection) {
									  $aggregate               = [];
									  $aggregate[]['$match']   = [
										  'month'      => [
											  '$lte' => $this->month,
											  '$gte' => date('Ym', strtotime($this->yearFirstDay)),
										  ],
										  'product_id' => ['$in' => $this->productIds],
									  ];
									  $aggregate[]['$group']   = [
										  '_id'         => [
											  'account_id' => '$account_id',
											  'product_id' => '$product_id',
										  ],
										  'money'       => ['$sum' => '$money',],
										  'fee_number'  => ['$sum' => '$section_invoked_number',],
										  'customer_id' => ['$first' => '$customer_id',],
										  'account_id'  => ['$first' => '$account_id',],
										  'product_id'  => ['$first' => '$product_id',],
									  ];
									  $aggregate[]['$project'] = [
										  '_id'         => 0,
										  'money'       => 1,
										  'fee_number'  => 1,
										  'customer_id' => 1,
										  'product_id'  => 1,
										  'account_id'  => 1,
									  ];
			
									  return $collection->aggregate($aggregate);
								  })
								  ->toArray();
		
		//日、月、年成本遍历数据，汇总到result变量中
		array_walk($dayData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['day_money']  = $money;
			$result[$key]['day_number'] = $number;
		});
		
		array_walk($monthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
			$result[$key]['year_money']   = $money;
			$result[$key]['year_number']  = $number;
		});
		
		array_walk($yearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = bcadd($result[$key]['year_money'], $money, 6);
			$result[$key]['year_number'] = bcadd($result[$key]['year_number'], $number);
		});
		
		//账单转移数据
		$transferData = $this->getTransfer();
		
		//融合账单转移数据
		array_walk($transferData, function ($item) use (&$result, $base) {
			$account_id          = $item['account_id'];
			$customer_id         = $item['customer_id'];
			$product_id          = $item['product_id'];
			$original_product_id = $item['original_product_id'];
			$key                 = $account_id . '_' . $product_id;
			$original_key        = $account_id . '_' . $original_product_id;
			//邦秒验减少转移数据
			if (!array_key_exists($original_key, $result)) {
				$result[$original_key]                = $base;
				$result[$original_key]['account_id']  = $account_id;
				$result[$original_key]['customer_id'] = $customer_id;
				$result[$original_key]['product_id']  = $original_product_id;
			}
			$result[$original_key]['day_number']   = bcsub($result[$original_key]['day_number'], $item['day_number']);
			$result[$original_key]['month_number'] = bcsub($result[$original_key]['month_number'], $item['month_number']);
			$result[$original_key]['year_number']  = bcsub($result[$original_key]['year_number'], $item['year_number']);
			$result[$original_key]['day_money']    = bcsub($result[$original_key]['day_money'], $item['day_money'], 4);
			$result[$original_key]['month_money']  = bcsub($result[$original_key]['month_money'], $item['month_money'], 4);
			$result[$original_key]['year_money']   = bcsub($result[$original_key]['year_money'], $item['year_money'], 4);
		});
		
		//融合特殊收入数据
		$special = $this->getSpecial();
		array_walk($special, function ($item) use (&$result, $base) {
			$account_id  = $item['account_id'];
			$customer_id = $item['customer_id'];
			$product_id  = $item['product_id'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['account_id']  = $account_id;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
			}
			$date = date('Ymd', strtotime($item['date']));
			if ($date == $this->day) {
				$result[$key]['day_number']   = bcadd($item['fee_number'], $result[$key]['day_number']);
				$result[$key]['day_money']    = bcadd($item['money'], $result[$key]['day_money'], 4);
				$result[$key]['month_number'] = bcadd($item['fee_number'], $result[$key]['month_number']);
				$result[$key]['month_money']  = bcadd($item['money'], $result[$key]['month_money'], 4);
				$result[$key]['year_number']  = bcadd($item['fee_number'], $result[$key]['year_number']);
				$result[$key]['year_money']   = bcadd($item['money'], $result[$key]['year_money'], 4);
			} else if ($date >= $this->monthFirstDay && $date < $this->day) {
				$result[$key]['month_number'] = bcadd($item['fee_number'], $result[$key]['month_number']);
				$result[$key]['month_money']  = bcadd($item['money'], $result[$key]['month_money'], 4);
				$result[$key]['year_number']  = bcadd($item['fee_number'], $result[$key]['year_number']);
				$result[$key]['year_money']   = bcadd($item['money'], $result[$key]['year_money'], 4);
			} else if ($date >= $this->yearFirstDay && $date < $this->day) {
				$result[$key]['year_number'] = bcadd($item['fee_number'], $result[$key]['year_number']);
				$result[$key]['year_money']  = bcadd($item['money'], $result[$key]['year_money'], 4);
			}
		});
		
		return array_values($result);
	}
	
	/**
	 * 获取每个产品维度的转移账单数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/9 10:08
	 *
	 * @return array
	 **/
	protected function getTransfer()
	{
		$base   = [
			'original_product_id' => '',
			'product_id'          => '',
			'account_id'          => '',
			'customer_id'         => '',
			'day_money'           => 0,
			'day_number'          => 0,
			'month_money'         => 0,
			'month_number'        => 0,
			'year_money'          => 0,
			'year_number'         => 0,
		];
		$result = [];
		
		//当日统计
		MongoTransferBill::query()
						 ->raw(function ($collection) {
							 $aggregate = [
								 [
									 '$match' => [
										 'date' => intval($this->day),
									 ],
								 ],
								 [
									 '$group' => [
										 '_id'                 => [
											 'original_product_id' => '$original_product_id',
											 'product_id'          => '$product_id',
											 'account_id'          => '$account_id',
										 ],
										 'money'               => ['$sum' => '$money'],
										 'fee_number'          => ['$sum' => '$fee_number'],
										 'original_product_id' => [
											 '$first' => '$original_product_id',
										 ],
										 'product_id'          => [
											 '$first' => '$product_id',
										 ],
										 'customer_id'         => [
											 '$first' => '$customer_id',
										 ],
										 'account_id'          => [
											 '$first' => '$account_id',
										 ],
									 ],
								 ],
								 [
									 '$project' => [
										 '_id' => 0,
									 ],
								 ],
							 ];
			
							 return $collection->aggregate($aggregate);
						 })
						 ->map(function ($item) use (&$result, $base) {
							 $key = $item->original_product_id . '_' . $item->product_id . '_' . $item->account_id;
							 if (!array_key_exists($key, $result)) {
								 $result[$key]                        = $base;
								 $result[$key]['product_id']          = $item->product_id;
								 $result[$key]['original_product_id'] = $item->original_product_id;
								 $result[$key]['customer_id']         = $item->customer_id;
								 $result[$key]['account_id']          = $item->account_id;
							 }
							 $result[$key]['day_number'] = bcadd($item['fee_number'], $result[$key]['day_number']);
							 $result[$key]['day_money']  = bcadd($item['money'], $result[$key]['day_money'], 4);
						 });
		//本月统计
		MongoTransferBill::query()
						 ->raw(function ($collection) {
							 $aggregate = [
								 [
									 '$match' => [
										 'date' => [
											 '$gte' => intval($this->monthFirstDay),
											 '$lte' => intval($this->day),
										 ],
									 ],
								 ],
								 [
									 '$group' => [
										 '_id'                 => [
											 'original_product_id' => '$original_product_id',
											 'product_id'          => '$product_id',
											 'customer_id'         => '$customer_id',
											 'account_id'          => '$account_id',
										 ],
										 'money'               => ['$sum' => '$money'],
										 'fee_number'          => ['$sum' => '$fee_number'],
										 'original_product_id' => [
											 '$first' => '$original_product_id',
										 ],
										 'product_id'          => [
											 '$first' => '$product_id',
										 ],
										 'customer_id'         => [
											 '$first' => '$customer_id',
										 ],
										 'account_id'          => [
											 '$first' => '$account_id',
										 ],
									 ],
								 ],
								 [
									 '$project' => [
										 '_id' => 0,
									 ],
								 ],
							 ];
			
							 return $collection->aggregate($aggregate);
						 })
						 ->map(function ($item) use (&$result, $base) {
							 $key = $item->original_product_id . '_' . $item->product_id . '_' . $item->account_id;
							 if (!array_key_exists($key, $result)) {
								 $result[$key]                        = $base;
								 $result[$key]['product_id']          = $item->product_id;
								 $result[$key]['original_product_id'] = $item->original_product_id;
								 $result[$key]['customer_id']         = $item->customer_id;
								 $result[$key]['account_id']          = $item->account_id;
							 }
							 $result[$key]['month_number'] = bcadd($item['fee_number'], $result[$key]['month_number']);
							 $result[$key]['month_money']  = bcadd($item['money'], $result[$key]['month_money'], 4);
						 });
		
		//当年统计
		MongoTransferBill::query()
						 ->raw(function ($collection) {
							 $aggregate = [
								 [
									 '$match' => [
										 'date' => [
											 '$gte' => intval($this->yearFirstDay),
											 '$lte' => intval($this->day),
										 ],
									 ],
								 ],
								 [
									 '$group' => [
										 '_id'                 => [
											 'original_product_id' => '$original_product_id',
											 'product_id'          => '$product_id',
											 'customer_id'         => '$customer_id',
											 'account_id'          => '$account_id',
										 ],
										 'money'               => ['$sum' => '$money'],
										 'fee_number'          => ['$sum' => '$fee_number'],
										 'original_product_id' => [
											 '$first' => '$original_product_id',
										 ],
										 'product_id'          => [
											 '$first' => '$product_id',
										 ],
										 'customer_id'         => [
											 '$first' => '$customer_id',
										 ],
										 'account_id'          => [
											 '$first' => '$account_id',
										 ],
									 ],
								 ],
								 [
									 '$project' => [
										 '_id' => 0,
									 ],
								 ],
							 ];
			
							 return $collection->aggregate($aggregate);
						 })
						 ->map(function ($item) use (&$result, $base) {
							 $key = $item->original_product_id . '_' . $item->product_id . '_' . $item->account_id;
							 if (!array_key_exists($key, $result)) {
								 $result[$key]                        = $base;
								 $result[$key]['product_id']          = $item->product_id;
								 $result[$key]['original_product_id'] = $item->original_product_id;
								 $result[$key]['customer_id']         = $item->customer_id;
								 $result[$key]['account_id']          = $item->account_id;
							 }
							 $result[$key]['year_number'] = bcadd($item['fee_number'], $result[$key]['year_number']);
							 $result[$key]['year_money']  = bcadd($item['money'], $result[$key]['year_money'], 4);
						 });
		
		return array_values($result);
	}
	
	
	/**
	 * 发送日账单
	 *
	 * @access protected
	 *
	 * @return void
	 **/
	protected function send()
	{
		try {
			//四 计算产品维度的数据
			//1.按产品、客户维度获取当月收入、当日收入
			
			//2.根据相应的规则，将各产品的收入进行汇总，
			
			//3.分别获取邦信分快捷版、邦秒验、邦企查的当日成本、当月累计成本，整合数据，并计算毛利
			
			//五 计算客户维度的数据
			
			//1.按客户维度获取当月收入、当日收入、当月成本、当日成本，并计算毛利
			$data = [];
			
			//四
			$data['product'] = $this->getProductData();
			
			//halt($data['product']['data']['bxf_short']);
			
			//五
			$data['customer'] = $this->getCustomerData();
			
			
			//生成excel
			$filename = $this->createExcelFile($data);
			
			//生成HTML
			$title = date('m月d日', strtotime($this->day)) . '权责收入及毛利数据统计（技术二部）';
			$html  = $this->createHtml($data, $title);
			
			
			//获取邮件收件人
			list($recipients, $cc) = $this->getEmail();
			
			$res = $this->setSubject($title)
						->setRecipients($recipients)
						->setCC($cc)
						->setAttachment([
							[
								'filepath' => $filename,
								'name'     => "报表明细.xlsx",
							],
						])
						->sendMail($html);
			
			if (!$res) {
				throw new \Exception("邮件发送失败");
			}
			$this->output->success("邮件发送成功");
		} catch (\Exception $exception) {
			$this->wechat("【{$this->name}】任务执行失败，原因是：{$exception->getMessage()} as Line:{$exception->getLine()}");
			$this->output->error($exception->getMessage() . ' as Line:' . $exception->getLine());
		}
		
	}
	
	/**
	 * 获取邮件收件人，抄送人
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/6/22 16:06
	 *
	 * @return array
	 **/
	protected function getEmail()
	{
		if (!empty($this->address)) {
			$recipients = array_map(function ($address) {
				list($name, $domain) = explode('@', $address);
				
				return compact('name', 'address');
			}, explode(',', $this->address));
			$cc         = [];
		} else {
			$emailConfig = (new EmailConfig())->getRecipientsAndCCByScene($this->scene);
			
			$recipients = $emailConfig['recipients'];
			$cc         = $emailConfig['cc'];
		}
		
		return [$recipients, $cc];
	}
	
	/**
	 * 生成HTML页面
	 *
	 * @access protected
	 *
	 * @param $data  array 数据
	 * @param $title string 邮件主题
	 *
	 * @return string 返回的是HTML内容
	 **/
	protected function createHtml($data, $title)
	{
		//生成产品维度的TABLE内容
		$productTable = $this->createProductTable($data['product']);
		//生成客户维度的TABLE内容
		$customerTable = $this->createCustomerTable($data['customer']);
		//特殊费用
		//$specialTable = $this->createSpecialTable($data['special']);
		
		return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <style>
        body {
            color : #333333;
        }
        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }
        h2 {
            width        : 98%;
            height       : 44px;
            line-height  : 44px;
            font-size    : 20px;
            font-weight  : bold;
            text-align   : center;
            margin       : 20px auto 2px;
            background   : rgba(229, 82, 45, 1);
            color        : #FFFFFF;
        }
        table {
            width       : 98%;
            border      : none;
            padding     : 0;
            margin      : 0 auto;
            font-size   : 14px;
            color       : #666666;
            border-left : 1px solid #CCCCCC;
            border-top  : 1px solid #CCCCCC;
        }
        tr {
            border-left : 1px solid #CCCCCC;
            border-top  : 1px solid #CCCCCC;
        }
        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            text-align    : center;
            padding       : 5px 0;
        }
        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
        }
        tr:nth-child(even) {
            background : #EEEEEE;
        }
        tr:hover {
            background : #CCCCCC;
        }
        .bold {
            font-weight : bold;
        }
        .fsz-16 {
            font-size : 15px;
        }
        .space{
            width  : 100%;
            height : 40px;
        }
    </style>
</head>
<body>
<h1>{$title}</h1>
<h2>产品维度权责报表</h2>
<table border="0" cellspacing="0" cellpadding="0">
    <tr class="fsz-16">
        <th>产品名称</th>
        <th>子产品名称</th>
        <th>收入计费用量</th>
        <th>当日权责收入</th>
        <th>当日权责成本</th>
        <th>当日权责毛利</th>
        <th>当月累计权责收入</th>
        <th>当月累计权责成本</th>
        <th>当月累计权责毛利</th>
        <th>本年累计权责收入</th>
        <th>本年累计权责成本</th>
        <th>本年累计权责毛利</th>
    </tr>
    {$productTable}
</table>
<h2>客户维度权责报表</h2>
<table border="0" cellspacing="0" cellpadding="0">
    <tr class="fsz-16">
        <th>客户ID</th>
        <th>客户名称</th>
        <th>收入计费用量</th>
        <th>当日权责收入</th>
        <th>当日权责成本</th>
        <th>当日权责毛利</th>
        <th>当月累计权责收入</th>
        <th>当月累计权责成本</th>
        <th>当月累计权责毛利</th>
        <th>本年累计权责收入</th>
        <th>本年累计权责成本</th>
        <th>本年累计权责毛利</th>
    </tr>
    {$customerTable}
</table>
<div class="space"></div>
</body>
</html>
HTML;
	}
	
	/**
	 * 特殊费用TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 特殊费用数据
	 *
	 * @return string
	 **/
	protected function createSpecialTable($data)
	{
		$html = '';
		array_walk($data, function ($item) use (&$html) {
			$html .= <<<TR
<tr>
    <td>{$item['customer_id']}</td>
    <td>{$item['customer_name']}</td>
    <td>{$item['type']}</td>
    <td>{$item['start_date']}</td>
    <td>{$item['name']}</td>
    <td>{$this->disposeMoney($item['money'])}</td>
    <td>{$item['product_name']}</td>
</tr>
TR;
		});
		
		return $html;
	}
	
	/**
	 * 客户维度权责报表TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 客户维度的数据
	 *
	 * @return string
	 **/
	protected function createCustomerTable($data)
	{
		$html  = '';
		$total = $data['total'];
		$html  .= <<<TR
<tr>
    <td class="bold" colspan="2">合计</td>
    <td>{$this->disposeNumber($total['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($total['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_profit_money'])}</td>
</tr>
TR;
		$data  = $data['data'];
		array_walk($data, function ($item) use (&$html) {
			$html .= <<<TR
<tr>
    <td>{$item['customer_id']}</td>
    <td>{$item['customer_name']}</td>
    <td>{$this->disposeNumber($item['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($item['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_profit_money'])}</td>
</tr>
TR;
		});
		
		return $html;
	}
	
	/**
	 * 产品维度权责报表TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 产品维度的数据
	 *
	 * @return string
	 **/
	protected function createProductTable($data)
	{
		$html  = '';
		$total = $data['total'];
		$html  .= <<<TR
<tr>
    <td class="bold" colspan="2">合计</td>
    <td>{$this->disposeNumber($total['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($total['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_profit_money'])}</td>
</tr>
TR;
		$data  = $data['data'];
		array_walk($data, function ($item) use (&$html) {
			if (!array_key_exists('children', $item)) {
				$html .= <<<TR
<tr>
    <td>{$item['name']}</td>
    <td>--</td>
    <td>{$this->disposeNumber($item['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($item['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_profit_money'])}</td>
</tr>
TR;
			} else {
				$rowspan = count($item['children']) + 1;
				$html    .= <<<TR
<tr>
        <td rowspan="{$rowspan}">{$item['name']}</td>
    <td>小计</td>
    <td>{$this->disposeNumber($item['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($item['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_profit_money'])}</td>
</tr>
TR;
				array_walk($item['children'], function ($childreItem) use (&$html) {
					$html .= <<<TR
<tr>
    <td>{$childreItem['name']}</td>
    <td>{$this->disposeNumber($childreItem['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($childreItem['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_year_profit_money'])}</td>
</tr>
TR;
				});
				
			}
		});
		
		return $html;
	}
	
	/**
	 * 生成商务维度的统计TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 统计数据
	 *
	 * @return string
	 **/
	protected function createSalesmanTable($data)
	{
		$html  = '';
		$total = $data['total'];
		$html  .= <<<TR
<tr>
    <td class="bold" colspan="2">合计</td>
    <td>{$this->disposeNumber($total['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($total['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_profit_money'])}</td>
</tr>
TR;
		$data  = $data['data'];
		array_walk($data, function ($item) use (&$html) {
			$itemTotal = $item['total'];
			$itemData  = $item['data'];
			$rowspan   = count($itemData) + 1;
			$html      .= <<<TR
<tr>
    <td rowspan="{$rowspan}">{$item['name']}</td>
    <td class="bold">小计</td>
    <td>{$this->disposeNumber($itemTotal['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_year_profit_money'])}</td>
</tr>
TR;
			array_walk($itemData, function ($salesmanItem) use (&$html) {
				$html .= <<<TR
<tr>
    <td>{$salesmanItem['name']}</td>
    <td>{$this->disposeNumber($salesmanItem['this_day_income_number'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_day_income_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_day_cost_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_day_profit_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_year_profit_money'])}</td>
</tr>
TR;
			});
		});
		
		return $html;
	}
	
	
	/**
	 * 生成excel
	 *
	 * @access protected
	 *
	 * @param $data array 数据
	 *
	 * @return string 返回的是文件的访问地址
	 **/
	protected function createExcelFile($data)
	{
		#初始化PHPExcel对象
		$phpExcelPath = app()->basePath() . '/vendor/PHPExcel-1.8/Classes';
		require_once $phpExcelPath . '/PHPExcel.php';
		require_once $phpExcelPath . '/PHPExcel/IOFactory.php';
		//require_once $phpExcelPath . ' / PHPExcel / Style / Alignment . php';
		
		$objPHPExcel = new \PHPExcel();
		$objPHPExcel->getProperties()
					->setCreator("Maarten Balliauw")
					->setLastModifiedBy("Maarten Balliauw")
					->setTitle("Office 2007 XLSX Test Document")
					->setSubject("Office 2007 XLSX Test Document")
					->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.")
					->setKeywords("office 2007 openxml php")
					->setCategory("Test result file");
		
		#第二个sheet【产品维度】
		$objPHPExcel->createSheet();
		$this->setSheet2($objPHPExcel->setActiveSheetIndex(0), $data['product']);
		
		#第三个sheet【客户维度】
		$objPHPExcel->createSheet();
		$this->setSheet3($objPHPExcel->setActiveSheetIndex(1), $data['customer']);
		
		#第四个sheet【特殊费用】
		//$objPHPExcel->createSheet();
		//$this->setSheet4($objPHPExcel->setActiveSheetIndex(3), $data['special']);
		
		#保存excel文件
		$objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
		
		$dirpath = app()->basePath() . '/public/profit_report/';
		if (!is_dir($dirpath)) {
			@mkdir($dirpath, 755);
		}
		$filename = 'day_bmy_' . $this->day . '_' . time() . ' . xls';
		$objWriter->save($dirpath . $filename);
		
		return $dirpath . $filename;
	}
	
	/**
	 * 生成第四个sheet
	 *
	 * @access protected
	 *
	 * @param $sheet         \PHPExcel_Worksheet
	 * @param $specialData   array 特殊费用数据
	 *
	 * @return void
	 **/
	protected function setSheet4($sheet, $specialData)
	{
		$sheet->setTitle('特殊费用');
		##标题
		$sheet->setCellValue('A1', '客户ID')
			  ->setCellValue('B1', '客户名称')
			  ->setCellValue('C1', '费用类型')
			  ->setCellValue('D1', '计入月份')
			  ->setCellValue('E1', '名称')
			  ->setCellValue('F1', '金额')
			  ->setCellValue('G1', '备注（产品）');
		$sheet->getStyle('A1:M1')
			  ->getFont()
			  ->setBold(true);
		$sheet->getStyle('A1:M1')
			  ->getFont()
			  ->setSize(12);
		##客户
		$row = 2;
		array_walk($specialData, function ($item) use (&$row, $sheet) {
			$sheet->setCellValue('A' . $row, $item['customer_id'])
				  ->setCellValue('B' . $row, $item['customer_name'])
				  ->setCellValue('C' . $row, $item['type'])
				  ->setCellValue('D' . $row, $item['start_date'])
				  ->setCellValue('E' . $row, $item['name'])
				  ->setCellValue('F' . $row, $this->disposeMoney($item['money']))
				  ->setCellValue('G' . $row, $item['product_name']);
			$row++;
		});
		
		#设置全局样式
		$sheet->getStyle('A1:G' . $row)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('A1:G' . $row)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('A1:G' . $row)
			  ->getFont()
			  ->setName('宋体');
		$sheet->getColumnDimension('A')
			  ->setWidth(20);
		$sheet->getColumnDimension('B')
			  ->setWidth(24);
		$sheet->getColumnDimension('C')
			  ->setWidth(20);
		$sheet->getColumnDimension('D')
			  ->setWidth(20);
		$sheet->getColumnDimension('E')
			  ->setWidth(36);
		$sheet->getColumnDimension('F')
			  ->setWidth(18);
		$sheet->getColumnDimension('G')
			  ->setWidth(22);
		$sheet->getColumnDimension('H')
			  ->setWidth(22);
		for ($i = 1; $i < $row; $i++) {
			$sheet->getRowDimension($i)
				  ->setRowHeight(18);
		}
	}
	
	/**
	 * 生成第三个sheet
	 *
	 * @access protected
	 *
	 * @param $sheet         \PHPExcel_Worksheet
	 * @param $customerData  array 客户维度的统计数据
	 *
	 * @return void
	 **/
	protected function setSheet3($sheet, $customerData)
	{
		$sheet->setTitle('客户维度');
		##标题
		$sheet->setCellValue('A1', '客户ID')
			  ->setCellValue('B1', '客户名称')
			  ->setCellValue('C1', '收入计费用量')
			  ->setCellValue('D1', '当日权责收入')
			  ->setCellValue('E1', '当日权责成本')
			  ->setCellValue('F1', '当日权责毛利')
			  ->setCellValue('G1', '当月累计权责收入')
			  ->setCellValue('H1', '当月累计权责成本')
			  ->setCellValue('I1', '当月累计权责毛利')
			  ->setCellValue('J1', '本年累计权责收入')
			  ->setCellValue('K1', '本年累计权责成本')
			  ->setCellValue('L1', '本年累计权责毛利');
		$sheet->getStyle('A1:L1')
			  ->getFont()
			  ->setBold(true);
		$sheet->getStyle('A1:L1')
			  ->getFont()
			  ->setSize(12);
		##合计
		$sheet->mergeCells('A2:B2')
			  ->getStyle('A2')
			  ->getFont()
			  ->setBold(true);
		$total = $customerData['total'];
		$sheet->setCellValue('A2', '合计')
			  ->setCellValue('C2', $this->disposeNumber($total['this_day_income_number']))
			  ->setCellValue('D2', $this->disposeMoney($total['this_day_income_money']))
			  ->setCellValue('E2', $this->disposeMoney($total['this_day_cost_money']))
			  ->setCellValue('F2', $this->disposeMoney($total['this_day_profit_money']))
			  ->setCellValue('G2', $this->disposeMoney($total['this_month_income_money']))
			  ->setCellValue('H2', $this->disposeMoney($total['this_month_cost_money']))
			  ->setCellValue('I2', $this->disposeMoney($total['this_month_profit_money']))
			  ->setCellValue('J2', $this->disposeMoney($total['this_year_income_money']))
			  ->setCellValue('K2', $this->disposeMoney($total['this_year_cost_money']))
			  ->setCellValue('L2', $this->disposeMoney($total['this_year_profit_money']));
		##客户
		$row = 3;
		array_walk($customerData['data'], function ($item) use (&$row, $sheet) {
			$sheet->setCellValue('A' . $row, $item['customer_id'])
				  ->setCellValue('B' . $row, $item['customer_name'])
				  ->setCellValue('C' . $row, $this->disposeNumber($item['this_day_income_number']))
				  ->setCellValue('D' . $row, $this->disposeMoney($item['this_day_income_money']))
				  ->setCellValue('E' . $row, $this->disposeMoney($item['this_day_cost_money']))
				  ->setCellValue('F' . $row, $this->disposeMoney($item['this_day_profit_money']))
				  ->setCellValue('G' . $row, $this->disposeMoney($item['this_month_income_money']))
				  ->setCellValue('H' . $row, $this->disposeMoney($item['this_month_cost_money']))
				  ->setCellValue('I' . $row, $this->disposeMoney($item['this_month_profit_money']))
				  ->setCellValue('J' . $row, $this->disposeMoney($item['this_year_income_money']))
				  ->setCellValue('K' . $row, $this->disposeMoney($item['this_year_cost_money']))
				  ->setCellValue('L' . $row, $this->disposeMoney($item['this_year_profit_money']));
			$row++;
		});
		
		#设置全局样式
		$sheet->getStyle('A1:L' . $row)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('A1:L' . $row)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('A1:L' . $row)
			  ->getFont()
			  ->setName('宋体');
		$sheet->getColumnDimension('A')
			  ->setWidth(20);
		$sheet->getColumnDimension('B')
			  ->setWidth(24);
		$sheet->getColumnDimension('C')
			  ->setWidth(18);
		$sheet->getColumnDimension('D')
			  ->setWidth(18);
		$sheet->getColumnDimension('E')
			  ->setWidth(18);
		$sheet->getColumnDimension('F')
			  ->setWidth(18);
		$sheet->getColumnDimension('G')
			  ->setWidth(22);
		$sheet->getColumnDimension('H')
			  ->setWidth(22);
		$sheet->getColumnDimension('I')
			  ->setWidth(22);
		$sheet->getColumnDimension('J')
			  ->setWidth(22);
		$sheet->getColumnDimension('K')
			  ->setWidth(22);
		$sheet->getColumnDimension('L')
			  ->setWidth(22);
		for ($i = 1; $i < $row; $i++) {
			$sheet->getRowDimension($i)
				  ->setRowHeight(18);
		}
	}
	
	/**
	 * 生成第二个sheet
	 *
	 * @access protected
	 *
	 * @param $sheet        \PHPExcel_Worksheet
	 * @param $productData  array 产品维度的统计数据
	 *
	 * @return void
	 **/
	protected function setSheet2($sheet, $productData)
	{
		$sheet->setTitle('产品维度');
		##标题
		$sheet->setCellValue('A1', '产品名称')
			  ->setCellValue('B1', '子产品名称')
			  ->setCellValue('C1', '收入计费用量')
			  ->setCellValue('D1', '当日权责收入')
			  ->setCellValue('E1', '当日权责成本')
			  ->setCellValue('F1', '当日权责毛利')
			  ->setCellValue('G1', '当月累计权责收入')
			  ->setCellValue('H1', '当月累计权责成本')
			  ->setCellValue('I1', '当月累计权责毛利')
			  ->setCellValue('J1', '当年累计权责收入')
			  ->setCellValue('K1', '当年累计权责成本')
			  ->setCellValue('L1', '当年累计权责毛利');
		$sheet->getStyle('A1:L1')
			  ->getFont()
			  ->setBold(true);
		$sheet->getStyle('A1:L1')
			  ->getFont()
			  ->setSize(12);
		##合计
		$sheet->mergeCells('A2:B2')
			  ->getStyle('A2')
			  ->getFont()
			  ->setBold(true);
		$total = $productData['total'];
		$sheet->setCellValue('A2', '合计')
			  ->setCellValue('C2', $this->disposeNumber($total['this_day_income_number']))
			  ->setCellValue('D2', $this->disposeMoney($total['this_day_income_money']))
			  ->setCellValue('E2', $this->disposeMoney($total['this_day_cost_money']))
			  ->setCellValue('F2', $this->disposeMoney($total['this_day_profit_money']))
			  ->setCellValue('G2', $this->disposeMoney($total['this_month_income_money']))
			  ->setCellValue('H2', $this->disposeMoney($total['this_month_cost_money']))
			  ->setCellValue('I2', $this->disposeMoney($total['this_month_profit_money']))
			  ->setCellValue('J2', $this->disposeMoney($total['this_year_income_money']))
			  ->setCellValue('K2', $this->disposeMoney($total['this_year_cost_money']))
			  ->setCellValue('L2', $this->disposeMoney($total['this_year_profit_money']));
		##产品
		$row = 3;
		array_walk($productData['data'], function ($item) use (&$row, $sheet) {
			if (array_key_exists('children', $item)) {
				$sheet->setCellValue('A' . $row, $item['name'])
					  ->setCellValue('B' . $row, '小计')
					  ->setCellValue('C' . $row, $this->disposeNumber($item['this_day_income_number']))
					  ->setCellValue('D' . $row, $this->disposeMoney($item['this_day_income_money']))
					  ->setCellValue('E' . $row, $this->disposeMoney($item['this_day_cost_money']))
					  ->setCellValue('F' . $row, $this->disposeMoney($item['this_day_profit_money']))
					  ->setCellValue('G' . $row, $this->disposeMoney($item['this_month_income_money']))
					  ->setCellValue('H' . $row, $this->disposeMoney($item['this_month_cost_money']))
					  ->setCellValue('I' . $row, $this->disposeMoney($item['this_month_profit_money']))
					  ->setCellValue('J' . $row, $this->disposeMoney($item['this_year_income_money']))
					  ->setCellValue('K' . $row, $this->disposeMoney($item['this_year_cost_money']))
					  ->setCellValue('L' . $row, $this->disposeMoney($item['this_year_profit_money']))
					  ->getStyle('B' . $row)
					  ->getFont()
					  ->setBold(true);
				$row++;
				//遍历子产品
				$children = $item['children'];
				$startRow = $row - 1;
				$sheet->mergeCells('A' . $startRow . ':A' . ($startRow + count($children)));
				array_walk($children, function ($childrenItem) use (&$row, $sheet) {
					$sheet->setCellValue('B' . $row, $childrenItem['name'])
						  ->setCellValue('C' . $row, $this->disposeNumber($childrenItem['this_day_income_number']))
						  ->setCellValue('D' . $row, $this->disposeMoney($childrenItem['this_day_income_money']))
						  ->setCellValue('E' . $row, $this->disposeMoney($childrenItem['this_day_cost_money']))
						  ->setCellValue('F' . $row, $this->disposeMoney($childrenItem['this_day_profit_money']))
						  ->setCellValue('G' . $row, $this->disposeMoney($childrenItem['this_month_income_money']))
						  ->setCellValue('H' . $row, $this->disposeMoney($childrenItem['this_month_cost_money']))
						  ->setCellValue('I' . $row, $this->disposeMoney($childrenItem['this_month_profit_money']))
						  ->setCellValue('J' . $row, $this->disposeMoney($childrenItem['this_year_income_money']))
						  ->setCellValue('K' . $row, $this->disposeMoney($childrenItem['this_year_cost_money']))
						  ->setCellValue('L' . $row, $this->disposeMoney($childrenItem['this_year_profit_money']));
					$row++;
				});
			} else {
				$sheet->setCellValue('A' . $row, $item['name'])
					  ->setCellValue('B' . $row, '--')
					  ->setCellValue('C' . $row, $this->disposeNumber($item['this_day_income_number']))
					  ->setCellValue('D' . $row, $this->disposeMoney($item['this_day_income_money']))
					  ->setCellValue('E' . $row, $this->disposeMoney($item['this_day_cost_money']))
					  ->setCellValue('F' . $row, $this->disposeMoney($item['this_day_profit_money']))
					  ->setCellValue('G' . $row, $this->disposeMoney($item['this_month_income_money']))
					  ->setCellValue('H' . $row, $this->disposeMoney($item['this_month_cost_money']))
					  ->setCellValue('I' . $row, $this->disposeMoney($item['this_month_profit_money']))
					  ->setCellValue('J' . $row, $this->disposeMoney($item['this_year_income_money']))
					  ->setCellValue('K' . $row, $this->disposeMoney($item['this_year_cost_money']))
					  ->setCellValue('L' . $row, $this->disposeMoney($item['this_year_profit_money']));
				$row++;
			}
		});
		
		#设置全局样式
		$sheet->getStyle('A1:L' . $row)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('A1:L' . $row)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('A1:L' . $row)
			  ->getFont()
			  ->setName('宋体');
		$sheet->getColumnDimension('A')
			  ->setWidth(18);
		$sheet->getColumnDimension('B')
			  ->setWidth(34);
		$sheet->getColumnDimension('C')
			  ->setWidth(18);
		$sheet->getColumnDimension('D')
			  ->setWidth(18);
		$sheet->getColumnDimension('E')
			  ->setWidth(18);
		$sheet->getColumnDimension('F')
			  ->setWidth(18);
		$sheet->getColumnDimension('G')
			  ->setWidth(22);
		$sheet->getColumnDimension('H')
			  ->setWidth(22);
		$sheet->getColumnDimension('I')
			  ->setWidth(22);
		$sheet->getColumnDimension('J')
			  ->setWidth(22);
		$sheet->getColumnDimension('K')
			  ->setWidth(22);
		$sheet->getColumnDimension('L')
			  ->setWidth(22);
		for ($i = 1; $i < $row; $i++) {
			$sheet->getRowDimension($i)
				  ->setRowHeight(18);
		}
	}
	
	/**
	 * 过滤负值
	 *
	 * @access protected
	 *
	 * @param $value integer|float 值
	 *
	 * @return integer|float
	 **/
	protected function filterMinus($value)
	{
		return number_format($value, 2);
		//return $value > 0 ? $value : 0;
	}
	
	/**
	 * 金额处理
	 *
	 * @access protected
	 *
	 * @param $money float 金额
	 *
	 * @return float
	 **/
	protected function disposeMoney($money)
	{
		return number_format(round($money, 0));
	}
	
	/**
	 * 调用量处理
	 *
	 * @access protected
	 *
	 * @param $number integer 调用量
	 *
	 * @return integer
	 **/
	protected function disposeNumber($number)
	{
		return number_format($number, 0);
	}
	
	/**
	 * 整理特殊费用数据
	 *
	 * @access protected
	 *
	 * @param $customerIds array 允许查询的客户ID
	 *
	 * @return array
	 **/
	protected function getSpecialData($customerIds)
	{
		return array_filter($this->specialData, function ($item) use ($customerIds) {
			return in_array($item['customer_id'], $customerIds);
		});
	}
	
	/**
	 * 计算客户维度的数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getCustomerData()
	{
		//基础数据
		$base = [
			'customer_id'              => '',
			'customer_name'            => '',
			'this_day_cost_money'      => 0,
			'this_day_income_money'    => 0,
			'this_month_cost_money'    => 0,
			'this_month_income_money'  => 0,
			'this_year_cost_money'     => 0,
			'this_year_income_money'   => 0,
			'this_day_cost_number'     => 0,
			'this_day_income_number'   => 0,
			'this_month_cost_number'   => 0,
			'this_month_income_number' => 0,
			'this_year_cost_number'    => 0,
			'this_year_income_number'  => 0,
			'this_day_profit_money'    => 0,
			'this_month_profit_money'  => 0,
			'this_year_profit_money'   => 0,
			'income_details'           => [],
			'cost_details'             => [],
		];
		
		$result          = [];
		$customerMapping = $this->getCustomerMapping();
		
		//遍历成本数据、并合计
		array_walk($this->costData, function ($costItem) use (&$result, $base, $customerMapping) {
			$customer_id = $costItem['customer_id'];
			if (empty($customer_id)) {
				return;
			}
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id]                  = $base;
				$result[$customer_id]['customer_id']   = $customer_id;
				$result[$customer_id]['customer_name'] = $customerMapping[$customer_id];
			}
			$result[$customer_id]['this_day_cost_money']    = bcadd($result[$customer_id]['this_day_cost_money'], $costItem['day_money'], 6);
			$result[$customer_id]['this_month_cost_money']  = bcadd($result[$customer_id]['this_month_cost_money'], $costItem['month_money'], 6);
			$result[$customer_id]['this_year_cost_money']   = bcadd($result[$customer_id]['this_year_cost_money'], $costItem['year_money'], 6);
			$result[$customer_id]['this_day_cost_number']   = bcadd($result[$customer_id]['this_day_cost_number'], $costItem['day_number']);
			$result[$customer_id]['this_month_cost_number'] = bcadd($result[$customer_id]['this_month_cost_number'], $costItem['month_number']);
			$result[$customer_id]['this_year_cost_number']  = bcadd($result[$customer_id]['this_year_cost_number'], $costItem['year_number']);
			$result[$customer_id]['cost_details'][]         = $costItem;
		});
		
		//遍历收入并合计
		array_walk($this->incomeData, function ($incomeItem) use (&$result, $base, $customerMapping) {
			$customer_id = $incomeItem['customer_id'];
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id]                  = $base;
				$result[$customer_id]['customer_id']   = $customer_id;
				$result[$customer_id]['customer_name'] = $customerMapping[$customer_id];
			}
			$result[$customer_id]['this_day_income_money']    = bcadd($result[$customer_id]['this_day_income_money'], $incomeItem['day_money'], 6);
			$result[$customer_id]['this_month_income_money']  = bcadd($result[$customer_id]['this_month_income_money'], $incomeItem['month_money'], 6);
			$result[$customer_id]['this_year_income_money']   = bcadd($result[$customer_id]['this_year_income_money'], $incomeItem['year_money'], 6);
			$result[$customer_id]['this_day_income_number']   = bcadd($result[$customer_id]['this_day_income_number'], $incomeItem['day_number']);
			$result[$customer_id]['this_month_income_number'] = bcadd($result[$customer_id]['this_month_income_number'], $incomeItem['month_number']);
			$result[$customer_id]['this_year_income_number']  = bcadd($result[$customer_id]['this_year_income_number'], $incomeItem['year_number']);
			$result[$customer_id]['income_details'][]         = $incomeItem;
		});
		
		//计算毛利润并排序
		$data = array_map(function ($item) {
			return $this->computeProfit($item);
		}, $result);
		array_multisort(array_column($data, $this->sortBy), SORT_DESC, $data);
		
		//计算合计数据
		$this_day_cost_money      = array_sum(array_column($data, 'this_day_cost_money'));
		$this_day_income_money    = array_sum(array_column($data, 'this_day_income_money'));
		$this_month_cost_money    = array_sum(array_column($data, 'this_month_cost_money'));
		$this_month_income_money  = array_sum(array_column($data, 'this_month_income_money'));
		$this_year_cost_money     = array_sum(array_column($data, 'this_year_cost_money'));
		$this_year_income_money   = array_sum(array_column($data, 'this_year_income_money'));
		$this_day_cost_number     = array_sum(array_column($data, 'this_day_cost_number'));
		$this_day_income_number   = array_sum(array_column($data, 'this_day_income_number'));
		$this_month_cost_number   = array_sum(array_column($data, 'this_month_cost_number'));
		$this_month_income_number = array_sum(array_column($data, 'this_month_income_number'));
		$this_year_cost_number    = array_sum(array_column($data, 'this_year_cost_number'));
		$this_year_income_number  = array_sum(array_column($data, 'this_year_income_number'));
		$total                    = $this->computeProfit(compact('this_day_cost_money', 'this_day_income_money', 'this_month_cost_money', 'this_month_income_money', 'this_year_cost_money', 'this_year_income_money', 'this_day_cost_number', 'this_day_income_number', 'this_month_cost_number', 'this_month_income_number', 'this_year_cost_number', 'this_year_income_number'));
		
		//过滤掉无意义的数据
		$data = array_filter($data, function ($item) {
			return !$this->nonUseData($item);
		});
		
		return compact('total', 'data');
	}
	
	/**
	 * 获取客户ID与之对应的客户名称
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getCustomerMapping()
	{
		if (is_null($this->cacheCustomerMapping)) {
			$this->cacheCustomerMapping = Customer::pluck('name', 'customer_id')
												  ->toArray();
		}
		
		return $this->cacheCustomerMapping;
	}
	
	/**
	 * 计算产品维度的数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getProductData()
	{
		//生成一个存储数据的容器
		$container = $this->initBaseProductData();
		
		//整理产品维度的收入
		$this->disposeProductIncome($container);
		//整理产品维度的成本
		$this->disposeProductCost($container);
		
		//过滤数据
		//计算数据
		//排序
		$container = array_map(function ($item) {
			if (array_key_exists('children', $item)) {
				//处理子节点数据
				#计算数据
				$item['children'] = array_map(function ($childrenItem) {
					return $this->computeProfit($childrenItem);
				}, $item['children']);
				#过滤数据
				$item['children'] = array_filter($item['children'], function ($chlidrenItem) {
					return !$this->nonUseData($chlidrenItem);
				});
				#排序
				array_multisort(array_column($item['children'], $this->sortBy), SORT_DESC, $item['children']);
			}
			
			return $this->computeProfit($item);
		}, $container);
		#排序
		array_multisort(array_column($container, $this->sortBy), SORT_DESC, $container);
		
		//计算合计数据
		$data                     = $container;
		$this_day_cost_money      = array_sum(array_column($data, 'this_day_cost_money'));
		$this_day_income_money    = array_sum(array_column($data, 'this_day_income_money'));
		$this_month_cost_money    = array_sum(array_column($data, 'this_month_cost_money'));
		$this_month_income_money  = array_sum(array_column($data, 'this_month_income_money'));
		$this_year_cost_money     = array_sum(array_column($data, 'this_year_cost_money'));
		$this_year_income_money   = array_sum(array_column($data, 'this_year_income_money'));
		$this_day_cost_number     = array_sum(array_column($data, 'this_day_cost_number'));
		$this_day_income_number   = array_sum(array_column($data, 'this_day_income_number'));
		$this_month_cost_number   = array_sum(array_column($data, 'this_month_cost_number'));
		$this_month_income_number = array_sum(array_column($data, 'this_month_income_number'));
		$this_year_cost_number    = array_sum(array_column($data, 'this_year_cost_number'));
		$this_year_income_number  = array_sum(array_column($data, 'this_year_income_number'));
		$total                    = $this->computeProfit(compact('this_day_cost_money', 'this_day_income_money', 'this_month_cost_money', 'this_month_income_money', 'this_year_cost_money', 'this_year_income_money', 'this_day_cost_number', 'this_day_income_number', 'this_month_cost_number', 'this_month_income_number', 'this_year_cost_number', 'this_year_income_number'));
		
		return compact('total', 'data');
	}
	
	/**
	 * 初始化产品数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function initBaseProductData()
	{
		$base = [
			'name'                     => '',
			'label'                    => '',
			'this_day_cost_money'      => 0,
			'this_day_income_money'    => 0,
			'this_month_cost_money'    => 0,
			'this_month_income_money'  => 0,
			'this_year_cost_money'     => 0,
			'this_year_income_money'   => 0,
			'this_day_cost_number'     => 0,
			'this_day_income_number'   => 0,
			'this_month_cost_number'   => 0,
			'this_month_income_number' => 0,
			'this_year_cost_number'    => 0,
			'this_year_income_number'  => 0,
			'this_day_profit_money'    => 0,
			'this_month_profit_money'  => 0,
			'this_year_profit_money'   => 0,
			'this_year_recharge_money' => 0,
		];
		
		//邦企查
		$data['bqc']          = $base;
		$data['bqc']['name']  = '邦企查';
		$data['bqc']['label'] = 'bqc';
		
		//邦秒验
		$data['bmy']             = $base;
		$data['bmy']['name']     = '邦秒验';
		$data['bmy']['label']    = 'bmy';
		$children                = Product::select(['product_id', 'product_name'])
										  ->where('father_id', '=', 200)
										  ->get()
										  ->map(function ($item) use ($base) {
											  $base['label'] = $item->product_id;
											  $base['name']  = $item->product_name;
			
											  return $base;
										  })
										  ->toArray();
		$children                = array_column($children, null, 'label');
		$children[801]           = $base;
		$children[801]['name']   = '号码状态查询';
		$children[801]['label']  = '801';
		$data['bmy']['children'] = $children;
		
		return $data;
	}
	
	/**
	 * 获取、计算、整理产品维度的成本数据
	 *
	 * @access protected
	 *
	 * @param $container   array 产品整理后的数据容器
	 *
	 * @return void
	 **/
	protected function disposeProductCost(&$container)
	{
		array_walk($this->costData, function ($item) use (&$container) {
			$product_id = $item['product_id'];
			$label      = $this->decideCategroyForCost($product_id);
			if (is_null($label)) {
				return;
			}
			
			switch (count($label)) {
				case 1:
					list($first) = $label;
					$container[$first]['this_day_cost_money']    += $item['day_money'];
					$container[$first]['this_month_cost_money']  += $item['month_money'];
					$container[$first]['this_year_cost_money']   += $item['year_money'];
					$container[$first]['this_day_cost_number']   += $item['day_number'];
					$container[$first]['this_month_cost_number'] += $item['month_number'];
					$container[$first]['this_year_cost_number']  += $item['year_number'];
					break;
				case 3:
					list($first, $second, $third) = $label;
					//子节点的
					$container[$first][$second][$third]['this_day_cost_money']    += $item['day_money'];
					$container[$first][$second][$third]['this_month_cost_money']  += $item['month_money'];
					$container[$first][$second][$third]['this_year_cost_money']   += $item['year_money'];
					$container[$first][$second][$third]['this_day_cost_number']   += $item['day_number'];
					$container[$first][$second][$third]['this_month_cost_number'] += $item['month_number'];
					$container[$first][$second][$third]['this_year_cost_number']  += $item['year_number'];
					
					//父节点的
					$container[$first]['this_day_cost_money']    += $item['day_money'];
					$container[$first]['this_month_cost_money']  += $item['month_money'];
					$container[$first]['this_year_cost_money']   += $item['year_money'];
					$container[$first]['this_day_cost_number']   += $item['day_number'];
					$container[$first]['this_month_cost_number'] += $item['month_number'];
					$container[$first]['this_year_cost_number']  += $item['year_number'];
					break;
			}
		});
	}
	
	/**
	 * 获取、计算、整理产品维度的收入数据
	 *
	 * @access protected
	 *
	 * @param $container   array 产品整理后的数据容器
	 *
	 * @return void
	 **/
	protected function disposeProductIncome(&$container)
	{
		array_walk($this->incomeData, function ($item) use (&$container) {
			$account_id = $item['account_id'];
			$product_id = $item['product_id'];
			$label      = $this->decideCategroyForIncome($account_id, $product_id);
			
			if (is_null($label)) {
				return;
			}
			switch (count($label)) {
				case 3:
					//存在子节点
					list($first, $second, $third) = $label;
					$container[$first][$second][$third]['this_day_income_money']    += $item['day_money'];
					$container[$first][$second][$third]['this_month_income_money']  += $item['month_money'];
					$container[$first][$second][$third]['this_year_income_money']   += $item['year_money'];
					$container[$first][$second][$third]['this_day_income_number']   += $item['day_number'];
					$container[$first][$second][$third]['this_month_income_number'] += $item['month_number'];
					$container[$first][$second][$third]['this_year_income_number']  += $item['year_number'];
					
					//父节点也要存
					$container[$first]['this_day_income_money']    += $item['day_money'];
					$container[$first]['this_month_income_money']  += $item['month_money'];
					$container[$first]['this_year_income_money']   += $item['year_money'];
					$container[$first]['this_day_income_number']   += $item['day_number'];
					$container[$first]['this_month_income_number'] += $item['month_number'];
					$container[$first]['this_year_income_number']  += $item['year_number'];
					break;
				case 1:
					//没有子节点
					list($first) = $label;
					$container[$first]['this_day_income_money']    += $item['day_money'];
					$container[$first]['this_month_income_money']  += $item['month_money'];
					$container[$first]['this_year_income_money']   += $item['year_money'];
					$container[$first]['this_day_income_number']   += $item['day_number'];
					$container[$first]['this_month_income_number'] += $item['month_number'];
					$container[$first]['this_year_income_number']  += $item['year_number'];
					break;
			}
		});
	}
	
	/**
	 * 确认当前的收入属于那个产品(成本的规则)
	 *
	 * @access protected
	 *
	 * @param $product_id integer 产品ID
	 *
	 * @return array
	 **/
	protected function decideCategroyForCost($product_id)
	{
		if ($product_id == 210) {
			return null;
		}
		if ($product_id == 401) {
			return ['bqc'];
		}
		if (in_array($product_id, get200ChildrenProductIds())) {
			return ['bmy', 'children', $product_id];
		}
		if (801 == $product_id) {
			return ['bmy', 'children', $product_id];
		}
		$this->output->warning("成本账单中存在不支持的产品ID：{$product_id}");
		
		return null;
	}
	
	/**
	 * 确认当前的收入属于那个产品(收入的规则)
	 *
	 * @access protected
	 *
	 * @param $account_id string 账号ID
	 * @param $product_id string 产品ID
	 *
	 * @return array
	 **/
	protected function decideCategroyForIncome($account_id, $product_id)
	{
		//邦信分快捷版--评分类字段
		if (in_array($product_id, getBxfShortScoreProductIds())) {
			return ['bxf_short', 'children', 'bxf_short_score'];
		}
		
		//邦信分快捷版--普通类字段
		if (in_array($product_id, getBxfStatProductIds())) {
			return ['bxf_short', 'children', 'bxf_short_stat'];
		}
		
		if ($product_id == 210) {
			return ['bxf_short', 'children', 'bxf_short_stat'];
		}
		
		//邦信分私有云
		if ($product_id == 501) {
			return ['bxf_price_cloud'];
		}
		
		//邦企查
		if ($product_id == 401) {
			return ['bqc'];
		}
		
		//邦秒验
		if (in_array($product_id, get200ChildrenProductIds())) {
			return ['bmy', 'children', $product_id];
		}
		
		if ($product_id == 801) {
			return ['bmy', 'children', 801];
		}
		
		//金盾
		if (in_array($product_id, get615ChildrenProductIds()) || in_array($product_id, [
				603,
				612,
				613,
				614,
				616,
				664,
			])
		) {
			return ['gold_shield', 'children', $product_id];
		}
		
		if ($product_id == 615) {
			return ['gold_shield', 'children', 661];
		}
		
		//邦信分详单版：包含邦信分详单版V1、邦信分详单版V2、
		if (in_array($product_id, [101, 105])) {
			return ['bxf_detail_list'];
		}
		
		//邦秒配详单版的收入根据计费依据确定，计费依据为“号码量”是邦秒配详单版，否则计为邦信分详单版
		if ($product_id == 104) {
			$isBmp = BillConfig::where('account_id', $account_id)
							   ->where('product_id', $product_id)
							   ->where('fee_basis', 1)
							   ->where('is_reset', 0)
							   ->where('is_delete', 0)
							   ->count();
			if ($isBmp > 0) {
				return ['bxf_detail_list'];
			} else {
				return ['bmp'];
			}
		}
		if (in_array($product_id, [601, 604])) {
			return ['bmp'];
		}
		//$this->output->warning('收入账单中存在不支持的产品ID：' . $product_id);
		//throw new \Exception('收入账单中存在不支持的产品ID：' . $product_id);
		return null;
	}
	
	/**
	 * 根据客户维度分别计算当日的成本、当月的累计成本
	 *
	 * @access protected
	 *
	 * @param $customerIds array 客户ID
	 *
	 * @return array
	 **/
	protected function getCostByCustomerIds($customerIds)
	{
		$thisDayCostMoney    = 0;
		$thisDayCostNumber   = 0;
		$thisMonthCostMoney  = 0;
		$thisMonthCostNumber = 0;
		$thisYearCostMoney   = 0;
		$thisYearCostNumber  = 0;
		//记录明细（方便回溯）
		$details = [];
		array_walk($this->costData, function ($item) use (&$thisDayCostMoney, &$thisDayCostNumber, &$thisMonthCostMoney, &$thisMonthCostNumber, &$details, &$thisYearCostMoney, &$thisYearCostNumber, $customerIds) {
			if (in_array($item['customer_id'], $customerIds)) {
				#设置金额
				$thisDayCostMoney   = bcadd($thisDayCostMoney, $item['day_money'], 6);
				$thisMonthCostMoney = bcadd($thisMonthCostMoney, $item['month_money'], 6);
				$thisYearCostMoney  = bcadd($thisYearCostMoney, $item['year_money'], 6);
				#设置量
				$thisDayCostNumber               = bcadd($thisDayCostMoney, $item['day_number']);
				$thisMonthCostNumber             = bcadd($thisMonthCostMoney, $item['month_number']);
				$thisYearCostNumber              = bcadd($thisYearCostMoney, $item['year_number']);
				$details[$item['customer_id']][] = $item;
			}
		});
		
		return [
			$thisDayCostMoney,
			$thisMonthCostMoney,
			$thisYearCostMoney,
			$thisDayCostNumber,
			$thisMonthCostNumber,
			$thisYearCostNumber,
		];
	}
	
	/**
	 * 根据客户维度分别计算当日的收入、当月的累计收入
	 *
	 * @access protected
	 *
	 * @param $customerIds array 客户ID
	 *
	 * @return array
	 **/
	protected function getIncomeByCustomerIds($customerIds)
	{
		$thisDayIncomeMoney    = 0;
		$thisDayIncomeNumber   = 0;
		$thisMonthIncomeMoney  = 0;
		$thisMonthIncomeNumber = 0;
		$thisYearIncomeMoney   = 0;
		$thisYearIncomeNumber  = 0;
		
		//记录明细（方便回溯）
		$details = [];
		array_walk($this->incomeData, function ($item) use (&$thisDayIncomeMoney, &$thisDayIncomeNumber, &$thisMonthIncomeMoney, &$thisMonthIncomeNumber, &$thisYearIncomeMoney, &$thisYearIncomeNumber, &$details, $customerIds) {
			if (in_array($item['customer_id'], $customerIds)) {
				#设置金额
				$thisDayIncomeMoney   = bcadd($thisDayIncomeMoney, $item['day_money'], 6);
				$thisMonthIncomeMoney = bcadd($thisMonthIncomeMoney, $item['month_money'], 6);
				$thisYearIncomeMoney  = bcadd($thisYearIncomeMoney, $item['year_money'], 6);
				#设置量
				$thisDayIncomeNumber             = bcadd($thisDayIncomeNumber, $item['day_number']);
				$thisMonthIncomeNumber           = bcadd($thisMonthIncomeNumber, $item['month_number']);
				$thisYearIncomeNumber            = bcadd($thisYearIncomeNumber, $item['year_number']);
				$details[$item['customer_id']][] = $item;
			}
		});
		
		return [
			$thisDayIncomeMoney,
			$thisMonthIncomeMoney,
			$thisYearIncomeMoney,
			$thisDayIncomeNumber,
			$thisMonthIncomeNumber,
			$thisYearIncomeNumber,
			$details,
		];
	}
	
	/**
	 * 计算每个单元（商务、产品、客户、部门）的当日毛利润、当月累计毛利润
	 *
	 * @access protected
	 *
	 * @param $data array 单元数据
	 *
	 * @return array
	 **/
	protected function computeProfit($data)
	{
		$this_day_cost_money     = $data['this_day_cost_money'];
		$this_day_income_money   = $data['this_day_income_money'];
		$this_month_cost_money   = $data['this_month_cost_money'];
		$this_month_income_money = $data['this_month_income_money'];
		$this_year_cost_money    = $data['this_year_cost_money'];
		$this_year_income_money  = $data['this_year_income_money'];
		
		$data['this_day_profit_money']   = bcsub($this_day_income_money, $this_day_cost_money, 6);
		$data['this_month_profit_money'] = bcsub($this_month_income_money, $this_month_cost_money, 6);
		$data['this_year_profit_money']  = bcsub($this_year_income_money, $this_year_cost_money, 6);
		
		return $data;
	}
	
	/**
	 * 判断一个数据是否为无意义数据（客户、产品（邦秒验子产品）如果是无意义数据，则过滤掉）
	 *
	 * @access protected
	 *
	 * @param $data array 数据
	 *
	 * @return boolean
	 **/
	protected function nonUseData($data)
	{
		//所有数据均为0
		return $data['this_day_cost_money'] == 0 && $data['this_day_income_money'] == 0 && $data['this_month_cost_money'] == 0 && $data['this_month_income_money'] == 0 && $data['this_year_cost_money'] == 0 && $data['this_year_income_money'] == 0 && $data['this_day_cost_number'] == 0 && $data['this_day_income_number'] == 0 && $data['this_month_cost_number'] == 0 && $data['this_month_income_number'] == 0 && $data['this_year_cost_number'] == 0 && $data['this_year_income_number'] == 0 && $data['this_day_profit_money'] == 0 && $data['this_month_profit_money'] == 0 && $data['this_year_profit_money'] == 0;
	}
}