<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/14 0014
 * Time: 18:26
 */

namespace App\Console\Commands;

use App\Models\ChannelAccount;
use Illuminate\Console\Command;

class SXCmccRemainAmount extends Command
{
    protected $signature = 'sx_cmcc_remain_amount';

    /**
     * @throws \Exception
     */
    public function handle()
    {
       $limit = $this->getLImit();
       if ($limit !== false){
           $limit = $limit/100;
           $channelaccount = new ChannelAccount();
           $channelaccount->updateData(10,['real_balance'=>$limit]);
       }
    }

    private function getLImit()
    {

        $config['token'] = 'token_e10adc3949ba59abbe56e057f20f883e';
        $config['url'] = 'https://squidproxy11.dianhua.cn/sxcmcc/openapi/dhb/queryBalance.action';

        $header = ['Content-Type:application/json;charset=utf-8', 'token:' . $config['token']];
        $param = new \ArrayObject();
        $infos = $this->curl($config['url'], $param, 1, $header, 20000);
        $infos && $infos = json_decode($infos, true);

        if (isset($infos['code']) && $infos['code'] == 0) {
            return $infos['result'];
        }else{
            return false;
        }
    }


    private function curl($url, $params = false, $ispost = 0, $header = [], $time_out = 2000)
    {
        //$httpInfo = array();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36');
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, $time_out);
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, $time_out);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        //设置header
        if ($header) {
            $params = json_encode($params);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        }

        $https = (substr($url, 0, 5) == 'https') ? true : false;
        if ($https) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); // 对认证证书来源的检查
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); // 从证书中检查SSL加密算法是否存在
        }
        if ($ispost) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_URL, $url);
        } else {
            if ($params) {
                if (is_array($params)) {
                    $params = http_build_query($params);
                }
                curl_setopt($ch, CURLOPT_URL, $url . (strpos($url, '?') ? '&' : '?') . $params);
            } else {
                curl_setopt($ch, CURLOPT_URL, $url);
            }
        }

        $response = curl_exec($ch);
        if ($response === FALSE) {
            return false;
        }
        curl_close($ch);
        return $response;
    }



}