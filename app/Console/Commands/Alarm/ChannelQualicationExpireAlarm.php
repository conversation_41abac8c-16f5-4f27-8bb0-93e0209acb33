<?php

namespace App\Console\Commands\Alarm;


use App\Models\ChannelQualication;
use App\Models\ChannelQualicationDetail;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;


/**
 * Class ChannelQualicationExpireAlarm
 * @package App\Console\Commands\Alarm
 *
 *
 * php artisan send_mail:channel_qualication_alarm --sendto=jianye.li --cc=jianye.li
 */
class ChannelQualicationExpireAlarm extends Command
{

    protected $signature = "send_mail:channel_qualication_alarm
    {--sendto= : 收件人, 添加此选项将强制使收件人为sendto}
    {--cc= : 抄送人, 添加此选项将强制使抄送人为cc}
    ";

    protected $description = "渠道资质临期报警";


    /**
     *  需要报警的合作状态 1未合作 2合作中
     * @var array
     */
    protected $alarmCooperationStatus = [1, 2];


    /**
     *  需要报警的到期天数
     * @var array
     */
    protected $alarmExpireDays = [30, 15, 7, 3, 1, 0];

    /**
     * 领导邮件收件人
     * @var string[]
     */
    protected $leaderEmail = ['yong.liu', 'mengsi.wang'];


    /**
     * 运营邮件收件人
     * @var string[]
     */
    protected $operatorEmail = ['ren.zhang', 'lili.liu', 'ningning.zhang'];

    /**
     * 是否抄送领导
     * @var bool
     */
    protected $isCCToLeader = false;

    protected $title = '渠道资质到期情况列表';

    /**
     *  上下级关系
     * @var array
     */
    protected $superior = [
        'zezhi.wu' => 'jing.zhou',
    ];

    public function handle()
    {
        $sendTo = $this->option('sendto');
        $cc = $this->option('cc'); // 命令行参数

        $aAlarmDataList = $this->agetAlarmDataList();
        if (!$aAlarmDataList) {
            return true;
        }
        // 销售发送属于自己的邮件
        $this->sendMailToSalesman($aAlarmDataList, $sendTo, $cc);
        // 发送给运营的完整数据
        $this->sendMailToOperator($aAlarmDataList, $sendTo, $cc);
        return true;
    }

    /**
     * @param $aAlarmDataList
     * @param string $sendTo
     * @param string $cc
     *
     * @return boolean
     */
    private function sendMailToSalesman($aAlarmDataList, $sendTo = '', $cc = '')
    {
        $salesmans = array_unique(array_column($aAlarmDataList, 'salesman'));
        foreach ($salesmans as $salesman) { //给销售发送邮件
            $html = $this->getHtml($aAlarmDataList, $salesman);
            if ($sendTo) {
                $emailSendTo = $sendTo;
            } elseif (isset($this->superior[$salesman])) {
                $emailSendTo = [$salesman, $this->superior[$salesman]];
            } else {
                $emailSendTo = $salesman;
            }
            $ccEmails = $cc ?? [];
            $this->sendMail($emailSendTo, $this->title, $html, $ccEmails);
        }

        return true;
    }

    /**
     * @param $aAlarmDataList
     * @param string $sendTo
     * @param string $cc
     * @return bool
     * @throws \Exception
     */
    private function sendMailToOperator($aAlarmDataList, $sendTo = '', $cc = '')
    {
        $totalDataHtml = $this->getHtml($aAlarmDataList);
        $emailSendTo = $sendTo ?? $this->operatorEmail;
        $ccEmails = $cc ?? ($this->isCCToLeader ? $this->leaderEmail : []);
        $this->sendMail($emailSendTo, $this->title, $totalDataHtml, $ccEmails);
        return true;
    }

    private function agetAlarmDataList()
    {
        $res = [];
        $aCond = [
            ['cooperation_status', 'in', $this->alarmCooperationStatus],
        ];

        $aDataList = ChannelQualication::aGetDataListByCond($aCond);
        if (!$aDataList) {
            return $res;
        }
        $ids = array_column($aDataList, 'id');
        $channelQualicationMap = array_column($aDataList, null, 'id');
        $futureDate = date('Y-m-d', strtotime('+30 days'));
        $currentDate = date('Y-m-d');
        $aCond = [
            ['channel_qualication_id', 'in', $ids],
            ['end_date', '<=', $futureDate],
            ['status', '=', 1],
        ];
        $aChannelDetailList = ChannelQualicationDetail::aGetDataListByCond($aCond);
        if (!$aChannelDetailList) {
            return $res;
        }
        foreach ($aChannelDetailList as $item) {
            $qualicationName = $item['qualication_name'];
            $channelQualicationId = $item['channel_qualication_id'];
            $endDate = $item['end_date'];
            $diffDays = $this->igetDiffDays($currentDate, $endDate); //计算日期差异
            if ($diffDays <= 0) { // 有资质到期 抄送领导
                $this->isCCToLeader = true;
            }

            if ($diffDays < 0 || in_array($diffDays, $this->alarmExpireDays)) {
                $channelName = isset($channelQualicationMap[$channelQualicationId]) ? $channelQualicationMap[$channelQualicationId]['channel_name'] : '';
                $salesman = isset($channelQualicationMap[$channelQualicationId]) ? $channelQualicationMap[$channelQualicationId]['salesman'] : '';
                $res[] = [
                    'channelName' => $channelName,
                    'qualicationName' => $qualicationName,
                    'salesman' => $salesman,
                    'endDate' => $endDate,
                    'diffDays' => $diffDays
                ];
            }
        }
        if ($res) { // 排序
            $diffDays = array_column($res, 'diffDays');
            array_multisort($diffDays, SORT_ASC, $res);
        }

        return $res;
    }

    /**
     * @param $aAlarmDataList
     * @param string $salesman 不传则获取全部的数据
     * @return string
     */
    private function getHtml($aAlarmDataList, $salesman = '')
    {
        $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 30px 0;
        }

        th {
            border: 1px solid black;
        }

        .title {
            padding: 8px;
            text-align: center;
        }
        
        td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
        }

        .split-cell {
            position: relative;
            background: linear-gradient(to bottom left,
            transparent 49.5%,
            currentColor 49.5%,
            currentColor 50.5%,
            transparent 50.5%);
        }

        .col-text {
            position: absolute;
            bottom: 2px;
            left: 2px;
            margin: 0;
            padding: 0; 
            line-height: 1; 
            text-align: left;
        }

        .row-text {
            position: absolute;
            top: 2px; 
            right: 2px; 
            margin: 0;
            padding: 0; 
            line-height: 1; 
            text-align: right;
        }
    </style>
</head>
<body>
<table>
    <thead>
        <tr style="background-color: #4874cb; color: white;">
            <th colspan="15" class="title">渠道资质到期情况列表</th>
        </tr>
        <tr style="background-color: #b6c6ea; color: white;">
            <td style="width: 6%">渠道简称</td>
            <td style="width: 6%">资质名称</td>
            <td style="width: 6%">到期时间</td>
            <td style="width: 6%">距离到期天数</td>
        </tr>
    </thead>
    <tbody>
HTML;
        foreach ($aAlarmDataList as $val) {
            $Style = $val["diffDays"] <= 0 ? 'style="color: #ff0000; font-weight: bold;"' : '';
            if ($salesman && $val['salesman'] != $salesman) {
                continue;
            }
            $html .= <<<HTML
        <tr>
          <td>{$val["channelName"]}</td>
          <td>{$val["qualicationName"]}</td>
          <td>{$val["endDate"]}</td>
          <td {$Style}>{$val["diffDays"]}</td>
        </tr>
HTML;
        }
        $html .= <<<HTML
    </tbody>
  </table>
</body>
HTML;

        return $html;
    }

    /**
     * @param $dateStr1
     * @param $dateStr2
     * @return int
     */
    private function igetDiffDays($dateStr1, $dateStr2)
    {

        $date1 = new \DateTime($dateStr1);
        $date2 = new \DateTime($dateStr2);
        $interval = $date1->diff($date2);
        $days = $interval->days;
        if ($interval->invert) {
            $days = -$days;
        }
        return $days;
    }


    /**
     * 发邮件
     * @param $sendTo
     * @param $title
     * @param $html
     * @param array $cc
     * @throws \Exception
     */
    private function sendMail($sendTo, $title, $html, $cc)
    {
        if (!is_array($sendTo)) {
            $sendTo = [$sendTo];
        }
        if (!is_array($cc)) {
            $cc = [$cc];
        }
        $sendAddress = [];
        foreach ($sendTo as $value) {
            $sendAddress[] = [
                'email' => $value . '@yulore.com'
            ];
        }
        $ccAddress = [];
        foreach ($cc as $value) {
            $ccAddress[] = [
                'email' => $value . '@yulore.com'
            ];
        }
        $mail = new SendMailService();
        $mail->setFromName('金融后台项目组')
            ->setAddressee($sendAddress)
            ->setCC($ccAddress)
            ->setSubject($title)
            ->setContent($html)
            ->send();
    }
}