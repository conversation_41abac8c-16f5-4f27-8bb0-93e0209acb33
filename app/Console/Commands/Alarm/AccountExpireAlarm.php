<?php

namespace App\Console\Commands\Alarm;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\SystemUser;
use App\Models\Customer;
use App\Models\DingdingCustomerApprovalProcess;
use App\Models\Product;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\FeishuRepository;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Exception;

class AccountExpireAlarm extends Command
{
    use CurlTrait;
    protected $signature = "account_expire_alarm:by_im {func?} {params?} {customer_id?} {account_id?} {product_ids?}";

    protected $description = "账号过期时间预警，自动发起审批流程";
    protected $assign_customer_id = "";//指定客户id
    protected $assign_account_id = "";//指定账号id
    protected $assign_product_ids = [];//指定产品

    const ALARM_TIME = 7 * 86400;//七天前
    /**
     * @var FeishuRepository
     */
    private $feishu;

    public function __construct()
    {
        parent::__construct();
        $this->feishu = new FeishuRepository();
    }

    public function handle()
    {
        $func = $this->argument('func') ?? 'done' ;
        $params = $this->argument('params') ?? '' ;
        $this->assign_customer_id = $this->argument('customer_id') ?? '' ;
        $this->assign_account_id = $this->argument('account_id') ?? '' ;
        $product_ids = $this->argument('product_ids') ?? "" ;
        if(!empty($product_ids)){
            $this->assign_product_ids = explode(',', $product_ids);
        }

        switch ($func) {
            case 'done':
                $this->done($params);
                break;
            case 'delDingdingLog':
                $this->delDingdingLog($params);
                break;
            default:
                exit('非法参数');
        }
    }

    /**
     * @throws Exception
     */
    protected function done($date) {
        $date = $date ?: 'today';
        $e_time = strtotime($date) + self::ALARM_TIME - 1;
        $s_time = strtotime($date) + self::ALARM_TIME - 86400;
        $where = [
            ['status', '=', 1],
            ['end_time', '>=', $s_time],
            ['end_time', '<=', $e_time],
        ];

        //获取过期时间内账号
        $datas = (new AccountProduct())->where($where)->get();
        if (!count($datas)) {
            exit('暂无数据！');
        }

        $datas = $datas->toArray();
        $accountIds = array_unique(array_column($datas, 'account_id'));
        $accountInfos = (new Account())->whereIn('account_id', $accountIds)->get()->toArray();
        $accountInfos = array_column($accountInfos, null, 'account_id');

        $customerIds = array_unique(array_column($accountInfos, 'customer_id'));
        $customerInfos = (new Customer())->whereIn('customer_id', $customerIds)->get()->toArray();
        $customerInfos = array_column($customerInfos, null, 'customer_id');

        //获取需要排除的产品id
        $filter_product_ids = Product::getHasProductFatherId();
        $filter_product_ids = array_column($filter_product_ids,'product_id');

        $productIds = array_unique(array_column($datas, 'product_id'));
        $productInfos = (new Product())->whereIn('product_id', $productIds)->get()->toArray();
        $productInfos = array_column($productInfos, null, 'product_id');

        $alarmDatas = [];
        foreach ($datas as $item) {
            //获取需要排除的产品id
            if(in_array($item['product_id'], $filter_product_ids)){
                continue;
            }

//            if(!in_array($item['account_id'], ['****************', '****************'])){
//                continue;
//            }

            $customerId = $accountInfos[$item['account_id']]['customer_id'];

            if(!empty($this->assign_customer_id)){
                if($this->assign_customer_id != $customerId){
                    continue;
                }
            }

            if(!empty($this->assign_account_id)){
                if($this->assign_account_id != $item['account_id']){
                    continue;
                }
            }

            if(!empty($this->assign_product_ids)){
                if(!in_array($item['product_id'], $this->assign_product_ids)){
                    continue;
                }
            }

            $existWhere =[
                ['customer_id', $customerId],
                ['account_id', $item['account_id']],
                ['product_ids', 'like', '%,' . $item['product_id'] . ',%'],
                ['level', 7],
                ['type', DingdingCustomerApprovalProcess::$type['合同到期']],
                ['create_time', '>', strtotime('today') - self::ALARM_TIME],
                ['create_time', '>=', strtotime('2024-03-06')]//新版上线 忽略历史数据
            ];
            //该判断避免7天内重复提交
            $log = DingdingCustomerApprovalProcess::getNotProcessed($existWhere);
            if ($log) {
                continue;
            }

            //判断账号产品延期次数
            $delay_times = $this->getDelayTimes($customerId, $item['account_id'], $item['product_id'], $item['end_time']);
            if (!isset($alarmDatas[$delay_times][$customerId][$item['account_id']])) {
                $alarmDatas[$delay_times][$customerId][$item['account_id']] = [];
            }

            //获取对应父产品id
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $father_id = $father_id ? $father_id : $item['product_id'];//401或501等其它产品父产品既是子产品
            $alarmDatas[$delay_times][$customerId][$item['account_id']][$father_id][] = [
                'productId' => $item['product_id'],
                'endTime' => $item['end_time'],
            ];
        }

        if (!$alarmDatas) {
            exit('暂无报警数据！');
        }
        
        foreach ($alarmDatas as $times => $customerData) {
            foreach ($customerData as $customerId => $value) {
                $customerInfo = $customerInfos[$customerId];
                //获取跟进销售及对应部门
                $feishu_user_id = $this->getDUserIdBySalesman($customerInfo['salesman']);
                if (!$feishu_user_id) {
                    Log::info("获取飞书user ID异常:" . json_encode([$customerInfo, $value]));
                    sendWechatNotice("获取飞书user ID异常:".json_encode([$customerInfo, $value]));
                }

                $accountStr = '';
                //$accountId = '';
                foreach ($value as $account_id => $product_arr) {
                    $accountInfo = $accountInfos[$account_id];
                    //$accountId = $account_id;
                    $productStr = '账号:'.$accountInfo['account_name'] . PHP_EOL;
                    foreach ($product_arr as $fpid => $arr) {
                        $father_name = RedisCache::instance('productId_productName_mapping')->get($fpid);
                        $productStr .= "父产品:" . $father_name.'----------' . PHP_EOL;
                        foreach ($arr as $v) {
                            $productInfo = $productInfos[$v['productId']];
                            $productStr .= "  --" . $productInfo['product_name'] . PHP_EOL;
                        }
                        $productStr .= PHP_EOL;
                    }
                    $accountStr .= $productStr.PHP_EOL;
                }

                $expire_time = date('Y-m-d', $e_time);
                $ext_time = date('Y-m-d', strtotime(date('Y-m-d', $e_time) . ' +1 month'));
                try {
                    $res = $this->feishu->create_account_expire_approval($feishu_user_id, $customerInfo['name'], $expire_time, $ext_time, $accountStr, $times);
                    $processInstanceId = $res['data']['instance_code'];
                } catch (Exception $e) {
                    //dd($e->getMessage());
                    Log::info("飞书创建审批异常:" . $e->getMessage());
                    sendWechatNotice("飞书创建审批异常:" . $e->getMessage());
                    continue;
                }

                $addLog = [];
                foreach ($value as $account_id => $product_arr) {
                    $product_ids = [];
                    foreach ($product_arr as $fpid => $arr) {
                        foreach ($arr as $v) {
                            $product_ids[] = $v['productId'];
                        }
                    }
                    $addLog[] = [
                        'customer_id' => $customerId,
                        'account_id' => $account_id,
                        'type' => 3,
                        'level' => 7,
                        'process_instance_id' => $processInstanceId,
                        'end_time' => $e_time,
                        'create_time' => time(),
                        'product_ids' => ',' . implode(',', $product_ids) . ','
                    ];
                }

                //添加钉钉报警处理流程
                DingdingCustomerApprovalProcess::insert($addLog);
            }
        }
    }

    protected function delDingdingLog($ids) {
        $ids = explode(',', $ids);
        $res = DingdingCustomerApprovalProcess::deleteById($ids);
    }


    /**
     * @throws Exception
     */
    private function getDUserIdBySalesman($salesman) {
        $result = '';
        //获取销售的电话
        $phone = SystemUser::getUserInfoByName($salesman)['phone'];
        //根据电话获取飞书的user id
        if (!empty($phone)){
            $res = $this->feishu->get_user_by_mobile($phone);
            $result =  $res[$phone]??'';
        }
        return $result;
    }

    private function getDelayTimes($customer_id, $account_id, $product_id, $end_time){

        $where =[
            ['customer_id', $customer_id],
            ['account_id', $account_id],
            ['product_ids', 'like', '%,' . $product_id . ',%'],
            ['level', 7],
            ['type', DingdingCustomerApprovalProcess::$type['合同到期']],
            ['is_end_cycle', 0],
            ['create_time', '>=', strtotime('2024-03-06')] //该条件限制是为了过滤历史数据,(这是新版预警,忽略历史数据)
        ];

        $list = DingdingCustomerApprovalProcess::getListByCondition($where, null, 'create_time desc')->toArray();
        if(count($list) == 0){
            $times = 1;
        }else{
            $times = $this->checkTimes($list, $end_time);
        }

        return $times;
    }


    private function checkTimes($data, $end_time){
        $yuZhi = 31*86400;//31天
        //本次到期时间 比上次到期时间 间隔超31天，说明这次为正式合同第一次到期(正式合同一般大于等于1年；且运营商定测试合同或到期合同一般延期最多一次1月)
        $diff = $end_time - $data[0]['end_time'];
        if($diff > $yuZhi){
            $ids = array_column($data, 'id');
            DingdingCustomerApprovalProcess::updateDataByIds($ids, ['is_end_cycle' => 1]);
            $times = 1;
        }else{
            $times = count($data) + 1;
        }

        return $times;
    }


}