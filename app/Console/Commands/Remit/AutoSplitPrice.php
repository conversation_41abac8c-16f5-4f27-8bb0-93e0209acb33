<?php

namespace App\Console\Commands\Remit;

use App\Models\Customer;
use Illuminate\Console\Command;
use App\Repositories\AutoSplitPriceRepository;
use Exception;

/**
 * 废弃
 *
 * 预付款认款自动拆单 初始化remit_balance表数据
 * 这里需要起始时间为202112的初始值
 *
 * 慧算账 C20210310TDXJ8J
 * php artisan remit:auto_split_price --customer_id C20210310TDXJ8J
 * php artisan remit:auto_split_price --customer_id C20210310TDXJ8J,C20200717CY26TG,C20210716I8QCT6,C20190606B7M68O
 */
class AutoSplitPrice extends Command
{
    /**
     * 脚本名称,参数
     * @var string
     */
    protected $signature = 'remit:auto_split_price
    {--end_time=    : 处理该月份之前的数据 不包括该月份 Ym}
    {--customer_id= : 指定客户 多个id使用","分割,不指定为全部预付费客户}';

    /**
     * 脚本描述
     * @var string
     */
    protected $description = '预付款认款自动拆单 更新/初始化数据';

    /**
     * 指定客户
     * @var false|string[]
     */
    private $customer_id = '';

    /**
     * 客户预付费类型
     */
    const PAYMENT_TYPE_PRE_PAYED = 1;

    /**
     * @var false|string 截止时间
     */
    private $end_time;

    /**
     * 构造函数
     */
    public function __construct(){
        parent::__construct();
    }

    /**
     * 执行
     * @access public
     * @return void
     **/
    public function handle(){
        $this->output->success("【{$this->description}】 开始");
        try {
            //设置参数
            $this->setOptions();
            $aspr_obj = new AutoSplitPriceRepository($this->customer_id, $this->end_time);
            $aspr_obj->auto_split();
            $this->output->success("【{$this->description}】 完成");
        } catch (Exception $exception) {
            $this->output->error("【{$this->description}】 失败: {$exception->getMessage()},{$exception->getFile()}:{$exception->getLine()}");
        }
    }

    /**
     * 处理传入参数
     * @access protected
     * @return void
     * @throws Exception
     */
    protected function setOptions(){
        $customer_id = $this->input->getOption('customer_id');
        if (!empty($customer_id)) {//替换默认值
            $_customer_ids = explode(',', $customer_id);
            //过滤后付费
            $_customer     = Customer::getCustomerListByCustomerIds(['customer_id', 'name','payment_type'],$_customer_ids);
            $customer_ids = [];
            foreach ($_customer as $info){
                if(self::PAYMENT_TYPE_PRE_PAYED == $info['payment_type']){
                    $customer_ids[] = $info['customer_id'];
                }else{
                    $this->output->warning($info['name']."[".$info['customer_id'].".]不是预付费客户,已经过滤");die;
                }
            }
            $this->customer_id = $customer_ids;
        } else {
            //获取所有预付款客户id
            $_customer_ids     = Customer::getCustomerListByPaymentType('customer_id', self::PAYMENT_TYPE_PRE_PAYED);
            $_customer_ids     = array_column($_customer_ids, 'customer_id');
            $this->customer_id = $_customer_ids;
        }

        $end_time = $this->input->getOption('end_time');
        $this->end_time = empty($end_time) ? date("Ym01") : $end_time."01";
    }
}