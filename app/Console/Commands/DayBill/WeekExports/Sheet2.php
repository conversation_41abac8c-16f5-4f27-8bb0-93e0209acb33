<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/13 0013
 * Time: 13:45
 */

namespace App\Console\Commands\DayBill\WeekExports;

use Maatwebsite\Excel\Concerns\{
    RegistersEventListeners, WithTitle, WithEvents
};
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class Sheet2 implements WithTitle, WithEvents
{
    use RegistersEventListeners;

    protected static $customerIncomeInfo;
    protected static $customerTotal;
    protected static $date_begin;
    protected static $date_end;

    public function __construct($customerIncomeInfo, $customerTotal, $date_begin, $date_end)
    {
        self::$customerIncomeInfo = $customerIncomeInfo;
        self::$customerTotal      = $customerTotal;
        self::$date_begin         = $date_begin;
        self::$date_end           = $date_end;
    }

    public function title(): string
    {
        return date('Y年m月d日', strtotime(self::$date_begin)) . '-' . date(
                'Y年m月d日',
                strtotime(self::$date_end)
            ) . '产品权责收入统计';
    }

    public static function afterSheet(AfterSheet $event)
    {
        $sheet = $event->sheet;

        $row = 1;
        //标题
        self::setCellItemRow($sheet, $row, ['客户ID', '客户名称', '计费用量', '本周计费用量权责收入', '当月累计权责收入']);

        //合计
        $row++;
        $total = self::format_data(self::$customerTotal);
        self::setCellItemRow(
            $sheet,
            $row,
            ['', '合计', $total['fee_number'], $total['fee_date_money'], $total['fee_this_month_money']]
        );

        foreach (self::$customerIncomeInfo as $item) {
            $row++;
            $item = self::format_data($item);
            self::setCellItemRow(
                $sheet,
                $row,
                [
                    $item['customer_id'],
                    $item['name'],
                    $item['fee_number'],
                    $item['fee_date_money'],
                    $item['fee_this_month_money']
                ]
            );
        }
        self::setStyle($sheet, $row);
    }

    //设置样式
    protected static function setStyle($sheet, $endRow)
    {
        //字体并适配大小
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $sheet->autoSize();

        //边线
        $range_cell = 'A1:E' . $endRow;
        $sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );

        //行高
        for ($i = 1; $i <= $endRow; $i++) {
            $sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        $sheet->getStyle("A1:E" . $endRow)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //设置居右
        $sheet->getStyle('C2:E' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    protected static function setCellItemRow($sheet, $row, $data)
    {
        $sheet->setCellValue('A' . $row, $data[0]);
        $sheet->setCellValue('B' . $row, $data[1]);
        $sheet->setCellValue('C' . $row, $data[2]);
        $sheet->setCellValue('D' . $row, $data[3]);
        $sheet->setCellValue('E' . $row, $data[4]);
    }

    protected static function format_data($data)
    {
        $data['fee_number']           = number_format($data['fee_number']);
        $data['fee_date_money']       = number_format($data['fee_date_money'], 2);
        $data['fee_this_month_money'] = number_format($data['fee_this_month_money'], 2);
        return $data;
    }
}