<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/12 0012
 * Time: 10:38
 */

namespace App\Console\Commands\DayBill;


class Html
{
    protected $productIncomeInfo;
    protected $customerIncomeInfo;

    protected $productTotal;
    protected $customerTotal;
    protected $date;
    protected $title = '客户-产品权责收入日报表';

    public function __construct($productIncomeInfo, $customerIncomeInfo, $date)
    {
        $this->productIncomeInfo  = $productIncomeInfo;
        $this->customerIncomeInfo = $customerIncomeInfo;
        $this->date               = $date;
        $this->computeTotal();
    }

    /**
     * 计算合计数据
     *
     * @access protected
     *
     * @return void
     **/
    protected function computeTotal()
    {
        $result                                     = array_column($this->productIncomeInfo, 'result');
        $this->productTotal                         = [
            'fee_number'           => array_sum(array_column($result, 'fee_number')),
            'fee_date_money'       => array_sum(array_column($result, 'fee_date_money')),
            'fee_this_month_money' => array_sum(array_column($result, 'fee_this_month_money'))
        ];
        $this->productTotal['fee_number']           += $result[6][0]['fee_number'];
        $this->productTotal['fee_date_money']       += $result[6][0]['fee_date_money'];
        $this->productTotal['fee_this_month_money'] += $result[6][0]['fee_this_month_money'];


        $this->customerTotal = [
            'fee_number'           => array_sum(array_column($this->customerIncomeInfo, 'fee_number')),
            'fee_date_money'       => array_sum(array_column($this->customerIncomeInfo, 'fee_date_money')),
            'fee_this_month_money' => array_sum(array_column($this->customerIncomeInfo, 'fee_this_month_money'))
        ];
    }

    public function display()
    {
        $title_date    = date('Y年m月d日', strtotime($this->date));
        $product_html  = $this->product_html();
        $customer_html = $this->customer_html();
        return <<<HTML
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>{$this->title}</title>
        <style>
            .tableArea { width:100%; height:auto; font-family: Arial, Helvetica, sans-serif; font-size: 14px; }
            .title { width:100%; height:26px; line-height:26px; color:#333; font-size:16px; font-weight:bold; text-align: center; }
            .table { width:96%; height:auto; overflow-x:auto; margin:20px auto; }
            .table table { height:auto; color:#333; border-top:1px solid #ccc; border-left:1px solid #ccc; width:100%;}
            .table table tr:hover{ background:#eeeeee; color: #333;}
            .table table th,.table table td { border-bottom: 1px solid #ccc; border-right: 1px solid #ccc; text-align:center; padding:4px 10px; box-sizing:border-box; }
            .table table th { font-weight:bold; min-width:80px !important; }
            .td_red{color:red;}
            .td_green{color:green;}
        </style>
    </head>
    <body>
        <div class="tableArea">
            <div class="title">{$title_date}产品权责收入统计</div>
            <div class="table">
                <table cellpadding="0" cellspacing="0" border="0">
                    <thead>
                        <tr>
                            <th>产品名称</th>
                            <th>子产品名称</th>
                            <th>计费用量</th>
                            <th>当日计费用量权责收入</th>
                            <th>当月累计权责收入</th>
                        </tr>
                    </thead>
                    <tbody>
                        {$product_html}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="tableArea">
            <div class="title">{$title_date}客户权责收入统计</div>    
                <div class="table">
                    <table cellpadding="0" cellspacing="0" border="0">
                        <thead>
                            <tr>
                                <th>客户ID</th>
                                <th>客户名称</th>
                                <th>计费用量</th>
                                <th>当日计费用量权责收入</th>
                                <th>当月累计权责收入</th>
                            </tr>
                        </thead>
                        <tbody>
                            {$customer_html}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </body>
</html>
HTML;
    }

    protected function customer_html()
    {
        $html = '';

        //合计
        $total = $this->format_data($this->customerTotal);
        $html  .= "<tr class='tr_total'><th></th><th>合计</th><th>{$total['fee_number']}</th><th>{$total['fee_date_money']}</th><th>{$total['fee_this_month_money']}</th></tr>";

        //每个客户
        foreach ($this->customerIncomeInfo as $customer_id => $item) {
            if ($item['fee_this_month_money'] == 0 && $item['status'] == 0) {
                continue;
            }
            $item = $this->format_data($item);
            $html .= "<tr><td>{$item['customer_id']}</td><td>{$item['name']}</td><td>{$item['fee_number']}</td><td>{$item['fee_date_money']}</td><td>{$item['fee_this_month_money']}</td></tr>";
        }
        return $html;
    }

    protected function product_html()
    {
        $html = '';
        //合计
        $total = $this->format_data($this->productTotal);
        $html  .= "<tr class='tr_total'><th>合计</th><th></th><th>{$total['fee_number']}</th><th>{$total['fee_date_money']}</th><th>{$total['fee_this_month_money']}</th></tr>";

        //各产品
        foreach ($this->productIncomeInfo as $label => $item) {
            //如果为空，则展示0
            if (empty($item['result'])) {
                $html .= '<tr><td>' . $item['name'] . '</td><td>' . $item['name'] . '</td><td>0</td><td>0</td><td>0</td></tr>';
                continue;
            }
            //如果为邦秒验，则需要展示子产品
            if ($label == 6) {
                $i    = 0;
                $rows = count($item['result']);
                foreach ($item['result'] as $value) {
                    $value = $this->format_data($value);
                    if ($i == 0) {
                        $html .= "<tr><td rowspan='{$rows}'>{$item['name']}</td><td>{$value['children_product_name']}</td><td>{$value['fee_number']}</td><td>{$value['fee_date_money']}</td><td>{$value['fee_this_month_money']}</td></tr></tr>";
                    } else {
                        $html .= "<tr><td>{$value['children_product_name']}</td><td>{$value['fee_number']}</td><td>{$value['fee_date_money']}</td><td>{$value['fee_this_month_money']}</td></tr></tr>";
                    }
                    $i++;
                }
                continue;
            }
            //其他正常产品依次展示
            $item['result'] = $this->format_data($item['result']);
            $html           .= "<tr><td>{$item['name']}</td><td>{$item['name']}</td><td>{$item['result']['fee_number']}</td><td>{$item['result']['fee_date_money']}</td><td>{$item['result']['fee_this_month_money']}</td></tr>";
        }
        return $html;
    }

    protected function format_data($data)
    {
        $data['fee_number']           = number_format($data['fee_number']);
        $data['fee_date_money']       = number_format($data['fee_date_money'], 2);
        $data['fee_this_month_money'] = number_format($data['fee_this_month_money'], 2);
        return $data;
    }
}