<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/12 0012
 * Time: 10:38
 */

namespace App\Console\Commands\DayBill;


use Illuminate\Mail\Mailable;

class Mail extends Mailable
{
    protected $html;
    protected $filename;
    protected $date;

    public function __construct($html, $filename, $date)
    {
        $this->html     = $html;
        $this->filename = $filename;
        $this->date     = $date;
    }

    public function build()
    {
        $this->subject(date('Y年m月d日', strtotime($this->date)) . '产品-客户权责收入统计')
            ->view(
                'emails.bill_day',
                [
                    'html' => $this->html
                ]
            )
            ->attach(
                $this->filename,
                [
                    'as' => '附件.xls'
                ]
            );
    }
}