<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/12/12 0012
 * Time: 10:38
 */

namespace App\Console\Commands\DayBill;

use App\Console\Commands\DayBill\WeekExports\Sheet1;
use App\Console\Commands\DayBill\WeekExports\Sheet2;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class WeekExcel implements WithMultipleSheets
{
    use Exportable;

    protected $productIncomeInfo;
    protected $customerIncomeInfo;

    protected $productTotal;
    protected $customerTotal;
    protected $date_begin;
    protected $date_end;
    protected $title = '客户-产品权责收入周报表';

    public function __construct($productIncomeInfo, $customerIncomeInfo, $date_begin, $date_end)
    {
        $this->productIncomeInfo  = $productIncomeInfo;
        $this->customerIncomeInfo = $customerIncomeInfo;
        $this->date_begin         = $date_begin;
        $this->date_end           = $date_end;
        $this->title              .= "({$date_begin} - {$date_end})";
        $this->computeTotal();
    }

    /**
     * 计算合计数据
     *
     * @access protected
     *
     * @return void
     **/
    protected function computeTotal()
    {
        $result                                     = array_column($this->productIncomeInfo, 'result');
        $this->productTotal                         = [
            'fee_number'           => array_sum(array_column($result, 'fee_number')),
            'fee_date_money'       => array_sum(array_column($result, 'fee_date_money')),
            'fee_this_month_money' => array_sum(array_column($result, 'fee_this_month_money'))
        ];
        $this->productTotal['fee_number']           += $result[6][0]['fee_number'];
        $this->productTotal['fee_date_money']       += $result[6][0]['fee_date_money'];
        $this->productTotal['fee_this_month_money'] += $result[6][0]['fee_this_month_money'];

        $this->customerTotal = [
            'fee_number'           => array_sum(array_column($this->customerIncomeInfo, 'fee_number')),
            'fee_date_money'       => array_sum(array_column($this->customerIncomeInfo, 'fee_date_money')),
            'fee_this_month_money' => array_sum(array_column($this->customerIncomeInfo, 'fee_this_month_money'))
        ];
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        //产品全责收入表
        $sheets[] = new Sheet1($this->productIncomeInfo, $this->productTotal, $this->date_begin, $this->date_end);

        //客户权责收入表
        $sheets[] = new Sheet2($this->customerIncomeInfo, $this->customerTotal, $this->date_begin, $this->date_end);

        return $sheets;
    }
}