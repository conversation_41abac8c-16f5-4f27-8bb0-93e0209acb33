<?php

namespace App\Console\Commands\DayBill;


use App\Models\BillConfig;
use App\Models\Customer;
use App\Models\MongoBillDay;
use App\Models\Product;
use Jenssegers\Mongodb\Eloquent\Model;

class Data
{
    protected $date;
    protected $data;
    //不参与计算的产品ID
    protected $filter_product_id = [205, 212, 220, 221, 222, 900];
    //不存于计算的客户ID
    protected $filter_customer_id = [];
    //不参与计算的账号ID
    protected $filter_account_id = [];

    //垃圾箱
    protected $customer_waste_data = [];
    protected $product_waste_data  = [];

    //单个产品的收入数据
    protected $item_product_result = [
        'children_product_name' => '',
        'fee_number'            => 0,
        'fee_date_money'        => 0,
        'fee_this_month_money'  => 0
    ];
    //展示的产品信息
    protected $product_info = [
        0 => [
            'name'      => '邦信分快捷版',
            'bill_list' => [],
            'result'    => []
        ],
        1 => [
            'name'      => '邦信分详单版',
            'bill_list' => [],
            'result'    => []
        ],
        2 => [
            'name'      => '邦信分私有云',
            'bill_list' => [],
            'result'    => []
        ],
        3 => [
            'name'      => '金盾',
            'bill_list' => [],
            'result'    => []
        ],
        4 => [
            'name'      => '邦秒配',
            'bill_list' => [],
            'result'    => []
        ],
        5 => [
            'name'      => '邦企查',
            'bill_list' => [],
            'result'    => []
        ],
        6 => [
            'name'      => '邦秒验',
            'bill_list' => [],
            'result'    => []
        ]
    ];

    protected $product_data;
    protected $children_product_210;
    protected $children_product_615;
    protected $children_product_200;

    public function __construct($date)
    {
        $this->date = $date;

        //获取所有的210子产品
        $this->children_product_210 = Product::where('father_id', 210)
            ->pluck('product_id')
            ->toArray();
        //获取所有的615子产品
        $this->children_product_615 = Product::where('father_id', 615)
            ->pluck('product_id')
            ->toArray();

        //获取200的子产品
        $this->children_product_200 = Product::where('father_id', 200)
            ->pluck('product_id')
            ->toArray();

        $this->product_data = Product::pluck('product_name', 'product_id')
            ->toArray();
    }

    /**
     * 获取日账单的全量数据
     *
     * @access protected
     *
     * @return Model
     **/
    protected function data()
    {
        if (empty($this->data)) {
            $this->data = MongoBillDay::where('date', '<=', $this->date)
                ->where('date', '>=', date('Ymd', strtotime('first day of ' . $this->date)))
                ->get();
        }
        return $this->data;
    }

    /**
     * 以产品维度获取日账单的计费
     *
     * @access
     *
     * @param
     * @param
     * @param
     *
     * @return
     **/
    public function product()
    {
        //将所有的计费配置进行分类
        //通过分好的账单标记进行计算
        $match = $this->getMatchProductArray();

        //获取全量账单数据
        $this->data()
            ->map(
                function ($item) use ($match) {
                    $customer_id = $item->customer_id;
                    $account_id  = $item->account_id;
                    $product_id  = $item->product_id;
                    $key         = $account_id . '_' . $product_id;
                    if (array_key_exists($key, $match) && !in_array(
                            $customer_id,
                            $this->filter_customer_id
                        ) && !in_array(
                            $account_id,
                            $this->filter_account_id
                        ) && !in_array($product_id, $this->filter_product_id)
                    ) {
                        $label = $match[$key];
                        $this->mergeProductInfo($label, $item->toArray());
                        return true;
                    } elseif ($product_id == 210) {
                        //
                        $shortcut_package_customer_id = [
                            'C201811012TPQQ8',
                            'C20190225VJOUQA',
                            'C20190801R97IIL'
                        ];
                        //210产品存在非计费配置生成的账单,此处进行补充
                        if (in_array($customer_id, $shortcut_package_customer_id)) {
                            $this->mergeProductInfo(0, $item->toArray());
                        }
                        return true;
                    }
                    $this->product_waste_data[] = compact('customer_id', 'account_id', 'product_id');
                    return true;
                }
            );

        //邦秒验子产品排序
        array_multisort(
            array_map(
                function ($item) {
                    return isset($item['fee_date_money']) ? $item['fee_date_money'] : 0;
                },
                $this->product_info[6]['result']
            ),
            SORT_DESC,
            $this->product_info[6]['result']
        );

        //邦秒验合计
        $bmy_total = [
            'children_product_name' => '合计',
            'fee_number'            => array_sum(array_column($this->product_info[6]['result'], 'fee_number')),
            'fee_date_money'        => array_sum(array_column($this->product_info[6]['result'], 'fee_date_money')),
            'fee_this_month_money'  => array_sum(
                array_column(
                    $this->product_info[6]['result'],
                    'fee_this_month_money'
                )
            ),
        ];
        array_unshift($this->product_info[6]['result'], $bmy_total);


        //排序
        $product_item = array_pop($this->product_info);

        array_multisort(
            array_map(
                function ($item) {
                    return isset($item['result']['fee_date_money']) ? $item['result']['fee_date_money'] : 0;
                },
                $this->product_info
            ),
            SORT_DESC,
            $this->product_info
        );

        array_push($this->product_info, $product_item);


        return $this->product_info;
    }

    /**
     * 将每个账单的数据计算到产品信息中
     *
     * @access protected
     *
     * @param $label integer 产品标记
     * @param $data  array 单条账单数据
     *
     * @return void
     **/
    protected function mergeProductInfo($label, $data)
    {
        $product_id                                = $data['product_id'];
        $this->product_info[$label]['bill_list'][] = $data;
        //邦秒验需要展示子产品
        if ($label == 6) {
            if (!array_key_exists($product_id, $this->product_info[$label]['result'])) {
                $this->product_info[$label]['result'][$product_id]                          = $this->item_product_result;
                $this->product_info[$label]['result'][$product_id]['children_product_name'] = $this->product_data[$product_id];
            }
            $result = &$this->product_info[$label]['result'][$product_id];
        } else {
            if (!array_key_exists('children_product_name', $this->product_info[$label]['result'])) {
                $this->product_info[$label]['result'] = $this->item_product_result;
            }
            $result = &$this->product_info[$label]['result'];
        }

        //计算数据
        $date = $data['date'];
        if ($date == $this->date) {
            $result['fee_number']           += isset($data['section_invoked_number']) && $data['section_invoked_number'] > 0 ? $data['section_invoked_number'] : 0;
            $result['fee_date_money']       += isset($data['money']) && $data['money'] > 0 ? $data['money'] : 0;
            $result['fee_this_month_money'] += isset($data['money']) ? $data['money'] : 0;
        } else {
            $result['fee_this_month_money'] += isset($data['money']) ? $data['money'] : 0;
        }
    }

    /**
     * 获取一个能够校验日账单数据匹配到响应产品的数组
     *
     * @access protected
     *
     * @return array
     **/
    public function getMatchProductArray()
    {
        //计费配置数据
        $data = BillConfig::where('start_date', '<=', date('Y-m-d', strtotime($this->date)))
            ->where('is_delete', 0)
            ->orderBy('start_date', 'desc')
            ->get()
            ->toArray();

        $match = [];
        array_walk(
            $data,
            function ($item) use (&$match) {
                $product_id = $item['product_id'];
                $account_id = $item['account_id'];
                //校验该计费配置所产生的账单需要放置于那个产品名下
                $key = $account_id . '_' . $product_id;
                if (!array_key_exists($key, $match)) {
                    $label = $this->decideCategroy($item);
                    if (!is_null($label)) {
                        $match[$key] = $label;
                    }
                }
            }
        );
        return $match;
    }

    /**
     * 判断一个计费配置属于那个产品
     *
     * @access protected
     *
     * @param $fee_config array 一条计费配置
     *
     * @return integer
     **/
    protected function decideCategroy($fee_config)
    {
        $product_id = $fee_config['product_id'];
        if (in_array($product_id, $this->children_product_210)) {
            return 0;
        }
        if ($product_id == 210) {
            return 0;
        }

        if ($product_id == 501) {
            return 2;
        }

        if ($product_id == 401) {
            return 5;
        }

        //邦信分详单版：包含邦信分详单版V1、邦信分详单版V2、计费依据非“号码量”的邦秒配详单版
        if (in_array($product_id, [101, 105])) {
            return 1;
        }

        if ($product_id == 104 && $fee_config['fee_basis'] == 1) {
            return 1;
        }

        //邦秒配：包含邦秒配单号版、邦秒配批量版、计费依据为“号码量”的邦秒配详单版
        if ($product_id == 104 && $fee_config['fee_basis'] == 2) {
            return 4;
        }
        if (in_array($product_id, [601, 604])) {
            return 4;
        }

        //邦秒验：邦秒验内所有子产品（其中205、212、220、221、222、900几个产品已废弃或不参与计费 ），801号码状态计入邦秒验
        if ($product_id == 801) {
            return 6;
        }

        if (in_array($product_id, $this->children_product_200)) {
            return 6;
        }

        //金盾产品
        if ($product_id == 603) {
            return 3;
        }
        if ($product_id == 612) {
            return 3;
        }
        if ($product_id == 613) {
            return 3;
        }
        if ($product_id == 614) {
            return 3;
        }
        if ($product_id == 616) {
            return 3;
        }
        if ($product_id == 615) {
            return 3;
        }

        if (in_array($product_id, $this->children_product_615)) {
            return 3;
        }

        return null;
    }

    /**
     * 获取客户收入数据
     *
     * @access protected
     *
     * @return array
     **/
    public function customer()
    {
        $customer_info = array_column($this->customer_info(), null, 'customer_id');

        $this->data()
            ->map(
                function ($item) use (&$customer_info) {
                    $date        = $item['date'];
                    $customer_id = $item['customer_id'];
                    $product_id  = $item['product_id'];
                    $account_id  = $item['account_id'];
                    if (array_key_exists($customer_id, $customer_info) && !in_array(
                            $customer_id,
                            $this->filter_customer_id
                        ) && !in_array(
                            $account_id,
                            $this->filter_account_id
                        ) && !in_array($product_id, $this->filter_product_id)
                    ) {
                        if ($date == $this->date) {
                            $customer_info[$customer_id]['fee_number']           += isset($item['section_invoked_number']) && $item['section_invoked_number'] > 0 ? $item['section_invoked_number'] : 0;
                            $customer_info[$customer_id]['fee_date_money']       += isset($item['money']) && $item['money'] > 0 ? $item['money'] : 0;
                            $customer_info[$customer_id]['fee_this_month_money'] += isset($item['money']) ? $item['money'] : 0;
                        } else {
                            $customer_info[$customer_id]['fee_this_month_money'] += isset($item['money']) ? $item['money'] : 0;
                        }
                    } else {
                        $this->customer_waste_data[] = compact('customer_id', 'product_id', 'account_id');
                    }
                }
            );

        //排序
        array_multisort(
            array_map(
                function ($item) {
                    return $item['fee_date_money'];
                },
                $customer_info
            ),
            SORT_DESC,
            $customer_info
        );

        return $customer_info;
    }

    /**
     * 获取所有存在计费配置的客户数据
     *
     * @access protected
     *
     * @return array
     **/
    public function customer_info()
    {
        //获取唯一的客户ID
        $customer_id = BillConfig::where('is_delete', 0)
            ->distinct('customer_id')
            ->pluck('customer_id')
            ->toArray();

        //获取客户信息
        return Customer::select(['customer_id', 'name', 'status'])
            ->whereIn('customer_id', $customer_id)
            ->get()
            ->map(
                function ($item) {
                    $item                         = $item->toArray();
                    $item['fee_number']           = 0;
                    $item['fee_date_money']       = 0;
                    $item['fee_this_month_money'] = 0;
                    return $item;
                }
            )
            ->toArray();
    }
}