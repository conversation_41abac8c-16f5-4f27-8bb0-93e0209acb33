<?php

namespace App\Console\Commands\DayBill\Exports;

use Maatwebsite\Excel\Concerns\{
    RegistersEventListeners, WithTitle, WithEvents
};
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class Sheet1 implements WithTitle, WithEvents
{
    use RegistersEventListeners;

    protected static $productIncomeInfo;
    protected static $productTotal;
    protected static $date;

    public function __construct($productIncomeInfo, $productTotal, $date)
    {
        self::$productIncomeInfo = $productIncomeInfo;
        self::$productTotal      = $productTotal;
        self::$date              = $date;
    }

    public function title(): string
    {
        return date('Y年m月d日', strtotime(self::$date)) . '产品权责收入统计';
    }

    public static function afterSheet(AfterSheet $event)
    {
        $sheet = $event->sheet;

        $row = 1;
        //标题
        self::setCellItemRow($sheet, $row, ['产品名称', '子产品名称', '计费用量', '当日计费用量权责收入', '当月累计权责收入']);

        //合计
        $row++;
        $total = self::format_data(self::$productTotal);
        self::setCellItemRow(
            $sheet,
            $row,
            ['合计', '', $total['fee_number'], $total['fee_date_money'], $total['fee_this_month_money']]
        );

        foreach (self::$productIncomeInfo as $label => $item) {
            $row++;
            if (empty($item['result'])) {
                self::setCellItemRow($sheet, $row, [$item['name'], $item['name'], 0, 0, 0]);
                continue;
            }

            if ($label != 6) {
                $result = self::format_data($item['result']);
                self::setCellItemRow(
                    $sheet,
                    $row,
                    [
                        $item['name'],
                        $item['name'],
                        $result['fee_number'],
                        $result['fee_date_money'],
                        $result['fee_this_month_money']
                    ]
                );
                continue;
            }

            $start = $row;
            $end   = $row + count($item['result']) - 1;
            $sheet->mergeCells('A' . $start . ':A' . $end);
            $sheet->setCellValue('A' . $start, $item['name']);
            foreach ($item['result'] as $value) {
                $value = self::format_data($value);
                self::setCellItemRow(
                    $sheet,
                    $row,
                    [
                        '',
                        $value['children_product_name'],
                        $value['fee_number'],
                        $value['fee_date_money'],
                        $value['fee_this_month_money']
                    ],
                    true
                );
                $row++;
            }
        }

        self::setStyle($sheet, $row - 1);
    }

    //设置样式
    protected static function setStyle($sheet, $endRow)
    {
        //字体并适配大小
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $sheet->autoSize();

        //边线
        $range_cell = 'A1:E' . $endRow;
        $sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
                'font'    => [
                    'name' => 'Arial',
                    'bold' => true,
                ]
            ]
        );

        //行高
        for ($i = 1; $i <= $endRow; $i++) {
            $sheet->getRowDimension($i)
                ->setRowHeight('18');
        }


        $sheet->getStyle("A1:E" . $endRow)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //设置居右
        $sheet->getStyle('C2:E' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    protected static function setCellItemRow($sheet, $row, $data, $isChidlren = false)
    {
        if (!$isChidlren) {
            $sheet->setCellValue('A' . $row, $data[0]);
        }
        $sheet->setCellValue('B' . $row, $data[1]);
        $sheet->setCellValue('C' . $row, $data[2]);
        $sheet->setCellValue('D' . $row, $data[3]);
        $sheet->setCellValue('E' . $row, $data[4]);
    }

    protected static function format_data($data)
    {
        $data['fee_number']           = number_format($data['fee_number']);
        $data['fee_date_money']       = number_format($data['fee_date_money'], 2);
        $data['fee_this_month_money'] = number_format($data['fee_this_month_money'], 2);
        return $data;
    }
}