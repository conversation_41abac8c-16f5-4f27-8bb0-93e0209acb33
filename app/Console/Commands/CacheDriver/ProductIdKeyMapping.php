<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 17:18
 */

namespace App\Console\Commands\CacheDriver;

use App\Models\Product;

//产品ID与产品KEY的映射关系
class ProductIdKeyMapping extends Driver
{
    protected $name = '产品ID与产品key的映射关系';
    protected $key  = 'product_id_key_mapping';
    protected $type = 'hash';

    public function cache()
    {
        //查询所有产品ID与产品key
        $productIds = Product::select(['product_id', 'product_key'])->get()->toArray();
        $data       = array_column($productIds, 'product_key', 'product_id');

        //生成缓存数据
        foreach ($data as $hKey => $hValue) {
            $this->getRedisConnection()->hSet($this->key, $hKey, $hValue);
        }
    }
}