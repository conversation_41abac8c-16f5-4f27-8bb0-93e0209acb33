<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 16:31
 */

namespace App\Console\Commands\CacheDriver;


use Illuminate\Support\Facades\Redis;

abstract class Driver
{
    protected $name = '';        //驱动的名称
    protected $key  = '';        //缓存的key
    protected $type = 'string';       // redis存储的方式 string hash


    //缓存数据
    abstract public function cache();

    public function getName()
    {
        return $this->name;
    }

    /**
     * 获取redis实例
     *
     * @access protected
     *
     * @return \Redis
     **/
    protected function getRedisConnection()
    {
        return Redis::connection('cache_commands');
    }

    /**
     * 获取缓存数据
     *  缓存数据支持两种模式，string与hash
     *
     * @access protected
     *
     * @param $hKey     string|array
     *
     * @return mixed
     **/
    public function getCacheInfo($hKey = null)
    {
        switch ($this->type) {
            case 'string' :
                return $this->getRedisConnection()->get($this->key);
                break;
            case 'hash':
                if (empty($hKey)) {
                    return $this->getRedisConnection()->hGetAll($this->key);
                } elseif (is_array($hKey)) {
                    $data = [];
                    foreach ($hKey as $hKeyItem) {
                        $data[$hKeyItem] = $this->getRedisConnection()->hGet($this->key, $hKeyItem);
                    }
                    return $data;
                } else {
                    return $this->getRedisConnection()->hGet($this->key, $hKey);
                }
                break;
            default:
                return $this->getRedisConnection()->get($this->key);
                break;
        }
    }
}