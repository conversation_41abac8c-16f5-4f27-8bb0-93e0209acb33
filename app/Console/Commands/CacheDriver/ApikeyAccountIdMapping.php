<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 16:35
 */

namespace App\Console\Commands\CacheDriver;

use App\Models\Account;

//账号APIKEY与账号ID映射表
class ApikeyAccountIdMapping extends Driver
{
    protected $name = '账号apikey与账号account_id的映射关系';
    protected $key  = 'apikey_account_id_mapping';
    protected $type = 'hash';

    public function cache()
    {
        $data = Account::select(['apikey', 'account_id'])
            ->where('apikey', '<>', ' ')
            ->where('is_delete', 0)
            ->get()
            ->toarray();
        $data = array_column($data, 'account_id', 'apikey');

        //生成缓存数据
        foreach ($data as $hKey => $hValue) {
            $this->getRedisConnection()->hSet($this->key, $hKey, $hValue);
        }
    }
}