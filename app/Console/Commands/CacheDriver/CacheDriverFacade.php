<?php

namespace App\Console\Commands\CacheDriver;


class CacheDriverFacade
{
    private $factory;

    public function __construct()
    {
        $this->factory = new CacheDriverFactory();
    }

    /**
     * 获取邦秒验（200）子产品的产品ID
     *
     * @access public
     *
     * @return array
     **/
    public function get200ChildrenProductId()
    {
        $driver = $this->factory->getCacheDriver('children_product_200');
        $data   = $driver->getCacheInfo();

        //如果不存在，需要重新缓存一下
        if (empty($data)) {
            $driver->cache();
            $data = $driver->getCacheInfo();
        }

        return @json_decode($data, true);
    }

    /**
     * 获取邦信分快捷版（210）子产品的产品ID
     *
     * @access public
     *
     * @return array
     **/
    public function get210ChildrenProductId()
    {
        $driver = $this->factory->getCacheDriver('children_product_210');
        $data   = $driver->getCacheInfo();

        //如果不存在，需要重新缓存一下
        if (empty($data)) {
            $driver->cache();
            $data = $driver->getCacheInfo();
        }

        return @json_decode($data, true);
    }
	
	/**
	 * 获取邦信分通信评分（1000）子产品的产品ID
	 *
	 * @access public
	 *
	 * @return array
	 **/
	public function get1000ChildrenProductId()
	{
		$driver = $this->factory->getCacheDriver('children_product_1000');
		$data   = $driver->getCacheInfo();
		
		//如果不存在，需要重新缓存一下
		if (empty($data)) {
			$driver->cache();
			$data = $driver->getCacheInfo();
		}
		
		return @json_decode($data, true);
	}

    /**
     * 获取金盾（615）子产品的产品ID
     *
     * @access public
     *
     * @return array
     **/
    public function get615ChildrenProductId()
    {
        $driver = $this->factory->getCacheDriver('children_product_615');
        $data   = $driver->getCacheInfo();

        //如果不存在，需要重新缓存一下
        if (empty($data)) {
            $driver->cache();
            $data = $driver->getCacheInfo();
        }

        return @json_decode($data, true);
    }

    /**
     * 获取某个产品key
     *
     * @access public
     *
     * @param $product_id integer 产品ID
     *
     * @return string
     **/
    public function getProductKeyByProductId($product_id)
    {
        $driver      = $this->factory->getCacheDriver('product_id_key_mapping');
        $product_key = $driver->getCacheInfo($product_id);

        //如果不存在，需要重新缓存一下
        if (empty($product_key)) {
            $driver->cache();
            $product_key = $driver->getCacheInfo($product_id);
        }

        return $product_key;
    }

    /**
     * 根据apikey获取客户ID
     *
     * @access public
     *
     * @param $apikey string
     *
     * @return string
     **/
    public function getCustomerIdByApikey($apikey)
    {
        $driver      = $this->factory->getCacheDriver('apikey_customer_id_mapping');
        $customer_id = $driver->getCacheInfo($apikey);

        //如果不存在，需要重新缓存一下
        if (empty($customer_id)) {
            $driver->cache();
            $customer_id = $driver->getCacheInfo($apikey);
        }

        return $customer_id;
    }

    /**
     * 根据apikey获取账号ID
     *
     * @access public
     *
     * @param $apikey string
     *
     * @return string
     **/
    public function getAccountIdByApikey($apikey)
    {
        $driver     = $this->factory->getCacheDriver('apikey_account_id_mapping');
        $account_id = $driver->getCacheInfo($apikey);

        //如果不存在，需要重新缓存一下
        if (empty($account_id)) {
            $driver->cache();
            $account_id = $driver->getCacheInfo($apikey);
        }

        return $account_id;
    }

    /**
     * 获取产品关联的数据源
     *
     * @access public
     *
     * @param $product_id integer
     *
     * @return array
     **/
    public function getUpstreamChannelByProductId($product_id)
    {
        $driver  = $this->factory->getCacheDriver('product_upstream_channel');
        $channel = $driver->getCacheInfo($product_id);

        //如果不存在，需要重新缓存一下
        if (empty($channel)) {
            $driver->cache();
            $channel = $driver->getCacheInfo($product_id);
        }

        return \GuzzleHttp\json_decode($channel);
    }

}