<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 16:33
 */

namespace App\Console\Commands\CacheDriver;


use App\Models\Product;

//1000子产品ID的缓存驱动
class ChildrenProduct1000 extends Driver
{
    protected $name = '金盾（1000）子产品的产品ID';
    protected $key  = 'children_product_1000';
    protected $type = 'string';

    public function cache()
    {
        //查询615产品的所有子产品
        $productIds = Product::where('father_id', 1000)->pluck('product_id')->toArray();
        $data       = json_encode($productIds, JSON_UNESCAPED_UNICODE);


        //生成缓存数据
        $this->getRedisConnection()->set($this->key, $data);
    }
}