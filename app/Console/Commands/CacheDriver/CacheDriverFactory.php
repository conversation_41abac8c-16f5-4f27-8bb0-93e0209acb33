<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 17:02
 */

namespace App\Console\Commands\CacheDriver;


class CacheDriverFactory
{
	protected $type = [
		'children_product_210'       => ChildrenProduct210::class,
		'children_product_200'       => ChildrenProduct200::class,
		'children_product_615'       => ChildrenProduct615::class,
		'children_product_1000'      => ChildrenProduct1000::class,
		'apikey_customer_id_mapping' => ApikeyCustomerIdMapping::class,
		'apikey_account_id_mapping'  => ApikeyAccountIdMapping::class,
		'product_id_key_mapping'     => ProductIdKeyMapping::class,
		'product_upstream_channel'   => ProductUpstreamChannel::class,
	];
	
	/**
	 * 获取缓存驱动
	 *
	 * @access protected
	 *
	 * @param $type string 驱动标记
	 *
	 * @return Driver
	 **/
	public function getCacheDriver($type)
	{
		if (!array_key_exists($type, $this->type)) {
			throw new \Exception("不存在的缓存驱动 : {$type}");
		}
		
		$className = array_get($this->type, $type);
		
		if (class_exists($className)) {
			$driver = new $className();
			if ($driver instanceof Driver) {
				return $driver;
			}
		}
		throw new \Exception("不存在的缓存驱动 : {$type}");
	}
	
	/**
	 * 获取支持的缓存类型
	 *
	 * @access public
	 *
	 * @return array
	 **/
	public function getAvailableDriver()
	{
		$result = [];
		foreach ($this->type as $type => $className) {
			$result[$type] = $this->getCacheDriver($type)
								  ->getName();
		}
		
		return $result;
	}
}