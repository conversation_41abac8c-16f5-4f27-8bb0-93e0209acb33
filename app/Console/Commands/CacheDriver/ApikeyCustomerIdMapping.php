<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 16:35
 */

namespace App\Console\Commands\CacheDriver;


use App\Models\Account;

//账号apikey与客户ID的映射表
class ApikeyCustomerIdMapping extends Driver
{
    protected $name = '账号apikey与客户customer_id的映射关系';
    protected $key  = 'apikey_customer_id_mapping';
    protected $type = 'hash';

    public function cache()
    {
        $data = Account::select(['apikey', 'customer_id'])
            ->where('apikey', '<>', '')
            ->where('is_delete', 0)
            ->get()
            ->toarray();
        $data = array_column($data, 'customer_id', 'apikey');

        //生成缓存数据
        foreach ($data as $hKey => $hValue) {
            $this->getRedisConnection()->hSet($this->key, $hKey, $hValue);
        }
    }
}