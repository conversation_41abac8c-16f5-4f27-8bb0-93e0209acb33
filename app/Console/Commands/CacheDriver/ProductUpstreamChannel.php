<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/5/7 0007
 * Time: 15:36
 */

namespace App\Console\Commands\CacheDriver;


use App\Models\Upstream;

class ProductUpstreamChannel extends Driver
{
    protected $name = '产品中包含的数据源';
    protected $key  = 'product_upstream_channel';
    protected $type = 'hash';

    public function cache()
    {
        //210 200所有子产品 401
        $productIds = (new CacheDriverFacade())->get200ChildrenProductId();
        $productIds[] = 210;
        $productIds[] = 401;
        //新加一个801的产品
        $productIds[] = 801;

        foreach ($productIds as $product_id) {
            $channel = Upstream::where('product_id', $product_id)->pluck('channel')->toArray();
            $this->getRedisConnection()->hSet($this->key, $product_id, json_encode($channel, JSON_UNESCAPED_UNICODE));
        }
    }
}