<?php

namespace App\Console\Commands\CreditAgency;

use App\Models\BillCustomerIncome;
use App\Models\BillCustomerIncomeV2;
use App\Models\BillProductIncome;
use App\Models\BillProductIncomeV2;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * 每20分钟计算当天
 */
class CalculateMoneyAgent extends Command
{
	/** @var string 命令 */
	public $signature = 'credit_agency:calculate_money_agent
    {--date= : 账单日，默认今日（格式Ymd）}
    {--days=1 : 计算多少日的数据}
    ';

	protected $description = '计算客户账单和产品账单的征信机构金额并更新';


	/**
	 * @var integer 计算日期（Ymd）
	 */
	protected $date = null;

	/**
	 * @var int 需要计算多少天的数据
	 */
	protected $days = 1;

	/**
	 * 定义不同source 对应不同的money_agent计算方式
	 *
	 * @var array
	 */
	protected $source_cal=[
		// 0=>'`money`',
		1=>'`money_original`*0.90',
		2=>'`money_original`*0.98',// 郑数交
        10=>'`money_original`*0.98',// 浙数交
    ];


	public function handle()
	{
		try {
			$this->checkParams();
			$this->Calculate();
			$this->output->success("计算任务已执行，时间：".date('Y-m-d H:i:s'));
		} catch (Exception $e) {
			$this->output->error("计算失败：".$e->getMessage());
		}
	}

	/**
	 * 设置命令参数
	 *
	 * @access   protected
	 */
	protected function checkParams()
	{
		$this->date = $this->input->getOption('date') ?: date('Ymd');

		if (!preg_match('/^\d{8}$/', $this->date)) {
			$this->output->error('日期格式不正确');
			die;
		}
		$this->days = $this->input->getOption('days') ?: 1;
		if (!is_numeric($this->days)) {
			$this->output->error('天数格式不正确');
			die;
		}
	}

	/**
	 * 执行计算逻辑
	 *
	 * @access   public
	 * @return void
	 */
	public function Calculate()
	{
		for ($i = 0; $i < $this->days; $i++) {
			$this->runItem(date('Ymd', strtotime("+{$i} days", strtotime($this->date))));
		}
	}

	/**
	 * 执行每一天的计算逻辑
	 *
	 * @access   protected
	 * @param $date integer 日期
	 *
	 * @return void
	 */
	protected function runItem($date)
	{
		//如果是未来的时间，则不需要执行
		if ($date > date('Ymd')) {
			return;
		}

		//后续增加source 在source_cal中添加定义
		foreach ($this->source_cal as $source => $calcol) {

			try {
                DB::beginTransaction();
				// 先计算到money_agent
				BillCustomerIncomeV2::where([
					'date'=>$date,
					'source'=>$source,
					'money_agent'=>0,
				])
				->update(['money_agent'=> DB::raw($calcol)]);

				BillProductIncomeV2::where([
					'date'=>$date,
					'source'=>$source,
					'money_agent'=>0,
				])->update(['money_agent'=>DB::raw($calcol)]);

				// 将money agent 赋值 给money 防止多次计算
                /*
				BillCustomerIncomeV2::where([
					'date'=>$date,
					'source'=>$source,
				])
				->update(['money'=> DB::raw('`money_agent`')]);

				BillProductIncomeV2::where([
					'date'=>$date,
					'source'=>$source,
				])->update(['money'=>DB::raw('`money_agent`')]);
                */

				DB::commit();
			} catch (Exception $e) {
				DB::rollBack();
				Log::error('command credit_agency:calculate_money_agent error:'.$e->getMessage().'   date:'.$date.'  source:'.$source.'  time:'.date('Y-m-d H:i:s'));
			}

		}
	}
}
