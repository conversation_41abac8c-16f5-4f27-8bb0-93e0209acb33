<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/26 0026
 * Time: 14:34
 */

namespace App\Console\Commands;


use App\Models\EmailConfig;
use App\Models\MongoUpstreamBill;
use App\Models\Product;
use App\Models\Upstream;
use App\TraitUpgrade\ExcelTrait;
use App\TraitUpgrade\MailTrait;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

//渠道成本账单日报
class ReportDayUpstreamChannelBill extends Command
{
	use WechatExceptionTrait, ExcelTrait, MailTrait;
	
	protected $signature   = 'reportDay:upstreamChannel
    {--date= : 发送某日的渠道成本统计，日期格式为Ymd}
    {--addressee= : 发送收件人，设置此项后将不使用邮件配置中的邮箱}';
	protected $description = '成本账单渠道周报表';
	
	protected $addressee = [];
	protected $date;
	protected $scene     = 'day_report_upsteam_channel_bill';
	
	//数据源标识与名称映射关系表
	protected $upstreamFieldNameMapping;
	
	//数据源与展示的报表的映射关系表
	protected $upstreamChildrenMapping;
	
	public function handle()
	{
		$res = $this->setOptions();
		if ($res) {
			try {
				//初始化需要展示的数据源
				$this->initUpstreamContiner();
				
				//获取本月的数据
				$data = $this->getCurrentMonthUpstreamBillInfo();
				//生成Excel
				$filepath = $this->createExcel($data);
				
				//生成html
				$html = $this->createHtml($data);
				
				//获取收件人、抄送人
				$addressee = $this->getEmailConfig();
				
				$recipients = array_get($addressee, 'recipients');
				$cc         = array_get($addressee, 'cc');
				//发送邮件
				
				$res = $this->setSubject('成本账单渠道日报表')
							->setRecipients($recipients)
							->setCC($cc)
							->setAttachment([
								[
									'filepath' => $filepath,
									'name'     => "渠道成本日报表_{$this->date}.xlsx",
								],
							])
							->sendMail($html);
				
				if (!$res) {
					throw new \Exception('邮件发送失败');
				}
			} catch (\Exception $exception) {
				
				halt($exception->getLine(), $exception->getMessage());
				//$this->wechatException("【{$this->name}】任务执行失败，原因是：{$exception->getMessage()}");
				//$this->output->error($exception->getMessage());
			}
		}
	}
	
	/**
	 * 获取邮件收件人、抄送人
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getEmailConfig()
	{
		if (empty($this->addressee)) {
			$emailConfig = new EmailConfig();
			
			return $emailConfig->getRecipientsAndCCByScene($this->scene);
		}
		$recipients = array_map(function ($address) {
			list($name, $domain) = explode('@', $address);
			
			return compact('name', 'address');
		}, $this->addressee);
		$cc         = [];
		
		return compact('cc', 'recipients');
	}
	
	/**
	 * 创建HTML
	 *
	 * @access protected
	 *
	 * @param $data array HTML中展示的数据
	 *
	 * @return string
	 **/
	protected function createHtml($data)
	{
		$date  = date('Y-m-d', strtotime($this->date));
		$title = "以下是{$date}渠道成本日报表，请查阅";
		$table = $this->setHtmlBody($data);
		$html  = <<<HTML
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>{$title}</title>
        <style>
            #tableArea { width:100%; height:auto; font-family: Arial, Helvetica, sans-serif; font-size: 14px; }
            .title { width:100%; height:26px; line-height:26px; color:#333; font-size:16px; font-weight:bold; text-align: center; }
            .table { width:98%; height:auto; overflow-x:auto; margin:20px auto; }
            .table table { height:auto; color:#333; border-top:1px solid #ccc; border-left:1px solid #ccc; }
            .table table tr:hover{ background:#eeeeee; }
            .table table th,.table table td { border-bottom: 1px solid #ccc; border-right: 1px solid #ccc; text-align:center; padding:4px 10px; box-sizing:border-box; }
            .table table th { font-weight:bold; min-width:80px !important; }
            .td_red{color:red;}
            .td_green{color:green;}
            h2 {
				width        : 98%;
				height       : 44px;
				line-height  : 44px;
				font-size    : 20px;
				font-weight  : bold;
				text-align   : center;
				margin       : 20px auto 2px;
				background   : rgba(229, 82, 45, 1);
				color        : #FFFFFF;
			}
        </style>
    </head>
    <body>
        <div id="tableArea">
            <div class="title">{$title}</div>
            {$table}
        </div>
    </body>
</html>
HTML;
		
		return $html;
	}
	
	/**
	 * 构造HTML的TABLE内容
	 *
	 * @access protected
	 *
	 * @param $data array HTML中展示的数据
	 *
	 * @return string
	 **/
	protected function setHtmlBody($data)
	{
		//		//仅显示今日的数据
		//		$data = array_filter($data, function ($item) {
		//			return $item['date'] == $this->date;
		//		});
		//
		//		$total = [
		//			'money'  => array_sum(array_column($data, 'money')),
		//			'number' => array_sum(array_column($data, 'number')),
		//		];
		$html = '';
		
		//渠道维度
		$html .= '<h2>渠道维度</h2>';
		$html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
		$html .= "<tr><th>渠道名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
		$html .= "<tr><th align='center'>合计</th><td align='center'>{$data['total']['fee_number']}</td><td align='center'>{$data['total']['money']}</td></tr>";
		foreach ($data['channel'] as $item) {
			$html .= "<tr><td align='center'>{$item['name']}</td><td>{$item['number']}</td><td>{$item['money']}</td></tr>";
		}
		$html .= "</table></div>";
		
		//产品维度
		$html .= '<h2>产品维度</h2>';
		$html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
		$html .= "<tr><th>产品名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
		$html .= "<tr><th align='center'>合计</th><td align='center'>{$data['total']['fee_number']}</td><td align='center'>{$data['total']['money']}</td></tr>";
		foreach ($data['product'] as $item) {
			$html .= "<tr><td align='center'>{$item['name']}</td><td>{$item['number']}</td><td>{$item['money']}</td></tr>";
		}
		$html .= "</table></div>";
		
		//产品渠道维度
		$html .= '<h2>渠道产品维度</h2>';
		$html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
		$html .= "<tr><th>渠道名称</th><th>产品名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
		$html .= "<tr><th align='center' colspan='2'>合计</th><td align='center'>{$data['total']['fee_number']}</td><td align='center'>{$data['total']['money']}</td></tr>";
		foreach ($data['productChannel'] as $item) {
			$html .= "<tr><td align='center'>{$item['channel_name']}</td><td align='center'>{$item['product_name']}</td><td>{$item['number']}</td><td>{$item['money']}</td></tr>";
		}
		$html .= "</table></div>";
		
		return $html;
		
		//标题
		//$table = '<tr><th>渠道名称</th> <th>产品名称</th> <th>运营商</th> <th>成本单价</th> <th>计费调用量</th> <th>权责成本（元）</th></tr>';
		
		//合计
		//		$number = number_format($total['number']);
		//		$money  = number_format($total['money'], 2);
		//		$table  .= "<tr><td>合计</td><td>--</td><td>--</td><td>--</td><td>{$number}</td><td>{$money}</td></tr>";
		//
		//		//内容
		//		array_walk($data, function ($item) use (&$table) {
		//			$number = number_format($item['number']);
		//			$money  = number_format($item['money'], 2);
		//			$table  .= "<tr> <td>{$item['channel']}</td> <td>{$item['product']}</td> <td>{$item['operator']}</td> <td>{$item['price']}</td> <td>{$number}</td> <td>{$money}</td> </tr>";
		//		});
		//
		//		return $table;
	}
	
	/**
	 * 创建Excel
	 *
	 * @access protected
	 *
	 * @param $data array excel中展示的数据
	 *
	 * @return string 文件路径
	 **/
	protected function createExcel($data)
	{
		$this->file_out_init();
		$this->setSheetTitle('渠道维度');
		
		$this->setWidth([24, 18, 18]);
		$this->addRowContent(['渠道名称', '计费用量', '金额'], 18, true);
		$this->addRowContent(['合计', $data['total']['fee_number'], $data['total']['money']], 16);
		$this->setBold('A2');
		foreach ($data['channel'] as $item) {
			$this->addRowContent([$item['name'], $item['number'], $item['money']], 16);
		}
		
		$this->php_excel->createSheet(1);
		$this->cutSheet(1);
		$this->setSheetTitle('产品维度');
		$this->col = 1;
		$this->setWidth([24, 18, 18]);
		$this->addRowContent(['渠道名称', '计费用量', '金额'], 18, true);
		$this->addRowContent(['合计', $data['total']['fee_number'], $data['total']['money']], 16);
		$this->setBold('A2');
		foreach ($data['product'] as $item) {
			$this->addRowContent([$item['name'], $item['number'], $item['money']], 16);
		}
		
		$this->php_excel->createSheet(2);
		$this->cutSheet(2);
		$this->setSheetTitle('渠道产品维度');
		$this->col = 1;
		$this->setWidth([24, 24, 18, 18]);
		$this->addRowContent(['渠道名称', '产品名称', '计费用量', '金额'], 18, true);
		$this->addRowContent(['合计', '', $data['total']['fee_number'], $data['total']['money']], 16);
		$this->setBold('A2');
		$this->mergeCell('A2:B2');
		foreach ($data['productChannel'] as $item) {
			$this->addRowContent([$item['channel_name'], $item['product_name'], $item['number'], $item['money']], 16);
		}
		
		
		//设置导出的内容
		//$this->setExcelBody($data);
		
		//保存的文件名称
		$baseDir = app()->basePath() . '/storage/upstreamChannelBill';
		if (!is_dir($baseDir)) {
			mkdir($baseDir, 0755);
		}
		$time     = time();
		$filepath = "{$baseDir}/day_{$this->date}_{$time}.xlsx";
		
		//设置sheet标题
		//保存文件
		$this->save($filepath);
		
		return $filepath;
	}
	
	/**
	 * 设置Excel的内容
	 *
	 * @access protected
	 *
	 * @param $data array excel中展示的数据
	 *
	 * @return void
	 **/
	protected function setExcelBody($data)
	{
		$total = [
			'money'  => array_sum(array_column($data, 'money')),
			'number' => array_sum(array_column($data, 'number')),
		];
		
		$this->setWidth([18, 18, 18, 18, 18, 18, 20]);
		
		//标题
		$this->addRowContent(['日期', '渠道名称', '产品名称', '运营商', '成本单价', '计费调用量', '权责成本（元）'], 18, true);
		
		//合计
		$this->setCellValue('A2', '合计');
		$this->setCellValue('C2', '--');
		$this->setCellValue('D2', '--');
		$this->setCellValue('E2', '--');
		$this->setCellValue('F2', number_format($total['number']));
		$this->setCellValue('G2', number_format($total['money']));
		$this->setCenter('A2:G2');
		$this->setHeight(18, '2');
		$this->mergeCell('A2:B2');
		$this->col++;
		
		//主要内容
		foreach ($data as $item) {
			$this->addRowContent([
				$item['date'],
				$item['channel'],
				$item['product'],
				$item['operator'],
				$item['price'],
				number_format($item['number']),
				number_format($item['money'], 2),
			], 16, false);
		}
	}
	
	/**
	 * 获取本月渠道成本数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getCurrentMonthUpstreamBillInfo()
	{
		$data = MongoUpstreamBill::query()
								 ->raw(function ($collection) {
									 $aggregate = [
										 [
											 '$match' => [
												 'date' => intval($this->date),
											 ],
										 ],
										 [
											 '$group' => [
												 '_id'        => [
													 'channel'    => '$channel',
													 'product_id' => '$product_id',
													 'date'       => '$date',
												 ],
												 'money'      => [
													 '$sum' => '$money',
												 ],
												 'fee_number' => [
													 '$sum' => '$fee_number',
												 ],
												 'channel'    => [
													 '$first' => '$channel',
												 ],
												 'product_id' => [
													 '$first' => '$product_id',
												 ],
											 ],
										 ],
										 [
											 '$project' => [
												 '_id'        => 0,
												 'money'      => 1,
												 'fee_number' => 1,
												 'channel'    => 1,
												 'product_id' => 1,
											 ],
										 ],
									 ];
			
									 return $collection->aggregate($aggregate);
								 })
								 ->toArray();
		
		//属于邦信分快捷版通信评分产品的渠道
		$channel_210_score = Upstream::where('product_id', 210)
									 ->where('type', 1)
									 ->pluck('channel')
									 ->toArray();
		//属于邦信分快捷版通信字段产品的渠道
		$channel_210_field = Upstream::where('product_id', 210)
									 ->where('type', 2)
									 ->pluck('channel')
									 ->toArray();
		
		//快捷版渠道信息
		$channel_stat = Product::where('product_id', 210)
							   ->value('channel_stat');
		if (empty($channel_stat)) {
			throw new \Exception('210产品数据源配置不存在');
		}
		$channel_stat             = json_decode($channel_stat, true);
		$interface_item_210       = array_get($channel_stat, 'interface_item');
		$interface2ChannelMapping = [];
		foreach ($interface_item_210 as $channel => $item) {
			foreach ($item as $interface => $temp) {
				$interface2ChannelMapping[$channel . '_' . $interface] = $channel;
			}
		}
		
		$upstream_210 = array_get($channel_stat, 'upstream');
		
		//邦秒验渠道信息
		$channel_stat = Product::where('product_id', 200)
							   ->value('channel_stat');
		if (empty($channel_stat)) {
			throw new \Exception('200产品数据源配置不存在');
		}
		$channel_stat = json_decode($channel_stat, true);
		$upstream_200 = array_filter(array_get($channel_stat, 'upstream'), function ($item) {
			$postfixMD5    = substr($item, -3);
			$postfixSHA256 = substr($item, -6);
			
			return $postfixMD5 != 'MD5' && $postfixSHA256 != 'SHA256';
		});
		
		//邦企查的渠道信息
		$channel_stat = Product::where('product_id', 401)
							   ->value('channel_stat');
		if (empty($channel_stat)) {
			throw new \Exception('401产品数据源配置不存在');
		}
		$channel_stat = json_decode($channel_stat, true);
		$upstream_401 = array_get($channel_stat, 'upstream');
		
		//渠道维度
		$channel = array_map(function ($name) {
			$money  = 0;
			$number = 0;
			
			return compact('name', 'number', 'money');
		}, array_merge($upstream_210, $upstream_200, $upstream_401));
		unset($channel['chuanglan']);
		$channel['chuanglan_200'] = ['name' => '创蓝-邦秒验', 'number' => 0, 'money' => 0];
		$channel['chuanglan_401'] = ['name' => '创蓝-邦企查', 'number' => 0, 'money' => 0];
		
		//产品维度
		$product              = Product::where('father_id', 200)
									   ->pluck('product_name', 'product_id')
									   ->toArray();
		$product[401]         = '邦企查';
		$product['210_score'] = '邦信分-通信评分';
		$product['210_field'] = '邦信分-通信字段';
		$product              = array_map(function ($name) {
			$money  = 0;
			$number = 0;
			
			return compact('name', 'number', 'money');
		}, $product);
		
		
		//产品、渠道
		$productChannel = [];
		
		
		//分别整理成渠道维度、产品维度、渠道产品维度
		array_walk($data, function ($item) use (&$channel, &$product, &$productChannel, $interface2ChannelMapping, $channel_210_score, $channel_210_field) {
			$itemChannel   = $item['channel'];
			$itemProductId = $item['product_id'];
			$itemNumber    = $item['fee_number'];
			$itemMoney     = $item['money'];
			
			$productChannelKey = [];
			if (210 == $itemProductId) {
				if (!array_key_exists($itemChannel, $interface2ChannelMapping)) {
					return;
				}
				$channel[$interface2ChannelMapping[$itemChannel]]['number'] += $itemNumber;
				$channel[$interface2ChannelMapping[$itemChannel]]['money']  += $itemMoney;
				$productChannelKey[]                                        = $interface2ChannelMapping[$itemChannel];
				
				if (in_array($itemChannel, $channel_210_score)) {
					$product['210_score']['number'] += $itemNumber;
					$product['210_score']['money']  += $itemMoney;
					$productChannelKey[]            = '210_score';
				} else if (in_array($itemChannel, $channel_210_field)) {
					$product['210_field']['number'] += $itemNumber;
					$product['210_field']['money']  += $itemMoney;
					$productChannelKey[]            = '210_field';
				}
				
			} else if (401 == $itemProductId) {
				if ('chuanglan' == $itemChannel) {
					$channel['chuanglan_401']['number'] += $itemNumber;
					$channel['chuanglan_401']['money']  += $itemMoney;
					$productChannelKey[]                = 'chuanglan_401';
				} else {
					$channel[$itemChannel]['number'] += $itemNumber;
					$channel[$itemChannel]['money']  += $itemMoney;
					$productChannelKey[]             = $itemChannel;
				}
				
				$product[401]['number'] += $itemNumber;
				$product[401]['money']  += $itemMoney;
				$productChannelKey[]    = 401;
			} else {
				$itemChannel = rtrim(rtrim($itemChannel, 'MD5'), 'SHA256');
				if ('chuanglan' == $itemChannel) {
					$channel['chuanglan_200']['number'] += $itemNumber;
					$channel['chuanglan_200']['money']  += $itemMoney;
					$productChannelKey[]                = 'chuanglan_200';
				} else {
					$channel[$itemChannel]['number'] += $itemNumber;
					$channel[$itemChannel]['money']  += $itemMoney;
					$productChannelKey[]             = $itemChannel;
				}
				
				$product[$itemProductId]['number'] += $itemNumber;
				$product[$itemProductId]['money']  += $itemMoney;
				$productChannelKey[]               = $itemProductId;
			}
			
			$productChannelKeyString = implode('', $productChannelKey);
			
			if (!array_key_exists($productChannelKeyString, $productChannel)) {
				$productChannel[$productChannelKeyString] = [
					'product_name' => $product[$productChannelKey[1]]['name'],
					'channel_name' => $channel[$productChannelKey[0]]['name'],
					'number'       => 0,
					'money'        => 0,
				];
			}
			
			$productChannel[$productChannelKeyString]['number'] += $itemNumber;
			$productChannel[$productChannelKeyString]['money']  += $itemMoney;
		});
		
		$total = [
			'fee_number' => number_format(array_sum(array_column($data, 'fee_number'))),
			'money'      => number_format(array_sum(array_column($data, 'money')), 2),
		];
		
		//过滤、排序、格式化
		$channel = array_filter($channel, function ($item) {
			return 0 != $item['money'] && 0 != $item['number'];
		});
		array_multisort(array_column($channel, 'money'), SORT_DESC, $channel);
		$channel = array_map(function ($item) {
			$item['number'] = number_format($item['number']);
			$item['money']  = number_format($item['money'], 2);
			
			return $item;
		}, $channel);
		
		$product = array_filter($product, function ($item) {
			return 0 != $item['money'] && 0 != $item['number'];
		});
		array_multisort(array_column($product, 'money'), SORT_DESC, $product);
		$product = array_map(function ($item) {
			$item['number'] = number_format($item['number']);
			$item['money']  = number_format($item['money'], 2);
			
			return $item;
		}, $product);
		
		$productChannel = array_filter($productChannel, function ($item) {
			return 0 != $item['money'] && 0 != $item['number'];
		});
		array_multisort(array_column($productChannel, 'channel_name'), SORT_DESC, array_column($productChannel, 'money'), SORT_DESC, $productChannel);
		$productChannel = array_map(function ($item) {
			$item['number'] = number_format($item['number']);
			$item['money']  = number_format($item['money'], 2);
			
			return $item;
		}, $productChannel);
		
		return compact('channel', 'product', 'productChannel', 'total');
		//		halt($channel);
		//		//渠道维度
		//		$channel = [];
		//		//产品维度
		//		$product = [];
		//		//渠道产品维度
		//		$channel_product = [];
		//
		//
		//		//先求合计
		//		$total = [
		//			'fee_number' => array_sum(array_column($data, 'fee_number')),
		//			'money'      => array_sum(array_column($data, 'money')),
		//		];
		//
		//
		//		halt($data);
		//
		//
		//		$result = [];
		//
		//		//获取产品映射表
		//		$productMapping = Product::pluck('product_name', 'product_id');
		//
		//		//对每条的数据
		//		array_walk($data, function ($item) use (&$result, $productMapping) {
		//			$this->disposeItemBill($item, $result, $productMapping);
		//		});
		//
		//		//过滤掉费用为0的、计费用量为0的数据
		//
		//		$result = array_filter(array_values($result), function ($item) {
		//			return array_get($item, 'money') != 0 || array_get($item, 'number') != 0;
		//		});
		//
		//		//排序
		//		array_multisort(array_column($result, 'date'), SORT_DESC, array_column($result, 'money'), SORT_DESC, $result);
		//
		//		return $result;
	}
	
	/**
	 * 获取成本统计
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/2 10:08
	 *
	 * @param $fields array 查询字段
	 * @param $where  array 查询条件
	 *
	 * @return array
	 */
	protected function getAggregateInfo($group, $match, $project)
	{
		return MongoUpstreamBill::query()
								->raw(function ($collection) use ($group, $match, $project) {
									$aggregate = [
										[
											'$match' => $match,
										],
										[
											'$group' => $group,
										],
										[
											'$project' => $project,
										],
									];
			
									return $collection->aggregate($aggregate);
								})
								->toArray();
	}
	
	/**
	 * 对每一天数据处理，判断数据那个产品，属于那个渠道，数据哪天，并累计用量和费用
	 *
	 * @access protected
	 *
	 * @param $data           array 每条统计数据
	 * @param &$result        array 统计的结果数据
	 * @param $productMapping array 产品映射关系表
	 *
	 * @return void
	 **/
	protected function disposeItemBill($data, &$result, $productMapping)
	{
		//获取数据源的标识
		$upstream = array_get($this->upstreamChildrenMapping, $data['channel']);
		if (empty($upstream)) {
			throw new \Exception("存在未知的数据源：{$data['channel']}");
		}
		
		//基础数据
		$base = [
			'date'     => '',
			'channel'  => '',
			'product'  => '',
			'operator' => '',
			'price'    => 0,
			'number'   => 0,
			'money'    => 0,
		];
		
		$productId = $data['product_id'];
		$date      = $data['date'];
		$channel   = array_get($this->upstreamFieldNameMapping, $upstream);
		if (empty($channel)) {
			throw new \Exception("存在未知的数据源名称：{$upstream}");
		}
		$product = array_get($productMapping, $productId);
		if (empty($product)) {
			throw new \Exception("存在未知的产品名称：{$product}");
		}
		
		$uniqueKey = $date . '_' . $upstream . '_' . $productId;
		switch ($productId) {
			//快捷版
			case 210:
				if (!array_key_exists($uniqueKey, $result)) {
					$result[$uniqueKey]             = $base;
					$result[$uniqueKey]['date']     = $date;
					$result[$uniqueKey]['channel']  = $channel;
					$result[$uniqueKey]['product']  = $product;
					$result[$uniqueKey]['operator'] = '--';
					$result[$uniqueKey]['price']    = '--';
				}
				$result[$uniqueKey]['number'] += $data['fee_number'];
				$result[$uniqueKey]['money']  += $data['money'];
				break;
			//邦企查
			case 401:
				if (!array_key_exists($uniqueKey, $result)) {
					$result[$uniqueKey]             = $base;
					$result[$uniqueKey]['date']     = $date;
					$result[$uniqueKey]['channel']  = $channel;
					$result[$uniqueKey]['product']  = $product;
					$result[$uniqueKey]['operator'] = '--';
					$result[$uniqueKey]['price']    = '--';
				}
				$result[$uniqueKey]['number'] += $data['fee_number'];
				$result[$uniqueKey]['money']  += $data['money'];
				break;
			//邦秒验
			default:
				//移动
				$uniqueKey = $date . '_' . $upstream . '_' . $productId . '_yd';
				if (!array_key_exists($uniqueKey, $result)) {
					$result[$uniqueKey]             = $base;
					$result[$uniqueKey]['date']     = $date;
					$result[$uniqueKey]['channel']  = $channel;
					$result[$uniqueKey]['product']  = $product;
					$result[$uniqueKey]['operator'] = '移动';
					$result[$uniqueKey]['price']    = $data['price_yd'];
				}
				$result[$uniqueKey]['number'] += $data['yd_number'];
				$result[$uniqueKey]['money']  += $data['yd_money'];
				
				//联通
				$uniqueKey = $date . '_' . $upstream . '_' . $productId . '_lt';
				if (!array_key_exists($uniqueKey, $result)) {
					$result[$uniqueKey]             = $base;
					$result[$uniqueKey]['date']     = $date;
					$result[$uniqueKey]['channel']  = $channel;
					$result[$uniqueKey]['product']  = $product;
					$result[$uniqueKey]['operator'] = '联通';
					$result[$uniqueKey]['price']    = $data['price_lt'];
				}
				$result[$uniqueKey]['number'] += $data['lt_number'];
				$result[$uniqueKey]['money']  += $data['lt_money'];
				
				//电信
				$uniqueKey = $date . '_' . $upstream . '_' . $productId . '_dx';
				if (!array_key_exists($uniqueKey, $result)) {
					$result[$uniqueKey]             = $base;
					$result[$uniqueKey]['date']     = $date;
					$result[$uniqueKey]['channel']  = $channel;
					$result[$uniqueKey]['product']  = $product;
					$result[$uniqueKey]['operator'] = '电信';
					$result[$uniqueKey]['price']    = $data['price_dx'];
				}
				$result[$uniqueKey]['number'] += $data['dx_number'];
				$result[$uniqueKey]['money']  += $data['dx_money'];
				break;
		}
	}
	
	/**
	 * 初始化数据源容器
	 *
	 * @access protected
	 *
	 * @return void
	 **/
	protected function initUpstreamContiner()
	{
		return;
		//获取渠道维度的数据
		$channel = [];
		//210产品的数据源的种类
		$channel_stat = Product::where('product_id', 210)
							   ->value('channel_stat');
		if (empty($channel_stat)) {
			throw new \Exception('210产品数据源配置不存在');
		}
		$channel_stat = json_decode($channel_stat, true);
		foreach (array_get($channel_stat, 'interface_item') as $field => $childrenUpstream) {
			
			
			halt($field);
			halt($childrenUpstream);
			foreach ($childrenUpstream as $childrenField => $childrenName) {
				$upstreamMapping[$field . '_' . $childrenField] = $field;
			}
		}
		
		
		//成本账单记录的数据源与需要展示的渠道映射关系表
		$upstreamMapping = [];
		
		//数据源标识与名称的映射关系表
		$upstream = [];
		
		
		//200产品的数据源的种类
		$channel_stat = Product::where('product_id', 200)
							   ->value('channel_stat');
		if (empty($channel_stat)) {
			throw new \Exception('200产品数据源配置不存在');
		}
		$channel_stat = json_decode($channel_stat, true);
		foreach (array_get($channel_stat, 'upstream') as $field => $name) {
			$upstreamMapping[$field] = $field;
			$upstream[$field]        = $name;
		}
		
		//401产品的数据源的种类
		$channel_stat = Product::where('product_id', 401)
							   ->value('channel_stat');
		if (empty($channel_stat)) {
			throw new \Exception('401产品数据源配置不存在');
		}
		$channel_stat = json_decode($channel_stat, true);
		$upstream     = array_merge($upstream, array_get($channel_stat, 'upstream'));
		
		foreach (array_get($channel_stat, 'upstream') as $field => $name) {
			$upstreamMapping[$field] = $field;
		}
		
		$this->upstreamFieldNameMapping = $upstream;
		$this->upstreamChildrenMapping  = $upstreamMapping;
	}
	
	/**
	 * 设置参数
	 *
	 * @access protected
	 *
	 * @return boolean
	 **/
	protected function setOptions()
	{
		$date       = $this->input->getOption('date');
		$this->date = empty($date) ? date('Ymd', strtotime('-1 days')) : $date;
		if (!preg_match('/^\d{8}$/', $this->date)) {
			$this->output->error('日期格式不正确');
			
			return false;
		}
		
		$addressee = $this->input->getOption('addressee');
		if (!empty($addressee)) {
			$this->addressee = explode(',', $addressee);
		}
		
		return true;
	}
	
}