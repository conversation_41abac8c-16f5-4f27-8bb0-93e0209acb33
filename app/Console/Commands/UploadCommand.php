<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Upload;
class UploadCommand extends Command
{
    /**
     * 命令行执行命令
     */
    protected $signature = 'jindun:deljindundata';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        //使用过的数据保留7天
        $where = ['status' => 1, 'bj'=>1];
        $info = Upload::where($where)
            ->get()
            ->toArray();
        if(empty($info)){
            return false;
        }
        $arr_id = [];
        $time = time();
        array_walk($info, function($val) use (&$arr_id, $time){
            if(($time - $val['created_at']) > 604800){
                $arr_id[] = $val['id'];
            }
        });
        if(empty($arr_id)){
            return false;
        }
        $res = Upload::whereIn('id', $arr_id)->delete();
        if($res){
            return true;
        }
        return false;
    }
}