<?php
/**
 * channel_account 表数据更新
 */

namespace App\Console\Commands\Operator;

use App\Models\ChannelAccount;
use App\Models\ChannelInterface;
use App\Models\StatisticsInterfaceUsage;
use Illuminate\Console\Command;

class UpdateChannelAccountData extends Command
{
    protected $signature = "operator:update_channel_account_data {
    --func= : 方法名称，updateDailyCallNum-更新日均调用量
    }";

    protected $description = 'channel_account 表数据更新';

    public function handle()
    {
        $func = $this->option('func') ?: '';
        switch ($func) {
            case 'updateDailyCallNum' :
                $this->updateDailyCallNum();
                break;
            default:
                exit('方法不存在');
        }
        exit('DONE!');
    }

    protected function updateDailyCallNum()
    {
        $channelAccountModel = new ChannelAccount();
        $statisticsInterfaceUsageModel = new StatisticsInterfaceUsage();
        //获取账户信息
        $channelAccountDatas = $channelAccountModel->get()->toArray();
        $channelIds = array_column($channelAccountDatas, 'channel_id');
        $res = (new ChannelInterface())->select('channel_id', 'id')
            ->whereIn('channel_id', $channelIds)
            ->get()->toArray();
        $channelInterfacesMap = [];
        foreach ($res as $item) {
            if (!isset($channelInterfacesMap[$item['channel_id']])) {
                $channelInterfacesMap[$item['channel_id']] = [];
            }
            $channelInterfacesMap[$item['channel_id']][] = $item['id'];
        }

        $endData = date('Ymd', strtotime('yesterday'));
        $startData = date('Ymd', strtotime($endData . ' -6 days'));
        foreach ($channelAccountDatas as $item) {
            $channelInterfaceId = [];
            if (isset($channelInterfacesMap[$item['channel_id']])) {
                $channelInterfaceId = $channelInterfacesMap[$item['channel_id']];
            }

            //全国移动特殊处理
            switch ($item['id']) {
                case 1://全国Y01
                    $channelInterfaceId = [257];
                    break;
                case 2://全国Y02
                    $channelInterfaceId = [440];
                    break;
            }

            if (!$channelInterfaceId) {
                continue;
            }

            //获取近7天调用量
            $where = [
                ['date', '>=', $startData],
                ['date', '<=', $endData],
            ];
            $statData = $statisticsInterfaceUsageModel->where($where)
                                ->whereIn('interface_id', $channelInterfaceId)
                                ->sum('success');
            $statData = ceil($statData / 7);
            $save = [
                'daily_call_num' => $statData
            ];
            $res = $channelAccountModel->where('id', $item['id'])->update($save);
            var_dump($res, [$item['id'], $statData]);
        }
        var_dump('DONE!');
    }
}