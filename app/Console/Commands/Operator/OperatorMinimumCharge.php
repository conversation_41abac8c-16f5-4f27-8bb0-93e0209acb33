<?php
/**
 * 运营商最低消费处理
 */

namespace App\Console\Commands\Operator;

use App\Models\BillCost;
use App\Models\ChannelAccount;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\ChannelAccountMinChargeSupplement;
use App\Models\ChannelInterface;
use Illuminate\Console\Command;

class OperatorMinimumCharge extends Command
{
    protected $signature = "operator:minimum_charge {
    --func= : 方法名称，init-初始化；done-处理最低消费
    }";

    protected $description = '运营商最低消费处理';

    /**
     * 低消配置数据
     */
    protected $minChargeOption = [
        3 => ['minCharge' => 50000],
        4 => ['minCharge' => 150000],
        5 => ['minCharge' => 50000],
        7 => ['minCharge' => 50000],
        8 => ['minCharge' => 50000],
        10 => ['minCharge' => 50000],
        11 => ['minCharge' => 50000],
        19 => ['minCharge' => 200000],
        20 => ['minCharge' => 50000],
        21 => ['minCharge' => 50000]
    ];

    public function handle()
    {
        $func = $this->option('func') ?: 'done';
        switch ($func) {
            case 'init' :
                $this->init();
                break;
            case 'done':
                $this->done();
                break;
            default:
                exit('方法不存在');
        }
        exit('DONE!');
    }

    protected function init()
    {
        $balanceStartDate = '2022-09-27';
        $map = $this->getCsvFileData('operator.csv');
        $channelAccountModel = new ChannelAccount();
        $channelAccountMinChargeSuModel = new ChannelAccountMinChargeSupplement();
        foreach ($map as $item) {
            //更新 channel_account 表数据（累计余额）
            $where = [
                ['id', '=', $item['id']]
            ];
            $save = [
                'balance_start_date' => $balanceStartDate,
                'balance' => $item['totalBalance'],
                'max_call_num' => $item['max_call_num'],
                'daily_call_num' => $item['daily_call_num'],
            ];
            $channelAccountModel->where($where)->update($save);
            //添加保底补偿记录
            if (!$item['floorCost']) {
                continue;
            }
            $supplementMoney = bcsub($item['floorCost'], $item['totalCost'], 3);
            $addData =[
                'channel_account_id' => $item['id'],
                'money' => $supplementMoney,
                'date' => '2022-08-01',
                'remark' => 'init',
                'create_time' => time(),
            ];
            $channelAccountMinChargeSuModel->add($addData);
        }
    }

    protected function done()
    {
        //获取前一个月的总消耗费用
        $startDate = date('Ym01', strtotime(date('Ym01') . ' -1 month'));
        $endDate = date('Ymd', strtotime(date('Ym01') . ' -1 days'));

        //八月初始化，无需计算
        if ($startDate == '********') {
            return true;
        }
        $channelAccountModel = new ChannelAccount();
        $channelAccountMinChargeSuModel = new ChannelAccountMinChargeSupplement();
        $channelAccountIds = array_keys($this->minChargeOption);
        $channelDatas = $channelAccountModel->whereIn('id', $channelAccountIds)->get()->toArray();
        //获取渠道账号信息
        $channelIds = array_column($channelDatas, 'channel_id');
        $res = (new ChannelInterface())->select('channel_id', 'id')
            ->whereIn('channel_id', $channelIds)
            ->get()->toArray();
        $channelInterfacesMap = [];
        foreach ($res as $item) {
            if (!isset($channelInterfacesMap[$item['channel_id']])) {
                $channelInterfacesMap[$item['channel_id']] = [];
            }
            $channelInterfacesMap[$item['channel_id']][] = $item['id'];
        }
        unset($res);

        foreach ($channelDatas as $v) {
            $channelInterfaceId = [];
            if (isset($channelInterfacesMap[$v['channel_id']])) {
                $channelInterfaceId = $channelInterfacesMap[$v['channel_id']];
            }

            if (!$channelInterfaceId) {
                continue;
            }

            //接口总调用费用
            $totalMoney = BillCost::getSumMoneyByDate($channelInterfaceId, $startDate, $endDate);
            $totalMoney = empty($totalMoney['totalmoney']) ? 0 : $totalMoney['totalmoney'];
            $adjust = ChannelAccountAdjust::getAccountMoney($startDate,$channelInterfaceId, $endDate);
            $fixedFee = ChannelAccountFixedFee::getAccountMoney($startDate,$v['id'], $endDate);
            $totalMoney = bcadd($totalMoney, $adjust, 3);
            $totalMoney = bcadd($totalMoney, $fixedFee, 3);
            $option = $this->minChargeOption[$v['id']];

            $suppWhere = [
                ['channel_account_id', $v['id']],
                ['date', date('Y-m-d', strtotime($startDate))],
            ];
            $existData = $channelAccountMinChargeSuModel->where($suppWhere)->first();

            if (!$existData) {
                if ($totalMoney >= $option['minCharge']) {
                    continue;
                }

                //添加低消补偿记录
                $supplementMoney = bcsub($option['minCharge'], $totalMoney, 3);

                $addData =[
                    'channel_account_id' => $v['id'],
                    'money' => $supplementMoney,
                    'date' => date('Y-m-d', strtotime($startDate)),
                    'remark' => $startDate . ':消耗' . $totalMoney,
                    'create_time' => time(),
                ];
                $channelAccountMinChargeSuModel->add($addData);
                continue;
            }

            if ($totalMoney >= $option['minCharge']) {
                $channelAccountMinChargeSuModel->where('id', $existData->id)->delete();
                continue;
            }

            $supplementMoney = bcsub($option['minCharge'], $totalMoney, 3);
            if ($existData->money == $supplementMoney) {
                continue;
            }
            $saveData =[
                'money' => $supplementMoney,
                'remark' => $startDate . ':消耗' . $totalMoney,
                'update_time' => time(),
            ];
            $channelAccountMinChargeSuModel->where('id', $existData->id)->update($saveData);
        }
    }

    /**
     * 获取文件内容
     *
     * @param $file
     */
    protected function getCsvFileData($file)
    {
        $return = [];
        $file = __DIR__ . '/TempData/' . $file;
        $f = fopen($file, 'r');
        $header = fgets($f);
        $header = explode(',', str_replace('"', '', trim($header)));
        foreach ($header as $key => $value) {
            $header[$key] = trim($value);
        }
        while(!feof($f)){
            $value = fgets($f);
            if (!$value) {
                continue;
            }
            $value = explode(',', str_replace('"', '', trim($value)));
            foreach ($value as $k => $v) {
                $value[$k] = trim($v);
            }
            $return[] = array_combine($header, $value);
        }
        fclose($f);
        return $return;
    }
}