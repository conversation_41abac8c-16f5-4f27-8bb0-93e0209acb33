<?php


namespace App\Console\Commands;


use App\Http\Repository\DeptRepository;
use App\Models\Common\CommonEnumModel;
use App\Models\Customer;
use App\Models\CustomerMonthlyBalance;
use App\Models\SystemUser;
use App\Providers\Tool\SendMailService;
use App\Repositories\CustomerSalesmanHistoryRepository;
use Illuminate\Console\Command;

/**
 * 金融业务部门账期邮件提醒
 *
 * php artisan send_mail:period_reminder leader --sendto=fan.yang --cc=fan.yang
 * php artisan send_mail:period_reminder area_leader --sendto=fan.yang --cc=fan.yang
 * php artisan send_mail:period_reminder salesman --sendto=fan.yang --cc=fan.yang
 * php artisan send_mail:period_reminder finance --sendto=fan.yang --cc=fan.yang
 * php artisan send_mail:period_reminder yun_ying --sendto=fan.yang --cc=fan.yang
 */
class SendPeriodReminderMail extends Command
{

    protected $signature = 'send_mail:period_reminder
    {type : 任务类型 leader: 领导, area_leader: 区域总监, salesman: 商务, finance: 财务, yun_ying: 运营}
    {--sendto= : 收件人, 添加此选项将强制使收件人为sendto}
    {--cc= : 抄送人, 添加此选项将强制使抄送人为cc}';

    protected $description = '金融业务部门账期邮件提醒';

    /**
     * 账期统计开始月份
     * @var
     */
    protected $startMonth = 202301;

    /**
     * 账期统计基础数据
     * @var array
     */
    protected $periodBaseData = [
        'period_greater_twelve' => 0,
        'period_equal_twelve' => 0,
        'period_equal_eleven' => 0,
        'period_equal_ten' => 0,
        'period_equal_nine' => 0,
        'period_equal_eight' => 0,
        'period_equal_seven' => 0,
        'period_equal_six' => 0,
        'period_equal_five' => 0,
        'period_equal_four' => 0,
        'period_equal_three' => 0,
        'period_equal_two' => 0,
        'period_equal_one' => 0,
        'total' => 0,
    ];

    /**
     * 区域维度展示数据
     * @var string[]
     */
    protected $areaList = [
        '华东非银部' => '华东非银',
        '华北非银部' => '华北非银',
        '华南非银部' => '华南非银',
        '华东银行部' => '银行',
        '华北银行部' => '银行',
        '华南银行部' => '银行',
        '银行销售部' => '银行',
    ];

    /**
     * 领导邮件收件人
     * @var string[]
     */
    protected $leaderEmailToUserNames = ['yong.liu', 'mengsi.wang'];

    /**
     * 区域邮件收件人
     * @var string[]
     */
    protected $areaLeaderEmailToUserNames = [
        '华东非银' => 'binfang.ma',
        '华南非银' => 'chao.qin',
        '华北非银' => 'zhihui.zhao01',
        '银行' => 'v.pan'
    ];

    /**
     * 财务邮件收件人
     * @var string[]
     */
    protected $financeEmailToUserNames = ['zhihui.zhao', 'huifang.liu', 'kailu.sun'];

    /**
     * 运营邮件收件人
     * @var string[]
     */
    protected $yunYingEmailToUserNames = ['ren.zhang', 'lili.liu', 'xiaolan.xie', 'yanjun.shi'];

    /**
     * 商务邮件抄送
     * @var string[]
     */
    protected $salesmanEmailCC = [
        'weijian.li' => 'jingjing.huang'
    ];

    /**
     * 邮件标题
     * @var string
     */
    protected $mailTitle = '金融业务部未回款';

    /**
     * 邮件标题映射
     * @var string[]
     */
    protected $mailTitleMap = [
        'leader_weekly' => '领导周报',
        'area_leader_daily' => '总监日报',
        'salesman_daily' => '销售日报',
        'salesman_monthly' => '销售月报',
        'finance_monthly' => '财务月报',
        'yun_ying_daily' => '运营日报',
    ];

    /**
     * 内部测试客户 - 区域&销售 统计时忽略
     * @var string[]
     */
    protected $filterCustomer = ['C20180828LOCNMG', 'C20200622KF31GS'];

    /**
     * 忽略某些商务 - 区域&销售 统计时忽略
     * @var string[]
     */
    protected $filterSalesman = ['jing.zhou', 'haoran.xu', 'qiang.liu', 'xinxuan.wang'];

    /**
     * 忽略某些商务 - 领导&财务&运营 统计时忽略
     * @var string[]
     */
    protected $totalFilterSalesman = ['jing.zhou', 'haoran.xu', 'xinxuan.wang'];

    /**
     * 来源数据
     * @var array
     */
    protected $sourceData = [];

    /**
     * 客户数据
     * @var array
     */
    protected $customers = [];

    /**
     * 按月份的客户商务数据
     * @var array
     */
    protected $monthlyData = [];

    /**
     * 运营组邮箱
     * @var string
     */
    protected $yunYingMail = '<EMAIL>';

    /**
     * 表格标题
     * @var string[]
     */
    protected $tableTitle = [
        'source' => '未回款账期——按来源维度（万元）',
        'area' => '直连未回款账期——按区域维度（万元）',
        'salesman' => '直连未回款账期——按商务维度（万元）',
        'customer' => '未回款客户明细——按来源维度（直连≥3个月账期、征信≥5个月账期）',
        'customer_all' => '未回款客户明细——按来源维度（所有）',
    ];

    /**
     * 表格列名映射
     * @var string[]
     */
    protected $tableColNameMap = [
        'source' => '来源',
        'area' => '区域',
        'salesman' => '商务',
    ];

    public function handle()
    {
        // 设置一些初始数据
        $this->setParams();

        $type = $this->argument('type');
        $sendTo = $this->option('sendto');
        $cc = $this->option('cc');

        $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 30px 0;
        }

        th {
            border: 1px solid black;
        }

        .title {
            padding: 8px;
            text-align: center;
        }
        
        td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
        }

        .split-cell {
            position: relative;
            background: linear-gradient(to bottom left,
            transparent 49.5%,
            currentColor 49.5%,
            currentColor 50.5%,
            transparent 50.5%);
        }

        .col-text {
            position: absolute;
            bottom: 2px;
            left: 2px;
            margin: 0;
            padding: 0; 
            line-height: 1; 
            text-align: left;
        }

        .row-text {
            position: absolute;
            top: 2px; 
            right: 2px; 
            margin: 0;
            padding: 0; 
            line-height: 1; 
            text-align: right;
        }
    </style>
</head>
<body>
<p>
<b>说明：</b><br>
直连账期数据：指通过羽乐科技接口调用产生的账期数据<br>
征信账期数据：指通过征信、数交所接口调用产生的账期数据<br>
<b>提示：</b>请重点关注直连≥3个月账期数据 、征信≥5个月账期数据，并把预计回款时间及时回填到“金融后台系统——客户回款统计页面”<br>
<font color="red"><b>重点提示：</b>直连账期≥6个月&直连账期≥9个月的情况需每个周五发邮件给领导审批！</font><br>
</p>
HTML;

        // 未回款数据
        $total = true;
        if (in_array($type, ['area_leader', 'salesman'])) {
            $total = false;
        }
        $unRemitData = $this->getUnRemitData($total);

        switch ($type) {
            case 'leader':
                // 按照来源维度展示未回款账期
                $sourceData = $this->formatPeriodTableDataBySource($unRemitData);
                $html .= $this->getPeriodHtmlTable($this->tableTitle['source'], $this->tableColNameMap['source'], $sourceData);

                // 按照区域维度展示未回款账期
                $areaData = $this->formatPeriodTableDataByArea($unRemitData);
                $html .= $this->getPeriodHtmlTable($this->tableTitle['area'], $this->tableColNameMap['area'], $areaData);

                // 按照商务维度展示直连未回款账期
                $salesmanData = $this->formatPeriodTableDataBySalesman($unRemitData);
                $html .= $this->getPeriodHtmlTable($this->tableTitle['salesman'], $this->tableColNameMap['salesman'], $salesmanData);

                // 未回款客户明细（直连≥3个月账期、征信≥5个月账期）
                $formatUnRemitData = $this->formatCustomerDetailData($unRemitData);
                $html .= $this->getCustomerDetailHtmlTable($this->tableTitle['customer'], $formatUnRemitData);

                $html .= <<<HTML
</body>
</html>
HTML;

                $emailSendTo = $sendTo ?: $this->leaderEmailToUserNames;
                $title = $this->mailTitle . '-' . $this->mailTitleMap['leader_weekly'];
                $ccEmails = $cc ? [['email' => $cc . '@yulore.com']] : [['email' => $this->yunYingMail]];
                $this->sendMail($emailSendTo, $title, $html, $ccEmails);
                break;
            case 'area_leader':
                // 整理每个大区总监的数据
                $areaData = $this->formatUnRemitDataByAreaLeader($unRemitData);

                foreach ($areaData as $leader => $areaVal) {
                    $areaHtml = $html;
                    // 按照来源维度展示未回款账期
                    $sourceData = $this->formatPeriodTableDataBySource($areaVal);
                    $areaHtml .= $this->getPeriodHtmlTable($this->tableTitle['source'], $this->tableColNameMap['source'], $sourceData);

                    // 按照商务角度下展示未回款账期
                    $salesmanData = $this->formatPeriodTableDataBySalesman($areaVal);
                    $areaHtml .= $this->getPeriodHtmlTable($this->tableTitle['salesman'], $this->tableColNameMap['salesman'], $salesmanData);

                    // 未回款客户明细（直连≥3个月账期、征信≥5个月账期）
                    $formatUnRemitData = $this->formatCustomerDetailData($areaVal);
                    $areaHtml .= $this->getCustomerDetailHtmlTable($this->tableTitle['customer'], $formatUnRemitData);

                    $areaHtml .= <<<HTML
</body>
</html>
HTML;

                    $emailSendTo = $sendTo ?: $leader;
                    $title = $this->mailTitle . '-' . $this->mailTitleMap['area_leader_daily'];
                    $this->sendMail($emailSendTo, $title, $areaHtml);
                }
                break;
            case 'salesman':
                $currentDay = date('j');
                // 整理出每个销售的未回款数据
                $salesmanData = $this->formatUnRemitDataBySalesman($unRemitData);
                foreach ($salesmanData as $salesman => $salesmanVal) {
                    $salesmanHtml = $html;
                    // 按照来源维度展示未回款账期
                    $sourceData = $this->formatPeriodTableDataBySource($salesmanVal);
                    $salesmanHtml .= $this->getPeriodHtmlTable($this->tableTitle['source'], $this->tableColNameMap['source'], $sourceData);

                    if ($currentDay == 1) {
                        // 全部未回款客户明细
                        $formatUnRemitData = $this->formatCustomerDetailData($salesmanVal, true);
                        $salesmanHtml .= $this->getCustomerDetailHtmlTable($this->tableTitle['customer_all'], $formatUnRemitData);
                        $title = $this->mailTitle . '-' . $this->mailTitleMap['salesman_monthly'];
                    } else {
                        // 未回款客户明细（直连≥3个月账期、征信≥5个月账期）
                        $formatUnRemitData = $this->formatCustomerDetailData($salesmanVal);
                        $salesmanHtml .= $this->getCustomerDetailHtmlTable($this->tableTitle['customer'], $formatUnRemitData);
                        $title = $this->mailTitle . '-' . $this->mailTitleMap['salesman_daily'];
                    }

                    $salesmanHtml .= <<<HTML
</body>
</html>
HTML;

                    $emailSendTo = $sendTo ?: $salesman;
                    $ccEmail = $this->salesmanEmailCC[$salesman] ?? '';
                    $ccEmails = [];
                    if ($ccEmail) {
                        $name = $cc ?: $ccEmail;
                        $ccEmails[] = [
                            'email' => $name . '@yulore.com'
                        ];
                    }
                    $this->sendMail($emailSendTo, $title, $salesmanHtml, $ccEmails);
                }
                break;
            case 'finance':
                // 按照来源维度展示未回款账期
                $sourceData = $this->formatPeriodTableDataBySource($unRemitData);
                $html .= $this->getPeriodHtmlTable($this->tableTitle['source'], $this->tableColNameMap['source'], $sourceData);

                // 全部未回款客户明细
                $formatUnRemitData = $this->formatCustomerDetailData($unRemitData, true);
                $html .= $this->getCustomerDetailHtmlTable($this->tableTitle['customer_all'], $formatUnRemitData);

                $html .= <<<HTML
</body>
</html>
HTML;

                $emailSendTo = $sendTo ?: $this->financeEmailToUserNames;
                $title = $this->mailTitle . '-' . $this->mailTitleMap['finance_monthly'];
                $ccEmails = $cc ? [['email' => $cc . '@yulore.com']] : [['email' => $this->yunYingMail]];
                $this->sendMail($emailSendTo, $title, $html, $ccEmails);
                break;
            case 'yun_ying':
                // 按照来源维度展示未回款账期
                $sourceData = $this->formatPeriodTableDataBySource($unRemitData);
                $html .= $this->getPeriodHtmlTable($this->tableTitle['source'], $this->tableColNameMap['source'], $sourceData);

                // 按照区域维度展示未回款账期
                $areaData = $this->formatPeriodTableDataByArea($unRemitData);
                $html .= $this->getPeriodHtmlTable($this->tableTitle['area'], $this->tableColNameMap['area'], $areaData);

                // 按照商务维度展示直连未回款账期
                $salesmanData = $this->formatPeriodTableDataBySalesman($unRemitData);
                $html .= $this->getPeriodHtmlTable($this->tableTitle['salesman'], $this->tableColNameMap['salesman'], $salesmanData);

                // 未回款客户明细（直连≥3个月账期、征信≥5个月账期）
                $formatUnRemitData = $this->formatCustomerDetailData($unRemitData);
                $html .= $this->getCustomerDetailHtmlTable($this->tableTitle['customer'], $formatUnRemitData);

                $html .= <<<HTML
</body>
</html>
HTML;
                $emailSendTo = $sendTo ?: $this->yunYingEmailToUserNames;
                $title = $this->mailTitle . '-' . $this->mailTitleMap['yun_ying_daily'];
                $this->sendMail($emailSendTo, $title, $html);
                break;
            default:
                break;
        }
    }

    /**
     * 设置一些初始数据
     */
    private function setParams()
    {
        // 来源
        $this->sourceData = CommonEnumModel::getTypeMaps('1');

        // 客户
        $customers = Customer::getAllCustomer('*');
        $this->customers = $customers;
        $customerIds = array_column($customers, 'customer_id');

        // 按月份获取客户商务数据
        $cshRepository = new CustomerSalesmanHistoryRepository();
        $endMonth = date('Ym', strtotime('-1 month'));
        $this->monthlyData = $cshRepository->getListMonthly($customerIds, $this->startMonth, $endMonth, 'Ym');
    }

    /**
     * 获取未回款数据
     * @param bool $total
     * @return mixed
     */
    private function getUnRemitData($total = false)
    {
        $fields = ["customer_id", "month_date", "unremit", "source"];
        $where = [
            ["month_date", ">=", $this->startMonth],   // 202301之后
            ["unremit", ">", 0.3],   // 存在未回款金额 & 未回款值 > 0.3
            ["source", "!=", -1],   // 来源排除-1, -1表示全部
        ];

        $filterCustomer = $this->filterCustomer;
        $filterSalesman = $this->filterSalesman;
        if ($total) {
            $filterCustomer = null;
            $filterSalesman = $this->totalFilterSalesman;
        }

        $data = CustomerMonthlyBalance::getUnRemitData($where, $fields, $filterCustomer);

        $result = [];
        foreach ($data as $val) {
            $salesman = $this->monthlyData[$val['customer_id']][$val['month_date']];
            // 某些商务忽略掉不统计
            if (in_array($salesman, $filterSalesman)) {
                continue;
            }
            $result[] = $val;
        }

        return $result;
    }

    /**
     * 按照区域整理未回款数据
     * @param $data
     * @return array
     */
    private function formatUnRemitDataByAreaLeader($data)
    {
        $salesmanDeptMap = DeptRepository::getSalesmanDeptMap();

        $result = [];
        foreach ($data as $val) {
            $salesman = $this->monthlyData[$val['customer_id']][$val['month_date']];
            $area = $salesmanDeptMap[$salesman]['area'] ?? '';
            $areaName = $this->areaList[$area] ?? '';
            $leader = $this->areaLeaderEmailToUserNames[$areaName] ?? '';

            if (!$leader) {
                continue;
            }

            $result[$leader][] = $val;
        }

        return $result;
    }

    /**
     * 按照商务整理未回款数据
     * @param $data
     * @return array
     */
    private function formatUnRemitDataBySalesman($data)
    {
        $result = [];
        foreach ($data as $val) {
            $salesman = $this->monthlyData[$val['customer_id']][$val['month_date']];
            $result[$salesman][] = $val;
        }

        return $result;
    }

    /**
     * 按照来源维度展示未回款账期
     * @param $data
     * @return array
     * @throws \Exception
     */
    private function formatPeriodTableDataBySource($data)
    {
        $result = [
            'total' => $this->periodBaseData,
        ];
        foreach ($data as $val) {
            $source = $val['source'];
            $name = $this->sourceData[$source];

            if (!isset($result[$name])) {
                $result[$name] = $this->periodBaseData;
            }

            $result = $this->statPeriodUnRemitData($val['month_date'], $name, $val['unremit'], $result);
        }

        $statData = [];
        foreach ($result as $source => $val) {
            if ($source == 'total') {
                continue;
            }
            $val['name'] = $source;
            $statData[] = $val;
        }

        $totalStatData = $result['total'];
        $totalStatData['name'] = '总计';
        $statData[] = $totalStatData;

        return $statData;
    }

    /**
     * 按照区域维度展示直连未回款账期
     * @param $data
     * @return array
     * @throws \Exception
     */
    private function formatPeriodTableDataByArea($data)
    {
        $salesmanDeptMap = DeptRepository::getSalesmanDeptMap();

        $result = [
            'total' => $this->periodBaseData,
        ];
        foreach ($data as $val) {
            if ($val['source']) {
                continue;
            }

            $salesman = $this->monthlyData[$val['customer_id']][$val['month_date']];
            $area = $salesmanDeptMap[$salesman]['area'] ?? '';
            $areaName = $this->areaList[$area] ?? '';

            if (!$areaName) {
                continue;
            }

            if (!isset($result[$areaName])) {
                $result[$areaName] = $this->periodBaseData;
            }

            $result = $this->statPeriodUnRemitData($val['month_date'], $areaName, $val['unremit'], $result);
        }

        $statData = [];
        foreach ($result as $area => $val) {
            if ($area == 'total') {
                continue;
            }
            $val['name'] = $area;
            $statData[] = $val;
        }

        $totalStatData = $result['total'];
        $totalStatData['name'] = '总计';
        $statData[] = $totalStatData;

        return $statData;
    }

    /**
     * 按照商务维度展示直连未回款账期
     * @param $data
     * @return array
     * @throws \Exception
     */
    private function formatPeriodTableDataBySalesman($data)
    {
        $result = [
            'total' => $this->periodBaseData,
        ];
        foreach ($data as $val) {
            if ($val['source']) {
                continue;
            }
            $salesman = $this->monthlyData[$val['customer_id']][$val['month_date']];
            if (!isset($result[$salesman])) {
                $result[$salesman] = $this->periodBaseData;
            }

            $result = $this->statPeriodUnRemitData($val['month_date'], $salesman, $val['unremit'], $result);
        }

        $statData = [];
        $allUserList = SystemUser::getAllUsers();
        $userData = array_column($allUserList, 'realname', 'username');

        foreach ($result as $salesman => $val) {
            if ($salesman == 'total') {
                continue;
            }
            $val['name'] = $userData[$salesman];
            $statData[] = $val;
        }

        $totalStatData = $result['total'];
        $totalStatData['name'] = '总计';
        $statData[] = $totalStatData;

        return $statData;
    }

    /**
     * 统计账期内未回款
     * @param $monthDate
     * @param $statKey
     * @param $unRemit
     * @param $result
     * @return array
     * @throws \Exception
     */
    private function statPeriodUnRemitData($monthDate, $statKey, $unRemit, $result)
    {
        // 总计
        $result[$statKey]['total'] += $unRemit;
        $result['total']['total'] += $unRemit;

        $periodNum = $this->getPeriodNum($monthDate);
        switch (true) {
            case $periodNum == 1:
                $result[$statKey]['period_equal_one'] += $unRemit;
                $result['total']['period_equal_one'] += $unRemit;
                break;
            case $periodNum == 2:
                $result[$statKey]['period_equal_two'] += $unRemit;
                $result['total']['period_equal_two'] += $unRemit;
                break;
            case $periodNum == 3:
                $result[$statKey]['period_equal_three'] += $unRemit;
                $result['total']['period_equal_three'] += $unRemit;
                break;
            case $periodNum == 4:
                $result[$statKey]['period_equal_four'] += $unRemit;
                $result['total']['period_equal_four'] += $unRemit;
                break;
            case $periodNum == 5:
                $result[$statKey]['period_equal_five'] += $unRemit;
                $result['total']['period_equal_five'] += $unRemit;
                break;
            case $periodNum == 6:
                $result[$statKey]['period_equal_six'] += $unRemit;
                $result['total']['period_equal_six'] += $unRemit;
                break;
            case $periodNum == 7:
                $result[$statKey]['period_equal_seven'] += $unRemit;
                $result['total']['period_equal_seven'] += $unRemit;
                break;
            case $periodNum == 8:
                $result[$statKey]['period_equal_eight'] += $unRemit;
                $result['total']['period_equal_eight'] += $unRemit;
                break;
            case $periodNum == 9:
                $result[$statKey]['period_equal_nine'] += $unRemit;
                $result['total']['period_equal_nine'] += $unRemit;
                break;
            case $periodNum == 10:
                $result[$statKey]['period_equal_ten'] += $unRemit;
                $result['total']['period_equal_ten'] += $unRemit;
                break;
            case $periodNum == 11:
                $result[$statKey]['period_equal_eleven'] += $unRemit;
                $result['total']['period_equal_eleven'] += $unRemit;
                break;
            case $periodNum == 12:
                $result[$statKey]['period_equal_twelve'] += $unRemit;
                $result['total']['period_equal_twelve'] += $unRemit;
                break;
            case $periodNum > 12:
                $result[$statKey]['period_greater_twelve'] += $unRemit;
                $result['total']['period_greater_twelve'] += $unRemit;
                break;
            default:
                break;
        }

        return $result;
    }

    /**
     * 未回款客户明细
     * @param $data
     * @param bool $all false：直连≥3个月账期、征信≥5个月账期 true：全部
     * @return array
     * @throws \Exception
     */
    private function formatCustomerDetailData($data, $all = false)
    {
        $ciSource = []; // 征信来源
        foreach ($this->sourceData as $name => $value) {
            if (!$name) {
                continue;
            }
            $ciSource[] = $name;
        }

        // 客户
        $customerData = [];
        foreach ($this->customers as $customer) {
            $customerData[$customer["customer_id"]] = $customer["name"];
        }

        $result = [];
        foreach ($data as $val) {
            $periodNum = $this->getPeriodNum($val['month_date']);

            if (!$all && (($periodNum < 3 && $val['source'] == 0) || ($periodNum < 5 && in_array($val['source'], $ciSource)))) {
                continue;
            }

            $val['source_name'] = $this->sourceData[$val['source']];
            $val['customer_name'] = $customerData[$val['customer_id']];
            $val['period_num'] = $periodNum;

            $result[] = $val;
        }
        return $result;
    }

    /**
     * 获取账期
     * @param $monthDate
     * @return string
     * @throws \Exception
     */
    private function getPeriodNum($monthDate)
    {
        $startDate = new \DateTime($monthDate . '01');
        $endDate = new \DateTime();
        $interval = $startDate->diff($endDate);
        $diffYear = $interval->y;
        $diffMonth = $interval->m;
        return ($diffYear * 12) + $diffMonth;
    }

    /**
     * 未回款账期表格
     * @param $title
     * @param $type
     * @param $data
     * @return string
     */
    private function getPeriodHtmlTable($title, $type, $data)
    {
        $html = <<<HTML
<table>
    <thead>
        <tr style="background-color: #4874cb; color: white;">
            <th colspan="15" class="title">$title</th>
        </tr>
        <tr style="background-color: #b6c6ea; color: white;">
            <th class="split-cell" style="width: 10%">
                <div class="row-text">账期</div>
                <div class="col-text">$type</div>
            </th>
            <td style="width: 6%">12+</td>
            <td style="width: 6%">12</td>
            <td style="width: 6%">11</td>
            <td style="width: 6%">10</td>
            <td style="width: 6%">9</td>
            <td style="width: 6%">8</td>
            <td style="width: 6%">7</td>
            <td style="width: 6%">6</td>
            <td style="width: 6%">5</td>
            <td style="width: 6%">4</td>
            <td style="width: 6%">3</td>
            <td style="width: 6%">2</td>
            <td style="width: 6%">1</td>
            <td style="width: 10%">总计</td>
        </tr>
    </thead>
    <tbody>
HTML;
        $periodKeys = array_keys($this->periodBaseData);
        $lastPeriodKey = end($periodKeys); // 获取最后一个周期key
        $lastRow = end($data); // 获取最后一行数据

        foreach ($data as $val) {
            $isLastRow = ($val === $lastRow); // 判断是否是最后一行
            $nameStyle = $isLastRow ? 'style="background-color: #f0f0f0; font-weight: bold;"' : '';
            $html .= <<<HTML
        <tr>
            <td {$nameStyle}>{$val["name"]}</td>
HTML;
            foreach ($periodKeys as $periodKey) {
                $roundValue = round($val[$periodKey] / 10000, 2);
                $value = !$roundValue ? 0 : $roundValue;
                $isLastCol = ($periodKey === $lastPeriodKey); // 判断是否是最后一列
                // 如果是最后一行或最后一列，添加特殊样式
                $style = '';
                if ($isLastRow || $isLastCol) {
                    $style = 'style="background-color: #f0f0f0; font-weight: bold;"';
                }
                $html .= <<<HTML
<td {$style}>{$value}</td>
HTML;
            }

            $html .= <<<HTML
        </tr>
HTML;
        }

        $html .= <<<HTML
    </tbody>
</table>
HTML;

        return $html;
    }

    /**
     * 未回款客户明细表格
     * @param $title
     * @param $data
     * @return string
     */
    private function getCustomerDetailHtmlTable($title, $data)
    {
        $total = 0;
        $html = <<<HTML
<table>
    <thead>
        <tr style="background-color: #4874cb; color: white;">
            <th colspan="5" class="title">$title</th>
        </tr>
        <tr style="background-color: #b6c6ea; color: white;">
            <td>客户名称</td>
            <td>账单月份</td>
            <td>账期数</td>
            <td>未回款金额（元）</td>
            <td>来源</td>
        </tr>
    </thead>
    <tbody>
HTML;
        foreach ($data as $val) {
            $unRemit = round($val["unremit"], 2);
            $unRemit = !$unRemit ? 0 : $unRemit;
            $html .= <<<HTML
        <tr>
            <td>{$val["customer_name"]}</td>
            <td>{$val["month_date"]}</td>
            <td>{$val["period_num"]}</td>
            <td>{$unRemit}</td>
            <td>{$val["source_name"]}</td>
        </tr>
HTML;
            $total += $val["unremit"];
        }

        $total = round($total, 2);
        $total = !$total ? 0 : $total;

        $html .= <<<HTML
        <tr style="background-color: #f0f0f0; font-weight: bold;">
            <td colspan="3">总计</td>
            <td colspan="2">{$total}</td>
        </tr>
    </tbody>
</table>
HTML;

        return $html;
    }

    /**
     * 发邮件
     * @param $sendTo
     * @param $title
     * @param $html
     * @param array $cc
     * @throws \Exception
     */
    private function sendMail($sendTo, $title, $html, $cc = [])
    {
        if (!is_array($sendTo)) {
            $sendTo = [$sendTo];
        }

        $sendAddress = [];
        foreach ($sendTo as $value) {
            $sendAddress[] = [
                'email' => $value . '@yulore.com'
            ];
        }

        $mail = new SendMailService();
        $mail->setFromName('金融后台项目组')
            ->setAddressee($sendAddress)
            ->setCC($cc)
            ->setSubject($title)
            ->setContent($html)
            ->sendByAsync();
    }
}