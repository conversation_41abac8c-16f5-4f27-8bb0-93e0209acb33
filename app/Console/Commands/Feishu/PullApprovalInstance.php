<?php

namespace App\Console\Commands\Feishu;

use App\Define\FeishuApprovalDefine;
use App\Http\Repository\ContractRepositorie;
use App\Models\Contract;
// use App\Models\PreTestManage\Approval\ApprovalTransfer;
use App\Models\feishu\Approval;
use App\Models\feishu\ApprovalInstance\ContractSealInstance;
use App\Models\feishu\ApprovalTransfer;
use App\Models\Product;
use App\Models\SystemUser;
use App\Models\feishu\FeishuApprovalInstance;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use SebastianBergmann\CodeCoverage\Report\PHP;

/**
 * 拉取飞书售前测试申请
 * php artisan fieshu_appoval:pull_approval --start=20250610 --end=20250626 --type=add
 * php artisan fieshu_appoval:pull_approval --type=update
 *
 * @uses PullApplyList
 */
class PullApprovalInstance extends Command
{
    protected $signature = 'fieshu_appoval:pull_approval
    {--approval_code= : approval_code}
    {--type= : add 添加实例, update 更新实例}
    {--start=    : 开始时间 Ymd}
    {--end=      : 结束时间 Ymd}';

    protected $description = '拉取飞书审批实例';

    private $start = 0;
    private $end   = 0;
    private $type   = 'add';
    private $approval_code = [
        FeishuApprovalDefine::APPROVAL_CODE_CONTRACT_SEAL,
    ];

    /**
     * @throws Exception
     */
    public function handle(){
        try {
            $now = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->set_param();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf($this->description . ' 执行开始时间:%s 耗时:%s', $now, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $err_msg = $exception->getFile() . ':' . $exception->getLine() . ' ' . $exception->getMessage();
            $msg = $this->description . ' 执行失败! ' . $err_msg;
            Log::error($msg);
            $this->output->warning($msg);
            $this->send_feishu_msg($msg);
        }
    }

    /**
     * @throws Exception
     */
    private function set_param() {

        $this->type = $this->input->getOption('type');
        if(!$this->type || !in_array($this->type, ['add', 'update'])) {
            $this->type = 'add';
        }

        if($this->type == 'add') {
            $this->start = $this->input->getOption('start');
            $this->end   = $this->input->getOption('end');

            if (!$this->input->getOption('start')) {
                $this->start = date('Ymd',strtotime('-1 day'));
            }
            if (!$this->input->getOption('end')) {
                $this->end = date('Ymd',strtotime('-1 day'));
            }
            if ($this->start > $this->end) {
                throw new Exception('--start 参数错误');
            }

            $this->start = strtotime($this->start . '00:00:00');
            $this->end   = strtotime($this->end . '23:59:59');
        }

        $approval_code = $this->input->getOption('approval_code');
        if($approval_code) {
            $this->approval_code = [$approval_code];
        }
    }

    /**
     * @throws Exception
     */
    private function main() {
        if($this->type == 'add'){
            // 拉取列表
            foreach ($this->approval_code as $approval_code) {
                $this->pull_approval_instance($approval_code);
            }
        }

        if($this->type == 'update'){
            $this->update_approval_instance();
        }
    }

    /**
     * @return void
     * @throws Exception
     */
    private function pull_approval_instance($approval_code) {
        $feishu_obj = new FeishuRepository('feishu_pre_test');
        $page_token = '';
        $page_size  = 50;
        do {
            $data = [
                'approval_code'   => $approval_code,
                'instance_status' => FeishuApprovalDefine::INSTANCE_STATUS_ALL,
                'start_time'      => $this->start,
                'end_time'        => $this->end,
                'page_size'       => $page_size,
                'page_token'      => $page_token,
            ];
            $res = $feishu_obj->get_approval_list($data);

            $count         = $res['count']         ?? 0;
            $has_more      = $res['has_more']      ?? false;
            $page_token    = $res['page_token']    ?? '';
            $instance_list = $res['instance_list'] ?? [];

            if ($count == 0 || empty($instance_list)) {
                break;
            }

            $instance_code_list = [];
            foreach($instance_list as $instance_info) {
                $instance_code_list[] = $instance_info['instance']['code'];
            }

            $exi_instance_code_list = FeishuApprovalInstance::getExistInstanceCodes($instance_code_list);

            // 逐个获取详情
            $init_data = [];
            foreach($instance_list as $instance_info) {
                $instance_code = $instance_info['instance']['code'];
                // 已存在
                if(in_array($instance_code,$exi_instance_code_list)) {
                    continue;
                }

                $init_data[] = [
                    'approval_code' => $approval_code,
                    'instance_code' => $instance_code,
                    'approval_name' => $instance_info['approval']['name'],
                    'start_time'    => date('Y-m-d H:i:s',intval($instance_info['instance']['start_time']/1000)),
                    'serial_number' => $instance_info['instance']['serial_id'],
                    'user_id'       => $instance_info['instance']['user_id'],
                    'status'        => FeishuApprovalDefine::INSTANCE_STATUS_INIT,
                    'created_at'    => date('Y-m-d H:i:s'),
                    'updated_at'    => date('Y-m-d H:i:s'),
                ];
            }
            FeishuApprovalInstance::insert($init_data);

        } while ($has_more);

    }

    /**
     *
     *
     * @return void
     * <AUTHOR> 2025-06-10 11:51:55
     *
     */
    private function update_approval_instance() {
        $need_update_status = [FeishuApprovalDefine::INSTANCE_STATUS_INIT,FeishuApprovalDefine::INSTANCE_STATUS_PENDING];
        $need_update_instance_list = FeishuApprovalInstance::getListByStatus($need_update_status);

        $count = 1;
        // 逐个获取详情
        foreach($need_update_instance_list as $instance_info) {
            $contract_seal_instance = $this->get_instance_info($instance_info->instance_code);

            if(is_null($contract_seal_instance->getStatus())){
                continue;
            }

            $instance_info->department_id = $contract_seal_instance->getDepartmentId();
            $instance_info->end_time      = $contract_seal_instance->getEndTime();
            $instance_info->form          = $contract_seal_instance->getOriginForm();
            $instance_info->open_id       = $contract_seal_instance->getOpenId();
            $instance_info->status        = $contract_seal_instance->getStatus();
            $instance_info->uuid          = $contract_seal_instance->getUuid();

            $instance_info->updated_at = date('Y-m-d H:i:s');

            $user_name = $this->get_user_name($instance_info->user_id);
            $instance_info->username = $user_name;
            $contract_seal_instance->setUsername($user_name);

            $is_in_contract = Contract::isExistsByInstanceCode($instance_info->instance_code);

            if(!$is_in_contract){
                $contract_seal_instance->transForm()->saveData();
            }

            $instance_info->save();

            $count ++;

            //避免频率限制 1000 次/分钟、50 次/秒
            if($count % 10 == 0) {
                sleep(1);
            }
        }
    }

    /**
     * @param string $instance_code
     *
     * @return ContractSealInstance
     */
    private function get_instance_info(string $instance_code) {
        $feishu_obj = new FeishuRepository('feishu_pre_test');
        $res = [];
        try {
            $res = $feishu_obj->get_approval_info($instance_code);
            if (empty($res)) {
                Log::warning($this->description . ' 审批信息为空', ['instance_code' => $instance_code]);
            }
        } catch (Exception $e) {
            Log::error($this->description . '获取审批信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $instance_code]);
        }

        return ApprovalTransfer::makeApproval($res);
    }

    /**
     *
     *
     * @param string $user_id
     *
     * @return string
     * <AUTHOR> 2025-06-10 15:43:03
     */
    private function get_user_name(string $user_id) {
        $feishu_obj = new FeishuRepository('feishu_pre_test');
        $user_name = '';
        try {
            $res = $feishu_obj->get_user_info($user_id);
            if (empty($res)) {
                Log::warning($this->description . ' 获取用户信息为空', ['user_id' => $user_id]);
            }
            $email = $res['user']['email'] ?? '';
            if($email) {
                $user_name = explode('@',$email)[0];
            }
        } catch (Exception $e) {
            Log::error($this->description . '获取用户信息失败 , msg: ' . $e->getMessage(), ['user_id' => $user_id]);
        }

        return $user_name;
    }


    /**
     * @param string $msg
     *
     * @return void
     * @throws Exception
     */
    private function send_feishu_msg(string $msg = '') {
        $feishu = new FeishuRepository();
        $mess = [
            'msg' => $msg
        ];
        $feishu->send_card_message_to_chat_group($msg,$mess);
    }
}