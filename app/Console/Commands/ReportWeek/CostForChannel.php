<?php
/**
 * Created by PhpStorm.
 * User: shuangxian.zhang
 * Date: 2021/4/25
 * Time: 11:34
 */

namespace App\Console\Commands\ReportWeek;


use App\Models\BillCost;
use App\Models\EmailConfig;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;

//渠道成本账单周报
class CostForChannel extends Command
{
    protected $signature   = 'report_week:cost_for_channel
    {--sunday= : 周日的日期, 格式Ymd，默认为上周周日的日期，必须是一个周日的日期，所发送的数据为这个周日所在周的报表统计}
    {--addressee= : 发送收件人，设置此项后将不使用邮件配置中的邮箱}';
    protected $description = '成本渠道周报表';

    protected $addressee = [];
    protected $sunday;
    protected $monday;
    protected $scene     = 'week_report_upsteam_channel_bill';

    public function handle()
    {
        $res = $this->setOptions();
        if ($res) {
            //获取当天的数据
            $data = $this->getCostInfo();

            //生成html
            $html = $this->createHtml($data);

            //获取收件人、抄送人
            $addressee = $this->getEmailConfig();

            //发送邮件
            $mail = new SendMailService();
            $mail->setFromName('金融后台项目组')
                ->setAddressee($addressee['recipients'])
                ->setCC($addressee['cc'])
                ->setSubject('成本账单渠道周报表')
                ->setContent($html)
                ->sendByAsync();
            $this->output->success("成本账单渠道周报表发送成功");
        }
    }

    /**
     * 获取邮件收件人、抄送人
     *
     * @access protected
     *
     * @return array
     **/
    protected function getEmailConfig()
    {
        if (empty($this->addressee)) {
            $emailConfig = new EmailConfig();
            $mail_arr = $emailConfig->getRecipientsAndCCByScene($this->scene);
            foreach ($mail_arr['recipients'] as $k => $v) {
                $mail_arr['recipients'][$k]['email'] = $v['address'];
                unset($mail_arr['recipients'][$k]['address']);
            }
            foreach ($mail_arr['cc'] as $k => $v) {
                $mail_arr['cc'][$k]['email'] = $v['address'];
                unset($mail_arr['cc'][$k]['address']);
            }
            return $mail_arr;
        }
        $recipients = array_map(function ($email) {
            $address_arr = explode('@', $email);
            $name = $address_arr[0];
            if (count($address_arr) == 1) {
                $email = $address_arr[0] . '@yulore.com';
            }
            return compact('name', 'email');
        }, $this->addressee);
        $cc         = [];

        return compact('cc', 'recipients');
    }

    /**
     * 创建HTML
     *
     * @access protected
     *
     * @param $data array HTML中展示的数据
     *
     * @return string
     **/
    protected function createHtml($data)
    {
        $date  = date('m月d日', strtotime($this->monday)) . ' -- ' . date('m月d日', strtotime($this->sunday));
        $title = "{$date}渠道成本周报表，请查阅";
        $table = $this->setHtmlBody($data);
        $html  = <<<HTML
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>{$title}</title>
        <style>
            #tableArea { width:100%; height:auto; font-family: Arial, Helvetica, sans-serif; font-size: 14px; }
            .title { width:100%; height:26px; line-height:26px; color:#333; font-size:16px; font-weight:bold; text-align: center; }
            .table { width:98%; height:auto; overflow-x:auto; margin:20px auto; }
            .table table { height:auto; color:#333; border-top:1px solid #ccc; border-left:1px solid #ccc; }
            .table table tr:hover{ background:#eeeeee; }
            .table table th,.table table td { border-bottom: 1px solid #ccc; border-right: 1px solid #ccc; text-align:center; padding:4px 10px; box-sizing:border-box; }
            .table table th { font-weight:bold; min-width:80px !important; }
            .td_red{color:red;}
            .td_green{color:green;}
            h2 {
				width        : 98%;
				height       : 44px;
				line-height  : 44px;
				font-size    : 20px;
				font-weight  : bold;
				text-align   : center;
				margin       : 20px auto 2px;
				background   : rgba(229, 82, 45, 1);
				color        : #FFFFFF;
			}
        </style>
    </head>
    <body>
        <div id="tableArea">
            <div class="title">{$title}</div>
            {$table}
        </div>
    </body>
</html>
HTML;

        return $html;
    }

    /**
     * 构造HTML的TABLE内容
     *
     * @access protected
     *
     * @param $data array HTML中展示的数据
     *
     * @return string
     **/
    protected function setHtmlBody($data)
    {
        $html = '';
        //邦秒验-渠道维度
        $html .= '<h2>邦秒验-渠道维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><th>渠道名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
        $html .= "<tr><th align='center'>合计</th><td align='center'>".number_format($data['channel_200_total']['number'])."</td><td align='center'>".number_format($data['channel_200_total']['money'])."</td></tr>";
        foreach ($data['channel_200'] as $item) {
            $html .= "<tr><td align='center'>{$item['channel_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td></tr>";
        }
        $html .= "</table></div>";

        //邦信分-渠道维度
        $html .= '<h2>邦信分-渠道维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><th>渠道名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
        $html .= "<tr><th align='center'>合计</th><td align='center'>".number_format($data['channel_210_1000_total']['number'])."</td><td align='center'>".number_format($data['channel_210_1000_total']['money'])."</td></tr>";
        foreach ($data['channel_210_1000'] as $item) {
            $html .= "<tr><td align='center'>{$item['channel_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td></tr>";
        }
        $html .= "</table></div>";

        //渠道维度
        $html .= '<h2>渠道维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><th>渠道名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
        $html .= "<tr><th align='center'>合计</th><td align='center'>".number_format($data['total']['number'])."</td><td align='center'>".number_format($data['total']['money'])."</td></tr>";
        foreach ($data['channel'] as $item) {
            $html .= "<tr><td align='center'>{$item['channel_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td></tr>";
        }
        $html .= "</table></div>";

        //产品维度
        $html .= '<h2>产品维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><th>产品名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
        $html .= "<tr><th align='center'>合计</th><td align='center'>".number_format($data['total']['number'])."</td><td align='center'>".number_format($data['total']['money'])."</td></tr>";
        foreach ($data['product'] as $item) {
            $html .= "<tr><td align='center'>{$item['product_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td></tr>";
        }
        $html .= "</table></div>";
        //产品渠道维度
        $html .= '<h2>渠道产品维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><th>渠道名称</th><th>产品名称</th><th>计费用量</th><th>权责成本（元）</th></tr>";
        $html .= "<tr><th align='center' colspan='2'>合计</th><td align='center'>".number_format($data['total']['number'])."</td><td align='center'>".number_format($data['total']['money'])."</td></tr>";
        foreach ($data['channel_product'] as $item) {
            foreach ($item as $item2) {
                $html .= "<tr><td align='center'>{$item2['channel_name']}</td><td align='center'>{$item2['product_name']}</td><td>".number_format($item2['number'])."</td><td>".number_format($item2['money'])."</td></tr>";
            }
        }
        $html .= "</table></div>";

        return $html;

    }

    /**
     * 获取本月渠道成本数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getCostInfo()
    {
        $cost = BillCost::getCostChannel($this->monday, $this->sunday);

        $data = [
            'channel' => [],
            'product' => [],
            'channel_product' => [],
            'total' => ['number'=>0, 'money'=>0],
            'channel_200' => [],
            'channel_200_total' => ['number'=>0, 'money'=>0],
            'channel_210_1000' => [],
            'channel_210_1000_total' => ['number'=>0, 'money'=>0]
        ];
        foreach ($cost as $k => $v) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($v['product_id']);
            //邦秒验-渠道维度
            if ($father_id == 200) {
                if (isset($data['channel_200'][$v['channel_id']])) {
                    $data['channel_200'][$v['channel_id']]['number'] += $v['number'];
                    $data['channel_200'][$v['channel_id']]['money'] += $v['money'];
                } else {
                    $data['channel_200'][$v['channel_id']]['number'] = $v['number'];
                    $data['channel_200'][$v['channel_id']]['money'] = $v['money'];
                    $data['channel_200'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_200_total']['money'] += $v['money'];
                $data['channel_200_total']['number'] += $v['number'];
            }
            //邦信分-渠道维度
            if (($father_id == 210) || ($father_id == 1000)) {
                if (isset($data['channel_210_1000'][$v['channel_id']])) {
                    $data['channel_210_1000'][$v['channel_id']]['number'] += $v['number'];
                    $data['channel_210_1000'][$v['channel_id']]['money'] += $v['money'];
                } else {
                    $data['channel_210_1000'][$v['channel_id']]['number'] = $v['number'];
                    $data['channel_210_1000'][$v['channel_id']]['money'] = $v['money'];
                    $data['channel_210_1000'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_210_1000_total']['money'] += $v['money'];
                $data['channel_210_1000_total']['number'] += $v['number'];
            }
            //渠道维度
            if (isset($data['channel'][$v['channel_id']])) {
                $data['channel'][$v['channel_id']]['number'] += $v['number'];
                $data['channel'][$v['channel_id']]['money'] += $v['money'];
            } else {
                $data['channel'][$v['channel_id']]['number'] = $v['number'];
                $data['channel'][$v['channel_id']]['money'] = $v['money'];
                $data['channel'][$v['channel_id']]['channel_name'] = $v['label'];
            }

            //产品维度
            if (isset($data['product'][$v['product_id']])) {
                $data['product'][$v['product_id']]['number'] += $v['number'];
                $data['product'][$v['product_id']]['money'] += $v['money'];
            } else {
                $data['product'][$v['product_id']]['number'] = $v['number'];
                $data['product'][$v['product_id']]['money'] = $v['money'];
                $data['product'][$v['product_id']]['product_name'] = $v['product_name'];
            }

            //渠道产品维度
            if (isset($data['channel_product'][$v['channel_id']][$v['product_id']])) {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['number'] += $v['number'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['money'] += $v['money'];
            } else {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['number'] = $v['number'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['money'] = $v['money'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['channel_name'] = $v['label'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['product_name'] = $v['product_name'];
            }

            $data['total']['money'] += $v['money'];
            $data['total']['number'] += $v['number'];
        }
        //各维度排序
        array_multisort(array_column($data['channel_200'], 'money'), SORT_DESC, $data['channel_200']);
        array_multisort(array_column($data['channel_210_1000'], 'money'), SORT_DESC, $data['channel_210_1000']);
        array_multisort(array_column($data['channel'], 'money'), SORT_DESC, $data['channel']);
        array_multisort(array_column($data['product'], 'money'), SORT_DESC, $data['product']);
        $sort_arr = [];
        foreach ($data['channel_product'] as $channel_id => $product) {
            foreach ($product as $product_id => $item) {
                if (isset($sort_arr[$channel_id])) {
                    $sort_arr[$channel_id] += $item['money'];
                } else {
                    $sort_arr[$channel_id] = $item['money'];
                }
            }
            array_multisort(array_column($data['channel_product'][$channel_id], 'money'), SORT_DESC, $data['channel_product'][$channel_id]);
        }
        arsort($sort_arr);
        uksort($data['channel_product'], function ($i, $j) use ($sort_arr){
            return $sort_arr[$i] < $sort_arr[$j];
        });

        return $data;
    }

    /**
     * 设置参数
     *
     * @access protected
     *
     * @return boolean
     **/
    protected function setOptions()
    {
        //设置所需要的日期
        $this->sunday = empty($this->option('sunday')) ? date('Ymd', strtotime('sunday -1 week')) : $this->option('sunday');
        if (!preg_match('/^\d{8}$/', $this->sunday)) {
            throw new \Exception('日期格式不正确');
        }
        if (date('w', strtotime($this->sunday)) != 0) {
            throw new \Exception('您所设置的日期不是一个周日的日期');
        }
        if ($this->sunday >= date('Ymd')) {
            throw new \Exception('您所设置的日期大于当前时间，不可生成');
        }
        $this->monday = date('Ymd', strtotime('monday -1 week', strtotime($this->sunday)));

        $addressee = $this->input->getOption('addressee');
        if (!empty($addressee)) {
            $this->addressee = explode(',', $addressee);
        }

        return true;
    }

}