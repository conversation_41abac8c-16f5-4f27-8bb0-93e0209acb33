<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\AdjustCostStatistics;
use App\Models\MongoUpstream;
use App\Models\MongoUpstreamBill;
use App\Models\UpstreamBillAdjust;
use App\Models\UpstreamChannelPrice;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

//生成上游数据源的账单（日）

/**
 *  1.获取参数，参数包含以下几种
 *      + 账单日           bill_date      默认昨日
 *      + 批量生成天数      days           默认1
 *      + 产品             product_id     默认all,代表所有
 *      + 渠道标记         channel        默认all，代表所有
 *
 **/
class GenUpstreamBill extends Command
{
	use WechatExceptionTrait;
	public $signature = 'upstream:gen-bill
    {--bill_date= : 账单日，默认昨日日期，格式：Ymd}
    {--days=1 : 批量生成账单天数，默认1}
    {--product_id=all : 产品ID，多个产品ID请使用,进行分割} 
    {--channel=all : 渠道标记，多个渠道标记请使用,进行分割}';
	
	/** @var string 命令提示 */
	public $description = '生成上游数据源的账单';
	
	protected $bill_date;
	protected $days           = 1;
	protected $product_id     = 'all';
	protected $channel        = 'all';
	protected $upstream_field = ['all', 'succ', 'failed', 'yd', 'lt', 'dx'];     //聚合字段
	
	public function __construct()
	{
		parent::__construct();
		$this->bill_date = date('Ymd', strtotime('-1 days'));
	}
	
	public function handle()
	{
		//校验参数
		$this->verifyOption();
		
		//遍历生成每一天的账单
		$billStartTime = strtotime($this->bill_date);       //需要生成账单中的第一天
		$billEndTime   = strtotime('+ ' . $this->days . 'days', $billStartTime);        //需要生成账单中的最后一天
		$todayTime     = strtotime('midnight');             //今天的时间戳
		for ($billTime = $billStartTime; $billTime < $billEndTime; $billTime += 86400) {
			//不允许生成今天之后的日账单
			if ($billTime > $todayTime) {
				break;
			}
			
			//生成日账单
			$this->genUpstreamBill($billTime);
			
		}
	}
	
	/**
	 * 校验参数
	 *
	 * @access protected
	 *
	 * @return void
	 **/
	protected function verifyOption()
	{
		$bill_date = $this->option('bill_date');
		if (!empty($bill_date) && is_numeric($bill_date) && $bill_date >= 20200101) {
			$this->bill_date = $bill_date;
		}
		
		$days = $this->option('days');
		if (!empty($days) && is_numeric($days) && $days > 0) {
			$this->days = $days;
		}
		
		$product_id = $this->option('product_id');
		if (!empty($product_id) && $product_id != 'all') {
			$this->product_id = array_map(function ($product_id) {
				return intval($product_id);
			}, explode(',', $product_id));
		}
		
		$channel = $this->option('channel');
		if (!empty($channel) && $channel != 'all') {
			$this->channel = explode(',', $channel);
		}
	}
	
	/**
	 * 生成日账单
	 *
	 * @access protected
	 *
	 * @param $time integer 日期（时间戳）
	 *
	 * @return void
	 **/
	protected function genUpstreamBill($time)
	{
		/**
		 * 1.清理当日的账单内容
		 * 2.拉取计费配置
		 * 3.计算账单并保存在数据库中
		 **/
		//清理当日的账单内容
		MongoUpstreamBill::where(function ($query) {
			if ($this->product_id != 'all' && is_array($this->product_id)) {
				$query->whereIn('product_id', $this->product_id);
			}
			if ($this->channel != 'all' && is_array($this->channel)) {
				$query->whereIn('channel', $this->channel);
			}
		})
						 ->where('date', intval(date('Ymd', $time)))
						 ->delete();
		
		//已经计算过的渠道-产品
		$calculatedChannelProductId = [];
		
		//拉取计费配置
		$bill_date = intval(date('Ymd', $time));
		UpstreamChannelPrice::where('start_date', '<=', date('Y-m-d', $time))
							->where('delete_time', 0)
							->where(function ($query) {
								if ($this->product_id != 'all' && is_array($this->product_id)) {
									$query->whereIn('product_id', $this->product_id);
								}
								if ($this->channel != 'all' && is_array($this->channel)) {
									$query->whereIn('channel', $this->channel);
								}
							})
							->orderBy('start_date', 'desc')
							->orderBy('create_time', 'desc')
							->get()
							->map(function ($item) use ($bill_date, &$calculatedChannelProductId) {
								if ($item->is_billing == 0) {
									return;
								}
								$uniqueKey = $item->channel . '_' . $item->product_id;
								if (!in_array($uniqueKey, $calculatedChannelProductId)) {
									$calculatedChannelProductId[] = $uniqueKey;
									$this->createUpstreamBillItem($item, $bill_date);
								}
							});
		
		//调整成本账单
		$this->adjust(date('Y-m-d', $time));
		
		$this->output->success($bill_date . ' 成功');
	}
	
	/**
	 * 调整成本账单
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/28 14:15
	 *
	 * @param $date string 调整日期
	 *
	 * @return void
	 **/
	protected function adjust($date)
	{
		$data = UpstreamBillAdjust::select([
			'apikey',
			'account_id',
			'customer_id',
			'channel',
			'product_id',
			'date',
			'money',
			'fee_number',
		])
								  ->where(function ($query) {
									  if ('all' != $this->channel) {
										  $query->whereIn('channel', $this->channel);
									  }
			
									  if ('all' != $this->product_id) {
										  $query->whereIn('product_id', $this->product_id);
									  }
								  })
								  ->where('date', $date)
								  ->get()
								  ->map(function ($item) {
									  $item->details    = '成本账单调整数据';
									  $item->date       = intval(date('Ymd', strtotime($item->date)));
									  $item->fee_number = intval($item->fee_number);
									  $item->money      = floatval($item->money);
			
									  return $item;
								  })
								  ->toArray();
		
		if (empty($data)){
			return;
		}
		
		MongoUpstreamBill::insert(array_values($data));
	}
	
	/**
	 * 根据一条计费配置生成一个账单
	 *
	 * @access protected
	 *
	 * @param $fee_config  Collection 计费配置
	 * @param $bill_date   string 账单日
	 *
	 * @return void
	 **/
	protected function createUpstreamBillItem($fee_config, $bill_date)
	{
		$price = @json_decode($fee_config->price, true);
		if (is_null($price)) {
			$this->wechat("脚本[{$this->name}]不能读取计费配置价格内容：" . json_encode($fee_config->toArray(), JSON_UNESCAPED_UNICODE));
			
			return;
		}
		$product_id = $fee_config->product_id;
		$channel    = $fee_config->channel;
		
		//汇总相关的量（账号维度）
		//遍历每一条数据,进行客户维度的合计计算
		$data = MongoUpstream::where('product_id', $product_id)
							 ->where('channel', $channel)
							 ->where('date', $bill_date)
							 ->get()
							 ->reduce(function ($result, $item) {
								 if (!isset($item->data)) {
									 return $result;
								 }
								 $apikey = $item->apikey;
								 //如果不存在apikey的统计量数据，需要先初始化
								 if (!array_key_exists($apikey, $result)) {
									 $result[$apikey]           = array_combine($this->upstream_field, array_fill(0, count($this->upstream_field), 0));
									 $result[$apikey]['apikey'] = $apikey;
								 }
								 //将本次查得的统计量，加入到客户的统计量中
								 foreach ($this->upstream_field as $field) {
									 if (!array_key_exists($field, $result[$apikey])) {
										 $result[$apikey][$field] = 0;
									 }
									 if (isset($item->data[$field])) {
										 $result[$apikey][$field] += $item->data[$field];
									 }
								 }
			
								 return $result;
							 }, []);
		
		//汇总成本调整的量
		//		$data = AdjustCostStatistics::where('date', date('Y-m-d', strtotime($bill_date)))
		//									->where('channel', $channel)
		//									->where('product_id', $product_id)
		//									->get()
		//									->reduce(function ($result, $item) {
		//										$apikey = $item->apikey;
		//										if (!array_key_exists($apikey, $result)) {
		//											$result[$apikey]           = array_combine($this->upstream_field, array_fill(0, count($this->upstream_field), 0));
		//											$result[$apikey]['apikey'] = $apikey;
		//										}
		//										//将本次查得的统计量，加入到客户的统计量中
		//										foreach ($this->upstream_field as $field) {
		//											if (!array_key_exists($field, $result[$apikey])) {
		//												$result[$apikey][$field] = 0;
		//											}
		//											if (0 != $item->$field) {
		//												$result[$apikey][$field] += $item->$field;
		//											}
		//										}
		//										return $result;
		//									}, $data);
		
		if (empty($data)) {
			return;
		}
		
		//整理数据
		//1.汇总金额和计费用量
		//2.查找对应的customer_id 和account_id
		
		$account_data = $this->getAccountData();
		$bill_data    = array_map(function ($item) use ($price, $bill_date, $account_data, $fee_config) {
			$details     = $this->makeEveryApikeyUpstreamBill($price, $item);
			$apikey      = $item['apikey'];
			$date        = $bill_date;
			$money       = array_sum(array_column($details, 'money'));
			$fee_number  = array_sum(array_values(array_intersect_key($item, $price)));
			$customer_id = array_key_exists($apikey, $account_data) ? $account_data[$apikey]['customer_id'] : '';
			$account_id  = array_key_exists($apikey, $account_data) ? $account_data[$apikey]['account_id'] : '';
			$channel     = $fee_config['channel'];
			$product_id  = $fee_config['product_id'];
			
			return compact('customer_id', 'account_id', 'apikey', 'channel', 'product_id', 'date', 'money', 'fee_number', 'details', 'price');
		}, $data);
		
		//保存账单数据
		MongoUpstreamBill::insert(array_values($bill_data));
	}
	
	/**
	 * 生成每个客户的账单（成本账单）
	 *
	 * @access protected
	 *
	 * @param $priceConfig     array 单价
	 * @param $statistic       array 统计量
	 *
	 * @return array
	 **/
	protected function makeEveryApikeyUpstreamBill($priceConfig, $statistic)
	{
		$bill_data = [];
		foreach ($this->upstream_field as $field) {
			$fee_number        = array_get($statistic, $field, 0);
			$money             = $fee_number * array_get($priceConfig, $field, 0);
			$price             = array_get($priceConfig, $field, '--');
			$bill_data[$field] = compact('price', 'fee_number', 'money');
		}
		
		return $bill_data;
	}
	
	/**
	 * 获取账号数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected $account_data;
	
	protected function getAccountData()
	{
		if (empty($this->account_data)) {
			$account_data       = Account::where('is_delete', '=', 0)
										 ->select('apikey', 'account_id', 'customer_id')
										 ->get()
										 ->toArray();
			$this->account_data = array_column($account_data, null, 'apikey');
		}
		
		return $this->account_data;
	}
}