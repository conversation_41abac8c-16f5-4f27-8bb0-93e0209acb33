<?php

namespace App\Console\Commands;

use App\Models\Channel;
use App\Models\ChannelAccount;
use App\Models\ChannelInterface;
use App\Models\ConfigPriceInterface;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

#运营商余额预警
class ChannelBalanceAlert extends Command
{
    public $redis;
    protected $signature = 'channel_balance_alert';

    #飞书机器人配置
    const FEISHU_ALERT_URL = 'https://open.feishu.cn/open-apis/bot/v2/hook/325295e7-066d-4491-a624-755470f944bd';
    const FEISHU_ALERT_SECRET = 'Lu47ucKwrknTGXkjAZLsmc';

    #需要预警的渠道列表
    const NEED_CHANNEL_IDS = [1 => 200000, 12 => 30000, 53 => 150000, 42 => 500000];
    #报警队列key
    const ALERT_CACHE_KEY = 'finance_channel_balance_alert';
    #最少可用天数队列key
    const ALERT_DAY_CACHE_KEY = 'finance_channel_lowday_alert';
    #暂停报警key
    const PAUSE_ALERT_KEY = 'finance_channel_balance_alert_pause_lbn';
    #接收报警电话
    const CALL_PHONE_MAP = [
        '刘丽丽' => ***********,
        '张韧' => 15611378317,
        '张宁宁' => 15521250596,
    ];

    public function __construct()
    {
        parent::__construct();
        $this->redis = Redis::connection('default');
    }

    public function handle()
    {
        #渠道余额预警
        $this->balanceWarning();
        #最少可用天数预警
        $this->calculateLowestBalanceWarningDays();
        #预警
        $this->feishuAlert();

        $this->output->success("完成");
    }

    #最少可用天数预警
    public function calculateLowestBalanceWarningDays()
    {
        $channel_balance_list = Channel::getChannelByChannelIdS(array_keys(self::NEED_CHANNEL_IDS));
        $channel_call_num = ChannelAccount::getChannelAccountPart();
        $channel_call_num = array_column($channel_call_num, 'max_call_num', 'channel_id');
        $channel_call_num[40] = $channel_call_num[42] ?? 0; // 添加默认值处理

        #获取渠道接口
        $channel_interfaces_map = (new ChannelInterface())->select('channel_id', 'id')
            ->whereIn('channel_id', array_keys(self::NEED_CHANNEL_IDS))
            ->get()
            ->groupBy('channel_id')
            ->map(function ($items) {
                return $items->pluck('id')->toArray();
            })
            ->toArray();

        foreach ($channel_balance_list as $item) {
            $channel_id = $item['channel_id'];

            #暂停报警
            $pause_key = self::PAUSE_ALERT_KEY . '_' . $channel_id;
            $stop_alert = $this->redis->get($pause_key);
            if ($stop_alert) {
                continue;
            }

            $channel_interface_id = $channel_interfaces_map[$channel_id] ?? [];

            if ($channel_id == 42) {
                $channel_interface_id = array_merge($channel_interface_id, $channel_interfaces_map[40] ?? []);
            }
            if (!$channel_interface_id) {
                continue;
            }
            #获取接口单价
            $price = $this->getPrice($channel_interface_id);
            $money_day = $price * ($channel_call_num[$channel_id] ?? 0);
            if (!$money_day) {
                continue;
            }
            echo $item['real_balance'] / $money_day . PHP_EOL;
            if ($item['real_balance'] / $money_day < 3) {
                $msg = "渠道：" . $item['label'] . "\n最少可用天数小于3天，请关注";
                $this->addAlertQueue(self::ALERT_DAY_CACHE_KEY, $channel_id, $msg);
            }
        }
    }

    #渠道余额预警
    public function balanceWarning()
    {
        $channel_list = Channel::getChannelByChannelIdS(array_keys(self::NEED_CHANNEL_IDS));
        foreach ($channel_list as $item) {
            $channel_id = $item['channel_id'];
            $balance = $item['real_balance'];
            $channel_name = $item['label'];

            if (!isset(self::NEED_CHANNEL_IDS[$channel_id])) {
                continue;
            }

            #暂停报警
            $pause_key = self::PAUSE_ALERT_KEY . '_' . $channel_id;
            $stop_alert = $this->redis->get($pause_key);
            if ($stop_alert) {
                continue;
            }

//            echo $channel_name . ' 报警余额阈值：' . self::NEED_CHANNEL_IDS[$channel_id] . ' 余额：' . $balance . PHP_EOL;
            if ($balance < self::NEED_CHANNEL_IDS[$channel_id]) {
                $msg = "渠道：" . $channel_name . "\n预警余额：" . self::NEED_CHANNEL_IDS[$channel_id] . "\n当前余额：" . $balance;
                $this->addAlertQueue(self::ALERT_CACHE_KEY, $channel_id, $msg);
            }
        }
    }


    #加入报警队列
    public function addAlertQueue($cache_key, $channel_id, $msg)
    {
        $this->redis->hset($cache_key, $channel_id, $msg);
        $this->redis->expire($cache_key, 86400 * 7);
    }


    #执行报警策略
    public function feishuAlert()
    {
        $currentHour = date('H:i');
        if (!in_array($currentHour, ['11:00', '18:00'])) {
            return;
        }
        $alert = 0;

        $msg_str = "运营商余额预警" . PHP_EOL . PHP_EOL;
        #余额预警
        $redis_result = $this->redis->hgetall(self::ALERT_CACHE_KEY);
        if ($redis_result) {
            $alert = 1;
            foreach ($redis_result as $msg) {
                $msg_str .= $msg . PHP_EOL . PHP_EOL;
            }
        }

        #最少可用天数预警
        $redis_result = $this->redis->hgetall(self::ALERT_DAY_CACHE_KEY);
        #隔天预警一次
        if ($redis_result && (date('d') % 2) == 0 && $currentHour === '11:00') {
            $alert = 1;
            foreach ($redis_result as $msg) {
                $msg_str .= $msg . PHP_EOL . PHP_EOL;
            }
        }
        $msg_str .= "当前时间：" . date('Y-m-d H:i') . PHP_EOL;

        if ($alert == 0) {
            return;
        }
        Func::sendFeiShu(self::FEISHU_ALERT_URL, self::FEISHU_ALERT_SECRET, $msg_str);

        foreach (self::CALL_PHONE_MAP as $phone) {
            Func::getAliCall($phone, '渠道余额预警', $msg_str);
        }

        #删除报警队列
        $this->redis->del(self::ALERT_CACHE_KEY, self::ALERT_DAY_CACHE_KEY);
    }

    protected function getPrice($interface_ids)
    {
        if (empty($interface_ids)) {
            return 0;
        }
        $interface_ids = is_array($interface_ids) ? implode(',', $interface_ids) : $interface_ids;
        $res = ConfigPriceInterface::getLatestPriceByIids($interface_ids);
        $price_arr = [];
        foreach ($res as $item) {
            $tmp = json_decode($item['price'], true);
            $tmp_price = empty($tmp[0]['price']) ? 0 : $tmp[0]['price'];
            $tmp_price = (string)$tmp_price;
            $price_arr[$tmp_price] = ($price_arr[$tmp_price] ?? 0) + 1;
        }

        arsort($price_arr);
        reset($price_arr);
        return key($price_arr);
    }
}
