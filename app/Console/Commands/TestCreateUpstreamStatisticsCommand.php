<?php

namespace App\Console\Commands;


use Illuminate\Console\Command;

//创建上游数据源调用量的测试数据
class TestCreateUpstreamStatisticsCommand extends Command
{
    protected $signature   = 'test:create_upstream_statistics
        {--first_date= : 所生成测试数据的第一天日期, 格式Ymd，默认今日}
        {--days=1 : 需要生成测试数据的天数，默认1天}
    ';
    protected $description = '创建上游数据源调用量的测试数据';

    protected $firstDate = '';
    protected $days      = 1;

    public function handle()
    {
        $res = $this->validateAggregate();


        if ($res) {
            $firstDayTimestamp = strtotime($this->firstDate);
            $lastDayTimestamp  = $firstDayTimestamp + 86400 * ($this->days - 1);

            for ($i = $firstDayTimestamp; $i <= $lastDayTimestamp; $i += 86400) {
                $date = intval(date('Ymd', $i));
                $this->createItemStatistics($date);
            }
        }
    }

    /**
     * 生成某一天的数据
     *
     * @access protected
     *
     * @param $date integer 日期
     *
     * @return void
     **/
    protected function createItemStatistics($date)
    {

    }

    /**
     * 校验参数
     *
     * @access protected
     *
     * @return boolean
     **/
    protected function validateAggregate()
    {
        $firstDate = $this->input->getOption('first_date') ?: date('Ymd');
        if (!empty($firstDate) && !preg_match('/^[\d]{8}$/', $firstDate)) {
            $this->output->error("first_date 格式不正确");
            return false;
        }
        $this->firstDate = $firstDate;

        $days = $this->input->getOption('days');
        if (!is_numeric($days) || $days < 1) {
            $this->output->error("days 格式不正确");
            return false;
        }
        $this->days = $days;
        return true;
    }
}