<?php

namespace App\Console\Commands;

use App\Models\BillConfig;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\Dept;
use App\Models\DeptGrade;
use App\Models\MoneyRecharge;
use App\Models\MongoBillDay;
use App\Models\MongoBillMonth;
use App\Models\MongoEmailQueueLogs;
use App\Models\MongoProfitLogs;
use App\Models\MongoProfitSalesmanLogs;
use App\Models\MongoTransferBill;
use App\Models\MongoUpstreamBill;
use App\Models\Product;
use App\Models\SystemUser;
use App\Models\Upstream;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Redis;

/**
 * 废弃
 */
class ReportMonthProfit extends Command
{
	use CurlTrait;
	use WechatExceptionTrait;
	
	protected $name = '利润权责月报表';
	//命令名称
	public $signature = 'reportMonth:profit
    {--month= : 月份, 格式Ym}
    {--username= : 收件人，以用户在后台的用户名为基准，多个用户使用,隔开，该参数不会改变用户的邮件授权情况，不传则对所有授权用户发送}
    ';
	/** @var string 命令提示 */
	public $description = '收入、成本、利润月报表邮件发送';
	
	
	//商务部门的ID
	protected $businessDeptId = 'DEPT2019061815074748';
	
	public $productBase = null;
	
	//约定发送给那些用户，为null代表发送给所有用户
	protected $username = null;
	
	
	//当日、本月累计数据使用的是日账单，本年累计非本月数据使用月账单
	//当日时间，即发送的账单日期
	protected $month;           //所处的月份
	protected $monthLastDay;     //当前月份的最后一天日期
	protected $yearFirstDay;   //本年第一天
	
	protected $incomeData;      //收入数据
	protected $costData;        //成本数据
	protected $rechargeData;    //充值数据
	protected $specialData;     //特殊金额数据
	protected $bxfShortCostData;    //邦信分快捷版成本数据
	
	//排序字段
	protected $sortBy = 'this_month_income_money';
	
	//客户名称映射表
	protected $cacheCustomerMapping = null;
	
	
	public function __construct()
	{
		parent::__construct();
	}
	
	/**
	 * @throws \Exception
	 */
	public function handle()
	{
		#初始化设置
		$res = $this->setAttribute();
		
		#发送邮件
		if ($res) {
			$this->send();
		}
	}
	
	/**
	 * 设置参数
	 *
	 * @access protected
	 *
	 * @return boolean
	 **/
	protected function setAttribute()
	{
		//设置所需要的日期
		$this->month = empty($this->option('month')) ? date('Ym', strtotime('first day of last month')) : $this->option('month');
		if (!preg_match('/^\d{6}$/', $this->month)) {
			$this->output->error('月份格式不正确');
			
			return false;
		}
		if ($this->month >= date('Ym')) {
			$this->output->error('您所设置的月份大于当前月份，不可生成');
			
			return false;
		}
		
		$this->monthLastDay = date('Ymd', strtotime('last day of ' . date('Y-m', strtotime($this->month . '01'))));
		$this->yearFirstDay = date('Y', strtotime($this->month . '01')) . '0101';
		
		//获取每个账号、每个产品维度的成本统计
		$this->costData = $this->getAggregateCost();
		//获取每个账号、每个产品维度的收入统计
		$this->incomeData = $this->getAggregateIncome();
		//获取每个客户的充值统计
		$this->rechargeData = $this->getAggregateRecharge();
		//获取特殊费用数据
		//$this->specialData = $this->getSpecial();
		//获取邦信分快捷版的成本数据
		$this->bxfShortCostData = $this->getBxfShortCost();
		
		$username = $this->option('username');
		if (!empty($username)) {
			$this->username = explode(',', $username);
		}
		
		return true;
	}
	
	/**
	 * 获取邦信分快捷版的成本
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/6/9 10:03
	 *
	 * @return array
	 **/
	protected function getBxfShortCost()
	{
		$result = [];
		$base   = [
			'customer_id'  => '',
			'product_id'   => '',
			'account_id'   => '',
			'month_money'  => 0,
			'month_number' => 0,
			'year_money'   => 0,
			'year_number'  => 0,
		];
		
		//评分类字段的成本渠道
		$store_channel = Upstream::where('product_id', 210)
								 ->where('type', 1)
								 ->pluck('channel')
								 ->toArray();
		//统计类字段的成本渠道
		$normal_channel = Upstream::where('product_id', 210)
								  ->where('type', 2)
								  ->pluck('channel')
								  ->toArray();
		
		//mongo统计查询条件
		$aggregate = [
			[
				'$match' => [
					'date'       => '',
					'product_id' => 210,
					'channel'    => '',
				],
			],
			[
				'$group' => [
					'_id'         => [
						'account_id' => '$account_id',
						'product_id' => '$product_id',
					],
					'money'       => ['$sum' => '$money',],
					'fee_number'  => ['$sum' => '$fee_number',],
					'customer_id' => ['$first' => '$customer_id',],
					'account_id'  => ['$first' => '$account_id',],
					'product_id'  => ['$first' => '$product_id',],
				],
			],
			[
				'$project' => [
					'_id'         => 0,
					'money'       => 1,
					'fee_number'  => 1,
					'customer_id' => 1,
					'product_id'  => 1,
					'account_id'  => 1,
				],
			],
		];
		
		//月查询条件
		$aggregate[0]['$match']['date']    = [
			'$gte' => intval($this->month . '01'),
			'$lte' => intval($this->monthLastDay),
		];
		$aggregate[0]['$match']['channel'] = ['$in' => $store_channel];
		$storeMonthAggregate               = $aggregate;
		$aggregate[0]['$match']['channel'] = ['$in' => $normal_channel];
		$normalMonthAggregate              = $aggregate;
		
		//年查询条件
		$aggregate[0]['$match']['date']    = [
			'$gte' => intval($this->yearFirstDay),
			'$lte' => intval($this->monthLastDay),
		];
		$aggregate[0]['$match']['channel'] = ['$in' => $store_channel];
		$storeYearAggregate                = $aggregate;
		$aggregate[0]['$match']['channel'] = ['$in' => $normal_channel];
		$normalYearAggregate               = $aggregate;
		
		//月数据
		$storeMonthData  = MongoUpstreamBill::query()
											->raw(function ($collection) use ($storeMonthAggregate) {
												return $collection->aggregate($storeMonthAggregate);
											})
											->toArray();
		$normalMonthData = MongoUpstreamBill::query()
											->raw(function ($collection) use ($normalMonthAggregate) {
												return $collection->aggregate($normalMonthAggregate);
											})
											->toArray();
		
		//年数据
		$storeYearData  = MongoUpstreamBill::query()
										   ->raw(function ($collection) use ($storeYearAggregate) {
											   return $collection->aggregate($storeYearAggregate);
										   })
										   ->toArray();
		$normalYearData = MongoUpstreamBill::query()
										   ->raw(function ($collection) use ($normalYearAggregate) {
											   return $collection->aggregate($normalYearAggregate);
										   })
										   ->toArray();
		
		
		//月、年成本遍历数据，汇总到result变量中
		array_walk($storeMonthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 251;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
		});
		array_walk($normalMonthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 241;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
		});
		array_walk($storeYearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 251;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = $money;
			$result[$key]['year_number'] = $number;
		});
		array_walk($normalYearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = 241;
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = $money;
			$result[$key]['year_number'] = $number;
		});
		
		return array_values($result);
	}
	
	/**
	 * 获取本年内的特殊费用数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getSpecial()
	{
		$customerMapping = $this->getCustomerMapping();
		
		return CustomerExpend::where('profile_show_date', '>=', date('Y-m-d', strtotime($this->yearFirstDay)))
							 ->where('profile_show_date', '<=', date('Y-m-d', strtotime($this->monthLastDay)))
							 ->orderBy('start_date')
							 ->get()
							 ->map(function ($item) use ($customerMapping) {
								 $customer_id = $item['customer_id'];
								 $account_id  = $item['customer_id'];
								 $product_id  = $item['product_id'];
								 $date        = intval(date('Ymd', strtotime($item['profile_show_date'])));
								 $money       = 0;
								 $fee_number  = 0;
								 if ($item['type'] == 1) {
									 $fee_number -= $item['fee_number'];
									 $money      -= $item['money'];
								 } else if ($item['type'] == 2) {
									 $fee_number += $item['fee_number'];
									 $money      += $item['money'];
								 }
			
								 return compact('customer_id', 'account_id', 'product_id', 'date', 'money', 'fee_number');
							 })
							 ->toArray();
	}
	
	/**
	 * 获取成本统计
	 *
	 * @access protected
	 *
	 * @return array    二维数组，每个单元的数据格式为
	 * [customer_id, product_id, account_id, week_money, week_number, month_money, month_number, year_money,
	 * month_number]
	 **/
	protected function getAggregateCost()
	{
		$result = [];
		$base   = [
			'customer_id'  => '',
			'product_id'   => '',
			'account_id'   => '',
			'month_money'  => 0,
			'month_number' => 0,
			'year_money'   => 0,
			'year_number'  => 0,
		];
		
		$monthData = MongoUpstreamBill::query()
									  ->raw(function ($collection) {
										  $aggregate               = [];
										  $aggregate[]['$match']   = [
											  'date' => [
												  '$gte' => intval($this->month . '01'),
												  '$lte' => intval($this->monthLastDay),
											  ],
										  ];
										  $aggregate[]['$group']   = [
											  '_id'         => [
												  'account_id' => '$account_id',
												  'product_id' => '$product_id',
											  ],
											  'money'       => [
												  '$sum' => '$money',
											  ],
											  'fee_number'  => [
												  '$sum' => '$fee_number',
											  ],
											  'customer_id' => [
												  '$first' => '$customer_id',
											  ],
											  'account_id'  => [
												  '$first' => '$account_id',
											  ],
											  'product_id'  => [
												  '$first' => '$product_id',
											  ],
										  ];
										  $aggregate[]['$project'] = [
											  '_id'         => 0,
											  'money'       => 1,
											  'fee_number'  => 1,
											  'customer_id' => 1,
											  'product_id'  => 1,
											  'account_id'  => 1,
										  ];
			
										  return $collection->aggregate($aggregate);
									  })
									  ->toArray();
		
		$yearData = MongoUpstreamBill::query()
									 ->raw(function ($collection) {
										 $aggregate               = [];
										 $aggregate[]['$match']   = [
											 'date' => [
												 '$gte' => intval($this->yearFirstDay),
												 '$lte' => intval($this->monthLastDay),
											 ],
										 ];
										 $aggregate[]['$group']   = [
											 '_id'         => [
												 'account_id' => '$account_id',
												 'product_id' => '$product_id',
											 ],
											 'money'       => [
												 '$sum' => '$money',
											 ],
											 'fee_number'  => [
												 '$sum' => '$fee_number',
											 ],
											 'customer_id' => [
												 '$first' => '$customer_id',
											 ],
											 'account_id'  => [
												 '$first' => '$account_id',
											 ],
											 'product_id'  => [
												 '$first' => '$product_id',
											 ],
										 ];
										 $aggregate[]['$project'] = [
											 '_id'         => 0,
											 'money'       => 1,
											 'fee_number'  => 1,
											 'customer_id' => 1,
											 'product_id'  => 1,
											 'account_id'  => 1,
										 ];
			
										 return $collection->aggregate($aggregate);
									 })
									 ->toArray();
		
		
		//日、月、年成本遍历数据，汇总到result变量中
		array_walk($monthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
		});
		array_walk($yearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = $money;
			$result[$key]['year_number'] = $number;
		});
		
		return array_values($result);
	}
	
	/**
	 * 获取收入统计
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getAggregateIncome()
	{
		$result = [];
		$base   = [
			'customer_id'  => '',
			'product_id'   => '',
			'account_id'   => '',
			'month_money'  => 0,
			'month_number' => 0,
			'year_money'   => 0,
			'year_number'  => 0,
		];
		
		$monthData = MongoBillMonth::query()
								   ->raw(function ($collection) {
									   $aggregate               = [];
									   $aggregate[]['$match']   = [
										   'month' => $this->month,
									   ];
									   $aggregate[]['$group']   = [
										   '_id'         => [
											   'account_id' => '$account_id',
											   'product_id' => '$product_id',
										   ],
										   'money'       => [
											   '$sum' => '$money',
										   ],
										   'fee_number'  => [
											   '$sum' => '$section_invoked_number',
										   ],
										   'customer_id' => [
											   '$first' => '$customer_id',
										   ],
										   'account_id'  => [
											   '$first' => '$account_id',
										   ],
										   'product_id'  => [
											   '$first' => '$product_id',
										   ],
									   ];
									   $aggregate[]['$project'] = [
										   '_id'         => 0,
										   'money'       => 1,
										   'fee_number'  => 1,
										   'customer_id' => 1,
										   'product_id'  => 1,
										   'account_id'  => 1,
									   ];
			
									   return $collection->aggregate($aggregate);
								   })
								   ->toArray();
		
		//年累计收入查询月账单
		$yearData = MongoBillMonth::query()
								  ->raw(function ($collection) {
									  $aggregate               = [];
									  $aggregate[]['$match']   = [
										  'month' => [
											  '$lte' => $this->month,
											  '$gte' => date('Ym', strtotime($this->yearFirstDay)),
										  ],
									  ];
									  $aggregate[]['$group']   = [
										  '_id'         => [
											  'account_id' => '$account_id',
											  'product_id' => '$product_id',
										  ],
										  'money'       => [
											  '$sum' => '$money',
										  ],
										  'fee_number'  => [
											  '$sum' => '$section_invoked_number',
										  ],
										  'customer_id' => [
											  '$first' => '$customer_id',
										  ],
										  'account_id'  => [
											  '$first' => '$account_id',
										  ],
										  'product_id'  => [
											  '$first' => '$product_id',
										  ],
									  ];
									  $aggregate[]['$project'] = [
										  '_id'         => 0,
										  'money'       => 1,
										  'fee_number'  => 1,
										  'customer_id' => 1,
										  'product_id'  => 1,
										  'account_id'  => 1,
									  ];
			
									  return $collection->aggregate($aggregate);
								  })
								  ->toArray();
		
		//日、月、年成本遍历数据，汇总到result变量中
		
		array_walk($monthData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['month_money']  = $money;
			$result[$key]['month_number'] = $number;
		});
		
		array_walk($yearData, function ($item) use (&$result, $base) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			$money       = $item['money'];
			$number      = $item['fee_number'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
				$result[$key]['account_id']  = $account_id;
			}
			$result[$key]['year_money']  = bcadd($result[$key]['year_money'], $money, 6);
			$result[$key]['year_number'] = bcadd($result[$key]['year_number'], $number);
		});
		
		//账单转移数据
		$transferData = $this->getTransfer();
		
		//融合账单转移数据
		array_walk($transferData, function ($item) use (&$result, $base) {
			$account_id          = $item['account_id'];
			$customer_id         = $item['customer_id'];
			$product_id          = $item['product_id'];
			$original_product_id = $item['original_product_id'];
			$key                 = $account_id . '_' . $product_id;
			$original_key        = $account_id . '_' . $original_product_id;
			//金盾增加转移数据
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['account_id']  = $account_id;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
			}
			$result[$key]['month_number'] = bcadd($item['month_number'], $result[$key]['month_number']);
			$result[$key]['year_number']  = bcadd($item['year_number'], $result[$key]['year_number']);
			$result[$key]['month_money']  = bcadd($item['month_money'], $result[$key]['month_money'], 4);
			$result[$key]['year_money']   = bcadd($item['year_money'], $result[$key]['year_money'], 4);
			//邦秒验减少转移数据
			if (!array_key_exists($original_key, $result)) {
				$result[$original_key]                = $base;
				$result[$original_key]['account_id']  = $account_id;
				$result[$original_key]['customer_id'] = $customer_id;
				$result[$original_key]['product_id']  = $original_product_id;
			}
			$result[$original_key]['month_number'] = bcsub($result[$original_key]['month_number'], $item['month_number']);
			$result[$original_key]['year_number']  = bcsub($result[$original_key]['year_number'], $item['year_number']);
			$result[$original_key]['month_money']  = bcsub($result[$original_key]['month_money'], $item['month_money'], 4);
			$result[$original_key]['year_money']   = bcsub($result[$original_key]['year_money'], $item['year_money'], 4);
		});
		
		//融合特殊收入数据
		$special = $this->getSpecial();
		array_walk($special, function ($item) use (&$result, $base) {
			$account_id  = $item['account_id'];
			$customer_id = $item['customer_id'];
			$product_id  = $item['product_id'];
			$key         = $account_id . '_' . $product_id;
			if (!array_key_exists($key, $result)) {
				$result[$key]                = $base;
				$result[$key]['account_id']  = $account_id;
				$result[$key]['customer_id'] = $customer_id;
				$result[$key]['product_id']  = $product_id;
			}
			$date  = date('Ymd', strtotime($item['date']));
			$month = date('Ym', strtotime($item['date']));
			if ($month == $this->month) {
				$result[$key]['month_number'] = bcadd($item['fee_number'], $result[$key]['month_number']);
				$result[$key]['month_money']  = bcadd($item['money'], $result[$key]['month_money'], 4);
				$result[$key]['year_number']  = bcadd($item['fee_number'], $result[$key]['year_number']);
				$result[$key]['year_money']   = bcadd($item['money'], $result[$key]['year_money'], 4);
			} else if ($date >= $this->yearFirstDay && $month < $this->month) {
				$result[$key]['year_number'] = bcadd($item['fee_number'], $result[$key]['year_number']);
				$result[$key]['year_money']  = bcadd($item['money'], $result[$key]['year_money'], 4);
			}
		});
		
		return array_values($result);
	}
	
	/**
	 * 获取每个产品维度的转移账单数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/9 10:08
	 *
	 * @return array
	 **/
	protected function getTransfer()
	{
		$base   = [
			'original_product_id' => '',
			'product_id'          => '',
			'account_id'          => '',
			'customer_id'         => '',
			'month_money'         => 0,
			'month_number'        => 0,
			'year_money'          => 0,
			'year_number'         => 0,
		];
		$result = [];
		
		//本月统计
		MongoTransferBill::query()
						 ->raw(function ($collection) {
							 $aggregate = [
								 [
									 '$match' => [
										 'date' => [
											 '$gte' => intval($this->month . '01'),
											 '$lte' => intval($this->monthLastDay),
										 ],
									 ],
								 ],
								 [
									 '$group' => [
										 '_id'                 => [
											 'original_product_id' => '$original_product_id',
											 'product_id'          => '$product_id',
											 'customer_id'         => '$customer_id',
											 'account_id'          => '$account_id',
										 ],
										 'money'               => ['$sum' => '$money'],
										 'fee_number'          => ['$sum' => '$fee_number'],
										 'original_product_id' => [
											 '$first' => '$original_product_id',
										 ],
										 'product_id'          => [
											 '$first' => '$product_id',
										 ],
										 'customer_id'         => [
											 '$first' => '$customer_id',
										 ],
										 'account_id'          => [
											 '$first' => '$account_id',
										 ],
									 ],
								 ],
								 [
									 '$project' => [
										 '_id' => 0,
									 ],
								 ],
							 ];
			
							 return $collection->aggregate($aggregate);
						 })
						 ->map(function ($item) use (&$result, $base) {
							 $key = $item->original_product_id . '_' . $item->product_id . '_' . $item->account_id;
							 if (!array_key_exists($key, $result)) {
								 $result[$key]                        = $base;
								 $result[$key]['product_id']          = $item->product_id;
								 $result[$key]['original_product_id'] = $item->original_product_id;
								 $result[$key]['customer_id']         = $item->customer_id;
								 $result[$key]['account_id']          = $item->account_id;
							 }
							 $result[$key]['month_number'] = bcadd($item['fee_number'], $result[$key]['month_number']);
							 $result[$key]['month_money']  = bcadd($item['money'], $result[$key]['month_money'], 4);
						 });
		
		//当年统计
		MongoTransferBill::query()
						 ->raw(function ($collection) {
							 $aggregate = [
								 [
									 '$match' => [
										 'date' => [
											 '$gte' => intval($this->yearFirstDay),
											 '$lte' => intval($this->monthLastDay),
										 ],
									 ],
								 ],
								 [
									 '$group' => [
										 '_id'                 => [
											 'original_product_id' => '$original_product_id',
											 'product_id'          => '$product_id',
											 'customer_id'         => '$customer_id',
											 'account_id'          => '$account_id',
										 ],
										 'money'               => ['$sum' => '$money'],
										 'fee_number'          => ['$sum' => '$fee_number'],
										 'original_product_id' => [
											 '$first' => '$original_product_id',
										 ],
										 'product_id'          => [
											 '$first' => '$product_id',
										 ],
										 'customer_id'         => [
											 '$first' => '$customer_id',
										 ],
										 'account_id'          => [
											 '$first' => '$account_id',
										 ],
									 ],
								 ],
								 [
									 '$project' => [
										 '_id' => 0,
									 ],
								 ],
							 ];
			
							 return $collection->aggregate($aggregate);
						 })
						 ->map(function ($item) use (&$result, $base) {
							 $key = $item->original_product_id . '_' . $item->product_id . '_' . $item->account_id;
							 if (!array_key_exists($key, $result)) {
								 $result[$key]                        = $base;
								 $result[$key]['product_id']          = $item->product_id;
								 $result[$key]['original_product_id'] = $item->original_product_id;
								 $result[$key]['customer_id']         = $item->customer_id;
								 $result[$key]['account_id']          = $item->account_id;
							 }
							 $result[$key]['year_number'] = bcadd($item['fee_number'], $result[$key]['year_number']);
							 $result[$key]['year_money']  = bcadd($item['money'], $result[$key]['year_money'], 4);
						 });
		
		return array_values($result);
	}
	
	/**
	 * 获取充值统计
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getAggregateRecharge()
	{
		$data = MoneyRecharge::selectRaw('sum(money) as money, customer_id')
							 ->where('remit_date', '>=', strtotime($this->yearFirstDay))
							 ->where('remit_date', '<=', strtotime($this->monthLastDay))
							 ->where('status', 3)
							 ->groupBy('customer_id')
							 ->get()
							 ->toArray();
		
		return array_column($data, 'money', 'customer_id');
	}
	
	
	/**
	 * 发送日账单
	 *
	 * @access protected
	 *
	 * @return void
	 **/
	protected function send()
	{
		//一 获取需要发送权责日报的用户、邮箱、权限规则（可见数据的规则）、所属部门ID[array]
		
		// {遍历:每个用户}
		
		//二 计算销售区域维度的数据
		//1.生成销售区域维度的部门用户数据
		//{判断:用户的可见数据规则}
		//{如果:全部数据，则生成全量的销售区域维度的统计数据，获取部门"DEPT2019061815074748"[部门ID]的所有部门及用户$businessDepartment([[name,id,member]])}
		//{如果:该用户属于此"DEPT2019061815074748"，获取该用户所具有的数据权限所涉及到的部门和用户$businessDepartment([[name,id,member]])}
		//{否则:$businessDepartment=[]}
		
		//2.获取$businessDepartment中所有的用户ID，并获取所有客户所具有的客户权限ID$customer_id，去重
		
		//3.获取$customer_id的当日成本合计、当日收入合计、当月成本合计、当月收入合计 $businessProfitData
		
		//4.与$businessDepartment进行最终整合，计算毛利
		
		//三 通过接口获取该用户所具有的客户权限 $customerIds
		
		//四 计算产品维度的数据
		//1.按产品、客户维度获取当月收入、当日收入
		
		//2.根据相应的规则，将各产品的收入进行汇总，
		
		//3.分别获取邦信分快捷版、邦秒验、邦企查的当日成本、当月累计成本，整合数据，并计算毛利
		
		//五 计算客户维度的数据
		
		//1.按客户维度获取当月收入、当日收入、当月成本、当日成本，并计算毛利
		
		//一
		$this->getUsers()
			 ->map(function ($item) {
			
				 try {
					 $res = $this->sendEvery($item);
					 if ($res) {
						 $this->output->success($item->username . '邮件入队成功');
					 }
				 } catch (\Exception $exception) {
					 $message      = $exception->getMessage();
					 $file         = $exception->getFile();
					 $line         = $exception->getLine();
					 $errorMessage = $item->username . "邮件入队失败\n Message:" . $message . "\n File:" . $file . '\n Line:' . $line;
					 #微信预警
					 $this->wechatException('【' . $this->name . '】' . $errorMessage);
					 $this->output->error($errorMessage);
				 }
			 });
	}
	
	/**
	 * 发送每一封邮件
	 *
	 * @access protected
	 *
	 * @param $user Collection
	 *
	 * @return boolean
	 **/
	protected function sendEvery($user)
	{
		$data = [];
		//二
		$username = $user->username;
		
		//如果设置了username参数，则过滤掉不需要发送的用户
		if (!is_null($this->username) && !in_array($username, $this->username)) {
			return false;
		}
		
		$email            = $user->email;
		$name             = $user->realname;
		$dataAuth         = $user->data_auth;
		$deptIds          = $user->deptIds;
		$data['business'] = $this->getBusinessData($email, $dataAuth, $deptIds);
		
		//三
		$allowReadCustomerIds = $this->getAllowReadCustomerIds($username);
		
		//四
		$data['product'] = $this->getProductData($allowReadCustomerIds);
		
		//五
		$data['customer'] = $this->getCustomerData($allowReadCustomerIds);
		
		//生成特殊费用的数据
		//$data['special'] = $this->getSpecialData($allowReadCustomerIds);
		
		//$date = $this->date;
		//记录日志
		//MongoProfitLogs::insert(compact('username', 'email', 'name', 'date', 'dataAuth', 'deptIds', 'data'));
		
		//生成excel
		$filename = $this->createExcelFile($data, $username);
		
		//生成HTML
		$title = date('Y年m月', strtotime($this->month . '01')) . '权责收入及毛利数据统计 -- （' . $name . '）收';
		$html  = $this->createHtml($data, $title);
		
		//redis入队（东宇的jar包将会从这个数据中发送邮件）
		$this->enQueue($title, $email, $html, $filename);
		
		return true;
	}
	
	/**
	 * 邮件入队
	 *
	 * @access protected
	 *
	 * @param $title    string 邮件主题
	 * @param $email    string 收件邮箱
	 * @param $html     string 邮件内容
	 * @param $filename string 附件的链接地址（url）
	 *
	 * @return void
	 **/
	protected function enQueue($title, $email, $html, $filename)
	{
		$fromName = "金融服务部";
		$isSingle = 1;
		$project  = '';
		$subject  = $title;
		$address  = [$email];
		$content  = $html;
		$attachs  = [
			[
				'url'  => $filename,
				'name' => '报表明细.xlsx',
			],
		];
		$data     = compact('fromName', 'isSingle', 'project', 'subject', 'content', 'address');
		
		/**
		 * @var \Illuminate\Redis\Connections\Connection
		 **/
		$redis             = Redis::connection('email_queue');
		$result            = $redis->lPush('MAIL_WARNING_QUEUE', json_encode($data, JSON_UNESCAPED_UNICODE));
		$create_at         = date('Y-m-d H:i:s');
		$data['create_at'] = $create_at;
		$data['result']    = $result;
		MongoEmailQueueLogs::insert($data);
	}
	
	/**
	 * 生成HTML页面
	 *
	 * @access protected
	 *
	 * @param $data  array 数据
	 * @param $title string 邮件主题
	 *
	 * @return string 返回的是HTML内容
	 **/
	protected function createHtml($data, $title)
	{
		//生成商务维度的TABLE内容
		$salesmanTable = $this->createsalesmanTable($data['business']);
		//生成产品维度的TABLE内容
		$productTable = $this->createProductTable($data['product']);
		//生成客户维度的TABLE内容
		$customerTable = $this->createCustomerTable($data['customer']);
		//特殊费用
		//$specialTable = $this->createSpecialTable($data['special']);
		
		return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <style>
        body {
            color : #333333;
        }
        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }
        h2 {
            width        : 98%;
            height       : 44px;
            line-height  : 44px;
            font-size    : 20px;
            font-weight  : bold;
            text-align   : center;
            margin       : 20px auto 2px;
            background   : rgba(229, 82, 45, 1);
            color        : #FFFFFF;
        }
        table {
            width       : 98%;
            border      : none;
            padding     : 0;
            margin      : 0 auto;
            font-size   : 14px;
            color       : #666666;
            border-left : 1px solid #CCCCCC;
            border-top  : 1px solid #CCCCCC;
        }
        tr {
            border-left : 1px solid #CCCCCC;
            border-top  : 1px solid #CCCCCC;
        }
        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            text-align    : center;
            padding       : 5px 0;
        }
        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
        }
        tr:nth-child(even) {
            background : #EEEEEE;
        }
        tr:hover {
            background : #CCCCCC;
        }
        .bold {
            font-weight : bold;
        }
        .fsz-16 {
            font-size : 15px;
        }
        .space{
            width  : 100%;
            height : 40px;
        }
    </style>
</head>
<body>
<h1>{$title}</h1>
<h2>商务维度权责报表</h2>
<table border="0" cellspacing="0" cellpadding="0">
    <tr class="fsz-16">
        <th>区域</th>
        <th>商务</th>
        <th>收入计费用量</th>
        <th>当月累计权责收入</th>
        <th>当月累计权责成本</th>
        <th>当月累计权责毛利</th>
        <th>本年累计现金收入</th>
        <th>本年累计权责收入</th>
        <th>本年累计权责成本</th>
        <th>本年累计权责毛利</th>
    </tr>
    {$salesmanTable}
</table>
<h2>产品维度权责报表</h2>
<table border="0" cellspacing="0" cellpadding="0">
    <tr class="fsz-16">
        <th>产品名称</th>
        <th>子产品名称</th>
        <th>收入计费用量</th>
        <th>当月累计权责收入</th>
        <th>当月累计权责成本</th>
        <th>当月累计权责毛利</th>
        <th>本年累计权责收入</th>
        <th>本年累计权责成本</th>
        <th>本年累计权责毛利</th>
    </tr>
    {$productTable}
</table>
<h2>客户维度权责报表</h2>
<table border="0" cellspacing="0" cellpadding="0">
    <tr class="fsz-16">
        <th>客户ID</th>
        <th>客户名称</th>
        <th>收入计费用量</th>
        <th>当月累计权责收入</th>
        <th>当月累计权责成本</th>
        <th>当月累计权责毛利</th>
        <th>本年累计现金收入</th>
        <th>本年累计权责收入</th>
        <th>本年累计权责成本</th>
        <th>本年累计权责毛利</th>
    </tr>
    {$customerTable}
</table>
<div class="space"></div>
</body>
</html>
HTML;
	}
	
	/**
	 * 特殊费用TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 特殊费用数据
	 *
	 * @return string
	 **/
	protected function createSpecialTable($data)
	{
		$html = '';
		array_walk($data, function ($item) use (&$html) {
			$html .= <<<TR
<tr>
    <td>{$item['customer_id']}</td>
    <td>{$item['customer_name']}</td>
    <td>{$item['type']}</td>
    <td>{$item['start_date']}</td>
    <td>{$item['name']}</td>
    <td>{$this->disposeMoney($item['money'])}</td>
    <td>{$item['product_name']}</td>
</tr>
TR;
		});
		
		return $html;
	}
	
	/**
	 * 客户维度权责报表TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 客户维度的数据
	 *
	 * @return string
	 **/
	protected function createCustomerTable($data)
	{
		$html  = '';
		$total = $data['total'];
		$html  .= <<<TR
<tr>
    <td class="bold" colspan="2">合计</td>
    <td>{$this->disposeNumber($total['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($total['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_recharge_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_profit_money'])}</td>
</tr>
TR;
		$data  = $data['data'];
		array_walk($data, function ($item) use (&$html) {
			$html .= <<<TR
<tr>
    <td>{$item['customer_id']}</td>
    <td>{$item['customer_name']}</td>
    <td>{$this->disposeNumber($item['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($item['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_recharge_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_profit_money'])}</td>
</tr>
TR;
		});
		
		return $html;
	}
	
	/**
	 * 产品维度权责报表TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 产品维度的数据
	 *
	 * @return string
	 **/
	protected function createProductTable($data)
	{
		$html  = '';
		$total = $data['total'];
		$html  .= <<<TR
<tr>
    <td class="bold" colspan="2">合计</td>
    <td>{$this->disposeNumber($total['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($total['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_profit_money'])}</td>
</tr>
TR;
		$data  = $data['data'];
		array_walk($data, function ($item) use (&$html) {
			if (!array_key_exists('children', $item)) {
				$html .= <<<TR
<tr>
    <td>{$item['name']}</td>
    <td>--</td>
    <td>{$this->disposeNumber($item['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($item['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_profit_money'])}</td>
</tr>
TR;
			} else {
				$rowspan = count($item['children']) + 1;
				$html    .= <<<TR
<tr>
        <td rowspan="{$rowspan}">{$item['name']}</td>
    <td>小计</td>
    <td>{$this->disposeNumber($item['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($item['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($item['this_year_profit_money'])}</td>
</tr>
TR;
				array_walk($item['children'], function ($childreItem) use (&$html) {
					$html .= <<<TR
<tr>
    <td>{$childreItem['name']}</td>
    <td>{$this->disposeNumber($childreItem['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($childreItem['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($childreItem['this_year_profit_money'])}</td>
</tr>
TR;
				});
				
			}
		});
		
		return $html;
	}
	
	/**
	 * 生成商务维度的统计TABLE
	 *
	 * @access protected
	 *
	 * @param $data array 统计数据
	 *
	 * @return string
	 **/
	protected function createSalesmanTable($data)
	{
		$html  = '';
		$total = $data['total'];
		$html  .= <<<TR
<tr>
    <td class="bold" colspan="2">合计</td>
    <td>{$this->disposeNumber($total['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($total['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_recharge_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($total['this_year_profit_money'])}</td>
</tr>
TR;
		$data  = $data['data'];
		array_walk($data, function ($item) use (&$html) {
			$itemTotal = $item['total'];
			$itemData  = $item['data'];
			$rowspan   = count($itemData) + 1;
			$html      .= <<<TR
<tr>
    <td rowspan="{$rowspan}">{$item['name']}</td>
    <td class="bold">小计</td>
    <td>{$this->disposeNumber($itemTotal['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_year_recharge_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($itemTotal['this_year_profit_money'])}</td>
</tr>
TR;
			array_walk($itemData, function ($salesmanItem) use (&$html) {
				$html .= <<<TR
<tr>
    <td>{$salesmanItem['name']}</td>
    <td>{$this->disposeNumber($salesmanItem['this_month_income_number'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_month_income_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_month_cost_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_month_profit_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_year_recharge_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_year_income_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_year_cost_money'])}</td>
    <td>{$this->disposeMoney($salesmanItem['this_year_profit_money'])}</td>
</tr>
TR;
			});
		});
		
		return $html;
	}
	
	
	/**
	 * 生成excel
	 *
	 * @access protected
	 *
	 * @param $data array 数据
	 * @param $name string 收件人在后台系统中的用户名
	 *
	 * @return string 返回的是文件的访问地址
	 **/
	protected function createExcelFile($data, $username)
	{
		#初始化PHPExcel对象
		$phpExcelPath = app()->basePath() . '/vendor/PHPExcel-1.8/Classes';
		require_once $phpExcelPath . '/PHPExcel.php';
		require_once $phpExcelPath . '/PHPExcel/IOFactory.php';
		//require_once $phpExcelPath . '/PHPExcel/Style/Alignment.php';
		
		$objPHPExcel = new \PHPExcel();
		$objPHPExcel->getProperties()
					->setCreator("Maarten Balliauw")
					->setLastModifiedBy("Maarten Balliauw")
					->setTitle("Office 2007 XLSX Test Document")
					->setSubject("Office 2007 XLSX Test Document")
					->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.")
					->setKeywords("office 2007 openxml php")
					->setCategory("Test result file");
		
		#第一个sheet【商务维度的数据】
		$this->setSheet1($objPHPExcel->setActiveSheetIndex(0), $data['business']);
		
		#第二个sheet【产品维度】
		$objPHPExcel->createSheet();
		$this->setSheet2($objPHPExcel->setActiveSheetIndex(1), $data['product']);
		
		#第三个sheet【客户维度】
		$objPHPExcel->createSheet();
		$this->setSheet3($objPHPExcel->setActiveSheetIndex(2), $data['customer']);
		
		#第四个sheet【特殊费用】
		//$objPHPExcel->createSheet();
		//$this->setSheet4($objPHPExcel->setActiveSheetIndex(3), $data['special']);
		
		#保存excel文件
		$objWriter = new \PHPExcel_Writer_Excel5($objPHPExcel);
		
		$dirpath = app()->basePath() . '/public/profit_report/';
		if (!is_dir($dirpath)) {
			@mkdir($dirpath, 755);
		}
		$filename = 'month_' . $username . '_' . $this->month . '_' . time() . '.xlsx';
		$objWriter->save($dirpath . $filename);
		
		return config('params.base_domain') . '/profit_report/' . $filename;
	}
	
	/**
	 * 生成第四个sheet
	 *
	 * @access protected
	 *
	 * @param $sheet         \PHPExcel_Worksheet
	 * @param $specialData   array 特殊费用数据
	 *
	 * @return void
	 **/
	protected function setSheet4($sheet, $specialData)
	{
		$sheet->setTitle('特殊费用');
		##标题
		$sheet->setCellValue('A1', '客户ID')
			  ->setCellValue('B1', '客户名称')
			  ->setCellValue('C1', '费用类型')
			  ->setCellValue('D1', '计入月份')
			  ->setCellValue('E1', '名称')
			  ->setCellValue('F1', '金额')
			  ->setCellValue('G1', '备注（产品）');
		$sheet->getStyle('A1:M1')
			  ->getFont()
			  ->setBold(true);
		$sheet->getStyle('A1:M1')
			  ->getFont()
			  ->setSize(12);
		##客户
		$row = 2;
		array_walk($specialData, function ($item) use (&$row, $sheet) {
			$sheet->setCellValue('A' . $row, $item['customer_id'])
				  ->setCellValue('B' . $row, $item['customer_name'])
				  ->setCellValue('C' . $row, $item['type'])
				  ->setCellValue('D' . $row, $item['start_date'])
				  ->setCellValue('E' . $row, $item['name'])
				  ->setCellValue('F' . $row, $this->disposeMoney($item['money']))
				  ->setCellValue('G' . $row, $item['product_name']);
			$row++;
		});
		
		#设置全局样式
		$sheet->getStyle('A1:G' . $row)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('A1:G' . $row)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('A1:G' . $row)
			  ->getFont()
			  ->setName('宋体');
		$sheet->getColumnDimension('A')
			  ->setWidth(20);
		$sheet->getColumnDimension('B')
			  ->setWidth(24);
		$sheet->getColumnDimension('C')
			  ->setWidth(20);
		$sheet->getColumnDimension('D')
			  ->setWidth(20);
		$sheet->getColumnDimension('E')
			  ->setWidth(36);
		$sheet->getColumnDimension('F')
			  ->setWidth(18);
		$sheet->getColumnDimension('G')
			  ->setWidth(22);
		$sheet->getColumnDimension('H')
			  ->setWidth(22);
		for ($i = 1; $i < $row; $i++) {
			$sheet->getRowDimension($i)
				  ->setRowHeight(18);
		}
	}
	
	/**
	 * 生成第三个sheet
	 *
	 * @access protected
	 *
	 * @param $sheet         \PHPExcel_Worksheet
	 * @param $customerData  array 客户维度的统计数据
	 *
	 * @return void
	 **/
	protected function setSheet3($sheet, $customerData)
	{
		$sheet->setTitle('客户维度');
		##标题
		$sheet->setCellValue('A1', '客户ID')
			  ->setCellValue('B1', '客户名称')
			  ->setCellValue('C1', '收入计费用量')
			  ->setCellValue('D1', '当月累计权责收入')
			  ->setCellValue('E1', '当月累计权责成本')
			  ->setCellValue('F1', '当月累计权责毛利')
			  ->setCellValue('G1', '本年累计现金收入')
			  ->setCellValue('H1', '本年累计权责收入')
			  ->setCellValue('I1', '本年累计权责成本')
			  ->setCellValue('J1', '本年累计权责毛利');
		$sheet->getStyle('A1:J1')
			  ->getFont()
			  ->setBold(true);
		$sheet->getStyle('A1:J1')
			  ->getFont()
			  ->setSize(12);
		##合计
		$sheet->mergeCells('A2:B2')
			  ->getStyle('A2')
			  ->getFont()
			  ->setBold(true);
		$total = $customerData['total'];
		$sheet->setCellValue('A2', '合计')
			  ->setCellValue('C2', $this->disposeNumber($total['this_month_income_number']))
			  ->setCellValue('D2', $this->disposeMoney($total['this_month_income_money']))
			  ->setCellValue('E2', $this->disposeMoney($total['this_month_cost_money']))
			  ->setCellValue('F2', $this->disposeMoney($total['this_month_profit_money']))
			  ->setCellValue('G2', $this->disposeMoney($total['this_year_recharge_money']))
			  ->setCellValue('H2', $this->disposeMoney($total['this_year_income_money']))
			  ->setCellValue('I2', $this->disposeMoney($total['this_year_cost_money']))
			  ->setCellValue('J2', $this->disposeMoney($total['this_year_profit_money']));
		##客户
		$row = 3;
		array_walk($customerData['data'], function ($item) use (&$row, $sheet) {
			$sheet->setCellValue('A' . $row, $item['customer_id'])
				  ->setCellValue('B' . $row, $item['customer_name'])
				  ->setCellValue('C' . $row, $this->disposeNumber($item['this_month_income_number']))
				  ->setCellValue('D' . $row, $this->disposeMoney($item['this_month_income_money']))
				  ->setCellValue('E' . $row, $this->disposeMoney($item['this_month_cost_money']))
				  ->setCellValue('F' . $row, $this->disposeMoney($item['this_month_profit_money']))
				  ->setCellValue('G' . $row, $this->disposeMoney($item['this_year_recharge_money']))
				  ->setCellValue('H' . $row, $this->disposeMoney($item['this_year_income_money']))
				  ->setCellValue('I' . $row, $this->disposeMoney($item['this_year_cost_money']))
				  ->setCellValue('J' . $row, $this->disposeMoney($item['this_year_profit_money']));
			$row++;
		});
		
		#设置全局样式
		$sheet->getStyle('A1:J' . $row)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('A1:J' . $row)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('A1:J' . $row)
			  ->getFont()
			  ->setName('宋体');
		$sheet->getColumnDimension('A')
			  ->setWidth(20);
		$sheet->getColumnDimension('B')
			  ->setWidth(24);
		$sheet->getColumnDimension('C')
			  ->setWidth(18);
		$sheet->getColumnDimension('D')
			  ->setWidth(22);
		$sheet->getColumnDimension('E')
			  ->setWidth(22);
		$sheet->getColumnDimension('F')
			  ->setWidth(22);
		$sheet->getColumnDimension('G')
			  ->setWidth(22);
		$sheet->getColumnDimension('H')
			  ->setWidth(22);
		$sheet->getColumnDimension('I')
			  ->setWidth(22);
		$sheet->getColumnDimension('J')
			  ->setWidth(22);
		for ($i = 1; $i < $row; $i++) {
			$sheet->getRowDimension($i)
				  ->setRowHeight(18);
		}
	}
	
	/**
	 * 生成第二个sheet
	 *
	 * @access protected
	 *
	 * @param $sheet        \PHPExcel_Worksheet
	 * @param $productData  array 产品维度的统计数据
	 *
	 * @return void
	 **/
	protected function setSheet2($sheet, $productData)
	{
		$sheet->setTitle('产品维度');
		##标题
		$sheet->setCellValue('A1', '产品名称')
			  ->setCellValue('B1', '子产品名称')
			  ->setCellValue('C1', '收入计费用量')
			  ->setCellValue('D1', '当月累计权责收入')
			  ->setCellValue('E1', '当月累计权责成本')
			  ->setCellValue('F1', '当月累计权责毛利')
			  ->setCellValue('G1', '当年累计权责收入')
			  ->setCellValue('H1', '当年累计权责成本')
			  ->setCellValue('I1', '当年累计权责毛利');
		$sheet->getStyle('A1:I1')
			  ->getFont()
			  ->setBold(true);
		$sheet->getStyle('A1:I1')
			  ->getFont()
			  ->setSize(12);
		##合计
		$sheet->mergeCells('A2:B2')
			  ->getStyle('A2')
			  ->getFont()
			  ->setBold(true);
		$total = $productData['total'];
		$sheet->setCellValue('A2', '合计')
			  ->setCellValue('C2', $this->disposeNumber($total['this_month_income_number']))
			  ->setCellValue('D2', $this->disposeMoney($total['this_month_income_money']))
			  ->setCellValue('E2', $this->disposeMoney($total['this_month_cost_money']))
			  ->setCellValue('F2', $this->disposeMoney($total['this_month_profit_money']))
			  ->setCellValue('G2', $this->disposeMoney($total['this_year_income_money']))
			  ->setCellValue('H2', $this->disposeMoney($total['this_year_cost_money']))
			  ->setCellValue('I2', $this->disposeMoney($total['this_year_profit_money']));
		##产品
		$row = 3;
		array_walk($productData['data'], function ($item) use (&$row, $sheet) {
			if (array_key_exists('children', $item)) {
				$sheet->setCellValue('A' . $row, $item['name'])
					  ->setCellValue('B' . $row, '小计')
					  ->setCellValue('C' . $row, $this->disposeNumber($item['this_month_income_number']))
					  ->setCellValue('D' . $row, $this->disposeMoney($item['this_month_income_money']))
					  ->setCellValue('E' . $row, $this->disposeMoney($item['this_month_cost_money']))
					  ->setCellValue('F' . $row, $this->disposeMoney($item['this_month_profit_money']))
					  ->setCellValue('G' . $row, $this->disposeMoney($item['this_year_income_money']))
					  ->setCellValue('H' . $row, $this->disposeMoney($item['this_year_cost_money']))
					  ->setCellValue('I' . $row, $this->disposeMoney($item['this_year_profit_money']))
					  ->getStyle('B' . $row)
					  ->getFont()
					  ->setBold(true);
				$row++;
				//遍历子产品
				$children = $item['children'];
				$startRow = $row - 1;
				$sheet->mergeCells('A' . $startRow . ':A' . ($startRow + count($children)));
				array_walk($children, function ($childrenItem) use (&$row, $sheet) {
					$sheet->setCellValue('B' . $row, $childrenItem['name'])
						  ->setCellValue('C' . $row, $this->disposeNumber($childrenItem['this_month_income_number']))
						  ->setCellValue('D' . $row, $this->disposeMoney($childrenItem['this_month_income_money']))
						  ->setCellValue('E' . $row, $this->disposeMoney($childrenItem['this_month_cost_money']))
						  ->setCellValue('F' . $row, $this->disposeMoney($childrenItem['this_month_profit_money']))
						  ->setCellValue('G' . $row, $this->disposeMoney($childrenItem['this_year_income_money']))
						  ->setCellValue('H' . $row, $this->disposeMoney($childrenItem['this_year_cost_money']))
						  ->setCellValue('I' . $row, $this->disposeMoney($childrenItem['this_year_profit_money']));
					$row++;
				});
			} else {
				$sheet->setCellValue('A' . $row, $item['name'])
					  ->setCellValue('B' . $row, '--')
					  ->setCellValue('C' . $row, $this->disposeNumber($item['this_month_income_number']))
					  ->setCellValue('D' . $row, $this->disposeMoney($item['this_month_income_money']))
					  ->setCellValue('E' . $row, $this->disposeMoney($item['this_month_cost_money']))
					  ->setCellValue('F' . $row, $this->disposeMoney($item['this_month_profit_money']))
					  ->setCellValue('G' . $row, $this->disposeMoney($item['this_year_income_money']))
					  ->setCellValue('H' . $row, $this->disposeMoney($item['this_year_cost_money']))
					  ->setCellValue('I' . $row, $this->disposeMoney($item['this_year_profit_money']));
				$row++;
			}
		});
		
		#设置全局样式
		$sheet->getStyle('A1:I' . $row)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('A1:I' . $row)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('A1:I' . $row)
			  ->getFont()
			  ->setName('宋体');
		$sheet->getColumnDimension('A')
			  ->setWidth(18);
		$sheet->getColumnDimension('B')
			  ->setWidth(34);
		$sheet->getColumnDimension('C')
			  ->setWidth(18);
		$sheet->getColumnDimension('D')
			  ->setWidth(22);
		$sheet->getColumnDimension('E')
			  ->setWidth(22);
		$sheet->getColumnDimension('F')
			  ->setWidth(22);
		$sheet->getColumnDimension('G')
			  ->setWidth(22);
		$sheet->getColumnDimension('H')
			  ->setWidth(22);
		$sheet->getColumnDimension('I')
			  ->setWidth(22);
		for ($i = 1; $i < $row; $i++) {
			$sheet->getRowDimension($i)
				  ->setRowHeight(18);
		}
	}
	
	/**
	 * 生成第一个sheet
	 *
	 * @access protected
	 *
	 * @param $sheet        \PHPExcel_Worksheet
	 * @param $businessData array 商务维度的统计数据
	 *
	 * @return void
	 **/
	protected function setSheet1($sheet, $businessData)
	{
		$sheet->setTitle('商务区域维度');
		##标题
		$sheet->setCellValue('A1', '区域')
			  ->setCellValue('B1', '商务')
			  ->setCellValue('C1', '收入计费用量')
			  ->setCellValue('D1', '当月累计权责收入')
			  ->setCellValue('E1', '当月累计权责成本')
			  ->setCellValue('F1', '当月累计权责毛利')
			  ->setCellValue('G1', '本年累计现金收入')
			  ->setCellValue('H1', '本年累计权责收入')
			  ->setCellValue('I1', '本年累计权责成本')
			  ->setCellValue('J1', '本年累计权责毛利');
		
		$sheet->getStyle('A1:J1')
			  ->getFont()
			  ->setBold(true);
		$sheet->getStyle('A1:J1')
			  ->getFont()
			  ->setSize(12);
		##合计
		$sheet->mergeCells('A2:B2')
			  ->getStyle('A2')
			  ->getFont()
			  ->setBold(true);
		$total = $businessData['total'];
		$sheet->setCellValue('A2', '合计')
			  ->setCellValue('C2', $this->disposeNumber($total['this_month_income_number']))
			  ->setCellValue('D2', $this->disposeMoney($total['this_month_income_money']))
			  ->setCellValue('E2', $this->disposeMoney($total['this_month_cost_money']))
			  ->setCellValue('F2', $this->disposeMoney($total['this_month_profit_money']))
			  ->setCellValue('G2', $this->disposeMoney($total['this_year_recharge_money']))
			  ->setCellValue('H2', $this->disposeMoney($total['this_year_income_money']))
			  ->setCellValue('I2', $this->disposeMoney($total['this_year_cost_money']))
			  ->setCellValue('J2', $this->disposeMoney($total['this_year_profit_money']));
		##部门
		$row = 3;
		array_walk($businessData['data'], function ($businessItem) use (&$row, $sheet) {
			$businessTotal = $businessItem['total'];
			$businessData  = $businessItem['data'];
			#小计
			$sheet->setCellValue('A' . $row, $businessItem['name'])
				  ->setCellValue('B' . $row, '小计')
				  ->setCellValue('C' . $row, $this->disposeNumber($businessTotal['this_month_income_number']))
				  ->setCellValue('D' . $row, $this->disposeMoney($businessTotal['this_month_income_money']))
				  ->setCellValue('E' . $row, $this->disposeMoney($businessTotal['this_month_cost_money']))
				  ->setCellValue('F' . $row, $this->disposeMoney($businessTotal['this_month_profit_money']))
				  ->setCellValue('G' . $row, $this->disposeMoney($businessTotal['this_year_recharge_money']))
				  ->setCellValue('H' . $row, $this->disposeMoney($businessTotal['this_year_income_money']))
				  ->setCellValue('I' . $row, $this->disposeMoney($businessTotal['this_year_cost_money']))
				  ->setCellValue('J' . $row, $this->disposeMoney($businessTotal['this_year_profit_money']))
				  ->mergeCells('A' . $row . ':A' . ($row + count($businessData)))
				  ->getStyle('B' . $row)
				  ->getFont()
				  ->setBold(true);
			$row++;
			#每个商务
			array_walk($businessData, function ($salesmanItem) use (&$row, $sheet) {
				$sheet->setCellValue('B' . $row, $salesmanItem['name'])
					  ->setCellValue('C' . $row, $this->disposeNumber($salesmanItem['this_month_income_number']))
					  ->setCellValue('D' . $row, $this->disposeMoney($salesmanItem['this_month_income_money']))
					  ->setCellValue('E' . $row, $this->disposeMoney($salesmanItem['this_month_cost_money']))
					  ->setCellValue('F' . $row, $this->disposeMoney($salesmanItem['this_month_profit_money']))
					  ->setCellValue('G' . $row, $this->disposeMoney($salesmanItem['this_year_recharge_money']))
					  ->setCellValue('H' . $row, $this->disposeMoney($salesmanItem['this_year_income_money']))
					  ->setCellValue('I' . $row, $this->disposeMoney($salesmanItem['this_year_cost_money']))
					  ->setCellValue('J' . $row, $this->disposeMoney($salesmanItem['this_year_profit_money']));
				$row++;
			});
		});
		
		#设置全局样式
		$sheet->getStyle('A1:J' . $row)
			  ->getAlignment()
			  ->setHorizontal(\PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('A1:J' . $row)
			  ->getAlignment()
			  ->setVertical(\PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('A1:J' . $row)
			  ->getFont()
			  ->setName('宋体');
		$sheet->getColumnDimension('A')
			  ->setWidth(18);
		$sheet->getColumnDimension('B')
			  ->setWidth(16);
		$sheet->getColumnDimension('C')
			  ->setWidth(18);
		$sheet->getColumnDimension('D')
			  ->setWidth(22);
		$sheet->getColumnDimension('E')
			  ->setWidth(22);
		$sheet->getColumnDimension('F')
			  ->setWidth(22);
		$sheet->getColumnDimension('G')
			  ->setWidth(22);
		$sheet->getColumnDimension('H')
			  ->setWidth(22);
		$sheet->getColumnDimension('I')
			  ->setWidth(22);
		$sheet->getColumnDimension('J')
			  ->setWidth(22);
		for ($i = 1; $i < $row; $i++) {
			$sheet->getRowDimension($i)
				  ->setRowHeight(18);
		}
	}
	
	/**
	 * 过滤负值
	 *
	 * @access protected
	 *
	 * @param $value integer|float 值
	 *
	 * @return integer|float
	 **/
	protected function filterMinus($value)
	{
		return number_format($value, 2);
		//return $value > 0 ? $value : 0;
	}
	
	/**
	 * 金额处理
	 *
	 * @access protected
	 *
	 * @param $money float 金额
	 *
	 * @return float
	 **/
	protected function disposeMoney($money)
	{
		return number_format(round($money, 0));
	}
	
	/**
	 * 调用量处理
	 *
	 * @access protected
	 *
	 * @param $number integer 调用量
	 *
	 * @return integer
	 **/
	protected function disposeNumber($number)
	{
		return number_format($number, 0);
	}
	
	/**
	 * 整理特殊费用数据
	 *
	 * @access protected
	 *
	 * @param $customerIds array 允许查询的客户ID
	 *
	 * @return array
	 **/
	protected function getSpecialData($customerIds)
	{
		return array_filter($this->specialData, function ($item) use ($customerIds) {
			return in_array($item['customer_id'], $customerIds);
		});
	}
	
	/**
	 * 计算客户维度的数据
	 *
	 * @access protected
	 *
	 * @param $customerIds array 允许查询的客户ID
	 *
	 * @return array
	 **/
	protected function getCustomerData($customerIds)
	{
		//基础数据
		$base = [
			'customer_id'              => '',
			'customer_name'            => '',
			'this_month_cost_money'    => 0,
			'this_month_income_money'  => 0,
			'this_year_cost_money'     => 0,
			'this_year_income_money'   => 0,
			'this_month_cost_number'   => 0,
			'this_month_income_number' => 0,
			'this_year_cost_number'    => 0,
			'this_year_income_number'  => 0,
			'this_month_profit_money'  => 0,
			'this_year_profit_money'   => 0,
			'this_year_recharge_money' => 0,
			'income_details'           => [],
			'cost_details'             => [],
		];
		
		$result          = [];
		$customerMapping = $this->getCustomerMapping();
		
		//遍历成本数据、并合计
		array_walk($this->costData, function ($costItem) use (&$result, $base, $customerMapping, $customerIds) {
			$customer_id = $costItem['customer_id'];
			if (!in_array($customer_id, $customerIds)) {
				return;
			}
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id]                  = $base;
				$result[$customer_id]['customer_id']   = $customer_id;
				$result[$customer_id]['customer_name'] = $customerMapping[$customer_id];
			}
			$result[$customer_id]['this_month_cost_money']  = bcadd($result[$customer_id]['this_month_cost_money'], $costItem['month_money'], 6);
			$result[$customer_id]['this_year_cost_money']   = bcadd($result[$customer_id]['this_year_cost_money'], $costItem['year_money'], 6);
			$result[$customer_id]['this_month_cost_number'] = bcadd($result[$customer_id]['this_month_cost_number'], $costItem['month_number']);
			$result[$customer_id]['this_year_cost_number']  = bcadd($result[$customer_id]['this_year_cost_number'], $costItem['year_number']);
			$result[$customer_id]['cost_details'][]         = $costItem;
		});
		
		//遍历收入并合计
		array_walk($this->incomeData, function ($incomeItem) use (&$result, $base, $customerMapping, $customerIds) {
			$customer_id = $incomeItem['customer_id'];
			if (!in_array($customer_id, $customerIds)) {
				return;
			}
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id]                  = $base;
				$result[$customer_id]['customer_id']   = $customer_id;
				$result[$customer_id]['customer_name'] = $customerMapping[$customer_id];
			}
			$result[$customer_id]['this_month_income_money']  = bcadd($result[$customer_id]['this_month_income_money'], $incomeItem['month_money'], 6);
			$result[$customer_id]['this_year_income_money']   = bcadd($result[$customer_id]['this_year_income_money'], $incomeItem['year_money'], 6);
			$result[$customer_id]['this_month_income_number'] = bcadd($result[$customer_id]['this_month_income_number'], $incomeItem['month_number']);
			$result[$customer_id]['this_year_income_number']  = bcadd($result[$customer_id]['this_year_income_number'], $incomeItem['year_number']);
			$result[$customer_id]['income_details'][]         = $incomeItem;
		});
		
		//遍历充值
		array_walk($this->rechargeData, function ($money, $customer_id) use (&$result, $base, $customerMapping, $customerIds) {
			if (!in_array($customer_id, $customerIds)) {
				return;
			}
			if (!array_key_exists($customer_id, $result)) {
				$result[$customer_id]                  = $base;
				$result[$customer_id]['customer_id']   = $customer_id;
				$result[$customer_id]['customer_name'] = $customerMapping[$customer_id];
			}
			$result[$customer_id]['this_year_recharge_money'] = bcadd($result[$customer_id]['this_year_recharge_money'], $money, 6);
		});
		
		//计算毛利润并排序
		$data = array_map(function ($item) {
			return $this->computeProfit($item);
		}, $result);
		array_multisort(array_column($data, $this->sortBy), SORT_DESC, $data);
		
		//计算合计数据
		$this_month_cost_money    = array_sum(array_column($data, 'this_month_cost_money'));
		$this_month_income_money  = array_sum(array_column($data, 'this_month_income_money'));
		$this_year_cost_money     = array_sum(array_column($data, 'this_year_cost_money'));
		$this_year_income_money   = array_sum(array_column($data, 'this_year_income_money'));
		$this_month_cost_number   = array_sum(array_column($data, 'this_month_cost_number'));
		$this_month_income_number = array_sum(array_column($data, 'this_month_income_number'));
		$this_year_cost_number    = array_sum(array_column($data, 'this_year_cost_number'));
		$this_year_income_number  = array_sum(array_column($data, 'this_year_income_number'));
		$this_year_recharge_money = array_sum(array_column($data, 'this_year_recharge_money'));
		$total                    = $this->computeProfit(compact('this_month_cost_money', 'this_month_income_money', 'this_year_cost_money', 'this_year_income_money', 'this_month_cost_number', 'this_month_income_number', 'this_year_cost_number', 'this_year_income_number', 'this_year_recharge_money'));
		
		//过滤掉无意义的数据
		$data = array_filter($data, function ($item) {
			return !$this->nonUseData($item);
		});
		
		return compact('total', 'data');
	}
	
	/**
	 * 获取客户ID与之对应的客户名称
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getCustomerMapping()
	{
		if (is_null($this->cacheCustomerMapping)) {
			$this->cacheCustomerMapping = Customer::pluck('name', 'customer_id')
												  ->toArray();
		}
		
		return $this->cacheCustomerMapping;
	}
	
	/**
	 * 计算产品维度的数据
	 *
	 * @access protected
	 *
	 * @param $customerIds array 允许查询的客户ID
	 *
	 * @return array
	 **/
	protected function getProductData($customerIds)
	{
		//生成一个存储数据的容器
		$container = $this->initBaseProductData();
		//整理产品维度的收入
		$this->disposeProductIncome($customerIds, $container);
		//整理产品维度的成本
		$this->disposeProductCost($customerIds, $container);
		
		//过滤数据
		//计算数据
		//排序
		$container = array_map(function ($item) {
			if (array_key_exists('children', $item)) {
				//处理子节点数据
				#计算数据
				$item['children'] = array_map(function ($childrenItem) {
					return $this->computeProfit($childrenItem);
				}, $item['children']);
				#过滤数据
				$item['children'] = array_filter($item['children'], function ($chlidrenItem) {
					return !$this->nonUseData($chlidrenItem);
				});
				#排序
				array_multisort(array_column($item['children'], $this->sortBy), SORT_DESC, $item['children']);
			}
			
			return $this->computeProfit($item);
		}, $container);
		#排序
		array_multisort(array_column($container, $this->sortBy), SORT_DESC, $container);
		
		//计算合计数据
		$data                     = $container;
		$this_month_cost_money    = array_sum(array_column($data, 'this_month_cost_money'));
		$this_month_income_money  = array_sum(array_column($data, 'this_month_income_money'));
		$this_year_cost_money     = array_sum(array_column($data, 'this_year_cost_money'));
		$this_year_income_money   = array_sum(array_column($data, 'this_year_income_money'));
		$this_month_cost_number   = array_sum(array_column($data, 'this_month_cost_number'));
		$this_month_income_number = array_sum(array_column($data, 'this_month_income_number'));
		$this_year_cost_number    = array_sum(array_column($data, 'this_year_cost_number'));
		$this_year_income_number  = array_sum(array_column($data, 'this_year_income_number'));
		$total                    = $this->computeProfit(compact('this_month_cost_money', 'this_month_income_money', 'this_year_cost_money', 'this_year_income_money', 'this_month_cost_number', 'this_month_income_number', 'this_year_cost_number', 'this_year_income_number'));
		
		return compact('total', 'data');
	}
	
	/**
	 * 初始化产品数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function initBaseProductData()
	{
		$base = [
			'name'                     => '',
			'this_month_cost_money'    => 0,
			'this_month_income_money'  => 0,
			'this_year_cost_money'     => 0,
			'this_year_income_money'   => 0,
			'this_month_cost_number'   => 0,
			'this_month_income_number' => 0,
			'this_year_cost_number'    => 0,
			'this_year_income_number'  => 0,
			'this_month_profit_money'  => 0,
			'this_year_profit_money'   => 0,
			'this_year_recharge_money' => 0,
		];
		
		//邦信分快捷版
		$data['bxf_short']                                         = $base;
		$data['bxf_short']['name']                                 = '邦信分';
		$data['bxf_short']['label']                                = 'bxf_short';
		$data['bxf_short']['children']                             = [];
		$data['bxf_short']['children']['bxf_short_score']          = $base;
		$data['bxf_short']['children']['bxf_short_score']['name']  = '通信评分';
		$data['bxf_short']['children']['bxf_short_score']['label'] = 'bxf_short_score';
		$data['bxf_short']['children']['bxf_short_stat']           = $base;
		$data['bxf_short']['children']['bxf_short_stat']['name']   = '通信字段';
		$data['bxf_short']['children']['bxf_short_stat']['label']  = 'bxf_short_stat';
		
		//邦信分详单版
		$data['bxf_detail_list']          = $base;
		$data['bxf_detail_list']['name']  = '邦信分详单版';
		$data['bxf_detail_list']['label'] = 'bxf_detail_list';
		
		//邦信分私有云
		$data['bxf_price_cloud']          = $base;
		$data['bxf_price_cloud']['name']  = '邦信分私有云';
		$data['bxf_price_cloud']['label'] = 'bxf_price_cloud';
		
		//邦秒配
		$data['bmp']          = $base;
		$data['bmp']['name']  = '邦秒配';
		$data['bmp']['label'] = 'bmp';
		
		//邦企查
		$data['bqc']          = $base;
		$data['bqc']['name']  = '邦企查';
		$data['bqc']['label'] = 'bqc';
		
		//邦秒验
		$data['bmy']             = $base;
		$data['bmy']['name']     = '邦秒验';
		$data['bmy']['label']    = 'bmy';
		$children                = Product::select([
			'product_id',
			'product_name',
		])
										  ->where('father_id', '=', 200)
										  ->get()
										  ->map(function ($item) use ($base) {
											  $base['label'] = $item->product_id;
											  $base['name']  = $item->product_name;
			
											  return $base;
										  })
										  ->toArray();
		$children                = array_column($children, null, 'label');
		$children[801]           = $base;
		$children[801]['name']   = '号码状态查询';
		$children[801]['label']  = '801';
		$data['bmy']['children'] = $children;
		
		//金盾
		$data['gold_shield']             = $base;
		$data['gold_shield']['name']     = '金盾';
		$children1                       = Product::select([
			'product_id',
			'product_name',
		])
												  ->where('father_id', '=', 615)
												  ->get()
												  ->map(function ($item) use ($base) {
													  $base['label'] = $item->product_id;
													  $base['name']  = $item->product_name;
			
													  return $base;
												  })
												  ->toArray();
		$children                        = array_column($children1, null, 'label');
		$children2                       = Product::select([
			'product_id',
			'product_name',
		])
												  ->whereIn('product_id', [
													  603,
													  612,
													  613,
													  614,
													  616,
													  664,
												  ])
												  ->get()
												  ->map(function ($item) use ($base) {
													  $base['label'] = $item->product_id;
													  $base['name']  = $item->product_name;
			
													  return $base;
												  })
												  ->toArray();
		$children                        = $children + array_column($children2, null, 'label');
		$data['gold_shield']['children'] = $children;
		
		return $data;
	}
	
	/**
	 * 获取、计算、整理产品维度的成本数据
	 *
	 * @access protected
	 *
	 * @param $customerIds array 客户ID
	 * @param $container   array 产品整理后的数据容器
	 *
	 * @return void
	 **/
	protected function disposeProductCost($customerIds, &$container)
	{
		array_walk($this->costData, function ($item) use ($customerIds, &$container) {
			if (!in_array($item['customer_id'], $customerIds)) {
				return;
			}
			$product_id = $item['product_id'];
			$label      = $this->decideCategroyForCost($product_id);
			
			switch (count($label)) {
				case 1:
					list($first) = $label;
					$container[$first]['this_month_cost_money']  += $item['month_money'];
					$container[$first]['this_year_cost_money']   += $item['year_money'];
					$container[$first]['this_month_cost_number'] += $item['month_number'];
					$container[$first]['this_year_cost_number']  += $item['year_number'];
					break;
				case 3:
					list($first, $second, $third) = $label;
					$container[$first][$second][$third]['this_month_cost_money']  += $item['month_money'];
					$container[$first][$second][$third]['this_year_cost_money']   += $item['year_money'];
					$container[$first][$second][$third]['this_month_cost_number'] += $item['month_number'];
					$container[$first][$second][$third]['this_year_cost_number']  += $item['year_number'];
					
					//父节点
					$container[$first]['this_month_cost_money']  += $item['month_money'];
					$container[$first]['this_year_cost_money']   += $item['year_money'];
					$container[$first]['this_month_cost_number'] += $item['month_number'];
					$container[$first]['this_year_cost_number']  += $item['year_number'];
					break;
			}
		});
		
		//邦信分快捷版成本拆分
		array_walk($this->bxfShortCostData, function ($item) use ($customerIds, &$container) {
			if (!in_array($item['customer_id'], $customerIds)) {
				return;
			}
			$product_id = $item['product_id'];
			$label      = $product_id == 251 ? 'bxf_short_score' : 'bxf_short_stat';
			//子节点的
			$container['bxf_short']['children'][$label]['this_month_cost_money']  += $item['month_money'];
			$container['bxf_short']['children'][$label]['this_year_cost_money']   += $item['year_money'];
			$container['bxf_short']['children'][$label]['this_month_cost_number'] += $item['month_number'];
			$container['bxf_short']['children'][$label]['this_year_cost_number']  += $item['year_number'];
			
			//父节点的
			$container['bxf_short']['this_month_cost_money']  += $item['month_money'];
			$container['bxf_short']['this_year_cost_money']   += $item['year_money'];
			$container['bxf_short']['this_month_cost_number'] += $item['month_number'];
			$container['bxf_short']['this_year_cost_number']  += $item['year_number'];
		});
	}
	
	/**
	 * 获取、计算、整理产品维度的收入数据
	 *
	 * @access protected
	 *
	 * @param $customerIds array 客户ID
	 * @param $container   array 产品整理后的数据容器
	 *
	 * @return void
	 **/
	protected function disposeProductIncome($customerIds, &$container)
	{
		array_walk($this->incomeData, function ($item) use (&$container, $customerIds) {
			$customer_id = $item['customer_id'];
			$account_id  = $item['account_id'];
			$product_id  = $item['product_id'];
			if (!in_array($customer_id, $customerIds)) {
				return;
			}
			$label = $this->decideCategroyForIncome($account_id, $product_id);
			if (is_null($label)) {
				return;
			}
			switch (count($label)) {
				case 3:
					//存在子节点
					list($first, $second, $third) = $label;
					$container[$first][$second][$third]['this_month_income_money']  += $item['month_money'];
					$container[$first][$second][$third]['this_year_income_money']   += $item['year_money'];
					$container[$first][$second][$third]['this_month_income_number'] += $item['month_number'];
					$container[$first][$second][$third]['this_year_income_number']  += $item['year_number'];
					
					//父节点
					$container[$first]['this_month_income_money']  += $item['month_money'];
					$container[$first]['this_year_income_money']   += $item['year_money'];
					$container[$first]['this_month_income_number'] += $item['month_number'];
					$container[$first]['this_year_income_number']  += $item['year_number'];
					break;
				case 1:
					//没有子节点
					list($first) = $label;
					$container[$first]['this_month_income_money']  += $item['month_money'];
					$container[$first]['this_year_income_money']   += $item['year_money'];
					$container[$first]['this_month_income_number'] += $item['month_number'];
					$container[$first]['this_year_income_number']  += $item['year_number'];
					break;
			}
		});
	}
	
	/**
	 * 获取非聚合的收入数据
	 *
	 * @access protected
	 *
	 * @param $customerIds array 客户ID
	 *
	 * @return Collection
	 **/
	protected function getNotAggregateIncome($customerIds)
	{
		return MongoBillDay::select('account_id', 'customer_id', 'product_id', 'money', 'date', 'section_invoked_number')
						   ->whereIn('customer_id', $customerIds)
						   ->where('date', '>=', $this->firstDate)
						   ->where('date', '<=', $this->date)
						   ->get()
						   ->map(function ($item) {
							   $account_id  = $item['account_id'];
							   $customer_id = $item['customer_id'];
							   $product_id  = $item['product_id'];
							   $date        = $item['date'];
							   $money       = $item['money'];
							   $fee_number  = $item['section_invoked_number'];
			
							   //获取计费配置
							   $fee_config = BillConfig::where('account_id', $account_id)
													   ->where('product_id', $product_id)
													   ->where('start_date', '<=', date('Y-m-d', strtotime($this->date)))
													   ->orderBy('start_date', 'DESC')
													   ->first();
							   if (is_null($fee_config)) {
								   $fee_config = [
									   'product_id'  => $product_id,
									   'account_id'  => $account_id,
									   'customer_id' => $customer_id,
								   ];
							   }
			
							   return compact('account_id', 'customer_id', 'product_id', 'date', 'money', 'fee_number', 'fee_config');
						   });
	}
	
	/**
	 * 确认当前的收入属于那个产品(成本的规则)
	 *
	 * @access protected
	 *
	 * @param $product_id integer 产品ID
	 *
	 * @return array
	 **/
	protected function decideCategroyForCost($product_id)
	{
		if ($product_id == 210) {
			return null;
		}
		if ($product_id == 401) {
			return ['bqc'];
		}
		if (in_array($product_id, get200ChildrenProductIds())) {
			return [
				'bmy',
				'children',
				$product_id,
			];
		}
		if (801 == $product_id) {
			return ['bmy', 'children', $product_id];
		}
		throw new \Exception("收入账单中存在不支持的产品ID：{$product_id}");
	}
	
	/**
	 * 确认当前的收入属于那个产品(收入的规则)
	 *
	 * @access protected
	 *
	 * @param $account_id string 账号ID
	 * @param $product_id string 产品ID
	 *
	 * @return array
	 **/
	protected function decideCategroyForIncome($account_id, $product_id)
	{
		//邦信分快捷版--评分类字段
		if (in_array($product_id, getBxfShortScoreProductIds())) {
			return [
				'bxf_short',
				'children',
				'bxf_short_score',
			];
		}
		
		//邦信分快捷版--普通类字段
		if (in_array($product_id, getBxfStatProductIds())) {
			return [
				'bxf_short',
				'children',
				'bxf_short_stat',
			];
		}
		
		if ($product_id == 210) {
			return [
				'bxf_short',
				'children',
				'bxf_short_stat',
			];
		}
		
		//邦信分私有云
		if ($product_id == 501) {
			return ['bxf_price_cloud'];
		}
		
		//邦企查
		if ($product_id == 401) {
			return ['bqc'];
		}
		
		//邦秒验
		if (in_array($product_id, get200ChildrenProductIds())) {
			return [
				'bmy',
				'children',
				$product_id,
			];
		}
		
		if ($product_id == 801) {
			return [
				'bmy',
				'children',
				801,
			];
		}
		
		//金盾
		if (in_array($product_id, get615ChildrenProductIds()) || in_array($product_id, [
				603,
				612,
				613,
				614,
				616,
				664,
			])
		) {
			return [
				'gold_shield',
				'children',
				$product_id,
			];
		}
		
		if ($product_id == 615) {
			return [
				'gold_shield',
				'children',
				661,
			];
		}
		
		//邦信分详单版：包含邦信分详单版V1、邦信分详单版V2、
		if (in_array($product_id, [
			101,
			105,
		])) {
			return ['bxf_detail_list'];
		}
		
		//邦秒配详单版的收入根据计费依据确定，计费依据为“号码量”是邦秒配详单版，否则计为邦信分详单版
		if ($product_id == 104) {
			$isBmp = BillConfig::where('account_id', $account_id)
							   ->where('product_id', $product_id)
							   ->where('fee_basis', 1)
							   ->where('is_reset', 0)
							   ->where('is_delete', 0)
							   ->count();
			if ($isBmp > 0) {
				return ['bxf_detail_list'];
			} else {
				return ['bmp'];
			}
		}
		if (in_array($product_id, [
			601,
			604,
		])) {
			return ['bmp'];
		}
		
		return null;
	}
	
	/**
	 * 获取某个用户能查看到的客户ID
	 *
	 * @access protected
	 *
	 * @param $username string 用户名
	 *
	 * @return array
	 **/
	protected function getAllowReadCustomerIds($username)
	{
		$url   = config('params.api.getAllowCustomerIds');
		$timer = 0;
		while ($timer < 3) {
			$data = $this->post($url, compact('username'));
			if ($data['status'] == 'success') {
				return $data['data'];
			} else {
				$timer++;
				continue;
			}
		}
		
		throw new \Exception("用户权限接口访问失败");
	}
	
	/**
	 * 获取需要发送利润日报的用户数据
	 *
	 * @access protected
	 *
	 * @return Collection
	 **/
	protected function getUsers()
	{
		$userCount = 0;
		$users     = SystemUser::where('profit_auth', 1)
							   ->get()
							   ->map(function ($item) use (&$userCount) {
								   //获取该用户的所属部门
								   $dept_id = $item->dept_id;
								   if (empty($dept_id)) {
									   $item->deptIds = [];
								   } else {
									   $item->deptIds = array_values(array_unique(array_merge(DeptGrade::where('grade_dept_id', $dept_id)
																									   ->pluck('grade_grand_father_id')
																									   ->toArray(), DeptGrade::where('grade_dept_id', $dept_id)
																															 ->pluck('grade_father_id')
																															 ->toArray())));
								   }
								   $userCount++;
			
								   return $item;
							   });
		
		return $users;
	}
	
	/**
	 * 计算销售区域维度的数据
	 *
	 * @access protected
	 *
	 * @param $email    string 邮箱地址
	 * @param $dataAuth string 数据权限（0全部数据  1本人数据  2本部门数据  3下级部门数据）
	 * @param $deptIds  array 所属的部门（因为是无限分级，所以包含了所有的部门）
	 *
	 * @return array
	 **/
	protected function getBusinessData($email, $dataAuth, $deptIds)
	{
		//获取该用户所能看见的商务部门及成员
		$businessDepartment = $this->getBusinessDepartment($email, $dataAuth, $deptIds);
		if (empty($businessDepartment)) {
			return [];
		}
		
		//获取该用户所具有的权限的客户所关联的商务用户名
		$salesmanUsernames = array_map(function ($item) {
			return str_replace('__', ' . ', $item);
		}, array_keys(array_merge_recursive(...array_column($businessDepartment, 'member'))));
		
		//获取所具有的权限的客户ID(通过商务的用户名进行关联查找),然后整合到businessDepartment中
		Customer::select('salesman', 'customer_id')
				->whereIn('salesman', $salesmanUsernames)
				->where('is_delete', 0)
				->each(function ($item) use (&$businessDepartment) {
					$salesman    = str_replace(' . ', '__', $item->salesman);
					$customer_id = $item->customer_id;
					foreach ($businessDepartment as &$businessDepartmentItem) {
						if (array_key_exists($salesman, $businessDepartmentItem['member'])) {
							$businessDepartmentItem['member'][$salesman]['customer_id'][] = $customer_id;
							break;
						}
					}
				});
		
		//遍历每个商务部门
		$data = array_map(function ($item) {
			$name = $item['dept_name'];
			//遍历每个商务
			$data = array_map(function ($memberItem) {
				$name        = $memberItem['name'];
				$customer_id = $memberItem['customer_id'];
				if (!empty($customer_id)) {
					//汇总成本数据
					list($this_month_cost_money, $this_year_cost_money, $this_month_cost_number, $this_year_cost_number) = $this->getCostByCustomerIds($customer_id);
					//汇总收入数据
					list($this_month_income_money, $this_year_income_money, $this_month_income_number, $this_year_income_number) = $this->getIncomeByCustomerIds($customer_id);
					
					//年充值金额
					$this_year_recharge_money = array_sum(array_intersect_key($this->rechargeData, array_flip($customer_id)));
				} else {
					$this_month_cost_money = $this_year_cost_money = $this_month_cost_number = $this_year_cost_number = $this_month_income_money = $this_year_income_money = $this_month_income_number = $this_year_income_number = $this_year_recharge_money = 0;
				}
				
				
				//计算利润
				$salesmanItem = $this->computeProfit(compact('name', 'this_month_cost_money', 'this_month_income_money', 'this_year_cost_money', 'this_year_income_money', 'this_month_cost_number', 'this_month_income_number', 'this_year_cost_number', 'this_year_income_number', 'this_year_recharge_money', 'customer_id', 'cost_details', 'income_details'));
				
				//暂时不需要记录
				//$this->salesmanLog($salesmanItem);
				return $salesmanItem;
			}, $item['member']);
			//计算部门的汇总数据(小计)
			$this_month_cost_money    = array_sum(array_column($data, 'this_month_cost_money'));
			$this_month_income_money  = array_sum(array_column($data, 'this_month_income_money'));
			$this_year_cost_money     = array_sum(array_column($data, 'this_year_cost_money'));
			$this_year_income_money   = array_sum(array_column($data, 'this_year_income_money'));
			$this_month_cost_number   = array_sum(array_column($data, 'this_month_cost_number'));
			$this_month_income_number = array_sum(array_column($data, 'this_month_income_number'));
			$this_year_cost_number    = array_sum(array_column($data, 'this_year_cost_number'));
			$this_year_income_number  = array_sum(array_column($data, 'this_year_income_number'));
			$this_year_recharge_money = array_sum(array_column($data, 'this_year_recharge_money'));
			$total                    = $this->computeProfit(compact('this_month_cost_money', 'this_month_income_money', 'this_year_cost_money', 'this_year_income_money', 'this_month_cost_number', 'this_month_income_number', 'this_year_cost_number', 'this_year_income_number', 'this_year_recharge_money'));
			
			//对每个商务进行排序
			$data = array_values($data);
			array_multisort(array_column($data, $this->sortBy), SORT_DESC, $data);
			
			return compact('name', 'total', 'data');
		}, $businessDepartment);
		
		//对每个部门进行排序
		$data = array_values($data);
		array_multisort(array_column(array_column($data, 'total'), $this->sortBy), SORT_DESC, $data);
		
		//汇总数据
		$everyBusinessTotal       = array_column($data, 'total');
		$this_month_cost_money    = array_sum(array_column($everyBusinessTotal, 'this_month_cost_money'));
		$this_month_income_money  = array_sum(array_column($everyBusinessTotal, 'this_month_income_money'));
		$this_year_cost_money     = array_sum(array_column($everyBusinessTotal, 'this_year_cost_money'));
		$this_year_income_money   = array_sum(array_column($everyBusinessTotal, 'this_year_income_money'));
		$this_month_cost_number   = array_sum(array_column($everyBusinessTotal, 'this_month_cost_number'));
		$this_month_income_number = array_sum(array_column($everyBusinessTotal, 'this_month_income_number'));
		$this_year_cost_number    = array_sum(array_column($everyBusinessTotal, 'this_year_cost_number'));
		$this_year_income_number  = array_sum(array_column($everyBusinessTotal, 'this_year_income_number'));
		$this_year_recharge_money = array_sum(array_column($everyBusinessTotal, 'this_year_recharge_money'));
		$total                    = $this->computeProfit(compact('this_month_cost_money', 'this_month_income_money', 'this_year_cost_money', 'this_year_income_money', 'this_month_cost_number', 'this_month_income_number', 'this_year_cost_number', 'this_year_income_number', 'this_year_recharge_money'));
		
		return compact('total', 'data');
	}
	
	/**
	 * 记录每个商务的日志内容
	 *
	 * @access protected
	 *
	 * @param $data array 日志数据
	 *
	 * @return void
	 **/
	protected function salesmanLog($data)
	{
		$data['date'] = $this->date;
		MongoProfitSalesmanLogs::insert($data);
	}
	
	/**
	 * 根据客户维度分别计算当日的成本、当月的累计成本
	 *
	 * @access protected
	 *
	 * @param $customerIds array 客户ID
	 *
	 * @return array
	 **/
	protected function getCostByCustomerIds($customerIds)
	{
		$thisMonthCostMoney  = 0;
		$thisMonthCostNumber = 0;
		$thisYearCostMoney   = 0;
		$thisYearCostNumber  = 0;
		//记录明细（方便回溯）
		$details = [];
		array_walk($this->costData, function ($item) use (&$thisMonthCostMoney, &$thisMonthCostNumber, &$details, &$thisYearCostMoney, &$thisYearCostNumber, $customerIds) {
			if (in_array($item['customer_id'], $customerIds)) {
				#设置金额
				$thisMonthCostMoney = bcadd($thisMonthCostMoney, $item['month_money'], 6);
				$thisYearCostMoney  = bcadd($thisYearCostMoney, $item['year_money'], 6);
				#设置量
				$thisMonthCostNumber             = bcadd($thisMonthCostMoney, $item['month_number']);
				$thisYearCostNumber              = bcadd($thisYearCostMoney, $item['year_number']);
				$details[$item['customer_id']][] = $item;
			}
		});
		
		return [
			$thisMonthCostMoney,
			$thisYearCostMoney,
			$thisMonthCostNumber,
			$thisYearCostNumber,
		];
	}
	
	/**
	 * 根据客户维度分别计算当日的收入、当月的累计收入
	 *
	 * @access protected
	 *
	 * @param $customerIds array 客户ID
	 *
	 * @return array
	 **/
	protected function getIncomeByCustomerIds($customerIds)
	{
		$thisMonthIncomeMoney  = 0;
		$thisMonthIncomeNumber = 0;
		$thisYearIncomeMoney   = 0;
		$thisYearIncomeNumber  = 0;
		
		//记录明细（方便回溯）
		$details = [];
		array_walk($this->incomeData, function ($item) use (&$thisMonthIncomeMoney, &$thisMonthIncomeNumber, &$thisYearIncomeMoney, &$thisYearIncomeNumber, &$details, $customerIds) {
			if (in_array($item['customer_id'], $customerIds)) {
				#设置金额
				$thisMonthIncomeMoney = bcadd($thisMonthIncomeMoney, $item['month_money'], 6);
				$thisYearIncomeMoney  = bcadd($thisYearIncomeMoney, $item['year_money'], 6);
				#设置量
				$thisMonthIncomeNumber           = bcadd($thisMonthIncomeNumber, $item['month_number']);
				$thisYearIncomeNumber            = bcadd($thisYearIncomeNumber, $item['year_number']);
				$details[$item['customer_id']][] = $item;
			}
		});
		
		return [
			$thisMonthIncomeMoney,
			$thisYearIncomeMoney,
			$thisMonthIncomeNumber,
			$thisYearIncomeNumber,
			$details,
		];
	}
	
	/**
	 * 根据数据权限获取当前用户所能看见的部门及成员数据
	 *
	 * @access protected
	 *
	 * @param $email       string 邮箱地址
	 * @param $dataAuth    string  数据权限（0全部数据  1本人数据  2本部门数据  3下级部门数据）
	 * @param $deptIds     array 所属所有的部门ID
	 *
	 * @return array
	 **/
	protected function getBusinessDepartment($email, $dataAuth, $deptIds)
	{
		$dataAuth = explode(',', $dataAuth);
		//全量的商务部门数据
		if (in_array(0, $dataAuth)) {
			return $this->getAllBusinessDepartment();
		}
		
		//如果不属于商务部门的用户，并且数据权限不是“全部数据”，则证明商务区域维度的统计无
		if (!in_array($this->businessDeptId, $deptIds)) {
			return [];
		}
		
		//其他三种数据权限代表的是累加状态
		$data = [];
		
		//当前用户的数据，下面三种情况都会用到的数据
		$user = SystemUser::where('email', $email)
						  ->first();
		
		if (in_array(1, $dataAuth)) {
			//代表的是本人的数据，所以需要获取本人所处于的部门ID
			$dept_id   = $user->dept_id;
			$dept_name = Dept::where('dept_id', $dept_id)
							 ->first()->dept_name;
			//PHP数组中不支持.字符，所以将.替换为__
			$username       = str_replace(' . ', '__', $user->username);
			$member         = [
				$username => [
					'name'        => $user->realname,
					'salesman'    => $username,
					'customer_id' => [],
				],
			];
			$data[$dept_id] = compact('dept_id', 'dept_name', 'member');
		}
		
		if (in_array(2, $dataAuth)) {
			//代表的是本部门的数据，所以需要获取本部门的ID
			$dept_id = $user->dept_id;
			$member  = SystemUser::select('realname', 'username')
								 ->where('dept_id', $dept_id)
								 ->get()
								 ->map(function ($item) {
									 //PHP数组中不支持.字符，所以将.替换为__
									 $username = str_replace(' . ', '__', $item->username);
				
									 return [
										 'name'        => $item->realname,
										 'salesman'    => $username,
										 'customer_id' => [],
									 ];
								 })
								 ->toArray();
			$member  = array_combine(array_column($member, 'salesman'), $member);
			if (!array_key_exists($dept_id, $data)) {
				$dept_name      = Dept::where('dept_id', $dept_id)
									  ->first()->dept_name;
				$data[$dept_id] = compact('dept_id', 'dept_name', 'member');
			} else {
				$data[$dept_id]['member'] = $member;
			}
		}
		
		if (in_array(3, $dataAuth)) {
			//代表的是下级部门的数据，在这里需要说明，目前报表仅支持到商务的下一级，所以需要判断当前用户是否为商务部门的成员，如果是，则可以查看到所有商务的用户及部门，如果不是，则返回的是[]
			if ($user->dept_id == $this->businessDeptId) {
				return $this->getAllBusinessDepartment();
			}
		}
		
		return $data;
	}
	
	/**
	 * 获取全量的商务部门及成员数据
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getAllBusinessDepartment()
	{
		return DeptGrade::where('grade_father_id', $this->businessDeptId)
						->pluck('grade_dept_id', 'grade_dept_id')
						->map(function ($item) {
							$dept_id   = $item;
							$dept_name = Dept::where('dept_id', $dept_id)
											 ->first()->dept_name;
							//获取这个部门的成员
							$member = SystemUser::select('realname', 'username')
												->where('dept_id', $dept_id)
												->get()
												->map(function ($item) {
													//PHP数组中不支持.字符，所以将.替换为__
													$username = str_replace(' . ', '__', $item->username);
				
													return [
														'name'        => $item->realname,
														'salesman'    => $username,
														'customer_id' => [],
													];
												})
												->toArray();
							//用salesman作为键
							$member = array_combine(array_column($member, 'salesman'), $member);
			
							return compact('dept_id', 'dept_name', 'member');
						})
						->filter(function ($item) {
							return !empty($item['member']);
						})
						->toArray();
	}
	
	/**
	 * 计算每个单元（商务、产品、客户、部门）的当日毛利润、当月累计毛利润
	 *
	 * @access protected
	 *
	 * @param $data array 单元数据
	 *
	 * @return array
	 **/
	protected function computeProfit($data)
	{
		$this_month_cost_money   = $data['this_month_cost_money'];
		$this_month_income_money = $data['this_month_income_money'];
		$this_year_cost_money    = $data['this_year_cost_money'];
		$this_year_income_money  = $data['this_year_income_money'];
		
		$data['this_month_profit_money'] = bcsub($this_month_income_money, $this_month_cost_money, 6);
		$data['this_year_profit_money']  = bcsub($this_year_income_money, $this_year_cost_money, 6);
		
		return $data;
	}
	
	/**
	 * 判断一个数据是否为无意义数据（客户、产品（邦秒验子产品）如果是无意义数据，则过滤掉）
	 *
	 * @access protected
	 *
	 * @param $data array 数据
	 *
	 * @return boolean
	 **/
	protected function nonUseData($data)
	{
		//所有数据均为0
		return $data['this_month_cost_money'] == 0 && $data['this_month_income_money'] == 0 && $data['this_year_cost_money'] == 0 && $data['this_year_income_money'] == 0 && $data['this_month_cost_number'] == 0 && $data['this_month_income_number'] == 0 && $data['this_year_cost_number'] == 0 && $data['this_year_income_number'] == 0 && $data['this_month_profit_money'] == 0 && $data['this_year_profit_money'] == 0 && $data['this_year_recharge_money'] == 0;
	}
	
}