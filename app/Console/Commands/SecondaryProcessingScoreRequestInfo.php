<?php

namespace App\Console\Commands;


use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\MongoCuccMonitor;
use App\Models\MongoSecondaryProcessingScore;
use App\Models\Product;
use App\Models\ProductType;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;
use Yulore\Apikey\Masking;

//对邦信分快捷版评分类字段进行二次处理，形成半成品数据
class SecondaryProcessingScoreRequestInfo extends Command
{
	use WechatExceptionTrait;
	
	protected $name        = '二次处理评分类字段请求数据';
	protected $signature   = 'bxf:process_score_request
    {--day= : 处理哪天的数据，格式为Ymd，默认昨日}
    {--days=1 : 需要处理几天的数据，默认为1，不得小于1}
    ';
	protected $description = '对邦信分快捷版评分类字段请求统计数据的二次处理';
	protected $product     = [];    //评分类字段的产品 field=>product_id
	protected $ctype       = [
		0,  //电信
		1,  //联通
		3,   //移动
		4,	//全国移动
	];
	protected $notMeanOValue;       //无意义的o_value值
	protected $uniqueIndex = ['day', 'product_id', 'customer_id', 'c_type', 'min'];       //入库唯一键校验
	
	public function handle()
	{
		$option = $this->checkOption();
		
		$day   = array_get($option, 'day');
		$days  = array_get($option, 'days');
		$today = date('Ymd');
		
		for ($i = 0; $i < $days; $i++) {
			$date = date('Ymd', strtotime('+' . $i . ' days', strtotime($day)));
			if ($date >= $today) {
				break;
			}
			try {
				$this->runProgressing($date);
				$this->output->success("{$date}天的数据处理完成");
			} catch (\Exception $exception) {
				$message = $exception->getMessage();
				$line    = $exception->getLine();
				$msg     = "{$date}天的数据处理失败，原因如下:\n Message:{$message} \n Line:{$line}";
				$this->output->error($msg);
				
				//微信预警
				$this->wechat("[{$this->name}]任务出现异常 ==> \n {$msg}");
			}
		}
		
	}
	
	/**
	 * 获取并校验参数
	 *
	 * @access protected
	 *
	 * @return array|false
	 **/
	protected function checkOption()
	{
		//设置失败的字段标记
		$this->notMeanOValue = config('params.bxf_short.notMeanOValue');
		
		$day = $this->input->getOption('day');
		if (empty($day)) {
			$day = date('Ymd', strtotime('-1 days'));
		}
		
		if (!preg_match('/^\d{8}$/', $day)) {
			$this->output->error('日期格式不正确');
			
			return false;
		}
		
		if ($day >= date('Ymd')) {
			$this->output->error('未来数据不可计算');
			
			return false;
		}
		
		$days = $this->input->getOption('days');
		if (empty($days) || !is_numeric($days)) {
			$this->output->error('天数格式不正确');
			
			return false;
		}
		
		return compact('day', 'days');
	}
	
	/**
	 * 获取产品ID、产品字段的对应关系
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getScoreProductMapping()
	{
		if (empty($this->product)) {
			$product = Product::where('father_id', 210)
							 ->pluck('mark', 'product_id')
							 ->toArray();
//			$config = @json_decode($config, true);
//			if (is_null($config)) {
//				throw new \Exception('解析邦信分快捷版产品配置失败');
//			}
//			$product = array_get(array_get($config, 0), 'option');
//			if (empty($product)) {
//				throw new \Exception('邦信分快捷版产品配置中的产品ID与字段名称对应关系获取失败');
//			}
//			$product          = array_column($product, 'opt_val', 'product_id');
			$score_product_id = ProductType::where('type', '=', 1)
										   ->pluck('product_id')
										   ->toArray();
			$this->product    = array_flip(array_intersect_key($product, array_flip($score_product_id)));
		}
		
		return $this->product;
	}
	
	/**
	 * 处理某一天的数据
	 *
	 * @access protected
	 *
	 * @param $day string 日期
	 *
	 * @return void
	 **/
	protected function runProgressing($day)
	{
		$day = intval($day);
		//获取当前数据中存在的客户ID与apikey映射关系 customer_id => [apikey1,apikey2]
		$customerMapping = $this->getCustomerInfo($day);
		
		//获取分值类产品的映射关系表
		$productMapping = $this->getScoreProductMapping();
		
		//获取分值区间的数据表
		$base = $this->initBaseCollect();
		
		//输出内容,并建造一个进度条
		$this->output->writeln("开始处理{$day}的数据");
		$max = count($customerMapping) * count($productMapping) * (count($base) + 1) * count($this->ctype);
		$this->output->createProgressBar();
		$this->output->progressStart($max);
		
		//存在5个维度的合计统计
		//时间维度、客户维度、产品维度、渠道维度、分支区间维度，当前已经对时间维度进行了划分、下面依次嵌套各维度进行汇总数据
		//客户维度
		
		array_walk($customerMapping, function ($apikey, $customer_id) use ($productMapping, $day, $base) {
			$apikey = $this->apikeyEncode($apikey, $day);
			//产品维度
			array_walk($productMapping, function ($product_id, $o_field) use ($apikey, $customer_id, $day, $base) {
				//渠道维度
				array_walk($this->ctype, function ($ctype) use ($product_id, $o_field, $apikey, $customer_id, $day, $base) {
					//分值区间维度
					array_walk($base, function ($data) use ($ctype, $product_id, $o_field, $apikey, $customer_id, $day) {
						$min    = intval($data['min']);
						$max    = intval($data['max']);
						$number = MongoCuccMonitor::where('day', $day)
												  ->whereIn('apikey', $apikey)
												  ->where('o_field', $o_field)
												  ->where('ctype', $ctype)
												  ->where('o_value', '>=', $min)
												  ->where('o_value', '<=', $max)
												  ->sum('cnt');
						if ($number == 0) {
							$this->output->progressAdvance();
							
							return;
						}
						$data['ctype']       = $ctype;
						$data['customer_id'] = $customer_id;
						$data['product_id']  = $product_id;
						$data['day']         = $day;
						$data['number']      = $number;
						
						//增加数据
						MongoSecondaryProcessingScore::updateOrcreate(array_only($data, $this->uniqueIndex), $data);
						$this->output->progressAdvance();
						
						return;
					});
					
					//特殊维度 分值为-9999的单独统计，为无效返回数
					$number = MongoCuccMonitor::where('day', $day)
											  ->whereIn('apikey', $apikey)
											  ->where('o_field', $o_field)
											  ->where('ctype', $ctype)
											  ->where('o_value', $this->notMeanOValue)
											  ->sum('cnt');
					if ($number != 0) {
						$min  = $max = $this->notMeanOValue;
						$data = compact('day', 'product_id', 'customer_id', 'ctype', 'min', 'max', 'name', 'number');
						MongoSecondaryProcessingScore::updateOrcreate(array_only($data, $this->uniqueIndex), $data);
					}
					$this->output->progressAdvance();
				});
			});
		});
		$this->output->progressFinish();
	}
	
	/**
	 * 获取客户与apikey的对应关系表
	 *
	 * @access protected
	 *
	 * @param $day integer 日期
	 *
	 * @return array
	 **/
	protected function getCustomerInfo($day)
	{
		//获取当日的所有数据的APIKEY
		$apikey = MongoCuccMonitor::where('day', $day)
								  ->pluck('apikey')
								  ->toArray();
		if (empty($apikey)) {
			throw new \Exception('未在数据表中找到当天的数据');
		}
		$apikey = array_values(array_unique($apikey));
		
		if ($day >= ********) {
			$apikey = array_map(function ($item) {
				return Masking::decode($item);
			}, $apikey);
		}
		//获取账号数据
		$accountInfo = Account::whereIn('apikey', $apikey)
							  ->pluck('customer_id', 'apikey')
							  ->toArray();
		$data = [];
		array_walk($accountInfo, function ($customer_id, $apikey) use (&$data) {
			if (!empty($apikey)) {
				$data[$customer_id][] = $apikey;
			}
		});
		
		return $data;
	}
	
	/**
	 * 初始化评分类字段的某一个产品的数据集合
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function initBaseCollect()
	{
		$base = [];
		//第一个与其他的不同
		$base[] = [
			'name'   => '0~50',
			'min'    => 0,
			'max'    => 50,
			'number' => 0,
		];
		for ($i = 51; $i < 1000; $i += 50) {
			$min    = $i;
			$max    = $min + 49;
			$name   = "{$min}~{$max}";
			$number = 0;
			$base[] = compact('name', 'min', 'max', 'number');
		}
		
		return $base;
	}
	
	/**
	 * 对APIKEY进行加密换算
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/31 10:20
	 *
	 * @param $apikey array
	 * @param $date   string 日期 Ymd
	 *
	 * @return array
	 **/
	protected function apikeyEncode($apikey, $date)
	{
		if ($date < ********) {
			return $apikey;
		}
		
		return array_merge($apikey, array_map(function ($item) {
			return Masking::encode($item);
		}, $apikey));
	}
}