<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\MongoUpstream;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

//转移邦秒验营收数据到金盾中
class TransferRevenue extends Command
{
	use WechatExceptionTrait;
	protected $signature = 'bill:transfer-revenue
	{--bill_date= : 转移的账单日期, 格式Ymd，默认今日}
    {--days=1 : 需要转移的天数，默认1天}
    {--account_id= : 需要转移那些账号的，默认所有，多个账号ID可用,隔开}
    {--customer_id= : 需要转移那些客户的，默认所有，多个客户ID可用,隔开}
    {--product_id= : 需要转移那些产品的，默认所有，多个产品ID可用,隔开}
	';
	
	protected $description = '转移邦秒验营收数据到金盾中';
	
	
	protected $bill_date;
	protected $end_date;
	protected $days              = 1;
	protected $apikey            = [];
	protected $productIds        = [];
	protected $goldShieldChannel = [
		'jindun',
		'jindunMD5',
		'jindunSHA256',
	];
	
	public function handle()
	{
		//校验参数
		$this->checkParams();
		
		//转移营收数据
		$this->transfer();
	}
	
	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/2 14:48
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
		$bill_date = $this->option('bill_date') ?: date('Ymd', strtotime('-1 days'));
		if (!preg_match('/^\d{8}$/', $bill_date)) {
			throw new \Exception('[bill_date]参数格式不正确，请输入Ymd的时间格式');
		}
		if ($bill_date >= date('Ymd')) {
			throw new \Exception('不能转移今日及今日之后的账单');
		}
		$this->bill_date = intval($bill_date);
		
		$days = $this->option('days') ?: 1;
		if (!is_numeric($days) || $days <= 0) {
			throw new \Exception('[days]参数格式不正确');
		}
		$this->days     = intval($days);
		$this->end_date = intval(date('Ymd', strtotime('+' . $this->days . ' days', strtotime($this->bill_date))));
		
		$account_id = $this->option('account_id');
		if (!empty($account_id)) {
			$account_id   = explode(',', $account_id);
			$this->apikey = Account::whereIn('account_id', $account_id)
								   ->pluck('apikey')
								   ->toArray();
		}
		
		$customer_id = $this->option('customer_id');
		if (!empty($customer_id)) {
			$customer_id  = explode(',', $customer_id);
			$apikey       = Account::whereIn('customer_id', $customer_id)
								   ->pluck('apikey')
								   ->toArray();
			$this->apikey = empty($this->apikey) ? $apikey : array_intersect($apikey, $this->apikey);
		}
		
		$product_id = $this->option('product_id');
		if (!empty($product_id)) {
			$this->productIds = explode(',', $product_id);
		}
	}
	
	/**
	 * 转移营收数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/2 14:58
	 *
	 * @return void
	 **/
	protected function transfer()
	{
		//根据数据源中的统计情况及参数，查询需要转移的apikey、产品ID等相关信息
		$prepareInfo = $this->getPrepareInfo();
		
		$bar = $this->output->createProgressBar(count($prepareInfo));
		$bar->start();
		
		foreach ($prepareInfo as $item) {
			$date       = $item['date'];
			$product_id = $item['product_id'];
			$apikey     = $item['apikey'];
			
			//生成转移账单
			try {
				app('transferRevenueService.bmyToGoldShield')->transfer($apikey, $product_id, $date);
			} catch (\Exception $exception) {
				$message = "为金盾转移邦秒验营收账单时，出现未知问题，Message:{$exception->getMessage()}，File:{$exception->getFile()}，Line:{$exception->getLine()}，Apikey:{$apikey}";
				$this->wechat($message);
			}
			
			$bar->advance();
		}
		
		$bar->finish();
		$this->output->success('生成成功');
		
	}
	
	/**
	 * 根据数据源中的数据情况及命令中所传的参数，获取需要转移的apikey、产品ID、日期等准备转移的信息
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/2 15:14
	 *
	 * @return array
	 **/
	protected function getPrepareInfo()
	{
		$data = MongoUpstream::query()
							 ->raw(function ($collection) {
								 $aggregate              = [];
								 $aggregate[0]['$match'] = [
									 'date'    => [
										 '$gte' => $this->bill_date,
										 '$lt'  => $this->end_date,
									 ],
									 'channel' => [
										 '$in' => $this->goldShieldChannel,
									 ],
								 ];
								 if (!empty($this->apikey)) {
									 $aggregate[0]['$match']['apikey'] = ['$in' => $this->apikey];
								 }
								 if (!empty($this->productIds)) {
									 $aggregate[0]['$match']['product_id'] = ['$in' => $this->productIds];
								 }
			
								 $aggregate[1]['$group'] = [
									 '_id'        => [
										 'date'       => '$date',
										 'product_id' => '$product_id',
										 'apikey'     => '$apikey',
									 ],
									 'date'       => ['$first' => '$date'],
									 'product_id' => ['$first' => '$product_id'],
									 'apikey'     => ['$first' => '$apikey'],
								 ];
			
								 $aggregate[2]['$project'] = [
									 '_id' => 0,
								 ];
			
								 return $collection->aggregate($aggregate);
							 })
							 ->toArray();
		return $data;
	}
	
	/**
	 * 清理垃圾数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/2 15:07
	 *
	 * @return void
	 **/
	protected function clean()
	{
	
	}
}