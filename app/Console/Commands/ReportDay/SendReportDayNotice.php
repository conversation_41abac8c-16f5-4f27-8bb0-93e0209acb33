<?php

/**
 * 推送产品拉取
 */
namespace App\Console\Commands\ReportDay;



use App\Models\BillOperatorMonthCheck;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\StatisticsInterfaceUsage;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Console\Command;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * php artisan push_interface_usage_for_enterprise_service
 */
class SendReportDayNotice extends Command
{
    use CurlTrait;

    protected $signature = 'send_report_day_notice';


    protected $description = '发送领导日报链接';

    protected $url = 'https://fin-stat.dianhua.cn';
    protected $send_url = 'http://fina-monitor.dianhua.cn/alarmlog/sendAlarmFeiShuGroup';


    public function handle()
    {
        //接收变量
        $yestday = date('Ymd', strtotime('-1 days'));
        $content = [
            'card_link' => [
                'url' => $this->url.'/ReportTask/MobileStat/index.html?date='.$yestday
            ],
            'elements' => [
                ['tag' => 'markdown', 'content' => '请查看'.$yestday.'日报数据! <at email=<EMAIL>></at>']
            ]
        ];

        $postData = [
            'group' => 'leader_report',
            'msg_type' => 'interactive',
            'content' => $content
        ];

        $res = $this->postRawJson($this->send_url, $postData);

        $this->output->success("通知结束");
    }




}
