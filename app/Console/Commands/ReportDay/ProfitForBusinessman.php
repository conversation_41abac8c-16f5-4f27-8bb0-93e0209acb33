<?php

namespace App\Console\Commands\ReportDay;

use App\Http\Repository\DeptRepository;
use App\Http\Repository\StatBaseRepository;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\CommonInfoModel;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\Dept;
use App\Models\SystemUser;
use App\Models\CustomerBillAdjust;
use App\Models\EmailConfig;
use App\Providers\Auth\DataAuth;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\TraitUpgrade\ExcelTrait;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
/**
 * 商务日报
 *
 * 收件人:
 * select * from email_config where type = 0 and status = 1 and scene = 'report_day_profit_businessman';
 *
 *
 * php artisan report_day:profit_for_businessman --username guojing.sun --sendto wei.xiu
 */
class ProfitForBusinessman extends Command
{
	use ExcelTrait;
	protected $signature   = "report_day:profit_for_businessman
	{--date= : 日期, 格式Ymd，默认昨日}
    {--username= : 收件人，以用户在后台的用户名为基准，多个用户使用,隔开，该参数不会改变用户的邮件授权情况，不传则对所有授权用户发送}
    {--sendto= : 收件人, 添加此选项将强制使收件人为sendto, 使用username指定的商务的数据}
    {--save : 是否将数据保存在数据库中}
    {--ignoreCheck : 是否忽略监测}
	";
	protected $description = '商务收入日报表邮件发送（用户版）';
	
	protected $emailConfigScene = 'report_day_profit_businessman';

	//各个商务部门的映射数据
	protected $deptId2DeptNameMapping = [];
	//用户与商务部门的映射数据
	protected $salesman2DeptIdMapping = [];
	//客户与商务用户的映射关系数据
	protected $customerId2SalesmanMapping = [];
	//商务用户与真实姓名的映射关系
	protected $salesman2RealNameMapping = [];

	//存储用户财务字段查看权限
	protected $financeAuthMap =[];

	//存储用户查看客户及来源查看权限
	protected $customerSourceAuthMap =[];

	// 当前发送账号的财务字段查看权限
	protected $financeAuth =[
		'show_money'=>0,
		'show_money_agent'=>0,//收入(征信)
		'show_cost'=>0,
		'show_profit'=>0,
	];

	/**
	 * @var array 约定发送给那些用户，为null代表发送给所有用户
	 */
	protected $username = null;

    /** @var null 指定收件人 */
    protected $sendto = null;

	/**
	 * @var integer 日期
	 */
	protected $date;
	protected $month;
	protected $firstDateOfMonth;
	protected $firstDateOfYear;
	
	/**
	 * @var array 基础的数据
	 */
	protected $data     = [];
	protected $monthly  = [];//按月记录数值
	protected $baseData = [
		'this_date_income_money'   => 0,
		'this_date_income_number'  => 0,
		'this_date_cost_money'     => 0,
		'this_date_cost_number'    => 0,
		'this_month_income_money'  => 0,
		'this_month_income_number' => 0,
		'this_month_cost_money'    => 0,
		'this_month_cost_number'   => 0,
		'this_year_income_money'   => 0,
		'this_year_income_number'  => 0,
		'this_year_cost_money'     => 0,
		'this_year_cost_number'    => 0,
		'this_year_recharge' 	   => 0,
		'this_date_profit'         => 0,
		'this_month_profit'        => 0,
		'this_year_profit'         => 0,
	];
	protected $monthlyBaseData = [
		'this_date_income_money'   => [],
		'this_date_income_number'  => [],
		'this_date_cost_money'     => [],
		'this_date_cost_number'    => [],
		'this_month_income_money'  => [],
		'this_month_income_number' => [],
		'this_month_cost_money'    => [],
		'this_month_cost_number'   => [],
		'this_year_income_money'   => [],
		'this_year_income_number'  => [],
		'this_year_cost_money'     => [],
		'this_year_cost_number'    => [],
		'this_year_recharge' 	   => [],
		'this_date_profit'         => [],
		'this_month_profit'        => [],
		'this_year_profit'         => [],
	];
	
	//排序的字段
	protected $orderField = 'this_date_income_money';
	
	/**
	 * @var array 邮件抄送人
	 */
	protected $cc = [

	];
	
	/**
	 * @var array 客户充值数据
	 */
	protected $recharge = [];
	/**
	 * @var array 客户的备注信息
	 */
	protected $remarks = [];
	
	/**
	 * @var string 如果保存HTML，只保存最后一个客户的
	 **/
	protected $saveHtml = '';

	/**
	 * @var array 商务能看到的用户
	 **/
	protected $auth_customer_ids = [];
	
	//渠道跟进人是当前发送用户的 所有客户
	protected $auth_channel_follower_customers = [];
    protected $belong_salesman_arr = [];
	
	//当前发送用户
	protected $current_name = '';
	
	// 可见收入 成本 利润的人员
	protected $authCostPorfit = [
		'yong.liu',
	];

    /** @var array 获取部门的后代部门 */
    protected $sub_depts = [
        '银行销售部' => [],
        '非银销售部' => [],
    ];

    /** @var string[] 可查看银行/非银行合计数据人员 */
    protected $authBankCount = [
        'yong.liu',
        'mengsi.wang'
    ];


    /** @var array[] 商务如果要查看额外客户 在这里配置 */
    private $salesman_add_customer = [
        'guojing.sun' => [//商务
            'C20240131Q7DSSE' => [//额外客户
                50000 => [1]//可查看的主产品和来源
            ],
        ],
    ];


    //脚本执行
	public function handle()
	{
		try {
			//设置参数
			$this->setParams();

			//对每一个用户发送邮件
			array_walk($this->username, [$this, 'send']);
			
			//将HTML保存在数据库中
			$this->saveHtml();
			
		} catch (\Exception $exception) {
            // echo $exception->getFile().':',$exception->getLine(),PHP_EOL;
            // echo $exception->getMessage(),PHP_EOL;
			sendCommandExceptionNotice($this, $exception);
		}
	}
	
	
	/**
	 * 将HTML保存在数据库中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/29 16:07
	 *
	 *
	 * @return void
	 */
	private function saveHtml()
	{
		//是否将HTML保存在数据库中
		if ($this->input->getOption('save')) {
			$content = json_encode(['html' => $this->saveHtml, 'date' => $this->date], JSON_UNESCAPED_UNICODE);
			
			CommonInfoModel::where('id', 7)
						   ->update(compact('content'));
		}
	}
	
	/**
	 * 对每一个用户发送邮件
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:49
	 *
	 * @param $username string 用户名
	 *
	 * @return void
	 */
	private function send($username)
	{
		//获取权限服务对象
		$dataAuthService = new DataAuth($username);
		$this->current_name = $username;

        //$userInfo = SystemUser::getUserInfoByName($username);
        //$salesman_arr = DeptRepository::getUnderLing($username);
        $underling_res = DeptRepository::getUnderLing($username);
        $salesman_arr = $underling_res['salesman'];

        $dept_struct = DeptRepository::getSaleDeptStruct($username);

        $sort_dept = $this->sort_dept($dept_struct);

        //获取管辖的用户(商务)
        // $salesman_arr = $dataAuthService->getBelongUsers();

        $this->belong_salesman_arr = array_unique(array_merge($salesman_arr, [$username]));

		//获取这个用户的所能查看的所有客户ID
        $this->auth_customer_ids = $dataAuthService->getCustomerIds();
		$this->auth_channel_follower_customers = $dataAuthService->getCustomerIdsSourceByUserName();

        //获取需要发送的数据
		$data = $this->getUsernameData($username);

		//获取大区维度的统计
		$businessInfo = $this->getBusinessInfo($data,$sort_dept);

		//获取客户维度统计
		$customerInfo = $this->getCustomerInfo($data,$username);

		//获取当前用户权限
		$this->financeAuthFormat($username,0);
		//标题
		$title = date('m月d日', strtotime($this->date)) . '权责收入及毛利数据统计 -- （' . $dataAuthService->getRealName() . '）收';
		//创建HTML
		$html = $this->createHtml($businessInfo, $customerInfo, $title, $username);

		//保存最后一个HTML内容
		$this->saveHtml = $html;

		//生成附件
		//$filename = $this->createExcel($businessInfo, $productInfo, $customerInfo);

        $sendto = $username;
        if($this->sendto) {//如果指定收件人
            $sendto = $this->sendto;
        }

		//发送邮件
		$mail = new SendMailService();
		$mail->setFromName('金融后台项目组')
			 ->setAddressee([['email' => $sendto.'@yulore.com', 'name' => $dataAuthService->getRealName()]])
			 ->setCC($this->cc)
			 ->setSubject($title)
			 ->setContent($html)
			 ->sendByAsync();
             // ->send();

		$this->output->success("[{$username}] 邮件发送成功");
	}


    /**
     * 按照商务架构排序,添加每个部门的层级数(deep)用于合并单元格,区分层级计算合计
     *
     * @param $dept_info
     *
     * @return array
     * <AUTHOR> 2024-03-22 20:13:56
     */
    private function sort_dept($dept_info){
        if(empty($dept_info)){
            return [];
        }
        $res = [];

        foreach($dept_info as $dept){
            $res[] = [
                'dept_id'   => $dept['value'],
                'dept_name' => $dept['label'],
            ];
            if(!empty($dept['children'])){
                $_res = $this->sort_dept($dept['children']);
                if(key_exists($dept['label'],$this->sub_depts)){
                    $this->sub_depts[$dept['label']] = array_column($_res, 'dept_id');
                }
                if(!empty($_res)) {
                    $res = array_merge($res, $_res);
                }
            }
        }
        return $res;
    }


	private function financeAuthFormat($username,$auth_type=1){
		if (in_array(-1, $this->financeAuthMap[$username]['user_money_product'])) {
			$this->financeAuth['show_money'] = 1;
		} else {
			$this->financeAuth['show_money'] = 0;
		}
		if($auth_type==0){
            if(in_array($username,$this->authCostPorfit)){
            	$this->financeAuth['show_cost'] = 1;
                $this->financeAuth['show_profit'] = 1;

            }else{
            	$this->financeAuth['show_cost'] = 0;
                $this->financeAuth['show_profit'] = 0;
            }
        }else{

			if (in_array(-1, $this->financeAuthMap[$username]['user_cost_product'])) {
				$this->financeAuth['show_cost'] = 1;
			} else {
				$this->financeAuth['show_cost'] = 0;
			}
			if ($this->financeAuth['show_money']&&$this->financeAuth['show_cost']) {
				$this->financeAuth['show_profit'] = 1;
			} else {
				$this->financeAuth['show_profit'] = 0;
			}
		}
	}
	
	/**
	 * 创建附件
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 16:50
	 *
	 * @param $businessInfo array 商务部门维度的统计数据
	 * @param $productInfo  array 产品维度的统计数据
	 * @param $customerInfo array 客户维度的统计数据
	 *
	 * @return string 文件路径
	 */
	private function createExcel($businessInfo, $productInfo, $customerInfo)
	{
		$this->file_out_init();
		
		//设置部门的sheet
		$this->setBusinessSheet($businessInfo);
	}
	
	/**
	 * 创建大区的sheet
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 16:52
	 *
	 * @param $data array 商务部门维度的统计数据
	 *
	 * @return void
	 */
	private function setBusinessSheet($data)
	{
		//设置列宽
		$this->setWidth([18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18]);
		//标题
		$title = ['区域', '商务', '计费用量', '当日收入', '当日成本', '当日毛利', '当月收入', '当月成本', '当月毛利', '本年收入', '本年成本', '本年毛利',];
		$this->addRowContent($title, 18, true, true);
		
		//合计
		$total = $data['total'];
		$this->addRowContent([
			'合计',
			'',
			round($total['this_date_income_number'], 0),
			round($total['this_date_income_money'], 2),
			round($total['this_date_cost_money'], 2),
			round($total['this_date_profit'], 2),
			round($total['this_month_income_money'], 2),
			round($total['this_month_cost_money'], 2),
			round($total['this_month_profit'], 2),
			round($total['this_year_income_money'], 2),
			round($total['this_year_cost_money'], 2),
			round($total['this_year_profit'], 2),
		], 16, false, true);
		$this->mergeCell('A2:B2');
		
		//每一个部门
		$data = $data['result'];
		foreach ($data as $business) {
			$start_col = $this->col;
			
			
			halt($business);
		}
		
		
	}
	
	/**
	 * 创建HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:49
	 *
	 * @param $businessInfo array 商务部门维度的统计数据
	 * @param $productInfo  array 产品维度的统计数据
	 * @param $customerInfo array 客户维度的统计数据
	 * @param $title        string 邮件标题
	 *
	 * @return string
	 */
	private function createHtml($businessInfo, $customerInfo, $title, $username)
	{

return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<h1>{$title}</h1>
<h2>商务维度权责报表</h2>
<table border="1" cellspacing="0" cellpadding="0">
{$this->createBusinessHtml($businessInfo,$username)}
</table>
<h2>客户维度权责报表</h2>
<table border="1" cellspacing="0" cellpadding="0">
{$this->createCustomerHtml($customerInfo)}
</table>
HTML;
	}
	
	/**
	 * 创建客户部门的HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:36
	 *
	 * @param $data array 客户维度的统计数据
	 *
	 * @return string
	 */
	private function createCustomerHtml($data)
	{
		$total = $data['total'];
		$data  = $data['result'];
		
		//标题
		$html = <<<HTML
<tr>
<th align="center">客户名称</th>
<th align="center">产品名称</th>
<th align="center">计费用量</th>
HTML;

$show_money = $this->financeAuth['show_money'];
$show_cost = $this->financeAuth['show_cost'];
$show_profit = $this->financeAuth['show_profit'];

$show_money && $html .= '<th align="center">当日收入</th>';
$show_cost && $html .= '<th align="center">当日成本</th>';
$show_profit && $html .= '<th align="center">当日毛利</th>';
$show_money && $html .= '<th align="center">当月收入</th>';
$show_cost && $html .= '<th align="center">当月成本</th>';
$show_profit && $html .= '<th align="center">当月毛利</th>';
$show_money && $html .= '<th align="center">现金收入(本年)</th>';
$show_money && $html .= '<th align="center">本年收入</th>';
$show_cost && $html .= '<th align="center">本年成本</th>';
$show_profit && $html .= '<th align="center">本年毛利</th>';
$html .= <<<HTML
<!-- <th align="center">备注</th> -->
</tr>
<tr>
<td align="center">合计</td>
<td align="center">--</td>

<td align="center">{$this->formatNumber($total['this_date_income_number'])}</td>
HTML;
// 合计
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_date_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_date_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_date_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_year_recharge']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_year_profit']).'</td>';
$html .= '<td align="center"></td></tr>';



array_walk($data, function ($customer) use (&$html,$show_money,$show_cost,$show_profit) {
    //部门维度
    $mergeRow = count($customer['products']) + 1;
    $html     .= <<<HTML
<tr>
<td align="center" rowspan="{$mergeRow}">{$customer['customer_name']}</td>
<td align="center">小计</td>
<td align="center">{$this->formatNumber($customer['this_date_income_number'])}</td>
HTML;

    $show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_date_income_money']).'</td>';
    $show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_date_cost_money']).'</td>';
    $show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_date_profit']).'</td>';
    $show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_income_money']).'</td>';
    $show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_cost_money']).'</td>';
    $show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_profit']).'</td>';
	$show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_recharge']).'</td>';
    $show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_income_money']).'</td>';
    $show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_cost_money']).'</td>';
    $show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_profit']).'</td>';


    array_walk($customer['products'], function ($customer) use (&$html, $show_money, $show_cost, $show_profit) {
$html .= <<<HTML
<tr>
<td align="center">{$customer['father_name']}</td>
<td align="center">{$this->formatNumber($customer['this_date_income_number'])}</td>
HTML;

        $show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_date_income_money']).'</td>';
        $show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_date_cost_money']).'</td>';
        $show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_date_profit']).'</td>';
        $show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_income_money']).'</td>';
        $show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_cost_money']).'</td>';
        $show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_profit']).'</td>';
        $show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_recharge']).'</td>';
        $show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_income_money']).'</td>';
        $show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_cost_money']).'</td>';
        $show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_profit']).'</td>';
        // $html .= '<td align="center">'.$customer['remark'].'</td></tr>';
    });
});
		return $html;
	}
	
	/**
	 * 创建商务部门的HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:50
	 *
	 * @param $data array 商务部门的统计数据
	 *
	 * @return string
	 */
	private function createBusinessHtml($data, $username)
	{
		$total = $data['total'];
        $sub_dept_total = $data['sub_dept_total'];
		$data  = $data['result'];
// ------------------ 表头
		//标题
		$html = <<<HTML
<tr>
<th align="center">区域</th>
<th align="center">商务</th>
<th align="center">计费用量</th>
HTML;
	$show_money = $this->financeAuth['show_money'];
	$show_cost = $this->financeAuth['show_cost'];
	$show_profit = $this->financeAuth['show_profit'];
	$show_money && $html .= '<th align="center">当日收入</th>';
	$show_cost && $html .= '<th align="center">当日成本</th>';
	$show_profit && $html .= '<th align="center">当日毛利</th>';
	$show_money && $html .= '<th align="center">当月收入</th>';
	$show_cost && $html .= '<th align="center">当月成本</th>';
	$show_profit && $html .= '<th align="center">当月毛利</th>';
	$show_money && $html .= '<th align="center">本年收入</th>';
	$show_cost && $html .= '<th align="center">本年成本</th>';
	$show_profit && $html .= '<th align="center">本年毛利</th>';
	$html .= <<<HTML
</tr>
<tr>
<td align="center" colspan="2">合计</td>
<td align="center">{$this->formatNumber($total['this_date_income_number'])}</td>
HTML;
// ------------------ 表头

	$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_date_income_money']).'</td>';
	$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_date_cost_money']).'</td>';
	$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_date_profit']).'</td>';
	$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_month_income_money']).'</td>';
	$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_month_cost_money']).'</td>';
	$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_month_profit']).'</td>';
	$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_year_income_money']).'</td>';
	$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_year_cost_money']).'</td>';
	$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_year_profit']).'</td>';
	$html .= '</tr>';
    array_walk($data, function ($business) use (&$html, $username, $show_money, $show_cost, $show_profit, &$sub_dept_total) {

        $mergeRow = count($business['salesman']) + 1;

        $hit_dept_name = '';
        if(in_array($username,$this->authBankCount)) {
            foreach ($this->sub_depts as $sub_dept_name => $sub_dept_ids) {
                //dd($sub_dept_name,$sub_dept_ids);
                if (in_array($business['dept_id'], $sub_dept_ids) && isset($sub_dept_total[$sub_dept_name])) {
                    $hit_dept_name = $sub_dept_name;
                    $sub_total     = $sub_dept_total[$sub_dept_name];

// ------------------ 合计
                    $html .= <<<HTML
<td align="center" rowspan="1">{$sub_dept_name}</td>
<td align="center">小计</td>
<td align="center">{$this->formatNumber($sub_total['this_date_income_number'])}</td>
HTML;
                    $show_money && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_date_income_money']) . '</td>';
                    $show_cost && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_date_cost_money']) . '</td>';
                    $show_profit && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_date_profit']) . '</td>';
                    $show_money && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_month_income_money']) . '</td>';
                    $show_cost && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_month_cost_money']) . '</td>';
                    $show_profit && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_month_profit']) . '</td>';
                    $show_money && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_year_income_money']) . '</td>';
                    $show_cost && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_year_cost_money']) . '</td>';
                    $show_profit && $html .= '<td align="center">' . $this->formatMoney($sub_total['this_year_profit']) . '</td>';
                    $html .= '</tr>';
                }
            }

            unset($sub_dept_total[$hit_dept_name]);
        }

        // ------------------ 部门维度
        $html     .= <<<HTML
<tr>
<td align="center" rowspan="{$mergeRow}">{$business['dept_name']}</td>
<td align="center">小计</td>
<td align="center">{$this->formatNumber($business['this_date_income_number'])}</td>
HTML;

$show_money && $html .= '<td align="center">'.$this->formatMoney($business['this_date_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($business['this_date_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($business['this_date_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($business['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($business['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($business['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($business['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($business['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($business['this_year_profit']).'</td>';
$html .= '</tr>';

			array_walk($business['salesman'], function ($salesman) use (&$html,$show_money,$show_cost,$show_profit) {
				$html .= <<<HTML
<tr>
<td align="center">{$salesman['real_name']}</td>
<td align="center">{$this->formatNumber($salesman['this_date_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($salesman['this_date_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($salesman['this_date_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($salesman['this_date_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($salesman['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($salesman['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($salesman['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($salesman['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($salesman['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($salesman['this_year_profit']).'</td>';
$html .= '</tr>';

			});
			
		});
		
		return $html;
	}
	
	/**
	 * 获取客户维度的统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:41
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	private function getCustomerInfo($data,$username=null)
	{
		//生成一个客户的数据容器
		$result = [];
		
		//融入到客户的数据容器中
		array_walk($data, function ($item) use (&$result,$username) {
			$father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
			$item['father_id'] = $father_id?$father_id:$item['product_id'];
			$this->fillCustomerProduct($item, $result,$username);
		});

        //过滤recharge数据，并返回
        $recharge = array_filter($this->recharge, function ($key) use($username) {
			list($customreid,$source) = explode('_',$key);
            return  !$this->filterDataSource($username,$customreid,$source);
        }, ARRAY_FILTER_USE_KEY);
		// 合并不同来源的充值记录
		$rechargeMap = [];
		array_walk($recharge, function ($item,$key) use (&$rechargeMap) {
			list($customreid,$father_id,$source) = explode('_',$key);
			$uk = $customreid.'_'.$father_id;
			if(!isset($rechargeMap[$uk])){
				$rechargeMap[$uk] = 0.00;
			}
			$rechargeMap[$uk] = bcadd($item,$rechargeMap[$uk],6);
		});

		//将客户充值数据填充到容器中
		array_walk($rechargeMap, function ($money, $uk) use (&$result) {
			$this->fillCustomerRecharge($uk, $money, $result);
		});

		//将客户的备注信息填充到容器中
		// array_walk($this->remarks, function ($content, $customer_id) use (&$result) {
		// 	$this->fillCustomerRemark($customer_id, $content, $result);
		// });

		$total = $this->getTotal($result);
		//汇总中加上本年累计充值
		$total['this_year_recharge'] = array_sum(array_column($result, 'this_year_recharge'));

		// 客户维度求和
		$res = [];
		foreach ($result as $customer_product){
			$this->fillCustomer($customer_product, $res);
		}
		foreach ($result as $customer_product){
			$res[$customer_product['customer_id']]['products'][] = $customer_product;
		}

		$result = $res;
		
		//对数据进行排序
		array_multisort(array_column($result,$this->orderField), SORT_DESC, $result);

		return compact('total', 'result');
	}
	
	/**
	 * 填充客户的充值数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/3/5 16:13
	 *
	 * @param $customer_id string 客户ID
	 * @param $money       float 充值金额
	 * @param $container   array 客户的数据容器
	 *
	 * @return void
	 */
	private function fillCustomerRecharge($uk, $money, &$container)
	{
		if (!array_key_exists($uk, $container)) {
			list($customer_id,$father_id) = explode('_',$uk);
			if($father_id>0){
				$father_name = RedisCache::instance('productId_productName_mapping')
				->get($father_id);
			}else{
				$father_name = '--';
			}
			$container[$uk] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')
												  ->get($customer_id),
				'father_name' =>$father_name,
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$uk] = array_merge($container[$uk], $this->baseData);
		}
		
		$container[$uk]['this_year_recharge'] = $money;
	}
	
	/**
	 * 填充客户的备注信息数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/3/5 16:35
	 *
	 * @param $customer_id   string 客户ID
	 * @param $content       string 备注信息
	 * @param $container     array 客户的数据容器
	 *
	 * @return void
	 */
	private function fillCustomerRemark($customer_id, $content, &$container)
	{
		if (!array_key_exists($customer_id, $container)) {
			$container[$customer_id] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')
												  ->get($customer_id),
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$customer_id] = array_merge($container[$customer_id], $this->baseData);
		}
		
		$container[$customer_id]['remark'] = $content;
	}
	
	/**
	 * 将最小颗粒的数据填充到客户的数据容器中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:42
	 *
	 * @param $data      array 最小颗粒的数据
	 * @param $container array 容器
	 *
	 * @return void
	 */
	private function fillCustomer($data, &$container)
	{
		$customer_id = $data['customer_id'];
		if (!isset($container[$customer_id])) {
			$father_name = '小计';

			$container[$customer_id] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')->get($customer_id),
				'father_id'          => 0,
				'father_name'        => $father_name,
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$customer_id] = array_merge($container[$customer_id], $this->baseData);
		}
		foreach (array_keys($this->baseData) as $key) {
            $container[$customer_id][$key] = bcadd($container[$customer_id][$key], $data[$key], 6);
		}
	}
	

	private function fillCustomerProduct($data, &$container,$username)
	{
		$customer_id = $data['customer_id'];
		$father_id = $data['father_id'];
		$uk = $customer_id .'_'.$father_id;
        $salesmans = array_get($this->customerId2SalesmanMapping, $customer_id, '');
		if (!array_key_exists($uk, $container)) {
			if($father_id>0){
				$father_name = RedisCache::instance('productId_productName_mapping')->get($father_id);
			}else{
				$father_name = '--';
			}
			$container[$uk] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')->get($customer_id),
				'father_id'          => $father_id,
				'father_name' 		=>	$father_name,
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$uk] = array_merge($container[$uk], $this->baseData);
		}

        $_customer_id = $data['customer_id'];
		foreach (array_keys($this->baseData) as $key) {
            if(is_array($data[$key])){
                foreach($data[$key] as $month => $val){
                    $salesman = $salesmans[$month]??'';
                    if (in_array($data['customer_id'], array_keys($this->auth_channel_follower_customers))) {
                        $salesman = $this->current_name;
                    }

                    if (!$salesman) {
                        continue;
                    }

                    if(!in_array($salesman, $this->belong_salesman_arr)){
                        if(key_exists($username,$this->salesman_add_customer)) {
                            if(!isset($this->salesman_add_customer[$username][$_customer_id][$father_id])) {
                                continue;
                            }
                        }else{
                            continue;
                        }
                    }

                    //获取所属部门
                    $dept_id = array_get($this->salesman2DeptIdMapping, $salesman, '');
                    if (!$dept_id) {
                        continue;
                    }
                    $container[$uk][$key] = bcadd($container[$uk][$key], $val, 6);
                }
            }
		}
	}

	/**
	 * 获取大区维度的统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 11:56
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	private function getBusinessInfo($data,$sort_dept){
		//生成一个大区的数据容器
		$result = [];
		//融入到大区的数据容器中
		array_walk($data, function ($item) use (&$result) {
			$this->fillBusiness($item, $result);
		});

		//对每一个大区求和，并且对每一个商务的数据进行排序
		$result = array_map(function ($item) {
			//商务排序
			$salesman = $item['salesman'];
			array_multisort(array_column($salesman, $this->orderField), SORT_DESC, $salesman);
			$item['salesman'] = $salesman;
			
			//求和数据
			$item = array_merge($item, $this->baseData);
			foreach ($salesman as $everySalesman) {
				foreach (array_keys($this->baseData) as $key) {
					$item[$key] = bcadd($item[$key], $everySalesman[$key], 6);
				}
			}
			
			return $item;
		}, $result);

        $sort_dept = array_column($sort_dept,'dept_name','dept_id');
        $sort_dept = array_keys($sort_dept);

        uasort($result,function($a,$b) use ($sort_dept) {
            if(array_search($a['dept_id'],$sort_dept) < array_search($b['dept_id'],$sort_dept)) {
                return -1;
            } else {
                return 1;
            }
        });

		//对数据进行排序
		//array_multisort(array_column($result, $this->orderField), SORT_DESC, $result);

		$total = $this->getTotal($result);
        $sub_dept_total = $this->getSubDeptTotal($result);
		return compact('total', 'result', 'sub_dept_total');
	}

	/**
	 * 汇总合计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:05
	 *
	 * @param $data array 各个维度的结果数据
	 *
	 * @return array
	 */
	private function getTotal($data)
	{
		$total = $this->baseData;

		array_walk($data, function ($item) use (&$total) {
			foreach (array_keys($this->baseData) as $key) {
				$total[$key] = bcadd($total[$key], $item[$key], 6);
			}
		});
		return $total;
	}


    /**
     * 获取指定部门的合计数据
     *
     * @param $data
     *
     * @return array
     * <AUTHOR> 2024-03-25 08:49:53
     */
    private function getSubDeptTotal($data){
        $dept_total = [];

        foreach($this->sub_depts as $dept_name => $sub_dept_ids){
            array_walk($data, function ($item) use ($sub_dept_ids,$dept_name,&$dept_total) {
                $dept_id = $item['dept_id'];
                foreach (array_keys($this->baseData) as $key) {
                    if(in_array($dept_id,$sub_dept_ids)){
                        if(!isset($dept_total[$dept_name])) {
                            $dept_total[$dept_name] = $this->baseData;
                        }
                        $dept_total[$dept_name][$key] = bcadd($dept_total[$dept_name][$key], $item[$key], 6);
                    }
                }
            });
        }

        return $dept_total;
    }
	
	/**
	 * 将最小颗粒的数据填充到大区的数据容器中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 13:56
	 *
	 * @param $data      array 最小颗粒的数据
	 * @param $container array 容器
	 *
	 * @return void
	 */
	private function fillBusiness($data, &$container)
	{
		$customer_id = $data['customer_id'];
		//获取所属商务用户
		$salesmans = array_get($this->customerId2SalesmanMapping, $customer_id, '');
        foreach (array_keys($this->baseData) as $key) {

            if(is_array($data[$key])){
                foreach($data[$key] as $month => $val){
                    $salesman = $salesmans[$month]??'';

                    if (in_array($data['customer_id'], array_keys($this->auth_channel_follower_customers))) {
                        $salesman = $this->current_name;
                    }

                    if (!$salesman) {
                        continue;
                    }

                    if(!in_array($salesman, $this->belong_salesman_arr)){
                        continue;
                    }

                    //获取所属部门
                    $dept_id = array_get($this->salesman2DeptIdMapping, $salesman, '');
                    if (!$dept_id) {
                        continue;
                    }

                    if (!array_key_exists($dept_id, $container)) {
                        $container[$dept_id] = [
                            'dept_id'   => $dept_id,
                            'dept_name' => array_get($this->deptId2DeptNameMapping, $dept_id, '--'),
                            'salesman'  => [],
                        ];
                    }

                    if (!array_key_exists($salesman, $container[$dept_id]['salesman'])) {
                        $real_name = array_get($this->salesman2RealNameMapping, $salesman, $salesman);
                        $container[$dept_id]['salesman'][$salesman] = array_merge(compact('salesman', 'real_name'), $this->baseData);
                    }
                    $container[$dept_id]['salesman'][$salesman][$key] = bcadd($container[$dept_id]['salesman'][$salesman][$key], $val, 6);
                }
            }
        }
	}

    /**
     * 获取某个用户所能看到的数据
     *
     * @access   private
     * @return array
     * @throws \Exception
     * <AUTHOR>
     * @datetime 2021/1/21 17:54
     *
     */
	private function getUsernameData($username)
	{
		//过滤data数据，并返回
		$data = array_filter($this->monthly, function ($key) use($username) {
			list($customreid,$productid,$source) = explode('_',$key);

            // 过滤 商务额外客户 产品
            if(isset($this->salesman_add_customer[$username][$customreid])) {
                $father_id = RedisCache::instance('productId_fatherId_mapping')->get($productid) ?: $productid;
                if(isset($this->salesman_add_customer[$username][$customreid][$father_id]) && in_array($source,$this->salesman_add_customer[$username][$customreid][$father_id])){
                    return true;
                }else {
                    return false;
                }
            }

			return  !$this->filterDataSource($username,$customreid,$source);
		},ARRAY_FILTER_USE_KEY);
		return $this->mergeSource($data);
	}
	/**
	 * 去掉用于数据过滤的来源字段
	 *
	 * @param [type] $data
	 * @return void
	 */
	public function mergeSource($data){
		$result = [];

		array_walk($data,function($item,$key)use(&$result){
			list($customer_id,$product_id,$source) = explode('_',$key);
			$unique_key = $customer_id . '_' . $product_id;
			if (!array_key_exists($unique_key, $result)) {
				$result[$unique_key]                = $this->monthlyBaseData;
				$result[$unique_key]['customer_id'] = $customer_id;
				$result[$unique_key]['product_id']  = $product_id;
				$result[$unique_key]['source']      = $source;
				$result[$unique_key]['father_id']   = $this->data[$key]['father_id'];
			}
			foreach ($item as $colkey => $value) {
				if(in_array($colkey,array_keys($this->monthlyBaseData))){
                    foreach($value as $month => $infos){
                        if(!isset($result[$unique_key][$colkey][$month])) {
                            $result[$unique_key][$colkey][$month] = 0;
                        }
                        foreach($infos as $v){
                            $result[$unique_key][$colkey][$month] = bcadd($result[$unique_key][$colkey][$month], $v, 6);
                        }
                    }
				}
			}
		});

		return $result;
	}
	/**
	 * 设置参数
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:12
	 *
	 * @return void
	 */
	private function setParams()
	{
		//校验是否发送报表
		$this->checkSend();

		//设置所需要的日期
		$date       = $this->option('date');
		$this->date = $date ?: date('Ymd', strtotime('-1 days'));
		if ($this->date > date('Ymd') || !preg_match('/^\d{8}$/', $this->date)) {
			throw new \Exception("日期格式不正确");
		}
		
		//给那个用户发送
		$username = $this->option('username');
		if ($username) {
			$this->username = explode(',', $username);
		} else {
			//获取需要接收邮件的所有用户
			$this->username = $this->getUsers();
		}


        $sendto = $this->option('sendto');
        if($sendto){
            $this->sendto = $sendto;
        }

        // 检查收入 成本 利润 显示权限 和客户来源权限
		$baseStatRepository = new StatBaseRepository();
		foreach ($this->username  as $key => $user) {
			$baseStatRepository->renderAuth($user);
			$this->financeAuthMap[$user] =$baseStatRepository->getFinanceAuthProduct();
			$this->customerSourceAuthMap[$user] = $baseStatRepository->getDataAndSourceAuthCustomerIdsForReport($user);

            if(key_exists($user,$this->salesman_add_customer)) {
                foreach(array_keys($this->salesman_add_customer[$user]) as $_customer_id) {
                    $this->customerSourceAuthMap[$user][$_customer_id] = [-1];
                }
            }
		}

		$this->month            = date('Ym', strtotime($this->date));
		$this->firstDateOfMonth = date('Ymd', strtotime('first day of this month', strtotime($this->date)));
		$this->firstDateOfYear  = date('Y', strtotime($this->date)) . '0101';

		//设置毛利及相关数据
		$this->setData();

		//数据校验
		$this->check();
		
		//设置客户的本年充值数据
		$this->setRecharge();
		
		//设置客户的备注数据
		$this->setRemarks();
		
		//设置大区所需的各种映射数据
		$this->setBusinessMappingInfo();
	}
	
	/**
	 * 校验是否发送
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/2/1 17:27
	 *
	 * @return void
	 */
	private function checkSend()
	{
		$isSend = Redis::connection('default')
					   ->get('is_send_profit');

		if (1 != $isSend) {
			throw new \Exception("根据后台设置，停止发送报表");
		}
	}
	
	/**
	 * 设置大区所需的各种映射数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 11:57
	 *
	 * @return void
	 */
	private function setBusinessMappingInfo()
	{
		//查询所有商务的子部门
        $dept_ids = DeptRepository::getSalesDept();

		//设置部门ID->部门名称的映射关系
		$this->deptId2DeptNameMapping = Dept::whereIn('dept_id', $dept_ids)
											->pluck('dept_name', 'dept_id')
											->toArray();
		//设置商务用户->商务部门ID的映射关系
		$this->salesman2DeptIdMapping = SystemUser::whereIn('dept_id', $dept_ids)
												  ->pluck('dept_id', 'username')
												  ->toArray();

		//设置商务用户->商务用户真实名称的映射关系
		$this->salesman2RealNameMapping = SystemUser::whereIn('dept_id', $dept_ids)
													->pluck('realname', 'username')
													->toArray();

		
		//设置客户ID->商务用户的映射关系
		// $this->customerId2SalesmanMapping = Customer::where('is_delete', 0)
		// 											->pluck('salesman', 'customer_id')
		// 											->toArray();

        		//设置客户ID->商务用户的映射关系
		$customer_ids = Customer::where('is_delete', 0)->get()->toArray();
        $customer_ids = array_column($customer_ids, 'customer_id');
        $cshr = new CustomerSalesmanHistoryRepository();
        $this->customerId2SalesmanMapping = $cshr->getListMonthly($customer_ids,date("Y-01"));
	}
	
	/**
	 * 获取全量需要发送的用户
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:36
	 *
	 * @return array
	 */
	private function getUsers()
	{
        $emailConfig = new EmailConfig();
        $users = $emailConfig->getRecipientsAndCCByScene($this->emailConfigScene);
        array_walk($users['recipients'], function(&$item){
            $item = substr($item['address'], 0, -11);
            return $item;
        });
        return $users['recipients'];
	}
	
	/**
	 * 毛利润校验
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:19
	 *
	 * @return void
	 */
	private function check()
	{
		//是否忽略监测
		if ($this->input->getOption('ignoreCheck')) {
			return;
		}
		//计算每个父产品的当日的权责营收、成本、毛利
		$result = [];
		array_walk($this->data, function ($item) use (&$result) {
			$father_id = $item['father_id'];
			if (!array_key_exists($father_id, $result)) {
				$result[$father_id] = 0;
			}
			$result[$father_id] = bcadd($result[$father_id], $item['this_date_profit'], 6);
		});
		
		//那些父产品必须不能<=0
		$ignoreCheckProductIds = Redis::connection('default')
									  ->get('ignoreCheckProductIds');
		$ignoreCheckProductIds = $ignoreCheckProductIds ? explode(',', $ignoreCheckProductIds) : [];
		//需要校验的产品ID

		$checkFatherIds = [210, 1000, 200, 615];// 20230103 去除501-邦信分私有云检查

		foreach ($checkFatherIds as $fatherId) {
			//如果redis中存在设置的不检验产品ID，则不检验
			if (in_array($fatherId, $ignoreCheckProductIds)) {
				continue;
			}
			if (array_get($result, $fatherId, 0) <= 0) {
				throw new \Exception("{$fatherId}产品净利润不大于0，终止权责报表发送");
			}
		}
		
		//利润之和不得<=0
		if (array_sum($result) <= 0 && empty($ignoreCheckProductIds)) {
			throw new \Exception("总净利润不大于0，终止权责报表发送");
		}
	}
	
	/**
	 * 数据校验失败，不发送报表账单
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/27 18:25
	 *
	 * @param $product_id int 那个产品的数据校验失败
	 *
	 * @return void
	 */
	private function isCheckError($product_id = 0)
	{
		if ($product_id) {
			$message = "[{$product_id}]产品净利润不大于0，暂停权责报表发送";
		} else {
			$message = "总净利润不大于0，暂停权责报表发送";
		}
		
		
		throw new \Exception($message);
	}
	
	
	/**
	 * 设置每个客户的当前的备注信息
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:00
	 *
	 * @return void
	 */
	private function setRemarks()
	{

		/*$this->remarks = BillNotes::where('cday', date('Y-m-d', strtotime($this->date)))
								  ->pluck('notes', 'customer_id')
								  ->toArray();*/

        $this->remarks = [];
	}
	
	/**
	 * 设置每个客户的本年充值数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 16:42
	 *
	 * @return void
	 */
	private function setRecharge()
	{
        $fields = 'r.customer_id as customer_id,sum(rsp.money) as money,rsp.product_id as product_id,r.source';
		$data = DB::table('remit_split_price as rsp')
		->leftJoin('remit as r', 'r.receipt_serial', '=', 'rsp.receipt_serial')
		->selectRaw($fields)
		->where('r.status', 3)
		->where('r.remit_date', '>=', strtotime($this->firstDateOfYear))
		->where('r.remit_date', '<=', strtotime($this->date))
		->groupBy(['r.customer_id','rsp.product_id','r.source'])
		->get()->toArray();
		// $data = MoneyRecharge::select(DB::raw('SUM(`money`) as money'), 'customer_id','source')
		// 					 ->where('status', 3)
		// 					 ->where('remit_date', '>=', strtotime($this->firstDateOfYear))
		// 					 ->where('remit_date', '<=', strtotime($this->date))
		// 					 ->groupBy('customer_id','source')
		// 					 ->get()
		// 					 ->toArray();
		array_walk($data,function($item){
			$uniquekey = $item['customer_id'].'_'.$item['product_id'].'_'.$item['source'];
			$this->recharge[$uniquekey ] = $item['money'];
		});
	}
	
	/**
	 * 设置data参数，data参数中，存储的是每个客户、每个子产品的日、月、年的收入、成本、毛利数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 16:30
	 *
	 * @return void
	 */
	private function setData()
	{
		/**
		 * 获取不同维度【时间维度（当日、当月、当年）、客户维度、子产品维度】的营收数据（包含特殊消费、特殊赠送）
		 * 并将获取到的数据按一定的格式填充到$this->data中
		 */
		$this->fillIncome();
		
		/**
		 * 获取不同维度【时间维度（当日、当月、当年）、客户维度、子产品维度】的成本数据（包含特殊成本费用）
		 * 并将获取到的数据按一定的格式填充到$this->data中
		 */
		$this->fillCost();
		
		/**
		 * 计算不同维度【时间维度（当日、当月、当年）、客户维度、子产品维度】的毛利数据
		 */
		array_walk($this->data, [$this, 'calculateItemProfit']);
	}
	
	/**
	 * 对每一条数据求年、月、日的利润数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 14:16
	 *
	 * @return void
	 */
	private function calculateItemProfit(&$data)
	{
		//日利润
		$data['this_date_profit'] = bcsub($data['this_date_income_money'], $data['this_date_cost_money'], 6);
		
		//月利润
		$data['this_month_profit'] = bcsub($data['this_month_income_money'], $data['this_month_cost_money'], 6);
		
		//年利润
		$data['this_year_profit'] = bcsub($data['this_year_income_money'], $data['this_year_cost_money'], 6);
		
		//补充一个父产品ID
		$data['father_id'] = RedisCache::instance('productId_fatherId_mapping')
									   ->get($data['product_id']) ?: $data['product_id'];
		
	}
	
	/**
	 * 填充成本数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 14:57
	 *
	 * @return void
	 */
	private function fillCost()
	{
		//填充本年的成本统计数据
		$this->fillThisYearCost();
		
		//填充本月的成本统计数据
		$this->fillThisMonthCost();
		
		//填充当日的成本统计数据
		$this->fillThisDateCost();
	}
	
	/**
	 * 填充本年的成本统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisYearCost()
	{
		//填充本年的成本统计数据，并将其保存在容器中
		$this->getBillCostMonthly($this->firstDateOfYear, $this->date)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
                 $month       = $item->month;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_year_cost_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_year_cost_money', $customer_id, $product_id, $money, $month, $item->source);
			 });
		
		//填充本年的客户成本数据，并将其保存在容器中
		$this->getSpecialCostMonthly($this->firstDateOfYear, $this->date)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			     $month       = $item->month;
				 //填充量和钱
				 $this->fillItem('this_year_cost_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_year_cost_money', $customer_id, $product_id, $money, $month, $item->source);
			 });

		// 填充本年的渠道成本数据，并将其保存在容器中
        $this->getSpecialCostChannelMonthly($this->firstDateOfYear, $this->date)
            ->each(function ($item) {
                $customer_id = $item->customer_id;
                $product_id = $item->product_id;
                $number = $item->number;
                $money = $item->money;
                $month = $item->month;

                // 填充量和钱
                $this->fillItem('this_year_cost_number', $customer_id, $product_id, $number,$month,0);
                $this->fillItem('this_year_cost_money', $customer_id, $product_id, $money,$month,0);
            });

        // 填充本年的固定费用成本数据，并将其保存在容器中
        $this->getSpecialCostFixedMonthly($this->firstDateOfYear, $this->date)
            ->each(function ($item) {
                $apikey = $item->apikey;
                $product_id = $item->product_id;
                $money = $item->money;
                $month = $item->month;
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);

                // 填充钱没有量
                $this->fillItem('this_year_cost_money', $customer_id, $product_id, $money,$month,0);
            });
	}
	
	/**
	 * 填充本月的成本统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisMonthCost()
	{
		//填充本月的成本统计数据，并将其保存在容器中
		$this->getBillCostMonthly($this->firstDateOfMonth, $this->date)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
				 $month       = $item->month;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')
										  ->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_month_cost_number', $customer_id, $product_id, $number,$month,$item->source);
				 $this->fillItem('this_month_cost_money', $customer_id, $product_id, $money,$month,$item->source);
			 });
		
		//填充本月的客户成本数据，并将其保存在容器中
		$this->getSpecialCostMonthly($this->firstDateOfMonth, $this->date)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			     $month       = $item->month;
				 //填充量和钱
				 $this->fillItem('this_month_cost_number', $customer_id, $product_id, $number,$month,$item->source);
				 $this->fillItem('this_month_cost_money', $customer_id, $product_id, $money,$month,$item->source);
			 });

		// 填充本月的渠道成本数据，并将其保存在容器中
        $this->getSpecialCostChannelMonthly($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $customer_id = $item->customer_id;
                $product_id = $item->product_id;
                $number = $item->number;
                $money = $item->money;
                $month = $item->month;
                // 填充量和钱
                $this->fillItem('this_month_cost_number', $customer_id, $product_id, $number,$month,$item->source);
                $this->fillItem('this_month_cost_money', $customer_id, $product_id, $money,$month,$item->source);
            });

        // 填充本月的固定费用成本数据，并将其保存在容器中
        $this->getSpecialCostFixedMonthly($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $apikey = $item->apikey;
                $product_id = $item->product_id;
                $money = $item->money;
                $month = $item->month;
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);

                // 填充钱没有量
                $this->fillItem('this_month_cost_money', $customer_id, $product_id, $money,$month,0);
            });
	}
	
	/**
	 * 填充当日的成本统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisDateCost()
	{
		//填充当日的成本统计数据，并将其保存在容器中
		$this->getBillCostMonthly($this->date, $this->date)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
			     $month = $item->month;
				 //填充量和钱
				 $this->fillItem('this_date_cost_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_date_cost_money', $customer_id, $product_id, $money, $month, $item->source);
			 });
		
		//填充当日的客户成本数据，并将其保存在容器中
		$this->getSpecialCostMonthly($this->date, $this->date)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			     $month = $item->month;
				 //填充量和钱
				 $this->fillItem('this_date_cost_number', $customer_id, $product_id, $number, $month,$item->source);
				 $this->fillItem('this_date_cost_money', $customer_id, $product_id, $money, $month,$item->source);
			 });

		// 填充当日的渠道成本数据，并将其保存在容器中
        $this->getSpecialCostChannelMonthly($this->date, $this->date)
            ->each(function ($item) {
                $customer_id = $item->customer_id;
                $product_id = $item->product_id;
                $number = $item->number;
                $money = $item->money;
                $month = $item->month;
                // 填充量和钱
                $this->fillItem('this_date_cost_number', $customer_id, $product_id, $number,$month,$item->source);
                $this->fillItem('this_date_cost_money', $customer_id, $product_id, $money,$month,$item->source);
            });

        // 填充当日的固定费用成本数据，并将其保存在容器中
        $this->getSpecialCostFixedMonthly($this->date, $this->date)
            ->each(function ($item) {
                $apikey = $item->apikey;
                $product_id = $item->product_id;
                $money = $item->money;
                $month = $item->month;
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);

                // 填充钱没有量
                $this->fillItem('this_date_cost_money', $customer_id, $product_id, $money,$month,0);
            });
	}
	
	/**
	 * 填充营收数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:07
	 *
	 * @return void
	 */
	private function fillIncome()
	{
		//填充本年的营收统计数据
		$this->fillThisYearIncome();

		//填充本月的
		$this->fillThisMonthIncome();

		//填充当日的
		$this->fillThisDateIncome();

		//企服企业减半收入
		$this->formatQifuIncome();
	}
	
	/**
	 * 填充本年的营收统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisYearIncome()
	{
		//填充本年的营收账单数据，并将其保存在容器中
		$this->getBillIncomeMonthly($this->firstDateOfYear, $this->date)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
				 $month       = $item->month;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_year_income_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_year_income_money', $customer_id, $product_id, $money,$month, $item->source);
			 });

		//填充本年的特殊营收数据，并将其保存在容器中
		$this->getSpecialIncomeMonthly($this->firstDateOfYear, $this->date)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			     $month       = $item->month;
				 //填充量和钱
				 $this->fillItem('this_year_income_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_year_income_money', $customer_id, $product_id, $money,$month, $item->source);
			 });
	}
	
	/**
	 * 填充本月的营收统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisMonthIncome()
	{
		//填充本月的营收账单数据，并将其保存在容器中
		$this->getBillIncomeMonthly($this->firstDateOfMonth, $this->date)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
                 $month       = $item->month;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_month_income_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_month_income_money', $customer_id, $product_id, $money,$month, $item->source);
			 });
		
		//填充本月的特殊营收数据，并将其保存在容器中
		$this->getSpecialIncomeMonthly($this->firstDateOfMonth, $this->date)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			     $month       = $item->month;
				 //填充量和钱
				 $this->fillItem('this_month_income_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_month_income_money', $customer_id, $product_id, $money,$month, $item->source);
			 });
	}
	
	/**
	 * 填充当日的营收统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisDateIncome()
	{
		//填充当日的营收账单数据，并将其保存在容器中
		$this->getBillIncomeMonthly($this->date, $this->date)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
                 $month       = $item->month;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);

				 //填充量和钱
				 $this->fillItem('this_date_income_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_date_income_money', $customer_id, $product_id, $money,$month, $item->source);
			 });
		
		//填充当日的特殊营收数据，并将其保存在容器中
		$this->getSpecialIncomeMonthly($this->date, $this->date)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			     $month       = $item->month;
				 //填充量和钱
				 $this->fillItem('this_date_income_number', $customer_id, $product_id, $number, $month, $item->source);
				 $this->fillItem('this_date_income_money', $customer_id, $product_id, $money,$month, $item->source);
			 });
	}
	
	/**
	 * 获取营收账单数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:08
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
    private function getBillIncomeMonthly($start_date, $end_date){
		return BillProductIncomeV2::select([
			'apikey',
			'product_id',
			'source',
            DB::raw("DATE_FORMAT(`date`,'%Y-%m') AS `month`"),
			DB::raw('SUM(`money`) as money'),
			DB::raw('SUM(`number`) as number'),
		])
								->where('date', '>=', $start_date)
								->where('date', '<=', $end_date)
								->groupBy('apikey', 'product_id','source','month')
								->get();
	}

	/**
	 * 获取成本账单数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 14:58
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getBillCostMonthly($start_date, $end_date)
	{
		return BillCostV2::select([
			'apikey',
			'product_id',
			'source',
            DB::raw("DATE_FORMAT(`date`,'%Y-%m') AS `month`"),
			DB::raw('SUM(`money`) as money'),
			DB::raw('SUM(`number`) as number'),
		])
					   ->where('date', '>=', $start_date)
					   ->where('date', '<=', $end_date)
					   ->groupBy('apikey', 'product_id', 'source','month')
					   ->get();
	}
	
	/**
	 * 获取特殊营收数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:09
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
    private function getSpecialIncomeMonthly($start_date, $end_date)
	{
		return CustomerExpend::select([
			'customer_id',
			'product_id',
			'type',
			'source',
            DB::raw("DATE_FORMAT(`profile_show_date`,'%Y-%m') AS `month`"),
			DB::raw('SUM( `money` ) as money'),
			DB::raw('SUM(`fee_number`) as number'),
		])
							 ->where('profile_show_date', '>=', date('Y-m-d', strtotime($start_date)))
							 ->where('profile_show_date', '<=', date('Y-m-d', strtotime($end_date)))
							 ->groupBy('customer_id', 'product_id', 'type', 'month','source')
							 ->get()
							 ->map(function ($item) {
								 $type = $item->type;
								 if ($type == 1) {
									 $item->money  = bcsub(0, $item->money, 6);
									 $item->number = bcsub(0, $item->number, 0);
								 }

								 return $item;
							 });
	}
	
	/**
	 * 获取特殊成本数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:09
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getSpecialCostMonthly($start_date, $end_date){
        return CustomerBillAdjust::getCustomerProductCostMonthly($start_date, $end_date);
	}


	private function getSpecialCostChannelMonthly($start_date, $end_date)
    {
        return ChannelAccountAdjust::getCustomerProductCostMontyly($start_date, $end_date);
    }

    private function getSpecialCostFixedMonthly($start_date, $end_date)
    {
        return ChannelAccountFixedFee::getCustomerProductCostMonthly($start_date, $end_date);
    }
	
	/**
	 * 将最小颗粒的数据填充到基础的数据中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:58
	 *
	 * @param $key         string 数据的key
	 * @param $customer_id string 客户ID
	 * @param $product_id  string 产品ID
	 * @param $value       integer|float 值
	 * @param $source	    int 来源
	 * @return void
	 */
	private function fillItem($key, $customer_id, $product_id, $value, $month ,$source=null)
	{
		$unique_key = $customer_id . '_' . $product_id . '_' . $source;
		
		if (!array_key_exists($unique_key, $this->data)) {
			$this->data[$unique_key]                = $this->baseData;
			$this->data[$unique_key]['customer_id'] = $customer_id;
			$this->data[$unique_key]['product_id']  = $product_id;
			$this->data[$unique_key]['source']      = $source;
		}
		
		if (!array_key_exists($key, $this->data[$unique_key])) {
			throw new \Exception("暂不支持填充{$key}数据");
		}
		
		$this->data[$unique_key][$key] = bcadd($this->data[$unique_key][$key], $value, 6);
		$this->monthly[$unique_key][$key][$month][] = $value;
	}

	// 企服收入减半
	private function formatQifuIncome(){
		$deductCol = ['this_date_income_money','this_month_income_money','this_year_income_money'];
		foreach ($this->data as $unique_key => $item) {
			list($customer_id,$product_id) = explode('_',$unique_key);
			$customerType = RedisCache::instance('customerId_customerType_mapping')->get($customer_id);
			if($customerType==2){// 企服企业收入减半
				foreach ($item as $key => $value) {
					if(in_array($key,$deductCol)){
						$this->data[$unique_key][$key] = bcdiv($value,2,6);
					}
				}
			}
		}

        foreach ($this->monthly as $unique_key => &$item) {
			list($customer_id,$product_id) = explode('_',$unique_key);
			$customerType = RedisCache::instance('customerId_customerType_mapping')->get($customer_id);
			if($customerType==2){// 企服企业收入减半
				foreach ($item as $key => &$value) {
					if(in_array($key,$deductCol)){
                        foreach($value as &$vs){
                            if(!is_array($vs)){
                                continue;
                            }
                            foreach($vs as &$v) {
                                $v = bcdiv($v, 2, 6);
                            }
                        }
					}
				}
			}
		}
	}
	
	/**
	 * 对量格式化
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:11
	 *
	 * @param $number float 数值
	 *
	 * @return integer
	 */
	private function formatNumber($number)
	{
		return number_format($number, 0);
	}
	
	/**
	 * 对金额格式化
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:12
	 *
	 * @param $money float 金额
	 *
	 * @return integer
	 */
	private function formatMoney($money)
	{
		return number_format(round($money, 0), 0);
	}


	// 按照客户及客户来源过滤数据
	public function filterDataSource($username,$customer_id,$source){
		$auth = $this->customerSourceAuthMap[$username];

		if(!isset($auth[$customer_id])){
			return true;// 被过滤掉
		}
		if(in_array(-1,$auth[$customer_id])||in_array($source,$auth[$customer_id])){
			return false;// 有权限，不用过滤
		}else{
			return true;// 无权限，被过滤掉
		}
	}
}