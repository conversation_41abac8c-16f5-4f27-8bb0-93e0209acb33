<?php

namespace App\Console\Commands\ReportDay;


use App\Define\ProductCategory;
use App\Define\StatDefine;
use App\Http\Repository\ReportDay\StatRepository;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\Category;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\ReportDay\StatisticsSummaryDailyKeyProductIncome;
use App\Models\ReportDay\StatisticsSummaryDailyMonthIncome;
use App\Models\ReportDay\StatisticsSummaryDailyProdcutCategory;
use App\Models\ReportDay\StatisticsSummaryDailySnapshotData;
use App\Models\StatisticsCustomerUsage;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Income\CustomerIncomeRepository;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;


class CreateProfitDataForLeader extends Command
{
    protected $signature   = "create_profit_data_for_leader
	{--date= : 日期, 格式Ymd，默认昨日}
	";
    protected $description = '收入、成本、利润日报基础数据生成（领导版）';


    //定义一个产品所属类别字典 product_category表定义
    private $category = [];

    //定义一个所属类别下要展示的产品名称
    //getCategoryProductByFatherId($father_id) 方法中定义了父产品 和 展示产品的关系

    private $showProductName = [];

    //定义一个类别下展示名称 和 类别的关系
    private $showProductNameAndCategory = [];

    private $productCategoryMap = [];

    private $showProductName200 = [];
    private $showProductName50000 = [];
    private $showProductName3100 = [];
    private $showProductName30000 = [];
    private $convertFatherId = [];//需要转换的父产品id

    //定义一个季度字典
    private $quarterName= [
        1 => '第一季度',
        2 => '第二季度',
        3 => '第三季度',
        4 => '第四季度',
    ];

    //月度产品统计表 如下父产品不需要合并为其他
    private $notMergeToOther = [
        '200',//帮秒验
        '210',//邦信分-通讯指数
        '1000',//邦信分-通讯评分
        '615',//号码风险等级
        '10000',// 号码分
        //'20000',//贷后风险指数
        '3100',//企服产品
        '30000',//存量洞察
    ];

    /**
     * @var string 羽乐科技客户ID (后台统计是排除了羽乐科技内部和售前测试,邮件这里只排除羽乐科技内部)
     */
    private $yuloreCustomerId = 'C20180828LOCNMG';

    /**
     * @var array 计算总调用量的时候需要排除这几个产品
     */
    private $totalNotInProductIds = [];

    /**
     * @var string[] 需要特殊计算的apikey
     */
    private $specialCostApikey = [];//['4d2050c36b56dde19c04ccd08a0b7478'];
    /**
     * @var int 特殊apikey记入的子产品
     */
    private $specialCostPid = 661;

    private $customerIncomeRepository = null;

    /**
     * @var integer 日期
     */
    protected $date;//当前执行日期 如20211220
    protected $month;//当前执行月份 如202112
    protected $firstDateOfMonth;//当前执行月份起始日期 如20211201
    protected $firstDateOfYear;//当前执行年份起始日期 如20210101

    /**
     * @var array 数据
     */
    protected $data = [
        'date'  => [],
        'month' => [],
        'year'  => [],//这个年数据 里面是该年按月分组的
        'year_product'  => [],//这个年数据 里面是该年按产品分组的
    ];

    protected $financeAuthMap = [];

    // 存储当前发送用户的权限
    protected $financeAuth = [];

    //要统计的重点产品
    protected $keyProductMap = [
//            ['father_id' => 210, 'product_ids' => [280], 'show_name' => '邦信分'],
            ['father_id' => 30000, 'product_ids' => [1200], 'show_name' => '邦运营'],
            ['father_id' => 30000, 'product_ids' => [39001,39002,39003], 'show_name' => 'AI创新'],
            ['father_id' => 70000, 'product_ids' => [], 'show_name' => '号码融'],
        ];

    protected $mainRep = null;
    
    //脚本执行
    public function handle()
    {
        try {
            //设置参数
            $this->setParams();

            //查询本年的产品维度的数据
            $this->setData();

            //生成产品分类日数据 createFirstTableInfo
            $data = $this->createProductCatIncome();
            unset($this->data['date']);
            $this->insertDailyProdcutCategoryData($data);

            //生成收入成本等指标数据按月份 createFourthlyTableInfo
            $data = $this->createIncomeByMonth();
            unset($this->data['year']);
            $this->insertDailyMonthIncomeData($data);

            //生成重点产品top3客户数据
            $this->createKeyProductIncome();

            //生成客户日收入增长或减少top n变化率数据(目前为top3)
            $this->createCustomerIncomeChangeRate();

            //生成昨日、去年同月、去年同期收入、毛利数据用于计算今日、当月、当年的增长量和增长率
            $this->createCompareDateIncomeData();

            //生成客户top10日、top10年收入数据
            $this->createCustomerTop10IncomeData();

            $this->output->success("领导日报基础数据生成成功");

        } catch (Exception $exception) {
            //echo $exception->getFile(),": ",$exception->getLine(),PHP_EOL,$exception->getMessage(),PHP_EOL;exit;
            sendCommandExceptionNotice($this, $exception);
        }
    }

    public function insertDailyMonthIncomeData($data){
        //先删除数据
        StatisticsSummaryDailyMonthIncome::where('date', $this->date)
            ->delete();

        $add = [];
        $create_time = time();
        foreach ($data as $item){
            if(!isset($item['data']) || empty($item['data'])){
                continue;
            }

            $q_name = $item['name'];
            foreach ($item['data'] as $month_info){
                $add[] = [
                    'q_name' => $q_name,
                    'total' => $month_info['total'],
                    'bill_number' => $month_info['bill_number'],
                    'income' => $month_info['income'],
                    'cost' => $month_info['cost'],
                    'profit' => $month_info['profit'],
                    'month' => $month_info['name'],
                    'date' => $this->date,
                    'create_time' => $create_time
                ];
            }
        }

        StatisticsSummaryDailyMonthIncome::insert($add);

        return true;
    }

    public function insertDailyProdcutCategoryData($data){
        //先删除数据
        StatisticsSummaryDailyProdcutCategory::where('date', $this->date)
            ->delete();

        $add = [];
        $create_time = time();
        foreach ($data as $item){
            if(!isset($item['data']) || empty($item['data'])){
                continue;
            }

            $category_name = $item['name'];
            foreach ($item['data'] as $product_cat_info){
                $add[] = [
                    'category_name' => $category_name,
                    'show_prodcut_name' => $product_cat_info['name'],
                    'total' => $product_cat_info['total'],
                    'bill_number' => $product_cat_info['bill_number'],
                    'income' => $product_cat_info['income'],
                    'cost' => $product_cat_info['cost'],
                    'profit' => $product_cat_info['profit'],
                    'year_income' => $product_cat_info['year_income'],
                    'year_cost' => $product_cat_info['year_cost'],
                    'year_profit' => $product_cat_info['year_profit'],
                    'date' => $this->date,
                    'create_time' => $create_time
                ];
            }
        }

        StatisticsSummaryDailyProdcutCategory::insert($add);

        return true;
    }



    public function createKeyProductIncome(){
        //先删除数据
        StatisticsSummaryDailyKeyProductIncome::where('date', $this->date)
            ->delete();

        $insert = [];

        foreach ($this->keyProductMap as $item){
            //获取日数据 按日收入排序前三的客户
            $dayData = $this->getTop3CustomerByIncome($item);
            $top3CustomerIds = array_keys($dayData);
            //根据日数据获取的客户 获取月数据
            $monthData = $this->getTop3CustomerByIncome(array_merge($item, ['customer_ids' => $top3CustomerIds]), 'month');

            $add = $this->formatKeyProductData($item['show_name'], $dayData, $monthData);
            $insert = array_merge($insert, $add);
        }

        if(!empty($insert)){
            StatisticsSummaryDailyKeyProductIncome::insert($insert);
        }

        return;
    }

    public function createCustomerIncomeChangeRate(){
        //先删除数据
        $where = ['date' => $this->date, 'type' => StatisticsSummaryDailySnapshotData::TYPE_CUSTOMER_INCOME_CHANGE_RATE];
        StatisticsSummaryDailySnapshotData::where($where)
            ->delete();

        //获取统计日客户收入列表
        $dimension = StatDefine::INCOME_DIMENSION_DATE_CUSTOMER;
        $rep = new MainRepository();
        $current_res = $rep->getBaseIncome($dimension, $this->date, $this->date, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' =>1]);
        if(!isset($current_res['status']) || $current_res['status'] !=0){
            return;
        }

        //根据统计日推算出比对日
        $pre_date = $this->getCompareDate();

        //获取比对日客户收入列表
        $pre_res = $rep->getBaseIncome($dimension, $pre_date, $pre_date, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' =>1]);
        if(!isset($pre_res['status']) || $pre_res['status'] !=0){
            return;
        }

        //格式化数据
        $all_data = [];
        $all_customer_ids = [];//当前日和比对日全量客户
        foreach ($current_res['data'] as $item){
            $date = $item['date'];
            $customer_id = $item['customer_id'];
            $income = $item['income'];
            $all_data[$date][$customer_id] = $income;
            if(!in_array($customer_id, $all_customer_ids)){
                $all_customer_ids[] = $customer_id;
            }
        }
        unset($current_res);

        foreach ($pre_res['data'] as $item){
            $date = $item['date'];
            $customer_id = $item['customer_id'];
            $income = $item['income'];
            $all_data[$date][$customer_id] = $income;
            if(!in_array($customer_id, $all_customer_ids)){
                $all_customer_ids[] = $customer_id;
            }
        }
        unset($pre_res);

        $diff_array = [];
        foreach ($all_customer_ids as $customer_id){

            $current_customer_income = $all_data[$this->date][$customer_id] ?? 0;//当前日客户收入
            $pre_customer_income = $all_data[$pre_date][$customer_id] ?? 0;//比对日客户收入
            $up_income = bcsub($current_customer_income, $pre_customer_income, 2);
            //$up_income = bcdiv($up_income, 10000, 2);
            $diff_array[$customer_id] = $up_income;
        }
        unset($all_customer_ids);

        $result = $this->findTopNCustomer($diff_array);
        $add = [];
        $nowtime = time();
        foreach ($result['up_topn'] as $item){
            $customer_id = $item['customer_id'];
            $current_customer_income = $all_data[$this->date][$customer_id] ?? 0;//当前日客户收入
            $pre_customer_income = $all_data[$pre_date][$customer_id] ?? 0;//比对日客户收入

            $diff_money = bcsub($current_customer_income, $pre_customer_income, 2);
            if($pre_customer_income <= 0){
                $ratio = 0;
            }else{
                $ratio = bcdiv($diff_money, $pre_customer_income, 4);
                $ratio = bcmul($ratio, 100, 2);
            }

            $add[] = [
                'customer_id' => $customer_id,
                'income' => $current_customer_income,//当前日客户收入
                'extend1' => $pre_customer_income,//比对日客户收入
                'extend2' => $ratio,//变化率
                'extend3' => 'up_topn',//增长或减少标识
                'date' => $this->date,//当前统计日
                'type' => StatisticsSummaryDailySnapshotData::TYPE_CUSTOMER_INCOME_CHANGE_RATE,
                'create_time' => $nowtime
            ];
        }

        foreach ($result['down_topn'] as $item){
            $customer_id = $item['customer_id'];
            $current_customer_income = $all_data[$this->date][$customer_id] ?? 0;//当前日客户收入
            $pre_customer_income = $all_data[$pre_date][$customer_id] ?? 0;//比对日客户收入

            $diff_money = bcsub($current_customer_income, $pre_customer_income, 2);
            if($pre_customer_income<=0){
                $ratio = 0;
            }else{
                $ratio = bcdiv($diff_money, $pre_customer_income, 4);
                $ratio = bcmul($ratio, 100, 2);
            }

            $add[] = [
                'customer_id' => $customer_id,
                'income' => $current_customer_income,//当前日客户收入
                'extend1' => $pre_customer_income,//比对日客户收入
                'extend2' => $ratio,//变化率
                'extend3' => 'down_topn',//增长或减少标识
                'date' => $this->date,//当前统计日
                'type' => StatisticsSummaryDailySnapshotData::TYPE_CUSTOMER_INCOME_CHANGE_RATE,
                'create_time' => $nowtime
            ];

        }

        if(!empty($add)){
            StatisticsSummaryDailySnapshotData::insert($add);
        }

    }

    public function createCustomerTop10IncomeData(){
        //先删除数据
        $where = ['date' => $this->date, 'type' => StatisticsSummaryDailySnapshotData::TYPE_CUSTOMER_TOP10_INCOME];
        StatisticsSummaryDailySnapshotData::where($where)->delete();

        if(is_null($this->mainRep)){
            $this->mainRep = new MainRepository();
        }

        $current_year = substr($this->date, 0, 4);//当前年
        //获取当前日top10客户收入
        $top10_day_list = $this->getCustomerTop10Income($this->date, $this->date, 'day');

        //获取当前年top10客户收入
        $top10_year_list = $this->getCustomerTop10Income($current_year.'0101', $this->date, 'year');

        $add = [];
        $nowtime = time();
        foreach ($top10_day_list as $key => $item){
            $day_customer_id = $item['day_customer_id'];//日top10客户
            $day_customer_income = $item['day_customer_income'];//日top10客户收入
            $day_customer_ratio = $item['day_customer_ratio'];//日top10客户收入占比

            $year_customer_id = $top10_year_list[$key]['year_customer_id'];//年top10客户
            $year_customer_income = $top10_year_list[$key]['year_customer_income'];//年top10客户收入
            $year_customer_ratio = $top10_year_list[$key]['year_customer_ratio'];//年top10客户收入占比
            $add[] = [
                'customer_id' => $day_customer_id,
                'income' => $day_customer_income,
                'extend1' => $day_customer_ratio,
                'extend2' => $year_customer_id,
                'year_income' => $year_customer_income,
                'extend3' => $year_customer_ratio,
                'date' => $this->date,//当前统计日
                'type' => StatisticsSummaryDailySnapshotData::TYPE_CUSTOMER_TOP10_INCOME,
                'create_time' => $nowtime
            ];
        }

        if(!empty($add)){
            StatisticsSummaryDailySnapshotData::insert($add);
        }

    }

    public function getCustomerTop10Income($start_date, $end_date, $type = 'day'){
        $dimension = StatDefine::INCOME_DIMENSION_CUSTOMER;
        $res = $this->mainRep->getBaseIncome($dimension, $start_date, $end_date, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' =>1]);
        //总收入
        $total_income = array_sum(array_column($res['data'], 'income'));
        //前10客户收入
        array_multisort(array_column($res['data'], 'income'), SORT_DESC, $res['data']);
        $top_n_customer = array_slice($res['data'], 0, 10);
        unset($res);
        $top_n_total_income = array_sum(array_column($top_n_customer, 'income'));

        $result = [];
        foreach ($top_n_customer as $item){
            $customer_id = $item['customer_id'];
            $income = $item['income'];

            //客户收入在日或年总收入中的占比
            $ratio = bcdiv($income, $total_income, 4);
            $ratio = bcmul($ratio, 100, 1).'%';

            $result[] = [
                    $type.'_customer_id' => $customer_id,
                    $type.'_customer_income' => $income,
                    $type.'_customer_ratio' => $ratio,
                ];
        }

        unset($top_n_customer);
        //前10客户总的占比情况
        $top_n_ratio = bcdiv($top_n_total_income, $total_income, 4);
        $top_n_ratio = bcmul($top_n_ratio, 100, 1).'%';
        $last = [
            $type.'_customer_id' => '合计',
            $type.'_customer_income' => $top_n_total_income,
            $type.'_customer_ratio' => $top_n_ratio,
        ];

        array_unshift($result, $last);

        return $result;
    }

    public function createCompareDateIncomeData(){
        //先删除数据
        $where = ['date' => $this->date];
        $whereType = [
            StatisticsSummaryDailySnapshotData::TYPE_YESTERERDAY_INCOME,
            StatisticsSummaryDailySnapshotData::TYPE_COMPAREMONTH_INCOME,
            StatisticsSummaryDailySnapshotData::TYPE_COMPAREYEAR_INCOME
        ];

        StatisticsSummaryDailySnapshotData::where($where)
            ->whereIn('type', $whereType)
            ->delete();

        //生成昨日(比较日)收入、成本数据
        $this->createCompareDayData();

        //获取比较的月、年的开始和结束日期范围
        $dateRange = $this->getCompareDateRange();
        //生成比较月(上年同月)收入、成本数据
        $this->createCompareMonthData($dateRange['compare_start_month_date'], $dateRange['compare_end_month_date']);
        //生成比较年(上年同日期范围)收入、成本数据
        $this->createCompareYearData($dateRange['compare_start_year_date'], $dateRange['compare_end_year_date']);

    }

    public function createCompareDayData(){
        //根据统计日推算出比对日(所谓的上一日或昨日 叫比对日更合适)
        $yesterday = $this->getCompareDate();
        $dimension = StatDefine::DIMENSION_HUIZONG;
        if(is_null($this->mainRep)){
            $this->mainRep = new MainRepository();
        }
        //$rep = new MainRepository();
        $current_res = $this->mainRep->getBaseIncome($dimension, $yesterday, $yesterday, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' =>1]);
        if(!isset($current_res['status']) || $current_res['status'] !=0){
            return;
        }
        $income = $current_res['data']['income'];

        //$rep = new MainRepository();
        $current_res = $this->mainRep->getBaseCost($dimension, $yesterday, $yesterday, [], [], [], [], ['is_query_month_data' => 0]);
        if(!isset($current_res['status']) || $current_res['status'] !=0){
            return;
        }
        $cost = $current_res['data']['cost'];

        $profit = bcsub($income, $cost, 6);

        $nowtime = time();
        $add = [

            'income' => $income,//昨日收入
            'cost' => $cost,//昨日成本
            'profit' => $profit,//昨日毛利
            'extend1' => $yesterday,//昨日日期(比较日期)
            'date' => $this->date,//当前统计日
            'type' => StatisticsSummaryDailySnapshotData::TYPE_YESTERERDAY_INCOME,
            'create_time' => $nowtime
        ];

        StatisticsSummaryDailySnapshotData::insert($add);
    }

    public function createCompareMonthData($start_date, $end_date){
        $dimension = StatDefine::DIMENSION_HUIZONG;
        if(is_null($this->mainRep)){
            $this->mainRep = new MainRepository();
        }
        //$rep = new MainRepository();
        $current_res = $this->mainRep->getBaseIncome($dimension, $start_date, $end_date, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' =>1]);
        if(!isset($current_res['status']) || $current_res['status'] !=0){
            return;
        }
        $income = $current_res['data']['income'];

        $current_res = $this->mainRep->getBaseCost($dimension, $start_date, $end_date, [], [], [], [], ['is_query_month_data' => 0]);
        if(!isset($current_res['status']) || $current_res['status'] !=0){
            return;
        }
        $cost = $current_res['data']['cost'];

        $profit = bcsub($income, $cost, 6);

        $nowtime = time();
        $add = [

            'month_income' => $income,//比较月(上年同月)收入
            'month_cost' => $cost,//比较月(上年同月)成本
            'month_profit' => $profit,//比较月(上年同月)毛利
            'extend1' => $start_date,//比较开始日期
            'extend2' => $end_date,//比较开始日期
            'date' => $this->date,//当前统计日
            'type' => StatisticsSummaryDailySnapshotData::TYPE_COMPAREMONTH_INCOME,
            'create_time' => $nowtime
        ];

        StatisticsSummaryDailySnapshotData::insert($add);
    }

    public function createCompareYearData($start_date, $end_date){
        $dimension = StatDefine::DIMENSION_HUIZONG;
        if(is_null($this->mainRep)){
            $this->mainRep = new MainRepository();
        }
        //$rep = new MainRepository();
        $current_res = $this->mainRep->getBaseIncome($dimension, $start_date, $end_date, [], [], [], [], ['is_query_month_data' => 0, 'is_halve_income' =>1]);
        if(!isset($current_res['status']) || $current_res['status'] !=0){
            return;
        }
        $income = $current_res['data']['income'];

        $current_res = $this->mainRep->getBaseCost($dimension, $start_date, $end_date, [], [], [], [], ['is_query_month_data' => 0]);
        if(!isset($current_res['status']) || $current_res['status'] !=0){
            return;
        }
        $cost = $current_res['data']['cost'];

        $profit = bcsub($income, $cost, 6);

        $nowtime = time();
        $add = [

            'year_income' => $income,//比较年(上年同日期范围)收入
            'year_cost' => $cost,//比较年(上年同日期范围)成本
            'year_profit' => $profit,//比较年(上年同日期范围)毛利
            'extend1' => $start_date,//比较开始日期
            'extend2' => $end_date,//比较开始日期
            'date' => $this->date,//当前统计日
            'type' => StatisticsSummaryDailySnapshotData::TYPE_COMPAREYEAR_INCOME,
            'create_time' => $nowtime
        ];

        StatisticsSummaryDailySnapshotData::insert($add);
    }

    public function getCompareDateRange(){
        //根据当前日期(统计日) 推算比较月日期(上年同月)
        $current_year = substr($this->date, 0, 4);//当前年
        $current_month = substr($this->date, 4, 2);//当前月
        $current_day = substr($this->date, 6, 2);//当前日

        $compare_year = $current_year - 1;//去年年份
        $compare_month = $current_month;//去年月份
        $compare_day = $current_day;//去年日

        $compare_start_month_date = $compare_year.$compare_month.'01';
        $compare_end_month_date = $compare_year.$compare_month.$compare_day;

        $compare_start_year_date = $compare_year.'0101';
        $compare_end_year_date = $compare_year.$compare_month.$compare_day;


        if(intval($current_month) == 2){
            //获取当前月份(2月份)的最后一天的日期
            $current_month_last_time = strtotime('+1 month -1 days', strtotime($current_year.$current_month.'01'));
            $current_month_last_date = date('Ymd', $current_month_last_time);
            if($current_month_last_date == $this->date){
                //如果当前日期(统计日)为二月份最后一天，则取去年2月份最后一天日期
                $compare_month_last_time = strtotime('+1 month -1 days', strtotime($compare_year.$compare_month.'01'));
                $compare_month_last_date = date('Ymd', $compare_month_last_time);
                $compare_end_month_date = $compare_end_year_date = $compare_month_last_date;
            }
        }

        return [
            'compare_start_month_date' => $compare_start_month_date, 'compare_end_month_date' => $compare_end_month_date,
            'compare_start_year_date' => $compare_start_year_date, 'compare_end_year_date' => $compare_end_year_date,
            ];
    }

    public function formatKeyProductData($show_name, $dayData, $monthData){
        $result = [];
        $add = [];
        foreach ($dayData as $item){
            $total = $item['total'] ?? 0;
            $money = $item['money'] ?? 0;
            $cost = $item['cost'] ?? 0;
            $customer_id = $item['customer_id'];
            $result[$customer_id]['total'] = $total;
            $result[$customer_id]['income'] = $money;
            $result[$customer_id]['cost'] = $cost;
            $result[$customer_id]['profit'] = bcsub($money, $cost, 2);
        }

        foreach ($monthData as $item){
            $money = $item['money'] ?? 0;
            $cost = $item['cost'] ?? 0;
            $customer_id = $item['customer_id'];
            $result[$customer_id]['month_income'] = $money;
            $result[$customer_id]['month_cost'] = $cost;
            $result[$customer_id]['month_profit'] = bcsub($money, $cost, 2);
        }

        unset($dayData);
        unset($monthData);

        $nowtime = time();
        foreach ($result as $custoemr_id => $item){
            $add[] = [
                'show_prodcut_name' => $show_name,
                'customer_id' => $custoemr_id,
                'total' => $item['total'] ?? 0,
                'income' => $this->formatMoney($item['income'] ?? 0),
                'cost' => $this->formatMoney($item['cost'] ?? 0),
                'profit' => $this->formatMoney($item['profit'] ?? 0),
                'month_income' => $this->formatMoney($item['month_income'] ?? 0),
                'month_cost' => $this->formatMoney($item['month_cost'] ?? 0),
                'month_profit' => $this->formatMoney($item['month_profit'] ?? 0),
                'date' => $this->date,
                'create_time' => $nowtime
            ];
        }

        unset($result);

        return $add;
    }

    public function getTop3CustomerByIncome($params = [], $date_type = 'day'){
        if(empty($params['product_ids'])){
            $sub_pids = Product::getSubPidByFatherId([$params['father_id']]);
        }else{
            $sub_pids = $params['product_ids'];
        }

        //top3客户日收入数据
        $top3DayIncome = BillProductIncomeV2::select([
            'account.customer_id',
            DB::raw('SUM(bill_product_income_v2.`money`) as money'),
        ])
            ->leftJoin('account', 'account.apikey', '=', 'bill_product_income_v2.apikey')
            ->when($date_type == 'day', function($query) use ($params){
                return $query->where('bill_product_income_v2.date', $this->date);
            })
            ->when($date_type == 'month', function($query) use ($params){
                return $query->where('bill_product_income_v2.date', '>=', $this->firstDateOfMonth)
                    ->where('bill_product_income_v2.date', '<=', $this->date);
            })
//            ->where('bill_product_income_v2.date', $this->date)
            ->where('bill_product_income_v2.father_id', $params['father_id'])
            ->whereIn('bill_product_income_v2.call_product_id', $sub_pids)
            ->when(isset($params['customer_ids']), function($query) use ($params){
                return $query->whereIn('account.customer_id', $params['customer_ids']);
            })
            ->groupBy(['account.customer_id'])
            ->orderBy('money', 'desc')
            ->limit(3)
            ->get()
            ->toArray();

        $top3CustomerIds = array_column($top3DayIncome, 'customer_id');
        $result = array_column($top3DayIncome, null,'customer_id');
        unset($top3DayIncome);

        //日总调用量
         StatisticsCustomerUsage::select([
            'account.customer_id',
            DB::raw('SUM(statistics_customer_usage.`total`) as total'),
        ])
            ->leftJoin('account', 'account.apikey', '=', 'statistics_customer_usage.apikey')
             ->when($date_type == 'day', function($query) use ($params){
                 return $query->where('statistics_customer_usage.date', $this->date);
             })
             ->when($date_type == 'month', function($query) use ($params){
                 return $query->where('statistics_customer_usage.date', '>=', $this->firstDateOfMonth)
                     ->where('statistics_customer_usage.date', '<=', $this->date);
             })
//            ->where('statistics_customer_usage.date', $this->date)
            ->whereIn('statistics_customer_usage.call_product_id', $sub_pids)
            ->whereIn('account.customer_id', $top3CustomerIds)
            ->groupBy(['account.customer_id'])
            ->get()
            ->each(function ($item) use (&$result) {
                $customer_id = $item->customer_id;
                $total = $item->total;
                if(isset($result[$customer_id]['total'])){
                    $result[$customer_id]['total'] += $total;
                }else{
                    $result[$customer_id]['total'] = $total;
                }
            });

        //日总普通成本
        BillCostV2::select([
            'account.customer_id',
            DB::raw('SUM(bill_cost_v2.`money`) as money'),
        ])
            ->leftJoin('account', 'account.apikey', '=', 'bill_cost_v2.apikey')
            ->when($date_type == 'day', function($query) use ($params){
                return $query->where('bill_cost_v2.date', $this->date);
            })
            ->when($date_type == 'month', function($query) use ($params){
                return $query->where('bill_cost_v2.date', '>=', $this->firstDateOfMonth)
                    ->where('bill_cost_v2.date', '<=', $this->date);
            })
//            ->where('bill_cost_v2.date', $this->date)
            ->whereIn('bill_cost_v2.product_id', $sub_pids)
            ->whereIn('account.customer_id', $top3CustomerIds)
            ->groupBy(['account.customer_id'])
            ->get()
            ->each(function ($item) use (&$result) {
                $customer_id = $item->customer_id;
                $money = $item->money;
                if(isset($result[$customer_id]['cost'])){
                    $result[$customer_id]['cost'] = bcadd($result[$customer_id]['cost'], $money, 6);
                }else{
                    $result[$customer_id]['cost'] = $money;
                }
            });

        //日渠道成本调整
        ChannelAccountAdjust::select([
            'customer_id',
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->when($date_type == 'day', function($query) use ($params){
                return $query->where('date', date('Y-m-d', strtotime($this->date)));
            })
            ->when($date_type == 'month', function($query) use ($params){
                return $query->where('date', '>=', date('Y-m-d', strtotime($this->firstDateOfMonth)))
                    ->where('date', '<=', date('Y-m-d', strtotime($this->date)));
            })
//            ->where('date', date('Y-m-d', strtotime($this->date)))
            ->whereIn('product_id', $sub_pids)
            ->whereIn('customer_id', $top3CustomerIds)
            ->groupBy(['customer_id'])
            ->get()
            ->each(function ($item) use (&$result) {
                $customer_id = $item->customer_id;
                $money = $item->money;
                $number = $item->number;
                if(isset($result[$customer_id]['cost'])){
                    $result[$customer_id]['cost'] = bcadd($result[$customer_id]['cost'], $money, 6);
                }else{
                    $result[$customer_id]['cost'] = $money;
                }

                if(isset($result[$customer_id]['total'])){
                    $result[$customer_id]['total'] = bcadd($result[$customer_id]['total'], $number, 0);
                }else{
                    $result[$customer_id]['total'] = $number;
                }
            });

        //日客户成本调整
        CustomerBillAdjust::select([
            'customer_id',
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->when($date_type == 'day', function($query) use ($params){
                return $query->where('date', $this->date);
            })
            ->when($date_type == 'month', function($query) use ($params){
                return $query->where('date', '>=', $this->firstDateOfMonth)
                    ->where('date', '<=', $this->date);
            })
//            ->where('date', $this->date)
            ->whereIn('product_id', $sub_pids)
            ->whereIn('customer_id', $top3CustomerIds)
            ->groupBy(['customer_id'])
            ->get()
            ->each(function ($item) use (&$result) {
                $customer_id = $item->customer_id;
                $money = $item->money;
                $number = $item->number;
                if(isset($result[$customer_id]['cost'])){
                    $result[$customer_id]['cost'] = bcadd($result[$customer_id]['cost'], $money, 6);
                }else{
                    $result[$customer_id]['cost'] = $money;
                }

                if(isset($result[$customer_id]['total'])){
                    $result[$customer_id]['total'] = bcadd($result[$customer_id]['total'], $number, 0);
                }else{
                    $result[$customer_id]['total'] = $number;
                }
            });

        //日固定成本调整
        ChannelAccountFixedFee::select([
            'account.customer_id',
            DB::raw('SUM(channel_account_fixed_fee.`money`) as money'),
        ])
            ->leftJoin('account', 'account.apikey', '=', 'channel_account_fixed_fee.apikey')
            ->when($date_type == 'day', function($query) use ($params){
                return $query->where('channel_account_fixed_fee.date', date('Y-m-d', strtotime($this->date)));
            })
            ->when($date_type == 'month', function($query) use ($params){
                return $query->where('channel_account_fixed_fee.date', '>=', date('Y-m-d', strtotime($this->firstDateOfMonth)))
                    ->where('channel_account_fixed_fee.date', '<=', date('Y-m-d', strtotime($this->date)));
            })
//            ->where('channel_account_fixed_fee.date', date('Y-m-d', strtotime($this->date)))
            ->whereIn('channel_account_fixed_fee.product_id', $sub_pids)
            ->whereIn('account.customer_id', $top3CustomerIds)
            ->groupBy(['account.customer_id'])
            ->get()
            ->each(function ($item) use (&$result) {
                $customer_id = $item->customer_id;
                $money = $item->money;
                if(isset($result[$customer_id]['cost'])){
                    $result[$customer_id]['cost'] = bcadd($result[$customer_id]['cost'], $money, 6);
                }else{
                    $result[$customer_id]['cost'] = $money;
                }
            });

        //日特殊消耗
        CustomerExpend::select([
            'customer_id',
            'type',
            DB::raw('SUM( `money` ) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->when($date_type == 'day', function($query) use ($params){
                return $query->where('profile_show_date', date('Y-m-d', strtotime($this->date)));
            })
            ->when($date_type == 'month', function($query) use ($params){
                return $query->where('profile_show_date', '>=', date('Y-m-d', strtotime($this->firstDateOfMonth)))
                    ->where('profile_show_date', '<=', date('Y-m-d', strtotime($this->date)));
            })
//            ->where('profile_show_date', date('Y-m-d', strtotime($this->date)))
            ->whereIn('product_id', $sub_pids)
            ->whereIn('customer_id', $top3CustomerIds)
            ->groupBy(['customer_id', 'type'])
            ->get()
            ->each(function ($item) use (&$result) {
                $type       = $item->type;
                $customer_id       = $item->customer_id;

                if ($type == 1) {
                    $money  = bcsub(0, $item->money, 6);
                    $number = bcsub(0, $item->number, 0);
                } else {
                    $number = $item->number;
                    $money  = $item->money;
                }

                if(isset($result[$customer_id]['total'])){
                    $result[$customer_id]['total'] += $number;
                }else{
                    $result[$customer_id]['total'] = $number;
                }

                if(isset($result[$customer_id]['money'])){
                    $result[$customer_id]['money'] = bcadd($result[$customer_id]['money'], $money, 6);
                }else{
                    $result[$customer_id]['money'] = $money;
                }

            });

        return $result;
    }



    /**
     * 生成第四个表格所需数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 20:42
     *
     * @return array
     */
    private function createIncomeByMonth()
    {
        $base = [
            //'month'       => '合计',
            'total'       => 0,
            'bill_number' => 0,
            'income'      => 0,
            'cost'        => 0,
            'profit'      => 0,
        ];

        $total         = $base;
        $total['name'] = '合计';

        #todo
        $result = [];
        array_walk($this->data['year'], function ($item) use (&$result, &$total, $base) {
            $month = $item['month'];
            $quarter = $this->getQuarter($month);
            ### 季度维度数据
            if (!array_key_exists($quarter, $result)) {
                $result[$quarter]         = $base;
                $result[$quarter]['q_id']         = $quarter;
                $result[$quarter]['name'] = array_get($this->quarterName, $quarter, '未定义');
                $result[$quarter]['data'] = [];
            }
            # 每个季度的数据
            $result[$quarter]['total']       = bcadd($result[$quarter]['total'], $item['total'], 0);
            $result[$quarter]['bill_number'] = bcadd($result[$quarter]['bill_number'], $item['bill_number'], 0);
            $result[$quarter]['income'] = bcadd($result[$quarter]['income'], $item['income'], 6);
            $result[$quarter]['cost']   = bcadd($result[$quarter]['cost'], $item['cost'], 6);
            $result[$quarter]['profit'] = bcadd($result[$quarter]['profit'], $item['profit'], 6);


            ### 月份维度数据
            if (!array_key_exists($quarter, $result[$quarter]['data'])) {
                $result[$quarter]['data'][$month]         = $base;
                $result[$quarter]['data'][$month]['name'] = $month;
            }
            # 每个月份的数据
            $result[$quarter]['data'][$month]['total']       = bcadd($result[$quarter]['data'][$month]['total'], $item['total'], 0);
            $result[$quarter]['data'][$month]['bill_number'] = bcadd($result[$quarter]['data'][$month]['bill_number'], $item['bill_number'], 0);
            $result[$quarter]['data'][$month]['income'] = bcadd($result[$quarter]['data'][$month]['income'], $item['income'], 6);
            $result[$quarter]['data'][$month]['cost']   = bcadd($result[$quarter]['data'][$month]['cost'], $item['cost'], 6);
            $result[$quarter]['data'][$month]['profit'] = bcadd($result[$quarter]['data'][$month]['profit'], $item['profit'], 6);

            ## 计算总数据
            $total['total']       = bcadd($total['total'], $item['total'], 0);
            $total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            $total['income']      = bcadd($total['income'], $item['income'], 6);
            $total['cost']        = bcadd($total['cost'], $item['cost'], 6);
            $total['profit']      = bcadd($total['profit'], $item['profit'], 6);

            return $item;
        });

        //dd($result);
        //排序
        array_multisort(array_column($result, 'q_id'), SORT_DESC, $result);

        //补充合计数据
        array_unshift($result, $total);

        //计算毛利率
        return array_map(function ($item) {
            //毛利率
            $income              = array_get($item, 'income', 0);
            $profit              = array_get($item, 'profit', 0);
            $item['profit_rate'] = $this->getRate($profit, $income, 2);

            //数据格式化
            $item['total']       = $this->formatNumber($item['total']);
            $item['bill_number'] = $this->formatNumber($item['bill_number']);
            $item['income']      = $this->formatMoney($item['income']);
            $item['cost']        = $this->formatMoney($item['cost']);
            $item['profit']      = $this->formatMoney($item['profit']);

            if (array_key_exists('data', $item)) {
                //同季度按月份排序
                $month_sort = array_column($item['data'], 'name');
                array_multisort($month_sort,SORT_DESC, $item['data']);

                $item['data'] = array_map(function ($item) {
                    //毛利率
                    $income              = array_get($item, 'income', 0);
                    $profit              = array_get($item, 'profit', 0);
                    $item['profit_rate'] = $this->getRate($profit, $income, 2);

                    //数据格式化
                    $item['total']       = $this->formatNumber($item['total']);
                    $item['bill_number'] = $this->formatNumber($item['bill_number']);
                    $item['income']      = $this->formatMoney($item['income']);
                    $item['cost']        = $this->formatMoney($item['cost']);
                    $item['profit']      = $this->formatMoney($item['profit']);

                    return $item;
                }, $item['data']);
            }

            return $item;
        }, $result);
    }


    //返回第几季度
    private function getQuarter($month){
        //传参格式如202108
        $month = substr($month, 4, 2);
        if(in_array($month, ['01', '02', '03'])){
            return 1;
        }

        if(in_array($month, ['04', '05', '06'])){
            return 2;
        }

        if(in_array($month, ['07', '08', '09'])){
            return 3;
        }

        if(in_array($month, ['10', '11', '12'])){
            return 4;
        }

    }


    /**
     * 创建第一个表格所需的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:58
     *
     * @return array
     */
    private function createProductCatIncome()
    {
        $base = [
            'total'       => 0,
            'bill_number' => 0,
            'income'      => 0,
            'cost'        => 0,
            'profit'      => 0,
            'month_income'=> 0,//当月收入
            'month_cost'  => 0,//当月成本
            'month_profit'=> 0,//当月毛利
            'year_income' => 0,//当年收入
            'year_cost'   => 0,//当年成本
            'year_profit' => 0,//当年毛利
        ];

        $total         = $base;
        $total['name'] = '合计';
        $result = [];
        #todo
        array_walk($this->data['date'], function ($item) use (&$result, &$total, $base) {
            $ignore_count_id = 3100;

            //产品的父级ID
            $father_id = $item['father_id'];

            //由于邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 其他(领导特殊要求),要做处理
            if(in_array($father_id, $this->convertFatherId)){
                //注意月年数据没有product_id，刚好第一个表格数据不需要月 年的数据, 下面代码已经注释
                $father_id = $this->convertProductId($item['father_id'], $item['product_id']);//转换产品id
            }

            //展示的产品ID
            $show_product_id = $this->getCategoryProductByFatherId($father_id);

            //获取类别ID
            $category_id = array_get($this->showProductNameAndCategory, $show_product_id, 0);

            ### 类别维度数据
            if (!array_key_exists($category_id, $result)) {
                $result[$category_id]         = $base;
                $result[$category_id]['name'] = array_get($this->category, $category_id, '未定义');
                $result[$category_id]['data'] = [];
            }
            # 每个类别的数据
            if ($ignore_count_id != $father_id) {//号码预警提示产品不计入总调用量/计费用量, 收入成本正常算进去(因为该产品量很大 钱却很少)
                $result[$category_id]['total']       = bcadd($result[$category_id]['total'], $item['total'], 0);
                $result[$category_id]['bill_number'] = bcadd($result[$category_id]['bill_number'], $item['bill_number'], 0);
            }
            $result[$category_id]['income'] = bcadd($result[$category_id]['income'], $item['income'], 6);
            $result[$category_id]['cost']   = bcadd($result[$category_id]['cost'], $item['cost'], 6);
            $result[$category_id]['profit'] = bcadd($result[$category_id]['profit'], $item['profit'], 6);

            ### 产品维度数据
            if (!array_key_exists($show_product_id, $result[$category_id]['data'])) {
                $result[$category_id]['data'][$show_product_id]         = $base;
                $result[$category_id]['data'][$show_product_id]['name'] = array_get($this->showProductName, $show_product_id, '未定义');
            }

            $result[$category_id]['data'][$show_product_id]['total']       = bcadd($result[$category_id]['data'][$show_product_id]['total'], $item['total'], 0);
            $result[$category_id]['data'][$show_product_id]['bill_number'] = bcadd($result[$category_id]['data'][$show_product_id]['bill_number'], $item['bill_number'], 0);        
            $result[$category_id]['data'][$show_product_id]['income'] = bcadd($result[$category_id]['data'][$show_product_id]['income'], $item['income'], 6);
            $result[$category_id]['data'][$show_product_id]['cost']   = bcadd($result[$category_id]['data'][$show_product_id]['cost'], $item['cost'], 6);
            $result[$category_id]['data'][$show_product_id]['profit'] = bcadd($result[$category_id]['data'][$show_product_id]['profit'], $item['profit'], 6);

            # 合计的数据
            //664产品,总量、计费用量不计入合计中
            if ($ignore_count_id != $father_id) {
                $total['total']       = bcadd($total['total'], $item['total'], 0);
                $total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            }
            $total['income'] = bcadd($total['income'], $item['income'], 6);
            $total['cost']   = bcadd($total['cost'], $item['cost'], 6);
            $total['profit'] = bcadd($total['profit'], $item['profit'], 6);
        });

        //dd($this->data['year_product']);
        array_walk($this->data['year_product'], function ($item) use (&$result, &$total, $base) {
            //产品的父级ID
            $father_id = $item['father_id'];

            //由于邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 其他(领导特殊要求),要做处理
            if(in_array($father_id, $this->convertFatherId)){
                //注意月年数据没有product_id
                $father_id = $this->convertProductId($item['father_id'], $item['product_id']);
            }
            //展示的产品ID
            $show_product_id = $this->getCategoryProductByFatherId($father_id);

            //获取类别ID
            $category_id = array_get($this->showProductNameAndCategory, $show_product_id, 0);

            ### 类别维度数据
            if (!array_key_exists($category_id, $result)) {
                //在这里理论上必须存在，如果不存在肯定有问题
                $result[$category_id]         = $base;
                $result[$category_id]['name'] = array_get($this->category, $category_id, '未定义');
                $result[$category_id]['data'] = [];
            }
            # 每个类别的数据
            if (664 != $father_id) {//号码预警提示产品不计入总调用量/计费用量, 收入成本正常算进去(因为该产品量很大 钱却很少)
                //$result[$category_id]['total']       = bcadd($result[$category_id]['total'], $item['total'], 0);
                //$result[$category_id]['bill_number'] = bcadd($result[$category_id]['bill_number'], $item['bill_number'], 0);
            }
            $result[$category_id]['year_income'] = bcadd($result[$category_id]['year_income'], $item['income'], 6);
            $result[$category_id]['year_cost']   = bcadd($result[$category_id]['year_cost'], $item['cost'], 6);
            $result[$category_id]['year_profit'] = bcadd($result[$category_id]['year_profit'], $item['profit'], 6);

            ### 产品维度数据
            if (!array_key_exists($show_product_id, $result[$category_id]['data'])) {
                //在这里理论上必须存在，如果不存在肯定有问题
                $result[$category_id]['data'][$show_product_id]         = $base;
                $result[$category_id]['data'][$show_product_id]['name'] = array_get($this->showProductName, $show_product_id, '未定义');
            }
            # 每个产品的数据
            if (664 != $father_id) {
                //$result[$category_id]['data'][$show_product_id]['total']       = bcadd($result[$category_id]['data'][$show_product_id]['total'], $item['total'], 0);
                //$result[$category_id]['data'][$show_product_id]['bill_number'] = bcadd($result[$category_id]['data'][$show_product_id]['bill_number'], $item['bill_number'], 0);
            }
            $result[$category_id]['data'][$show_product_id]['year_income'] = bcadd($result[$category_id]['data'][$show_product_id]['year_income'], $item['income'], 6);
            $result[$category_id]['data'][$show_product_id]['year_cost']   = bcadd($result[$category_id]['data'][$show_product_id]['year_cost'], $item['cost'], 6);
            $result[$category_id]['data'][$show_product_id]['year_profit'] = bcadd($result[$category_id]['data'][$show_product_id]['year_profit'], $item['profit'], 6);

            # 合计的数据
            //664产品,总量、计费用量不计入合计中
            if (664 != $father_id) {
                //$total['total']       = bcadd($total['total'], $item['total'], 0);
                //$total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            }
            $total['year_income'] = bcadd($total['year_income'], $item['income'], 6);
            $total['year_cost']   = bcadd($total['year_cost'], $item['cost'], 6);
            $total['year_profit'] = bcadd($total['year_profit'], $item['profit'], 6);
        });

        //排序
        ksort($result);
        $result = array_map(function ($item) {
            if($item['name'] == '代理'){
                //邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 实时在网状态 其他(领导特殊要求)
                //其他其他排序在代理分类中的最后面 因此特殊处理
                $tmp_detail = [];
                foreach($item['data'] as $key => $detail){
                    if($detail['name'] == '其他'){
                        $tmp_detail = $detail;
                        unset($item['data'][$key]);
                    }
                }
                array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);
                if(!empty($tmp_detail)) {
                    $item['data'][] = $tmp_detail;
                }
            }else if($item['name'] == '存量洞察'){
                //存量运营、存量分层 排序在存量洞察分类中的最后面 因此特殊处理
                $tmp_detail = [];
                foreach($item['data'] as $key => $detail){
                    if(in_array($detail['name'], ['存量运营', '存量分层'])){
                        $tmp_detail[] = $detail;
                        unset($item['data'][$key]);
                    }
                }
                array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);
                foreach ($tmp_detail as $tmp_detail_value){
                    $item['data'][] = $tmp_detail_value;
                }
            }else{
                array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);
            }

            return $item;
        }, $result);

        //补充合计数据
        array_unshift($result, $total);

        $total_income = $total['income'];//总日收入
        $total_cost = $total['cost'];//总日成本
        $total_profit = $total['profit'];//总日毛利

        //计算毛利率
        return array_map(function ($item) use ($total_income, $total_cost, $total_profit){
            //毛利率
            $income              = array_get($item, 'income', 0);
            $profit              = array_get($item, 'profit', 0);
            $cost              = array_get($item, 'cost', 0);
            $item['profit_rate'] = $this->getRate($profit, $income, 2);

            //数据格式化
            $item['total']       = $this->formatNumber($item['total']);
            $item['bill_number'] = $this->formatNumber($item['bill_number']);
            $item['income']      = $this->formatMoney($item['income']);
            $item['day_income_rate'] = $this->getRate($income, $total_income, 2);
            $item['cost']        = $this->formatMoney($item['cost']);
            $item['day_cost_rate'] = $this->getRate($cost, $total_cost, 2);
            $item['profit']      = $this->formatMoney($item['profit']);
            $item['day_profit_rate'] = $this->getRate($profit, $total_profit, 2);

            /*
            $item['month_income'] = $this->formatMoney($item['month_income']);
            $item['month_cost']   = $this->formatMoney($item['month_cost']);
            $item['month_profit'] = $this->formatMoney($item['month_profit']);
            */
            $item['year_income']      = $this->formatMoney($item['year_income']);
//            $item['year_cost']        = $this->formatMoney($item['year_cost']);
            $item['year_profit']      = $this->formatMoney($item['year_profit']);
            //*/

            if (array_key_exists('data', $item)) {
                $item['data'] = array_map(function ($item) use ($total_income, $total_cost, $total_profit){
                    //毛利率
                    $income              = array_get($item, 'income', 0);
                    $profit              = array_get($item, 'profit', 0);
                    $cost                = array_get($item, 'cost', 0);
                    $item['profit_rate'] = $this->getRate($profit, $income, 2);

                    //数据格式化
                    $item['total']       = $this->formatNumber($item['total']);
                    $item['bill_number'] = $this->formatNumber($item['bill_number']);
                    $item['income']      = $this->formatMoney($item['income']);
                    $item['day_income_rate'] = $this->getRate($income, $total_income, 2);
                    $item['cost']        = $this->formatMoney($item['cost']);
                    $item['day_cost_rate'] = $this->getRate($cost, $total_cost, 2);
                    $item['profit']      = $this->formatMoney($item['profit']);
                    $item['day_profit_rate'] = $this->getRate($profit, $total_profit, 2);
                    $item['year_income']      = $this->formatMoney($item['year_income']);
                    $item['year_profit']      = $this->formatMoney($item['year_profit']);

                    return $item;
                }, $item['data']);
            }

            return $item;
        }, $result);
    }

    /**
     * 设置参数
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:12
     *
     * @return void
     */
    private function setParams()
    {
        //确认是否发送
//        $this->checkSend();

        //设置所需要的日期
        $date       = $this->option('date');
        $this->date = $date ?: date('Ymd', strtotime('-1 days'));
        if ($this->date > date('Ymd') || !preg_match('/^\d{8}$/', $this->date)) {
            throw new Exception("日期格式不正确");
        }

        $this->customerIncomeRepository = new CustomerIncomeRepository();

        $this->month            = date('Ym', strtotime($this->date));
        $this->firstDateOfMonth = date('Ymd', strtotime('first day of this month', strtotime($this->date)));
        $this->firstDateOfYear  = date('Y', strtotime($this->date)) . '0101';
        $this->totalNotInProductIds = array_column(Product::getHasProductFatherId(), 'product_id');

        // 获取产品分类
        $this->setCategory();
        //获取产品对外展示的名字 && 类别下展示名称和类别的关系 && 转换的父产品等
        $this->showProductName = ProductCategory::SHOW_PRODUCT_NAME;
        $this->showProductNameAndCategory = ProductCategory::SHOW_PRODUCT_NAME_AND_CATEGORY;
        $this->productCategoryMap = ProductCategory::PRODUCT_CATEGORY_MAP;
        $this->showProductName200 = ProductCategory::SHOW_PRODUCT_NAME_200;
        $this->showProductName50000 = ProductCategory::SHOW_PRODUCT_NAME_50000;
        $this->showProductName3100 = ProductCategory::SHOW_PRODUCT_NAME_3100;
        $this->showProductName30000 = ProductCategory::SHOW_PRODUCT_NAME_30000;
        $this->convertFatherId = ProductCategory::CONVERT_FATHER_ID;
    }


    /**
     * 获取产品分类和分类名称的map
     *
     * @return void
     */
    private function  setCategory(){
        $this->category = Category::pluck('category_name','category_id');
    }

    /**
     * 设置需要发送的各类数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:54
     *
     * @return void
     */
    private function setData()
    {
        //补充营收数据
        $this->fillIncome();

        //补充成本数据
        $this->fillCost();

        //补充总调用量数据
        $this->fillTotal();

        //计算数据
        $this->calculateProfit();
    }

    /**
     * 计算利润
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:42
     *
     * @return void
     */
    private function calculateProfit()
    {
        //计算当日的数据
        $this->data['date'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['date']);

        //计算当月的数据
        $this->data['month'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['month']);

        //计算当年的数据
        $this->data['year'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['year']);

        //计算当年产品维度的数据
        $this->data['year_product'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['year_product']);

    }

    /**
     * 补充总调用量数据
     * 总调用量定义：所有子产品成功调用量之和
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:01
     *
     * @return void
     */
    private function fillTotal()
    {
        //补充当日的总调用量数据
        $this->fillDateTotal();

        //补充当月的总调用量数据
        $this->fillMonthTotal();

        //补充当年的总调用量数据
        $this->fillYearTotal();
    }

    /**
     * 将营收数据补充到待发送数据中
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:56
     *
     * @return void
     */
    private function fillIncome()
    {
        //补充当日的营收数据
        $this->fillDateIncome();

        //补充本月的营收数据
        $this->fillMonthIncome();

        //补充本年的营收数据
        $this->fillYearIncome();
    }

    /**
     * 将成本数据补充到待发送数据中
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:34
     *
     * @return void
     */
    private function fillCost()
    {
        //补充当日的成本数据
        $this->fillDateCost();

        //补充当月的成本数据
        $this->fillMonthCost();

        //补充当年的成本数据
        $this->fillYearCost();
    }

    /**
     * 补充当月的成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillYearCost()
    {
        //填充当月的成本统计数据，并将其保存在容器中
        BillCostV2::select([
            'apikey',
            'product_id',
            'source',
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '>=', $this->firstDateOfYear)
            ->where('date', '<=', $this->date)
            //->when($this->specialCostApikey, function($query) {
            //    return $query->whereNotIn('apikey', $this->specialCostApikey);
            //})
            ->groupBy('apikey', 'product_id', 'source', 'month')
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

            });


        CustomerBillAdjust::select([
            'customer_id',
            'product_id',
            'source',
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->where('date', '>=', $this->firstDateOfYear)
            ->where('date', '<=', $this->date)
            ->where('delete_at', '=', 0)
            ->groupBy('customer_id', 'product_id', 'source', 'month')
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

            });

        //填充当月的渠道成本调整数据
        $this->getSpecialCostChannelMonth($this->firstDateOfYear, $this->date)
            ->each(function ($item) {
                $month      = $item->month;
                $month      = date('Ym', strtotime($month));
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

            });

        //填充当月的固定费用成本数据
        $this->getSpecialCostFixedMonth($this->firstDateOfYear, $this->date)
            ->each(function ($item) {
                $month      = $item->month;
                $month      = date('Ym', strtotime($month));
                $product_id = $item->product_id;
                $number     = 0;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

            });

    }

    /**
     * 补充当月的成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillMonthCost()
    {
        //填充当月的成本统计数据，并将其保存在容器中
        $this->getBillCost($this->firstDateOfMonth, $this->date, $this->specialCostApikey)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

        //临时代码，计算特殊apikey的成本
        $spacialCost = $this->getSpecialBillCost($this->firstDateOfMonth, $this->date, $this->specialCostApikey);
        if ($spacialCost) {
            $this->fillMonthItem($this->specialCostPid, $spacialCost['money'], $spacialCost['number'], 'cost');
        }

        //填充当月的特殊营收数据，并将其保存在容器中
        $this->getSpecialCost($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

        //填充当月的渠道成本调整数据
        $this->getSpecialCostChannel($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

        //填充当月的固定费用成本数据
        $this->getSpecialCostFixed($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = 0;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

    }

    /**
     * 补充当月的总调用量数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:05
     *
     * @return void
     */
    private function fillMonthTotal()
    {
        $this->getTotal($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $this->fillMonthItem($product_id, 0, $number, 'total');
            });
    }

    /**
     * 补充当年的总调用量数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:05
     *
     * @return void
     */
    private function fillYearTotal()
    {
        StatisticsCustomerUsage::select([
            'product_id',
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`total`) as number'),
        ])
            ->leftJoin('account', 'account.apikey', '=', 'statistics_customer_usage.apikey')
            ->whereNotIn('product_id', $this->totalNotInProductIds)
            ->where('date', '>=', $this->firstDateOfYear)
            ->where('date', '<=', $this->date)
            ->where('customer_id', '!=', $this->yuloreCustomerId)
            ->groupBy('product_id', 'month')
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $this->fillYearItem($month, $product_id, 0, $number, 'total');

                $this->fillYearItemByProductId($month, $product_id, 0, $number, 'total');

            });
    }

    /**
     * 补充本年应收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 10:39
     *
     * @return void
     */
    private function fillYearIncome()
    {
        //填充当年的营收账单数据，并将其保存在容器中
        BillProductIncomeV2::select([
            'apikey',
            'product_id',
            'source',
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '<=', $this->date)
            ->where('date', '>=', $this->firstDateOfYear)
            ->groupBy(['apikey', 'product_id', 'source', 'month'])
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                //企服企业收入减半20221208
                //if($this->checkQifuByApikey($item->apikey)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition($item->apikey, '', $product_id)){
                    $money = bcdiv($money,2,6);
                };
                //补充到当日的账单中
                $this->fillYearItem($month, $product_id, $money, $number);

                // 根据产品id和年份分组 补充到当日的账单中
                $this->fillYearItemByProductId($month, $product_id, $money, $number);

            });

        //填充当年的特殊营收数据，并将其保存在容器中
        CustomerExpend::select([
            'customer_id',
            'product_id',
            'type',
            'source',
            DB::raw('LEFT(`profile_show_date`, 7) as month'),
            DB::raw('SUM( `money` ) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->where('profile_show_date', '>=', date('Y-m-d', strtotime($this->firstDateOfYear)))
            ->where('profile_show_date', '<=', date('Y-m-d', strtotime($this->date)))
            ->groupBy(['customer_id', 'product_id', 'type', 'source', 'month'])
            ->get()
            ->each(function ($item) use (&$result) {
                $type       = $item->type;
                $month      = $item->month;
                $month      = date('Ym', strtotime($month));
                $product_id = $item->product_id;
                //企服企业收入减半20221208
                //if($this->checkQifuByCustomerId($item->customer_id)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition('', $item->customer_id, $product_id)){
                    $item->money = bcdiv($item->money,2,6);
                }
                if ($type == 1) {
                    $money  = bcsub(0, $item->money, 6);
                    $number = bcsub(0, $item->number, 0);
                } else {
                    $number = $item->number;
                    $money  = $item->money;
                }
                $this->fillYearItem($month, $product_id, $money, $number);

                //根据产品id和年份分组 补充到当日的账单中
                $this->fillYearItemByProductId($month, $product_id, $money, $number);

            });
    }

    /**
     * 补充本月应收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 10:39
     *
     * @return void
     */
    private function fillMonthIncome()
    {
        //填充当月的营收账单数据，并将其保存在容器中
        $this->getBillIncome($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                // 企服企业收入减半20221208
                //if($this->checkQifuByApikey($item->apikey)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition($item->apikey, '', $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillMonthItem($product_id, $money, $number);
            });

        //填充当月的特殊营收数据，并将其保存在容器中
        $this->getSpecialIncome($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                //企服企业收入减半20221208
                //if($this->checkQifuByCustomerId($item->customer_id)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition('', $item->customer_id, $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillMonthItem($product_id, $money, $number);
            });
    }

    /**
     * 补充当日的成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillDateCost()
    {
        //填充当日的成本统计数据，并将其保存在容器中
        $this->getBillCost($this->date, $this->date, $this->specialCostApikey)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

            });

        //临时代码，计算特殊apikey的成本
//        $spacialCost = $this->getSpecialBillCost($this->date, $this->date, $this->specialCostApikey);
//        if ($spacialCost) {
//            $this->fillDateItem($this->specialCostPid, $spacialCost['money'], $spacialCost['number'], 'cost');
//        }

        //填充当日的特殊营收数据，并将其保存在容器中(客户成本调整)
        $this->getSpecialCost($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

            });

        //填充当日的渠道成本调整数据
        $this->getSpecialCostChannel($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

            });

        //填充当日的固定费用成本数据
        $this->getSpecialCostFixed($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

            });
    }

    private function fillYearItemByProductId($month, $product_id, $money, $number, $type = 'income')
    {
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;
        //$year = substr($month, 0, 4);
        if (!array_key_exists($product_id, $this->data['year_product'])) {
            $this->data['year_product'][$product_id] = [
                //父产品ID
                'father_id'       => $father_id,
                //子产品
                'product_id'      => $product_id,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['year_product'][$product_id]['income'] = bcadd($this->data['year_product'][$product_id]['income'], $money, 6);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id != 604 && 664 != $product_id) {
                    $this->data['year_product'][$product_id]['bill_number'] = bcadd($this->data['year_product'][$product_id]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['year_product'][$product_id]['cost'] = bcadd($this->data['year_product'][$product_id]['cost'], $money, 6);
                break;
            case 'total':
                //664产品不计入计费用量
                if (664 != $product_id) {
                    $this->data['year_product'][$product_id]['total'] = bcadd($this->data['year_product'][$product_id]['total'], $number, 0);
                    //如果是邦秒配批量版，它的计费用量就是总查询量
                    if ($product_id == 604) {
                        $this->data['year_product'][$product_id]['bill_number'] = bcadd($this->data['year_product'][$product_id]['bill_number'], $number, 0);
                    }
                }

                break;
        }
    }

    /**
     * 补充当日的营收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillDateIncome()
    {
        //填充当日的营收账单数据，并将其保存在容器中
        $this->getBillIncome($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                // 企服企业收入减半20221208
                //if($this->checkQifuByApikey($item->apikey)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition($item->apikey, '', $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillDateItem($product_id, $money, $number);

            });

        //填充当日的特殊营收数据，并将其保存在容器中
        $this->getSpecialIncome($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                //企服企业收入减半20221208
                //if($this->checkQifuByCustomerId($item->customer_id)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition('', $item->customer_id, $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillDateItem($product_id, $money, $number);

            });
    }

    /**
     * 补充当日的总调用量数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:05
     *
     * @return void
     */
    private function fillDateTotal()
    {
        $this->getTotal($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $this->fillDateItem($product_id, 0, $number, 'total');

            });
    }

    /**
     * 填充当日的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:03
     *
     * @param $product_id integer 子产品ID
     * @param $money      float 金额
     * @param $number     integer 计费用量
     * @param $type       string 数据类型
     *
     * @return void
     */
    private function fillDateItem($product_id, $money, $number, $type = 'income')
    {
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;

        if (!array_key_exists($product_id, $this->data['date'])) {
            $this->data['date'][$product_id] = [
                //产品ID
                'product_id'  => $product_id,
                //父产品ID
                'father_id'   => $father_id,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['date'][$product_id]['income'] = bcadd($this->data['date'][$product_id]['income'], $money, 6);

                //邦秒配按总查询量计算计费用量,不是邦秒配的产品正常从账单表中取量(账单表中量即计费量)即可
                if ($product_id != 604) {
                    $this->data['date'][$product_id]['bill_number'] = bcadd($this->data['date'][$product_id]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['date'][$product_id]['cost'] = bcadd($this->data['date'][$product_id]['cost'], $money, 6);
                break;
            case 'total':
                $this->data['date'][$product_id]['total'] = bcadd($this->data['date'][$product_id]['total'], $number, 0);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id == 604) {
                    $this->data['date'][$product_id]['bill_number'] = bcadd($this->data['date'][$product_id]['bill_number'], $number, 0);
                }

                break;
        }
    }


    /**
     * 填充当月的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:03
     *
     * @param $product_id integer 子产品ID
     * @param $money      float 金额
     * @param $number     integer 计费用量
     * @param $type       string 数据类型
     *
     * @return void
     */
    private function fillMonthItem($product_id, $money, $number, $type = 'income')
    {
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;

        if (!array_key_exists($father_id, $this->data['month'])) {
            $this->data['month'][$father_id] = [
                //父产品ID
                'father_id'   => $father_id,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['month'][$father_id]['income'] = bcadd($this->data['month'][$father_id]['income'], $money, 6);
                //如果是邦秒配，它的计费用量就是总查询量,不是邦秒配的产品正常从账单表中取量(账单表中量即计费量)即可
                if ($product_id != 604) {
                    $this->data['month'][$father_id]['bill_number'] = bcadd($this->data['month'][$father_id]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['month'][$father_id]['cost'] = bcadd($this->data['month'][$father_id]['cost'], $money, 6);
                break;
            case 'total':
                $this->data['month'][$father_id]['total'] = bcadd($this->data['month'][$father_id]['total'], $number, 0);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id == 604) {
                    $this->data['month'][$father_id]['bill_number'] = bcadd($this->data['month'][$father_id]['bill_number'], $number, 0);
                }
                break;
        }
    }

    /**
     * 填充当年的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:03
     *
     * @param $month      integer 月份
     * @param $product_id integer 子产品ID
     * @param $money      float 金额
     * @param $number     integer 计费用量
     * @param $type       string 数据类型
     *
     * @return void
     */
    private function fillYearItem($month, $product_id, $money, $number, $type = 'income')
    {
        if (!array_key_exists($month, $this->data['year'])) {
            $this->data['year'][$month] = [
                //父产品ID
                'month'       => $month,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['year'][$month]['income'] = bcadd($this->data['year'][$month]['income'], $money, 6);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id != 604 && 664 != $product_id) {
                    $this->data['year'][$month]['bill_number'] = bcadd($this->data['year'][$month]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['year'][$month]['cost'] = bcadd($this->data['year'][$month]['cost'], $money, 6);
                break;
            case 'total':
                //664产品不计入计费用量
                if (664 != $product_id) {
                    $this->data['year'][$month]['total'] = bcadd($this->data['year'][$month]['total'], $number, 0);
                    //如果是邦秒配批量版，它的计费用量就是总查询量
                    if ($product_id == 604) {
                        $this->data['year'][$month]['bill_number'] = bcadd($this->data['year'][$month]['bill_number'], $number, 0);
                    }
                }

                break;
        }
    }


    /**
     * 确认每一个父产品的部门
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 11:00
     *
     * @param $father_id integer 父产品ID
     *
     * @return integer 部门ID
     */
    private function getShowDepartmentId($father_id)
    {
        if (in_array($father_id, [101, 210, 1000, 105, 501, 615, 616, 612, 664])) {
            return 0;//一部
        }

        if (in_array($father_id, [200, 401, 104, 601, 604])) {
            return 1;//二部
        }

        if (in_array($father_id, [10000])) {
            return 2;//三部
        }

        //其他的，有新增产品需要调整
        return 0;
    }

    /**
     * 确认数据那个产品的数据(产品类型)
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:48
     *
     * @param $fahter_id  integer 父产品ID
     *
     * @return integer 展示的产品KEY
     */
    private function getShowProductId($father_id)
    {
        if (200 == $father_id) {
            return 0;
        }

        if (in_array($father_id, [210, 1000, 101, 105, 501])) {
            return 1;
        }

        if (in_array($father_id, [615, 612, 664, 616])) {
            return 2;
        }

        if (401 == $father_id) {
            return 3;
        }

        if (in_array($father_id, [104, 601, 604])) {
            return 4;
        }

        if (in_array($father_id, [10000])) {
            return 5;
        }

        if (in_array($father_id, [30000])) {
            return 6;
        }
        if (in_array($father_id, [3100])) {
            return 7;
        }
        //未识别的都算作[号码风险等级]
        return 2;
        //throw new \Exception("未识别的产品ID{$father_id}");
    }

    //根据父产品id返回所属类别下的展示产品
    private function getCategoryProductByFatherId($father_id){
        return $this->productCategoryMap[$father_id] ?? 13;//如果不存在，则归类到 类别为自有、产品为其他中
    }

    private function convertProductId($father_id, $product_id){
        switch ($father_id){
            case 200:
                if(in_array($product_id,$this->showProductName200)){
                    return $father_id.'-'.$product_id;
                }
                return '200-other';
            case 3100:
                if(in_array($product_id,$this->showProductName3100)){
                    return $father_id.'-'.$product_id;
                }
                return '3100-other';
            case 30000:
                if(in_array($product_id,$this->showProductName30000)){
                    return $father_id.'-'.$product_id;
                }
                return '30000-other';
            case 50000:
                if(in_array($product_id,$this->showProductName50000)){
                    return $father_id.'-'.$product_id;
                }
                return '50000-other';
            default:
                throw new Exception('转化产品id异常');
        }
    }


    /**
     * 计算利润
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:33
     *
     * @param $left     integer 分子
     * @param $right    integer 分母
     * @param $decimal  integer 小数位数
     *
     * @return string
     */
    private function getRate($left, $right, $decimal)
    {
        if (0 == intval($right)) {
            return 'NA';
        }

        return round(bcmul(bcdiv($left, $right, $decimal + 4), 100, $decimal + 2), $decimal) . '%';
    }

    /**
     * 对量格式化
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/22 15:11
     *
     * @param $number float 数值
     *
     * @return integer
     */
    private function formatNumber($number)
    {
//        return number_format($number, 0);
        return $number;
    }

    /**
     * 对金额格式化
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/22 15:12
     *
     * @param $money float 金额
     *
     * @return integer
     */
    private function formatMoney($money)
    {
        return round($money, 1);
//        return number_format(round($money, 1), 1);
    }

    /**
     * 获取特殊营收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:09
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     *
     * @return Collection
     */
    private function getSpecialIncome($start_date, $end_date)
    {
        return CustomerExpend::select([
            'customer_id',
            'product_id',
            'type',
            'source',
            DB::raw('SUM( `money` ) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->where('profile_show_date', '>=', date('Y-m-d', strtotime($start_date)))
            ->where('profile_show_date', '<=', date('Y-m-d', strtotime($end_date)))
            ->groupBy(['customer_id', 'product_id', 'type', 'source'])
            ->get()
            ->map(function ($item) use (&$result) {
                $type = $item->type;
                if ($type == 1) {
                    $item->money  = bcsub(0, $item->money, 6);
                    $item->number = bcsub(0, $item->number, 0);
                }

                return $item;
            });
    }

    /**
     * 获取营收账单数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:08
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     *
     * @return Collection
     */
    private function getBillIncome($start_date, $end_date)
    {
        return BillProductIncomeV2::select([
            'apikey',
            'product_id',
            'source',
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->groupBy(['apikey', 'product_id', 'source'])
            ->get();
    }


    /**
     * 获取成本账单数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 14:58
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     * @param $specialApikey   array 不参与计算的apikey
     *
     * @return Collection
     */
    private function getBillCost($start_date, $end_date, $specialApikey=[])
    {
        return BillCostV2::select([
            'apikey',
            'product_id',
            'source',
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->when($specialApikey, function($query, $specialApikey) {
                return $query->whereNotIn('apikey', $specialApikey);
            })
            ->groupBy(['apikey', 'product_id', 'source'])
            ->get();
    }

    /**
     * 金盾调用邦信分产品，成本需要记到金盾中，临时加的一个方法。以后需要删除
     * @param $start_date integer
     * @param $end_date integer
     * @param $specialApikey array
     * @return mixed
     */
    private function getSpecialBillCost($start_date, $end_date, $specialApikey)
    {
        return BillCostV2::select([
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->whereIn('apikey', $specialApikey)
            ->first()->toArray();
    }

    /**
     * 获取特殊成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:09
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     *
     * @return Collection
     */
    private function getSpecialCost($start_date, $end_date)
    {
        /*
        return UpstreamBillAdjust::select([
            'product_id',
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
                                 ->where('date', '>=', date('Y-m-d', strtotime($start_date)))
                                 ->where('date', '<=', date('Y-m-d', strtotime($end_date)))
                                 ->groupBy('product_id')
                                 ->get();
        */
        return CustomerBillAdjust::getProductCost($start_date, $end_date);


    }

    private function getSpecialCostChannel($start_date, $end_date)
    {

        return ChannelAccountAdjust::getProductCost($start_date, $end_date);

    }

    private function getSpecialCostChannelMonth($start_date, $end_date)
    {

        return ChannelAccountAdjust::getProductMonthCost($start_date, $end_date);

    }

    private function getSpecialCostFixed($start_date, $end_date)
    {

        return ChannelAccountFixedFee::getProductCost($start_date, $end_date);

    }

    private function getSpecialCostFixedMonth($start_date, $end_date)
    {

        return ChannelAccountFixedFee::getProductMonthCost($start_date, $end_date);

    }

    /**
     * 获取产品的总调用量
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:07
     *
     * @param $start_date  integer 开始日期
     * @param $end_date    integer 截止日期
     *
     * @return Collection
     */
    private function getTotal($start_date, $end_date)
    {
        return StatisticsCustomerUsage::select(['statistics_customer_usage.apikey as apikey', 'product_id', 'source', DB::raw('SUM(`total`) as number')])
            ->leftJoin('account', 'account.apikey', '=', 'statistics_customer_usage.apikey')
            ->whereNotIn('product_id', $this->totalNotInProductIds)
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->where('customer_id', '!=', $this->yuloreCustomerId)
            ->groupBy(['apikey', 'product_id', 'source'])
            ->get();
    }



    protected function formatNumStr($num)
    {
        $return = $num;
        if ($num >= 1000 || $num <= -1000) {
            $return = sprintf ( "%.1f", $num / 10000 ) . "万";
        }else{
            $return = round($return, 2);
        }
        return $return;
    }

    protected function checkQifuByApikey($apikey){
        $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
        if(!empty($customer_id)){
            $customerType =  RedisCache::instance('customerId_customerType_mapping')->get($customer_id);
            if($customerType==2){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    protected function checkQifuByCustomerId($customer_id){
            $customerType =  RedisCache::instance('customerId_customerType_mapping')->get($customer_id);
            if($customerType==2){
                return true;
            }else{
                return false;
            }
    }


    /**
     * @return void
     * 总体逻辑为 工作日和工作日比, 休息日和休息日比
     * 注：休息日为平时的周六日 和 节假日的法定休息日期；另外节假日中因调休需要补班的日期按正常工作日算
     */
    protected function getCompareDate(){

        $rep = new StatRepository();
        $holiday = $rep->getHolidayConfig();

        //判断统计日为工作日(work_date)还是休息日(free_date), 要比较的上一日应该为临近统计日的上一个工作日或休息日
        $current_date_type = $rep->getDateType($this->date, $holiday);

        $pre_date = $this->date;
        $check_days = 15;//最多检测15天 以防止死循环(实际上可以不用这么判断)
        while ($check_days){
            $pre_date = date('Ymd', strtotime('-1 days', strtotime($pre_date)));
            $pre_date_type = $rep->getDateType($pre_date, $holiday);
            if($current_date_type == $pre_date_type){
                break;
            }
        }

        return $pre_date;
    }



    /**
     * @return void
    1、如果统计日为周六 和 上周日比
    2、如果统计日为周日 和 周六比(前一天)
    3、如果统计日为周一 和 上周五比
    4、其他统计日都和前一天比
     */
    protected function getCompareDateBak(){
        $weekdayNumber = date('w', strtotime($this->date));
        //0代表星期日，1代表星期一，以此类推，6代表星期六
        //$weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        if($weekdayNumber == 6){
            //如果统计日为周六 和 上周日比
            $pre_date = date('Ymd', strtotime('-6 days', strtotime($this->date)));
        }else if($weekdayNumber == 1){
            //如果统计日为周一 和 上周五比
            $pre_date = date('Ymd', strtotime('-3 days', strtotime($this->date)));
        }else{
            $pre_date = date('Ymd', strtotime('-1 days', strtotime($this->date)));
        }

        return $pre_date;
    }


    public function findTopNCustomer($data = [], $top = 3){
        $up_topn = [];
        $down_topn = [];
        foreach ($data as $customer_id => $diff_money){
            if($diff_money >= 0){
                $up_topn[$customer_id] = $diff_money;
            }else{
                $down_topn[$customer_id] = bcsub(0, $diff_money, 2);
            }
        }

        unset($data);
        arsort($up_topn);
        arsort($down_topn);
        $up_topn = array_slice($up_topn, 0, $top);
        $down_topn = array_slice($down_topn, 0, $top);

        $return = ['up_topn' => [], 'down_topn' => []];
        foreach ($up_topn as $customer_id => $money){
            //$name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id)?:$customer_id;
            $return['up_topn'][] = [
                //'customer_name' => $name,
                'customer_id' => $customer_id,
                'money' => $money,
            ];
        }

        foreach ($down_topn as $customer_id => $money){
            //$name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id)?:$customer_id;
            $return['down_topn'][] = [
                //'customer_name' => $name,
                'customer_id' => $customer_id,
                'money' => $money,
            ];
        }

        unset($up_topn);
        unset($down_topn);
        return $return;
    }

}
