<?php

namespace App\Console\Commands\ReportDay;

use App\Define\ProductCategory;
use App\Http\Repository\StatBaseRepository;
use App\Models\BillCostV2;
use App\Models\BillNotes;
use App\Models\BillProductIncomeV2;
use App\Models\Category;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\CommonInfoModel;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Models\CustomerBillAdjust;
use App\Models\EmailConfig;
use App\Providers\Auth\DataAuth;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use App\Repositories\Cost\ProductCostRepository;
use App\Repositories\Income\CustomerIncomeRepository;
use App\Repositories\Income\ProductIncomeRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * php artisan report_day:profit_for_leader --username wei.xiu
 */
class ProfitForLeader extends Command
{
    protected $signature   = "report_day:profit_for_leader
	{--date= : 日期, 格式Ymd，默认昨日}
    {--username= : 收件人，以用户在后台的用户名为基准，多个用户使用,隔开，不传则对所有授权用户发送}
    {--save : 是否将数据保存在数据库中}
	";
    protected $description = '收入、成本、利润日报表邮件发送（领导版）';

    protected $emailConfigScene = 'profit_for_leader';

    protected $username = [];


    // 可见收入 成本 利润的人员
    protected $authCostPorfit = [
        'yong.liu',
        'ren.zhang',
    ];

    /**
     * @var array 展示的产品数据(产品类型)
     * getShowProductId($father_id) 方法中定义了父产品 和 展示产品的关系
     */
    private $showProductIds = [
        0 => '邦秒验', //200
        1 => '邦信分', //210, 1000, 101, 105, 501
        2 => '号码风险等级', //615, 612, 664, 616
        3 => '邦企查', //401
        4 => '邦秒配', //104, 601, 604
        5 => '号码分', //10000
        6 => '存量激活', //30000 事件分->存量激活
        7 => '企服产品', //3100
        8 => '号码融', //70000
    ];

    /**
     * @var array 技术部门
     */
    private $department = [
        0 => '一部',
        1 => '二部',
        2 => '三部',
    ];

    /**
     * @var array 展示的产品ID->部门ID映射关系
     */
    private $showProductId2DepartmentId = [
        0 => 1,
        1 => 0,
        2 => 0,
        3 => 1,
        4 => 1,
        5 => 2,
    ];

    //定义一个产品所属类别字典 product_category表定义
    private $category = [];

    private $showProductName = [];

    //定义一个类别下展示名称 和 类别的关系
    private $showProductNameAndCategory = [];

    private $productCategoryMap = [];

    private $showProductName200 = [];
    private $showProductName50000 = [];
    private $showProductName3100 = [];
    private $showProductName30000 = [];
    private $convertFatherId = [];//需要转换的父产品id

    //定义一个季度字典
    private $quarterName= [
        1 => '第一季度',
        2 => '第二季度',
        3 => '第三季度',
        4 => '第四季度',
    ];

    //月度产品统计表 如下父产品不需要合并为其他
    private $notMergeToOther = [
        '200',//帮秒验
        '210',//邦信分-通讯指数
        '1000',//邦信分-通讯评分
        '615',//号码风险等级
        '10000',// 号码分
        //'20000',//贷后风险指数
        '3100',//企服产品
        '30000',//存量洞察
    ];

    /**
     * @var string 羽乐科技客户ID (后台统计是排除了羽乐科技内部和售前测试,邮件这里只排除羽乐科技内部)
     */
    private $yuloreCustomerId = 'C20180828LOCNMG';

    /**
     * @var string 备注信息
     */
    private $remark = '';

    /**
     * @var array 计算总调用量的时候需要排除这几个产品
     */
    private $totalNotInProductIds = [];

    /**
     * @var string[] 需要特殊计算的apikey
     */
    private $specialCostApikey = [];//['4d2050c36b56dde19c04ccd08a0b7478'];
    /**
     * @var int 特殊apikey记入的子产品
     */
    private $specialCostPid = 661;

    private $customerIncomeRepository = null;

    /**
     * @var integer 日期
     */
    protected $date;//当前执行日期 如20211220
    protected $month;//当前执行月份 如202112
    protected $firstDateOfMonth;//当前执行月份起始日期 如20211201
    protected $firstDateOfYear;//当前执行年份起始日期 如20210101

    /**
     * @var array 数据
     */
    protected $data = [
        'date'  => [],
        'month' => [],
        'year'  => [],//这个年数据 里面是该年按月分组的
        'year_product'  => [],//这个年数据 里面是该年按产品分组的
        'month_product'=>[],// 父产品月度数据
        'month_sub_product'=>[],// 子产品月度数据
        'source_date'=>[],// 区分soure的日数据(source维度)
        'source_customer_date'=>[],// 区分soure的日数据(客户维度)
    ];

    protected $financeAuthMap = [];

    // 存储当前发送用户的权限
    protected $financeAuth = [];
    
    //脚本执行
    public function handle()
    {
        try {
            //设置参数
            $this->setParams();
            foreach ($this->username as $username) {
                $this->financeAuthFormat($username,0);
                //生成HTML页面
                $html = $this->createHtml();

                //发送邮件
                $this->sendMail($html,$username);
                $this->saveHtml($html);

                $this->output->success("[$username] 邮件发送成功");
            }

            //存储HTML内容
        } catch (Exception $exception) {
            //echo $exception->getFile(),": ",$exception->getLine(),PHP_EOL,$exception->getMessage(),PHP_EOL;
            sendCommandExceptionNotice($this, $exception);
        }
    }

    /**
	 * 获取全量需要发送的用户
	 *
	 * @access   private
	 *
	 * @return array
	 */
	private function getUsers()
	{
        $emailConfig = new EmailConfig();
        $users = $emailConfig->getRecipientsAndCCByScene($this->emailConfigScene);
        array_walk($users['recipients'], function(&$item){
            $item = substr($item['address'], 0, -11);
            return $item;
        });
        return $users['recipients'];
	}

    /**
     * 将HTML保存在数据库中
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 16:07
     *
     * @param $html string 页面展示内容
     *
     * @return void
     */
    private function saveHtml($html)
    {
        //是否将HTML保存在数据库中
        if ($this->input->getOption('save')) {
            $content = json_encode(['html' => $html, 'date' => $this->date], JSON_UNESCAPED_UNICODE);

            CommonInfoModel::where('id', 6)
                ->update(compact('content'));
        }
    }

    /**
     * 发送邮件
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 13:36
     *
     * @param $html string 邮件内容
     *
     * @return void
     */
    private function sendMail($html,$username=null)
    {
        $title = date('Y-m-d', strtotime($this->date)) . "产品调用量数据及收入情况";
		$dataAuthService = new DataAuth($username);

        $sendMailService = new SendMailService();

        $user_address = [['email' => $username . '@yulore.com', 'name' => $dataAuthService->getRealName()]];

        $sendMailService->setSubject($title)
            ->setContent($html)
            ->setAddressee($user_address)
            ->setFromName('金融后台项目组')
            ->send();
    }

    /**
     * 生成HTML页面
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:57
     *
     * @return string
     */
    private function createHtml()
    {
        $html = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"></head><body>';

        //设置样式
        $html .= $this->createHtmlStyle();

        //生成第一个表格所需的数据
        $data = $this->createFirstTableInfo();
        //生成第一个表格 每日父产品调用收入
        $html .= $this->createFirstTableHtml($data);

        //生成第二个表格所需数据(由原来的第四个表格调整为第二个)
        $data = $this->createFourthlyTableInfo();
        //生成第二个表格 月度调用量 收入
        $html .= $this->createFourthlyTableHtml($data);

        if ($this->financeAuth['show_money']) {
            //生成第三个表格所需数据 (由原来的第五个表格调整为第三个)
            $data = $this->createFatherIdMonthIncomeData();
            //生成第三个表格 月度调 收入
            $html .= $this->createFatherIdMonthIncomeHtml($data);
        }


        //第四个表格征信机构-来源维度数据
        $data = $this->getSourceData();
        $html .= $this->createSourceTableHtml($data);

        //第四第五个表格 客户收入
        $data = $this->getCustomerData();
        $html .= $this->createCustomerTableHtml($data);

        /*
          //朴道数据 第四第五个表格 朴道-产品维度 朴道客户维度

          $data = $this->getPudaoDatas();
          $html .= $this->createPudaoFirstTableHtml($data['product']);
          $html .= $this->createPudaoSecondTableHtml($data['customer']);
      */


        //生成第6个表格所需数据
        //$data = $this->createTwiceTableInfo();
        //生成第6个表格 每日子产品调用量收入
        //$html .= $this->createTwiceTableHtml($data);


        //生成第三个表格所需数据
        //$data = $this->createThirdTableInfo();
        //生成第三个表格
        //$html .= $this->createThirdTableHtml($data);

        return $html . '</body></html>';
    }

    /**
     * 设置页面样式
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 13:45
     *
     * @return string
     */
    private function createHtmlStyle()
    {
        return <<<HTML
<style>
	body {
		color : #333333;
	}

	.table_title {
        width       : 98%;
        height      : 30px;
        line-height : 30px;
        color       : #333333;
        border      : 700;
    }

	table {
		width         : 98%;
		border        : none;
		padding       : 0;
		margin        : 0 auto  10px;
		font-size     : 14px;
		color         : #666666;
		border-bottom : none;
		border-right  : none;
		border-left   : 1px solid #CCCCCC;
		border-top    : 1px solid #CCCCCC;
	}

	tr {
		border-right  : none;
		border-bottom : none;
		border-left   : 1px solid #CCCCCC;
		border-top    : 1px solid #CCCCCC;
	}

	td, th {
		border-right  : 1px solid #CCCCCC;
		border-bottom : 1px solid #CCCCCC;
		border-left   : none;
		border-top    : none;
		padding       : 5px 5px 5px 0;
	}

	th {
		color       : #333333;
		font-weight : bold;
		height      : 20px;
		line-height : 20px;
		padding     : 8px 0;
	}

	tr:nth-child(even) {
		background : #EEEEEE;
	}

	tr:hover {
		background : #CCCCCC;
	}

	.bold {
		font-weight : bold;
	}

	.fsz-16 {
		font-size : 15px;
	}

	.space {
		width  : 100%;
		height : 40px;
	}

	.remark {
		width       : 98%;
		margin      : 10px auto 20px;
		padding     : 5px;
		color       : red;
		font-size   : 14px;
		font-weight : bolder;
	}
</style>
HTML;

    }

    /**
     * 生成产品月度收入数据
     *
     * @param [type] $data
     * @return void
     */
    private function createFatherIdMonthIncomeHtml($data){
        $display = <<<HTML
<b class="table_title">三: 每月产品权责收入统计</b>
<table border="1" cellpadding="5" cellspacing="0" width="57%">
<tr>
	<th align="center" nowrap="nowrap">产品分类</th>
	<th align="center" nowrap="nowrap">产品</th>
HTML;
    foreach($data['total_month'] as $monthinfo){
        $display .='<th align="center" nowrap="nowrap">'.$monthinfo['name'].'</th>';
    }
    $display .= <<<HTML
	<th align="center" nowrap="nowrap">合计</th>
</tr>
HTML;
        // 月度统计数据
        $display .= "<tr><td align='center' colspan='2' >合计</td>";
        foreach ($data['total_month'] as $month =>$item) {
            $display .= "<td align='right'><font color='red' style='font-weight:1000;'>{$this->formatNumStr($item['income'])}</font></td>";
        }
        $display .= "<td align='right'><font color='red' style='font-weight:1000;'>{$this->formatNumStr($data['total_all'])}</font></td>";
        $display .= '</tr>';
        foreach ($data['list'] as $cate_id =>$cateinfo) {

            $rowspan = count($cateinfo) + 1;
            $cate_name = array_get($this->category, $cate_id, '未定义'); // 分类名
            //小计开始
            $display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">{$cate_name}</td>
	<td align="center">小计</td>
HTML;

            foreach ($data['sub_total'][$cate_id]['data'] as $sub_item){
                // 收入数据
                $display .="<td align='right'><font color='red' style='font-weight:1000;'>{$this->formatNumStr($sub_item['income'])}</font></td>";
            }
            //合计列
            $sum_total_income = $data['sub_total'][$cate_id]['total_income'] ?? 0;
            $display .="<td align='right'><font color='red' style='font-weight:1000;'>{$this->formatNumStr($sum_total_income)}</font></td>";
            $display .= '</tr>';
            //小计结束

            //$colstr = "<td align='center' rowspan=".$rowspan." >".array_get($this->category, $cate_id, '未定义')."</td>";
            foreach($cateinfo as $show_pid => $item){
                // 产品名
                $father_name = array_get($this->showProductName, $show_pid, '未定义');
                $display .= "<tr>"."<td align='center'>{$father_name}</td>";
                //$display .= "<tr>".$colstr."<td align='center'>{$father_name}</td>";
                foreach ($item as $itemItem) {
                    // 收入数据
                    $display .="<td align='right'>{$this->formatNumStr($itemItem['income'])}</td>";
                }
                // 父产品的统计数据
                $display .="<td align='right'><font color='red' style='font-weight:1000;'>{$this->formatNumStr($data['total'][$show_pid]['income'])}</font></td>";
                $display .= '</tr>';
                //$colstr = "";
            }
        }
        $display .= "</table><br/>";
        return $display;
    }

    /**
     * 生成第四个table表格的内容
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:28
     *
     * @param $data array 数据
     *
     * @return string
     */
    private function createFourthlyTableHtml($data)
    {
        $show_money = $this->financeAuth['show_money'];
        $show_cost = $this->financeAuth['show_cost'];
        $show_profit = $this->financeAuth['show_profit'];

        $display = <<<HTML
<b class="table_title">二: 每月产品调用数据情况 (特别约定：羽乐科技内部账号、号码预警提示产品不计入总调用量/计费用量)</b>
<table border="1" cellpadding="5" cellspacing="0" width="57%">
<tr>
	<th align="center" nowrap="nowrap">季度</th>
	<th align="center" nowrap="nowrap">月份</th>
	<th align="center" nowrap="nowrap">总调用量</th>
	<th align="center" nowrap="nowrap">计费调用量</th>
HTML;

	$show_money && $display .=  '<th align="center" nowrap="nowrap">权责收入</th>';
	$show_cost && $display .=  '<th align="center" nowrap="nowrap">权责成本</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">权责毛利</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">权责毛利率</th>';

    $display .= '</tr>';
        $total   = array_shift($data);
        $display .= <<<HTML
<tr>
	<td align="center" colspan="2">合计</td>
	<td align="right">{$this->formatNumStr($total['total'])}</td>
	<td align="right">{$this->formatNumStr($total['bill_number'])}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red">'.$this->formatNumStr($total['income']).'</font></td>';
	$show_cost && $display .=  '<td align="right">'.$this->formatNumStr($total['cost']).'</td>';
	$show_profit && $display .=  '<td align="right"><font color="red">'.$this->formatNumStr($total['profit']).'</font></td>';
	$show_profit && $display .=  '<td align="center">'.$total['profit_rate'].'</td>';
    $display .= '</tr>';
        foreach ($data as $item) {
            $rowspan = count($item['data']) + 1;
            //小计
            $display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">{$item['name']}</td>
	<td align="center">小计</td>
	<td align="right">{$this->formatNumStr($item['total'])}</td>
	<td align="right">{$this->formatNumStr($item['bill_number'])}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red">'.$this->formatNumStr($item['income']).'</font></td>';
	$show_cost && $display .=  '<td align="right">'.$this->formatNumStr($item['cost']).'</td>';
	$show_profit && $display .=  '<td align="right"><font color="red">'.$this->formatNumStr($item['profit']).'</font></td>';
	$show_profit && $display .=  '<td align="center">'.$item['profit_rate'].'</td>';
    $display .= '</tr>';
            foreach ($item['data'] as $itemItem) {
                //小计
                $display .= <<<HTML
<tr>
	<td align="center">{$itemItem['name']}</td>
	<td align="right">{$this->formatNumStr($itemItem['total'])}</td>
	<td align="right">{$this->formatNumStr($itemItem['bill_number'])}</td>
HTML;
	$show_money && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['income']).'</td>';
	$show_cost && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['cost']).'</td>';
	$show_profit && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['profit']).'</td>';
	$show_profit && $display .=  '<td align="center">'.$itemItem['profit_rate'].'</td>';
    $display .= '</tr>';
            }
        }

        $display .= "</table><br/>";

        return $display;
    }

    /**
     * 生成第三个table表格的内容
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:28
     *
     * @param $data array 数据
     *
     * @return string
     */
    private function createThirdTableHtml($data)
    {
        $display = <<<HTML
<b class="table_title">三: 当月产品调用数据情况小计 (特别约定：羽乐科技内部账号、号码预警提示产品不计入总调用量/计费用量)</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">部门</th>
	<th align="center" nowrap="nowrap">父产品</th>
	<th align="center" nowrap="nowrap">总调用量</th>
	<th align="center" nowrap="nowrap">计费调用量</th>
	<th align="center" nowrap="nowrap">权责收入</th>
	<th align="center" nowrap="nowrap">权责成本</th>
	<th align="center" nowrap="nowrap">权责毛利</th>
	<th align="center" nowrap="nowrap">权责毛利率</th>
</tr>
HTML;
        //合计
        $total   = array_shift($data);
        $display .= <<<HTML
<tr>
	<td colspan="2" align="center">合计</td>
	<td align="right">{$total['total']}</td>
	<td align="right">{$total['bill_number']}</td>
	<td align="right">{$total['income']}</td>
	<td align="right">{$total['cost']}</td>
	<td align="right">{$total['profit']}</td>
	<td align="center">{$total['profit_rate']}</td>
</tr>
HTML;
        //正常数据
        array_walk($data, function ($item) use (&$display) {
            $data                   = array_get($item, 'data', []);
            $show_sub_product_count = count($data);
            $rowspan                = $show_sub_product_count + 1;
            $display                .= <<<HTML
<tr>
	<td rowspan="{$rowspan}" align="center">{$item['name']}</td>
	<td align="center">小计</td>
	<td align="right">{$item['total']}</td>
	<td align="right">{$item['bill_number']}</td>
	<td align="right">{$item['income']}</td>
	<td align="right">{$item['cost']}</td>
	<td align="right">{$item['profit']}</td>
	<td align="center">{$item['profit_rate']}</td>
</tr>
HTML;
            array_walk($item['data'], function ($item) use (&$display) {
                $display .= <<<HTML
<tr>
	<td align="center">{$item['name']}</td>
	<td align="right">{$item['total']}</td>
	<td align="right">{$item['bill_number']}</td>
	<td align="right">{$item['income']}</td>
	<td align="right">{$item['cost']}</td>
	<td align="right">{$item['profit']}</td>
	<td align="center">{$item['profit_rate']}</td>
</tr>
HTML;
            });
        });

        $display .= "</table><br/>";


        return $display;
    }

    /**
     * 生成第二个table表格的内容
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:28
     *
     * @param $data array 数据
     *
     * @return string
     */
    private function createTwiceTableHtml($data)
    {
        $show_money = $this->financeAuth['show_money'];
        $show_cost = $this->financeAuth['show_cost'];
        $show_profit = $this->financeAuth['show_profit'];

        $display = <<<HTML
<b class="table_title">六: 每日产品调用数据情况 (特别约定：羽乐科技内部账号、号码预警提示产品不计入总调用量/计费用量)</b>
<table border="1" cellpadding="5" cellspacing="0" width="57%">
<tr>
	<th align="center" nowrap="nowrap">产品类型</th>
	<th align="center" nowrap="nowrap">子产品</th>
	<th align="center" nowrap="nowrap">总调用量</th>
	<th align="center" nowrap="nowrap">计费调用量</th>
HTML;

	$show_money && $display .=  '<th align="center" nowrap="nowrap">权责收入</th>';
	$show_cost && $display .=  '<th align="center" nowrap="nowrap">权责成本</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">权责毛利</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">权责毛利率</th>';
    $display .= '</tr>';

        //合计
        $total   = array_shift($data);
        $display .= <<<HTML
<tr>
	<td colspan="2" align="center">合计</td>
	<td align="right">{$this->formatNumStr($total['total'])}</td>
	<td align="right">{$this->formatNumStr($total['bill_number'])}</td>
HTML;
	$show_money && $display .=  '<td align="right">'.$this->formatNumStr($total['income']).'</td>';
	$show_cost && $display .=  '<td align="right">'.$this->formatNumStr($total['cost']).'</td>';
	$show_profit && $display .=  '<td align="right">'.$this->formatNumStr($total['profit']).'</td>';
	$show_profit && $display .=  '<td align="center">'.$total['profit_rate'].'</td>';
    $display .= '</tr>';

        //正常数据
        array_walk($data, function ($item) use (&$display,$show_money,$show_cost,$show_profit) {
            $data                   = array_get($item, 'data', []);
            $show_sub_product_count = count($data);
            $rowspan                = $show_sub_product_count + 1;
            $display                .= <<<HTML
<tr>
	<td rowspan="{$rowspan}" align="center">{$item['name']}</td>
	<td align="center">小计</td>
	<td align="right">{$this->formatNumStr($item['total'])}</td>
	<td align="right">{$this->formatNumStr($item['bill_number'])}</td>
HTML;
	$show_money && $display .=  '<td align="right">'.$this->formatNumStr($item['income']).'</td>';
	$show_cost && $display .=  '<td align="right">'.$this->formatNumStr($item['cost']).'</td>';
	$show_profit && $display .=  '<td align="right">'.$this->formatNumStr($item['profit']).'</td>';
	$show_profit && $display .=  '<td align="center">'.$item['profit_rate'].'</td>';
    $display .= '</tr>';

            array_walk($item['data'], function ($item) use (&$display,$show_money,$show_cost,$show_profit) {
                $display .= <<<HTML
<tr>
	<td align="center">{$item['name']}</td>
	<td align="right">{$this->formatNumStr($item['total'])}</td>
	<td align="right">{$this->formatNumStr($item['bill_number'])}</td>
HTML;

	$show_money && $display .=  '<td align="right">'.$this->formatNumStr($item['income']).'</td>';
	$show_cost && $display .=  '<td align="right">'.$this->formatNumStr($item['cost']).'</td>';
	$show_profit && $display .=  '<td align="right">'.$this->formatNumStr($item['profit']).'</td>';
	$show_profit && $display .=  '<td align="center">'.$item['profit_rate'].'</td>';
    $display .= '</tr>';

    });
        });

        $display .= "</table><br/>";

        return $display;
    }

    /**
     * 生成第一个table表格的内容
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:28
     *
     * @param $data array 数据
     *
     * @return string
     */
    private function createFirstTableHtml($data)
    {
        $show_money = $this->financeAuth['show_money'];
        $show_cost = $this->financeAuth['show_cost'];
        $show_profit = $this->financeAuth['show_profit'];

        $display = <<<HTML
<b class="table_title">一: 每日产品调用数据情况小计 (特别约定：羽乐科技内部账号、号码预警提示产品不计入总调用量/计费用量)</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">类别</th>
	<th align="center" nowrap="nowrap" width="128px">产品</th>
	<th align="center" nowrap="nowrap">日总调用量</th>
	<th align="center" nowrap="nowrap">日计费量</th>
HTML;
	$show_money && $display .=  '<th align="center" nowrap="nowrap">日收入</th>';
	$show_money && $display .=  '<th align="center" nowrap="nowrap">日收入占比</th>';
	$show_cost && $display .=   '<th align="center" nowrap="nowrap">日成本</th>';
	$show_profit && $display .= '<th align="center" nowrap="nowrap">日毛利</th>';
	$show_profit && $display .= '<th align="center" nowrap="nowrap">日毛利占比</th>';
	$show_profit && $display .= '<th align="center" nowrap="nowrap">日毛利率</th>';
	$show_money && $display .=  '<th align="center" nowrap="nowrap">当年收入</th>';
	$show_profit && $display .= '<th align="center" nowrap="nowrap">当年毛利</th>';
	
    $display .= '</tr>';
        $total   = array_shift($data);
        $display .= <<<HTML
<tr>
	<td align="center" colspan="2">合计</td>
	<td align="right">{$this->formatNumStr($total['total'])}</td>
	<td align="right">{$this->formatNumStr($total['bill_number'])}</td>
HTML;

	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['income']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="#1000ff" style="font-weight:1000;">100%</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['cost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['profit']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="#1000ff" style="font-weight:1000;">100%</font></td>';
	$show_profit && $display .=  '<td align="center">'.$total['profit_rate'].'</td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['year_income']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['year_profit']).'</font></td>';
	
    $display .= '</tr>';

        foreach ($data as $item) {
            $rowspan = count($item['data']) + 1;
            //小计
            $display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">{$item['name']}</td>
	<td align="center">小计</td>
	<td align="right">{$this->formatNumStr($item['total'])}</td>
	<td align="right">{$this->formatNumStr($item['bill_number'])}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['income']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="#1000ff" style="font-weight:1000;">'.$item['day_income_rate'].'</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['cost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['profit']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="#1000ff" style="font-weight:1000;">'.$item['day_profit_rate'].'</font></td>';
	$show_profit && $display .=  '<td align="center">'.$item['profit_rate'].'</td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['year_income']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['year_profit']).'</font></td>';
	
    $display .= '</tr>';
            foreach ($item['data'] as $itemItem) {
                //小计
                $display .= <<<HTML
<tr>
	<td align="center">{$itemItem['name']}</td>
	<td align="right">{$this->formatNumStr($itemItem['total'])}</td>
	<td align="right">{$this->formatNumStr($itemItem['bill_number'])}</td>
HTML;

	$show_money && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['income']).'</td>';
	$show_money && $display .=  '<td align="right">'.$itemItem['day_income_rate'].'</td>';
	$show_cost && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['cost']).'</td>';
	$show_profit && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['profit']).'</td>';
	$show_profit && $display .=  '<td align="right">'.$itemItem['day_profit_rate'].'</td>';
	$show_profit && $display .=  '<td align="center">'.$itemItem['profit_rate'].'</td>';
	$show_money && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['year_income']).'</td>';
	$show_profit && $display .=  '<td align="right">'.$this->formatNumStr($itemItem['year_profit']).'</td>';

    $display .= '</tr>';

}
        }

        $colspan = ($show_money ? 3 : 0) + ($show_cost ? 1 : 0) + ($show_profit ? 4 : 0) + 3;//计算备注说明合并列数
        $display .= '<tr>';
        $display .= '<td align="center">备注说明</td>';
        $display .= '<td colspan="'.$colspan.'">';
        $display .= '自有其他：邦企查,邦秒配,海外产品<br>';
        $display .= '代理其他：反电诈,二次放号,空号检测,携号转网,手机号消费档次,身份证二要素核验,手机号二要素验证,<br>';
        $display .= '<span style="width:5em;display:inline-block"></span>银行卡四要素验证,手机号近三个月流量查询,手机号近三个月停机次数,银行卡归属地及卡类型查询<br>';
        // $display .= '代理 读秒分:邦秒验产品的 欺诈分, APP分, 多头分, 消费分, 通用分1, 通用分2, 通用分3, 通用分4<br>';
        // $display .= '自有 邦秒验自有分:通用分V2,V3以及各个活跃指数<br>';
        // $display .= '自有 金盾:金盾(不包含风险验证),金盾贷前<br>';
        // $display .= '自有 企服-品牌号:品牌号,电话名片<br>';
        // $display .= '自有 企服-神盾:除品牌号,电话名片外的企服产品以及风险验证<br>';
        $display .= '</td>';
        $display .= '</tr>';

        $display .= "</table><br/>";


        $display .= $this->remark;

        return $display;
    }

    /**
     * 创建第二个表格所需的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:58
     *
     * @return array
     */
    private function createTwiceTableInfo()
    {
        $base                 = [
            'total'       => 0,
            'bill_number' => 0,
            'income'      => 0,
            'cost'        => 0,
            'profit'      => 0,
        ];
        $total                = $base;
        $total['father_name'] = '合计';
        $total['sub_name']    = '';

        $result = [];
        array_walk($this->data['date'], function ($item) use (&$result, &$total, $base) {

            $ignore_count_id = 3100;
            //产品的父级ID
            $father_id = $item['father_id'];
            //产品ID
            $product_id = $item['product_id'];

            //展示的子产品
            $show_sub_product_id = $this->getShowSubProductId($father_id, $product_id);
            //展示的产品ID
            $show_product_id = $this->getShowProductId($father_id);

            # 每个展示父产品的数据
            if (!array_key_exists($show_product_id, $result)) {
                $result[$show_product_id]         = $base;
                $result[$show_product_id]['name'] = array_get($this->showProductIds, $show_product_id, '未定义');
                $result[$show_product_id]['data'] = [];
            }

            $result[$show_product_id]['total']       = bcadd($result[$show_product_id]['total'], $item['total'], 0);
            $result[$show_product_id]['bill_number'] = bcadd($result[$show_product_id]['bill_number'], $item['bill_number'], 0);
            
            $result[$show_product_id]['income'] = bcadd($result[$show_product_id]['income'], $item['income'], 6);
            $result[$show_product_id]['cost']   = bcadd($result[$show_product_id]['cost'], $item['cost'], 6);
            $result[$show_product_id]['profit'] = bcadd($result[$show_product_id]['profit'], $item['profit'], 6);

            #每个展示子产品的数据
            if (!array_key_exists($show_sub_product_id, $result[$show_product_id]['data'])) {
                $result[$show_product_id]['data'][$show_sub_product_id]         = $base;
                $result[$show_product_id]['data'][$show_sub_product_id]['name'] = RedisCache::instance('productId_productName_mapping')
                    ->get($show_sub_product_id);
            }
            $result[$show_product_id]['data'][$show_sub_product_id]['total']       = bcadd($result[$show_product_id]['data'][$show_sub_product_id]['total'], $item['total'], 0);
            $result[$show_product_id]['data'][$show_sub_product_id]['bill_number'] = bcadd($result[$show_product_id]['data'][$show_sub_product_id]['bill_number'], $item['bill_number'], 0);
            $result[$show_product_id]['data'][$show_sub_product_id]['income']      = bcadd($result[$show_product_id]['data'][$show_sub_product_id]['income'], $item['income'], 6);
            $result[$show_product_id]['data'][$show_sub_product_id]['cost']        = bcadd($result[$show_product_id]['data'][$show_sub_product_id]['cost'], $item['cost'], 6);
            $result[$show_product_id]['data'][$show_sub_product_id]['profit']      = bcadd($result[$show_product_id]['data'][$show_sub_product_id]['profit'], $item['profit'], 6);


            # 合计的数据
            //664产品总量、计费量不计入总计中
            if ($ignore_count_id != $father_id) {
                $total['total']       = bcadd($total['total'], $item['total'], 0);
                $total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            }
            $total['income'] = bcadd($total['income'], $item['income'], 6);
            $total['cost']   = bcadd($total['cost'], $item['cost'], 6);
            $total['profit'] = bcadd($total['profit'], $item['profit'], 6);
        });

        //过滤
        $result = array_map(function ($item) {
            $item['data'] = array_filter($item['data'], function ($item) {
                return 0 != floatval($item['income']);
            });

            return $item;
        }, $result);

        //排序
        $result = array_map(function ($item) {
            array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);

            return $item;
        }, $result);
        array_multisort(array_column($result, 'income'), SORT_DESC, $result);

        //补充合计数据
        array_unshift($result, $total);

        //计算毛利率
        return array_map(function ($item) {
            //毛利率
            $income              = array_get($item, 'income', 0);
            $profit              = array_get($item, 'profit', 0);
            $item['profit_rate'] = $this->getRate($profit, $income, 2);

            //数据格式化
            $item['total']       = $this->formatNumber($item['total']);
            $item['bill_number'] = $this->formatNumber($item['bill_number']);
            $item['income']      = $this->formatMoney($item['income']);
            $item['cost']        = $this->formatMoney($item['cost']);
            $item['profit']      = $this->formatMoney($item['profit']);

            if (array_key_exists('data', $item)) {
                $item['data'] = array_map(function ($item) {
                    //毛利率
                    $income              = array_get($item, 'income', 0);
                    $profit              = array_get($item, 'profit', 0);
                    $item['profit_rate'] = $this->getRate($profit, $income, 2);

                    //数据格式化
                    $item['total']       = $this->formatNumber($item['total']);
                    $item['bill_number'] = $this->formatNumber($item['bill_number']);
                    $item['income']      = $this->formatMoney($item['income']);
                    $item['cost']        = $this->formatMoney($item['cost']);
                    $item['profit']      = $this->formatMoney($item['profit']);

                    return $item;
                }, $item['data']);
            }

            return $item;
        }, $result);
    }

    /**
     * 生成第三个表格所需数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 20:42
     *
     * @return array
     */
    private function createThirdTableInfo()
    {
        $base   = [
            'total'       => 0,
            'bill_number' => 0,
            'income'      => 0,
            'cost'        => 0,
            'profit'      => 0,
        ];
        $total  = $base;
        $result = [];

        array_walk($this->data['month'], function ($item) use (&$result, &$total, $base) {
            //父产品ID
            $father_id = $item['father_id'];

            //部门ID
            $department_id = $this->getShowDepartmentId($father_id);

            ## 计算部门的数据
            if (!array_key_exists($department_id, $result)) {
                $result[$department_id]         = $base;
                $result[$department_id]['name'] = $this->department[$department_id];
                $result[$department_id]['data'] = [];
            }
            //664产品不计入小计、总计
            if (664 != $father_id) {
                $result[$department_id]['total']       = bcadd($result[$department_id]['total'], $item['total'], 0);
                $result[$department_id]['bill_number'] = bcadd($result[$department_id]['bill_number'], $item['bill_number'], 0);
            }
            $result[$department_id]['income'] = bcadd($result[$department_id]['income'], $item['income'], 6);
            $result[$department_id]['cost']   = bcadd($result[$department_id]['cost'], $item['cost'], 6);
            $result[$department_id]['profit'] = bcadd($result[$department_id]['profit'], $item['profit'], 6);

            ## 计算产品的数据
            if (!array_key_exists($father_id, $result[$department_id])) {
                $result[$department_id]['data'][$father_id]         = $base;
                $result[$department_id]['data'][$father_id]['name'] = RedisCache::instance('productId_productName_mapping')
                    ->get($father_id);
            }
            $result[$department_id]['data'][$father_id]['total']       = bcadd($result[$department_id]['data'][$father_id]['total'], $item['total'], 0);
            $result[$department_id]['data'][$father_id]['bill_number'] = bcadd($result[$department_id]['data'][$father_id]['bill_number'], $item['bill_number'], 0);
            $result[$department_id]['data'][$father_id]['income']      = bcadd($result[$department_id]['data'][$father_id]['income'], $item['income'], 6);
            $result[$department_id]['data'][$father_id]['cost']        = bcadd($result[$department_id]['data'][$father_id]['cost'], $item['cost'], 6);
            $result[$department_id]['data'][$father_id]['profit']      = bcadd($result[$department_id]['data'][$father_id]['profit'], $item['profit'], 6);

            ## 计算总数据
            //664产品不计入小计、总计
            if (664 != $father_id) {
                $total['total']       = bcadd($total['total'], $item['total'], 0);
                $total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            }
            $total['income'] = bcadd($total['income'], $item['income'], 6);
            $total['cost']   = bcadd($total['cost'], $item['cost'], 6);
            $total['profit'] = bcadd($total['profit'], $item['profit'], 6);

        });

        //排序
        ksort($result);
        $result = array_map(function ($item) {
            array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);

            return $item;
        }, $result);

        //补充合计数据
        array_unshift($result, $total);

        //计算毛利率
        return array_map(function ($item) {
            //毛利率
            $income              = array_get($item, 'income', 0);
            $profit              = array_get($item, 'profit', 0);
            $item['profit_rate'] = $this->getRate($profit, $income, 2);

            //数据格式化
            $item['total']       = $this->formatNumber($item['total']);
            $item['bill_number'] = $this->formatNumber($item['bill_number']);
            $item['income']      = $this->formatMoney($item['income']);
            $item['cost']        = $this->formatMoney($item['cost']);
            $item['profit']      = $this->formatMoney($item['profit']);

            if (array_key_exists('data', $item)) {
                $item['data'] = array_map(function ($item) {
                    //毛利率
                    $income              = array_get($item, 'income', 0);
                    $profit              = array_get($item, 'profit', 0);
                    $item['profit_rate'] = $this->getRate($profit, $income, 2);

                    //数据格式化
                    $item['total']       = $this->formatNumber($item['total']);
                    $item['bill_number'] = $this->formatNumber($item['bill_number']);
                    $item['income']      = $this->formatMoney($item['income']);
                    $item['cost']        = $this->formatMoney($item['cost']);
                    $item['profit']      = $this->formatMoney($item['profit']);

                    return $item;
                }, $item['data']);
            }

            return $item;
        }, $result);
    }

    /**
     * 生成第四个表格所需数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 20:42
     *
     * @return array
     */
    private function createFourthlyTableInfo()
    {
        $base = [
            //'month'       => '合计',
            'total'       => 0,
            'bill_number' => 0,
            'income'      => 0,
            'cost'        => 0,
            'profit'      => 0,
        ];

        $total         = $base;
        $total['name'] = '合计';

        #todo
        $result = [];
        array_walk($this->data['year'], function ($item) use (&$result, &$total, $base) {
            $month = $item['month'];
            $quarter = $this->getQuarter($month);
            ### 季度维度数据
            if (!array_key_exists($quarter, $result)) {
                $result[$quarter]         = $base;
                $result[$quarter]['q_id']         = $quarter;
                $result[$quarter]['name'] = array_get($this->quarterName, $quarter, '未定义');
                $result[$quarter]['data'] = [];
            }
            # 每个季度的数据
            $result[$quarter]['total']       = bcadd($result[$quarter]['total'], $item['total'], 0);
            $result[$quarter]['bill_number'] = bcadd($result[$quarter]['bill_number'], $item['bill_number'], 0);
            $result[$quarter]['income'] = bcadd($result[$quarter]['income'], $item['income'], 6);
            $result[$quarter]['cost']   = bcadd($result[$quarter]['cost'], $item['cost'], 6);
            $result[$quarter]['profit'] = bcadd($result[$quarter]['profit'], $item['profit'], 6);


            ### 月份维度数据
            if (!array_key_exists($quarter, $result[$quarter]['data'])) {
                $result[$quarter]['data'][$month]         = $base;
                $result[$quarter]['data'][$month]['name'] = $month;
            }
            # 每个月份的数据
            $result[$quarter]['data'][$month]['total']       = bcadd($result[$quarter]['data'][$month]['total'], $item['total'], 0);
            $result[$quarter]['data'][$month]['bill_number'] = bcadd($result[$quarter]['data'][$month]['bill_number'], $item['bill_number'], 0);
            $result[$quarter]['data'][$month]['income'] = bcadd($result[$quarter]['data'][$month]['income'], $item['income'], 6);
            $result[$quarter]['data'][$month]['cost']   = bcadd($result[$quarter]['data'][$month]['cost'], $item['cost'], 6);
            $result[$quarter]['data'][$month]['profit'] = bcadd($result[$quarter]['data'][$month]['profit'], $item['profit'], 6);

            ## 计算总数据
            $total['total']       = bcadd($total['total'], $item['total'], 0);
            $total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            $total['income']      = bcadd($total['income'], $item['income'], 6);
            $total['cost']        = bcadd($total['cost'], $item['cost'], 6);
            $total['profit']      = bcadd($total['profit'], $item['profit'], 6);

            return $item;
        });

        //dd($result);
        //排序
        array_multisort(array_column($result, 'q_id'), SORT_DESC, $result);

        //补充合计数据
        array_unshift($result, $total);

        //计算毛利率
        return array_map(function ($item) {
            //毛利率
            $income              = array_get($item, 'income', 0);
            $profit              = array_get($item, 'profit', 0);
            $item['profit_rate'] = $this->getRate($profit, $income, 2);

            //数据格式化
            $item['total']       = $this->formatNumber($item['total']);
            $item['bill_number'] = $this->formatNumber($item['bill_number']);
            $item['income']      = $this->formatMoney($item['income']);
            $item['cost']        = $this->formatMoney($item['cost']);
            $item['profit']      = $this->formatMoney($item['profit']);

            if (array_key_exists('data', $item)) {
                //同季度按月份排序
                $month_sort = array_column($item['data'], 'name');
                array_multisort($month_sort,SORT_DESC, $item['data']);

                $item['data'] = array_map(function ($item) {
                    //毛利率
                    $income              = array_get($item, 'income', 0);
                    $profit              = array_get($item, 'profit', 0);
                    $item['profit_rate'] = $this->getRate($profit, $income, 2);

                    //数据格式化
                    $item['total']       = $this->formatNumber($item['total']);
                    $item['bill_number'] = $this->formatNumber($item['bill_number']);
                    $item['income']      = $this->formatMoney($item['income']);
                    $item['cost']        = $this->formatMoney($item['cost']);
                    $item['profit']      = $this->formatMoney($item['profit']);

                    return $item;
                }, $item['data']);
            }

            return $item;
        }, $result);
    }

    /**
     * 生成父产品月度统计表格所需数据
     *
     * @return array
     * @throws Exception
     */
    private function createFatherIdMonthIncomeData()
    {
        $base = [
            'income'      => 0,
        ];

        $total         = $base;
        $total['name'] = '合计';

        $result['list'] = $result['total'] =$result['total_month'] = $result['sub_total'] = [];
        $result['total_all']=0.00;
        array_walk($this->data['month_sub_product'], function ($item) use (&$result, $base) {

            $month = $item['month'];
            $father_id = $item['father_id'];
            //由于邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 其他(领导特殊要求),要做处理
            if(in_array($father_id, $this->convertFatherId)){
                //注意月年数据没有product_id，刚好第一个表格数据不需要月 年的数据, 下面代码已经注释
                $father_id = $this->convertProductId($item['father_id'], $item['product_id']);//转换产品id
            }

            //展示的产品ID
            $show_product_id = $this->getCategoryProductByFatherId($father_id);

            //获取类别ID
            $cate_id = array_get($this->showProductNameAndCategory, $show_product_id, 0);

            //$father_name = array_get($this->showProductName, $show_product_id, '未定义');

            # 产品月份维度数据
            if (!array_key_exists($cate_id, $result['list'])) {
                $result['list'][$cate_id]         = [];
            }

            if (!array_key_exists($show_product_id, $result['list'][$cate_id])) {
                $result['list'][$cate_id][$show_product_id]         = [];
            }
            if (!array_key_exists($month, $result['list'][$cate_id][$show_product_id])) {
                $result['list'][$cate_id][$show_product_id][$month]         = $base;
                $result['list'][$cate_id][$show_product_id][$month]['month'] = $month;
            }
            $result['list'][$cate_id][$show_product_id][$month]['income'] = bcadd($result['list'][$cate_id][$show_product_id][$month]['income'], $item['income'], 2);

            # 计算产品总数据
            if (!array_key_exists($show_product_id, $result['total'])) {
                $result['total'][$show_product_id]         = $base;
            }
            $result['total'][$show_product_id]['income']= bcadd($result['total'][$show_product_id]['income'], $item['income'], 2);
            
            # 计算月度总数据
            if (!array_key_exists($month, $result['total_month'])) {
                $result['total_month'][$month]         = $base;
                $result['total_month'][$month]['name'] = ltrim(substr($month,4,2),'0').'月';
            }
            $result['total_month'][$month]['income']= bcadd($result['total_month'][$month]['income'], $item['income'], 2);

            # 计算分类数据(分类小计)
            if (!array_key_exists($cate_id, $result['sub_total'])) {
                $result['sub_total'][$cate_id]['data']         = [];
                $result['sub_total'][$cate_id]['total_income']         = 0;
            }
            if (!array_key_exists($month, $result['sub_total'][$cate_id]['data'])) {
                $result['sub_total'][$cate_id]['data'][$month]         = $base;
            }
            $result['sub_total'][$cate_id]['data'][$month]['income']= bcadd($result['sub_total'][$cate_id]['data'][$month]['income'], $item['income'], 2);
            $result['sub_total'][$cate_id]['total_income'] = bcadd($result['sub_total'][$cate_id]['total_income'], $item['income'], 2);

            //总计
            $result['total_all'] =bcadd($result['total_all'], $item['income'], 2);
            // 填充月份无数据的收入为0
            foreach(array_keys($result['total_month']) as $month){
                foreach( $result['list'] as $cate_id => $valfather){
                    foreach($valfather as $show_pid =>$item){
                        if (!array_key_exists($month, $result['list'][$cate_id][$show_pid])) {
                            $result['list'][$cate_id][$show_pid][$month]         = $base;
                            $result['list'][$cate_id][$show_pid][$month]['month'] = $month;
                        }
                    }
                }
            }
        });

        //小计数据处理
        foreach ($result['sub_total'] as $cate_id => $cate_data){
            krsort($result['sub_total'][$cate_id]['data']);
        }

        // // 按照产品总收入排序
        $total = $result['total'];
//        $list = $result['list'];
//        $list = array_map(function ($item) use($total) {
//                $allowkeys = array_keys($item);
//                $catetotal = array_filter($total,function($key)use($allowkeys){
//                    return in_array($key,$allowkeys);
//                },ARRAY_FILTER_USE_KEY);
//                array_multisort(array_column($catetotal, 'income'), SORT_DESC, $item);
//            return $item;
//        }, $list);

//        $result['list'] = $list;

        krsort($result['total_month']);
        foreach($result['list'] as $tidx => $tlist) {
            foreach ($tlist as $showpid => $pmonth_data) {
                krsort($result['list'][$tidx][$showpid]);
            }
        }
        ksort($result['list']);

        return $result;
    }

    private function getCateIdByFatherId($father_id,$product_id){

        //由于邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 其他(领导特殊要求),要做处理
        if(in_array($father_id, $this->convertFatherId)){//处理海外状态 海外时长
            //注意月年数据没有product_id，刚好第一个表格数据不需要月 年的数据, 下面代码已经注释
            $father_id = $this->convertProductId($father_id, $product_id);//转换产品id
        }
        //展示的产品ID
        $show_product_id = $this->getCategoryProductByFatherId($father_id);
        //获取类别ID
        return array_get($this->showProductNameAndCategory, $show_product_id, 0);
    }

    /**
     * 获取展示的子产品ID
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 18:39
     *
     * @param $father_id    integer 父产品ID
     * @param $product_id   integer 产品ID
     *
     * @return integer
     */
    private function getShowSubProductId($father_id, $product_id)
    {
        if (in_array($father_id, [200, 615, 10000, 3100])) {
            return $product_id;
        }

        return $father_id;
    }

    //返回第几季度
    private function getQuarter($month){
        //传参格式如202108
        $month = substr($month, 4, 2);
        if(in_array($month, ['01', '02', '03'])){
            return 1;
        }

        if(in_array($month, ['04', '05', '06'])){
            return 2;
        }

        if(in_array($month, ['07', '08', '09'])){
            return 3;
        }

        if(in_array($month, ['10', '11', '12'])){
            return 4;
        }

    }

    //征信机构-来源维度数据
    private function getSourceData(){

        $result = ['data' => [], 'total' => []];
        $source_map = [
            '0' => '直连',
            '1' => '征信',
            '2' => '数交所',
            '10' => '数交所',
        ];

        foreach ($this->data['source_date'] as $source => $item){
            if(isset($source_map[$source])){
                $show_name = $source_map[$source];
            }else{
                $show_name = '其它';
            }

            if(isset($result['data'][$show_name])){
                $result['data'][$show_name]['income'] = bcadd($result['data'][$show_name]['income'], $item['income'], 6);
                $result['data'][$show_name]['month_income'] = bcadd($result['data'][$show_name]['month_income'], $item['month_income'], 6);
                $result['data'][$show_name]['year_income'] = bcadd($result['data'][$show_name]['year_income'], $item['year_income'], 6);
            }else{
                $result['data'][$show_name]['income'] = $item['income'];
                $result['data'][$show_name]['month_income'] = $item['month_income'];
                $result['data'][$show_name]['year_income'] = $item['year_income'];
            }

            //存放日收入各来源总和、月收入各来源总和、年收入各来源总和
            if(empty($result['total'])){
                $result['total']['income'] = $item['income'];
                $result['total']['month_income'] = $item['month_income'];
                $result['total']['year_income'] = $item['year_income'];
            }else{
                $result['total']['income'] = bcadd($result['total']['income'], $item['income'], 6);
                $result['total']['month_income'] = bcadd($result['total']['month_income'], $item['month_income'], 6);
                $result['total']['year_income'] = bcadd($result['total']['year_income'], $item['year_income'], 6);
            }

        }

        array_multisort(array_column($result['data'], 'income'), SORT_DESC, $result['data']);
        return $result;
    }

    private function getCustomerData(){
        $base = [
            'total'       => 0,
            'income'      => 0,
            'year_income' => 0,//当年收入
            'cost'        => 0,//日成本
            'profit'      => 0,//日毛利
            'year_income' => 0,//当年收入
            'year_cost' => 0,//当年成本
            'year_profit' => 0,//当年毛利
        ];
        $total         = $base;
        $total['name'] = '合计';
        //$total['data'] = [];
        $result = ['data'=>[]];

        array_walk($this->data['source_customer_date'], function ($item) use (&$result, &$total, $base) {

            //客户名称
            $customer_name = RedisCache::instance('customerId_customerName_mapping')
                ->get($item['customer_id']) ?: $item['customer_id'];

            ### 客户维度数据
            if (!array_key_exists($customer_name, $result['data'])) {
                $result['data'][$customer_name]         = $base;
                $result['data'][$customer_name]['name'] = $customer_name;
                //$result['data'][$customer_name]['data'] = [];
            }

            ## 每个客户的数据
            $result['data'][$customer_name]['total']       = bcadd($result['data'][$customer_name]['total'], $item['total'], 0);
            $result['data'][$customer_name]['income'] = bcadd($result['data'][$customer_name]['income'], $item['income'], 6);
            $result['data'][$customer_name]['year_income'] = bcadd($result['data'][$customer_name]['year_income'], $item['year_income'], 6);
            $result['data'][$customer_name]['cost'] = bcadd($result['data'][$customer_name]['cost'], $item['cost'], 6);
            $result['data'][$customer_name]['year_cost'] = bcadd($result['data'][$customer_name]['year_cost'], $item['year_cost'], 6);

            $total['total'] = bcadd($total['total'], $item['total'], 0);
            $total['income'] = bcadd($total['income'], $item['income'], 6);
            $total['year_income'] = bcadd($total['year_income'], $item['year_income'], 6);
            $total['cost'] = bcadd($total['cost'], $item['cost'], 6);
            $total['year_cost'] = bcadd($total['year_cost'], $item['year_cost'], 6);

        });

        //补充合计数据
        array_unshift($result['data'], $total);

        //按客户日收入倒序排序
        array_multisort(array_column($result['data'], 'income'), SORT_DESC, $result['data']);

        return $result;
    }

    /**
     * 创建第一个表格所需的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:58
     *
     * @return array
     */
    private function createFirstTableInfo()
    {
        $base = [
            'total'       => 0,
            'bill_number' => 0,
            'income'      => 0,
            'cost'        => 0,
            'profit'      => 0,
            'month_income'=> 0,//当月收入
            'month_cost'  => 0,//当月成本
            'month_profit'=> 0,//当月毛利
            'year_income' => 0,//当年收入
            'year_cost'   => 0,//当年成本
            'year_profit' => 0,//当年毛利
        ];

        $total         = $base;
        $total['name'] = '合计';
        $result = [];
        #todo
        array_walk($this->data['date'], function ($item) use (&$result, &$total, $base) {
            $ignore_count_id = 3100;

            //产品的父级ID
            $father_id = $item['father_id'];

            //由于邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 其他(领导特殊要求),要做处理
            if(in_array($father_id, $this->convertFatherId)){
                //注意月年数据没有product_id，刚好第一个表格数据不需要月 年的数据, 下面代码已经注释
                $father_id = $this->convertProductId($item['father_id'], $item['product_id']);//转换产品id
            }

            //展示的产品ID
            $show_product_id = $this->getCategoryProductByFatherId($father_id);

            //获取类别ID
            $category_id = array_get($this->showProductNameAndCategory, $show_product_id, 0);

            ### 类别维度数据
            if (!array_key_exists($category_id, $result)) {
                $result[$category_id]         = $base;
                $result[$category_id]['name'] = array_get($this->category, $category_id, '未定义');
                $result[$category_id]['data'] = [];
            }
            # 每个类别的数据
            if ($ignore_count_id != $father_id) {//号码预警提示产品不计入总调用量/计费用量, 收入成本正常算进去(因为该产品量很大 钱却很少)
                $result[$category_id]['total']       = bcadd($result[$category_id]['total'], $item['total'], 0);
                $result[$category_id]['bill_number'] = bcadd($result[$category_id]['bill_number'], $item['bill_number'], 0);
            }
            $result[$category_id]['income'] = bcadd($result[$category_id]['income'], $item['income'], 6);
            $result[$category_id]['cost']   = bcadd($result[$category_id]['cost'], $item['cost'], 6);
            $result[$category_id]['profit'] = bcadd($result[$category_id]['profit'], $item['profit'], 6);

            ### 产品维度数据
            if (!array_key_exists($show_product_id, $result[$category_id]['data'])) {
                $result[$category_id]['data'][$show_product_id]         = $base;
                $result[$category_id]['data'][$show_product_id]['name'] = array_get($this->showProductName, $show_product_id, '未定义');
            }

            $result[$category_id]['data'][$show_product_id]['total']       = bcadd($result[$category_id]['data'][$show_product_id]['total'], $item['total'], 0);
            $result[$category_id]['data'][$show_product_id]['bill_number'] = bcadd($result[$category_id]['data'][$show_product_id]['bill_number'], $item['bill_number'], 0);        
            $result[$category_id]['data'][$show_product_id]['income'] = bcadd($result[$category_id]['data'][$show_product_id]['income'], $item['income'], 6);
            $result[$category_id]['data'][$show_product_id]['cost']   = bcadd($result[$category_id]['data'][$show_product_id]['cost'], $item['cost'], 6);
            $result[$category_id]['data'][$show_product_id]['profit'] = bcadd($result[$category_id]['data'][$show_product_id]['profit'], $item['profit'], 6);

            # 合计的数据
            //664产品,总量、计费用量不计入合计中
            if ($ignore_count_id != $father_id) {
                $total['total']       = bcadd($total['total'], $item['total'], 0);
                $total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            }
            $total['income'] = bcadd($total['income'], $item['income'], 6);
            $total['cost']   = bcadd($total['cost'], $item['cost'], 6);
            $total['profit'] = bcadd($total['profit'], $item['profit'], 6);
        });

        /*
        array_walk($this->data['month'], function ($item) use (&$result, &$total, $base) {
            //产品的父级ID
            $father_id = $item['father_id'];
            //展示的产品ID
            $show_product_id = $this->getCategoryProductByFatherId($father_id);

            //获取类别ID
            $category_id = array_get($this->showProductNameAndCategory, $show_product_id, 0);

            ### 类别维度数据

            if (!array_key_exists($category_id, $result)) {
                $result[$category_id]         = $base;
                $result[$category_id]['name'] = array_get($this->category, $category_id, '未定义');
                $result[$category_id]['data'] = [];
            }
            # 每个类别的数据
            if (664 != $father_id) {//号码预警提示产品不计入总调用量/计费用量, 收入成本正常算进去(因为该产品量很大 钱却很少)
                //$result[$category_id]['total']       = bcadd($result[$category_id]['total'], $item['total'], 0);
                //$result[$category_id]['bill_number'] = bcadd($result[$category_id]['bill_number'], $item['bill_number'], 0);
            }

            $result[$category_id]['month_income'] = bcadd($result[$category_id]['month_income'], $item['income'], 6);
            $result[$category_id]['month_cost']   = bcadd($result[$category_id]['month_cost'], $item['cost'], 6);
            $result[$category_id]['month_profit'] = bcadd($result[$category_id]['month_profit'], $item['profit'], 6);

            ### 产品维度数据

            if (!array_key_exists($show_product_id, $result[$category_id]['data'])) {
                $result[$category_id]['data'][$show_product_id]         = $base;
                $result[$category_id]['data'][$show_product_id]['name'] = array_get($this->showProductName, $show_product_id, '未定义');

            }
            # 每个产品的数据
            if (664 != $father_id) {
                //$result[$category_id]['data'][$show_product_id]['total']       = bcadd($result[$category_id]['data'][$show_product_id]['total'], $item['total'], 0);
                //$result[$category_id]['data'][$show_product_id]['bill_number'] = bcadd($result[$category_id]['data'][$show_product_id]['bill_number'], $item['bill_number'], 0);
            }

            $result[$category_id]['data'][$show_product_id]['month_income'] = bcadd($result[$category_id]['data'][$show_product_id]['month_income'], $item['income'], 6);
            $result[$category_id]['data'][$show_product_id]['month_cost']   = bcadd($result[$category_id]['data'][$show_product_id]['month_cost'], $item['cost'], 6);
            $result[$category_id]['data'][$show_product_id]['month_profit'] = bcadd($result[$category_id]['data'][$show_product_id]['month_profit'], $item['profit'], 6);

            # 合计的数据
            //664产品,总量、计费用量不计入合计中
            if (664 != $father_id) {
                //$total['total']       = bcadd($total['total'], $item['total'], 0);
                //$total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            }
            $total['month_income'] = bcadd($total['month_income'], $item['income'], 6);
            $total['month_cost']   = bcadd($total['month_cost'], $item['cost'], 6);
            $total['month_profit'] = bcadd($total['month_profit'], $item['profit'], 6);
        });
*/
        //dd($this->data['year_product']);
        array_walk($this->data['year_product'], function ($item) use (&$result, &$total, $base) {
            //产品的父级ID
            $father_id = $item['father_id'];

            //由于邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 其他(领导特殊要求),要做处理
            if(in_array($father_id, $this->convertFatherId)){
                //注意月年数据没有product_id
                $father_id = $this->convertProductId($item['father_id'], $item['product_id']);
            }
            //展示的产品ID
            $show_product_id = $this->getCategoryProductByFatherId($father_id);

            //获取类别ID
            $category_id = array_get($this->showProductNameAndCategory, $show_product_id, 0);

            ### 类别维度数据
            if (!array_key_exists($category_id, $result)) {
                //在这里理论上必须存在，如果不存在肯定有问题
                $result[$category_id]         = $base;
                $result[$category_id]['name'] = array_get($this->category, $category_id, '未定义');
                $result[$category_id]['data'] = [];
            }
            # 每个类别的数据
            if (664 != $father_id) {//号码预警提示产品不计入总调用量/计费用量, 收入成本正常算进去(因为该产品量很大 钱却很少)
                //$result[$category_id]['total']       = bcadd($result[$category_id]['total'], $item['total'], 0);
                //$result[$category_id]['bill_number'] = bcadd($result[$category_id]['bill_number'], $item['bill_number'], 0);
            }
            $result[$category_id]['year_income'] = bcadd($result[$category_id]['year_income'], $item['income'], 6);
            $result[$category_id]['year_cost']   = bcadd($result[$category_id]['year_cost'], $item['cost'], 6);
            $result[$category_id]['year_profit'] = bcadd($result[$category_id]['year_profit'], $item['profit'], 6);

            ### 产品维度数据
            if (!array_key_exists($show_product_id, $result[$category_id]['data'])) {
                //在这里理论上必须存在，如果不存在肯定有问题
                $result[$category_id]['data'][$show_product_id]         = $base;
                $result[$category_id]['data'][$show_product_id]['name'] = array_get($this->showProductName, $show_product_id, '未定义');
            }
            # 每个产品的数据
            if (664 != $father_id) {
                //$result[$category_id]['data'][$show_product_id]['total']       = bcadd($result[$category_id]['data'][$show_product_id]['total'], $item['total'], 0);
                //$result[$category_id]['data'][$show_product_id]['bill_number'] = bcadd($result[$category_id]['data'][$show_product_id]['bill_number'], $item['bill_number'], 0);
            }
            $result[$category_id]['data'][$show_product_id]['year_income'] = bcadd($result[$category_id]['data'][$show_product_id]['year_income'], $item['income'], 6);
            $result[$category_id]['data'][$show_product_id]['year_cost']   = bcadd($result[$category_id]['data'][$show_product_id]['year_cost'], $item['cost'], 6);
            $result[$category_id]['data'][$show_product_id]['year_profit'] = bcadd($result[$category_id]['data'][$show_product_id]['year_profit'], $item['profit'], 6);

            # 合计的数据
            //664产品,总量、计费用量不计入合计中
            if (664 != $father_id) {
                //$total['total']       = bcadd($total['total'], $item['total'], 0);
                //$total['bill_number'] = bcadd($total['bill_number'], $item['bill_number'], 0);
            }
            $total['year_income'] = bcadd($total['year_income'], $item['income'], 6);
            $total['year_cost']   = bcadd($total['year_cost'], $item['cost'], 6);
            $total['year_profit'] = bcadd($total['year_profit'], $item['profit'], 6);
        });
//        */

        //排序
        ksort($result);
        $result = array_map(function ($item) {
            if($item['name'] == '代理'){
                //邦秒验产品拆分显示 手机号在网时长 手机号在网状态 手机号三要素验证 实时在网状态 其他(领导特殊要求)
                //其他其他排序在代理分类中的最后面 因此特殊处理
                $tmp_detail = [];
                foreach($item['data'] as $key => $detail){
                    if($detail['name'] == '其他'){
                        $tmp_detail = $detail;
                        unset($item['data'][$key]);
                    }
                }
                array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);
                if(!empty($tmp_detail)) {
                    $item['data'][] = $tmp_detail;
                }
            }else if($item['name'] == '自有'){
                //存量洞察-邦运营、存量洞察-存量分层 排序在自有分类中的最后面 因此特殊处理
                $tmp_detail = [];
                foreach($item['data'] as $key => $detail){
                    if(in_array($detail['name'], ['存量洞察-邦运营', '存量洞察-存量分层'])){
                        $tmp_detail[] = $detail;
                        unset($item['data'][$key]);
                    }
                }
                array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);
                foreach ($tmp_detail as $tmp_detail_value){
                    $item['data'][] = $tmp_detail_value;
                }
            }else{
                array_multisort(array_column($item['data'], 'income'), SORT_DESC, $item['data']);
            }

            return $item;
        }, $result);

        //补充合计数据
        array_unshift($result, $total);

        $total_income = $total['income'];//总日收入
        $total_cost = $total['cost'];//总日成本
        $total_profit = $total['profit'];//总日毛利

        //计算毛利率
        return array_map(function ($item) use ($total_income, $total_cost, $total_profit){
            //毛利率
            $income              = array_get($item, 'income', 0);
            $profit              = array_get($item, 'profit', 0);
            $cost              = array_get($item, 'cost', 0);
            $item['profit_rate'] = $this->getRate($profit, $income, 2);

            //数据格式化
            $item['total']       = $this->formatNumber($item['total']);
            $item['bill_number'] = $this->formatNumber($item['bill_number']);
            $item['income']      = $this->formatMoney($item['income']);
            $item['day_income_rate'] = $this->getRate($income, $total_income, 2);
            $item['cost']        = $this->formatMoney($item['cost']);
            $item['day_cost_rate'] = $this->getRate($cost, $total_cost, 2);
            $item['profit']      = $this->formatMoney($item['profit']);
            $item['day_profit_rate'] = $this->getRate($profit, $total_profit, 2);

            /*
            $item['month_income'] = $this->formatMoney($item['month_income']);
            $item['month_cost']   = $this->formatMoney($item['month_cost']);
            $item['month_profit'] = $this->formatMoney($item['month_profit']);
            */
            $item['year_income']      = $this->formatMoney($item['year_income']);
//            $item['year_cost']        = $this->formatMoney($item['year_cost']);
            $item['year_profit']      = $this->formatMoney($item['year_profit']);
            //*/

            if (array_key_exists('data', $item)) {
                $item['data'] = array_map(function ($item) use ($total_income, $total_cost, $total_profit){
                    //毛利率
                    $income              = array_get($item, 'income', 0);
                    $profit              = array_get($item, 'profit', 0);
                    $cost                = array_get($item, 'cost', 0);
                    $item['profit_rate'] = $this->getRate($profit, $income, 2);

                    //数据格式化
                    $item['total']       = $this->formatNumber($item['total']);
                    $item['bill_number'] = $this->formatNumber($item['bill_number']);
                    $item['income']      = $this->formatMoney($item['income']);
                    $item['day_income_rate'] = $this->getRate($income, $total_income, 2);
                    $item['cost']        = $this->formatMoney($item['cost']);
                    $item['day_cost_rate'] = $this->getRate($cost, $total_cost, 2);
                    $item['profit']      = $this->formatMoney($item['profit']);
                    $item['day_profit_rate'] = $this->getRate($profit, $total_profit, 2);
                    $item['year_income']      = $this->formatMoney($item['year_income']);
                    $item['year_profit']      = $this->formatMoney($item['year_profit']);

                    return $item;
                }, $item['data']);
            }

            return $item;
        }, $result);
    }

    /**
     * 设置参数
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:12
     *
     * @return void
     */
    private function setParams()
    {
        //确认是否发送
        $this->checkSend();

        //设置所需要的日期
        $date       = $this->option('date');
        $this->date = $date ?: date('Ymd', strtotime('-1 days'));
        if ($this->date > date('Ymd') || !preg_match('/^\d{8}$/', $this->date)) {
            throw new Exception("日期格式不正确");
        }

        $this->customerIncomeRepository = new CustomerIncomeRepository();

		//给那个用户发送
		$username = $this->option('username');
		if ($username) {
			$this->username = explode(',', $username);
		} else {
			//获取需要接收邮件的所有用户
			$this->username = $this->getUsers();
		}

        $this->month            = date('Ym', strtotime($this->date));
        $this->firstDateOfMonth = date('Ymd', strtotime('first day of this month', strtotime($this->date)));
        $this->firstDateOfYear  = date('Y', strtotime($this->date)) . '0101';
        $this->totalNotInProductIds = array_column(Product::getHasProductFatherId(), 'product_id');
        // foreach($this->totalNotInProductIds as $k => $pid){
        //     if($pid == 50000){
        //         unset($this->totalNotInProductIds[$k]);
        //         break;
        //     }
        // }
        array_merge($this->totalNotInProductIds);

        // 检查收入 成本 利润 显示权限 和客户来源权限
        $baseStatRepository = new StatBaseRepository();
        foreach ($this->username  as $user) {
            $baseStatRepository->renderAuth($user);
            $this->financeAuthMap[$user] =$baseStatRepository->getFinanceAuthProduct();
        }

        // 获取产品分类
        $this->setCategory();

        //获取产品对外展示的名字 && 类别下展示名称和类别的关系 && 转换的父产品等
        $this->showProductName = ProductCategory::SHOW_PRODUCT_NAME;
        $this->showProductNameAndCategory = ProductCategory::SHOW_PRODUCT_NAME_AND_CATEGORY;
        $this->productCategoryMap = ProductCategory::PRODUCT_CATEGORY_MAP;
        $this->showProductName200 = ProductCategory::SHOW_PRODUCT_NAME_200;
        $this->showProductName50000 = ProductCategory::SHOW_PRODUCT_NAME_50000;
        $this->showProductName3100 = ProductCategory::SHOW_PRODUCT_NAME_3100;
        $this->showProductName30000 = ProductCategory::SHOW_PRODUCT_NAME_30000;
        $this->convertFatherId = ProductCategory::CONVERT_FATHER_ID;

        //查询本年的产品维度的数据
        $this->setData();

        //设置备注信息
        $this->setRemark();
        
    }

    /**
     * 
     *
     * @param [type] $username
     * @param integer $auth_type 哪些人可见成本利润  0代码控制 1 system_user_product 表控制
     * @return void
     */
    private function financeAuthFormat($username,$auth_type=1){
        if(in_array(-1,$this->financeAuthMap[$username]['user_money_product'])){
            $this->financeAuth['show_money'] = 1;
        }else{
            $this->financeAuth['show_money'] = 0;
        }

        if($auth_type==0){
            if(in_array($username,$this->authCostPorfit)){
            	$this->financeAuth['show_cost'] = 1;
                $this->financeAuth['show_profit'] = 1;

            }else{
            	$this->financeAuth['show_cost'] = 0;
                $this->financeAuth['show_profit'] = 0;
            }
        }else{
            if(in_array(-1,$this->financeAuthMap[$username]['user_cost_product'])){
                $this->financeAuth['show_cost'] = 1;
            }else{
                $this->financeAuth['show_cost'] = 0;
            }
            if($this->financeAuth['show_money']&&$this->financeAuth['show_cost']){
                $this->financeAuth['show_profit'] = 1;
            }else{
                $this->financeAuth['show_profit'] = 0;
            }
        }
	}

    /**
     * 校验是否发送
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/2/1 17:27
     *
     * @return void
     */
    private function checkSend()
    {
        $isSend = Redis::connection('default')
            ->get('is_send_profit');
        if (1 != $isSend) {
            throw new Exception("根据后台设置，停止发送报表");
        }
    }

    /**
     * 设置备注信息
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 17:47
     *
     * @return void
     */
    private function setRemark()
    {
        $data = BillNotes::where('cday', date('Y-m-d', strtotime($this->date)))
            ->pluck('notes')
            ->toArray();
        if ($data) {
            $this->remark .= '<b><font color="red" class="remark">提示：</font></b><br/>';
            foreach ($data as $item) {
                $this->remark .= "<font color=\"red\" class=\"remark\">{$item}</font><br/>";
            }
            $this->remark .= '<br/><br/>';
        }
    }

    /**
     * 获取产品分类和分类名称的map
     *
     * @return void
     */
    private function  setCategory(){
        $this->category = Category::pluck('category_name','category_id');
    }

    /**
     * 设置需要发送的各类数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:54
     *
     * @return void
     */
    private function setData()
    {
        //补充营收数据
        $this->fillIncome();

        //补充成本数据
        $this->fillCost();

        //补充总调用量数据
        $this->fillTotal();

        //计算数据
        $this->calculateProfit();
    }

    /**
     * 计算利润
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:42
     *
     * @return void
     */
    private function calculateProfit()
    {
        //计算当日的数据
        $this->data['date'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['date']);

        //计算当月的数据
        $this->data['month'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['month']);

        //计算当年的数据
        $this->data['year'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['year']);

        //计算当年产品维度的数据
        $this->data['year_product'] = array_map(function ($item) {
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);

            return $item;
        }, $this->data['year_product']);
    }

    /**
     * 补充总调用量数据
     * 总调用量定义：所有子产品成功调用量之和
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:01
     *
     * @return void
     */
    private function fillTotal()
    {
        //补充当日的总调用量数据
        $this->fillDateTotal();

        //补充当月的总调用量数据
        $this->fillMonthTotal();

        //补充当年的总调用量数据
        $this->fillYearTotal();
    }

    /**
     * 将营收数据补充到待发送数据中
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:56
     *
     * @return void
     */
    private function fillIncome()
    {
        //补充当日的营收数据
        $this->fillDateIncome();

        //补充本月的营收数据
        $this->fillMonthIncome();

        //补充本年的营收数据
        $this->fillYearIncome();
    }

    /**
     * 将成本数据补充到待发送数据中
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:34
     *
     * @return void
     */
    private function fillCost()
    {
        //补充当日的成本数据
        $this->fillDateCost();

        //补充当月的成本数据
        $this->fillMonthCost();

        //补充当年的成本数据
        $this->fillYearCost();
    }

    /**
     * 补充当月的成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillYearCost()
    {
        //填充当月的成本统计数据，并将其保存在容器中
        BillCostV2::getProfitMonthlyCost($this->firstDateOfYear,$this->date)
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

                //填充年成本(含source维度)
                //$this->fillYearItemWithSource($item->source, $month, $product_id, $money, $number, 'cost');

                //填充年成本到账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->apikey, $product_id, $item->source, $money, $number, 'year_cost');
            });

        //临时代码，填充本年的特殊apikey成本
        /*
        BillCost::select([
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '>=', $this->firstDateOfYear)
            ->where('date', '<=', $this->date)
            ->when($this->specialCostApikey, function($query) {
                return $query->whereIn('apikey', $this->specialCostApikey);
            })
            ->groupBy('month')
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $this->specialCostPid, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $this->specialCostPid, $money, $number, 'cost');
            });
        */
        CustomerBillAdjust::select([
            'customer_id',
            'product_id',
            'source',
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->where('date', '>=', $this->firstDateOfYear)
            ->where('date', '<=', $this->date)
            ->where('delete_at', '=', 0)
            ->groupBy('customer_id', 'product_id', 'source', 'month')
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

                //填充年成本(含source维度)
                //$this->fillYearItemWithSource($item->source, $month, $product_id, $money, $number, 'cost');

                //填充年成本到账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->customer_id, $product_id, $item->source, $money, $number, 'year_cost', 'customer_id');
            });

        //填充当月的渠道成本调整数据
        $this->getSpecialCostChannelMonth($this->firstDateOfYear, $this->date)
            ->each(function ($item) {
                $month      = $item->month;
                $month      = date('Ym', strtotime($month));
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

                //填充年成本(含source维度)
                //$this->fillYearItemWithSource($item->source, $month, $product_id, $money, $number, 'cost');

                //填充年成本到账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->customer_id, $product_id, $item->source, $money, $number, 'year_cost', 'customer_id');
            });

        //填充当月的固定费用成本数据
        $this->getSpecialCostFixedMonth($this->firstDateOfYear, $this->date)
            ->each(function ($item) {
                $month      = $item->month;
                $month      = date('Ym', strtotime($month));
                $product_id = $item->product_id;
                $number     = 0;
                $money      = $item->money;

                //填充量和钱
                $this->fillYearItem($month, $product_id, $money, $number, 'cost');

                $this->fillYearItemByProductId($month, $product_id, $money, $number, 'cost');

                //填充年成本(含source维度)
                //$this->fillYearItemWithSource($item->source, $month, $product_id, $money, $number, 'cost');

                //填充年成本到账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->apikey, $product_id, $item->source, $money, $number, 'year_cost');
            });

    }

    /**
     * 补充当月的成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillMonthCost()
    {
        //填充当月的成本统计数据，并将其保存在容器中
        $this->getBillCost($this->firstDateOfMonth, $this->date, $this->specialCostApikey)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

        //临时代码，计算特殊apikey的成本
        $spacialCost = BillCostV2::getProfitSpecialBillCost($this->firstDateOfMonth, $this->date, $this->specialCostApikey);
        if ($spacialCost) {
            $this->fillMonthItem($this->specialCostPid, $spacialCost['money'], $spacialCost['number'], 'cost');
        }

        //填充当月的特殊营收数据，并将其保存在容器中
        $this->getSpecialCost($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

        //填充当月的渠道成本调整数据
        $this->getSpecialCostChannel($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

        //填充当月的固定费用成本数据
        $this->getSpecialCostFixed($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = 0;
                $money      = $item->money;

                //填充量和钱
                $this->fillMonthItem($product_id, $money, $number, 'cost');
            });

    }

    /**
     * 补充当月的总调用量数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:05
     *
     * @return void
     */
    private function fillMonthTotal()
    {
        $this->getTotal($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $this->fillMonthItem($product_id, 0, $number, 'total');
            });
    }

    /**
     * 补充当年的总调用量数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:05
     *
     * @return void
     */
    private function fillYearTotal()
    {
        StatisticsCustomerUsage::select([
            'product_id',
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`total`) as number'),
        ])
            ->leftJoin('account', 'account.apikey', '=', 'statistics_customer_usage.apikey')
            ->whereNotIn('product_id', $this->totalNotInProductIds)
            ->where('date', '>=', $this->firstDateOfYear)
            ->where('date', '<=', $this->date)
            ->where('customer_id', '!=', $this->yuloreCustomerId)
            ->groupBy('product_id', 'month')
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $this->fillYearItem($month, $product_id, 0, $number, 'total');

                $this->fillYearItemByProductId($month, $product_id, 0, $number, 'total');
            });
    }

    /**
     * 补充本年应收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 10:39
     *
     * @return void
     */
    private function fillYearIncome()
    {
        //填充当年的营收账单数据，并将其保存在容器中
        BillProductIncomeV2::select([
            'apikey',
            'product_id',
            'source',
            DB::raw('LEFT(`date`, 6) as month'),
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '<=', $this->date)
            ->where('date', '>=', $this->firstDateOfYear)
            ->groupBy(['apikey', 'product_id', 'source', 'month'])
            ->get()
            ->each(function ($item) {
                $month      = $item->month;
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                //企服企业收入减半20221208
                //if($this->checkQifuByApikey($item->apikey)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition($item->apikey, '', $product_id)){
                    $money = bcdiv($money,2,6);
                };
                //补充到当日的账单中
                $this->fillYearItem($month, $product_id, $money, $number);
                // 根据产品id和年份分组 补充到当日的账单中
                $this->fillYearItemByProductId($month, $product_id, $money, $number);
                // 填充父产品月度
                $this->fillFatherIdMonth($month, $product_id, $money, $number);
                // 填充子产品月度
                $this->fillSubProductIdMonth($month, $product_id, $money, $number);

                //按来源填充子产品年数据
                //$this->fillYearItemWithSource($item->source, $month, $product_id, $money, $number);
                $this->fillYearItemWithSourceV2($item->source, $month, $money, $number);

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->apikey, $product_id, $item->source, $money, $number, 'year_income');
            });

        //填充当年的特殊营收数据，并将其保存在容器中
        CustomerExpend::select([
            'customer_id',
            'product_id',
            'type',
            'source',
            DB::raw('LEFT(`profile_show_date`, 7) as month'),
            DB::raw('SUM( `money` ) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->where('profile_show_date', '>=', date('Y-m-d', strtotime($this->firstDateOfYear)))
            ->where('profile_show_date', '<=', date('Y-m-d', strtotime($this->date)))
            ->groupBy(['customer_id', 'product_id', 'type', 'source', 'month'])
            ->get()
            ->each(function ($item) use (&$result) {
                $type       = $item->type;
                $month      = $item->month;
                $month      = date('Ym', strtotime($month));
                $product_id = $item->product_id;
                //企服企业收入减半20221208
                //if($this->checkQifuByCustomerId($item->customer_id)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition('', $item->customer_id, $product_id)){
                    $item->money = bcdiv($item->money,2,6);
                }
                if ($type == 1) {
                    $money  = bcsub(0, $item->money, 6);
                    $number = bcsub(0, $item->number, 0);
                } else {
                    $number = $item->number;
                    $money  = $item->money;
                }
                $this->fillYearItem($month, $product_id, $money, $number);

                //根据产品id和年份分组 补充到当日的账单中
                $this->fillYearItemByProductId($month, $product_id, $money, $number);
                // 填充父产品月度
                $this->fillFatherIdMonth($month, $product_id, $money, $number);
                // 填充子产品月度
                $this->fillSubProductIdMonth($month, $product_id, $money, $number);

                //按来源填充子产品年数据
                //$this->fillYearItemWithSource($item->source, $month, $product_id, $money, $number);
                $this->fillYearItemWithSourceV2($item->source, $month, $money, $number);

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->customer_id, $product_id, $item->source, $money, $number, 'year_income', 'customer_id');
            });
    }

    /**
     * 补充本月应收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 10:39
     *
     * @return void
     */
    private function fillMonthIncome()
    {
        //填充当月的营收账单数据，并将其保存在容器中
        $this->getBillIncome($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                // 企服企业收入减半20221208
                //if($this->checkQifuByApikey($item->apikey)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition($item->apikey, '', $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillMonthItem($product_id, $money, $number);
                //补充到当月的账单中 按source维度
                $this->fillMonthItemWithSource($item->source, $money, $number);
            });

        //填充当月的特殊营收数据，并将其保存在容器中
        $this->getSpecialIncome($this->firstDateOfMonth, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                //企服企业收入减半20221208
                //if($this->checkQifuByCustomerId($item->customer_id)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition('', $item->customer_id, $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillMonthItem($product_id, $money, $number);
                //补充到当月的账单中 按source维度
                $this->fillMonthItemWithSource($item->source, $money, $number);
            });
    }

    /**
     * 补充当日的成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillDateCost()
    {
        //填充当日的成本统计数据，并将其保存在容器中
        $this->getBillCost($this->date, $this->date, $this->specialCostApikey)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

                //补充到当日的账单中(含有source维度)
                //$this->fillDateItemWithSource($product_id, $item->source, $money, $number, 'cost');

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->apikey, $product_id, $item->source, $money, $number, 'cost');
            });

        //临时代码，计算特殊apikey的成本
//        $spacialCost = $this->getSpecialBillCost($this->date, $this->date, $this->specialCostApikey);
//        if ($spacialCost) {
//            $this->fillDateItem($this->specialCostPid, $spacialCost['money'], $spacialCost['number'], 'cost');
//        }

        //填充当日的特殊营收数据，并将其保存在容器中(客户成本调整)
        $this->getSpecialCost($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

                //补充到当日的账单中(含有source维度)
                //$this->fillDateItemWithSource($product_id, $item->source, $money, $number, 'cost');

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->customer_id, $product_id, $item->source, $money, $number, 'cost', 'customer_id');
            });

        //填充当日的渠道成本调整数据
        $this->getSpecialCostChannel($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

                //补充到当日的账单中(含有source维度)
                //$this->fillDateItemWithSource($product_id, $item->source, $money, $number, 'cost');

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->customer_id, $product_id, $item->source, $money, $number, 'cost', 'customer_id');
            });

        //填充当日的固定费用成本数据
        $this->getSpecialCostFixed($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;

                //填充量和钱
                $this->fillDateItem($product_id, $money, $number, 'cost');

                //补充到当日的账单中(含有source维度)
                //$this->fillDateItemWithSource($product_id, $item->source, $money, $number, 'cost');

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->apikey, $product_id, $item->source, $money, $number, 'cost');
            });
    }

    /**
     * 补充当日的营收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 15:58
     *
     * @return void
     */
    private function fillDateIncome()
    {
        //填充当日的营收账单数据，并将其保存在容器中
        $this->getBillIncome($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                // 企服企业收入减半20221208
                //if($this->checkQifuByApikey($item->apikey)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition($item->apikey, '', $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillDateItem($product_id, $money, $number);

                //补充到当日的账单中(区分soure来源 产品维度的数据)
                //$this->fillDateItemWithSource($product_id, $item->source, $money, $number);
                //补充到当日的账单中(区分soure来源 soure维度的数据)
                $this->fillDateItemWithSourceV2($item->source, $money, $number);

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->apikey, $product_id, $item->source, $money, $number);
            });

        //填充当日的特殊营收数据，并将其保存在容器中
        $this->getSpecialIncome($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                //企服企业收入减半20221208
                //if($this->checkQifuByCustomerId($item->customer_id)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition('', $item->customer_id, $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillDateItem($product_id, $money, $number);

                //补充到当日的账单中(区分soure来源和产品维度的数据)
                //$this->fillDateItemWithSource($product_id, $item->source, $money, $number);
                //补充到当日的账单中(soure维度的数据)
                $this->fillDateItemWithSourceV2($item->source, $money, $number);

                //补充到当日的账单中(区分soure来源 客户维度的数据)
                $this->fillDateItemWithCustomerSource($item->customer_id, $product_id, $item->source, $money, $number, 'income', 'customer_id');
            });
    }

    private function fillDateIncomeWithSource(){
        //填充当日的营收账单数据，并将其保存在容器中
        $this->getBillIncomeWithSource($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                // 企服企业收入减半20221208
                if($this->checkQifuByApikey($item->apikey)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillDateItem($product_id, $money, $number);
            });

        $this->getBillIncome($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                // 企服企业收入减半20221208
                //if($this->checkQifuByApikey($item->apikey)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition($item->apikey, '', $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillDateItem($product_id, $money, $number);
            });

        //填充当日的特殊营收数据，并将其保存在容器中
        $this->getSpecialIncome($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $money      = $item->money;
                //企服企业收入减半20221208
                //if($this->checkQifuByCustomerId($item->customer_id)){
                if($this->customerIncomeRepository->isHalveIncomeByCondition('', $item->customer_id, $product_id)){
                    $money = bcdiv($money,2,6);
                }
                //补充到当日的账单中
                $this->fillDateItem($product_id, $money, $number);
            });
    }

    /**
     * 补充当日的总调用量数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:05
     *
     * @return void
     */
    private function fillDateTotal()
    {
        $this->getTotal($this->date, $this->date)
            ->each(function ($item) {
                $product_id = $item->product_id;
                $number     = $item->number;
                $this->fillDateItem($product_id, 0, $number, 'total');
                //区分source来源 产品维度 填充日总量指标
                //$this->fillDateItemWithSource($product_id, $item->source, 0, $number, 'total');

                //区分source来源 客户维度 填充日总量指标
                $this->fillDateItemWithCustomerSource($item->apikey, $product_id, $item->source, 0, $number, 'total');
            });
    }

    /**
     * 填充当日的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:03
     *
     * @param $product_id integer 子产品ID
     * @param $money      float 金额
     * @param $number     integer 计费用量
     * @param $type       string 数据类型
     *
     * @return void
     */
    private function fillDateItem($product_id, $money, $number, $type = 'income')
    {
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;

        if (!array_key_exists($product_id, $this->data['date'])) {
            $this->data['date'][$product_id] = [
                //产品ID
                'product_id'  => $product_id,
                //父产品ID
                'father_id'   => $father_id,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['date'][$product_id]['income'] = bcadd($this->data['date'][$product_id]['income'], $money, 6);

                //邦秒配按总查询量计算计费用量,不是邦秒配的产品正常从账单表中取量(账单表中量即计费量)即可
                if ($product_id != 604) {
                    $this->data['date'][$product_id]['bill_number'] = bcadd($this->data['date'][$product_id]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['date'][$product_id]['cost'] = bcadd($this->data['date'][$product_id]['cost'], $money, 6);
                break;
            case 'total':
                $this->data['date'][$product_id]['total'] = bcadd($this->data['date'][$product_id]['total'], $number, 0);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id == 604) {
                    $this->data['date'][$product_id]['bill_number'] = bcadd($this->data['date'][$product_id]['bill_number'], $number, 0);
                }

                break;
        }
    }


    private function fillDateItemWithCustomerSource($customer, $product_id, $source, $money, $number, $type = 'income', $user_type='apikey'){
        if($user_type == 'apikey'){
            //客户ID
            $customer_id = RedisCache::instance('apikey_customerId_mapping')
                ->get($customer) ?: $customer;
        }else{
            $customer_id = $customer;
        }

//        //父产品ID
//        $father_id = RedisCache::instance('productId_fatherId_mapping')
//            ->get($product_id) ?: $product_id;

        $key = $source.'_'.$customer_id;
        if (!isset($this->data['source_customer_date'][$key])) {
            $this->data['source_customer_date'][$key] = [
                //客户ID
                'customer_id'  => $customer_id,
                //来源
                'source'   => $source,
                //总调用量
                'total'       => 0,
                //当日营收金额
                'income'      => 0,
                //当年营收金额
                'year_income'      => 0,
                //成本金额
                'cost'        => 0,
                'year_cost'        => 0,
                //计费用量
                'bill_number' => 0,
                //年计费用量
                'year_bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['source_customer_date'][$key]['income'] = bcadd($this->data['source_customer_date'][$key]['income'], $money, 6);

                //邦秒配按总查询量计算计费用量,不是邦秒配的产品正常从账单表中取量(账单表中量即计费量)即可
                if ($product_id != 604) {
                    $this->data['source_customer_date'][$key]['bill_number'] = bcadd($this->data['source_customer_date'][$key]['bill_number'], $number, 0);
                }
                break;
            case 'year_income':
                //金额相加
                $this->data['source_customer_date'][$key]['year_income'] = bcadd($this->data['source_customer_date'][$key]['year_income'], $money, 6);

                //邦秒配按总查询量计算计费用量,不是邦秒配的产品正常从账单表中取量(账单表中量即计费量)即可
                if ($product_id != 604) {
                    $this->data['source_customer_date'][$key]['year_bill_number'] = bcadd($this->data['source_customer_date'][$key]['year_bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['source_customer_date'][$key]['cost'] = bcadd($this->data['source_customer_date'][$key]['cost'], $money, 6);
                break;
            case 'year_cost':
                $this->data['source_customer_date'][$key]['year_cost'] = bcadd($this->data['source_customer_date'][$key]['year_cost'], $money, 6);
                break;
            case 'total':
                $this->data['source_customer_date'][$key]['total'] = bcadd($this->data['source_customer_date'][$key]['total'], $number, 0);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id == 604) {
                    $this->data['source_customer_date'][$key]['bill_number'] = bcadd($this->data['source_customer_date'][$key]['bill_number'], $number, 0);
                }

                break;
        }
    }

    private function fillDateItemWithSourceV2($source, $money, $number, $type = 'income'){

        $key = $source;
        if (!isset($this->data['source_date'][$key])) {
            $this->data['source_date'][$key] = [
                //来源
                'source'   => $source,
                //当日营收金额
                'income'      => 0,
                //当月营收金额
                'month_income'      => 0,
                //当年营收金额
                'year_income'      => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['source_date'][$key]['income'] = bcadd($this->data['source_date'][$key]['income'], $money, 6);
                break;
            case 'cost':
                $this->data['source_date'][$key]['cost'] = bcadd($this->data['source_date'][$key]['cost'], $money, 6);
                break;
        }
    }

    private function fillDateItemWithSource($product_id, $source, $money, $number, $type = 'income'){
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;

        $key = $source.'_'.$product_id;
        if (!isset($this->data['source_date'][$key])) {
            $this->data['source_date'][$key] = [
                //产品ID
                'product_id'  => $product_id,
                //父产品ID
                'father_id'   => $father_id,
                //来源
                'source'   => $source,
                //总调用量
                'total'       => 0,
                //当日营收金额
                'income'      => 0,
                //当年营收金额
                'year_income'      => 0,
                //成本金额
                'cost'        => 0,
                'year_cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['source_date'][$key]['income'] = bcadd($this->data['source_date'][$key]['income'], $money, 6);

                //邦秒配按总查询量计算计费用量,不是邦秒配的产品正常从账单表中取量(账单表中量即计费量)即可
                if ($product_id != 604) {
                    $this->data['source_date'][$key]['bill_number'] = bcadd($this->data['source_date'][$key]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['source_date'][$key]['cost'] = bcadd($this->data['source_date'][$key]['cost'], $money, 6);
                break;
            case 'total':
                $this->data['source_date'][$key]['total'] = bcadd($this->data['source_date'][$key]['total'], $number, 0);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id == 604) {
                    $this->data['source_date'][$key]['bill_number'] = bcadd($this->data['source_date'][$key]['bill_number'], $number, 0);
                }

                break;
        }

    }


    /**
     * 填充当月的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:03
     *
     * @param $product_id integer 子产品ID
     * @param $money      float 金额
     * @param $number     integer 计费用量
     * @param $type       string 数据类型
     *
     * @return void
     */
    private function fillMonthItem($product_id, $money, $number, $type = 'income')
    {
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;

        if (!array_key_exists($father_id, $this->data['month'])) {
            $this->data['month'][$father_id] = [
                //父产品ID
                'father_id'   => $father_id,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['month'][$father_id]['income'] = bcadd($this->data['month'][$father_id]['income'], $money, 6);
                //如果是邦秒配，它的计费用量就是总查询量,不是邦秒配的产品正常从账单表中取量(账单表中量即计费量)即可
                if ($product_id != 604) {
                    $this->data['month'][$father_id]['bill_number'] = bcadd($this->data['month'][$father_id]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['month'][$father_id]['cost'] = bcadd($this->data['month'][$father_id]['cost'], $money, 6);
                break;
            case 'total':
                $this->data['month'][$father_id]['total'] = bcadd($this->data['month'][$father_id]['total'], $number, 0);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id == 604) {
                    $this->data['month'][$father_id]['bill_number'] = bcadd($this->data['month'][$father_id]['bill_number'], $number, 0);
                }
                break;
        }
    }

    /**
     * 填充当年的数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:03
     *
     * @param $month      integer 月份
     * @param $product_id integer 子产品ID
     * @param $money      float 金额
     * @param $number     integer 计费用量
     * @param $type       string 数据类型
     *
     * @return void
     */
    private function fillYearItem($month, $product_id, $money, $number, $type = 'income')
    {
        if (!array_key_exists($month, $this->data['year'])) {
            $this->data['year'][$month] = [
                //父产品ID
                'month'       => $month,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['year'][$month]['income'] = bcadd($this->data['year'][$month]['income'], $money, 6);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id != 604 && 664 != $product_id) {
                    $this->data['year'][$month]['bill_number'] = bcadd($this->data['year'][$month]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['year'][$month]['cost'] = bcadd($this->data['year'][$month]['cost'], $money, 6);
                break;
            case 'total':
                //664产品不计入计费用量
                if (664 != $product_id) {
                    $this->data['year'][$month]['total'] = bcadd($this->data['year'][$month]['total'], $number, 0);
                    //如果是邦秒配批量版，它的计费用量就是总查询量
                    if ($product_id == 604) {
                        $this->data['year'][$month]['bill_number'] = bcadd($this->data['year'][$month]['bill_number'], $number, 0);
                    }
                }

                break;
        }
    }

    private function fillYearItemByProductId($month, $product_id, $money, $number, $type = 'income')
    {
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;
        //$year = substr($month, 0, 4);
        if (!array_key_exists($product_id, $this->data['year_product'])) {
            $this->data['year_product'][$product_id] = [
                //父产品ID
                'father_id'       => $father_id,
                //子产品
                'product_id'      => $product_id,
                //总调用量
                'total'       => 0,
                //营收金额
                'income'      => 0,
                //成本金额
                'cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['year_product'][$product_id]['income'] = bcadd($this->data['year_product'][$product_id]['income'], $money, 6);
                //如果是邦秒配，它的计费用量就是总查询量
                if ($product_id != 604 && 664 != $product_id) {
                    $this->data['year_product'][$product_id]['bill_number'] = bcadd($this->data['year_product'][$product_id]['bill_number'], $number, 0);
                }
                break;
            case 'cost':
                $this->data['year_product'][$product_id]['cost'] = bcadd($this->data['year_product'][$product_id]['cost'], $money, 6);
                break;
            case 'total':
                //664产品不计入计费用量
                if (664 != $product_id) {
                    $this->data['year_product'][$product_id]['total'] = bcadd($this->data['year_product'][$product_id]['total'], $number, 0);
                    //如果是邦秒配批量版，它的计费用量就是总查询量
                    if ($product_id == 604) {
                        $this->data['year_product'][$product_id]['bill_number'] = bcadd($this->data['year_product'][$product_id]['bill_number'], $number, 0);
                    }
                }

                break;
        }
    }

    private function fillYearItemWithSourceV2($source, $month, $money, $number, $type = 'income'){

        $key =  $source;
        if (!isset($this->data['source_date'][$key])) {
            $this->data['source_date'][$key] = [
                //月份
                //'month'       => $month,
                'source' =>$source,
                //当日营收金额
                'income'      => 0,
                //当月营收金额
                'month_income'      => 0,
                //当年营收金额
                'year_income'      => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['source_date'][$key]['year_income'] = bcadd($this->data['source_date'][$key]['year_income'], $money, 6);
                break;
            case 'cost':
                $this->data['source_date'][$key]['year_cost'] = bcadd($this->data['source_date'][$key]['year_cost'], $money, 6);
                break;
        }
    }

    private function fillMonthItemWithSource($source, $money, $number, $type = 'income'){

        $key =  $source;
        if (!isset($this->data['source_date'][$key])) {
            $this->data['source_date'][$key] = [
                'source' =>$source,
                //当日营收金额
                'income'      => 0,
                //当月营收金额
                'month_income'      => 0,
                //当年营收金额
                'year_income'      => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['source_date'][$key]['month_income'] = bcadd($this->data['source_date'][$key]['month_income'], $money, 6);
                break;
            case 'cost':
                $this->data['source_date'][$key]['month_cost'] = bcadd($this->data['source_date'][$key]['month_cost'], $money, 6);
                break;
        }
    }

    private function fillYearItemWithSource($source, $month, $product_id, $money, $number, $type = 'income'){
        //子产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;
        $key =  $source.'_'.$product_id;
        if (!isset($this->data['source_date'][$key])) {
            $this->data['source_date'][$key] = [
                //父产品ID
                'father_id'       => $father_id,
                //月份
                'month'       => $month,
                'product_id' =>$product_id,
                'source' =>$source,
                //总调用量
                'total'       => 0,
                //当日营收金额
                'income'      => 0,
                //当年营收金额
                'year_income'      => 0,
                //成本金额
                'cost'        => 0,
                'year_cost'        => 0,
                //计费用量
                'bill_number' => 0,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['source_date'][$key]['year_income'] = bcadd($this->data['source_date'][$key]['year_income'], $money, 6);
                break;
            case 'cost':
                $this->data['source_date'][$key]['year_cost'] = bcadd($this->data['source_date'][$key]['year_cost'], $money, 6);
                break;
        }
    }

    private function fillSubProductIdMonth($month, $product_id, $money, $number, $type = 'income')
    {
        //子产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;
        $key =  $product_id.'_'.$month;
        if (!array_key_exists($key, $this->data['month_sub_product'])) {
            $this->data['month_sub_product'][$key] = [
                //父产品ID
                'father_id'       => $father_id,
                //月份
                'month'       => $month,
                //营收金额
                'income'      => 0,
                //
                'product_id' =>$product_id,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['month_sub_product'][$key]['income'] = bcadd($this->data['month_sub_product'][$key]['income'], $money, 6);
                break;
        }
    }

    private function fillFatherIdMonth($month, $product_id, $money, $number, $type = 'income')
    {
        //父产品ID
        $father_id = RedisCache::instance('productId_fatherId_mapping')
            ->get($product_id) ?: $product_id;
        $key =  $father_id.'_'.$month;
        if (!array_key_exists($key, $this->data['month_product'])) {
            $this->data['month_product'][$key] = [
                //父产品ID
                'father_id'       => $father_id,
                //月份
                'month'       => $month,
                //营收金额
                'income'      => 0,
                //
                'product_id' =>$product_id,
            ];
        }

        switch ($type) {
            case 'income':
                //金额相加
                $this->data['month_product'][$key]['income'] = bcadd($this->data['month_product'][$key]['income'], $money, 6);
                break;
        }
    }

    /**
     * 确认每一个父产品的部门
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/29 11:00
     *
     * @param $father_id integer 父产品ID
     *
     * @return integer 部门ID
     */
    private function getShowDepartmentId($father_id)
    {
        if (in_array($father_id, [101, 210, 1000, 105, 501, 615, 616, 612, 664])) {
            return 0;//一部
        }

        if (in_array($father_id, [200, 401, 104, 601, 604])) {
            return 1;//二部
        }

        if (in_array($father_id, [10000])) {
            return 2;//三部
        }

        //其他的，有新增产品需要调整
        return 0;
    }

    /**
     * 确认数据那个产品的数据(产品类型)
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 16:48
     *
     * @param $fahter_id  integer 父产品ID
     *
     * @return integer 展示的产品KEY
     */
    private function getShowProductId($father_id)
    {
        if (200 == $father_id) {
            return 0;
        }

        if (in_array($father_id, [210, 1000, 101, 105, 501])) {
            return 1;
        }

        if (in_array($father_id, [615, 612, 664, 616])) {
            return 2;
        }

        if (401 == $father_id) {
            return 3;
        }

        if (in_array($father_id, [104, 601, 604])) {
            return 4;
        }

        if (in_array($father_id, [10000])) {
            return 5;
        }

        if (in_array($father_id, [30000])) {
            return 6;
        }
        if (in_array($father_id, [3100])) {
            return 7;
        }
        //未识别的都算作[号码风险等级]
        return 2;
        //throw new \Exception("未识别的产品ID{$father_id}");
    }

    //根据父产品id返回所属类别下的展示产品
    private function getCategoryProductByFatherId($father_id){
        return $this->productCategoryMap[$father_id] ?? 13;//如果不存在，则归类到 类别为自有、产品为其他中
    }

    private function convertProductId($father_id, $product_id){
        switch ($father_id){
            case 200:
                if(in_array($product_id,$this->showProductName200)){
                    return $father_id.'-'.$product_id;
                }
                return '200-other';
            case 3100:
                if(in_array($product_id,$this->showProductName3100)){
                    return $father_id.'-'.$product_id;
                }
                return '3100-other';
            case 30000:
                if(in_array($product_id,$this->showProductName30000)){
                    return $father_id.'-'.$product_id;
                }
                return '30000-other';
            case 50000:
                if(in_array($product_id,$this->showProductName50000)){
                    return $father_id.'-'.$product_id;
                }
                return '50000-other';
            default:
                throw new Exception('转化产品id异常');
        }
    }


    /**
     * 计算利润
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:33
     *
     * @param $left     integer 分子
     * @param $right    integer 分母
     * @param $decimal  integer 小数位数
     *
     * @return string
     */
    private function getRate($left, $right, $decimal)
    {
        if (0 == intval($right)) {
            return 'NA';
        }

        return round(bcmul(bcdiv($left, $right, $decimal + 4), 100, $decimal + 2), $decimal) . '%';
    }

    /**
     * 对量格式化
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/22 15:11
     *
     * @param $number float 数值
     *
     * @return integer
     */
    private function formatNumber($number)
    {
//        return number_format($number, 0);
        return $number;
    }

    /**
     * 对金额格式化
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/22 15:12
     *
     * @param $money float 金额
     *
     * @return integer
     */
    private function formatMoney($money)
    {
        return round($money, 1);
//        return number_format(round($money, 1), 1);
    }

    /**
     * 获取特殊营收数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:09
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     *
     * @return Collection
     */
    private function getSpecialIncome($start_date, $end_date)
    {
        return CustomerExpend::select([
            'customer_id',
            'product_id',
            'type',
            'source',
            DB::raw('SUM( `money` ) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
            ->where('profile_show_date', '>=', date('Y-m-d', strtotime($start_date)))
            ->where('profile_show_date', '<=', date('Y-m-d', strtotime($end_date)))
            ->groupBy(['customer_id', 'product_id', 'type', 'source'])
            ->get()
            ->map(function ($item) use (&$result) {
                $type = $item->type;
                if ($type == 1) {
                    $item->money  = bcsub(0, $item->money, 6);
                    $item->number = bcsub(0, $item->number, 0);
                }

                return $item;
            });
    }

    /**
     * 获取营收账单数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:08
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     *
     * @return Collection
     */
    private function getBillIncome($start_date, $end_date)
    {
        return BillProductIncomeV2::select([
            'apikey',
            'product_id',
            'source',
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`number`) as number'),
        ])
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->groupBy(['apikey', 'product_id', 'source'])
            ->get();
    }


    /**
     * 获取成本账单数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 14:58
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     * @param $specialApikey   array 不参与计算的apikey
     *
     * @return Collection
     */
    private function getBillCost($start_date, $end_date, $specialApikey=[]) {
        return BillCostV2::getProfitBillCost($start_date, $end_date, $specialApikey);
    }

    /**
     * 获取特殊成本数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:09
     *
     * @param $start_date integer 开始日期
     * @param $end_date   integer 截止日期
     *
     * @return Collection
     */
    private function getSpecialCost($start_date, $end_date)
    {
        /*
        return UpstreamBillAdjust::select([
            'product_id',
            DB::raw('SUM(`money`) as money'),
            DB::raw('SUM(`fee_number`) as number'),
        ])
                                 ->where('date', '>=', date('Y-m-d', strtotime($start_date)))
                                 ->where('date', '<=', date('Y-m-d', strtotime($end_date)))
                                 ->groupBy('product_id')
                                 ->get();
        */
        return CustomerBillAdjust::getProductCost($start_date, $end_date);


    }

    private function getSpecialCostChannel($start_date, $end_date)
    {

        return ChannelAccountAdjust::getProductCost($start_date, $end_date);

    }

    private function getSpecialCostChannelMonth($start_date, $end_date)
    {

        return ChannelAccountAdjust::getProductMonthCost($start_date, $end_date);

    }

    private function getSpecialCostFixed($start_date, $end_date)
    {

        return ChannelAccountFixedFee::getProductCost($start_date, $end_date);

    }

    private function getSpecialCostFixedMonth($start_date, $end_date)
    {

        return ChannelAccountFixedFee::getProductMonthCost($start_date, $end_date);

    }

    /**
     * 获取产品的总调用量
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/28 17:07
     *
     * @param $start_date  integer 开始日期
     * @param $end_date    integer 截止日期
     *
     * @return Collection
     */
    private function getTotal($start_date, $end_date)
    {
        return StatisticsCustomerUsage::select(['statistics_customer_usage.apikey as apikey', 'product_id', 'source', DB::raw('SUM(`total`) as number')])
            ->leftJoin('account', 'account.apikey', '=', 'statistics_customer_usage.apikey')
            ->whereNotIn('product_id', $this->totalNotInProductIds)
            ->where('date', '>=', $start_date)
            ->where('date', '<=', $end_date)
            ->where('customer_id', '!=', $this->yuloreCustomerId)
            ->groupBy(['apikey', 'product_id', 'source'])
            ->get();
    }

    /**
     * 获取朴道数据
     */
    protected function getPudaoDatas()
    {
        $filterProductIds = $this->totalNotInProductIds;
        $params = [
            'start_date' => $this->date,
            'end_date' => $this->date,
            'source' => StatisticsCustomerUsage::$source['朴道']
        ];

        $productCostRepository = new ProductCostRepository();
        $productIncomeRepository = new ProductIncomeRepository();

        //获取朴道日调用量
        $where = [
            ['date', '>=', $this->date],
            ['date', '<=', $this->date],
            ['source', StatisticsCustomerUsage::$source['朴道']]
        ];

        $pudaoUsage = StatisticsCustomerUsage::getStatList($where, null, null, null, $filterProductIds);
        $pudaoUsage = count($pudaoUsage) > 0 ? $pudaoUsage->toArray() : [];


        $statisticsCustomerUsage = new StatisticsCustomerUsage();

        //产品维度日调用总量
        $totalWhere = $totalWhere = [
            ['date', '>=', $this->date],
            ['date', '<=', $this->date]
        ];
        $fields = ['product_id', DB::raw('sum(total) AS total')];
        $dayTotalDatas = $statisticsCustomerUsage->select($fields)
                                            ->where($totalWhere)
                                            ->whereNotIn('product_id', $filterProductIds)
                                            ->groupBy('product_id')
                                            ->get();
        $dayTotalDatas = count($dayTotalDatas) > 0 ? $dayTotalDatas->toArray() : [];

        //客户维度日调用总量
        $CusTotalWhere = $totalWhere = [
            ['date', '>=', $this->date],
            ['date', '<=', $this->date]
        ];
        $cusFields = ['apikey', DB::raw('sum(total) AS total')];
        $cusDayTotalDatas = $statisticsCustomerUsage->select($cusFields)
            ->where($CusTotalWhere)
            ->whereNotIn('product_id', $filterProductIds)
            ->groupBy('apikey')
            ->get();
        $cusDayTotalDatas = count($cusDayTotalDatas) > 0 ? $cusDayTotalDatas->toArray() : [];

        //日收入
        $income = $productIncomeRepository->getProductIncomeTotalList($params);

        //日成本
        $cost = $productCostRepository->getProductCostTotalList($params);
        $yearParams = [
            'start_date' => $this->firstDateOfYear,
            'end_date' => $this->date,
            'source' => StatisticsCustomerUsage::$source['朴道']
        ];

        //当年收入
        $yearIncome = $productIncomeRepository->getProductIncomeTotalList($yearParams);

        //当年成本
        $yearCost = $productCostRepository->getProductCostTotalList($yearParams);

        $productIds = array_unique(array_merge(array_column($pudaoUsage,'product_id'),array_column($yearIncome,'product_id'),array_column($yearCost,'product_id'), array_column($dayTotalDatas,'product_id')));
        $fatherMap = [];
        foreach ($productIds as $id) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($id) ?: $id;
            $father_name = RedisCache::instance('productId_productName_mapping')->get($father_id);
            $fatherMap[$id] = [
                'father_id' => $father_id,
                'father_name' => $father_name,
            ];
        }

        $apikeys = array_unique(array_merge(array_column($cusDayTotalDatas, 'apikey'), array_column($pudaoUsage,'apikey')));
        $apikeyMap = [];
        foreach ($apikeys as $id) {
            $customerId = RedisCache::instance('apikey_customerId_mapping')->get($id);
            $customerName = RedisCache::instance('customerId_customerName_mapping')->get($customerId);
            $apikeyMap[$id] = [
                'customer_id' => $customerId,
                'customer_name' => $customerName,
            ];
        }
        $tmpCustomerIds = array_unique(array_merge(array_column($yearIncome, 'customer_id'),array_column($yearCost, 'customer_id')));
        $cusotmerMap = [];
        foreach ($tmpCustomerIds as $id) {
            $cusotmerMap[$id] = RedisCache::instance('customerId_customerName_mapping')->get($id);
        }
        $tpl = [
            'dayPudaoUsage' => 0,
            'dayTotalUsage' => 0,
            'dayIncome' => 0,
            'dayCost' => 0,
            'yearIncome' => 0,
            'yearCost' => 0
        ];


        $productDatas = ['allTotal' => $tpl, 'data' => []];//产品维度
        $customerDatas = ['allTotal' => $tpl, 'data' => []];//客户维度

        //年收入
        foreach ($yearIncome as $v) {
            //企服企业收入减半20221208
            //if($this->checkQifuByCustomerId($v['customer_id'])){
            if($this->customerIncomeRepository->isHalveIncomeByCondition('', $v['customer_id'], $v['product_id'])){
                $v['money'] = bcdiv($v['money'],2,6);
            }

            $fatherInfo = $fatherMap[$v['product_id']];
            $customerName = $cusotmerMap[$v['customer_id']];
            if ($v['money'] <= 0) {
                continue;
            }
            //产品维度
            if (!isset($productDatas['data'][$v['father_id']])) {
                $productDatas['data'][$v['father_id']] = [
                    'father_id' => $v['father_id'],
                    'product_name' => $fatherInfo['father_name'],
                    'dayPudaoUsage' => 0,
                    'dayTotalUsage' => 0,
                    'dayIncome' => 0,
                    'dayCost' => 0,
                    'yearIncome' => 0,
                    'yearCost' => 0,
                ];
            }
            $productDatas['data'][$v['father_id']]['yearIncome'] += $v['money'];

            //客户维度
            if (!isset($customerDatas['data'][$v['customer_id']])) {
                $customerDatas['data'][$v['customer_id']] = [
                    'customer_id' => $v['customer_id'],
                    'customer_name' => $customerName,
                    'dayPudaoUsage' => 0,
                    'dayTotalUsage' => 0,
                    'dayIncome' => 0,
                    'dayCost' => 0,
                    'yearIncome' => 0,
                    'yearCost' => 0,
                ];
            }

            $customerDatas['data'][$v['customer_id']]['yearIncome'] += $v['money'];
            //合计
            $productDatas['allTotal']['yearIncome'] += $v['money'];
            $customerDatas['allTotal']['yearIncome'] += $v['money'];
        }
        //年成本
        foreach ($yearCost as $v) {
            $fatherInfo = $fatherMap[$v['product_id']];
            $customerName = $cusotmerMap[$v['customer_id']];
            //产品维度
            if (!isset($productDatas['data'][$v['father_id']])) {
                $productDatas['data'][$v['father_id']] = [
                    'father_id' => $v['father_id'],
                    'product_name' => $fatherInfo['father_name'],
                    'dayPudaoUsage' => 0,
                    'dayTotalUsage' => 0,
                    'dayIncome' => 0,
                    'dayCost' => 0,
                    'yearIncome' => 0,
                    'yearCost' => 0,
                ];
            }
            $productDatas['data'][$v['father_id']]['yearCost'] += $v['cost'];

            //客户维度
            if (!isset($customerDatas['data'][$v['customer_id']])) {
                $customerDatas['data'][$v['customer_id']] = [
                    'customer_id' => $v['customer_id'],
                    'customer_name' => $customerName,
                    'dayPudaoUsage' => 0,
                    'dayTotalUsage' => 0,
                    'dayIncome' => 0,
                    'dayCost' => 0,
                    'yearIncome' => 0,
                    'yearCost' => 0,
                ];
            }

            $customerDatas['data'][$v['customer_id']]['yearCost'] += $v['cost'];
            //合计
            $productDatas['allTotal']['yearCost'] += $v['cost'];
            $customerDatas['allTotal']['yearCost'] += $v['cost'];
        }


        //用量
        foreach ($pudaoUsage as $usage) {
            $fatherInfo = $fatherMap[$usage['product_id']];
            $customerInfo = $apikeyMap[$usage['apikey']];

            //产品维度
            if (!isset($productDatas['data'][$fatherInfo['father_id']])) {
                $productDatas['data'][$fatherInfo['father_id']] = [
                    'father_id' => $fatherInfo['father_id'],
                    'product_name' => $fatherInfo['father_name'],
                    'dayPudaoUsage' => 0,
                    'dayTotalUsage' => 0,
                    'dayIncome' => 0,
                    'dayCost' => 0,
                    'yearIncome' => 0,
                    'yearCost' => 0,
                ];
            }
            $productDatas['data'][$fatherInfo['father_id']]['dayPudaoUsage'] += $usage['total'];

            $productDatas['allTotal']['dayPudaoUsage'] += $usage['total'];

            //客户维度
            if (!isset($customerDatas['data'][$customerInfo['customer_id']])) {
                $customerDatas['data'][$customerInfo['customer_id']] = [
                    'customer_id' => $customerInfo['customer_id'],
                    'customer_name' => $customerInfo['customer_name'],
                    'dayPudaoUsage' => 0,
                    'dayTotalUsage' => 0,
                    'dayIncome' => 0,
                    'dayCost' => 0,
                    'yearIncome' => 0,
                    'yearCost' => 0,
                ];
            }
            $customerDatas['data'][$customerInfo['customer_id']]['dayPudaoUsage'] += $usage['total'];
            //合计
            $customerDatas['allTotal']['dayPudaoUsage'] += $usage['total'];
        }

        //产品维度日总量
        foreach ($dayTotalDatas as $item) {
            $fatherId = $fatherMap[$item['product_id']]['father_id'];
            if (!isset($productDatas['data'][$fatherId])) {
                continue;
            }
            //产品维度
            $productDatas['data'][$fatherId]['dayTotalUsage'] += $item['total'];
            $productDatas['allTotal']['dayTotalUsage'] += $item['total'];
        }

        //产品维度日总量
        foreach ($cusDayTotalDatas as $item) {
            $customerId = $apikeyMap[$item['apikey']]['customer_id'];
            //客户维度
            if (!isset($customerDatas['data'][$customerId])) {
                continue;
            }
            $customerDatas['data'][$customerId]['dayTotalUsage'] += $item['total'];
            //合计
            $customerDatas['allTotal']['dayTotalUsage'] += $item['total'];
        }

        //客户维度日总量

        //成本
        foreach ($cost as $v) {
            $productDatas['data'][$v['father_id']]['dayCost'] += $v['cost'];
            $customerDatas['data'][$v['customer_id']]['dayCost'] += $v['cost'];
            //合计
            $productDatas['allTotal']['dayCost'] += $v['cost'];
            $customerDatas['allTotal']['dayCost'] += $v['cost'];
        }

        //收入
        foreach ($income as $v) {
            //企服企业收入减半20221208
            //if($this->checkQifuByCustomerId($v['customer_id'])){
            if($this->customerIncomeRepository->isHalveIncomeByCondition('', $v['customer_id'], $v['product_id']??0)){
                $v['money'] = bcdiv($v['money'],2,6);
            }
            
            $productDatas['data'][$v['father_id']]['dayIncome'] += $v['money'];
            $customerDatas['data'][$v['customer_id']]['dayIncome'] += $v['money'];
            //合计
            $productDatas['allTotal']['dayIncome'] += $v['money'];
            $customerDatas['allTotal']['dayIncome'] += $v['money'];
        }
        //数据排序
        foreach ($productDatas['data'] as $k => $v) {
            $productDatas['data'][$k]['tmpRate'] = $v['dayTotalUsage'] ? ($v['dayPudaoUsage']/$v['dayTotalUsage'])*100  : 0;
        }

        foreach ($customerDatas['data'] as $k => $v) {
            $customerDatas['data'][$k]['tmpRate'] = $v['dayTotalUsage'] ? ($v['dayPudaoUsage']/$v['dayTotalUsage'])*100  : 0;
        }
        $tmpProRate = array_column($productDatas['data'], 'tmpRate');
        $tmpProUse = array_column($productDatas['data'], 'dayPudaoUsage');
        array_multisort($tmpProRate,SORT_DESC, $tmpProUse, SORT_DESC, $productDatas['data']);

        $tmpCusRate = array_column($customerDatas['data'], 'tmpRate');
        $tmpCusUse = array_column($customerDatas['data'], 'dayPudaoUsage');
        array_multisort($tmpCusRate,SORT_DESC, $tmpCusUse, SORT_DESC, $customerDatas['data']);

        $return = [
            'product' => $productDatas,
            'customer' => $customerDatas,
        ];

        return $return;
    }


    private function createSourceTableHtml($data)
    {
        $display = <<<HTML
<b class="table_title">四: 来源-收入维度</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
    <th align="center" nowrap="nowrap">来源</th>
	<th align="center" nowrap="nowrap">日收入</th>
	<th align="center" nowrap="nowrap">日占比</th>
	<th align="center" nowrap="nowrap">月收入</th>
	<th align="center" nowrap="nowrap">月占比</th>
	<th align="center" nowrap="nowrap">年收入</th>
	<th align="center" nowrap="nowrap">年占比</th>
HTML;
        $display .= '</tr>';

        foreach ($data['data'] as $source_name => $item) {

            $day_total_income = $data['total']['income'] ?? 0;
            $month_total_income = $data['total']['month_income'] ?? 0;
            $year_total_income = $data['total']['year_income'] ?? 0;
            //日占比
            $item['day_rate'] = $this->getRate($item['income'], $day_total_income, 2);
            //日占比
            $item['month_rate'] = $this->getRate($item['month_income'], $month_total_income, 2);
            //日占比
            $item['year_rate'] = $this->getRate($item['year_income'], $year_total_income, 2);

            //小计
            $display .= <<<HTML
<tr>
	<td align="center">$source_name</td>
	<td align="right">{$this->formatNumStr($item['income'])}</td>
	<td align="right">{$item['day_rate']}</td>
    <td align="right">{$this->formatNumStr($item['month_income'])}</td>
	<td align="right">{$item['month_rate']}</td>
    <td align="right">{$this->formatNumStr($item['year_income'])}</td>
	<td align="right">{$item['year_rate']}</td>
HTML;

            $display .= '</tr>';

        }

        $display .= "</table><br/>";

        $display .= $this->remark;

        return $display;
    }

    protected function createPudaoFirstTableHtmlBak($data)
    {
        $show_money = $this->financeAuth['show_money'];
        $show_cost = $this->financeAuth['show_cost'];
        $show_profit = $this->financeAuth['show_profit'];
        $display = <<<HTML
<b class="table_title">四: 朴道-产品维度 </b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">征信机构</th>
	<th align="center" nowrap="nowrap" width="120px">产品</th>
	<th align="center" nowrap="nowrap">日总调用量</th>
	<th align="center" nowrap="nowrap">日总调用量占比</th>
HTML;
	$show_money && $display .=  '<th align="center" nowrap="nowrap">日收入</th>';
	$show_cost && $display .=  '<th align="center" nowrap="nowrap">日成本</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">日毛利</th>';
	$show_money && $display .=  '<th align="center" nowrap="nowrap">当年收入</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">当年毛利</th>';
    $display .= '</tr>';

        $total = $data['allTotal'];
        $total['uRate'] = $this->getRate($total['dayPudaoUsage'], $total['dayTotalUsage'], 2);
        $total['dayIncome'] = $this->formatMoney($total['dayIncome']);
        $total['dayCost'] = $this->formatMoney($total['dayCost']);
        $total['yearIncome'] = $this->formatMoney($total['yearIncome']);
        $total['yearCost'] = $this->formatMoney($total['yearCost']);
        $total['dayProfit'] = $this->formatMoney($total['dayIncome'] - $total['dayCost']);
        $total['yearProfit'] = $this->formatMoney($total['yearIncome'] - $total['yearCost']);
        $display .= <<<HTML
<tr>
	<td align="center" colspan="2">合计</td>
	<td align="right">{$this->formatNumStr($total['dayPudaoUsage'])}</td>
	<td align="right">{$total['uRate']}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['dayIncome']).'</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['dayCost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['dayProfit']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['yearIncome']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['yearProfit']).'</font></td>';
    $display .= '</tr>';

        $i = 1;
        $rowspan = count($data['data']);
        foreach ($data['data'] as $item) {

            $item['uRate'] = $this->getRate($item['dayPudaoUsage'], $item['dayTotalUsage'], 2);
            $item['dayIncome'] = $this->formatMoney($item['dayIncome']);
            $item['dayCost'] = $this->formatMoney($item['dayCost']);
            $item['yearIncome'] = $this->formatMoney($item['yearIncome']);
            $item['yearCost'] = $this->formatMoney($item['yearCost']);
            $item['dayProfit'] = $this->formatMoney($item['dayIncome'] - $item['dayCost']);
            $item['yearProfit'] = $this->formatMoney($item['yearIncome'] - $item['yearCost']);
            if ($i == 1) {
                $display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">朴道</td>
	<td align="center">{$item['product_name']}</td>
	<td align="right">{$this->formatNumStr($item['dayPudaoUsage'])}</td>
	<td align="right">{$item['uRate']}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayIncome']).'</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayCost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayProfit']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearIncome']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearProfit']).'</font></td>';
    $display .= '</tr>';

                $i++;
                continue;
            }
            $display .= <<<HTML
<tr>
	<td align="center">{$item['product_name']}</td>
	<td align="right">{$this->formatNumStr($item['dayPudaoUsage'])}</td>
	<td align="right">{$item['uRate']}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayIncome']).'</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayCost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayProfit']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearIncome']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearProfit']).'</font></td>';
    $display .= '</tr>';

        }

        $display .= "</table><br/>";

        return $display;
    }

    private function createCustomerTableHtml($data)
    {
        $show_money = $this->financeAuth['show_money'];
        $show_cost = $this->financeAuth['show_cost'];
        $show_profit = $this->financeAuth['show_profit'];

        $display = <<<HTML
<b class="table_title">五: 客户收入</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap" width="128px">客户</th>
	<th align="center" nowrap="nowrap">日总调用量</th>
	<th align="center" nowrap="nowrap">日总调用量占比</th>
HTML;
        $show_money && $display .=  '<th align="center" nowrap="nowrap">日收入</th>';
        $show_money && $display .=  '<th align="center" nowrap="nowrap">日收入占比</th>';
        $show_cost && $display .=  '<th align="center" nowrap="nowrap">日成本</th>';
        $show_profit && $display .=  '<th align="center" nowrap="nowrap">日毛利</th>';
        $show_money && $display .=  '<th align="center" nowrap="nowrap">当年收入</th>';
        $show_money && $display .=  '<th align="center" nowrap="nowrap">年收入占比</th>';
        $show_profit && $display .=  '<th align="center" nowrap="nowrap">当年毛利</th>';
        $display .= '</tr>';

        $total   = array_shift($data['data']);
        $total_day_number = $total['total'] ?? 0;//日总调用量
        $total_day_income = $total['income'] ?? 0;//日总收入
        $total_day_cost = $total['cost'] ?? 0;//日总成本
        $total_day_profit = bcsub($total_day_income, $total_day_cost, 6);//日总毛利
        $total_year_income = $total['year_income'] ?? 0;//年总收入
        $total_year_cost = $total['year_cost'] ?? 0;//年总成本
        $total_year_profit = bcsub($total_year_income, $total_year_cost, 6);//年总毛利

        //合计行
        $display .= <<<HTML
<tr>
	<td align="center" >合计</td>
	<td align="right">{$this->formatNumStr($total_day_number)}</td>
	<td align="right">100%</td>
HTML;
        $show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total_day_income).'</font></td>';
        $show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">100%</font></td>';
        $show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total_day_cost).'</font></td>';
        $show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total_day_profit).'</font></td>';
        $show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total_year_income).'</font></td>';
        $show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">100%</font></td>';
        $show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total_year_profit).'</font></td>';

        $display .= '</tr>';
        foreach ($data['data'] as $customer_name => $item) {

            //日总调用量占比
            $item['day_total_rate'] = $this->getRate($item['total'], $total_day_number, 2);
            //日收入占比
            $item['day_income_rate'] = $this->getRate($item['income'], $total_day_income, 2);
            //年收入占比
            $item['year_income_rate'] = $this->getRate($item['year_income'], $total_year_income, 2);
            //日毛利
            $item['profit'] = bcsub($item['income'], $item['cost'], 6);
            //年毛利
            $item['year_profit'] = bcsub($item['year_income'], $item['year_cost'], 6);

            $display .= <<<HTML
<tr>
	<td align="center">{$item['name']}</td>
	<td align="right">{$this->formatNumStr($item['total'])}</td>
	<td align="right">{$item['day_total_rate']}</td>
HTML;

            $show_money && $display .= '<td align="right">' . $this->formatNumStr($item['income']) . '</td>';
            $show_money && $display .= '<td align="right">' . $item['day_income_rate'] . '</td>';
            $show_cost && $display .= '<td align="right">' . $this->formatNumStr($item['cost']) . '</td>';
            $show_profit && $display .= '<td align="right">' . $this->formatNumStr($item['profit']) . '</td>';
            $show_money && $display .= '<td align="right">' . $this->formatNumStr($item['year_income']) . '</td>';
            $show_money && $display .= '<td align="right">' . $item['year_income_rate'] . '</td>';
            $show_profit && $display .= '<td align="right">' . $this->formatNumStr($item['year_profit']) . '</td>';

            $display .= '</tr>';

        }

        $display .= "</table><br/>";

        $display .= $this->remark;

        return $display;
    }

    protected function createPudaoSecondTableHtmlBak($data)
    {
        $show_money = $this->financeAuth['show_money'];
        $show_cost = $this->financeAuth['show_cost'];
        $show_profit = $this->financeAuth['show_profit'];

        $display = <<<HTML
<b class="table_title">五: 朴道-客户维度 </b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">征信机构</th>
	<th align="center" nowrap="nowrap" width="120px">用户</th>
	<th align="center" nowrap="nowrap">日总调用量</th>
	<th align="center" nowrap="nowrap">日总调用量占比</th>
HTML;
	$show_money && $display .=  '<th align="center" nowrap="nowrap">日收入</th>';
	$show_cost && $display .=  '<th align="center" nowrap="nowrap">日成本</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">日毛利</th>';
	$show_money && $display .=  '<th align="center" nowrap="nowrap">当年收入</th>';
	$show_profit && $display .=  '<th align="center" nowrap="nowrap">当年毛利</th>';
    $display .= '</tr>';

        $total = $data['allTotal'];
        $total['uRate'] = $this->getRate($total['dayPudaoUsage'], $total['dayTotalUsage'], 2);
        $total['dayIncome'] = $this->formatMoney($total['dayIncome']);
        $total['dayCost'] = $this->formatMoney($total['dayCost']);
        $total['yearIncome'] = $this->formatMoney($total['yearIncome']);
        $total['yearCost'] = $this->formatMoney($total['yearCost']);
        $total['dayProfit'] = $this->formatMoney($total['dayIncome'] - $total['dayCost']);
        $total['yearProfit'] = $this->formatMoney($total['yearIncome'] - $total['yearCost']);
        $display .= <<<HTML
<tr>
	<td align="center" colspan="2">合计</td>
	<td align="right">{$this->formatNumStr($total['dayPudaoUsage'])}</td>
	<td align="right">{$total['uRate']}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['dayIncome']).'</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['dayCost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['dayProfit']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['yearIncome']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($total['yearProfit']).'</font></td>';
    $display .= '</tr>';

        $i = 1;
        $rowspan = count($data['data']);
        foreach ($data['data'] as $item) {

            $item['uRate'] = $this->getRate($item['dayPudaoUsage'], $item['dayTotalUsage'], 2);
            $item['dayIncome'] = $this->formatMoney($item['dayIncome']);
            $item['dayCost'] = $this->formatMoney($item['dayCost']);
            $item['yearIncome'] = $this->formatMoney($item['yearIncome']);
            $item['yearCost'] = $this->formatMoney($item['yearCost']);
            $item['dayProfit'] = $this->formatMoney($item['dayIncome'] - $item['dayCost']);
            $item['yearProfit'] = $this->formatMoney($item['yearIncome'] - $item['yearCost']);
            if ($i == 1) {
                $display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">朴道</td>
	<td align="center">{$item['customer_name']}</td>
	<td align="right">{$this->formatNumStr($item['dayPudaoUsage'])}</td>
	<td align="right">{$item['uRate']}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayIncome']).'</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayCost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayProfit']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearIncome']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearProfit']).'</font></td>';
    $display .= '</tr>';
                $i++;
                continue;
            }
            $display .= <<<HTML
<tr>
	<td align="center">{$item['customer_name']}</td>
	<td align="right">{$this->formatNumStr($item['dayPudaoUsage'])}</td>
	<td align="right">{$item['uRate']}</td>
HTML;
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayIncome']).'</font></td>';
	$show_cost && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayCost']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['dayProfit']).'</font></td>';
	$show_money && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearIncome']).'</font></td>';
	$show_profit && $display .=  '<td align="right"><font color="red" style="font-weight:1000;">'.$this->formatNumStr($item['yearProfit']).'</font></td>';
$display .= '</tr>';
        }

        $display .= "</table><br/>";

        return $display;
    }

    protected function getAgentRowSpan($data){
        $cate_num = 0;
        $product_num = 0;
        foreach ($data as $cate_id => $item){
            $cate_num++;
            foreach ($item['data'] as $show_product_id => $item2){
                $product_num++;
            }
        }

        $rowspan = $product_num + $cate_num + 1;//最后加1 是因为类别和产品合并单元格的合计占一行
        return $rowspan;
    }

    protected function getCateRowSpan($data){
        $product_num = count($data) ?:0;

        $rowspan = $product_num + 1;//最后加1 是因为小计占一行
        return $rowspan;
    }

    protected function formatNumStr($num)
    {
        $return = $num;
        if ($num >= 1000 || $num <= -1000) {
            $return = sprintf ( "%.1f", $num / 10000 ) . "万";
        }else{
            $return = round($return, 2);
        }
        return $return;
    }

    protected function checkQifuByApikey($apikey){
        $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
        if(!empty($customer_id)){
            $customerType =  RedisCache::instance('customerId_customerType_mapping')->get($customer_id);
            if($customerType==2){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    protected function checkQifuByCustomerId($customer_id){
            $customerType =  RedisCache::instance('customerId_customerType_mapping')->get($customer_id);
            if($customerType==2){
                return true;
            }else{
                return false;
            }
    }

}
