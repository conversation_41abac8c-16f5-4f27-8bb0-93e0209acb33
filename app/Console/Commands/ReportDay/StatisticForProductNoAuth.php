<?php

namespace App\Console\Commands\ReportDay;

use App\Models\BillCost;
use App\Models\BillNotes;
use App\Models\BillProductIncome;
use App\Models\BillProductIncomeV2;
use App\Models\CommonInfoModel;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\Dept;
use App\Models\DeptGrade;
use App\Models\EmailConfig;
use App\Models\MoneyRecharge;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Models\SystemUser;
use App\Models\UpstreamBillAdjust;
use App\Providers\Auth\DataAuth;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use App\TraitUpgrade\ExcelTrait;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * 没有商务权限验证。所有人都能看到全部数据的产品日报
 * Class StatisticForProductNoAuth
 * @package App\Console\Commands\ReportDay
 */
class StatisticForProductNoAuth extends Command
{
	use ExcelTrait;
	protected $signature   = "report_day:statistic_for_product_no_auth
	{--date= : 日期, 格式Ymd，默认昨日}
    {--username= : 收件人，以用户在后台的用户名为基准，多个用户使用,隔开，该参数不会改变用户的邮件授权情况，不传则对所有授权用户发送}
    {--products= : 主产品的id，多个以逗号隔开，默认为所有产品}
    {--mailUser= : 临时收件人，用于上线前检测数据是否正常}
    {--ignoreCheck : 是否忽略监测}
	";
	protected $description = '产品调用量日报表(全国客户)';
	
	//商务部门的ID
	protected $emailConfigScene = 'report_day_statistic_products_no_auth';


	/**
	 * @var array 约定发送给那些用户，为null代表发送给所有用户
	 */
	protected $username = null;
	
	/**
	 * @var integer 日期
	 */
	protected $date;

    /**
     * 要查询的所有子产品id
     * @var
     */
	protected $product_ids;

	/**
	 * @var array 基础的数据
	 */
	protected $data     = [];
	protected $baseData = [
		'total'     => 0,
		'number'    => 0,
	];
	
	//排序的字段
	protected $orderField = 'total';
	
	/**
	 * @var array 邮件抄送人
	 */
	protected $cc = [
    ];
	
	/**
	 * @var string 如果保存HTML，只保存最后一个客户的
	 **/
	protected $saveHtml = '';
	
	//脚本执行
	public function handle()
	{
		try {
			//设置参数
			$this->setParams();
			
			//对每一个用户发送邮件
			array_walk($this->username, [$this, 'send']);

		} catch (\Exception $exception) {
			sendCommandExceptionNotice($this, $exception);
		}
	}
	
	
	/**
	 * 将HTML保存在数据库中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/29 16:07
	 *
	 *
	 * @return void
	 */
	private function saveHtml()
	{
		//是否将HTML保存在数据库中
		if ($this->input->getOption('save')) {
			$content = json_encode(['html' => $this->saveHtml, 'date' => $this->date], JSON_UNESCAPED_UNICODE);
			
			CommonInfoModel::where('id', 7)
						   ->update(compact('content'));
		}
	}
	
	/**
	 * 对每一个用户发送邮件
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:49
	 *
	 * @param $username string 用户名
	 *
	 * @return void
	 */
	private function send($username)
	{
		//获取权限服务对象
		$dataAuthService = new DataAuth($username);

		//获取需要发送的数据
//		$data = $this->getUsernameData($dataAuthService);

		//获取产品维度统计
		$statistic = $this->getStatisticInfo($this->data);

		//标题
		$title = date('m月d日', strtotime($this->date)) . '产品调用量统计(全国客户) -- （' . $dataAuthService->getRealName() . '）收';

		//创建HTML
		$html = $this->createHtml($statistic, $title);
		//保存最后一个HTML内容
		$this->saveHtml = $html;

		//发送邮件
        $mail_user = $this->option('mailUser');
        if ($mail_user) {
            $user_address = [['email' => $mail_user . '@yulore.com', 'name' => $dataAuthService->getRealName()]];
        } else {
            $user_address = [['email' => $username . '@yulore.com', 'name' => $dataAuthService->getRealName()]];
        }

		$mail = new SendMailService();
		$mail->setFromName('金融后台项目组')
			 ->setAddressee($user_address)
			 ->setCC($this->cc)
			 ->setSubject($title)
			 ->setContent($html)
			 ->sendByAsync();
		
		$this->output->success("[{$username}] 邮件发送成功");
	}
	
	/**
	 * 创建附件
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 16:50
	 *
	 * @param $businessInfo array 商务部门维度的统计数据
	 * @param $productInfo  array 产品维度的统计数据
	 * @param $customerInfo array 客户维度的统计数据
	 *
	 * @return string 文件路径
	 */
	private function createExcel($businessInfo, $productInfo, $customerInfo)
	{
		$this->file_out_init();
		
		//设置部门的sheet
		$this->setBusinessSheet($businessInfo);
	}

	
	/**
	 * 创建HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:49
	 *
	 * @param $statistic array 调用量数据
	 * @param $title        string 邮件标题
	 *
	 * @return string
	 */
	private function createHtml($statistic, $title)
	{
		return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<h1>{$title}</h1>
{$this->createStatisticHtml($statistic)}
HTML;
	}
	
	/**
	 * 创建产品调用量的HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:36
	 *
	 * @param $data array 客户维度的统计数据
	 *
	 * @return string
	 */
	private function createStatisticHtml($data)
	{
	    $html = '';
	    foreach ($data as $k => $v) {
            //渲染每个主产品的表格头部和合计行
            $html .= <<<HTML
<h2>{$v['total_col']['father_name']}</h2>
<table border="1" cellspacing="0" cellpadding="0">
<tr>
<th align="center">客户名称</th>
<th align="center">产品名称</th>
<th align="center">总调用量</th>
<th align="center">有效调用量</th>
<th align="center">有效调用率</th>
</tr>
<tr>
<td align="center" colspan="2">合计</td>
<td align="center">{$this->formatNumber($v['total_col']['total'])}</td>
<td align="center">{$this->formatMoney($v['total_col']['valid'])}</td>
<td align="center">{$v['total_col']['valid_ratio']}%</td>
</tr>
HTML;
            foreach ($v as $customer_id => $products) {
                if ($customer_id == 'total_col') continue;
                $rowSpan = count($products);    //统计数组不算在内
                //渲染每个中各用户的合计列
                $html .= <<<HTML
<tr>
<td align="center" rowspan="{$rowSpan}">{$products['total_col']['customer_name']}</td>
<td align="center">合计</td>
<td align="center">{$this->formatMoney($products['total_col']['total'])}</td>
<td align="center">{$this->formatMoney($products['total_col']['valid'])}</td>
<td align="center">{$products['total_col']['valid_ratio']}%</td>
</tr>
HTML;
                foreach ($products as $product_id => $item) {
                    if ($product_id == 'total_col') continue;
                    //渲染每个子产品的列
                    $html .= <<<HTML
<tr>
<td align="center">{$item['product_name']}</td>
<td align="center">{$this->formatMoney($item['total'])}</td>
<td align="center">{$this->formatMoney($item['valid'])}</td>
<td align="center">{$item['valid_ratio']}%</td>
</tr>
HTML;
                    }

                }
            $html .= '</table>';
            }

		return $html;
	}

	/**
	 * 获取客户维度的统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:41
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	private function getCustomerInfo($data)
	{
		//生成一个客户的数据容器
		$result = [];

		//融入到客户的数据容器中
		array_walk($data, function ($item) use (&$result) {
			$this->fillCustomer($item, $result);
		});

		//将客户充值数据填充到容器中
		array_walk($this->recharge, function ($money, $customer_id) use (&$result) {
			$this->fillCustomerRecharge($customer_id, $money, $result);
		});

		//将客户的备注信息填充到容器中
		array_walk($this->remarks, function ($content, $customer_id) use (&$result) {
			$this->fillCustomerRemark($customer_id, $content, $result);
		});

		//对数据进行排序
		array_multisort(array_column($result, $this->orderField), SORT_DESC, $result);

		$total = $this->getTotal($result);
		//汇总中加上本年累计充值
		$total['this_year_recharge'] = array_sum(array_column($result, 'this_year_recharge'));

		return compact('total', 'result');
	}

	/**
	 * 将最小颗粒的数据填充到客户的数据容器中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:42
	 *
	 * @param $data      array 最小颗粒的数据
	 * @param $container array 容器
	 *
	 * @return void
	 */
	private function fillCustomer($data, &$container)
	{
		$customer_id = $data['customer_id'];
		
		if (!array_key_exists($customer_id, $container)) {
			$container[$customer_id] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')
												  ->get($customer_id),
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$customer_id] = array_merge($container[$customer_id], $this->baseData);
		}
		
		foreach (array_keys($this->baseData) as $key) {
			$container[$customer_id][$key] = bcadd($container[$customer_id][$key], $data[$key], 6);
		}
	}
	
	/**
	 * 获取主产品维度的统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:29
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	private function getStatisticInfo($data)
	{
        //生成一个产品的数据容器
        $result = [];
        //整理数据格式
        array_walk($data, function ($item) use (&$result) {
            $result[$item['father_id']][$item['customer_id']][$item['product_id']] = $item;
        });

        //计算主产品和客户的合计数据
        foreach ($result as $father_id => $customers) {
            $father_name = RedisCache::instance('productId_productName_mapping')->get($father_id);
            $result[$father_id]['total_col'] = ['total' => 0, 'valid' => 0, 'father_id' => $father_id, 'father_name' => $father_name];
            foreach ($customers as $customer_id => $products) {
                $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
                $result[$father_id][$customer_id]['total_col'] = ['total' => 0, 'valid' => 0, 'customer_name'=>$customer_name];
                foreach ($products as $product_id => $item) {
                    $result[$father_id]['total_col']['total'] += $item['total'];
                    $result[$father_id]['total_col']['valid'] += $item['valid'];
                    $result[$father_id][$customer_id]['total_col']['total'] += $item['total'];
                    $result[$father_id][$customer_id]['total_col']['valid'] += $item['valid'];
                }
                $ratio = $result[$father_id][$customer_id]['total_col']['total'] ? bcdiv($result[$father_id][$customer_id]['total_col']['valid'], $result[$father_id][$customer_id]['total_col']['total'], 4) * 100 : 0;
                $result[$father_id][$customer_id]['total_col']['valid_ratio'] = $ratio;
            }
            $ratio = $result[$father_id]['total_col']['total'] ? bcdiv($result[$father_id]['total_col']['valid'], $result[$father_id]['total_col']['total'], 4) * 100 : 0;
            $result[$father_id]['total_col']['valid_ratio'] = $ratio;
        }

        //对数据进行排序
        $sort_father_id = [];
        foreach ($result as $k => $v) {
            $sort_father_id[$k] = $v['total_col']['total'];
        }
        array_multisort($sort_father_id, SORT_DESC, $result);
        foreach ($result as $father_id => $customers) {
            $sort_customer = [];
            foreach ($customers as $k => $v) {
                if (isset($v['total_col']['total'])) {
                    $sort_customer[$k] = $v['total_col']['total'];
                } else {
                    $sort_customer[$k] = 0;
                }
            }
            array_multisort($sort_customer, SORT_DESC, $result[$father_id]);
        }
		return $result;
	}

	/**
	 * 汇总合计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:05
	 *
	 * @param $data array 各个维度的结果数据
	 *
	 * @return array
	 */
	private function getTotal($data)
	{
		$total = $this->baseData;
		
		array_walk($data, function ($item) use (&$total) {
			foreach (array_keys($this->baseData) as $key) {
				$total[$key] = bcadd($total[$key], $item[$key], 6);
			}
		});
		
		return $total;
	}

	/**
	 * 获取某个用户所能看到的数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:54
	 *
	 * @param $dataAuthService DataAuth 权限服务对象
	 *
	 * @return array
	 */
	private function getUsernameData($dataAuthService)
	{
		//获取当前用户能查看哪些客户的数据
		$customer_ids = $dataAuthService->getCustomerIds();

		//过滤data数据，并返回
		return array_filter($this->data, function ($item) use ($customer_ids) {
			return in_array($item['customer_id'], $customer_ids);
		});
	}
	
	/**
	 * 设置参数
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:12
	 *
	 * @return void
	 */
	private function setParams()
	{
		//设置所需要的日期
		$date       = $this->option('date');
		$this->date = $date ?: date('Ymd', strtotime('-1 days'));
		if ($this->date > date('Ymd') || !preg_match('/^\d{8}$/', $this->date)) {
			throw new \Exception("日期格式不正确");
		}
		
		//给那个用户发送
		$username = $this->option('username');
		if ($username) {
			$this->username = explode(',', $username);
		} else {
			//获取需要接收邮件的所有用户
			$this->username = $this->getUsers();
		}

		//设置所有子产品id
        $this->setProducts();

        //设置毛利及相关数据
        $this->setData();
	}
	
	/**
	 * 获取全量需要发送的用户
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:36
	 *
	 * @return array
	 */
	private function getUsers()
	{
        $emailConfig = new EmailConfig();
        $users = $emailConfig->getRecipientsAndCCByScene($this->emailConfigScene);
        array_walk($users['recipients'], function(&$item){
            $item = substr($item['address'], 0, -11);
            return $item;
        });
        return $users['recipients'];
	}
	

	/**
	 * 设置data参数，data参数中，存储的是每个客户、每个子产品的日、月、年的收入、成本、毛利数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 16:30
	 *
	 * @return void
	 */
	private function setData()
	{
		/**
		 * 获取调用量数据
		 * 并将获取到的数据按一定的格式填充到$this->data中
		 */
		$this->fillStatistic();

		/**
		 * 计算查得率
		 */
		array_walk($this->data, [$this, 'calculateItem']);
	}

    /**
     * 填充调用量数据
     *
     * @access   private
     * <AUTHOR>
     * @datetime 2021/1/19 11:07
     *
     * @return void
     */
    private function fillStatistic()
    {
        $statistic_date = date('Ymd', strtotime($this->date));
        //填充当日的营收账单数据，并将其保存在容器中
        $usage = StatisticsCustomerUsage::select([
            'apikey',
            'product_id',
            DB::raw('SUM(`total`) as total'),
            DB::raw('SUM(`valid`) as valid'),
        ])
            ->where('date', '=', $statistic_date)
            ->when($this->product_ids, function($query, $product_ids){
                $query->whereIn('product_id', $product_ids);
            })
            ->groupBy('apikey', 'product_id')
            ->get();
        //过滤掉主接口调用量
        $usage = $usage->filter(function ($item){
            $type = RedisCache::instance('productId_productType_mapping')->get($item->product_id);
            return ($type != 2);
        });
        $usage->each(function ($item) {
            $apikey  = $item->apikey;
            $product_id  = $item->product_id;
            $total      = $item->total;
            $valid       = $item->valid;
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
            $product_name = RedisCache::instance('productId_productName_mapping')->get($product_id);
            //如果father_id为0则代表是父产品
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
            if (!$father_id) {
                $father_id = $product_id;
            }
            $unique_key = $customer_id . '_' . $product_id;
            if (isset($this->data[$unique_key])) {
                $this->data[$unique_key]['total'] = bcadd($this->data[$unique_key]['total'], $total, 6);
                $this->data[$unique_key]['valid'] = bcadd($this->data[$unique_key]['valid'], $valid, 6);
            } else {
                $this->data[$unique_key] =
                    compact('product_id', 'father_id', 'product_name', 'customer_id', 'total', 'valid');
            }
        });
    }

    private function setProducts() {
        $products = $this->option('products');
        //如果不传则发送所有产品（传错也会发所有产品）
        if ($products) {
            $products = explode(',', $products);
            $this->product_ids = array_column(Product::getChildProduct($products), 'product_id');
        }
    }
	
	/**
	 * 计算后期数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 14:16
	 *
	 * @return void
	 */
	private function calculateItem(&$data)
	{
		//日利润
		$data['valid_ratio'] = $data['total'] ? bcdiv($data['valid'], $data['total'], 4) * 100 : 0;
	}
	/**
	 * 获取营收账单数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:08
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getBillIncome($start_date, $end_date)
	{
		return BillProductIncomeV2::select([
			'apikey',
			'product_id',
			DB::raw('SUM(`money`) as money'),
			DB::raw('SUM(`number`) as number'),
		])
								->where('date', '>=', $start_date)
								->where('date', '<=', $end_date)
								->groupBy('apikey', 'product_id')
								->get();
	}
	
	/**
	 * 获取成本账单数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 14:58
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getBillCost($start_date, $end_date)
	{
		return BillCost::select([
			'apikey',
			'product_id',
			DB::raw('SUM(`money`) as money'),
			DB::raw('SUM(`number`) as number'),
		])
					   ->where('date', '>=', $start_date)
					   ->where('date', '<=', $end_date)
					   ->groupBy('apikey', 'product_id')
					   ->get();
	}

	/**
	 * 将最小颗粒的数据填充到基础的数据中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:58
	 *
	 * @param $key         string 数据的key
	 * @param $customer_id string 客户ID
	 * @param $product_id  string 产品ID
	 * @param $value       integer|float 值
	 *
	 * @return void
	 */
	private function fillItem($key, $customer_id, $product_id, $value)
	{
		$unique_key = $customer_id . '_' . $product_id;
		
		if (!array_key_exists($unique_key, $this->data)) {
			$this->data[$unique_key]                = $this->baseData;
			$this->data[$unique_key]['customer_id'] = $customer_id;
			$this->data[$unique_key]['product_id']  = $product_id;
		}
		
		if (!array_key_exists($key, $this->data[$unique_key])) {
			throw new \Exception("暂不支持填充{$key}数据");
		}
		
		$this->data[$unique_key][$key] = bcadd($this->data[$unique_key][$key], $value, 6);
	}
	
	/**
	 * 对量格式化
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:11
	 *
	 * @param $number float 数值
	 *
	 * @return integer
	 */
	private function formatNumber($number)
	{
		return number_format($number, 0);
	}
	
	/**
	 * 对金额格式化
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:12
	 *
	 * @param $money float 金额
	 *
	 * @return integer
	 */
	private function formatMoney($money)
	{
		return number_format(round($money, 0), 0);
	}
}