<?php
/**
 * Created by PhpStorm.
 * User: shuangxian.zhang
 * Date: 2021/4/25
 * Time: 11:34
 */

namespace App\Console\Commands\ReportDay;


use App\Models\BillCost;
use App\Models\EmailConfig;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;

//渠道成本账单日报
class CostForChannel extends Command
{
    protected $signature   = 'report_day:cost_for_channel
    {--date= : 发送某日的渠道成本统计，日期格式为Ymd}
    {--addressee= : 发送收件人，设置此项后将不使用邮件配置中的邮箱}';
    protected $description = '成本渠道日报表';

    protected $addressee = [];
    protected $date;
    protected $scene     = 'day_report_upsteam_channel_bill';

    public function handle()
    {
        $res = $this->setOptions();
        if ($res) {
            //获取当天的数据
            $data = $this->getCostInfo();

            //生成html
            $html = $this->createHtml($data);

            //获取收件人、抄送人
            $addressee = $this->getEmailConfig();

            //发送邮件
            $mail = new SendMailService();
            $mail->setFromName('金融后台项目组')
                ->setAddressee($addressee['recipients'])
                ->setCC($addressee['cc'])
                ->setSubject('成本账单渠道日报表')
                ->setContent($html)
                ->sendByAsync();
            $this->output->success("成本账单渠道日报表发送成功");
        }
    }

    /**
     * 获取邮件收件人、抄送人
     *
     * @access protected
     *
     * @return array
     **/
    protected function getEmailConfig()
    {
        if (empty($this->addressee)) {
            $emailConfig = new EmailConfig();
            $mail_arr = $emailConfig->getRecipientsAndCCByScene($this->scene);
            foreach ($mail_arr['recipients'] as $k => $v) {
                $mail_arr['recipients'][$k]['email'] = $v['address'];
                unset($mail_arr['recipients'][$k]['address']);
            }
            foreach ($mail_arr['cc'] as $k => $v) {
                $mail_arr['cc'][$k]['email'] = $v['address'];
                unset($mail_arr['cc'][$k]['address']);
            }
            return $mail_arr;
        }
        $recipients = array_map(function ($email) {
            $address_arr = explode('@', $email);
            $name = $address_arr[0];
            if (count($address_arr) == 1) {
                $email = $address_arr[0] . '@yulore.com';
            }
            return compact('name', 'email');
        }, $this->addressee);
        $cc         = [];

        return compact('cc', 'recipients');
    }

    /**
     * 创建HTML
     *
     * @access protected
     *
     * @param $data array HTML中展示的数据
     *
     * @return string
     **/
    protected function createHtml($data)
    {
        $date  = date('Y-m-d', strtotime($this->date));
        $title = "以下是{$date}渠道成本日报表，请查阅";
        $table = $this->setHtmlBody($data);
        $html  = <<<HTML
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>{$title}</title>
        <style>
            #tableArea { width:100%; height:auto; font-family: Arial, Helvetica, sans-serif; font-size: 14px; }
            .title { width:100%; height:26px; line-height:26px; color:#333; font-size:16px; font-weight:bold; text-align: center; }
            .table { width:98%; height:auto; overflow-x:auto; margin:20px auto; }
            .table table { height:auto; color:#333; border-top:1px solid #ccc; border-left:1px solid #ccc; }
            .table table tr:hover{ background:#eeeeee; }
            .table table th,.table table td { border-bottom: 1px solid #ccc; border-right: 1px solid #ccc; text-align:center; padding:4px 10px; box-sizing:border-box; }
            .table table th { font-weight:bold; min-width:80px !important; }
            .td_red{color:red;}
            .td_green{color:green;}
            h2 {
				width        : 98%;
				height       : 44px;
				line-height  : 44px;
				font-size    : 20px;
				font-weight  : bold;
				text-align   : center;
				margin       : 20px auto 2px;
				background   : rgba(229, 82, 45, 1);
				color        : #FFFFFF;
			}
        </style>
    </head>
    <body>
        <div id="tableArea">
            <div class="title">{$title}</div>
            {$table}
        </div>
    </body>
</html>
HTML;

        return $html;
    }

    /**
     * 构造HTML的TABLE内容
     *
     * @access protected
     *
     * @param $data array HTML中展示的数据
     *
     * @return string
     **/
    protected function setHtmlBody($data)
    {
        $html = '';
        //邦秒验-渠道维度
        $html .= '<h2>邦秒验-渠道维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><th>序号</th><th>渠道名称</th><th>计费用量</th><th>权责成本（元）</th><th>本月权责成本（元）</th><th>本季度权责成本（元）</th><th>本年权责成本（元）</th></tr>";
        $html .= "<tr><th align='center' colspan='2'>合计</th><td align='center'>".number_format($data['channel_200_total']['number'])."</td><td align='center'>".number_format($data['channel_200_total']['money'])."</td><td align='center'>" . number_format($data['channel_200_total']['monthMoney']) ."</td><td align='center'>" . number_format($data['channel_200_total']['seasonMoney']) . "</td><td align='center'>" . number_format($data['channel_200_total']['yearMoney']) . "</td></tr>";
        $i= 1;
        foreach ($data['channel_200'] as $item) {
            $html .= "<tr><td>$i</td><td align='center'>{$item['channel_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td><td>".number_format($item['monthMoney'])."</td><td>".number_format($item['seasonMoney'])."</td><td>".number_format($item['yearMoney'])."</td></tr>";
            $i++;
        }
        $html .= "</table></div>";

        //邦信分-渠道维度
        $html .= '<h2>邦信分-渠道维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><th>序号</th><th>渠道名称</th><th>计费用量</th><th>权责成本（元）</th><th>本月权责成本（元）</th><th>本季度权责成本（元）</th><th>本年权责成本（元）</th></tr>";
        $html .= "<tr><th align='center' colspan='2'>合计</th><td align='center'>".number_format($data['channel_210_1000_total']['number'])."</td><td align='center'>".number_format($data['channel_210_1000_total']['money'])."</td><td align='center'>".number_format($data['channel_210_1000_total']['monthMoney'])."</td><td align='center'>".number_format($data['channel_210_1000_total']['seasonMoney'])."</td><td align='center'>".number_format($data['channel_210_1000_total']['yearMoney'])."</td></tr>";
        $i= 1;
        foreach ($data['channel_210_1000'] as $item) {
            $html .= "<tr><td>$i</td><td align='center'>{$item['channel_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td><td>".number_format($item['monthMoney'])."</td><td>".number_format($item['seasonMoney'])."</td><td>".number_format($item['yearMoney'])."</td></tr>";
            $i++;
        }
        $html .= "</table></div>";

        //渠道维度
        $html .= '<h2>渠道维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><td>序号</td><th>渠道名称</th><th>计费用量</th><th>权责成本（元）</th><th>本月权责成本（元）</th><th>本季度权责成本（元）</th><th>本年权责成本（元）</th></tr>";
        $html .= "<tr><th align='center' colspan='2'>合计</th><td align='center'>".number_format($data['total']['number'])."</td><td align='center'>".number_format($data['total']['money'])."</td><td align='center'>".number_format($data['total']['monthMoney'])."</td><td align='center'>".number_format($data['total']['seasonMoney'])."</td><td align='center'>".number_format($data['total']['yearMoney'])."</td></tr>";
        $i = 1;
        foreach ($data['channel'] as $item) {
            $html .= "<tr><td>$i</td><td align='center'>{$item['channel_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td><td>".number_format($item['monthMoney'])."</td><td>".number_format($item['seasonMoney'])."</td><td>".number_format($item['yearMoney'])."</td></tr>";
            $i++;
        }
        $html .= "</table></div>";

        //产品维度
//        $html .= '<h2>产品维度</h2>';
//        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
//        $html .= "<tr><th>产品名称</th><th>计费用量</th><th>权责成本（元）</th><th>本月权责成本（元）</th><th>本季度权责成本（元）</th><th>本年权责成本（元）</th></tr>";
//        $html .= "<tr><th align='center'>合计</th><td align='center'>".number_format($data['total']['number'])."</td><td align='center'>".number_format($data['total']['money'])."</td><td align='center'>".number_format($data['total']['monthMoney'])."</td><td align='center'>".number_format($data['total']['seasonMoney'])."</td><td align='center'>".number_format($data['total']['yearMoney'])."</td></tr>";
//        foreach ($data['product'] as $item) {
//            $html .= "<tr><td align='center'>{$item['product_name']}</td><td>".number_format($item['number'])."</td><td>".number_format($item['money'])."</td><td>".number_format($item['monthMoney'])."</td><td>".number_format($item['seasonMoney'])."</td><td>".number_format($item['yearMoney'])."</td></tr>";
//        }
//        $html .= "</table></div>";
        //产品渠道维度
        $html .= '<h2>渠道产品维度</h2>';
        $html .= "<div class=\"table\"><table class=\"table\" cellpadding=\"0\" cellspacing=\"0\" border=\"1\">";
        $html .= "<tr><td>序号</td><th>渠道名称</th><th>产品名称</th><th>计费用量</th><th>权责成本（元）</th><th>本月权责成本（元）</th><th>本季度权责成本（元）</th><th>本年权责成本（元）</th></tr>";
        $html .= "<tr><th align='center' colspan='3'>合计</th><td align='center'>".number_format($data['total']['number'])."</td><td align='center'>".number_format($data['total']['money'])."</td><td align='center'>".number_format($data['total']['monthMoney'])."</td><td align='center'>".number_format($data['total']['seasonMoney'])."</td><td align='center'>".number_format($data['total']['yearMoney'])."</td></tr>";
        $j = 1;
        foreach ($data['channel_product'] as $item) {
            $count = count($item);
            $i = 1;
            if ($i == 1) {
                $tmp = reset($item);
                $html .= "<tr><td rowspan='{$count}'>$j</td><td align='center' rowspan='{$count}'>{$tmp['channel_name']}</td><td align='center'>{$tmp['product_name']}</td><td>".number_format($tmp['number'])."</td><td>".number_format($tmp['money'])."</td><td>".number_format($tmp['monthMoney'])."</td><td>".number_format($tmp['seasonMoney'])."</td><td>".number_format($tmp['yearMoney'])."</td></tr>";
            }
            array_shift($item);
            foreach ($item as $item2) {
                $html .= "<tr><td align='center'>{$item2['product_name']}</td><td>".number_format($item2['number'])."</td><td>".number_format($item2['money'])."</td><td>".number_format($item2['monthMoney'])."</td><td>".number_format($item2['seasonMoney'])."</td><td>".number_format($item2['yearMoney'])."</td></tr>";
                $i++;
            }
            $j++;
        }
        $html .= "</table></div>";

        return $html;

    }

    /**
     * 获取本月渠道成本数据
     *
     * @access protected
     *
     * @return array
     **/
    protected function getCostInfo()
    {
        //当天数据
        $cost = BillCost::getCostChannel($this->date, $this->date);

        //当月数据
        $monthStart = date('Ym01', strtotime($this->date));
        $costMonth = BillCost::getCostChannel($monthStart, $this->date);

        //当季度
        $season = ceil((date('n'))/3);//当前是第几季度
        $seasonSatrt = date('Ymd', mktime(0, 0, 0,$season*3-3+1,1,date('Y')));
        $costSea = BillCost::getCostChannel($seasonSatrt, $this->date);

        //当年
        $costYear = BillCost::getCostChannel(date('Y0101', strtotime($this->date)), $this->date);

        $data = [
            'channel' => [],
            'product' => [],
            'channel_product' => [],
            'total' => [
                'number' => 0,
                'money' => 0,
                'monthMoney' => 0,
                'seasonMoney' => 0,
                'yearMoney' => 0
            ],
            'channel_200' => [],
            'channel_200_total' => [
                'number' => 0,
                'money' => 0,
                'monthMoney' => 0,
                'seasonMoney' => 0,
                'yearMoney' => 0
            ],
            'channel_210_1000' => [],
            'channel_210_1000_total' => [
                'number' => 0,
                'money' => 0,
                'monthMoney' => 0,
                'seasonMoney' => 0,
                'yearMoney' => 0
            ]
        ];

        $fatherIdMap = [];
        foreach ($costYear as $k => $v) {
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($v['product_id']);
            $fatherIdMap[$v['product_id']] = $father_id;
            //邦秒验-渠道维度
            if ($father_id == 200) {
                if (isset($data['channel_200'][$v['channel_id']])) {
                    $data['channel_200'][$v['channel_id']]['yearMoney'] += $v['money'];
                } else {
                    $data['channel_200'][$v['channel_id']]['number'] = 0;
                    $data['channel_200'][$v['channel_id']]['money'] = 0;
                    $data['channel_200'][$v['channel_id']]['monthMoney'] = 0;
                    $data['channel_200'][$v['channel_id']]['seasonMoney'] = 0;
                    $data['channel_200'][$v['channel_id']]['yearMoney'] = $v['money'];
                    $data['channel_200'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_200_total']['yearMoney'] += $v['money'];
            }
            //邦信分-渠道维度
            if (($father_id == 210) || ($father_id == 1000)) {
                if (isset($data['channel_210_1000'][$v['channel_id']])) {
                    $data['channel_210_1000'][$v['channel_id']]['yearMoney'] += $v['money'];
                } else {
                    $data['channel_210_1000'][$v['channel_id']]['number'] = 0;
                    $data['channel_210_1000'][$v['channel_id']]['money'] = 0;
                    $data['channel_210_1000'][$v['channel_id']]['monthMoney'] = 0;
                    $data['channel_210_1000'][$v['channel_id']]['seasonMoney'] = 0;
                    $data['channel_210_1000'][$v['channel_id']]['yearMoney'] = $v['money'];
                    $data['channel_210_1000'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_210_1000_total']['yearMoney'] += $v['money'];
            }
            //渠道维度
            if (isset($data['channel'][$v['channel_id']])) {
                $data['channel'][$v['channel_id']]['yearMoney'] += $v['money'];
            } else {
                $data['channel'][$v['channel_id']]['number'] = 0;
                $data['channel'][$v['channel_id']]['money'] = 0;
                $data['channel'][$v['channel_id']]['monthMoney'] = 0;
                $data['channel'][$v['channel_id']]['seasonMoney'] = 0;
                $data['channel'][$v['channel_id']]['yearMoney'] = $v['money'];
                $data['channel'][$v['channel_id']]['channel_name'] = $v['label'];
            }

//            //产品维度
//            if (isset($data['product'][$v['product_id']])) {
//                $data['product'][$v['product_id']]['yearMoney'] += $v['money'];
//            } else {
//                $data['product'][$v['product_id']]['number'] = 0;
//                $data['product'][$v['product_id']]['money'] = 0;
//                $data['product'][$v['product_id']]['monthMoney'] = 0;
//                $data['product'][$v['product_id']]['seasonMoney'] = 0;
//                $data['product'][$v['product_id']]['yearMoney'] = $v['money'];
//                $data['product'][$v['product_id']]['product_name'] = $v['product_name'];
//            }

            //渠道产品维度
            if (isset($data['channel_product'][$v['channel_id']][$v['product_id']])) {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['yearMoney'] += $v['money'];
            } else {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['number'] = 0;
                $data['channel_product'][$v['channel_id']][$v['product_id']]['money'] = 0;
                $data['channel_product'][$v['channel_id']][$v['product_id']]['monthMoney'] = 0;
                $data['channel_product'][$v['channel_id']][$v['product_id']]['seasonMoney'] = 0;
                $data['channel_product'][$v['channel_id']][$v['product_id']]['yearMoney'] = $v['money'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['channel_name'] = $v['label'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['product_name'] = $v['product_name'];
            }

            $data['total']['yearMoney'] += $v['money'];
        }

        foreach ($costSea as $k => $v) {
            $father_id = $fatherIdMap[$v['product_id']];
            //邦秒验-渠道维度
            if ($father_id == 200) {
                if (isset($data['channel_200'][$v['channel_id']])) {
                    $data['channel_200'][$v['channel_id']]['seasonMoney'] += $v['money'];
                } else {
                    $data['channel_200'][$v['channel_id']]['seasonMoney'] = $v['money'];
                    $data['channel_200'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_200_total']['seasonMoney'] += $v['money'];
            }
            //邦信分-渠道维度
            if (($father_id == 210) || ($father_id == 1000)) {
                if (isset($data['channel_210_1000'][$v['channel_id']])) {
                    $data['channel_210_1000'][$v['channel_id']]['seasonMoney'] += $v['money'];
                } else {
                    $data['channel_210_1000'][$v['channel_id']]['seasonMoney'] = $v['money'];
                    $data['channel_210_1000'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_210_1000_total']['seasonMoney'] += $v['money'];
            }
            //渠道维度
            if (isset($data['channel'][$v['channel_id']])) {
                $data['channel'][$v['channel_id']]['seasonMoney'] += $v['money'];
            } else {
                $data['channel'][$v['channel_id']]['seasonMoney'] = $v['money'];
                $data['channel'][$v['channel_id']]['channel_name'] = $v['label'];
            }

            //产品维度
//            if (isset($data['product'][$v['product_id']])) {
//                $data['product'][$v['product_id']]['seasonMoney'] += $v['money'];
//            } else {
//                $data['product'][$v['product_id']]['seasonMoney'] = $v['money'];
//                $data['product'][$v['product_id']]['product_name'] = $v['product_name'];
//            }

            //渠道产品维度
            if (isset($data['channel_product'][$v['channel_id']][$v['product_id']])) {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['seasonMoney'] += $v['money'];
            } else {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['seasonMoney'] = $v['money'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['channel_name'] = $v['label'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['product_name'] = $v['product_name'];
            }

            $data['total']['seasonMoney'] += $v['money'];
        }

        foreach ($costMonth as $k => $v) {
            $father_id = $fatherIdMap[$v['product_id']];
            //邦秒验-渠道维度
            if ($father_id == 200) {
                if (isset($data['channel_200'][$v['channel_id']])) {
                    $data['channel_200'][$v['channel_id']]['monthMoney'] += $v['money'];
                } else {
                    $data['channel_200'][$v['channel_id']]['monthMoney'] = $v['money'];
                    $data['channel_200'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_200_total']['monthMoney'] += $v['money'];
            }
            //邦信分-渠道维度
            if (($father_id == 210) || ($father_id == 1000)) {
                if (isset($data['channel_210_1000'][$v['channel_id']])) {
                    $data['channel_210_1000'][$v['channel_id']]['monthMoney'] += $v['money'];
                } else {
                    $data['channel_210_1000'][$v['channel_id']]['monthMoney'] = $v['money'];
                    $data['channel_210_1000'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_210_1000_total']['monthMoney'] += $v['money'];
            }
            //渠道维度
            if (isset($data['channel'][$v['channel_id']])) {
                $data['channel'][$v['channel_id']]['monthMoney'] += $v['money'];
            } else {
                $data['channel'][$v['channel_id']]['monthMoney'] = $v['money'];
                $data['channel'][$v['channel_id']]['channel_name'] = $v['label'];
            }

            //产品维度
//            if (isset($data['product'][$v['product_id']])) {
//                $data['product'][$v['product_id']]['monthMoney'] += $v['money'];
//            } else {
//                $data['product'][$v['product_id']]['monthMoney'] = $v['money'];
//                $data['product'][$v['product_id']]['product_name'] = $v['product_name'];
//            }

            //渠道产品维度
            if (isset($data['channel_product'][$v['channel_id']][$v['product_id']])) {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['monthMoney'] += $v['money'];
            } else {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['monthMoney'] = $v['money'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['channel_name'] = $v['label'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['product_name'] = $v['product_name'];
            }

            $data['total']['monthMoney'] += $v['money'];
        }


        foreach ($cost as $k => $v) {
            $father_id = $fatherIdMap[$v['product_id']];
//            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($v['product_id']);
            //邦秒验-渠道维度
            if ($father_id == 200) {
                if (isset($data['channel_200'][$v['channel_id']])) {
                    $data['channel_200'][$v['channel_id']]['number'] += $v['number'];
                    $data['channel_200'][$v['channel_id']]['money'] += $v['money'];
                } else {
                    $data['channel_200'][$v['channel_id']]['number'] = $v['number'];
                    $data['channel_200'][$v['channel_id']]['money'] = $v['money'];
                    $data['channel_200'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_200_total']['money'] += $v['money'];
                $data['channel_200_total']['number'] += $v['number'];
            }
            //邦信分-渠道维度
            if (($father_id == 210) || ($father_id == 1000)) {
                if (isset($data['channel_210_1000'][$v['channel_id']])) {
                    $data['channel_210_1000'][$v['channel_id']]['number'] += $v['number'];
                    $data['channel_210_1000'][$v['channel_id']]['money'] += $v['money'];
                } else {
                    $data['channel_210_1000'][$v['channel_id']]['number'] = $v['number'];
                    $data['channel_210_1000'][$v['channel_id']]['money'] = $v['money'];
                    $data['channel_210_1000'][$v['channel_id']]['channel_name'] = $v['label'];
                }
                $data['channel_210_1000_total']['money'] += $v['money'];
                $data['channel_210_1000_total']['number'] += $v['number'];
            }
            //渠道维度
            if (isset($data['channel'][$v['channel_id']])) {
                $data['channel'][$v['channel_id']]['number'] += $v['number'];
                $data['channel'][$v['channel_id']]['money'] += $v['money'];
            } else {
                $data['channel'][$v['channel_id']]['number'] = $v['number'];
                $data['channel'][$v['channel_id']]['money'] = $v['money'];
                $data['channel'][$v['channel_id']]['channel_name'] = $v['label'];
            }

            //产品维度
//            if (isset($data['product'][$v['product_id']])) {
//                $data['product'][$v['product_id']]['number'] += $v['number'];
//                $data['product'][$v['product_id']]['money'] += $v['money'];
//            } else {
//                $data['product'][$v['product_id']]['number'] = $v['number'];
//                $data['product'][$v['product_id']]['money'] = $v['money'];
//                $data['product'][$v['product_id']]['product_name'] = $v['product_name'];
//            }

            //渠道产品维度
            if (isset($data['channel_product'][$v['channel_id']][$v['product_id']])) {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['number'] += $v['number'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['money'] += $v['money'];
            } else {
                $data['channel_product'][$v['channel_id']][$v['product_id']]['number'] = $v['number'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['money'] = $v['money'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['channel_name'] = $v['label'];
                $data['channel_product'][$v['channel_id']][$v['product_id']]['product_name'] = $v['product_name'];
            }

            $data['total']['money'] += $v['money'];
            $data['total']['number'] += $v['number'];
        }
        //各维度排序
        array_multisort(array_column($data['channel_200'], 'yearMoney'), SORT_DESC, $data['channel_200']);
        array_multisort(array_column($data['channel_210_1000'], 'yearMoney'), SORT_DESC, $data['channel_210_1000']);
        array_multisort(array_column($data['channel'], 'yearMoney'), SORT_DESC, $data['channel']);
//        array_multisort(array_column($data['product'], 'money'), SORT_DESC, $data['product']);
        $sort_arr = [];
        foreach ($data['channel_product'] as $channel_id => $product) {
            foreach ($product as $product_id => $item) {
                if (isset($sort_arr[$channel_id])) {
                    $sort_arr[$channel_id] += $item['yearMoney'];
                } else {
                    $sort_arr[$channel_id] = $item['yearMoney'];
                }
            }
            array_multisort(array_column($data['channel_product'][$channel_id], 'yearMoney'), SORT_DESC, $data['channel_product'][$channel_id]);
        }
        arsort($sort_arr);
        uksort($data['channel_product'], function ($i, $j) use ($sort_arr){
            return $sort_arr[$i] < $sort_arr[$j];
        });

        return $data;
    }

    /**
     * 设置参数
     *
     * @access protected
     *
     * @return boolean
     **/
    protected function setOptions()
    {
        $date       = $this->input->getOption('date');
        $this->date = empty($date) ? date('Ymd', strtotime('-1 days')) : $date;
        if (!preg_match('/^\d{8}$/', $this->date)) {
            $this->output->error('日期格式不正确');

            return false;
        }

        $addressee = $this->input->getOption('addressee');
        if (!empty($addressee)) {
            $this->addressee = explode(',', $addressee);
        }

        return true;
    }

}