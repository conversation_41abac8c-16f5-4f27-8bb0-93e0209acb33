<?php

namespace App\Console\Commands\ReportDay;

use App\Models\BillCost;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\EmailConfig;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\ChannelInterfaceRepository;
use App\Repositories\ChannelRepository;
use App\Models\ChannelRemit;


//计算成本账单
class OperatorMonitoring extends Command
{
	protected $signature = "operator:monitoring {email?}
	{--endDate= : 结束时间 }";

	protected $description = '运营商相关数据监测';

    protected $endDate = '';//结束时间

	//protected  $channel_money  = [1 => 0.1, 3 => 0.2, 4 => 0.15, 5 => 0.1, 6 => 0.15, 7 => 0.1, 8 => 0.5, 9 => 0.1, 10 => 0.1, 11 => 0.1,12 =>0.1,13=>0.1];

	public function handle(ChannelInterfaceRepository $channelInterfaceRepository)
	{
        $this->email = $this->argument('email');
        $this->endDate = $this->input->getOption('endDate') ?: '';
        $this->sendMail($channelInterfaceRepository);
        // ChannelRemit::getSumMoney(**********,113);

	}

	/**
	 * 获取发送的内容
	 *
	 * @return int
	 */

	protected  function  sendMail($channelInterfaceRepository)
    {
        //$channel = new Channel();

        $Emails = EmailConfig::getEamilAddressByScene('channel_limit_daily_report');

        $data = $channelInterfaceRepository->getChannelInterface($this->endDate);

        //日报发送内容拼接
        $html = $this->createHtml($data);

        $mail = new SendMailService();


        if(!empty($Emails)){
            foreach ($Emails as $v){
                if (!is_null($this->email)){
                    if ($v['email'] != $this->email){
                        continue;
                    }
                }
                //日报 每日发送
                $mail->setFromName('金融后台项目组')
                    ->setAddressee([$v])
                    //->setCC($this->cc)
                    ->setSubject('运营商数据监控')
                    ->setContent($html)
                    ->sendByAsync();
            }
        }


    }


    /*
     * 生成邮件发送内容
     */
    private function createHtml($data,$flag = null)
    {
        $title = "运营商数据监控";
        $content = '';

$content .= '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
            font-size   : 12px;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<div>
<h1>'; $title; $content .= '</h1>';
    if ($flag == 'diffday'){
       $html =  $this->createDiffDayChannelHtml($data);
    }elseif ($flag == 'restday'){
       $html =  $this->createRustDayChannelHtml($data);
    }else{
      $html =   $this->createChannelHtml($data);
    }
$content .= $html;

$content .= '<br />
</div> 
<span style="color: red">注：<br /></span>
<span style="color: red">1、上海电信：后付费</span><br />
<span style="color: red">2、陕西移动：预付费，运营商同步暂不充值</span><br />
<span style="color: red">3、山西移动：运营商接口拉取余额</span><br />
<br>
注释：<br />
1、最少可用：依据近半年的数据，‌选取消耗最多的一天作为计算基准<br/>
2、最少可用(日均)：依据近7天的数据，‌计算日均消耗作为计算基准<br/>
';

 return $content;

}

    /*
     * 生成邮件发送内容中的表格
     */
    private function createChannelHtml($data)
    {
        $html = '';

        $html .= <<<HTML
      
        <h2> 运营商数据</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
            <th></th>
            <th colspan="4">保底</th>
            <th align="center" style="width:1px;background-color: rgba(229, 82, 45, 1)"> &nbsp;</th>
            <th colspan="3">合同</th>
        </tr>
        <tr>
        <th  style="width:100px;  text-align:center" align="center">运营商</th>
        <th style="width:100px;  text-align:center" align="center">余额</th>
        <th style="width:120px;  text-align:center" align="center">最少可用</th>
        <th style="width:120px;  text-align:center" align="center">最少可用(日均)</th>
        <th style="width:150px;  text-align:center" align="center">待处理情况</th>
        <th align="center" style="width:1px;background-color: rgba(229, 82, 45, 1)"> &nbsp;</th>
        <th style="width:80px;  text-align:center" align="center">合同到期</th>
        <th style="width:100px;  text-align:center" align="center">合同到期距今</th>
        <th style="width:150px;  text-align:center" align="center">待处理情况</th>
        </tr>

HTML;
        //动态加载成功列

        $today = date('Y-m-d',time());

        foreach($data as $k=>$v){
            $diffday = $this->diffBetweenTwoDays($v['contract_end_date'],$today);
            $html .= '<tr><td align="center">'.$v['label'].'</td>';

            //保底
            $html .= '<td align="center">'.$v['floor_balance'].'</td>';
            if($v['floor_rest_money_day'] <=3 && $v['floor_rest_money_day'] != -1 ){
                if (isset($v['alarm_type_2']) && $v['alarm_type_2'] == 2){
                    $html .=  '<td align="center" style= "background-color: orange;color: white;">'.$v['floor_rest_money_day'].'</td>';
                }else{
                    $html .=  '<td align="center" style= "background-color: red;color: white;">'.$v['floor_rest_money_day'].'</td>';
                }

            }else{
                $restMoneyDay = $v['floor_rest_money_day'] == -1 ? '--' : $v['floor_rest_money_day'];
                $html .=  '<td align="center">'.$restMoneyDay.'</td>';
            }

            if($v['floor_daily_call_num_money_day'] <=15 && $v['floor_daily_call_num_money_day'] != -1 ){
                if (isset($v['alarm_type_2']) && $v['alarm_type_2'] == 2){
                    $html .=  '<td align="center" style= "background-color: orange;color: white;">'.$v['floor_daily_call_num_money_day'].'</td>';
                }else{
                    $html .=  '<td align="center" style= "background-color: #E4EC0AFF;color: white;">' .$v['floor_daily_call_num_money_day'].'</td>';
                }

            }else{
                $daily_call_num_money_day = $v['floor_daily_call_num_money_day'] == -1 ? '--' : $v['floor_daily_call_num_money_day'];
                $html .=  '<td align="center">'.$daily_call_num_money_day.'</td>';
            }

            $alarm_mark_2 = isset($v['alarm_mark_2'])? $v['alarm_mark_2'] : '--';
            if ($alarm_mark_2 == '--'){
                $html .=  '<td align="center">'.$alarm_mark_2.'</td>';
            }else{
                $html .=  '<td align="center" style= "background-color: orange;color: white;">'.$alarm_mark_2.'</td>';
            }

            $html .= '<td align="center" style="background-color: rgba(229, 82, 45, 1)"></td>';

            //合同
            $html .= '<td align="center">'.$v['contract_end_date'].'</td>';
            if($diffday <= 90 && $v['contract_end_date'] != '--' ){
                if (isset($v['alarm_type_3']) && $v['alarm_type_3'] == 2){
                    $html .= '<td align="center" style= "background-color: orange;color: white;">'.$diffday.'</td>';
                }else{
                    $html .= '<td align="center" style= "background-color: red;color: white;">'.$diffday.'</td>';
                }

            }else{
                $diffday = $diffday == 0 ? '--' : $diffday;
                $html .= '<td align="center">'.$diffday.'</td>';
            }

            $alarm_mark_3 = isset($v['alarm_mark_3'])? $v['alarm_mark_3'] : '--';
            if ($alarm_mark_3 == '--'){
                $html .=  '<td align="center">'.$alarm_mark_3.'</td></tr>';
            }else{
                $html .=  '<td align="center" style= "background-color: orange;color: white;">'.$alarm_mark_3.'</td></tr>';
            }

        }
        $html .= '</table>';


        return $html;
    }

    //合同到期警报内容
    private function createDiffDayChannelHtml($data)
    {
        $html = '';

        $html .= <<<HTML
        <h2> 合同到期警报</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">运营商</th>
        <th align="center">合同到期日</th>
        <th align="center">合同剩余时间</th>
       
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $k=>$v){

            $html .= '<tr><td align="center">'.$v['label'].'</td>
                     <td align="center">'.$v['contract_end_date'].'</td>
                     <td align="center">'.$v['diffday'].'</td></tr>';

        }


        $html .= '</table>';


        return $html;
    }


    //接口调用天数警报内容
    private function createRustDayChannelHtml($data)
    {
        $html = '';

        $html .= <<<HTML
        <h2> 接口调用天数警报</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">运营商</th>
        <th align="center">接口剩余调用天数</th>
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $k=>$v){

            $html .= '<tr><td align="center">'.$v['label'].'</td>
                     <td align="center">'.$v['rest_num_day'].'</td></tr>';

        }


        $html .= '</table>';


        return $html;
    }

        /*
         * $day1 合同到期时间
         * $day2 当前日期
         */

        private  function  diffBetweenTwoDays ($day1, $day2)
        {
          $second1 = strtotime($day1);
          $second2 = strtotime($day2);
          $second = $second1 - $second2;
           if($second > 0) {
             $diffday =   $second / 86400;
           }else{
               $diffday = 0;
           }

           return  $diffday;
        }

}