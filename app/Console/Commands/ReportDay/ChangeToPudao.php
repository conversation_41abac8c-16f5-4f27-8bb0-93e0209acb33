<?php

namespace App\Console\Commands\ReportDay;


use DateInterval;
use DatePeriod;
use DateTime;
use Exception;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Product;
use App\TraitUpgrade\ExcelTrait;
use App\TraitUpgrade\MailTrait;
use Illuminate\Console\Command;
use App\Models\StatisticsCustomerUsage;
use App\Providers\Tool\SendMailService;
use App\Providers\Auth\DataAuth;

/**
 * 发送切换朴道统计数据
 *
 * php artisan report_day:change_to_pudao --date ******** --addressee wei.xiu --customer_id C20180904CEMD2W
 * php artisan report_day:change_to_pudao --addressee wei.xiu --customer_id C20190614WWMKLO
 */
class ChangeToPudao extends Command
{
    use ExcelTrait, MailTrait;

    /**
     * 脚本名称,参数
     * @var string
     */
    protected $signature = 'report_day:change_to_pudao
    {--date= : 指定查询日期(格式Ymd)}
    {--addressee= : 指定收件人(多个收件人用,分隔)}
    {--customer_id= : 客户ID(多个客户ID以,分隔)}';

    /**
     * 脚本描述
     * @var string
     */
    protected $description = '切换朴道客户统计';

    /**
     * 收件人列表
     * @var string[]
     */
    protected $addressee = [
        'lili.liu',
        'ren.zhang',
    ];

    /**
     * 抄送列表
     * @var array
     */
    protected $cc = [];

    /**
     * 需要统计的日期
     * @var mixed
     */
    private $end_date;
    private $start_date;

    /**
     * 临时key拼链接字符
     */
    const KEY_SEPARATOR = '_';
    /**
     * 待查询客户信息
     * @var array
     */
    private $customer_infos;

    public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];//过滤 羽乐科技内部,销售测试

    /**
     * 构造函数
     */
    public function __construct(){
        parent::__construct();
    }

    /**
     * 执行
     * @access public
     * @return void
     **/
    public function handle(){
        $this->output->success("切换朴道客户统计 开始");
        try {
            //设置参数
            $this->get_params();

            //生成报表数据
            $data = $this->get_usage_info();

            //生成excel
            $filename = $this->generate_excel($data);

            //生成html
            $html = $this->create_html();

            $addressee = [];
            foreach($this->addressee as $name) {
                $dataAuthService = new DataAuth($name);
                $addressee[] = ['email' => $name . '@yulore.com', 'name' => $dataAuthService->getRealName()];
            }

            //发送邮件
            $mail = new SendMailService();
            $mail->setFromName('切换朴道客户统计')
                 ->setAddressee($addressee)
                 ->setCC($this->cc)
                 ->setSubject("切换朴道客户统计")
                 ->setContent($html)
                 ->setAttachment([
                     [
                         'name'      => "切换朴道客户统计_$this->end_date",
                         'file_path' => $filename,
                     ],
                 ])
                 ->sendByAsync();
                 // ->send();

                $this->output->success("邮件发送成功");
        } catch (Exception $exception) {
            $this->output->error("【{$this->description}】执行失败: {$exception->getMessage()},{$exception->getFile()}:{$exception->getLine()}");
        }
        $this->output->success("切换朴道客户统计 结束");
    }

    /**
     * 生成Html页面
     *
     * @access protected
     * @return string
     **/
    protected function create_html(){
        $title = date('Y年m月d日', strtotime($this->end_date)) . '切换朴道客户的统计数据';
        return <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>{$title}</title>
    </head>
    <body>
        <div id="tableArea">
            <div class="title">附件为{$title},请查阅</div>
        </div>
    </body>
</html>
HTML;
    }

    /**
     * 生成Excel文件
     *
     * @access protected
     *
     * @param $data array 报表数据
     *
     * @return string
     **/
    protected function generate_excel($data){
        list($keep, $still_in_dhb, $in_pd, $continuous_pd, $product_map,$product_father,$account_list,$dept_info,) = $data;
        $this->file_out_init();
        $this->setSheetTitle('已切换客户');
        $this->addRowContent(['客户', '帐号', '产品名称', '子产品', '商务', '区域', '30天羽乐科技调用', '30天朴道调用', '60天羽乐科技调用', '60天朴道调用', '连续调用朴道天数'], 18, true);
        $this->setWidth([18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18,]);

        foreach($in_pd as $customer_id){

            $tc_pd_30 = $keep['pd_30'][$customer_id] ?? [];
            $tc_pd_60 = $keep['pd_60'][$customer_id] ?? [];
            $target_customer = array_merge($tc_pd_60,$tc_pd_30);
            foreach($target_customer as $tk => $usage_days){
                $cell = [];
                list($_customer_id, $apikey, $product_id) = explode(self::KEY_SEPARATOR, $tk);
                $cell['customer_name'] = $this->customer_infos[$customer_id]['name'];
                $cell['account_name']  = $account_list[$apikey]['account_name'];
                $cell['father_name']   = $product_father[$product_map[$product_id]['father_id']]['product_name'];
                $cell['product_name']  = $product_map[$product_id]['product_name'];
                $cell['salesman']      = $dept_info[$this->customer_infos[$customer_id]['salesman']]['realname'];
                $cell['salesman_area'] = $dept_info[$this->customer_infos[$customer_id]['salesman']]['dept_name'];
                $cell['dh_30']         = $keep['dh_30'][$customer_id][$tk]??0;
                $cell['pd_30']         = $keep['pd_30'][$customer_id][$tk]??0;
                $cell['dh_60']         = $keep['dh_60'][$customer_id][$tk]??0;
                $cell['pd_60']         = $keep['pd_60'][$customer_id][$tk]??0;
                $cell['continuous_pd'] = $continuous_pd[$customer_id][$tk]??0;
                $this->addRowContent(array_values($cell), 16, false, false);
            }
        }

        $this->php_excel->createSheet(1);
        $this->cutSheet(1);
        $this->col = 1;
        $this->setSheetTitle('其他客户');
        $this->addRowContent(['客户', '帐号', '产品名称', '子产品', '商务', '区域', '30天羽乐科技调用', '30天朴道调用', '60天羽乐科技调用', '60天朴道调用', '连续调用朴道天数'], 18, true);
        $this->setWidth([18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18,]);

        foreach($still_in_dhb as $customer_id){
            $tc_pd_30 = $keep['pd_30'][$customer_id] ?? [];
            $tc_pd_60 = $keep['pd_60'][$customer_id] ?? [];
            $tc_dh_30 = $keep['dh_30'][$customer_id] ?? [];
            $tc_dh_60 = $keep['dh_60'][$customer_id] ?? [];
            $target_customer = array_merge($tc_pd_60,$tc_pd_30,$tc_dh_30,$tc_dh_60);
            foreach($target_customer as $tk => $usage_days){
                $cell = [];
                list($_customer_id, $apikey, $product_id) = explode(self::KEY_SEPARATOR, $tk);
                $cell['customer_name'] = $this->customer_infos[$customer_id]['name'];
                $cell['account_name']  = $account_list[$apikey]['account_name'];
                $cell['father_name']   = $product_father[$product_map[$product_id]['father_id']]['product_name'];
                $cell['product_name']  = $product_map[$product_id]['product_name'];
                $cell['salesman']      = $dept_info[$this->customer_infos[$customer_id]['salesman']]['realname'];
                $cell['salesman_area'] = $dept_info[$this->customer_infos[$customer_id]['salesman']]['dept_name'];
                $cell['dh_30']         = $keep['dh_30'][$customer_id][$tk]??0;
                $cell['pd_30']         = $keep['pd_30'][$customer_id][$tk]??0;
                $cell['dh_60']         = $keep['dh_60'][$customer_id][$tk]??0;
                $cell['pd_60']         = $keep['pd_60'][$customer_id][$tk]??0;
                $cell['continuous_pd'] = $continuous_pd[$customer_id][$tk]??0;
                $this->addRowContent($cell, 16, false, false);
            }
        }

        $this->cutSheet(0);

        //保存的文件名称
        $baseDir = app()->basePath() . '/storage';

        if(!is_dir($baseDir)){
            mkdir($baseDir, 0755);
        }
        $filepath = "{$baseDir}/{$this->description}_{$this->end_date}.xlsx";
        //保存文件
        $this->save($filepath);

        return $filepath;
    }

    /**
     * 处理传入参数
     * @access protected
     * @return void
     * @throws Exception
     */
    protected function get_params(){
        $date = $this->input->getOption('date');
        if(empty($date)){
            $date = date("Ymd");
        }
        $today = date('Ymd');
        if($date > $today){
            throw new Exception('不可发送未来的报表');
        }
        //转化为unix时间戳
        $this->end_date = $date;
        $this->start_date = date("Ymd",strtotime("-60 day",strtotime($this->end_date)));

        $addressee = $this->input->getOption('addressee');
        if(!empty($addressee)){//替换默认值
            $this->addressee = explode(',', $addressee);
        }

        $customer_id = $this->input->getOption('customer_id');
        if(!empty($customer_id)){
            $customer_id = explode(',', $customer_id);
        }
        $customer_infos = [];
        Customer::select(['customer_id','name','company','salesman',])
                ->when($customer_id,function($query,$customer_id){
                    return $query->whereIn('customer_id', $customer_id);
                })
                ->whereNotIn('customer_id',$this->filter_customer)
                ->where('status',Customer::STATUS_AVAILABLE)
                ->where('is_delete',Customer::IS_DELETE_UNDELETED)
                ->get()
                ->map(function($info) use(&$customer_infos){
                    $customer_infos[$info->customer_id] = [
                        'customer_id' => $info->customer_id,
                        'name'        => $info->name,
                        'company'     => $info->company,
                        'salesman'    => $info->salesman,
                    ];
                });
        $this->customer_infos = $customer_infos;
    }

    /**
     * 获取客户过去60天请求产品次数数据
     */
    private function get_usage_info(){
        //product_id=401或者501,601,604,612,617(特殊产品情况 产品id和父产品id一样或没有子产品的父产品)
        $father_products = Product::select(["product_id","product_name"])->whereIn('father_id',[0,401])->get()->toArray();
        $father_ids_o = array_column($father_products, 'product_id');
        $father_ids_g = Product::getGeneralProduct();//获取所有没有子产品的产品(父产品)
        $father_ids_g = array_column($father_ids_g, 'product_id');
        $father_ids = array_diff($father_ids_o,$father_ids_g);//所有存在子产品的产品

        $product_father = [];
        foreach($father_products as $info) {
            $product_father[$info['product_id']] = $info;
        }

        //商务 区域 信息
        $dept_info = [];
        $dept = [];
        $_dept = SystemDept::getAllDeptInfo();
        foreach($_dept as $info){
            $dept[$info['dept_id']] = $info;
        }
        $salesman = array_column($this->customer_infos, 'salesman');
        $_dept_info = SystemUser::getInfosByUserName($salesman);
        foreach($_dept_info as $v){
            $dept_info[$v['username']] = [
                'realname'  => $v['realname'],
                'dept_name' => $dept[$v['dept_id']]['dept_name'],
            ];
        }


        $days          = $this->get_days();
        $keep          = [];
        $continuous_pd = [];//连续调用朴道天数

        $product_ids            = [];
        $apikeys                = [];
        $apikey_customer_id_map = [];
        $still_in_dhb           = [];
        $in_pd                  = [];
        $_customer_ids          = array_column($this->customer_infos, 'customer_id');
        $_apikey_list           = Account::whereIn('customer_id', $_customer_ids)->where('status',1)->where('apikey', '!=', '')->get()->toArray();
        foreach($_apikey_list as $item){
            $apikey_customer_id_map[$item['customer_id']][] = $item['apikey'];
        }

        foreach($this->customer_infos as $customer_id => $info){
            if(!key_exists($customer_id, $apikey_customer_id_map)){
                continue;
            }
            $_usage_list = StatisticsCustomerUsage::select(['apikey','product_id','date','total','source'])
                ->whereIn('apikey',$apikey_customer_id_map[$customer_id])
                ->where('date','>=',$this->start_date)
                ->where('date','<',$this->end_date)
                ->whereIn('source',[0,1])
                ->orderBy("apikey")
                ->orderBy("product_id")
                ->orderBy("source")
                ->orderBy("date","desc")
                ->get()->toArray();

            $usage_list = [];
            foreach($_usage_list as $usage_info){
                if(in_array($usage_info['product_id'], $father_ids)){
                    continue;
                }

                $k = $this->get_key($customer_id,$usage_info['apikey'],$usage_info['product_id'],$usage_info['source']);
                if(!key_exists($usage_info['apikey'], $apikeys)){
                    $apikeys[$usage_info['apikey']] = $usage_info['apikey'];
                }
                if(!key_exists($usage_info['product_id'], $product_ids)){
                    $product_ids[$usage_info['product_id']] = $usage_info['product_id'];
                }
                if(!key_exists($k, $usage_list)){
                    $usage_list[$k] = $days;
                }
                $usage_list[$k][$usage_info['date']]++;//这里统计有调用的情况
            }

            foreach($usage_list as $key => $usage_days){
                list($_customer_id, $apikey, $product_id, $source) = explode(self::KEY_SEPARATOR, $key);
                $tok = $this->get_key($_customer_id, $apikey, $product_id,"");

                $i = 0;

                foreach($usage_days as $day => $use_product_count){
                    //连续调用朴道天数
                    if($source == 1){
                        if($use_product_count > 0){
                            if(isset($continuous_pd[$customer_id][$tok])){
                                $continuous_pd[$customer_id][$tok] = $continuous_pd[$customer_id][$tok] + 1;
                            }else{
                                $continuous_pd[$customer_id][$tok] = 1;
                            }
                        }else{
                            $continuous_pd[$customer_id][$tok] = 0;
                        }
                    }

                    if($use_product_count > 0){
                        if($source == 0){
                            if(!isset($keep['dh_30'][$customer_id][$tok])){
                                $keep['dh_30'][$customer_id][$tok] = 0;
                                $keep['dh_60'][$customer_id][$tok] = 0;
                            }
                            if($i < 30){
                                $keep['dh_30'][$customer_id][$tok]++;
                            }
                            $keep['dh_60'][$customer_id][$tok]++;
                            if(!key_exists($customer_id, $still_in_dhb)){
                                $still_in_dhb[$customer_id] = $customer_id;
                            }
                        }
                        if($source == 1){
                            if(!isset($keep['pd_30'][$customer_id][$tok])){
                                $keep['pd_30'][$customer_id][$tok] = 0;
                                $keep['pd_60'][$customer_id][$tok] = 0;
                            }
                            if($i < 30){
                                $keep['pd_30'][$customer_id][$tok]++;
                            }
                            $keep['pd_60'][$customer_id][$tok]++;
                        }
                    }
                    $i++;
                }
            }
        }

        //产品信息
        $product_map = [];
        $_product_list = Product::select(['product_id', 'product_name', 'father_id'])->whereIn('product_id', $product_ids)->get()->toArray();
        foreach($_product_list as $item){
            if(in_array($item['product_id'], $father_ids_g)){
                $item['father_id'] = $item['product_id'];
            }
            $product_map[$item['product_id']] = $item;
        }

        //账户信息
        $account_list = [];
        $_account_list = Account::select(['apikey', 'account_name'])->whereIn('apikey', array_values($apikeys))->get()->toArray();
        foreach($_account_list as $info){
            $account_list[$info['apikey']] = $info;
        }

        foreach($this->customer_infos as $customer_id => $info){
            if(!key_exists($customer_id, $still_in_dhb)){
                $in_pd[$customer_id] = $customer_id;
            }
        }

        return [
            $keep,//各个产品在各个渠道的使用统计
            $still_in_dhb,//依然使用羽乐科技的客户
            $in_pd,//使用朴道的客户
            $continuous_pd,//未调用羽乐科技天数
            $product_map,//产品信息
            $product_father,//父产品信息
            $account_list,//帐号信息
            $dept_info,//销售信息 区域信息
        ];
    }


    /**
     * 获取开始时间到结束时间中的日期
     * @return array
     */
    private function get_days(){
        $days = [];
        //获取间隔时间相隔的月数
        try {
            $start = new DateTime($this->start_date);
            $end   = new DateTime($this->end_date);
            // 时间间距 这里设置的是一天
            $interval = DateInterval::createFromDateString('1 day');
            $period   = new DatePeriod($start, $interval, $end);

            foreach($period as $dt){
                $day        = $dt->format("Ymd");
                $days[$day] = 0;
            }
            // $days = array_reverse($days,true);
        } catch (Exception $e) {
        }
        return $days;
    }


    private function get_key($customer_id, $apikey, $product_id, $source){
        if($source === ""){
            return $customer_id . self::KEY_SEPARATOR . $apikey . self::KEY_SEPARATOR . $product_id;
        }
        return $customer_id . self::KEY_SEPARATOR . $apikey . self::KEY_SEPARATOR . $product_id . self::KEY_SEPARATOR . $source;
    }
}