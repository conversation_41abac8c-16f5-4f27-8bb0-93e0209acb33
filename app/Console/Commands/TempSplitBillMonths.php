<?php

namespace App\Console\Commands;

use App\Mail\BillMonth;
use App\Models\Account;
use App\Models\BillProductIncome;
use App\Models\CustomerExpend;
use App\Models\MongoBillMonth;
use App\Models\MongoUpstream;
use App\Models\Product;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Class TempSplitBillMonths 拆分邦信分快捷版账单
 * @package App\Console\Commands
 */
class TempSplitBillMonths extends Command
{
	protected $signature = "temp:split_bill_months";
	
	protected $description = '拆分邦信分快捷版的历史客户账单';
	
	//已经处理过的ID记录
	private $id = [];
	
	//结果存储表
	private $result = [];
	
	private $product_ids = [];
	
	//存在特殊消费，但是没有真实账单的数据
	private $special_customer_ids = [];
	
	public function __construct()
	{
		parent::__construct();
		
		$product_ids   = Product::whereIn('father_id', [210, 1000])
								->pluck('product_id')
								->toArray();
		$product_ids[] = 1000;
		$product_ids[] = 210;
		
		$this->product_ids = $product_ids;
	}
	
	public function handle()
	{
		//查询邦信分快捷版的数据
		$this->output->writeln(">>处理历史月份账单...");
		$info = $this->getInfo();
		
		$total       = count($info);
		$progressBar = $this->output->createProgressBar($total);
		
		//对每一条记录进行拆分
		array_walk($info, function ($item) use ($progressBar) {
			$this->splitInfo($item);
			$progressBar->advance();
		});
		
		$progressBar->finish();
		
		//10-12月份账单的数据
		$this->output->newLine();
		$this->output->writeln(">>处理10-12月份账单...");
		$this->getNewBillProductIncome();
		
		//特殊消耗
		$this->output->writeln(">>处理特殊费用...");
		$this->getSpecialMoney();
		
		
		$this->output->writeln(">>写入文件...");
		//写入一个文件中
		$fp = fopen('./1.csv', 'w+');
		
		foreach ($this->result as $item) {
			$mark       = array_get($item, 'mark', '');
			$account_id = array_get($item, 'account_id', '');
			$content    = "{$item['customer_id']},{$account_id},{$item['month']},{$item['operator']},{$item['money']},{$mark}\n";
			fwrite($fp, $content);
		}
		
		fclose($fp);
		
		$this->output->writeln(">存在只有特殊消费，无法拆分的数据...");
		
		$fp = fopen('./2.csv', 'w+');
		foreach ($this->special_customer_ids as $item) {
			fwrite($fp, "{$item['customer_id']},{$item['month']}\n");
		}
		
		fclose($fp);
	}
	
	private function getSpecialMoney()
	{
		$info = [];
		//确认每个客户、每个月份在哪个渠道下有费用
		foreach ($this->result as $item) {
			$customer_id = $item['customer_id'];
			if (!array_key_exists('operator', $item)) {
				halt($item);
			}
			$operator = $item['operator'];
			$month    = $item['month'];
			if (!array_key_exists($customer_id, $info)) {
				$info[$customer_id] = [];
			}
			if (!array_key_exists($month, $info[$customer_id])) {
				$info[$customer_id][$month] = [];
			}
			
			if (!in_array($operator, $info[$customer_id][$month])) {
				$info[$customer_id][$month][] = $operator;
			}
		}
		
		//特殊消费
		CustomerExpend::select(['customer_id', 'money', 'start_date', 'type'])
					  ->whereIn('product_id', $this->product_ids)
					  ->where('start_date', '>=', 202001)
					  ->get()
					  ->map(function ($item) use ($info) {
						  $customer_id = $item['customer_id'];
						  $month       = $item['start_date'];
						  $mark        = '特殊费用';
						  $operator    = 'CUCC';
						  $money       = $item['type'] == 1 ? 0 - $item['money'] : $item['money'];
						  if (!array_key_exists($customer_id, $info) || !array_key_exists($month, $info[$customer_id])) {
							  $this->special_customer_ids[] = compact('customer_id', 'month');
							  $this->result[]               = compact('customer_id', 'month', 'mark', 'operator', 'money');
						  } else {
							  //获取这个客户的所有账号
							  $account_ids = Account::where('customer_id', $customer_id)
													->where('is_delete', 0)
													->pluck('account_id')
													->toArray();
							  $ratio       = $this->getRatio($account_ids, intval($month . '01'), intval($month . '32'));
				
							  $this->splitSpecial(compact('customer_id', 'month', 'money'), $ratio);
						  }
					  })
					  ->toArray();
		
		
	}
	
	private function splitSpecial($data, $ratio)
	{
		$total   = array_sum($ratio);
		$surplus = $money = $data['money'];
		
		foreach ($ratio as &$item) {
			$item    = bcdiv(bcmul($item, $money, 6), $total, 6);
			$surplus = bcsub($surplus, $item, 6);
		}
		
		//如果不为0，则需要补充到最后一个中
		if ($surplus) {
			$last_key         = array_last(array_keys($ratio));
			$ratio[$last_key] = bcadd($ratio[$last_key], $surplus, 6);
		}
		
		//将数据补充到结果中
		$customer_id = $data['customer_id'];
		$month       = $data['month'];
		$mark        = '特殊消费';
		
		foreach ($ratio as $operator => $money) {
			$this->result[] = compact('customer_id', 'month', 'money', 'operator', 'mark');
		}
	}
	
	private function getNewBillProductIncome()
	{
		$result = BillProductIncome::select([
			DB::raw('SUM(`money`) as money'),
			'operator',
			DB::raw('LEFT(`date`, 6) as month'),
			'apikey',
		])
								   ->whereIn('father_id', [210, 1000])
								   ->where('date', '>=', ********)
								   ->groupBy([
									   DB::raw('LEFT(`date`, 6)'),
									   'operator',
									   'apikey',
								   ])
								   ->get()
								   ->map(function ($item) {
									   $apikey            = $item['apikey'];
									   $item->account_id  = RedisCache::instance('apikey_accountId_mapping')
																	  ->get($apikey);
									   $item->customer_id = RedisCache::instance('apikey_customerId_mapping')
																	  ->get($apikey);
			
									   return $item;
								   })
								   ->toArray();
		
		$this->result = array_merge($this->result, $result);
	}
	
	/**
	 * 拆分单条账单数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/28 10:55
	 *
	 * @param $data array 数据
	 *
	 * @return void
	 */
	private function splitInfo($data)
	{
		//已经处理过的，不需要处理了
		$id = $data['_id'];
		if (array_key_exists($id, $this->id)) {
			return;
		}
		$this->id[$id] = '';
		
		//金额为0的不需要处理
		if ($data['money'] == 0) {
			return;
		}
		
		//账单中的数据
		$fee_price_rule = array_get(array_get($data, 'section_source', []), 'fee_price_rule', 1);
		$month          = array_get($data, 'month');
		$section_begin  = array_get($data, 'section_begin', date('Ymd', strtotime('first day of this month', strtotime($month . '01'))));
		$section_begin  = intval($section_begin);
		$section_end    = array_get($data, 'section_end', date('Ymd', strtotime('last day of this month', strtotime($month . '01'))));
		$section_end    = intval($section_end);
		$account_id     = array_get($data, 'account_id');
		switch ($account_id) {
			case '****************':
				$account_ids = ['****************', '****************'];
				break;
			case '****************':
				$account_ids = ['****************', '****************'];
				break;
			default:
				$account_ids = [$account_id];
				break;
		}
		
		//如果是父产品计费（最好的情况）
		if ($data['product_id'] == 210) {
			
			if ($fee_price_rule == 2) {
				//区分运营商
				$ratio = $this->getRatio($account_ids, $section_begin, $section_end);
				
				$this->splitDiffOperator210($data, $ratio);
			} else {
				//如果不区分运营商、则证明单价一致
				$ratio = $this->getRatio($account_ids, $section_begin, $section_end);
				
				$this->splitNotDiffOperator210($data, $ratio);
			}
		} else {
			//子产品计费，需要先把所有的产品的账单进行计算
			$this->splitSubProductInfo($account_ids, $section_begin, $section_end);
		}
	}
	
	/**
	 * 获取与某计费配置相同的所有子产品计费账单之和
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/28 15:50
	 *
	 * @param $account_ids   array 账号ID
	 * @param $section_begin string 区间开始时间
	 * @param $section_end   string 区间截止时间
	 *
	 * @return array
	 */
	private function splitSubProductInfo($account_ids, $section_begin, $section_end)
	{
		$info = MongoBillMonth::whereIn('product_id', $this->product_ids)
							  ->whereIn('account_id', $account_ids)
							  ->where('section_begin', strval($section_begin))
							  ->where('section_end', strval($section_end))
							  ->where('money', '<>', 0)
							  ->get()
							  ->toArray();
		
		//先把这些_id加入到已处理过后的记录中
		foreach ($info as $item) {
			$this->id[$item['_id']] = '';
		}
		
		$data = $info[0];
		//账单中的数据
		$fee_price_rule = array_get(array_get($data, 'section_source', []), 'fee_price_rule', 1);
		$month          = array_get($data, 'month');
		$section_begin  = array_get($data, 'section_begin', date('Ymd', strtotime('first day of this month', strtotime($month . '01'))));
		$section_begin  = intval($section_begin);
		$section_end    = array_get($data, 'section_end', date('Ymd', strtotime('last day of this month', strtotime($month . '01'))));
		$section_end    = intval($section_end);
		$account_id     = array_get($data, 'account_id');
		if ($account_id == '****************') {
			$account_ids = ['****************', '****************'];
		} else {
			$account_ids = [$account_id];
		}
		
		if ($fee_price_rule == 2) {
			//区分运营商
			$ratio = $this->getRatio($account_ids, $section_begin, $section_end);
			
			$this->splitDiffOperatorSubProduct($info, $ratio);
		} else {
			//如果不区分运营商、则证明单价一致
			//总金额
			$money         = array_sum(array_column($info, 'money'));
			$data['money'] = $money;
			$ratio         = $this->getRatio($account_ids, $section_begin, $section_end);
			$this->splitNotDiffOperatorSubProduct($data, $ratio);
		}
	}
	
	
	private function splitNotDiffOperatorSubProduct($data, $ratio)
	{
		$total   = array_sum($ratio);
		$surplus = $money = $data['money'];
		
		foreach ($ratio as &$item) {
			$item    = bcdiv(bcmul($item, $money, 6), $total, 6);
			$surplus = bcsub($surplus, $item, 6);
		}
		
		//如果不为0，则需要补充到最后一个中
		if ($surplus) {
			$last_key         = array_last(array_keys($ratio));
			$ratio[$last_key] = bcadd($ratio[$last_key], $surplus, 6);
		}
		
		//将数据补充到结果中
		$account_id    = $data['account_id'];
		$customer_id   = $data['customer_id'];
		$month         = $data['month'];
		$section_begin = array_get($data, 'section_begin', date('Ymd', strtotime('first day of this month', strtotime($month . '01'))));
		$section_end   = array_get($data, 'section_end', date('Ymd', strtotime('last day of this month', strtotime($month . '01'))));
		
		
		foreach ($ratio as $operator => $money) {
			$this->result[] = compact('customer_id', 'account_id', 'month', 'section_begin', 'section_end', 'money', 'operator');
		}
	}
	
	private function splitDiffOperatorSubProduct($info, $ratio)
	{
		$money = [
			'yd'     => 0,
			'lt'     => 0,
			'dx'     => 0,
			'all_yd' => 0,
		];
		
		foreach ($info as $data) {
			//计算移动、联通、电信的每个运营商的
			$section_number = array_get($data, 'section_number', []);
			if (empty($section_number)) {
				halt("子产品区分运营商计费中，不存在[section_number]", $data);
			}
			$fee_number = array_get($section_number, 'fee_number', []);
			if (empty($fee_number)) {
				halt("子产品区分运营商计费中，不存在[fee_number]", $data);
			}
			
			$yd     = array_get($fee_number, 'yd', 0);
			$lt     = array_get($fee_number, 'lt', 0);
			$dx     = array_get($fee_number, 'dx', 0);
			$all_yd = array_get($fee_number, 'all_yd', 0);
			
			//计算移动、联通、电信的价格
			$price = array_get($data, 'section_source', []);
			if (empty($price)) {
				halt("子产品区分运营商计费中，不存在[section_source]");
			}
			$price = array_get($price, 'fee_price', []);
			if (empty($price)) {
				halt("子产品区分运营商计费中，不存在[fee_price]");
			}
			$price        = json_decode($price, true);
			$yd_price     = array_get($price, '0', 0);
			$lt_price     = array_get($price, '1', 0);
			$dx_price     = array_get($price, '2', 0);
			$all_yd_price = array_get($price, '3', 0);
			
			$money['yd']     = bcadd($money['yd'], bcmul($yd, $yd_price, 6), 6);
			$money['lt']     = bcadd($money['lt'], bcmul($lt, $lt_price, 6), 6);
			$money['dx']     = bcadd($money['dx'], bcmul($dx, $dx_price, 6), 6);
			$money['all_yd'] = bcadd($money['all_yd'], bcmul($all_yd, $all_yd_price, 6), 6);
		}
		
		$first         = $info[0];
		$account_id    = $first['account_id'];
		$customer_id   = $first['customer_id'];
		$month         = $first['month'];
		$section_begin = array_get($first, 'section_begin', date('Ymd', strtotime('first day of this month', strtotime($month . '01'))));
		$section_end   = array_get($first, 'section_end', date('Ymd', strtotime('last day of this month', strtotime($month . '01'))));
		
		
		//全国移动
		if ($money['all_yd'] != 0) {
			$this->result[] = [
				'customer_id'   => $customer_id,
				'account_id'    => $account_id,
				'month'         => $month,
				'section_begin' => $section_begin,
				'section_end'   => $section_end,
				'money'         => $money['all_yd'],
				'operator'      => 'ALLCMCC',
			];
		}
		
		//联通
		if ($money['lt'] != 0) {
			$this->result[] = [
				'customer_id'   => $customer_id,
				'account_id'    => $account_id,
				'month'         => $month,
				'section_begin' => $section_begin,
				'section_end'   => $section_end,
				'money'         => $money['lt'],
				'operator'      => 'CUCC',
			];
		}
		
		//电信
		if ($money['dx'] != 0) {
			$this->result[] = [
				'customer_id'   => $customer_id,
				'account_id'    => $account_id,
				'month'         => $month,
				'section_begin' => $section_begin,
				'section_end'   => $section_end,
				'money'         => $money['dx'],
				'operator'      => 'CTCC',
			];
		}
		
		//各省移动
		if ($money['yd'] != 0) {
			
			//拿到省移动的比例
			$pro_ratio = [];
			foreach ($ratio as $operator => $all) {
				if (in_array($operator, [
					'BJCMCC',
					'JSCMCC',
					'HBCMCC',
					'SDCMCC',
					'SCCMCC',
				])) {
					$pro_ratio[$operator] = $all;
				}
			}
			
			
			$total   = array_sum($pro_ratio);
			$surplus = $money = $money['yd'];
			
			foreach ($pro_ratio as &$item) {
				$item    = bcdiv(bcmul($item, $money, 6), $total, 6);
				$surplus = bcsub($surplus, $item, 6);
			}
			
			//如果不为0，则需要补充到最后一个中
			if ($surplus) {
				$last_key             = array_last(array_keys($pro_ratio));
				$pro_ratio[$last_key] = bcadd($pro_ratio[$last_key], $surplus, 6);
			}
			
			foreach ($pro_ratio as $operator => $money) {
				$this->result[] = compact('customer_id', 'account_id', 'month', 'section_begin', 'section_end', 'money', 'operator');
			}
		}
	}
	
	/**
	 * 拆分不区分运营商的210账单
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/28 14:12
	 *
	 * @param $data  array 账单数据
	 * @param $ratio array 比例数据
	 *
	 * @return void
	 */
	private function splitNotDiffOperator210($data, $ratio)
	{
		$total   = array_sum($ratio);
		$surplus = $money = $data['money'];
		
		foreach ($ratio as &$item) {
			$item    = bcdiv(bcmul($item, $money, 6), $total, 6);
			$surplus = bcsub($surplus, $item, 6);
		}
		
		//如果不为0，则需要补充到最后一个中
		if ($surplus) {
			$last_key         = array_last(array_keys($ratio));
			$ratio[$last_key] = bcadd($ratio[$last_key], $surplus, 6);
		}
		
		//将数据补充到结果中
		$account_id    = $data['account_id'];
		$customer_id   = $data['customer_id'];
		$month         = $data['month'];
		$section_begin = array_get($data, 'section_begin', date('Ymd', strtotime('first day of this month', strtotime($month . '01'))));
		$section_end   = array_get($data, 'section_end', date('Ymd', strtotime('last day of this month', strtotime($month . '01'))));
		
		
		foreach ($ratio as $operator => $money) {
			$this->result[] = compact('customer_id', 'account_id', 'month', 'section_begin', 'section_end', 'money', 'operator');
		}
	}
	
	/**
	 * 拆分区分运营商的210账单
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/28 14:12
	 *
	 * @param $data  array 账单数据
	 * @param $ratio array 比例数据
	 *
	 * @return void
	 */
	private function splitDiffOperator210($data, $ratio)
	{
		//计算移动、联通、电信的每个运营商的
		$section_number = array_get($data, 'section_number', []);
		if (empty($section_number)) {
			halt("210区分运营商计费中，不存在[section_number]", $data);
		}
		$fee_number = array_get($section_number, 'fee_number', []);
		if (empty($fee_number)) {
			halt("210区分运营商计费中，不存在[fee_number]", $data);
		}
		
		$yd     = array_get($fee_number, 'yd', 0);
		$lt     = array_get($fee_number, 'lt', 0);
		$dx     = array_get($fee_number, 'dx', 0);
		$all_yd = array_get($fee_number, 'all_yd', 0);
		
		//计算移动、联通、电信的价格
		$price = array_get($data, 'section_source', []);
		if (empty($price)) {
			halt("210区分运营商计费中，不存在[section_source]");
		}
		$price = array_get($price, 'fee_price', []);
		if (empty($price)) {
			halt("210区分运营商计费中，不存在[fee_price]");
		}
		$price        = json_decode($price, true);
		$yd_price     = array_get($price, '0', 0);
		$lt_price     = array_get($price, '1', 0);
		$dx_price     = array_get($price, '2', 0);
		$all_yd_price = array_get($price, '3', 0);
		
		$yd_money     = bcmul($yd, $yd_price, 6);
		$lt_money     = bcmul($lt, $lt_price, 6);
		$dx_money     = bcmul($dx, $dx_price, 6);
		$all_yd_money = bcmul($all_yd, $all_yd_price, 6);
		
		$account_id    = $data['account_id'];
		$customer_id   = $data['customer_id'];
		$month         = $data['month'];
		$section_begin = array_get($data, 'section_begin', date('Ymd', strtotime('first day of this month', strtotime($month . '01'))));
		$section_end   = array_get($data, 'section_end', date('Ymd', strtotime('last day of this month', strtotime($month . '01'))));
		
		
		//全国移动
		if ($all_yd_money != 0) {
			$this->result[] = [
				'customer_id'   => $customer_id,
				'account_id'    => $account_id,
				'month'         => $month,
				'section_begin' => $section_begin,
				'section_end'   => $section_end,
				'money'         => $all_yd_money,
				'operator'      => 'ALLCMCC',
			];
		}
		
		//联通
		if ($lt_money != 0) {
			$this->result[] = [
				'customer_id'   => $customer_id,
				'account_id'    => $account_id,
				'month'         => $month,
				'section_begin' => $section_begin,
				'section_end'   => $section_end,
				'money'         => $lt_money,
				'operator'      => 'CUCC',
			];
		}
		
		//电信
		if ($dx_money != 0) {
			$this->result[] = [
				'customer_id'   => $customer_id,
				'account_id'    => $account_id,
				'month'         => $month,
				'section_begin' => $section_begin,
				'section_end'   => $section_end,
				'money'         => $dx_money,
				'operator'      => 'CTCC',
			];
		}
		
		//各省移动
		if ($yd_money != 0) {
			
			//拿到省移动的比例
			$pro_ratio = [];
			foreach ($ratio as $operator => $all) {
				if (in_array($operator, [
					'BJCMCC',
					'JSCMCC',
					'HBCMCC',
					'SDCMCC',
					'SCCMCC',
				])) {
					$pro_ratio[$operator] = $all;
				}
			}
			
			
			$total   = array_sum($pro_ratio);
			$surplus = $money = $yd_money;
			
			foreach ($pro_ratio as &$item) {
				$item    = bcdiv(bcmul($item, $money, 6), $total, 6);
				$surplus = bcsub($surplus, $item, 6);
			}
			
			//如果不为0，则需要补充到最后一个中
			if ($surplus) {
				$last_key             = array_last(array_keys($pro_ratio));
				$pro_ratio[$last_key] = bcadd($pro_ratio[$last_key], $surplus, 6);
			}
			
			foreach ($pro_ratio as $operator => $money) {
				$this->result[] = compact('customer_id', 'account_id', 'month', 'section_begin', 'section_end', 'money', 'operator');
			}
		}
	}
	
	
	/**
	 * 根据成本用量获取每个渠道所占的比例
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12/28 11:13
	 *
	 * @param $account_id     array 账号ID
	 * @param $start_date     integer 开始日期
	 * @param $end_date       integer 截止日期
	 *
	 * @return array
	 */
	private function getRatio($account_id, $start_date, $end_date)
	{
		$data = [
			'BJCMCC'  => 0,
			'JSCMCC'  => 0,
			'HBCMCC'  => 0,
			'SCCMCC'  => 0,
			'SDCMCC'  => 0,
			'ALLCMCC' => 0,
			'CUCC'    => 0,
			'CTCC'    => 0,
		];
		
		foreach ($data as $regex => &$all) {
			$all = $this->getUpstreamStatistics($account_id, $start_date, $end_date, "^{$regex}_");
		}
		
		$result = array_filter($data, function ($number) {
			return $number > 0;
		});
		
		if (empty($result)) {
			return ['CUCC' => 1];
		} else {
			return $result;
		}
	}
	
	/**
	 * 获取渠道的数据源统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/28 13:45
	 *
	 * @param $account_id          array 账号ID
	 * @param $start_date          integer 开始日期
	 * @param $end_date            integer 截止日期
	 * @param $regex_channel       string 渠道正则
	 *
	 * @return
	 */
	private function getUpstreamStatistics($account_id, $start_date, $end_date, $regex_channel)
	{
		$aggregate = [
			[
				'$match' => [
					'channel'    => [
						'$regex' => $regex_channel,
					],
					'account_id' => [
						'$in' => $account_id,
					],
					'date'       => [
						'$gte' => $start_date,
						'$lte' => $end_date,
					],
					'product_id' => 210,
				
				],
			],
			[
				'$group' => [
					'_id' => '$product_id',
					'all' => [
						'$sum' => '$data.all',
					],
				],
			],
		];
		$data      = MongoUpstream::query()
								  ->raw(function ($collction) use ($aggregate) {
									  return $collction->aggregate($aggregate);
								  })
								  ->toArray();
		
		if (empty($data)) {
			return 0;
		}
		
		return $data[0]['all'];
	}
	
	/**
	 * 查询邦信分快捷版的数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/28 9:58
	 *
	 * @return array
	 */
	private function getInfo()
	{
		return MongoBillMonth::whereIn('product_id', $this->product_ids)
							 ->where('month', '>=', '202001')
							 ->where('month', '<', '202010')
							 ->where('money', '<>', 0)
							 ->get()
							 ->toArray();
	}
}