<?php

namespace App\Console\Commands;

use App\Models\AccountProduct;
use Illuminate\Console\Command;

class AccountProductExpire extends Command
{
    protected $signature = 'account_product_expire';

    protected $description = '账号产品状态与截止日期联动';

    public function handle()
    {
        $time = strtotime(date('Y-m-d 00:00:00', time()));
        // 可用并过期的产品-账号
        $list = AccountProduct::getAccountByTime($time);
        if (!$list) {
            return false;
        }

        $id = array_column($list, 'id');
        AccountProduct::whereIn('id', $id)->update(['status' => 0]);
    }
}