<?php


namespace App\Console\Commands\Monitor;

use Illuminate\Console\Command;
use App\Http\Repository\StatPeroidRepository;

class ProductPeroidList extends Command
{
    protected $signature = 'product_peroid_save';


    /**
     * @throws \Exception
     */
    public function handle()
    {

        try{
            $rep = new StatPeroidRepository();
            $rep->getPeroidList(['save' => true]);

        }catch (\Exception $e){
            //dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendCommandExceptionNotice($this, $e);

        }

        $this->output->success("成功执行");

    }







}