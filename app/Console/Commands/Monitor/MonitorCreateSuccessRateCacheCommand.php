<?php

namespace App\Console\Commands\Monitor;


use App\Models\MonitorLogProductModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * 排重计算蚂蚁的成功调用率
 * Class MonitorCreateSuccessRateCacheCommand
 * @package App\Console\Commands\Monitor
 */
class MonitorCreateSuccessRateCacheCommand extends Command
{
	protected $signature = "monitor:create_success_rate_cache
	{--product_id=281 : 产品ID}
	";
	
	
	/**
	 * @var integer 产品ID
	 */
	protected $product_id;
	/**
	 * @var string 账号唯一标识
	 */
	protected $apikey;
	
	public function handle()
	{
		//设置参数
		$this->setParam();
		
		//获取成功调用量
		$success = $this->getSuccessCount();
		
		//获取总查询量
		$total = $this->getTotal();
		
		
		if ($total) {
			$rate = bcdiv($success, $total, 6);
			
			$redis = Redis::connection('default');
			$key   = $this->apikey . '_' . $this->product_id;
			
			$redis->hset('MonitorSuccessRateUniqueInParamCache', $key, $rate);
			$redis->hset('MonitorSuccessRateUniqueInParamCache', $key . '_total', $total);
			$redis->hset('MonitorSuccessRateUniqueInParamCache', $key . '_success', $success);
			$redis->hset('MonitorSuccessRateUniqueInParamCache', $key . '_update_at', date('Y-m-d H:i:s'));
		}
	}
	
	/**
	 * 设置参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 20:08
	 *
	 * @return void
	 */
	protected function setParam()
	{
		$this->product_id = $this->input->getOption('product_id');
		$this->apikey     = config('params.mayi_apikey');;
	}
	
	/**
	 * 获取总量
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 20:05
	 *
	 * @return integer
	 */
	protected function getTotal()
	{
		$count = MonitorLogProductModel::select(DB::raw('count(DISTINCT `in_param`) as count'))
									   ->where('apikey', $this->apikey)
									   ->where('product_id', $this->product_id)
									   ->get()
									   ->first();
		
		return $count['count'];
	}
	
	/**
	 * 获取成功调用量
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 20:12
	 *
	 * @return integer
	 */
	protected function getSuccessCount()
	{
		$count = MonitorLogProductModel::select(DB::raw('count(DISTINCT `in_param`) as count'))
									   ->where('status', 0)
									   ->where('apikey', $this->apikey)
									   ->where('product_id', $this->product_id)
									   ->get()
									   ->first();
		
		return $count['count'];
	}
}