<?php

namespace App\Console\Commands\Monitor;

use App\Models\Channel;
use App\Models\feishu\FeishuChannelContractApprovalProcess;
use App\Models\SystemUser;
use App\Providers\Tool\SendMailService;
use App\Repositories\FeishuRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

//渠道合同到期监控
class ChannelContractDueMonitor extends Command
{
	protected $signature = 'channel_contract_due
	{--type= : 默认1根据可用渠道列表检测渠道是否到期；2拉取飞书审批实例且同步更新日志状态等信息}
	{--appoint_date= : 指定日期}
	{--appoint_channel_id= : 指定渠道id}
	';

	protected $description = '渠道合同到期监控';

	protected $start_date;
	protected $end_date;
	protected $appoint_date;
	protected $appoint_channel_id;
	protected $type = 1;

    //间隔合同到期那些天开始发邮箱
    protected $email_interval_days = [60, 45, 30, 22, 15, 7, 0, -30, -60, -90];
    //间隔合同到期那些天开始发OA
    protected $oa_interval_days = [30, 15, 7, 0, -30, -60, -90];
    //发送邮件数据
    protected $send_email_data = [];
    //发送oa数据
    protected $send_oa_data = [];
    protected $feishu = '';

    protected $username_to_email_map = [
        '林蒙蒙' => '<EMAIL>',
        '王梦思' => '<EMAIL>',
        '武泽智' => '<EMAIL>',
        '张宁宁' => '<EMAIL>',
    ];

    protected $email_map = [
        '林蒙蒙' => [
            'direct_address' => [['email' => '<EMAIL>']],
            'cc_address' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ]
        ],
        '王梦思' => [
            'direct_address' => [['email' => '<EMAIL>']],
            'cc_address' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
        ],
        '武泽智' => [
            'direct_address' => [['email' => '<EMAIL>']],
            'cc_address' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ]
        ],
    ];


    /* 特殊渠道 因此重复的需要过滤掉
    联通、数盾，是一家，合同预警报：联通
    品钛、品钛大接口，是一家，合同预警报：品钛大接口
    瀚境、瀚境V2，是一家，合同预警报：瀚境
    全国移动、移动评分、移动01，是一家，合同预警报：移动01
    【号码融】C5、【号码融】D-C5，是一家，合同预警报：【号码融】C5
     */
    protected $filter_channnel = [110, 112, 136, 8, 40, 811];


	public function handle()
	{
		//校验参数
        if(!$this->checkParams()){
            return;
        }

        if($this->type == 1){
            //获取所有可用渠道
            $list = $this->getUsableChannel();

            $this->done($list);

            $this->output->success("渠道合同到期监控成功执行");
        }else{

            $this->pullApprovalInstances();

            $this->output->success("渠道合同到期审批实例拉取更新结束");
        }

	}

	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2024/12/23
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
        $options = $this->options();

        $this->type = $options['type'] ?: 1;
        $this->appoint_date = $options['appoint_date'] ?: date('Ymd');
        $this->appoint_channel_id = $options['appoint_channel_id'] ?: '';

        if (!preg_match('/^\d{8}$/', $this->appoint_date)) {
            echo "指定日期格式不正确\r\n";
            return false;
        }

        return true;

	}

    protected function pullApprovalInstances(){
        $list = $this->getApprovelLogData();

        if(empty($list)){
            return false;
        }

        try{
            $all_status = array_keys(FeishuChannelContractApprovalProcess::$status);
            $this->feishu = new FeishuRepository();
            foreach ($list as $item){
                $id = $item['id'];
                $instance_id = $item['process_instance_id'];
                $info = $this->feishu->get_publicity_approval_instance_info($instance_id);
                usleep(200000);
                if(isset($info['code']) && $info['code'] == 0 && in_array($info['data']['status'], $all_status)){
                    $this->updateApprovalLog($id, $info['data']['status'], $info['data']['form']);
                }else{
                    $msg = $info['msg'] ?? '';
                    $this->sendException($msg, $item['channel_id'], '拉取飞书公出审批失败');
                }

            }
        }catch (\Exception $e){
            $this->sendException('', $e->getMessage(), '拉取飞书合同到期审批异常');
        }

    }

    protected function getUsableChannel(){
        $result = [];
        $expire_time = 90*86400;//90天
        Channel::where('status', 1)
            ->get()
            ->each(function ($item) use(&$result, $expire_time){
                //如果指定了渠道 就只检测指定渠道
                if(!empty($this->appoint_channel_id)){
                    if($item['channel_id'] != $this->appoint_channel_id){
                        return;
                    }
                }

                if(in_array($item['channel_id'], $this->filter_channnel)){
                    return;
                }

                //如果合同到期日期不是一个有效的日期 忽略检测；如1970-01-01、0000-00-00、或空等等
                $contract_end_time = strtotime($item['contract_end_date']);
                if($contract_end_time < 0 || empty($contract_end_time)){
                    return;
                }

                //到期超过90天 不在提醒
                //$now_time = strtotime(date('Ymd'));
                $now_time = strtotime($this->appoint_date);
                if($now_time > $contract_end_time && ($now_time - $contract_end_time) > $expire_time){
                    return;
                }

                //如果没有商务跟进人进行提醒处理(最好报警处理)
                if(empty($item['channel_follower'])){
                    $item['channel_follower'] = '王广利';
                }

                $result[] = $item->toArray();
            });

        return $result;
    }

	protected function done($list){
        //$now_time = strtotime(date('Ymd'));
        $now_time = strtotime($this->appoint_date);
        foreach ($list as $item){
            //从日志中判断是否续约 是否完成续约等 看是否过滤该渠道
            $is_filter = $this->IsFilterChannelByIsRenewal($item['channel_id']);
            if($is_filter){
                continue;
            }

            $is_filter = $this->IsFilterChannelByIsCompleteRenewal($item['channel_id']);
            if($is_filter){
                continue;
            }

            //计算距离到期天数
            //$item['contract_end_date'] = '2024-11-25';
            $contract_end_time = strtotime($item['contract_end_date']);
            $due_days = ($contract_end_time - $now_time)/86400;

            //过邮件规则
            $this->checkEmailRules($due_days, $item);

            //过飞书OA规则
            $this->checkOARules($due_days, $item);
        }

        //发送邮箱预警
        $this->sendEmailWarning();

        ////发送飞书oa审批预警
        $this->sendOAWarning();

    }

    protected function sendEmailWarning(){
        if(empty($this->send_email_data)){
            return;
        }

        foreach ($this->send_email_data as $follower => $follower_data){
            $this->sendMail($follower, $follower_data);
        }

    }

    protected function sendOAWarning(){
        if(empty($this->send_email_data)){
            return;
        }

        $addLog = [];
        $this->feishu = new FeishuRepository();
        foreach ($this->send_oa_data as $item){
            /*
            if($item['channel_follower'] != '武泽智'){
                continue;
            }

            if( $item['channel_id']!=809){
                continue;
            }
            */

            try {
                //如果上个审批状态还在审批中，则先取消在发起新的
                $check = $this->checkContractApprovalLog($item);
                if(!$check){
                    continue;
                }

                $user_id = $this->getUserIdByRealname($item['channel_follower']);
                //$user_id = $this->getUserIdByRealname('张宁宁');

                //发飞书审批 & 写日志
                $res = $this->feishu->create_channel_contract_approval($user_id, $item);
                if(isset($res['code']) && $res['code'] == 0){
                    //审批正常发成功在写日志
                    $addLog[] = [
                        'user_id' => $user_id,//跟进人飞书中对应的uid
                        'channel_id' => $item['channel_id'],
                        'contract_end_date' => $item['contract_end_date'],
                        'contract_due_days' => $item['due_days'],
                        'channel_follower' => $item['channel_follower'],
                        'process_instance_id' => $res['data']['instance_code'] ?? '',
                        'create_time' => time()
                    ];

                }else{
                    $this->sendException($res, $item['channel_id']);
                }
            }catch (\Exception $e){
                //$error = '获取渠道商务跟进人'.$item['channel_follower'].'的飞书userid异常';
                $error = '文件'.$e->getFile().'; 行号'.$e->getLine().';'.$e->getMessage();
                $this->sendException($error, $item['channel_id']);
                continue;
            }


        }

        //写日志
        if(!empty($addLog)){
            FeishuChannelContractApprovalProcess::insert($addLog);
        }

    }


    protected function IsFilterChannelByIsRenewal($channel_id){
        $log = FeishuChannelContractApprovalProcess::select(['*'])
            ->where('channel_id', $channel_id)
            ->where('is_renewal', 0)
            ->where('status', 'APPROVED')
            ->where('real_result', 2)
            ->orderBy('id', 'desc')
            ->first();

        if(count($log)){
            $log = $log->toArray();
        }else{
            $log = [];
        }

        if(empty($log)){
            //不过滤
            return false;
        }

        $diff = time() - $log['create_time'];
        if($diff > 100*86400){
            //不过滤(超过100天后 可能该渠道从不续约线下又变成续约了，新一轮的到期来了，因此不用过滤，而这个100天的时间限制是随便定的)
            return false;
        }else{
            //过滤(100天之内该渠道已经不续约了，但是运营没有及时在后台修改对应状态，导致第二次(如还有15日第二次到期提醒)预警触发，这时应该过滤不在报警
            return true;
        }

    }

    protected function IsFilterChannelByIsCompleteRenewal($channel_id){
        $log = FeishuChannelContractApprovalProcess::select(['*'])
            ->where('channel_id', $channel_id)
            ->where('is_complete_renewal', 1)
            ->where('status', 'APPROVED')
            ->where('real_result', 2)
            ->orderBy('id', 'desc')
            ->first();

        if(count($log)){
            $log = $log->toArray();
        }else{
            $log = [];
        }

        if(empty($log)){
            //不过滤
            return false;
        }

        $diff = time() - $log['create_time'];
        if($diff > 100*86400){
            //不过滤(超过100天后 可能该渠道从完成续约且运营对到期的合同日期已修改成新的日期了，但新的一轮的到期又来了(一般合同都是最低一年的时间)，因此不用过滤，而这个100天的时间限制是随便定的)
            return false;
        }else{
            //过滤(100天之内该渠道已经完成续约了，但是运营没有及时在后台修改对应合同日期，导致第二次(如还有15日第二次到期提醒)预警触发，这时应该过滤不在报警
            $now_date = Date('Y-m-d');
            $new_contract_date = $log['new_contract_date'];
            if ($now_date == $new_contract_date){ // 预期完成日期与当前日期一致报警 其他情况下不预警
                 return false;
            }else{
                 return true;
            }
        }

    }

    protected function checkEmailRules($due_days, $info){
        if(!in_array($due_days, $this->email_interval_days)){
            return;
        }

        $this->send_email_data[$info['channel_follower']][] = [
            'channel_id' => $info['channel_id'],
            'channel_name' => $info['label'],
            'contract_end_date' => $info['contract_end_date'],//合同到期日
            'due_days' => $due_days,//距离到期天数
        ];

    }

    protected function checkOARules($due_days, $info){
        if(!in_array($due_days, $this->oa_interval_days)){
            return;
        }

        $this->send_oa_data[] = [
            'channel_id' => $info['channel_id'],
            'channel_name' => $info['label'],
            'contract_end_date' => $info['contract_end_date'],//合同到期日
            'due_days' => $due_days,//距离到期天数
            'channel_follower' => $info['channel_follower']//跟进人
        ];
    }

    protected  function  sendMail($follower, $data)
    {
        //跟进人不同，抄送的人也不同
        $direct_address = $this->email_map[$follower]['direct_address'];
        $cc_address = $this->email_map[$follower]['cc_address'];

        //$direct_address = [['email' => '<EMAIL>']];
        //$cc_address = [['email' => '<EMAIL>'], ['email' => '<EMAIL>']];

        //发送内容拼接
        $html = $this->createHtml($data);
        $mail = new SendMailService();
        //$subject = date('Ymd').'渠道合同到期预警';
        $subject = $this->appoint_date.'渠道合同到期预警';


        //发送
        $mail->setFromName('金融后台项目组')
            ->setAddressee($direct_address)
            ->setCC($cc_address)
            ->setSubject($subject)
            ->setContent($html)
            ->sendByAsync();
//            ->send();

    }



    /*
     * 生成邮件发送内容
     */
    private function createHtml($data)
    {
        $content = '';

        $content .= '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
            font-size   : 12px;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<div>';
//<h1>'; $content .= '</h1>';

        $html =  $this->createFirstTable($data);

        $content .= $html;

        $content .= '<br />
</div> 

';

        return $content;

    }


    //警报内容
    private function createFirstTable($data)
    {
        $html = '';

        $html .= <<<HTML
        <h4>&nbsp;&nbsp;截止今日，以下合同即将到期，需要您关注，及时做续约准备！</h4>
        <h2>渠道到期情况列表</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">渠道id</th>
        <th align="center">渠道名称</th>
        <th align="center">合同到期时间</th>
        <th align="left">距离到期天数</th>
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $key=>$item){

            $html .= <<<HTML
<tr>
	<td style="text-align:center"  text-align:center" align="center" >{$item['channel_id']}</td>
	<td style="text-align:center" align="center" >{$item['channel_name']}</td>
	<td align="left">{$item['contract_end_date']}</td>
	<td align="left">{$item['due_days']}</td>
</tr>
HTML;

        }

        $html .= '</table>';

        return $html;
    }


    public function getUserIdByRealname($realname = '') {
        $result = '';

        //根据配置邮箱获取飞书的user id
        $email = $this->username_to_email_map[$realname] ?? '';
        if (!empty($email)){
            $res = $this->feishu->get_user_by_email($email);
            $result =  $res[$email]??'';
        }
        if(!empty($result)){
            return $result;
        }

        //获取销售的邮箱(如果没有配置则查库获取)
        $email = SystemUser::getUserInfoByRealname($realname)['email'];
        //根据邮箱获取飞书的user id
        if (!empty($email)){
            $res = $this->feishu->get_user_by_email($email);
            $result =  $res[$email]??'';
        }
        if(!empty($result)){
            return $result;
        }

        //获取销售的电话
        $phone = SystemUser::getUserInfoByRealname($realname)['phone'];
        //根据电话获取飞书的user id
        if (!empty($phone)){
            $res = $this->feishu->get_user_by_mobile($phone);
            $result =  $res[$phone]??'';
        }
        return $result;
    }

    public function checkContractApprovalLog($channel_info){
        //往前推31天 这个时间限制无所谓
        //$now_time = strtotime(date('Ymd'));
        $now_time = strtotime($this->appoint_date);
        $start_time = $now_time - 31*86400;
        $where = [
            ['create_time', '>=', $start_time],
            ['status', '=', 'PENDING'],
            ['real_result', '=', 1],
            ['channel_id', '=', $channel_info['channel_id']],
        ];
        $log = FeishuChannelContractApprovalProcess::getNotProcessed($where);
        if(empty($log)){
            return true;
        }
        try{

            //如果存在审批中的则先取消(使用回撤审批也可以，但是回撤操作没有备注，因此使用拒绝审批操作，但拒绝审批需要先获取审批详情，获取到一些关键参数)
            $instance_id = $log['process_instance_id'];
            $info = $this->feishu->get_publicity_approval_instance_info($instance_id);
            usleep(200000);

            if(isset($info['code']) && $info['code'] == 0){
                if($info['data']['status'] == 'PENDING'){
                    return $this->updateApprovalProcess($log, $info['data']);
                }else{
                    //说明真实状态已经不是PENDING，直接同步修改日志状态即可
                    $updateData['status'] = $info['data']['status'];
                    $updateData['real_result'] = 2;
                    $updateData['update_time'] = time();
                    FeishuChannelContractApprovalProcess::updateData(['id' => $log['id']], $updateData);
                    return true;
                }

            }else{
                $msg = $info['msg'] ?? '';
                $this->sendException($msg, $channel_info['channel_id'], '渠道合同到期实例详情获取失败');
                return false;
            }

        }catch (\Exception $e){
            $error = '文件'.$e->getFile().'; 行号'.$e->getLine().';'.$e->getMessage();
            $this->sendException($error, $channel_info['channel_id'], '渠道合同到期实例详情获取失败');
            return false;
        }

        return false;
    }

    public function updateApprovalProcess($log, $data = []){
        $task_list = $data['task_list'] ?? [];
        $task_id = '';
        $user_id = '';
        foreach ($task_list as $item){
           if($item['status'] == 'PENDING'){
               $task_id = $item['id'];
               $user_id = $item['user_id'];
               break;
           }
        }

        $remark = '该渠道新的审批已发起,系统替该审批人自动拒绝旧的审批';
        $data = [
            'approval_code' => $data['approval_code'] ?? '',
            'instance_code' => $data['instance_code'] ?? '',
            'user_id' => $user_id,
            'task_id' => $task_id,
            'comment' => $remark,
        ];

        try{
            //拒绝审批任务
            $info = $this->feishu->reject_approval_task($data);
            if(isset($info['code']) && $info['code'] == 0){
                //更新审批日志
                $updateData['status'] = 'REJECTED';
                $updateData['real_result'] = 2;
                $updateData['update_time'] = time();
                $updateData['remark'] = $remark;
                FeishuChannelContractApprovalProcess::updateData(['id' => $log['id']], $updateData);
                return true;
            }else{
                $msg = $info['msg'] ?? '';
                $this->sendException($msg, $log['channel_id'], '渠道合同到期审批拒绝失败');
                return false;
            }

        }catch (\Exception $e){
            $error = '文件'.$e->getFile().'; 行号'.$e->getLine().';'.$e->getMessage();
            $this->sendException($error, $log['channel_id'], '渠道合同到期审批拒绝异常');
            return false;
        }

        return false;
    }

    protected function updateApprovalLog($id, $status, $form){
        if($status == 'PENDING'){
            return;
        }

        //默认值
        $is_renewal = 0;
        $is_complete_renewal = 0;
        $new_contract_date = '1970-01-01';
        $remark = '';
        $form = json_decode($form, true);
        foreach ($form as $item){
            if($item['name'] == '是否续约'){
                $is_renewal = $item['value'] == '是' ? 1 : 0;
            }

            if($item['name'] == '是否完成续约'){
                $is_complete_renewal = $item['value'] == '是' ? 1 : 0;
            }

            if($item['name'] == '预计完成时间'){
                $new_contract_date = substr($item['value'], 0, 10);
            }

            if($item['name'] == '原因'){
                $remark = mb_substr($item['value'], 0, 100);
            }
        }

        //更新审批日志
        $updateData['is_renewal'] = $is_renewal;
        $updateData['is_complete_renewal'] = $is_complete_renewal;
        $updateData['new_contract_date'] = $new_contract_date;
        $updateData['remark'] = $remark;
        $updateData['status'] = $status;
        $updateData['real_result'] = 2;
        $updateData['update_time'] = time();
        FeishuChannelContractApprovalProcess::updateData(['id' => $id], $updateData);
    }

    //获取审批日志
    private function getApprovelLogData()
    {
        //往前推31天 这个时间限制无所谓
        $now_time = strtotime(date('Ymd'));
        $start_time = $now_time - 31*86400;
        $where = [
            ['create_time', '>=', $start_time],
            ['status', '=', 'PENDING'],
            ['real_result', '=', 1],
        ];
        $list = FeishuChannelContractApprovalProcess::getListByCondition($where);

        return count($list) ? $list->toArray() : [];
    }

    public function sendException($message = '', $channel = '', $title = '渠道合同到期实例创建失败'){
        $msg = $title.PHP_EOL;
        $msg .= PHP_EOL;
        $msg .= "渠道id:".$channel.PHP_EOL;
        $msg .= "创建时间:".date('Y-m-d H:i').PHP_EOL;
        $msg .= "错误信息:".json_encode($message, JSON_UNESCAPED_UNICODE);

        sendWechatNotice($msg);
    }


}