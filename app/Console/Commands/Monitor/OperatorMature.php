<?php

namespace App\Console\Commands\Monitor;

use App\Models\ChannelAccount;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Console\Command;
use App\Models\ChannelAccountAlarmLogs;
use Illuminate\Support\Facades\Log;


class OperatorMature extends Command
{

    use CurlTrait;
    protected $signature = 'operator:mature_monitoring';
    protected $description = '运营商相关数据监测';

    public function handle()
    {
        //获取渠道合同到期日期信息
        $channelAccountDatas = $this->getContractInfos();

        //获取当日报警提醒信息 log
        $channelAccountAlarmLogsModel = new ChannelAccountAlarmLogs();

        $alarmDatas = [];
        foreach ($channelAccountDatas as $k => $v){
            if (!$v['external_channel_id'] || $v['status'] != ChannelAccount::STATUS_VALID) {
                continue;
            }

            //合同到期
            $res = $this->checkContractEndDateAlarm($v);

            //无报警提醒
            if (!$res['level']) {
                continue;
            }
            $where = [
                ['external_channel_id', '=', $v['external_channel_id']],
                ['level', '=', $res['level']],
                ['type', '=', $res['type']],
            ];

            //获取指定类型等级没过期的报警日志
            $alarmLog = $channelAccountAlarmLogsModel->getNotExpireDataByWhere($where);

            //存在,不再重复提醒
            if ($alarmLog) {
                continue;
            }

            $alarmDatas[] = [
                'externalChannelId' => $v['external_channel_id'],
                'level' => $res['level'],
                'type' => $res['type'],
                'alarmTimeTnterval' => $res['alarmTimeInterval'],
                'alarmMsg' => $res['alarmMsg'],
            ];
        }

        //没有报警
        if (!$alarmDatas) {
            echo 'NO_ALARM' . PHP_EOL;
            die;
        }

        $time = time();
        foreach ($alarmDatas as $alarmData) {
            //调用接口报警提醒
            $alarmRes = $this->callAlarm($alarmData);
            $expireTime =  $time + 60 * $alarmData['alarmTimeTnterval'];
            //添加日志
            $addLog = [
                'external_channel_id' => $alarmData['externalChannelId'],
                'type' => $alarmData['type'],
                'level' => $alarmData['level'],
                'expire_time' => date('Y-m-d H:i:s', $expireTime),
                'created_time' => date('Y-m-d H:i:s', $time),
                'extend' => json_encode($alarmRes),
            ];
            ChannelAccountAlarmLogs::insert($addLog);
        }
        echo 'DONE' . PHP_EOL;
        die;
    }

    //检测合同过期报警
    public function checkContractEndDateAlarm($channelData)
    {
        $return = ['level' => 0, 'alarmTimeTnterval' => 0, 'type'=> ChannelAccountAlarmLogs::TYPE_CONTRACT];

        $today = date('Y-m-d',time());
        $remainder = $this->diffBetweenTwoDays($channelData['contract_end_date'], $today);

        $level = 0;
        $alarmTimeInterval = 0;
        $remainderDay = round($remainder);
        $alarmMsg = $channelData['account'] . " 渠道合同还有{$remainderDay}天到期";//一天
        //合同到期30 - 90天  报警级别是关注，频率为每天
        if ($remainder > 30 && $remainder <=90) {
            $level = ChannelAccountAlarmLogs::LEVEL_GUANZHU;
            $alarmTimeInterval = 1440;//一天
        }

        //合同到期1 -30天  报警级别是严重，频率为每天
        if ($remainder >= 1 && $remainder <=30) {
            $level = ChannelAccountAlarmLogs::LEVEL_YANZHONG;
            $alarmTimeInterval = 1440;//一天
        }

        //合同到期当天  报警级别是严重，频率为每小时
        if ($remainder < 1 && $remainder >= 0) {
            $level = ChannelAccountAlarmLogs::LEVEL_JQ_YANZHONG;
            $alarmTimeInterval = 60;//一小时
            $alarmMsg = $channelData['account'] . " 渠道合同今天到期";
        }

        //合同已经过期  报警级别是严重，频率为每小时
        if ($remainder < 0) {
            $level = ChannelAccountAlarmLogs::LEVEL_JQ_YANZHONG;
            $alarmTimeInterval = 60;//一小时
            $alarmMsg = $channelData['account'] . " 渠道合同已经过期";
        }

        $return['level'] = $level;
        $return['alarmTimeInterval'] = $alarmTimeInterval;
        $return['alarmMsg'] = $alarmMsg;
        return $return;
    }

    public function callAlarm($data)
    {
        $return = ['alarmType'=>'','alarmRes'=>''];
        switch ($data['level']) {
            case ChannelAccountAlarmLogs::LEVEL_GUANZHU:
                $return['alarmType'] = 'feishu';
                $return['alarmRes'] = $this->alarmByFeishu($data['alarmMsg']);
                break;
            case ChannelAccountAlarmLogs::LEVEL_YANZHONG:
            case ChannelAccountAlarmLogs::LEVEL_JQ_YANZHONG:
                $return['alarmType'] = ['feishu','wechat','phone'];
                $return['alarmRes']['feishu'] = $this->alarmByFeishu($data['alarmMsg']);
                $return['alarmRes']['wechat'] = $this->alarmByWechat($data['alarmMsg']);
                $return['alarmRes']['phone'] = $this->alarmByPhone($data['alarmMsg'], ***********);
                break;
        }
        return $return;
    }

    public function alarmByFeishu($alarmMsg)
    {
        return $this->sendMsg($alarmMsg, 3);
    }

    public function alarmByWechat($alarmMsg)
    {
        return $this->sendMsg($alarmMsg, 2);
    }

    public function sendMsg($content, $level)
    {
        $url = 'http://fina-monitor.dianhua.cn/alarmlog/sendAlarm';
        $post = [
            'product_id'=>0,
            'title'=>'渠道运营商预警',
            'content'=>$content,
            'level'=>$level,
            'model'=>'operator' . date('Ymd'),
            'group_tag'=>'operator',
        ];
        return $this->post($url, $post);
    }

    public function alarmByPhone($alarmMsg, $phone)
    {
        $url = 'http://fina-monitor.dianhua.cn/call/phone';
        $timestamp = time();
        $secre = 'ali_yun_call_phone_666';
        $str = urlencode("msg={$alarmMsg}&phone={$phone}&timestamp={$timestamp}&title=渠道运营商预警");
        $sign = md5($str . $secre);
        $postData = [
            'phone' => $phone,
            'title' => '渠道运营商预警',
            'msg' => $alarmMsg,
            'timestamp' => $timestamp,
            'sign' => $sign,
        ];
        return $this->post($url, $postData);
    }

    /*
     * $day1 合同到期时间
     * $day2 当前日期
     */

    private  function  diffBetweenTwoDays ($day1, $day2)
    {
        $second1 = strtotime($day1);
        $second2 = strtotime($day2);
        $second = $second1 - $second2;
        if($second != 0) {
            $diffday =   $second / 86400;
        }elseif($second == 0){
            $diffday = 0;
        }
        return  $diffday;
    }

    public function getContractInfos()
    {
        //请求接口更新本地
        $url = 'http://fin-operator.dianhua.cn/service1/channel_consumption_status/get_channel_contract';
        $data = $this->post($url,[]);
        $curlDatas = empty($data['data']) ? [] : $data['data'];

        Log::info('PULL_CHANNEL_CONTRACTS',$data);

        //获取本地 mysql
        $channelAccountModel = new ChannelAccount();
        $channelAccountDatas = $channelAccountModel->getChannelAccount();

        //接口数据异常
        if (!$curlDatas) {
            $this->alarmByPhone('合同日期接口返回数据为空', ***********);
            return $channelAccountDatas;
        }
        $mysqlMap = array_column($channelAccountDatas, null, 'external_channel_id');
        //数据对比
        foreach ($curlDatas as $item) {
            if (empty($item['channel_id']) || empty($item['contract_end_date'])) {
                $this->alarmByPhone('合同日期接口返回数据异常', ***********);
            }

            //存在即更新
            if (isset($mysqlMap[$item['channel_id']])) {
                $updateData = [
                    'contract_end_date' => $item['contract_end_date'],
                    'status' => ChannelAccount::STATUS_VALID
                ];
                $channelAccountModel->updateData($mysqlMap[$item['channel_id']]['id'], $updateData);
                unset($mysqlMap[$item['channel_id']]);
                continue;
            }

            //不存在则新增
            $addData = [
                'account'=>$item['channel_name'],
                'external_channel_id'=>$item['channel_id'],
                'contract_end_date'=>$item['contract_end_date'],
            ];
            $res = ChannelAccount::insert($addData);
            if (!$res) {
                $this->alarmByPhone('合同日期接口新增mysql数据异常', ***********);
            }
        }

        //下架渠道
        if (!empty($mysqlMap)) {
            $updateIds = array_column($mysqlMap,'id');
            $updateData = [
                'status' => ChannelAccount::STATUS_UNVALID
            ];
            $channelAccountModel->updateDataByIds($updateIds, $updateData);
        }

        return $channelAccountModel->getChannelAccount();
    }
}