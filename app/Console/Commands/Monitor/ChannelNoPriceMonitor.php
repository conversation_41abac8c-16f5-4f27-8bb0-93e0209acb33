<?php

namespace App\Console\Commands\Monitor;

use App\Models\Account;
use App\Models\BillCostV2;
use App\Models\ChannelInterface;
use App\Models\ConfigPriceInterface;
use App\Models\EmailConfig;
use App\Models\StatisticsInterfaceUsage;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;


//有渠道接口调用量无计费预警
class ChannelNoPriceMonitor extends Command
{
	protected $signature = "channel_no_price_warning {email?}
	{--start_date= : 统计起始日期（格式Ymd）}
    {--end_date= : 统计截止日期 (格式Ymd)}
    ";

	protected $description = '渠道有调用量无计费预警';

    private $scene = 'channel_no_price_warning';
    private $start_date;
    private $end_date;
    //no_price无计费: 没有配置计费配置、后期添加的计费配置但是之前的调用的没有重跑
    //zero_price计费为0:有账单，但是money<=0(价格配置为0或非正常价格如价格前有空格)
    private $result = ['no_price' => [], 'zero_price' => []];
    private $data = ['no_price' => [], 'zero_price' => []];
    private $zhi_hui_zu_ji_iids = [];//智慧足迹接口id列表
    private $self_iids = [];//自有渠道接口id列表

	public function handle()
	{
        if(!$this->checkParams()){
            return;
        }

        $this->email = $this->argument('email');

        $date = $this->start_date;
        while ($date <= $this->end_date){
            $this->checkProductPriceByUsage($date);
            $date = date('Ymd', strtotime($date) + 86400);
        }

        foreach ($this->result['no_price'] as $iid => $item){
            $row['interface_id'] = $iid;
            $days = count($item);
            $row['date'] = implode(', ', $item).' (共计'.$days.'天)';

            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($row['interface_id']);

            $this->data['no_price'][$channel_id][] = $row;
        }
        unset($this->result['no_price']);

        foreach ($this->result['zero_price'] as $iid => $item){
            $row['interface_id'] = $iid;
            $days = count($item);
            $row['date'] = implode(', ', $item).' (共计'.$days.'天)';

            $channel_id = RedisCache::instance('iid_channelId_mapping')->get($row['interface_id']);

            $this->data['zero_price'][$channel_id][] = $row;
        }
        unset($this->result['zero_price']);

        $this->sendMail();

	}

    private function checkParams(){
        $options = $this->options();
        $this->start_date = $options['start_date'];
        $this->end_date = $options['end_date'];

        if(empty($this->start_date) && empty($this->end_date)){
            echo "起始日期和截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->start_date) && empty($this->end_date)){
            echo "截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->end_date) && empty($this->start_date)){
            echo "起始日期不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->start_date) && !empty($this->start_date)) {
            echo "起始日期格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date) && !empty($this->end_date)) {
            echo "截止日期格式不正确\r\n";
            return false;
        }

        $this->zhi_hui_zu_ji_iids = ChannelInterface::select([
            'id',
            'channel_id'
        ])
            ->where('channel_id', 141)
            ->pluck('id')
            ->toArray();

        $this->self_iids = ChannelInterface::select([
            'id',
            'channel_id'
        ])
            ->where('channel_id', 124)
            ->pluck('id')
            ->toArray();
        return true;
    }

    protected function checkProductPriceByUsage($date){
        //获取有调用量的渠道接口列表
        $usageList = StatisticsInterfaceUsage::getCallIidStat(['date' => $date]);
        $usageListTmp = [];
        foreach ($usageList as $item){
            if($item['total_num'] <= 0 || ($item['success_num'] <= 0 && $item['valid_num'] <= 0 && $item['charge_num'] <= 0)){
                continue;
            }
            $index = $item['interface_id'];
            $usageListTmp[$index] = ['success_num' => $item['success_num'], 'valid_num' => $item['valid_num'], 'charge_num' => $item['charge_num']];
        }
        unset($usageList);

        $billList = BillCostV2::getCallIidStat(['date' => $date]);
        $billListTmp = [];
        foreach ($billList as $item){
            $index = $item['interface_id'];
            if(!isset($billListTmp[$index])){
                $billListTmp[$index] = ['money' => $item['money'], 'money_original' => $item['money_original']];
            }else{
                $billListTmp[$index]['money'] = bcadd($billListTmp[$index]['money'], $item['money'], 6);
                $billListTmp[$index]['money_original'] = bcadd($billListTmp[$index]['money_original'], $item['money_original'], 6);
            }

        }
        //unset($billList);

        $priceConfigList = $this->getPriceConfig($date);
        foreach ($usageListTmp as $iid => $item){
            if(in_array($iid, $this->self_iids)){
                continue;
            }

            if(!isset($priceConfigList[$iid]) ){

                $this->result['no_price'][$iid][] = $date;
                continue;
            }

            $priceInfo = $priceConfigList[$iid];
            $price = json_decode($priceInfo['price'], true);
            if (json_last_error() != JSON_ERROR_NONE) {

                $msg = "渠道有调用量无计费预警".PHP_EOL;
                $msg .= PHP_EOL;
                $msg .= "数据日期:".$date.PHP_EOL;
                $msg .= "接口Id:".$iid.PHP_EOL;
                $msg .= "报警原因:计费配置价格解析异常";
                sendWechatNotice($msg);
                //dd($val, '价格解析异常');
            }

            $price_model_arr = [];
            foreach ($price as $p){
                if($p['price_model'] == 1){
                    $model = '成功量';
                }else if($p['price_model'] == 2){
                    $model = '查得量';
                }else if($p['price_model'] == 3){
                    $model = '计费量';
                }else{
                    $model = '未知';
                }

                $price_model_arr[] = $model;
            }
            $accord = $this->checkPriceModel($date, $iid, $price_model_arr);

            if($accord == '成功量'){
                $field = 'success_num';
            }else if($accord == '查得量'){
                $field = 'valid_num';
            }else if($accord == '计费量'){
                $field = 'charge_num';//计费量
            }else{
                continue;
            }

            //有计费配置且对应依据的调用量也存在 但无账单收入
            if(!isset($billListTmp[$iid])){
                //有对应计费依据调用量 却无收入的是需要预警的；无对应计费依据调用量 无收入的是正常的不用预警；
                if($item[$field] > 0){

                    $this->result['no_price'][$iid][] = $date;
                }

            }

        }
        unset($usageListTmp);
        unset($billListTmp);

        //有计费配置 也有账单 但是收入为0（说明计费价格为0）
        $zero_price = [];
        foreach ($billList as $item){
            $iid = $item['interface_id'];
            $product_id = $item['product_id'];
            $source = $item['source'];
            $index = $iid."_".$product_id."_".$source;

            //历史数据处理操作原因
            if(in_array($product_id, [41001, 41002]) && in_array($iid, $this->zhi_hui_zu_ji_iids) && $source ==1){
                if($item['money_original'] <= 0){
                    $zero_price[$index][] = $date;
                }
            }else if($product_id>70000 && $product_id<79999 && $source ==1){
                if($item['money_original'] <= 0){
                    $zero_price[$index][] = $date;
                }
            }else {
                if($item['money'] <= 0){
                    $zero_price[$index][] = $date;
                }
            }

        }
        unset($billList);

        //按接口id合并一下
        foreach ($zero_price as $key => $item_days){
            $arr = explode('_', $key);
            $iid = $arr[0];
            if(!isset($this->result['zero_price'][$iid])){
                $this->result['zero_price'][$iid] = [];
            }
            //$product_id = $arr[1];
            //$source = $arr[2];
            foreach ($item_days as $v_date){
                if(!in_array($v_date, $this->result['zero_price'][$iid])){
                    $this->result['zero_price'][$iid][] = $v_date;
                }
            }

        }

        return true;
    }

	/**
	 * 获取发送的内容
	 *
	 * @return int
	 */

	protected  function  sendMail()
    {

        $emails = EmailConfig::getEamilAddressByScene($this->scene);
        //日报发送内容拼接
        $html = $this->createHtml($this->data);

        $mail = new SendMailService();
        $subject = $this->start_date.'-'.$this->end_date.'渠道接口有调用量无计费监控';

        if(!empty($emails)){
            foreach ($emails as $v){
                if (!is_null($this->email)){
                    if ($v['email'] != $this->email){
                        continue;
                    }
                }
                //日报 每日发送
                $mail->setFromName('金融后台项目组')
                    ->setAddressee([$v])
                    //->setCC($this->cc)
                    ->setSubject($subject)
                    ->setContent($html)
//                    ->sendByAsync();
                    ->send();
            }
        }


    }



    protected function getPriceConfig($date)
    {
        $result = [];

        ConfigPriceInterface::where('start_date', '<=', $date)
            ->orderBy('start_date', 'desc')
            ->get()
            ->map(function ($item) use (&$result) {
                $interface_id = $item['interface_id'];

                $key = $interface_id;
                if (!array_key_exists($key, $result)) {

                    $result[$key] = $item->toArray();
                }

            });

        return $result;

    }

    protected function checkPriceModel($date, $interface_id, $price_model_arr){
        $price_model_arr = array_unique($price_model_arr);
        $msg = "渠道有调用量无计费预警".PHP_EOL;
        $msg .= PHP_EOL;
        $msg .= "数据日期:".$date.PHP_EOL;
        $msg .= "接口Id:".$interface_id.PHP_EOL;
        if(in_array('未知', $price_model_arr)){
            $msg .= "报警原因:计费配置中计费依据存在未知";
            sendWechatNotice($msg);
            return '';
        }

        if(count($price_model_arr) >= 2){
            $msg .= "报警原因:计费配置中计费依据存在两种或两种以上";
            sendWechatNotice($msg);
            return '';
        }

        return $price_model_arr[0];
    }


    /*
     * 生成邮件发送内容
     */
    private function createHtml($data)
    {
        $content = '';

$content .= '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
            font-size   : 12px;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<div>';
//<h1>'; $content .= '</h1>';

$html =  $this->createFirstTable($data['no_price']);
$html .= '<br />';
$html .=  $this->createSecondTable($data['zero_price']);

$content .= $html;

$content .= '<br />
</div> 

';

 return $content;

}


    //警报内容
    private function createFirstTable($data)
    {
        $html = '';

        $html .= <<<HTML
        <h2>无计费配置列表</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">渠道</th>
        <th align="center">接口</th>
        <th align="left">日期</th>
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $channel_id=>$item){
            $rowspan = count($item);
            $channel_name = $this->getChannelName($channel_id);
            $firstRow  = array_shift($item);
            $interface_name = $this->getInterfaceName($firstRow['interface_id']);
            $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center"  text-align:center" align="center" rowspan="{$rowspan}">$channel_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$interface_name</td>
	<td align="left">{$firstRow['date']}</td>
</tr>
HTML;
            foreach ($item as $key => $item2){
                $interface_name = $this->getInterfaceName($item2['interface_id']);
                $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$interface_name</td>
	<td align="left">{$item2['date']}</td>
</tr>
HTML;
            }

        }


        $html .= '</table>';

        return $html;
    }

    private function createSecondTable($data)
    {
        $html = '';

        $html .= <<<HTML
        <h2>价格为0渠道接口列表</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">渠道</th>
        <th align="center">接口</th>
        <th align="left">日期</th>
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $channel_id=>$item){
            $rowspan = count($item);
            $channel_name = $this->getChannelName($channel_id);
            $firstRow  = array_shift($item);
            $interface_name = $this->getInterfaceName($firstRow['interface_id']);
            $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center" rowspan="{$rowspan}">$channel_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$interface_name</td>
	<td align="left">{$firstRow['date']}</td>
</tr>
HTML;
            foreach ($item as $key => $item2){
                $interface_name = $this->getInterfaceName($item2['interface_id']);
                $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$interface_name</td>
	<td align="left">{$item2['date']}</td>
</tr>
HTML;
            }

        }


        $html .= '</table>';

        return $html;
    }


    public function getChannelName($channel_id = ''){
        $channel_name = RedisCache::instance('channelId_label_mapping')->get($channel_id);
        return $channel_name;
    }

    public function getProductName($product_id = ''){
        $p_name = RedisCache::instance('productId_productName_mapping')->get($product_id);
        return $p_name;
    }

    public function getProductFatherId($product_id = ''){
        $fpid = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
        if(!$fpid){
            return $product_id;
        }
        return $fpid;
    }

    public function getAccountName($apikey = ''){
        $info = Account::getAccountByApikey($apikey);
        return $info['account_name'] ?? '';
    }

    public function getInterfaceName($iid = ''){
        $info = ChannelInterface::getInterfaceInfo(['id' => $iid]);
        return $info['label'] ?? '';
    }



}