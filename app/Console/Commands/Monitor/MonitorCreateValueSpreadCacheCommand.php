<?php

namespace App\Console\Commands\Monitor;

use App\Models\Monitor\ConfigProductValue;
use App\Models\MonitorLogProductModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * 创建蚂蚁金服值分布的缓存数据
 * Class MonitorCreateValueSpreadCacheCommand
 * @package App\Console\Commands\Monitor
 */
class MonitorCreateValueSpreadCacheCommand extends Command
{
	protected $signature = "monitor:create_value_spread_cache
	{--product_id=281 : 产品ID}
	";
	
	/**
	 * @var integer 产品ID
	 */
	protected $product_id;
	/**
	 * @var string 账号唯一标识
	 */
	protected $apikey;
	
	/**
	 * @var array 获取这个产品的值分布区间
	 */
	protected $section;
	
	public function handle()
	{
		//设置参数
		$this->setParam();
		
		if (!$this->section) {
			return;
		}
		
		//获取统计数据
		$data = $this->getUniqueInfo();
		if (!$data) {
			return;
		}
		
		//遍历统计的数据，将其进行合计
		$result = array_reduce($data, function ($result, $item) {
			$value = $this->getValue($item);
			
			if (!array_key_exists($value, $result)) {
				$result[$value] = [
					'count'    => 0,
					'run_time' => 0,
				];
			}
			
			$result[$value]['count']    = bcadd($result[$value]['count'], $item['count']);
			$result[$value]['run_time'] = bcadd($result[$value]['run_time'], $item['run_time']);
			
			return $result;
		}, []);
		
		$unique_success = array_sum(array_column($result, 'count'));
		
		$unique_total = $this->getUniqueTotal();
		
		//计算平均响应时间
		$run_time = $this->getAvgRunTime($result);
		
		$json = json_encode([
			'items'    => array_map(function ($item) {
				return $item['count'];
			}, $result),
			'run_time' => $run_time,
		], JSON_UNESCAPED_UNICODE);
		
		//获取不去重的调用成功量
		$success = $this->getSuccessTotal();
		
		//获取不去重的调用总量
		$total = $this->getAllTotal();
		
		//
		
		//redis存储
		$key   = $this->apikey . '_' . $this->product_id;
		$redis = Redis::connection('default');
		$redis->hset('MonitorCache', $key, $json);
		$redis->hset('MonitorCache', $key . '_all', $total);
		$redis->hset('MonitorCache', $key . '_success', $success);
		$redis->hset('MonitorCache', $key . '_unique_total', $unique_total);
		$redis->hset('MonitorCache', $key . '_unique_success', $unique_success);
		$redis->hset('MonitorCache', $key . '_update_at', date('Y-m-d H:i:s'));
	}
	
	/**
	 * 获取不去重总调用量
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 20:32
	 *
	 * @return array
	 */
	protected function getAllTotal()
	{
		return MonitorLogProductModel::where('apikey', $this->apikey)
									 ->where('product_id', $this->product_id)
									 ->count();
		
	}
	
	protected function getSuccessTotal()
	{
		return MonitorLogProductModel::where('apikey', $this->apikey)
									 ->where('product_id', $this->product_id)
									 ->where('status', 0)
									 ->count();
	}
	
	protected function getUniqueTotal()
	{
		return MonitorLogProductModel::select(DB::raw('count(DISTINCT `in_param`) as count'))
									  ->where('apikey', $this->apikey)
									  ->where('product_id', $this->product_id)
									  ->get()
									  ->first()->count;
	}
	
	
	/**
	 * 计算平均响应时间
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 20:47
	 *
	 * @param $data array 统计数据
	 *
	 * @return integer
	 */
	protected function getAvgRunTime($data)
	{
		$run_time = 0;
		$total    = 0;
		
		foreach ($data as $item) {
			$run_time = bcadd($run_time, $item['run_time']);
			$total    = bcadd($total, $item['count']);
		}
		
		return bcdiv($run_time, $total);
	}
	
	/**
	 * 获取值(根据数据库中配置的值范围区间，将其生成一个新的值)
	 *
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 13:43
	 *
	 * @param $data array 单条原始日志
	 *
	 * @return integer
	 */
	protected function getValue($data)
	{
		foreach ($this->section as $sectionItem) {
			$value     = $sectionItem['value'];
			$old_value = $data['value'];
			$type      = $sectionItem['type'];
			if (false !== $result = $this->isValue($value, $old_value, $type)) {
				return $result;
			}
		}
		
		return null;
	}
	
	/**
	 * 判断是否为当前区间的值
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 13:51
	 *
	 * @param $value integer 目标值
	 * @param $old   integer 原始日志值
	 * @param $type  integer 判断类型
	 *
	 * @return boolean
	 */
	protected function isValue($value, $old, $type)
	{
		switch ($type) {
			case 1:
				//等于
				
				return $value == $old && strlen($value) == strlen($old) ? $value : false;
				break;
			case 2:
				//大于
				
				return $old > $value ? '>' . $value : false;
				break;
			case 3:
				//小于
				
				return $old < $value ? '<' . $value : false;
				break;
			case 4:
				//区间
				$value = trim(trim($value, '('), ')');
				list($min, $max) = explode(',', $value);
				
				return $old >= $min && $old <= $max ? $value : false;
				break;
			case 5:
				//大于等于
				
				return $old >= $value ? '>=' . $value : false;
				break;
			case 6:
				//小于等于
				
				return $old <= $value ? '<=' . $value : false;
				break;
		}
		
		return false;
	}
	
	/**
	 * 设置参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 20:08
	 *
	 * @return void
	 */
	protected function setParam()
	{
		$this->product_id = $this->input->getOption('product_id');
		$this->apikey     = config('params.mayi_apikey');;
		$this->section    = ConfigProductValue::where('pid', $this->product_id)
											  ->get()
											  ->toArray();
	}
	
	/**
	 * 获取去重统计数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 20:32
	 *
	 * @return array
	 */
	protected function getUniqueInfo()
	{
		return MonitorLogProductModel::select([
			DB::raw('count(DISTINCT `in_param`) as count'),
			DB::raw('sum(`run_time`) as run_time'),
			'value',
		])
									 ->where('apikey', $this->apikey)
									 ->where('status', 0)
									 ->where('product_id', $this->product_id)
									 ->groupBy('value')
									 ->get()
									 ->toArray();
		
	}
	
	
}