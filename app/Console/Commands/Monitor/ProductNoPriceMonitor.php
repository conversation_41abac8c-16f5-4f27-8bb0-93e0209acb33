<?php

namespace App\Console\Commands\Monitor;

use App\Models\Account;
use App\Models\BillCost;
use App\Models\BillProductIncomeV2;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ConfigPriceCustomer;
use App\Models\EmailConfig;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\ChannelInterfaceRepository;
use App\Repositories\ChannelRepository;
use App\Models\ChannelRemit;


//有调用量无计费产品预警

/**
 * 主要解决的有问题的情况：
 * 1、一个客户可能1-3号调用了 但是4号加的计费配置 且计费从4号开始(这种重跑账单都没用1-3号是不会生成账单的)
 * 2、一个客户可能1-3号调用了 但是4号加的计费配置 且计费从1号开始(这种重跑账单可以)
 */
class ProductNoPriceMonitor extends Command
{
	protected $signature = "product_no_price_warning {email?}
	{--start_date= : 统计起始日期（格式Ymd）}
    {--end_date= : 统计截止日期 (格式Ymd)}
    ";

	protected $description = '产品有调用量无计费预警';

    private $scene = 'product_no_price_warning';
    private $start_date;
    private $end_date;
    private $result = ['no_price' => [], 'zero_price' => []];
    private $data = ['no_price' => [], 'zero_price' => []];

	public function handle()
	{
        if(!$this->checkParams()){
            return;
        }

        $this->email = $this->argument('email');

        $date = $this->start_date;
        while ($date <= $this->end_date){
            $this->checkProductPriceByUsage($date);
            $date = date('Ymd', strtotime($date) + 86400);
        }

        foreach ($this->result['no_price'] as $key => $item){
            $arr = explode('_', $key);
            $row['apikey'] = $arr[0];
            $row['product_id'] = $arr[1];
            $days = count($item);
            $row['date'] = implode(', ', $item).' (共计'.$days.'天)';

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($row['apikey']);

            $this->data['no_price'][$customer_id][] = $row;
        }
        unset($this->result['no_price']);

        foreach ($this->result['zero_price'] as $key => $item){
            $arr = explode('_', $key);
            $row['apikey'] = $arr[0];
            $row['product_id'] = $arr[1];
            $days = count($item);
            $row['date'] = implode(', ', $item).' (共计'.$days.'天)';

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($row['apikey']);

            $this->data['zero_price'][$customer_id][] = $row;
        }
        unset($this->result['zero_price']);

        $this->sendMail();

	}

    private function checkParams(){
        $options = $this->options();
        $this->start_date = $options['start_date'];
        $this->end_date = $options['end_date'];

        if(empty($this->start_date) && empty($this->end_date)){
            echo "起始日期和截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->start_date) && empty($this->end_date)){
            echo "截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->end_date) && empty($this->start_date)){
            echo "起始日期不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->start_date) && !empty($this->start_date)) {
            echo "起始日期格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date) && !empty($this->end_date)) {
            echo "截止日期格式不正确\r\n";
            return false;
        }

        return true;
    }

    protected function checkProductPriceByUsage($date){
        //获取有调用量的客户产品列表
        $usageList = StatisticsCustomerUsage::getCallProductStat(['date' => $date]);
        $usageListTmp = [];
        foreach ($usageList as $item){
            if($item['total_num'] <= 0 || ($item['success_num'] <= 0 && $item['valid_num'] <= 0)){
                continue;
            }
            $index = $item['apikey'].'_'.$item['call_product_id'];
            $usageListTmp[$index] = ['success_num' => $item['success_num'], 'valid_num' => $item['valid_num']];
        }
        unset($usageList);

        $billList = BillProductIncomeV2::getCallProductStat(['date' => $date]);
        $billListTmp = [];
        foreach ($billList as $item){
            $index = $item['apikey'].'_'.$item['call_product_id'];
            $billListTmp[$index] = ['money' => $item['moeny']];
        }
        unset($billList);

        $priceConfigList = $this->getPriceConfig($date);
        foreach ($usageListTmp as $key => $item){
            $arr = explode('_', $key);
            $apikey = $arr[0];
            $product_id = $arr[1];
            $fpid = $this->getProductFatherId($product_id);

            if(!isset($priceConfigList[$apikey.'_'.$fpid]) ){

                $this->result['no_price'][$key][] = $date;
                continue;
            }

            $priceInfo = $priceConfigList[$apikey.'_'.$fpid];
            $buyPids = explode(',', $priceInfo['product_ids']);
            if(!in_array($product_id, $buyPids)){

                $this->result['no_price'][$key][] = $date;
                continue;
            }

            if($priceInfo['accord'] == 1){
                $field = 'success_num';
            }else{
                $field = 'valid_num';
            }

            //无账单收入
            if(!isset($billListTmp[$key])){
                //有计费依据调用量 却无收入的是需要预警的；无计费依据调用量 无收入的是正常的不用预警；
                if($item[$field] > 0){

                    $this->result['no_price'][$key][] = $date;
                }
                continue;
            }

            //有计费配置 也有账单 但是收入为0（说明计费价格为0）
            if($billListTmp[$key]['money'] <= 0){
                $this->result['zero_price'][$key][] = $date;
            }

        }

        unset($usageListTmp);
        unset($billListTmp);
        return true;
    }

	/**
	 * 获取发送的内容
	 *
	 * @return int
	 */

	protected  function  sendMail()
    {

        $emails = EmailConfig::getEamilAddressByScene($this->scene);
        //日报发送内容拼接
        $html = $this->createHtml($this->data);

        $mail = new SendMailService();
        $subject = $this->start_date.'-'.$this->end_date.'客户产品有调用量无计费监控';

        if(!empty($emails)){
            foreach ($emails as $v){
                if (!is_null($this->email)){
                    if ($v['email'] != $this->email){
                        continue;
                    }
                }
                //日报 每日发送
                $mail->setFromName('金融后台项目组')
                    ->setAddressee([$v])
                    //->setCC($this->cc)
                    ->setSubject($subject)
                    ->setContent($html)
                    ->sendByAsync();
                    //->send();
            }
        }


    }



    protected function getPriceConfig($date, $apikeys = null, $product_ids = null)
    {
        $result = [];
        $model  = new ConfigPriceCustomer();
        $model  = $model->where('start_date', '<=', $date);
        if (!is_null($apikeys)) {
            $model = $model->whereIn('apikey', $apikeys);
        }
        if (!is_null($product_ids)) {
            $model = $model->whereIn('father_id', $product_ids);
        }

        $model->orderBy('start_date', 'desc')
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($item) use (&$result) {
                $apikey    = $item['apikey'];
                $father_id = $item['father_id'];

                //取出客户apikey对应有那些代调用户,包含客户自己
                $key = $apikey . '_' . $father_id;
                if (!array_key_exists($key, $result)) {
                    $result[$key] = $item->toArray();
                }

            });

        return $result;
    }


    /*
     * 生成邮件发送内容
     */
    private function createHtml($data)
    {
        //$title = "产品有调用量无计费监控";
        $content = '';

$content .= '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
            font-size   : 12px;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<div>';
//<h1>'; $content .= '</h1>';

$html =  $this->createFirstTable($data['no_price']);
$html .= '<br />';
$html .=  $this->createSecondTable($data['zero_price']);

$content .= $html;

$content .= '<br />
</div> 

';

 return $content;

}


    //警报内容
    private function createFirstTable($data)
    {
        $html = '';

        $html .= <<<HTML
        <h2>无计费配置列表</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">客户</th>
        <th align="center">账号</th>
        <th align="center">产品</th>
        <th align="left">日期</th>
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $customer_id=>$item){
            $rowspan = count($item);
            $customer_name = $this->getCustomerName($customer_id);
            $firstRow  = array_shift($item);
            $account_name = $this->getAccountName($firstRow['apikey']);
            $product_name = $this->getProductName($firstRow['product_id']);
            $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center"  text-align:center" align="center" rowspan="{$rowspan}">$customer_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$account_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$product_name</td>
	<td align="left">{$firstRow['date']}</td>
</tr>
HTML;
            foreach ($item as $key => $item2){
                $account_name = $this->getAccountName($item2['apikey']);
                $product_name = $this->getProductName($item2['product_id']);
                $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$account_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$product_name</td>
	<td align="left">{$item2['date']}</td>
</tr>
HTML;
            }

        }


        $html .= '</table>';

        return $html;
    }

    private function createSecondTable($data)
    {
        $html = '';

        $html .= <<<HTML
        <h2>价格为0产品列表</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">客户</th>
        <th align="center">账号</th>
        <th align="center">产品</th>
        <th align="left">日期</th>
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $customer_id=>$item){
            $rowspan = count($item);
            $customer_name = $this->getCustomerName($customer_id);
            $firstRow  = array_shift($item);
            $account_name = $this->getAccountName($firstRow['apikey']);
            $product_name = $this->getProductName($firstRow['product_id']);
            $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center" rowspan="{$rowspan}">$customer_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$account_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$product_name</td>
	<td align="left">{$firstRow['date']}</td>
</tr>
HTML;
            foreach ($item as $key => $item2){
                $account_name = $this->getAccountName($item2['apikey']);
                $product_name = $this->getProductName($item2['product_id']);
                $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$account_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center">$product_name</td>
	<td align="left">{$item2['date']}</td>
</tr>
HTML;
            }

        }


        $html .= '</table>';

        return $html;
    }


    public function getCustomerName($customer_id = ''){
        $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
        return $customer_name;
    }

    public function getProductName($product_id = ''){
        $p_name = RedisCache::instance('productId_productName_mapping')->get($product_id);
        return $p_name;
    }

    public function getProductFatherId($product_id = ''){
        $fpid = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
        if(!$fpid){
            return $product_id;
        }
        return $fpid;
    }

    public function getAccountName($apikey = ''){
        $info = Account::getAccountByApikey($apikey);
        return $info['account_name'] ?? '';
    }



}