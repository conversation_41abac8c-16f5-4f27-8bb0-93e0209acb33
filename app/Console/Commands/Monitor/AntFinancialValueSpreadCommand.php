<?php

namespace App\Console\Commands\Monitor;


use App\Models\ClickHouse\AntCallStatic;
use App\Models\Monitor\ConfigAntFinancialBatch;
use App\Models\Monitor\ConfigProductValue;
use App\Models\MonitorLogProductModel;
use App\Providers\Monitor\ProductStatusService;
use App\Providers\Monitor\ValueSpreadGoalValueService;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * Class AntFinancialValueSpreadCommand 蚂蚁金服跑批去重值分布计算脚本
 * @package App\Console\Commands\Monitor
 */
class AntFinancialValueSpreadCommand extends Command
{
    protected $signature     = "monitor:value_spread_ant_financial
	{--product_ids=281 : 产品ID，多个产品以(,)隔开}
	{--config_id= : 批次配置表，如果存在，则只计算批次的数据}
	";
    protected $apikey;
    protected $product_ids;
    protected $config_id;
    protected $valueSpreadGoadValueService;
    protected $maxId;
    protected $status;
    protected $update_time;
    protected $antCallStaticModel;
    protected $redisHashName = 'antFinancialValueSpreadCache';
    /**
     * @var \Redis
     */
    protected $redis;

    public function __construct()
    {
        parent::__construct();

        $this->valueSpreadGoadValueService = new ValueSpreadGoalValueService();
        $this->antCallStaticModel = new AntCallStatic();
    }

    public function handle()
    {
        ini_set('memory_limit', '100M');
        //校验参数
        if (!$this->checkParams()) {
            return;
        }

        //获取批次配置
        $config = $this->getConfigs();


        if (empty($config)) {
            return;
        }

        //计算每个批次的值分布数据
        array_walk($config, function ($config) {
            $this->runByConfig($config);
        });
    }

    /**
     * 计算每个批次的值分布数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/2 18:32
     *
     * @param $config array 配置信息
     *
     * @return void
     */
    protected function runByConfig($config)
    {
        //对每一个账号、每一个产品计算值分布数据
        foreach ($this->product_ids as $product_id) {
            $this->runByProductIdAndApikey($product_id, $this->apikey, $config);
        }
    }

    /**
     * 计算每一个账号、每一个产品的值分布数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/2 18:48
     *
     * @param $product_id integer 产品ID
     * @param $apikey     string 账号唯一标识
     * @param $config     array 批次配置
     *
     * @return void
     */
    protected function runByProductIdAndApikey($product_id, $apikey, $config)
    {
        //获取去重后的值分布数据
        $result = $this->getNoRepeatInfo($product_id, $apikey, $config);

        //获取不去重的值分布数据
        $this->getInfo($product_id, $apikey, $config, $result['items']);

        //按key正序
        ksort($result['items']);

        //设置缓存数据
        $key         = $config['id'] . '_' . $product_id;
        $update_time = $this->update_time;
        $run_time    = time() - strtotime($update_time);
        $this->redis->hSet($this->redisHashName, $key, json_encode(compact('result', 'update_time', 'run_time'), JSON_UNESCAPED_UNICODE));

        //设置最近更新时间
        ConfigAntFinancialBatch::where('id', $config['id'])
            ->update(['last_update_time' => strtotime($update_time)]);
    }

    /**
     * 获取不去重的数据，并将数据融入到结果集中
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 18:05
     *
     * @param    $product_id integer 产品ID
     * @param    $apikey     string 账号唯一标识
     * @param    $config     array 批次配置
     * @param    $result     array 去重的结果集
     *
     * @return void
     */
    protected function getInfo($product_id, $apikey, $config, &$result)
    {
        $data = $this->antCallStaticModel->getProductAmountGroupChannelNotDistinct($apikey,$product_id,$config['start_time'],$config['end_time']);

        array_walk($data,function ($item) use (&$result){
            $channel_id = $item['channel_id'];
            $count      = $item['count'];
            if (array_key_exists($channel_id, $result)) {
                $result[$channel_id]['total']        = $count;
                $result[$channel_id]['total_repeat'] = bcsub($result[$channel_id]['total'], $result[$channel_id]['total_no_repeat']);
                $result[$channel_id]['repeat_rate']  = $this->calculateRate($result[$channel_id]['total_repeat'], $result[$channel_id]['total']);
            }

            //汇总数据加一次
            $result[0]['total'] = bcadd($result[0]['total'], $count);

        });

        $result[0]['total_repeat'] = bcsub($result[0]['total'], $result[0]['total_no_repeat']);
        $result[0]['repeat_rate']  = $this->calculateRate($result[0]['total_repeat'], $result[0]['total']);
    }

    /**
     * 计算去重后的相关数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/2 18:53
     *
     * @param    $product_id integer 产品ID
     * @param    $apikey     string 账号唯一标识
     * @param    $config     array 批次配置
     *
     * @return array
     */
    protected function getNoRepeatInfo($product_id, $apikey, $config)
    {
        //获取去重的成功调用统计数据
        $result = $this->getNoRepeatSuccessInfo($product_id, $apikey, $config);

        //获取去重的总调用统计数据
        $this->getNoRepeatTotalInfo($product_id, $apikey, $config, $result);

        //获取去重的各个状态的统计数据
        $this->getNoRepeatStatusInfo($product_id, $apikey, $config, $result);

        //获取附加的状态信息
        $statuses = $this->getStatus($result);

        //获取附加的值信息
        $values = $this->getGoalValues($product_id);

        //对去重的数据进行汇总，整理出一个标准的表格头
        $header = $this->getTableHeader($statuses, $values);

        //对去重的数据进行整理，整理为一个二维数组格式
        $items = $this->tidyNoRepeatInfo($statuses, $values, $result);

        return compact('header', 'items');
    }

    /**
     * 整理去重的数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 17:04
     *
     * @param $statuses       array 附加的状态信息
     * @param $values         array 附加的值信息
     * @param $data           array 处理前的数据
     *
     * @return array
     */
    protected function tidyNoRepeatInfo($statuses, $values, $data)
    {
        return array_map(function ($item) use ($statuses, $values) {
            $data = [
                'channel_name'    => $item['channel_name'],
                'total'           => 0,
                'total_no_repeat' => $item['total'],
                'total_repeat'    => 0,
                'repeat_rate'     => '--',
                'success'         => $item['success'],
                'success_rate'    => $item['success_rate'],
                'channel_rate'    => $item['channel_rate'],
                'run_time'        => $item['run_time'],
            ];

            //状态的数据
            foreach ($statuses as $status) {
                $data['status_' . $status] = array_get($item['status'], $status, 0);
            }

            //值的数据
            foreach ($values as $value) {
                $valueItem               = array_get($item['value'], $value, []);
                $data['value_' . $value] = array_get($valueItem, 'rate', '--') . '（' . array_get($valueItem, 'success', 0) . '）';
            }

            return $data;
        }, $data);
    }

    /**
     * 对去重的数据进行汇总，整理出一个标准的表格头
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 17:07
     *
     * @param $statuses       array 附加的状态信息
     * @param $values         array 附加的值信息
     *
     * @return array
     */
    protected function getTableHeader($statuses, $values)
    {
        //汇总表格标题头
        $list = [
            'channel_name'    => '渠道名称',
            'total'           => '总调用量',
            'total_no_repeat' => '去重总量',
            'repeat_rate'     => '数据重复率',
            'success'         => '成功调用量',
            'success_rate'    => '成功率',
            'channel_rate'    => '渠道占比',
            'run_time'        => '平均响应时间',
        ];

        $productStatusService = new ProductStatusService();

        foreach ($statuses as $status) {
            $list['status_' . $status] = $productStatusService->getStatusInfo(210, $status);
        }

        //值分布表格头
        $value_spread = [
            'channel_name' => '渠道名称',
        ];
        foreach ($values as $value) {
            $value_spread['value_' . $value] = $value;
        }

        return compact('list', 'value_spread');
    }

    /**
     * 获取所有的状态数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 17:42
     *
     * @param $data array 去重的未处理的数据
     *
     * @return array
     */
    protected function getStatus($data)
    {
        $result = [];
        array_walk($data, function ($item) use (&$result) {
            foreach (array_keys($item['status']) as $status) {
                $result[] = $status;
            }
        });

        return array_values(array_unique($result));
    }

    /**
     * 获取所有的值数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 17:44
     *
     * @param $product_id integer 产品ID
     *
     * @return array
     */
    protected function getGoalValues($product_id)
    {
        return $this->valueSpreadGoadValueService->getGoalValues($product_id);
    }

    /**
     *  获取去重的各个状态的统计数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 16:26
     *
     * @param $product_id integer 产品ID
     * @param $apikey     string 账号唯一标识
     * @param $config     array 批次配置
     * @param $result     array 将数据导入到的目标数组
     *
     * @return void
     */
    protected function getNoRepeatStatusInfo($product_id, $apikey, $config, &$result)
    {
        //查询所有未成功的入参数据
        $failedInParams = $this->antCallStaticModel->getFailStatusTel($apikey,$product_id,$config['start_time'],$config['end_time']);

//        $failedInParams = array_column($failedInParams, null, 'tel');
//
//        $tel = implode("','",array_values(array_unique(array_column($failedInParams, 'tel'))));
//        $tel = "'".$tel."'";

        //确认这些入参的数据是否存在重试并且成功的数据
//        $successInParams = $this->antCallStaticModel->getSuccessStatusTel($apikey,$product_id,$tel,$config['start_time'],$config['end_time']);
//
//        if (!empty($successInParams)){
//            $successInParams = array_column($successInParams,'channel_id','tel');
//        }
        $successInParams = [];

        //根据key求差集（失败中存在，成功中不存在的数据）
//        $data = array_values(array_diff_key($failedInParams, $successInParams));

        array_walk($failedInParams, function ($item) use (&$result) {
            $status     = $item['status'];
            $channel_id = $item['channel_id'];

            if (!array_key_exists($channel_id, $result)) {
                $result[$channel_id] = $this->getChannelBaseInfo($channel_id);
            }

            if (!array_key_exists($status, $result[$channel_id]['status'])) {
                $result[$channel_id]['status'][$status] = 0;
            }
            $result[$channel_id]['status'][$status]+=$item['count'];

            //汇总数据加一次
            if (!array_key_exists($status, $result[0]['status'])) {
                $result[0]['status'][$status] = 0;
            }
            $result[0]['status'][$status]+=$item['count'];

        });

    }

    /**
     *  获取去重的总调用统计数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 16:26
     *
     * @param $product_id integer 产品ID
     * @param $apikey     string 账号唯一标识
     * @param $config     array 批次配置
     * @param $result     array 将数据导入到的目标数组
     *
     * @return void
     */
    protected function getNoRepeatTotalInfo($product_id, $apikey, $config, &$result)
    {
        $data = $this->antCallStaticModel->getProductAmountGroupChannel($apikey,$product_id,$config['start_time'],$config['end_time']);

        array_walk($data,function ($item) use(&$result){
            $channel_id = array_get($item, 'channel_id', 0);
            if ($channel_id == 0){
                $channel_id = -1;
            }

            //如果渠道不存在
            if (!array_key_exists($channel_id, $result)) {
                $result[$channel_id] = $this->getChannelBaseInfo($channel_id);
            }
            $result[$channel_id]['total']        = $item['count'];
            $result[$channel_id]['success_rate'] = $this->calculateRate($result[$channel_id]['success'], $item['count']);

            //汇总的数据也需要加一次
            $result[0]['total'] = bcadd($result[0]['total'], $item['count']);
        });

        $result[0]['success_rate'] = $this->calculateRate($result[0]['success'], $result[0]['total']);
    }

    /**
     * 获取去重的成功数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 15:43
     *
     * @param $product_id integer 产品ID
     * @param $apikey     string 账号唯一标识
     * @param $config     array 批次配置
     *
     * @return array
     */
    protected function getNoRepeatSuccessInfo($product_id, $apikey, $config)
    {
        //成功总量
        $success = 0;

        //值分布数据
        $result = [];

        $data = $this->antCallStaticModel->getProductAmountGroupData($apikey,$product_id,$config['start_time'],$config['end_time']);

        array_walk($data,function ($item) use(&$result,&$success,$product_id){
            //汇总每个渠道的相关统计
            $count        = array_get($item, 'count', 0);
            $channel_id   = array_get($item, 'channel_id', 0);
            $origin_value = array_get($item, 'value', 0);
            $run_time     = array_get($item, 'run_time', 0);
            $value        = $this->valueSpreadGoadValueService->getGoalValue($origin_value, $product_id);
            $success      = bcadd($success, $count);

            //如果渠道不存在
            if (!array_key_exists($channel_id, $result)) {
                $result[$channel_id] = $this->getChannelBaseInfo($channel_id);
            }

            $result[$channel_id]['success']  = bcadd($result[$channel_id]['success'], $count);
            $result[$channel_id]['run_time'] = bcadd($result[$channel_id]['run_time'], $run_time);

            //处理值分布
            if (!array_key_exists($value, $result[$channel_id]['value'])) {
                $result[$channel_id]['value'][$value] = [
                    'value'   => $value,
                    'success' => 0,
                    'rate'    => '--',
                ];
            }

            $result[$channel_id]['value'][$value]['success'] = bcadd($result[$channel_id]['value'][$value]['success'], $count);

        });

        //计算汇总渠道的成功率数据
        $total     = $this->calculateTotalNoRepeatSuccessInfo($result);
        $result[0] = $total;

        //计算每一个渠道的占比
        return $this->calculateNoRepeatSuccessInfo($result, $success);
    }

    /**
     * 计算汇总渠道的去重成功量数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/4 11:03
     *
     * @return array
     */
    protected function calculateTotalNoRepeatSuccessInfo($items)
    {
        $total = [
            'channel_id'   => 0,
            'channel_name' => '合计',
            'total'        => 0,
            'success'      => 0,
            'success_rate' => '--',
            'channel_rate' => '--',
            'run_time'     => 0,
            'value'        => [],
            'status'       => [],
        ];

        foreach ($items as $item) {
            $total['success']  = bcadd($total['success'], $item['success']);
            $total['run_time'] = bcadd($total['run_time'], $item['run_time']);
        }

        return $total;
    }

    /**
     * 计算每一个渠道的值占比数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 14:14
     *
     * @param    $data    array 每个渠道的数据
     * @param    $success integer 去重后的成功调用量
     *
     * @return array
     */
    protected function calculateNoRepeatSuccessInfo($data, $success)
    {
        return array_map(function ($item) use ($success) {
            $item['channel_rate'] = $this->calculateRate($item['success'], $success);

            $item['value'] = array_map(function ($value) use ($item) {
                $value['rate'] = $this->calculateRate($value['success'], $item['success']);

                return $value;
            }, $item['value']);

            //平均响应时间
            $item['run_time'] = $item['success'] ? bcdiv($item['run_time'], $item['success']) : 0;

            return $item;
        }, $data);
    }

    /**
     * 获取一个渠道的基础数据（用于填充）
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 16:57
     *
     * @param $channel_id integer 渠道ID
     *
     * @return array
     */
    protected function getChannelBaseInfo($channel_id)
    {
        return [
            'channel_id'   => $channel_id,
            'channel_name' => RedisCache::instance('channelId_label_mapping')
                ->get($channel_id) ?: '未定义渠道',
            'total'        => 0,
            'success'      => 0,
            'success_rate' => '--',    //成功率
            'channel_rate' => '--',    //渠道占比
            'run_time'     => 0,
            'value'        => [],
            'status'       => [],
        ];
    }

    /**
     * 获取配置项
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/2 17:37
     *
     * @return array
     */
    protected function getConfigs()
    {
        if ($this->config_id) {
            return ConfigAntFinancialBatch::where('id', $this->config_id)
                ->get()
                ->toArray();
        }

        $time = time();

        return ConfigAntFinancialBatch::where(function ($query) use ($time) {
            $query->where('start_time', '<=', $time)
                ->where('end_time', '>=', $time);
        })
            ->orWhere(function ($query) use ($time) {
                $query->where('last_update_time', '<=', '`end_time`')
                    ->where('end_time', '<=', $time);
            })
            ->get()
            ->toArray();
    }

    /**
     * 校验参数
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/2 17:27
     *
     * @return boolean
     */
    protected function checkParams()
    {
        $this->apikey = config('params.mayi_apikey');
        if (empty($this->apikey)) {
            $this->output->error("请键入APIKEY");

            return false;
        }

        $this->product_ids = explode(',', $this->input->getOption('product_ids'));
        if (empty($this->product_ids)) {
            $this->output->error("请键入产品ID");

            return false;
        }

        $config_id = $this->input->getOption('config_id');
        if ($config_id) {
            $count = ConfigAntFinancialBatch::where('id', $config_id)
                ->count();

            if (!$count) {
                $this->output->error("该批次配置ID不存在");

                return false;
            }

            $this->config_id = $config_id;
        }

        //获取当前时间内最大的记录ID
        $this->maxId = MonitorLogProductModel::max('id');


        $this->update_time = date('Y-m-d H:i:s');

        $this->redis = Redis::connection('default');

        return true;
    }

    /**
     * 获取一个比例
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/12/3 16:33
     *
     * @param $molecule integer 分子
     * @param $denominator integer 分母
     * @param $default   string 默认值（出错的时候的值）
     * @param $precision integer 保留的小数位数
     *
     * @return string
     */
    protected function calculateRate($molecule, $denominator, $default = '--', $precision = 2)
    {
        return $denominator ? round(bcmul(bcdiv($molecule, $denominator, 4 + $precision), 100, 2 + $precision), $precision) . '%' : $default;
    }
}