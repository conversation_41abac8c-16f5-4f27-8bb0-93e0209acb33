<?php


namespace App\Console\Commands;

use App\Exports\StatTmp\Stat101Export;
use App\Exports\StatTmp\Stat104Export;
use App\Exports\StatTmp\Stat105Export;
use App\Exports\StatTmp\Stat210Export;
use App\Exports\StatTmp\Stat401Export;
use App\Exports\StatTmp\Stat601Export;
use App\Exports\StatTmp\StatYanExport;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\Customer;
use App\Models\MongoStatis;
use App\Models\Product;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class GenCrawlerStat extends Command
{
    /** @var string 命令 */
    public $signature = 'gen:stat';

    /** @var string 命令提示 */
    public $description = '生成爬虫相关的统计信息';

    /** @var int 正在操作的产品ID */
    private $product_id;

    public function handle()
    {
        try {
            // 实际执行
            $this->_handleDo();

            $this->output->success("成功生成文件");
        } catch (\Exception $e) {

            $msg = '生成文件失败  ' . $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
            $this->error($msg);
        } catch (\Error $e) {
            $msg = '生成文件失败  ' . $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
            $this->output->error($msg);
        }
    }

    private function _handleDo()
    {
        // 邦信分详单版v1
        $this->_export101();

        // 邦信分详单版v2
        $this->_export105();

        // 邦企查
        $this->_export401();

        // 邦秒配单号版
        $this->_export601();

        // 邦秒配详单版
        $this->_export104();

        // 邦信分快捷版
        $this->_export210();

        // 邦秒验
        $this->_exportYan();
    }

    private function _exportYan()
    {
        $product_name = "邦秒验";
        $this->output->note("开始导出" . $product_name . "信息");

        // 开通了的客户列表
        $list_customers = $this->_getCustomerByProductkey("fbaace1340a8706863ed6ae17560355c");

        // 各个客户的调用量信息
        $list_details = $this->_getDetailsForYan($list_customers);

        // 生成excel
        Excel::store(new StatYanExport($list_details), "stat_yan.xlsx");

        $this->output->success("成功导出" . $product_name . "信息");
    }

    /**
     * 获取开通了特定产品的客户列表
     * @param string $product_key
     * @return array
     */
    private function _getCustomerByProductkey(string $product_key): array
    {
        // 开通了这些产品的账号
        $list_account_ids = $this->_getAccountIdByProductKey($product_key);

        // 开通了这些账号的客户
        return $this->_getCustomerWithAccountId($list_account_ids);
    }

    /**
     * 开通了这些产品的账号
     * @param string $product_key
     * @return array
     */
    private function _getAccountIdByProductKey(string $product_key): array
    {
        $product_id = [
          '$in' =>  $this->getProductIdsByProductKey($product_key)
        ];

        return AccountProduct::getListByCondition(compact('product_id'), ["account_id"])
            ->pluck("account_id")
            ->toArray();
    }

    private function _export210()
    {
        $this->product_id = 210;
        $product_name = "邦信分快捷版";
        $this->output->note("开始导出" . $product_name . "信息");

        // 开通了的客户列表
        $list_customers = $this->_getCustomerWithProduct($this->product_id);

        // 各个客户的调用量信息
        $list_details = $this->_getDetailsFor210($list_customers);

        // 生成excel
        Excel::store(new Stat210Export($list_details), "stat_" . $this->product_id . ".xlsx");

        $this->output->success("成功导出" . $product_name . "信息");
    }

    /**
     * @param string $product_key
     * @return array
     */
    private function getProductIdsByProductKey(string $product_key): array
    {
        return Product::getListByCondition(compact('product_key'), ["product_id"])
            ->pluck("product_id")
            ->toArray();
    }

    /**
     * @param array $list_customers
     * @return array
     */
    private function _getDetailsForYan(array $list_customers): array
    {
        //  产品ID列表
        $list_product_ids = $this->getProductIdsByProductKey("fbaace1340a8706863ed6ae17560355c");

        return array_reduce($list_customers, function ($carry, $item_customer) use($list_product_ids){
            // 客户下面的apieky
            $list_apikeys = $this->_getApikeyListByCustomerId($item_customer['customer_id']);

            // succ = yd_succ + lt_succ + dx_succ

            list($yd_succ, $lt_succ, $dx_succ) = [
                $this->_getFieldNumberForYan($list_apikeys, $list_product_ids, "yd_succ"),
                $this->_getFieldNumberForYan($list_apikeys, $list_product_ids, "lt_succ"),
                $this->_getFieldNumberForYan($list_apikeys, $list_product_ids, "dx_succ"),
            ];

            $item_customer["succ"] = $yd_succ + $lt_succ + $dx_succ;
            $item_customer["time_start"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->whereIn("product_id", $list_product_ids)
                ->min("amount_date");

            $item_customer["time_end"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->whereIn("product_id", $list_product_ids)
                ->max("amount_date");

            // 过滤掉没有统计信息的客户
            if ($item_customer["time_start"]) {
                $carry[] = $item_customer;
            }
            return $carry;
        }, []);
    }

    /**
     * @param array $list_customers
     * @return array
     */
    private function _getDetailsFor210(array $list_customers): array
    {
        return array_reduce($list_customers, function ($carry, $item_customer) {
            // 客户下面的apieky
            $list_apikeys = $this->_getApikeyListByCustomerId($item_customer['customer_id']);

            // yidong_success + yidong_cache + liantong_success + liantong_cache + dianxin_success + dianxin_cache

            list($yidong_success, $yidong_cache, $liantong_success, $liantong_cache, $dianxin_success, $dianxin_cache) = [
                $this->_getFieldNumberFor210($list_apikeys, "yidong_success"),
                $this->_getFieldNumberFor210($list_apikeys, "yidong_cache"),
                $this->_getFieldNumberFor210($list_apikeys, "liantong_success"),
                $this->_getFieldNumberFor210($list_apikeys, "liantong_cache"),
                $this->_getFieldNumberFor210($list_apikeys, "dianxin_success"),
                $this->_getFieldNumberFor210($list_apikeys, "dianxin_cache"),
            ];

            $item_customer["succ"] = $yidong_success + $yidong_cache + $liantong_success + $liantong_cache + $dianxin_success + $dianxin_cache;

            $item_customer["time_start"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->min("amount_date");

            $item_customer["time_end"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->max("amount_date");

            // 过滤掉没有统计信息的客户
            if ($item_customer["time_start"]) {
                $carry[] = $item_customer;
            }
            return $carry;
        }, []);
    }

    /**
     * @param array $list_apikeys
     * @param array $list_product_ids
     * @param string $filed
     * @return int
     */
    private function _getFieldNumberForYan(array $list_apikeys, array $list_product_ids, string $filed): int
    {
        return MongoStatis::whereIn("apikey", $list_apikeys)
            ->whereIn("product_id", $list_product_ids)
            ->sum("stat_data." . $filed);
    }


    /**
     * @param array $list_apikeys
     * @param string $filed
     * @return int
     */
    private function _getFieldNumberFor210(array $list_apikeys, string $filed): int
    {
        return MongoStatis::whereIn("apikey", $list_apikeys)
            ->where(["product_id" => $this->product_id])
            ->sum("stat_data." . $filed);
    }

    private function _export104()
    {
        $this->product_id = 104;
        $product_name = "邦秒配详单版";
        $this->output->note("开始导出" . $product_name . "信息");

        // 开通了的客户列表
        $list_customers = $this->_getCustomerWithProduct($this->product_id);

        // 各个客户的调用量信息
        $list_details = $this->_getDetailsFor104($list_customers);

        // 生成excel
        Excel::store(new Stat104Export($list_details), "stat_" . $this->product_id . ".xlsx");

        $this->output->success("成功导出" . $product_name . "信息");
    }

    /**
     * @param array $list_customers
     * @return array
     */
    private function _getDetailsFor104(array $list_customers): array
    {
        return array_reduce($list_customers, function ($carry, $item_customer) {
            // 客户下面的apieky
            $list_apikeys = $this->_getApikeyListByCustomerId($item_customer['customer_id']);

            $item_customer["succ"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->sum("stat_data.succ");

            $item_customer["time_start"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->min("amount_date");

            $item_customer["time_end"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->max("amount_date");

            // 过滤掉没有统计信息的客户
            if ($item_customer["time_start"]) {
                $carry[] = $item_customer;
            }
            return $carry;
        }, []);
    }

    /**
     *  邦企查
     */
    private function _export601()
    {
        $this->product_id = 601;
        $product_name = "邦秒配单号版";
        $this->output->note("开始导出" . $product_name . "信息");

        // 开通了的客户列表
        $list_customers = $this->_getCustomerWithProduct($this->product_id);

        // 各个客户的调用量信息
        $list_details = $this->_getDetailsFor601($list_customers);

        // 生成excel
        Excel::store(new Stat601Export($list_details), "stat_" . $this->product_id . ".xlsx");

        $this->output->success("成功导出" . $product_name . "信息");
    }

    /**
     * @param array $list_customers
     * @return array
     */
    private function _getDetailsFor601(array $list_customers): array
    {
        return array_reduce($list_customers, function ($carry, $item_customer) {
            // 客户下面的apieky
            $list_apikeys = $this->_getApikeyListByCustomerId($item_customer['customer_id']);

            $item_customer["itag"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->sum("stat_data.itag");

            $item_customer["time_start"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->min("amount_date");

            $item_customer["time_end"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->max("amount_date");

            // 过滤掉没有统计信息的客户
            if ($item_customer["time_start"]) {
                $carry[] = $item_customer;
            }
            return $carry;
        }, []);
    }

    /**
     *  邦企查
     */
    private function _export401()
    {
        $this->product_id = 401;

        $this->output->note("开始邦企查信息");
        // 开通了105的客户列表
        $list_customers = $this->_getCustomerWithProduct($this->product_id);

        // 各个客户的调用量信息
        $list_details = $this->_getDetailsFor401($list_customers);

        // 生成excel
        Excel::store(new Stat401Export($list_details), "stat_" . $this->product_id . ".xlsx");

        $this->output->success("成功导出邦企查信息");
    }

    /**
     * @param array $list_customers
     * @return array
     */
    private function _getDetailsFor401(array $list_customers): array
    {
        return array_reduce($list_customers, function ($carry, $item_customer) {
            // 客户下面的apieky
            $list_apikeys = $this->_getApikeyListByCustomerId($item_customer['customer_id']);

            $item_customer["valid_num"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->sum("stat_data.valid_num");

            $item_customer["valid_name_or_address_num"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->sum("stat_data.valid_name_or_address_num");

            $item_customer["time_start"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->min("amount_date");

            $item_customer["time_end"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->max("amount_date");

            // 过滤掉没有统计信息的客户
            if ($item_customer["time_start"]) {
                $carry[] = $item_customer;
            }
            return $carry;
        }, []);
    }


    /**
     *  邦信分详单版V2
     */
    private function _export105()
    {
        $this->product_id = 105;
        $this->output->note("开始导出邦信分详单版v2信息");
        // 开通了105的客户列表
        $list_customers = $this->_getCustomerWithProduct($this->product_id);

        // 各个客户的调用量信息
        $list_details = $this->_getDetailsFor105($list_customers);

        // 生成excel
        Excel::store(new Stat105Export($list_details), "stat_105.xlsx");

        $this->output->success("成功导出邦信分详单版v2信息");
    }


    /**
     * @param array $list_customers
     * @return array
     */
    private function _getDetailsFor105(array $list_customers): array
    {
        return array_reduce($list_customers, function ($carry, $item_customer) {
            // 客户下面的apieky
            $list_apikeys = $this->_getApikeyListByCustomerId($item_customer['customer_id']);

            $item_customer["succ"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->sum("stat_data.succ");

            $item_customer["time_start"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->min("amount_date");

            $item_customer["time_end"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->max("amount_date");

            // 过滤掉没有统计信息的客户
            if ($item_customer["time_start"]) {
                $carry[] = $item_customer;
            }
            return $carry;
        }, []);
    }

    /**
     *  邦信分详单版V1
     */
    private function _export101()
    {
        $this->product_id = 101;
        $this->output->note("开始导出邦信分详单版v1信息");
        // 开通了101的客户列表
        $list_customers = $this->_getCustomerWithProduct($this->product_id);

        // 各个客户的调用量信息
        $list_details = $this->_getDetailsFor101($list_customers);

        // 生成excel
        Excel::store(new Stat101Export($list_details), "stat_" . $this->product_id . ".xlsx");

        $this->output->success("成功导出邦信分详单版v1信息");
    }

    private function _getDetailsFor101(array $list_customers): array
    {
        return array_reduce($list_customers, function ($carry, $item_customer) {
            // 客户下面的apieky
            $list_apikeys = $this->_getApikeyListByCustomerId($item_customer['customer_id']);

            $item_customer["success"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->sum("stat_data.success");
            $item_customer["time_start"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->min("amount_date");

            $item_customer["time_end"] = MongoStatis::whereIn("apikey", $list_apikeys)
                ->where(["product_id" => $this->product_id])
                ->max("amount_date");

            // 过滤掉没有统计信息的客户
            if ($item_customer["time_start"]) {
                $carry[] = $item_customer;
            }
            return $carry;
        }, []);
    }

    /**
     * @param string $customer_id
     * @return array
     */
    private function _getApikeyListByCustomerId(string $customer_id): array
    {
        // 客户和账号的映射关系
        $list_maaping = $this->_getMappingBetweenApikeyAndCustomer();
        return $list_maaping[$customer_id] ?? [];
    }

    /**
     * 获取开通了特定产品的客户列表
     * @param int $product_id
     * @return array
     */
    private function _getCustomerWithProduct(int $product_id): array
    {
        // 开通了这些产品的账号
        $list_account_ids = $this->_getAccountIdByProduct($product_id);

        // 开通了这些账号的客户
        return $this->_getCustomerWithAccountId($list_account_ids);
    }

    /**
     * 开通了这些账号的客户
     * @param array $list_account_ids
     * @return array
     */
    private function _getCustomerWithAccountId(array $list_account_ids): array
    {
        $list_customer_ids = Account::whereIn("account_id", $list_account_ids)
            ->select("customer_id")
            ->distinct("customer_id")
            ->get()
            ->pluck("customer_id")
            ->toArray();

        return Customer::whereIn("customer_id", $list_customer_ids)
            ->select(["customer_id", "name"])
            ->get()
            ->toArray();
    }

    /**
     * 开通了这些产品的账号
     * @param int $product_id
     * @return array
     */
    private function _getAccountIdByProduct(int $product_id): array
    {
        return AccountProduct::getListByCondition(compact('product_id'), ["account_id"])
            ->pluck("account_id")
            ->toArray();
    }

    /**
     * @return array
     */
    private function _getMappingBetweenApikeyAndCustomer(): array
    {
        static $list_maaping;
        if ($list_maaping) {
            return $list_maaping;
        }
        return $list_maaping = Account::getListByCondition([], ['customer_id', 'apikey'])
            ->reduce(function ($carry, $item) {
                $carry[$item->customer_id][] = $item->apikey;
                return $carry;
            }, []);
    }
}