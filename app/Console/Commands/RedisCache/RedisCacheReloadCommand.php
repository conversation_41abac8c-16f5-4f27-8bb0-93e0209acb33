<?php

namespace App\Console\Commands\RedisCache;

use App\Providers\RedisCache\RedisCache;
use Illuminate\Console\Command;

/**
 * Class RedisCacheReloadCommand 重载缓存数据
 * @package App\Console\Commands\RedisCache
 */
class RedisCacheReloadCommand extends Command
{
	protected $signature = "redis_cache:reload
	{--all : 是否更新全部缓存}
	";
	
	protected $description = "redis_cache是一套缓存数据的标准，此命令用于对所有的缓存进行更新";
	
	public function __construct()
	{
		parent::__construct();
	}
	
	/**
	 * 脚本开始处理
	 *
	 * @access   public
	 * <AUTHOR>
	 * @datetime 2020/12/29 14:14
	 *
	 * @return void
	 */
	public function handle()
	{
		$all = $this->input->getOption('all');
		
		if ($all) {
			$this->reloadAll();
		} else {
			$types = $this->getAllTypes();
			array_unshift($types, 'all');
			$type = $this->choice("请选择重载的缓存", $types);
			if ('all' == $type) {
				$this->reloadAll();
			} else {
				$this->reload($type);
			}
		}
	}
	
	/**
	 * 重载某个缓存
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/29 14:32
	 *
	 * @param $name string 缓存名称
	 *
	 * @return void
	 */
	private function reload($name)
	{
		RedisCache::instance($name)
				  ->reload();
		
		//打印
		$this->output->success("重载成功");
	}
	
	/**
	 * 重载所有缓存
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/29 14:29
	 *
	 * @return void
	 */
	private function reloadAll()
	{
		//所有支持的缓存
		$types = $this->getAllTypes();
		
		
		foreach ($types as $name) {
			RedisCache::instance($name)
					  ->reload();
			
			//打印
			$this->output->success("[{$name}]重载成功");
		}
	}
	
	/**
	 * 获取全部的缓存类型
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/29 14:13
	 *
	 * @return array
	 */
	private function getAllTypes()
	{
		return array_keys(config('redis_cache.driver'));
	}
}