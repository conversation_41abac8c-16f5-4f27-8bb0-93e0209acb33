<?php

namespace App\Console\Commands;

use App\Mail\BxfreturnSendEmail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Models\BxfFieldMonitorModel;
use App\Models\EmailConfig;

class BxfreturnSendEmailCommand extends Command
{
    /**
     * 命令行执行命令
     */
    protected $signature = 'bxf:return-sendemail';
    protected $scene = 'bxf_return_sendemail';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $email_model = new EmailConfig();
        $email_config = $email_model->getRecipientsAndCCByScene($this->scene);
        $to_email = array_column($email_config['recipients'], 'address');
        $cc_email = array_column($email_config['cc'], 'address');

        $cday = date('Y-m-d');
        $c5day = date("Y-m-d",strtotime("-5 day"));

        $info = BxfFieldMonitorModel::where('cday', '>=', $c5day)
            ->where('cday', '<=', $cday)
            ->orderBy('channel', 'desc')
            ->orderBy('cday', 'desc')
            ->get()
            ->toArray();
        array_walk($info, function(&$value, $key){
            $lt_0_pct = sprintf("%.2f",$value['lt_0_pct'] * 100);
            $eq_0_pct = sprintf("%.2f",$value['eq_0_pct'] * 100);
            $gt_0_pct = sprintf("%.2f",$value['gt_0_pct'] * 100);
            $gt_5_pct = sprintf("%.2f",$value['gt_5_pct'] * 100);
            $value['lt_0_pct'] = $lt_0_pct != 0 ? $lt_0_pct.'%' : 0;
            $value['eq_0_pct'] = $eq_0_pct != 0 ? $eq_0_pct.'%' : 0;
            $value['gt_0_pct'] = $gt_0_pct != 0 ? $gt_0_pct.'%' : 0;
            $value['gt_5_pct'] = $gt_5_pct != 0 ? $gt_5_pct.'%' : 0;
        });
        //$to_email = '<EMAIL>';
        //$cc_email = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        Mail::to($to_email)
            ->cc($cc_email)
            ->send(new BxfreturnSendEmail($info));
    }
}
