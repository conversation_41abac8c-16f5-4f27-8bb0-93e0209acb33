<?php


namespace App\Console\Commands\Customer;

use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Product;
use App\Models\Remit;
use Illuminate\Console\Command;
use App\Models\BillCost;
use App\Models\BillProductIncome;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\Account;
use App\Models\BillProductIncomeV2;
use App\Models\CustomerExpend;
use App\Models\ConfigPriceCustomer;
use App\Models\ConfigPriceInterface;
use App\Models\ClickHouse\RequestProductLog;
use App\TraitUpgrade\CurlTrait;

class ChannelIncome extends Command
{
    use CurlTrait;
    protected $signature = 'channel_income
    {--father_id= : 父产品id}
    {--start_date= : 统计日期(格式Y-m-d）默认当前日期}
    {--end_date= : 统计日期(格式Y-m-d）默认当前日期}';

    public $degree = 6;
    public $filePath = '';//存放结果的日志文件
    public $fileDetailPath = '';//存放明细结果的日志文件
    public $start_date;
    public $end_date;
    public $apikeys;
    public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];
    public $filter_apikey = [];
    public $data = [];
    public $customer_product_cost = [];
    public $customer_product_income = [];
    //ck中carrier运营商和mysql中的运营商operator映射关系
    public $ck_carrier_map = [
        0 => 'CMCC',
        1 => 'CUCC',
        2 => 'CTCC',
        3 => 'CMCC',
    ];

    /**
     * @throws \Exception
     */
    public function handle()
    {

        try{

            $params = $this->getParams();
            if($params['father_id'] == 210){
                $this->bmyChannelData($params);
            }

            dd('success');

            $this->getDateRange($params['limit_month'], $params['month']);
            //dd($this->start_date, $this->end_date);
            $list = BillProductIncomeV2::getMonthIncome($this->start_date, $this->end_date, $params);
            $expend_data = $this->getExpend($params);   //特殊消耗
            $cost_data = $this->getCost($params); //成本 普通成本
            $adjust_cost_data = $this->getAdjustCost($params); //客户成本调整
            $channel_adjust_cost_data = $this->getChannelAdjustCost($params);//渠道成本调整
            $fixed_cost_data = $this->getChannelFixedCost($params);//固定费用成本调整

            //dd($this->current_month, $list);

            $customer_month_tmp = [];
            foreach($list as $key => $val){

                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
                if(empty($customer_id) || is_array($customer_id)){
                    dd($val['apikey']."缓存异常");
                }
                $total_money = $this->customer_product_income[$customer_id][$val['product_id']]['income']??0;
                $this->customer_product_income[$customer_id][$val['product_id']]['income'] = bcadd($total_money, $val['s_money'], $this->degree);
                $unique_key = $customer_id.'_'.$val['product_id'];
                if(!in_array($unique_key, $customer_month_tmp)){
                    $customer_month_tmp[] = $unique_key;
                }
            }

            //dd($customer_month_tmp);

            $this->formatIncomeData($expend_data, $customer_month_tmp);
            $this->formatCostData($cost_data, $adjust_cost_data, $channel_adjust_cost_data, $fixed_cost_data, $customer_month_tmp);

            //dd($customer_month_tmp);
            $first_arr = [];
            foreach($customer_month_tmp as $val){
                $item = explode('_', $val);
                $customer_id = $item[0];
                $product_id = $item[1];
                $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
                if($father_id == 0){
                    $father_id = $product_id;
                }

                //判断是否首次全责收入
                $this->findStartMonth();

                $check = $this->checkFirstIncome($customer_id, $product_id);

                $arr = [
                    'customer_id' => $customer_id,
                    'product_id' => $product_id,
                    'father_id' => $father_id,
                ];
                $income = $this->customer_product_income[$customer_id][$product_id]['income']??0;
                $cost = $this->customer_product_cost[$customer_id][$product_id]['cost'] ?? 0;
                $profit = bcsub($income, $cost, $this->degree);
                if($check){
                    $arr['new_income'] = $income;
                    $arr['new_cost'] = $cost;
                    $arr['new_profit'] = $profit;
                    $arr['old_income'] = 0;
                    $arr['old_cost'] = 0;
                    $arr['old_profit'] = 0;
                }else{
                    $arr['new_income'] = 0;
                    $arr['new_cost'] = 0;
                    $arr['new_profit'] = 0;
                    $arr['old_income'] = $income;
                    $arr['old_cost'] = $cost;
                    $arr['old_profit'] = $profit;
                }

                $this->data[] = $arr;

            }

            $this->formatAllData($params);
            dd('ok');

        }catch (\Exception $e){
            dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendCommandExceptionNotice($this, $e);

        }

        $this->output->success("成功执行");

    }

    public function bmyChannelData($params){
        $apikey_success = [];//计费依据成功量的apikey
        $apikey_valid = [];//计费依据查得量的apikey
        if($params['father_id'] == 200){
            $list = $this->getPriceConfig($params);
            $list = array_column($list, 'accord', 'apikey');
            foreach($list as $key => $accord){
                if($accord == 1){
                    $apikey_success[] = $key;
                }else{
                    $apikey_valid[] = $key;
                }
            }
        }

        $channel_success = [];//计费依据成功量的channel
        $channel_valid = [];//计费依据查得量的channel
        if($params['father_id'] == 210){
            //当某个渠道在计费配置没渠道(如之前海南移动没配置计费配置,或start_date日期条件有问题)
            //这种情况下计费依据两个数组都没有,然而ck库里却有这个渠道(比如虽然没有配置计费配置，依然可以调用走该渠道)
            //这样最终这个渠道会在最终结果数组中重复出现
            $list = $this->getChannelPriceConfigs($params);
            $list = array_column($list, 'price', 'interface_id');

            foreach($list as $iid => $price){
                $channel_id = RedisCache::instance('iid_channelId_mapping')->get($iid);

                if(strpos($price,'"price_model":1') !== false || strpos($price,'"price_model":"1"') !== false ){
                    if(!in_array($channel_id, $channel_success)){
                        $channel_success[] = $channel_id;
                    }

                }else{
                    if(!in_array($channel_id, $channel_valid)){
                        //echo $price.PHP_EOL;
                        $channel_valid[] = $channel_id;
                    }

                }
            }

        }

        //dd($channel_valid, $channel_success);

        //dd(array_intersect($apikey_success, $apikey_valid));
        //$url = "https://finance-manage-api.dianhua.cn/statProduct/customer";
        //$url = "http://finance-manage-api-guangli.dianhua.cn/statProduct/customer";
        //$postData = ['start_date' => $this->start_date, 'end_date' => $this->end_date, 'father_id' => $params['father_id'],
          //          'test_data' => '0', 'user_cookie' => '9j7m67s5p98cdgoa5cqb6iqjm3'
          //          ];
        //$result = $this->postRawJson($url, $postData);
        $path = storage_path()."/logs/";
        /*
        $fp = fopen($path."channel.log","r");
        $file_arr = [];
        while(!feof($fp)){
            $row = fgets($fp, 1024);//逐行读取。如果fgets不写length参数，默认是读取1k。
            $row = trim($row);
            if(empty($row)){
                continue;
            }

            //替换中文下的,
            $row = str_replace(array("\r\n", "\r", "\n", " ","　","\t","\n","\r"), ",", $row);
            $arr = explode(',', $row);

            $customer_id = trim($arr[0]);
            $product_name = trim($arr[1]);
            $fee_num = trim($arr[2]);
            $income = trim($arr[3]);
            $file_arr[$customer_id][$product_name] = [
                'fee_num' => $fee_num,
                'income' => $income,
            ];
        }
        fclose($fp);
        */

        $rpModel = new RequestProductLog();
        //$filter_apikey = array_merge($params['filter_apikey'], $apikey_success);
        $filter_apikey = array_merge($params['filter_apikey'], []);

        //先排除计费依据是成功量的
        //$result = $rpModel->getBmyChannelUsage([], $filter_apikey, $params['product_ids'], $this->start_date, $this->end_date, 'valid_num');

        $result = $rpModel->getBxfChannelUsage([], $filter_apikey, $params['product_ids'], $this->start_date, $this->end_date, 'valid_num', $channel_success);

        foreach($result as $item){
            if(intval($item['channel_id']) == 0){
                $channel_name = '未知';
            }else{
                $channel_name = RedisCache::instance('channelId_label_mapping')->get($item['channel_id']);
            }

            file_put_contents($path."channel_res.txt", $channel_name."\t".$item['valid_num']."\t"."valid".PHP_EOL, FILE_APPEND);

        }

        $result = $rpModel->getBxfChannelUsage([], $filter_apikey, $params['product_ids'], $this->start_date, $this->end_date, 'valid_num', $channel_valid);
        foreach($result as $item){
            if(intval($item['channel_id']) == 0){
                $channel_name = '未知';
            }else{
                $channel_name = RedisCache::instance('channelId_label_mapping')->get($item['channel_id']);
            }

            file_put_contents($path."channel_res.txt", $channel_name."\t".$item['valid_num']."\t"."success".PHP_EOL, FILE_APPEND);

        }

        dd('ok');


        //邦秒验
        //运营商为CMCC中国移动时，需特殊处理，ck中carrier=0时都和carrier=3的合并起来后 在与后台mysql中为CMCC的比较
        $tmp = [];
        $channel_arr = [];
        foreach($result as $item){
            //channel_id=0为缓存也算一种渠道
            /*
            if($item['carrier'] == 0){
                $item['carrier'] = 3;
            }
            */

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']);
            $product_name = RedisCache::instance('productId_productName_mapping')->get($item['pids']);
            $num = $tmp[$item['channel_id']][$product_name][$customer_id]['valid_num'] ?? 0;
            $tmp[$item['channel_id']][$product_name][$customer_id]['valid_num'] = $item['valid_num'] + $num;
            $uniquekey = $item['channel_id'].'_'.$product_name.'_'.$customer_id;
            if(!in_array($uniquekey, $channel_arr)){
                $channel_arr[] = $uniquekey;
            }

        }

        $result = [];
        foreach($channel_arr as $unique){
            $channel_info = explode('_', $unique);
            $channel_id = $channel_info[0];
            $product_name = $channel_info[1];
            $customer_id = $channel_info[2];

            $detail = [
                'channel_id' => $channel_id,
                'product_name' => $product_name,
                'customer_id' => $customer_id,
                'valid_num' => $tmp[$channel_id][$product_name][$customer_id]['valid_num'] ?? 0,
            ];

            $result[] = $detail;

        }

        $title = "渠道名"."\t"."产品名"."\t"."产品对应渠道查得量"."\t"."客户id".
            "\t"."客户名"."\t"."产品对应客户收入"."\t"."客户计费量";
        file_put_contents($path."channel_res.txt", $title.PHP_EOL, FILE_APPEND);

        foreach($result as $val){
            if(intval($val['channel_id']) == 0){
                $channel_name = '缓存';
            }else{
                $channel_name = RedisCache::instance('channelId_label_mapping')->get($val['channel_id']);
            }

            if(empty($val['customer_id'])){
                $customer_name = '客户名异常';
            }else{
                $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($val['customer_id']);
            }

            $income = $file_arr[$val['customer_id']][$val['product_name']]['income'] ?? 0;
            $fee_num = $file_arr[$val['customer_id']][$val['product_name']]['fee_num'] ?? 0;
            $output = $channel_name."\t".$val['product_name']."\t".$val['valid_num']."\t".$val['customer_id'].
                "\t".$customer_name."\t".$income."\t".$fee_num;

            file_put_contents($path."channel_res.txt", $output.PHP_EOL, FILE_APPEND);

        }

        dd('ok');


    }

    private function getChannelPriceConfigs($params)
    {
        $result = [];
        ConfigPriceInterface::where(function ($query) {
            //if (!is_null($this->interface_ids)) {
             //   $query->whereIn('interface_id', $this->interface_ids);
            //}
        })
            ->where('start_date', '<=', $this->end_date)
            ->orderBy('start_date', 'desc')
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($item) use (&$result) {
                $interface_id = $item['interface_id'];
                if (!array_key_exists($interface_id, $result)) {
                    $result[$interface_id] = $item->toArray();
                }
            });

        return $result;
    }

    protected function getPriceConfig($params)
    {
        $result = [];
        $model  = new ConfigPriceCustomer();
        $model  = $model->where('start_date', '<=', $this->start_date);
        if (!is_null($this->apikeys)) {
            $model = $model->whereIn('apikey', $this->apikeys);
        }
        if (isset($params['father_id']) && !empty($params['father_id'])) {
            $model = $model->where('father_id', $params['father_id']);
        }

        $model->orderBy('start_date', 'desc')
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($item) use (&$result) {
                $apikey    = $item['apikey'];
                $father_id = $item['father_id'];
                $key       = $apikey . '_' . $father_id;
                if (!array_key_exists($key, $result)) {
                    $result[$key] = $item->toArray();
                }
            });


        return $result;
    }

    public function getParams(){
        $params = [];
        $father_id = $this->input->getOption('father_id') ?: '';
        if(empty($father_id)){
            $this->output->error('father_id不能为空');
            die;
        }
        $params['father_id'] = $father_id;

        $this->start_date = $this->input->getOption('start_date') ?: '';
        $this->end_date = $this->input->getOption('end_date') ?: '';
        if(empty($this->start_date) || empty($this->end_date)){
            $this->output->error('起始日期,截止日期日期不能为空');
            die;
        }
        if(!empty($this->start_date) && empty($this->end_date)){
            $this->output->error('截止日期不能为空');
            die;
        }

        if(!empty($this->end_date) && empty($this->start_date)){
            $this->output->error('起始日期不能为空');
            die;
        }

        if (!preg_match('/^\d{8}$/', $this->start_date) && !empty($this->start_date)) {
            $this->output->error('起始日期格式不正确');
            die;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date) && !empty($this->end_date)) {
            $this->output->error('截止日期格式不正确');
            die;
        }

        $this->filePath = storage_path()."/logs/first_income_customer_".date('Ymd').".log";
        $this->fileDetailPath = storage_path()."/logs/first_income_customer_detail".date('Ymd').".log";
        //dd($filePath);
        if(file_exists($this->filePath)){
            unlink($this->filePath);
        }

        if(file_exists($this->fileDetailPath)){
            unlink($this->fileDetailPath);
        }

        $params['filter_customer'] = $this->filter_customer;
        $this->filter_apikey = $params['filter_apikey'] = Account::getApikeysByCustomerIdsNew($this->filter_customer);
        if(isset($params['father_id']) && !empty($params['father_id'])){
            $params['product_ids'] = array_column(Product::getChildProduct([$params['father_id']]), 'product_id');
        }

        if(isset($params['customer_id']) && !empty($params['customer_id'])){
            $params['apikey'] = Account::getApikeysByCustomerIdsNew([$params['customer_id']]);
        }

        return $params;
    }


    public function getCost($params){

        return BillCost::getCostByDate($this->start_date, $this->end_date, $params);

    }

    public function getAdjustCost($params){

        return CustomerBillAdjust::getCostByDate($this->start_date, $this->end_date, $params);

    }

    public function getChannelAdjustCost($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        return ChannelAccountAdjust::getCostByDate($start_date, $end_date, $params);

    }

    public function getChannelFixedCost($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        return ChannelAccountFixedFee::getCostByDate($start_date, $end_date, $params);
    }

    public function getExpend($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        $expend_data['sub'] = CustomerExpend::getExpendByDate($start_date, $end_date, array_merge($params,['type' => 1]));
        $expend_data['add'] = CustomerExpend::getExpendByDate($start_date, $end_date, array_merge($params,['type' => 2]));

        return $expend_data;
    }

    public function formatAllData($params){
        $arr = [];
        $customer = [];

        array_multisort(array_column($this->data, 'father_id'), SORT_DESC, $this->data);

        foreach($this->data as $val){

            $father_name = RedisCache::instance('productId_productName_mapping')->get($val['father_id']);
            $product_name = RedisCache::instance('productId_productName_mapping')->get($val['product_id']);

            $total_new_income = $arr[$val['customer_id']]['new_income']??0;
            $arr[$val['customer_id']]['new_income'] = bcadd($total_new_income, $val['new_income'], $this->degree);

            $total_new_cost = $arr[$val['customer_id']]['new_cost']??0;
            $arr[$val['customer_id']]['new_cost'] = bcadd($total_new_cost, $val['new_cost'], $this->degree);

            $total_new_profit = $arr[$val['customer_id']]['new_profit']??0;
            $arr[$val['customer_id']]['new_profit'] = bcadd($total_new_profit, $val['new_profit'], $this->degree);

            $total_old_income = $arr[$val['customer_id']]['old_income']??0;
            $arr[$val['customer_id']]['old_income'] = bcadd($total_old_income, $val['old_income'], $this->degree);

            $total_old_cost = $arr[$val['customer_id']]['old_cost']??0;
            $arr[$val['customer_id']]['old_cost'] = bcadd($total_old_cost, $val['old_cost'], $this->degree);

            $total_old_profit = $arr[$val['customer_id']]['old_profit']??0;
            $arr[$val['customer_id']]['old_profit'] = bcadd($total_old_profit, $val['old_profit'], $this->degree);

            $total_income = $arr[$val['customer_id']]['total_income']??0;
            $new_old_income = bcadd($val['new_income'], $val['old_income'], $this->degree);
            $arr[$val['customer_id']]['total_income'] = bcadd($total_income, $new_old_income, $this->degree);

            $total_cost = $arr[$val['customer_id']]['total_cost']??0;
            $new_old_cost = bcadd($val['new_cost'], $val['old_cost'], $this->degree);
            $arr[$val['customer_id']]['total_cost'] = bcadd($total_cost, $new_old_cost, $this->degree);

            if(!in_array($val['customer_id'], $customer)){
                $customer[] = $val['customer_id'];
            }

            $str = $val['customer_id']."\t".$father_name."\t".$product_name."\t".
                $val['new_income']."\t".$val['new_cost']."\t".
                $val['old_income']."\t".$val['old_cost']."\t".
                $val['new_profit']."\t".$val['old_profit'];

            file_put_contents($this->fileDetailPath, $str.PHP_EOL, FILE_APPEND);

        }

        //客户认款单数据
        $remit_list = Remit::getMoneyByDate($this->start_date, $this->end_date, $params);
        foreach($remit_list as $val){
            if(!in_array($val['customer_id'], $customer)){
                $customer[] = $val['customer_id'];
            }
        }
        $remit_list = array_column($remit_list, 's_money', 'customer_id');

        $dept = SystemDept::getAllDeptInfo();
        $dept = array_column($dept, 'dept_name', 'dept_id');
        foreach($customer as $customer_id){
            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
            $customer_info = Customer::getCustomerInfo($customer_id);
            $sys_user = SystemUser::getPhoneByUserName($customer_info['salesman']);
            $area = $dept[$sys_user['dept_id']]??'unknow';
            $daokuan = $remit_list[$customer_id]??0;
            $total_income = $arr[$customer_id]['total_income']??0;
            $total_cost = $arr[$customer_id]['total_cost']??0;
            $new_income = $arr[$customer_id]['new_income']??0;
            $new_cost = $arr[$customer_id]['new_cost']??0;
            $old_income = $arr[$customer_id]['old_income']??0;
            $old_cost = $arr[$customer_id]['old_cost']??0;
            $new_profit = $arr[$customer_id]['new_profit']??0;
            $old_profit = $arr[$customer_id]['old_profit']??0;

            $str = $customer_id."\t".$customer_name."\t".$customer_info['company']."\t".
                $customer_info['salesman']."\t".$area."\t".
                $total_income."\t". $total_cost."\t".
                $new_income."\t". $new_cost."\t".
                $old_income."\t". $old_cost."\t".
                $new_profit."\t". $old_profit."\t".
                $daokuan;

            file_put_contents($this->filePath, $str.PHP_EOL, FILE_APPEND);

        }


    }


    public function formatIncomeData($expend_data, &$customer_month_tmp){
        foreach($expend_data['add'] as $val){
            $total_income = $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] ?? 0;
            $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] = bcadd($total_income, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($expend_data['sub'] as $val){
            $total_income = $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] ?? 0;
            $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] = bcsub($total_income, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        return true;
    }

    public function  formatCostData($cost_data, $adjust_cost_data, $channel_adjust_cost_data, $fixed_cost_data, &$customer_month_tmp){

        foreach($cost_data as $val){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
            $total_cost = $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $customer_id.'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($adjust_cost_data as $val){
            $total_cost = $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($channel_adjust_cost_data as $val){
            $total_cost = $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($fixed_cost_data as $val){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
            $total_cost = $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $customer_id.'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        return true;
    }


    public function getDateRange($limit = 1, $month){
        $this->current_month = str_replace('-', '', $month);//当前月
        $this->end_date = $this->getLastDay($month.'-01');//查看日期所在月的最后一天
        if($limit == 1){//限定月份
            $this->start_date = date('Ymd', strtotime($month.'-01'));
        }else{
            $this->start_date = $this->getMonth(11, $this->current_month).'01';
        }

        return true;
    }

    /**
     * 获取某个日期所在月的最后一天
     * @param $date
     * @return false|string
     */
    public function getLastDay($date)
    {
        $firstday = date('Y-m-01', strtotime($date));
        $lastday = date('Ymd', strtotime("$firstday +1 month -1 day"));
        return $lastday;
    }

    //获取指定日期的前后几个月
    public function getMonth($sign, $month = '')
    {
        //得到系统的年月
        $tmp_date = empty($month) ? date("Ym") : $month;
        //切割出年份
        $tmp_year = substr($tmp_date,0,4);
        //切割出月份
        $tmp_mon = substr($tmp_date,4,2);
        // 得到当前月份的下几月
        $tmp_nextmonth = mktime(0,0,0,$tmp_mon+$sign,1,$tmp_year);
        // 得到当前月份的前几月
        $tmp_forwardmonth = mktime(0,0,0,$tmp_mon-$sign,1,$tmp_year);
        return $fm_next_month = date("Ym",$tmp_forwardmonth);
    }

    public function findStartMonth(){
        //1 先根据当前月份判断
        //如果统计的当前月份比首次开始月份日期都早 都按老项目处理;
        if($this->first_start_month > $this->current_month){
            $this->find_start_month = $this->first_start_month;
            return;
        }

        $before_month = $this->getMonth(11, $this->current_month);

        if($this->first_start_month > $before_month){
            $this->find_start_month = $this->first_start_month;
            return;
        }

        $this->find_start_month = $before_month;

        return;
    }

    public function checkFirstIncome($customer_id, $product_id){
        $customer_apikeys = Account::getApikeysByCustomerIdsNew([$customer_id]);
        //不含边界月
        $firstIncome = BillProductIncome::checkFirstIncome($this->find_start_month.'01', $customer_apikeys, $product_id);
        if($firstIncome == 0){//新项目
            $is_new = true;
        }else{
            //老项目
            $is_new = false;
        }


        return $is_new;
    }


    public function getEveryMonthIncome($customer_id, $father_id, $product_id, $month, &$customer_tmp, &$customer_apikeys, &$first_arr, &$sort_key){
        $first_arr[$sort_key]['customer_id'] = $customer_id;
        $first_arr[$sort_key]['father_id'] = $father_id;
        $first_arr[$sort_key]['product_id'] = $product_id;

        $first_arr[$sort_key]['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
        $first_arr[$sort_key]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
        $first_arr[$sort_key]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($product_id);
        $first_arr[$sort_key]['s_month'] = $month;
        //正常收入
        $income = $customer_tmp[$customer_id][$father_id][$product_id][$month]['total_money'];
        $condition['customer_id'] = $customer_id;
        $condition['father_id'] = $father_id;
        $condition['product_id'] = $product_id;
        $condition['s_date'] = $month.'01';
        $condition['e_date'] = $this->getLastDay($month.'01');
        //特殊消耗
        $incomesub = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 1]));
        $incomeadd = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 2]));
        $incomesub = $incomesub[0]['s_money'] ?? 0;
        $incomeadd = $incomeadd[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['income'] = bcsub(bcadd($income, $incomeadd, $this->degree), $incomesub, $this->degree);

        $condition2['apikey'] = $customer_apikeys;
        $condition2['product_id'] = $product_id;
        $condition2['s_date'] = $condition['s_date'];
        $condition2['e_date'] = $condition['e_date'];
        //普通成本
        $cost = BillCost::getBetweenDate($condition2);
        $cost = $cost[0]['s_money'] ?? 0;

        $condition3['customer_id'] = $customer_id;
        $condition3['product_id'] = $product_id;
        $condition3['s_date'] = $condition['s_date'];
        $condition3['e_date'] = $condition['e_date'];
        //客户成本调整
        $adjust_cost = CustomerBillAdjust::getBetweenDate($condition3);
        $adjust_cost = $adjust_cost[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['cost'] = bcadd($cost, $adjust_cost, $this->degree);

        $customer_info = Customer::getCustomerInfo($customer_id);
        $first_arr[$sort_key]['salesman'] = $customer_info['salesman'];
        $sys_user = SystemUser::getPhoneByUserName($customer_info['salesman']);
        $dept_info = SystemDept::getInfoByDeptId($sys_user['dept_id']);
        $first_arr[$sort_key]['area'] = $dept_info['dept_name'];

        $sort_key++;

        return true;
    }



}