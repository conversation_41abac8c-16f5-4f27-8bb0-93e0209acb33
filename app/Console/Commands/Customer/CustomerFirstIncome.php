<?php


namespace App\Console\Commands\Customer;

use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\Channel;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\ChannelInterface;
use App\Models\Common\CommonEnumModel;
use App\Models\Product;
use App\Models\Remit;
use App\Repositories\CustomerSalesmanHistoryRepository;
use Illuminate\Console\Command;
use App\Models\BillCost;
use App\Models\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\Account;
use App\Models\CustomerExpend;
use App\Models\SalesCommission;
use App\Models\SalesCommissionRunRecord;

/**
 * php artisan customer_first_income --limit_month 1 --mode 1 --month 2023-11
 */
class CustomerFirstIncome extends Command
{
    protected $signature = 'customer_first_income
    {--limit_month= : 是否限定月份（0不限定 、1限定）默认0}
    {--mode= : 运行模式（0读表 用于计划任务执行 、1手动命令）默认0}
    {--month= : 查看日期(格式Y-m）默认当前日期}';

    public $degree = 6;
    public $filePath = '';//存放结果的日志文件
    public $fileDetailPath = '';//存放明细结果的日志文件
    public $start_date;
    public $end_date;
    public $current_month;//当前月
    public $find_start_month;//查找边界月份
    public $first_start_month = 202107;//首次开始比较的日期
    // public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];
    public $filter_customer = [];
    public $filter_apikey = [];
    public $data = [];
    public $customer_product_cost = [];
    public $customer_product_income = [];
    public $mode=0;     // 运行模式 0 计划任务读表 1 兼容手动命令
    public $run_id = ''; // 计划任务运行时任务表的运行id
    // private $skip_interface_id;//邦信分需要过滤运营商成本

    // private $skip_channel_id;//邦信分需要过滤运营商成本

    //客户切换客户id分组 key位置的customer_id 按值中的所有customer_id算新旧提成
    private $switch_customer_map = [];
    /**
     * @var \Laravel\Lumen\Application|mixed
     */
    private $same_product_map;

    // C20250627WTIVPS	FA20250627NA4VCP	d7313adcc2d2789bd9cdcd362f472922
    // C20240426HUZDP7	TA20240426WL9GJQ	bf597a7bda394652e25d8c59427add58
    /** @var array 提成特殊配置 */
    private $ex_cost_config = [
        'C20240426HUZDP7_2025-06_1_41001' => 8781.87,
        'C20250627WTIVPS_2025-06_1_41001' => 2655.32,

        'C20240426HUZDP7_2025-06_1_41002' => 1561.27,
        'C20250627WTIVPS_2025-06_1_41002' => 570.77,
    ];

    /**
     * @throws \Exception
     */
    public function handle()
    {
        try{
            //当根据产品id等参数查询时，到款是不准确的，因为认款表中没有产品维度
            $params = $this->getParams();

            $this->getDateRange($params['limit_month'], $params['month']);
            //
            // $params['customer_id'] = 'C20220701XI0AA5';
            // $params['apikey'] = [
            //     'f5c496c0c68a7730d2fbc380d37fe2c8',
            //     '82cf5b469a682cb327f71fe9080dcfb1',
            //     '4c573202ec031d87e2b26a36332e5eef',
            // ];

            $list = BillProductIncomeV2::getMonthIncomeWithSource($this->start_date, $this->end_date, $params);
            //$list = BillProductIncome::getMonthIncome($this->start_date, $this->end_date, $params);
            $expend_data = $this->getExpend($params);   //特殊消耗
            $cost_data = $this->getCost($params); //成本 普通成本
            $adjust_cost_data = $this->getAdjustCost($params); //客户成本调整
            $channel_adjust_cost_data = $this->getChannelAdjustCost($params);//渠道成本调整
            $fixed_cost_data = $this->getChannelFixedCost($params);//固定费用成本调整

            $customer_month_tmp = [];
            foreach($list as $key => $val){

                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
                if(empty($customer_id) || is_array($customer_id)){
                    dd($val['apikey']."缓存异常");
                }
                $source = $val['source'];
                $total_money = $this->customer_product_income[$customer_id][$val['product_id']][$source]['income']??0;
                $this->customer_product_income[$customer_id][$val['product_id']][$source]['income'] = bcadd($total_money, $val['s_money'], $this->degree);
                $unique_key = $customer_id.'_'.$val['product_id'].'_'.$source;
                if(!in_array($unique_key, $customer_month_tmp)){
                    $customer_month_tmp[] = $unique_key;
                }
            }

            $this->formatIncomeData($expend_data, $customer_month_tmp);
            $this->formatCostData($cost_data, $adjust_cost_data, $channel_adjust_cost_data, $fixed_cost_data, $customer_month_tmp);

            $this->findStartMonth();

            $first_arr = [];
            foreach($customer_month_tmp as $val){
                $item = explode('_', $val);
                $customer_id = $item[0];
                $product_id = $item[1];
                $source = $item[2];
                $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
                if($father_id == 0){
                    $father_id = $product_id;
                }

                //判断是否首次全责收入
                //判断是否新老项目不用来源，只管客户就行
                $customer_ids = [$customer_id];
                //判断是否同一个客户添加了新的客户id
                if(key_exists($customer_id, $this->switch_customer_map)){
                    $customer_ids = $this->switch_customer_map[$customer_id];// switch_customer_map 数组中值包含了所有需要考虑的客户id
                }
                [$check,$earliest_month] = $this->checkFirstIncome($customer_ids, $product_id);

                $arr = [
                    'customer_id' => $customer_id,
                    'product_id' => $product_id,
                    'father_id' => $father_id,
                    'source' => $source,
                    'earlist_month' => $earliest_month,
                ];
                $income = $this->customer_product_income[$customer_id][$product_id][$source]['income']??0;
                $cost = $this->customer_product_cost[$customer_id][$product_id][$source]['cost'] ?? 0;

                $ex_key = $customer_id.'_'.$params['month'].'_'.$source.'_'.$product_id;
                if(isset($this->ex_cost_config[$ex_key])){
                    $cost = $this->ex_cost_config[$ex_key];
                }

                $profit = bcsub($income, $cost, $this->degree);

                if($check){
                    $arr['new_income'] = $income;
                    $arr['new_cost'] = $cost;
                    $arr['new_profit'] = $profit;
                    $arr['old_income'] = 0;
                    $arr['old_cost'] = 0;
                    $arr['old_profit'] = 0;
                }else{
                    $arr['new_income'] = 0;
                    $arr['new_cost'] = 0;
                    $arr['new_profit'] = 0;
                    $arr['old_income'] = $income;
                    $arr['old_cost'] = $cost;
                    $arr['old_profit'] = $profit;
                }

                $this->data[] = $arr;
            }

            $this->formatAllData($params);
            dd('ok');

        }catch (\Exception $e){
            if($this->mode==0){// 更新数据库任务状态
                SalesCommissionRunRecord::where('run_id',$this->run_id)->update(['run_status'=>4,'remarks'=>$e->getMessage()]);
            }
            dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendCommandExceptionNotice($this, $e);
        }

        $this->output->success("成功执行");

    }


    public function getParams(){
        $params = [];
        $this->mode = $this->input->getOption('mode') ?: 0;
        if($this->mode==1){// 手动命令模式
            $params['limit_month'] = $this->input->getOption('limit_month') ?$this->input->getOption('limit_month'): 1;
            if(empty($params['limit_month'])){
                $params['limit_month'] = 1;
            }
            $params['month'] = $this->input->getOption('month') ?: date('Y-m');
        }else{
            $onerecord = SalesCommissionRunRecord::where('run_status',1)
            ->where('is_delete',0)->first();
            if($onerecord){
                $onerecord = $onerecord->toArray();
                $params['limit_month'] = $onerecord['limit_month'];
                $params['month'] = $onerecord['month'];
                $this->run_id = $onerecord['run_id'];
                // 任务状态更新未计算中
                SalesCommissionRunRecord::where('run_id',$onerecord['run_id'])->update(['run_status'=>2,'update_at'=>time()]);
                // 先清除旧数据
                SalesCommission::where('month',$onerecord['month'])->delete();
            }
        }
        if(!isset($params['month'])){
            dd('month not found');
        }
        $this->filePath = storage_path()."/logs/客户提成总表_".$params['month'].".csv";
        $this->fileDetailPath = storage_path()."/logs/客户提成明细_".$params['month'].".csv";
        //dd($filePath);
        if(file_exists($this->filePath)){
            unlink($this->filePath);
        }

        if(file_exists($this->fileDetailPath)){
            unlink($this->fileDetailPath);
        }

        $params['filter_customer'] = $this->filter_customer;
        // $this->filter_apikey = $params['filter_apikey'] = Account::getApikeysByCustomerIdsNew($this->filter_customer);
        $this->filter_apikey = $params['filter_apikey'] = [];// = Account::getApikeysByCustomerIdsNew($this->filter_customer);
        if(!empty($params['father_id'])){
            $params['product_ids'] = array_column(Product::getChildProduct([$params['father_id']]), 'product_id');
        }

        if(!empty($params['customer_id'])){
            $params['apikey'] = Account::getApikeysByCustomerIdsNew([$params['customer_id']]);
        }

        // $_channel_ids = Channel::getOperatorChannelIds();
        // $channel_ids = array_column($_channel_ids,'channel_id');
        // $this->skip_channel_id = $channel_ids;

        // $_interface_id = ChannelInterface::getListByCondition([],'*',$channel_ids)->toArray();

        // $this->skip_interface_id = array_column($_interface_id,'id');

        $this->switch_customer_map = config('customer.switch_customer_map');
        $this->same_product_map    = config('customer.same_product_map');

        return $params;
    }


    public function getCost($params){

        return BillCostV2::getCostByDateWithSourceAndInterfaceId($this->start_date, $this->end_date, $params);
    }

    public function getAdjustCost($params){

        return CustomerBillAdjust::getCostByDateAndInterfaceId($this->start_date, $this->end_date, $params);

    }

    public function getChannelAdjustCost($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        return ChannelAccountAdjust::getCostByDateAndInterface($start_date, $end_date, $params);

    }

    public function getChannelFixedCost($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        return ChannelAccountFixedFee::getCostByDateAndChannelId($start_date, $end_date, $params);
    }

    public function getExpend($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        $expend_data['sub'] = CustomerExpend::getExpendByDateWithSource($start_date, $end_date, array_merge($params,['type' => 1]));
        $expend_data['add'] = CustomerExpend::getExpendByDateWithSource($start_date, $end_date, array_merge($params,['type' => 2]));

        return $expend_data;
    }

    /**
     * @throws \Exception
     */
    public function formatAllData($params){
        $userMap = SystemUser::pluck('realname','username');
        $deptMap = SystemUser::pluck('dept_id','username');

        $cshr = new CustomerSalesmanHistoryRepository();

        $arr = [];
        $customer = [];
        array_multisort(array_column($this->data, 'father_id'), SORT_DESC, $this->data);

        $file_detail = fopen($this->fileDetailPath, "w+");
        $file = fopen($this->filePath, "w+");
        //bom
        fwrite($file_detail, "\xEF\xBB\xBF");
        fwrite($file, "\xEF\xBB\xBF");
        $header = '客户id,客户名称,客户公司,商务,大区,收入,成本,新收入,新成本,老收入,老成本,新毛利,老毛利,到款,来源';
        $headerDetail = '客户id,客户名称,父产品,子产品,新收入,新成本,老收入,老成本,新毛利,老毛利,来源,新收入开始月份';
        fputcsv($file, explode(",",$header));
        fputcsv($file_detail, explode(",",$headerDetail));
        $source_map = CommonEnumModel::getTypeMaps('1');
        $source_map[99] = '测试';
        $source_map[100] = '测试';

        foreach($this->data as $val){

            $father_name = RedisCache::instance('productId_productName_mapping')->get($val['father_id']);
            $product_name = RedisCache::instance('productId_productName_mapping')->get($val['product_id']);
            $source = $source_map[$val['source']];

            $total_new_income = $arr[$val['customer_id']][$source]['new_income']??0;
            $arr[$val['customer_id']][$source]['new_income'] = bcadd($total_new_income, $val['new_income'], $this->degree);

            $total_new_cost = $arr[$val['customer_id']][$source]['new_cost']??0;
            $arr[$val['customer_id']][$source]['new_cost'] = bcadd($total_new_cost, $val['new_cost'], $this->degree);

            $total_new_profit = $arr[$val['customer_id']][$source]['new_profit']??0;
            $arr[$val['customer_id']][$source]['new_profit'] = bcadd($total_new_profit, $val['new_profit'], $this->degree);

            $total_old_income = $arr[$val['customer_id']][$source]['old_income']??0;
            $arr[$val['customer_id']][$source]['old_income'] = bcadd($total_old_income, $val['old_income'], $this->degree);

            $total_old_cost = $arr[$val['customer_id']][$source]['old_cost']??0;
            $arr[$val['customer_id']][$source]['old_cost'] = bcadd($total_old_cost, $val['old_cost'], $this->degree);

            $total_old_profit = $arr[$val['customer_id']][$source]['old_profit']??0;
            $arr[$val['customer_id']][$source]['old_profit'] = bcadd($total_old_profit, $val['old_profit'], $this->degree);

            $total_income = $arr[$val['customer_id']][$source]['total_income']??0;
            $new_old_income = bcadd($val['new_income'], $val['old_income'], $this->degree);
            $arr[$val['customer_id']][$source]['total_income'] = bcadd($total_income, $new_old_income, $this->degree);

            $total_cost = $arr[$val['customer_id']][$source]['total_cost']??0;
            $new_old_cost = bcadd($val['new_cost'], $val['old_cost'], $this->degree);
            $arr[$val['customer_id']][$source]['total_cost'] = bcadd($total_cost, $new_old_cost, $this->degree);

            if(!in_array($val['customer_id'].'_'.$source, $customer)){
                $customer[] = $val['customer_id'].'_'.$source;
            }

            $str = [
                $val['customer_id'],
                RedisCache::instance('customerId_customerName_mapping')->get($val['customer_id']),
                $father_name,
                $product_name,
                $val['new_income'],
                $val['new_cost'],
                $val['old_income'],
                $val['old_cost'],
                $val['new_profit'],
                $val['old_profit'],
                $source,
                $val['earlist_month'],
            ];

            if($this->mode==0){ // 计划任务写表
                $val['run_id'] = $this->run_id;
                $val['month'] = $params['month'];
                SalesCommission::insert($val);
            }
            fputcsv($file_detail, $str);
        }

        //客户认款单数据
        $remit_list = Remit::getMoneyByDateWithSource($this->start_date, $this->end_date, $params);
        $remit_list_money = [];
        foreach($remit_list as $val){
            $source = $source_map[$val['source']];
            $unique_key = $val['customer_id'].'_'.$source;
            if(!in_array($unique_key, $customer)){
                $customer[] = $unique_key;
            }

            $remit_list_money[$unique_key] = $val['s_money'];
        }

        $dept = SystemDept::getAllDeptInfo();
        $dept = array_column($dept, 'dept_name', 'dept_id');
        $customerList = Customer::select()->get()->toArray();
        $salesMap = array_column($customerList, 'salesman', 'customer_id');
        $companyMap = array_column($customerList, 'company', 'customer_id');

        foreach($customer as $info){
            $detail = explode('_', $info);
            $customer_id = $detail[0];

            //获取当月销售
            $cshr_month = $cshr->getListMonthly([$customer_id],$params['month'],$params['month']);//Y-m
            $salesman = $cshr_month[$customer_id][$params['month']] ?? '未配置商务';
            // $salesman = isset($salesMap[$customer_id])?$salesMap[$customer_id]:'admin';

            $source = $detail[1];
            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
            $sys_user = isset($userMap[$salesman])?$userMap[$salesman]:'admin';
            $dept_id = isset($deptMap[$salesman])?$deptMap[$salesman]:'unknow';
            $area = isset($dept[$dept_id])?$dept[$dept_id]:'unknow';
            $daokuan = isset($remit_list_money[$customer_id.'_'.$source])?$remit_list_money[$customer_id.'_'.$source]:0;
            $total_income = $arr[$customer_id][$source]['total_income']??0;
            $total_cost = $arr[$customer_id][$source]['total_cost']??0;
            $new_income = $arr[$customer_id][$source]['new_income']??0;
            $new_cost = $arr[$customer_id][$source]['new_cost']??0;
            $old_income = $arr[$customer_id][$source]['old_income']??0;
            $old_cost = $arr[$customer_id][$source]['old_cost']??0;
            $new_profit = $arr[$customer_id][$source]['new_profit']??0;
            $old_profit = $arr[$customer_id][$source]['old_profit']??0;
            $company = isset($companyMap[$customer_id])?$companyMap[$customer_id]:'unknow';

            // 英文公司名称可能有英文逗号，所以加一个双引号
            $str = [
                $customer_id,
                $customer_name,
                $company,
                $sys_user,
                $area,
                $total_income,
                $total_cost,
                $new_income,
                $new_cost,
                $old_income,
                $old_cost,
                $new_profit,
                $old_profit,
                $daokuan,
                $source
            ];
            fputcsv($file, $str);
        }
        fclose($file);
        fclose($file_detail);
        if($this->mode==0){ // 计划任务 更新为完成计算
            SalesCommissionRunRecord::where('run_id',$this->run_id)->update(['run_status'=>3,'update_at'=>time()]);
        }
        // 修改csv文件权限
        chmod($this->filePath,'0644');
        chmod($this->fileDetailPath,'0644');
    }


    public function formatIncomeData($expend_data, &$customer_month_tmp){
        foreach($expend_data['add'] as $val){
            $source = $val['source'];
            $total_income = $this->customer_product_income[$val['customer_id']][$val['product_id']][$source]['income'] ?? 0;
            $this->customer_product_income[$val['customer_id']][$val['product_id']][$source]['income'] = bcadd($total_income, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'].'_'.$source;
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($expend_data['sub'] as $val){
            $source = $val['source'];
            $total_income = $this->customer_product_income[$val['customer_id']][$val['product_id']][$source]['income'] ?? 0;
            $this->customer_product_income[$val['customer_id']][$val['product_id']][$source]['income'] = bcsub($total_income, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'].'_'.$source;
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        return true;
    }

    public function  formatCostData($cost_data, $adjust_cost_data, $channel_adjust_cost_data, $fixed_cost_data, &$customer_month_tmp){
        foreach($cost_data as $val){
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($val['product_id']);
            // if(in_array($val['interface_id'],$this->skip_interface_id)){//邦信分 不计算运营商成本
            //     continue;
            // }
            // if(in_array($father_id,[210,1000]) && in_array($val['interface_id'],$this->skip_interface_id)){//邦信分 不计算运营商成本
            //     continue;
            // }
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
            $total_cost = $this->customer_product_cost[$customer_id][$val['product_id']][$val['source']]['cost'] ?? 0;
            $this->customer_product_cost[$customer_id][$val['product_id']][$val['source']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $customer_id.'_'.$val['product_id'].'_'.$val['source'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        $source = 0;
        foreach($adjust_cost_data as $val){
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($val['product_id']);
            // if(in_array($val['interface_id'],$this->skip_interface_id)){//邦信分 不计算运营商成本
            //     continue;
            // }
            // if(in_array($father_id,[210,1000]) && in_array($val['interface_id'],$this->skip_interface_id)){//邦信分 不计算运营商成本
            //     continue;
            // }
            $total_cost = $this->customer_product_cost[$val['customer_id']][$val['product_id']][$val['source']]['cost'] ?? 0;
            $this->customer_product_cost[$val['customer_id']][$val['product_id']][$val['source']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'].'_'.$val['source'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($channel_adjust_cost_data as $val){
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($val['product_id']);
            // if(in_array($val['interface_id'],$this->skip_interface_id)){//邦信分 不计算运营商成本
            //     continue;
            // }
            // if(in_array($father_id,[210,1000]) && in_array($val['interface_id'],$this->skip_interface_id)){//邦信分 不计算运营商成本
            //     continue;
            // }
            $total_cost = $this->customer_product_cost[$val['customer_id']][$val['product_id']][$val['source']]['cost'] ?? 0;
            $this->customer_product_cost[$val['customer_id']][$val['product_id']][$val['source']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'].'_'.$val['source'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($fixed_cost_data as $val){
            // if(in_array($father_id,[210,1000]) && in_array($val['channel_id'],$this->skip_channel_id)){//邦信分 不计算运营商成本
            //     continue;
            // }
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
            $total_cost = $this->customer_product_cost[$customer_id][$val['product_id']][$val['source']]['cost'] ?? 0;
            $this->customer_product_cost[$customer_id][$val['product_id']][$val['source']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $customer_id.'_'.$val['product_id'].'_'.$val['source'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }
    }


    public function getDateRange($limit = 1, $month){
        $this->current_month = str_replace('-', '', $month);//当前月
        $this->end_date = $this->getLastDay($month.'-01');//查看日期所在月的最后一天
        if($limit == 1){//限定月份
            $this->start_date = date('Ymd', strtotime($month.'-01'));
        }else{
            $this->start_date = $this->getMonth(11, $this->current_month).'01';
        }
        if($this->mode==0){
            SalesCommissionRunRecord::where('run_id',$this->run_id)->update([
                    'start_date'=>$this->start_date,
                    'end_date'=>$this->end_date,
                    'update_at'=>time(),
                ]);
        }
        return true;
    }

    /**
     * 获取某个日期所在月的最后一天
     * @param $date
     * @return false|string
     */
    public function getLastDay($date)
    {
        $firstday = date('Y-m-01', strtotime($date));
        $lastday = date('Ymd', strtotime("$firstday +1 month -1 day"));
        return $lastday;
    }

    //获取指定日期的前后几个月
    public function getMonth($sign, $month = '')
    {
        //得到系统的年月
        $tmp_date = empty($month) ? date("Ym") : $month;
        //切割出年份
        $tmp_year = substr($tmp_date,0,4);
        //切割出月份
        $tmp_mon = substr($tmp_date,4,2);
        // 得到当前月份的下几月
        $tmp_nextmonth = mktime(0,0,0,$tmp_mon+$sign,1,$tmp_year);
        // 得到当前月份的前几月
        $tmp_forwardmonth = mktime(0,0,0,$tmp_mon-$sign,1,$tmp_year);
        return $fm_next_month = date("Ym",$tmp_forwardmonth);
    }

    public function findStartMonth(){
        //1 先根据当前月份判断
        //如果统计的当前月份比首次开始月份日期都早 都按老项目处理;
        if($this->first_start_month > $this->current_month){
            $this->find_start_month = $this->first_start_month;
            return;
        }

        $before_month = $this->getMonth(11, $this->current_month);

        if($this->first_start_month > $before_month){
            $this->find_start_month = $this->first_start_month;
            return;
        }

        $this->find_start_month = $before_month;

        return;
    }

    private function checkFirstIncome($customer_ids, $product_id){
        //得物 手机号实时在网状态(216)与手机号在网状态(203)保持一致
        if(isset($this->same_product_map[$customer_ids[0]][$product_id])){
            $product_id = $this->same_product_map[$customer_ids[0]][$product_id];
        }

        $customer_apikeys = Account::getApikeysByCustomerIdsNew($customer_ids);
        //不含边界月
        // $firstIncome = BillProductIncomeV2::checkFirstIncome($this->find_start_month.'01', $customer_apikeys, $product_id);
        $bill_earliest = BillProductIncomeV2::getEarliestIncome($customer_apikeys, $product_id);
        $bill_earliest = $bill_earliest == '' ? '' : date('Ym', strtotime($bill_earliest));

        // bill_product_income_v2 表中没有 在customer_expend 中获取
        $expend_earliest = CustomerExpend::getEarliestIncome($customer_ids, $product_id);
        $expend_earliest = $expend_earliest == '' ? '' : date('Ym', strtotime($expend_earliest));

        if ($bill_earliest == '' && $expend_earliest != '') {
            $earliest_income_date = $expend_earliest;
        } elseif ($bill_earliest != '' && $expend_earliest == '') {
            $earliest_income_date = $bill_earliest;
        } elseif ($bill_earliest == '' && $expend_earliest == '') {
            $earliest_income_date = '';
        } else {
            $earliest_income_date = min($bill_earliest, $expend_earliest);
        }

        //一年之内是新
        $is_new = $earliest_income_date > $this->find_start_month;

        return [$is_new,$earliest_income_date];
    }


    public function getEveryMonthIncome($customer_id, $father_id, $product_id, $month, &$customer_tmp, &$customer_apikeys, &$first_arr, &$sort_key){
        $first_arr[$sort_key]['customer_id'] = $customer_id;
        $first_arr[$sort_key]['father_id'] = $father_id;
        $first_arr[$sort_key]['product_id'] = $product_id;

        $first_arr[$sort_key]['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
        $first_arr[$sort_key]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
        $first_arr[$sort_key]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($product_id);
        $first_arr[$sort_key]['s_month'] = $month;
        //正常收入
        $income = $customer_tmp[$customer_id][$father_id][$product_id][$month]['total_money'];
        $condition['customer_id'] = $customer_id;
        $condition['father_id'] = $father_id;
        $condition['product_id'] = $product_id;
        $condition['s_date'] = $month.'01';
        $condition['e_date'] = $this->getLastDay($month.'01');
        //特殊消耗
        $incomesub = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 1]));
        $incomeadd = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 2]));
        $incomesub = $incomesub[0]['s_money'] ?? 0;
        $incomeadd = $incomeadd[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['income'] = bcsub(bcadd($income, $incomeadd, $this->degree), $incomesub, $this->degree);

        $condition2['apikey'] = $customer_apikeys;
        $condition2['product_id'] = $product_id;
        $condition2['s_date'] = $condition['s_date'];
        $condition2['e_date'] = $condition['e_date'];
        //普通成本
        $cost = BillCost::getBetweenDate($condition2);
        $cost = $cost[0]['s_money'] ?? 0;

        $condition3['customer_id'] = $customer_id;
        $condition3['product_id'] = $product_id;
        $condition3['s_date'] = $condition['s_date'];
        $condition3['e_date'] = $condition['e_date'];
        //客户成本调整
        $adjust_cost = CustomerBillAdjust::getBetweenDate($condition3);
        $adjust_cost = $adjust_cost[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['cost'] = bcadd($cost, $adjust_cost, $this->degree);

        $customer_info = Customer::getCustomerInfo($customer_id);
        $first_arr[$sort_key]['salesman'] = $customer_info['salesman'];
        $sys_user = SystemUser::getPhoneByUserName($customer_info['salesman']);
        $dept_info = SystemDept::getInfoByDeptId($sys_user['dept_id']);
        $first_arr[$sort_key]['area'] = $dept_info['dept_name'];

        $sort_key++;

        return true;
    }
}