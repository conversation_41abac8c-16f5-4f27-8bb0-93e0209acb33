<?php

namespace App\Console\Commands\Customer;

use Illuminate\Console\Command;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\Product;
use App\Models\Customer;
use App\Providers\Tool\SendMailService;
use App\Providers\Auth\DataAuth;

class CustomerExpireAlarm extends Command
{
    protected $signature = "customer:exprie_alarm
    {--limit_date= : 日期, 格式Ymd，默认3天前}
    {--username= : 收件人姓名 如wei.xiu 仅供测试使用}
    ";

    protected $description = '客户账号到期预警';

    /**
    * @var array 邮件抄送人
    */
    protected $cc = [
        // [
        //    'name'  => '修伟',
        //    'email' => 'wei.xiuyulore.com',
        // ],
    ];

    //状态映射
    protected $contract_status_mapping = [
        1 => '已签约已付款',
        2 => '已签约未付款',
        3 => '未签约',
        4 => '特殊客户',
        5 => '其他',
    ];


    protected $tableHeader = [
        'customer_id'     => '客户ID',
        'customer_name'   => '客户名称',
        'account_name'    => '账号名称',
        'product_name'    => '产品名称',
        'contract_status' => '签约状态',
        'end_time'        => '到期时间',
    ];

    //邮件通知列表
    protected $address = [
        'lili.liu',
        'ren.zhang',
        // 'lei.huang',
        'xiaolan.xie',
        'yanjun.shi',
        // 'chang.liu',
        // 'yanming.li',
        // 'wei.xiu',
    ];

    //剩余期限提醒天数
    protected $expire_limit_days = [
        'sign'     => [7,15,30,60],
        'not_sign' => [3,7],
    ];

    protected $sign_text_mapping = [
        'sign'     => '已签约客户账号',
        'not_sign' => '未签约以及其他客户账号',
    ];

    //指定剩余期限
    protected $limit_date = 0;
    protected $username   = '';//指定收件人

    public function handle(){
        try {
            //设置参数
            $this->setParams();
            if($this->limit_date != 0){//如果传入时间则只取该时间
                $tmp_days = strtotime($this->limit_date) - time();
                $tmp_days = intval(($tmp_days - $tmp_days % 86400) / 86400) + 1;
                $this->expire_limit_days = [
                    'sign'     => [$tmp_days],
                    'not_sign' => [$tmp_days],
                ];
            }

            //发送邮件
            foreach($this->expire_limit_days as $tag => $days){
                foreach($days as $day){
                    $this->send($tag,$day);
                }
            }
        } catch (\Exception $exception) {
            sendCommandExceptionNotice($this, $exception);
        }
    }

    /**
     * 获取脚本传入参数
     * 默认情况下不传入参数
     * @return void
     * @throws \Exception
     */
    private function setParams(){
        //设置所需要的日期
        $limit_date = $this->input->getOption('limit_date') ? $this->input->getOption('limit_date') : 0;
        if ($limit_date != 0 && !preg_match('/^\d{8}$/', $limit_date)) {
            $this->output->error("limit_date 日期格式不正确");
            die;
        }
        $this->limit_date = $limit_date == 0 ? 0 : $limit_date;
        $this->username = $this->input->getOption('username') ? $this->input->getOption('username') : '';
    }

    /**
     * 获取查询条件
     * 已签约 未签约及其他
     * @param $para
     * @return array
     * @throws \Exception
     */
    public function getCustomerExpireTimes($tag,$days){
        //已签约7天以内 未签约3天内的每天都提醒
        if (in_array($days, [3, 7])) {
            $end = strtotime(date('Y-m-d 23:59:59', strtotime('+' . $days . ' days')));
            $start = (($tag == 'sign' && $days == 7) || $days == 3) ? strtotime(date('Y-m-d', time())) : ($end - 86399);
            $param['start_end_time'] = $start;
            $param['end_end_time'] = $end;
            return $param;
        }
        //7天以上的只查询 days天后的那一天是否过期
        $time = strtotime(date('Y-m-d', strtotime('+' . $days . ' days')));
        $param['start_end_time'] = $time;
        $param['end_end_time'] = $time + 86399;
        return $param;
    }

    public function getData($tag,$end_days){
        $list = [];
        // 即将过期账号列表
        $times = $this->getCustomerExpireTimes($tag,$end_days);

        $account_product = [];
        $fields = ['account_id', 'product_id', 'contract_status', 'end_time'];
        \DB::connection()->enableQueryLog();

        if($tag == 'sign'){//已签约
            $account_product = AccountProduct::getSingList($fields,$times['start_end_time'],$times['end_end_time']);
        }else{//未签约
            $account_product = AccountProduct::getNotSingList($fields,$times['start_end_time'],$times['end_end_time']);
        }

        if(empty($account_product)){
            return $list;
        }

        $account_ids = array_unique(array_column($account_product,'account_id'));

        $account_infos = Account::select('account_id','account_name', 'customer_id')->whereIn('account_id',$account_ids)->where('status',1)->get()->toArray();
        if(empty($account_infos)){
            return $list;
        }

        $account_list = array_column($account_infos, null, 'account_id');
        $customer_ids = array_column($account_list, 'customer_id');

        $customer_infos = Customer::select('customer_id','salesman','name')->whereIn('customer_id',$customer_ids)->where('status',1)->get()->toArray();
        if(empty($customer_infos)){
            return $list;
        }
        $customer_list   = array_column($customer_infos, null, 'customer_id');

        $product_list = Product::select('product_id','product_name')->get()->toArray();
        $product_list = array_column($product_list, null, 'product_id');
        $contract_status = $this->contract_status_mapping;
        array_map(function ($v) use (&$list, $account_list, $customer_list, $product_list, $contract_status) {
            if (isset($product_list[$v['product_id']]) && isset($account_list[$v['account_id']]) && isset($customer_list[$account_list[$v['account_id']]['customer_id']])) {
                $temp['customer_id']   = $account_list[$v['account_id']]['customer_id'];
                $temp['customer_name'] = $customer_list[$temp['customer_id']]['name'];
                $temp['account_name']  = $account_list[$v['account_id']]['account_name'];
                $temp['product_name']  = $product_list[$v['product_id']]['product_name'];
                $temp['salesman']      = isset($customer_list[$temp['customer_id']]['salesman'])?$customer_list[$temp['customer_id']]['salesman']:'';
                $v['contract_status']  = $contract_status[$v['contract_status']];
                $v['end_time']         = date('Y-m-d', $v['end_time']);
                $temp                  = array_merge($v, $temp);
                $list[] = $temp;
            }
        }, $account_product);
        return $list;
    }

    /**
     * 发送邮件方法
     * @return void
     */
    private function send($tag,$days){
        $data = $this->getData($tag,$days);
        if(empty($data)){
            return;
        }

        //根据签约状态和时间调整标题 已签约7天以内 未签约3天以内
        //以下是已签约客户账号产品到期7天以内的帐号信息，请查阅
        //以下是未签约及其他客户账号产品到期7天的帐号信息，请查阅
        $title = '以下是'.$this->sign_text_mapping[$tag].'产品到期' . $days . ((($tag == 'sign' && $days == 7) || $days == 3)?'天以内':'天') .'的帐号信息，请（';
        $suff = '）查阅';
        //发送到运营
        foreach($this->address as $name) {
            if(!empty($this->username)){
                $name = $this->username;
            }
            $dataAuthService = new DataAuth($name);
            //创建HTML
            $html = $this->createHtml($title.$dataAuthService->getRealName().$suff, $data);

            //发送邮件
            $mail = new SendMailService();
            $mail->setFromName('金融项目预警')
                 ->setAddressee([['email' => $name . '@yulore.com', 'name' => $dataAuthService->getRealName()]])
                 ->setCC($this->cc)
                 ->setSubject($title.$dataAuthService->getRealName().$suff)
                 ->setContent($html)
                 ->sendByAsync();

            $this->output->success("[{$name}] 邮件发送成功");
        }

        $infos = [];
        foreach($data as $info){
            if(!empty($info['salesman'])) {
                $infos[$info['salesman']][] = $info;
            }
        }

        foreach ($infos as $salesman => $list){
            if(!empty($this->username)){
                $salesman = $this->username;
            }
            $dataAuthService = new DataAuth($salesman);
            //创建HTML
            $html = $this->createHtml($title.$dataAuthService->getRealName().$suff,$list);
            //发送邮件
            $mail = new SendMailService();
            $mail->setFromName('金融项目预警')
                ->setAddressee([['email' => $salesman . '@yulore.com', 'name' => $dataAuthService->getRealName()]])
                ->setCC($this->cc)
                ->setSubject($title.$dataAuthService->getRealName().$suff)
                ->setContent($html)
                ->sendByAsync();

            $this->output->success("[{$salesman}] 邮件发送成功");
        }
    }

    private function createHtml($title,$list){
        $html = <<<HTML
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>{$title}</title>
        <style>
            .title{
                font-weight: bold;
                font-size:2em;
                text-align:center;
            }

            th {
                font-weight: bold;
            }

            div {
                width: 100%; font-family: Arial, Helvetica, sans-serif; font-size: 14px;
            }

            #tableArea .dataTables_wrapper {
                position: relative;
                clear: both;
                zoom: 1;
                overflow-x: auto;
            }

            #tableArea table{
                width: 1000px;
                table-layout: fixed;
                margin: 0 auto;
            }

            table,
            td,
            th {
              text-align: center;
              border: 1px solid #000;
              border-collapse: collapse;
            }

            td {
               word-wrap: break-word;
            }

           .error_show {
                color: red;
                font-weight: bold;
                font-size: large;
           }
        </style>
    </head>
    <body>
        <div id="tableArea">
            <div class="title">
                <p>{$title}</p>
            </div>
            <div style="display:flex;justify-content:center;">
                <table>
                    <tr>
HTML;
        foreach ($this->tableHeader as $key => $value) {
            $html .= '<th>' . $value . '</th>';
        }

        $html .= '</tr>';
        foreach ($list as $key => $value) {
            $html .= '<tr>';
            foreach ($this->tableHeader as $kk => $vv) {
                $class_name = '';
                if ($kk == 'end_time') {
                    $class_name = 'class="error_show"';
                }
                $html .= '<td ' . $class_name . '>' . $value[$kk] . '</td>';
            }
            $html .= '</tr>';
        }

    $html .=<<<HTML
                </table>
            </div>
        </div>
    </body>
</html>
HTML;
        return $html;
    }
}
