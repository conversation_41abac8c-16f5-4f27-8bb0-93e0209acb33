<?php

namespace App\Console\Commands\Customer;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\Crs\SystemUser;
use App\Models\Customer;
use App\Models\CustomerDuser;
use App\Models\DingdingCustomerApprovalProcess;
use App\Models\StatisticsCustomerUsage;
use App\TraitUpgrade\CurlTrait;
use App\Utils\AliCall;
use App\Utils\DingDing;
use Illuminate\Console\Command;

/**
 * Class CustomerProductLimitWarning 客户产品限量预警
 * @package App\Console\Commands\Customer
 */

class CustomerProductLimitWarning extends Command
{
    use CurlTrait;
    protected $signature = "customer_product_limit_warning";

    protected $limit_account;
    protected $limit_account_ids;
    protected $apikey_product;
    protected $dingding;

    public function __construct()
    {
        parent::__construct();

        $this->limit_account = AccountProduct::getLimitAccountProduct();
        $this->limit_account_ids = array_column($this->limit_account,'account_id');
        $this->apikey_product = Account::getApikeyByAccountId($this->limit_account_ids);
        $this->dingding = new DingDing(config('params.dingding.appkey'),config('params.dingding.secret'));
    }

    public function handle()
    {
        $stay_alert_data =  $this->getConsumptionByApikey();
        $this->sendStayAlert($stay_alert_data);
    }

    //根据apikey 和 pid 获取日 月 年 总消耗
    public function getConsumptionByApikey()
    {
        //本日
        $day = date('Ymd',time());
        //本月开始时间
        $timestamp = mktime(0, 0, 0, date('m'), 1, date('Y'));
        $month = date('Ymd', $timestamp);
        //本年开始时间
        $timestamp = mktime(0, 0, 0, 1, 1, date('Y'));
        $year = date('Ymd', $timestamp);

        $apikey_product = array_column($this->apikey_product,NULL,'account_id');
        //合并apikey
        foreach ($this->limit_account as $key=>$vo){
            //包含apikey 的账号-产品集合
            $limit_account[] = array_merge($vo,$apikey_product[$vo['account_id']]);
        }
        $stay_alert_data = [];
        //获取带报警内容
        array_walk($limit_account,function (&$item) use($day,$month,$year,&$stay_alert_data){
            //如果设置日限量 查询本日消耗
            if ($item['daily_limit'] > 0){
                $day_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$day,$day);
                $daily_rate = $day_limit/$item['daily_limit'];
                //占比大于5成 添加报警
                if ($daily_rate >= 0.5){
                    $item['daily_use'] = $day_limit;
                    $item['daily_use_rate'] = $daily_rate;
                }
            }
            //如果设置月限量 查询本月消耗
            if ($item['month_limit'] > 0){
                $month_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$month,$day);
                $month_rate = $month_limit/$item['month_limit'];
                if ($month_rate >= 0.5){
                    $item['month_use'] = $month_limit;
                    $item['month_use_rate'] = $month_rate;
                }
            }
            //如果设置年限量 查询本年消耗
            if ($item['year_limit'] > 0){
                $year_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$year,$day);
                $year_rate = $year_limit/$item['year_limit'];
                if ($year_rate >= 0.5){
                    $item['year_use'] = $year_limit;
                    $item['year_use_rate'] = $year_rate;
                }
            }
            //如果设置总限量 查询总消耗
            if ($item['year_limit'] > 0 && !is_null($item['limit_start_date'])){
                $start_date = str_replace('-', '', $item['limit_start_date']);
                $total_limit = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'],$item['product_id'],$start_date,$day);
                $total_rate = $total_limit/$item['total_limit'];
                if ($total_rate >= 0.5){
                    $item['total_use'] = $total_limit;
                    $item['total_use_rate'] = $total_rate;
                }
            }
            if (isset($item['daily_use']) || isset($item['month_use']) || isset($item['year_use']) || isset($item['total_use'])){
                array_push($stay_alert_data,$item);
            }
        });

        return $stay_alert_data;
    }

    private function sendStayAlert($stay_alert_data)
    {
        array_walk($stay_alert_data ,function ($item){
            $alarm_info =   $this->getAlarmContent($item);
            $this->performAlarm($alarm_info);
        });
    }

    private function performAlarm($alarm_info)
    {
        if (empty($alarm_info)){
            return false;
        }

        foreach ($alarm_info as $v){
//            //限量由张韧审批
//            $user_id = DINGDING_ZHANGREN_USERID;
//            //抄送杨雪莉
//            $cc =DINGDING_YANGXUELI_USERID;

            $level = $this->getAlarmLevel($v['use'],$v['limit']);
            //确认是否已报警
            $where = [
                ['customer_id','=',$v['customer_id']],
                ['account_id','=',$v['account_id']],
                ['type','=',1],  //限量
                ['level','=',$level],
                ['real_result','=',1],
            ];
            $process = DingdingCustomerApprovalProcess::getNotProcessed($where);

            if (!empty($process)){
                continue;
            }

            $formValue = [
                [
                    "name" => "类型",
                    "value" => $v['type'],
                ],
                [
                    "name" => "客户",
                    "value" => $v['customer_name'],
                ],
                [
                    "name" => "账号",
                    "value" => $v['account_name'],
                ],
                [
                    "name" => "限量",
                    "value" =>$v['use'].'/'. $v['limit'],
                ],
            ];
            $approvers_v2 = [
                'task_action_type'=>'NONE',
                "user_ids"=> [
                    DINGDING_ZHANGREN_USERID,
                    //DINGDING_LIJIANYE_USERID,
                ]
            ];
            $cc = [
                'cc_position'=>'FINISH',
                'cc_list'=> DINGDING_YANGXUELI_USERID
               //'cc_list'=> DINGDING_LIJIANYE_USERID

            ];

            $dept_id = $this->getDeptId(DINGDING_ZHANGREN_USERID);

             //钉钉报警
           $process_instance_id = $this->dingding->createProcessApprovers(DINGDING_ALARM_PROCESS_CODE,DINGDING_ZHANGREN_USERID,$dept_id,$formValue,$approvers_v2,$cc);

           $insert_data = ['customer_id'=>$v['customer_id'],'account_id'=>$v['account_id'],'type'=>1,'level'=>$level,'process_instance_id'=>$process_instance_id,'create_time'=>time()];
           //添加钉钉报警处理流程
           DingdingCustomerApprovalProcess::insert($insert_data);
            //限量报警 电话告知
           AliCall::call(ALARM_PHONE_ZR);
        }
    }
    private function  getAlarmContent($item)
    {
        $account_info = Account::getAccountInfoByApikey($item['apikey']);
        $customer_info = Customer::getOneItemByCondition(['customer_id'=>$account_info['customer_id']]);
        $alarm_info = [];
        if (isset($item['daily_use'])){
           // $alarm_content['alarm_content'][] =  $this->getRealContent(1,$account_info['account_name'],$customer_info['name'],$item['daily_limit'],$item['daily_use'],$item['daily_use_rate']);
            $alarm_info[] =  ['type'=>'客户日限量','account_name'=>$account_info['account_name'],'customer_name'=>$customer_info['name'],'limit'=>$item['daily_limit'],'use'=>$item['daily_use'],'account_id'=>$account_info['account_id'],'customer_id'=>$account_info['customer_id'],'salesman'=>$customer_info['salesman']];
        }

        if (isset($item['month_use'])){
           // $alarm_content['alarm_content'][] =  $this->getRealContent(2,$account_info['account_name'],$customer_info['name'],$item['month_limit'],$item['month_use'],$item['month_use_rate']);
            $alarm_info[] =  ['type'=>'客户月限量','account_name'=>$account_info['account_name'],'customer_name'=>$customer_info['name'],'limit'=>$item['month_limit'],'use'=>$item['month_use'],'account_id'=>$account_info['account_id'],'customer_id'=>$account_info['customer_id'],'salesman'=>$customer_info['salesman']];
        }

        if (isset($item['year_use'])){
           // $alarm_content['alarm_content'][] =  $this->getRealContent(3,$account_info['account_name'],$customer_info['name'],$item['year_limit'],$item['year_use'],$item['year_use_rate']);
            $alarm_info[] =  ['type'=>'客户年限量','account_name'=>$account_info['account_name'],'customer_name'=>$customer_info['name'],'limit'=>$item['year_limit'],'use'=>$item['year_use'],'account_id'=>$account_info['account_id'],'customer_id'=>$account_info['customer_id'],'salesman'=>$customer_info['salesman']];
        }

        if (isset($item['total_use'])){
          //  $alarm_content['alarm_content'][] =  $this->getRealContent(4,$account_info['account_name'],$customer_info['name'],$item['total_limit'],$item['total_use'],$item['total_use_rate'],$item['limit_start_date']);
            $alarm_info[] =  ['type'=>'客户总限量','account_name'=>$account_info['account_name'],'customer_name'=>$customer_info['name'],'limit'=>$item['total_limit'],'use'=>$item['total_use'],'account_id'=>$account_info['account_id'],'customer_id'=>$account_info['customer_id'],'limit_start_date'=>$item['limit_start_date'],'salesman'=>$customer_info['salesman']];
        }

        return $alarm_info;
    }

//    private function  getRealContent($type,$customer_name,$account_name,$limit,$use,$use_rate,$start_date = null)
//    {
//        //日报警内容
//        if ($type == 1){
//            return "报警信息：客户账号日限量报警\n客户名称：$customer_name\n账户名称：$account_name\n日限量：$limit\n本日用量：$use\n\n使用占比：$use_rate";
//        }elseif ($type == 2){//月限量报警内容
//            return "报警信息：客户账号月限量报警\n客户名称：$customer_name\n账户名称：$account_name\n月限量：$limit\n本月用量：$use\n\n使用占比：$use_rate";
//        }elseif ($type == 3){ //年限量报警内容
//            return "报警信息：客户账号年限量报警\n客户名称：$customer_name\n账户名称：$account_name\n年限量：$limit\n本年用量：$use\n\n使用占比：$use_rate";
//        }elseif($type == 4){ //总限量报警内容
//            return "报警信息：客户账号总限量报警\n客户名称：$customer_name\n账户名称：$account_name\n总限量：$limit\n总用量：$use\n\n使用占比：$use_rate\n\n总用量开始时间：$start_date";
//        }
//    }


     private  function  getDUserIdBySalesman($salesman)
     {
         $result = '';
         //获取销售的电话
         $phone = SystemUser::getPhoneByUserName($salesman)['phone'];
         //根据电话获取钉钉的userid
         if (!empty($phone)){
            $res =  $this->dingding->getUserIdByPhoneNumber($phone);
            if ($res['errcode'] == 0){
                $result =  $res['result']['userid'];
            }
         }

         return $result;
     }

    private  function  getDeptId($userid)
    {
        $dept_id = '';
        //根据userid 获取部门id
        $res =  $this->dingding->getDepIdByUserId($userid);
        if ($res['errcode'] == 0){
            $dept_id =  $res['result']['parent_list'][0]['parent_dept_id_list'][0];
        }
        $dept_id = !empty($dept_id) ? $dept_id : DEFAULT_DEPT_ID;

        return $dept_id;
    }

    private function getAlarmLevel($use,$limit)
    {
        $rate = $use/$limit * 100;
        if ($rate>= 50 && $rate<80){
            return 50;
        }elseif ($rate>= 80 && $rate<100){
            return 80;
        }else{
            return 100;
        }
    }

}


