<?php


namespace App\Console\Commands\Customer;


use App\Models\AccountProduct;
use App\Models\Customer\CustomerHistoryUsage;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use Illuminate\Console\Command;


class CustomerProductUsage extends Command
{

    protected $signature = 'statis:customer_product_usage';

    //定义一个暂时只查的父产品
    public $fatherIds = [200, 210, 1000, 10000, 615, 30000];

    /**
     * @throws \Exception
     */
    public function handle()
    {

        try{

            $date = date('Ymd', strtotime("-1 day"));
            $end_time = strtotime($date) + 86400 - 1;

            //获取要查询的子产品
            $product_ids = Product::getSubPidByFatherId($this->fatherIds);
            //获取产品状态可用、截止时间未到时间的客户产品列表
            $list = AccountProduct::getAvailableList($end_time, $product_ids);
            $have_limit_arr = [];//定义一个有限量的数组
            $month_limit_apikey = [];//定义一个月限量的用户数组
            $year_limit_apikey = [];//定义一个年限量的用户数组
            $total_limit_apikey = [];//定义一个总限量的用户产品数组
            foreach ($list as $item) {
                if ($item['daily_limit'] == -1 && $item['month_limit'] == -1 &&
                    $item['year_limit'] == -1 && $item['total_limit'] == -1
                ) {

                    continue;
                }

                $have_limit_arr[] = $item;

                //因为月限量和年限量和总限量的直接查询耗时较多 因此暂不直接查询，而是先记录都有那些用户(本身这两种情况的客户并不是特别多)
                if($item['month_limit'] > 0){
                    $month_limit_apikey[] = ['apikey' => $item['apikey'], 'product_id' =>$item['product_id']];
                }

                if($item['year_limit'] > 0){
                    $year_limit_apikey[] = ['apikey' => $item['apikey'], 'product_id' =>$item['product_id']];
                }

                if($item['total_limit'] > 0){
                    $limit_date = $item['limit_start_date'] ? str_replace( '-', '', $item['limit_start_date']) : date('Ymd', $item['create_at']);
                    $total_limit_apikey[] = ['apikey' => $item['apikey'], 'product_id' =>$item['product_id'], 'limit_date' => $limit_date];
                }

            }
            unset($list);

            //直接获取月用量
            $month_list = $this->getMonthList($date, array_unique(array_column($month_limit_apikey, 'product_id')), array_column($month_limit_apikey, 'apikey'));

            //获取年用量
            $year_list = $this->getYearList($date, array_unique(array_column($year_limit_apikey, 'product_id')), array_column($year_limit_apikey, 'apikey'));

            //获取总用量
            $total_list = $this->getTotalList($date, $total_limit_apikey);

            //获取历史表数据
            $history_data = $this->getCustomerHistoryUsage();

            //插入或更新数据
            $insert = [];
            foreach ($have_limit_arr as $item){

                $row['apikey'] = $item['apikey'];
                $row['product_id'] = $item['product_id'];
                $row['end_date'] = $date;

                if($item['month_limit'] == -1){
                    $row['month_usage'] = -1;
                }else{
                    $row['month_usage'] = $month_list[$item['apikey']][$item['product_id']]['month_usage'] ?? 0;
                }

                if($item['year_limit'] == -1){
                    $row['year_usage'] = -1;
                }else{
                    $row['year_usage'] = $year_list[$item['apikey']][$item['product_id']]['year_usage'] ?? 0;
                }

                if($item['total_limit'] == -1){
                    $row['total_usage'] = -1;
                }else{
                    $row['total_usage'] = $total_list[$item['apikey']][$item['product_id']]['total_usage'] ?? 0;
                }

                $row['create_time'] = time();

                //判断一下是更新还是插入
                if(isset($history_data[$item['apikey']][$item['product_id']]['id'])){
                    $this->updateData($history_data[$item['apikey']][$item['product_id']]['id'], $date, $row);
                }else{
                    $insert[] = $row;
                }

            }

            $this->addData($insert);

        }catch (\Exception $e){
            //dd($e->getMessage(), $e->getFile(), $e->getLine());
            $msg = "客户产品用量更新失败".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "数据日期:".$date.PHP_EOL;
            $msg .= "错误文件:".$e->getFile().PHP_EOL;
            $msg .= "错误行号:".$e->getLine().PHP_EOL;
            $msg .= "错误详情:".$e->getMessage();
            sendWechatNotice($msg);
        }

        $this->output->success("成功执行");

    }


    public function getCustomerHistoryUsage(){
        $list = CustomerHistoryUsage::getListByCondition([]);
        //格式化数据
        $res = [];
        foreach ($list as $item){
            $res[$item['apikey']][$item['product_id']]['id'] = $item['id'];
        }
        unset($list);

        return $res;
    }

    public function addData($data){
        if(!empty($data)){
            $addChunk = array_chunk($data, 200);
            foreach ($addChunk as $addArr) {
                CustomerHistoryUsage::insert($addArr);
            }
        }

        return true;
    }

    public function updateData($id, $date, $row){
        $up_data = [
            'month_usage' => $row['month_usage'],
            'year_usage' => $row['year_usage'],
            'total_usage' => $row['total_usage'],
            'end_date' => $date,
            'update_time' => time()
        ];
        CustomerHistoryUsage::where('id', $id)->update($up_data);
        return true;
    }


    public function getMonthList($date, $product_ids, $apikeys){
        $month_start_date = substr($date, 0, 6).'01';
        $month_where = [
            ['date', '>=', $month_start_date],
            ['date', '<=', $date]
        ];

        $month_list = StatisticsCustomerUsage::customerProductUsage($month_where, $product_ids, $apikeys)->toArray();
        //格式化数据
        $res = [];
        foreach ($month_list as $item){
            $res[$item['apikey']][$item['product_id']]['month_usage'] = $item['total'];
        }

        unset($month_list);
        return $res;
    }

    public function getYearList($date, $product_ids, $apikeys){
        $year_start_date = substr($date, 0, 4).'0101';
        $year_where = [
            ['date', '>=', $year_start_date],
            ['date', '<=', $date]
        ];

        $year_list = StatisticsCustomerUsage::customerProductUsage($year_where, $product_ids, $apikeys)->toArray();
        //格式化数据
        $res = [];
        foreach ($year_list as $item){
            $res[$item['apikey']][$item['product_id']]['year_usage'] = $item['total'];
        }

        unset($year_list);
        return $res;
    }

    public function getTotalList($date, $data){
        $total_list = [];
        foreach ($data as $item){
            $total_where = [
                ['date', '>=', $item['limit_date']],
                ['date', '<=', $date],
                ['apikey', '=', $item['apikey']],
                ['call_product_id', '=', $item['product_id']],
            ];
            $total_info = StatisticsCustomerUsage::customerProductUsage($total_where)->toArray();
            if(isset($total_info[0]) && !empty($total_info[0])){
                $total_list[] = $total_info[0];
            }
        }

        //格式化数据
        $res = [];
        foreach ($total_list as $item){
            $res[$item['apikey']][$item['product_id']]['total_usage'] = $item['total'];
        }

        unset($data);
        unset($total_list);
        return $res;
    }



}