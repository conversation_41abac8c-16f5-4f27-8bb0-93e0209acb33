<?php

namespace App\Console\Commands\Customer;

use App\Define\Common;
use App\Define\StatDefine;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\BillProductIncome;
use App\Models\BillProductIncomeV2;
use App\Models\ConfigPriceCustomer;
use App\Models\Customer;
use App\Models\Customer\CustomerNewOldCollect;
use App\Models\SystemAccessLog;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * 客户年度新老数据统计
 *
 * 1 按计费配置时间计算新老客户 配置时间满一年为老客户
 *
 *
 * php artisan customer:yearly_new_old_customer_v2 --date=******** --export_type=group
 * php artisan customer:yearly_new_old_customer_v2 --date=******** --export_type=customer
 */
class YearlyNewOldCustomer extends Command
{
    /**
     * 脚本名称,参数
     * @var string
     */
    protected $signature = 'customer:yearly_new_old_customer_v2
    {--date=: Ymd 截至日期}
    {--export_type=: group:按主体; customer:按客户}';

    /**
     * 脚本描述
     * @var string
     */
    protected $description = '客户年度新老数据统计';


    /** @var int 5个月 */
    protected $five_month_seconds = 12960000;

    protected $cache_log_data = [];


    /** @var array 主体配置 处理后为 主体名称 => [客户id,客户id] */
    private $group_config = [
        ['智度小贷', 'C201909104C7ADM'],
        ['智度小贷', 'C202306218X2QJX'],
        ['友微科技', 'C20181101M41IR3'],
        ['友微科技', 'C202304235J1LCK'],
        ['友微科技', 'C20230427516DZ5'],
        ['友微科技', 'C202305099N4F9R'],
        ['友好物', 'C20230612V0LCKT'],
        ['友好物', 'C20240109H1HX3K'],
        ['宜信', 'C20200812O4G4F8'],
        ['宜信', 'C20230411IN3O9Y'],
        ['海尔小额贷', 'C20200213A46X6K'],
        ['易车', 'C20230109BDUIBW'],
        ['易车', 'C20220509FBWLMJ'],
        ['得物', 'C20210716I8QCT6'],
        ['洋钱罐', 'C202103245FCGEZ'],
        ['九四智能', 'C20211206J10EKB'],
        ['九四智能', 'C20231031U40J5T'],
        ['洋钱罐', 'C20230306GCGWAM'],
        ['洋钱罐', 'C20230616U76IJY'],
        ['鑫涌算力', 'C20181101CKS5WI'],
        ['鑫涌算力', 'C20210129M847BZ'],
        ['信飞', 'C20220926GVCU98'],
        ['信飞', 'C20211014HWQSKY'],
        ['小花钱包', 'C20200601JIIUZU'],
        ['小花钱包', 'C20220812SQCXUX'],
        ['我来数科', 'C20220713FBZN02'],
        ['我来数科', 'C20240227XALZWY'],
        ['未讯', 'C201908051UCYAT'],
        ['未讯', 'C20221025U8IEW3'],
        ['未讯', 'C20230209XB7XGO'],
        ['维信', 'C201811018VUWII'],
        ['维信', 'C20220221A4IIUN'],
        ['苏宁', 'C20220316B4OLOD'],
        ['苏宁', 'C20220317IKQKB2'],
        ['数钥', 'C20220826LISTX9'],
        ['数钥', 'C20200108QJSR2B'],
        ['数禾', 'C20210519EJEXJO'],
        ['数禾', 'C20220527SP8RXB'],
        ['数禾', 'C20221108QGACLI'],
        ['世纪银通', 'C20190401S7ASMU'],
        ['磊通', 'C20230425MMFDX7'],
        ['世纪银通', 'C20240312ZL0XUY'],
        ['小莱助贷', 'C20230517YCKXYE'],
        ['萨摩耶', 'C20231010VQ7Q3G'],
        ['融七牛', 'C2020120895PURH'],
        ['融七牛', 'C202305290KBC2Y'],
        ['融七牛', 'C20230425DB5K46'],
        ['俏租机', 'C20230628R8EC2L'],
        ['俏租机', 'C2024032173DYCJ'],
        ['南京铭智', 'C20220531ZO08RQ'],
        ['南京铭智', 'C20221108I9U8SU'],
        ['蚂蚁', 'C20190614WWMKLO'],
        ['蚂蚁', 'C202108258VWANY'],
        ['蚂蚁', 'C20221223U6K5EI'],
        ['蚂蚁', 'C20230113Z0USHH'],
        ['微米', 'C20200728NU5F0E'],
        ['满帮', 'C2023070586CGLK'],
        ['满帮', 'C20230706X9OOOO'],
        ['晋商消费', 'C20230901BLTF03'],
        ['晋商消费', 'C20211027538X90'],
        ['锦锐科技', 'C20220926WXULUA'],
        ['锦锐科技', 'C202302283N0F77'],
        ['锦锐科技', 'C20230228FUDIEQ'],
        ['桔子', 'C202209076BY4GB'],
        ['桔子', 'C201908143BJAH7'],
        ['湖北消金', 'C20220926PCYLU4'],
        ['湖北消金', 'C20201123P0T4YT'],
        ['国政通', 'C20191029RPKC4H'],
        ['哈啰', 'C20221215NHJALS'],
        ['哈啰', 'C20230509YBK9VY'],
        ['时光金科', 'C20200813LX9Z2C'],
        ['时光金科', 'C201811018WV5VV'],
        ['杭银消金', 'C20190801R97IIL'],
        ['杭银消金', 'C20220926F86JSK'],
        ['海发宝诚', 'C201811012A5B6W'],
        ['海发宝诚', 'C20221116TXGCQ2'],
        ['国政通', 'C20210416U0B6ZQ'],
        ['国美', 'C2019070560JUMS'],
        ['滴滴金融', 'C202304269P0Q9O'],
        ['国美', 'C20230823BDEH5L'],
        ['孚临', 'C20181101IOLJH1'],
        ['孚临', 'C202308210PN18J'],
        ['凤凰木', 'C20191115TBPAAU'],
        ['凤凰木', 'C2024011939QTRU'],
        ['成都风明', 'C202207209ZLRP6'],
        ['恒昌', 'C20230913YMDLJF'],
        ['恒昌', 'C2018110138JB60'],
        ['成都风明', 'C202308281X9A96'],
        ['百维金科', 'C20190228RN4GZ4'],
        ['百维金科', 'C20230703RGRD58'],
        ['360', 'C20220424XD2DOT'],
        ['360', 'C20200722WMI2AF'],
        ['360', 'C20220424U7J7X6'],
        ['信和小贷', 'C20240511GTU5ZP'],
        ['信和小贷', 'C20191227JJ8XH2'],
        ['海尔小额贷', 'C20240328QY277L'],
        ['海尔小额贷', 'C20191015J1JJPD'],
        ['微米', 'C20240403VQVS5U'],
        ['微米', 'C202404037WXW6K'],
        ['国美', 'C20240729QCSTFK'],
        ['磊通', 'C20240709KHHK4G'],
        ['恒昌', 'C202407239NFPYN'],
        ['小莱助贷', 'C20240325PPRNMZ'],
        ['小莱助贷', 'C20240717ZX7WTZ'],
        ['时光金科', 'C20240812YS28LA'],
        ['民和小贷', 'C20240821A0NID2'],
        ['民和小贷', 'C202310171TBUW7'],
        ['国美', 'C20240821R2OJM4'],
        ['恒昌', 'C20240827RF9JS3'],
        ['青年优品', 'C20240306TIMSO6'],
        ['青年优品', 'C20240829SHN459'],
        ['得物', 'C20240830XUKAAA'],
        ['乐信', 'C20240320OMMUPU'],
        ['乐信', 'C20240830EDZCHD'],



        //以上是运营给出的,下面是相同公司名称的补充数据


        ['58金融','C20240612GC5B0D'],
        ['58金融','C20200225LYAJVX'],
        ['富民银行','C20220706ZD1SJC'],
        ['富民银行','C20181101KTFTJQ'],
        ['伟仕金服','C201811018G270Y'],
        ['伟仕金服','C20181101GGOBS2'],
        ['福州微微云','C201811017LVMY7'],
        ['福州微微云','C20181101LILKX5'],
        ['分啦分期','C20191105AIT1VT'],
        ['分啦分期','C20181101YPB9L7'],
        ['随手记','C20181101YRXUX2'],
        ['随手记','C20181101LHVREG'],
        ['团贷网','C20181217DTQ97V'],
        ['团贷网','C20181101FQBVZT'],
        ['复融','C20190923ZA9710'],
        ['复融','C20190314BS8JMB'],
        ['启元信息','C20181217D4BJCI'],
        ['启元信息','C20181217FTL6PZ'],
        ['启元信息','C20181101VS6AJO'],
        ['启元信息','C20181101CCE390'],
        ['浙数交','C20230801HY2WHW'],
        ['浙数交','C20230317VW9BBE'],
        ['江苏银行','C20240923VB6ZQO'],
        ['江苏银行','C20230807CTQ3IN'],
        ['晋城银行','C201908222PAO4G'],
        ['晋城银行','C20180903PZEF3D'],
        ['黑牛','C20190212LTI4GE'],
        ['黑牛','C20190212EPW4HR'],
        ['分期超人','C20230518SB1Z8U'],
        ['分期超人','C20181101WRR233'],
        ['徙木金融','C20190102FAOQW7'],
        ['徙木金融','C20181101N2RO9B'],
        ['雪松普惠','C20181101JZ9IRT'],
        ['雪松普惠','C20181101IUB34M'],
        ['九四智能','C201904152KU52Q'],
        ['大箴科技','C20181101KSJFO8'],
        ['大箴科技','C20181101MGKK4J'],
        ['宁波通商行','C20211108P2MM8G'],
        ['宁波通商行','C20181101UQILA4'],
        ['聚合','C20220720WGYTEB'],
        ['聚合','C20190813NFWFKB'],
        ['天津成兴融担','C20240822AAP4Q3'],
        ['天津成兴融担','C20240312V28AU9'],
        ['天创信用','C2023110117CY7D'],
        ['天创信用','C20220613QXKY34'],
        ['松花江租赁','C20191106XT16WS'],
        ['松花江租赁','C20181101ACZR4G'],
        ['金美信','C20231221Q2SO6Z'],
        ['金美信','C202312211D830Q'],
        ['金美信','C20181101983YOM'],
        [Common::COMPANY_CN_NAME,'C20231221U4YJSY'],
        [Common::COMPANY_CN_NAME,'C20231113E66N9F'],
        [Common::COMPANY_CN_NAME,'C20180828LOCNMG'],
        ['掌众','C20190919H7FKQT'],
        ['掌众','C20181227K57C7R'],
        ['信德数据','C20181227L3CLS3'],
        ['信德数据','C201812271A5MAV'],
        ['中腾信','C20181101W1F24P'],
        ['中腾信','C20181101N8GI07'],
        ['点融','C20200218730V7W'],
        ['点融','C20191224UC3NJT'],
        ['拍拍信','C202312206YG1SU'],
        ['拍拍信','C20181101QLF5BS'],
        ['沃服','C20181227GEFT0D'],
        ['沃服','C201812278FAKEY'],
        ['快钱','C20181101X16FXP'],
        ['快钱','C20190107XZ8PHK'],
        ['信而富','C2018110168A1LQ'],
        ['信而富','C2018121729TM1L'],
        ['信而富','C201812172B9P4B'],
    ];

    /** @var array 主体配置 客户id => 主体名称 */
    private $customer_group_map = [];

    /**
     * 构造函数
     */
    public function __construct(){
        parent::__construct();

        $this->group_config = Customer::getGroupCustomers();

        //处理主体关系
        $group_config = [];
        foreach($this->group_config as $info){
            $group_id  = $info['group_id'];
            $customer_id = $info['customer_id'];
            $group_config[$group_id][] = $customer_id;
            $this->customer_group_map[$customer_id] = $group_id;
        }
        $this->group_config = $group_config;
    }

    /**
     * 执行
     *
     * @access public
     * @return void
     * @throws Exception
     */
    public function handle(){
        $date        = $this->input->getOption('date');
        $export_type = $this->input->getOption('export_type');

        $this->output->success("【{$this->description}】 开始");

        $this->get_customer_product($date,$export_type);
        // $customer_list = $this->get_customer_product($date,$export_type);

        // $type_name = $export_type == 'group' ? '按主体导出' : '按客户导出';
        // $file_name = "新老客户数据_".$type_name."_对比时间_".$date."_导出时间_".date("YmdHis");
        // $header = ['客户','是否为新客户','客户开始计费时间','产品','是否为新产品','产品开始计费时间','客户是否禁用','禁用时间'];
        // $this->out_csv($header,$customer_list,$file_name);

        $this->output->success("【{$this->description}】 完成");
    }

    /**
     *
     *
     * @param $date
     * @param $export_type
     *
     * @throws Exception
     * <AUTHOR> 2024-09-24 16:41:50
     */
    private function get_customer_product($date,$export_type) {
        $date_timestamp = strtotime($date);
        $month = date('Ym',$date_timestamp);

        echo $month,':',$export_type,PHP_EOL;

        $all_customers = Customer::select(['customer_id','name'])->where([['create_at','<=',$date_timestamp]])->get()->toArray();

        switch($export_type){
            case 'group':
                $_customer_list = $this->get_customer_map_with_group($all_customers);
                break;
            case 'customer':
                $_customer_list = $this->get_customer_map_without_group($all_customers);
                break;
            default:
                $_customer_list = [];
                $this->output->warning("【{$this->description}】 export_type 参数错误!!!");
                break;
        }

        $this->cache_customer_status_change_data($date_timestamp);

        $father_id_maps = [];
        $customer_list = [];
        foreach($_customer_list as $rel_id => $info){
            $account_list = Account::getListByCustomerIds($info['customer_id']);

            $apikey_arr   = array_column($account_list, 'apikey');

            $config_prices = ConfigPriceCustomer::select(['apikey','father_id','product_ids','start_date'])
                                                ->whereIn('apikey', $apikey_arr)
                                                ->where([['start_date','<=',$date]])
                                                ->get()->toArray();
            if(empty($config_prices)){
                continue;
            }
            $min_start_date = min(array_column($config_prices,'start_date'));

            $config_prices_list = [];
            foreach($config_prices as $cp_info){
                $_product_ids = explode(',',$cp_info['product_ids']);
                unset($cp_info['product_ids']);
                foreach($_product_ids as $_product_id) {
                    $father_id_maps[$_product_id] = $cp_info['father_id'];
                    $cp_info['product_id'] = $_product_id;
                    $config_prices_list[] = $cp_info;
                }
            }

            $_product_prices_arr = $product_prices_arr = [];
            $min_start_date = $config_prices_list[0]['start_date'];//计算新老客户
            foreach($config_prices_list as $cp_info){
                if($min_start_date > $cp_info['start_date']){
                    $min_start_date = $cp_info['start_date'];
                }
                if(!isset($_product_prices_arr[$cp_info['father_id']])){
                    $_product_prices_arr[$cp_info['father_id']] = $cp_info['start_date'];
                }else{
                    if($_product_prices_arr[$cp_info['father_id']] > $cp_info['start_date']){
                        $_product_prices_arr[$cp_info['father_id']] = $cp_info['start_date'];
                    }
                }
                if(!isset($_product_prices_arr[$cp_info['product_id']])){
                    $_product_prices_arr[$cp_info['product_id']] = $cp_info['start_date'];
                }else{
                    if($_product_prices_arr[$cp_info['product_id']] > $cp_info['start_date']){
                        $_product_prices_arr[$cp_info['product_id']] = $cp_info['start_date'];
                    }
                }
            }
            foreach($_product_prices_arr as $_product_id => $product_start_date){
                // 计算新老产品
                $diff_time = $date - $product_start_date;
                if($diff_time < 10000) {
                    $product_prices_arr[$_product_id] = ['old_or_new' => 'new', 'product_start_date' => $product_start_date];
                }else{
                    $product_prices_arr[$_product_id] = ['old_or_new' =>'old','product_start_date' => $product_start_date];
                }
            }

            $customer_list[$rel_id]['start_date'] = $min_start_date;
            $customer_list[$rel_id]['is_new'] = ($date - $min_start_date) < 10000 ? 'new' : 'old';
            $customer_list[$rel_id]['product_start_date'] = $product_prices_arr;


            $disable_status = $this->get_group_disable_time($info['customer_id'],$customer_list[$rel_id]['is_new'],$date,$min_start_date,$date_timestamp);
            $customer_list[$rel_id]['disable']      = $disable_status['is_disable'];
            $customer_list[$rel_id]['disable_time'] = $disable_status['disable_time'];
            $customer_list[$rel_id]['disable_status_time'] = $disable_status['disable_status_time'];
            echo $info['customer_id'][0],' - ',$disable_status['is_disable'],' - ',$disable_status['disable_time'],' - ',$disable_status['disable_status_time'],PHP_EOL;
        }

        foreach($customer_list as $rel_id => $info){
            $now = date('Y-m-d H:i:s');
            foreach($info['product_start_date'] as $product_id => $product_is_old_or_new_info) {
                $product_start_date = $product_is_old_or_new_info['product_start_date'];
                $product_is_old_or_new = $product_is_old_or_new_info['old_or_new'];
                $res = [
                    'collect_type'        => $export_type == 'customer' ? Common::CUSTOMER_NEW_OLD_COLLECT_TYPE_CUSTOMER : Common::CUSTOMER_NEW_OLD_COLLECT_TYPE_GROUP,
                    'rel_id'              => $rel_id,
                    'month'               => $month,
                    'is_new_customer'     => $info['is_new'] == 'new' ? Common::CUSTOMER_NEW_OLD_NEW_CUSTOMER : Common::CUSTOMER_NEW_OLD_OLD_CUSTOMER,
                    'customer_start_date' => $info['start_date'],
                    'product_id'          => $product_id,
                    'father_id'           => isset($father_id_maps[$product_id]) ? ($father_id_maps[$product_id] == $product_id ? 0 : $father_id_maps[$product_id]) : 0,
                    'is_new_product'      => $product_is_old_or_new == 'new' ? Common::CUSTOMER_NEW_OLD_NEW_PRODUCT : Common::CUSTOMER_NEW_OLD_OLD_PRODUCT,
                    'product_start_date'  => $product_start_date,
                    'customer_disable'    => $info['disable'] ? Common::CUSTOMER_NEW_OLD_CUSTOMER_DISABLE : Common::CUSTOMER_NEW_OLD_CUSTOMER_ABLE,
                    'disable_status_time' => $info['disable_status_time'],
                    'disable_date'        => $info['disable_time'],
                    'created_at'          => $now,
                    'updated_at'          => $now,
                ];

                CustomerNewOldCollect::updateOrCreate([
                    'collect_type' => $res['collect_type'],
                    'rel_id'       => $res['rel_id'],
                    'month'        => $res['month'],
                    'product_id'   => $res['product_id'],
                ], $res);
            }
        }
    }

    private function cache_customer_status_change_data($date_timestamp){
        $where = [
            ['uri','=','/Account/Customer/edit'],
            ['created_at','>',**********],
            ['created_at','<',$date_timestamp],
        ];

        $cus_res = SystemAccessLog::where($where)->orderBy('id','asc')->get()->toArray();
        foreach($cus_res as $info){
            $content = json_decode($info['content'],true);
            if(isset($content['callback_url'])){
                continue;
            }
            $customer_id = $content['customer_id'];
            $status = $content['status'];
            $this->cache_log_data['customer_edit-'.$customer_id.'-'.$date_timestamp] = [
                'status' => $status,
                'created_at' => $info['created_at'],
            ];
        }


        $where = [
            ['uri','=','/Account/Account/edit'],
            ['created_at','>',**********],
            ['created_at','<',$date_timestamp],
        ];

        $acc_res = SystemAccessLog::where($where)->orderBy('id','asc')->get()->toArray();
        foreach($acc_res as $info){
            $content = json_decode($info['content'],true);
            $apikey = $content['apikey'];
            $status = $content['status'];
            $this->cache_log_data['account_edit-'.$apikey.'-'.$date_timestamp] =[
                'status' => $status,
                'created_at' => $info['created_at'],
            ];
        }
    }


    private function get_customer_map_with_group($all_customers) {
        $customer_list = [];
        foreach($all_customers as $info){
            if(key_exists($info['customer_id'], $this->customer_group_map)){
                $customer_list[$this->customer_group_map[$info['customer_id']]]['customer_id'][] = $info['customer_id'];
            }else{
                $customer_list[$info['customer_id']]['customer_id'][] = $info['customer_id'];
            }
        }
        return $customer_list;
    }

    private function get_customer_map_without_group($all_customers) {
        $customer_list = [];
        foreach($all_customers as $info){
                $customer_list[$info['customer_id']]['customer_id'][] = $info['customer_id'];
        }
        return $customer_list;
    }


    /**
     *
     *
     * @param     $customer_ids
     * @param     $is_new
     * @param     $date
     * @param int $customer_start_date     最早配置时间 开始时间
     * @param int $date_timestamp 新老客户定义时间 查询日志时不能大于这个时间
     *
     * @return array
     * <AUTHOR> 2024-12-31 16:29:10
     */
    private function get_group_disable_time($customer_ids, $is_new, $date, $customer_start_date, $date_timestamp) {
        $def_res = [
            'is_disable'          => false,
            'disable_time'        => null,
            'disable_status_time' => null,
        ];

        // 新客户不会流失
        if($is_new != 'old'){
            echo __LINE__,':新客户不流失,';
            return $def_res;
        }

        $customer_list = Customer::getCustomerListByCustomerIds('*', $customer_ids);
        $customer_list_2 = array_column($customer_list,null,'customer_id');

        //根据帐号状态判断
        $account_list = Account::getListByCustomerIds($customer_ids);
        $account_list_2 = array_column($account_list, null, 'apikey');
        $apikeys = array_filter(array_column($account_list, 'apikey'));

        $fmt_date = date('Ymd', $date_timestamp);

        // 大于 2021-11-17
        if($date_timestamp >= **********) {
            echo 'after:';

            $is_disable       = 0;
            $last_update_time = 0;
            foreach($customer_ids as $customer_id) {
                $res = $this->cache_log_data['customer_edit-' . $customer_id . '-' . $date_timestamp]??[];
                if($res) {
                    $is_disable       = $is_disable + $res['status'];
                    $last_update_time = max($last_update_time, $res['created_at']);
                }else{
                    $is_disable       = $is_disable + $customer_list_2[$customer_id]['status'];
                    $last_update_time = max($last_update_time, date('Y-m-d H:i:s',$customer_list_2[$customer_id]['update_at']));
                }
            }

            if($is_disable == 0){
                // $res  = $res->toArray();
                echo __LINE__,':禁用客户 log,';
                return [
                    'is_disable'   => true,
                    'disable_time' => $this->get_lost_date($last_update_time, $customer_start_date),
                    'disable_status_time' => $last_update_time,
                ];
            }

        }else{
            echo 'before:';
            //2021-11-17 之前的判断方式
            //根据客户状态判断是否禁用
            $is_disable       = 0;
            $last_update_time = 0;
            foreach ($customer_list as $customer_info) {
                $is_disable       = $is_disable + $customer_info['status'];
                $last_update_time = max($last_update_time, $customer_info['update_at']);
            }

            if($is_disable == 0){
                $max_date = BillProductIncomeV2::select([DB::raw('max(date) as max_date')])->whereIn('apikey', $apikeys)->first()->toArray();
                $max_date = $max_date['max_date'];
                if($max_date){
                    echo __LINE__, ':禁用客户 有调用,';
                    $max_date = date('Y-m-d h:i:s', strtotime($max_date));
                }else{
                    echo __LINE__, ':禁用客户 无调用,';
                    $max_date = $customer_start_date;
                }

                return [
                    'is_disable'          => true,
                    'disable_time'        => $this->get_lost_date($max_date, $customer_start_date),
                    'disable_status_time' => date('Y-m-d', strtotime($max_date)),
                ];
            }
        }

        // > 2021-11-19
        if($date_timestamp >= **********) {
            $is_disable       = 0;
            $last_update_time = 0;
            foreach($apikeys as $apikey) {
                $res = $this->cache_log_data['account_edit-' . $apikey . '-' . $date_timestamp] ?? [];
                if($res) {
                    $is_disable       = $is_disable + $res['status'];
                    $last_update_time = max($last_update_time, $res['created_at']);
                }else{
                    $is_disable       = $is_disable + $account_list_2[$apikey]['status'];
                    $last_update_time = max($last_update_time,  date('Y-m-d H:i:s',$account_list_2[$apikey]['update_at']));
                }
            }
            if($is_disable == 0){
                echo __LINE__,':禁用帐号 log,';
                return [
                    'is_disable'   => true,
                    'disable_time' => $this->get_lost_date($last_update_time, $customer_start_date),
                    'disable_status_time' => $last_update_time,
                ];
            }
        }else {
            $is_disable       = 0;
            $last_update_time = 0;

            foreach ($account_list as $account_info) {
                $is_disable       = $is_disable + $account_info['status'];
                $last_update_time = max($last_update_time, $account_info['update_at']);
            }
            if ($is_disable == 0){
                $max_date = BillProductIncomeV2::select([DB::raw('max(date) as max_date')])->whereIn('apikey', $apikeys)->first()->toArray();
                $max_date = $max_date['max_date'];
                if($max_date){
                    echo __LINE__, ':禁用帐号 有调用,';
                    $max_date = date('Y-m-d h:i:s',$max_date);
                }else{
                    echo __LINE__, ':禁用帐号 无调用,';
                    $max_date = $customer_start_date;
                }

                return [
                    'is_disable'          => true,
                    'disable_time'        => $this->get_lost_date($max_date, $customer_start_date),
                    'disable_status_time' => date('Y-m-d', $last_update_time),
                ];
            }
        }

        $is_disable           = 0;
        $last_update_time     = 0;
        $account_ids          = array_column($account_list, 'account_id');
        $account_product_list = AccountProduct::whereIn('account_id', $account_ids)->where([['create_at', '<=', $date_timestamp]])->get()->toArray();
        $account_product_ids  = [];
        foreach ($account_product_list as $ap_info) {
            $is_disable = $is_disable + $ap_info['status'];
            $account_product_ids[] = $ap_info['product_id'];
            $last_update_time = max($last_update_time, $ap_info['update_at']);
        }

        if ($is_disable == 0) {
            $last_call_date = BillProductIncomeV2::whereIn('apikey', $apikeys)->whereIn('call_product_id', $account_product_ids)->where([['date', '<=', $fmt_date]])->orderBy('date', 'desc')->first();
            if ($last_call_date) { //电话邦
                $last_call_date->toArray();
                $last_call_date = $last_call_date['date'];
            } else {
                //如果没调用 使用创建时间
                $last_call_date = $customer_start_date;
            }

            echo __LINE__, ':全部产品禁用';
            return [
                'is_disable'          => true,
                'disable_time'        => $this->get_lost_date(date('Y-m-d H:i:s', strtotime($last_call_date)), $customer_start_date),
                'disable_status_time' => date('Y-m-d', strtotime($last_call_date)),
            ];
        } else {
            //判断是否是5个月渠道客户
            $is_source_customer = BillProductIncomeV2::whereIn('apikey', $apikeys)->where([['source', '!=', 0], ['date', '<=', $fmt_date]])->first();

            if (!$is_source_customer) { //电话邦
                echo __LINE__, ':非渠道 未禁用全部产品 不流失';
                return $def_res;
            } else { //渠道
                //创建后一年为新客户之后为老客户, 渠道客户成为老客户后5个月没调用才可能为禁用
                $customer_start_date_add_17_month = date('Ymd', strtotime('+17 month', strtotime($customer_start_date)));
                if ($date <= $customer_start_date_add_17_month) {
                    // 没到17个月
                    echo __LINE__, ':渠道小于17个月 无流失,';
                    return $def_res;
                }

                // 获取上次调用时间
                $max_date = BillProductIncomeV2::select([DB::raw('max(date) as max_date')])->whereIn('apikey', $apikeys)->where([['source', '!=', 0], ['date', '<=', $fmt_date]])->first()->toArray();
                $max_date  = $max_date['max_date'];

                $max_date_timestamp = strtotime($max_date);
                $max_date_ymd = date('Ymd', $max_date_timestamp);

                $old_date_ymd = date('Ymd',strtotime('+1 year',strtotime($customer_start_date)));

                //当前脚本对比时间
                $diff_seconds = strtotime($old_date_ymd) - $max_date_timestamp; //成为老客户时间 - 最后调用时间
                $five_month_seconds = strtotime(date('Ym01', strtotime('+5 month', $max_date_timestamp))) - $max_date_timestamp;

                if ($diff_seconds > $five_month_seconds) {
                    echo __LINE__, ':渠道超过5个月,';
                    // 最后的调用时间在成为客户之后
                    if ($max_date_ymd > $old_date_ymd) {
                        // 应为最后的权责时间的下一个月
                        $max_date_timestamp = $max_date_timestamp + $five_month_seconds;
                        echo __LINE__, ':最后调用时间在成为老客户之后,';
                        return [
                            'is_disable'          => true,
                            'disable_time'        => $this->get_lost_date(date('Y-m-d H:i:s', $max_date_timestamp), $customer_start_date),
                            'disable_status_time' => null,
                        ];
                    } else {
                        echo __LINE__, ':最后调用时间在成为老客户之前,';
                        $old_date_ymd = date('Y-m-d H:i:s', strtotime('+5 month', strtotime($old_date_ymd)));
                        return [
                            'is_disable'          => true,
                            'disable_time'        => $old_date_ymd,
                            'disable_status_time' => null,
                        ];
                    }
                }else{
                    echo __LINE__,':调用时间小于5个月';
                }
                //如果不存在调用或5个月内有调用，则不是禁用
            }
        }

        echo __LINE__,':新客户不流失,';
        return $def_res;
    }

    /**
     *
     * 如果流失时间是新客户 -> 流失时间为 最早计费时间 + 一年
     *
     * @param string $lost_date 流失时间 Y-m-d H:i:s
     * @param string $min_config_date 配置的最早计费时间 Ymd
     *
     * @return string
     * <AUTHOR> 2025-05-19 19:39:38
     */
    private function get_lost_date($lost_date, $min_config_date) {
        echo 'get_lost_date:',date('Ymd',strtotime($lost_date)), ' ' ,$min_config_date,PHP_EOL;
        if(date('Ymd',strtotime($lost_date)) - $min_config_date < 10000){
            $lost_date = date('Y-m-d H:i:s',strtotime('+1 year',strtotime($min_config_date)));
        }
        return $lost_date;
    }


    /**
     * 导出csv文件
     *
     * @param $header
     * @param $data
     * @param $file_name
     *
     * @return void
     * <AUTHOR> 2024-09-24 18:34:54
     */
    private function out_csv($header,$data,$file_name){
        $file_path = storage_path()."/logs/".$file_name.".csv";
        echo "file path : ",$file_path,PHP_EOL;

        $file = fopen($file_path, "w+");
        //bom
        fwrite($file, "\xEF\xBB\xBF");

        fputcsv($file, $header);

        foreach($data as $info){
            fputcsv($file, $info);
        }

        fclose($file);
    }
}
