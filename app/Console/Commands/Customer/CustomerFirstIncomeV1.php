<?php


namespace App\Console\Commands\Customer;

use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Product;
use App\Models\Remit;
use Illuminate\Console\Command;
use App\Models\BillCost;
use App\Models\BillProductIncome;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\Account;
use App\Models\CustomerExpend;

//已废弃 暂时留着备用
class CustomerFirstIncomeV1 extends Command
{
    protected $signature = 'customer_first_income_v1
    {--limit_month= : 是否限定月份（0不限定 、1限定）默认0}
    {--month= : 查看日期(格式Y-m）默认当前日期}';

    public $degree = 6;
    public $filePath = '';//存放结果的日志文件
    public $fileDetailPath = '';//存放明细结果的日志文件
    public $start_date;
    public $end_date;
    public $current_month;//当前月
    public $find_start_month;//查找边界月份
    public $first_start_month = 202107;//首次开始比较的日期
    public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];
    public $filter_apikey = [];
    public $data = [];
    public $customer_product_cost = [];
    public $customer_product_income = [];

    /**
     * @throws \Exception
     */
    public function handle()
    {

        try{
            //当根据产品id等参数查询时，到款是不准确的，因为认款表中没有产品维度
            $params = $this->getParams();

            $this->getDateRange($params['limit_month'], $params['month']);
            //dd($this->start_date, $this->end_date);
            $list = BillProductIncome::getMonthIncome($this->start_date, $this->end_date, $params);
            $expend_data = $this->getExpend($params);   //特殊消耗
            $cost_data = $this->getCost($params); //成本 普通成本
            $adjust_cost_data = $this->getAdjustCost($params); //客户成本调整
            $channel_adjust_cost_data = $this->getChannelAdjustCost($params);//渠道成本调整
            $fixed_cost_data = $this->getChannelFixedCost($params);//固定费用成本调整

            //dd($this->current_month, $list);

            $customer_month_tmp = [];
            foreach($list as $key => $val){

                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
                if(empty($customer_id) || is_array($customer_id)){
                    dd($val['apikey']."缓存异常");
                }
                $total_money = $this->customer_product_income[$customer_id][$val['product_id']]['income']??0;
                $this->customer_product_income[$customer_id][$val['product_id']]['income'] = bcadd($total_money, $val['s_money'], $this->degree);
                $unique_key = $customer_id.'_'.$val['product_id'];
                if(!in_array($unique_key, $customer_month_tmp)){
                    $customer_month_tmp[] = $unique_key;
                }
            }

            //dd($customer_month_tmp);

            $this->formatIncomeData($expend_data, $customer_month_tmp);
            $this->formatCostData($cost_data, $adjust_cost_data, $channel_adjust_cost_data, $fixed_cost_data, $customer_month_tmp);

            //dd($customer_month_tmp);
            $first_arr = [];
            foreach($customer_month_tmp as $val){
                $item = explode('_', $val);
                $customer_id = $item[0];
                $product_id = $item[1];
                $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
                if($father_id == 0){
                    $father_id = $product_id;
                }

                //判断是否首次全责收入
                $this->findStartMonth();

                $check = $this->checkFirstIncome($customer_id, $product_id);

                $arr = [
                    'customer_id' => $customer_id,
                    'product_id' => $product_id,
                    'father_id' => $father_id,
                ];
                $income = $this->customer_product_income[$customer_id][$product_id]['income']??0;
                $cost = $this->customer_product_cost[$customer_id][$product_id]['cost'] ?? 0;
                $profit = bcsub($income, $cost, $this->degree);
                if($check){
                    $arr['new_income'] = $income;
                    $arr['new_cost'] = $cost;
                    $arr['new_profit'] = $profit;
                    $arr['old_income'] = 0;
                    $arr['old_cost'] = 0;
                    $arr['old_profit'] = 0;
                }else{
                    $arr['new_income'] = 0;
                    $arr['new_cost'] = 0;
                    $arr['new_profit'] = 0;
                    $arr['old_income'] = $income;
                    $arr['old_cost'] = $cost;
                    $arr['old_profit'] = $profit;
                }

                $this->data[] = $arr;

            }

            $this->formatAllData($params);
            dd('ok');

        }catch (\Exception $e){
            dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendCommandExceptionNotice($this, $e);

        }

        $this->output->success("成功执行");

    }


    public function getParams(){
        $params = [];

        $params['limit_month'] = $this->input->getOption('limit_month') ?: 0;
        if(empty($params['limit_month'])){
            $params['limit_month'] = 0;
        }
        $params['month'] = $this->input->getOption('month') ?: date('Y-m');

        $this->filePath = storage_path()."/logs/first_income_customer_".date('Ymd').".log";
        $this->fileDetailPath = storage_path()."/logs/first_income_customer_detail".date('Ymd').".log";
        //dd($filePath);
        if(file_exists($this->filePath)){
            unlink($this->filePath);
        }

        if(file_exists($this->fileDetailPath)){
            unlink($this->fileDetailPath);
        }

        $params['filter_customer'] = $this->filter_customer;
        $this->filter_apikey = $params['filter_apikey'] = Account::getApikeysByCustomerIdsNew($this->filter_customer);
        if(isset($params['father_id']) && !empty($params['father_id'])){
            $params['product_ids'] = array_column(Product::getChildProduct([$params['father_id']]), 'product_id');
        }

        if(isset($params['customer_id']) && !empty($params['customer_id'])){
            $params['apikey'] = Account::getApikeysByCustomerIdsNew([$params['customer_id']]);
        }

        return $params;
    }


    public function getCost($params){

        return BillCost::getCostByDate($this->start_date, $this->end_date, $params);

    }

    public function getAdjustCost($params){

        return CustomerBillAdjust::getCostByDate($this->start_date, $this->end_date, $params);

    }

    public function getChannelAdjustCost($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        return ChannelAccountAdjust::getCostByDate($start_date, $end_date, $params);

    }

    public function getChannelFixedCost($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        return ChannelAccountFixedFee::getCostByDate($start_date, $end_date, $params);
    }

    public function getExpend($params){
        $start_date = date('Y-m-d', strtotime($this->start_date));
        $end_date = date('Y-m-d', strtotime($this->end_date));

        $expend_data['sub'] = CustomerExpend::getExpendByDate($start_date, $end_date, array_merge($params,['type' => 1]));
        $expend_data['add'] = CustomerExpend::getExpendByDate($start_date, $end_date, array_merge($params,['type' => 2]));

        return $expend_data;
    }

    public function formatAllData($params){
        $arr = [];
        $customer = [];

        array_multisort(array_column($this->data, 'father_id'), SORT_DESC, $this->data);

        foreach($this->data as $val){

            $father_name = RedisCache::instance('productId_productName_mapping')->get($val['father_id']);
            $product_name = RedisCache::instance('productId_productName_mapping')->get($val['product_id']);

            $total_new_income = $arr[$val['customer_id']]['new_income']??0;
            $arr[$val['customer_id']]['new_income'] = bcadd($total_new_income, $val['new_income'], $this->degree);

            $total_new_cost = $arr[$val['customer_id']]['new_cost']??0;
            $arr[$val['customer_id']]['new_cost'] = bcadd($total_new_cost, $val['new_cost'], $this->degree);

            $total_new_profit = $arr[$val['customer_id']]['new_profit']??0;
            $arr[$val['customer_id']]['new_profit'] = bcadd($total_new_profit, $val['new_profit'], $this->degree);

            $total_old_income = $arr[$val['customer_id']]['old_income']??0;
            $arr[$val['customer_id']]['old_income'] = bcadd($total_old_income, $val['old_income'], $this->degree);

            $total_old_cost = $arr[$val['customer_id']]['old_cost']??0;
            $arr[$val['customer_id']]['old_cost'] = bcadd($total_old_cost, $val['old_cost'], $this->degree);

            $total_old_profit = $arr[$val['customer_id']]['old_profit']??0;
            $arr[$val['customer_id']]['old_profit'] = bcadd($total_old_profit, $val['old_profit'], $this->degree);

            $total_income = $arr[$val['customer_id']]['total_income']??0;
            $new_old_income = bcadd($val['new_income'], $val['old_income'], $this->degree);
            $arr[$val['customer_id']]['total_income'] = bcadd($total_income, $new_old_income, $this->degree);

            $total_cost = $arr[$val['customer_id']]['total_cost']??0;
            $new_old_cost = bcadd($val['new_cost'], $val['old_cost'], $this->degree);
            $arr[$val['customer_id']]['total_cost'] = bcadd($total_cost, $new_old_cost, $this->degree);

            if(!in_array($val['customer_id'], $customer)){
                $customer[] = $val['customer_id'];
            }

            $str = $val['customer_id']."\t".$father_name."\t".$product_name."\t".
                $val['new_income']."\t".$val['new_cost']."\t".
                $val['old_income']."\t".$val['old_cost']."\t".
                $val['new_profit']."\t".$val['old_profit'];

            file_put_contents($this->fileDetailPath, $str.PHP_EOL, FILE_APPEND);

        }

        //客户认款单数据
        $remit_list = Remit::getMoneyByDate($this->start_date, $this->end_date, $params);
        foreach($remit_list as $val){
            if(!in_array($val['customer_id'], $customer)){
                $customer[] = $val['customer_id'];
            }
        }
        $remit_list = array_column($remit_list, 's_money', 'customer_id');

        $dept = SystemDept::getAllDeptInfo();
        $dept = array_column($dept, 'dept_name', 'dept_id');
        foreach($customer as $customer_id){
            $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
            $customer_info = Customer::getCustomerInfo($customer_id);
            $sys_user = SystemUser::getPhoneByUserName($customer_info['salesman']);
            $area = $dept[$sys_user['dept_id']]??'unknow';
            $daokuan = $remit_list[$customer_id]??0;
            $total_income = $arr[$customer_id]['total_income']??0;
            $total_cost = $arr[$customer_id]['total_cost']??0;
            $new_income = $arr[$customer_id]['new_income']??0;
            $new_cost = $arr[$customer_id]['new_cost']??0;
            $old_income = $arr[$customer_id]['old_income']??0;
            $old_cost = $arr[$customer_id]['old_cost']??0;
            $new_profit = $arr[$customer_id]['new_profit']??0;
            $old_profit = $arr[$customer_id]['old_profit']??0;

            $str = $customer_id."\t".$customer_name."\t".$customer_info['company']."\t".
                $customer_info['salesman']."\t".$area."\t".
                $total_income."\t". $total_cost."\t".
                $new_income."\t". $new_cost."\t".
                $old_income."\t". $old_cost."\t".
                $new_profit."\t". $old_profit."\t".
                $daokuan;

            file_put_contents($this->filePath, $str.PHP_EOL, FILE_APPEND);

        }


    }


    public function formatIncomeData($expend_data, &$customer_month_tmp){
        foreach($expend_data['add'] as $val){
            $total_income = $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] ?? 0;
            $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] = bcadd($total_income, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($expend_data['sub'] as $val){
            $total_income = $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] ?? 0;
            $this->customer_product_income[$val['customer_id']][$val['product_id']]['income'] = bcsub($total_income, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        return true;
    }

    public function  formatCostData($cost_data, $adjust_cost_data, $channel_adjust_cost_data, $fixed_cost_data, &$customer_month_tmp){

        foreach($cost_data as $val){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
            $total_cost = $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $customer_id.'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($adjust_cost_data as $val){
            $total_cost = $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($channel_adjust_cost_data as $val){
            $total_cost = $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$val['customer_id']][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $val['customer_id'].'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        foreach($fixed_cost_data as $val){
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($val['apikey']);
            $total_cost = $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] ?? 0;
            $this->customer_product_cost[$customer_id][$val['product_id']]['cost'] = bcadd($total_cost, $val['s_money'], $this->degree);
            $unique_key = $customer_id.'_'.$val['product_id'];
            if(!in_array($unique_key, $customer_month_tmp)){
                $customer_month_tmp[] = $unique_key;
            }
        }

        return true;
    }


    public function getDateRange($limit = 1, $month){
        $this->current_month = str_replace('-', '', $month);//当前月
        $this->end_date = $this->getLastDay($month.'-01');//查看日期所在月的最后一天
        if($limit == 1){//限定月份
            $this->start_date = date('Ymd', strtotime($month.'-01'));
        }else{
            $this->start_date = $this->getMonth(11, $this->current_month).'01';
        }

        return true;
    }

    /**
     * 获取某个日期所在月的最后一天
     * @param $date
     * @return false|string
     */
    public function getLastDay($date)
    {
        $firstday = date('Y-m-01', strtotime($date));
        $lastday = date('Ymd', strtotime("$firstday +1 month -1 day"));
        return $lastday;
    }

    //获取指定日期的前后几个月
    public function getMonth($sign, $month = '')
    {
        //得到系统的年月
        $tmp_date = empty($month) ? date("Ym") : $month;
        //切割出年份
        $tmp_year = substr($tmp_date,0,4);
        //切割出月份
        $tmp_mon = substr($tmp_date,4,2);
        // 得到当前月份的下几月
        $tmp_nextmonth = mktime(0,0,0,$tmp_mon+$sign,1,$tmp_year);
        // 得到当前月份的前几月
        $tmp_forwardmonth = mktime(0,0,0,$tmp_mon-$sign,1,$tmp_year);
        return $fm_next_month = date("Ym",$tmp_forwardmonth);
    }

    public function findStartMonth(){
        //1 先根据当前月份判断
        //如果统计的当前月份比首次开始月份日期都早 都按老项目处理;
        if($this->first_start_month > $this->current_month){
            $this->find_start_month = $this->first_start_month;
            return;
        }

        $before_month = $this->getMonth(11, $this->current_month);

        if($this->first_start_month > $before_month){
            $this->find_start_month = $this->first_start_month;
            return;
        }

        $this->find_start_month = $before_month;

        return;
    }

    public function checkFirstIncome($customer_id, $product_id){
        $customer_apikeys = Account::getApikeysByCustomerIdsNew([$customer_id]);
        //不含边界月
        $firstIncome = BillProductIncome::checkFirstIncome($this->find_start_month.'01', $customer_apikeys, $product_id);
        if($firstIncome == 0){//新项目
            $is_new = true;
        }else{
            //老项目
            $is_new = false;
        }


        return $is_new;
    }


    public function getEveryMonthIncome($customer_id, $father_id, $product_id, $month, &$customer_tmp, &$customer_apikeys, &$first_arr, &$sort_key){
        $first_arr[$sort_key]['customer_id'] = $customer_id;
        $first_arr[$sort_key]['father_id'] = $father_id;
        $first_arr[$sort_key]['product_id'] = $product_id;

        $first_arr[$sort_key]['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
        $first_arr[$sort_key]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
        $first_arr[$sort_key]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($product_id);
        $first_arr[$sort_key]['s_month'] = $month;
        //正常收入
        $income = $customer_tmp[$customer_id][$father_id][$product_id][$month]['total_money'];
        $condition['customer_id'] = $customer_id;
        $condition['father_id'] = $father_id;
        $condition['product_id'] = $product_id;
        $condition['s_date'] = $month.'01';
        $condition['e_date'] = $this->getLastDay($month.'01');
        //特殊消耗
        $incomesub = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 1]));
        $incomeadd = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 2]));
        $incomesub = $incomesub[0]['s_money'] ?? 0;
        $incomeadd = $incomeadd[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['income'] = bcsub(bcadd($income, $incomeadd, $this->degree), $incomesub, $this->degree);

        $condition2['apikey'] = $customer_apikeys;
        $condition2['product_id'] = $product_id;
        $condition2['s_date'] = $condition['s_date'];
        $condition2['e_date'] = $condition['e_date'];
        //普通成本
        $cost = BillCost::getBetweenDate($condition2);
        $cost = $cost[0]['s_money'] ?? 0;

        $condition3['customer_id'] = $customer_id;
        $condition3['product_id'] = $product_id;
        $condition3['s_date'] = $condition['s_date'];
        $condition3['e_date'] = $condition['e_date'];
        //客户成本调整
        $adjust_cost = CustomerBillAdjust::getBetweenDate($condition3);
        $adjust_cost = $adjust_cost[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['cost'] = bcadd($cost, $adjust_cost, $this->degree);

        $customer_info = Customer::getCustomerInfo($customer_id);
        $first_arr[$sort_key]['salesman'] = $customer_info['salesman'];
        $sys_user = SystemUser::getPhoneByUserName($customer_info['salesman']);
        $dept_info = SystemDept::getInfoByDeptId($sys_user['dept_id']);
        $first_arr[$sort_key]['area'] = $dept_info['dept_name'];

        $sort_key++;

        return true;
    }



}