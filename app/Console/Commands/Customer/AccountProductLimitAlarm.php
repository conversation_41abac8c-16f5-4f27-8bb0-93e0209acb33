<?php

namespace App\Console\Commands\Customer;

use App\Models\Account;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;
use App\Models\AccountProductLimit;
use App\Models\StatisticsCustomerUsage;

class AccountProductLimitAlarm extends Command
{
    protected $signature = 'account:product_limit
    {--addressee= : 收件人，多个收件人以,隔开}';

    protected $description = '客户产品限量';

    protected $addressee = [
        '<EMAIL>',
        '<EMAIL>',
    ];

    public function handle()
    {
        $start_time = strtotime('3 days');
        $end_time = strtotime('4 days');
        $limit_list = AccountProductLimit::where([['end_time', '>=', $start_time], ['end_time', '<', $end_time]])->get()->toArray();

        if (!$limit_list) return;

        $addressee = $this->option('addressee');
        if ($addressee) {
            $this->addressee = explode(',', $addressee);
        }

        $apikey = array_column($limit_list, 'apikey');
        $account_list = Account::whereIn('apikey', $apikey)->get()->toArray();
        $account_list = array_column($account_list, 'account_name', 'apikey');
        $email = [];
        foreach ($limit_list as $item) {
            $start = date('Ymd', $item['start_time']);
            $end = date('Ymd', $item['end_time']);
            $total = StatisticsCustomerUsage::getUsageByApikeyProductDate($item['apikey'], $item['product_id'], $start, $end);
            if ($item['total_limit'] > 0 && $total < $item['total_limit']) {
                $rate = ($total / $item['total_limit'] * 100) . '%';
                $email[] = [$item['apikey'], $account_list[$item['apikey']], '限量：' . $item['total_limit'] . '，已使用：' . $total . '，占比：' . $rate];
            }
        }

        $title = "近3天客户测试限量预警";

        $header = ['apikey', '账号名称', '调用情况'];
        $html = $this->mailTemplate($header, $email);

        $sendMailService = new SendMailService();

        $addressee = array_map(function ($email) {
            return compact('email');
        }, $this->addressee);

        $sendMailService->setSubject($title)
            ->setContent($html)
            ->setAddressee($addressee)
            ->setFromName('金融后台项目组')
            ->send();
    }

    protected function mailTemplate($header, $data)
    {
        $table_style = 'width="90%"  border="1" bordercolor="#000" cellspacing="0" cellpadding="10"';
        $tr_style = 'style="background:#D9D9D9"';
        $_table = '<table ' . $table_style . '>content</table>';

        //标题
        $_td = '';
        foreach ($header as $td) {
            $_td .= '<td>' . $td . '</td>';
        }
        $_tr = '<tr ' . $tr_style . '>' . $_td . '</tr>' . PHP_EOL;

        //内容
        foreach ($data as $row) {
            $_td = '';
            foreach ($row as $td) {
                $_td .= '<td>' . $td . '</td>';
            }
            $_tr .= '<tr>' . $_td . '</tr>' . PHP_EOL;
        }
        return str_replace('content', $_tr, $_table);
    }
}