<?php

namespace App\Console\Commands\Customer;

use App\Models\BalanceWarningBasicsModel;
use App\Models\BalanceWarningModel;
use App\Models\Crs\SystemUser;
use App\Models\Customer;
use App\Models\CustomerAlarmProcessing;
use App\Models\DingdingCustomerApprovalProcess;
use App\Providers\RedisCache\RedisCache;
use App\Utils\AliCall;
use App\Utils\DingDing;
use Illuminate\Console\Command;
use App\TraitUpgrade\WechatExceptionTrait;

/**
 * Class BalanceWarningByWechat 钉钉余额预警 (代码中有wechat字样，名字命名显然不对，历史原因暂不用理会)
 * @package App\Console\Commands\Customer
 */
class BalanceWarningByWechat extends Command
{
    use WechatExceptionTrait;
	protected $signature = "balance_warning:send_wechat
	{--disregard : 不计入重复记录，下次存在同样处于阈值内的预警数据时，不会被过滤}
	";
	
	
	protected $description = "通过钉钉进行余额预警";
	/**
	 * @var array 本次余额预警所使用的余额基础数据
	 */
	private $balanceWarningBasicsInfo;
	
	/**
	 * @var array 预付款客户ID
	 */
	private $advanceCustomerIds;
	
	/**
	 * @var array 后付款客户ID
	 */
	private $postPaymentCustomerIds;
	
	/**
	 * @var array
	 */
	private $balanceWarningInfo;

	private  $dingding;
	
	/**
	 * @var array 预警级别
	 */
	private $warningLevel = [
		30 => '关注',
		7 => '关注',
		7  => '严重',
		3  => '极其严重',
	];

	/**
	 * @var array 预警级别
     *
	 */
	private  $alarmtype = [
//       1 => '[30,7)',
//       2 => '[7,0)',
//       3 => '[0,-15)',
//       4 => '[-15,-30)',
//       5 => '[-30,-60)',
//       6 => '[-60,-∞)',
        1 => '关注',
        2 => '严重',
        3 => '极其严重',
        4 => '极其严重',
        5 => '极其严重',
        6 => '极其严重',
    ];
	
	public function __construct()
	{
		parent::__construct();
        //查询预付费客户id
		$this->advanceCustomerIds       = $this->getAdvanceCustomerIds();
		//查询预付费用户 预计可用天数 <= 30天的客户
		$this->balanceWarningBasicsInfo = $this->getBalanceWarningBasicsInfo($this->advanceCustomerIds);
        //dingtalk
        $this->dingding = new DingDing(config('params.dingding.appkey'),config('params.dingding.secret'));
	}
	
	/**
	 * 获取预付款客户ID
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 10:11
	 *
	 * @return array
	 */
	private function getAdvanceCustomerIds()
	{
		return Customer::where('is_delete', 0)
					   ->where('payment_type', 1)
					   ->pluck('customer_id')
					   ->toArray();
	}
	
	/**
	 * 获取后付款客户ID
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 10:11
	 *
	 * @return array
	 */
	private function getPostPaymentCustomerIds()
	{
		return Customer::where('is_delete', 0)
					   ->where('payment_type', 2)
					   ->pluck('customer_id')
					   ->toArray();
	}
	
	/**
	 * 获取余额预警基础数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 18:24
	 *
	 * @return array
	 */
	private function getBalanceWarningBasicsInfo($advanceCustomerIds)
	{
		return  BalanceWarningBasicsModel::select(['customer_id', 'balance', 'days', 'surplus_credit', 'credit_balance'])
                                          ->whereIn('customer_id',$advanceCustomerIds)
                                         ->where('everyday_income', '>', 0)
                                         ->where('days','<=',30)
										 ->get()
										 ->toArray();
	}
	
	/**
	 * 获取历史的触发预警的记录数据（用于确认是否第一次触发）
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 11:06
	 *
	 * @return array
	 */
	private function getBalanceWarningInfo()
	{
		return BalanceWarningModel::select(['customer_id', 'days'])
								  ->get()
								  ->toArray();
	}
	
	public function handle()
	{
	    //对于客户可用天数预计小于三十天的进行处理
         $this->getCustomerWarning($this->balanceWarningBasicsInfo);
	}

	/*
	 * 对于可用天数小于30的客户进行处理
	 */
    private function getCustomerWarning($balanceWarningBasicsInfo)
    {
        if (empty($balanceWarningBasicsInfo)){
            return false;
        }
        array_walk($balanceWarningBasicsInfo,function ($item){
                $level = $this->getAlarmLevel($item['days']);

                $where = [
                    ['customer_id','=',$item['customer_id']],
                    ['type','=',2],  //余额
                    ['level','=',$level],
                    ['real_result','=',1],
                ];


                $process = DingdingCustomerApprovalProcess::getNotProcessed($where);

                if (!empty($process)){
                   return false;
                }
             $customer_info = Customer::getCustomerInfo($item['customer_id']);
             $user_id = $this->getDUserIdBySalesman($customer_info['salesman']);

              $dept_id = $this->getDeptId($user_id);

            if (empty($user_id)){
                return false;
            }
            $formValue = [
                [
                    "name" => "类型",
                    "value" => '客户余额',
                ],
                [
                    "name" => "客户",
                    "value" => $customer_info['name'],
                ],
                [
                    "name" => "剩余天数",
                    "value" => $item['days'],
                ]
            ];
            $approvers_v2 = [
                //一级审批 商务
                ['task_action_type'=>'NONE',
                    "user_ids"=> [
                        DINGDING_ZHANGREN_USERID,
                    ]],
                //二级审批  张韧
                ['task_action_type'=>'NONE',
                    "user_ids"=> [
                        // $user_id,
                        DINGDING_ZHANGREN_USERID,
                    ]]
            ];

            $cc = [
                'cc_position'=>'FINISH',
                'cc_list'=>DINGDING_YANGXUELI_USERID

            ];

             //钉钉报警 余额预警
            $process_instance_id =  $this->dingding->createProcessApprovers(DINGDING_ALARM_PROCESS_CODE,$user_id,$dept_id,$formValue,$approvers_v2,$cc);

            $insert_data = ['customer_id'=>$item['customer_id'],'type'=>2,'level'=>$level,'process_instance_id'=>$process_instance_id,'create_time'=>time()];

            //添加钉钉报警处理流程
            DingdingCustomerApprovalProcess::insert($insert_data);
        });
    }

    private function getCustomer($customer_id)
    {
        return Customer::select(['name'])
                         ->where('customer_id','=',$customer_id)
                         ->get()->toArray();
    }

    private function getWarning($type,$data)
    {
        $customer_id = '';
        $content = '';
        foreach ($data as $v){
            $customer_id .= $v['customer_id'].',';
            $content .= '【'.$v['customer_name'].'】'.$v['days'].'('.intval($v['balance']).')'.PHP_EOL;
        }
        $customer_id = rtrim($customer_id,',');
        $alarm_range = $this->getAlarmRange($type);
        return "报警信息：余额预警\n客户类型：预付费客户\n报警级别：{$this->alarmtype[$type]}\n报警范围：$alarm_range\n\n$content\n\n\n请处理：http://bj-yulore-alarm.dianhua.cn/wx.php?type=begin&itemId=107&aid=1&customer_id={$customer_id}&alarmtype={$type}";
    }

    private function getCustomerAlarmProcessing($customer_id,$type)
    {
        return CustomerAlarmProcessing::select()->where('customer_id','=',$customer_id)
                                                ->where('type','=',$type)
                                                ->where('status','=',1)
                                                ->orderBy('id','desc')
                                                ->limit(1)
                                                ->get()->toArray();

    }

    private function getAlarmLevel($days)
    {
        if ($days<=30 && $days >7){
            return 30;
        }elseif ($days<=7){
            return 7;
        }
    }


    private function getAlarmRange($type)
    {
        $return = '';
       switch ($type){
           case 1:
               $return = '[30,7)';
               break;
           case 2:
               $return = '[7,0)';
                break;
           case 3:
               $return = '[0,-15)';
               break;
           case 4:
               $return = '[-15,-30)';
               break;
           case 5:
               $return = '[-30,-60)';
               break;
           case 6:
               $return = '[-60,-∞)';
               break;
       }

        return  $return;
    }



    private  function  getDUserIdBySalesman($salesman)
    {
        $result = '';
        //获取销售的电话
        $phone = SystemUser::getPhoneByUserName($salesman)['phone'];
        //根据电话获取钉钉的userid
        if (!empty($phone)){
            $res =  $this->dingding->getUserIdByPhoneNumber($phone);
            if ($res['errcode'] == 0){
                $result =  $res['result']['userid'];
            }
        }
        return $result;
    }

    private  function  getDeptId($userid)
    {
        $dept_id = '';
        //根据userid 获取部门id
        $res =  $this->dingding->getDepIdByUserId($userid);
        if ($res['errcode'] == 0){
            $dept_id =  $res['result']['parent_list'][0]['parent_dept_id_list'][0];
        }
        $dept_id = !empty($dept_id) ? $dept_id : DEFAULT_DEPT_ID;

        return $dept_id;
    }


	/**
	 * 记录本次触发的数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 15:36
	 *
	 * @param $data array 触发预警的数据
	 *
	 * @return void
	 */
	private function writeBalanceWarningInfo($data, $days)
	{
		if ($this->input->getOption('disregard')) {
			return;
		}
		if ($days == 3) {
			return;
		}

		$data = array_map(function ($customer_id) use ($days) {
			return compact('customer_id', 'days');
		}, array_column($data, 'customer_id'));

		//入库数据
		BalanceWarningModel::insert($data);
	}

	/**
	 * 生成预付费客户的预警数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 14:47
	 *
	 * @param $data        array 预警数据
	 * @param $days        integer 预警阈值
	 *
	 * @return string
	 */
	private function createAdvanceCustomerWarningContent($data, $days)
	{
		$content = $this->getWarningTitle($days, '预付款客户');

		foreach ($data as $item) {
			$customer_id   = $item['customer_id'];
			$customer_name = RedisCache::instance('customerId_customerName_mapping')
									   ->get($customer_id);
			$days          = $item['days'];
			$balance       = round($item['balance'], 2);
			$content       .= "【{$customer_name}】{$days}($balance)\n";
		}

		return $content;
	}
	
	/**
	 * 获取预警标题头
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 15:19
	 *
	 * @param $days          integer 预警天数阈值
	 * @param $type          string 客户类型
	 *
	 * @return string
	 */
	private function getWarningTitle($days, $paymentType)
	{
		return "报警信息：余额预警\n客户类型：{$paymentType}\n触发阈值：{$days}天\n报警级别：{$this->warningLevel[$days]}\n\n";
	}
	

	
	/**
	 * 清理预估可用天数超过30天的客户的预警记录
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 10:40
	 *
	 * @return void
	 */
	private function cleanMoreThirtyDaysBalanceWarningInfo()
	{
		//获取预估天数大于30天的客户
		$customer_ids = $this->getMoreThenDaysCustomerIds(30);
		
		if (empty($customer_ids)) {
			return;
		}
		
		//删除这个客户所有的记录（7/15/30天的都需要删除）
		BalanceWarningModel::whereIn('customer_id', $customer_ids)
						   ->delete();
	}


	
	/**
	 * 获取预估可用天数大于一定天数的客户ID
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 10:50
	 *
	 * @param $days integer 天数基准值
	 *
	 * @return array
	 */
	private function getMoreThenDaysCustomerIds($days)
	{
		return array_column($this->getMoreThenDaysBalanceWarningBasicsInfo($days), 'customer_id');
	}
	
	/**
	 * 获取预估可用天数大于一定天数的余额数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 11:12
	 *
	 * @param $days integer 天数基准值
	 *
	 * @return array
	 */
	private function getMoreThenDaysBalanceWarningBasicsInfo($days)
	{
		return array_filter($this->balanceWarningBasicsInfo, function ($item) use ($days) {
			if ($item['days'] > $days) {
				return true;
			}
			
			return false;
		});
	}


	/**
	 * 发送余额预警的内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 17:33
	 *
	 * @param $content string 余额预警的内容
	 *
	 * @return void
	 */
	private function send($content)
	{
//		sendWechatNotice($content, 'balance_warning');
		sendWechatNotice($content, 'balance_warning');
	}
	
}