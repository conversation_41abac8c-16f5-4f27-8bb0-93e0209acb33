<?php


namespace App\Console\Commands\Customer;

use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Product;
use App\Models\Remit;
use Illuminate\Console\Command;
use App\Models\BillCost;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\Account;
use App\Models\BillProductIncomeV2;
use App\Models\CustomerExpend;
use App\Models\ConfigPriceCustomer;
use App\Models\ConfigPriceInterface;
use App\Models\ClickHouse\RequestProductLog;
use App\TraitUpgrade\CurlTrait;

class ProductWeekIncome extends Command
{
    use CurlTrait;
    protected $signature = 'product_week_income
    {--father_id= : 父产品id}
    {--start_date= : 统计日期(格式Y-m-d）默认当前日期}
    {--end_date= : 统计日期(格式Y-m-d）默认当前日期}';

    public $degree = 6;
    public $filePath = '';//存放结果的日志文件
    public $fileDetailPath = '';//存放明细结果的日志文件
    public $start_date;
    public $end_date;
    public $apikeys;
    public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];
    public $filter_apikey = [];
    public $data = [];
    public $customer_product_cost = [];
    public $customer_product_income = [];
    //ck中carrier运营商和mysql中的运营商operator映射关系
    public $ck_carrier_map = [
        0 => 'CMCC',
        1 => 'CUCC',
        2 => 'CTCC',
        3 => 'CMCC',
    ];

    /**
     * @throws \Exception
     */
    public function handle()
    {

        try{
            #todo
            //$start_date = '2021-01-01';//周五 第1周
            $start_date = '2022-01-01';//周五 第1周
            #todo
            //$day = 4;//周五
            $day = 5;//周五
            $dijizhou = 1;
            $zhou_num = 0;
            $res = [];
            for($i = 365; $i > 0; $i--){
                $yushu = $day % 7;
                if($yushu == 0){//说明是周日 新的一周 计算这是第几周
                    //$zhou_num++;
                    //$dijizhou = $dijizhou + $zhou_num;
                    $dijizhou++;
                }
                $week = 7 - $yushu;//算出这是周几
                //echo date('Y-m-d', strtotime($start_date) - $day * 86400);
                #todo
                //$res[$dijizhou][] = date('Ymd', strtotime($start_date) + ($day - 4)*86400);
                $res[$dijizhou][] = date('Ymd', strtotime($start_date) + ($day - 5)*86400);

                $day++;
            }

            //dd($res[1], $res[2], $res[3]);
            $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];//羽乐科技内部、售前测试
            $filter_apikey = array_column(Account::whereIn('customer_id', $filter_customer)->get()->toArray(), 'apikey');

            $path = storage_path();
            $filePath = $path."/logs/".date('Ymd').'BMY_income.txt';
            $filePath2 = $path."/logs/".date('Ymd').'BMY_profit.txt';
            $father_id = 200;
            foreach($res as $key => $val){
                #todo
                //if($key<40){
                if($key>15){
                    continue;
                }

                $s_date = current($val);
                $e_date = end($val);
                $f_s_date = date('Y-m-d', strtotime($s_date));
                $f_e_date = date('Y-m-d', strtotime($e_date));

                #todo
                //$product_list = [202];
                //$product_list = [203, 216];
                //$product_list = [201, 213];
                $product_list = [204,205,206,207,208,211,212,214,215,230,217,231,218,900,219,220,232,233,234,237,235,221,222,238,310,311,312,313,314,315,316,317,901,318,239,240,320,321,322,323,324,325,326,327,328,329,330,331];

                $condition = [
                    'father_id' => $father_id,
                    'product_list' => $product_list,
                    's_date' => $s_date,
                    'e_date' => $e_date
                ];

                $condition2 = [
                    'father_id' => $father_id,
                    'product_list' => $product_list,
                    's_date' => $f_s_date,
                    'e_date' => $f_e_date
                ];

                $income_data = BillProductIncomeV2::getBetweenDate($condition, $filter_apikey);
                $income = $income_data[0]['s_money']??0;

                $cost_data = BillCost::getBetweenDate($condition, $filter_apikey);
                //ChannelAccountFixedFee 日期需要带-
                $fixed_cost_data = ChannelAccountFixedFee::getBetweenDate($condition2, $filter_apikey); //固定费用成本
                $customer_cost_data = CustomerBillAdjust::getBetweenDate($condition, $filter_customer);//客户成本调整
                //ChannelAccountFixedFee 日期需要带-
                $channel_cost_data = ChannelAccountAdjust::getBetweenDate($condition2, $filter_apikey);  //渠道成本调整(由于历史原因名字起的不符合业务语义，其实应该叫成本调整表)

                $cost = $cost_data[0]['s_money']??0;
                $fixed_cost = $fixed_cost_data[0]['s_money']??0;
                $customer_cost = $customer_cost_data[0]['s_money']??0;
                $channel_cost = $channel_cost_data[0]['s_money']??0;
                $total_cost = bcadd($cost, $fixed_cost, 2);
                $total_cost = bcadd($total_cost, $customer_cost, 2);
                $total_cost = bcadd($total_cost, $channel_cost, 2);

                //产品月度平账数据,分加减两部分
                $expend_data_sub = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 1]), $filter_customer);
                $expend_data_add = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 2]), $filter_customer);
                //返回全量且不重复的所有产品

                $expend_sub = $expend_data_sub[0]['s_money']??0;
                $expend_add = $expend_data_add[0]['s_money']??0;

                $show_income = bcadd($income, $expend_add, $this->degree);
                $show_income = bcsub($show_income, $expend_sub, 2);

                $rights_income = bcsub($show_income, $total_cost, 2);
                //$rights_income = bcadd($rights_income, $expend_add, $this->degree);
                //$rights_income = bcsub($rights_income, $expend_sub, 2);

                file_put_contents($filePath, "第".$key."周\t".$s_date.'-'.$e_date."\t".$show_income."\t".PHP_EOL, FILE_APPEND);
                file_put_contents($filePath2, "第".$key."周\t".$s_date.'-'.$e_date."\t".$rights_income."\t".PHP_EOL, FILE_APPEND);

            }

            dd('ok');

        }catch (\Exception $e){
            dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendCommandExceptionNotice($this, $e);

        }

        $this->output->success("成功执行");

    }




}