<?php

namespace App\Console\Commands\Customer;

use App\Models\Crs\SystemUser;
use App\Models\Customer;
use App\TraitUpgrade\CurlTrait;
use App\Utils\AliCall;
use Illuminate\Console\Command;
use App\Models\DingdingCustomerApprovalProcess;

class DingdingCustomerApprovalProcessOver extends Command
{
    //use  WechatExceptionTrait;
    use CurlTrait;
    protected $signature = 'ding:customer_approval_process_over';

    protected $description = '钉钉报警审批超过8小时未处理';

    protected  $type = [
           1=>'限量预警',
           2=>'余额预警',
           3=>'产品截止日期预警'
    ];

    public function handle()
    {
        $dingding_approvel_process_data = $this->getApprovelProcessData();
        $this->getProcessInstanceStatus($dingding_approvel_process_data);
    }

    //获取最新的待审批数据
    private function getApprovelProcessData()
    {
        $dingding_approvel_process_data = DingdingCustomerApprovalProcess::getDingProcessing();
        return $dingding_approvel_process_data;
    }

    //获取更新审批状态
    private function getProcessInstanceStatus($dingding_approvel_process_data)
    {
        $hour = date('H');

         foreach ($dingding_approvel_process_data as $v){
             if (time() - $v['create_time'] > 8 * 3600){
                 $type = $this->type[$v['type']];
                 $customer_info = Customer::getOneItemByCondition(['customer_id'=>$v['customer_id']]);
                 $customer_name = $customer_info['name'];
                 $phone = $this->getPhoneBySalesman($customer_info['salesman']);

                  $str = "报警类型：客户".$type."超过8小时未处理\n\n\n客户：$customer_name\n";

                   //发送微信报警
                   $this->post(SEND_WECHAT_ALARM_URL,['content'=>$str]);

                   if ($hour>22 || $hour < 7){
                       continue;
                   }

                   $type = '客户'.$type;
                   AliCall::call($phone,['type'=>$type],DINGTALK_PHONE_CODE);

             }
         }
    }


    private  function  getPhoneBySalesman($salesman)
    {

        $phone = SystemUser::getPhoneByUserName($salesman)['phone'];

        $phone = empty($phone)? ALARM_PHONE_LC : $phone;


        return $phone;
    }

}