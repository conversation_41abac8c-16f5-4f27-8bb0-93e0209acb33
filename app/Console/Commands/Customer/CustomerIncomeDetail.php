<?php


namespace App\Console\Commands\Customer;

use App\Models\BillCustomerIncome;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Product;
use App\Models\Remit;
use Illuminate\Console\Command;
use App\Models\BillCost;
use App\Models\BillProductIncome;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\Account;
use App\Models\CustomerExpend;

class CustomerIncomeDetail extends Command
{
    //客户详细收入情况
    protected $signature = 'customer_detail_income
    {--limit_month= : 是否限定月份（0不限定 、1限定）默认0}
    {--month= : 查看日期(格式Y-m）默认当前日期}';

    public $degree = 6;
    public $filePath = '';//存放结果的日志文件
    public $fileDetailPath = '';//存放明细结果的日志文件
    public $start_date = ********;
    public $end_date = ********;
    public $current_month;//当前月
    public $find_start_month;//查找边界月份
    public $first_start_month = 202107;//首次开始比较的日期
    public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];
    public $filter_apikey = [];
    public $data = [];
    public $customer_product_cost = [];
    public $customer_product_income = [];

    /**
     * @throws \Exception
     */
    public function handle()
    {

        try{

            //$params = $this->getParams();
            $this->filePath = storage_path()."/logs/all_customer_income".date('Ymd').".log";

            $list = Customer::getListByCondition([], ['customer_id', 'name']);
            foreach ($list as $val){
                $customer_id = $val['customer_id'];
                $customer_name = $val['name'];

                if(in_array($customer_id, $this->filter_customer)){
                    continue;
                }

                $apikey_arr = Account::getApikeysByCustomerIdsNew([$customer_id]);
                $apikey_arr = array_filter($apikey_arr);

                $output = $customer_id."\t".$customer_name."\t";
                $income_tmp = [
                    '2017' => 0,
                    '2018' => 0,
                    '2019' => 0,
                    '2020' => 0,
                    '2021' => 0,
                    '2022' => 0,
                ];

                if(empty($apikey_arr)){
                    $output = $output .  "no\t" . "no\t" . "no\t";

                }else {

                    $use_product = BillCustomerIncome::checkCustomerUseMainProduct($this->start_date, $this->end_date, $apikey_arr);
                    if (empty($use_product)) {
                        $use_product_name = 'no';
                    } else {
                        $use_product_name = '';
                        foreach ($use_product as $use) {
                            $father_id = $use['father_id'];
                            $father_name = RedisCache::instance('productId_productName_mapping')->get($father_id);

                            $use_product_name .= $father_name . '/';
                        }

                    }

                    $first = BillCustomerIncome::checkCustomerIncome($this->start_date, $this->end_date, $apikey_arr, 'first');
                    $first_income = 'no';
                    if (!empty($first)) {
                        $first_income = $first['date'];
                    }

                    $last = BillCustomerIncome::checkCustomerIncome($this->start_date, $this->end_date, $apikey_arr, 'last');
                    $last_income = 'no';
                    if (!empty($last)) {
                        $last_income = $last['date'];
                    }
                    $output = $output . $use_product_name . "\t" . $first_income . "\t" . $last_income . "\t";

                    $income_list = BillCustomerIncome::getYearIncome($this->start_date, $this->end_date, $apikey_arr);
                    foreach ($income_list as $v_income){
                        $income_tmp[$v_income['s_year']] = $v_income['s_money'];
                    }
                }

                $expend_sub = CustomerExpend::getCustomerYearExpend($this->start_date, $this->end_date, 1, [$customer_id]);
                $expend_add = CustomerExpend::getCustomerYearExpend($this->start_date, $this->end_date, 2, [$customer_id]);
                foreach ($expend_sub as $v_sub){
                    $income_tmp[$v_sub['s_year']] = bcsub($income_tmp[$v_sub['s_year']], $v_sub['s_money'], 2);
                }

                foreach ($expend_add as $v_add){
                    $income_tmp[$v_add['s_year']] = bcadd($income_tmp[$v_add['s_year']], $v_add['s_money'], 2);
                }

                foreach ($income_tmp as $yeas => $right_income){
                    $output = $output.$right_income."\t";
                }

                file_put_contents($this->filePath, $output.PHP_EOL, FILE_APPEND);

            }
            dd('ok');

        }catch (\Exception $e){
            dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendCommandExceptionNotice($this, $e);

        }

        $this->output->success("成功执行");

    }


    public function getParams(){
        $params = [];

        $params['limit_month'] = $this->input->getOption('limit_month') ?: 0;
        if(empty($params['limit_month'])){
            $params['limit_month'] = 0;
        }
        $params['month'] = $this->input->getOption('month') ?: date('Y-m');

        $this->filePath = storage_path()."/logs/first_income_customer_".date('Ymd').".log";
        $this->fileDetailPath = storage_path()."/logs/first_income_customer_detail".date('Ymd').".log";
        //dd($filePath);
        if(file_exists($this->filePath)){
            unlink($this->filePath);
        }

        if(file_exists($this->fileDetailPath)){
            unlink($this->fileDetailPath);
        }

        $params['filter_customer'] = $this->filter_customer;
        $this->filter_apikey = $params['filter_apikey'] = Account::getApikeysByCustomerIdsNew($this->filter_customer);
        if(isset($params['father_id']) && !empty($params['father_id'])){
            $params['product_ids'] = array_column(Product::getChildProduct([$params['father_id']]), 'product_id');
        }

        if(isset($params['customer_id']) && !empty($params['customer_id'])){
            $params['apikey'] = Account::getApikeysByCustomerIdsNew([$params['customer_id']]);
        }

        return $params;
    }



}