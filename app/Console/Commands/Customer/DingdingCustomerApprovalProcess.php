<?php

namespace  App\Console\Commands\Customer;

use App\Utils\DingDing;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Models\DingdingCustomerApprovalProcess as approvalprocess;

class DingdingCustomerApprovalProcess extends Command
{

    protected $signature = 'ding:customer_approval_process';

    protected $description = '客户钉钉处理流程';

    public function handle()
    {
        $dingding_approvel_process_data = $this->getApprovelProcessData();
        $this->getProcessInstanceStatus($dingding_approvel_process_data);
    }

    //获取最新的待审批数据
    private function getApprovelProcessData()
    {
        $dingding_approvel_process_data = approvalprocess::getDingProcessing();
        return $dingding_approvel_process_data;
    }

    //获取更新审批状态
    private function getProcessInstanceStatus($dingding_approvel_process_data)
    {
        if (empty($dingding_approvel_process_data)){
            return false;
        }

        $dingding = new DingDing(config('params.dingding.appkey'),config('params.dingding.secret'));

        array_walk($dingding_approvel_process_data,function ($item) use($dingding){
            $res =  $dingding->getProcessInstance($item['process_instance_id']);
            if ($res['errcode'] == 0 && in_array($res['process_instance']['status'], ['COMPLETED', 'TERMINATED', 'CANCELED'])){

                $updateData['result'] = $res['process_instance']['result'] ?? '';
                $updateData['status'] = $res['process_instance']['status'] ?? '';
                $updateData['real_result'] = 2;  //审批结束
                //更新审批结果
                approvalprocess::updateData(['id'=>$item['id']],$updateData);
                //更新低级别审批结果
                $where = [
                    ['account_id','=' ,$item['account_id']],
                    ['customer_id','=',$item['customer_id']],
                    ['type','=',$item['type']],
                    ['real_result','=',1],
                    ['level','<=',$item['level']],
                ];
                approvalprocess::updateData($where,$updateData);
            }
        });

    }

}