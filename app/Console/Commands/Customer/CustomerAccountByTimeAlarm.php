<?php

namespace App\Console\Commands\Customer;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\Crs\SystemUser;
use App\Models\Customer;
use App\Models\DingdingCustomerApprovalProcess;
use App\TraitUpgrade\CurlTrait;
use App\Utils\DingDing;
use Illuminate\Console\Command;

/**
 * Class CustomerAccountByTimeAlarm 客户账户截至日期预警
 * @package App\Console\Commands\Customer
 */

class CustomerAccountByTimeAlarm extends Command
{
    use CurlTrait;
    protected $signature = "customer_account_bytime_alarm";

    protected $alarm_accont;
    protected $dingding;

    public function __construct()
    {
        parent::__construct();
        //截至时间小于此时间 报警
        $alarm_time = time() + 86400 * 60;
        $this->alarm_accont = AccountProduct::getAccountByTime($alarm_time);
        $this->dingding = new DingDing(config('params.dingding.appkey'),config('params.dingding.secret'));
    }

    public function handle()
    {
       $this->executeAlarm();
    }

    private function executeAlarm()
    {
         if (empty($this->alarm_accont)){
             return false;
         }
         array_walk($this->alarm_accont,function ($item){
              $alarm_info = $this->getAlarmContent($item);
               $this->performAlarm($alarm_info);
         });
    }


    private function performAlarm($alarm_info)
    {
        if (empty($alarm_info)){
            return false;
        }
        //发送微信报警
        //$this->post(SEND_WECHAT_ALARM_URL,['content'=>$alarm_content['content']]);
        $level = $this->getAlarmLevel($alarm_info['diff_day']);

        $where = [
            ['customer_id','=',$alarm_info['customer_id']],
            ['account_id','=',$alarm_info['account_id']],
            ['type','=',3],  //合同到期
            ['level','=',$level],
            ['real_result','=',1],
        ];
        $process = DingdingCustomerApprovalProcess::getNotProcessed($where);

        if (!empty($process)){
            return false;
        }

        $user_id = $this->getDUserIdBySalesman($alarm_info['salesman']);
       // $leader_user_id = $this->getDUserIdBySalesman();
        $dept_id = $this->getDeptId($user_id);

        if (empty($user_id)){
           return false;
        }

        $formValue = [
            [
                "name" => "类型",
                "value" => '账户产品截止日期',
            ],
            [
               "name" => "账号",
               "value" => $alarm_info['account_name'],
            ],
            [
                "name" => "截至日期",
                "value" => $alarm_info['end_time'],
            ],
            [
                "name" => "剩余天数",
                "value" => $alarm_info['diff_day'],
            ]
          ];
        $approvers_v2 = [
            //一级审批 商务
            ['task_action_type'=>'NONE',
            "user_ids"=> [
                DINGDING_ZHANGREN_USERID,
            ]],
            //二级审批  张韧
            ['task_action_type'=>'NONE',
                "user_ids"=> [
                   // $user_id,
                    DINGDING_ZHANGREN_USERID,
             ]]
          ];

        $cc = [
            'cc_position'=>'FINISH',
            'cc_list'=>DINGDING_YANGXUELI_USERID

        ];
            //钉钉报警 客户账号截至日期报警
        // $this->dingding->createProcessApprovers('PROC-E7C08750-0464-4056-B6D3-4575B8369E4E',$user_id,$dept_id,$formValue,$approvers_v2,$cc);
        $process_instance_id =  $this->dingding->createProcessApprovers(DINGDING_ALARM_PROCESS_CODE,$user_id,$dept_id,$formValue,$approvers_v2,$cc);

        $insert_data = ['customer_id'=>$alarm_info['customer_id'],'account_id'=>$alarm_info['account_id'],'type'=>3,'level'=>$level,'process_instance_id'=>$process_instance_id,'create_time'=>time()];
        //添加钉钉报警处理流程
        DingdingCustomerApprovalProcess::insert($insert_data);
    }

    private function getAlarmContent($item)
    {
        $res = [];
       // $account_name = $item['account_name'];
        $end_time = date('Y-m-d',$item['end_time']);
        $diff = $this->diffBetweenTwoDays($item['end_time'],time());
        $account_info = Account::getAccountById($item['account_id']);
        $customer_info = Customer::getOneItemByCondition(['customer_id'=>$account_info['customer_id']]);
        $account_name = $account_info['account_name'];
       // $res['content'] =  "报警信息：账户截至日期报警\n账户名称：$account_name\n账户截至日期：$end_time\n剩余时长：$diff\n\n";
        $res['customer_name']  = $customer_info['name'];
        $res['customer_id']  = $customer_info['customer_id'];
        $res['account_name']  = $account_name;
        $res['account_id']  = $account_info['account_id'];
        $res['end_time']  = $end_time;
        $res['diff_day']  = $diff;
        $res['salesman'] = $customer_info['salesman'];

        return $res;
    }
    /*
     * $day1 合同到期时间
     * $day2 当前日期
    */
    private  function  diffBetweenTwoDays ($day1, $day2)
    {
        $second = $day1 - $day2;
        if($second != 0) {
            $diffday =   floor($second / 86400);
        }elseif($second == 0){
            $diffday = 0;
        }
        return  $diffday;
    }

    private  function  getDUserIdBySalesman($salesman)
    {
        $result = '';
        //获取销售的电话
        $phone = SystemUser::getPhoneByUserName($salesman)['phone'];
        //根据电话获取钉钉的userid
        if (!empty($phone)){
            $res =  $this->dingding->getUserIdByPhoneNumber($phone);
            if ($res['errcode'] == 0){
                $result =  $res['result']['userid'];
            }
        }
        return $result;
    }

    private  function  getDeptId($userid)
    {
        $dept_id = '';
        //根据userid 获取部门id
        $res =  $this->dingding->getDepIdByUserId($userid);
        if ($res['errcode'] == 0){
            $dept_id =  $res['result']['parent_list'][0]['parent_dept_id_list'][0];
        }
        $dept_id = !empty($dept_id) ? $dept_id : DEFAULT_DEPT_ID;

        return $dept_id;
    }

    private function  getAlarmLevel($diff_day)
    {
      if ($diff_day <= 7){
          return 7;
      }elseif ($diff_day > 7 && $diff_day<=30){
          return  30;
      }elseif ($diff_day>30 && $diff_day <= 60){
          return  60;
      }
    }
}



