<?php


namespace App\Console\Commands\Customer;

use App\Models\BillProductIncomeV2;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Product;
use App\Models\Remit;
use Illuminate\Console\Command;
use App\Models\BillCost;
use App\Models\BillProductIncome;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\Account;
use App\Models\CustomerExpend;

//根据客户特殊配置，计算收入设置特殊消耗
class CustomerIncomeCalculate extends Command
{
    protected $signature = 'customer_income_calculate
    {--month= : 查看日期(格式Y-m）默认当前日期}';

    public $degree = 6;
    public $filePath = '';//存放结果的日志文件
    public $start_date;
    public $end_date;
    public $current_month;//当前月

    public $filter_customer = ['C20180828LOCNMG', 'C20200622KF31GS'];
    public $filter_apikey = [];
    public $data = [];

    public $find_apikey = ['a128f4e9512052a565de9ac28e393d53'];
    //public $find_apikey = ['a01463e997ba1afa11df6bf4720e9e3d'];
    public $find_product = [11104, 11105, 11106, 11107];
    //public $find_product = [10104, 10105];

    /**
     * @throws \Exception
     */
    public function handle()
    {

        try{
            $params = $this->getParams();

            $this->getDateRange($params['month']);
            //dd($this->start_date, $this->end_date, $params);
            $where = ['apikey' => $this->find_apikey, 'product_ids' => $this->find_product];
            $list = BillProductIncomeV2::getMonthIncome($this->start_date, $this->end_date, $where);

            $arr = [];
            foreach ($list as $val){
                $apikey = $val['apikey'];
                $total_money = $arr[$apikey]['total_money'] ?? 0;
                $total_number = $arr[$apikey]['total_number'] ?? 0;

                $arr[$apikey]['total_money'] = bcadd($total_money, $val['s_money'], 6);
                $arr[$apikey]['total_number'] = $total_number + $val['s_number'];

                $product_id = $val['product_id'];
                $arr[$apikey]['product_ids'] = $arr[$apikey]['product_ids'] ?? [];
                if(!in_array($product_id, $arr[$apikey]['product_ids'])){
                    $arr[$apikey]['product_ids'][] = $product_id;
                }

            }

            foreach ($arr as $apikey => $val){
                $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
                if(empty($customer_id) || is_array($customer_id)){
                    throw new \Exception($apikey."缓存异常");
                }

                $total_money = $val['total_money'];
                $total_number = $val['total_number'];
                $product_ids = $val['product_ids'];
                $real_total_money = $this->checkIncome($total_number);
                if($real_total_money > $total_money){
                    $type = 2;//add
                    $expend = bcsub($real_total_money, $total_money, 6);
                }else if($total_money > $real_total_money){
                    $type = 1;//sub
                    $expend = bcsub($total_money, $real_total_money, 6);
                }else{
                    continue;
                }

                $avg_money = bcdiv($expend, count($product_ids), 2);
                $now_time = time();
                $base = [
                    'customer_id' => $customer_id,
                    'start_date' => substr($this->start_date, 0, 6),
                    'name'  => '事件分包月计费调整',
                    'money' => $avg_money,
                    'type' => $type,
                    'remark' => '国美事件分包月计费调整',
                    'profile_show_date' => date('Y-m-d', strtotime($this->start_date)),
                    'create_time' => $now_time,
                    'update_time' => $now_time,
                ];
                foreach ($product_ids as $pid){
                    $base['product_id'] = $pid;
                    $batch_add[] = $base;
                }

                CustomerExpend::insert($batch_add);
            }

            dd('ok');

        }catch (\Exception $e){
            //dd($e->getMessage(), $e->getFile(), $e->getLine());
            sendCommandExceptionNotice($this, $e->getMessage());

        }

        $this->output->success("成功执行");

    }

    public function checkIncome($total_number = 0){
        switch ($total_number){
            case $total_number>0 && $total_number<=1600*10000 :
                $money = 85000;
                break;
            case $total_number>1600*10000 && $total_number<=3200*10000 :
                $money = 139000;
                break;
            case $total_number>3200*10000 && $total_number<=6400*10000 :
                $money = 192000;
                break;
            case $total_number>6400*10000 && $total_number<=9600*10000 :
                $money = 249600;
                break;
            case $total_number>9600*10000 && $total_number<=12800*10000 :
                $money = 300000;
                break;
            case $total_number>12800*10000 && $total_number<=18000*10000 :
                $money = 378000;
                break;
            case $total_number>18000*10000 :
                $beyond_number = $total_number - 18000*10000;
                $price = 0.0021;
                $beyond_money = bcmul($beyond_number, $price, 6);
                $money = bcadd(378000, $beyond_money, 6);
                break;
            default:
                $money = 0;
                break;
        }

        return $money;
    }


    public function getParams(){
        $params = [];
        $pre_month = date('Y-m', strtotime('-1 month'));

        $params['month'] = $this->input->getOption('month') ?: $pre_month;

        $params['filter_customer'] = $this->filter_customer;

        return $params;
    }


    public function getDateRange($month){
        $this->current_month = str_replace('-', '', $month);//当前月
        $this->end_date = $this->getLastDay($month.'-01');//查看日期所在月的最后一天
        $this->start_date = date('Ymd', strtotime($month.'-01'));

        return true;
    }

    /**
     * 获取某个日期所在月的最后一天
     * @param $date
     * @return false|string
     */
    public function getLastDay($date)
    {
        $firstday = date('Y-m-01', strtotime($date));
        $lastday = date('Ymd', strtotime("$firstday +1 month -1 day"));
        return $lastday;
    }

    //获取指定日期的前后几个月
    public function getMonth($sign, $month = '')
    {
        //得到系统的年月
        $tmp_date = empty($month) ? date("Ym") : $month;
        //切割出年份
        $tmp_year = substr($tmp_date,0,4);
        //切割出月份
        $tmp_mon = substr($tmp_date,4,2);
        // 得到当前月份的下几月
        $tmp_nextmonth = mktime(0,0,0,$tmp_mon+$sign,1,$tmp_year);
        // 得到当前月份的前几月
        $tmp_forwardmonth = mktime(0,0,0,$tmp_mon-$sign,1,$tmp_year);
        return $fm_next_month = date("Ym",$tmp_forwardmonth);
    }




}