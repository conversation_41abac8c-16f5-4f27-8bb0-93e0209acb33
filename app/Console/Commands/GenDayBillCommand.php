<?php

namespace App\Console\Commands;

use App\Models\MongoBillDay;
use App\Models\MongoBillDayLogs;
use App\Models\MongoBillMonth;
use App\Models\MongoLog;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

class GenDayBillCommand extends Command
{
    use WechatExceptionTrait;

    /** @var string 命令 */
    public $signature = 'bill:generate-day';

    /** @var string 命令提示 */
    public $description = '生成日账单';

    /** @var   object 进度条 */
    private $bar;

    /** @var MongoBillDayLogs 操作的单元 */
    private $item;

    /**
     * @param MongoBillDayLogs $item
     */
    public function setItem(MongoBillDayLogs $item)
    {
        $this->item = $item;
        $this->item->day = date('d', strtotime($item->date));
    }

    /**
     * @throws \Exception
     */
    public function handle()
    {
        try {
            // 实际执行
            $this->handleDo();

            $msg = '生成日账单成功';
            $this->outputInOneWay($msg);
        } catch (\Exception $e) {

            $msg = '生成日账单失败 msg: ' . $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
            $this->outputInOneWay($msg, false);
        }
    }

    /**
     * 实际执行
     */
    private function handleDo()
    {
        // 获取要执行的列表条件
        $list_items = $this->getLoopList();
        $this->bar = $this->output->createProgressBar(count($list_items));

        // 循环计算 && 插入
        $list_items->each(function ($item) {
            $this->setItem($item);
            $this->loopComputedAndInsert();

            $this->bar->advance();
        });
        $this->bar->finish();
    }

    /**
     * 循环计算 && 插入
     * @throws \Exception
     */
    private function loopComputedAndInsert()
    {
        // 如果今天是2号 则今天账单等同于今天的log
        if ($this->item->day == '01') {
            $this->insertBillWhenFirstDay();
            return;
        }

        // 如果昨天是新开启的一个时间节点, 则今天账单等同于今天的log
//        if ($this->determineIsNewBegin()) {
//            $this->insertBillWhenFirstDay();
//            return;
//        }

        // 如果没有昨天的历史数据， 则今天是不进行操作的
        if (!$this->determineHasDataOfTwoDayAgo()) {
            $this->insertBillWhenFirstDay();
            return;
        }

        // 如果是最后一天的话
//        if ($this->_determineTheLastDayOfMonth()) {
//            $this->_insertLastDayBill();
//            return;
//        }

        // 今天生成的总合的数据 - 昨天生成总和数据 = 今天的数据
        $this->insertWhenHasBaseInfo();
    }

    /**
     * 插入每个月最后一天的数据
     * @throws \Exception
     */
    private function _insertLastDayBill()
    {
        // 获取上个月整月的账单
        $bill_whole = $this->_getBillOfWholeMonth();

        // 最后一天之前的账单总和
        $money_before_last_day = $this->getTwoDaysAgoInfoAfterTidy();

        // 计算 && 插入数据
        $this->computedThenInsert($bill_whole, $money_before_last_day);
    }

    private function _getBillOfWholeMonth(): array
    {
        // 条件
        $where = $this->_genParamsForWholeMonth();

        //  聚合
        $money = MongoBillMonth::where($where)->sum('money');
        $section_invoked_number = MongoBillMonth::where($where)->sum('section_invoked_number');
        return compact('money', 'section_invoked_number');
    }

    /**
     * @return array
     */
    private function _genParamsForWholeMonth(): array
    {
        $where = array_only($this->item->toArray(), ['customer_id', 'account_id', 'product_id']);
        $where['product_id'] = (int)$where['product_id'];
        $where['month'] = date('Ym', strtotime('last day of last month'));
        return $where;
    }

    /**
     * 如果是上个月最后一天的话
     * @return bool
     */
    private function _determineTheLastDayOfMonth(): bool
    {
        return $this->item->date == date('d', strtotime('last day of last month'));
    }

    /**
     * 当有基础的数据的时候，进行插入操作( 今天的数据 - 昨天的数据 = 今天的数据)
     * @throws \Exception
     */
    private function insertWhenHasBaseInfo()
    {
        // 昨天数据
        $money_yesterday = $this->getYesterdayInfo();

        // 前天数据
        $money_two_day_ago = $this->getTwoDaysAgoInfoAfterTidy();

        // 计算 && 插入数据
        $this->computedThenInsert($money_yesterday, $money_two_day_ago);
    }

    /**
     * 计算 && 插入数据
     * @param array $money_yesterday
     * @param array $money_two_day_ago
     * @throws \Exception
     */
    private function computedThenInsert(array $money_yesterday, array $money_two_day_ago)
    {
        // 参数
        $item = $this->genParams($money_yesterday, $money_two_day_ago);

        // 生成单元
        MongoBillDay::create($item);

        // 检查金额是否异常
        $this->validateMoney($item);
    }

    /**
     * @param array $item
     * @throws \Exception
     */
    private function validateMoney(array $item)
    {
        if ($item['money'] >= 0) {
            return;
        }

        $msg = date('Y-m-d') . ' 生成日账单遇到异常; slug_id : ' . $item['slug_id'] . ' 生成的金额小于0  msg: ' . json_encode($item, JSON_UNESCAPED_UNICODE);
        $this->wechatException($msg);
    }

    /**
     * 生成单元
     * @param array $money_yesterday
     * @param array $money_two_day_ag
     * @return array
     * @throws \Exception
     */
    private function genParams(array $money_yesterday, array $money_two_day_ag): array
    {
        $item = array_only($this->item->toArray(), ['product_id', 'customer_id', 'account_id', 'date', 'month']);
        $item['money'] = floatval($money_yesterday['money'] - $money_two_day_ag['money']);
        $item['section_invoked_number'] = $money_yesterday['section_invoked_number'] - $money_two_day_ag['section_invoked_number'];

        $item['product_id'] = (int)$item['product_id'];

        // 注入唯一键
        $item['slug_id'] = \Ramsey\Uuid\Uuid::uuid4()->toString();
        return $item;
    }

    /**
     * 前天数据
     * @return array
     */
    private function getTwoDaysAgoInfoAfterTidy(): array
    {
        // 条件
        $where = $this->genParamsForTwoDaysAgo();

        // 聚合
        $money = MongoBillDayLogs::where($where)->sum('money');
        $section_invoked_number = MongoBillDayLogs::where($where)->sum('section_invoked_number');
        return compact('section_invoked_number', 'money');
    }

    /**
     * 条件
     * @return array
     */
    private function genParamsForTwoDaysAgo(): array
    {
        return [
            'customer_id' => $this->item->customer_id,
            'account_id' => $this->item->account_id,
            'product_id' => (int)$this->item->product_id,
            'date' => date('Ymd', strtotime('-2 days'))
        ];
    }

    /**
     * 计算昨天的金额 && 调用量
     */
    private function getYesterdayInfo()
    {
        // 条件
        $where = $this->genParamsForYesterday();

        //  聚合
        $money = MongoBillDayLogs::where($where)->sum('money');
        $section_invoked_number = MongoBillDayLogs::where($where)->sum('section_invoked_number');
        return compact('money', 'section_invoked_number');
    }

    /**
     * 条件
     * @return array
     */
    private function genParamsForYesterday(): array
    {
        $where = array_only($this->item->toArray(), ['customer_id', 'account_id', 'product_id', 'date']);
        $where['product_id'] = (int)$where['product_id'];
        return $where;
    }

    /**
     * 是否有两天前的数据
     * @return bool
     */
    private function determineHasDataOfTwoDayAgo(): bool
    {
        $where = array_only($this->item->toArray(), ['customer_id', 'account_id', 'product_id']);
        $where['product_id'] = (int)$where['product_id'];
        $where['date'] = date('Ymd', strtotime('-2 days'));

        return (bool)MongoBillDayLogs::getOneItem($where);
    }

    /**
     * 如果昨天是新开启的一个时间节点,
     * @return bool
     */
    private function determineIsNewBegin(): bool
    {
        return $this->item->section_begin == date('d', strtotime( '+1 day', strtotime($this->item->date)));
    }

    /**
     * 如果今天是2号 则今天账单等同于今天的log
     */
    private function insertBillWhenFirstDay()
    {
        $item = array_only($this->item->toArray(), ['customer_id', 'month', 'account_id', 'product_id', 'money', 'date', 'section_invoked_number']);
        $item['product_id'] = (int)$item['product_id'];
        $item['section_invoked_number'] = (int)$item['section_invoked_number'];
        $item['money'] = (float)$item['money'];

        // 注入唯一键
        $item['slug_id'] = \Ramsey\Uuid\Uuid::uuid4()->toString();
        MongoBillDay::create($item);
    }

    /**
     * 获取要执行的列表
     * @return mixed
     */
    private function getLoopList()
    {
        // 条件
        $section_end = date('Ymd', strtotime('-1 days'));

        return MongoBillDayLogs::getListItems(compact('section_end'));
    }

    /**
     * 统一输出
     * @param string $msg
     * @param bool $success 是否选择成功的输出方式
     * @throws \Exception
     */
    private function outputInOneWay(string $msg, $success = true)
    {
        if ($success) {
            $this->output->success($msg);
            $this->wechatException($msg);
            return;
        }
        MongoLog::create(['error' => $msg, 'msg' => '遇到异常', 'slug_id' => $this->item->uuid, 'action' => 'bill']);
        $this->output->error($msg);
        $this->wechatException($msg);
    }
}