<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\BillConfig;
use App\Models\MongoBillSection;
use App\Models\MongoLog;
use Illuminate\Console\Command;
use App\TraitUpgrade\WechatExceptionTrait;

class InitBillSectionCommand extends Command
{
    use WechatExceptionTrait;

    /** @var string 命令 */
    public $signature = 'bill:init-sections {--range=month : month上个月的计费片段, all所有月份的计费片段} 
    {--customer_id= : 客户ID}
    {--product_id= : 产品ID} 
    {--account_id= : 账单ID}';

    /** @var string 命令提示 */
    public $description = '初始化账单计费配置区间';

    /** @var   object 进度条 */
    private $bar;

    /** @var string 执行初始化--all操作的时候的操作人 */
    private $operator_person;

    /** @var string 操作理由 */
    private $operator_reason;

    /** @var array 禁用的账号ID列表 */
    private $list_forbidden_account_ids = [];

    /** @var  array 禁用产品ID列表 */
    private $list_forbidden_product_ids = [];

    /** @var string 催收快捷版产品key */
    private $product_key_cushou_short = '99bbdb8426f8b4e8d0cc3ebd92484590';

    /** @var string redis链接 */
    private $redis_bill_connection = 'db_backend';

    /** @var string 账单rollback的key */
    private $redis_section_rollback_key = 'back_bill_sections_rollback_key';

    /** @var array 生成的uuid的集合 */
    private $list_operator_uuids = [];

    /**
     * 计费配置初始化
     * @throws \Exception
     */
    public function handle()
    {
        try {
            // 校验选项
            $this->validateOption();

            // 关于range的确认
            $this->reminder();

            // 插入新的数据
            $this->handleWrite();

            // 提示
            $msg = '历史账单区间已经生成!' . ($this->operator_person ? ' 操作人:' . $this->operator_person .
                    ' 执行的是--range=all操作, 原因是' . $this->operator_reason : '');
            $this->outputInOneWay($msg);
        } catch (\Exception $e) {
            // 存储异常时候需要回退的sections
            $this->storeUuidForRollback();

            // 设置提示
            $msg = '历史账单区间生成失败 ' . ($this->operator_person ? ' 操作人:' . $this->operator_person : '') .
                ' 执行的是--range=all操作, 原因是' . $this->operator_reason .
                'msg:' . $e->getMessage();

            $this->outputInOneWay($msg, false);
        }
    }

    /**
     * 将本次命令生成的uuid,存储到redis中，给rollback备用
     */
    private function storeUuidForRollback()
    {
        $uuid_json = json_encode($this->list_operator_uuids, JSON_UNESCAPED_UNICODE);
        app('redis')->connection($this->redis_bill_connection)
            ->set($this->redis_section_rollback_key, $uuid_json);
    }

    /**
     * @throws \Exception
     */
    private function reminder()
    {
        if ($this->option('range') != 'all') {
            return;
        }

        // 问姓名
        $this->operator_person = $operator_person = trim($this->ask('请问您的姓名,我们需要记录下来，方便后期追述！'));
        if (!$operator_person) {
            throw new \Exception('请问您的姓名,我们需要记录下来，方便后期追述!');
        }

        // 确认
        $msg = '使用range=all会软删除之前全部的旧配置(如果存在的话), 您确定需要这样做吗?';
        $confirmation = $this->choice($msg, ['Y', 'N'], 0);
        if (strtoupper($confirmation) == 'N') {
            throw new \Exception('您选择退出(--range=all)!');
        }

        // 原因
        $this->operator_reason = $reason = $this->ask('请输入这样的执行的原因!');
        $msg = '使用--range=all选项生成计费片段的操作';
        $action = 'init-sections';

        MongoLog::create(compact('msg', 'action', 'reason', 'operator_person'));
    }

    /**
     * 校验选项
     * @throws \Exception
     */
    private function validateOption()
    {
        $range = $this->option('range');
        if (!in_array($range, ['all', 'month'])) {
            throw new \Exception('--range选项不合法');
        }
    }

    /**
     * @param string $msg
     * @param bool $success 是否选择成功的输出方式
     * @throws \Exception
     */
    private function outputInOneWay(string $msg, $success = true)
    {
        if ($success) {
            $this->output->success($msg);
            $this->wechatException($msg);
            return;
        }
        $this->output->error($msg);
        $this->wechatException($msg);
    }

    /**
     * 插入新的数据
     */
    private function handleWrite()
    {
        // 客户id,账号id，产品id做唯一键得到的聚合计费配置列表
        $list_bill_configs = $this->getAggregateBillConfigList();

        // 时间切割
        $this->separateBillConfigToSections($list_bill_configs);
    }

    /**
     * 按照月份切割区间
     * @param array $list_bill_configs
     */
    private function separateBillConfigToSections(array $list_bill_configs)
    {
        $this->bar = $this->output->createProgressBar(count($list_bill_configs));

        // 容器
        $list_container = [];
        array_walk($list_bill_configs, function ($config_bill) use (&$list_container) {

            // 如果只有一种配置
            if (count($config_bill) == 1) {
                $this->convertConfigToSectionsWhenJustOne($config_bill[0]);
                $this->bar->advance();
                return;
            }

            // 多种配置的情况
            $this->separateBillConfigToSectionsWhenMuch($config_bill);
            $this->bar->advance();
        });

        $this->bar->finish();

        // 存储异常时候需要回退的sections
        $this->storeUuidForRollback();
    }

    /**
     * 某个账号的某个产品有多条配置的时候
     * @param array $list_bill_configs
     */
    private function separateBillConfigToSectionsWhenMuch(array $list_bill_configs)
    {
        // 格式化多配置的账单
        $list_bill_configs = $this->formatBillConfigWhenMuch($list_bill_configs);

        // 对时间做一些基本的整理
        $list_date_base_section = $this->getFormatDateListWhenMuch($list_bill_configs);

        // 数据库注入配置
        $this->writeBillConfigWhenMuch($list_bill_configs, $list_date_base_section);
    }

    /**
     * 数据库注入配置
     * @param array $list_bill_configs
     * @param array $list_date_base_section 做了预先处理的时间分割
     */
    private function writeBillConfigWhenMuch(array $list_bill_configs, array $list_date_base_section)
    {
        // copy用来寻找参照物
        $list_bill_configs_copy = $list_bill_configs;

        // 容器
        array_walk($list_bill_configs, function ($bill_config, $start_date_format) use ($list_date_base_section, $list_bill_configs_copy) {
            // 整理过的日期的左右边界
            $section_base_item = $list_date_base_section[$start_date_format];


            // 分割 && 写入
            $this->separateAndWriteWhenMuch($section_base_item, $bill_config, $list_bill_configs_copy);
        });
    }

    /**
     * 根据账单配置生成基础的账单信息
     * @param array $bill_config
     * @return array
     */
    private function getBaseBillInfoFromSourceBill(array $bill_config): array
    {
        $timestamp = strtotime($bill_config['start_date']);
        $start_day = date('Ymd', $timestamp);
        $month = date('Ym', $timestamp);
        list($product_id, $account_id, $customer_id, $section_source) = [$bill_config['product_id'], $bill_config['account_id'],
            $bill_config['customer_id'], $bill_config];

        // 片段的通用配置
        return compact('product_id', 'account_id', 'customer_id', 'month', 'start_day', 'section_source');
    }

    /**
     * 分割 && 写入
     * @param array $section_base_item_date 切割出来的时间区间
     * @param array $bill_config_item 正在出来的账单区间
     * @param array $list_bill_configs_copy 账单配置的副本
     */
    private function separateAndWriteWhenMuch(array $section_base_item_date, array $bill_config_item, array $list_bill_configs_copy)
    {
        list($current, $next, $same_month) = [$section_base_item_date['current'], $section_base_item_date['next'],
            $section_base_item_date['same_month']];
        $month_now = date('Ym' . '01');

        // 如果当前月份已经超过了本月 则不需要写操作
        if ($current >= $month_now) {
            return;
        }

        // 多配置的时候,最后一个开区间的处理
        if ($section_base_item_date['next'] == null) {
            $this->writeWhenMuchAndIsOpened($section_base_item_date, $list_bill_configs_copy);
            return;
        }

        // 如果是在同一个月, 则片段开始和结束时间就可以确定下来了
        if ($same_month) {
            $this->writeWhenTwoSectionInOneMonth($section_base_item_date, $bill_config_item, $list_bill_configs_copy);
            return;
        }

        // 如果不是在同一个月
        $this->writeWhenTwoSectionNotOneMonth($section_base_item_date, $bill_config_item, $list_bill_configs_copy);
    }

    /**
     * 多配置的时候,最后一个开区间的处理
     * @param array $section_base_item_date
     * @param array $list_bill_configs_copy
     */
    private function writeWhenMuchAndIsOpened(array $section_base_item_date, array $list_bill_configs_copy)
    {
        $current = $section_base_item_date['current'];
        $this->convertConfigToSectionsWhenJustOne($list_bill_configs_copy[$current]);
    }

    /**
     * 当划分的两个片段是不在同一个月的时候
     * @param array $section_base_item_date
     * @param array $bill_config_item
     * @param $list_bill_configs_copy
     */
    private function writeWhenTwoSectionNotOneMonth(array $section_base_item_date, array $bill_config_item, $list_bill_configs_copy)
    {
        list($current, $next, $same_month) = [$section_base_item_date['current'], $section_base_item_date['next'],
            $section_base_item_date['same_month']];

        // 基础信息
        $section_common = $this->getBaseBillInfoFromSourceBill($bill_config_item);
        $section_common['section_refer'] = $list_bill_configs_copy[$next];
        $section_common['section_source'] = $bill_config_item;

        // 不在同一个月的闭区间月份分割
        $list_month_sections = $this->separateMonthSectionWhenClosedAndNotOneMonth($current, $next);

        // 写入
        array_walk($list_month_sections, function ($month_section) use ($section_common) {
            $item = array_merge($section_common, $month_section);

            // 校验是否满足插入的要求
            if ($this->determineCanGenSection($item)) {
                $this->writeOneItemToBillSection($item);
            }
        });
    }

    /**
     * 不在同一个月的闭区间月份分割
     * @param string $current
     * @param string $next
     * @return array
     */
    private function separateMonthSectionWhenClosedAndNotOneMonth(string $current, string $next): array
    {
        list($month_current, $month_next) = [date('Ym', strtotime($current)), date('Ym', strtotime($next))];

        // 容器
        $list_container = [];

        // 月份分割出来的片段
        $list_month_sections = $this->getMonthSectionList();
        foreach ($list_month_sections as $month => $item) {
            list($day_begin, $day_last) = [$item['day_begin'], $item['day_last']];

            // 循环到区间开始的时间的同月， 则
            if ($day_begin <= $current && $month == $month_current) {
                $list_container[$month] = [
                    'section_begin' => strval($current),
                    'section_end' => strval($day_last),
                    'month' => strval($month)
                ];
            } elseif ($day_begin > $current && $month < $month_next) {
                // 如果越过了开始月 && 没有到结束的月份

                $list_container[$month] = [
                    'section_begin' => strval($day_begin),
                    'section_end' => strval($day_last),
                    'month' => strval($month)
                ];
            } elseif ($day_begin > $current && $month == $month_next) {
                // 如果越过了开始月 && 达到了结束月
                $section_end = date('Ymd', strtotime('-1 day', strtotime($next)));
                if ($section_end >= $day_begin) {
                    $list_container[$month] = [
                        'section_begin' => strval($day_begin),
                        'section_end' => $section_end,
                        'month' => strval($month)
                    ];
                }

            }

            // 如果循环已经越过了$next月份 就没有必要循环下去了
            if ($day_begin > $next) {
                break;
            }
        }

        return $list_container;
    }


    /**
     * 当划分的两个片段是在同一个月的时候
     * @param array $section_base_item_date
     * @param array $bill_config_item
     * @param array $list_bill_configs_copy
     * @throws \Exception
     */
    private function writeWhenTwoSectionInOneMonth(array $section_base_item_date, array $bill_config_item, array $list_bill_configs_copy)
    {
        list($current, $next, $same_month) = [$section_base_item_date['current'], $section_base_item_date['next'],
            $section_base_item_date['same_month']];

        // 时间切割的开始结束时间确定
        $section_begin = $current;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($next)));
        $section_source = $bill_config_item;
        $section_refer = $list_bill_configs_copy[$next];
        $section_common = $this->getBaseBillInfoFromSourceBill($bill_config_item);
        // 写入数据库
        $item = array_merge($section_common, compact('section_begin', 'section_end', 'section_refer', 'section_source'));

        // 校验是否满足插入的要求
        if ($this->determineCanGenSection($item)) {
            $this->writeOneItemToBillSection($item);
        }
    }

    /**
     * 是否满足插入数据库的条件
     * @param array $item
     * @return bool
     */
    private function determineCanGenSection(array $item): bool
    {
        $range = $this->option('range');
        list($last_month, $item_month) = [
            date('Ym', strtotime('first day of last month')),
            $item['month']
        ];

        if ($range == 'all') {
            return $last_month >= $item_month;
        }

        // 如果限制了上月
        return $last_month == $item_month;
    }

    /**
     * 写入单条的配置
     * @param array $item
     * @throws \Exception
     */
    private function writeOneItemToBillSection(array $item)
    {
        // 参数格式转化
        $item['product_id'] = intval($item['product_id']);
        $item['account_id'] = strval($item['account_id']);
        $item['customer_id'] = strval($item['customer_id']);
        $item['month'] = strval($item['month']);
        $item['start_day'] = strval($item['start_day']);
        $item['section_begin'] = strval($item['section_begin']);
        $item['section_end'] = strval($item['section_end']);

        // 注入唯一键
        $item['uuid'] = \Ramsey\Uuid\Uuid::uuid4()->toString();
        MongoBillSection::create($item);

        // 更新回退的uuid
        array_push($this->list_operator_uuids, $item['uuid']);
    }

    /**
     * 多配置的时候,对时间做一些基本的整理
     * @param array $list_bill_configs
     * @return array
     */
    private function getFormatDateListWhenMuch(array $list_bill_configs)
    {
        $list_date_keys = array_keys($list_bill_configs);

        // 容器
        $list_container = [];

        // 划分左右边界 [next, current, same_month是否是同意月]
        for ($current_index = 0; $current_index < count($list_date_keys); $current_index++) {
            $next_index = $current_index + 1;
            $current = $list_date_keys[$current_index];
            $next = $list_date_keys[$next_index] ?? null;
            $same_month = date('Ym', strtotime($current)) == date('Ym', strtotime($next));

            // 组成的单元
            $list_container[$current] = compact('same_month', 'next', 'current');
        }

        return $list_container;
    }

    /**
     * 格式化多配置
     * @param array $list_bill_configs
     * @return array|mixed
     */
    private function formatBillConfigWhenMuch(array $list_bill_configs)
    {
        $list_bill_configs = array_reduce($list_bill_configs, function ($carry, $item) {
            $item['start_date_format'] = $start_date_format = date('Ymd', strtotime($item['start_date']));
            $carry[$start_date_format] = $item;
            return $carry;
        }, []);
        ksort($list_bill_configs);
        return $list_bill_configs;
    }


    /**
     * 当多个配置的时候,获取分割后的时间区间
     * @param string $day_begin
     * @return array
     */
    private function genTimeSectionWhenMuch(string $day_begin)
    {
        // 开始天,月,年 && 本月
        $day_begin = date('Ymd', strtotime($day_begin));
        $month_begin = date('Ym', strtotime($day_begin));
        $month_now = date('Ym');

        // 如果还没有到开始计费用的时候
        if ($month_begin >= $month_now) {
            return [];
        }

        // 格式化后的月份区间
        $list_month_sections = $this->getMonthSectionList();

        // 切割时间从传递的月份到上个月
        $list_container = [];
        array_walk($list_month_sections, function ($item, $month) use ($day_begin, $month_begin, &$list_container, $month_now) {

            // 如果是本月的话
            if ($month == $month_begin && $month < $month_now) {
                list($section_begin, $section_end) = [$day_begin, $item['day_last']];
                $list_container[] = compact('section_begin', 'section_end');
            } elseif ($month > $month_begin && $month < $month_now) {
                list($section_begin, $section_end) = [$item['day_begin'], $item['day_last']];
                $list_container[] = compact('section_begin', 'section_end');
            }
        });

        return $list_container;
    }

    /**
     * 某个账号的某个产品只有一条配置的时候
     * @param array $config_bill
     */
    private function convertConfigToSectionsWhenJustOne(array $config_bill)
    {
        // 基本信息
        $start_day = date('Ymd', strtotime($config_bill['start_date']));

        // 片段的通用配置
        $section_common = $this->getBaseBillInfoFromSourceBill($config_bill);

        // 获取计费配置分割的时间区间
        $list_section_days = $this->genTimeSectionWhenJustOne($start_day);

        array_walk($list_section_days, function ($item__day_section) use ($section_common) {
            $item = array_merge($section_common, $item__day_section);

            // 是否可以插入数据库
            if ($this->determineCanGenSection($item)) {
                $this->writeOneItemToBillSection($item);
            }
        });
    }

    /**
     * 2017 -- 2021每月的分割的时间区间
     * @return array
     */
    private function getMonthSectionList(): array
    {
        list($day_begin, $day_end) = ['20150101', '20220101'];

        // 容器
        $list_container = [];
        while ($day_begin < $day_end) {
            $day_last = date('Ymd', strtotime('last day of this month', strtotime($day_begin)));
            $month = date('Ym', strtotime($day_begin));
            $list_container[$month] = compact('day_begin', 'day_last');

            // 变更
            $day_begin = date('Ymd', strtotime('first day of next month', strtotime($day_begin)));
        }

        return $list_container;
    }

    /**
     * 当只有一个配置的时候,获取分割后的时间区间
     * @param string $day_begin
     * @return array
     */
    private function genTimeSectionWhenJustOne(string $day_begin)
    {
        // 开始天,月,年 && 本月
        $day_begin = date('Ymd', strtotime($day_begin));
        $month_begin = date('Ym', strtotime($day_begin));
        $month_now = date('Ym');

        // 如果还没有到开始计费用的时候
        if ($month_begin >= $month_now) {
            return [];
        }

        // 格式化后的月份区间
        $list_month_sections = $this->getMonthSectionList();

        // 切割时间从传递的月份到上个月
        $list_container = [];
        array_walk($list_month_sections, function ($item, $month) use ($day_begin, $month_begin, &$list_container, $month_now) {

            // 如果是本月的话
            if ($month == $month_begin && $month < $month_now) {
                list($section_begin, $section_end) = [$day_begin, $item['day_last']];

                $list_container[] = compact('section_begin', 'section_end', 'month');
            } elseif ($month > $month_begin && $month < $month_now) {
                list($section_begin, $section_end) = [$item['day_begin'], $item['day_last']];
                $list_container[] = compact('section_begin', 'section_end', 'month');
            }
        });

        return $list_container;
    }

    /**
     * 客户id,账号id，产品id做唯一键得到的聚合计费配置列表
     * @return array
     */
    private function getAggregateBillConfigList(): array
    {
        // 生成条件
        $where = $this->genParamsForList();
        return BillConfig::where($where)
            ->orderByRaw('start_date desc')
            ->get()
            ->reduce(function ($carry, $item) {
                list($customer_id, $account_id, $product_id) = [$item['customer_id'], $item['account_id'], $item['product_id']];
                $key_unique = $customer_id . '_' . $account_id . '_' . $product_id;
                $carry[$key_unique][] = $item->toArray();
                return $carry;
            }, []);
    }

    /**
     * 生成条件
     * @return array
     */
    private function genParamsForList(): array
    {
        list($customer_id, $product_id, $account_id, $is_delete) = [
            trim($this->option('customer_id')),
            trim($this->option('product_id')),
            trim($this->option('account_id')),
            0
        ];

        $where = compact('is_delete');
        if ($customer_id) {
            $where['customer_id'] = $customer_id;
        }

        if ($account_id) {
            $where['account_id'] = $account_id;
        }

        if ($product_id) {
            $where['product_id'] = (int)$product_id;
        }
        return $where;
    }
}
