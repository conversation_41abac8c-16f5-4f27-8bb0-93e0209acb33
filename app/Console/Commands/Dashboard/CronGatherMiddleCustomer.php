<?php

namespace App\Console\Commands\Dashboard;

use App\Models\Account;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\CustomerSalesmanHistory;
use App\Models\BillCostV2;
use App\Models\BillCustomerIncomeV2;
use App\Models\StatisticsGatherCustomer;
use App\Models\SystemUser;
use App\Utils\Helpers\Func;
use App\Repositories\FeishuRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 聚合客户维度数据
 * php artisan dashboard:cron_gather_middle_customer --start_date=******** --end_date=******** --customer_id=C20180828LOCNMG,C20180831BZ7V8N
 * @uses CronGatherMiddleCustomer
 */
class CronGatherMiddleCustomer extends Command
{
    protected $signature = 'dashboard:cron_gather_middle_customer
	{--start_date= : 数据聚合开始日期，默认昨日（格式Ymd）}
	{--end_date= : 数据聚合结束日期，默认当天（格式Ymd）}
    {--customer_id= : 客户ID（多个客户ID以,隔开）}';

    const CRON_NAME = '聚合客户维度数据';
    /**
     * 处理数据时间段
     * @var null
     */
    private $iStartDate= null;
    private $iEndDate = null;
    /**
     * 聚合客户ID
     * @var array
     */
    private $aCustomerId = [];
    private $aApikeyList = [];

    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();
            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s, 数据区间:%s, 客户:%s",
                $sNow, $cost, "[{$this->iStartDate} - {$this->iEndDate}]", implode(',', $this->aCustomerId) ?: '(空)');
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $this->output->success($exception->getMessage());

            $oFeishu = new FeishuRepository();
            $aMessage = [
                'err_msg' => $exception->getMessage(),
                'start_date' => $this->iStartDate,
                'end_date' => $this->iEndDate,
                'customer_id' => $this->aCustomerId,
            ];
            $oFeishu->send_card_message_to_chat_group(self::CRON_NAME . '脚本执行失败!', $aMessage);
        }
    }

    /**
     * @return void
     * @throws \Exception
     */
    private function setParam(){
        $iToday = (int)date('Ymd');
        $this->iStartDate = (int)($this->input->getOption('start_date') ?: date('Ymd', strtotime('-1 day', time())));
        $this->iEndDate = (int)($this->input->getOption('end_date') ?: $iToday);
        if ($this->iEndDate > $iToday) {
            $this->iEndDate = $iToday;
        }
        if ($this->iStartDate > $iToday) {
            throw new \Exception('起始日期范围不能超过当天');
        }
        if ($this->iStartDate > $this->iEndDate) {
            throw new \Exception('起始日期范围不能大于结束时间');
        }

        $sCustomerId = $this->input->getOption('customer_id') ?: '';
        if ($sCustomerId) {
            $this->aCustomerId = explode(',', $sCustomerId);
        }
    }

    /**
     * @return bool
     */
    private function main(){
        // 构造日期
        $aDateList = [];
        $i = $this->iStartDate;
        while ($i <= $this->iEndDate) {
            $aDateList[] = $i;
            $i = date('Ymd', strtotime('+1 day', strtotime($i)));
        }
        $aDateGroupList = array_chunk($aDateList, 10);

        if ($this->aCustomerId) {
            $aApikeyList = Account::getApikeysByCustomerIdsNew($this->aCustomerId);
            $this->aApikeyList = array_values(array_filter($aApikeyList));
            if (empty($this->aApikeyList)) {
                throw new \Exception('指定的客户ID没有账号信息');
            }
        }

        // 所有 销售人员 : 部门 对照
        $aUserDeptMap = $this->getAllSalesmanData();

        // 按日期分批处理
        foreach ($aDateGroupList as $aDateGroup) {
            // 查重,防止数据重复
            $aWhereIn = [
                'date' => $aDateGroup,
                'customer_id' => $this->aCustomerId,
            ];
            $iCount = StatisticsGatherCustomer::getCount([], $aWhereIn);
            if ($iCount) {
                StatisticsGatherCustomer::clear([], $aWhereIn);
            }
            // 聚合 客户账号维度 收入,成本 数据
            $aWhereIn = [
                'date' => $aDateGroup,
                'apikey' => $this->aApikeyList,
            ];
            // 基于 bill_customer_income_v2 聚合成 [日期+客户账号+收入金额+调用量]
            $aIncomeGatherList = BillCustomerIncomeV2::getCustomerGatherDataList([], $aWhereIn, []);
            // 基于 bill_cost_v2 聚合成 [日期+客户账号+成本金额]
            $aCostGatherList = BillCostV2::getCustomerGatherDataList([], $aWhereIn, []);

            if ($aIncomeGatherList || $aCostGatherList) {
                $aIncomeApikeyList = array_column($aIncomeGatherList, 'apikey');
                $aCostApikeyList = array_column($aCostGatherList, 'apikey');
                $aApikeyList = array_values(array_filter(array_unique(array_merge($aIncomeApikeyList, $aCostApikeyList))));
                list($aCustomerApikeyGroup, $aCurrentCustomerSalesmanMap, $aHistoryGroup) = $this->buildCustomerDataMap($aApikeyList);
                // 构造 收入数据 数据, ['date + apikey' => ['money' => 0, 'number' => 0], ...]
                $aIncomeData = $this->formatIncomeData($aIncomeGatherList);
                // 构造 成本数据 数据, ['date + apikey' => ['money' => 0], ...]
                $aCostData = $this->formatCostData($aCostGatherList);
                // 特殊计费数据

                $aInsertList = $this->gatherData($aDateGroup, $aCustomerApikeyGroup, $aIncomeData, $aCostData, $aUserDeptMap, $aCurrentCustomerSalesmanMap, $aHistoryGroup);

                // 批量增加
                if ($aInsertList) {
                    StatisticsGatherCustomer::insert($aInsertList);
                }
            }
        }

        return true;
    }

    /**
     * @param $aApikeyList
     * @return array
     */
    private function buildCustomerDataMap($aApikeyList) {
        $aAccountList = Account::getAccountByApikeyList($aApikeyList);
        // 客户ID : [客户账号] map
        $aCustomerApikeyGroup = [];
        foreach ($aAccountList as $aAccount) {
            $aAccount['apikey'] and $aCustomerApikeyGroup[$aAccount['customer_id']][] = $aAccount['apikey'];
        }
        // 客户ID : 当前销售人员 map
        $aCustomerId = array_keys($aCustomerApikeyGroup);
        $aCustomerList = Customer::getCustomerByIdList($aCustomerId);
        $aCurrentCustomerSalesmanMap = array_column($aCustomerList, 'salesman', 'customer_id');
        // 客户ID : [历史销售人员] map
        $aCustomerSalesmanHistoryList = CustomerSalesmanHistory::getList($aCustomerId);
        $aHistoryGroup = [];
        foreach ($aCustomerSalesmanHistoryList as $aHistory) {
            $sCustomerId = $aHistory['customer_id'];
            $aHistoryGroup[$sCustomerId][] = [
                'salesman' => $aHistory['salesman'],
                'start_day' => $aHistory['start_day'],
                'end_day' => $aHistory['end_day'],
            ];
        }

        return [$aCustomerApikeyGroup, $aCurrentCustomerSalesmanMap, $aHistoryGroup];
    }

    /**
     * @return array
     */
    private function getAllSalesmanData(){
        $aUserList = SystemUser::getAllUsers();
        return array_column($aUserList, 'dept_id', 'username');
    }

    /**
     * @param $aGatherList
     * @return array
     */
    private function formatIncomeData($aGatherList){
        $aDataList = [];
        foreach ($aGatherList as $aGather) {
            $sKey = $aGather['date'] . '_' . $aGather['apikey'];
            $aDataList[$sKey] = [
                'money' => $aGather['money'],
                'number' => $aGather['number'],
            ];
        }

        return $aDataList;
    }

    /**
     * @param $aGatherList
     * @return array
     */
    private function formatCostData($aGatherList){
        $aDataList = [];
        foreach ($aGatherList as $aGather) {
            $sKey = $aGather['date'] . '_' . $aGather['apikey'];
            $aDataList[$sKey] = [
                'money' => $aGather['money'],
            ];
        }

        return $aDataList;
    }

    /**
     * @param $aDateList
     * @param $aCustomerApikeyGroup
     * @param $aIncomeData
     * @param $aCostData
     * @param $aUserDeptMap
     * @param $aCurrentCustomerSalesmanMap
     * @param $aHistoryGroup
     * @return array
     */
    private function gatherData($aDateList, $aCustomerApikeyGroup, $aIncomeData, $aCostData, $aUserDeptMap, $aCurrentCustomerSalesmanMap, $aHistoryGroup){
        $iNow = time();

        $aDataList = [];
        foreach ($aDateList as $iDate) {
            foreach ($aCustomerApikeyGroup as $sCustomerId => $aApikeyList) {
                // 当前 销售人员
                $sCurrentSalesman = $aCurrentCustomerSalesmanMap[$sCustomerId];
                // 数据当日 销售人员
                $sSalesman = $this->getSalesman($iDate, $sCustomerId, $aHistoryGroup) ?: $sCurrentSalesman;

                $iIncome = 0;
                $iNumber = 0;
                $iCost = 0;
                foreach ($aApikeyList as $sApikey) {
                    $sKey = $iDate . '_' . $sApikey;

                    $income = $aIncomeData[$sKey]['money'] ?? 0;
                    $number = $aIncomeData[$sKey]['number'] ?? 0;
                    $income and $iIncome = bcadd($iIncome, $income, 6);
                    $number and $iNumber = bcadd($iNumber, $number);

                    $cost = $aCostData[$sKey]['money'] ?? 0;
                    $cost and $iCost = bcadd($iCost, $cost, 6);
                }

                if (!$iIncome && !$iNumber && !$iCost) {
                    continue;
                }

                $aDataList[] = [
                    'date' => $iDate,
                    'customer_id' => $sCustomerId,
                    'dept_id' => $aUserDeptMap[$sSalesman] ?: '',
                    'money' => Func::mockMysqlEncrypt($iIncome),
                    'number' => Func::mockMysqlEncrypt($iNumber),
                    'cost_money' => Func::mockMysqlEncrypt($iCost),
                    'create_time' => $iNow,
                    'modify_time' => $iNow,
                ];
            }
        }

        return $aDataList;
    }

    /**
     * @param $iTheDay
     * @param $sCustomerId
     * @param $aHistoryGroup
     * @return mixed|string
     */
    private function getSalesman($iTheDay, $sCustomerId, $aHistoryGroup){
        $sSalesman = '';

        $aHistoryList = $aHistoryGroup[$sCustomerId] ?? [];
        if (isset($aHistoryList)) {
            foreach ($aHistoryList as $aHistory) {
                if ($aHistory['start_day'] <= $iTheDay && $aHistory['end_day'] && $aHistory['end_day'] >= $iTheDay) {
                    $sSalesman = $aHistory['salesman'];
                    break;
                } else if ($aHistory['start_day'] <= $iTheDay && !$aHistory['end_day']) {
                    $sSalesman = $aHistory['salesman'];
                    break;
                }
            }
        }

        return $sSalesman;
    }
}