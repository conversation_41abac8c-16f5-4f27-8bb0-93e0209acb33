<?php

namespace App\Console\Commands\Dashboard;

use App\Define\StatDefine;
use App\Models\BillCostV2;
use App\Models\BillProductIncomeV2;
use App\Models\CustomerExpend;
use App\Models\Product;
use App\Models\StatisticsGatherProduct;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\MainRepository;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 聚合产品维度数据
 * php artisan dashboard:cron_gather_middle_product --start_date=20240701 --end_date=20240725 --product_id=201,202
 * @uses CronGatherMiddleProduct
 */
class CronGatherMiddleProduct extends Command
{
    protected $signature = 'dashboard:cron_gather_middle_product
	{--start_date= : 数据聚合开始日期，默认昨日（格式Ymd）}
	{--end_date= : 数据聚合结束日期，默认当天（格式Ymd）}
    {--product_id= : 子产品ID（多个产品ID以,隔开）}';

    const CRON_NAME = '聚合产品维度数据';
    /**
     * 处理数据时间段
     * @var null
     */
    private $iStartDate = null;
    private $iEndDate = null;
    /**
     * 聚合产品ID
     * @var array
     */
    private $aProductId = [];

    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s, 数据区间:%s, 子产品:%s",
                $sNow, $cost, "[{$this->iStartDate} - {$this->iEndDate}]", implode(',', $this->aProductId) ?: '(空)');
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $this->output->success($exception->getMessage());

            $oFeishu = new FeishuRepository();
            $aMessage = [
                'err_msg' => $exception->getMessage(),
                'start_date' => $this->iStartDate,
                'end_date' => $this->iEndDate,
                'product_id' => $this->aProductId,
            ];
            $oFeishu->send_card_message_to_chat_group(self::CRON_NAME . '脚本执行失败! ', $aMessage);
        }
    }

    /**
     * @return void
     * @throws \Exception
     */
    private function setParam(){
        $iToday = (int)date('Ymd');
        $this->iStartDate = (int)($this->input->getOption('start_date') ?: date('Ymd', strtotime('-1 day', time())));
        $this->iEndDate = (int)($this->input->getOption('end_date') ?: $iToday);
        if ($this->iEndDate > $iToday) {
            $this->iEndDate = $iToday;
        }
        if ($this->iStartDate > $iToday) {
            throw new \Exception('起始日期范围不能超过当天');
        }
        if ($this->iStartDate > $this->iEndDate) {
            throw new \Exception('起始日期范围不能大于结束时间');
        }

        $sProductId = $this->input->getOption('product_id') ?: 0;
        if ($sProductId) {
            $aProductId = explode(',', $sProductId);
            // 做一次查询,只保留有效产品ID
            $aProductList = Product::getProductListByProductIds($aProductId);
            $this->aProductId = array_column($aProductList, 'product_id');
        }
    }

    /**
     * @return bool
     */
    private function main(){

        // 构造日期
        $aDateList = [];
        $i = $this->iStartDate;
        while ($i <= $this->iEndDate) {
            $aDateList[] = $i;
            $i = date('Ymd', strtotime('+1 day', strtotime($i)));
        }

        $aDateGroupList = array_chunk($aDateList, 10);

        // 构造子父产品ID对照
        $aProductMap = Product::getAllParentIdMap();

        // 按日期分批处理
        foreach ($aDateGroupList as $aDateGroup) {
            $aWhereIn = [
                'date' => $aDateGroup,
                'product_id' => $this->aProductId,
            ];
            // 查重,防止数据重复
            $iCount = StatisticsGatherProduct::getCount([], $aWhereIn);
            if ($iCount) {
                StatisticsGatherProduct::clear([], $aWhereIn);
            }
            // 基于 bill_product_income_v2 聚合成 [日期+子产品+收入金额+调用量]
            $aIncomeGatherList = $this->getIncomeGatherList($aDateGroup);

            // 基于 bill_cost_v2 聚合成 [日期+子产品+成本金额]
            $aCostGatherList = $this->getCostGatherList($aDateGroup);

            if ($aIncomeGatherList || $aCostGatherList) {
                // 构造 收入数据 数据, ['date + product_id' => ['money' => 0, 'number' => 0], ...]
                $aIncomeData = $this->formatIncomeData($aIncomeGatherList);
                // 构造 成本数据 数据, ['date + product_id' => ['money' => 0], ...]
                $aCostData = $this->formatCostData($aCostGatherList);
                // 聚合数据
                $aInsertList = $this->gatherData($aDateGroup, $aProductMap, $aIncomeData, $aCostData);
                // 批量增加
                if ($aInsertList) {
                    StatisticsGatherProduct::insert($aInsertList);
                }
            }
        }

        return true;
    }

    /**
     * @param $aGatherList
     * @return array
     */
    private function formatIncomeData($aGatherList){
        $aDataList = [];
        foreach ($aGatherList as $aGather) {
            $sKey = $aGather['date'] . '_' . $aGather['product_id'];
            $aDataList[$sKey] = [
                'money' => $aGather['income'],
                'number' => $aGather['number'],
            ];
        }

        return $aDataList;
    }

    /**
     * @param $aGatherList
     * @return array
     */
    private function formatCostData($aGatherList){
        $aDataList = [];
        foreach ($aGatherList as $aGather) {
            $sKey = $aGather['date'] . '_' . $aGather['product_id'];
            $aDataList[$sKey] = [
                'money' => $aGather['cost'],
            ];
        }

        return $aDataList;
    }

    /**
     * @param $aDateList
     * @param $aProductMap
     * @param $aIncomeData
     * @param $aCostData
     * @return array
     */
    private function gatherData($aDateList, $aProductMap, $aIncomeData, $aCostData){
        $iNow = time();

        $aDataList = [];
        foreach ($aDateList as $iDate) {
            foreach ($aProductMap as $iProductId => $iFatherId) {
                if (!empty($this->aProductId) && !in_array($iProductId, (array)$this->aProductId)) {
                    continue;
                }

                $sKey = $iDate . '_' . $iProductId;
                $aIncome = $aIncomeData[$sKey] ?? [];
                $aCost = $aCostData[$sKey] ?? [];
                // 收入和成本数据同时为空时, 则不记录
                if (empty($aIncome) && empty($aCost)) {
                    continue;
                }

                $aDataList[] = [
                    'date' => $iDate,
                    'product_id' => $iProductId,
                    'father_id' => $iFatherId ?: $iProductId, // 父产品时, father_id 为他自己
                    'money' => Func::mockMysqlEncrypt($aIncome['money'] ?? 0),
                    'number' => Func::mockMysqlEncrypt($aIncome['number'] ?? 0),
                    'cost_money' => Func::mockMysqlEncrypt($aCost['money'] ?? 0),
                    'create_time' => $iNow,
                    'modify_time' => $iNow,
                ];
            }
        }

        return $aDataList;
    }

    public function getIncomeGatherList($dateGroup){
        $start_date = $dateGroup[0];
        $end_date = end($dateGroup);
        $rep = new MainRepository();
        $dimension = StatDefine::INCOME_DIMENSION_DATE_PRODUCT;
        $res = $rep->getBaseIncome($dimension, $start_date, $end_date, [], [], [], $this->aProductId, ['is_query_month_data' => 0, 'is_halve_income' => 1]);

        if(isset($res['status']) && $res['status'] == 0){
            return $res['data'];
        }

        return [];
    }

    public function getCostGatherList($dateGroup){
        $start_date = $dateGroup[0];
        $end_date = end($dateGroup);
        $rep = new MainRepository();
        $dimension = StatDefine::COST_DIMENSION_DATE_PRODUCT;
        $res = $rep->getBaseCost($dimension, $start_date, $end_date, [], [], [], $this->aProductId, ['is_query_month_data' => 0]);

        if(isset($res['status']) && $res['status'] == 0){
            return $res['data'];
        }

        return [];
    }

}