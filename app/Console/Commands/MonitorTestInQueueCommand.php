<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

/**
 * Class MonitorTestInQueueCommand 预警系统数据入队测试脚本
 * @package App\Console\Commands
 */
class MonitorTestInQueueCommand extends Command
{
	protected $signature = 'monitor:test_in_queue';
	
	protected $description = '预警系统数据入队测试脚本';
	
	protected $key = 'request_log';
	
	public function handle()
	{
		$redis = Redis::connection('monitor_queue');
		
		$max = 500000;
		//$progress = $this->output->createProgressBar($max);
		for ($i = 0; $i < $max; $i++) {
			$this->createUserOrChannelRequestLog($redis);
			//$progress->advance();
		}
		
		//$progress->finish();
		//$this->output->success("入库完成");
		
	}
	
	/**
	 * 创建一条用户/渠道请求日志
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/27 15:02
	 *
	 * @param $redis \Redis
	 *
	 * @return void
	 */
	protected function createUserOrChannelRequestLog($redis)
	{
		$data = [];
		if (1 == rand(0, 9) % 2) {
			$data['product'] = [$this->getAProductLog()];
			
			$redis->lPush('request_log', json_encode($data, JSON_UNESCAPED_UNICODE));
		} else {
			$data['channel'] = [$this->getAChannelLog()];
			$redis->lPush('request_log', json_encode($data, JSON_UNESCAPED_UNICODE));
		}
	}
	
	/**
	 * 获取一个产品的日志
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/29 18:12
	 *
	 * @return array
	 */
	protected function getAProductLog()
	{
		$apikey   = $this->getRandApikey();
		$in_param = $this->getRandString(64);
		$pid      = $this->getRandProductId();
		$operator = $this->getRandOperator();
		$run_time = rand(100, 1000);
		$status   = 0;
		$value    = rand(-6, 1000);
		$sid      = $this->getRandString(32);
		$time     = time();
		
		return compact('pid', 'apikey', 'operator', 'in_param', 'run_time', 'status', 'value', 'sid', 'time');
	}
	
	/**
	 * 获取一个渠道的日志
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/29 18:43
	 *
	 * @return array
	 */
	protected function getAChannelLog()
	{
		$info       = $this->getRandString(10);
		$channel_id = $this->getRandChannelId();
		$apikey     = $this->getRandApikey();
		$in_param   = $this->getRandString(64);
		$pid        = $this->getRandProductId();
		$run_time   = rand(100, 1000);
		$status     = 0;
		$value      = rand(-6, 1000);
		$sid        = $this->getRandString(32);
		$time       = time();
		
		return compact('channel_id', 'pid', 'in_param', 'run_time', 'status', 'info', 'value', 'sid', 'time');
	}
	
	/**
	 * 获取一个随机的apikey
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/29 17:42
	 *
	 * @return string
	 */
	protected function getRandApikey()
	{
		$apikeys = [
			'b98296d1fe1c563cae1381a1c34ba721',
			'acb606b5193c9f89f79291640db7f8e2',
			'7ebc928ae888f93fc19952087f3a8041',
			'5d14c3650355256d318481ce74bebf9c',
			'0420ddd0de52c36712b17a58b5cbedd5',
			'aaf4d04ba6417a3855aa507de36362aa',
			'239c782dc7043502e294fee6ec1ff997',
			'23f2186b7ac407891ccb9a5bcc5cd18f',
			'edec3682eeb4e34c056bca3efe3e586c',
			'b54ea123f06312e2d7d9add421094689',
		];
		
		return $apikeys[rand(0, 9)];
	}
	
	/**
	 * 获取一个随机的产品ID
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/29 18:10
	 *
	 * @return integer
	 */
	protected function getRandProductId()
	{
		$product_ids = [
			101,
			102,
			104,
			105,
			106,
			107,
			108,
			109,
			200,
			201,
			202,
			203,
			204,
			205,
			206,
			207,
			208,
			209,
			210,
			211,
		];
		
		return $product_ids[rand(0, 19)];
	}
	
	/**
	 * 获取一个随机的渠道ID
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/29 18:43
	 *
	 * @return integer
	 */
	protected function getRandChannelId()
	{
		$channel_ids = [
			1,
			2,
			3,
			4,
			5,
			6,
			7,
			8,
			9,
			10,
			11,
			12,
			13,
			14,
			15,
			16,
			17,
			18,
			19,
			20,
		];
		
		return $channel_ids[rand(0, 19)];
	}
	
	/**
	 * 获取随机运营商
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/29 18:52
	 *
	 * @return string
	 */
	protected function getRandOperator()
	{
		$operators = [
			'CMCC',
			'CUCC',
			'CTCC',
			'BJCMCC',
			'SDCMCC',
			'JSCMCC',
			'HBCMCC',
			'SCCMCC',
		];
		
		return $operators[rand(0, 7)];
	}
	
	/**
	 * 获取一个随机的字符串
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/27 15:15
	 *
	 * @param $length integer 字符长度
	 *
	 * @return string
	 */
	protected function getRandString($length)
	{
		$data = [
			"a",
			"b",
			"c",
			"d",
			"e",
			"f",
			"g",
			"h",
			"i",
			"j",
			"k",
			"l",
			"m",
			"n",
			"o",
			"p",
			"q",
			"r",
			"s",
			"t",
			"u",
			"v",
			"w",
			"x",
			"y",
			"z",
			"A",
			"B",
			"C",
			"D",
			"E",
			"F",
			"G",
			"H",
			"I",
			"J",
			"K",
			"L",
			"M",
			"N",
			"O",
			"P",
			"Q",
			"R",
			"S",
			"T",
			"U",
			"V",
			"W",
			"X",
			"Y",
			"Z",
			"0",
			"1",
			"2",
			"3",
			"4",
			"5",
			"6",
			"7",
			"8",
			"9",
			"a",
			"b",
			"c",
			"d",
			"e",
			"f",
			"g",
			"h",
			"i",
			"j",
			"k",
			"l",
			"m",
			"n",
			"o",
			"p",
			"q",
			"r",
			"s",
			"t",
			"u",
			"v",
			"w",
			"x",
			"y",
			"z",
			"A",
			"B",
			"C",
			"D",
			"E",
			"F",
			"G",
			"H",
			"I",
			"J",
			"K",
			"L",
			"M",
			"N",
			"O",
			"P",
			"Q",
			"R",
			"S",
			"T",
			"U",
			"V",
			"W",
			"X",
			"Y",
			"Z",
			"0",
			"1",
			"2",
			"3",
			"4",
			"5",
			"6",
			"7",
			"8",
			"9",
		];
		
		return implode('', array_values(array_intersect_key($data, array_flip(array_rand($data, $length)))));
	}
}