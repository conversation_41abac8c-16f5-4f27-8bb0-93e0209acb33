<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/4/28 0028
 * Time: 16:16
 */

namespace App\Console\Commands;

//生成缓存信息
use App\Console\Commands\CacheDriver\ApikeyAccountIdMapping;
use App\Console\Commands\CacheDriver\ApikeyCustomerIdMapping;
use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Console\Commands\CacheDriver\CacheDriverFactory;
use App\Console\Commands\CacheDriver\ChildrenProduct200;
use App\Console\Commands\CacheDriver\ChildrenProduct210;
use App\Console\Commands\CacheDriver\ChildrenProduct615;
use App\Console\Commands\CacheDriver\Driver;
use Illuminate\Console\Command;

/**
 * 1.210子产品信息
 * 2.200子产品信息
 * 3.615子产品信息
 * 4.apikey-customer_id映射信息
 *
 **/
class GenCacheCommand extends Command
{
    protected $cacheDriverFactory;

    public function __construct()
    {
        $this->cacheDriverFactory = new CacheDriverFactory();
        $availableDriverArray     = $this->cacheDriverFactory->getAvailableDriver();

        $typeString = "";
        foreach ($availableDriverArray as $type => $name) {
            $typeString .= "{$type} : {$name}   ";
        }

        $this->signature = 'gen:cache
        {--type=all : 生成的缓存标记。目前仅支持 >>>> all : 重新缓存所有驱动内容   ' . $typeString . '}';
        parent::__construct();
    }

    public function handle()
    {

        $type = $this->input->getOption('type');

        if ($type == 'all') {
            foreach ($this->cacheDriverFactory->getAvailableDriver() as $type => $name) {
                $this->cacheDriverFactory->getCacheDriver($type)->cache();
                $this->output->success("[{$name}] 缓存成功");
            }
        } else {
            $driver = $this->cacheDriverFactory->getCacheDriver($type);
            $name   = $driver->getName();
            $driver->cache();
            $this->output->success("[{$name}] 缓存成功");
        }
    }
}