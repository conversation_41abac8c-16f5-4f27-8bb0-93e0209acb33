<?php

namespace App\Console\Commands;

use App\Models\Monitor\ConfigProductValue;
use App\Models\MonitorLogProductModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class TestCreateAllValueSpread extends Command
{
	protected $signature = 'test:create_all_value_spread';
	
	protected $section;
	
	public function __construct()
	{
		parent::__construct();
		
		$this->section = ConfigProductValue::select([
			'type',
			'value',
		])
										   ->where('pid', 218)
										   ->get()
										   ->toArray();
	}
	
	public function handle()
	{
		$start_time = time();
		ini_set("memory_limit", '4G');
		
		$data = $this->getInfo();
		
		$result   = [];
		$run_time = 0;
		$total    = 0;
		foreach ($data as $old_value => $item) {
			$value = $this->getValue($old_value);
			if (!array_key_exists($value, $result)) {
				$result[$value] = 0;
			}
			$result[$value] += $item['total'];
			$run_time       += $item['run_time'];
			$total          += $item['total'];
		}
		
		
		$end_time = time();
		//内存
		$memory = memory_get_usage();
		$memory /= 1024;
		$memory /= 1024;
		
		$time = $end_time - $start_time;
		
		$this->output->writeln("占用内存: {$memory} MB");
		$this->output->writeln("占用时间: {$time} S");
		
		//平均响应时间
		$run_time = $run_time / $total;
		$this->output->writeln("平均响应时间: {$run_time} S");
		
		$count = count($result);
		$this->output->writeln("结果数据条数：{$count}");
		//halt($result);
	}
	
	/**
	 * 获取值(根据数据库中配置的值范围区间，将其生成一个新的值)
	 *
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 13:43
	 *
	 * @param $old_value integer 单条原始日志
	 *
	 * @return integer
	 */
	protected function getValue($old_value)
	{
		
		foreach ($this->section as $sectionItem) {
			$value = $sectionItem['value'];
			$type  = $sectionItem['type'];
			if (false !== $result = $this->isValue($value, $old_value, $type)) {
				return $result;
			}
		}
		
		return null;
	}
	
	/**
	 * 判断是否为当前区间的值
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 13:51
	 *
	 * @param $value integer 目标值
	 * @param $old   integer 原始日志值
	 * @param $type  integer 判断类型
	 *
	 * @return boolean
	 */
	protected function isValue($value, $old, $type)
	{
		switch ($type) {
			case 1:
				//等于
				
				return $value == $old && strlen($value) == strlen($old) ? $value : false;
				break;
			case 2:
				//大于
				
				return $old > $value ? '>' . $value : false;
				break;
			case 3:
				//小于
				
				return $old < $value ? '<' . $value : false;
				break;
			case 4:
				//区间
				$value = trim(trim($value, '('), ')');
				list($min, $max) = explode(',', $value);
				
				return $old >= $min && $old <= $max ? $value : false;
				break;
			case 5:
				//大于等于
				
				return $old >= $value ? '>=' . $value : false;
				break;
			case 6:
				//小于等于
				
				return $old <= $value ? '<=' . $value : false;
				break;
		}
		
		return false;
	}
	
	
	/**
	 * 获取数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 15:13
	 *
	 * @return array
	 */
	protected function getInfo()
	{
		$model = new MonitorLogProductModel();
		
		$result = [];
		$model->prefix('20201127')
						->select([DB::raw('DISTINCT `in_param`'), 'value', 'run_time'])
						->where('status', 0)
						->where('apikey', '')
						->where('product_id', 281)
						->get()
						->map(function ($item) use (&$result) {
							$value = $item['value'];
			
							if (!array_key_exists($value, $result)) {
								$result[$value] = [
									'total'    => 0,
									'run_time' => 0,
								];
							}
							$result[$value]['total']++;
							$result[$value]['run_time'] += $item['run_time'];
						});
		
		//$this->output->writeln("内存峰值：{$memory} MB");
		
		return $result;
		
	}
}