<?php

namespace App\Console\Commands;

use App\Models\BillCost;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\MongoUpstreamBill;
use App\Models\Product;
use App\Models\StatisticsInterfaceUsage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Class TempTransOldCost 将旧版成本数据导入到新版中
 * 只导200、210、1000、401的数据
 * 会导到 statistics_interface_usage、bill_cost 两个表中
 * @package App\Console\Commands
 */
class TempTransOldCost extends Command
{
    protected $signature   = 'temp:trans_old_cost
    {--date= : 数据开始日期Ymd}
    {--days= : 天数}';
    protected $date;
    protected $days =1;
    protected $channel_map = [];
    protected $channel_interface_map = [];
    protected $bxf_pid_map = [];
    protected $product_ids = [];
    protected $transStat = [];
    protected $transCost = [];

	public function handle()
	{
        if ($this->setParams()) {
            for ($i = 0; $i < $this->days; $i++) {
                $curr_date = date('Ymd', strtotime("+{$i} days", strtotime($this->date)));
                //判断是否已经存在新版本数据,如果存在则不会转移
                $this->setTrans($curr_date, [200, 210, 401]);
                //按天从mongo中拿旧账单
                $mongo_data = $this->getMongoBill($curr_date);
                foreach ($mongo_data as $bill) {

                    if (count($bill['details']) != 6) continue;//特殊消耗
                    if (in_array($bill['product_id'], $this->product_ids[200])) {
                        $this->transBMY($bill);  //邦秒验
                    } elseif (in_array($bill['product_id'], $this->product_ids[210])) {
                        continue;
                        $this->transBXF($bill);  //邦信分
                    } elseif (in_array($bill['product_id'], $this->product_ids[401])) {
                        continue;
                        $this->transBQC($bill);  //邦企查
                    }
                }
                echo $curr_date."重跑完毕".PHP_EOL;
            }
        }
		$this->output->success('校验通过');
	}
    /**
     * 设置参数
     *
     * @access protected
     *
     * @return boolean
     **/
    protected function setParams()
    {
        $date       = $this->input->getOption('date');
        $this->date = empty($date) ? '20200101' : $date;
        if (!preg_match('/^\d{8}$/', $this->date)) {
            $this->output->error('日期格式不正确');
            return false;
        }

        $days = $this->input->getOption('days');
        $this->days = empty($days) ? 1 : intval($days);
        if ($days < 0) {
            $this->output->error('days不正确');
            return false;
        }
        $this->product_ids[200] = array_column(Product::getChildProduct([200]), 'product_id');
        $this->product_ids[210] = array_column(Product::getChildProduct([210,1000]), 'product_id');
        $this->product_ids[401] = array_column(Product::getChildProduct([401]), 'product_id');
        $this->channel_map = array_column(Channel::select(['channel_id', 'name'])->get()->toArray(), 'channel_id', 'name');
        $this->channel_interface_map = $this->getChannelInterfaceMap();
        $this->bxf_pid_map = $this->getBXFPidMap();
        return true;
    }
    protected function setTrans($date, $father_ids){
        foreach ($father_ids as $fid) {
            $this->transStat[$fid] = true;//$this->checkHasStat($date, $fid);
            $this->transCost[$fid] = true;//$this->checkHasCost($date, $fid);
        }
    }
    /**
     * 转移邦秒验数据
     * @param $bill
     */
	protected function transBMY($bill) {
        $iid = $this->getBMYIid($bill);
        $encrypt = $this->getBMYEncrypt($bill);
        $stat_base = [
            'apikey' => $bill['apikey'],
            'encrypt' => $encrypt,
            'product_id' => $bill['product_id'],
            'date' => $bill['date'],
            'create_time' => time(),
            'update_time' => time(),
            'interface_id' => $iid,
            'iid' => $iid,
        ];
        $cost_base = [
            'interface_id' => $iid,
            'apikey' => $bill['apikey'],
            'encrypt' => $encrypt,
            'product_id' => $bill['product_id'],
            'date' => $bill['date'],
            'create_time' => time(),
            'update_time' => time(),
        ];
        //根据运营商不同，生成不同的账单
        if ($bill['details']['yd']['fee_number'] > 0) {
            $stat = [
                'operator' => 'CMCC',
                'total' => $bill['details']['yd']['fee_number'],
                'success' => $bill['details']['yd']['fee_number'],
                'valid' => $bill['details']['yd']['fee_number']
            ];
            $cost = [
                'operator' => 'CMCC',
                'number' => $bill['details']['yd']['fee_number'],
                'money' => $bill['details']['yd']['money']
            ];
            $this->transStat[200] && $this->insertStat(array_merge($stat, $stat_base));
            //自有渠道只有量没有钱，不用生成账单
            if ($bill['details']['yd']['money'] > 0 && $this->transCost[200]) {
                $this->insertCost(array_merge($cost, $cost_base));
            }
        }
        if ($bill['details']['lt']['fee_number'] > 0) {
            $stat = [
                'operator' => 'CUCC',
                'total' => $bill['details']['lt']['fee_number'],
                'success' => $bill['details']['lt']['fee_number'],
                'valid' => $bill['details']['lt']['fee_number']
            ];
            $cost = [
                'operator' => 'CUCC',
                'number' => $bill['details']['lt']['fee_number'],
                'money' => $bill['details']['lt']['money']
            ];
            $this->transStat[200] && $this->insertStat(array_merge($stat, $stat_base));
            //自有渠道只有量没有钱，不用生成账单
            if ($bill['details']['lt']['money'] > 0 && $this->transCost[200]) {
                $this->insertCost(array_merge($cost, $cost_base));
            }
        }
        if ($bill['details']['dx']['fee_number'] > 0) {
            $stat = [
                'operator' => 'CTCC',
                'total' => $bill['details']['dx']['fee_number'],
                'success' => $bill['details']['dx']['fee_number'],
                'valid' => $bill['details']['dx']['fee_number']
            ];
            $cost = [
                'operator' => 'CTCC',
                'number' => $bill['details']['dx']['fee_number'],
                'money' => $bill['details']['dx']['money']
            ];
            $this->transStat[200] && $this->insertStat(array_merge($stat, $stat_base));
            //自有渠道只有量没有钱，不用生成账单
            if ($bill['details']['dx']['money'] > 0 && $this->transCost[200]) {
                $this->insertCost(array_merge($cost, $cost_base));
            }
        }
    }
    /**
     * 转移邦信分数据
     * @param $bill
     */
    protected function transBXF($bill) {
        list($channel, $interface) = $this->getBXFChannelIId($bill['channel']);
        $channel_id = $this->channel_map[$channel];
        $iid = $this->channel_interface_map[$channel_id][$interface];
        $product_id = $this->bxf_pid_map[$bill['channel']];
        if (!$bill['details']['all']['fee_number']) {
            return; //过滤无意义数据
        }
        $stat_data = [
            'apikey' => $bill['apikey'],
            'operator' => $channel,
            'product_id' => $product_id,
            'interface_id' => $iid,
            'date' => $bill['date'],
            'create_time' => time(),
            'update_time' => time(),
            'iid' => $iid,
            'total' => $bill['details']['all']['fee_number'],
            'success' => $bill['details']['succ']['fee_number'],
            'valid' => $bill['details']['succ']['fee_number']
        ];
        $cost_data = [
            'apikey' => $bill['apikey'],
            'operator' => $channel,
            'product_id' => $product_id,
            'interface_id' => $iid,
            'date' => $bill['date'],
            'create_time' => time(),
            'update_time' => time(),
            'number' => ($bill['details']['all']['money'] > 0) ? $bill['details']['all']['fee_number'] : $bill['details']['succ']['fee_number'],
            'money' => $bill['details']['succ']['money'] + $bill['details']['all']['money'] + $bill['details']['failed']['money'],
        ];
        $this->transStat[210] && $this->insertStat($stat_data);
        $this->transCost[210] && $this->insertCost($cost_data);
    }
    /**
     * 转移邦企查数据
     * @param $bill
     */
    protected function transBQC($bill) {
        $iid = $this->getBQCIId($bill['channel']);
        $product_id = 401;
        if (!$bill['details']['all']['fee_number']) {
            return; //过滤无意义数据
        }
        $stat_data = [
            'apikey' => $bill['apikey'],
            'product_id' => $product_id,
            'date' => $bill['date'],
            'create_time' => time(),
            'update_time' => time(),
            'interface_id' => $iid,
            'iid' => $iid,
            'total' => $bill['details']['all']['fee_number'],
            'success' => $bill['details']['succ']['fee_number'],
            'valid' => $bill['details']['succ']['fee_number']
        ];
        $cost_data = [
            'interface_id' => $iid,
            'apikey' => $bill['apikey'],
            'product_id' => $product_id,
            'date' => $bill['date'],
            'create_time' => time(),
            'update_time' => time(),
            'number' => ($bill['details']['all']['money'] > 0) ? $bill['details']['all']['fee_number'] : $bill['details']['succ']['fee_number'],
            'money' => $bill['details']['succ']['money'] + $bill['details']['all']['money'] + $bill['details']['failed']['money'],
        ];
        $this->transStat[401] && $this->insertStat($stat_data);
        $this->transCost[401] && $this->insertCost($cost_data);
    }

    /**
     * 判断当天是否有新版账单，如果有则不执行转移操作
     * @param $date
     * @param $father_id
     */
    protected function checkHasStat($date, $father_id){
        $res = StatisticsInterfaceUsage::select([DB::raw('count(*) AS total')])
            ->where([['date', '=', $date]])->whereIn('product_id', $this->product_ids[$father_id])->get()->toArray();
        if ($res[0]['total'] > 0) echo $date."已存在新版调用量,程序跳过".PHP_EOL;
        return $res[0]['total'] == 0;
    }
    protected function checkHasCost($date, $father_id){
        $res = BillCost::select([DB::raw('count(*) AS total')])
            ->where([['date', '=', $date]])->whereIn('product_id', $this->product_ids[$father_id])->get()->toArray();
        if ($res[0]['total'] > 0) echo $date."已存在新版成本,程序跳过".PHP_EOL;
        return $res[0]['total'] == 0;
    }

    protected function insertStat($item) {
        StatisticsInterfaceUsage::insert($item);
    }

    protected function insertCost($item) {
	    BillCost::insert($item);
    }

    protected function getMongoBill($date) {
        return  MongoUpstreamBill::query()
            ->raw(function ($collcetion) use ($date) {
                $aggregate = [['$match' => ['date' => intval($date)]]];
                return $collcetion->aggregate($aggregate);
            })
            ->toArray();
    }

    protected function getChannelInterfaceMap() {
	    $channel_interface_map = [];
	    $interfaces = ChannelInterface::select(['id', 'channel_id', 'name'])->get()->toArray();
	    foreach ($interfaces as $item) {
            $channel_interface_map[$item['channel_id']][$item['name']] = $item['id'];
        }
	    return $channel_interface_map;
    }
	
    protected function getBMYEncrypt($bill) {
        if (strpos($bill['channel'], 'MD5')){
            return 'MD5';
        }
        if (strpos($bill['channel'], 'SHA256')) {
            return 'SHA256';
        }
        return 'CLEAR';
    }

    protected function getBXFChannelIId($old_channel){
        $channel = substr($old_channel, 0, strpos($old_channel, '_'));
        $interface = substr($old_channel, strpos($old_channel, '_') + 1);
        return [$channel, $interface];
    }
    protected function getBQCIId($old_channel){
//        $channel = [
//            'tianyancha' => 201,
//            'chuanglan' => 120
//        ];
        $interface = [
            'tianyancha' => 383,
            'chuanglan' => 259
        ];
        return $interface[$old_channel];
    }

	protected function getBMYIid($bill){
        $channel_name = trim(trim($bill['channel'], 'MD5'), 'SHA256');
        $channel = [
            101 => 'juhe',//聚合
            102 => 'datau',//大有
            103 => 'geo',//集奥，弃用
            104 => 'xiaoan',//小安
            105 => 'dumiao',//读秒
            106 => 'fkss',//风控实时状态
            107 => 'tanzhen',//探真
            108 => 'liantong',//联通，弃用
            109 => 'jitui',//极推
            110 => 'shudun',//数盾，联通直连
            111 => 'jindan',//金蛋
            112 => 'dumiaov2',//品钛v2
            113 => 'czty',//诚智天扬
            114 => 'dianxin',//电信
            115 => 'geov2',//集奥v2
            116 => 'shmf',//数盒魔方
            117 => 'lingdi',//领地
            118 => 'xinyan',//新颜
            119 => 'dumiaov3',//品钛v3
            120 => 'chuanglan',//创蓝
            121 => 'jindun',//金盾
            122 => 'zhongqing',//中青信用
            123 => 'rongshu',//青岛融数
            124 => 'self', //自有渠道
            125 => 'youkun', //游昆
            126 => 'hanjing', //瀚境
        ];
        $interface = [
            201 => 'CheckName',
            202 => 'NetTime',
            203 => 'NetStatus',
            204 => 'ConsLevel',
            205 => 'CheckWork',
            206 => 'DownTimes',
            207 => 'Flow',
            208 => 'CheckFour',
            211 => 'Attribution',
            212 => 'CheckAddr',
            213 => 'CheckNm',
            214 => 'CheckTwo',
            215 => 'CheckBlack',
            216 => 'CheckRealStatus',
            217 => 'IsContacts',
            218 => 'BankcardCore',
            219 => 'RealCity',
            220 => 'MaxCity',
            223 => 'Active',
            230 => 'CreScore',
            231 => 'OnlScore',
            232 => 'RiskPoints',
            233 => 'ThreeScore',
            234 => 'ConScore',
            235 => 'OfflScore',
            237 => 'OnlHigScore',
            238 => 'CreScoreHigh',
            239 => 'SwitchNetWork',
            240 => 'Slience',
            310 => 'FuMianScore',
            311 => 'NetMoveScore',
            312 => 'DuoTouScore',
            313 => 'ElecBusinessScore',
            314 => 'OnlineCashOneScore',
            315 => 'OnlineCashTwoScore',
            316 => 'OnlineCashThreeScore',
            317 => 'OnlineCashFourScore',
            318 => 'LiveScore',
            320 => 'Active',
            321 => 'ActiveBack',
            322 => 'ActiveBackPre30',
            323 => 'ActiveBackPre60',
            324 => 'ActiveBackPre90',
            325 => 'DiffBackPre30',
            326 => 'DiffBackPre60',
            327 => 'DiffBackPre90',
            328 => 'DiffBackPre90Avg',
            329 => 'DiffBackToday',
            330 => 'DiffBackTodayMax',
            331 => 'DiffBackTodayMin',
            900 => 'Opdata900',
        ];
        $channel_id = array_search($channel_name, $channel);
        return $this->channel_interface_map[$channel_id][$interface[$bill['product_id']]];
    }
    protected function getBXFPidMap(){
        return [
            'CUCC_score_4' => '251',
            'CUCC_cs_latest_phone_days' => '252',
            'CUCC_cs_first_phone_days' => '253',
            'CUCC_cs_phone_numbers' => '254',
            'CUCC_cs_total_phone_times' => '255',
            'CUCC_cs_called_times' => '256',
            'CUCC_cs_phone_times_in_15s' => '257',
            'CUCC_cs_phone_times_between_15_30s' => '258',
            'CUCC_cs_phone_times_over_60s' => '259',
            'CUCC_yscs_latest_phone_days' => '260',
            'CUCC_yscs_first_phone_days' => '261',
            'CUCC_yscs_phone_numbers' => '262',
            'CUCC_yscs_total_phone_times' => '263',
            'CUCC_yscs_called_times' => '264',
            'CUCC_yscs_phone_times_in_15s' => '265',
            'CUCC_yscs_phone_times_between_15_30s' => '266',
            'CUCC_yscs_phone_times_over_60s' => '267',
            'CUCC_cs_phone_numbers_in_7_days' => '268',
            'CUCC_cs_called_times_in_7_days' => '269',
            'CUCC_yscs_phone_numbers_in_7_days' => '270',
            'CUCC_yscs_called_times_in_7_days' => '271',
            'CUCC_cs_phone_numbers_in_14_days' => '272',
            'CUCC_cs_called_times_in_14_days' => '273',
            'CUCC_yscs_phone_numbers_in_14_days' => '274',
            'CUCC_yscs_called_times_in_14_days' => '275',
            'CUCC_cs_phone_numbers_in_21_days' => '276',
            'CUCC_cs_called_times_in_21_days' => '277',
            'CUCC_yscs_phone_numbers_in_21_days' => '278',
            'CUCC_yscs_called_times_in_21_days' => '279',
            'CUCC_cs_phone_numbers_in_30_days' => '280',
            'CUCC_cs_called_times_in_30_days' => '281',
            'CUCC_yscs_phone_numbers_in_30_days' => '282',
            'CUCC_yscs_called_times_in_30_days' => '283',
            'CUCC_cs_phone_numbers_between_30_60_days' => '284',
            'CUCC_cs_called_times_between_30_60_days' => '285',
            'CUCC_yscs_phone_numbers_between_30_60_days' => '286',
            'CUCC_yscs_called_times_between_30_60_days' => '287',
            'CUCC_dm_score' => '288',
            'CUCC_total_call_count' => '241',
            'CUCC_ave_number_count' => '242',
            'CUCC_called_rate_str' => '243',
            'CUCC_max_block' => '244',
            'CUCC_morning_rate_str' => '245',
            'CUCC_first_day' => '246',
            'CUCC_last_day' => '247',
            'CUCC_score_3' => '289',
            'CUCC_score_1' => '290',
            'CUCC_score_2' => '291',
            'CUCC_score_5' => '292',
            'CUCC_score_6' => '293',
            'CUCC_score_7' => '294',
            'CUCC_score_8' => '295',
            'CUCC_score_9' => '296',
            'CUCC_score_10' => '297',
            'CUCC_score_11' => '298',
            'JSCMCC_ESR10000000183' => '252',
            'JSCMCC_ESR10000000184' => '253',
            'JSCMCC_ESR10000000185' => '254',
            'JSCMCC_ESR10000000186' => '255',
            'JSCMCC_ESR10000000187' => '256',
            'JSCMCC_ESR10000000188' => '257',
            'JSCMCC_ESR10000000189' => '258',
            'JSCMCC_ESR10000000190' => '259',
            'JSCMCC_ESR10000000191' => '260',
            'JSCMCC_ESR10000000192' => '261',
            'JSCMCC_ESR10000000193' => '262',
            'JSCMCC_ESR10000000194' => '263',
            'JSCMCC_ESR10000000195' => '264',
            'JSCMCC_ESR10000000196' => '265',
            'JSCMCC_ESR10000000197' => '266',
            'JSCMCC_ESR10000000198' => '267',
            'JSCMCC_ESR10000000199' => '268',
            'JSCMCC_ESR10000000200' => '269',
            'JSCMCC_ESR10000000201' => '270',
            'JSCMCC_ESR10000000202' => '271',
            'JSCMCC_ESR10000000203' => '272',
            'JSCMCC_ESR10000000204' => '273',
            'JSCMCC_ESR10000000205' => '274',
            'JSCMCC_ESR10000000206' => '275',
            'JSCMCC_ESR10000000207' => '276',
            'JSCMCC_ESR10000000208' => '277',
            'JSCMCC_ESR10000000209' => '278',
            'JSCMCC_ESR10000000210' => '279',
            'JSCMCC_ESR10000000211' => '280',
            'JSCMCC_ESR10000000212' => '281',
            'JSCMCC_ESR10000000213' => '282',
            'JSCMCC_ESR10000000214' => '283',
            'JSCMCC_ESR10000000215' => '284',
            'JSCMCC_ESR10000000216' => '285',
            'JSCMCC_ESR10000000217' => '286',
            'JSCMCC_ESR10000000218' => '287',
            'JSCMCC_ESR10000000181' => '251',
            'JSCMCC_max_block' => '244',
            'SCCMCC_cs_latest_phone_days' => '252',
            'SCCMCC_cs_first_phone_days' => '253',
            'SCCMCC_cs_phone_numbers' => '254',
            'SCCMCC_cs_total_phone_times' => '255',
            'SCCMCC_cs_called_times' => '256',
            'SCCMCC_cs_phone_times_in_15s' => '257',
            'SCCMCC_cs_phone_times_between_15_30s' => '258',
            'SCCMCC_cs_phone_times_over_60s' => '259',
            'SCCMCC_yscs_latest_phone_days' => '260',
            'SCCMCC_yscs_first_phone_days' => '261',
            'SCCMCC_yscs_phone_numbers' => '262',
            'SCCMCC_yscs_total_phone_times' => '263',
            'SCCMCC_yscs_called_times' => '264',
            'SCCMCC_yscs_phone_times_in_15s' => '265',
            'SCCMCC_yscs_phone_times_between_15_30s' => '266',
            'SCCMCC_yscs_phone_times_over_60s' => '267',
            'SCCMCC_cs_phone_numbers_in_7_days' => '268',
            'SCCMCC_cs_called_times_in_7_days' => '269',
            'SCCMCC_yscs_phone_numbers_in_7_days' => '270',
            'SCCMCC_yscs_called_times_in_7_days' => '271',
            'SCCMCC_cs_phone_numbers_in_14_days' => '272',
            'SCCMCC_cs_called_times_in_14_days' => '273',
            'SCCMCC_yscs_phone_numbers_in_14_days' => '274',
            'SCCMCC_yscs_called_times_in_14_days' => '275',
            'SCCMCC_cs_phone_numbers_in_21_days' => '276',
            'SCCMCC_cs_called_times_in_21_days' => '277',
            'SCCMCC_yscs_phone_numbers_in_21_days' => '278',
            'SCCMCC_yscs_called_times_in_21_days' => '279',
            'SCCMCC_cs_phone_numbers_in_30_days' => '280',
            'SCCMCC_cs_called_times_in_30_days' => '281',
            'SCCMCC_yscs_phone_numbers_in_30_days' => '282',
            'SCCMCC_yscs_called_times_in_30_days' => '283',
            'SCCMCC_cs_phone_numbers_between_30_60_days' => '284',
            'SCCMCC_cs_called_times_between_30_60_days' => '285',
            'SCCMCC_yscs_phone_numbers_between_30_60_days' => '286',
            'SCCMCC_yscs_called_times_between_30_60_days' => '287',
            'SCCMCC_main_score' => '251',
            'SCCMCC_dm_score' => '288',
            'SCCMCC_total_call_count' => '241',
            'SCCMCC_ave_number_count' => '242',
            'SCCMCC_called_rate' => '243',
            'SCCMCC_max_block' => '244',
            'SCCMCC_morning_rate' => '245',
            'SCCMCC_first_day' => '246',
            'SCCMCC_last_day' => '247',
            'SDCMCC_cs_latest_phone_days' => '252',
            'SDCMCC_cs_first_phone_days' => '253',
            'SDCMCC_cs_phone_numbers' => '254',
            'SDCMCC_cs_total_phone_times' => '255',
            'SDCMCC_cs_called_times' => '256',
            'SDCMCC_cs_phone_times_in_15s' => '257',
            'SDCMCC_cs_phone_times_between_15_30s' => '258',
            'SDCMCC_cs_phone_times_over_60s' => '259',
            'SDCMCC_yscs_latest_phone_days' => '260',
            'SDCMCC_yscs_first_phone_days' => '261',
            'SDCMCC_yscs_phone_numbers' => '262',
            'SDCMCC_yscs_total_phone_times' => '263',
            'SDCMCC_yscs_called_times' => '264',
            'SDCMCC_yscs_phone_times_in_15s' => '265',
            'SDCMCC_yscs_phone_times_between_15_30s' => '266',
            'SDCMCC_yscs_phone_times_over_60s' => '267',
            'SDCMCC_cs_phone_numbers_in_7_days' => '268',
            'SDCMCC_cs_called_times_in_7_days' => '269',
            'SDCMCC_yscs_phone_numbers_in_7_days' => '270',
            'SDCMCC_yscs_called_times_in_7_days' => '271',
            'SDCMCC_cs_phone_numbers_in_14_days' => '272',
            'SDCMCC_cs_called_times_in_14_days' => '273',
            'SDCMCC_yscs_phone_numbers_in_14_days' => '274',
            'SDCMCC_yscs_called_times_in_14_days' => '275',
            'SDCMCC_cs_phone_numbers_in_21_days' => '276',
            'SDCMCC_cs_called_times_in_21_days' => '277',
            'SDCMCC_yscs_phone_numbers_in_21_days' => '278',
            'SDCMCC_yscs_called_times_in_21_days' => '279',
            'SDCMCC_cs_phone_numbers_in_30_days' => '280',
            'SDCMCC_cs_called_times_in_30_days' => '281',
            'SDCMCC_yscs_phone_numbers_in_30_days' => '282',
            'SDCMCC_yscs_called_times_in_30_days' => '283',
            'SDCMCC_cs_phone_numbers_between_30_60_days' => '284',
            'SDCMCC_cs_called_times_between_30_60_days' => '285',
            'SDCMCC_yscs_phone_numbers_between_30_60_days' => '286',
            'SDCMCC_yscs_called_times_between_30_60_days' => '287',
            'SDCMCC_score_1' => '251',
            'SDCMCC_total_call_count' => '241',
            'SDCMCC_ave_number_count' => '242',
            'SDCMCC_called_rate' => '243',
            'SDCMCC_max_block' => '244',
            'SDCMCC_morning_rate' => '245',
            'SDCMCC_first_day' => '246',
            'SDCMCC_last_day' => '247',
            'SDCMCC_score_2' => '289',
            'SDCMCC_score_3' => '290',
            'SDCMCC_score_4' => '292',
            'SDCMCC_score_5' => '295',
            'SDCMCC_score_6' => '296',
            'SDCMCC_score_7' => '298',
            'HBCMCC_cs_latest_phone_days' => '252',
            'HBCMCC_cs_first_phone_days' => '253',
            'HBCMCC_cs_phone_numbers' => '254',
            'HBCMCC_cs_total_phone_times' => '255',
            'HBCMCC_cs_called_times' => '256',
            'HBCMCC_cs_phone_times_in_15s' => '257',
            'HBCMCC_cs_phone_times_between_15_30s' => '258',
            'HBCMCC_cs_phone_times_over_60s' => '259',
            'HBCMCC_yscs_latest_phone_days' => '260',
            'HBCMCC_yscs_first_phone_days' => '261',
            'HBCMCC_yscs_phone_numbers' => '262',
            'HBCMCC_yscs_total_phone_times' => '263',
            'HBCMCC_yscs_called_times' => '264',
            'HBCMCC_yscs_phone_times_in_15s' => '265',
            'HBCMCC_yscs_phone_times_between_15_30s' => '266',
            'HBCMCC_yscs_phone_times_over_60s' => '267',
            'HBCMCC_cs_phone_numbers_in_7_days' => '268',
            'HBCMCC_cs_called_times_in_7_days' => '269',
            'HBCMCC_yscs_phone_numbers_in_7_days' => '270',
            'HBCMCC_yscs_called_times_in_7_days' => '271',
            'HBCMCC_cs_phone_numbers_in_14_days' => '272',
            'HBCMCC_cs_called_times_in_14_days' => '273',
            'HBCMCC_yscs_phone_numbers_in_14_days' => '274',
            'HBCMCC_yscs_called_times_in_14_days' => '275',
            'HBCMCC_cs_phone_numbers_in_21_days' => '276',
            'HBCMCC_cs_called_times_in_21_days' => '277',
            'HBCMCC_yscs_phone_numbers_in_21_days' => '278',
            'HBCMCC_yscs_called_times_in_21_days' => '279',
            'HBCMCC_cs_phone_numbers_in_30_days' => '280',
            'HBCMCC_cs_called_times_in_30_days' => '281',
            'HBCMCC_yscs_phone_numbers_in_30_days' => '282',
            'HBCMCC_yscs_called_times_in_30_days' => '283',
            'HBCMCC_cs_phone_numbers_between_30_60_days' => '284',
            'HBCMCC_cs_called_times_between_30_60_days' => '285',
            'HBCMCC_yscs_phone_numbers_between_30_60_days' => '286',
            'HBCMCC_yscs_called_times_between_30_60_days' => '287',
            'HBCMCC_main_score' => '251',
            'HBCMCC_dm_score' => '288',
            'HBCMCC_total_call_count' => '241',
            'HBCMCC_ave_number_count' => '242',
            'HBCMCC_called_rate' => '243',
            'HBCMCC_max_block' => '244',
            'HBCMCC_morning_rate' => '245',
            'HBCMCC_first_day' => '246',
            'HBCMCC_last_day' => '247',
            'ALLCMCC_CreditScore_gfdz_m0' => '711',
            'ALLCMCC_saleScore_gfdz_m0' => '712',
            'BJCMCC_c19_total_csf_score' => '251',
            'BJCMCC_c01_A_cs_history' => '264',
            'BJCMCC_c06_A_overdue_extent_D7' => '264',
            'BJCMCC_c03_A_payments_intention_two' => '272',
            'BJCMCC_c12_B_various_borrow_D60' => '272',
            'BJCMCC_c07_A_overdue_extent_D14' => '273',
            'BJCMCC_c05_A_whole_overdue_extent' => '273',
            'BJCMCC_c17_B_payments_intention' => '274',
            'BJCMCC_c18_B_cs_history' => '274',
            'BJCMCC_c09_A_various_borrow_D14' => '281',
            'BJCMCC_c10_A_various_borrow_D21' => '281',
            'BJCMCC_c11_A_various_borrow_D30' => '284',
            'BJCMCC_c02_A_payments_intention_one' => '284',
            'BJCMCC_c13_B_whole_various_borrow' => '285',
            'BJCMCC_c14_B_whole_overdue_extent' => '285',
            'BJCMCC_c04_A_payments_intention_three' => '287',
            'BJCMCC_c08_A_whole_various_borrow' => '287',
            'BJCMCC_c15_abnormal_score_1' => '243',
            'BJCMCC_c16_abnormal_score_2' => '247',
            'CUCC_main_score' => '251',
            'SDCMCC_total_stat_count' => '251',
            'SDCMCC_total_score_count' => '289',
        ];
    }
}