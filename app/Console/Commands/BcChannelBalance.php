<?php

namespace App\Console\Commands;

use App\Models\BillCostV2;
use App\Models\Channel;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\ChannelInterface;
use App\Models\ChannelRemit;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;

//计算成本账单
class BcChannelBalance extends Command
{
    protected $signature = 'bc_channel_balance';
    protected $description = '实时计算运营商余额';

    public function handle()
    {
        #历史余额
        $history_balance = Channel::getChannelNeedBalance();

        foreach ($history_balance as $item) {
            $channel_id = $item['channel_id'];
            #山西移动请求真实余额
            if ($channel_id == 12) {
                $balance = $this->getSxCmccBalance();
                if ($balance != -1) {
                    Channel::updateRealBalanceById($item['id'], ['real_balance' => $balance]);
                }
                continue;
            }

            $balance = $tmp_balance = $item['balance']; # 开始计算日期 时 余额
            $date = $item['balance_start_date']; #余额开始计算日期

            #全国移动=全国移动+移动评分+中移金科v2
            if (in_array($channel_id, [8, 40])) {
                continue;
            }
            #全国移动 和 中移金科v2 余额 记到 移动评分渠道
            $channel_ids = $channel_id == 42 ? [8, 40, 42] : [$channel_id];

            #联通渠道特殊处理 （不含保底）
            $category = $channel_id == 1 ? 2 : null;

            #充值金额
            $recharge_money = ChannelRemit::getRechargeModel($date, $channel_ids);
            #真实消耗
            $interface_ids = $this->getIids($channel_ids);   //渠道下(接口id)
            $real_cost = BillCostV2::getChannelSumCost($date, $interface_ids);
            #特殊消耗
            $expend_cost = ChannelAccountAdjust::getChannelSumCost($date, $channel_ids, null, $category);
            #固定消耗
            $fixed_cost = ChannelAccountFixedFee::getChannelSumCost($date, $channel_ids);

            echo '渠道ID:' . $channel_id . ' 历史余额:' . $balance . ' ';
            echo '充值金额:' . $recharge_money . ' ';
            echo '真实成本:' . $real_cost . ' ';
            echo '特殊消耗:' . $expend_cost . ' ';
            echo '固定成本:' . $fixed_cost . ' ';
            #加上 充值金额
            if ($recharge_money) {
                $balance = bcadd($balance, $recharge_money, 6);
            }
            #减去 真实成本
            if ($real_cost) {
                $balance = bcsub($balance, $real_cost, 6);
            }
            #减去 特殊消耗
            if ($expend_cost) {
                $balance = bcsub($balance, $expend_cost, 6);
            }
            #减去 固定消耗
            if ($fixed_cost) {
                $balance = bcsub($balance, $fixed_cost, 6);
            }
            echo '实时余额:' . $balance . ' ';
            if ($tmp_balance == $balance) {
                echo '无需更新' . PHP_EOL;
                continue;
            }
            $update_result = Channel::updateRealBalanceById($item['id'], ['real_balance' => $balance]);
            echo '更新余额状态:' . $update_result . PHP_EOL;
        }

        $this->output->success("计算完成");
    }

    #获取山西移动余额
    public function getSxCmccBalance()
    {
        $url = 'http://cuishou-express.dianhua.cn/GenBalance?channel_name=sxcmcc';
        $result = Func::curlHandle($url, 0, 0, 0, [], 15000);
        $result = $result['data'];
        if (!isset($result['code']) || !isset($result['result'])) {
            return -1;
        }
        if ($result['code'] == 0) {
            return $result['result'] / 100;
        }
        return -1;
    }

    #根据渠道ID 获取接口ID
    protected function getIids($channel_ids)
    {
        return array_column(ChannelInterface::getListByCondition([], ['id'], $channel_ids)->toArray(), 'id');
    }
}