<?php

/**
 * 拉取crm访客计划 同步至飞书公出审批
 */
namespace App\Console\Commands\PushDatas;


use App\Http\Repository\StatProductRepository;
use App\Models\Account;
use App\Models\BillProductIncomeV2;
use App\Models\CompanyType;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\feishu\FeishuPublicityApprovalProcess;
use App\Models\RemitSplitPrice;
use App\Models\SystemUser;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\CustomerIncomeRepository;
use Illuminate\Console\Command;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use SebastianBergmann\CodeCoverage\Report\PHP;

class PushFeishuPublicityApprovalProcess extends Command
{
    use CurlTrait;

    protected $signature = 'push_publicity_approval';


    protected $description = '拉取crm访客计划同步至飞书公出审批';

    protected $corpid = '';//公司id
    protected $token = '';//api签名使用token
    protected $host = '';//销帮帮请求host
    //接口列表
    protected $apiList = [
        'customerDetail' => '/pro/v2/api/customer/detail', //客户详情接口
        'planList' => '/pro/v2/api/communicatePlan/list', //访客计划列表接口
        'planDetail' => '/pro/v2/api/communicatePlan/detail', //访客计划详情接口
        'contactDetail' => '/pro/v2/api/contact/detail', //联系人详情接口
    ];

    protected $feishu;

    public function handle()
    {
        $this->initData();

        $this->createApprovalByPlan();//访客计划 同步飞书公出审批

        $this->output->success("同步飞书公出审批结束");
    }

    public function initData(){
        $this->host = config('params.crm_xbongbong.host');
        $this->corpid = config('params.crm_xbongbong.corpid');
        $this->token = config('params.crm_xbongbong.token');
        return true;
    }

    public function createApprovalByPlan(){
        $this->feishu = new FeishuRepository();

        //获取当天访客计划列表
        $time = strtotime(date('Y-m-d H:i', time()));
        $stat_time = $time - 3*60;//往前推3分钟
        $stat_time = strtotime(date('Ymd', $stat_time));

        $currentPage = 1;//默认从第一页开始
        $res = $this->getPlanListByDate($currentPage, $stat_time);
        $totalPage = $res['result']['totalPage'];

        $this->addPlanInfo($res['result']['list'], 1);
        for($page = $currentPage + 1; $page<=$totalPage; $page++){
            $res = $this->getPlanListByDate($page, $stat_time);
            $this->addPlanInfo($res['result']['list'], $page);
        }

        return;
    }


    public function addPlanInfo($data, $page = 1){
        $addLog = [];
        foreach ($data as $item) {
            /*
            if(!in_array($item['dataId'], [1481539, 1481470])){
                continue;
            }
            */

            //访客计划状态非未跟进的直接略过
            if($item['data']['text_11'] != '1'){
                continue;
            }

            $plan_info = $this->getPlanDetailInfo($item['dataId']);
            $visit_type = $plan_info['result']['data']['text_3']['text'] ?? ''; //拜访方式(线上 线下)
            if($visit_type != '线下'){
                continue;
            }

            $plan_no = $plan_info['result']['data']['serialNo']; //计划编号
            //如果计划已经同步过 需跳过
            $log = FeishuPublicityApprovalProcess::getNotProcessed(['plan_no' => $plan_no]);
            if(!empty($log)){
                continue;
            }

            $user_realname = $plan_info['result']['data']['creatorId']['name'] ?? '';
            try {
                $user_id = $this->getUserIdByRealname($user_realname);
                //$user_id = $this->getUserIdByRealname('王广利');
                $plan_name = $plan_info['result']['data']['text_1'] ?? '';
                $visit_s_time = $plan_info['result']['data']['date_1'] ?? 0;//拜访开始时间
                $visit_e_time = $plan_info['result']['data']['date_5'] ?? 0;//拜访结束时间
                $visit_purpose = $plan_info['result']['data']['text_4']['text'] ?? '';//拜访目的
                $remark = $plan_info['result']['data']['text_6'] ?? '';//备注
                $note = '访客流水号:'.$plan_no.PHP_EOL;
                $note .= '访客计划名称:'.$plan_name.PHP_EOL;
                $note .= '拜访目的:'.$visit_purpose.PHP_EOL;
                $note .= '备注:'.$remark;

                if($visit_s_time >= $visit_e_time){
                    continue;
                }

                $customer_info_list = [];
                $customer_name1 = $plan_info['result']['data']['text_2']['name'] ?? '';//客户名称1
                $customer_contacts1 = $plan_info['result']['data']['text_14'] ?? '';//客户联系人1
                if(!empty($customer_contacts1)){
                    $customer_contacts1 = $this->getContactDetail($customer_contacts1, $plan_no);
                    usleep(300000);
                }
                if(!empty($customer_name1)){
                    $customer_info_list[] = ['c' => $customer_name1, 'n' => $customer_contacts1];
                }

                $customer_name2 = $plan_info['result']['data']['text_15'] ?? '';//客户名称2
                if(!empty($customer_name2)){
                    $customer_name2 = $this->getCustomerDetailV2($customer_name2);
                    usleep(300000);
                }
                $customer_contacts2 = $plan_info['result']['data']['text_16'] ?? '';//客户联系人2
                if(!empty($customer_contacts2)){
                    $customer_contacts2 = $this->getContactDetail($customer_contacts2, $plan_no);
                    usleep(300000);
                }

                if(!empty($customer_name2)){
                    $customer_info_list[] = ['c' => $customer_name2, 'n' => $customer_contacts2];
                }

                $customer_name3 = $plan_info['result']['data']['text_17'] ?? '';//客户名称3
                if(!empty($customer_name3)){
                    $customer_name3 = $this->getCustomerDetailV2($customer_name3);
                    usleep(300000);
                }
                $customer_contacts3 = $plan_info['result']['data']['"text_18'] ?? '';//客户联系人3
                if(!empty($customer_contacts3)){
                    $customer_contacts3 = $this->getContactDetail($customer_contacts3, $plan_no);
                    usleep(300000);
                }
                if(!empty($customer_name3)){
                    $customer_info_list[] = ['c' => $customer_name3, 'n' => $customer_contacts3];
                }

                $customer_name4 = $plan_info['result']['data']['text_19'] ?? '';//客户名称4
                if(!empty($customer_name4)){
                    $customer_name4 = $this->getCustomerDetailV2($customer_name4);
                    usleep(300000);
                }
                $customer_contacts4 = $plan_info['result']['data']['"text_20'] ?? '';//客户联系人4
                if(!empty($customer_contacts4)){
                    $customer_contacts4 = $this->getContactDetail($customer_contacts4, $plan_no);
                    usleep(300000);
                }
                if(!empty($customer_name4)){
                    $customer_info_list[] = ['c' => $customer_name4, 'n' => $customer_contacts4];
                }

                //发飞书公出审批 & 写日志
                $res = $this->feishu->create_publicity_approval($user_id, $visit_s_time, $visit_e_time, $customer_info_list, $note);
                if(isset($res['code']) && $res['code'] == 0){
                    //审批正常发成功在写日志
                    $addLog[] = [
                        'user_id' => $user_id,//商务飞书中对应的uid
                        'plan_no' => $plan_no,//访客流水号
                        'plan_name' => $plan_name,//计划名称
                        'customer_name1' => $customer_name1,//客户名称1
                        'customer_contacts1' => $customer_contacts1,//客户联系人1
                        'customer_name2' => $customer_name2,//客户名称2
                        'customer_contacts2' => $customer_contacts2,//客户联系人2
                        'customer_name3' => $customer_name3,//客户名称3
                        'customer_contacts3' => $customer_contacts3,//客户联系人3
                        'customer_name4' => $customer_name4,//客户名称4
                        'customer_contacts4' => $customer_contacts4,//客户联系人4
                        'creator' => $user_realname,//执行人/商务姓名
                        'visit_s_time' => $visit_s_time,//拜访开始时间
                        'visit_e_time' => $visit_e_time,//拜访结束时间
                        'visit_purpose' => $visit_purpose,//拜访目的
                        'remark' => $remark,//备注
                        'process_instance_id' => $res['data']['instance_code'] ?? '',
                        'create_time' => time()
                    ];

                }else{
                    $this->sendException($res, $plan_no);
                }
            }catch (\Exception $e){
                $error = '获取用户username='.$user_realname.'的飞书userid异常';
                $this->sendException($error, $plan_no);
                continue;
            }

        }

        //写日志
        FeishuPublicityApprovalProcess::insert($addLog);

    }

    public function getCustomerDetailV2($dataId, $plan_no = ''){
        $url = $this->host . $this->apiList['customerDetail'];
        $postData = [
            'corpid' => $this->corpid,
            'dataId' => $dataId,
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        //dd(json_encode($res, JSON_UNESCAPED_UNICODE));
        if($res['code'] != 1){
            $this->sendException($res, $plan_no, '客户详情查询失败');
        }

        $name = $res['result']['data']['text_1'] ?? '';
        return $name;
    }

    public function getContactDetail($dataId, $plan_no = ''){
        //获取联系人详情接口
        $url = $this->host . $this->apiList['contactDetail'];
        $postData = [
            'dataId' => $dataId,
            'corpid' => $this->corpid,
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        if($res['code'] != 1){
            $this->sendException($res, $plan_no, '获取联系人查询失败');
            //dd($postData, '查询失败');
        }

        $name = $res['result']['data']['text_1'] ?? '';
        return $name;
    }

    public function getUserIdByRealname($realname = '') {
        $result = '';
        //获取销售的邮箱
        $email = SystemUser::getUserInfoByRealname($realname)['email'];
        //根据邮箱获取飞书的user id
        if (!empty($email)){
            $res = $this->feishu->get_user_by_email($email);
            $result =  $res[$email]??'';
        }
        if(!empty($result)){
            return $result;
        }

        //获取销售的电话
        $phone = SystemUser::getUserInfoByRealname($realname)['phone'];
        //根据电话获取飞书的user id
        if (!empty($phone)){
            $res = $this->feishu->get_user_by_mobile($phone);
            $result =  $res[$phone]??'';
        }
        return $result;
    }

    public function getPlanDetailInfo($id){
        //获取访客计划详情
        $url = $this->host . $this->apiList['planDetail'];
        $postData = [
            'dataId' => $id,
            'corpid' => $this->corpid,
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);

        if($res['code'] != 1){
            dd($postData, '查询失败');
        }

        return $res;
    }

    public function getPlanListByDate($currentPage, $stat_time){
        //访客计划列表7504312
        $url = $this->host . $this->apiList['planList'];
        $postData = [
            'conditions' => [
                [
                    'attr' => 'date_1',
                    'fieldType' => 4,
                    'symbol' => 'equal',
                    'value' => [$stat_time]
                ]
            ],
            'formId' => 7504312,
            'corpid' => $this->corpid,
            'page' => $currentPage,
            'pageSize' => 40 //最大值100
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        if($res['code'] != 1){
            dd($postData, $res, '查询失败');
        }

        return $res;
    }

    public function setHeaderData($data){
        $str = json_encode($data, JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES) . $this->token;
        //$str = json_encode($data, JSON_UNESCAPED_UNICODE) . $this->token;
        $sign = hash('sha256', $str);
        $headers = ['sign: '.$sign];
        return $headers;
    }

    public function sendException($message = '', $plan_no = '', $title = '飞书公出实例创建失败'){
        $msg = $title.PHP_EOL;
        $msg .= PHP_EOL;
        $msg .= "计划编号:".$plan_no.PHP_EOL;
        $msg .= "创建时间:".date('Y-m-d H:i').PHP_EOL;
        $msg .= "错误信息:".json_encode($message, JSON_UNESCAPED_UNICODE);
        sendWechatNotice($msg);
    }

}
