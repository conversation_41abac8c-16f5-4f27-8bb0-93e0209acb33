<?php

/**
 * 推送产品拉取
 */
namespace App\Console\Commands\PushDatas;



use App\Models\BillOperatorMonthCheck;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\StatisticsInterfaceUsage;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Console\Command;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * php artisan push_interface_usage_for_enterprise_service
 */
class PushInterfaceUsageForEnterpriseService extends Command
{
    use CurlTrait;

    protected $signature = 'push_interface_usage_for_enterprise_service
    {--start_date= : 开始日期，格式：Ymd}
    {--end_date= : 结束日期，格式:Ymd}
    {--product_ids= : 父产品IDs（多个产品ID用 , 隔开）}
    {--month= : 默认为空按天,传则按该月份的日期来(Ym)}
    {--is_check= : 是否核对月账单,默认0不核对，1核对}
    ';

    protected $isCheck = 0;
    protected $checkMonth = '';
    //推送的产品ID
    protected static $productIds = [210, 1000];


    protected $description = '推送渠道调用量';

    // protected $url = '*************:7011/receive_interface_data/channel_data';//原地址
    // protected $url = '**************:7011/receive_interface_data/channel_data';//测试环境

    //fin-operator线上 注意服务器是否可以请求到,是否需要配置hosts文件
    protected $url = 'http://fin-operator.dianhua.cn/service1/receive_interface_data/channel_data';
    protected $interfaceMap = [];
    protected $channelMap = [];

    public function handle()
    {
        //接收变量
        $today = date('Ymd');
        $start_date = !empty($this->option('start_date')) ? $this->option('start_date') : $today;
        $end_date = !empty($this->option('end_date')) ? $this->option('end_date') : $today;
        if($start_date > $end_date){
            $this->output->error('开始时间不能大于结束时间');
            exit;
        }
        if(!empty($this->option('start_date')) && $this->option('start_date') < '20200701'){
            $this->output->error('开始时间必须大于20200701');
            exit;
        }

        $productIds = $this->option('product_ids');
        $productIds = $productIds ? explode(',', $productIds) : self::$productIds;

        $month = $this->option('month');

        $interfaces = ChannelInterface::select(['id', 'channel_id', 'name', 'label'])->get()->toArray();
        $this->interfaceMap = array_column($interfaces, null, 'id');

        $channels = Channel::select(['channel_id', 'name', 'label'])->get()->toArray();
        $this->channelMap = array_column($channels, null, 'channel_id');

        if(!empty($month)){
            $this->isCheck = !empty($this->option('is_check')) ? $this->option('is_check') : 0;
            $this->checkMonth = $month;
            //推整个月的聚合数据即可
            $this->pushUsageByMonth($month, $productIds);
            return;
        }

        $date = $start_date;
        while ($date <= $end_date){
            $this->pushInterfaceUsage($date, $date, $date, $productIds);
            $date = date('Ymd', strtotime($date) + 86400);
        }

        $this->output->success("【{$this->description}】 结束");
    }

    public function pushUsageByMonth($month, $productIds){
        $start_date = $month.'01';
        $end_date = date("Ymt", strtotime($start_date));
        $this->pushInterfaceUsage($month, $start_date, $end_date, $productIds);
        return true;
    }

    public function pushInterfaceUsage($date, $start_date, $end_date, $productIds){
        $bxf_data = StatisticsInterfaceUsage::getInterfaceUsageForEnterpriseService($start_date, $end_date, $productIds, 'bxf');
        $bmy_data = StatisticsInterfaceUsage::getInterfaceUsageForEnterpriseService($start_date, $end_date, [200], 'bmy');
        $data = array_merge($bxf_data, $bmy_data);
        if(empty($data)){
            return;
        }

        foreach ($data as &$val){
            $val['channel_id'] = $this->interfaceMap[$val['interface_id']]['channel_id'] ?? 0;
            $val['interface_label'] = $this->interfaceMap[$val['interface_id']]['label'] ?? '异常';
            $val['interface_name'] = $this->interfaceMap[$val['interface_id']]['name'] ?? '异常';

            $val['channel_label'] = $this->channelMap[$val['channel_id']]['label'] ?? '异常';
            $val['channel_name'] = $val['operator'] ?: '异常';
            unset($val['operator']);
        }

        if($this->isCheck){
            //核对月账单数据
            $this->addBillOperatorData($data);
            return;
        }

        $push_time = date('Y-m-d H:i:s');
        $result = [
            'time_info' => [
                'date' => $date,
                'time' => $push_time
            ],
            'list' => $data
        ];

        //如果失败可重推一次
        $flag = false;
        for($i = 1; $i <= 2; $i++){
            //推送 json数据json_encode($result)
            $res = $this->postRawJson($this->url, $result);
            if(isset($res['status']) && $res['status'] == 0){
                $flag = true;
                break;
            }
        }

        if($flag == false){
            $msg = "推送渠道调用量到企服失败".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "数据日期:".$date.PHP_EOL;
            $msg .= "推送时间:".$push_time.PHP_EOL;
            $msg .= "接口响应:".json_encode($res, JSON_UNESCAPED_UNICODE);
            sendWechatNotice($msg);
        }

        return true;
    }


    public function addBillOperatorData($data){

        $add = [];
        foreach ($data as $item){
            $where = [
                'channel_id' => $item['channel_id'],
                'interface_id' => $item['interface_id'],
                'month' => $this->checkMonth,
            ];

            $info = BillOperatorMonthCheck::getOneItemByCondition($where);
            if(empty($info)){
                $row = [
                    'channel_id' => $item['channel_id'],
                    'interface_id' => $item['interface_id'],
                    'month' => $this->checkMonth,
                    'total' => $item['total'],
                    'success' => $item['success'],
                    'valid' => $item['valid'],
                    'create_time' => time(),
                ];
                $add[] = $row;
            }else{
                if($info['status'] == 1){
                    //已确认的 数据不能在变更
                    continue;
                }

                BillOperatorMonthCheck::updateData($where, ['total' => $item['total'], 'success' => $item['success'],'valid' => $item['valid'], 'update_time' => time()]);
            }
        }

        if(!empty($add)){
            $addLogChunk = array_chunk($add, 50);
            foreach ($addLogChunk as $addArr) {
                BillOperatorMonthCheck::addData($addArr);
            }
        }

        return;
    }




}
