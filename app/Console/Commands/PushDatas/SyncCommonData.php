<?php

/**
 * 同步账号 产品等公用信息到ck
 */
namespace App\Console\Commands\PushDatas;


use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\AccountProductCustom;
use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\ClickHouse\DictDb;
use App\Models\Customer;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncCommonData extends Command
{
    protected $signature = 'sync_common_data_to_ck';

    protected $description = '同步公用数据到ck';


    public function handle()
    {
        //同步account信息到ck字典
        $this->syncAccountData();
        //同步product
        $this->syncProductData();
        //同步channel
        $this->syncChannelData();
        //同步interface
        $this->syncInterfaceData();
        //同步account_product
        $this->syncAccountProductData();
        //同步account_product_custom
        $this->syncAccountProductCustomData();
        //同步customer_group
        $this->syncCustomerGroupData();

        //同步customer
        $this->syncCustomerData();
        //同步dchannel表
        $this->syncDictChannelData();

    }

    public function syncAccountProductData(){
        $account_map = Account::pluck('apikey', 'account_id')->toArray();
        //$end_time = strtotime(date('Ymd')) + 86400 - 1;
        $where = [
//            ['status', '=', 1],
//            ['end_time', '>=', $end_time]
        ];
        $list = AccountProduct::getListByCondition($where);
        $insertData = [];
        foreach ($list as $item){
            $apikey = $account_map[$item['account_id']] ?? '';
            $add = [
                $apikey,
                $item['account_id'],
                $item['product_id'],
                $item['status'],
                $item['end_time'],
                $item['daily_limit'],
                $item['month_limit'],
                $item['year_limit'],
                $item['total_limit'],
                $item['create_at'],
                $item['limit_start_date']
            ];

            $insertData[] = $add;
        }

        unset($account_map);
        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_account_product');//先清
        $colmuns = ['apikey', 'account_id', 'product_id', 'status', 'end_time', 'daily_limit', 'month_limit', 'year_limit', 'total_limit', 'create_at', 'limit_start_date'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_account_product', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);
        return;
    }

    public function syncAccountProductCustomData(){
        $where = [
            ['type', '>=', 20],
            ['type', '<=', 21]
        ];
        $list = AccountProductCustom::getDataByCondition($where);
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['father_id'],
                $item['product_id'],
                $item['apikey'],
                $item['extend'],
                $item['type'],
                $item['data'],
                $item['create_at'],
                $item['update_at']
            ];

            $insertData[] = $add;
        }

        //unset($account_map);
        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_account_product_custom');//先清
        $colmuns = ['father_id', 'product_id', 'apikey', 'extend', 'type', 'data', 'create_at', 'update_at'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_account_product_custom', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);
        return;
    }

    public function syncCustomerGroupData(){
        $list = Customer\CustomerGroup::getGroupList();
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['group_id'],
                $item['group_name'],
                $item['status']
            ];

            $insertData[] = $add;
        }

        //unset($account_map);
        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_customer_group');//先清
        $colmuns = ['group_id', 'group_name', 'status'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_customer_group', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);
        return;
    }

    public function syncCustomerData(){
        $list = Customer::getListByCondition([]);
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['customer_id'],
                $item['name'],
                $item['company'],
                $item['c_type'],
                $item['group_id'],
                $item['type'],
            ];

            $insertData[] = $add;
        }

        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_customer');//先清
        $colmuns = ['customer_id', 'name', 'company', 'c_type', 'group_id', 'type'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_customer', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);
        return;
    }


    public function syncInterfaceData(){
        $list = ChannelInterface::getListByCondition([], ['id', 'channel_id', 'name', 'label']);
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['channel_id'],
                $item['id'],//iid
                $item['name'],
                $item['label']
            ];

            $insertData[] = $add;
        }

        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_channel_interface');//先清
        $colmuns = ['channel_id', 'iid', 'name', 'label'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_channel_interface', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);

        return;
    }

    public function syncChannelData(){
        $list = Channel::getChannelByCondition();
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['channel_id'],
                $item['name'],
                $item['label']
            ];

            $insertData[] = $add;
        }

        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_channel');//先清
        $colmuns = ['channel_id', 'name', 'label'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_channel', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);

        return;
    }

    public function syncDictChannelData(){
        $list = Channel::getChannelByCondition();
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['father_id'],
                $item['label'],
                $item['channel_id'],
                $item['status']
            ];

            $insertData[] = $add;
        }

        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('dict_channel');//先清
        $colmuns = ['father_id', 'label', 'channel_id', 'status'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('dict_channel', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);

        return;
    }

    public function syncProductData(){
        $list = Product::getAllProduct();
        $insertData = [];
        foreach ($list as $item){
            $add = [
                $item['value'],//product_id
                $item['label'],//product_name
                $item['father_id']
            ];

            $insertData[] = $add;
        }

        unset($list);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_product');//先清
        $colmuns = ['product_id', 'product_name', 'father_id'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_product', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);
        return;
    }

    public function syncAccountData(){
        $list = Customer::select(['name', 'customer_id', 'group_id'])->get()->toArray();
        $customer_map = array_column($list, 'name', 'customer_id');
        $customer_group_map = array_column($list, 'group_id', 'customer_id');

        $group_list = Customer\CustomerGroup::getGroupList();
        $group_map = array_column($group_list, 'group_name', 'group_id');

        $account_list = Account::getAllAccount();
        $insertData = [];
        foreach ($account_list as $item){
            $customer_name = $customer_map[$item['customer_id']] ?? $item['customer_id'];
            $group_id = $customer_group_map[$item['customer_id']] ?? '';
            $group_name= $group_map[$group_id] ?? '';
            $add = [
                $item['account_id'],
                $item['cid'],
                $item['customer_id'],
                $customer_name,
                $item['value'],//apikey
                $item['label'],//account_name
                $group_id,
                $group_name,
                $item['status'],
                $item['type'],
                $item['end_time']
            ];

            $insertData[] = $add;
        }

        unset($account_list);
        unset($customer_map);
        $addLogChunk = array_chunk($insertData, 200);
        $ck = new DictDb();
        $ck->truncate('finance_account');//先清
        $colmuns = ['account_id', 'cid', 'customer_id', 'customer_name', 'apikey', 'account_name', 'group_id', 'group_name', 'status', 'type', 'end_time'];
        foreach ($addLogChunk as $addArr) {
            $ck->add('finance_account', $addArr, $colmuns);
        }

        unset($insertData);
        unset($addLogChunk);
        return;
    }


}
