<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/14 0014
 * Time: 18:26
 */

namespace App\Console\Commands;


use App\Models\MongoLogsStatisticsCustomerUsage;
use App\Models\StatisticsInterfaceUsageCopy;
use App\Models\MongoStatis;
use App\Models\Channel;
use App\Models\ChannelInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use App\Models\MongoUpstreamShortcutStatistic;

class ProcessCostData extends Command
{
    protected $signature = 'ProcessCostData';

    private $date = [['20190101','20190201'],['20190201','20190301'],['20190301','20190401'],['20190401','20190501'],['20190501','20190601'],['20190601','20190701'],['20190701','20190801'],['20190801','20190901'],['20190901','20191001'],['20191001','20191101'],['20191101','20191201'],['20191201','20200101']];

    /**
     * @throws \Exception
     */
    public function handle(StatisticsInterfaceUsageCopy $statisticsInterfaceUsageCopy)
    {

        $workers = 12;
        for($i = 0; $i < $workers; $i++){
            $processIds[$i] = pcntl_fork();

            switch ($processIds[$i]) {
                case -1 :
                    echo "fork failed : {$i} \r\n";
                    exit;
                case 0 :
                    // 子进程处理数据
                    $this->insertData($statisticsInterfaceUsageCopy,$this->date[$i]);
                    exit;
                default :
                    break;
            }
        }

        $this->insertData($statisticsInterfaceUsageCopy,$this->date[3]);

        //子进程完成之后要退出
        while (count($processIds) > 0) {
            $mypid = pcntl_waitpid(-1, $status, WNOHANG);
            foreach ($processIds as $key => $pid) {
                if ($mypid == $pid || $mypid == -1) {
                    unset($processIds[$key]);
                }
            }
        }

    }

    protected function insertData($statisticsInterfaceUsageCopy,$date)
    {
        $data = MongoUpstreamShortcutStatistic::where("day",">=",$date[0])->where("day","<",$date[1])->get();

        $data = $this->dealWithData($data);

        $chunk_result = array_chunk($data, 300);
        foreach ($chunk_result as $v){
            $statisticsInterfaceUsageCopy->insert($v);
        }
    }

    protected function dealWithData($data)
    {

        $dealData = [];
        foreach ($data as $k=>$v){

            $interface_id = $this->getiid($v['upstream'],$v['field']);
            $new['iid'] = $interface_id;
           // $new['apikey'] = $v['apikey'];
            $new['operator'] = $v['upstream'];
           // $new['product_id'] = $this->getProductId();
            $new['date'] = $v['day'];
            $new['node'] = $v['node'];
            $new['date'] = $v['day'];
            $new['total'] = $v['data']['success'] + $v['data']['fail'];
            $new['success'] = $v['data']['success'];
            $new['valid'] =$v['data']['success'];
            $temp = (array)$v['created_at'];
            $new['create_time'] =strtotime($temp['date']);
            $temp = (array)$v['updated_at'];
            $new['update_time'] =strtotime($temp['date']);
            $new['interface_id'] = $interface_id;
            array_push($dealData,$new);
        }

        return $dealData;
    }

    protected function getiid($operator,$interfacename)
    {
        $channel = Channel::getChannelByName($operator);
        if (!empty($channel)){
            $channel_id = $channel['channel_id'];
            $interface_id = ChannelInterface::getchannelInterfaceByName($interfacename,$channel_id);
            if (!empty($interface_id)){
                return $interface_id['id'];
            }else{
                return '';
            }
        }else{
            return '';
        }
    }
}