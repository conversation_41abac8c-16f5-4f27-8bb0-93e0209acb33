<?php

namespace App\Console\Commands\Opdata;

use App\Models\ClickHouse\RequestProductLog;
use App\Models\Opdata\SelfChannelRuleMonitorLog;
use Illuminate\Console\Command;

class StatSelfRuleExtend extends Command
{
    protected $signature = 'stat_self_rule_extend';
    protected $description= '统计自有渠道规则的扩展数据';

    protected $net_status_pids = [216, 41002, 225, 203, 41012, 228];

    protected $update_rule_fields = [];

    public function handle() {
        $start_time = strtotime('-1 days');
        $end_time = time();
        $two_week_ago = strtotime('-2 weeks');


        $request_product_log = new RequestProductLog();
        $self_channel_rule_logs = SelfChannelRuleMonitorLog::getTotalEffectiveInfos();
        foreach ($self_channel_rule_logs as $self_channel_rule_log) {
            $id = $self_channel_rule_log['id'];
            $apikey = $self_channel_rule_log['apikey'];
            $pids = $self_channel_rule_log['pids'];
            $lastest_change_time = $self_channel_rule_log['lastest_change_time'];

            // 统计近一天的查得总量
            $valid_num = $request_product_log->getBmyProductValidCnt($apikey, $pids, $start_time, $end_time);

            // 如果是在网状态相关产品，统计近一天
            if (in_array($pids, $this->net_status_pids)) {
                $shutdown_num = $request_product_log->getBmyProductValidCnt($apikey, $pids, $start_time, $end_time, null, '4');
                $this->update_rule_fields[$id]['shutdown_ratio'] = $valid_num > 0 ? strval(bcdiv($shutdown_num, $valid_num, 4) * 100) : "";
            }

            $self_num = $request_product_log->getBmyProductValidCnt($apikey, $pids, $start_time, $end_time, 124);
            $this->update_rule_fields[$id]['self_channel_call_ratio'] = $valid_num > 0 ? strval(bcdiv($self_num, $valid_num, 4) * 100) : "";

            $two_week_call_num = $request_product_log->getBmyCntByApikeyAndPids($apikey, $pids, $two_week_ago, $end_time);
            $this->update_rule_fields[$id]['two_week_call_num'] = $two_week_call_num;
            $date_diff = intval(($end_time - $lastest_change_time) / 86400);
            $this->update_rule_fields[$id]['delay_day'] = $date_diff;
        }

        $self_channel_rule_monitor_log = new SelfChannelRuleMonitorLog();

        if ($this->update_rule_fields) {
            foreach ($this->update_rule_fields as $id => $update_rule_field) {
                $self_channel_rule_monitor_log->where(['id' => $id])->update($update_rule_field);
            }
        }
    }
}