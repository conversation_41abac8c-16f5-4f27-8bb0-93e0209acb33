<?php
/**
 * 临时脚本
 */
namespace App\Console\Commands\Opdata;

use App\Models\ClickHouse\RequestProductLog;
use App\TraitUpgrade\CurlTrait;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;

class CurlOpdata227Command extends Command
{
    use CurlTrait;
    protected $signature = 'curl_opdata_227';

    protected $description = '调用腾讯反欺诈';

    public function handle()
    {
        $yesterday = strtotime('yesterday');
        $today = strtotime('today');

        //从ck 中获取前一日 调用量为 1200 左右的用户
        $requestLogModel = new RequestProductLog();
        $res = $requestLogModel->getValidCountLogDataByHaving($yesterday, $today, 200, 1200);
        $tmpApikeyPids = $res[0];

        $yesterdayNum = $tmpApikeyPids['num'];
        $yesterdayApikey = $tmpApikeyPids['apikey'];
        $yesterdayPids = $tmpApikeyPids['pids'];

        $ratio = round(1200 / $yesterdayNum, 2) * 100;

        //获取同时段调用数据
        $currentMin = strtotime(date('Y-m-d H:i:00'));
        $yesEnd = $currentMin - 86400;
        $yesStart = $currentMin - 86400 - 60;
        $logDatas = $requestLogModel->getVaildLogDatasByProductId($yesStart, $yesEnd, $yesterdayApikey, $yesterdayPids);
        if (!count($logDatas)) {
            exit('EMPTY_LOG_DATA');
        }

        //获取所需数据数量
        $needNum = ceil(count($logDatas) * $ratio);
        $needLogDatas = array_chunk($logDatas, $needNum)[0];
        $ttl = mt_rand(10,30);
        foreach ($needLogDatas as $item) {
            $sleep = mt_rand(0,15);
            if ($ttl > 0) {
                if ($sleep > $ttl) {
                    sleep($ttl);
                } else {
                    sleep($sleep);
                }
                $ttl = $ttl - $sleep;
            }
            $phone = $item['tel'] ?? hash('sha256', 159 . mt_rand(10000000,99999999));
            //调用opdata 227 产品
            $params = [
                'products' => '227',
                'phone' => $phone,
            ];
            $this->getInfoFromOpdata($params);
        }
    }

    public function getInfoFromOpdata($param, $url = '')
    {
        $apikey = $param['apikey'] ?? '616A2395d1d7F39E6b4d0d39894A7EDC';
        $screct = $param['appsecret'] ?? 'fe980fbe9f0a2f88a054ab02b94a830ca6aa5f6c1f9f42ad2dd5653b0b9ca9bb';

        unset($param['apikey']);
        unset($param['appsecret']);

        $url = $url ?: "http://opdata.dianhua.cn/index/index";

        $nonce = mt_rand(1000, 9999);
        $timestamp = time();

        $postData = [
            'products' => $param['products'],
            'phone' => $param['phone'],
            'idnum' => $param['idnum'] ?? "",
            'name' => $param['name'] ?? "",
            'readCache' => 2,
            'timestamp' => $timestamp,
            'apikey' => $apikey,
            'nonce' => $nonce,
            'signature' => Func::sign($apikey, $screct, $nonce, $timestamp)
        ];

        unset($param['products']);
        unset($param['phone']);
        unset($param['idnum']);
        unset($param['name']);

        if ($param) {
            $postData = array_merge($postData, $param);
        }
        $header = [
            'Content-Type:application/json',
        ];
        $info = Func::curlHandle($url, $postData, 1, 1, $header);
        return $info;
    }
}