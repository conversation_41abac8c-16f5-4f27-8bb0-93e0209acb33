<?php

namespace App\Console\Commands\Opdata;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\Opdata\SelfChannelRuleMonitorLog;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;

class PullSelfRuleMonitor extends Command
{
    protected $signature = 'pull_self_rule_monitor';
    protected $description= '拉取自有渠道规则监控数据';

    protected $monitor_pids = [202, 224, 41001,216, 41002, 225, 203, 41012, 228];

    protected $test_customer_id = ['C20180828LOCNMG','C20200622KF31GS','C20180904CEMD2W','C20240814ZUK239'];

    protected $update_rule_fields = [];

    protected $insert_data = [];

    public function handle() {
        $sub_product_account_api = getenv('SUB_PRODUCT_ACCOUNT_API');
        $next_flage = '';

        $self_channel_rule_logs = SelfChannelRuleMonitorLog::get()->toArray();

        $self_channel_rule_map = [];
        foreach ($self_channel_rule_logs as $self_channel_rule_log) {
            $apikey = $self_channel_rule_log['apikey'];
            $pids = $self_channel_rule_log['pids'];
            $key = implode("_", [$apikey, $pids]);
            $self_channel_rule_map[$key] = $self_channel_rule_log;
        }
        while (true) {
            //请求后台接口
            $config_data = $this->getNewConfigInfos($sub_product_account_api, $next_flage);

            //判断数据是否为空，空则跳出，结束循环
            if (empty($config_data['data']['list'])) {
                break;
            }
            $next_flage = $config_data['data']['next_flage'];
            $format_data = $this->formatConfigData($config_data['data']['list']);

            $this->fetchChangedData($self_channel_rule_map, $format_data);

        }

        $this->updateSelfChannelRule();
    }


    public function updateSelfChannelRule() {
        $self_channel_rule_monitor_log = new SelfChannelRuleMonitorLog();

        if ($this->update_rule_fields) {
            foreach ($this->update_rule_fields as $id => $update_rule_field) {
                $self_channel_rule_monitor_log->where([
                    'id' => $id,
                ])->update($update_rule_field);
            }
        }

        if ($this->insert_data) {
            foreach ($this->insert_data as $insert_datum) {
                $self_channel_rule_monitor_log::insert($insert_datum);
            }
        }
    }

    public function fetchChangedData($self_channel_rule_map, $format_data) {
        foreach ($format_data as $format_data_item) {
            $apikey = $format_data_item['apikey'];
            $account_name = $format_data_item['account_name'];
            $customer_id = $format_data_item['customer_id'];
            $group_id = $format_data_item['group_id'];
            $pids = $format_data_item['pids'];
            $status = $format_data_item['status'];
            $self_channel_rule = $format_data_item['self_channel_rule'];
            $self_channel_threshold = $format_data_item['self_channel_threshold'];

            $key = implode("_", [$apikey, $pids]);
            if (isset($self_channel_rule_map[$key])) {
                $old_info = $self_channel_rule_map[$key];
                $old_id = $old_info['id'];
                $old_status = $old_info['status'];
                $old_self_channel_rule = $old_info['self_channel_rule'];
                $old_self_channel_threshold = $old_info['self_channel_threshold'];
                $opening_time = $old_info['opening_time'];
                if (empty($opening_time)) {
                    $account_info = Account::getAccountInfoByApikey($apikey);
                    $account_id = $account_info['account_id'] ?? '';
                    $account_product_info = AccountProduct::getInfoByAccountIdAndProductId($account_id, $pids);
                    $opening_time = $account_product_info['create_at'] ?? 0;
                    $this->update_rule_fields[$old_id] = [
                        'opening_time' => $opening_time
                    ];
                }

                if ($old_status != $status) {
                    $this->update_rule_fields[$old_id] = [
                        'status' => $status
                    ];
                }

                if ($old_self_channel_rule != $self_channel_rule) {
                    $this->update_rule_fields[$old_id] = [
                        'self_channel_rule' => $self_channel_rule,
                        'lastest_change_time' => time()
                    ];
                }

                if ($old_self_channel_threshold != $self_channel_threshold) {
                    $this->update_rule_fields[$old_id] = [
                        'self_channel_threshold' => $self_channel_threshold,
                        'lastest_change_time' => time()
                    ];
                }
            } else {
                if ($status == SelfChannelRuleMonitorLog::STATUS_EFFECTIVE) {
                    $account_info = Account::getAccountInfoByApikey($apikey);
                    $account_id = $account_info['account_id'] ?? '';
                    $account_product_info = AccountProduct::getInfoByAccountIdAndProductId($account_id, $pids);
                    $opening_time = $account_product_info['create_at'] ?? 0;
                    $this->insert_data[] = ['apikey' => $apikey,
                        'account_id' => $account_info['account_id'] ?? '',
                        'account_name' => $account_name,
                        'customer_id' => $customer_id,
                        'group_id' => $group_id,
                        'pids' => $pids,
                        'status' => $status,
                        'self_channel_rule' => $self_channel_rule,
                        'self_channel_threshold' => $self_channel_threshold,
                        'lastest_change_time' => time(),
                        'opening_time' => $opening_time
                    ];
                }
            }
        }
    }
    public function formatConfigData($config_data) {
        $result = [];
        foreach ($config_data as $datum) {
            if (in_array($datum['account_info']['customer_id'], $this->test_customer_id)) {
                continue;
            }

            foreach ($datum['product_info'] as $product_item) {
                $product_id = $product_item['product_id'];
                if (in_array($product_id, $this->monitor_pids)) {
                    $item = [];
                    $item['apikey'] = $datum['account_info']['apikey'];
                    $item['account_name'] = $datum['account_info']['account_name'];
                    $item['customer_id'] = $datum['account_info']['customer_id'];
                    $item['group_id'] = $datum['account_info']['group_id'];
                    $item['pids'] = $product_item['product_id'];
                    $item['status'] = $product_item['status'];
                    $item['end_time'] = $product_item['end_time'];
                    $item['self_channel_rule'] = $product_item['self_channel_rule'];
                    $item['self_channel_threshold'] = $product_item['self_channel_threshold'];
                    $result[] = $item;
                }
            }
        }

        return $result;
    }
    //获取配置信息
    public function getNewConfigInfos($url, $nextFlage = '')
    {
        $postDatas = [
            'main_pid' => 200,
            'next_flage' => $nextFlage,
        ];

        $head = [
            'Content-Type:application/json'
        ];
        $res = Func::curlHandle($url, $postDatas, 1, 1,$head,5000);
        if ($res['http_code'] != 200) {//请求失败，重试一次
            $res = Func::curlHandle($url, $postDatas, 1, 1,$head,5000);
            if ($res['http_code'] != 200) {
                echo 'Failed to request back-api ' . $url . PHP_EOL;
            }
        }
        return empty($res['data']) ? [] : $res['data'];
    }

}