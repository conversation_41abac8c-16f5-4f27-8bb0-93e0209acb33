<?php
/**
 * 收入拆分
 */
namespace App\Console\Commands\Opdata;

use App\Models\ClickHouse\RequestProductLog;
use App\Models\StatisticsCustomerUsage;
use App\TraitUpgrade\CurlTrait;
use App\Utils\Helpers\Func;
use Illuminate\Console\Command;
use PhpOffice\PhpSpreadsheet\Shared\OLE\PPS\Root;

class CustomerUsageSplitCommand extends Command
{
    use CurlTrait;
    protected $signature = 'customer_usage_split
    {--func= : 方法}
    {--productIds= : 产品IDs}
    {--apikey= : 用户apikey}
    {--startDate= : 结束时间}
    {--endDate= : 开始时间}
    ';

    protected $description = '用户统计拆分';

    public function handle()
    {
        $func = $this->input->getOption('func') ?: '';
        $productIds = $this->input->getOption('productIds') ?: '';
        $apikey = $this->input->getOption('apikey') ?: '';
        $startDate = $this->input->getOption('startDate') ?: '';
        $endDate = $this->input->getOption('endDate') ?: '';
        switch ($func) {
            case 'updateField':
                $this->updateField();
                break;
            case 'splitUsage':
                $this->splitUsage($productIds,$startDate,$endDate,$apikey);
                break;
            default:
                exit('ERROR!');
        }
        exit('DONE!');
    }

    public function updateField()
    {
        //查出统计数据（group by)
        $countDatas = StatisticsCustomerUsage::getCountDatasByGroupBy('product_id');
        foreach ($countDatas as $countItem) {
            //获取ID批量更新
            $lastId = 0;
            while (true) {
                $where = [
                    ['id', '>', $lastId],
                    ['product_id', '=', $countItem['product_id']],
                ];
                //获取ID
                $ids = StatisticsCustomerUsage::getIdsByWhere($where, 3000);
                if (!$ids) {
                    break;
                }
                $ids = array_column($ids,'id');
                $lastId = max($ids);

                $updateDatas = [
                    'call_product_id' => $countItem['product_id']
                ];
                //更新数据
                $res = StatisticsCustomerUsage::updateDatasByIds($updateDatas, $ids);
                if (!$res) {
                    echo "UPDATE_ERROE\t" . json_encode($ids) . PHP_EOL;
                }
            }
            echo 'FINISH_PRODUCT_ID:' . $countItem['product_id'] . PHP_EOL;
        }
    }

    public function splitUsage($productIds,$startDate,$endDate,$apikey='')
    {
        $date = $startDate;
        while (true) {
            if (strtotime($date) > strtotime($endDate)) {
                break;
            }
            $this->splitUsageByDay($productIds,$date,$apikey);
            $date = date('Ymd', strtotime($date) + 86400);
        }
    }
    //拆分今年的数据
    public function splitUsageByDay($productIds,$date,$apikey='')
    {
        $productIds = explode(',', $productIds);
        $startTime = strtotime($date . ' 00:00:00');
        $endTime = strtotime($date . ' 23:59:59');
        //获取ck中自查得数据
        $logModel = new RequestProductLog();
        $logDatas = $logModel->getCountDatasForStatis($productIds,$startTime,$endTime,$apikey);
        $chargMap = [
            '202' => 224,
            '203' => 225,
            '216' => 225,
        ];
        $operatorMap = [
            1 => 'CUCC',
            2 => 'CTCC',
            3 => 'CMCC',
        ];
        $statisModel = new StatisticsCustomerUsage();
        $pushDatas =[];
        foreach ($logDatas as $logItem) {

            //查出对应后台统计量
            $where = [
                'apikey' => $logItem['apikey'],
                'product_id' => $logItem['pids'],
                'node' => $logItem['node'],
                'operator' => $operatorMap[$logItem['carrier']],
                'date' => $logItem['d'],
                'source' => $logItem['sourceStr'],
            ];
            $tmpStatisDatas = $statisModel->getDatasByWhere($where);
            //后台统计表没有，则不拆分
            if (!$tmpStatisDatas) {
                continue;
            }

            //减调用自有查得量
            $tmpStatisDatas = $tmpStatisDatas[0];

            $remainTotal = $tmpStatisDatas['total'] > $logItem['num'] ? $tmpStatisDatas['total'] - $logItem['num'] : 0;
            $remainSuccess = $tmpStatisDatas['success'] > $logItem['num'] ? $tmpStatisDatas['success'] - $logItem['num'] : 0;
            $remainValid = $tmpStatisDatas['valid'] > $logItem['num'] ? $tmpStatisDatas['valid'] - $logItem['num'] : 0;

            $newTotal = $remainTotal > 0 ? $logItem['num'] : $tmpStatisDatas['total'];
            $newSuccess = $remainSuccess > 0 ? $logItem['num'] : $tmpStatisDatas['success'];
            $newValid = $remainValid > 0 ? $logItem['num'] : $tmpStatisDatas['valid'];

            //全部为自有查得，记录ID，统一删除
            if ($remainTotal == 0) {
                file_put_contents(dirname(dirname(dirname(dirname(__DIR__)))) . '/storage/logs/delIds.txt', json_encode($tmpStatisDatas) . PHP_EOL,FILE_APPEND);
                //删除数据
                $deleteRes = $statisModel->deleteByIds([$tmpStatisDatas['id']]);
                if (!$deleteRes) {
                    file_put_contents(dirname(dirname(dirname(dirname(__DIR__)))) . '/storage/logs/delERROR.txt', json_encode($tmpStatisDatas) . PHP_EOL,FILE_APPEND);
                    continue;
                }
            }

            //重新生成统计推送数据
            $chargPid = $chargMap[$logItem['pids']];
            $newStatis = [
                'call_product_id' => $logItem['pids'],
                'apikey' => $logItem['apikey'],
                'operator' => $operatorMap[$logItem['carrier']],
                'source' => $logItem['sourceStr'],
                'total' => $newTotal,
                'success' => $newSuccess,
                'valid' => $newValid
            ];
            $pushDatas[$logItem['node']][$chargPid][] = $newStatis;

            $remainStatis = [
                'call_product_id' => $logItem['pids'],
                'apikey' => $logItem['apikey'],
                'operator' => $operatorMap[$logItem['carrier']],
                'source' => $logItem['sourceStr'],
                'total' => $remainTotal,
                'success' => $remainSuccess,
                'valid' => $remainValid
            ];
            $pushDatas[$logItem['node']][$logItem['pids']][] = $remainStatis;
        }

        //生成推送数据
        foreach ($pushDatas as $node => $itemArr) {
            foreach ($itemArr as $pid => $sendData) {
                $url    = 'http://back-api.dianhua.cn/statistics/customer/sendUsage';
                $params = [
                    'product_key' => 'fbaace1340a8706863ed6ae17560355c',
                    'product_id' => $pid,
                    'node' => $node,
                    'date' => $date,
                    'data' => $sendData,
                ];

                $res = $this->postRawJson($url, $params);
                echo "success\t{$node}\t{$pid}\t{$date}\t" . json_encode($res) . PHP_EOL;
            }
        }
    }

    /**
     * POST打包通过raw方式以JSON格式数据进行查询
     *
     * @access   public
     * <AUTHOR>
     * @datetime 2020/8/5 17:00
     *
     * @param $url     string 接口地址
     * @param $params  array 参数内容，以键值对形式存在
     * @param $options array 特殊的请求配置项， 详细参见curl_setopt方法的选项 [CURLOPT_TIMEOUT => 10]
     *
     * @return array 返回结果 ['code' => 0, 'message' => '', 'data' => '']
     *               code    CURL错误码
     *               message    CURL错误信息
     *               data    接口响应数据
     **/
    public function postRawJson($url, $params, $options = [])
    {
        $params                      = json_encode($params, JSON_UNESCAPED_UNICODE);
        $options[CURLOPT_HTTPHEADER] = [
            'X-AjaxPro-Method:ShowList',
            'Content-Type: application/json; charset=utf-8',
            'Content-Length: ' . strlen($params),
        ];
        $options[CURLOPT_POST]       = true;
        $options[CURLOPT_POSTFIELDS] = $params;

        return $this->curl($url, $options);
    }

    /**
     * 接口请求统一方法
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/8/5 16:25
     *
     * @param $url     string 接口地址
     * @param $options array 配置项
     *
     * @return array
     **/
    protected function curl($url, $options)
    {
        $curl = curl_init();

        $defaultOptions = [
            CURLOPT_URL            => $url,
            CURLOPT_CONNECTTIMEOUT => 3,
            CURLOPT_TIMEOUT        => 10,
            CURLOPT_RETURNTRANSFER => true,
        ];
        $options        = $defaultOptions + $options;
        curl_setopt_array($curl, $options);

        $data    = curl_exec($curl);
        $code    = curl_errno($curl);
        $message = curl_error($curl);
        $httpcode = curl_getinfo($curl,CURLINFO_HTTP_CODE); ;

        return compact('code', 'message', 'data');
    }
}