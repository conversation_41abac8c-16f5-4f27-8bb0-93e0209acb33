<?php

namespace App\Console\Commands\Balance;

use App\Models\Common\CommonEnumModel;
use Illuminate\Console\Command;
use App\Providers\BillIncome\BillStatistics\BalanceService;
use App\Models\Customer;
use App\Models\CustomerMonthlyBalance;


/**
 * 计算预付款用户每月余额数据
 *
 * php artisan balance:monthly --customer_id C20210310TDXJ8J --month_date 20230201
 * php artisan balance:monthly --customer_id C20210310TDXJ8J,C20200717CY26TG,C20210716I8QCT6,C20190606B7M68O
 */
class MonthlyBalance extends Command
{
    protected $signature = 'balance:monthly
    {--month_date= : 更新余额信息至该月份(格式Ymd)}
    {--customer_id= : 客户ID(多个客户ID以,隔开)}';

    protected $description = '计算预付款用户每月余额数据';

    /**
     * -1 为全部数据
     * 0 羽乐科技
     * 1 朴道
     * @var int[] 来源
     */
    protected $source_map = [];

    public function handle(){
        $this->output->success("计算用户每月余额数据脚本开始执行");

        $this->source_map = CommonEnumModel::getTypeMaps('1');
        $this->source_map[''] = '全部';

        //获取参数
        $params = $this->get_params();

        $start_time   = '20160101';
        $end_time     = array_get($params, 'month_date');
        $customer_id  = array_get($params, 'customer_id');
        $customer_map = array_get($params, 'customer_map');
        try {
            foreach ($this->source_map as $source => $source_name){
                $bs = new BalanceService($end_time,$start_time,$source);
                //分批次查询
                $customer_id_chunk = array_chunk($customer_id,25);
                foreach($customer_id_chunk as $customer_ids){
                    $res = $bs->getCustomerBalanceV2ByMonth($customer_ids);
                    foreach ($res as $cid => $info) {
                        foreach ($info as $month => $item) {
                            // if($month < $customer_map[$cid]['create_month'] && $item['balance'] == 0 && $item['consume'] == 0 && $item['remit'] == 0){
                            //     continue;
                            // }
                            //计算当月未回款 不需要考虑负数的情况
                            $unremit = bcsub($item['consume'],$item['remit'],6);

                            $where = [
                                'customer_id' => $cid,
                                'month_date'  => $month,
                                'source'      => $source === '' ? -1 : $source,
                            ];
                            $data = [
                                'customer_id' => $cid,
                                'month_date'  => $month,
                                'balance'     => $item['balance'],
                                'consume'     => $item['consume'],
                                'remit'       => $item['remit'],
                                'unremit'     => $unremit,
                                'source'      => $source === '' ? -1 : $source,
                            ];
                            //更新或插入
                            CustomerMonthlyBalance::updateOrCreate($where,$data);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $this->output->error($e->getMessage());
        }
        $this->output->success("计算用户每月余额数据脚本执行完成");
    }

    /**
     * 获取命令参数
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/18 15:53
     *
     * @return array
     */
    protected function get_params(){
        $params = [];

        //账单日
        $params['month_date'] = $this->input->getOption('month_date') ?? date('Ymd',strtotime(date('Ym01')) - 1);

        if (!preg_match('/^\d{8}$/', $params['month_date'])) {
            $this->output->error('日期格式不正确');
            die;
        }

        //客户ID
        $customer_id = $this->input->getOption('customer_id');
        if (!empty($customer_id)) {
            $customer_id = explode(',', $customer_id);
            //过滤掉禁用用户
            $customer_infos = Customer::getCustomerListByCustomerIds(['customer_id','name','company','create_at'],$customer_id,1);
        }else{
            // 获取所有预付款customer_id
            $customer_infos = Customer::getListByCondition(['is_delete' => 0],['customer_id','name','company','create_at']);
        }
        $customer_ids = [];
        $customer_map = [];
        foreach($customer_infos as $info){
            $customer_ids[] = $info['customer_id'];
            $customer_map[$info['customer_id']]['create_month'] = date("Ym",$info['create_at']);
            $customer_map[$info['customer_id']]['name']         =$info['name'];
            $customer_map[$info['customer_id']]['company']      =$info['company'];
        }
        $params['customer_id'] = $customer_ids;
        $params['customer_map'] = $customer_map;

        return $params;
    }
}