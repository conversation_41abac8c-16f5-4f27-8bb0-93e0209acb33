<?php

namespace App\Console\Commands;

use App\Models\MonitorLogProductModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

/**
 * Class MonitorCreateProductValueSpread 创建产品值分布
 * @package App\Console\Commands
 */
class MonitorCreateProductValueSpread extends Command
{
	protected $signature = "monitor:create_product_value_spread
	{--date= : 处理哪天的数据(Ymd)}
	";
	
	protected $description = '生成值分布数据';
	
	protected $date;
	
	/**
	 * @var \Redis
	 */
	protected $redis;
	
	public function handle()
	{
		//校验参数
		if (!$this->checkParams()) {
			return;
		}
		
		//链接redis
		$this->redis = Redis::connection('monitor_queue');
		
		//获取数据
	}
	
	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/26 16:51
	 *
	 * @return boolean
	 */
	protected function checkParams()
	{
		$this->date = $this->input->getOption('date');
		if (!$this->date) {
			$this->date = date('Ymd');
			
			return true;
		}
		
		if (!preg_match('/^\d{8}$/', $this->date)) {
			$this->output->error('');
			
			return false;
		}
		
		return true;
	}
	
	/**
	 * 从数据表中取出数据，并按小时|apikey|channel_id|product_id分组
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/26 16:48
	 *
	 * @return array
	 */
	protected function getItems()
	{
		//查看已经处理的最大ID
		$max_id = $this->redis->get('maxIdProductValueSpread_' . $this->date) ?: 0;
		
		$result = [];
		
		(new MonitorLogProductModel())->prefix($this->date)
									  ->select([
										  'apikey',
										  'product_id',
										  'channel_id',
										  'in_param',
										  'run_time',
										  'status',
										  'value',
										  'time',
									  ])
									  ->where('id', '>', $max_id)
									  ->orderBy('id', 'asc')
									  ->get()
									  ->map(function ($item) {
										  if ($item['status']) {
											  return;
										  }
			
			
									  });
		
		
	}
	
	
}