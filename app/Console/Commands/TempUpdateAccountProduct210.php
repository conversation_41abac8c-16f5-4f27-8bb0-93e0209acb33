<?php

namespace App\Console\Commands;

use App\Models\AccountProduct;
use App\Models\AccountProductModel;
use App\Models\Product;
use Illuminate\Console\Command;

/**
 * Class TempUpdateAccountProduct210 修改210产品的历史配置项
 * @package App\Console\Commands
 */
class TempUpdateAccountProduct210 extends Command
{
	public $signature = 'temp:update_account_product_210';
	
	
	public function handle()
	{
		$data = $this->getData();
		
		$mapping = $this->getMapping();
		
		$result = [];
		foreach ($data as $id => $item) {
			$item   = json_decode($item, true);
			$fields = array_get($item, 'fields');
			if (empty($fields)) {
				halt('不存在fields字段', $id, $item);
			}
			
			$product_ids = [];
			foreach ($fields as $field) {
				if (array_key_exists($field, $mapping)) {
					$product_ids[] = $mapping[$field];
				} else {
					halt($field);
				}
			}
			$item['product_ids'] = $product_ids;
			unset($item['sub_product_ids']);
			$result[$id]         = json_encode($item, JSON_UNESCAPED_UNICODE);
		}
		
		//进行校验一下
		foreach ($data as $id => $item) {
			$resultItem = json_decode($result[$id], true);
			unset($resultItem['product_ids']);
			unset($resultItem['sub_product_ids']);
			$item = json_decode($item, true);
			unset($item['product_ids']);
			unset($item['sub_product_ids']);
			$item = json_encode($item, JSON_UNESCAPED_UNICODE);
			if (json_encode($resultItem) !== $item || md5(json_encode($resultItem)) !== md5($item)) {
				$this->output->error('校验失败');
				$this->output->error('ID' . $id);
				$this->output->error('原始JSON' . $item);
			}
		}
		
		
		//修改数据
		foreach ($result as $id => $item) {
			AccountProduct::where('id', $id)->update(['data' => $item]);
		}
		
		$this->output->success('校验通过');
	}
	
	protected function getData()
	{
		return AccountProduct::where('product_id', 210)
							 ->pluck('data', 'id')
							 ->toArray();
	}
	
	protected function getMapping()
	{
		return Product::where('father_id', 210)
					  ->pluck('product_id', 'mark')
					  ->toArray();
	}
	
}