<?php

namespace App\Console\Commands;

use App\Models\BillCostV2;
use App\Models\Channel;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\ChannelInterface;
use Illuminate\Console\Command;
use App\Models\ChannelCostMinconsumeSpread;

//计算成本账单
class AutoBottomChannelCost extends Command
{
    protected $signature = 'auto_bottom_channel_cost
	{--channel_id= : 渠道ID，默认所有渠道}
    {--month= : 月份（个数Ym）}';

    protected $description = '自动分摊渠道保底费用';

    /**
     * 1.‌确定执行月份‌：‌
     * 不传--month 参数时，‌默认执行上个月的保底分摊。
     * 传入--month 参数时，‌执行指定月份的保底分摊。‌
     */
    public function handle()
    {
        $month = $this->input->getOption('month');
        if ($month) {
            if (!preg_match('/^\d{6}$/', $month)) {
                $this->output->error('日期格式错误,例:202408');
                return;
            }
            $month = date('Ym', strtotime('+1 months', strtotime($month . '01')));
        } else {
            $month = date('Ym');
        }
        $channel_id = $this->input->getOption('channel_id') ?: 0;
        $this->autoBottom($month, $channel_id);
    }

    #每月2号自动执行保底分摊
    public function autoBottom($month, $channel_id = 0)
    {
        $today_timestamp = strtotime($month . '01');
        #获取需要保底分摊的渠道 （支持传入指定渠道）。
        $channel_list = Channel::getNeedBottomCostChannel($channel_id);

        foreach ($channel_list as $channel_data) {
            $channel_id = $channel_data['channel_id'];
            $bottom_money = $channel_data['bottom_money'];
            $channel_ids = [$channel_id];

            #联通计费周期特殊处理
            if ($channel_id == 1) {
                $start_date = date('Y-m-27', strtotime('-2 months', $today_timestamp));
                $end_date = date('Y-m-26', strtotime('-1 months', $today_timestamp));
            } else {
                $start_date = date('Y-m-01', strtotime('-1 months', $today_timestamp));
                $end_date = date('Y-m-t', strtotime('-1 months', $today_timestamp));
            }

            #真实消耗
            $interface_ids = $this->getIids($channel_ids);   //渠道下(接口id)
            $real_cost = BillCostV2::getChannelSumCost($start_date, $interface_ids, $end_date);
            #特殊消耗
            $expend_cost = ChannelAccountAdjust::getChannelSumCost($start_date, $channel_ids, $end_date);
            #固定消耗
            $fixed_cost = ChannelAccountFixedFee::getChannelSumCost($start_date, $channel_ids, $end_date);
            #总成本
            $all_cost = bcadd($real_cost, $expend_cost, 6);
            $all_cost = bcadd($all_cost, $fixed_cost, 6);

            #总成本大于保底金额，无需执行保底
            if ($all_cost >= $bottom_money) {
                continue;
            }
            $need_bottom_money = bcsub($bottom_money, $all_cost, 6);
            $insert_data = [
                'month' => date('Ym', strtotime('-1 months', $today_timestamp)),
                'operator' => $channel_id,
                'money' => $need_bottom_money,
                'category' => 1,
                'remarks' => '脚本自动执行保底分摊',
            ];
            #入队列
            if (!ChannelCostMinconsumeSpread::addRecord($insert_data, 'AutoBottomChannelCost')) {
                $this->output->error("保底分摊失败");
            } else {
                $this->output->success("保底分摊成功");
            }
        }
    }

    #根据渠道ID 获取接口ID
    protected function getIids($channel_ids)
    {
        return array_column(ChannelInterface::getListByCondition([], ['id'], $channel_ids)->toArray(), 'id');
    }

}
