<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\MongoBillMonth;
use App\Models\ShortcutPackage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

//生成邦信分快捷版打包计费的月账单
class GenShortcutPackageMonthBill extends Command
{

    /** @var string 命令 */
    public $signature = 'bill:generate-shortcut-package-month 
    {--range=month : month上个月的账单, all所有月份的账单, year 本年度所有月份}
    {--customer_id= : 客户ID}
    {--month= : 月份, 在设置此参数的之后,则--range选项则被忽略, 格式Ym}
    ';

    protected $description = '生成快捷版打包客户的月账单';

    protected $customer_id = [
        'C201811012TPQQ8',
        'C20190225VJOUQA',
        'C20190801R97IIL'
    ];
	
	protected $account_id = [
		'C201811012TPQQ8' => '****************',      //读秒
		'C20190225VJOUQA' => '****************',      //广汽租赁
		'C20190801R97IIL' => '****************'       //杭银消金
	];
	
    protected $start_month = '201912';

    public function handle()
    {
        try {
            // 实际执行
            $this->handleDo();

            $this->output->writeln('生成账单成功');
            //$msg = '生成账单成功 --range:' . $this->option('range');
            //$this->outputInOneWay($msg);
        } catch (\Exception $e) {
            // 将本次命令生成的uuid,存储到redis中，给rollback备用
//            $this->storeUuidForRollback();
//
            $msg = '生成账单失败 msg: ' . $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
            $this->output->error($msg);
//            $this->outputInOneWay($msg, false);
        }
    }

    /**
     * 生成打包账单
     *
     * @access protected
     *
     * @return void
     **/
    protected function handleDo()
    {
        $month       = $this->getBillMonth();
        $customer_id = $this->getBillCustomerId();

        foreach ($month as $itemMonth) {
            foreach ($customer_id as $itemCustomerId) {
                $this->createItemMonthBill($itemMonth, $itemCustomerId);
            }
        }

    }

    /**
     * 获取需要生成的月份
     *
     * @access protected
     *
     * @return array
     **/
    protected function getBillMonth()
    {
        $month = $this->option('month');
        if ($month) {
            return $month >= $this->start_month ? [$month] : [];
        } else {
            $range     = $this->option('range');
            $end_month = date('Ym', strtotime('first day of last month'));
            if ($range == 'month') {
                return [$end_month];
            }
            $month = [];
            for ($i = strtotime($this->start_month); $i <= strtotime($end_month); $i += 2764800) {
                $month[] = date('Ym', $i);
            }
            return $month;
        }
    }

    /**
     * 获取需要生成的客户ID
     *
     * @access protected
     *
     * @return array
     **/
    protected function getBillCustomerId()
    {
        $customer_id = $this->option('customer_id');
        if ($customer_id) {
            return array_intersect($this->customer_id, [$customer_id]);
        }
        return $this->customer_id;
    }

    /**
     * 生成某一个月份某一个客户的月账单
     *
     * @access protected
     *
     * @param $month       string 月份
     * @param $customer_id string 客户ID
     *
     * @return void
     **/
    protected function createItemMonthBill($month, $customer_id)
    {
        //获取客户与之对应的apikey
        $apikey = Account::where('customer_id', '=', $customer_id)
            ->pluck('apikey')
            ->filter(
                function ($item) {
                    return !empty($item);
                }
            )
            ->toArray();

        $functionName = 'get' . $customer_id . 'NumberAndMoney';

        if (!method_exists($this, $functionName)) {
            throw new \Exception("不存在{$customer_id}的计费规则");
        }
        $detail = $this->$functionName($apikey, $month);

        $uuid                         = md5(time() . $customer_id);
        $account_id                   = $this->account_id[$customer_id];
        $product_id                   = 210;
        $section_begin                = $month . '01';
        $section_end                  = date('Ymd', strtotime('last day of last month', strtotime($month)));
        $section_source               = [];
        $money                        = array_sum(array_column($detail, 'money'));
        $section_number['fee_number'] = $detail;
        $section_invoked_number       = array_sum(array_column($detail, 'number'));
        $billMonth                    = compact(
            'uuid',
            'account_id',
            'month',
            'customer_id',
            'product_id',
            'section_number',
            'section_begin',
            'section_end',
            'money',
            'section_invoked_number',
            'section_source'
        );

        MongoBillMonth::create($billMonth);
    }

    /**
     * 获取读秒的计费用量&&费用
     *
     * @access protected
     *
     * @param $apikey array
     * @param $month  string 月份
     *
     * @return array
     **/
    protected function getC201811012TPQQ8NumberAndMoney($apikey, $month)
    {
        //获取计费用量
        //评分字段为1的，普通字段为0的请求数量
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where('graded_number', '=', 1)
            ->where('normal_number', '=', 0)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->count();
        $price  = 0.5;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');
        //评分字段为0的，普通字段为小于等于4的
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where('graded_number', '=', 0)
            ->where('normal_number', '<=', 4)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->count();
        $price  = 1.4;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');
        //平分字段为1，普通字段不为0
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where('graded_number', '=', 1)
            ->where('normal_number', '!=', 0)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->count();
        $price  = 2.57;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');
        return $list;
    }

    /**
     * 获取广汽租赁的计费用量&&费用
     *
     * @access protected
     *
     * @param $apikey array
     * @param $month  string 月份
     *
     * @return array
     **/
    protected function getC20190225VJOUQANumberAndMoney($apikey, $month)
    {
        //字段个数小于等于2个的
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where(DB::raw('`graded_number` + `normal_number`'), '<=', 2)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->sum(DB::raw('`graded_number` + `normal_number`'));

        $price  = 0.5;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');

        //字段格式为3的
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where(DB::raw('`graded_number` + `normal_number`'), '=', 3)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->sum(DB::raw('`graded_number` + `normal_number`'));
        $price  = 0.45;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');

        //字段格式为4的
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where(DB::raw('`graded_number` + `normal_number`'), '=', 4)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->sum(DB::raw('`graded_number` + `normal_number`'));
        $price  = 0.425;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');

        //字段格式为5的
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where(DB::raw('`graded_number` + `normal_number`'), '>=', 3)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->sum(DB::raw('`graded_number` + `normal_number`'));
        $price  = 0.4;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');

        return $list;
    }

    /**
     * 获取杭银消金的计费用量&&费用
     *
     * @access protected
     *
     * @param $apikey array
     * @param $month  string 月份
     *
     * @return array
     **/
    protected function getC20190801R97IILNumberAndMoney($apikey, $month)
    {
        //字段个数等于1个的
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where(DB::raw('`graded_number` + `normal_number`'), '=', 1)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->sum(DB::raw('`graded_number` + `normal_number`'));
        $price  = 0.3;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');

        //字段格式为3的
        $number = ShortcutPackage::whereIn('apikey', $apikey)
            ->where(DB::raw('`graded_number` + `normal_number`'), '=', 2)
            ->where('request_date', '>=', $month . '01')
            ->where('request_date', '<=', $this->getPrevMonthLastDay($month))
            ->sum(DB::raw('`graded_number` + `normal_number`'));
        $price  = 0.29;
        $money  = $number * $price;
        $list[] = compact('number', 'price', 'money');

        return $list;
    }

    /**
     * 获取某个月份的上个月份的最后一天
     *
     * @access protected
     *
     * @param $month string 月份
     *
     * @return string
     **/
    protected function getPrevMonthLastDay($month)
    {
        return date('Ymd', strtotime(date('Ym', strtotime($month . '01') + 2764800) . '01') - 1);
    }
}