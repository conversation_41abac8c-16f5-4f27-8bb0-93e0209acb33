<?php

namespace App\Console\Commands;

use App\Models\MonitorLogChannelModel;
use App\Models\MonitorLogProductModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

/**
 * Class AlarmQueueManagerCommand 预警队列管理
 * @package App\Console\Commands
 */
class MonitorQueueManagerCommand extends Command
{
	protected $signature   = "monitor:queue_manager
	{--sleep=3 : 睡眠时间}
	{--cli_id=0 : 队列ID}
	{--speed=100 : 速度，每次取出队列数据个数,设置越大所占用内存越高}
	{--once : 执行一次后退出}
	{--restart : 是否平滑重启}
	{--rollback : 错误回滚}
	";
	protected $description = '监控系统队列管理';
	
	protected $list_name = 'request_log';
	
	protected $count_channel = 'count_channel_table';
	protected $count_product = 'count_product_table';

	protected $notSaveApikey = [
	    '2a73a25b0e991edf77bef3670d9f3475',
        '76EE8FA57AE0bb6E78c9364163d62944',
        "4dfc29f22b23be9d60c42a82564661f2",
        "478deb18cc1ec42ef057e3174f897d9d"

    ];
	
	const COUNT_SEPARATOR = '##';
	
	protected $product_data = [];
	protected $channel_data = [];
	
	//脚本开始时间
	protected $start_date;
	
	/**
	 * @var \Redis
	 */
	protected $redis;
	
	//睡眠时间
	protected $sleep = 3;
	
	//监控ID
	protected $cli_id = 0;
	
	//数据处理速度
	protected $speed = 100;
	
	//执行一次后退出
	protected $once = false;
	
	public function __construct()
	{
		parent::__construct();
		
		//设置脚本开始执行时间
		$this->start_date = date('Y-m-d H:i:s');
		
		$this->redis = Redis::connection('monitor_queue');
	}
	
	public function handle()
	{
		if (!$this->checkParams()) {
			return;
		}
		
		//确认要做什么
		if ($this->input->getOption('restart')) {
			//平滑重启
			$this->redis->setex('monitor_queue_manager_restart', 4, 1);
		} else if ($this->input->getOption('rollback')) {
			//回滚错误
		} else {
			$this->inMysql();
		}
	}
	
	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/16 18:48
	 *
	 * @return boolean
	 */
	protected function checkParams()
	{
		$this->sleep = $this->input->getOption('sleep');
		if (!is_numeric($this->sleep) || $this->sleep <= 0) {
			$this->output->error("睡眠时间必须为一个正整数类型");
			
			return false;
		}
		
		$this->cli_id = $this->input->getOption('cli_id');
		if (!is_numeric($this->cli_id) || $this->cli_id < 0) {
			$this->output->error("进程ID必须为一个大于0的整数类型");
			
			return false;
		}
		
		$this->speed = $this->input->getOption('speed');
		if (!is_numeric($this->speed) || $this->speed <= 0) {
			$this->output->error("数据处理速度必须为一个正整数类型");
			
			return false;
		}
		
		$this->once = $this->input->getOption('once');
		
		return true;
	}
	
	/**
	 * 将数据入库到Mysql中
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/27 17:05
	 *
	 * @return void
	 */
	protected function inMysql()
	{
		while (true) {
			try {
				//拉取数据并整理成指定的格式，后续可以直接入库
				$result = $this->getRedisItems();
				if (!$result) {
					$this->writeLog();
					sleep($this->sleep);
					continue;
				}
				
				
				//基础日志数据入库MYSQL
				$this->insert();
				
				/**
				 * 对产品、渠道通过不同维度进行计数，并将计数存储在redis中
				 * 产品维度：key = [product_id]_[apikey]_[operator]
				 * 渠道维度：key = [product_id]_[channel_id]
				 *
				 * 另外，在保存计数数据时，需要校验各维度中的数据时间是否存在，如果不存在，则保存这一维度中的最小time字段值
				 *
				 * 在redis中存储的数据键分别为
				 *    产品维度计数数据：counter_product_key
				 *    产品维度计数时间数据：counter_time_product_key
				 *    渠道维度计数数据：counter_channel_key
				 *    渠道维度计数时间数据：counter_time_channel_key
				 *
				 **/
				$this->insertCounter();
				
				//将数据生成一个JOB，用于汇总数据（值分布）
				//$this->createValueSpreadJob();
				
				//打印日志
				$this->writeLog();
			} catch (\Exception $exception) {
				$this->output->error("脚本执行失败：" . $exception->getMessage() . ' AT Line:' . $exception->getLine() . ' ON ' . $exception->getFile());
				break;
			}
		}
	}
	
	
	/**
	 * 计算值分布Job
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 10:32
	 *
	 * @return void
	 */
	protected function createValueSpreadJob()
	{
		//dispatch(new CreateValueSpreadJob($this->product_data));
	}
	
	/**
	 * 平滑重启
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 10:06
	 *
	 * @return void
	 */
	protected function restart()
	{
		if ($this->redis->get('monitor_queue_manager_restart')) {
			die;
		}
		
		if ($this->once) {
			die;
		}
	}
	
	/**
	 * 成功信息打印
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/27 9:58
	 *
	 * @return void
	 */
	protected function writeLog()
	{
		//计算入库数据条数
		$product_count = count($this->product_data);
		$channel_count = count($this->channel_data);
		$count         = $product_count + $channel_count;
		$date          = date('Y-m-d H:i:s');
		$cli_id        = $this->cli_id;
		
		if ($count) {
			$message = "{$date}: [{$cli_id}]号队列成功入库{$count}条日志，其中产品日志{$product_count}条，渠道日志{$channel_count}条";
		} else {
			$message = "{$date}: [{$cli_id}]号队列未发现需要处理的日志，休息{$this->sleep}秒";
		}
		
		$this->output->writeln($message);
		
		//打印完成日志后，都需要监测是否需要重启，如果重启，直接die，由supervisor重启
		$this->restart();
	}
	
	/**
	 * 获取redis中的一次需要处理日志数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/27 17:33
	 *
	 * @return boolean 队列是否不为空
	 */
	protected function getRedisItems()
	{
		//先重置数据
		$this->product_data = [];
		$this->channel_data = [];
		
		
		//先查询一下队列中数据的数量，如果不存在，则需要睡眠三秒
		if (!$this->redis->lLen($this->list_name)) {
			return false;
		}
		
		//计数器，用于记录取出的数据条数
		$counter = 0;
		while ($counter < $this->speed) {
			$item = $this->redis->rPop($this->list_name);
			//如果队列已经空了，直接返回
			if (!$item) {
				return true;
			}
			//解析JSON数据，并将JSON数据中的每一条数据插入到成员变量中
			$item = @json_decode($item, true);
			if ($item) {
				$this->inProductMemberVariable($item);
				$this->inChannelMemberVariable($item);
			}
			$counter++;
		}
		
		return true;
	}
	
	/**
	 * 将一条请求日志按请求日期，请求方式，放入到产品成员变量中
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/27 17:52
	 *
	 * @param $data array 日志数据
	 *
	 * @return void
	 */
	protected function inProductMemberVariable($data)
	{
		if (!$data || !array_key_exists('product', $data)) {
			return;
		}
		$logs = $data['product'];
		array_walk($logs, function ($log) {
			$time                 = array_get($log, 'time') ?: time();
			$product_id           = array_get($log, 'product_id', 0);
			$operator             = array_get($log, 'operator') ?: '';
			$source               = array_get($log, 'source') ?: 0;
			$channel_id           = array_get($log, 'channel_id', 0);
			$apikey               = array_get($log, 'apikey', '');
			$in_param             = array_get($log, 'in_param', '');
			$run_time             = array_get($log, 'run_time', 0);
			$status               = array_get($log, 'status', 0);
			$sid                  = array_get($log, 'sid', '');
			$value                = array_get($log, 'value', '');
			$cli_id               = $this->cli_id;

			if( !in_array($apikey, $this->notSaveApikey) ){
                $this->product_data[] = compact('product_id', 'apikey', 'channel_id', 'operator', 'source','in_param', 'run_time', 'status', 'sid', 'value', 'cli_id', 'time');
            }
		});
	}
	
	/**
	 * 将一条请求日志按请求日期，请求方式，放入到渠道成员变量中
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/27 17:52
	 *
	 * @param $data array 日志数据
	 *
	 * @return void
	 */
	protected function inChannelMemberVariable($data)
	{
		if (!$data || !array_key_exists('channel', $data)) {
			return;
		}
		$logs = $data['channel'];
		array_walk($logs, function ($log) {
			$time                 = array_get($log, 'time') ?: time();
			$custom_key           = array_get($log, 'custom_key', 0);
			$channel_id           = array_get($log, 'channel_id', 0);
			$apikey               = array_get($log, 'apikey', 0);
			$in_param             = array_get($log, 'in_param', '');
			$run_time             = array_get($log, 'run_time', 0);
			$status               = array_get($log, 'status', 0);
			$info                 = array_get($log, 'info', '');
			$sid                  = array_get($log, 'sid', '');
			$cli_id               = $this->cli_id;
			$value                = array_get($log, 'value', '');

            if( !in_array($apikey, $this->notSaveApikey) ){
                $this->channel_data[] = compact('custom_key', 'channel_id', 'apikey', 'in_param', 'run_time', 'status', 'info', 'sid', 'value', 'cli_id', 'time');
            }
		});
	}
	
	/**
	 * 将数据插入到指定的数据表中
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/28 14:42
	 *
	 * @return void
	 */
	protected function insert()
	{
		//用户访问日志入库
		$this->insertProductLog();
		
		//渠道访问日志入库
		$this->insertChannelLog();
	}
	
	/**
	 * 以产品_账号_运营商维度进行计数，将其保存在计数表中
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/30 10:10
	 *
	 * @return void
	 */
	protected function insertCounter()
	{
		//开启管道操作
		$this->redis->pipeline(function ($pipeline) {
			//插入产品维度的计数数据
			$this->insertCounterProduct($pipeline);
			
			//插入渠道维度的计数数据
			$this->insertCounterChannel($pipeline);
		});
	}
	
	/**
	 * 渠道维度计数数据插入
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/30 11:51
	 *
	 * @param $pipeline Object redis管道对象
	 *
	 * @return void
	 */
	protected function insertCounterChannel($pipeline)
	{
		$data = $this->getCounterChannel();
		
		//遍历数据插入
		foreach ($data as $item) {
			//产品计数数据键
			$counter_key = $item['key'];
			//产品计数时间数据键
			$counter_time_key = $item['key'] . self::COUNT_SEPARATOR . 'time';
			
			$pipeline->hIncrBy($this->count_channel, $counter_key, intval($item['number']));
			$pipeline->hSetNx($this->count_channel, $counter_time_key, intval($item['time']));
		}
	}
	
	/**
	 * 获取产品_渠道维度的计数数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/30 10:15
	 *
	 * @return array
	 */
	protected function getCounterChannel()
	{
		$result = [];
		foreach ($this->channel_data as $prefix => $item) {
			$key = $item['custom_key'] . self::COUNT_SEPARATOR . $item['channel_id'];
			if (!array_key_exists($key, $result)) {
				$result[$key] = [
					'key'    => $key,
					'number' => 0,
					'time'   => $item['time'],
				];
			}
			$result[$key]['number']++;
			$result[$key]['time'] = min($result[$key]['time'], $item['time']);
		}
		
		return array_values($result);
	}
	
	/**
	 * 产品维度计数数据插入
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/30 11:40
	 *
	 * @param $pipeline Object redis管道对象
	 *
	 * @return void
	 */
	protected function insertCounterProduct($pipeline)
	{
		//获取产品维度的计数数据
		$data = $this->getCounterProduct();
		
		//遍历数据插入
		foreach ($data as $item) {
			//产品计数数据键
			$counter_key = $item['key'];
			//产品计数时间数据键
			$counter_time_key = $item['key'] . self::COUNT_SEPARATOR . 'time';
			
			$pipeline->hIncrBy($this->count_product, $counter_key, intval($item['number']));
			
			$pipeline->hSetNx($this->count_product, $counter_time_key, intval($item['time']));
		}
	}
	
	/**
	 * 获取产品_账号_运营商维度的计数数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/30 10:15
	 *
	 * @return array
	 */
	protected function getCounterProduct()
	{
		$result = [];
		foreach ($this->product_data as $item) {
			//$data 是某一天的一系列日志
			$key = $item['product_id'] . self::COUNT_SEPARATOR . $item['apikey'] . self::COUNT_SEPARATOR . $item['channel_id'];
			if (!array_key_exists($key, $result)) {
				$result[$key] = [
					'key'    => $key,
					'number' => 0,
					'time'   => $item['time'],
				];
			}
			$result[$key]['number']++;
			$result[$key]['time'] = min($result[$key]['time'], $item['time']);
		}
		
		return array_values($result);
	}
	
	/**
	 * 用户访问日志入库
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/28 14:43
	 *
	 * @return void
	 */
	protected function insertProductLog()
	{
		if (empty($this->product_data)) {
			return;
		}
		
		(new MonitorLogProductModel())->insert($this->product_data);
	}
	
	/**
	 * 渠道访问日志入库
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/28 14:43
	 *
	 * @return void
	 */
	protected function insertChannelLog()
	{
		if (empty($this->channel_data)) {
			return;
		}
		(new MonitorLogChannelModel())->insert($this->channel_data);
	}
}