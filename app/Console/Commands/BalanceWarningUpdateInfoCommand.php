<?php

namespace App\Console\Commands;

use App\Jobs\CreateLogsJob;
use App\Models\BalanceWarningBasicsModel;
use App\Models\CommonInfoModel;
use App\Models\Customer;
use App\Providers\BillIncome\BillStatistics\BalanceService;
use App\Providers\BillIncome\BillStatistics\IncomeService;
use App\Providers\BillIncome\CalculateCustomerIncomeService;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

/**
 * Class BalanceWarningUpdateInfoCommand 创建余额预警所需的数据，
 * @package App\Console\Commands
 */
class BalanceWarningUpdateInfoCommand extends Command
{
	use WechatExceptionTrait;
	
	protected $signature = "balance_warning:update_info";
	
	/**
	 * @var integer 今日的日期
	 */
	private $today;
	
	/**
	 * @var integer 昨日的日期
	 */
	private $yesterday;
	
	/**
	 * @var integer 本月月初的日期
	 */
	private $this_month_first_date;
	
	/**
	 * @var integer 上月月末的日期
	 */
	private $prev_month_last_date;
	
	public function __construct()
	{
		parent::__construct();
		$this->today                 = date('Ymd');
		$this->yesterday             = date('Ymd', strtotime('-1 days'));
		$this->this_month_first_date = date('Ymd', strtotime('first day of this month'));
		$this->prev_month_last_date  = date('Ymd', strtotime('last day of -1 months'));
	}
	
	public function handle()
	{
		$datetime = date('Y-m-d H:i:s');

		try {
			//获取数据
			$data = $this->getInfo();

			//数据入库
			$this->inMysql($data);
			
			//记录最后一次的更新时间
			$this->writeUpdateTime($datetime);
		} catch (\Exception $exception) {
			$this->wechat("更新余额预警数据时出现BUG，{$exception->getMessage()}");
		}
	}
	
	/**
	 * 记录最后一次的更新时间
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 11:43
	 *
	 * @param $datetime string 更新日期
	 *
	 * @return void
	 */
	private function writeUpdateTime($datetime)
	{
		CommonInfoModel::where('id', 1)
					   ->update([
						   'content' => $datetime,
					   ]);
	}
	
	/**
	 * 数据入库
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 11:26
	 *
	 * @param $data array 数据入库
	 *
	 * @return void
	 */
	private function inMysql($data)
	{
		array_walk($data, function ($item) {
			//数据入库
			BalanceWarningBasicsModel::updateOrCreate(array_only($item, ['customer_id']), $item);
			
			//记录日志
			$this->log($item);
		});
	}
	
	/**
	 * 记录日志
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 11:27
	 *
	 * @param $data array 每个客户的日志
	 *
	 * @return void
	 */
	private function log($data)
	{
		dispatch(new CreateLogsJob('MongoLogsBalanceWarningUpdateInfo', $data));
	}
	
	
	/**
	 * 获取余额预警中存储的数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/12/15 18:13
	 *
	 * @throws \Exception
	 *
	 * @return array
	 */
	private function getInfo()
	{
		//获取今日的营收统计数据
//		$today_incomes = $this->getTodayIncomes();
		$today_incomes = [];    //原来不生成当日账单，现在会实时统计，不需要实时计算当日营收

		//获取本月的营收统计数据
		$this_month_incomes = $this->getThisMonthIncomes($today_incomes);

		//获取当前的余额数据
		$balances = $this->getBalances($today_incomes);

		//获取所有客户的授信额度
		$credits = $this->getCredits();

		//获取当前的授信余额 = 授信额度 + 当前的余额
		$credit_balances = $this->getCreditBalances($balances, $credits);
		
		//获取所有客户的剩余授信额度 = 授信余额 - 本月的营收统计
		$surplus_credits = $this->getSurplusCredits($credit_balances, $this_month_incomes);

		//获取客户平均每日营收统计的基准值
		$everyday_incomes = $this->getEverydayIncomes();

		
		//根据客户的付款类型预估其可用天数
		$days = $this->estimateUsableDays($balances, $everyday_incomes);
		
		//融合数据
		return $this->combineInfo($today_incomes, $this_month_incomes, $balances, $credits, $credit_balances, $surplus_credits, $everyday_incomes, $days);
	}
	
	/**
	 * 融合余额预警中存储的数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 11:14
	 *
	 * @param $today_incomes      array 今日的营收统计数据
	 * @param $this_month_incomes array 本月的营收统计数据
	 * @param $balances           array 当前的余额数据
	 * @param $credits            array 授信额度
	 * @param $credit_balances    array 授信余额
	 * @param $surplus_credits    array 本月剩余授信额度
	 * @param $everyday_incomes   array 每日营收统计的基准值
	 * @param $days               array 预估可用天数
	 *
	 * @return array
	 */
	private function combineInfo($today_incomes, $this_month_incomes, $balances, $credits, $credit_balances, $surplus_credits, $everyday_incomes, $days)
	{
		$result       = [];
		$customer_ids = array_keys($this_month_incomes);
		foreach ($customer_ids as $customer_id) {
			$result[$customer_id] = [
				'customer_id'       => $customer_id,
				'today_income'      => array_get($today_incomes, $customer_id, 0),
				'this_month_income' => array_get($this_month_incomes, $customer_id, 0),
				'balance'           => array_get($balances, $customer_id, 0),
				'credit'            => array_get($credits, $customer_id, 0),
				'credit_balance'    => array_get($credit_balances, $customer_id, 0),
				'surplus_credit'    => array_get($surplus_credits, $customer_id, 0),
				'everyday_income'   => array_get($everyday_incomes, $customer_id, 0),
				'days'              => array_get($days, $customer_id, 0),
			];
		}

		return $result;
	}
	
	/**
	 * 预估可用天数
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/15 18:45
	 *
	 * @param $balances                   array 每个客户的余额
	 * @param $surplus_credits            array 授信余额
	 * @param $everyday_incomes           array 每个客户的平均日营收基准值
	 *
	 * @return array
	 */
	private function estimateUsableDays($balances, $everyday_incomes)
	{
        //设置结果数据存储变量
        $result = [];
        $customer_ids = Customer::getAllCustomerIds();

        foreach ($customer_ids as $customer_id) {
            $everyday_income = array_get($everyday_incomes, $customer_id, 0);
            $surplus_credit = array_get($balances, $customer_id, 0);
            if ($everyday_income == 0 || $surplus_credit == 0){
                $result[$customer_id] = 0;
            }else{
                $result[$customer_id] = $this->estimateDaysCustomer($surplus_credit, $everyday_income);
            }
        }
//		//获取所有客户的付款类型
//		$paymentTypes = $this->getCustomerPaymentTypes();
//		foreach ($paymentTypes as $customer_id => $paymentType) {
//			$balance         = array_get($balances, $customer_id, 0);
//			$surplus_credit  = array_get($surplus_credits, $customer_id, 0);
//			$everyday_income = array_get($everyday_incomes, $customer_id, 0);
//
//			switch ($paymentType) {
//				case 1:
//					$result[$customer_id] = $this->estimateDaysForAdvanceCustomer($balance, $everyday_income);
//					break;
//				case 2:
//					$result[$customer_id] = $this->estimateDaysForPostPaymentCustomer($surplus_credit, $everyday_income);
//					break;
//				default:
//					throw new \Exception("客户【{$customer_id}】的付款类型【{$paymentType}】不正确");
//					break;
//			}
//		}
		
		return $result;
	}

    /**
     * 授信余额计算客户预估可用天数
     * @param $balance
     * @param $everyday_income
     * @return false|float|int
     */
    private function estimateDaysCustomer($balance, $everyday_income)
    {
        return ceil(bcdiv($balance, $everyday_income, 6));
    }

	/**
	 * 预付款客户预估可用天数
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 10:46
	 *
	 * @param $balance         float 客户的余额
	 * @param $everyday_income float 客户的日营收基准值
	 *
	 * @return integer
	 */
	private function estimateDaysForAdvanceCustomer($balance, $everyday_income)
	{
		if ($balance <= 0) {
			return 0;
		}
		if ($everyday_income <= 0) {
			return -1;
		}
		
		return ceil(bcdiv($balance, $everyday_income, 6));
		
	}
	
	/**
	 * 后付款客户预估可用天数
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 10:59
	 *
	 * @param $surplus_credit    float 本月的剩余授信额度
	 * @param $everyday_income   float 客户的日营收基准值
	 *
	 * @return integer
	 */
	private function estimateDaysForPostPaymentCustomer($surplus_credit, $everyday_income)
	{
		if ($surplus_credit <= 0) {
			return 0;
		}
		
		if ($everyday_income <= 0) {
			return -1;
		}
		
		return ceil(bcdiv($surplus_credit, $everyday_income, 6));
	}
	
	/**
	 * 获取所有客户的付款类型
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 10:45
	 *
	 * @return array
	 */
	private function getCustomerPaymentTypes()
	{
		return Customer::where('is_delete', 0)
					   ->pluck('payment_type', 'customer_id')
					   ->toArray();
	}
	
	/**
	 * 获取客户平均每日营收统计的基准值
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 10:04
	 *
	 * @return array
	 */
	private function getEverydayIncomes()
	{
		/**
		 * 根据前7天的营收数据，计算每日营收的基准值
		 */
		$start_date = date('Ymd', strtotime('-17 days'));
		$end_date   = date('Ymd', strtotime('-2 days'));
		$incomes    = (new IncomeService($end_date, $start_date))->getCustomerIncome();
		
		return array_map(function ($money) {
			return bcdiv($money, 15, 6);
		}, array_column($incomes, 'money', 'customer_id'));
	}
	
	/**
	 * 获取剩余授信额度
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/16 16:25
	 *
	 * @param $credits            array 客户的授信额度
	 * @param $this_month_incomes array 客户的本月消费
	 *
	 * @return array
	 */
	private function getSurplusCredits($credits, $this_month_incomes)
	{
		foreach ($this_month_incomes as $customer_id => $this_month_income) {
			if (!array_key_exists($customer_id, $credits)) {
				$credits[$customer_id] = 0;
			}
			$credits[$customer_id] = bcsub($credits[$customer_id], $this_month_income, 6);
		}
		
		return $credits;
	}
	
	/**
	 * 获取当前的授信余额
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/15 18:37
	 *
	 * @param $balances array 当前的余额数据
	 * @param $credits  array 客户的授信额度
	 *
	 * @return array
	 */
	private function getCreditBalances($balances, $credits)
	{
		//计算每一个客户的授信余额
		foreach ($credits as $customer_id => $credit) {
			if (!array_key_exists($customer_id, $balances)) {
				$balances[$customer_id] = 0;
			}
			
			$balances[$customer_id] = bcadd($credit, $balances[$customer_id], 6);
		}
		
		return $balances;
	}
	
	/**
	 * 获取当前所有客户的授信额度
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/15 18:38
	 *
	 * @return array
	 */
	private function getCredits()
	{
		return Customer::where('is_delete', 0)
					   ->pluck('credit', 'customer_id')
					   ->toArray();
	}
	
	/**
	 * 获取今日的余额数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/15 18:31
	 *
	 * @param $today_incomes array 当日的营收统计数据
	 *
	 * @return array
	 */
	private function getBalances($today_incomes)
	{
		//获取截止昨日的余额
		$balances = (new BalanceService($this->today))->getCustomerBalance();
		$balances = array_column($balances, 'money', 'customer_id');

		//对每一个客户进行 昨日余额-当日的消费，既得到了当日的余额
		foreach ($today_incomes as $customer_id => $today_income) {
			if (!array_key_exists($customer_id, $balances)) {
				$balances[$customer_id] = 0;
			}
			
			$balances[$customer_id] = bcsub($balances[$customer_id], $today_income, 6);
		}
		
		return $balances;
	}
	
	/**
	 * 获取本月的营收统计数据（包含当日）
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/15 18:15
	 *
	 * @param $today_incomes array 当日的营收统计数据
	 *
	 * @return array
	 */
	private function getThisMonthIncomes($today_incomes)
	{
		$incomes = (new IncomeService($this->today, $this->this_month_first_date))->getCustomerIncome();
		$incomes = array_column($incomes, 'money', 'customer_id');
		
		foreach ($today_incomes as $customer_id => $today_income) {
			if (!array_key_exists($customer_id, $incomes)) {
				$incomes[$customer_id] = 0;
			}
			
			$incomes[$customer_id] = bcadd($incomes[$customer_id], $today_income, 6);
		}
		
		return $incomes;
	}
	
	/**
	 * 获取今日的营收统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/15 17:41
	 *
	 * @return array
	 */
	private function getTodayIncomes()
	{
		$service = new CalculateCustomerIncomeService($this->today);

		return $service->getCustomerIncomes();
	}
}