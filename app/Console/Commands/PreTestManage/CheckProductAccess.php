<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\PreTestManage;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ConfigPriceCustomer;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyProduct;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 检查产品是否接入情况
 * php artisan pre_test_manage:check_product_access
 * @uses CheckProductAccess
 */
class CheckProductAccess extends Command
{
    protected $signature = 'pre_test_manage:check_product_access';

    const CRON_NAME = '[检查产品是否接入]';

    private $sNow = null;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->sNow = date('Y-m-d H:i:s');
    }

    private function main() {
        $aCond = [
            ['customer_id', '<>', ''],
        ];
        $aApplyList = ApplyCustomer::aGetListByCond($aCond);
        if (empty($aApplyList)) {
            return true;
        }
        $iTotalOpenCount = 0;
        foreach ($aApplyList as $aApply) {
            $iApplyId = $aApply['id'];
            $sCustomerId = $aApply['customer_id'];

            $aApplyProductList = $this->aGetApplyProductList($iApplyId);
            if (empty($aApplyProductList)) {
                continue;
            }
            $aProductId = array_column($aApplyProductList, 'product_id');
            $aAccountList = Account::getListByCustomerIds([$sCustomerId]);
            if (empty($aAccountList)) {
                continue;
            }
            // 计算每个子产品开通情况
            $aActivateMap = $this->getActivateConfig($aAccountList, $aProductId);
            if (empty($aActivateMap)) {
                continue;
            }
            // 计算每个子产品是否配置计费配置
            $aFeeConfigMap = $this->getFeeConfig($aAccountList, $aProductId);
            // 更新 子产品维度开通状态,计费配置
            $iOpenCount = $this->updateProduct($aApplyProductList, $aActivateMap, $aFeeConfigMap);
            $iTotalOpenCount += $iOpenCount;
            // 更新 主产品维度开通状态
            $this->updateFather($iApplyId);
        }

        $this->sendFeishuUserMessage('已开通子产品数量:' . $iTotalOpenCount);

        return true;
    }

    /**
     * @param $iApplyId
     * @return array
     */
    private function aGetApplyProductList($iApplyId = 0) {
        if (!$iApplyId) {
            return [];
        }
        // 调用情况可查的子产品
        $aCond = [
            ['apply_id', '=', $iApplyId],
            ['product_id', '>', 0],
            ['father_id', '>', 0],
        ];
        return ApplyProduct::aGetDataByCond($aCond);
    }

    /**
     * @param $aAccountList
     * @param $aProductId
     * @return array
     */
    private function getActivateConfig($aAccountList = [], $aProductId = []) {
        // account_product 配置情况
        $aAccountProductList = $this->aGetAccountProductDataList($aAccountList, $aProductId);
        if (empty($aAccountProductList)) {
            return [];
        }
        // 计算每个子产品是否开通
        return $this->computeOpen($aProductId, $aAccountProductList);
    }

    /**
     * @param $aAccountList
     * @param $aProductId
     * @return array
     */
    private function aGetAccountProductDataList($aAccountList = [], $aProductId = []) {
        if (empty($aAccountList) || empty($aProductId)) {
            return [];
        }
        $aAccountId = array_column($aAccountList, 'account_id');
        if (empty($aAccountId)) {
            return [];
        }
        $aCond = [
            'account_id' => $aAccountId,
            'product_id' => $aProductId,
            'status' => AccountProduct::STATUS_AVAILABLE
        ];
        $aField = ['account_id', 'product_id', 'status', 'end_time', 'create_at', 'update_at'];
        return AccountProduct::getList($aCond, $aField);
    }

    /**
     * @param $aProductId
     * @param $aAccountProductList
     * @return array
     */
    private function computeOpen($aProductId = [], $aAccountProductList = []) {
        $aMapList = [];
        foreach ($aAccountProductList as $aData) {
            $iProductId = $aData['product_id'];
            if (!in_array($iProductId, $aProductId)) {
                continue;
            }

            $sConfigEndTime = date('Y-m-d H:i:s', $aData['end_time']);

            if ($sConfigEndTime < $this->sNow) {
                continue;
            }
            $aMapList[$iProductId][] = $aData['create_at'];
        }

        $aMap = [];
        foreach ($aMapList as $iProductId => $aTime) {
            $aMap[$iProductId] = min($aTime);
        }

        return $aMap;
    }

    /**
     * @param $aAccountList
     * @param $aProductId
     * @return array
     */
    private function getFeeConfig($aAccountList = [], $aProductId = []) {
        $aFeeConfigList = $this->getFeeConfigDataList($aAccountList, $aProductId);
        if (empty($aFeeConfigList)) {
            return [];
        }

        return $this->computeFeeConfig($aProductId, $aFeeConfigList);
    }

    /**
     * @param $aAccountList
     * @param $aProductId
     * @return array
     */
    private function getFeeConfigDataList($aAccountList = [], $aProductId = []) {
        if (empty($aAccountList) || empty($aProductId)) {
            return [];
        }
        $aApikey = array_column($aAccountList, 'apikey');
        if (empty($aApikey)) {
            return [];
        }

        return ConfigPriceCustomer::select(['apikey', 'product_ids', 'start_date'])
            ->whereIn('apikey', $aApikey)->whereNull('delete_time')->get()->toArray();
    }

    /**
     * @param $aProductId
     * @param $aFeeConfigList
     * @return array
     */
    private function computeFeeConfig($aProductId = [], $aFeeConfigList = []) {
        $aProductTime = [];
        foreach ($aFeeConfigList as $aData) {
            $sConfProductId = $aData['product_ids'];
            $aConfProductId = explode(',', $sConfProductId);
            $iStartTime = strtotime($aData['start_date']);
            foreach ($aConfProductId as $iConfProductId) {
                $aProductTime[$iConfProductId][] = $iStartTime;
            }
        }

        $aMap = [];
        foreach ($aProductId as $iProductId) {
            $aTime = $aProductTime[$iProductId] ?? [];
            $aMap[$iProductId] = $aTime ? min($aTime) : 0;
        }

        return $aMap;
    }

    /**
     * @param $aApplyProductList
     * @param $aActivateMap
     * @param $aFeeConfigMap
     * @return int
     */
    private function updateProduct($aApplyProductList = [], $aActivateMap = [], $aFeeConfigMap = []) {
        $iOpenCount = 0;
        $aUpdateList = [];
        foreach ($aApplyProductList as $aApplyProduct) {
            $iProductId = $aApplyProduct['product_id'];
            $iActivateTime = $aActivateMap[$iProductId] ?? 0;
            $iFeeConfigTime = $aFeeConfigMap[$iProductId] ?? 0;

            if ($iActivateTime) {
                $iOpenCount += 1;
            }
            $aUpdateList[] = [
                'id' => $aApplyProduct['id'],
                'activate_time' => $iActivateTime ? date('Y-m-d H:i:s', $iActivateTime) : null,
                'is_open' => $iActivateTime ? PreTestManage::IS_YES : PreTestManage::IS_NO,
                'fee_config_time' => $iFeeConfigTime ? date('Y-m-d H:i:s', $iFeeConfigTime) : null,
                'is_fee' => $iFeeConfigTime ? PreTestManage::IS_YES : PreTestManage::IS_NO,
            ];
        }
        if ($aUpdateList) {
            ApplyProduct::batchUpdateData($aUpdateList, 'id');
        }

        return $iOpenCount;
    }

    /**
     * @param $iApplyId
     * @return true
     */
    private function updateFather($iApplyId = 0) {
        $aCond = [
            ['apply_id', '=', $iApplyId],
            ['product_id', '>', 0],
            ['father_id', '>', 0],
        ];
        $aApplyProductList = ApplyProduct::aGetDataByCond($aCond);
        $aApplyFatherId = [];
        $aActivateMap = [];
        $aFeeConfigMap = [];
        foreach ($aApplyProductList as $aItem) {
            $aApplyFatherId[] = $aItem['apply_father_id'];
            $iActivateTime = $aItem['activate_time'] ? strtotime($aItem['activate_time']) : 0;
            $aActivateMap[$aItem['apply_father_id']][] = $iActivateTime;
            $iFeeConfigTime = $aItem['fee_config_time'] ? strtotime($aItem['fee_config_time']) : 0;
            $aFeeConfigMap[$aItem['apply_father_id']][] = $iFeeConfigTime;
        }

        $aApplyFatherId = array_unique($aApplyFatherId);
        foreach ($aApplyFatherId as $iApplyFatherId) {
            $aActivateTime = $aActivateMap[$iApplyFatherId] ?? [];
            $aActivateTime = array_filter($aActivateTime);
            $iActivateTime = !empty($aActivateTime) ? min($aActivateTime) : 0;

            $aFeeConfigTime = $aFeeConfigMap[$iApplyFatherId] ?? [];
            $aFeeConfigTime = array_filter($aFeeConfigTime);
            $iFeeConfigTime = !empty($aFeeConfigTime) ? min($aFeeConfigTime) : 0;

            $aUpdate = [
                'is_open' => $iActivateTime ? PreTestManage::IS_YES : PreTestManage::IS_NO,
                'activate_time' => $iActivateTime ? date('Y-m-d H:i:s', $iActivateTime) : NULL,
                'is_fee' => $iFeeConfigTime ? PreTestManage::IS_YES : PreTestManage::IS_NO,
                'fee_config_time' => $iFeeConfigTime ? date('Y-m-d H:i:s', $iFeeConfigTime) : NULL,
                'test_status' => PreTestManage::STATUS_ACCESS_DONE,
            ];
            ApplyFather::iUpdateById([$iApplyFatherId], $aUpdate);
        }

        return true;
    }

    /**
     * @param $sMsg
     * @return void
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }
}