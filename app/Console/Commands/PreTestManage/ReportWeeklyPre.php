<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\PreTestManage;
use App\Models\Account;
use App\Models\Common\CommonEnumModel;
use App\Models\ConfigPriceCustomer;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyFatherObjective;
use App\Models\PreTestManage\ApplyProduct;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Models\SystemUser;
use App\Repositories\FeishuRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 售前测试管理系统 售前部门-周报统计
 * php artisan pre_test_manage:report_weekly_pre
 * @uses ReportWeeklyPre
 */
class ReportWeeklyPre extends Command
{
    protected $signature = 'pre_test_manage:report_weekly_pre';

    const CRON_NAME = '[售前部门-周报统计]';

    private $aSalesmanDeptMap = [];
    private $aSalesNameDeptMap = [];
    private $aSalesNameMap = [];
    private $aDeptMap = [];

    private $iTheDay = null;

    private $toDay = null;

    private $aInitCond = [];

    private $aObjectiveMap = [];

    /**
     * @return void
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sErrMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $aUserList = SystemUser::getAllUsers();
        $this->aSalesmanDeptMap = array_column($aUserList, 'dept_id', 'realname');
        $this->aSalesNameDeptMap = array_column($aUserList, 'dept_id', 'username');
        $this->aSalesNameMap = array_column($aUserList, 'realname', 'username');
        $aDeptList = SystemDept::getAllDeptInfo();
        $this->aDeptMap = array_column($aDeptList, 'dept_name', 'dept_id');
        $iLaterDay = strtotime('+3 days', time()); // 3天后日期
        $iThisFriday = strtotime("friday this week", $iLaterDay); // 3天后日期 所在周 周五
        $iEndTime = strtotime('-1 weeks', $iThisFriday); // 1周前 周五
        $this->iTheDay = strtotime(date('Y-m-d', $iEndTime));
        $this->aInitCond = [
            ['apply_time', '>=', '2025-01-01 00:00:00'],
        ];
        $this->aObjectiveMap = CommonEnumModel::aGetPreTestMap();
        $this->toDay = strtotime(date('Y-m-d 00:00:00', strtotime('+1 day'))); //今日结束的日期
    }

    /**
     * @return true
     */
    private function main() {
        // 异常客户
        $aAbnormalData = $this->abnormalData();
        // 商务新增效果反馈
        $aFeedbackData = $this->feedbackData();
        // 新增调用客户
        $aCallData = $this->callData();
        // 测试贡献计费量 TOP5客户
        $feeData = $this->feeData();

        $aData = [
            'abnormal_data' => $aAbnormalData,
            'feedback_data' => $aFeedbackData,
            'call_data' => $aCallData,
            'fee_data' => $feeData,
        ];
        $this->sendFeishuUserMessage(json_encode($aData));

        return true;
    }

    /**
     * 异常客户
     * @return array
     */
    private function  abnormalData() {
        // 连续60天未反馈效果
        $aAbnormalList1 = $this->getAbnormalType1();
        // 效果好超过30天未上线
        $aAbnormalList2 = $this->getAbnormalType2();
        // 上线满30天,累计调用量<1000的客户
        $aAbnormalList3 = $this->getAbnormalType3();

        $aAbnormalTypeList = array_merge($aAbnormalList1, $aAbnormalList2, $aAbnormalList3);
        $aApplyId = array_column($aAbnormalTypeList, 'apply_id');
        $aApplyCustomerList = ApplyCustomer::aGetListByApplyIdList($aApplyId);
        $aApplyCustomerMap = array_column($aApplyCustomerList, null, 'id');

        $aRet1 = $this->formatAbnormalData($aAbnormalList1, '连续60天,未反馈效果', $aApplyCustomerMap);
        $aRet2 = $this->formatAbnormalData($aAbnormalList2, '效果好,超过30天未上线', $aApplyCustomerMap);
        $aRet3 = $this->formatAbnormalData($aAbnormalList3, '上线满30天,累计调用量不到1000', $aApplyCustomerMap);

        return array_merge($aRet1, $aRet2, $aRet3);
    }

    /**
     * @return array
     */
    private function getAbnormalType1() {
        $aCond = [
            ['return_time', '<', date('Y-m-d H:i:s', $this->toDay - 60 * 86400)],
            ['return_time', '>=', date('Y-m-d H:i:s', $this->toDay - 67 * 86400)],
            ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT]
        ];
        $aCond = array_merge($this->aInitCond, $aCond);

        return ApplyFather::aGetDataByCond($aCond);
    }

    /**
     * @return array
     */
    private function getAbnormalType2() {
        $aCond = [
            ['feedback_time', '<', date('Y-m-d H:i:s', $this->toDay - 30 * 86400)],
            ['feedback_time', '>=', date('Y-m-d H:i:s', $this->toDay - 37 * 86400)],
            ['test_result', '=', PreTestManage::TEST_RESULT_GOOD],
            ['is_open', '=', PreTestManage::IS_NO],
        ];
        $aCond = array_merge($this->aInitCond, $aCond);

        return ApplyFather::aGetDataByCond($aCond);
    }

    /**
     * @return array
     */
    private function getAbnormalType3() {
        $aCond = [
            ['activate_time', '<', date('Y-m-d H:i:s', $this->toDay - 30 * 86400)],
            ['activate_time', '>=', date('Y-m-d H:i:s', $this->toDay - 37 * 86400)],
            ['is_open', '=', PreTestManage::IS_YES],
            ['number_total', '<', 1000],
        ];
        $aCond = array_merge($this->aInitCond, $aCond);

        return ApplyFather::aGetDataByCond($aCond);
    }

    /**
     * @param $aAbnormalTypeList
     * @param $sAbnormalType
     * @param $aApplyCustomerMap
     * @return array
     */
    private function formatAbnormalData($aAbnormalTypeList = [], $sAbnormalType = '', $aApplyCustomerMap = []) {
        $aRet = [];
        foreach ($aAbnormalTypeList as $aItem) {
            $aApplyCustomer = $aApplyCustomerMap[$aItem['apply_id']] ?? [];
            if (empty($aApplyCustomer)) {
                continue;
            }

            $aRet[] = [
                'company_short_name' => $aApplyCustomer['company_short_name'],
                'company_type' => PreTestManage::COMPANY_TYPE_TEXT_MAP[$aApplyCustomer['company_type']] ?? '-',
                'dept' => $this->aDeptMap[$this->aSalesmanDeptMap[$aItem['salesman']]] ?? '-',
                'salesman' => $aItem['salesman'],
                'apply_father_name' => $aItem['apply_father_name'],
                'return_time' => $aItem['return_time'],
                'abnormal_type' => $sAbnormalType,
            ];
        }

        return $aRet;
    }

    /**
     * @return array
     */
    private function feedbackData() {
        $aCond = [
            ['feedback_time', '>=', date('Y-m-d H:i:s', $this->iTheDay)],
            ['feedback_time', '<=', date('Y-m-d H:i:s', time())],
        ];

        $aCond = array_merge($this->aInitCond, $aCond);
        $aApplyFatherList = ApplyFather::aGetDataByCond($aCond);

        // 客户信息
        $aApplyId = array_column($aApplyFatherList, 'apply_id');
        $aApplyCustomerList = ApplyCustomer::aGetListByApplyIdList($aApplyId);
        $aApplyCustomerMap = array_column($aApplyCustomerList, null, 'id');
        // 指标信息
        $aApplyFatherId = array_column($aApplyFatherList, 'id');
        $aFatherObjectiveMap = $this->getFatherObjectiveMap($aApplyFatherId);
        // 子产品信息
        $aProductIdStr = array_column($aApplyFatherList, 'product_list');
        $aProductIdList = array_unique(explode(',', implode(',', $aProductIdStr)));
        $aProductList = Product::getProductListByProductIds($aProductIdList, ['product_id', 'product_name']);
        $aProductMap = array_column($aProductList, 'product_name', 'product_id');

        $aRet = [];
        foreach ($aApplyFatherList as $aItem) {
            $aApplyCustomer = $aApplyCustomerMap[$aItem['apply_id']] ?? [];
            if (empty($aApplyCustomer)) {
                continue;
            }
            $aObjective = $aFatherObjectiveMap[$aItem['id']] ?? [];
            $aProductId = array_filter(array_unique(explode(',', $aItem['product_list'])));
            $aProductName = [];
            foreach ($aProductId as $iProductId) {
                $aProductName[] = $aProductMap[$iProductId];
            }

            $aRet[] = [
                'company_short_name' => $aApplyCustomer['company_short_name'],
                'dept' => $this->aDeptMap[$this->aSalesmanDeptMap[$aItem['salesman']]] ?? '-',
                'salesman' => $aItem['salesman'],
                'apply_father_name' => $aItem['apply_father_name'],
                'product_name' => implode(',', $aProductName),
                'return_time' => $aItem['return_time'],
                'test_result' => PreTestManage::TEST_RESULT_TEXT_MAP[$aItem['test_result']] ?? '-',
                'objective' => implode(', ', $aObjective),
                'access_action' => PreTestManage::ACCESS_ACTION_TEXT_MAP[$aItem['access_action']] ?? '-',
            ];
        }

        return $aRet;
    }

    /**
     * @param $aApplyFatherId
     * @return array
     */
    private function getFatherObjectiveMap($aApplyFatherId = []) {
        $aFatherObjectiveList = ApplyFatherObjective::aGetListByApplyIdList($aApplyFatherId);

        $aMap = [];
        foreach ($aFatherObjectiveList as $aItem) {
            $aMap[$aItem['apply_father_id']][] = $this->aObjectiveMap[$aItem['objective_id']] . ': ' . $aItem['result_value'];
        }

        return $aMap;
    }

    /**
     * @return array
     */
     private function callData(){
         $startDate = date('Y-m-d H:i:s', $this->iTheDay);
         $endDate = date('Y-m-d H:i:s',time());

         $aCond = [
             ['call_time', '>=', $startDate],
             ['call_time','<',$endDate]
         ];
         // 前一周内申请调用客户、产品
         $applyProducts= ApplyProduct::aGetListByCond($aCond);
         if (empty($applyProducts)){
             return [];
         }
         $applyIds = array_column($applyProducts,'apply_id');
         $applyCustomerInfos = ApplyCustomer::aGetListByApplyIdList($applyIds);
         $applyIdInfo = array_column($applyCustomerInfos,null,'id');

         $productIds = array_unique(array_column($applyProducts,'product_id'));
         $aProductList = Product::getProductListByProductIds($productIds, ['product_id', 'product_name']);
         $aProductMap = array_column($aProductList, 'product_name', 'product_id');


         $aRet = [];
         foreach ($applyProducts as $applyProduct){
             $applyCutomerInfo = isset($applyIdInfo[$applyProduct['apply_id']]) ? $applyIdInfo[$applyProduct['apply_id']] : [];
             if (empty($applyCutomerInfo)){
                 continue;
             }
             $company_short_name = isset($applyCutomerInfo['company_short_name']) ? $applyCutomerInfo['company_short_name'] : '-';
             $salesman = isset($applyCutomerInfo['salesman']) ? $applyCutomerInfo['salesman'] : '-';
             $name = isset($applyCutomerInfo['email']) ? strstr($applyCutomerInfo['email'], '@', true) : '';
             $customerId = $applyCutomerInfo['customer_id'];
             $fee_date = $applyProduct['call_time'];
             $index = $customerId . $applyProduct['product_id'];
             $aRet[$index] = [
                 'company_short_name' => $company_short_name,
                 'dept' => $this->aDeptMap[$this->aSalesNameDeptMap[$name]] ?? '-',
                 'salesman' => $salesman,
                 'product_name' => $aProductMap[$applyProduct['product_id']] ?? $applyProduct['product_id'],
                 'fee_date' => $fee_date,
                 'week_number' => $applyProduct['number_total_7d'],
                 'total_number' => $applyProduct['number_total'],
             ];
         }

         return array_values($aRet);
     }

    /**
     * @return array
     */
    private function feeData()
    {
        $sStartDate = date('Y-m-d H:i:s', $this->iTheDay);
        $sEndDate = date('Y-m-d H:i:s', time());

        $aCond = [
            ['updated_at', '<=', $sEndDate],
            ['updated_at', '>=', $sStartDate],

        ];
        $aCond = array_merge($this->aInitCond, $aCond);
        $apply_fees = ApplyFather::aGetDataByCond($aCond,['*'],'fee_total');

        if (empty($apply_fees)) {
            return [];
        }

        $applyIds = array_column($apply_fees,'apply_id');
        $aCond = [['id', 'in', $applyIds]];

        $apply_customers = ApplyCustomer::aGetListByCond($aCond);

        $customer_applys = [];
        foreach ($apply_customers as $apply_customer){
           $customer_applys[$apply_customer['customer_id']][] =  $apply_customer['id'];
        }

        $customer_infos = array_column($apply_customers,null,'id');

        $res_data = [];
        foreach ($apply_fees as $apply_fee){
            foreach ($customer_applys as $customer_id => $customer_apply){
                if (in_array($apply_fee['apply_id'],$customer_apply)){
                     if (isset($res_data[$customer_id])){
                         $res_data[$customer_id]['fee_total'] += $apply_fee['fee_total'];
                         $res_data[$customer_id]['fee_total_7d'] += $apply_fee['fee_total_7d'];
                         array_push($res_data[$customer_id]['apply_father_name'],$apply_fee['apply_father_name']);
                     }else{
                         $company_short_name = isset($customer_infos[$apply_fee['apply_id']]) ? $customer_infos[$apply_fee['apply_id']]['company_short_name'] : '';
                         $salesman = isset($customer_infos[$apply_fee['apply_id']]) ? strstr($customer_infos[$apply_fee['apply_id']]['email'], '@', true) : '';
                         $res_data[$customer_id] =  ['fee_total'=>$apply_fee['fee_total'],'fee_total_7d'=>$apply_fee['fee_total_7d'],'company_short_name'=>$company_short_name,'salesman'=>$salesman,'apply_father_name'=>[$apply_fee['apply_father_name']]];
                     }
                }
            }
        }

        // 1. 按score字段降序排序
        usort($res_data, function($a, $b) {
            return $b['fee_total_7d'] - $a['fee_total_7d']; // 降序排列
        });

        // 2. 取前5个元素
        $top5 = array_slice($res_data, 0, 5);

        $aRet = [];
        foreach ($top5 as $v){
            $salesman = $v['salesman'];
            $deptId = $this->aSalesNameDeptMap[$salesman] ?? null;
            $aRet[] = [
                'company_short_name' => $v['company_short_name'],
                'apply_father_name' => implode(',',array_unique($v['apply_father_name'])),
                'dept' => ($deptId !== null && isset($this->aDeptMap[$deptId])) ? $this->aDeptMap[$deptId] : '-',
                'salesman' => isset($this->aSalesNameMap[$v['salesman']]) ? $this->aSalesNameMap[$v['salesman']] : $v['salesman'],
                'fee_total_7d' =>  $v['fee_total_7d'],
                'fee_total' =>  $v['fee_total'],
            ];
        }

        return  $aRet;
    }



    /**
     * @param $sMsg
     * @return void
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_PRETERT_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }
}