<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\PreTestManage;
use App\Http\Repository\DeptRepository;
use App\Models\Dept;
use App\Models\PreTestManage\ActionRecord;
use App\Models\SystemAccessLog;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 售前测试管理系统 操作日志分析
 * php artisan pre_test_manage:record_action_log --start_time=1725897600 --end_time=1725984000
 * @uses RecordActionLog
 */
class RecordActionLog extends Command
{
    protected $signature = 'pre_test_manage:record_action_log
    {--start_time= : 开始时间}
    {--end_time= : 结束时间}';

    const CRON_NAME = '[操作日志分析]';

    /**
     * 申请时间-起止范围
     * @var int
     */
    private $iStartTime = 0;
    private $iEndTime = 0;

    /**
     * @return void
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s, 开始时间:%s, 结束时间:%s",
                $sNow, $cost, date('Y-m-d H:i:s', $this->iStartTime), date('Y-m-d H:i:s', $this->iEndTime));
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sErrMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->iEndTime = (int)($this->input->getOption('end_time')) ?: strtotime(date('Y-m-d') . '00:00:00');
        $this->iStartTime = (int)($this->input->getOption('start_time')) ?: strtotime('-1 days', $this->iEndTime);
    }

    /**
     * @return true
     */
    private function main() {
        $sDeptId = Dept::SALE_DEPT_ID;
        $aDeptInfo = DeptRepository::getSaleDeptRecursion($sDeptId, true);
        $aDeptData = $aDeptInfo['dept_infos'] ?? [];
        $aUserList = $this->getDeptSalesmanListByOption($aDeptData, false);
        $aUserList[] = 'kai.lin';
        $aUserList[] = 'chang.liu';
        $aUserList[] = 'ren.zhang';

        $aCond = [
            ['created_at', '>=', $this->iStartTime],
            ['created_at', '<', $this->iEndTime],
            ['username', 'in', $aUserList],
            ['uri', 'like', 'pre_test_manage%'],
        ];
        $aDataList = SystemAccessLog::getUserHourGroupDataByCond($aCond);

        $aUserDateDataList = [];
        foreach ($aDataList as $aData) {
            $sHour = $aData['hour'] . ':00:00';
            $sDay = date('Ymd', strtotime($sHour));
            $iHour = date('H', strtotime($sHour));

            $aUserDateDataList[$aData['username']][$sDay][$iHour] = $aData['count'];
        }

        $aInsertList = [];
        foreach ($aUserDateDataList as $sUser => $aDateDataList) {
            foreach ($aDateDataList as $sDate => $aData) {
                $aInsertList[] = [
                    'username' => $sUser,
                    'date' => $sDate,
                    'content' => json_encode($aData),
                ];
            }
        }
        if ($aInsertList) {
            ActionRecord::insert($aInsertList);
        }

        return true;
    }

    /**
     * @param $aNodeList
     * @return array
     */
    private function getDeptSalesmanListByOption($aNodeList = [], $bRealName = true) {
        $aSalesmanList = [];
        foreach ($aNodeList as $aNode) {
            if ($aNode['type'] == 'dept') {
                $aData = $this->getDeptSalesmanListByOption($aNode['children'], $bRealName);
                $aSalesmanList = array_merge($aSalesmanList, $aData);
            } else if ($aNode['type'] == 'salesman') {
                $aSalesmanList[] = $bRealName ? $aNode['label'] : $aNode['value'];
            }
        }

        return $aSalesmanList;
    }

    /**
     * @param $sMsg
     * @return void
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }
}