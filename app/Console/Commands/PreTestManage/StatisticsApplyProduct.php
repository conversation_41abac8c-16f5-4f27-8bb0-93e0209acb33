<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\PreTestManage;
use App\Define\StatDefine;
use App\Models\Account;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyProduct;
use App\Models\StatisticsCustomerUsage;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 统计接入客户调用情况
 * php artisan pre_test_manage:statistics_apply_product
 * @uses StatisticsApplyProduct
 */
class StatisticsApplyProduct extends Command
{
    protected $signature = 'pre_test_manage:statistics_apply_product;
    {--action= : 操作}';

    const CRON_NAME = '[统计接入客户调用情况]';

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {}

    private function main() {
        // 计算 对接时间
        $this->runCallTime();
       // 计算 所有调用量、近7天调用量 近90天调用量，调用天数
       $this->runTotalNumber();
        // 计算 计费量(所有和上周)
        $this->runTotalFee();

        return true;
    }

    /**
     * @return true
     */
    private function runCallTime() {
        // 已绑定生产客户
        $aCond = [
            ['customer_id', '<>', ''],
        ];
        $aApplyList = ApplyCustomer::aGetListByCond($aCond);

        $aCustomerList = array_column($aApplyList, 'customer_id');
        if (empty($aCustomerList)) {
            return true;
        }
        $aCustomerList = array_values(array_unique($aCustomerList));
        $aCusApiMap = $this->aGetApiKeyList($aCustomerList);

        $aUpdate = [];
        $sNow = date('Y-m-d H:i:s');
        $aNeedCheckApplyId = [];
        foreach ($aApplyList as $aApply) {
            $iApplyId = $aApply['id'];
            $sCustomerId = $aApply['customer_id'];

            $aApiKeyList = $aCusApiMap[$sCustomerId];
            if (empty($aApiKeyList)) {
                continue;
            }

            $aCond = [
                ['apply_id' , '=', $iApplyId],
                ['father_id' , '>', 0],
                ['product_id' , '>', 0],
                ['is_fee' , '=', PreTestManage::IS_YES],
                ['is_open' , '=', PreTestManage::IS_YES],
            ];
            $aApplyProductList = ApplyProduct::aGetDataByCond($aCond, ['id', 'apply_id', 'father_id', 'product_id', 'is_open', 'call_time', 'fee_config_time'], ['call_time']);
            if (empty($aApplyProductList)) {
                continue;
            }

            // 设置计费之后 首次调用时间
            foreach ($aApplyProductList as $aApplyProduct){
                $aFirstData = $this->sGetFirstData($aApiKeyList, $aApplyProduct['fee_config_time'], [$aApplyProduct['product_id']]);
                if ($aFirstData){
                    $aUpdate[] = [
                        'id' => $aApplyProduct['id'],
                        'call_time' => date('Y-m-d H:i:s', strtotime(strval($aFirstData))),
                        'updated_at' => $sNow,
                    ];
                }
            }
        }

        if ($aUpdate) {
            ApplyProduct::batchUpdateData($aUpdate, 'id');
        }
        if ($aNeedCheckApplyId) {
            $aApplyProductList = ApplyProduct::aGetListByApplyIdList($aNeedCheckApplyId, ['id', 'apply_id', 'apply_father_id', 'is_call', 'call_time']);

            $aFatherCalled = [];
            foreach ($aApplyProductList as $aItem) {
                $aItem['call_time'] and $aFatherCalled[$aItem['apply_father_id']][] = $aItem['call_time'];
            }
            // 主产品维度
            $aFatherUpdate = [];
            foreach ($aFatherCalled as $iApplyFatherId => $aDate) {
                $aFatherUpdate[] = [
                    'id' => $iApplyFatherId,
                    'call_time' => min($aDate),
                    'updated_at' => $sNow,
                ];
            }
            if ($aFatherUpdate) {
                ApplyFather::batchUpdateData($aFatherUpdate, 'id');
            }
        }

        return true;
    }

    /**
     * @param $aCustomerList
     * @return array
     */
    private function aGetApiKeyList($aCustomerList) {
        $aList = Account::getListByCustomerIds($aCustomerList);

        $aMap = [];
        foreach ($aList as $aItem) {
            $aMap[$aItem['customer_id']][] = $aItem['apikey'];
        }

        return $aMap;
    }

    /**
     * @param $aApiKeyList
     * @param $sFeeConfigTime
     * @param $aProductId
     * @return String
     */
    private function sGetFirstData($aApiKeyList = [], $sFeeConfigTime = '', $aProductId = []) {
        $aFirstDataList = StatisticsCustomerUsage::aGetFirstDataByApiKey($aApiKeyList, $sFeeConfigTime, $aProductId);
        if ($aFirstDataList){
             $dates = array_column($aFirstDataList,'date');
             return min($dates);
        }
        return  '';
    }

    /**
     * @return true
     */
    private function runTotalNumber() {
        // 已绑定生产客户
        $aCond = [
            ['customer_id', '<>', ''],
        ];
        $aApplyList = ApplyCustomer::aGetListByCond($aCond);

        $aCustomerList = array_column($aApplyList, 'customer_id');
        if (empty($aCustomerList)) {
            return true;
        }
        $aCustomerList = array_values(array_unique($aCustomerList));
        $aCusApiMap = $this->aGetApiKeyList($aCustomerList);

        $sNow = date('Y-m-d H:i:s');
        $aApplyGroupList = array_chunk($aApplyList, 50);
        foreach ($aApplyGroupList as $aApplyGroup) {
            $aUpdate = [];
            foreach ($aApplyGroup as $aApply) {
                $iApplyId = $aApply['id'];
                $sCustomerId = $aApply['customer_id'];
                $sApplyTime = $aApply['apply_time'];

                $aApiKeyList = $aCusApiMap[$sCustomerId];
                if (empty($aApiKeyList)) {
                    continue;
                }

                $aCond = [
                    ['apply_id', '=', $iApplyId],
                    ['father_id', '>', 0],
                    ['product_id', '>', 0],
                    ['is_open', '=', PreTestManage::IS_YES],
                ];
                $aApplyProductList = ApplyProduct::aGetDataByCond($aCond, ['id', 'apply_id', 'father_id', 'product_id', 'is_open']);
                if (empty($aApplyProductList)) {
                    continue;
                }

                $aProductId = array_column($aApplyProductList, 'product_id');
                $aProductDataMap = $this->aGetTotalNumber($aApiKeyList, $aProductId, $sApplyTime);

                foreach ($aApplyProductList as $aItem) {
                    if (isset($aProductDataMap[$aItem['product_id']])) {
                        $aUpdate[] = [
                            'id' => $aItem['id'],
                            'number_total' => $aProductDataMap[$aItem['product_id']]['number_total'],
                            'number_total_7d' => $aProductDataMap[$aItem['product_id']]['number_total_7d'],
                            'number_total_90d' => $aProductDataMap[$aItem['product_id']]['number_total_90d'],
                            'date_total_90d' => $aProductDataMap[$aItem['product_id']]['date_total_90d'],
                            'updated_at' => $sNow,
                        ];
                    }
                }
            }

            if ($aUpdate) {
                ApplyProduct::batchUpdateData($aUpdate, 'id');
            }

            // 聚合到 father维度
            $aApplyId = array_column($aApplyGroup, 'id');
            $aStatList = ApplyProduct::aGetListByApplyIdList($aApplyId, ['apply_id', 'apply_father_id', 'number_total', 'number_total_7d','number_total_90d']);
            $aMap = [];
            foreach ($aStatList as $aStat) {
                $aMap[$aStat['apply_father_id']]['number_total'] = 0;
                $aMap[$aStat['apply_father_id']]['number_total_7d'] = 0;
                $aMap[$aStat['apply_father_id']]['number_total_90d'] = 0;
            }


            foreach ($aStatList as $aStat) {
                $aMap[$aStat['apply_father_id']]['number_total'] +=  $aStat['number_total'];;
                $aMap[$aStat['apply_father_id']]['number_total_7d'] +=  $aStat['number_total_7d'];
                $aMap[$aStat['apply_father_id']]['number_total_90d'] +=  $aStat['number_total_90d'];
            }
            $aUpdate = [];
            foreach ($aMap as $iApplyFatherId => $iNumberTotal) {
                $aUpdate[] = [
                    'id' => $iApplyFatherId,
                    'number_total' => $iNumberTotal['number_total'],
                    'number_total_7d' => $iNumberTotal['number_total_7d'],
                    'number_total_90d' => $iNumberTotal['number_total_90d'],
                    'updated_at' => $sNow,
                ];
            }
            if ($aUpdate) {
                ApplyFather::batchUpdateData($aUpdate, 'id');
            }
        }

        return true;
    }

    /**
     * @param $aApiKeyList
     * @param $aProductId
     * @param $sApplyTime
     * @return array
     */
    private function aGetTotalNumber($aApiKeyList = [], $aProductId = [], $sApplyTime = '') {
        $sStartData = date('Ymd', strtotime($sApplyTime));
        $eStartData = date('Ymd',time());

        $repository = new MainRepository();
        $aDataList = $repository->getBaseCustomerUsage(StatDefine::DIMENSION_DATE_PRODUCT, $sStartData, $eStartData, [], $aApiKeyList, [], $aProductId, ['is_query_month_data' => 0]);

        if ($aDataList['status'] != 0 || !isset($aDataList['data']) || empty($aDataList['data'])){
            return [];
        }

        $fee_date = $this->getFeeDate();
        $ninety_date = $this->getNinetyDate();
        $aMap = [];
        foreach ($aDataList['data'] as $aItem) {
            $iProductId = $aItem['product_id'];
            $date = $aItem['date'];
            $aMap[$iProductId]['number'][] = (int)$aItem['success'];

            if ($date >= $fee_date[0] && $date <= $fee_date[1]){
                $aMap[$iProductId]['number_total_7d'][] = (int)$aItem['success'];
            }

            if ($date >= $ninety_date[0] && $date <= $ninety_date[1]){
                $aMap[$iProductId]['number_total_90d'][] = (int)$aItem['success'];
                $aMap[$iProductId]['date'][] = (int)$aItem['date'];
            }
        }

        $aDataMap = [];
        foreach ($aProductId as $iProductId) {
            if (isset($aMap[$iProductId])) {
                $aDataMap[$iProductId] = [
                    'number_total' => array_sum($aMap[$iProductId]['number']),
                    'number_total_7d' => isset($aMap[$iProductId]['number_total_7d']) ? array_sum($aMap[$iProductId]['number_total_7d']) : 0,
                    'number_total_90d' => isset($aMap[$iProductId]['number_total_90d']) ? array_sum($aMap[$iProductId]['number_total_90d']) : 0,
                    'date_total_90d' => isset($aMap[$iProductId]['date']) ? count(array_unique($aMap[$iProductId]['date'])) : 0,
                ];
            }
        }

        return $aDataMap;
    }



    private function runTotalFee()
    {
        // 已绑定生产客户
        $aCond = [
            ['customer_id', '<>', ''],
        ];
        $aApplyList = ApplyCustomer::aGetListByCond($aCond);


        $aCustomerList = array_column($aApplyList, 'customer_id');
        if (empty($aCustomerList)) {
            return true;
        }

        $aCustomerList = array_values(array_unique($aCustomerList));
        $aCusApiMap = $this->aGetApiKeyList($aCustomerList);


        $sNow = date('Y-m-d H:i:s');
        $aApplyGroupList = array_chunk($aApplyList, 50);

        $fee_date = $this->getFeeDate();

        foreach ($aApplyGroupList as $aApplyGroup){
            $aUpdate = $this->getFeeUpdateData($aApplyGroup,$aCusApiMap,$sNow,$fee_date);
            if ($aUpdate) {
                ApplyProduct::batchUpdateData($aUpdate, 'id');
            }
            // 聚合到 father维度
            $aApplyId = array_column($aApplyGroup, 'id');
            $aStatList = ApplyProduct::aGetListByApplyIdList($aApplyId, ['apply_id', 'apply_father_id', 'fee_total','fee_total_7d']);
            $aMap = [];
            foreach ($aStatList as $aStat) {
                if (isset($aMap[$aStat['apply_father_id']]['fee_total'])){
                    $aMap[$aStat['apply_father_id']]['fee_total'] += $aStat['fee_total'];
                }else{
                    $aMap[$aStat['apply_father_id']]['fee_total'] = $aStat['fee_total'];
                }

                if (isset($aMap[$aStat['apply_father_id']]['fee_total_7d'])){
                    $aMap[$aStat['apply_father_id']]['fee_total_7d'] += $aStat['fee_total_7d'];
                }else{
                    $aMap[$aStat['apply_father_id']]['fee_total_7d'] = $aStat['fee_total_7d'];
                }
            }
            $aUpdate = [];
            foreach ($aMap as $iApplyFatherId => $iNumberTotal) {
                $aUpdate[] = [
                    'id' => $iApplyFatherId,
                    'fee_total_7d' => $iNumberTotal['fee_total_7d'],
                    'fee_total' => $iNumberTotal['fee_total'],
                    'updated_at' => $sNow,
                ];
            }
            if ($aUpdate) {
                ApplyFather::batchUpdateData($aUpdate, 'id');
            }
        }
        return true;
    }



    /**
     * @return array
     */
    private function getFeeUpdateData($aApplyGroup,$aCusApiMap,$sNow,$fee_date){
        $aUpdate = [];
        if (empty($aApplyGroup)){
            return $aUpdate;
        }
        foreach ($aApplyGroup as $aApply){
            $iApplyId = $aApply['id'];
            $sCustomerId = $aApply['customer_id'];
            $sApplyTime = $aApply['apply_time'];

                $aApiKeyList = $aCusApiMap[$sCustomerId];
                if (empty($aApiKeyList)) {
                    continue;
                }
                $aCond = [
                    ['apply_id', '=', $iApplyId],
                    ['father_id', '>', 0],
                    ['product_id', '>', 0],
                    ['is_fee', '=', PreTestManage::IS_YES],
                    ['is_open', '=', PreTestManage::IS_YES],
                ];
                $aApplyProductList = ApplyProduct::aGetDataByCond($aCond, ['id', 'apply_id', 'father_id', 'product_id', 'is_open']);
                if (empty($aApplyProductList)) {
                    continue;
                }

                $aProductId = array_column($aApplyProductList, 'product_id');
                $dimension = StatDefine::INCOME_DIMENSION_DATE_PRODUCT;
                $rep = new MainRepository();

                $sDate = date('Ymd',strtotime($sApplyTime));
                $eDate = date('Ymd',strtotime($sNow));

                // 查询累计的计费量
                $current_res = $rep->getBaseIncome($dimension, $sDate, $eDate, [$sCustomerId], [], [], $aProductId, ['is_query_month_data'=>0]);

                if(!isset($current_res['status']) || $current_res['status'] !=0 || empty($current_res['data'])){
                    continue;
                }

                $totalProductDataMap = [];
                $lastweekProductDataMap = [];
                foreach ($current_res['data'] as $item){
                    $date= $item['date'];
                    $product_id = $item['product_id'];
                    $number = $item['number'];
                    if(isset($totalProductDataMap[$product_id]['number'])){
                        $totalProductDataMap[$product_id]['number'] += $number;
                    }else{
                        $totalProductDataMap[$product_id]['number'] = $number;
                    }
                    if ($date >= $fee_date[0] && $date <= $fee_date[1]){
                        if(isset($lastweekProductDataMap[$product_id]['number'])){
                            $lastweekProductDataMap[$product_id]['number'] += $number;
                        }else{
                            $lastweekProductDataMap[$product_id]['number'] = $number;
                        }
                    }
                }
            foreach ($aApplyProductList as $aItem) {
                $update = [];
                if (isset($totalProductDataMap[$aItem['product_id']])) {
                    $update = ['id' => $aItem['id'], 'updated_at' => $sNow, 'fee_total'=>$totalProductDataMap[$aItem['product_id']]['number']];
                }
                if (isset($lastweekProductDataMap[$aItem['product_id']])){
                    $update['fee_total_7d'] = $lastweekProductDataMap[$aItem['product_id']]['number'];
                }
                if ($update && isset($update['id'])){
                    $aUpdate[] = $update;
                }
            }
        }

        return $aUpdate;
    }

    /**
     *
     * @return array
     */
    private function getFeeDate(){

        $today = date('Ymd');
        $thursWeek = date('Ymd', strtotime('this thursday'));

        if ($today <= $thursWeek){ //本周四前
            $fridayWeek = date('Ymd', strtotime('last friday'));
        }else{ //周五及之后
            $fridayWeek = date('Ymd', strtotime('this friday'));
            $thursWeek = date('Ymd', strtotime('next thursday'));
        }

        return [$fridayWeek,$thursWeek];
    }

    private function getNinetyDate(){
        $sStartData = date('Ymd', strtotime('-90 days'));
        $eStartData = date('Ymd',time());

        return [$sStartData,$eStartData];
    }


    /**
     * @param $sMsg
     * @return void
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }
}