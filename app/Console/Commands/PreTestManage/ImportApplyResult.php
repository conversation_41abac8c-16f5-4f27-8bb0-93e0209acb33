<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\PreTestManage;
use App\Mail\PreTestMonitorCustomerAbnormal;
use App\Models\Account;
use App\Models\Customer;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyFatherObjective;
use App\Models\PreTestManage\ApplyList;
use App\Models\PreTestManage\ApplyProduct;
use App\Models\PreTestManage\Approval\Approval;
use App\Models\PreTestManage\Approval\ApprovalTransfer;
use App\Models\PreTestManage\Approval\CallStable;
use App\Models\PreTestManage\Approval\ExpectOnline;
use App\Models\PreTestManage\Approval\Grayscale;
use App\Models\PreTestManage\Approval\PreTest;
use App\Models\PreTestManage\BusinessProduct;
use App\Models\PreTestManage\BusinessProductData;
use App\Models\PreTestManage\MonitorApproval;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * 数据导入
 * php artisan pre_test_manage:import_apply_result --action=1
 * @uses ImportApplyResult
 */
class ImportApplyResult extends Command
{
    protected $signature = 'pre_test_manage:import_apply_result;
    {--action= : 操作}';

    const CRON_NAME = '数据导入';

    private $iAction = null;
    /**
     * @var FeishuRepository
     */
    private $oFeishuPreTestRep = null;

    /**
     * @var BusinessProductData
     */
    protected $oBusinessProduct = null;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s, 操作:%s",
                $sNow, $cost, $this->iAction);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $this->output->warning($sErrMsg);

            $sMsg = self::CRON_NAME . '脚本执行失败! ' . $sErrMsg;
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->iAction = (int)($this->input->getOption('action')) ?: 0;

        $this->oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = '8cc1f829';
        try {
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $this->oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    /**
     * @return true
     */
    private function main() {
        switch ($this->iAction) {
            case 1:
                // 绑定产品
                $this->main_bind_product();
                break;
            case 2:
                // 绑定客户
                $this->main_bind_customer();
                break;
            case 3:
                // 刷 father表 product_list字段值
                $this->main_v3();
                break;
            case 4:
                // 刷 工单实际状态
                $this->main_v4();
                break;
            case 5:
                // 查看 未录入father表的工单 测试产品枚举
//                $this->main_v5();
                break;
            case 6:
                // 刷 未录入father表的工单
                $this->main_v6();
                break;
            case 7:
                // 刷 客户监控salesman字段
                $this->main_v7();
                break;
            case 8:
                $this->main_v8();
                break;
            case 9:
                $this->main_v9();
                break;
            case 10:
                $this->main_v10();
                break;
            default:
                break;
        }

        return true;
    }

    /**
     * @param $aStatusList
     * @return mixed|null
     */
    private function calculateStatusPriority($aStatusList) {
        $aPriority = PreTestManage::STATUS_PRIORITY;
        // 最高优先级
        $iHighPriority = 0;
        // 最高优先级状态
        $iHighStatus = null;

        foreach ($aStatusList as $iStatus) {
            if (isset($aPriority[$iStatus]) && $aPriority[$iStatus] > $iHighPriority) {
                $iHighPriority = $aPriority[$iStatus];
                $iHighStatus = $iStatus;
            }
        }

        return $iHighStatus;
    }

    /**
     * @return bool
     * @throws Exception
     */
    private function main_bind_product() {
        $aDataList = $this->bind_product_data();

        $aProductList = Product::getAllProduct();
        $aProductMap = array_column($aProductList, 'value', 'label');

        foreach ($aDataList as $aData) {
            $iApplyId = $aData[0];
            $iApplyFatherId = $aData[1];
            $aProductName = explode(',', $aData[2]);

            $aProductId = [];
            foreach ($aProductName as $sName) {
                $iProductId = $aProductMap[$sName] ?? 0;

                $iProductId and $aProductId[] = [$iProductId, $iProductId];
            }

            $this->bindProduct($iApplyId, $iApplyFatherId, $aProductId);
        }

        return true;
    }

    /**
     * @return array
     */
    private function bind_product_data(){
        return [
            [462,364,"事件分F01"],
            [685,806,"HMRO_C10001,HMRO_C03002,HMRO_C03004,HMRO_C03006,HMRO_C03008"],
        ];
    }

    /**
     * @param $iApplyId
     * @param $iApplyFatherId
     * @param $aProductList
     * @return bool
     * @throws Exception
     */
    private function bindProduct($iApplyId = 0, $iApplyFatherId = 0, $aProductList = []) {
        if (!$iApplyId || empty($aProductList)) {
            return false;
        }

        // 申请信息
        $oApply = ApplyCustomer::find($iApplyId);
        if (is_null($oApply)) {
            return false;
        }
        $oApplyFather = ApplyFather::find($iApplyFatherId);
        if (empty($oApplyFather)) {
            return false;
        }
        $aObjectList = [];
        if ($oApplyFather->have_objective) {
            $aObjectList = ApplyFatherObjective::aGetDataByApplyFatherId($iApplyFatherId);
        }
        // 已测试产品
        $aApplyProductList = ApplyProduct::aGetListByApplyIdList([$iApplyId]);
        $aExistProductId = array_column($aApplyProductList, 'product_id');

        $aProductId = [];
        foreach ($aProductList as $aItem) {
            list($iFatherId, $iProductId) = $aItem;
            $aProductId[] = $iProductId;
        }
        // 需要新增测试产品
        $aNewProductId = array_diff($aProductId, $aExistProductId);
        if (empty($aNewProductId)) {
            return true;
        }
        $sTime = date("Y-m-d H:i:s");
        // 新增产品信息
        $aProductData = Product::getProductListByProductIds($aNewProductId, ['product_id', 'product_name', 'father_id']);
        // 主子产品关系
        $aProductTree = [];
        $aFatherId = [];
        foreach ($aProductData as $aData) {
            $iFatherId = $aData['father_id'] ?: $aData['product_id'];
            $aFatherId[] = $iFatherId;
            $aProductTree[$iFatherId][] = $aData['product_id'];
        }
        // 主产品信息
        $aFatherData = Product::getProductListByProductIds($aFatherId, ['product_id', 'product_name', 'father_id']);
        $aFatherNameMap = array_column($aFatherData, 'product_name', 'product_id');

        $aProductInsertList = [];
        $aObjectInsertList = [];
        foreach ($aProductTree as $iFatherId => $aProduct) {
            $aInsertFatherData = [
                'apply_id' => $iApplyId,
                'father_id' => $iFatherId,
                'apply_father_name' => $aFatherNameMap[$iFatherId] ?? '',
                'test_status' => PreTestManage::STATUS_ACCESS_DONE,
                'test_result' => $oApplyFather->test_result,
                'test_result_remark' => $oApplyFather->test_result_remark,
                'access_action' => PreTestManage::ACCESS_ACTION_ABLE,
                'not_access_reason' => PreTestManage::UNABLE_REASON_DEFAULT,
                'access_remark' => $oApplyFather->access_remark,
                'schedule_time' => $sTime,
                'have_objective' => $oApplyFather->have_objective,
                'is_schedule' => PreTestManage::IS_BIND,
                'is_call' => PreTestManage::IS_NO,
                'date_total_90d' => 0,
                'number_total_90d' => 0,
                'start_time' => $oApplyFather->start_time,
                'return_time' => $oApplyFather->return_time,
                'bind_time' => $oApply->bind_time,
                'apply_time' => $oApply->apply_time,
                'created_at' => $sTime,
            ];
            $iTheApplyFatherId = ApplyFather::insertGetId($aInsertFatherData);

            foreach ($aObjectList as $aObject) {
                $aObjectInsertList[] = [
                    'apply_id' => $iApplyId,
                    'apply_father_id' => $iTheApplyFatherId,
                    'father_id' => $iFatherId,
                    'objective_id' => $aObject['objective_id'],
                    'result_value' => $aObject['result_value'],
                    'remark' => $aObject['remark'],
                    'created_at' => $sTime,
                ];
            }

            foreach ($aProduct as $iProductId) {
                $aProductInsertList[] = [
                    'apply_id' => $iApplyId,
                    'apply_father_id' => $iTheApplyFatherId,
                    'product_id' => $iProductId,
                    'father_id' => $iFatherId,
                    'apply_father_name' => 'PC端新增',
                    'apply_product_name' => '',
                    'test_status' => PreTestManage::STATUS_ACCESS_DONE,
                    'test_result' => PreTestManage::TEST_RESULT_DEFAULT,
                    'test_result_remark' => '',
                    'access_action' => PreTestManage::ACCESS_ACTION_ABLE,
                    'not_access_reason' => PreTestManage::UNABLE_REASON_DEFAULT,
                    'access_remark' => '',
                    'is_schedule' => PreTestManage::IS_BIND,
                    'is_call' => PreTestManage::IS_NO,
                    'date_total_90d' => 0,
                    'number_total_90d' => 0,
                    'start_time' => $oApplyFather->start_time,
                    'return_time' => $oApplyFather->return_time,
                    'bind_time' => $oApply->bind_time,
                    'apply_time' => $oApply->apply_time,
                    'created_at' => $sTime,
                ];
            }
        }
        if ($aProductInsertList) {
            ApplyProduct::insert($aProductInsertList);
        }
        if ($aObjectInsertList) {
            ApplyFatherObjective::insert($aObjectInsertList);
        }

        $oApplyFather->is_delete = PreTestManage::IS_YES;
        $oApplyFather->save();

        return true;
    }

    /**
     * @return true
     */
    public function main_bind_customer() {
        $aDataList = $this->bind_customer_data();

        $aMap = [];
        foreach ($aDataList as $aItem) {
            list($iApplyId, $sCustomerId) = $aItem;
            $aMap[$iApplyId] = $sCustomerId;
        }

        foreach ($aMap as $iApplyId => $sCustomerId) {
            $this->bindCustomer($iApplyId, $sCustomerId);
        }

        return true;
    }

    /**
     * @param $iApplyId
     * @param $sCustomerId
     * @return bool
     */
    private function bindCustomer($iApplyId, $sCustomerId) {
        $sNow = date("Y-m-d H:i:s");
        $oApply = ApplyCustomer::find($iApplyId);
        if (is_null($oApply)) {
            return false;
        }
        $oApply->customer_id = $sCustomerId;
        $oApply->updated_at = date("Y-m-d H:i:s");
        if (!$sCustomerId) {
            $aApplyFatherList = ApplyFather::aGetListByApplyIdList([$iApplyId]);
            $aStatusList = [];
            // 回退产品维度进度
            $aUpdate = [];
            foreach ($aApplyFatherList as $aItem) {
                $iStatus = PreTestManage::STATUS_WAITING;
                if ($aItem['access_action']) {
                    $iStatus = $aItem['access_action'] == PreTestManage::ACCESS_ACTION_ABLE ? PreTestManage::STATUS_ACCESS_ABLE : PreTestManage::STATUS_ACCESS_UNABLE;
                } else if ($aItem['test_result']) {
                    $iStatus = PreTestManage::STATUS_FEEDBACK;
                } else if ($aItem['return_time']) {
                    $iStatus = PreTestManage::STATUS_TEST_DONE;
                } else if ($aItem['start_time']) {
                    $iStatus = PreTestManage::STATUS_TESTING;
                }
                $aStatusList[] = $iStatus;
                $aUpdate[] = [
                    'id' => $aItem['id'],
                    'test_status' => $iStatus,
                ];
            }
            if ($aUpdate) {
                ApplyFather::batchUpdateData($aUpdate, 'id');
            }
            // 重新计算申请维度
            $oApply->status = $this->calculateStatusPriority($aStatusList);
            $oApply->save();

            return true;
        }

        $oApply->is_call = PreTestManage::IS_NO;
        $oApply->status = PreTestManage::STATUS_ACCESS_DONE;
        $oApply->bind_time = $sNow;
        $oApply->save();

        $aUpdateData = [
            'bind_time' => $sNow,
            'updated_at' => $sNow,
        ];
        ApplyFather::iUpdateByApplyId($iApplyId, $aUpdateData);

        return true;
    }


    /**
     * @return array[]
     */
    private function bind_customer_data() {
        return [
            [1124,"C20220424XD2DOT"],
            [1121,"C20220424XD2DOT"],
            [1121,"C20220424XD2DOT"],
        ];
    }

    /**
     * @return true
     */
    private function main_v3() {
        $iId = 0;
        $iOffset = 0;
        $iPerPage = 100;

        do {
            $aCond = [
                ['id', '>', $iId],
                ['father_id', '>', 0],
            ];
            $aDataList = ApplyFather::aGetListByCond($aCond, $iOffset, $iPerPage, 'ASC', 'id');
            if (empty($aDataList)) {
                break;
            }

            $aApplyFatherId = array_column($aDataList, 'id');
            $aCond = [
                ['apply_father_id', 'in', $aApplyFatherId]
            ];
            $aApplyProductList = ApplyProduct::aGetDataByCond($aCond, ['apply_father_id', 'product_id'], ['deleted_at']);
            $aGatherList = [];
            foreach ($aApplyProductList as $aItem) {
                $aGatherList[$aItem['apply_father_id']][] = $aItem['product_id'];
            }
            $aUpdate = [];
            foreach ($aGatherList as $id => $aProductId) {
                $aUpdate[] = [
                    'id' => $id,
                    'product_list' => implode(',', $aProductId),
                ];
            }
            if ($aUpdate) {
                ApplyFather::batchUpdateData($aUpdate, 'id');
            }

            $iId = max($aApplyFatherId);
        } while (true);

        return true;
    }

    /**
     * @return true
     */
    public function main_v4() {
        $aCond = [
            ['approval_code', 'in', [PreTestManage::APPROVAL_CODE_TEST_WITH_ACCOUNT, PreTestManage::APPROVAL_CODE_TEST_WITH_MODEL]],
        ];
        $aDataList = ApplyList::aGetDataListByCond($aCond);

        $this->oBusinessProduct = new BusinessProductData();

        foreach ($aDataList as $oApply) {
            // 获取申请信息详情
            $oInstance = $this->getInstanceInfo($oApply->instance_code);
            $sInstanceStatus = $oInstance->getStatus();
            // [异常数据] 不处理
            if (is_null($sInstanceStatus)) {
                continue;
            }
            $oApply->instance_status = $sInstanceStatus;
            $oApply->save();
        }

        return true;
    }

    /**
     * @param $sInstanceCode
     * @return Approval|CallStable|ExpectOnline|Grayscale|PreTest
     */
    private function getInstanceInfo($sInstanceCode = '') {
        $aResult = [];
        try {
            $aResult = $this->oFeishuPreTestRep->get_approval_info($sInstanceCode);
            if (empty($aResult)) {
                Log::warning(self::CRON_NAME . ' 申请工单信息为空', ['instance_code' => $sInstanceCode]);
            }
        } catch (Exception $e) {
            Log::error(self::CRON_NAME . '获取申请工单信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $sInstanceCode]);
        }

        return ApprovalTransfer::makeApproval($aResult);
    }

    /**
     * @return true
     */
    private function main_v5() {
        $aApplyId = [
            571,
            669,
            773,
            787,
            858,
            901,
            1141,
        ];
        $aApplyCustomerList = ApplyCustomer::aGetListByApplyIdList($aApplyId);
        foreach ($aApplyCustomerList as $aApplyCustomer) {
            $sInstanceCode = $aApplyCustomer['instance_code'];
            $this->getInstanceInfo($sInstanceCode);
        }

        return true;
    }

    /**
     * @return bool
     */
    private function main_v6() {
        $sNow = date('Y-m-d H:i:s');
        $aId = [
            19,35,36,37,38,39,40,41,42
        ];
        $aCond = [
            ['id', 'in', $aId],
        ];
        $aBusinessProductList = BusinessProduct::aGetDataByCond($aCond);
        $aBusinessProductMap = array_column($aBusinessProductList, null, 'business_product_name');

        $aApplyId = [
            375,
            437,
            502,
            571,
            669,
            692,
            701,
            773,
            781,
            787,
            858,
            867,
            872,
            873,
            874,
            875,
            901,
            918,
            930,
            931,
            960,
            971,
            1007,
            1141,
            1163,
        ];
        $aApplyCustomerList = ApplyCustomer::aGetListByApplyIdList($aApplyId);

        foreach ($aApplyCustomerList as $aApplyCustomer) {
            $iApplyId = $aApplyCustomer['id'];
            $aProductInfo = json_decode($aApplyCustomer['product_info'], true);
            $aFatherList = json_decode($aProductInfo['father_list'], true);
            if (empty($aFatherList)) {
                continue;
            }
            $iSampleSize = $aProductInfo['sample_size_10000'] ?? 0;
            foreach ($aFatherList as $sFather) {
                $sBusinessProductName = $sFather;
                if ($sFather == '易诉') {
                    $sBusinessProductName =  '自有运营评分';
                }

                $aBusinessProduct = $aBusinessProductMap[$sBusinessProductName];

                $aProductId = [];
                $aInsertFatherData = [
                    'apply_id' => $iApplyId,
                    'father_id' => $aBusinessProduct['product_id'],
                    'product_key' => $aBusinessProduct['product_key'],
                    'apply_father_name' => $sFather,
                    'product_list' => implode(',', $aProductId),
                    'purpose' => $aApplyCustomer['purpose'],
                    'test_status' => PreTestManage::STATUS_WAITING,
                    'test_result' => PreTestManage::TEST_RESULT_DEFAULT,
                    'test_result_remark' => '',
                    'access_action' => PreTestManage::ACCESS_ACTION_DEFAULT,
                    'not_access_reason' => PreTestManage::UNABLE_REASON_DEFAULT,
                    'access_remark' => '',
                    'is_schedule' => PreTestManage::IS_MINUS_ONE,
                    'is_call' => PreTestManage::IS_MINUS_ONE,
                    'apply_time' => $aApplyCustomer['apply_time'],
                    'salesman' => $aApplyCustomer['salesman'],
                    'sample_size' => $iSampleSize,
                    'created_at' => $sNow,
                ];
                ApplyFather::insert($aInsertFatherData);
            }
        }

        return true;
    }

    private function main_v7() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }
            $aDataList = $oDataList->toArray();
            $aCustomerId = array_values(array_unique(array_column($aDataList, 'customer_id')));
            $aCustomerMap = $this->getCustomerMap($aCustomerId);

            // 写入审批关联表
            $aId = [];
            $aUpdateList = [];
            foreach ($oDataList as $oData) {
                $aId[] = $oData->id;
                $aCustomer = $aCustomerMap[$oData->customer_id] ?? [];
                if (empty($aCustomer)) {
                    continue;
                }
                $sSalesman = $aCustomer['salesman'] ?? '';

                $aUpdateList[] = [
                    'id' => $oData->id,
                    'salesman' => $sSalesman,
                ];
            }

            if ($aUpdateList) {
                MonitorCustomerProduct::batchUpdateData($aUpdateList);
            }

            $iId = max($aId);
        } while (true);

        return true;
    }

    private function main_v8() {
        // update customer set level_scale = '' , level_income = 0, level_scale_income = '' ;
        $aDataList = $this->main_v8_data();

        $aUpdateList = [];
        foreach ($aDataList as $aData) {
            list($sCustomerName, $iLevel1, $iLevel2, $iLevel3) = $aData;
            $aUpdateList[] = [
                'name' => $sCustomerName,
                'level_scale' => $iLevel1,
                'level_income' => $iLevel2,
                'level_scale_income' => $iLevel3,
            ];
        }
        Customer::batchUpdateData($aUpdateList, 'name');

        return true;
    }

    private function main_v8_data() {
        return [
            ["武汉农商行","B","8","B8"],
            ["浙商银行","S","4","S4"],
            ["锡商银行","B","8","B8"],
            ["苏商银行","A","8","A8"],
            ["华瑞银行","A","8","A8"],
            ["济宁银行","A","8","A8"],
            ["汉口银行","B","8","B8"],
            ["新网银行","A","8","A8"],
            ["北京银行卡中心","S","8","S8"],
            ["湖南三湘银行","A","8","A8"],
            ["民泰银行","B","7","B7"],
            ["华夏银行","S","8","S8"],
            ["亿联银行","A","7","A7"],
            ["邮储银行","S","8","S8"],
            ["徽商银行","A","6","A6"],
            ["浦发银行-品牌号","S","7","S7"],
            ["北京石景山区人民法院特邀汉法通云调解中心-品牌号","C","8","C8"],
            ["北京银行杭分","C","8","C8"],
            ["光大信用卡","S","8","S8"],
            ["昆仑银行","C","8","C8"],
            ["晋商银行","C","7","C7"],
            ["重庆银行","A","8","A8"],
            ["佛山农商行","B","8","B8"],
            ["汇融银行","C","8","C8"],
            ["富滇银行","C","8","C8"],
            ["厦门银行","B","7","B7"],
            ["招商银行","S","8","S8"],
            ["工商银行","S","6","S6"],
            ["三峡银行","C","8","C8"],
            ["中国银行深圳分行","A","7","A7"],
            ["金城银行","A","8","A8"],
            ["乌鲁木齐银行","C","8","C8"],
            ["哈尔滨银行","B","8","B8"],
        ];
//        return [
//            ["宝德融资租赁","","8","8"],
//            ["哈啰-存量洞察","A","1","A1"],
//            ["北京农商行","A","8","A8"],
//            ["成都豆秒-品牌号","C","8","C8"],
//            ["中信卡中心-鸿联九五","C","7","C7"],
//            ["神州优车-品牌号","C","8","C8"],
//            ["锦锐科技-品牌号","C","8","C8"],
//            ["邮政速递-品牌号","C","8","C8"],
//            ["分转科技","C","8","C8"],
//            ["晋商消金","C","8","C8"],
//            ["山西信托","C","7","C7"],
//            ["瀚华小贷","","8","8"],
//            ["苏银消金","B","8","B8"],
//            ["尚诚消金","B","8","B8"],
//            ["美团金融","S","8","S8"],
//            ["极融云科","S","3","S3"],
//            ["携程金融","A","8","A8"],
//            ["信飞","A","2","A2"],
//            ["腾桥信息","A","6","A6"],
//            ["马上消费","S","8","S8"],
//            ["三农信","S","1","S1"],
//            ["借钱呗","C","8","C8"],
//            ["星网科技","C","8","C8"],
//            ["分期乐","A","8","A8"],
//            ["民盛租赁","C","8","C8"],
//            ["TD","C","8","C8"],
//            ["大数信科","C","8","C8"],
//            ["永旺小贷","C","8","C8"],
//            ["中信消金","C","7","C7"],
//            ["桥水融担","C","8","C8"],
//            ["萨摩耶","B","4","B4"],
//            ["乾贯融担","","8","8"],
//            ["承德银行","C","8","C8"],
//            ["润乾融担","C","8","C8"],
//            ["微店","C","8","C8"],
//            ["千方小贷","C","8","C8"],
//            ["全民钱包","B","8","B8"],
//            ["成兴融担-众利","","8","8"],
//            ["帮客帮","C","6","C6"],
//            ["福建元寅-刷宝","","8","8"],
//            ["招联消金","A","6","A6"],
//            ["盛银消费","C","8","C8"],
//            ["百信银行","A","8","A8"],
//            ["友微科技","A","2","A2"],
//            ["宜信科技","S","2","S2"],
//            ["明东华融担","S","6","S6"],
//            ["海尔消费","A","4","A4"],
//            ["新桥公瑾","","8","8"],
//            ["融七牛","C","4","C4"],
//            ["国美金融","B","2","B2"],
//            ["广达小贷","C","8","C8"],
//            ["哈银消费","B","7","B7"],
//            ["翼支付","","8","8"],
//            ["兴业消金","A","8","A8"],
//            ["融联世纪","C","8","C8"],
//            ["天创信用","C","4","C4"],
//            ["百融","C","6","C6"],
//            ["我来数科","","8","8"],
//            ["汇景租赁","","8","8"],
//            ["新焦点","C","8","C8"],
//            ["音悦邦","C","8","C8"],
//            ["易通融金-代运营","C","8","C8"],
//            ["华道征信","C","8","C8"],
//            ["世渺金融","C","8","C8"],
//            ["邮惠万家","C","8","C8"],
//            ["盛京银行","","8","8"],
//            ["锦安数科-朴道临时账号","C","8","C8"],
//            ["小象优品","C","5","C5"],
//            ["廊坊银行","B","5","B5"],
//            ["长银消金","B","5","B5"],
//            ["阳光消金","C","7","C7"],
//            ["益通祥融担","B","8","B8"],
//            ["小米消金","C","6","C6"],
//            ["平安普惠","S","8","S8"],
//            ["蚂蚁消金","S","1","S1"],
//            ["江苏银行-品牌号","A","8","A8"],
//            ["蚂蚁区块链","","8","8"],
//            ["芝麻信用","","8","8"],
//            ["58金融","C","6","C6"],
//            ["友好物科技","","8","8"],
//            ["飞泉金服","C","8","C8"],
//            ["崇天小贷","C","8","C8"],
//            ["乐享借","C","7","C7"],
//            ["拍拍贷-代运营","S","6","S6"],
//            ["百维-朴道临时账号","C","4","C4"],
//            ["360借条","S","3","S3"],
//            ["维仕融担","A","4","A4"],
//            ["中原消金","B","3","B3"],
//            ["数禾","B","6","B6"],
//            ["通融小贷","C","8","C8"],
//            ["金诺小贷-四川众诚","C","8","C8"],
//            ["小赢科技","A","1","A1"],
//            ["前海比邻金融","C","8","C8"],
//            ["字节跳动","","8","8"],
//            ["啄木鸟","C","8","C8"],
//            ["随手记-代运营","","8","8"],
//            ["广汽租赁","C","7","C7"],
//            ["河北银行","A","8","A8"],
//            ["蓝海银行-神盾","B","7","B7"],
//            ["阿里巴巴","C","8","C8"],
//            ["未讯科技","C","4","C4"],
//            ["泰隆银行","B","8","B8"],
//            ["捷信消费","C","8","C8"],
//            ["度小满金融","S","6","S6"],
//            ["货车帮","C","6","C6"],
//            ["金盈子","A","8","A8"],
//            ["金美信","C","5","C5"],
//            ["猪八戒小贷","C","8","C8"],
//            ["富民银行","A","6","A6"],
//            ["重庆旺店科技","C","8","C8"],
//            ["成都优卡","","8","8"],
//            ["光大永明人寿","C","7","C7"],
//            ["众安保险","C","8","C8"],
//            ["数钥-代运营","C","8","C8"],
//            ["中银消费","A","8","A8"],
//            ["唯品富邦消金","B","8","B8"],
//            ["宁波通商行","C","6","C6"],
//            ["蚂蚁上数","S","3","S3"],
//            ["安与信","C","6","C6"],
//            ["信合元","","5","5"],
//            ["天翼保理","C","6","C6"],
//            ["长银五八消金","B","7","B7"],
//            ["嘀嗒趣课","C","8","C8"],
//            ["八戒租","C","8","C8"],
//            ["小薇小额","C","8","C8"],
//            ["宁波银行","S","4","S4"],
//            ["宁银消金","A","4","A4"],
//            ["天津成兴融担","C","7","C7"],
//            ["成都风明","C","7","C7"],
//            ["读秒","C","8","C8"],
//            ["成都乐超人","C","8","C8"],
//            ["联洋国融","C","8","C8"],
//            ["合纽信息","C","8","C8"],
//            ["安徽创瑞","C","8","C8"],
//            ["极推科技","C","5","C5"],
//            ["联众金融","C","8","C8"],
//            ["微众银行","S","1","S1"],
//            ["蒙商消金-代运营","C","8","C8"],
//            ["湖北消金","C","8","C8"],
//            ["众邦银行","A","6","A6"],
//            ["锦程消费","C","8","C8"],
//            ["中邮消金","A","5","A5"],
//            ["有能","C","8","C8"],
//            ["小花钱包","B","4","B4"],
//            ["大诚融担","","8","8"],
//            ["中黔联融担","C","8","C8"],
//            ["百特","C","8","C8"],
//            ["兴邦金租","C","8","C8"],
//            ["平安消金","A","4","A4"],
//            ["杭州银行","A","3","A3"],
//            ["爱租机","C","8","C8"],
//            ["vivo","C","8","C8"],
//            ["华通泰融担","C","8","C8"],
//            ["洲联科技","C","8","C8"],
//            ["九四智能","C","8","C8"],
//            ["源旅科技","C","8","C8"],
//            ["湖南永诚","C","8","C8"],
//            ["大搜车","B","7","B7"],
//            ["花生好车","C","8","C8"],
//            ["中互数科","C","8","C8"],
//            ["民和小贷","","8","8"],
//            ["转转","C","7","C7"],
//            ["富金富","C","8","C8"],
//            ["洽客科技","C","8","C8"],
//            ["至诚融担","C","8","C8"],
//            ["荔商融担","","8","8"],
//            ["即科金融","C","8","C8"],
//            ["快乐通宝小贷","C","8","C8"],
//            ["杭银消金","A","4","A4"],
//            ["网商小贷","C","7","C7"],
//            ["银融致信科技-神盾","C","8","C8"],
//            ["青海阿盼达","","8","8"],
//            ["智度小贷","C","7","C7"],
//            ["张家口银行","C","8","C8"],
//            ["随行付","C","8","C8"],
//            ["合创信通","C","8","C8"],
//            ["小牛租机","C","7","C7"],
//            ["秒众","C","7","C7"],
//            ["幸福消金","C","8","C8"],
//            ["海南凤凰木","","8","8"],
//            ["财付通","A","8","A8"],
//            ["小萌金融","C","8","C8"],
//            ["俏租机","C","7","C7"],
//            ["众汇创业融资","C","8","C8"],
//            ["厦门大数据","","8","8"],
//            ["科讯金服","C","7","C7"],
//            ["沃金融租","C","8","C8"],
//            ["孚临科技","C","7","C7"],
//            ["蔚来汽车","C","8","C8"],
//            ["正东华","C","8","C8"],
//            ["世纪银通","C","7","C7"],
//            ["华章汉辰","C","6","C6"],
//            ["赞塔","","8","8"],
//            ["华强小贷","C","8","C8"],
//            ["安迪泰","C","8","C8"],
//            ["阳光财险","C","8","C8"],
//            ["美的","C","8","C8"],
//            ["海创云链","C","8","C8"],
//            ["蜂泰科技","C","7","C7"],
//            ["云麓科技","C","8","C8"],
//            ["浩瀚汇通小贷","C","8","C8"],
//            ["京东数科","S","1","S1"],
//            ["青年优品","C","8","C8"],
//            ["无疆","C","8","C8"],
//            ["海发宝诚-品牌号","C","8","C8"],
//            ["处成科技","","8","8"],
//            ["甜橙融担","C","6","C6"],
//            ["亚联财","C","8","C8"],
//            ["鼎山","C","8","C8"],
//            ["卓京科技","C","8","C8"],
//            ["连连小贷","C","8","C8"],
//            ["国政通","C","8","C8"],
//            ["携程-全网标记","C","8","C8"],
//            ["煜富冯","C","8","C8"],
//            ["微神马","C","8","C8"],
//            ["河南农担","C","8","C8"],
//            ["中和农信","C","8","C8"],
//            ["宇轩云","C","8","C8"],
//            ["河北金租","C","8","C8"],
//            ["美家时贷","C","8","C8"],
//            ["大衍科技","C","8","C8"],
//            ["朴道","C","8","C8"],
//            ["广东和翼","C","8","C8"],
//            ["排列科技","C","8","C8"],
//            ["中裔融担","C","8","C8"],
//            ["冰鉴信息","C","8","C8"],
//            ["QUARK","C","8","C8"],
//            ["对元计算","C","8","C8"],
//            ["小向创新","C","8","C8"],
//            ["国誉","C","8","C8"],
//            ["恒智普惠","C","6","C6"],
//            ["中安数盾","C","7","C7"],
//            ["Happy pay","C","8","C8"],
//            ["易拨通-神盾","","8","8"],
//            ["信用飞-代运营","","7","7"],
//            ["贝壳金科","C","8","C8"],
//            ["分蛋","C","8","C8"],
//            ["无界领智","C","8","C8"],
//            ["仲利国际","C","7","C7"],
//            ["恒美租机","C","8","C8"],
//        ];
    }

    /**
     * @param $aCustomerId
     * @return array
     */
    private function getCustomerMap($aCustomerId = []) {
        $aCustomerList = Customer::getCustomerByIdList($aCustomerId);

        return array_column($aCustomerList, null, 'customer_id');
    }

    /**
     * @return true
     */
    private function main_v9() {
        $aCond = [
            ['father_id', '=', 0]
        ];
        $aDataList = MonitorApproval::aGetDataListByCond($aCond);

        $aMonitorId = array_column($aDataList, 'monitor_id');
        $aCond = [
            ['id', 'in', $aMonitorId]
        ];
        $aMonitorList = MonitorCustomerProduct::aGetDataListByCond($aCond);
        $aMonitorMap = array_column($aMonitorList, null, 'id');

        $aUpdateList = [];
        foreach ($aDataList as $aData) {
            $aUpdateList[] = [
                'id' => $aData['id'],
                'father_id' => $aMonitorMap[$aData['monitor_id']]['father_id'],
                'product_id' => $aMonitorMap[$aData['monitor_id']]['product_id'],
            ];
        }
        if ($aUpdateList) {
            MonitorApproval::batchUpdateData($aUpdateList);
        }

        return true;
    }

    /**
     * @return true
     */
    private function main_v10() {
        $iId = 0;
        $iNow = time();
        do {
            $aCond = [
                ['id', '>', $iId],
                ['grayscale_start_time', '>', 0],
                ['grayscale_end_time', '>', 0],
                ['docking_status', 'notIn',  [MonitorCustomerProduct::DOCKING_STATUS_NORMAL, MonitorCustomerProduct::DOCKING_STATUS_NOT_FOLLOW]],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }

            $aCustomerId = array_column($oDataList->toArray(), 'customer_id');
            $aCustomerApikeyMap = $this->aGetApiKeyList($aCustomerId);

            $aId = [];
            $aUpdateList = [];
            foreach ($oDataList as $oData) {
                $aId[] = $oData->id;

                $aApikey = $aCustomerApikeyMap[$oData->customer_id];
                $sStartDate = date('Ymd', $oData->grayscale_start_time);
                $sEndDate = date('Ymd', min($oData->grayscale_end_time, $iNow));
                $iGrayCallNumber = $this->iGetDataByTable($aApikey, $oData->product_id, $sStartDate, $sEndDate);

                if ($iGrayCallNumber != $oData->gray_call_number) {
                    $aUpdateList[] = [
                        'id' => $oData->id,
                        'gray_call_number' => $iGrayCallNumber,
                    ];
                }

            }
            if ($aUpdateList) {
                MonitorCustomerProduct::batchUpdateData($aUpdateList, 'id');
            }
            $iId = max($aId);
        } while (true);

        return true;
    }

    /**
     * @param $aCustomerId
     * @return array
     */
    private function aGetApiKeyList($aCustomerId) {
        $aList = Account::getListByCustomerIds($aCustomerId);

        $aMap = [];
        foreach ($aList as $aItem) {
            $aMap[$aItem['customer_id']][] = $aItem['apikey'];
        }

        return $aMap;
    }

    /**
     * @param $aApikey
     * @param $iProductId
     * @param $sStartDate
     * @param $sEndDate
     * @return int
     */
    private function iGetDataByTable($aApikey = [], $iProductId = 0, $sStartDate = '', $sEndDate = '') {
        if (empty($aApikey) || empty($iProductId) || (empty($sStartDate) && empty($sEndDate))) {
            return 0;
        }
        $aDataList = StatisticsCustomerUsage::aGetProductEachDayDataByApiKey($aApikey, $iProductId, $sStartDate, $sEndDate);
        if (empty($aDataList)) {
            return 0;
        }
        $aNumber = array_column($aDataList, 'success');

        return array_sum($aNumber);
    }
}