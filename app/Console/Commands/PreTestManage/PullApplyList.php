<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\AccountApplyManage;
use App\Define\PreTestManage;
use App\Models\Crs\SystemUser;
use App\Models\PreTestManage\Approval\ApprovalTransfer;
use App\Models\PreTestManage\ApplyList;
use App\Models\PreTestManage\Approval\Approval;
use App\Models\PreTestManage\Approval\CallStable;
use App\Models\PreTestManage\Approval\ExpectOnline;
use App\Models\PreTestManage\Approval\Grayscale;
use App\Models\PreTestManage\Approval\PreTest;
use App\Models\PreTestManage\BusinessProductData;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 拉取飞书售前测试申请
 * php artisan pre_test_manage:pull_apply_list --start_time=********** --end_time=**********
 * @uses PullApplyList
 */
class PullApplyList extends Command
{
    protected $signature = 'pre_test_manage:pull_apply_list
    {--start_time= : 开始时间}
    {--end_time= : 结束时间}';

    const CRON_NAME = '[拉取飞书售前测试申请]';

    /**
     * 申请时间-起止范围
     * @var int
     */
    private $iStartTime = 0;
    private $iEndTime = 0;

    private $aApprovalCode = [];

    private $aUserMap = [];

    /**
     * @var FeishuRepository
     */
    private $oFeishuPreTestRep = null;

    /**
     * @var BusinessProductData
     */
    protected $oBusinessProduct = null;

    private $sTime = null;

    private $aUnableInstanceStatus = [
        PreTestManage::INSTANCE_STATUS_RECALL,
        PreTestManage::INSTANCE_STATUS_CANCELED,
        PreTestManage::INSTANCE_STATUS_REJECT,
        PreTestManage::INSTANCE_STATUS_REJECTED,
        PreTestManage::INSTANCE_STATUS_DELETED,
    ];

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s, 开始时间:%s, 结束时间:%s",
                $sNow, $cost, date('Y-m-d H:i:s', $this->iStartTime), date('Y-m-d H:i:s', $this->iEndTime));
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->iEndTime = (int)($this->input->getOption('end_time')) ?: time();
        $this->iStartTime = (int)($this->input->getOption('start_time')) ?: strtotime('-8 days', $this->iEndTime);
        $this->sTime = date('Y-m-d H:i:s');
        $this->aApprovalCode = [
//            PreTestManage::APPROVAL_CODE_TEST,
            PreTestManage::APPROVAL_CODE_TEST_WITH_ACCOUNT,
            PreTestManage::APPROVAL_CODE_TEST_WITH_MODEL,
            AccountApplyManage::APPROVAL_CODE_TEST_WITH_APPLY_ACCOUNT,
        ];
        $this->oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
        $this->oBusinessProduct = new BusinessProductData();

        $this->aUserMap = SystemUser::getFsUserInfoMap();
    }

    /**
     * @return bool
     * @throws Exception
     */
    private function main() {
        // 拉取列表
        foreach ($this->aApprovalCode as $sApprovalCode) {
            $this->pullApprovalList($sApprovalCode);
        }

        // 逐个获取详情
        $this->getApprovalData();

        return true;
    }

    /**
     * @return true
     * @throws Exception
     */
    private function pullApprovalList($sApprovalCode) {
        $sPageToken = '';
        $iPageSize = 50;
        do {
            $aData = [
                'approval_code' => $sApprovalCode,
                'instance_status' => PreTestManage::INSTANCE_STATUS_ALL,
                'start_time' => $this->iStartTime,
                'end_time' => $this->iEndTime,
                'page_size' => $iPageSize,
                'page_token' => $sPageToken,
            ];
            $aResult = $this->oFeishuPreTestRep->get_approval_list($aData);
            $iCount = $aResult['count'] ?? 0;
            $bHasMore = $aResult['has_more'] ?? false;
            $sPageToken = $aResult['page_token'] ?? '';
            $aInstanceList = $aResult['instance_list'] ?? [];

            if (!$iCount || empty($aInstanceList)) {
                return true;
            }

            $this->saveApprovalList($aInstanceList);
        } while ($bHasMore);

        return true;
    }

    /**
     * @param $aInstanceList
     * @return true
     */
    private function saveApprovalList($aInstanceList) {
        $aDataList = [];
        foreach ($aInstanceList as $aInstance) {
            $aItem = $aInstance['instance'];
            $sApprovalCode = $aInstance['approval']['code'] ?? '';

            $aDataList[$aItem['code']] = [
                'code' => $aItem['code'],
                'approval_code' => $sApprovalCode,
                'status' => $aItem['status'],
                'user_id' => $aItem['user_id'],
                'apply_time' => date('Y-m-d H:i:s', substr($aItem['start_time'] ?? 0, 0, 10)),
            ];
        }

        // 数据查重
        $aInstanceCode = array_keys($aDataList);
        $aCond = [
            ['instance_code', 'in', $aInstanceCode]
        ];
        $aExistList = ApplyList::aGetListByCond($aCond, 0, count($aInstanceCode));
        $aExistInstanceCode = array_column($aExistList, 'instance_code');
        // 保存新增的申请
        $aInsertCode = array_diff($aInstanceCode, $aExistInstanceCode);
        if (empty($aInsertCode)) {
            return true;
        }
        $aInsertList = [];
        foreach ($aInsertCode as $sInsertCode) {
            $aData = $aDataList[$sInsertCode];

            $aInsertList[] = [
                'approval_code' => $aData['approval_code'],
                'instance_code' => $aData['code'],
                'instance_status' => strtoupper($aData['status']),
                'user_id' => $aData['user_id'],
                'apply_time' => $aData['apply_time'],
                'is_done' => 0,
                'created_at' => $this->sTime,
            ];
        }

        if ($aInsertList) {
            ApplyList::batchInsert($aInsertList);
        }

        return true;
    }

    /**
     * @return bool
     */
    private function getApprovalData() {
        $aCond = [
            ['is_done', '=', 0],
            ['approval_code', 'in', $this->aApprovalCode],
        ];
        $aDataList = ApplyList::aGetDataListByCond($aCond);

        foreach ($aDataList as $oApply) {
            // 废弃装填直接标记为已处理
            if (in_array($oApply->instance_status, $this->aUnableInstanceStatus)) {
                $oApply->is_done = 1;
                $oApply->save();
                continue;
            }
            // 获取申请信息详情
            $oInstance = $this->getInstanceInfo($oApply->instance_code);
            $sInstanceStatus = $oInstance->getStatus();
            // [异常数据] 不处理
            if (is_null($sInstanceStatus)) {
                continue;
            }
            // 废弃装填直接标记为已处理
            if (in_array($sInstanceStatus, $this->aUnableInstanceStatus)) {
                $oApply->is_done = 1;
                $oApply->instance_status = $sInstanceStatus;
                $oApply->save();
                continue;
            }
            // 审批通过 或者 审批中
            if (PreTestManage::INSTANCE_STATUS_APPROVED == $sInstanceStatus || PreTestManage::INSTANCE_STATUS_PENDING == $sInstanceStatus) {
                $aUserInfo = $this->getUserInfo($oInstance->getUserId());
                try {
                    $oInstance->transForm()->saveData($aUserInfo);
                    $oApply->is_done = 1;
                    $oApply->save();
                } catch (Exception $e) {
                    Log::error(self::CRON_NAME . '保存申请工单信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $oApply->instance_code]);
                }
            }
        }

        return true;
    }

    /**
     * @param $sInstanceCode
     * @return Approval|CallStable|ExpectOnline|Grayscale|PreTest
     */
    private function getInstanceInfo($sInstanceCode = '') {
        $aResult = [];
        try {
            $aResult = $this->oFeishuPreTestRep->get_approval_info($sInstanceCode);
            if (empty($aResult)) {
                Log::warning(self::CRON_NAME . ' 申请工单信息为空', ['instance_code' => $sInstanceCode]);
            }
        } catch (Exception $e) {
            Log::error(self::CRON_NAME . '获取申请工单信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $sInstanceCode]);
        }

        return ApprovalTransfer::makeApproval($aResult, $this->oBusinessProduct);
    }

    /**
     * @param $sUserId
     * @return array
     */
    private function getUserInfo($sUserId = '') {
        $aUserInfo = $this->aUserMap[$sUserId] ?? [];

        if (!empty($aUserInfo)) {
            return $aUserInfo;
        }

        try {
            $aResult = $this->oFeishuPreTestRep->get_user_info($sUserId);
            $aInfo = $aResult['user'] ?? [];
            $aSystemUser = SystemUser::getInfosByRealName($aInfo['name'] ?? '');
            if ($aSystemUser) {
                SystemUser::saveFsUserId($aSystemUser['id'], $sUserId);
            }
        } catch (Exception $e) {
            $aInfo = [
                'name' => PreTestManage::ACTION_ADMIN_UNKNOWN,
                'email' => '',
            ];
            Log::error(self::CRON_NAME . ' 获取用户信息失败 , msg: ' . $e->getMessage(), ['user_id' => $sUserId]);
        }

        $this->aUserMap[$sUserId] = [
            'user_id' => $sUserId,
            'name' => $aInfo['name'] ?? PreTestManage::ACTION_ADMIN_UNKNOWN,
            'email' => $aInfo['email'] ?? '',
        ];

        return $this->aUserMap[$sUserId] ;
    }

    /**
     * @param $sMsg
     * @return void
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_PRETERT_USER_ID;
        try {
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $this->oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }
}