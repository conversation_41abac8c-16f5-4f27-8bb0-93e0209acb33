<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\AccountApplyManage;
use App\Define\PreTestManage;
use App\Models\PreTestManage\ApplyManual;
use App\Models\PreTestManage\Approval\Approval;
use App\Models\PreTestManage\Approval\ApprovalTransfer;
use App\Models\PreTestManage\Approval\CallStable;
use App\Models\PreTestManage\Approval\ExpectOnline;
use App\Models\PreTestManage\Approval\Grayscale;
use App\Models\PreTestManage\Approval\PreTest;
use App\Models\PreTestManage\BusinessProductData;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 检查需人工介入工单状态
 * php artisan pre_test_manage:check_manual
 * @uses CheckManual
 */
class CheckManual extends Command
{
    protected $signature = 'pre_test_manage:check_manual;';

    const CRON_NAME = '[检查需人工介入工单状态]';

    /**
     * @var FeishuRepository
     */
    private $oFeishuPreTestRep = null;

    /**
     * @var array
     */
    private $approvalCodeList = [];

    /**
     * @var BusinessProductData
     */
    protected $oBusinessProduct = null;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam()
    {
        $this->oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');

        $this->approvalCodeList = [
            AccountApplyManage::APPROVAL_CODE_TEST_WITH_APPLY_ACCOUNT,
            PreTestManage::APPROVAL_CODE_TEST_WITH_ACCOUNT
        ];
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '')
    {
        $sUserId = PreTestManage::MANAGE_PRETERT_USER_ID;
        try {
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $this->oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }

    /**
     * @return true
     */
    private function main()
    {
        // 更新 审批定义模板code
        $this->checkManual();
        // 更新正式账号申请数据、售前测试&测试账号申请
        $this->checkAccountApplications();
        // 更新正式证号申请历史数据
        $this->checkAccountApplicationHistory();
        // 7天后  正式账号申请数据、售前测试&测试账号申请 没有更新monitor_customer_product表的数据
        $this->alarmWeekData();

        return true;
    }

    //
    private function checkManual()
    {
        $aCond = [
            ['is_done', '=', PreTestManage::IS_MINUS_ONE],
            ['approval_code', 'notIn', $this->approvalCodeList]
        ];
        $oDataList = ApplyManual::oGetDataListByCond($aCond);
        if (!$oDataList->count()) {
            return true;
        }

        foreach ($oDataList as $oApply) {
            // 获取申请信息详情
            $oInstance = $this->getInstanceInfo($oApply->instance_code);
            $sInstanceStatus = $oInstance->getStatus();
            // [异常数据] 不处理
            if (is_null($sInstanceStatus)) {
                continue;
            }
            if (PreTestManage::INSTANCE_STATUS_APPROVED == $sInstanceStatus) {
                $oInstance = $oInstance->transForm();
                $aBaseData = $oInstance->getData();
                $aExtendData = $oInstance->getExtendList();
                $oApply->customer_id = trim($aBaseData['customer_id'] ?? '');
                $oApply->product_list = trim($aExtendData['product_id_list'] ?? '');
                $oApply->is_done = PreTestManage::IS_NO;
            }
            if (in_array($sInstanceStatus, [PreTestManage::INSTANCE_STATUS_CANCELED, PreTestManage::INSTANCE_STATUS_REJECT])) {
                $oApply->is_done = PreTestManage::IS_YES;
            }
            $oApply->instance_status = $sInstanceStatus;
            $oApply->save();
        }

        return true;
    }

    private function checkAccountApplications()
    {
        $aCond = [
            ['is_done', '=', PreTestManage::IS_MINUS_ONE],
            ['approval_code', 'in', $this->approvalCodeList]
        ];
        $oDataList = ApplyManual::oGetDataListByCond($aCond);
        if (!$oDataList->count()) {
            return true;
        }

        foreach ($oDataList as $oApply) {
            // 获取申请信息详情
            $oInstance = $this->getInstanceInfo($oApply->instance_code);
            $sInstanceStatus = $oInstance->getStatus();
            // [异常数据] 不处理
            if (is_null($sInstanceStatus)) {
                continue;
            }
           if (PreTestManage::INSTANCE_STATUS_APPROVED == $sInstanceStatus) { // 审批通过
                $oInstance = $oInstance->transForm();
                $aBaseData = $oInstance->getData();
                $aExtendData = $oInstance->getExtendList();
                $aUndefined = $oInstance->getUndefined();
                $aProductInfo = $oInstance->getProductInfo();
                $aContent = array_merge($aBaseData, $aExtendData, $aUndefined, $aProductInfo);
                $productIdList = $aBaseData['product_id_list'] ?? $aExtendData['product_id_list'] ?? null;
                if ($productIdList === null) {
                    continue;
                }
                $product_id_list = str_replace(' ', '', $productIdList);
                $oApply->customer_id = trim($aBaseData['customer_id'] ?? '');
                $oApply->product_list = $product_id_list;
                $oApply->content = json_encode($aContent);
                $oApply->is_done = $oInstance->updateMonitorCustomerProduct($oApply);
            }
            if (in_array($sInstanceStatus, [PreTestManage::INSTANCE_STATUS_CANCELED, PreTestManage::INSTANCE_STATUS_REJECT, PreTestManage::INSTANCE_STATUS_REJECTED, PreTestManage::INSTANCE_STATUS_DELETED, PreTestManage::INSTANCE_STATUS_RECALL])) {
                $oApply->is_done = PreTestManage::IS_YES;
            }
            $oApply->instance_status = $sInstanceStatus;
            $oApply->save();
        }

        return true;
    }


    private function checkAccountApplicationHistory()
    {
        $aCond = [
            ['is_done', '=', PreTestManage::IS_NO_HAVE_CUSTOMER_PRODUCT],
            ['approval_code', 'in', $this->approvalCodeList],
            ['instance_status', '=', PreTestManage::INSTANCE_STATUS_APPROVED],
        ];
        // 查询申请通过但没有更新 monitor_customer_product 的数据
        $oDataList = ApplyManual::oGetDataListByCond($aCond);
        if (!$oDataList->count()) {
            return true;
        }
        foreach ($oDataList as $oApply) {
            $aData = [
               'approval_code' => $oApply->approval_code,
                'form'=>'{}',
                'status' => PreTestManage::INSTANCE_STATUS_APPROVED,
            ];
            $oInstance = ApprovalTransfer::makeApproval($aData);
            $oApply->is_done = $oInstance->updateMonitorCustomerProduct($oApply);
            if ($oApply->is_done == PreTestManage::IS_NO){
                $oApply->save();
            }
        }

        return true;
    }


    private function alarmWeekData()
    {
        $last_week = date('Y-m-d', strtotime('-7 days'));
        $aCond = [
            ['is_done', '=', PreTestManage::IS_NO_HAVE_CUSTOMER_PRODUCT],
            ['approval_code', 'in', $this->approvalCodeList],
            ['instance_status', '=', PreTestManage::INSTANCE_STATUS_APPROVED],
            ['updated_at', '<', $last_week],
        ];

        $aDataList = ApplyManual::aGetListByCond($aCond,['customer_id','product_list','updated_at']);
        if (empty($aDataList)) {
            return true;
        }

        $sDataJson = json_encode($aDataList);
        $this->sendFeishuUserMessage('超过7天未更新monitor_customer_product的数据:' . $sDataJson);

        return true;
    }

    /**
     * @param $sInstanceCode
     * @return Approval|CallStable|ExpectOnline|Grayscale|PreTest
     */
    private function getInstanceInfo($sInstanceCode = '')
    {
        $aResult = [];
        try {
            $aResult = $this->oFeishuPreTestRep->get_approval_info($sInstanceCode);
            if (empty($aResult)) {
                Log::warning(self::CRON_NAME . ' 申请工单信息为空', ['instance_code' => $sInstanceCode]);
            }
        } catch (Exception $e) {
            Log::error(self::CRON_NAME . '获取申请工单信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $sInstanceCode]);
        }

        return ApprovalTransfer::makeApproval($aResult, $this->oBusinessProduct);
    }
}