<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\PreTestManage;
use App\Models\Crs\SystemUser;
use App\Models\PreTestManage\ApplyFather;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 打卡信息处理
 * php artisan pre_test_manage:feedback_clock --action=0
 * @uses FeedbackClock
 */
class FeedbackClock extends Command
{
    protected $signature = 'pre_test_manage:feedback_clock;
    {--action= : 操作}';

    const CRON_NAME = '[打卡信息处理]';

    const ACTION_ALERT = 1; // 提示反馈打卡

    private $iAction = null;

    private $sApplyStartTime = '2024-10-01 00:00:00';

    /**
     * @var FeishuRepository
     */
    private $oFeishuPreTestRep = null;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s, 操作:%s", $sNow, $cost, $this->iAction);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage(PreTestManage::MANAGE_USER_ID, $sMsg);
        }
    }

    /**
     * @return void
     */
    private function setParam() {
        $this->iAction = (int)($this->input->getOption('action')) ?: 0;

        $this->oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
    }

    /**
     * @return true
     */
    private function main() {
        switch ($this->iAction) {
            case self::ACTION_ALERT:
                $this->doAlert();
                break;
            default:
                break;
        }

        return true;
    }

    /**
     * 每周五 12点 提示反馈打卡
     * @return true
     */
    private function doAlert() {
        $sMsg = '%s你好, %s前申请的售前测试, 有%s条产品没有更新反馈信息, 请及时反馈.';
        $sLimitDay = date('Y-m-d', strtotime('-14 day'));
        $aUserCountDataList = $this->getUndoneUserList();
        foreach ($aUserCountDataList as $sUserId => $aData) {
            if (PreTestManage::ACTION_ADMIN_UNKNOWN == $aData['name']) {
                continue;
            }

            $this->sendFeishuUserMessage($sUserId, sprintf($sMsg, $aData['name'], $sLimitDay, $aData['count']));
        }

        return true;
    }

    /**
     * @return array
     */
    private function getUndoneUserList() {
        $aCond = [
            ['apply_time', '>=', $this->sApplyStartTime],
            ['is_schedule', '=', PreTestManage::IS_NO],
            ['test_result', '=', PreTestManage::TEST_RESULT_DEFAULT],
            ['test_status', '=', PreTestManage::STATUS_TEST_DONE],
            ['schedule_time', '<=', date('Y-m-d H:i:s')],
        ];
        $aScheduleUndoneData = ApplyFather::aGetSalesmanGroupDataByCond($aCond);
        if (empty($aScheduleUndoneData)) {
            return [];
        }
        $aScheduleUndoneDataMap = array_column($aScheduleUndoneData, 'count', 'salesman');
        $aFsUserMap = SystemUser::getFsUserInfoMap();
        $aNameUserIdMap = array_column($aFsUserMap, 'user_id', 'name');

        $aMap = [];
        foreach ($aScheduleUndoneDataMap as $sSalesman => $iCount) {
            $sUserId = $aNameUserIdMap[$sSalesman] ?? '';
            if (!$sUserId) {
                continue;
            }
            $aMap[$sUserId] = [
                'name' => $sSalesman,
                'count' => $iCount,
            ];
        }

        return $aMap;
    }

    /**
     * @param $sUserId
     * @param $sMsg
     * @return void
     */
    private function sendFeishuUserMessage($sUserId = '', $sMsg = '') {
        if (empty($sUserId) || empty($sMsg)) {
            Log::warning(self::CRON_NAME . ' Invalid user ID or message.');
            return;
        }

        try {
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => $sMsg
            ];
            $this->oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }

    /**
     * @param $sUserId
     * @param $sMsg
     * @param $aParam
     * @return void
     */
    private function sendFeishuUserPostMessage($sUserId = '', $sMsg = '', $aParam = []) {
        if (empty($sUserId) || empty($sMsg)) {
            Log::warning(self::CRON_NAME . ' Invalid user ID or message.');
            return;
        }
        try {
            $sParam = http_build_query($aParam);
            $sReceiveIdType = 'user_id';
            $sMsgType = 'post';
            $aContent = [
                'zh_cn' => [
                    'title' => '反馈提醒',
                    'content' => [
                        [
                            [
                                'tag' => 'text',
                                'text' => $sMsg,
                            ],
                            [
                                'tag' => 'a',
                                'href' => 'https://finance-manage.dianhua.cn/Stat/PreTest/index.html?' . $sParam,
                                'text' => '点击处理',
                            ],
                        ],
                    ],
                ],
            ];
            $this->oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . " Failed to send post message to user {$sUserId}: " . $e->getMessage());
        }
    }
}