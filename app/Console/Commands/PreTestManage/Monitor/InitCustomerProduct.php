<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestMonitor;
use App\Define\StatDefine;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ConfigPriceCustomer;
use App\Models\Customer;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\PreTestManage\MonitorWeekStatistics;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 初始化监控数据
 * php artisan pre_test_manage_monitor:init_customer_product --action=0
 * @uses InitCustomerProduct
 */
class InitCustomerProduct extends Command
{
    protected $signature = 'pre_test_manage_monitor:init_customer_product;
    {--action= : 操作}';

    const CRON_NAME = '初始化监控数据';
    private $iAction = null;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $msg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $this->output->warning($msg);

            // $oFeishu = new FeishuRepository();
            // $aMessage = [
            //     'err_msg'     => $msg,
            //     'month'       => $this->iMonth,
            //     'customer_id' => $this->aCustomerId,
            // ];
            // $oFeishu->send_card_message_to_chat_group(self::CRON_NAME . '脚本执行失败!', $aMessage);
        }
    }

    private function setParam() {
        $this->iAction = (int)($this->input->getOption('action')) ?: 0;
    }

    private function main() {
        switch ($this->iAction) {
            case 1:
                // 统计 监控数据主体
                // 客户 主体 产品  子产品 开通时间, 有效期截止时间 产品转正时间
                $this->runBaseData();
                break;
            case 2:
                // 联调时间 调用时间
                $this->runTimeData();
                break;
            case 3:
                $this->runNumberData();
                break;
            default:
                // 近五周调用信息
                $this->runBaseData();
                $this->runTimeData();
                $this->runNumberData();
                break;
        }

        return true;
    }

    /**
     * @return true
     */
    private function runBaseData() {
        $aProductFatherMap = Product::getOriginParentIdMap();

        $iId = 0;
        do {
            $aDataList = $this->getCustomerList($iId);
//            $aDataList = Customer::select(['*'])->where('customer_id', '=', 'C201810196KCVX0')->get()->toArray();
            if (empty($aDataList)) {
                break;
            }
            $aId = array_column($aDataList, 'id');
            $iId = max($aId);

            $aCustomerId = array_column($aDataList, 'customer_id');
            $aCustomerGroupMap = array_column($aDataList, 'group_id', 'customer_id');

            list($aCustomerAccountMap, $aCustomerFeeMap) = $this->getCustomerAccountProductData($aCustomerId);

            $aInsertList = [];
            foreach ($aCustomerAccountMap as $sCustomerId => $aAccountProductData) {
                $sGroupId = $aCustomerGroupMap[$sCustomerId] ?? '';

                $aFeeTimeData = $aCustomerFeeMap[$sCustomerId] ?? [];

                foreach ($aAccountProductData as $iProductId => $aAccountProductList) {
                    $iFatherId = $aProductFatherMap[$iProductId] ?? 0;
                    if (!$iFatherId) {
                        continue;
                    }

                    if (count($aAccountProductList) > 1) {
                        $aCreateTime = array_column($aAccountProductList, 'create_at');
                        $aEndTime = array_column($aAccountProductList, 'end_time');

                        $iActivateTime = min($aCreateTime);
                        $iActivateEndTime = max($aEndTime);

                    } else {
                        $aData = current($aAccountProductList);
                        $iActivateTime = $aData['create_at'];
                        $iActivateEndTime = $aData['end_time'];
                    }

                    $aFeeTime = $aFeeTimeData[$iProductId] ?? [];

                    $aInsertList[] = [
                        'customer_id' => $sCustomerId,
                        'group_id' => $sGroupId,
                        'product_id' => $iProductId,
                        'father_id' => $iFatherId,
                        'activate_time' => $iActivateTime,
                        'activate_end_time' => $iActivateEndTime,
                        'fee_time' => $aFeeTime ? min($aFeeTime) : 0,
                        'docking_status' => MonitorCustomerProduct::DOCKING_STATUS_DEFAULT,
                    ];
                }
            }
            
            if ($aInsertList) {
                MonitorCustomerProduct::batchInsert($aInsertList);
            }

        } while (true);

        return true;
    }

    /**
     * @param $iId
     * @return array
     */
    private function getCustomerList($iId = 0) {
        $aInnerCustomer = PreTestMonitor::BAN_CUSTOMER_LIST;
        $aList = Customer::select(['id', 'customer_id', 'group_id'])->where('id', '>', $iId)
            // 去掉 内部账号
            ->whereNotIn('customer_id', $aInnerCustomer)
            ->where('status', 1)
            ->limit(200)->orderBy('id', 'ASC')->get()->toArray();

        return $aList;
    }

    /**
     * @param $aCustomerId
     * @return array
     */
    private function getCustomerAccountProductData($aCustomerId = []) {
        $aAccountList = Account::getListByCustomerIds($aCustomerId);

        // 产品计费相关信息
        $aApikey = array_column($aAccountList,  'apikey');
        $aApikeyFeeMap = $this->getFeeConfig($aApikey);

        // 产品开通相关信息
        $aAccountId = array_column($aAccountList,  'account_id');
        $aCond = [
            ['end_time', '>', time()],
            ['account_id', 'in', $aAccountId],
        ];
        $aField = [
            'account_id',
            'product_id',
            'create_at',
            'end_time',
        ];
        $aAccountProductList = AccountProduct::aGetDataByCond($aCond, $aField);
        $aAccountProductDataMap = [];
        foreach ($aAccountProductList as $aItem) {
            $aAccountProductDataMap[$aItem['account_id']][] = $aItem;
        }

        $aCustomerAccountMap = [];
        $aCustomerFeeMap = [];
        foreach ($aAccountList as $aItem) {
            $sCustomerId = $aItem['customer_id'];
            $sAccountId = $aItem['account_id'];
            $aAccountProductDataList = $aAccountProductDataMap[$sAccountId] ?? [];
            if (empty($aAccountProductDataList)) {
                continue;
            }

            foreach ($aAccountProductDataList as $aData) {
                $aCustomerAccountMap[$sCustomerId][$aData['product_id']][] = $aData;
            }

            $sApikey = $aItem['apikey'];
            $aFeeDataList = $aApikeyFeeMap[$sApikey] ?? [];
            if ($aFeeDataList) {
                foreach ($aFeeDataList as $iProductId => $iTime) {
                    $aCustomerFeeMap[$sCustomerId][$iProductId][] = $iTime;
                }
            }
        }

        return [$aCustomerAccountMap, $aCustomerFeeMap];
    }

    /**
     * @param $aApikey
     * @return array
     */
    private function getFeeConfig($aApikey = []) {
        $aDataList = ConfigPriceCustomer::select(['apikey', 'product_ids', 'start_date'])->whereIn('apikey', $aApikey)->whereNull('delete_time')->get()->toArray();

        $aMap = [];
        foreach ($aDataList as $aData) {
            $sApikey = $aData['apikey'];
            $sProductId = $aData['product_ids'];
            $aProductId = explode(',', $sProductId);
            $iStartTime = strtotime($aData['start_date']);
            foreach ($aProductId as $iProductId) {
                $aMap[$sApikey][$iProductId][] = $iStartTime;
            }
        }

        $aRet = [];
        foreach ($aMap as $sApikey => $aProductTime) {
            foreach ($aProductTime as $iProductId => $aTime) {
                $aRet[$sApikey][$iProductId] = min($aTime);
            }
        }

        return $aRet;
    }

    /**
     * @return true
     */
    private function runTimeData() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['debug_time', '=', 0],
            ];
            $aDataList = MonitorCustomerProduct::aGetDataByCond($aCond, ['*'], 0, 50, 'id', 'ASC');
            if (empty($aDataList)) {
                break;
            }
            $aId = array_column($aDataList, 'id');
            $iId = max($aId);

            $aCustomerList = array_column($aDataList, 'customer_id');
            $aCusApiMap = $this->aGetApiKeyList($aCustomerList);
            foreach ($aDataList as $aData) {
                $sCustomerId = $aData['customer_id'];

                $aApiKeyList = $aCusApiMap[$sCustomerId];
                if (empty($aApiKeyList)) {
                    continue;
                }
                // 产品联调
                $iActivateTime = $aData['activate_time'];
                $iDebugTime = $this->aGetFirstData($aApiKeyList, $iActivateTime, [$aData['product_id']]);

                $iFeeTime = $aData['fee_time'];
                $iCallTime = $aData['call_time'];
                if ($iFeeTime) {
                    $iCallTime = $this->aGetFirstData($aApiKeyList, $iFeeTime, [$aData['product_id']]);
                }

                if ($iDebugTime) {
                    $aUpdate = [
                        'debug_time' => $iDebugTime,
                        'call_time' => $iCallTime,
                    ];
                    MonitorCustomerProduct::iUpdateById($aData['id'], $aUpdate);
                }
            }
        } while (true);

        return true;
    }

    /**
     * @param $aCustomerList
     * @return array
     */
    private function aGetApiKeyList($aCustomerList) {
        $aList = Account::getListByCustomerIds($aCustomerList);

        $aMap = [];
        foreach ($aList as $aItem) {
            $aMap[$aItem['customer_id']][] = $aItem['apikey'];
        }

        return $aMap;
    }

    /**
     * @param $aApiKeyList
     * @param $iStartTime
     * @param $aProductId
     * @return int
     */
    private function aGetFirstData($aApiKeyList = [], $iStartTime = 0, $aProductId = []) {
        $sStartTime = date('Ymd', $iStartTime);
        $aFirstDataList = StatisticsCustomerUsage::aGetFirstDataByApiKey($aApiKeyList, $sStartTime, $aProductId);

        $aTime = [];
        foreach ($aFirstDataList as $aItem) {
            $aTime[] = strtotime($aItem['date']);
        }

        return $aTime ? min($aTime) : 0;
    }

    /**
     * @return true
     */
    private function runNumberData() {
        $sStartDate = '********';
//        $sStartDate = '********'; // todo
        $iStartTime = strtotime($sStartDate);

        $iNow = time();
//        $iNow = strtotime('20230710'); // todo
        $iLaterDay = strtotime('+3 days', $iNow); // 3天后日期
        $iThisThursday = strtotime("thursday this week", $iLaterDay); // 3天后日期 所在周 周四
        $iEndTime = strtotime('-1 weeks', $iThisThursday); // 1周前 周四
        $sEndDate = date('Ymd', $iEndTime);

        $iStartWeek = date('oW', strtotime('+3 days', $iStartTime)); // week_id 向右偏移3天
        $iEndWeek = date('oW', strtotime('+3 days', $iEndTime)); // week_id 向右偏移3天

        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
            ];
            $aDataList = MonitorCustomerProduct::aGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (empty($aDataList)) {
                break;
            }
            $aId = array_column($aDataList, 'id');
            $iId = max($aId);

            $aCustomerList = array_column($aDataList, 'customer_id');
            $aCusApiMap = $this->aGetApiKeyList($aCustomerList);

            foreach ($aDataList as $aItem) {
                $aApiKeyList = $aCusApiMap[$aItem['customer_id']] ?? [];
                if (empty($aApiKeyList)) {
                    continue;
                }

                if (!$aItem['fee_time']) {
                    // 表   s, e
                    list($aNumberMap, $aIncomeMap) = $this->getDataByTable($aApiKeyList, $aItem['product_id'], $sStartDate, $sEndDate);
                } else {
                    if ($aItem['fee_time'] < $iStartTime) {
                        // 接口 s, e
                        list($aNumberMap, $aIncomeMap) = $this->getDataByInterface($aItem['customer_id'], $aApiKeyList, $aItem['product_id'], $sStartDate, $sEndDate);
                    } else {
                        // 表 s, f
                        $sFeeDate = date('Ymd', $aItem['fee_time']);
                        list($aTableNumber, $aTableIncome) = $this->getDataByTable($aApiKeyList, $aItem['product_id'], $sStartDate, $sFeeDate);
                        // 接口 f, e
                        list($aInterfaceNumber, $aInterfaceIncome) = $this->getDataByInterface($aItem['customer_id'], $aApiKeyList, $aItem['product_id'], $sFeeDate, $sEndDate);

                        $aNumberMap = $aInterfaceNumber + $aTableNumber;
                        $aIncomeMap = $aInterfaceIncome + $aTableIncome;
                    }
                }

                $aWeekMap = [];
                for ($i = $iStartTime; $i <= $iEndTime; $i += 86400) {
                    $d = date('Ymd', $i);
                    $iWeek = date('oW', strtotime('+3 days', $i));

                    if (!isset($aWeekMap[$iWeek])) {
                        $aWeekMap[$iWeek] = [
                            'number' => $aNumberMap[$d] ?? 0,
                            'income' => $aIncomeMap[$d] ?? 0,
                        ];
                    } else {
                        $aWeekMap[$iWeek]['number'] += $aNumberMap[$d] ?? 0;
                        $aWeekMap[$iWeek]['income'] = bcadd($aWeekMap[$iWeek]['income'], $aIncomeMap[$d] ?? 0);
                    }
                }

                $iMonitorId = $aItem['id'];
                // 删除
                MonitorWeekStatistics::iDeleteByMonitorAndWeek($iMonitorId, $iStartWeek, $iEndWeek);
                // 新增
                $aInsertList = [];
                foreach ($aWeekMap as $iWeekId => $aData) {
                    $aInsertList[] = [
                        'monitor_id' => $iMonitorId,
                        'week_id' => $iWeekId,
                        'number' => $aData['number'] ?? 0,
                        'income' => $aData['income'] ?? 0,
                    ];
                }
                if ($aInsertList) {
                    MonitorWeekStatistics::batchInsert($aInsertList);
                }
            }
        } while (true);

        return true;
    }

    /**
     * @param $aApikey
     * @param $iProductId
     * @param $sStartDate
     * @param $sEndDate
     * @return array
     */
    private function getDataByTable($aApikey = [], $iProductId = 0, $sStartDate = '', $sEndDate = '') {
        if (empty($aApikey) || empty($iProductId) || (empty($sStartDate) && empty($sEndDate))) {
            return [];
        }
        $aDataList = StatisticsCustomerUsage::aGetProductEachDayDataByApiKey($aApikey, $iProductId, $sStartDate, $sEndDate);

        
        $aDateNumberMap = array_column($aDataList, 'success', 'date');
        $aDateIncomeMap = [];

        return [$aDateNumberMap, $aDateIncomeMap];
    }

    /**
     * @param $sCustomerId
     * @param $aApikey
     * @param $iProductId
     * @param $sStartDate
     * @param $sEndDate
     * @return array
     */
    private function getDataByInterface($sCustomerId, $aApikey = [], $iProductId = 0, $sStartDate = '', $sEndDate = '') {
        if (empty($aApikey) || empty($iProductId) || (empty($sStartDate) && empty($sEndDate))) {
            return [];
        }

        $iDimension = StatDefine::INCOME_DIMENSION_DATE_CUSTOMER;
        $aDataList = (new MainRepository())->getBaseIncome($iDimension, $sStartDate, $sEndDate, [$sCustomerId], $aApikey, [], [$iProductId], ['is_query_month_data' => 0]);

        $aDateNumberMap = [];
        $aDateIncomeMap = [];
        if ($aDataList['data']) {
            $aDateNumberMap = array_column($aDataList['data'], 'number', 'date');
            $aDateIncomeMap = array_column($aDataList['data'], 'income', 'date');

        }

        return [$aDateNumberMap, $aDateIncomeMap];
    }
}