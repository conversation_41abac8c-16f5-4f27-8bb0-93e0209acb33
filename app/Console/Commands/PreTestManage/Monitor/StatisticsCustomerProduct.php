<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestManage;
use App\Define\PreTestMonitor;
use App\Define\StatDefine;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ConfigPriceCustomer;
use App\Models\Customer;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\PreTestManage\MonitorWeekStatistics;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 统计接入客户调用情况
 * php artisan pre_test_manage_monitor:statistics_customer_product_all_time
 * @uses StatisticsCustomerProduct
 */
class StatisticsCustomerProduct extends Command
{
    protected $signature = 'pre_test_manage_monitor:statistics_customer_product_all_time;
    {--action= : 操作}';

    const CRON_NAME = '[统计所有客户调用情况]';

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {}

    private function main() {
        // 统计 监控数据主体
        // 客户 主体 产品  子产品 开通时间, 有效期截止时间 产品转正时间
        $this->runIncrementData();
        // 联调时间 计费时间 调用时间
        $this->runDebugTimeData();
        $this->runFeeTimeData();
        $this->runCallTimeData();

        return true;
    }

    /**
     * @return bool
     */
    private function runIncrementData() {
        $aProductFatherMap = Product::getOriginParentIdMap();
        $sLastFriday = date('Y-m-d', strtotime('-1 week', time()));
        $iTime = strtotime($sLastFriday);
//        $iTime = **********; // todo

        $aCond = [
            ['create_at', '>', $iTime],
            ['end_time', '>',$iTime],
        ];
        $aNewData = AccountProduct::aGetDataByCond($aCond);
        $aCond = [
            ['update_at', '>', $iTime],
            ['end_time', '>',$iTime],
        ];
        $aUpdateData = AccountProduct::aGetDataByCond($aCond);
        $aDataList = array_merge($aNewData, $aUpdateData);
        $aDataList = array_column($aDataList, null, 'id');
        $aAccountId = array_values(array_unique(array_column($aDataList, 'account_id')));
        if (empty($aAccountId)) {
            return true;
        }
        $aAccountList = Account::getListByAccountIds($aAccountId);
        if (empty($aAccountList)) {
            return false;
        }

        $aAccountCustomerMap = array_column($aAccountList, 'customer_id', 'account_id');
        $aCustomerId = array_values(array_unique($aAccountCustomerMap));
        $aCustomerList = $this->getCustomerList($aCustomerId);
        $aCustomerGroupMap = array_column($aCustomerList, 'group_id', 'customer_id');
        $aCustomerMap = array_column($aCustomerList, null, 'customer_id');
        $fatherIdsWithoutProduct = $this->aGetFatchIdWithoutProductId();

        foreach ($aDataList as $aData) {
            $sCustomerId = $aAccountCustomerMap[$aData['account_id']] ?? '';
            if (!$sCustomerId || !isset($aCustomerGroupMap[$sCustomerId])) {
                continue;
            }
            $iCreateTime = $aData['create_at'];
            $iEndTime = $aData['end_time'];
            $iProductId = $aData['product_id'];
            $iFatherId = $aProductFatherMap[$iProductId] ?? 0;
            if (!$iFatherId && isset($fatherIdsWithoutProduct[$iProductId])) {
                $iFatherId = $iProductId;
            } elseif (!$iFatherId) {
                continue;
            }

            $oMonitor = MonitorCustomerProduct::oGetOneByCustomerProductId($sCustomerId, $iProductId);
            if (empty($oMonitor)) {
                $oMonitor = new MonitorCustomerProduct();
                $oMonitor->customer_id = $sCustomerId;
                $oMonitor->group_id = $aCustomerGroupMap[$sCustomerId];
                $oMonitor->father_id = $iFatherId;
                $oMonitor->product_id = $iProductId;
                $oMonitor->activate_time = $iCreateTime;
                $oMonitor->activate_end_time = $iEndTime;
                $oMonitor->docking_status = MonitorCustomerProduct::DOCKING_STATUS_DEFAULT;
                $oMonitor->salesman = $aCustomerMap[$sCustomerId]['salesman'] ?? '';
            } else {
                if ($oMonitor->activate_end_time < $iEndTime) {
                    $oMonitor->activate_end_time = $iEndTime;
                }
            }
            $oMonitor->save();
        }
        
        return true;
    }

    /**
     *  查询没有子产品主产品id
     * @return array
     */
    private function aGetFatchIdWithoutProductId(){
        $aCond = [
            ['father_id', '=',0],
        ];
        $aField = ['product_id'];
        $allFatherIdsMap = Product::aGetDataByCond($aCond,$aField); //查询主产品
        $allFatherIds = array_column($allFatherIdsMap,'product_id');
        $aCond = [
            ['father_id', '!=',0],
        ];
        $aField = [DB::raw('DISTINCT `father_id`')];
        $fatherIdsWithProductMap = Product::aGetDataByCond($aCond,$aField); //查询有子产品的主产品
        $fatherIdsWithProduct = array_column($fatherIdsWithProductMap,'father_id');

        $fatherIdsWithoutProductId = array_diff($allFatherIds,$fatherIdsWithProduct);

        return $fatherIdsWithoutProductId;
    }

    /**
     * @param $aCustomerId
     * @return array
     */
    private function getCustomerList($aCustomerId = []) {
        $aBan = PreTestMonitor::BAN_CUSTOMER_LIST;
        $aCustomerId = array_diff($aCustomerId, $aBan);
        $aList = Customer::select(['id', 'customer_id', 'group_id', 'salesman'])->whereIn('customer_id', $aCustomerId)->get()->toArray();

        return $aList;
    }

    /**
     * @return true
     */
    private function runDebugTimeData() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['debug_time', '=', 0],
            ];
            $aDataList = MonitorCustomerProduct::aGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (empty($aDataList)) {
                break;
            }
            $aId = array_column($aDataList, 'id');
            $iId = max($aId);

            $aCustomerList = array_column($aDataList, 'customer_id');
            $aCusApiMap = $this->aGetApiKeyList($aCustomerList);
            foreach ($aDataList as $aData) {
                $sCustomerId = $aData['customer_id'];

                $aApiKeyList = $aCusApiMap[$sCustomerId];
                if (empty($aApiKeyList)) {
                    continue;
                }
                // 产品联调
                $iActivateTime = $aData['activate_time'];
                $iDebugTime = $this->aGetFirstData($aApiKeyList, $iActivateTime, [$aData['product_id']]);

                if ($iDebugTime) {
                    $aUpdate = [
                        'debug_time' => $iDebugTime,
                    ];
                    MonitorCustomerProduct::iUpdateById($aData['id'], $aUpdate);
                }
            }
        } while (true);

        return true;
    }

    /**
     * @return true
     */
    private function runFeeTimeData() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['fee_time', '=', 0],
            ];
            $aDataList = MonitorCustomerProduct::aGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (empty($aDataList)) {
                break;
            }
            $aId = array_column($aDataList, 'id');
            $iId = max($aId);

            $aCustomerList = array_column($aDataList, 'customer_id');
            $aCusApiMap = $this->aGetApiKeyList($aCustomerList);
            foreach ($aDataList as $aData) {
                $sCustomerId = $aData['customer_id'];

                $aApiKeyList = $aCusApiMap[$sCustomerId];
                if (empty($aApiKeyList)) {
                    continue;
                }

                $iFeeTime = $this->getFeeConfig($aApiKeyList, $aData['father_id'], $aData['product_id']);
                if ($iFeeTime) {
                    $aUpdate = [
                        'fee_time' => $iFeeTime,
                    ];
                    MonitorCustomerProduct::iUpdateById($aData['id'], $aUpdate);
                }
            }
        } while (true);

        return true;
    }

    /**
     * @param $aApikey
     * @return int
     */
    private function getFeeConfig($aApikey = [], $iFatherId = 0, $iProductId = 0) {
        $aDataList = ConfigPriceCustomer::select(['apikey', 'product_ids', 'start_date'])
            ->where('father_id', $iFatherId)->where('product_ids', 'like', '%'. $iProductId . '%')
            ->whereIn('apikey', $aApikey)->whereNull('delete_time')->get()->toArray();

        $aTime = [];
        foreach ($aDataList as $aData) {
            $sProductId = $aData['product_ids'];
            $aProductId = explode(',', $sProductId);
            $iStartTime = strtotime($aData['start_date']);
            foreach ($aProductId as $iTheProductId) {
                $iTheProductId == $iProductId and $aTime[] = $iStartTime;
            }
        }

        return $aTime ? min($aTime) : 0;
    }

    /**
     * @return true
     */
    private function runCallTimeData() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['fee_time', '>', 0],
                ['call_time', '=', 0],
            ];
            $aDataList = MonitorCustomerProduct::aGetDataByCond($aCond, ['*'], 0, 50, 'id', 'ASC');
            if (empty($aDataList)) {
                break;
            }
            $aId = array_column($aDataList, 'id');
            $iId = max($aId);

            $aCustomerList = array_column($aDataList, 'customer_id');
            $aCusApiMap = $this->aGetApiKeyList($aCustomerList);
            foreach ($aDataList as $aData) {
                $sCustomerId = $aData['customer_id'];

                $aApiKeyList = $aCusApiMap[$sCustomerId];
                if (empty($aApiKeyList)) {
                    continue;
                }
                $iFeeTime = $aData['fee_time'];
                $iCallTime = $this->aGetFirstData($aApiKeyList, $iFeeTime, [$aData['product_id']]);

                if ($iCallTime) {
                    $aUpdate = [
                        'call_time' => $iCallTime,
                    ];
                    MonitorCustomerProduct::iUpdateById($aData['id'], $aUpdate);
                }
            }
        } while (true);

        return true;
    }

    /**
     * @param $aCustomerList
     * @return array
     */
    private function aGetApiKeyList($aCustomerList) {
        $aList = Account::getListByCustomerIds($aCustomerList);

        $aMap = [];
        foreach ($aList as $aItem) {
            $aMap[$aItem['customer_id']][] = $aItem['apikey'];
        }

        return $aMap;
    }

    /**
     * @param $aApiKeyList
     * @param $iStartTime
     * @param $aProductId
     * @return int
     */
    private function aGetFirstData($aApiKeyList = [], $iStartTime = 0, $aProductId = []) {
        $sStartTime = date('Ymd', $iStartTime);
        $aFirstDataList = StatisticsCustomerUsage::aGetFirstDataByApiKey($aApiKeyList, $sStartTime, $aProductId);

        $aTime = [];
        foreach ($aFirstDataList as $aItem) {
            $aTime[] = strtotime($aItem['date']);
        }

        return $aTime ? min($aTime) : 0;
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
}