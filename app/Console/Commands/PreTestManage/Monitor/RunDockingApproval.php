<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestManage;
use App\Define\PreTestMonitor;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Providers\Approval\MonitorApprovalHandle;
use App\Providers\Approval\Strategy\Strategy;
use App\Providers\Approval\Strategy\StrategyScript;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 根据状态提交流程工单
 * php artisan pre_test_manage_monitor:run_docking_approval
 * @uses RunDockingApproval
 */
class RunDockingApproval extends Command
{
    protected $signature = 'pre_test_manage_monitor:run_docking_approval;';

    const CRON_NAME = '[根据状态提交流程工单]';

    private $iNow = 0;
    private $iStatStartTime = 0;

    /**
     * @var Strategy | StrategyScript
     */
    private $oStrategy = null;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->iNow = time();
        $this->iStatStartTime = strtotime('2025-01-01');

        $this->oStrategy = new StrategyScript();
    }

    /**
     * @return true
     */
    private function main() {
        // 灰度上线时间提醒
        $this->runForGrayscale();
        // 超出预计上线时间提醒
        $this->runForExpectOnline();
        // 调用量级稳定确认
        $this->runForCallStable();

        return true;
    }

    /**
     * @return true
     */
    private function runForGrayscale() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['grayscale_start_time', '<=', ($this->iNow - 86400 * 7)],
                ['grayscale_start_time', '>', 0],
                ['gray_call_number', '<', 100],
                ['activate_status', '>', 0],
                ['activate_time', '>=', $this->iStatStartTime],
                ['docking_status', 'notIn', [MonitorCustomerProduct::DOCKING_STATUS_NORMAL, MonitorCustomerProduct::DOCKING_STATUS_NOT_FOLLOW]],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 1000, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }
            $iId = max(array_column($oDataList->toArray(), 'id'));
            try {
                $oHandle = new MonitorApprovalHandle($oDataList, PreTestMonitor::APPROVAL_CODE_GRAYSCALE);
                $oHandle->setStrategy($this->oStrategy)->buildGatherList()->handle();
            } catch (\Exception $e) {
                $sMsg = self::CRON_NAME . ' 创建审批失败! 原因: ' . $e->getMessage();
                Log::error($sMsg, ['type' => __FUNCTION__]);
            }
        } while (true);

        return true;
    }

    /**
     * @return true
     */
    private function runForExpectOnline() {
        $iTimeLimit = $this->iNow - 86400 * 7;
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['expect_online_time', '<', $iTimeLimit],
                ['expect_online_time', '>', 0],
                ['expect_number', '<', 100],
                ['activate_status', '>', 0],
                ['activate_time', '>=', $this->iStatStartTime],
                ['docking_status', 'notIn', [MonitorCustomerProduct::DOCKING_STATUS_NORMAL, MonitorCustomerProduct::DOCKING_STATUS_NOT_FOLLOW]],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 1000, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }
            $iId = max(array_column($oDataList->toArray(), 'id'));
            try {
                $oHandle = new MonitorApprovalHandle($oDataList, PreTestMonitor::APPROVAL_CODE_EXPECT_ONLINE);
                $oHandle->setStrategy($this->oStrategy)->buildGatherList()->handle();
            } catch (\Exception $e) {
                $sMsg = self::CRON_NAME . ' 创建审批失败! 原因: ' . $e->getMessage();
                Log::error($sMsg, ['type' => __FUNCTION__]);
            }
        } while (true);

        return true;
    }

    /**
     * @return true
     */
    private function runForCallStable() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['call_days', '=', 30],
                ['activate_status', '>', 0],
                ['activate_time', '>=', $this->iStatStartTime],
                ['docking_status', '=', MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_5],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }
            $iId = max(array_column($oDataList->toArray(), 'id'));
            try {
                $oHandle = new MonitorApprovalHandle($oDataList, PreTestMonitor::APPROVAL_CODE_CALL_STABLE);
                $oHandle->setStrategy($this->oStrategy)->buildGatherList()->handle();
            } catch (\Exception $e) {
                $sMsg = self::CRON_NAME . ' 创建审批失败! 原因: ' . $e->getMessage();
                Log::error($sMsg, ['type' => __FUNCTION__]);
            }
        } while (true);

        return true;
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
}