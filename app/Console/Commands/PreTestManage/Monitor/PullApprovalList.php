<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestManage;
use App\Models\PreTestManage\Approval\Approval;
use App\Models\PreTestManage\Approval\ApprovalTransfer;
use App\Models\PreTestManage\Approval\CallStable;
use App\Models\PreTestManage\Approval\ExpectOnline;
use App\Models\PreTestManage\Approval\Grayscale;
use App\Models\PreTestManage\Approval\PreTest;
use App\Models\PreTestManage\MonitorApproval;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 拉取客户监控流程工单
 * php artisan pre_test_manage_monitor:pull_approval_list
 * @uses PullApprovalList
 */
class PullApprovalList extends Command
{
    protected $signature = 'pre_test_manage_monitor:pull_approval_list';

    const CRON_NAME = '[拉取客户监控流程工单]';

    /**
     * @var FeishuRepository
     */
    private $oFeishuPreTestRep = null;

    private $aUnableInstanceStatus = [
        PreTestManage::INSTANCE_STATUS_RECALL,
        PreTestManage::INSTANCE_STATUS_CANCELED,
        PreTestManage::INSTANCE_STATUS_REJECT,
        PreTestManage::INSTANCE_STATUS_REJECTED,
        PreTestManage::INSTANCE_STATUS_DELETED,
    ];

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam()
    {
        $this->oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
    }

    /**
     * @return true
     * @throws Exception
     */
    private function main()
    {
        $aCond = [
            ['is_done', '=', 0],
        ];
        $oDataList = MonitorApproval::oGetDataListByCond($aCond);
        if (!$oDataList->count()) {
            return true;
        }

        foreach ($oDataList as $oData) {
            $oInstance = $this->getInstanceInfo($oData->instance_code);
            $sInstanceStatus = $oInstance->getStatus();
            // 异常数据 || 审批中 不处理
            if (is_null($sInstanceStatus)
                || PreTestManage::INSTANCE_STATUS_PENDING == $sInstanceStatus
            ) {
                continue;
            }
            // 废弃装填直接标记为已处理
            if (in_array($sInstanceStatus, $this->aUnableInstanceStatus)) {
                $oData->is_done = 1;
                $oData->instance_status = $sInstanceStatus;
                $oData->save();
                continue;
            }
            // 审批通过
            if (PreTestManage::INSTANCE_STATUS_APPROVED == $sInstanceStatus) {
                try {
                    $oMonitor = MonitorCustomerProduct::find($oData->monitor_id);
                    $oInstance->transForm()->setMonitor($oMonitor)->saveData();
                    $aData = $oInstance->getData();
                    $oData->instance_status = $sInstanceStatus;
                    $oData->content = json_encode($aData);
                    $oData->is_done = 1;
                    $oData->reason_type = $aData['reason_type'] ?? 0;
                    $oData->save();
                } catch (Exception $e) {
                    Log::error(self::CRON_NAME . '保存申请工单信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $oData->instance_code]);
                }
            }
        }

        return true;
    }

    /**
     * @param $sInstanceCode
     * @return Approval|CallStable|ExpectOnline|Grayscale|PreTest
     */
    private function getInstanceInfo($sInstanceCode = '') {
        $aResult = [];
        try {
            $aResult = $this->oFeishuPreTestRep->get_approval_info($sInstanceCode);
            if (empty($aResult)) {
                Log::warning(self::CRON_NAME . ' 申请工单信息为空', ['instance_code' => $sInstanceCode]);
            }
        } catch (Exception $e) {
            Log::error(self::CRON_NAME . '获取申请工单信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $sInstanceCode]);
        }

        return ApprovalTransfer::makeApproval($aResult, null);
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
}