<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestManage;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\StatisticsCustomerUsage;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

/**
 * 计算对接状态
 * php artisan pre_test_manage_monitor:run_docking_status
 * @uses RunDockingStatus
 */
class RunDockingStatus extends Command
{
    protected $signature = 'pre_test_manage_monitor:run_docking_status;';

    const CRON_NAME = '[计算对接状态]';

    private $iNow = 0;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->iNow = time();
    }

    /**
     * @return true
     */
    private function main() {
        // 计算 对接状态
        $this->runDockingStatus();
        // 计算 灰度期间调用量
        $this->runGrayCallNum();
        // 计算 预计上线后7天内计费量
        $this->runExpectNum();

        return true;
    }

    /**
     * @return true
     */
    private function runDockingStatus() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }
            // 客户-产品 所有账号开通状态配置信息
            $aCustomerProductMap = $this->aGetCustomerProductMap($oDataList);

            $aId = [];
            $aUpdateList = [];
            foreach ($oDataList as $oData) {
                $aId[] = $oData->id;

                // 计算产品最新开通状态
                $aProductConf = $aCustomerProductMap[$oData->customer_id][$oData->product_id] ?? [];
                list($iActivateStatus, $iActivateEndTime) = $this->aGetProductStatus($aProductConf);
                if ($iActivateStatus == $oData->activate_status
                    && in_array($oData->docking_status, [MonitorCustomerProduct::DOCKING_STATUS_NORMAL, MonitorCustomerProduct::DOCKING_STATUS_NOT_FOLLOW])
                ) {
                    continue;
                }
                $iTimeDiff = 0;
                $iCallDays = 0;
                $iCallNumber = 0;
                if ($iActivateStatus) {
                    list($iCallDays, $iCallNumber, $iStatCallDays) = MonitorCustomerProduct::aGetCallDays($oData);
                    $oData->call_days = $iCallDays;
                    list($iNewDockStatus, $iTimeDiff) = MonitorCustomerProduct::aGetDockingStatus($oData);
                } else {
                    $iNewDockStatus = MonitorCustomerProduct::DOCKING_STATUS_NOT_FOLLOW;
                }
                $aUpdateList[] = [
                    'id' => $oData->id,
                    'docking_status' => $iNewDockStatus,
                    'time_diff' => $iTimeDiff,
                    'call_days' => $iCallDays,
                    'call_number' => $iCallNumber,
                    'activate_status' => $iActivateStatus,
                    'activate_end_time' => $iActivateEndTime,
                ];
            }
            if ($aUpdateList) {
                MonitorCustomerProduct::batchUpdateData($aUpdateList, 'id');
            }
            $iId = max($aId);
        } while (true);

        return true;
    }

    /**
     * @param Collection $oDataList
     * @return array
     */
    private function aGetCustomerProductMap(Collection $oDataList) {
        $aDataList = $oDataList->toArray();
        $aCustomerId = array_unique(array_column($aDataList, 'customer_id'));
        // 账号
        $aAccountList = Account::getListByCustomerIds($aCustomerId);
        // 产品开通相关信息
        $aAccountId = array_column($aAccountList,  'account_id');
        $aCond = [
            ['account_id', 'in', $aAccountId],
        ];
        $aField = [
            'account_id',
            'product_id',
            'create_at',
            'end_time',
            'status',
        ];
        $aAccountProductList = AccountProduct::aGetDataByCond($aCond, $aField);
        $aAccountProductDataMap = [];
        foreach ($aAccountProductList as $aItem) {
            $iStatus = $aItem['status'];
            if (AccountProduct::STATUS_UNAVAILABLE == $aItem['status']
                || $aItem['end_time'] < $this->iNow
            ) {
                $iStatus = AccountProduct::STATUS_UNAVAILABLE;
            }
            $aConf = [
                'product_id' => $aItem['product_id'],
                'status' => $iStatus,
                'end_time' => $aItem['end_time'],
            ];
            $aAccountProductDataMap[$aItem['account_id']][] = $aConf;
        }
        $aCustomerProductMap = [];
        foreach ($aAccountList as $aItem) {
            $sAccountId = $aItem['account_id'];
            $aAccountProductConf = $aAccountProductDataMap[$sAccountId] ?? [];
            if (empty($aAccountProductConf)) {
                continue;
            }

            foreach ($aAccountProductConf as $aConf) {
                $aCustomerProductMap[$aItem['customer_id']][$aConf['product_id']][] = $aConf;
            }
        }

        return $aCustomerProductMap;
    }

    /**
     * @param $aProductConf
     * @return array
     */
    private function aGetProductStatus($aProductConf = []) {
        $iStatus = 0;
        $iEndTimeOn = 0;
        $iEndTimeOff = 0;

        foreach ($aProductConf as $aConf) {
            if ($aConf['status']) {
                $iStatus = $aConf['status'];
                $iEndTimeOn = max($iEndTimeOn, $aConf['end_time']);
            } else {
                $iEndTimeOff = max($iEndTimeOff, $aConf['end_time']);
            }
        }

        return [$iStatus, $iStatus ? $iEndTimeOn : $iEndTimeOff];
    }

    /**
     * @return true
     */
    private function runGrayCallNum() {
        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['grayscale_start_time', '>', 0],
                ['grayscale_end_time', '>', 0],
                ['grayscale_start_time', '<', $this->iNow],
                ['grayscale_end_time', '>', $this->iNow],
                ['docking_status', 'notIn',  [MonitorCustomerProduct::DOCKING_STATUS_NORMAL, MonitorCustomerProduct::DOCKING_STATUS_NOT_FOLLOW]],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }

            $aCustomerId = array_column($oDataList->toArray(), 'customer_id');
            $aCustomerApikeyMap = $this->aGetApiKeyList($aCustomerId);

            $aId = [];
            $aUpdateList = [];
            foreach ($oDataList as $oData) {
                $aId[] = $oData->id;

                $aApikey = $aCustomerApikeyMap[$oData->customer_id];
                $sStartDate = date('Ymd', $oData->grayscale_start_time);
                $sEndDate = date('Ymd', min($oData->grayscale_end_time, $this->iNow));
                $iGrayCallNumber = $this->iGetDataByTable($aApikey, $oData->product_id, $sStartDate, $sEndDate);

                if ($iGrayCallNumber != $oData->gray_call_number) {
                    $aUpdateList[] = [
                        'id' => $oData->id,
                        'gray_call_number' => $iGrayCallNumber,
                    ];
                }

            }
            if ($aUpdateList) {
                MonitorCustomerProduct::batchUpdateData($aUpdateList, 'id');
            }
            $iId = max($aId);
        } while (true);

        return true;
    }

    /**
     * @param $aCustomerId
     * @return array
     */
    private function aGetApiKeyList($aCustomerId) {
        $aList = Account::getListByCustomerIds($aCustomerId);

        $aMap = [];
        foreach ($aList as $aItem) {
            $aMap[$aItem['customer_id']][] = $aItem['apikey'];
        }

        return $aMap;
    }

    /**
     * @param $aApikey
     * @param $iProductId
     * @param $sStartDate
     * @param $sEndDate
     * @return int
     */
    private function iGetDataByTable($aApikey = [], $iProductId = 0, $sStartDate = '', $sEndDate = '') {
        if (empty($aApikey) || empty($iProductId) || (empty($sStartDate) && empty($sEndDate))) {
            return 0;
        }
        $aDataList = StatisticsCustomerUsage::aGetProductEachDayDataByApiKey($aApikey, $iProductId, $sStartDate, $sEndDate);
        if (empty($aDataList)) {
            return 0;
        }
        $aNumber = array_column($aDataList, 'success');

        return array_sum($aNumber);
    }

    /**
     * @return true
     */
    private function runExpectNum() {
        $iId = 0;
        $iLimitTime = $this->iNow - 86400 * 7;
        do {
            $aCond = [
                ['id', '>', $iId],
                ['expect_online_time', '<', $iLimitTime],
                ['expect_online_time', '>', 0],
                ['fee_time', '<',  $iLimitTime],
                ['fee_time', '>',  0],
                ['docking_status', 'notIn',  [MonitorCustomerProduct::DOCKING_STATUS_NORMAL, MonitorCustomerProduct::DOCKING_STATUS_NOT_FOLLOW]],
            ];
            $oDataList = MonitorCustomerProduct::oGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (!$oDataList->count()) {
                break;
            }

            $aId = [];
            $aUpdateList = [];
            foreach ($oDataList as $oData) {
                $aId[] = $oData->id;

                $iExpectNumber = MonitorCustomerProduct::iGetExpectNumber($oData);
                $aUpdateList[] = [
                    'id' => $oData->id,
                    'expect_number' => $iExpectNumber,
                ];
            }
            if ($aUpdateList) {
                MonitorCustomerProduct::batchUpdateData($aUpdateList, 'id');
            }
            $iId = max($aId);
        } while (true);

        return true;
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
}