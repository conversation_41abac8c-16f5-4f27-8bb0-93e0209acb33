<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestManage;
use App\Define\PreTestMonitor;
use App\Define\StatDefine;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ConfigPriceCustomer;
use App\Models\Customer;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\PreTestManage\MonitorWeekStatistics;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 统计所有客户周调用情况
 * php artisan pre_test_manage_monitor:statistics_customer_product_week
 * @uses StatisticsCustomerProductWeek
 */
class StatisticsCustomerProductWeek extends Command
{
    protected $signature = 'pre_test_manage_monitor:statistics_customer_product_week';

    const CRON_NAME = '[统计所有客户周调用情况]';

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {}

    private function main() {
        // 近五周调用量
        $this->runNumberData();

        return true;
    }

    /**
     * @return true
     */
    private function runNumberData() {
        $iNow = time();
//        $iNow = 1688140800; // todo  2023-07-01
        $iLaterDay = strtotime('+3 days', $iNow); // 3天后日期
        $iThisThursday = strtotime("thursday this week", $iLaterDay); // 3天后日期 所在周 周四
        $iThisFriday = strtotime("friday this week", $iLaterDay); // 3天后日期 所在周 周五
        $iStartTime = strtotime('-6 weeks', $iThisFriday); // 5周前 周五
        $iEndTime = strtotime('-1 weeks', $iThisThursday); // 1周前 周四

        $sStartDate = date('Ymd', $iStartTime);
        $sEndDate = date('Ymd', $iEndTime);

        $iStartWeek = date('oW', strtotime('+3 days', $iStartTime)); // week_id 向右偏移3天
        $iEndWeek = date('oW', strtotime('+3 days', $iEndTime)); // week_id 向右偏移3天

        $iId = 0;
        do {
            $aCond = [
                ['id', '>', $iId],
            ];
            $aDataList = MonitorCustomerProduct::aGetDataByCond($aCond, ['*'], null, 50, 'id', 'ASC');
            if (empty($aDataList)) {
                break;
            }
            $aId = array_column($aDataList, 'id');
            $iId = max($aId);

            $aCustomerList = array_column($aDataList, 'customer_id');
            $aCusApiMap = $this->aGetApiKeyList($aCustomerList);

            foreach ($aDataList as $aItem) {
                $aApiKeyList = $aCusApiMap[$aItem['customer_id']] ?? [];
                if (empty($aApiKeyList)) {
                    continue;
                }

                if (!$aItem['fee_time']) {
                    // 表   s, e
                    list($aNumberMap, $aIncomeMap) = $this->getDataByTable($aApiKeyList, $aItem['product_id'], $sStartDate, $sEndDate);
                } else {
                    if ($aItem['fee_time'] < $iStartTime) {
                        // 接口 s, e
                        list($aNumberMap, $aIncomeMap) = $this->getDataByInterface($aItem['customer_id'], $aApiKeyList, $aItem['product_id'], $sStartDate, $sEndDate);
                    } else {
                        // 表 s, f
                        $sFeeDate = date('Ymd', $aItem['fee_time']);
                        list($aTableNumber, $aTableIncome) = $this->getDataByTable($aApiKeyList, $aItem['product_id'], $sStartDate, $sFeeDate);
                        // 接口 f, e
                        list($aInterfaceNumber, $aInterfaceIncome) = $this->getDataByInterface($aItem['customer_id'], $aApiKeyList, $aItem['product_id'], $sFeeDate, $sEndDate);

                        $aNumberMap = $aInterfaceNumber + $aTableNumber;
                        $aIncomeMap = $aInterfaceIncome + $aTableIncome;
                    }
                }

                $aWeekMap = [];
                for ($i = $iStartTime; $i <= $iEndTime; $i += 86400) {
                    $d = date('Ymd', $i);
                    $iWeek = date('oW', strtotime('+3 days', $i));

                    if (!isset($aWeekMap[$iWeek])) {
                        $aWeekMap[$iWeek] = [
                            'number' => $aNumberMap[$d] ?? 0,
                            'income' => $aIncomeMap[$d] ?? 0,
                        ];
                    } else {
                        $aWeekMap[$iWeek]['number'] += $aNumberMap[$d] ?? 0;
                        $aWeekMap[$iWeek]['income'] = bcadd($aWeekMap[$iWeek]['income'], $aIncomeMap[$d] ?? 0);
                    }
                }

                $iMonitorId = $aItem['id'];
                // 删除
                MonitorWeekStatistics::iDeleteByMonitorAndWeek($iMonitorId, $iStartWeek, $iEndWeek);
                // 新增
                $aInsertList = [];
                foreach ($aWeekMap as $iWeekId => $aData) {
                    $aInsertList[] = [
                        'monitor_id' => $iMonitorId,
                        'week_id' => $iWeekId,
                        'number' => $aData['number'] ?? 0,
                        'income' => $aData['income'] ?? 0,
                    ];
                }
                if ($aInsertList) {
                    MonitorWeekStatistics::batchInsert($aInsertList);
                }
            }
        } while (true);

        return true;
    }

    /**
     * @param $aApikey
     * @param $iProductId
     * @param $sStartDate
     * @param $sEndDate
     * @return array
     */
    private function getDataByTable($aApikey = [], $iProductId = 0, $sStartDate = '', $sEndDate = '') {
        if (empty($aApikey) || empty($iProductId) || (empty($sStartDate) && empty($sEndDate))) {
            return [];
        }
        $aDataList = StatisticsCustomerUsage::aGetProductEachDayDataByApiKey($aApikey, $iProductId, $sStartDate, $sEndDate);


        $aDateNumberMap = array_column($aDataList, 'success', 'date');
        $aDateIncomeMap = [];

        return [$aDateNumberMap, $aDateIncomeMap];
    }

    /**
     * @param $sCustomerId
     * @param $aApikey
     * @param $iProductId
     * @param $sStartDate
     * @param $sEndDate
     * @return array
     */
    private function getDataByInterface($sCustomerId, $aApikey = [], $iProductId = 0, $sStartDate = '', $sEndDate = '') {
        if (empty($aApikey) || empty($iProductId) || (empty($sStartDate) && empty($sEndDate))) {
            return [];
        }

        $iDimension = StatDefine::INCOME_DIMENSION_DATE_CUSTOMER;
        $aDataList = (new MainRepository())->getBaseIncome($iDimension, $sStartDate, $sEndDate, [$sCustomerId], $aApikey, [], [$iProductId], ['is_query_month_data' => 0]);

        $aDateNumberMap = [];
        $aDateIncomeMap = [];
        if ($aDataList['data']) {
            $aDateNumberMap = array_column($aDataList['data'], 'number', 'date');
            $aDateIncomeMap = array_column($aDataList['data'], 'income', 'date');

        }

        return [$aDateNumberMap, $aDateIncomeMap];
    }

    /**
     * @param $aCustomerList
     * @return array
     */
    private function aGetApiKeyList($aCustomerList) {
        $aList = Account::getListByCustomerIds($aCustomerList);

        $aMap = [];
        foreach ($aList as $aItem) {
            $aMap[$aItem['customer_id']][] = $aItem['apikey'];
        }

        return $aMap;
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
}