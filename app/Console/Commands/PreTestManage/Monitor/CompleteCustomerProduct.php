<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestMonitor;
use App\Define\StatDefine;
use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ConfigPriceCustomer;
use App\Models\Customer;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\PreTestManage\MonitorWeekStatistics;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use App\Repositories\Income\MainRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *  补全监控数据
 * php artisan pre_test_manage_monitor:complete_customer_product
 * @uses CompleteCustomerProduct
 */
class CompleteCustomerProduct extends Command
{
    protected $signature = 'pre_test_manage_monitor:complete_customer_product';

    const CRON_NAME = '补全监控数据';

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . ", 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $msg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $this->output->warning($msg);

        }
    }

    private function setParam()
    {
    }

    private function main()
    {
        $this->runBaseData();
        return true;
    }

    /**
     * @return true
     */
    private function runBaseData()
    {
        $fatherIdsWithoutProduct = $this->aGetFatchIdWithoutProductId();
        $aCond = [
            ['end_time', '>', time()],
            ['status', '=', 1],
            ['product_id', 'in', $fatherIdsWithoutProduct],
        ];
        $aField = [
            'account_id',
            'product_id',
            'create_at',
            'end_time',
        ];
        $aAccountProductList = AccountProduct::aGetDataByCond($aCond, $aField);
        if (empty($aAccountProductList)) {
            return true;
        }
        $accountids = array_column($aAccountProductList, 'account_id');
        $accountInfos = Account::getListByAccountIds($accountids);
        $accountInfoMap = array_column($accountInfos, null, 'account_id');
        $aInsertList = [];
        foreach ($aAccountProductList as $accountProduct) {
            $accountId = $accountProduct['account_id'];
            $productId = $accountProduct['product_id'];
            $createAt = $accountProduct['create_at'];
            $endTime = $accountProduct['end_time'];
            if (!isset($accountInfoMap[$accountId])) {
                continue;
            }
            $accountInfo = $accountInfoMap[$accountId];
            $index = $productId . '_' . $accountInfo['customer_id'];

            if (isset($aInsertList[$index])) {
                $existing = &$aInsertList[$index];
                $existing['activate_time'] = min($existing['activate_time'], $createAt);
                $existing['activate_end_time'] = max($existing['activate_end_time'], $endTime);
            } else {
                $aInsertList[$index] = [
                    'customer_id' => $accountInfo['customer_id'],
                    'group_id' => $accountInfo['group_id'],
                    'product_id' => $productId,
                    'father_id' => $productId,
                    'activate_time' => $createAt,
                    'activate_end_time' => $endTime,
                    'docking_status' => MonitorCustomerProduct::DOCKING_STATUS_DEFAULT,
                ];
            }
        }
        if ($aInsertList) {
            MonitorCustomerProduct::batchInsert(array_values($aInsertList));
        }
    }


    /**
     *  查询没有子产品主产品id
     * @return array
     */
    private function aGetFatchIdWithoutProductId()
    {
        $aCond = [
            ['father_id', '=', 0],
        ];
        $aField = ['product_id'];
        $allFatherIdsMap = Product::aGetDataByCond($aCond, $aField); //查询主产品
        $allFatherIds = array_column($allFatherIdsMap, 'product_id');
        $aCond = [
            ['father_id', '!=', 0],
        ];
        $aField = [DB::raw('DISTINCT `father_id`')];
        $fatherIdsWithProductMap = Product::aGetDataByCond($aCond, $aField); //查询有子产品的主产品
        $fatherIdsWithProduct = array_column($fatherIdsWithProductMap, 'father_id');

        $fatherIdsWithoutProductId = array_diff($allFatherIds, $fatherIdsWithProduct);

        return $fatherIdsWithoutProductId;
    }


}