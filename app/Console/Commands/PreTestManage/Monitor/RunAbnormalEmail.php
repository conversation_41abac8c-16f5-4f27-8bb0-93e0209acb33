<?php

namespace App\Console\Commands\PreTestManage\Monitor;

use App\Define\PreTestManage;
use App\Mail\PreTestMonitorCustomerAbnormal;
use App\Models\CompanyType;
use App\Models\Customer;
use App\Models\Dept;
use App\Models\PreTestManage\MonitorCustomerProduct;
use App\Models\Product;
use App\Models\SystemUser;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

/**
 * 日报-邮件提醒:计算售前客户开通产品异常状态
 * php artisan pre_test_manage_monitor:run_abnormal_email
 * @uses RunAbnormalEmail
 */
class RunAbnormalEmail extends Command
{
    protected $signature = 'pre_test_manage_monitor:run_abnormal_email;';

    const CRON_NAME = '[日报-邮件提醒:计算售前客户开通产品异常状态]';

    /**
     * 对接异常状态 : 提醒时长限制
     * @var int[]
     */
    private $aTimeDiffLimit = [
        MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_1 => 30,
        MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_2 => 10,
        MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_3 => 15,
        MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_5 => 0,
    ];

    /**
     * 客户类型中,银行类
     * @var array
     */
    private $aBankType = [];

    /**
     * 产品信息
     * @var array
     */
    private $aFatherMap = [];
    private $aProductMap = [];

    /**
     * 销售组员信息
     * @var array
     */
    private $aSalesManMap = [];
    /**
     * 部门 : 销售组员[]
     * @var array
     */
    private $aDeptSalesmanList = [];
    /**
     * 部门信息
     * @var array
     */
    private $aDeptId = [];
    private $aDeptMap = [];
    private $aDeptGatherConf = [];

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->aBankType = CompanyType::getChildType(1);
        $aFatherList = Product::getFatherProducts();
        $this->aFatherMap = array_column($aFatherList, 'product_name', 'product_id');
        $aProductList = Product::getAllSubProduct(['product_id', 'product_name']);
        $this->aProductMap = array_column($aProductList, 'product_name', 'product_id');
        $this->aDeptId = [
            'DEPT2019061815103928', // 华东非银部
            'DEPT2019061815114998', // 华南非银部
            'DEPT2019061815085572', // 华北非银部
            'DEPT2023070615053952', // 华东银行部
            'DEPT2024031311512954', // 华南银行部
            'DEPT2023070615055818', // 华北银行部
        ];
        $aSalesManList = SystemUser::getUserInfoByDeptIdList($this->aDeptId);
        foreach ($aSalesManList as $aSalesMan) {
            $this->aSalesManMap[$aSalesMan['username']] = $aSalesMan;
            $this->aDeptSalesmanList[$aSalesMan['dept_id']][] = $aSalesMan['username'];
        }
        $this->aDeptMap = Dept::getDeptsInfo();
        $this->aDeptGatherConf = [
            '非银销售部' => [
                'DEPT2019061815103928',
                'DEPT2019061815114998',
                'DEPT2019061815085572',
            ],
            '银行销售部' => [
                'DEPT2023070615053952',
                'DEPT2024031311512954',
                'DEPT2023070615055818',
            ],
        ];
    }

    /**
     * @return true
     */
    private function main() {
        // 查询异常数据
        $aSalesManAbnormalList = $this->getAbnormalData();
        // 灰度中数据
        $aSalesManGrayList = $this->getGrayData();
        // 查询转化数据
        $aSalesManRateList = $this->getRateData();

        if (empty($aSalesManAbnormalList) && empty($aSalesManRateList)) {
            return true;
        }
        // 邮件发送
        $this->sendEmail($aSalesManAbnormalList, $aSalesManRateList, $aSalesManGrayList);

        return true;
    }

    /**
     * @return array
     */
    private function getAbnormalData() {
        $aCond = [
            ['activate_time', '>=', strtotime('2025-01-01 00:00:00')],
            ['activate_status', '=', 1],
            ['docking_status', 'in', [
                MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_1,
                MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_2,
                MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_3,
                MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_5,
            ]],
        ];
        $aDataList = MonitorCustomerProduct::aGetDataListByCond($aCond);
        if (empty($aDataList)) {
            return [];
        }

        $aCustomerId = array_unique(array_column($aDataList, 'customer_id'));
        $aCustomerList = Customer::getCustomerByIdList($aCustomerId);
        $aCustomerMap = array_column($aCustomerList, null, 'customer_id');

        $aSalesManAbnormalList = [];
        foreach ($aDataList as $aData) {
            $iDockingStatus = $aData['docking_status'];
            $sCustomerId = $aData['customer_id'];
            $aCustomer = $aCustomerMap[$sCustomerId] ?? [];
            $sSalesman = $aCustomer['salesman'] ?? '';
            $aSalesMan = $this->aSalesManMap[$sSalesman] ?? [];
            if (empty($aCustomer) || empty($sSalesman) || empty($aSalesMan)) {
                continue;
            }

            $iMulti = 1;
            if (in_array($aCustomer['type'], $this->aBankType)) {
                $iMulti = 2;
            }
            $iTimeDiffLimit = ($this->aTimeDiffLimit[$iDockingStatus] ?? 0) * $iMulti;
            if ($aData['time_diff'] < $iTimeDiffLimit) {
                continue;
            }

            $aSalesManAbnormalList[$sSalesman][$iDockingStatus][] = [
                'customer' => $aCustomer['name'],
                'customer_level' => $aCustomer['level_scale_income'] ?: '-',
                'father_name' => $this->aFatherMap[$aData['father_id']] ?? '',
                'product_name' => $this->aProductMap[$aData['product_id']] ?? '',
                'activate_time' => date('Y-m-d', $aData['activate_time']),
                'expect_online_time' => $aData['expect_online_time'] ? date('Y-m-d', $aData['expect_online_time']) : '待确认',
                'expect_online_time_sort' => $aData['expect_online_time'],
                'time_diff' => $aData['time_diff'],
                'call_days' => $aData['call_days'],
                'call_number' => $aData['call_number'],
                'daily_number' => (string)(int)($aData['call_days'] ? ($aData['call_number'] / $aData['call_days']) : 0),
                'salesman' => $aSalesMan['realname'] ?? $sSalesman,
                'dept' => $this->aDeptMap[$aSalesMan['dept_id']]['dept_name'] ?? $sSalesman,
                'dept_id' => $aSalesMan['dept_id'],
            ];
        }

        return $aSalesManAbnormalList;
    }

    /**
     * @return array
     */
    private function getGrayData() {
        /*
            1、当前时间介于灰度开始及结束时间，则展示
         * */
        $iNow = time();
        $aCond = [
            ['grayscale_start_time', '>', 0],
            ['grayscale_end_time', '>', 0],
            ['grayscale_start_time', '<=', $iNow],
            ['grayscale_end_time', '>=', $iNow],
            ['activate_status', '=', 1],
        ];
        $aDataList = MonitorCustomerProduct::aGetDataListByCond($aCond);
        $aCustomerId = array_unique(array_column($aDataList, 'customer_id'));
        $aCustomerList = Customer::getCustomerByIdList($aCustomerId);
        $aCustomerMap = array_column($aCustomerList, null, 'customer_id');

        $aMap = [];
        foreach ($aDataList as $aData) {
            $sCustomerId = $aData['customer_id'];
            $aCustomer = $aCustomerMap[$sCustomerId] ?? [];
            $sSalesman = $aCustomer['salesman'] ?? '';
            $aSalesMan = $this->aSalesManMap[$sSalesman] ?? [];
            if (empty($aCustomer) || empty($sSalesman) || empty($aSalesMan)) {
                continue;
            }
            $sSalesman = $aSalesMan['realname'] ?? $sSalesman;
            $sDeptId = $aSalesMan['dept_id'];

            $aMap[$sDeptId][$sSalesman][] = [
                'customer' => $aCustomer['name'],
                'customer_level' => $aCustomer['level_scale_income'] ?: '-',
                'father_name' => $this->aFatherMap[$aData['father_id']] ?? '',
                'product_name' => $this->aProductMap[$aData['product_id']] ?? '',
                'salesman' => $sSalesman,
                'grayscale_start_time' => $aData['grayscale_start_time'] ? date('Y-m-d', $aData['grayscale_start_time']) : '-',
                'grayscale_end_time' => $aData['grayscale_end_time'] ? date('Y-m-d', $aData['grayscale_end_time']) : '-',
                'dept' => $this->aDeptMap[$aSalesMan['dept_id']]['dept_name'] ?? $sSalesman,
                'dept_id' => $aSalesMan['dept_id'],
                'username' => $aSalesMan['username'],
            ];
        }

        $aRet = [];
        foreach ($this->aDeptId as $sDeptId) {
            $aSalesList = $aMap[$sDeptId] ?? [];
            if (empty($aSalesList)) {
                continue;
            }
            foreach ($aSalesList as $aItem) {
                $aRet = array_merge($aRet, $aItem);
            }
        }

        return $aRet;
    }

    /**
     * @return array
     */
    private function getRateData() {
        $aCond = [
            ['activate_time', '>=', strtotime(date('Y') . '-01-01 00:00:00')],
            ['activate_status', '=', 1],
        ];
        $aDataList = MonitorCustomerProduct::aGetDataListByCond($aCond);
        if (empty($aDataList)) {
            return [];
        }

        $aCustomerId = array_unique(array_column($aDataList, 'customer_id'));
        $aCustomerList = Customer::getCustomerByIdList($aCustomerId);
        $aCustomerMap = array_column($aCustomerList, null, 'customer_id');
        $aSalesManList = array_keys($this->aSalesManMap);
        $aSalesManData = [];
        foreach ($aSalesManList as $sSalesMan) {
            $aSalesManData[$sSalesMan] = [
                'total' => 0,
                'success' => 0,
            ];
        }

        foreach ($aDataList as $aData) {
            $sCustomerId = $aData['customer_id'];
            $aCustomer = $aCustomerMap[$sCustomerId] ?? [];
            $sSalesman = $aCustomer['salesman'] ?? '';
            $aSalesMan = $this->aSalesManMap[$sSalesman] ?? [];
            if (empty($aCustomer) || empty($sSalesman) || empty($aSalesMan)) {
                continue;
            }

            $iSuccess = MonitorCustomerProduct::DOCKING_STATUS_NORMAL == $aData['docking_status'] ? 1 : 0;

            $aSalesManData[$sSalesman]['total']++;
            $aSalesManData[$sSalesman]['success'] += $iSuccess;
        }

        $aSalesManRateList = [];
        foreach ($aSalesManData as $sSalesMan => $aRateData) {
            $aSalesMan = $this->aSalesManMap[$sSalesMan] ?? [];
            $aSalesManRateList[$sSalesMan] = [
                'row_span' => 0,
                'col_span' => 0,
                'dept' => $this->aDeptMap[$aSalesMan['dept_id']]['dept_name'] ?? $sSalesMan,
                'dept_id' => $aSalesMan['dept_id'],
                'salesman' => $aSalesMan['realname'],
                'year' => date('Y') . '年',
                'activate_count' => $aRateData['total'],
                'success_count' => $aRateData['success'],
                'rate' => $aRateData['total'] ? round(($aRateData['success'] / $aRateData['total']) * 100, 1) . '%' : '0%',
                'total_flag' => 0,
            ];
        }

        return $aSalesManRateList;
    }

    /**
     * @param $aSalesManAbnormalList
     * @param $aSalesManRateList
     * @param $aSalesManGrayList
     * @return true
     */
    private function sendEmail($aSalesManAbnormalList = [], $aSalesManRateList = [], $aSalesManGrayList = []) {
        list($aAllData, $aDeptData, $aSalesmanEmailMap, $ccConfig) = $this->getEmailConfig();

        // 提醒所有异常&转化
        if ($aAllData) {
            $aEmail = $aAllData;
            $aSalesMan = array_keys($this->aSalesManMap);

            $aEmailData = $this->buildEmailData($aSalesMan, $aSalesManAbnormalList, $aSalesManRateList, $aSalesManGrayList, true, true);

            foreach ($aEmail as $sEmail) {
                $this->email([$sEmail], $aEmailData);
            }
        }
        // 提醒负责部门异常&转化
        if ($aDeptData) {
            foreach ($aDeptData as $sEmail => $aDept) {
                $aEmail = [$sEmail];

                $aSalesMan = [];
                foreach ($aDept as $sDept) {
                    $aSalesMan = array_merge($aSalesMan, $this->aDeptSalesmanList[$sDept]);
                }

                $aEmailData = $this->buildEmailData($aSalesMan, $aSalesManAbnormalList, $aSalesManRateList, $aSalesManGrayList);
                $this->email($aEmail, $aEmailData);
            }
        }
        // 提醒销售个人异常&转化
        if ($aSalesmanEmailMap) {
            $aSalesManGrayMap = [];
            foreach ($aSalesManGrayList as $aItem) {
                $aSalesManGrayMap[$aItem['username']][] = $aItem;
            }

            foreach ($aSalesmanEmailMap as $sUserName => $sEmail) {
                $aEmail = [$sEmail];
                $aSalesMan = [$sUserName];
                $cc = $ccConfig[$sUserName] ?? [];
                $aGrayData = $aSalesManGrayMap[$sUserName] ?? [];
                $aEmailData = $this->buildEmailData($aSalesMan, $aSalesManAbnormalList, $aSalesManRateList, $aGrayData, false);
                $aEmailData['total'] and $this->email($aEmail, $aEmailData, $cc);
            }
        }

        return true;
    }

    /**
     * @return array
     */
    private function getEmailConfig() {
        $aAllData = [
            '<EMAIL>', // 刘勇
            '<EMAIL>', // 王梦思
            '<EMAIL>', // 张韧
            '<EMAIL>', // 刘丽丽
            '<EMAIL>', // 谢小兰
            '<EMAIL>', // 史艳君
//            '<EMAIL>',
        ];
        $aDeptData = [
            '<EMAIL>' => ['DEPT2019061815103928'], // 马彬芳-华东非银部
            '<EMAIL>' => ['DEPT2019061815114998'], // 秦超-华南非银部
            '<EMAIL>' => ['DEPT2019061815085572'], // 赵志辉-华北非银部
            '<EMAIL>' => [
                'DEPT2023070615053952', // 华东银行部
                'DEPT2024031311512954', // 华南银行部
                'DEPT2023070615055818', // 华北银行部
            ], // 潘武星-华东银行部,华北银行部,华南银行部
        ];
        $aSalesmanEmailMap = array_column($this->aSalesManMap, 'email', 'username');
        $cc = [
            'weijian.li' => ['<EMAIL>'],
        ];

        return [
            $aAllData,
            $aDeptData,
            $aSalesmanEmailMap,
            $cc
        ];
    }

    /**
     * @param $aIncludeSalesMan
     * @param $aSalesManAbnormalList
     * @param $aSalesManRateList
     * @param $aSalesManGrayList
     * @param $iNeedTotal
     * @param $iLeader
     * @return array
     */
    private function buildEmailData($aIncludeSalesMan = [], $aSalesManAbnormalList = [], $aSalesManRateList = [], $aSalesManGrayList = [], $iNeedTotal = true, $iLeader = false) {
        $aAbnormalStat = [];
        $aAbnormal_1 = [];
        $aAbnormal_2 = [];
        $aAbnormal_3 = [];
        $aAbnormal_5 = [];
        $aGrayData = $aSalesManGrayList;

        foreach ($aIncludeSalesMan as $sSalesman) {
            $aAbnormalData = $aSalesManAbnormalList[$sSalesman] ?? [];
            $aAbnormal_1 = array_merge($aAbnormal_1, $aAbnormalData[MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_1] ?? []);
            $aAbnormal_2 = array_merge($aAbnormal_2, $aAbnormalData[MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_2] ?? []);
            $aAbnormal_3 = array_merge($aAbnormal_3, $aAbnormalData[MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_3] ?? []);
            $aAbnormal_5 = array_merge($aAbnormal_5, $aAbnormalData[MonitorCustomerProduct::DOCKING_STATUS_ABNORMAL_5] ?? []);
        }
        $this->sortArrayWithZeroLast($aAbnormal_3, 'expect_online_time_sort');
        $this->sortArrayWithZeroLast($aAbnormal_5, 'expect_online_time_sort');
        if ($iLeader) {
            $aAbnormalStat = $this->buildAbnormalStat($aAbnormal_1, $aAbnormal_2, $aAbnormal_3, $aAbnormal_5);
        }
        $aRate = $this->buildRateData($aIncludeSalesMan, $aSalesManRateList, $iNeedTotal, $iLeader);
        $iTotal = count($aAbnormal_1) + count($aAbnormal_2) + count($aAbnormal_3) + count($aAbnormal_5) + $aRate[0]['activate_count'];

        return [
            'abnormal_stat' => $aAbnormalStat,
            'abnormal_1' => $aAbnormal_1,
            'abnormal_2' => $aAbnormal_2,
            'abnormal_3' => $aAbnormal_3,
            'abnormal_5' => $aAbnormal_5,
            'gray_data' => $aGrayData,
            'customer_conversion_rate' => $aRate,
            'mark' => '',
            'total' => $iTotal,
            'is_leader' => (int)$iLeader,
        ];
    }

    /**
     * @param $aAbnormal_1
     * @param $aAbnormal_2
     * @param $aAbnormal_3
     * @param $aAbnormal_5
     * @return array
     */
    private function buildAbnormalStat($aAbnormal_1, $aAbnormal_2, $aAbnormal_3, $aAbnormal_5) {
        $aDeptAbnormal_1Map = $this->buildDeptAbnormalMap($aAbnormal_1);
        $aDeptAbnormal_2Map = $this->buildDeptAbnormalMap($aAbnormal_2);
        $aDeptAbnormal_3Map = $this->buildDeptAbnormalMap($aAbnormal_3);
        $aDeptAbnormal_5Map = $this->buildDeptAbnormalMap($aAbnormal_5);

        $aRet = [];
        $iStat_1 = $iStat_2 = $iStat_3 = $iStat_5 = 0;
        foreach ($this->aDeptGatherConf as $sAreaName => $aDeptIdList) {
            $aArea = [];
            $iTotal_1 = $iTotal_2 = $iTotal_3 = $iTotal_5 = 0;
            foreach ($aDeptIdList as $sDeptId) {
                $iT1 = count($aDeptAbnormal_1Map[$sDeptId] ?? []);
                $iT2 = count($aDeptAbnormal_2Map[$sDeptId] ?? []);
                $iT3 = count($aDeptAbnormal_3Map[$sDeptId] ?? []);
                $iT5 = count($aDeptAbnormal_5Map[$sDeptId] ?? []);

                $iTotal_1 += $iT1;
                $iTotal_2 += $iT2;
                $iTotal_3 += $iT3;
                $iTotal_5 += $iT5;

                $aArea[] = [
                    'row_span' => 0,
                    'col_span' => 0,
                    'area_name' => $sAreaName,
                    'dept_name' => $this->aDeptMap[$sDeptId]['dept_name'],
                    'abnormal_1' => $iT1,
                    'abnormal_2' => $iT2,
                    'abnormal_3' => $iT3,
                    'abnormal_5' => $iT5,
                    'total_flag' => false,
                ];
            }

            $aArea[] = [
                'row_span' => 0,
                'col_span' => 0,
                'area_name' => $sAreaName,
                'dept_name' => '小计',
                'abnormal_1' => $iTotal_1,
                'abnormal_2' => $iTotal_2,
                'abnormal_3' => $iTotal_3,
                'abnormal_5' => $iTotal_5,
                'total_flag' => true,
            ];
            $aArea[0]['row_span'] = count($aArea);

            $aRet = array_merge($aRet, $aArea);

            $iStat_1 += $iTotal_1;
            $iStat_2 += $iTotal_2;
            $iStat_3 += $iTotal_3;
            $iStat_5 += $iTotal_5;
        }

        $aRet[] = [
            'row_span' => 0,
            'col_span' => 2,
            'area_name' => '合计',
            'dept_name' => '',
            'abnormal_1' => $iStat_1,
            'abnormal_2' => $iStat_2,
            'abnormal_3' => $iStat_3,
            'abnormal_5' => $iStat_5,
            'total_flag' => true,
        ];

        return $aRet;
    }

    /**
     * @param $aAbnormalList
     * @return array
     */
    private function buildDeptAbnormalMap($aAbnormalList = []) {
        $aMap = [];
        foreach ($aAbnormalList as $sAbnormal) {
            $aMap[$sAbnormal['dept_id']][] = $sAbnormal;
        }

        return $aMap;
    }

    /**
     * @param $aIncludeSalesMan
     * @param $aSalesManRateList
     * @param $iNeedTotal
     * @param $iLeader
     * @return array
     */
    private function buildRateData($aIncludeSalesMan = [], $aSalesManRateList = [], $iNeedTotal = true, $iLeader = false) {
        $aDeptGroup = [];
        foreach ($aIncludeSalesMan as $sSalesman) {
            $aSalesMan = $this->aSalesManMap[$sSalesman] ?? [];
            $aDept = $this->aDeptMap[$aSalesMan['dept_id']] ?? [];
            $aRateData = $aSalesManRateList[$sSalesman] ?? [];
            if (empty($aSalesMan) || empty($aDept) || empty($aRateData)) {
                continue;
            }
            $aDeptGroup[$aDept['dept_id']][] = $aRateData;
        }
        if (empty($aDeptGroup)) {
            return [];
        }

        $iDeptCount = count($aDeptGroup);
        $iAllTotal = 0;
        $iAllSuccess = 0;

        $aRet = [];
        $aDeptGather = [];
        $aDeptTotalMap = [];

        foreach ($aDeptGroup as $sDeptId => $aGroupData) {
            $iTotal = 0;
            $iSuccess = 0;
            $aDeptData = [];
            foreach ($aGroupData as $aItem) {
                $iTotal += $aItem['activate_count'];
                $iSuccess += $aItem['success_count'];

                $aDeptData[] = $aItem;
                $aDeptGather[$sDeptId][] = $aItem;
            }

            if ($iNeedTotal) {
                $aDeptTotal = [
                    'row_span' => 0,
                    'col_span' => 0,
                    'dept' => $this->aDeptMap[$sDeptId]['dept_name'] ?? $sDeptId,
                    'dept_id' => $sDeptId,
                    'salesman' => '小计',
                    'year' => date('Y') . '年',
                    'activate_count' => $iTotal,
                    'success_count' => $iSuccess,
                    'rate' => $iTotal ? round(($iSuccess / $iTotal) * 100, 1) . '%' : '0%',
                    'total_flag' => 1,
                ];
                if (count($aDeptData) > 1) {
                    $aDeptData[] = $aDeptTotal;
                    $aDeptGather[$sDeptId][] = $aDeptTotal;
                }
                $aDeptTotalMap[$sDeptId] = $aDeptTotal;
            }
            $aDeptData[0]['row_span'] = count($aDeptData);

            $aRet = array_merge($aRet, $aDeptData);
            $iAllTotal += $iTotal;
            $iAllSuccess += $iSuccess;
        }

        if ($iLeader) {
            $aRet = [];

            foreach ($this->aDeptGatherConf as $sDeptName => $aDeptIdList) {
                $iTotal = 0;
                $iSuccess = 0;

                foreach ($aDeptIdList as $sDeptId) {
                    $aDeptData = $aDeptGather[$sDeptId] ?? [];
                    $aDeptData[0]['row_span'] = count($aDeptData);
                    $aRet = array_merge($aRet, $aDeptData);

                    $aDeptTotal = $aDeptTotalMap[$sDeptId] ?? [];

                    $iTotal += $aDeptTotal['activate_count'] ?? 0;
                    $iSuccess += $aDeptTotal['success_count'] ?? 0;
                }

                $aTotal = [
                    'row_span' => 1,
                    'col_span' => 2,
                    'dept' => $sDeptName,
                    'dept_id' => '',
                    'salesman' => '',
                    'year' => date('Y') . '年',
                    'activate_count' => $iTotal,
                    'success_count' => $iSuccess,
                    'rate' => $iTotal ? round(($iSuccess / $iTotal) * 100, 1) . '%' : '0%',
                    'total_flag' => 1,
                ];
                $aRet[] = $aTotal;
            }
        }

        if ($iNeedTotal && $iDeptCount > 1) {
            $aRet[] = [
                'row_span' => 1,
                'col_span' => 2,
                'dept' => '合计',
                'dept_id' => '',
                'salesman' => '',
                'year' => date('Y') . '年',
                'activate_count' => $iAllTotal,
                'success_count' => $iAllSuccess,
                'rate' => $iAllTotal ? round(($iAllSuccess / $iAllTotal) * 100, 1) . '%' : '0%',
                'total_flag' => 1,
            ];
        }

        return $aRet;
    }

    /**
     * @param $aEmail
     * @param $aArrInfo
     * @return mixed
     */
    private function email($aEmail = [], $aArrInfo = [], $cc = []) {
//        $aArrInfo['mark'] = '原始:' . implode(',', $aEmail) . ' | cc:' . implode(',', $cc); // todo
//        $aEmail = ['<EMAIL>', '<EMAIL>'];
//        $cc = ['<EMAIL>'];
//        $aEmail = ['<EMAIL>', '<EMAIL>'];
//        $cc = [];

        return Mail::to($aEmail)->cc($cc)->send(new PreTestMonitorCustomerAbnormal($aArrInfo));
    }

    /**
     * @param $array
     * @param $column
     * @return void
     */
    private function sortArrayWithZeroLast(&$array, $column = '') {
        usort($array, function($a, $b) use ($column) {
            $valA = $a[$column];
            $valB = $b[$column];

            // 处理0值的逻辑
            if ($valA == 0 && $valB == 0) return 0; // 两个都是0，顺序不变
            if ($valA == 0) return 1; // 仅$a是0，$b排前面
            if ($valB == 0) return -1; // 仅$b是0，$a排前面

            // 非0值正常比较
            return $valA <=> $valB;
        });
    }

    /**
     * @param $sMsg
     * @return void
     * @throws Exception
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }
}