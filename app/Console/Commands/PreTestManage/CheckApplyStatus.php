<?php

namespace App\Console\Commands\PreTestManage;

use App\Define\PreTestManage;
use App\Http\Repository\PreTestManage\ApplyRepository;
use App\Models\PreTestManage\ApplyCustomer;
use App\Models\PreTestManage\ApplyFather;
use App\Models\PreTestManage\ApplyList;
use App\Models\PreTestManage\Approval\Approval;
use App\Models\PreTestManage\Approval\ApprovalTransfer;
use App\Models\PreTestManage\Approval\CallStable;
use App\Models\PreTestManage\Approval\ExpectOnline;
use App\Models\PreTestManage\Approval\Grayscale;
use App\Models\PreTestManage\Approval\PreTest;
use App\Models\PreTestManage\BusinessProductData;
use App\Models\Product;
use App\Repositories\FeishuRepository;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 检查工单状态
 * php artisan pre_test_manage:check_apply_status
 * @uses CheckApplyStatus
 */
class CheckApplyStatus extends Command
{
    protected $signature = 'pre_test_manage:check_apply_status;';

    const CRON_NAME = '[检查工单状态]';

    /**
     * @var FeishuRepository
     */
    private $oFeishuPreTestRep = null;

    /**
     * @var BusinessProductData
     */
    protected $oBusinessProduct = null;

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $sNow = date('Y-m-d H:i:s');
            $ts = microtime(true);

            $this->setParam();

            $this->main();

            $te = microtime(true);
            $cost = $te - $ts;
            $msg = sprintf(self::CRON_NAME . " 执行开始时间:%s 耗时:%s", $sNow, $cost);
            Log::info($msg);
            $this->output->success($msg);
        } catch (\Exception $exception) {
            $sErrMsg = $exception->getFile() . ":" . $exception->getLine() . " " . $exception->getMessage();
            $sMsg = self::CRON_NAME . ' 执行失败! ' . $sErrMsg;
            Log::error($sMsg);
            $this->output->warning($sMsg);
            $this->sendFeishuUserMessage($sMsg);
        }
    }

    private function setParam() {
        $this->oFeishuPreTestRep = new FeishuRepository('feishu_pre_test');
        $this->oBusinessProduct = new BusinessProductData();
    }

    /**
     * @param $sMsg
     * @return void
     */
    private function sendFeishuUserMessage($sMsg = '') {
        $sUserId = PreTestManage::MANAGE_USER_ID;
        try {
            $sReceiveIdType = 'user_id';
            $sMsgType = 'text';
            $aContent = [
                'text' => (string)$sMsg
            ];
            $this->oFeishuPreTestRep->send_user_message($sReceiveIdType, $sUserId, $sMsgType, $aContent);
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 飞书消息发送失败, msg: ' . $e->getMessage(), ['user_id' => $sUserId, 'msg' => $sMsg]);
        }
    }

    /**
     * @return true
     */
    private function main() {
        // 售前测试&测试账号申请 回填apikey & 回填customer_id
        $this->checkAccount();

        // 产品建模&定制申请 回填子产品id
        $this->checkModel();

        return true;
    }

    /**
     * @return true
     */
    private function checkAccount() {
        $aCond = [
            ['approval_code', '=', PreTestManage::APPROVAL_CODE_TEST_WITH_ACCOUNT],
            ['instance_status', '=', PreTestManage::INSTANCE_STATUS_PENDING],
        ];
        $aDataList = ApplyList::aGetDataListByCond($aCond);
        if (empty($aDataList)) {
            return true;
        }

        $this->oBusinessProduct = new BusinessProductData();

        foreach ($aDataList as $oApply) {
            // 获取申请信息详情
            $oInstance = $this->getInstanceInfo($oApply->instance_code);
            $sInstanceStatus = $oInstance->getStatus();
            // [异常数据] 不处理
            if (is_null($sInstanceStatus)) {
                continue;
            } else if (PreTestManage::INSTANCE_STATUS_PENDING == $sInstanceStatus) {
                $this->handleDataForApikey($oInstance->transForm());
                $this->handleDataForSampleSize($oInstance->transForm());
            } else if (PreTestManage::INSTANCE_STATUS_APPROVED == $sInstanceStatus) {
                $this->handleDataForCustomerId($oInstance->transForm());
            } else if (in_array($sInstanceStatus, [PreTestManage::INSTANCE_STATUS_CANCELED, PreTestManage::INSTANCE_STATUS_RECALL, PreTestManage::INSTANCE_STATUS_REJECT])) {
                $this->handleDataToAbort($oInstance->transForm());
            }
            $oApply->instance_status = $sInstanceStatus;
            $oApply->save();
        }

        return true;
    }

    /**
     * @param $sInstanceCode
     * @return Approval|CallStable|ExpectOnline|Grayscale|PreTest
     */
    private function getInstanceInfo($sInstanceCode = '') {
        $aResult = [];
        try {
            $aResult = $this->oFeishuPreTestRep->get_approval_info($sInstanceCode);
            if (empty($aResult)) {
                Log::warning(self::CRON_NAME . ' 申请工单信息为空', ['instance_code' => $sInstanceCode]);
            }
        } catch (Exception $e) {
            Log::error(self::CRON_NAME . '获取申请工单信息失败 , msg: ' . $e->getMessage(), ['instance_code' => $sInstanceCode]);
        }

        return ApprovalTransfer::makeApproval($aResult, $this->oBusinessProduct);
    }

    /**
     * @param Approval $oInstance
     * @return bool
     */
    private function handleDataForApikey(Approval $oInstance) {
        $aBaseData = $oInstance->getData();
        $sApikey = trim($aBaseData['apikey'] ?? '');
        if (empty($sApikey)) {
            return false;
        }
        $oApplyCustomer = ApplyCustomer::oGetOneByInstanceCode($oInstance->getInstanceCode());
        if (is_null($oApplyCustomer) || !empty($oApplyCustomer->apikey)) {
            return false;
        }
        $oApplyCustomer->apikey = $sApikey;
        $oApplyCustomer->save();

        return true;
    }

    /**
     * @param Approval $oInstance
     * @return bool
     */
    private function handleDataForSampleSize(Approval $oInstance) {
        $aProductInfo = $oInstance->getProductInfo();

        $aSimpleField = [
            PreTestManage::DEFAULT_SAMPLE_SIZE_ACTUAL,
            PreTestManage::SAMPLE_SIZE_ACTUAL_0,
            PreTestManage::SAMPLE_SIZE_ACTUAL_1,
            PreTestManage::SAMPLE_SIZE_ACTUAL_2,
        ];
        $iHaveActual = false;
        foreach ($aSimpleField as $sField) {
            if (isset($aProductInfo[$sField]) && $aProductInfo[$sField] > 0) {
                $iHaveActual = true;
                break;
            }
        }
        if (!$iHaveActual) {
            return false;
        }
        $oApplyCustomer = ApplyCustomer::oGetOneByInstanceCode($oInstance->getInstanceCode());
        if (is_null($oApplyCustomer)) {
            return false;
        }

        $aMap = $this->aBuildFsProductMap();
        $aFatherList = ApplyFather::aGetListByApplyIdList([$oApplyCustomer->id]);
        $aUpdate = [];
        foreach ($aFatherList as $aFather) {
            $sFsKey = $aMap[$aFather['product_key']] ?? '';
            if (!$sFsKey || !empty($aFather['sample_size'])) {
                continue;
            }
            $sSampleSizeField = PreTestManage::SAMPLE_SIZE_MAP[$sFsKey] ?? PreTestManage::DEFAULT_SAMPLE_SIZE_ACTUAL;

            $aUpdate[] = [
                'id' => $aFather['id'],
                'sample_size' => $aProductInfo[$sSampleSizeField] ?? 0,
            ];
        }
        if ($aUpdate) {
            ApplyFather::batchUpdateData($aUpdate, 'id');
        }

        return true;
    }

    /**
     * @return array
     */
    private function aBuildFsProductMap() {
        $aMap = [];
        $aFsProduct = $this->oBusinessProduct->getFsDataMap();
        foreach ($aFsProduct as $aItem) {
            $aMap[$aItem['product_key']] = $aItem['fs_key'];
            if (!empty($aItem['child'])) {
                foreach ($aItem['child'] as $aSubItem) {
                    $aMap[$aSubItem['product_key']] = $aSubItem['fs_key'];
                }
            }
        }

        return $aMap;
    }

    /**
     * @param Approval $oInstance
     * @return bool
     */
    private function handleDataForCustomerId(Approval $oInstance) {
        $oApplyCustomer = ApplyCustomer::oGetOneByInstanceCode($oInstance->getInstanceCode());
        if (is_null($oApplyCustomer)) {
            return false;
        }
        $aBaseData = $oInstance->getData();
        $isApprovalAccount = $aBaseData['is_approval_account'];
        if (PreTestManage::IS_YES != $isApprovalAccount) {
            $oApplyCustomer->need_account = PreTestManage::IS_NO;
            $oApplyCustomer->save();
            return true;
        }
        $sCustomerId = trim($aBaseData['customer_id'] ?? '');
        $sApikey = trim($aBaseData['apikey'] ?? '');
        $this->bindCustomer($oApplyCustomer, $sCustomerId, $sApikey);

        return true;
    }

    /**
     * @param Approval $oInstance
     * @return bool
     */
    private function handleDataToAbort(Approval $oInstance) {
        $oApplyCustomer = ApplyCustomer::oGetOneByInstanceCode($oInstance->getInstanceCode());
        if (is_null($oApplyCustomer)) {
            return false;
        }
        $oApplyCustomer->status = PreTestManage::STATUS_TEST_ABORT;
        $oApplyCustomer->save();

        ApplyFather::iUpdateByApplyId($oApplyCustomer->id, ['test_status' => PreTestManage::STATUS_TEST_ABORT]);

        return true;
    }

    /**
     * @param ApplyCustomer $oApplyCustomer
     * @param $sCustomerId
     * @param $sApikey
     * @return bool
     */
    private function bindCustomer(ApplyCustomer $oApplyCustomer, $sCustomerId = '', $sApikey = '') {
        $sNow = date("Y-m-d H:i:s");
        $iApplyId = $oApplyCustomer->id;
        if ($sCustomerId) {
            $oApplyCustomer->customer_id = $sCustomerId;
            $oApplyCustomer->is_call = PreTestManage::IS_NO;
            $oApplyCustomer->status = PreTestManage::STATUS_ACCESS_DONE;
            $oApplyCustomer->bind_time = $sNow;
        }
        if (empty($oApplyCustomer->apikey)) {
            $oApplyCustomer->apikey = $sApikey;
        }
        $oApplyCustomer->need_account = PreTestManage::IS_NO;
        $oApplyCustomer->updated_at = $sNow;
        $oApplyCustomer->save();

        if ($sCustomerId) {
            $aUpdateData = [
                'bind_time' => $sNow,
                'updated_at' => $sNow,
            ];
            ApplyFather::iUpdateByApplyId($iApplyId, $aUpdateData);
        }

        return true;
    }

    /**
     * @return true
     */
    private function checkModel() {
        $aCond = [
            ['approval_code', '=', PreTestManage::APPROVAL_CODE_TEST_WITH_MODEL],
            ['instance_status', '=', PreTestManage::INSTANCE_STATUS_PENDING],
        ];
        $aDataList = ApplyList::aGetDataListByCond($aCond);
        if (empty($aDataList)) {
            return true;
        }

        $this->oBusinessProduct = new BusinessProductData();

        foreach ($aDataList as $oApply) {
            // 获取申请信息详情
            $oInstance = $this->getInstanceInfo($oApply->instance_code);
            $sInstanceStatus = $oInstance->getStatus();
            // [异常数据] 不处理
            if (is_null($sInstanceStatus) || PreTestManage::INSTANCE_STATUS_PENDING == $sInstanceStatus) {
                continue;
            } else if (PreTestManage::INSTANCE_STATUS_APPROVED == $sInstanceStatus) {
                $bRet = $this->handleDataForModel($oInstance->transForm());
                if (!$bRet) {
                    continue;
                }
            } else if (in_array($sInstanceStatus, [PreTestManage::INSTANCE_STATUS_CANCELED, PreTestManage::INSTANCE_STATUS_RECALL, PreTestManage::INSTANCE_STATUS_REJECT])) {
                $this->handleDataToAbort($oInstance->transForm());
            }
            $oApply->instance_status = $sInstanceStatus;
            $oApply->save();
        }

        return true;
    }

    /**
     * @param Approval $oInstance
     * @return bool
     */
    private function handleDataForModel(Approval $oInstance) {
        $aExtendData = $oInstance->getExtendList();
        $sProductIdList = trim($aExtendData['product_id_list'] ?? '');
        if (empty($sProductIdList)) {
            return true;
        }
        $aProductIdList = array_map('intval', array_filter(array_map('trim', explode(',', $sProductIdList))));
        $aProductId = array_values(array_unique($aProductIdList));
        $aProductDataList = Product::getProductListByProductIds($aProductId, ['product_id', 'product_name', 'father_id']);
        if (count($aProductId) !== count($aProductDataList)) {
            // 异常情况
            $sInstanceCode = $oInstance->getInstanceCode();
            $sMsg = self::CRON_NAME . ' 定制产品工单: [' . $sInstanceCode. ' ], 子产品ID(' . $aExtendData['product_id_list'] . ')有异常';
            $this->sendFeishuUserMessage($sMsg);

            return false;
        }
        $aProductList = [];
        foreach ($aProductDataList as $aProductId) {
            $aProductList[] = [$aProductId['father_id'], $aProductId['product_id']];
        }

        $oApplyCustomer = ApplyCustomer::oGetOneByInstanceCode($oInstance->getInstanceCode());
        if (is_null($oApplyCustomer)) {
            return true;
        }
        $iApplyId = $oApplyCustomer->id;

        $aCond = [
            ['apply_id', '=', $iApplyId]
        ];
        $aFatherList = ApplyFather::aGetDataByCond($aCond);
        $aFather = current($aFatherList);
        $iApplyFatherId = $aFather['id'];

        try {
            (new ApplyRepository())->bindProductFunc($iApplyId, $iApplyFatherId, $aProductList);

            $this->sendFeishuUserMessage(json_encode([
                'message' => '建模工单-上线产品信息',
                'company_short_name' => $oApplyCustomer->company_short_name,
                'salesman' => $oApplyCustomer->salesman,
                'product_list' => $aProductList,
                'apply_id' => $iApplyId,
                'apply_father_id' => $iApplyFatherId,
            ]));
        } catch (\Exception $e) {
            Log::error(self::CRON_NAME . ' 绑定产品失败, ' . $e->getMessage(), ['apply_id' => $iApplyId, 'apply_father_id' => $iApplyFatherId, 'product_list' => $aProductList]);
            return false;
        }

        return true;
    }
}