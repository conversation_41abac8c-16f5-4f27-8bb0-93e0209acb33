<?php

namespace App\Console\Commands\BaseCheck;

use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Console\Command;

class PullChannelStatDataCommand extends Command
{
    use CurlTrait;
    protected $signature = 'pull:channel_stat 
    {--date= : 日期}';

    protected $description = '拉取指定日期渠道统计';

    //数据接收接口
//    const BASE_CHECK_GET_CHANNEL_STAT_URL = 'http://ning-test-base-check.dianhua.cn/getChannelStat';
    const BASE_CHECK_GET_CHANNEL_STAT_URL = 'http://base-check.dianhua.cn/getChannelStat';

    const BACK_API_PUSH_CHANNEL_STAT_URL = 'http://back-api.dianhua.cn/statistics/interface/sendUsage';
//    const BACK_API_PUSH_CHANNEL_STAT_URL = 'http://ning-test-back-api.dianhua.cn/statistics/interface/sendUsage';

    public function handle()
    {
        $date = $this->input->getOption('date') ?: date('Ymd');
        $date = date('Ymd', strtotime($date));
        $this->pullDatas($date);
        //推送前一天的量
        if (time() <= strtotime(date('Y-m-d 00:10:00'))) {
            $this->pullDatas(date("Ymd",strtotime("-1 day")));
        }
        exit('执行成功' . PHP_EOL);
    }

    protected function pullDatas($date)
    {
        $pullData = $this->get(self::BASE_CHECK_GET_CHANNEL_STAT_URL, ['date' => $date]);

        if(empty($pullData) || $pullData['status'] != 0){
            exit('获取数据失败' . PHP_EOL);
        }
        if (empty($pullData['data'])) {
            exit('暂无统计数据' . PHP_EOL);
        }
        $redis = new CacheDriverFacade();
        $mail = [];
        foreach ($pullData['data'] as $data) {
            //校验产品数据
            $productId = array_get($data, 'product_id');
            $productKey = array_get($data, 'product_key');
            $onlineProductKey = $redis->getProductKeyByProductId($productId);
            if (!$productId || !$productKey || $onlineProductKey != $productKey) {
                continue;
            }
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($productId);
            $father_id = $father_id ? $father_id : $productId;//401或501等其它产品父产品既是子产品
            if ($father_id != 3000) {
                continue;
            }
            //校验日期
            $date = array_get($data, 'date');
            if (!$date || !preg_match('/^\d{8}$/', $date)) {
                continue;
            }

            //获取传递的数据
            $node       = $data['node'] ?: 'india';
            $product_id = $data['product_id'];
            $date       = $data['date'];
            $tmpData       = array_get($data, 'data', []);
            if (empty($tmpData)) {
                continue;
            }
            $tmp = [];
            foreach ($tmpData as $value) {
                $tmp[$value['apikey']][] = $value['data'];
            }
            $sendItemDatas = [];
            foreach ($tmp as $tmpApikey => $tmparr) {
                $sendItemDatas[] = [
                    'apikey' => $tmpApikey,
                    'data' => $tmparr,
                ];
            }

            $postDatas = [
                'product_key' => $onlineProductKey,
                'product_id' => $product_id,
                'node' => $node,
                'date' => $date,
                'data' => $sendItemDatas,
            ];

            for ($i = 0; $i < 2; $i++) {
                // 推送渠道统计到后台
                $res = $this->postRawJson(self::BACK_API_PUSH_CHANNEL_STAT_URL, $postDatas);
                if (isset($res['code']) && $res['code'] == 0) {
                    break;
                }
                if ($i == 1) {
                    $success = isset($res['success']) ? $res['success'] : true;
                    $code = $success ? 200 : $res['code'];
                    $message = $success ? 'ok' : $res['msg'];
                    $status = isset($res['status']) ? $res['status'] : '';
                    $msg = isset($res['msg']) ? $res['msg'] : 'ok';
                    $mail[] = [$product_id, $code, $message, $status, $msg];
                }
            }
        }
        if ($mail) {
            $mailService = new SendMailService();
            $title = ['产品ID', 'HTTP状态码', 'HTTP状态码描述', '接口错误码', '接口错误描述'];
            $html = self::mailTemplate($title, $mail);
            $mailService->setFromName('金融后台项目组')
                ->setAddressee([['email' => '<EMAIL>']])
                ->setSubject('海外基础版核验——推送渠道统计邮件预警')
                ->setContent($html)
                ->send();
        }
    }
    /**
     * @param $title
     * @param $data
     * @return mixed
     * egg:
     *  $title = ['标题1', '标题2', '标题3'];
     *  $data = [
     *           ['内容1', '内容2', '内容3'],
     *           ['内容1', '内容2', '内容3'],
     *           ['内容1', '内容2', '内容3'],
     *            //......
     *      ];
     */
    public static function mailTemplate($title, $data)
    {
        $table_style = 'width="90%"  border="1" bordercolor="#000" cellspacing="0" cellpadding="10"';
        $tr_style = 'style="background:#D9D9D9"';
        $_table = '<table ' . $table_style . '>content</table>';

        //标题
        $_td = '';
        foreach ($title as $td) {
            $_td .= '<td>' . $td . '</td>';
        }
        $_tr = '<tr ' . $tr_style . '>' . $_td . '</tr>' . PHP_EOL;

        //内容
        foreach ($data as $row) {
            $_td = '';
            foreach ($row as $td) {
                $_td .= '<td>' . $td . '</td>';
            }
            $_tr .= '<tr>' . $_td . '</tr>' . PHP_EOL;
        }
        return str_replace('content', $_tr, $_table);
    }



}