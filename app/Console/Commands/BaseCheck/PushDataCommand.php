<?php

namespace App\Console\Commands\BaseCheck;

use App\Models\Account;
use App\Models\AccountProductModel;
use App\Models\Product;
use App\Providers\BillCost\CreateBillCost;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Console\Command;

class PushDataCommand extends Command
{
    use CurlTrait;
    protected $signature = 'push:product_account_info 
    {--productIds= : 产品IDs}';

    protected $description = '推送指定产品信息及开通此产品的用户信息';

    protected $productIds = [
        3001,3002,3003
    ];

    //数据接收接口
//    const BASE_CHECK_PRODUCT_ACCOUNT_URL = 'http://ning-test-base-check.dianhua.cn/account';
    const BASE_CHECK_PRODUCT_ACCOUNT_URL = 'http://base-check.dianhua.cn/account';

    public function handle()
    {
        $productIds = $this->input->getOption('productIds') ?: '';
        $productIds = $productIds ? explode(',', $productIds) : $this->productIds;
        $res = $this->getInfos($productIds);

        if (!$res) {
            exit('获取数据异常' . PHP_EOL);
        }

        $postData = ['data' => $res];
        $curlRes = $this->postRawJson(self::BASE_CHECK_PRODUCT_ACCOUNT_URL, $postData);

        if(empty($curlRes) || $curlRes['status'] != 0){
            exit('推送失败' . PHP_EOL);
        }
        exit('推送成功' . PHP_EOL);
    }

    protected function getInfos($productIds)
    {
        $accountProductModel = new AccountProductModel([]);
        $where = ['product_id' => $productIds];
        $res = $accountProductModel->getListV2($where);

        //未查询到数据时，区分情况返回
        if (empty($res)) {
            return false;
        }
        //查询到数据时，根据要求对数据进行组装
        $result = [];
        foreach ($res as $val) {
            $data = [];
            if ($val['data'] != 'null') {
                $data = json_decode($val['data'], true);
            }

            if (!isset($result[$val['apikey']])) {
                $access_ip = [];
                if ($val['access_ip'] != '') {
                    $access_ip = unserialize($val['access_ip']);
                }

                $result[$val['apikey']]['account_info'] = [
                    'account_real_id'       => $val['account_real_id'],
                    'account_name'          => $val['account_name'],
                    'apikey'                => $val['apikey'],
                    'appsecret'             => $val['appsecret'],
                    'end_time'              => $val['account_endtime'],
                    'access_ip'             => $access_ip,
                    'status'                => $val['account_status'],
                    'cid'                   => $val['cid'],
                    'user_agent_number'     => $val['user_agent_number'],
                    'account_concurrency'   => $val['account_concurrency'],
                ];
            }
            $result[$val['apikey']]['product_info'][$val['product_id']] = [
                'product_name'     => $val['product_name'],
                'daily_limit'      => $val['daily_limit'],
                'month_limit'      => $val['month_limit'],
                'year_limit'       => $val['year_limit'],
                'total_limit'      => $val['total_limit'],
                'concurrency'      => $val['concurrency'],
                'end_time'         => $val['end_time'],
                'status'           => $val['status'],
                'limit_start_date' => $val['limit_start_date'],
            ];
            $result[$val['apikey']]['product_info'][$val['product_id']] = array_merge_recursive($result[$val['apikey']]['product_info'][$val['product_id']], $data);

        }

        return $result;
    }
}