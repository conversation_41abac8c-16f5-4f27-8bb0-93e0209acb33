<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ProductAmount;
use Illuminate\Console\Command;

class InitAmountCacheCommand extends Command
{

    /*
     * 缓存天数
     * */
    private $cache_days = 1;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'init-amount:cache';

    /*
     * redis连接
     * */
    private $redis_connection = 'db_backend';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = <<<EOF
    
                            执行时间: 每天凌晨3点执行
                            执行目的: 初始化每天每个待用产品的日月年调用量的redis属性
EOF;

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $status = 1;
        Account::where(compact('status'))
            ->get(['apikey', 'account_id'])
            ->map(function ($item) {
                // 执行缓存
                $this->handleCache($item['apikey'], $item['account_id']);
            });
        $this->info('Well Done!');
    }

    /**
     * 执行缓存
     * @param string $apikey
     * @param string $account_id
     */
    private function handleCache(string $apikey, string $account_id)
    {
        $status = 1;
        AccountProduct::where(compact('status', 'account_id'))
            ->get(['product_id'])
            ->each(function ($item) use ($apikey) {
                // 解决缓存问题
                $this->handleCacheDo($apikey, $item['product_id']);
            });
        $this->info('caching account_id :' . $account_id . '! Please just waiting');
    }

    /**
     * 缓存DO
     * @param string $apikey
     * @param string $product_id
     */
    private function handleCacheDo(string $apikey, string $product_id)
    {
        // 缓存key
        $cache_key = 'product_amount_' . $product_id . '_' . $apikey . '_' . date("Ymd");

        // 缓存信息
        $cache_data = $this->getCacheData(compact('apikey', 'product_id'));

        // do
        app('redis')->connection($this->redis_connection)->hMset($cache_key, $cache_data);
        app('redis')->connection($this->redis_connection)->expire($cache_key, $this->cache_days*86400);
    }

    /**
     * 获取缓存信息
     * @param array $where
     * @return array
     */
    private function getCacheData(array $where)
    {
        $daily_amount = $this->aggregateSumAmount($where, date('Y-m-d'));
        $month_amount = $this->aggregateSumAmount($where, date('Y-m' . '-01'));
        $year_amount = $this->aggregateSumAmount($where, date('Y' . '-01-01'));
        $total_amount = $this->aggregateSumAmount($where, date('0000' . '-01-01'));
        return compact('daily_amount', 'month_amount', 'year_amount', 'total_amount');
    }

    /**
     * 聚合获取某段时间的总和
     * @param array $where
     * @param string $day_format
     * @return int
     */
    private function aggregateSumAmount(array $where, string $day_format): int
    {
        return (int)ProductAmount::where($where)
            ->where('amount_date', '>=', $day_format)
            ->sum('daily_amount');
    }
}
