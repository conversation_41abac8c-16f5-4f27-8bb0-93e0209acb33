<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\LtSendEmail;
use App\Models\BxfChannelPsiMonitor;
use App\Models\EmailConfig;

class LtSendEmailCommand extends Command
{
    /**
     * 命令行执行命令
     */
    protected $signature = 'bxf:liantong-sendemail';
    protected $scene = 'bxf_liantong_sendemail';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $email_model = new EmailConfig();
        $email_config = $email_model->getRecipientsAndCCByScene($this->scene);
        $to_email = array_column($email_config['recipients'], 'address');
        $cc_email = array_column($email_config['cc'], 'address');

        $cday = date('Y-m-d', strtotime('-1 day'));
        $where = compact('cday');
        $info = BxfChannelPsiMonitor::where($where)
            ->get()
            ->toArray();
        $arr_info = [
            'yanzhong' => [],
            'guanzhu' => []
        ];
        if(!empty($info)){
            array_walk($info, function($val) use(&$arr_info){
                if($val['psi_ab'] > 0.25 || $val['psi_ac'] > 0.25){
                    $arr_info['yanzhong'][] = $val;
                }
                if(($val['psi_ab'] <= 0.25 && $val['psi_ac'] <= 0.25) && ($val['psi_ab'] > 0.1 || $val['psi_ac'] > 0.1)){
                    $arr_info['guanzhu'][] = $val;
                }
            });
        }


        //$to_email = '<EMAIL>';
        //$cc_email = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        Mail::to($to_email)
            ->cc($cc_email)
            ->send(new LtSendEmail($arr_info));
    }
}
