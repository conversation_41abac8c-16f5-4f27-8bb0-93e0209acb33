<?php

namespace App\Console\Commands\TransferData;

use App\Define\StatDefine;
use App\Models\BillCostV2;
use App\Models\BillCustomerIncomeV2;
use App\Models\BillProductIncomeV2;
use App\Models\ChannelInterface;
use App\Models\CustomerExpend;
use App\Models\Income\BillIncomeMonthData;
use App\Models\Product;
use App\Models\RerunBillRecord;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Income\MainRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

//更新特殊产品账单收入数据
class UpdateSpecialProductBillIncome extends Command
{
    protected $signature = 'update_special_product_bill_income
	{--start_date= : 起始日期, 格式Ymd}
	{--end_date= : 结束日期, 格式Ymd}
	{--apikey= : 账号apikey（多个以,隔开）}
	{--father_id= : 父产品id（多个以,隔开）}
    {--type= : 默认1根据日期，更新账单的数据；2检测计费配置}
	';

    protected $description = 'soure为朴道的核验前筛和号码融等特殊产品需要对收入进行更新处理';

    protected $start_date;
    protected $end_date;
    protected $apikeys = [];
    protected $father_ids = [];
    protected $product_ids = [];
    protected $current_date;
    protected $repository;
    protected $type;
    protected $sum = 0;
    protected $pd_apikey = 'bf597a7bda394652e25d8c59427add58';
    //朴道推广-02(实际为廊坊银行)
    protected $pd_langfang_apikey = 'd7313adcc2d2789bd9cdcd362f472922';
    protected $pd_langfang_product_ids = [41001, 41002];
    protected $pdPromotionApikey = ['d7313adcc2d2789bd9cdcd362f472922', '63212f6177595966ab7a93166aeb2365', '56d23cb9f85230bbdabca1b8a5aa4378', '420973268bbe3900131d93d9ac634746'];
    protected $pdPromotionProduct = [41001, 41002];

    //要处理的的特殊产品
    protected $productMap = [
        ['father_id' => 200, 'product_ids' => [41001, 41002, 41012], 'show_name' => '核验前筛'],
        ['father_id' => 70000, 'product_ids' => [], 'show_name' => '号码融'],
        ['father_id' => 210, 'product_ids' => [], 'show_name' => '邦信分通信指数'],
    ];

    protected $zhiHuiZuJi_channel_ids = [141];//智慧足迹渠道id
    protected $zhiHuiZuJi_interface_ids = [];//智慧足迹渠道对应接口id
    protected $hmr_inner_channel_ids = [801,802];//号码融内部(羽乐科技)渠道id
    protected $hmr_inner_iids = [];//号码融内部(羽乐科技)渠道对应的接口id
    protected $hmr_inner_801_channel_ids = [801];//号码融内部(羽乐科技)801渠道
    protected $hmr_inner_801_iids = [];//号码融内部(羽乐科技)渠道801对应的接口id
    protected $bxf_zycmcc_channel_ids = [41];//邦信分移动02渠道
    protected $bxf_zycmcc_iids = [];//邦信分移动02渠道41对应的接口id

    public function handle()
    {
        $this->type = !empty($this->option('type')) ? $this->option('type') : 1;

        if($this->type == 1){
            //更新账单数据
            $this->updateBillIncomeData();

        }else{
            //根据重跑账单记录和特殊消耗记录作为条件生成部分月数据
            //$this->createPartDataWithConditions();
        }

        $this->output->success("号码融、前筛升级版、邦信分移动02等特殊账单处理成功");
    }

    public function updateBillIncomeData(){
        //校验参数
        if(!$this->checkParams()){
            return;
        }

        $this->current_date = $this->start_date;
        while ($this->current_date <= $this->end_date){
            //循环处理每天每个父产品对应的数据
            foreach ($this->productMap as $item){
                $this->product_ids = $item['product_ids'];
                $this->doneBillProductIncome($item);
                $this->doneBillCustomerIncome($item);

                //补充测试数据(测试账号或客户测试数据 有成本无收入的情况，需要补充进来)
                $this->supplementTestData($item);
            }

            $this->current_date = date('Ymd', strtotime('+1 days', strtotime($this->current_date)));
        }

    }


    /**
     * 校验参数
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2024/09/11
     *
     * @return void
     **/
    protected function checkParams()
    {
        $options = $this->options();

        $this->start_date = $options['start_date'] ?? '';
        $this->end_date = $options['end_date'] ?? '';
        $apikeys = $options['apikey'] ?? '';
        $father_ids = $options['father_id'] ?? [];
        if(empty($this->start_date) || empty($this->end_date)){
            echo "起始日期和结束日期不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date)) {
            echo "起始日期格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date)) {
            echo "截止日期格式不正确\r\n";
            return false;
        }

        if (!empty($apikeys)) {
            $apikeys       = explode(',', $apikeys);
            $this->apikeys = array_filter($apikeys);
        }

        if (!empty($father_ids)) {
            $father_ids       = explode(',', $father_ids);
            $this->father_ids = $father_ids = array_filter($father_ids);
        }

        foreach ($this->productMap as $key => &$item){
            if(!empty($father_ids) && !in_array($item['father_id'], $father_ids)){
                unset($this->productMap[$key]);
                continue;
            }
            if(empty($item['product_ids'])){
                $item['product_ids'] = Product::getSubPidByFatherId([$item['father_id']]);
            }
        }

//        foreach ($this->productMap as $item){
//            if(!empty($father_ids) && !in_array($item['father_id'], $father_ids)){
//                continue;
//            }
//            if(empty($item['product_ids'])){
//                $product_ids = Product::getSubPidByFatherId([$item['father_id']]);
//                $this->product_ids = array_merge($this->product_ids, $product_ids);
//            }else{
//                $this->product_ids = array_merge($this->product_ids, $item['product_ids']);
//            }
//
//        }

        //提前获取智慧足迹所有接口id
        $ids = ChannelInterface::getInterfaceByChannelIds($this->zhiHuiZuJi_channel_ids);
        $this->zhiHuiZuJi_interface_ids = array_column($ids, 'id');

        //提前获取号码融内部渠道所有接口id
        $ids = ChannelInterface::getInterfaceByChannelIds($this->hmr_inner_channel_ids);
        $this->hmr_inner_iids = array_column($ids, 'id');

        //提前获取号码融内部渠道801所有接口id
        $ids = ChannelInterface::getInterfaceByChannelIds($this->hmr_inner_801_channel_ids);
        $this->hmr_inner_801_iids = array_column($ids, 'id');

        //提前获取邦信分移动02渠道41所有接口id
        $ids = ChannelInterface::getInterfaceByChannelIds($this->bxf_zycmcc_channel_ids);
        $this->bxf_zycmcc_iids = array_column($ids, 'id');

        /*
        if (!empty($options['customer_id'])) {
            $customer_ids       = explode(',', $options['customer_id']);
            $this->customer_ids = array_filter($customer_ids);
        }
        */

        return true;

    }

    protected function doneBillProductIncome($pinfo){
        //获取产品收入列表
        $product_income_list = $this->getBillProductIncomeList($pinfo);

        foreach ($product_income_list as $item){
            if($item['father_id'] == 200) {
                //更新前筛产品收入
                $this->updatePreProductIncome($item);

            }else if($item['father_id'] == 70000){
                //更新号码融产品收入
                $this->updateHmrProductIncome($item);

            }else if($item['father_id'] == 210){
                //更新邦信分移动02产品收入
                if($item['operator'] == 'ZYCMCC'){
                    $this->updateBxfZYCMCCProductIncome($item);
                }

            }

        }

    }

    protected function supplementTestData($pinfo){

        if($pinfo['father_id'] == 200) {
            //补充前筛产品-我司推广数据
            $this->supplementPreProductIncomeByDhbPromotion($pinfo);

            //补充前筛产品-朴道推广数据(这个不用 因为这个已经指定朴道账号了)

        }else if($pinfo['father_id'] == 70000){
            //补充号码融产品-我司推广数据
            $this->supplementHmrProductIncomeByDhbPromotion($pinfo);

            //补充号码融产品-朴道推广数据(这个不用 因为这个已经指定朴道账号了)

        }else if($pinfo['father_id'] == 210){
            //补充邦信分移动02数据
            $this->supplementBxfZYCMCCProductIncome($pinfo);
        }

    }

    protected function doneBillCustomerIncome($pinfo){
        //获取更新后的收入列表
        $product_income_list = $this->getBillProductIncomeIdsGroupByCustomerIncomeId($pinfo);
        if(empty($product_income_list)){
            return;
        }

        $bill_customer_income_ids = array_column($product_income_list, 'bill_customer_income_id');
        $chunk_ids = array_chunk($bill_customer_income_ids, 50);

        //批量 一次一百个
        foreach($chunk_ids as $itemArr){

            $update_income_list = $this->getBillProductIncomeGroupByCustomerIncomeId($itemArr);
            foreach ($update_income_list as $item){
                #todo 添加事务
                BillCustomerIncomeV2::where('id', $item['bill_customer_income_id'])->update(['money' => $item['money'], 'money_agent' => $item['money']]);
            }

        }

    }

    public function getBillProductIncomeList($condition = []){
        $list = BillProductIncomeV2::select(['*'])
            ->where('source', 1)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($this->apikeys), function($query){
                return $query->whereIn('apikey', $this->apikeys);
            })
            ->when(!empty($this->product_ids), function($query) {
                return $query->whereIn('call_product_id', $this->product_ids);
            })
            ->when((isset($condition['father_id']) && $condition['father_id'] == 210), function($query) {
                return $query->where('operator', 'ZYCMCC');
            })
            ->get();

        $result = count($list) ? $list->toArray() : [];

        unset($list);
        return $result;
    }

    public function getBillProductIncomeIdsGroupByCustomerIncomeId($condition = []){
        $select  = [
            'bill_customer_income_id'
        ];

        $list = BillProductIncomeV2::select($select)
            ->where('source', 1)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($this->apikeys), function($query){
                return $query->whereIn('apikey', $this->apikeys);
            })
            ->when(!empty($this->product_ids), function($query) {
                return $query->whereIn('call_product_id', $this->product_ids);
            })
//            ->when((isset($condition['father_id']) && $condition['father_id'] == 210), function($query) {
//                return $query->where('operator', 'ZYCMCC');
//            })
            ->groupBy(['bill_customer_income_id'])
            ->get();

        $result = count($list) ? $list->toArray() : [];

        unset($list);
        return $result;
    }


    public function getBillProductIncomeGroupByCustomerIncomeId($bill_customer_income_ids = []){
        $select  = [
            'bill_customer_income_id',
            DB::raw('SUM(`money`) as money')
        ];

        $list = BillProductIncomeV2::select($select)
            ->where('source', 1)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($bill_customer_income_ids), function($query) use ($bill_customer_income_ids){
                return $query->whereIn('bill_customer_income_id', $bill_customer_income_ids);
            })
            ->groupBy(['bill_customer_income_id'])
            ->get();

        $result = count($list) ? $list->toArray() : [];

        unset($list);
        return $result;
    }

    public function updatePreProductIncome($row){
        //判断是否朴道推广
        $is_pd = $this->isPdPromotion($row['apikey']);
        //判断是否朴道推广-廊坊银行等推广账号
        $is_pd_account = $this->isPdPromotionAccount($row['apikey'], $row['call_product_id']);
        if($is_pd || $is_pd_account){
            //前筛产品-朴道推广
            $this->updatePreProductIncomeByPdPromotion($row);
        }else{
            //前筛产品-我司推广
            $this->updatePreProductIncomeByDhbPromotion($row);
        }

    }

    public function updateHmrProductIncome($row){
        //判断是否朴道推广
        $is_pd = $this->isPdPromotion($row['apikey']);
        if($is_pd){
            //号码融产品-朴道推广
            $this->updateHmrProductIncomeByPdPromotion($row);
        }else{
            //号码融产品-我司推广
            $this->updateHmrProductIncomeByDhbPromotion($row);
        }
    }

    public function updateBxfZYCMCCProductIncome($row){
        //扣点前收入*0.9 - 对应维度成本
        $where['apikey'] = $row['apikey'];
        $where['product_id'] = $row['call_product_id'];
        $where['operator'] = $row['operator'];
        $where['date'] = $row['date'];
        $where['source'] = $row['source'];

        //获取 渠道移动02对应维度成本
        $cost_money = $this->getCostMoneyByCondition(array_merge($where, ['interface_id' => $this->bxf_zycmcc_iids]));

        $income = bcmul($row['money_original'], 0.9, 6);

        $diff_money = bcsub($income, $cost_money, 6);
        /* $diff_money会有负数的情况
        if($row['apikey'] == '8b586306bfe8fc4246e5d1a7dd176a1d'){
           echo $row['date']."\t".$cost_money.PHP_EOL;
        }
        */

        #todo 添加事务
        BillProductIncomeV2::where('id', $row['id'])->update(['money' => $diff_money, 'money_finance' => $diff_money, 'money_agent' => $diff_money]);
    }

    //前筛产品-朴道推广
    public function updatePreProductIncomeByPdPromotion($row){
        //时长：(对应维度计费量 - 智慧足迹对应维度的成本用量)*0.04
        //状态：(对应维度计费量 - 智慧足迹对应维度的成本用量)*0.03
        $product_id = $row['call_product_id'];
        if($product_id == 41001){
            $pirce = 0.04;
            $interface_id = 638;
        }else if($product_id == 41002){
            $pirce = 0.03;
            $interface_id = 639;
        }else{
            return;
        }

        $number = $row['number'];//计费用量

        $where['apikey'] = $row['apikey'];
        $where['product_id'] = $product_id;
        $where['operator'] = $row['operator'];
        $where['date'] = $row['date'];
        $where['source'] = $row['source'];
        $where['interface_id'] = [$interface_id];
        //获取智慧足迹对应维度的成本用量
        $cost_number = $this->getCostNumberByCondition($where);

        $diff_number = $number - $cost_number;
        if($diff_number <= 0){
            #todo 报警
            return;
        }

        $money = bcmul($diff_number, $pirce, 6);

        #todo 添加事务
        BillProductIncomeV2::where('id', $row['id'])->update(['money' => $money, 'money_finance' => $money, 'money_agent' => $money]);
    }

    //前筛产品-我司推广
    public function updatePreProductIncomeByDhbPromotion($row){
        //扣点前的收入*0.9 - 对应维度的成本
        $where['apikey'] = $row['apikey'];
        $where['product_id'] = $row['call_product_id'];
        $where['operator'] = $row['operator'];
        $where['date'] = $row['date'];
        $where['source'] = $row['source'];
        $where['interface_id'] = $this->zhiHuiZuJi_interface_ids;
        //获取智慧足迹对应维度的成本
        $cost_money = $this->getCostMoneyByCondition($where);

        $income = bcmul($row['money_original'], 0.9, 6);
        $diff_money = bcsub($income, $cost_money, 6);
        /* $diff_money会有负数的情况
        if($row['apikey'] == '8b586306bfe8fc4246e5d1a7dd176a1d'){
           echo $row['date']."\t".$cost_money.PHP_EOL;
        }
        */

        #todo 添加事务
        BillProductIncomeV2::where('id', $row['id'])->update(['money' => $diff_money, 'money_finance' => $diff_money, 'money_agent' => $diff_money]);
    }

    //号码融产品-朴道推广
    public function updateHmrProductIncomeByPdPromotion($row){
        //号码融内部渠道用量指801、802都为内部
        //号码融内部成本 = 号码融内部渠道801用量*0.05 (其中内部成本只根据801即可；其中801需配计费，802无计费)
        //(客户扣点前的收入 -  对应维度成本(排除号码融内部渠道) - 号码融内部成本) * 0.2  + 号码融内部成本

        $where = [];
        $where['apikey'] = $row['apikey'];
        $where['product_id'] = $row['call_product_id'];
        $where['operator'] = $row['operator'];
        $where['date'] = $row['date'];
        $where['source'] = $row['source'];

        //获取 对应维度成本(排除号码融内部内部)
        $cost_money = $this->getCostMoneyByCondition(array_merge($where, ['filter_iids' => $this->hmr_inner_iids]));

        //获取 号码融内部成本
        $inner_cost_money = $this->getCostMoneyByCondition(array_merge($where, ['interface_id' => $this->hmr_inner_801_iids]));

        //技术成本
        $tech_cost = bcadd($cost_money, $inner_cost_money, 6);

        $diff_money = bcsub($row['money_original'], $tech_cost, 6);

        //技术服务费
        $service_fee = bcmul($diff_money, 0.2, 6);

        $money = bcadd($service_fee, $inner_cost_money, 6);
        //有可能为负值
        if($money < 0){
            #todo 报警
        }

        #todo 添加事务
        BillProductIncomeV2::where('id', $row['id'])->update(['money' => $money, 'money_finance' => $money, 'money_agent' => $money]);
    }

    //号码融产品-我司推广
    public function updateHmrProductIncomeByDhbPromotion($row){
        //号码融内部渠道用量指801、802都为内部
        //号码融内部成本 = 号码融内部渠道801用量*0.05 (其中内部成本只根据801即可；其中801需配计费，802无计费)
        //(客户扣点前的收入 -  对应维度成本(排除号码融内部渠道) - 号码融内部成本) * 0.8  + 号码融内部成本
        $where = [];
        $where['apikey'] = $row['apikey'];
        $where['product_id'] = $row['call_product_id'];
        $where['operator'] = $row['operator'];
        $where['date'] = $row['date'];
        $where['source'] = $row['source'];

        //获取 对应维度成本(排除号码融内部内部)
        $cost_money = $this->getCostMoneyByCondition(array_merge($where, ['filter_iids' => $this->hmr_inner_iids]));

        //获取 号码融内部成本
        $inner_cost_money = $this->getCostMoneyByCondition(array_merge($where, ['interface_id' => $this->hmr_inner_801_iids]));

        //技术成本
        $tech_cost = bcadd($cost_money, $inner_cost_money, 6);

        $diff_money = bcsub($row['money_original'], $tech_cost, 6);

        //技术服务费
        $service_fee = bcmul($diff_money, 0.8, 6);

        $money = bcadd($service_fee, $inner_cost_money, 6);
        //有可能为负值
        if($money < 0){
            #todo 报警
        }

        #todo 添加事务
        BillProductIncomeV2::where('id', $row['id'])->update(['money' => $money, 'money_finance' => $money, 'money_agent' => $money]);
    }

    //判断是否朴道推广账号
    public function isPdPromotion($apikey = ''){
        if($apikey == $this->pd_apikey){
            return true;
        }

        return false;
    }

    public function isPdPromotionForLangfangBank($apikey, $product_id){
        if($apikey == $this->pd_langfang_apikey
            && in_array($product_id, $this->pd_langfang_product_ids)
        ){
            return true;
        }

        return false;
    }

    public function isPdPromotionAccount($apikey, $product_id){
        if(in_array($apikey, $this->pdPromotionApikey)
            && in_array($product_id, $this->pdPromotionProduct)
        ){
            return true;
        }

        return false;
    }

    public function getCostNumberByCondition($params = []){
        $result = BillCostV2::select([DB::raw('SUM(`number`) as number')])
            ->when(isset($params['apikey']), function($query) use ($params){
                return $query->where('apikey', $params['apikey']);
            })
            ->when(isset($params['product_id']), function($query) use ($params){
                return $query->where('product_id', $params['product_id']);
            })
            ->when(isset($params['operator']), function($query) use ($params){
                return $query->where('operator', $params['operator']);
            })
            ->when(isset($params['date']), function($query) use ($params){
                return $query->where('date', $params['date']);
            })
            ->when(isset($params['source']), function($query) use ($params){
                return $query->where('source', $params['source']);
            })
            ->when(isset($params['interface_id']), function($query) use ($params){
                return $query->whereIn('interface_id', $params['interface_id']);
            })
            ->first()
            ->toArray();

        return $result['number'] ?: 0;
    }


    public function getCostMoneyByCondition($params = []){
        $result = BillCostV2::select([DB::raw('SUM(`money_original`) as money')])
            ->when(isset($params['apikey']), function($query) use ($params){
                return $query->where('apikey', $params['apikey']);
            })
            ->when(isset($params['product_id']), function($query) use ($params){
                return $query->where('product_id', $params['product_id']);
            })
            ->when(isset($params['operator']), function($query) use ($params){
                return $query->where('operator', $params['operator']);
            })
            ->when(isset($params['date']), function($query) use ($params){
                return $query->where('date', $params['date']);
            })
            ->when(isset($params['source']), function($query) use ($params){
                return $query->where('source', $params['source']);
            })
            ->when(isset($params['interface_id']), function($query) use ($params){
                return $query->whereIn('interface_id', $params['interface_id']);
            })
            ->when(isset($params['filter_iids']), function($query) use ($params){
                return $query->whereNotIn('interface_id', $params['filter_iids']);
            })
            ->first()
            ->toArray();

        return $result['money'] ?: 0;
    }


    public function getBillProductIncomeListv2($condition = []){
        $list = BillProductIncomeV2::select(['*'])
            ->where('source', 1)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($this->apikeys), function($query){
                return $query->whereIn('apikey', $this->apikeys);
            })
            ->when(!empty($this->product_ids), function($query) {
                return $query->whereIn('call_product_id', $this->product_ids);
            })
            ->when((isset($condition['father_id']) && $condition['father_id'] == 210), function($query) {
                return $query->where('operator', 'ZYCMCC');
            })
            ->get();

        $result = count($list) ? $list->toArray() : [];

        unset($list);
        return $result;
    }

    //补充前筛产品-我司推广数据
    protected function supplementPreProductIncomeByDhbPromotion($pinfo){
        $pinfo['filter_apikey'] = array_merge([$this->pd_apikey], $this->pdPromotionApikey);
        $pinfo['interface_ids'] = $this->zhiHuiZuJi_interface_ids;
        $income_list = $this->getBillProductIncomeGroupList($pinfo);
        $cost_list = $this->getBillCostGroupList($pinfo);
        if(empty($cost_list)){
            return;
        }

        foreach ($cost_list as $c_item){
            if(!in_array($c_item, $income_list)){
                //添加前筛产品-我司推广特殊消耗
                $this->addCustomerExpendPreProductByDhbPromotion($c_item);
            }
        }

    }


    //补充号码融产品-我司推广数据
    protected function supplementHmrProductIncomeByDhbPromotion($pinfo){
        //号码融内部成本 = 号码融内部渠道801用量*0.05 (其中内部成本只根据801即可；其中801需配计费，802无计费)
        //(客户扣点前的收入 -  对应维度成本(排除号码融内部渠道) - 号码融内部成本) * 0.8  + 号码融内部成本

        //获取对应维度收入
        $pinfo['filter_apikey'] = [$this->pd_apikey];
        $income_list = $this->getBillProductIncomeGroupList($pinfo);

        //对应维度成本(排除号码融内部渠道)
        $cost_list = $this->getBillCostGroupList(array_merge($pinfo, ['filter_interface_ids' => $this->hmr_inner_iids]));

        //获取 号码融内部成本
        $inner_cost_list = $this->getBillCostGroupList(array_merge($pinfo, ['interface_ids' => $this->hmr_inner_801_iids]));

        $cost_list = array_unique(array_merge($cost_list, $inner_cost_list));

        if(empty($cost_list)){
            return;
        }

        foreach ($cost_list as $c_item){
            if(!in_array($c_item, $income_list)){
                //添加号码融产品-我司推广特殊消耗
                $this->addCustomerExpendHmrProductByDhbPromotion($c_item, $pinfo);
            }
        }


    }

    //补充邦信分移动02数据
    protected function supplementBxfZYCMCCProductIncome($pinfo){
        $pinfo['operators'] = ['ZYCMCC'];
        $pinfo['interface_ids'] = $this->bxf_zycmcc_iids;
        $income_list = $this->getBillProductIncomeGroupList($pinfo);
        $cost_list = $this->getBillCostGroupList($pinfo);
        if(empty($cost_list)){
            return;
        }

        foreach ($cost_list as $c_item){
            if(!in_array($c_item, $income_list)){
                //添加邦信分移动02特殊消耗
                $this->addCustomerExpendBxfZYCMCC($c_item, $pinfo);
            }
        }

    }

    //添加号码融产品-我司推广特殊消耗
    protected function addCustomerExpendHmrProductByDhbPromotion($row, $condition = []){
        $arr = explode('_', $row);
        $apikey = $arr[0] ?? '';
        $product_id = $arr[1] ?? '';
        if(empty($apikey) || empty($product_id)){
            return;
        }

        BillCostV2::select(['apikey', 'product_id'])
            ->where('source', 1)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($this->product_ids), function($query) {
                return $query->whereIn('product_id', $this->product_ids);
            })
            ->when((isset($condition['filter_apikey']) && !empty($condition['filter_apikey'])), function($query) use ($condition){
                return $query->whereNotIn('apikey', $condition['filter_apikey']);
            })
            ->when((isset($condition['father_id']) && $condition['father_id'] == 210), function($query) {
//                return $query->where('operator', 'ZYCMCC');
                return $query->whereIn('interface_id', $this->bxf_zycmcc_iids);
            })
            ->when((isset($condition['filter_interface_ids']) && !empty($condition['filter_interface_ids'])), function($query) use ($condition){
                return $query->whereNotIn('interface_id', $condition['filter_interface_ids']);
            })
            ->when((isset($condition['interface_ids']) && !empty($condition['interface_ids'])), function($query) use ($condition){
                return $query->whereIn('interface_id', $condition['interface_ids']);
            })
            ->groupBy(['apikey', 'product_id'])
            ->get();

        //对应维度成本(排除号码融内部渠道)
        $cost = BillCostV2::select([DB::raw('SUM(`money_original`) as money')])
            ->where('source', 1)
            ->whereNotIn('interface_id', $this->hmr_inner_iids)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($apikey), function($query) use($apikey){
                return $query->where('apikey', $apikey);
            })
            ->when(!empty($product_id), function($query) use($product_id){
                return $query->where('product_id', $product_id);
            })
            ->get()
            ->first()
            ->toArray();

        $cost_money = $cost['money'] ?: 0;

        //对应号码融内部成本
        $inner_cost = BillCostV2::select([DB::raw('SUM(`money_original`) as money')])
            ->where('source', 1)
            ->whereIn('interface_id', $this->hmr_inner_801_iids)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($apikey), function($query) use($apikey){
                return $query->where('apikey', $apikey);
            })
            ->when(!empty($product_id), function($query) use($product_id){
                return $query->where('product_id', $product_id);
            })
            ->get()
            ->first()
            ->toArray();

        $inner_cost_money = $inner_cost['money'] ?: 0;

        if($cost_money == 0 && $inner_cost_money == 0){
            return;
        }

        //技术成本
        $tech_cost = bcadd($cost_money, $inner_cost_money, 6);

        $diff_money = bcsub(0, $tech_cost, 6);//对应收入看作0

        //技术服务费
        $service_fee = bcmul($diff_money, 0.8, 6);

        $adjust_money = bcadd($service_fee, $inner_cost_money, 6);

        if($adjust_money == 0){
            return;
        }

        //特殊消耗中的钱通过type判断正负，money都是正的
        if($adjust_money < 0){
            $type = 1;
        }else{
            $type = 2;
        }

        $nowtime = time();
        $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
        $addRow = [
            'customer_id' => $customer_id,
            'start_date' => substr($this->current_date, 0, 6),
            'name' => '特殊账单程序调整',
            'money' => abs($adjust_money),
            'money_finance' => abs($adjust_money),
            'type' => $type,
            'remark' => '涉及日期:'.$this->current_date.':'.'号码融产品-我司推广',
            'profile_show_date' => substr($this->current_date, 0, 4).'-'.substr($this->current_date, 4, 2).'-01',
            'product_id' => $product_id,
            'create_time' => $nowtime,
            'update_time' => $nowtime,
            'source' => 1,
        ];

        CustomerExpend::insert($addRow);
    }

    //添加前筛产品-我司推广特殊消耗
    protected function addCustomerExpendPreProductByDhbPromotion($row){
        $arr = explode('_', $row);
        $apikey = $arr[0] ?? '';
        $product_id = $arr[1] ?? '';
        if(empty($apikey) || empty($product_id)){
            return;
        }

        $result = BillCostV2::select([DB::raw('SUM(`money_original`) as money')])
            ->where('source', 1)
            ->whereIn('interface_id', $this->zhiHuiZuJi_interface_ids)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($apikey), function($query) use($apikey){
                return $query->where('apikey', $apikey);
            })
            ->when(!empty($product_id), function($query) use($product_id){
                return $query->where('product_id', $product_id);
            })
            ->get()
            ->first()
            ->toArray();

        $money = $result['money'] ?: 0;

        $adjust_money = bcsub(0, $money, 5);//真实收入=收入-成本
        if($adjust_money == 0){
            return;
        }

        //特殊消耗中的钱通过type判断正负，money都是正的
        if($adjust_money < 0){
            $type = 1;
        }else{
            $type = 2;
        }

        $nowtime = time();
        $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
        $addRow = [
            'customer_id' => $customer_id,
            'start_date' => substr($this->current_date, 0, 6),
            'name' => '特殊账单程序调整',
            'money' => abs($money),
            'money_finance' => abs($money),
            'type' => $type,
            'remark' => '涉及日期:'.$this->current_date.':'.'前筛产品-我司推广',
            'profile_show_date' => substr($this->current_date, 0, 4).'-'.substr($this->current_date, 4, 2).'-01',
            'product_id' => $product_id,
            'create_time' => $nowtime,
            'update_time' => $nowtime,
            'source' => 1,
        ];

        CustomerExpend::insert($addRow);
    }

    //添加邦信分移动02特殊消耗
    protected function addCustomerExpendBxfZYCMCC($row, $condition = []){
        $arr = explode('_', $row);
        $apikey = $arr[0] ?? '';
        $product_id = $arr[1] ?? '';
        if(empty($apikey) || empty($product_id)){
            return;
        }

        $result = BillCostV2::select([DB::raw('SUM(`money_original`) as money')])
            ->where('source', 1)
            ->whereIn('interface_id', $this->bxf_zycmcc_iids)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($apikey), function($query) use($apikey){
                return $query->where('apikey', $apikey);
            })
            ->when(!empty($product_id), function($query) use($product_id){
                return $query->where('product_id', $product_id);
            })
            ->when((isset($condition['operators']) && !empty($condition['operators'])), function($query) use ($condition){
                return $query->whereIn('operator', $condition['operators']);
            })
            ->get()
            ->first()
            ->toArray();

        $money = $result['money'] ?: 0;

        $adjust_money = bcsub(0, $money, 5);//真实收入=收入-成本
        if($adjust_money == 0){
            return;
        }

        //特殊消耗中的钱通过type判断正负，money都是正的
        if($adjust_money < 0){
            $type = 1;
        }else{
            $type = 2;
        }

        $nowtime = time();
        $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($apikey);
        $addRow = [
            'customer_id' => $customer_id,
            'start_date' => substr($this->current_date, 0, 6),
            'name' => '特殊账单程序调整',
            'money' => abs($money),
            'money_finance' => abs($money),
            'type' => $type,
            'remark' => '涉及日期:'.$this->current_date.':'.'邦信分移动02',
            'profile_show_date' => substr($this->current_date, 0, 4).'-'.substr($this->current_date, 4, 2).'-01',
            'product_id' => $product_id,
            'create_time' => $nowtime,
            'update_time' => $nowtime,
            'source' => 1,
        ];

        CustomerExpend::insert($addRow);
    }

    protected function getBillProductIncomeGroupList($condition = []){
        $result = [];
        BillProductIncomeV2::select(['apikey', 'call_product_id as product_id'])
            ->where('source', 1)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($this->product_ids), function($query) {
                return $query->whereIn('call_product_id', $this->product_ids);
            })
            ->when((isset($condition['filter_apikey']) && !empty($condition['filter_apikey'])), function($query) use ($condition){
                return $query->whereNotIn('apikey', $condition['filter_apikey']);
            })
            ->when((isset($condition['operators']) && !empty($condition['operators'])), function($query) use ($condition){
                return $query->whereIn('operator', $condition['operators']);
            })
            ->when((isset($condition['father_id']) && $condition['father_id'] == 210), function($query) {
                return $query->where('operator', 'ZYCMCC');
            })
            ->groupBy(['apikey', 'call_product_id'])
            ->get()
            ->each(function ($item) use (&$result){

                $index = $item['apikey'].'_'.$item['product_id'];
                if(!in_array($index, $result)){
                    $result[] = $index;
                }

            });

        return $result;
    }

    protected function getBillCostGroupList($condition = []){
        $result = [];
        BillCostV2::select(['apikey', 'product_id'])
            ->where('source', 1)
            ->when($this->current_date, function($query){
                return $query->where('date', $this->current_date);
            })
            ->when(!empty($this->product_ids), function($query) {
                return $query->whereIn('product_id', $this->product_ids);
            })
            ->when((isset($condition['filter_apikey']) && !empty($condition['filter_apikey'])), function($query) use ($condition){
                return $query->whereNotIn('apikey', $condition['filter_apikey']);
            })
            ->when((isset($condition['father_id']) && $condition['father_id'] == 210), function($query) {
//                return $query->where('operator', 'ZYCMCC');
                return $query->whereIn('interface_id', $this->bxf_zycmcc_iids);
            })
            ->when((isset($condition['filter_interface_ids']) && !empty($condition['filter_interface_ids'])), function($query) use ($condition){
                return $query->whereNotIn('interface_id', $condition['filter_interface_ids']);
            })
            ->when((isset($condition['operators']) && !empty($condition['operators'])), function($query) use ($condition){
                return $query->whereIn('operator', $condition['operators']);
            })
            ->when((isset($condition['interface_ids']) && !empty($condition['interface_ids'])), function($query) use ($condition){
                return $query->whereIn('interface_id', $condition['interface_ids']);
            })
            ->groupBy(['apikey', 'product_id'])
            ->get()
            ->each(function ($item) use (&$result){

                $index = $item['apikey'].'_'.$item['product_id'];
                if(!in_array($index, $result)){
                    $result[] = $index;
                }

            });

        return $result;
    }



}