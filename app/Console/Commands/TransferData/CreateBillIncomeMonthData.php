<?php

namespace App\Console\Commands\TransferData;

use App\Define\StatDefine;
use App\Models\CustomerExpend;
use App\Models\Income\BillIncomeMonthData;
use App\Models\RerunBillRecord;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Income\MainRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

//生成月账单收入数据
class CreateBillIncomeMonthData extends Command
{
	protected $signature = 'create_bill_income_month_data
	{--start_month= : 起始月, 格式Ym}
	{--end_month= : 结束月, 格式Ym}
    {--type= : 默认1根据日期，生成整月的数据；2根据特殊消耗和重跑记录生成部分月数据}
	';
	
	protected $description = '通过日账单和特殊消耗基础表数据生成收入月表,生成后用于作为中间表加快查询速度';

    protected $start_date;
    protected $end_date;
	protected $start_month;
	protected $end_month;
	protected $repository;
	protected $type;


	
	public function handle()
	{
        $this->type = !empty($this->option('type')) ? $this->option('type') : 1;

        if($this->type == 1){
            //生成完整月数据
            $this->createFullMonthData();
        }else{
            //根据重跑账单记录和特殊消耗记录作为条件生成部分月数据
            $this->createPartDataWithConditions();
        }

        $this->output->success("月收入中间表数据生成成功");
	}

    public function createFullMonthData(){
        //校验参数
        if(!$this->checkParams()){
            return;
        }

        $end_month_first_date = $this->end_month.'01';
        $end_month_last_time = strtotime('+1 months -1 days', strtotime($end_month_first_date));
        $end_month_last_date = date('Ymd', $end_month_last_time);

        $start_month_first_date = $this->start_month.'01';
        $this->start_date = $start_month_first_date;

        $this->repository = new MainRepository();

        while ($this->start_date <= $end_month_last_date){
            $this->end_date = date('Ymd', strtotime('+1 months -1 days', strtotime($this->start_date)));

            //一个月 一个月的循环处理每月的数据
            $this->done();

            $this->start_date = date('Ymd', strtotime('+1 months', strtotime($this->start_date)));
        }
    }


    public function createPartDataWithConditions(){
        $this->repository = new MainRepository();
        try {
            //根据重跑账单
            //$this->createDataByBillRecord();

            //根据特殊消耗
            $this->createDataByExpend();
        }catch (\Exception $exception){
            $msg = "账单月数据更新异常".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "任务type:".$this->type.PHP_EOL;
            $msg .= "异常信息:".mb_substr($exception->getMessage(), 0, 160);

            sendWechatNotice($msg);
        }

    }

    public function createDataByBillRecord(){
        $records = [];
        $update_ids = [];
        //获取重跑记录
        $bill_list = RerunBillRecord::getListByCondition(['is_rerun_month_data' => 0])->toArray();
        foreach ($bill_list as $item){
            $diff = time() - $item['create_at'];
            if($diff < 600){
                //新建10分钟之内 先不用，因为这时候账单可能还未生成
                continue;
            }

            $father_id = $item['father_id'];
            $start_date = $item['start_date'];
            $end_date = $item['end_date'];
            $months = $this->splitMonth($start_date, $end_date);
            foreach ($months as $oneMonth){
                $month = substr($oneMonth['s_date'], 0, 6);
                $is_have_data = $this->checkMonthData($month);
                //月中间表还没有生成该月数据的话 直接跳过
                if(!$is_have_data){
                    continue;
                }
                $records[$father_id][$month][] = $item['customer_id'];
            }

            $update_ids[] = $item['id'];
        }

        unset($bill_list);

        foreach ($records as $father_id => $months_data){
            foreach ($months_data as $month => $customer_data){
//                echo $father_id.PHP_EOL;
//                echo $month.PHP_EOL;
//                var_dump($customer_data);
//                echo '-----------'.PHP_EOL;

                $customer_ids = array_unique($customer_data);
                //先删除月账单表中的数据
                $this->deleteMonthData($father_id, $month, $customer_ids);

                //按条件重新生成月数据
                $this->createMonthData($father_id, $month, $customer_ids);
            }
        }

        //更新records
        if(!empty($update_ids)){
            RerunBillRecord::whereIn('id', $update_ids)->update(['is_rerun_month_data' => 1]);
        }

        #todo 通知发票更新
        unset($records);
    }

    public function createDataByExpend(){
        $records = [];
        $update_ids = [];
        //获取重跑记录
        $expend_list = CustomerExpend::getListByCondition(['is_rerun_month_data' => 0])->toArray();
        foreach ($expend_list as $item){

            $product_id = $item['product_id'];
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id) ?: $product_id;
            $month = date('Ym', strtotime($item['profile_show_date']));
            /*
            $is_have_data = $this->checkMonthData($month);
            //月中间表还没有生成该月数据的话 直接跳过
            if(!$is_have_data){
                continue;
            }
            */

            $customer_id = $item['customer_id'];
            $records[$father_id][$month][] = $customer_id;

            $update_ids[] = $item['id'];
        }

        unset($expend_list);

        foreach ($records as $father_id => $months_data){
            foreach ($months_data as $month => $customer_data){
                $customer_ids = array_unique($customer_data);
                //先删除月账单表中的数据
                //$this->deleteMonthData($father_id, $month, $customer_ids);

                //按条件重新生成月数据
                //$this->createMonthData($father_id, $month, $customer_ids);

                //通知发票更新
                Artisan::call('invoice:generate_month_consume', [
                    '--case' => 3,
                    '--month' => $month,
                    '--customer_id' => implode(',', $customer_ids)
                ]);

            }
        }

        //更新records
        if(!empty($update_ids)){
            CustomerExpend::whereIn('id', $update_ids)->update(['is_rerun_month_data' => 1]);
        }

        unset($records);
    }

    public function deleteMonthData($father_id, $month, $customer_ids){
        $sub_pids_map = $this->repository->getSubPidsByFatherIdsWithAngle([$father_id]);
        $sub_pids = array_keys($sub_pids_map);

        $flag = BillIncomeMonthData::where(['month' => $month])
            ->whereIn('customer_id', $customer_ids)
            ->whereIn('call_product_id', $sub_pids)
            ->delete();
        if(!$flag){
            $msg = "账单月数据删除异常".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "删除月份:".$month.PHP_EOL;
            $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "任务type:".$this->type.PHP_EOL;
            $msg .= "删除父产品:".$father_id;
            sendWechatNotice($msg);
        }

        return $flag;
    }

    public function checkMonthData($check_month){
        $is_have_data = BillIncomeMonthData::select([DB::raw('count(*) as num')])->where('month', $check_month)->first()->toArray();
        if($is_have_data['num'] == 0){
            return false;
        }

        return true;
    }

    public function createMonthData($father_id, $month, $customer_ids){
        $this->start_date = $month.'01';
        $this->end_date = date('Ymd', strtotime('+1 months -1 days', strtotime($month.'01')));

        $this->done($customer_ids, [], [$father_id], []);
    }

	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2024/08/22
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
        $options = $this->options();

        $this->start_month = $options['start_month'];
        $this->end_month = $options['end_month'];
        if(empty($this->start_month) || empty($this->end_month)){
            echo "起始月份和结束月份不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{6}$/', $this->start_month)) {
            echo "起始月份格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{6}$/', $this->end_month)) {
            echo "截止月份格式不正确\r\n";
            return false;
        }

        /*
        if (!empty($options['customer_id'])) {
            $customer_ids       = explode(',', $options['customer_id']);
            $this->customer_ids = array_filter($customer_ids);
        }

        if (!empty($options['apikey'])) {
            $apikeys       = explode(',', $options['apikey']);
            $this->apikeys = array_filter($apikeys);
        }

        if (!empty($options['product_id'])) {
            $product_ids       = explode(',', $options['product_id']);
            $this->product_ids = array_filter($product_ids);
        }
        */

        return true;

	}

	protected function done($customer_ids = [], $apikeys = [], $father_ids = [], $product_ids = []){
        //echo $this->start_date.'##'.$this->end_date.PHP_EOL;
        $res = $this->repository->getBaseIncome(StatDefine::DIMENSION_DEVELOPER, $this->start_date, $this->end_date, $customer_ids, $apikeys, $father_ids, $product_ids, ['is_query_month_data' => 0]);

        if($res['status'] != 0){
            //响应异常报警
            $msg = "账单月数据异常".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "开始日期:".$this->start_date.PHP_EOL;
            $msg .= "结束日期:".$this->end_date.PHP_EOL;
            $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "任务type:".$this->type.PHP_EOL;
            $msg .= "获取列表数据错误:".$res['msg'];
            sendWechatNotice($msg);
        }

        $add = [];
        $nowtime = time();
        $month = substr($this->start_date, 0, 6);

        foreach ($res['data'] as $item){
            $add[] = [
                'customer_id' => $item['customer_id'],
                'apikey' => $item['apikey'],
                'product_id' => $item['product_id'],
                'call_product_id' => $item['call_product_id'],
                'operator' => $item['operator'],
                'source' => $item['source'],
                'money' => $item['income'],
                'number' => (int)$item['number'],
                'month' => (int)$month,
                'create_time' => $nowtime,
            ];
        }

        unset($res);

        $addChunk = array_chunk($add, 150);
        foreach ($addChunk as $itemArr) {
            try {
                $insert = BillIncomeMonthData::insert($itemArr);
                if(!$insert){
                    //响应异常报警
                    $msg = "账单月数据异常".PHP_EOL;
                    $msg .= PHP_EOL;
                    $msg .= "开始日期:".$this->start_date.PHP_EOL;
                    $msg .= "结束日期:".$this->end_date.PHP_EOL;
                    $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
                    $msg .= "任务type:".$this->type.PHP_EOL;
                    $msg .= "数据插入失败";
                    sendWechatNotice($msg);
                }
            }catch (\Exception $exception){
                $msg = "账单月数据异常".PHP_EOL;
                $msg .= PHP_EOL;
                $msg .= "开始日期:".$this->start_date.PHP_EOL;
                $msg .= "结束日期:".$this->end_date.PHP_EOL;
                $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
                $msg .= "任务type:".$this->type.PHP_EOL;
                $msg .= "数据插入异常：".mb_substr($exception->getMessage(), 0, 200);
                sendWechatNotice($msg);
            }

        }

        unset($addChunk);
        unset($add);

    }


    public function splitMonth($s_date, $e_date){
        $start = new \DateTime($s_date);
        $end = new \DateTime($e_date);
        $end->modify('last day of this month'); // 调整结束日期到该月的最后一天
        $interval = new \DateInterval('P1M');
        $dateRange = new \DatePeriod($start, $interval, $end);

        $months = [];
        foreach ($dateRange as $month) {
            $is_full_month = 1;//是否完整月份 1完整、 0不完整
            $monthStart = (int)$month->format('Ym01');
            $monthEnd = (int)$month->format('Ymt');
            if($monthStart < $s_date){
                $is_full_month = 0;
                $monthStart = $s_date;
            }

            if($monthEnd > $e_date){
                $is_full_month = 0;
                $monthEnd = $e_date;
            }

            $months[] = ['is_full_month' => $is_full_month, 's_date' => $monthStart, 'e_date' => $monthEnd];
        }

        return $months;
    }



}