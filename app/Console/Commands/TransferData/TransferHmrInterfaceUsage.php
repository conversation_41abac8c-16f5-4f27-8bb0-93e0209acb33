<?php

namespace App\Console\Commands\TransferData;

use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use Illuminate\Console\Command;

//通过号码融客户用量分析对应渠道的用量
class TransferHmrInterfaceUsage extends Command
{
	protected $signature = 'transfer_hmr_usage
	{--start_date= : 起始日期, 格式Ymd，默认今日}
	{--end_date= : 结束日期, 格式Ymd，默认今日}
    {--customer_id= : 需要转移那些客户的，默认所有，多个客户ID可用,隔开}
    {--apikey= : 账号标识（多个标识以,隔开）}
    {--product_id= : 子产品ID（多个产品ID以,隔开）}
	';
	
	protected $description = '通过号码融客户用量分析对应渠道的用量';

	protected $start_date;
	protected $end_date;
	protected $apikeys = [];
	protected $customer_ids = [];
	protected $product_ids = [];
	protected $product_key = '43776afaba17e9d7b008875be829911e';
	protected $product_map = [
        '72001' => [
            [
                'id' => 670,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanScore',
            ]
        ],
        '72002' => [
            [
                'id' => 671,
                'channel' => 'HmrC2',
                'interface' => 'MultipleLoans',
            ]
        ],
        '72003' => [
            [
                'id' => 675,
                'channel' => 'HmrC1',
                'interface' => 'Scagedhb003Mod',
            ]
        ],
        '72004' => [
            [
                'id' => 676,
                'channel' => 'HmrC1',
                'interface' => 'Scagedhb005Mod',
            ]
        ],
        '72005' => [
            [
                'id' => 673,
                'channel' => 'HmrC2',
                'interface' => 'RiskV7GtongyongScore',
            ]
        ],
        '72006' => [
            [
                'id' => 674,
                'channel' => 'HmrC2',
                'interface' => 'RiskV7GLowIrrScore',
            ],
            [
                'id' => 672,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanV23Score',
            ],
        ],
        '72007' => [
            [
                'id' => 670,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanScore',
            ],
            [
                'id' => 674,
                'channel' => 'HmrC2',
                'interface' => 'RiskV7GLowIrrScore',
            ],
        ],
        '72008' => [
            [
                'id' => 685,
                'channel' => 'HmrC7',
                'interface' => 'XcModelC07001',
            ]
        ],
        '72009' => [
            [
                'id' => 684,
                'channel' => 'HmrC2',
                'interface' => 'RiskV7GHighIrrScore',
            ]
        ],
        '72010' => [
            [
                'id' => 671,
                'channel' => 'HmrC2',
                'interface' => 'MultipleLoans',
            ]
        ],
        '72011' => [
            [
                'id' => 686,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanV2DownV3Score',
            ]
        ],
        '71001' => [
            [
                'id' => 686,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanV2DownV3Score',
            ]
        ],
        '72014' => [
            [
                'id' => 807,
                'channel' => 'HmrC4',
                'interface' => 'BusiTypeQuery1016of1672',
            ]
        ],
        '72015' => [
            [
                'id' => 807,
                'channel' => 'HmrC4',
                'interface' => 'BusiTypeQuery1016of1672',
            ],
            [
                'id' => 804,
                'channel' => 'HmrC5',
                'interface' => 'BlueCollarHdScoreV11',
            ]
        ],
        '72016' => [
            [
                'id' => 801,
                'channel' => 'HmrC6',
                'interface' => 'EcommerceReturns',
            ],
            [
                'id' => 802,
                'channel' => 'HmrC6',
                'interface' => 'PDGZLabel',
            ],
            [
                'id' => 803,
                'channel' => 'HmrC6',
                'interface' => 'EcommerceBlacklistLabel',
            ]
        ],
        '72017' => [
            [
                'id' => 807,
                'channel' => 'HmrC4',
                'interface' => 'BusiTypeQuery1016of1672',
            ],
            [
                'id' => 805,
                'channel' => 'HmrC5',
                'interface' => 'BlueCollarHdScoreV12',
            ]
        ],
        '71003' => [
            [
                'id' => 674,
                'channel' => 'HmrC2',
                'interface' => 'RiskV7GLowIrrScore',
            ]
        ],
        '71004' => [
            [
                'id' => 671,
                'channel' => 'HmrC2',
                'interface' => 'MultipleLoans',
            ]
        ],
        '71005' => [
            [
                'id' => 686,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanV2DownV3Score',
            ]
        ],
        '71006' => [
            [
                'id' => 686,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanV2DownV3Score',
            ]
        ],
        '71007' => [
            [
                'id' => 671,
                'channel' => 'HmrC2',
                'interface' => 'MultipleLoans',
            ]
        ],
        '71008' => [
            [
                'id' => 671,
                'channel' => 'HmrC2',
                'interface' => 'MultipleLoans',
            ]
        ],
        '71009' => [
            [
                'id' => 671,
                'channel' => 'HmrC2',
                'interface' => 'MultipleLoans',
            ]
        ],
        '71010' => [
            [
                'id' => 685,
                'channel' => 'HmrC7',
                'interface' => 'XcModelC07001',
            ]
        ],
        '75001' => [
            [
                'id' => 671,
                'channel' => 'HmrC2',
                'interface' => 'MultipleLoans',
            ]
        ],
        '72019' => [
            [
                'id' => 830,
                'channel' => 'HmrC10',
                'interface' => 'HuoyanRiskHelloV5Score',
            ]
        ],
    ];

	
	public function handle()
	{
		//校验参数
        if(!$this->checkParams()){
            return;
        }

        if(empty($this->start_date) && empty($this->end_date)){
            $this->start_date = $this->end_date = date("Ymd");//统计时间
        }

        $date = $this->start_date;
        while ($date <= $this->end_date){
            $this->done($date);
            $date = date('Ymd', strtotime($date) + 86400);
        }

        $this->output->success("号码融渠道用量预估成功执行");
	}
	
	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2021/12/29
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
        $options = $this->options();
        $this->start_date = $options['start_date'];
        $this->end_date = $options['end_date'];
        if(!empty($this->start_date) && empty($this->end_date)){
            echo "截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->end_date) && empty($this->start_date)){
            echo "起始日期不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->start_date) && !empty($this->start_date)) {
            echo "起始日期格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date) && !empty($this->end_date)) {
            echo "截止日期格式不正确\r\n";
            return false;
        }

        if (!empty($options['customer_id'])) {
            $customer_ids       = explode(',', $options['customer_id']);
            $this->customer_ids = array_filter($customer_ids);
        }

        if (!empty($options['apikey'])) {
            $apikeys       = explode(',', $options['apikey']);
            $this->apikeys = array_filter($apikeys);
        }

        if (!empty($options['product_id'])) {
            $product_ids       = explode(',', $options['product_id']);
            $this->product_ids = array_filter($product_ids);
        }else{
            $this->product_ids = Product::whereIn('father_id', [70000])
                ->pluck('product_id')
                ->toArray();
        }

        return true;

	}

	protected function done($date){
        $list = StatisticsCustomerUsage::getProductUsageByNode(['date' => $date], $this->product_ids)->toArray();
        $data = [];

        foreach ($list as $item){
            $index = $item['product_id'].'_'.$item['node'];

            $data[$index]['data'][] = [
                'apikey' => $item['apikey'],
                'data' => $this->getInterfaceUsage($item),
            ];
        }

        unset($list);

        foreach ($data as $key => $value){
            $arr = explode('_', $key);
            $product_id = $arr[0];
            $node = $arr[1];

            $this->sendCustomerUsageStatistics($product_id, $this->product_key, $node, $date, $value['data']);

        }

        return;

    }

    public function sendCustomerUsageStatistics($product_id, $product_key, $node, $date, $data)
    {
        $url = config('params.request_url.SEND_INTERFACE_USAGE_API');
        $params = compact('product_key', 'product_id', 'node', 'date', 'data');
        $res = $this->postRawJson($url, $params);
        $response = json_decode($res['data'], true);

        if (0 != $res['code'] || $response['code'] != 0) {
            //响应异常报警
            $msg = "号码融渠道用量预估推送失败".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "数据日期:".$date.PHP_EOL;
            $msg .= "推送时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "接口响应:".$response['message'];

            sendWechatNotice($msg);
        }

        return true;
    }

    public function getInterfaceUsage($item){
        $interface_list = $this->product_map[$item['product_id']] ?? [];
        $data = [];
        foreach ($interface_list as $interface){
            $data[] = [
                'channel' => $interface['channel'],
                'operator' => 'CMCC',
                'encrypt' => 'CLEAR',
                'source' => 1,
                'total' => $item['total'],
                'success' => $item['success'],
                'valid' => $item['valid'],
                'interface' => $interface['interface']
            ];
        }

        return $data;
    }

    public function postRawJson($url, $params, $options = [])
    {
        $params                      = json_encode($params, JSON_UNESCAPED_UNICODE);
        $options[CURLOPT_HTTPHEADER] = [
            'X-AjaxPro-Method:ShowList',
            'Content-Type: application/json; charset=utf-8',
            'Content-Length: ' . strlen($params),
        ];
        $options[CURLOPT_POST]       = true;
        $options[CURLOPT_POSTFIELDS] = $params;

        return $this->curl($url, $options);
    }

    public function curl($url, $options)
    {
        $curl = curl_init();

        $defaultOptions = [
            CURLOPT_URL            => $url,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_TIMEOUT        => 20,
            CURLOPT_RETURNTRANSFER => true,
        ];
        $options        = $defaultOptions + $options;
        curl_setopt_array($curl, $options);

        $data    = curl_exec($curl);
        $code    = curl_errno($curl);
        $message = curl_error($curl);
        $httpcode = curl_getinfo($curl,CURLINFO_HTTP_CODE); ;

        return compact('code', 'message', 'data');
    }





}