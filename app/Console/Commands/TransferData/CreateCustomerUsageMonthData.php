<?php

namespace App\Console\Commands\TransferData;

use App\Define\StatDefine;
use App\Models\Usage\CustomerUsageMonthData;
use App\Repositories\Income\MainRepository;
use Illuminate\Console\Command;

//生成客户用量月数据
class CreateCustomerUsageMonthData extends Command
{
	protected $signature = 'create_customer_usage_month_data
	{--start_month= : 起始月, 格式Ym}
	{--end_month= : 结束月, 格式Ym}
	';
	
	protected $description = '生成客户用量月中间表加快查询速度';

    protected $start_date;
    protected $end_date;
	protected $start_month;
	protected $end_month;
	protected $repository;
	protected $type;


	
	public function handle()
	{

        //生成完整月数据
        $this->createFullMonthData();

        $this->output->success("客户用量月中间表数据生成成功");
	}

    public function createFullMonthData(){
        //校验参数
        if(!$this->checkParams()){
            return;
        }

        $end_month_first_date = $this->end_month.'01';
        $end_month_last_time = strtotime('+1 months -1 days', strtotime($end_month_first_date));
        $end_month_last_date = date('Ymd', $end_month_last_time);

        $start_month_first_date = $this->start_month.'01';
        $this->start_date = $start_month_first_date;

        $this->repository = new MainRepository();

        while ($this->start_date <= $end_month_last_date){
            $this->end_date = date('Ymd', strtotime('+1 months -1 days', strtotime($this->start_date)));

            //一个月 一个月的循环处理每月的数据
            $this->done();

            $this->start_date = date('Ymd', strtotime('+1 months', strtotime($this->start_date)));
        }
    }




	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2024/08/22
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
        $options = $this->options();

        $this->start_month = $options['start_month'];
        $this->end_month = $options['end_month'];
        if(empty($this->start_month) || empty($this->end_month)){
            echo "起始月份和结束月份不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{6}$/', $this->start_month)) {
            echo "起始月份格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{6}$/', $this->end_month)) {
            echo "截止月份格式不正确\r\n";
            return false;
        }

        /*
        if (!empty($options['customer_id'])) {
            $customer_ids       = explode(',', $options['customer_id']);
            $this->customer_ids = array_filter($customer_ids);
        }

        if (!empty($options['apikey'])) {
            $apikeys       = explode(',', $options['apikey']);
            $this->apikeys = array_filter($apikeys);
        }

        if (!empty($options['product_id'])) {
            $product_ids       = explode(',', $options['product_id']);
            $this->product_ids = array_filter($product_ids);
        }
        */

        return true;

	}

	protected function done($customer_ids = [], $apikeys = [], $father_ids = [], $product_ids = []){

        $res = $this->repository->getBaseCustomerUsage(StatDefine::DIMENSION_DEVELOPER, $this->start_date, $this->end_date, $customer_ids, $apikeys, $father_ids, $product_ids, ['is_query_month_data' => 0]);

        if($res['status'] != 0){
            //响应异常报警
            $msg = "客户用量月中间表数据生成异常".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "开始日期:".$this->start_date.PHP_EOL;
            $msg .= "结束日期:".$this->end_date.PHP_EOL;
            $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "获取列表数据错误:".$res['msg'];
            sendWechatNotice($msg);
        }

        $add = [];
        $nowtime = time();
        $month = substr($this->start_date, 0, 6);

        foreach ($res['data'] as $item){
            $add[] = [
                'customer_id' => $item['customer_id'],
                'apikey' => $item['apikey'],
                'product_id' => $item['product_id'],
                'call_product_id' => $item['call_product_id'],
                'operator' => $item['operator'],
                'total' => $item['total'],
                'success' => $item['success'],
                'valid' => $item['valid'],
                'charge' => $item['charge'],
                'source' => $item['source'],
                'month' => (int)$month,
                'create_time' => $nowtime,
            ];
        }

        unset($res);

        $addChunk = array_chunk($add, 150);
        foreach ($addChunk as $itemArr) {
            try {
                $insert = CustomerUsageMonthData::insert($itemArr);
                if(!$insert){
                    //响应异常报警
                    $msg = "客户用量月中间表数据生成异常".PHP_EOL;
                    $msg .= PHP_EOL;
                    $msg .= "开始日期:".$this->start_date.PHP_EOL;
                    $msg .= "结束日期:".$this->end_date.PHP_EOL;
                    $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
                    $msg .= "数据插入失败";
                    sendWechatNotice($msg);
                }
            }catch (\Exception $exception){
                $msg = "客户用量月中间表数据生成异常".PHP_EOL;
                $msg .= PHP_EOL;
                $msg .= "开始日期:".$this->start_date.PHP_EOL;
                $msg .= "结束日期:".$this->end_date.PHP_EOL;
                $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
                $msg .= "数据插入异常：".mb_substr($exception->getMessage(), 0, 200);
                sendWechatNotice($msg);
            }

        }

        unset($addChunk);
        unset($add);

    }


}