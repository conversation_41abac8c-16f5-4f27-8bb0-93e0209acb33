<?php

namespace App\Console\Commands\TransferData;

use App\Define\StatDefine;
use App\Models\BillCostV2;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\Cost\BillCostMonthData;
use App\Models\CustomerBillAdjust;
use App\Models\CustomerExpend;
use App\Models\RerunBillRecord;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Income\MainRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

//生成月账单成本数据
class CreateBillCostMonthData extends Command
{
	protected $signature = 'create_bill_cost_month_data
	{--start_month= : 起始月, 格式Ym}
	{--end_month= : 结束月, 格式Ym}
    {--type= : 默认1根据日期，生成整月的数据；2根据普通成本账单、渠道成本调整、客户成本调整、固定费用等记录生成部分月数据}
	';
	
	protected $description = '通过日成本账单和成本调整等基础表数据生成成本月表,生成后用于作为中间表加快查询速度';

    protected $start_date;
    protected $end_date;
	protected $start_month;
	protected $end_month;
	protected $cache_months = [];
	protected $repository;
	protected $type;


	
	public function handle()
	{
        $this->type = !empty($this->option('type')) ? $this->option('type') : 1;

        if($this->type == 1){
            //生成完整月数据
            $this->createFullMonthData();
        }else{
            //根据成本调整记录等作为条件生成部分月数据
            $this->createPartDataWithConditions();
        }

        $this->output->success("月收入中间表数据生成成功");
	}

    public function createFullMonthData(){
        //校验参数
        if(!$this->checkParams()){
            return;
        }

        $end_month_first_date = $this->end_month.'01';
        $end_month_last_time = strtotime('+1 months -1 days', strtotime($end_month_first_date));
        $end_month_last_date = date('Ymd', $end_month_last_time);

        $start_month_first_date = $this->start_month.'01';
        $this->start_date = $start_month_first_date;

        $this->repository = new MainRepository();

        while ($this->start_date <= $end_month_last_date){
            $this->end_date = date('Ymd', strtotime('+1 months -1 days', strtotime($this->start_date)));

            //一个月 一个月的循环处理每月的数据
            $this->done();

            $this->start_date = date('Ymd', strtotime('+1 months', strtotime($this->start_date)));
        }
    }


    public function createPartDataWithConditions(){
        $this->repository = new MainRepository();
        try {
            //获取月缓存表中都有那些月数据
            $this->cache_months = $this->getCacheMonth();

            //根据普通账单变更记录
            $this->createDataByBillCost();

            //根据渠道成本调整记录
            $this->createDataByChannelAdjust();

            //根据客户成本调整
            $this->createDataByCustomerAdjust();

            //固定成本调整
            $this->createDataByFixedAdjust();
        }catch (\Exception $exception){
            $msg = "账单月数据更新异常".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "任务type:".$this->type.PHP_EOL;
            $msg .= "异常信息:".mb_substr($exception->getMessage(), 0, 160);
            sendWechatNotice($msg);
        }

    }

    public function getCacheMonth(){
        $list = BillCostMonthData::select(['month'])
            //->where(['month' => 202507])
            ->groupBy(['month'])
            ->get()
            ->toArray();

        $list = array_column($list, 'month');
        return $list;
    }

    public function createDataByBillCost(){
        $records = [];
        $now_time = time();//1743587400
        $current_minute = date('Y-m-d H:i:00', $now_time);//当前分钟
        $current_minute_time = strtotime($current_minute);
        $start_create_time = strtotime('-10 Minutes', $current_minute_time);//查询的创建时间往前推10分钟

        $current_date = date('Ymd', $now_time);//当前日期
        $current_time = strtotime($current_date);//当前日期对应时间戳
        $start_time = strtotime('-90 days', $current_time);//查询的统计日期往前推90天(这个天数是根据经验定的，也可以在多往前推几天)
        $start_date = date('Ymd', $start_time);

        //获取重跑、变动记录
        $bill_list = BillCostV2::getChangeCustomerProduct($start_date, $current_date, $start_create_time, $current_minute_time);
        if(empty($bill_list)){
            return;
        }

        foreach ($bill_list as $item){
            $product_id = $item['product_id'];
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id) ?: $product_id;
            $month = $item['s_month'];
            $is_have_data = $this->checkMonthData($month);
            //月中间表还没有生成该月数据的话 直接跳过
            if(!$is_have_data){
                continue;
            }

            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']) ?: $item['apikey'];

            $records[$father_id][$month][] = $customer_id;
        }

        unset($bill_list);


        foreach ($records as $father_id => $months_data){
            foreach ($months_data as $month => $customer_data){

                $customer_ids = array_unique($customer_data);

                //先删除月账单表中的数据
                //$this->deleteMonthData($father_id, $month, $customer_ids);

                //按条件重新生成月数据
                $this->createMonthData($father_id, $month, $customer_ids);
            }
        }

    }

    public function createDataByChannelAdjust(){
        $records = [];
        $update_ids = [];
        //获取重跑记录
        $adjust_list = ChannelAccountAdjust::getListByCondition(['is_rerun_month_data' => 0])->toArray();
        foreach ($adjust_list as $item){
            $product_id = $item['product_id'];
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id) ?: $product_id;
            $month = date('Ym', strtotime($item['date']));
            $is_have_data = $this->checkMonthData($month);
            //月中间表还没有生成该月数据的话 直接跳过
            if(!$is_have_data){
                continue;
            }

            $customer_id = $item['customer_id'];
            $records[$father_id][$month][] = $customer_id;

            $update_ids[] = $item['id'];
        }

        unset($adjust_list);


        foreach ($records as $father_id => $months_data){
            foreach ($months_data as $month => $customer_data){

                $customer_ids = array_unique($customer_data);
                //先删除月账单表中的数据
                //$this->deleteMonthData($father_id, $month, $customer_ids);

                //按条件重新生成月数据
                $this->createMonthData($father_id, $month, $customer_ids);
            }
        }

        //更新records
        if($update_ids){
            ChannelAccountAdjust::whereIn('id', $update_ids)->update(['is_rerun_month_data' => 1]);
        }

        #todo 通知发票更新
        unset($records);
    }

    public function createDataByCustomerAdjust(){
        $records = [];
        $update_ids = [];
        //获取客户成本调整记录
        $adjust_list = CustomerBillAdjust::getListByCondition(['is_rerun_month_data' => 0])->toArray();
        foreach ($adjust_list as $item){

            $father_id = $item['father_id'];
            $month = date('Ym', strtotime($item['date']));
            $is_have_data = $this->checkMonthData($month);
            //月中间表还没有生成该月数据的话 直接跳过
            if(!$is_have_data){
                continue;
            }

            $customer_id = $item['customer_id'];
            $records[$father_id][$month][] = $customer_id;

            $update_ids[] = $item['id'];
        }

        unset($adjust_list);

        foreach ($records as $father_id => $months_data){
            foreach ($months_data as $month => $customer_data){
                $customer_ids = array_unique($customer_data);
                //先删除月账单表中的数据
                //$this->deleteMonthData($father_id, $month, $customer_ids);

                //按条件重新生成月数据
                $this->createMonthData($father_id, $month, $customer_ids);
            }
        }

        //更新records
        if(!empty($update_ids)){
            CustomerBillAdjust::whereIn('id', $update_ids)->update(['is_rerun_month_data' => 1]);
        }

        #todo 通知发票更新
        unset($records);
    }

    public function createDataByFixedAdjust(){
        $records = [];
        $update_ids = [];
        //获取重跑记录
        $adjust_list = ChannelAccountFixedFee::getListByCondition(['is_rerun_month_data' => 0])->toArray();
        foreach ($adjust_list as $item){
            $father_id = $item['father_id'];
            $customer_id = RedisCache::instance('apikey_customerId_mapping')->get($item['apikey']) ?: $item['apikey'];
            $month = date('Ym', strtotime($item['date']));
            $is_have_data = $this->checkMonthData($month);
            //月中间表还没有生成该月数据的话 直接跳过
            if(!$is_have_data){
                continue;
            }

            $records[$father_id][$month][] = $customer_id;

            $update_ids[] = $item['id'];
        }

        unset($adjust_list);


        foreach ($records as $father_id => $months_data){
            foreach ($months_data as $month => $customer_data){

                $customer_ids = array_unique($customer_data);
                //先删除月账单表中的数据
                //$this->deleteMonthData($father_id, $month, $customer_ids);

                //按条件重新生成月数据
                $this->createMonthData($father_id, $month, $customer_ids);
            }
        }

        //更新records
        if($update_ids){
            ChannelAccountFixedFee::whereIn('id', $update_ids)->update(['is_rerun_month_data' => 1]);
        }

        #todo 通知发票更新
        unset($records);
    }

    public function deleteMonthData($father_ids, $month, $customer_ids){
        //$sub_pids_map = $this->repository->getSubPidsByFatherIdsWithAngle([$father_id]);
        $sub_pids_map = $this->repository->getSubPidsByFatherIdsWithAngle($father_ids);
        $sub_pids = array_keys($sub_pids_map);

        $flag = BillCostMonthData::where(['month' => $month])
            ->when($customer_ids, function($query) use ($customer_ids){
                return $query->whereIn('customer_id', $customer_ids);
            })
            ->when($father_ids, function($query) use ($sub_pids){
                return $query->whereIn('product_id', $sub_pids);
            })
            ->delete();
        if(!$flag){
            $msg = "成本账单月数据删除异常".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "删除月份:".$month.PHP_EOL;
            $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "任务type:".$this->type.PHP_EOL;
            $msg .= "删除父产品:".implode(',', $father_ids);
            sendWechatNotice($msg);
        }

        return $flag;
    }

    public function checkMonthData($check_month){
        if(in_array($check_month, $this->cache_months)){
            return true;
        }

        return false;

//        $is_have_data = BillCostMonthData::select([DB::raw('count(*) as num')])->where('month', $check_month)->first()->toArray();
//        if($is_have_data['num'] == 0){
//            return false;
//        }
//
//        return true;
    }

    public function createMonthData($father_id, $month, $customer_ids){
        $this->start_date = $month.'01';
        $this->end_date = date('Ymd', strtotime('+1 months -1 days', strtotime($month.'01')));

        $this->done($customer_ids, [], [$father_id], []);
    }

	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2024/08/22
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
        $options = $this->options();

        $this->start_month = $options['start_month'];
        $this->end_month = $options['end_month'];
        if(empty($this->start_month) || empty($this->end_month)){
            echo "起始月份和结束月份不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{6}$/', $this->start_month)) {
            echo "起始月份格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{6}$/', $this->end_month)) {
            echo "截止月份格式不正确\r\n";
            return false;
        }

        /*
        if (!empty($options['customer_id'])) {
            $customer_ids       = explode(',', $options['customer_id']);
            $this->customer_ids = array_filter($customer_ids);
        }

        if (!empty($options['apikey'])) {
            $apikeys       = explode(',', $options['apikey']);
            $this->apikeys = array_filter($apikeys);
        }

        if (!empty($options['product_id'])) {
            $product_ids       = explode(',', $options['product_id']);
            $this->product_ids = array_filter($product_ids);
        }
        */

        return true;

	}

	protected function done($customer_ids = [], $apikeys = [], $father_ids = [], $product_ids = []){
        //echo $this->start_date.'##'.$this->end_date.PHP_EOL;
        $res = $this->repository->getBaseCost(StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR, $this->start_date, $this->end_date, $customer_ids, $apikeys, $father_ids, $product_ids, ['is_query_month_data' => 0]);

        if($res['status'] != 0){
            //响应异常报警
            $msg = "成本账单月数据异常".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "开始日期:".$this->start_date.PHP_EOL;
            $msg .= "结束日期:".$this->end_date.PHP_EOL;
            $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
            $msg .= "任务type:".$this->type.PHP_EOL;
            $msg .= "获取列表数据错误:".$res['msg'];
            sendWechatNotice($msg);
        }

        $add = [];
        $nowtime = time();
        $month = substr($this->start_date, 0, 6);

        //插入数据之前先删除月账单表中的数据
        $this->deleteMonthData($father_ids, $month, $customer_ids);

        foreach ($res['data'] as $item){
            $add[] = [
                'customer_id' => $item['customer_id'],
                'apikey' => $item['apikey'],
                'product_id' => $item['product_id'],
                'channel_id' => $item['channel_id'],
                'interface_id' => $item['interface_id'],
                'operator' => $item['operator'],
                'encrypt' => $item['encrypt'],
                'source' => $item['source'],
                'type' => $item['type'],
                'money' => $item['cost'],
                'number' => (int)$item['number'],
                'month' => (int)$month,
                'create_time' => $nowtime,
            ];
        }

        unset($res);

        $addChunk = array_chunk($add, 150);
        foreach ($addChunk as $itemArr) {
            try {
                $insert = BillCostMonthData::insert($itemArr);
                if(!$insert){
                    //响应异常报警
                    $msg = "成本账单月数据异常".PHP_EOL;
                    $msg .= PHP_EOL;
                    $msg .= "开始日期:".$this->start_date.PHP_EOL;
                    $msg .= "结束日期:".$this->end_date.PHP_EOL;
                    $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
                    $msg .= "任务type:".$this->type.PHP_EOL;
                    $msg .= "数据插入失败";
                    sendWechatNotice($msg);
                }
            }catch (\Exception $exception){
                $msg = "成本账单月数据异常".PHP_EOL;
                $msg .= PHP_EOL;
                $msg .= "开始日期:".$this->start_date.PHP_EOL;
                $msg .= "结束日期:".$this->end_date.PHP_EOL;
                $msg .= "执行时间:".date('Y-m-d H:i').PHP_EOL;
                $msg .= "任务type:".$this->type.PHP_EOL;
                $msg .= "数据插入异常：".mb_substr($exception->getMessage(), 0, 200);
                sendWechatNotice($msg);
            }

        }

        unset($addChunk);
        unset($add);

    }


    public function splitMonth($s_date, $e_date){
        $start = new \DateTime($s_date);
        $end = new \DateTime($e_date);
        $end->modify('last day of this month'); // 调整结束日期到该月的最后一天
        $interval = new \DateInterval('P1M');
        $dateRange = new \DatePeriod($start, $interval, $end);

        $months = [];
        foreach ($dateRange as $month) {
            $is_full_month = 1;//是否完整月份 1完整、 0不完整
            $monthStart = (int)$month->format('Ym01');
            $monthEnd = (int)$month->format('Ymt');
            if($monthStart < $s_date){
                $is_full_month = 0;
                $monthStart = $s_date;
            }

            if($monthEnd > $e_date){
                $is_full_month = 0;
                $monthEnd = $e_date;
            }

            $months[] = ['is_full_month' => $is_full_month, 's_date' => $monthStart, 'e_date' => $monthEnd];
        }

        return $months;
    }



}