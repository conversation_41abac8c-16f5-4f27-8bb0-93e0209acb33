<?php

namespace App\Console\Commands\TransferData;

use App\Jobs\TransferTogetherCallUsageJob;
use App\Models\AccountTransferConfig;
use Illuminate\Console\Command;

//账号打包量从ck中计算，写入后台mysql
class TransferTogetherCallUsage extends Command
{
	protected $signature = 'usage:transfer_together_call
	{--start_date= : 转移的账单起始日期, 格式Ymd，默认今日}
	{--end_date= : 转移的账单结束日期, 格式Ymd，默认今日}
    {--customer_id= : 需要转移那些客户的，默认所有，多个客户ID可用,隔开}
    {--apikey= : 主账号标识（多个标识以,隔开）}
	';
	
	protected $description = '客户非打包账号量转移到打包账号中';

	protected $start_date;
	protected $end_date;
	protected $apikeys = [];
	protected $customer_ids = [];

	
	public function handle()
	{
		//校验参数
        if(!$this->checkParams()){
            return;
        }

        if(empty($this->start_date) && empty($this->end_date)){
            $this->start_date = $this->end_date = date("Ymd",strtotime("-1 day"));//统计时间
        }

        $config = $this->getTransferConfig();
        foreach($config as $item){

            dispatch(new TransferTogetherCallUsageJob($item, $this->start_date, $this->end_date));
        }

        $this->output->success("要转移的非打包账号已成功入队，请稍后确认是否成功执行");
	}
	
	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2021/12/29
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
        $options = $this->options();
        $this->start_date = $options['start_date'];
        $this->end_date = $options['end_date'];
        if(!empty($this->start_date) && empty($this->end_date)){
            echo "截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->end_date) && empty($this->start_date)){
            echo "起始日期不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->start_date) && !empty($this->start_date)) {
            echo "起始日期格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date) && !empty($this->end_date)) {
            echo "截止日期格式不正确\r\n";
            return false;
        }

        if (!empty($options['customer_id'])) {
            $customer_ids       = explode(',', $options['customer_id']);
            $this->customer_ids = array_filter($customer_ids);
        }

        if (!empty($options['apikey'])) {
            $apikeys       = explode(',', $options['apikey']);
            $this->apikeys = array_filter($apikeys);
        }

        return true;

	}

	protected function getTransferConfig(){

        $model  = new AccountTransferConfig();
        $model  = $model->select(['id', 'customer_id', 'father_id', 'from_apikey', 'to_apikey'])
                  ->where(['type' => 10, 'is_delete' => 0]);
        if (!empty($this->apikeys)) {
            $model = $model->whereIn('from_apikey', $this->apikeys);
        }
        if (!empty($this->customer_ids)) {
            $model = $model->whereIn('customer_id', $this->customer_ids);
        }
        //dd($model->toSql());
        $result = $model->get()->toArray();

        return $result;
    }
	


}