<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/3/2 0002
 * Time: 13:05
 */

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\MongoCuccMonitor;
use App\Models\MongoSecondaryProcessingStatis;
use App\Models\Product;
use App\Models\ProductType;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;
use Yulore\Apikey\Masking;

//对邦信分快捷版统计类字段进行二次处理，形成半成品数据
class SecondaryProcessingStatisRequestInfo extends Command
{
	use WechatExceptionTrait;
	
	protected $signature   = 'bxf:process_statis_request
    {--day= : 处理哪天的数据，格式为Ymd，默认昨日}
    {--days=1 : 需要处理几天的数据，默认为1，不得小于1}
    ';
	protected $description = '对邦信分快捷版统计类字段请求统计数据的二次处理';
	protected $product     = [];    //统计类字段的产品 field=>product_id
	protected $ctype       = [
		0,  //电信
		1,  //联通
		3   //移动
	];
	protected $notMeanOValue;      //无意义的o_value值
	protected $uniqueIndex = ['day', 'product_id', 'customer_id', 'c_type', 'o_value'];       //入库唯一键校验
	
	public function handle()
	{
		$option = $this->checkOption();
		
		$day   = array_get($option, 'day');
		$days  = array_get($option, 'days');
		$today = date('Ymd');
		
		for ($i = 0; $i < $days; $i++) {
			$date = date('Ymd', strtotime('+' . $i . ' days', strtotime($day)));
			if ($date >= $today) {
				break;
			}
			try {
				$this->runProgressing($date);
				$this->output->success("{$date}天的数据处理完成");
			} catch (\Exception $exception) {
				$message = $exception->getMessage();
				$line    = $exception->getLine();
				$msg     = "{$date}天的数据处理失败，原因如下:\n Message:{$message} \n Line:{$line}";
				$this->output->error($msg);
				
				//微信预警
				$this->wechat("[{$this->name}]任务出现异常 ==> \n {$msg}");
			}
		}
		
	}
	
	/**
	 * 获取并校验参数
	 *
	 * @access protected
	 *
	 * @return array|false
	 **/
	protected function checkOption()
	{
		//设置失败的字段标记
		$this->notMeanOValue = config('params.bxf_short.notMeanOValue');
		
		$day = $this->input->getOption('day');
		if (empty($day)) {
			$day = date('Ymd', strtotime('-1 days'));
		}
		
		if (!preg_match('/^\d{8}$/', $day)) {
			$this->output->error('日期格式不正确');
			
			return false;
		}
		
		if ($day >= date('Ymd')) {
			$this->output->error('未来数据不可计算');
			
			return false;
		}
		
		$days = $this->input->getOption('days');
		if (empty($days) || !is_numeric($days)) {
			$this->output->error('天数格式不正确');
			
			return false;
		}
		
		return compact('day', 'days');
	}
	
	/**
	 * 获取产品ID、产品字段的对应关系
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getScoreProductMapping()
	{
		if (empty($this->product)) {
			$product = Product::where('father_id', 210)
							  ->pluck('mark', 'product_id')
							  ->toArray();
//			$config = @json_decode($config, true);
//			if (is_null($config)) {
//				throw new \Exception('解析邦信分快捷版产品配置失败');
//			}
//			$product = array_get(array_get($config, 0), 'option');
//			if (empty($product)) {
//				throw new \Exception('邦信分快捷版产品配置中的产品ID与字段名称对应关系获取失败');
//			}
//			$product = array_column($product, 'opt_val', 'product_id');
			//统计类全量字段的产品ID
			$statis_product_id = ProductType::where('type', '=', 2)
											->pluck('product_id')
											->toArray();
			//字段与统计类产品ID映射关系表
			$statis_product_mapping = array_flip(array_intersect_key($product, array_flip($statis_product_id)));
			//ii型统计类字段的产品ID
			$ii_product_id = config('params.bxf_short.ii_product_id');
			
			//整理映射关系表，将类型插入
			$this->product = array_map(function ($product_id) use ($ii_product_id) {
				$type = in_array($product_id, $ii_product_id) ? 2 : 1;
				
				return compact('product_id', 'type');
			}, $statis_product_mapping);
		}
		
		return $this->product;
	}
	
	/**
	 * 处理某一天的数据
	 *
	 * @access protected
	 *
	 * @param $day string 日期
	 *
	 * @return void
	 **/
	protected function runProgressing($day)
	{
		$day = intval($day);
		//获取当前数据中存在的客户ID与apikey映射关系 customer_id => [apikey1,apikey2]
		$customerMapping = $this->getCustomerInfo($day);
		
		//获取分值类产品的映射关系表
		$productMapping = $this->getScoreProductMapping();
		
		
		//输出内容,并建造一个进度条
		$this->output->writeln("开始处理{$day}的数据");
		$max = count($customerMapping) * count($productMapping) * 9 * count($this->ctype);
		$this->output->createProgressBar();
		$this->output->progressStart($max);
		
		
		//存在5个维度的合计统计
		//时间维度、客户维度、产品维度、渠道维度、分段值维度，当前已经对时间维度进行了划分、下面依次嵌套各维度进行汇总数据
		//客户维度
		array_walk($customerMapping, function ($apikey, $customer_id) use ($productMapping, $day) {
			$apikey = $this->apikeyEncode($apikey, $day);
			//产品维度
			array_walk($productMapping, function ($productItem, $o_field) use ($apikey, $customer_id, $day) {
				//渠道维度
				array_walk($this->ctype, function ($ctype) use ($productItem, $o_field, $apikey, $customer_id, $day) {
					//分段值维度
					//确认该产品的类型
					$product_id = $productItem['product_id'];       //产品ID
					$type       = $productItem['type'];             //类型 i型 ii型
					
					switch ($type) {
						case 1:
							//i型数据处理
							$this->computeIInfo($day, $product_id, $customer_id, $ctype, $o_field, $apikey);
							break;
						case 2:
							//ii型数据处理
							$this->computeIIInfo($day, $product_id, $customer_id, $ctype, $o_field, $apikey);
							break;
						default:
							throw new \Exception("未定义{$type}类型的计算方法");
							break;
					}
				});
			});
		});
		$this->output->progressFinish();
	}
	
	/**
	 * i型数据处理
	 *
	 * @access protected
	 *
	 * @param $day         integer 日期
	 * @param $product_id  integer 产品ID
	 * @param $customer_id string 客户ID
	 * @param $ctype       integer 渠道
	 * @param $o_field     string 字段名称
	 * @param $aipkey      array 该客户下的apikey
	 *
	 * @return void
	 **/
	protected function computeIInfo($day, $product_id, $customer_id, $ctype, $o_field, $apikey)
	{
		$product_id = intval($product_id);
		
		//-1 -> 小于0的都是 -1
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->whereIn('o_value', [-1, -3, -4, -5, -6])
								   ->sum('cnt');
		$o_value = -1;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		
		//0字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 0)
								   ->sum('cnt');
		$o_value = 0;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//1字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 1)
								   ->sum('cnt');
		$o_value = 1;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//2字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 2)
								   ->sum('cnt');
		$o_value = 2;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//3字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 3)
								   ->sum('cnt');
		$o_value = 3;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//4字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 4)
								   ->sum('cnt');
		$o_value = 4;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//5字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 5)
								   ->sum('cnt');
		$o_value = 5;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//6字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', '>=', 6)
								   ->sum('cnt');
		$o_value = 6;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//失败字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', $this->notMeanOValue)
								   ->sum('cnt');
		$o_value = $this->notMeanOValue;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
	}
	
	/**
	 * ii型数据处理
	 *
	 * @access protected
	 *
	 * @param $day         integer 日期
	 * @param $product_id  integer 产品ID
	 * @param $customer_id string 客户ID
	 * @param $ctype       integer 渠道
	 * @param $o_field     string 字段名称
	 * @param $aipkey      array 该客户下的apikey
	 *
	 * @return void
	 **/
	protected function computeIIInfo($day, $product_id, $customer_id, $ctype, $o_field, $apikey)
	{
		$product_id = intval($product_id);
		$data       = $this->initBaseCollect();
		//-1
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', -1)
								   ->sum('cnt');
		$o_value = -1;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//0字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 3)
								   ->sum('cnt');
		$o_value = 0;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//1字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 7)
								   ->sum('cnt');
		$o_value = 1;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//2字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 14)
								   ->sum('cnt');
		$o_value = 2;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//3字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 21)
								   ->sum('cnt');
		$o_value = 3;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//4字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 30)
								   ->sum('cnt');
		$o_value = 4;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//5字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 60)
								   ->sum('cnt');
		$o_value = 5;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//6字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', 90)
								   ->sum('cnt');
		$o_value = 6;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
		
		//失败字段
		$number  = MongoCuccMonitor::where('day', $day)
								   ->whereIn('apikey', $apikey)
								   ->where('o_field', $o_field)
								   ->where('ctype', $ctype)
								   ->where('o_value', $this->notMeanOValue)
								   ->sum('cnt');
		$o_value = $this->notMeanOValue;
		$this->insertMongo(compact('day', 'product_id', 'customer_id', 'ctype', 'o_value', 'number'));
	}
	
	/**
	 * 获取客户与apikey的对应关系表
	 *
	 * @access protected
	 *
	 * @param $day integer 日期
	 *
	 * @return array
	 **/
	protected function getCustomerInfo($day)
	{
		//获取当日的所有数据的APIKEY
		$apikey = MongoCuccMonitor::where('day', $day)
								  ->pluck('apikey')
								  ->toArray();
		if (empty($apikey)) {
			throw new \Exception('未在数据表中找到当天的数据');
		}
		$apikey = array_values(array_unique($apikey));
		if ($day >= ********) {
			$apikey = array_map(function ($item) {
				return Masking::decode($item);
			}, $apikey);
		}
		//获取账号数据
		$accountInfo = Account::whereIn('apikey', $apikey)
							  ->pluck('customer_id', 'apikey')
							  ->toArray();
		
		$data = [];
		array_walk($accountInfo, function ($customer_id, $apikey) use (&$data) {
			if (!empty($apikey)) {
				$data[$customer_id][] = $apikey;
			}
		});
		
		return $data;
	}
	
	/**
	 * 增加整理成功的数据
	 *
	 * @access protected
	 *
	 * @param $data array 需要增加的数据
	 *
	 * @return void
	 **/
	protected function insertMongo($data)
	{
		if ($data['number'] != 0) {
			MongoSecondaryProcessingStatis::updateOrcreate(array_only($data, $this->uniqueIndex), $data);
		}
		$this->output->progressAdvance();
	}
	
	/**
	 * 初始化统计类字段的某一个产品的数据集合
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function initBaseCollect()
	{
		$base = [];
		for ($i = -1; $i <= 6; $i++) {
			$base[$i] = [
				'name'   => $i,
				'number' => 0,
			];
		}
		
		return $base;
	}
	
	/**
	 * 对APIKEY进行加密换算
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/31 10:20
	 *
	 * @param $apikey array
	 * @param $date   string 日期 Ymd
	 *
	 * @return array
	 **/
	protected function apikeyEncode($apikey, $date)
	{
		if ($date < ********) {
			return $apikey;
		}
		
		return array_merge($apikey, array_map(function ($item) {
			return Masking::encode($item);
		}, $apikey));
	}
}