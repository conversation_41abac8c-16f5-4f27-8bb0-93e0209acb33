<?php

namespace App\Console\Commands;


use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Symfony\Component\Console\Helper\ProgressBar;

class TestWashChannelLogs extends Command
{
	protected $signature = 'test:wash_channel_logs';
	
	/**
	 * @var ProgressBar
	 */
	protected $progressBar;
	
	protected $notfoundUserLogSid = [];
	
	public function handle()
	{
		$total             = TestChannelLogs::count();
		$this->progressBar = $this->output->createProgressBar($total);
		//获取总数据量
		TestChannelLogs::chunk(1000, function ($data) {
			$insert_data = [];
			$data->map(function ($item) use (&$insert_data) {
				$res = $this->washItem($item);
				if (false !== $res) {
					$insert_data[] = $res;
				}
				$this->progressBar->advance();
			});
			
			//插入数据
			TestLogsChannel::insert($insert_data);
		});
		
		$this->progressBar->finish();
	}
	
	/**
	 * 清洗单条数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:16
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return array|false
	 */
	protected function washItem($item)
	{
		$sid               = $item->sid;
		$user_request_info = $this->getUserRequestInfo($sid);
		
		//查找用户请求日志，如果不存在，或者条数大于1，则不处理
		if (count($user_request_info) == 0) {
			$this->notfoundUserLogSid[] = $sid;
			
			return false;
		} else if (count($user_request_info) > 1) {
			$this->output->error("存在多个sid=`{$sid}`的用户请求数据");
			
			return false;
		}
		$user_request_info = $user_request_info[0];
		
		//如果不是蚂蚁的数据，过滤掉
		$apikey = $user_request_info['apikey'];
		if ('ImfEhGgrki42/N1xKP3t7K+U+Qgd70iYL38P4CuGDYqE1jtjaTFp5nvB4hf2HWNn' != $apikey) {
			return false;
		}
		
		//产品ID
		$product_id = $this->getProductId($user_request_info);
		
		//渠道ID
		$channel_id = $item->channel;
		
		//入参
		$in_param = $item->tel_sha256;
		
		//执行时间
		$run_time = bcmul($item->take_seconds, 1000);
		
		//状态码
		$status = $this->getStatus($item);
		
		//info
		$message = json_decode($item->message, true);
		$info    = $message['error'];
		
		//value
		$value = 0;
		if (0 == $status) {
			$data = json_decode($user_request_info['data'], true);
			if (array_key_exists('c_cTimesIn30', $data) || array_key_exists('c_numIn30', $data)) {
				$value = array_key_exists('c_cTimesIn30', $data) ? $data['c_cTimesIn30'] : $data['c_numIn30'];
			} else {
				throw new \Exception("value格式不正确{$user_request_info['data']}。sid={$sid}");
				
			}
		}
		
		//sid
		$sid = $item->sid;
		
		return compact('product_id', 'channel_id', 'in_param', 'run_time', 'status', 'info', 'value', 'sid');
	}
	
	/**
	 * 获取状态码
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:41
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return integer
	 */
	protected function getStatus($item)
	{
		if ($item->take_seconds > 2.8) {
			return 599;
		}
		
		if (empty($item->output)) {
			return 598;
		}
		
		switch ($item->channel) {
			case 1:
				//中国联通
				return $this->getCUCCStatus($item);
				break;
			case 2:
				//中国电信（不存在）
				break;
			case 3:
				//北京移动
				return $this->getBJCMCCStatus($item);
				break;
			case 4:
				//江苏移动
				return $this->getJSCMCCStatus($item);
				break;
			case 5:
				//四川移动
				return $this->getSCCMCCStatus($item);
				break;
			case 6:
				//山东移动
				return $this->getSDCMCCStatus($item);
				break;
			case 7:
				//河北移动
				return $this->getHBCMCCStatus($item);
				break;
			case 8:
				//全国移动（不存在）
				break;
		}
		throw new \Exception("未知的渠道ID，sid={$item->sid}");
	}
	
	/**
	 * 获取河北移动的状态码
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:52
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return integer
	 */
	protected function getHBCMCCStatus($item)
	{
		$output = json_decode($item->output, true);
		
		if (!array_key_exists('code', $output)) {
			if (array_key_exists('res_code', $output)) {
				return $output['res_code'];
			}
			throw new \Exception("河北移动不存在code/res_code输出参数，解析失败。sid={$item->sid}");
		}
		
		return $output['code'];
	}
	
	/**
	 * 获取山东移动的状态码
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:52
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return integer
	 */
	protected function getSDCMCCStatus($item)
	{
		$output = json_decode($item->output, true);
		
		if (!array_key_exists('respCode', $output)) {
			throw new \Exception("山东移动不存在respCode输出参数，解析失败。sid={$item->sid}");
		}
		
		return $output['respCode'];
	}
	
	/**
	 * 获取江苏移动的状态码
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:52
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return integer
	 */
	protected function getJSCMCCStatus($item)
	{
		$output = json_decode($item->output, true);
		
		//不存在value则代表失败
		if (array_key_exists('error_msg', $output)) {
			list($error, $code) = explode('-', $output['error_msg']);
			
			return $code;
		}
		
		return 0;
	}
	
	/**
	 * 获取北京移动的状态码
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:52
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return integer
	 */
	protected function getBJCMCCStatus($item)
	{
		$output = json_decode($item->output, true);
		if (!array_key_exists('code', $output)) {
			throw new \Exception("北京移动不存在code输出参数，解析失败。sid={$item->sid}");
		}
		
		return $output['code'];
	}
	
	/**
	 * 获取联通的状态码
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:52
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return integer
	 */
	protected function getCUCCStatus($item)
	{
		$output = json_decode($item->output, true);
		if (!array_key_exists('code', $output)) {
			throw new \Exception("中国联通不存在code输出参数，解析失败。sid={$item->sid}");
		}
		
		return $output['code'] == '00' ? 0 : $output['code'];
	}
	
	/**
	 * 获取四川移动的状态码
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:41
	 *
	 * @param $item Collection 单条渠道请求记录
	 *
	 * @return integer
	 */
	protected function getSCCMCCStatus($item)
	{
		$output = json_decode($item->output, true);
		if (!array_key_exists('code', $output)) {
			throw new \Exception("四川移动不存在code输出参数，解析失败。sid={$item->sid}");
		}
		
		return in_array($output['code'], [10001, 10002]) ? 0 : $output['code'];
	}
	
	
	/**
	 * 获取对应的用户请求数据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 15:43
	 *
	 * @param $sid string 客户ID
	 *
	 * @return array
	 */
	protected function getUserRequestInfo($sid)
	{
		return TestUserLogs::where('sid', $sid)
						   ->get()
						   ->toArray();
	}
	
	
	/**
	 * 根据用户请求日志，获取请求的子产品ID
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/11/17 16:36
	 *
	 * @param $data array 用户请求日志
	 *
	 * @return integer
	 */
	protected function getProductId($data)
	{
		$input = json_decode($data['input'], true);
		
		return $input['products'][0];
	}
}


class TestChannelLogs extends Model
{
	protected $table = 'channel_request_log_2020_10';
	
	protected $connection = 'test';
	
}

class TestUserLogs extends Model
{
	protected $table      = 'user_request_log_202010';
	protected $connection = 'test';
}

class TestLogsChannel extends Model
{
	protected $table      = 'log_channel';
	protected $connection = 'test';
}