<?php

namespace App\Console\Commands;

use App\Models\MongoUpstream;
use App\Models\MongoUpstreamBillEconomy;
use App\Models\UpstreamChannelPrice;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

//计算金盾帮助邦秒验节约的成本账单
class GenEconomyUpstreamBill extends Command
{
	use WechatExceptionTrait;
	protected $signature   = 'upstream:gen-economy-bill
	{--bill_date= : 账单日，默认昨日日期，格式：Ymd}
    {--days=1 : 批量生成账单天数，默认1}
	';
	protected $description = '计算金盾帮助邦秒验节约的成本账单';
	
	//每个产品在计算节约账单中所需要依据的渠道名称，系统根据渠道名称找到当前适配的单价，然后计算节约成本账单
	protected $accord = [
		216 => 'czty',
		203 => 'czty',
		232 => 'lingdi',
		238 => 'lingdi',
	];
	
	//金盾渠道的标识
	protected $goldShieldUpstreamChannel = ['jindun', 'jindunMD5', 'jindunSHA256'];
	//账单日
	protected $bill_date;
	protected $days = 1;
	
	public function handle()
	{
		if ($this->setAttribute()) {
			$step = 0;
			while (true) {
				$bill_date = intval(date('Ymd', strtotime("+{$step} days", strtotime($this->bill_date))));
				//生成当前账单日的节约成本账单
				$this->genBill($bill_date);
				$step++;
				if ($step == $this->days) {
					break;
				}
			}
		}
	}
	
	public function __construct()
	{
		parent::__construct();
	}
	
	/**
	 * 设置参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/10 10:34
	 *
	 * @return boolean
	 **/
	protected function setAttribute()
	{
		$this->bill_date = empty($this->option('bill_date')) ? date('Ymd', strtotime('-1 days')) : $this->option('bill_date');
		
		if ($this->bill_date >= date('Ymd') || $this->bill_date < 20200101 || !preg_match('/^\d{8}$/', $this->bill_date)) {
			$this->output->error('日期格式不正确');
			
			return false;
		}
		
		$this->days = $this->option('days') ?: 1;
		if (!is_numeric($this->days) || $this->days <= 0) {
			$this->output->error('天数格式不正确');
			
			return false;
		}
		
		return true;
	}
	
	/**
	 * 生成某日的节约成本账单
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/10 10:51
	 *
	 * @param $date integer 日期
	 *
	 * @return void
	 **/
	protected function genBill($date)
	{
		if ($date >= date('Ymd')) {
			return;
		}
		
		//获取量级
		$statistics = $this->getGoldShieldUpstreamStatistics($date);
		
		//遍历每个产品的量级，然后计算账单
		array_walk($statistics, function ($item) use ($date) {
			
			$product_id = $item['_id'];
			$channel    = array_get($this->accord, $product_id, '');
			if (empty($channel)) {
				$this->wechat("邦秒验新增产品采用金盾分流数据源，脚本[{$this->name}]暂不支持：[productId:{$product_id}]");
				
				return;
			}
			
			//获取某产品的当前日期的计费配置
			$feeConfig = UpstreamChannelPrice::where('start_date', '<=', date('Y-m-d', strtotime($date)))
											 ->where('channel', $channel)
											 ->where('product_id', $product_id)
											 ->select('price')
											 ->first()
											 ->toArray();
			if (empty($feeConfig)) {
				$this->wechat("脚本[{$this->name}]中邦秒验产品[{$product_id}]暂未找到渠道[{$channel}]在适用于日期[{$date}]的计费配置");
				
				return;
			}
			$price = json_decode($feeConfig['price'], true);
			
			//计算账单
			$this->computeBill($item, $price, $date);
		});
		
		$this->output->success("{$date}");
	}
	
	/**
	 * 计算账单
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/10 11:17
	 *
	 * @param $statistics array 统计数据
	 * @param $price      array 单价
	 * @param $date       integer 日期
	 *
	 * @return void
	 **/
	protected function computeBill($statistics, $price, $date)
	{
		$product_id = $statistics['_id'];
		
		$number  = 0;
		$money   = 0;
		$details = [];
		foreach ($price as $priceField => $priceItem) {
			$numberItem = array_get($statistics, $priceField, 0);
			$moneyItem  = floatval(bcmul($numberItem, $priceItem, 4));
			$details[]  = [
				'money'  => $moneyItem,
				'number' => $numberItem,
				'price'  => $priceItem,
			];
			$number     = bcadd($numberItem, $number);
			$money      = bcadd($money, $moneyItem, 4);
		}
		
		$money  = floatval($money);
		$number = intval($number);
		
		//需要保存的数据
		$data = compact('product_id', 'date', 'number', 'money', 'price', 'details');
		
		//保存数据
		MongoUpstreamBillEconomy::updateOrCreate(array_only($data, ['product_id', 'date']), $data);
	}
	
	/**
	 * 获取金盾的成本调用量
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/7/10 10:52
	 *
	 * @param $date integer 日期
	 *
	 * @return array
	 **/
	protected function getGoldShieldUpstreamStatistics($date)
	{
		return MongoUpstream::query()
							->raw(function ($collcetion) use ($date) {
								$aggregate = [
									[
										'$match' => [
											'channel' => [
												'$in' => $this->goldShieldUpstreamChannel,
											],
											'date'    => $date,
										],
									],
									[
										'$group' => [
											'_id'    => '$product_id',
											'yd'     => ['$sum' => '$data.yd'],
											'dx'     => ['$sum' => '$data.dx'],
											'lt'     => ['$sum' => '$data.lt'],
											'succ'   => ['$sum' => '$data.succ'],
											'failed' => ['$sum' => '$data.failed'],
										],
									],
								];
			
								return $collcetion->aggregate($aggregate);
							})
							->toArray();
	}
}