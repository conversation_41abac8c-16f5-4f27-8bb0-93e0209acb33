<?php

namespace App\Console\Commands;

use App\Models\MongoBillMonth;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;
use App\Models\MongoLog;

class BillMonthRollbackCommand extends Command
{
    use WechatExceptionTrait;

    /** @var string 命令 */
    public $signature = 'bill:rollback';

    /** @var string 命令提示 */
    public $description = '回滚上次执行generate:bill-month --range=$range产生的账单和日志';

    /** @var   object 进度条 */
    private $bar;

    /** @var string 回滚操作人 */
    private $operator_person;

    /** @var string 回滚的原因 */
    private $operator_reason;

    /** @var string redis链接 */
    private $redis_bill_connection = 'db_backend';

    /** @var string 账单rollback的key */
    private $redis_bill_rollback_key = 'back_api_bill_rollback_key';

    /** @var array 要回滚到uuid列表 */
    private $list_uuids_wait_rollback;

    /**
     * @throws \Exception
     */
    public function handle()
    {
        try {
            $this->handleDo();
            $msg = $this->operator_person . ' 恭喜, 回滚账单成功! 请重新生成账单';
            $this->outputInOneWay($msg);
        } catch (\Exception $e) {
            $msg = $this->operator_person . ' 抱歉,回滚账单失败, msg:' . $e->getMessage();
            $this->outputInOneWay($msg, false);
        }
    }

    /**
     * 具体执行
     * @throws \Exception
     */
    private function handleDo()
    {
        // 检查输入
        $this->validatePromptingInput();

        // 回滚
        $this->rollbackAction();
    }

    /**
     * 回滚
     * @throws \Exception
     */
    private function rollbackAction()
    {
        // 设置uuids列表
        $this->setListUuidsWhichRollback();

        // 回滚账单 && 日志
        $this->rollbackBillAndLog();
    }

    /**
     * 回滚日志
     */
    private function rollbackBillAndLog()
    {
        $this->output->text('开始回滚日志和账单');
        $this->bar = $this->output->createProgressBar(count($this->list_uuids_wait_rollback));
        array_walk($this->list_uuids_wait_rollback, function($uuid){
            MongoLog::where(compact('uuid'))->delete();
            MongoBillMonth::where(compact('uuid'))->delete();
            $this->bar->advance();
        });
        $this->bar->finish();
        $this->output->text('日志和账单回滚成功');
    }

    /**
     * 设置uuids列表
     * @throws \Exception
     */
    private function setListUuidsWhichRollback()
    {
        $uuids_str  = app('redis')->connection($this->redis_bill_connection)
            ->get($this->redis_bill_rollback_key);

        // 没有找到历史记录
        if (!$uuids_str) {
            throw new \Exception('请确认之前是否执行过生成账单的操作; 未找到历史记录');
        }

        $this->list_uuids_wait_rollback = json_decode($uuids_str, true);
    }

    /**
     * 校验输入
     * @throws \Exception
     */
    private function validatePromptingInput()
    {
        $this->operator_person = $operator_name = trim($this->ask('请问您的姓名,我们需要记录下来，方便后期追述！'));
        if (!$operator_name) {
            throw new \Exception('请问您的姓名,我们需要记录下来，方便后期追述!');
        }

        $confirmation = $this->choice('请问您确定要执行rollback操作吗?', ['Y', 'N'], 0);
        if (strtoupper($confirmation) == 'N') {
            throw new \Exception('您选择了不再继续执行账单的回滚操作!');
        }

        $this->operator_reason = $reason = $this->ask('请输入这样的执行的原因!');
    }

    /**
     * 统一输出
     * @param string $msg
     * @param bool $success 是否选择成功的输出方式
     * @throws \Exception
     */
    private function outputInOneWay(string $msg, $success = true)
    {
        if ($success) {
            $this->output->success($msg);
            MongoLog::create([
                'operator_person' => $this->operator_person,
                'msg' => '回滚成功',
                'operator_result' => true,
                'action' => 'rollback',
                'reason' => $this->operator_reason
            ]);
            $this->wechatException($msg);
            return;
        }

        $this->output->error($msg);
        $this->wechatException($msg);
        MongoLog::create([
            'operator_person' => $this->operator_person,
            'msg' => $msg,
            'operator_result' => false,
            'action' => 'rollback',
            'reason' => $this->operator_reason
        ]);
    }
}
