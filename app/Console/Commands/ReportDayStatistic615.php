<?php

namespace App\Console\Commands;


class ReportDayStatistic615 extends ProductDayReport
{
    public function __construct()
    {
        $defaultDate     = date('Ymd', strtotime('-1 days'));
        $this->signature = 'reportDay:statistic_615
        {--date=' . $defaultDate . ' : 发送的日报的日期}
        {--addressee= : 定向发送给收件人（邮箱地址），多个邮箱地址以,隔开}
        ';
        parent::__construct();
    }

    protected $description = '金盾调用量日报表';

    protected $emailConfigScene = 'report_day_statistic_615';
    protected $productId        = [
        615,
        641,
        642,
        643,
        644,
        645,
        646,
        647,
        648,
        649,
        661,
        662,
        663
    ];
    protected $productName      = '金盾';
    protected $statisticsField  = [
        'succ'     => ['$sum' => '$stat_data.succ'],
        'cus_all'  => ['$sum' => '$stat_data.cus_all'],
        'cus_succ' => ['$sum' => '$stat_data.cus_succ'],
        'cdnums'   => ['$sum' => '$stat_data.cdnums']
    ];
    protected $sortField        = 'cus_all';
    protected $compareDays      = 1;
    protected $excelRow         = [
        ['name' => '客户ID', 'width' => 20, 'field' => 'customer_id'],
        ['name' => '客户名称', 'width' => 24, 'field' => 'customer_name'],
        ['name' => '签约类型', 'width' => 18, 'field' => 'contract_status_string'],
        ['name' => '主接口总查询量', 'width' => 18, 'field' => 'cus_all', 'callback' => 'numberFormat'],
        ['name' => '主接口有效查询量', 'width' => 22, 'field' => 'cus_succ', 'callback' => 'numberFormat'],
        ['name' => '有效查询字段量', 'width' => 18, 'field' => 'succ', 'callback' => 'numberFormat'],
        ['name' => '查得量', 'width' => 18, 'field' => 'cdnums', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums', 'callback' => 'ratioFormat'],
        ['name' => '预警提示(贷前)', 'width' => 18, 'field' => 'cdnums_641', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_641', 'callback' => 'ratioFormat'],
        ['name' => '小号中介号', 'width' => 18, 'field' => 'cdnums_642', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_642', 'callback' => 'ratioFormat'],
        ['name' => '信用风险等级', 'width' => 18, 'field' => 'cdnums_643', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_643', 'callback' => 'ratioFormat'],
        ['name' => '欺诈风险等级', 'width' => 18, 'field' => 'cdnums_644', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_644', 'callback' => 'ratioFormat'],
        ['name' => '活跃指数', 'width' => 18, 'field' => 'cdnums_645', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_645', 'callback' => 'ratioFormat'],
        ['name' => '当天查询次数', 'width' => 18, 'field' => 'cdnums_646', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_646', 'callback' => 'ratioFormat'],
        ['name' => '近10天查询次数', 'width' => 18, 'field' => 'cdnums_647', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_647', 'callback' => 'ratioFormat'],
        ['name' => '背景行业类型', 'width' => 18, 'field' => 'cdnums_648', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_648', 'callback' => 'ratioFormat'],
        ['name' => '预警提示(贷后)', 'width' => 18, 'field' => 'cdnums_649', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_649', 'callback' => 'ratioFormat'],
        ['name' => '信用风险等级V2', 'width' => 18, 'field' => 'cdnums_661', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_661', 'callback' => 'ratioFormat'],
        ['name' => '欺诈风险等级V2', 'width' => 18, 'field' => 'cdnums_662', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_662', 'callback' => 'ratioFormat'],
        ['name' => '欺诈风险等级（定制）', 'width' => 22, 'field' => 'cdnums_663', 'callback' => 'numberFormat'],
        ['name' => '查得率', 'width' => 18, 'field' => 'rate_cdnums_663', 'callback' => 'ratioFormat']
    ];


    protected function disposeReportInfo($customerInfo, $statisticInfo)
    {
        $apikeyCustomerIdMapping = $customerInfo['apikeyCustomerIdMapping'];
        $yuloreApikey            = $customerInfo['yuloreApikey'];
        $customerInfo            = $customerInfo['customerInfo'];

        $base = [
            'customer_id'            => '',
            'customer_name'          => '',
            'contract_status'        => '',
            'contract_status_string' => '',
            'status'                 => 1,
            'cus_all'                => 0,
            'cus_succ'               => 0,
            'succ'                   => 0,
            'cdnums'                 => 0
        ];
        array_walk($this->productId, function ($productId) use (&$base) {
            $base = array_merge($base, ["all_{$productId}" => 0, "succ_{$productId}" => 0, "cdnums_{$productId}" => 0]);
        });

        $notYuloreTotal                  = $total = $base;
        $notYuloreTotal['customer_name'] = '客户调用量合计';
        $total['customer_name']          = '总合计';

        $result = [];


        array_walk($statisticInfo,
            function ($item) use (&$result, &$total, &$notYuloreTotal, $apikeyCustomerIdMapping, $yuloreApikey, $customerInfo, $base) {
                $date = $item['date'];
                if ($date != $this->date) {
                    return;
                }
                $apikey    = $item['apikey'];
                $productId = $item['product_id'];
                $succ      = array_get($item, 'succ');
                $cdnums    = array_get($item, 'cdnums');

                $customerId = $apikeyCustomerIdMapping[$apikey];
                if (empty($customerId)) {
                    return;
                }

                if (!($customerInfo = array_get($customerInfo, $customerId))) {
                    return;
                }

                if (!array_key_exists($customerId, $result)) {
                    $result[$customerId]                           = $base;
                    $result[$customerId]['customer_id']            = $customerId;
                    $result[$customerId]['customer_name']          = $customerInfo['customer_name'];
                    $result[$customerId]['contract_status']        = $customerInfo['contract_status'];
                    $result[$customerId]['contract_status_string'] = $customerInfo['contract_status_string'];
                    $result[$customerId]['status']                 = $customerInfo['status'];
                }

                if ($productId == 615) {
                    $cus_all                         = array_get($item, 'cus_all');
                    $cus_succ                        = array_get($item, 'cus_succ');
                    $result[$customerId]['cus_all']  += $cus_all;
                    $result[$customerId]['cus_succ'] += $cus_succ;

                    //合计
                    $total['cus_all']  += $cus_all;
                    $total['cus_succ'] += $cus_succ;

                    //非内部账号调用合计
                    if (in_array($apikey, $yuloreApikey)) {
                        return;
                    }
                    $notYuloreTotal['cus_all']  += $cus_all;
                    $notYuloreTotal['cus_succ'] += $cus_succ;
                    return;
                }

                $result[$customerId]["succ_{$productId}"]   += $succ;
                $result[$customerId]['succ']                += $succ;
                $result[$customerId]["cdnums_{$productId}"] += $cdnums;
                $result[$customerId]['cdnums']              += $cdnums;


                //合计
                $total["succ_{$productId}"]   += $succ;
                $total['succ']                += $succ;
                $total["cdnums_{$productId}"] += $cdnums;
                $total['cdnums']              += $cdnums;

                //非内部账号调用合计
                if (in_array($apikey, $yuloreApikey)) {
                    return;
                }
                $notYuloreTotal["succ_{$productId}"]   += $succ;
                $notYuloreTotal['succ']                += $succ;
                $notYuloreTotal["cdnums_{$productId}"] += $cdnums;
                $notYuloreTotal['cdnums']              += $cdnums;
            });

        array_unshift($result, $notYuloreTotal);
        array_unshift($result, $total);
        return $result;
    }

    protected function computeResultItem($data)
    {

        $data['rate_cdnums'] = $this->computeRatio($data['cdnums'], $data['succ']);
        array_walk($this->productId, function ($productId) use (&$data) {
            $data["rate_cdnums_{$productId}"] = $this->computeRatio($data["cdnums_{$productId}"],
                $data["succ_{$productId}"]);
        });

        return $data;
    }

    protected function filterResultItem($data)
    {
        if ($data['cus_all'] == 0) {
            if ($data['status'] != 1) {
                return false;
            }
            if (in_array($data['contract_status'], [3, 4, 5])) {
                return false;
            }
        }
        return true;
    }
}