<?php

namespace App\Console\Commands;

use App\Mail\BxfQudaoFenbuEmail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Models\BxfCntTimeModel;
use App\Models\EmailConfig;

class BxfQudaoFenbuCommand extends Command
{
    /**
     * 命令行执行命令
     */
    protected $signature = 'bxf:qudao-fenbu';
    protected $scene = 'bxf_qudao_fenbu';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        //获取邮件发送人
        $email_model = new EmailConfig();
        $email_config = $email_model->getRecipientsAndCCByScene($this->scene);
        $to_email = array_column($email_config['recipients'], 'address');
        $cc_email = array_column($email_config['cc'], 'address');
        //获取当天的数据
        $day = date('Y-m-d', strtotime("-1 day"));
        $obj = BxfCntTimeModel::where(['input_day'=>$day])->get();
        if(!$obj){
            return false;
        }
        $res = $obj->toArray();

        $new_res = [];
        array_walk($res, function($value, $key) use (&$new_res){
            //对数据做处理
           $new_res[$value['cday']][$value['flag']] = $value;
        });
        $flag_arr = [
            'lt' => 0,
            'bjyd' => 0,
            'hbyd' => 0,
            'jsyd' => 0,
            'sdyd' => 0,
            'scyd' => 0
        ];

        $arr1 = [
            'lt_0' => isset($new_res['day1']['lt_0']) ? $new_res['day1']['lt_0'] : $flag_arr,
            'eq_0' => isset($new_res['day1']['eq_0']) ? $new_res['day1']['eq_0'] : $flag_arr,
            'gt_0' => isset($new_res['day1']['gt_0']) ? $new_res['day1']['gt_0'] : $flag_arr

        ];
        $arr7 = [
            'lt_0' => isset($new_res['day7']['lt_0']) ? $new_res['day7']['lt_0'] : $flag_arr,
            'eq_0' => isset($new_res['day7']['eq_0']) ? $new_res['day7']['eq_0'] : $flag_arr,
            'gt_0' => isset($new_res['day7']['gt_0']) ? $new_res['day7']['gt_0'] : $flag_arr
        ];
        $arr30 = [
            'lt_0' => isset($new_res['day30']['lt_0']) ? $new_res['day30']['lt_0'] : $flag_arr,
            'eq_0' => isset($new_res['day30']['eq_0']) ? $new_res['day30']['eq_0'] : $flag_arr,
            'gt_0' => isset($new_res['day30']['gt_0']) ? $new_res['day30']['gt_0'] : $flag_arr
        ];
        $arr90 = [
            'lt_0' => isset($new_res['day90']['lt_0']) ? $new_res['day90']['lt_0'] : $flag_arr,
            'eq_0' => isset($new_res['day90']['eq_0']) ? $new_res['day90']['eq_0'] : $flag_arr,
            'gt_0' => isset($new_res['day90']['gt_0']) ? $new_res['day90']['gt_0'] : $flag_arr
        ];

      /*  $arr1 = [
            'lt' => [
                'lt' => 77259,
                'bjyd' => 2728,
                'hbyd' => 6555,
                'jsyd' => 12034,
                'sdyd' => 6089,
                'scyd' => 12775
            ],
            'eq' => [
                'lt' => 500282,
                'bjyd' => 18130,
                'hbyd' => 69269,
                'jsyd' => 67669,
                'sdyd' => 51663,
                'scyd' => 104322
            ],
            'gt' => [
                'lt' => 40961,
                'bjyd' => 2324,
                'hbyd' => 5870,
                'jsyd' => 8693,
                'sdyd' => 1517,
                'scyd' => 9344
            ]
        ];*/
        $total_lt1 =  array_sum(array_column($arr1,'lt'));
        $total_bjyd1 =  array_sum(array_column($arr1,'bjyd'));
        $total_hbyd1 =  array_sum(array_column($arr1,'hbyd'));
        $total_jsyd1 =  array_sum(array_column($arr1,'jsyd'));
        $total_sdyd1 =  array_sum(array_column($arr1,'sdyd'));
        $total_scyd1 =  array_sum(array_column($arr1,'scyd'));

        foreach($arr1 as $key=>&$value){
            $ltb = $total_lt1 == 0 ? '0' : sprintf("%.2f",($value['lt']/$total_lt1) * 100);
            $value['ltb'] = $ltb == 0 ? '0' : $ltb.'%';
            $bjydb = $total_bjyd1 == 0 ? '0' : sprintf("%.2f",($value['bjyd']/$total_bjyd1) * 100);
            $value['bjydb'] = $bjydb == 0 ? '0' : $bjydb.'%';
            $hbydb = $total_hbyd1 == 0 ? '0' : sprintf("%.2f",($value['hbyd']/$total_hbyd1) * 100);
            $value['hbydb'] = $hbydb == 0 ? '0' : $hbydb.'%';
            $jsydb = $total_jsyd1 == 0 ? '0' : sprintf("%.2f",($value['jsyd']/$total_jsyd1) * 100);
            $value['jsydb'] = $jsydb == 0 ? '0' : $jsydb.'%';
            $sdydb = $total_sdyd1 == 0 ? '0' : sprintf("%.2f",($value['sdyd']/$total_sdyd1) * 100);
            $value['sdydb'] = $sdydb == 0 ? '0' : $sdydb.'%';
            $scydb = $total_scyd1 == 0 ? '0' : sprintf("%.2f",($value['scyd']/$total_scyd1) * 100);
            $value['scydb'] = $scydb == 0 ? '0' : $scydb.'%';
        }
        $arr1['total'] = [
            'total_lt' => $total_lt1,
            'total_bjyd' => $total_bjyd1,
            'total_hbyd' => $total_hbyd1,
            'total_jsyd' => $total_jsyd1,
            'total_sdyd' => $total_sdyd1,
            'total_scyd' => $total_scyd1
        ];


        $total_lt7 =  array_sum(array_column($arr7,'lt'));
        $total_bjyd7 =  array_sum(array_column($arr7,'bjyd'));
        $total_hbyd7 =  array_sum(array_column($arr7,'hbyd'));
        $total_jsyd7 =  array_sum(array_column($arr7,'jsyd'));
        $total_sdyd7 =  array_sum(array_column($arr7,'sdyd'));
        $total_scyd7 =  array_sum(array_column($arr7,'scyd'));

        foreach($arr7 as $key=>&$value){
            $ltb = $total_lt7 == 0 ? '0' : sprintf("%.2f",($value['lt']/$total_lt7) * 100);
            $value['ltb'] = $ltb == 0 ? '0' : $ltb.'%';
            $bjydb = $total_bjyd7 == 0 ? '0' : sprintf("%.2f",($value['bjyd']/$total_bjyd7) * 100);
            $value['bjydb'] = $bjydb == 0 ? '0' : $bjydb.'%';
            $hbydb = $total_hbyd7 == 0 ? '0' : sprintf("%.2f",($value['hbyd']/$total_hbyd7) * 100);
            $value['hbydb'] = $hbydb == 0 ? '0' : $hbydb.'%';
            $jsydb = $total_jsyd7 == 0 ? '0' : sprintf("%.2f",($value['jsyd']/$total_jsyd7) * 100);
            $value['jsydb'] = $jsydb == 0 ? '0' : $jsydb.'%';
            $sdydb = $total_sdyd7 == 0 ? '0' : sprintf("%.2f",($value['sdyd']/$total_sdyd7) * 100);
            $value['sdydb'] = $sdydb == 0 ? '0' : $sdydb.'%';
            $scydb = $total_scyd7 == 0 ? '0' : sprintf("%.2f",($value['scyd']/$total_scyd7) * 100);
            $value['scydb'] = $scydb == 0 ? '0' : $scydb.'%';
        }
        $arr7['total'] = [
            'total_lt' => $total_lt7,
            'total_bjyd' => $total_bjyd7,
            'total_hbyd' => $total_hbyd7,
            'total_jsyd' => $total_jsyd7,
            'total_sdyd' => $total_sdyd7,
            'total_scyd' => $total_scyd7
        ];



        $total_lt30 =  array_sum(array_column($arr30,'lt'));
        $total_bjyd30 =  array_sum(array_column($arr30,'bjyd'));
        $total_hbyd30 =  array_sum(array_column($arr30,'hbyd'));
        $total_jsyd30 =  array_sum(array_column($arr30,'jsyd'));
        $total_sdyd30 =  array_sum(array_column($arr30,'sdyd'));
        $total_scyd30 =  array_sum(array_column($arr30,'scyd'));

        foreach($arr30 as $key=>&$value){
            $ltb = $total_lt30 == 0 ? '0' : sprintf("%.2f",($value['lt']/$total_lt30) * 100);
            $value['ltb'] = $ltb == 0 ? '0' : $ltb.'%';
            $bjydb = $total_bjyd30 == 0 ? '0' : sprintf("%.2f",($value['bjyd']/$total_bjyd30) * 100);
            $value['bjydb'] = $bjydb == 0 ? '0' : $bjydb.'%';
            $hbydb = $total_hbyd30 == 0 ? '0' : sprintf("%.2f",($value['hbyd']/$total_hbyd30) * 100);
            $value['hbydb'] = $hbydb == 0 ? '0' : $hbydb.'%';
            $jsydb = $total_jsyd30 == 0 ? '0' : sprintf("%.2f",($value['jsyd']/$total_jsyd30) * 100);
            $value['jsydb'] = $jsydb == 0 ? '0' : $jsydb.'%';
            $sdydb = $total_sdyd30 == 0 ? '0' : sprintf("%.2f",($value['sdyd']/$total_sdyd30) * 100);
            $value['sdydb'] = $sdydb == 0 ? '0' : $sdydb.'%';
            $scydb = $total_scyd30 == 0 ? '0' : sprintf("%.2f",($value['scyd']/$total_scyd30) * 100);
            $value['scydb'] = $scydb == 0 ? '0' : $scydb.'%';
        }
        $arr30['total'] = [
            'total_lt' => $total_lt30,
            'total_bjyd' => $total_bjyd30,
            'total_hbyd' => $total_hbyd30,
            'total_jsyd' => $total_jsyd30,
            'total_sdyd' => $total_sdyd30,
            'total_scyd' => $total_scyd30
        ];


        $total_lt90 =  array_sum(array_column($arr90,'lt'));
        $total_bjyd90 =  array_sum(array_column($arr90,'bjyd'));
        $total_hbyd90 =  array_sum(array_column($arr90,'hbyd'));
        $total_jsyd90 =  array_sum(array_column($arr90,'jsyd'));
        $total_sdyd90 =  array_sum(array_column($arr90,'sdyd'));
        $total_scyd90 =  array_sum(array_column($arr90,'scyd'));

        foreach($arr90 as $key=>&$value){
            $ltb = $total_lt90 == 0 ? '0' : sprintf("%.2f",($value['lt']/$total_lt90) * 100);
            $value['ltb'] = $ltb == 0 ? '0' : $ltb.'%';
            $bjydb = $total_bjyd90 == 0 ? '0' : sprintf("%.2f",($value['bjyd']/$total_bjyd90) * 100);
            $value['bjydb'] = $bjydb == 0 ? '0' : $bjydb.'%';
            $hbydb = $total_hbyd90 == 0 ? '0' : sprintf("%.2f",($value['hbyd']/$total_hbyd90) * 100);
            $value['hbydb'] = $hbydb == 0 ? '0' : $hbydb.'%';
            $jsydb = $total_jsyd90 == 0 ? '0' : sprintf("%.2f",($value['jsyd']/$total_jsyd90) * 100);
            $value['jsydb'] = $jsydb == 0 ? '0' : $jsydb.'%';
            $sdydb = $total_sdyd90 == 0 ? '0' : sprintf("%.2f",($value['sdyd']/$total_sdyd90) * 100);
            $value['sdydb'] = $sdydb == 0 ? '0' : $sdydb.'%';
            $scydb = $total_scyd90 == 0 ? '0' : sprintf("%.2f",($value['scyd']/$total_scyd90) * 100);
            $value['scydb'] = $scydb == 0 ? '0' : $scydb.'%';
        }
        $arr90['total'] = [
            'total_lt' => $total_lt90,
            'total_bjyd' => $total_bjyd90,
            'total_hbyd' => $total_hbyd90,
            'total_jsyd' => $total_jsyd90,
            'total_sdyd' => $total_sdyd90,
            'total_scyd' => $total_scyd90
        ];
        $arr = [
            'arr1' => $arr1,
            'arr7' => $arr7,
            'arr30' => $arr30,
            'arr90' => $arr90
        ];

        Mail::to($to_email)
            ->cc($cc_email)
            ->send(new BxfQudaoFenbuEmail($arr));
    }
}
