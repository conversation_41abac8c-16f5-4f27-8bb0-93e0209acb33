<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/14 0014
 * Time: 18:26
 */

namespace App\Console\Commands;


use App\Models\Channel;
use App\Models\ChannelAccountAdjust;
use App\Models\ChannelAccountFixedFee;
use App\Models\ChannelInterface;
use App\Models\ChannelRemit;
use App\Models\MongoLogsStatisticsCustomerUsage;
use App\Models\MongoStatis;
use App\Models\BillCustomerIncome;
use App\Models\Product;
use App\Utils\DingDing;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use App\Models\BillCost;
use App\Models\BillProductIncome;
use App\Models\Crs\SystemUser;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerBillAdjust;
use App\Providers\RedisCache\RedisCache;
use App\Models\Account;
use App\Models\CustomerExpend;
use App\Models\StatisticsInterfaceUsage;

class Test extends Command
{
    protected $signature = 'test
    {--limit_month= : 是否限定月份（0不限定 、1限定）}
    {--month= : 查看日期(格式Y-m）}';

    public $degree = 6;

    /**
     * @throws \Exception
     */
    public function handle()
    {


        $list = ChannelRemit::getList();
        foreach ($list as $item){
            $channel_id = intval($item['channel_id']);
            if($channel_id == 0){
                $channel_name = '历史数据';
                $compay_name = '历史数据';
            }else{
                $channel_name = RedisCache::instance('channelId_label_mapping')->get($channel_id);
                $channel = Channel::getChannelById(['channel_id' => $channel_id]);
                $compay_name = $channel['company'];
            }

            $shoukuan_name = $item['name'];
            $shoukuan_bank = $item['bank'];
            $order_no = $item['remit_serial'];
            $money = $item['money'];
            $date = date('Ymd', $item['remit_date']);

            $info = $channel_name."\t".$compay_name."\t".$shoukuan_name."\t".$shoukuan_bank.
                    "\t".$money."\t".$date."\t".$order_no;

            $file = storage_path().'/logs/channel_remit.log';
            file_put_contents($file, $info.PHP_EOL, FILE_APPEND);
        }
        dd('ok');

        $start_date = ********;
        $end_date = ********;

        $file = storage_path().'/logs/channel_cost.log';
        //$file = storage_path().'/logs/channel_allcmcc_cost.log';
        $range = $this->showMonthRange($start_date, $end_date);
        foreach($range as $month){
            $month_start = date('Ymd', strtotime($month.'-01'));
            $month_end = $this->getLastDay($month.'-01');
            $month_start_str = date('Y-m-d', strtotime($month_start));
            $month_end_str = date('Y-m-d', strtotime($month_end));

            $channel_list = Channel::getAllChannellabel();
            foreach($channel_list as $channel){

                if($channel['channel_id'] > 100 || $channel['channel_id'] == 8){
                    //continue;
                }

                if($channel['channel_id'] != 110){
                    continue;
                }

                /*
                if($channel['channel_id'] != 8){
                    continue;
                }
                */


                $iids = ChannelInterface::getListByCondition([], ['id'], [$channel['channel_id']])->toArray();
                $iids = array_column($iids, 'id');
                if(empty($iids)){
                    continue;
                }

                /*
                foreach($iids as $iid){
                    if($iid == 257){
                        $i_name = '信用分_Y0l';
                    }else{
                        $i_name = '邦信分_评分Y02';
                    }
                */

                    $cost = BillCost::test(['s_date'=>$month_start, 'e_date'=>$month_end, 'interface_ids'=>$iids]);
                    //$cost = BillCost::test(['s_date'=>$month_start, 'e_date'=>$month_end, 'interface_ids'=>[$iid]]);
                    $real_usage = $cost[0]['s_number'] ?? 0;
                    $real_cost = $cost[0]['s_money'] ?? 0;
                    $adjust_cost = ChannelAccountAdjust::test(['s_date'=>$month_start_str, 'e_date'=>$month_end_str, 'interface_ids'=>$iids]);
                    //$adjust_cost = ChannelAccountAdjust::test(['s_date'=>$month_start_str, 'e_date'=>$month_end_str, 'interface_ids'=>[$iid]]);
                    $adjust_cost = $adjust_cost[0]['s_money'] ?? 0;
                    $fixed_cost = ChannelAccountFixedFee::test(['s_date'=>$month_start_str, 'e_date'=>$month_end_str, 'channel_id'=>$channel['channel_id']]);
                    $fixed_cost = $fixed_cost[0]['s_money'] ?? 0;

                    $total_cost = bcadd($real_cost, $adjust_cost, 2);
                    $total_cost = bcadd($total_cost, $fixed_cost, 2);
                    if($total_cost == 0){
                        continue;
                    }

                    $usage = StatisticsInterfaceUsage::test($month_start, $month_end, $iids);
                    //$usage = StatisticsInterfaceUsage::test($month_start, $month_end, [$iid]);
                    $sucess_usage = empty($usage['s_success']) ? 0 : $usage['s_success'];
                    $valid_usage = empty($usage['s_valid']) ? 0 : $usage['s_valid'];
                    $info = $month."\t".$channel['label']."\t".$total_cost."\t".$real_usage."\t".$sucess_usage."\t".$valid_usage;
                    //$info = $month."\t".$channel['label']."\t".$i_name."\t".$total_cost."\t".$real_usage."\t".$sucess_usage."\t".$valid_usage;

                    file_put_contents($file, $info.PHP_EOL, FILE_APPEND);
                //}

            }

        }

        dd('ok');
    }

    private function log($content)
    {

        $filename = __DIR__ . '/../../../runtime/test.log';
        $fp = fopen($filename, 'a+');

        $time = date('Y-m-d H:i:s');

        $content = $time . ' : ' . $content . "\n";

        fwrite($fp, $content);
    }


    public function getParams(){
        $params = [];

        $params['limit_month'] = $this->input->getOption('limit_month') ?: 0;
        if(empty($params['limit_month'])){
            $params['limit_month'] = 0;
        }
        $params['month'] = $this->input->getOption('month') ?: date('Y-m-d');

        return $params;
    }

    public function getDateRange($limit = 1, $month){
        $end_date = $this->getLastDay($month.'-01');//查看日期所在月的最后一天
        if($limit == 1){//限定月份
            $start_date = date('Ymd', strtotime($month.'-01'));
        }else{
            $start_date = $this->getMonth(11, str_replace('-', '', $month)).'01';
        }

        return [$start_date, $end_date];
    }

    /**
     * 获取某个日期所在月的最后一天
     * @param $date
     * @return false|string
     */
    public function getLastDay($date)
    {
        $firstday = date('Y-m-01', strtotime($date));
        $lastday = date('Ymd', strtotime("$firstday +1 month -1 day"));
        return $lastday;
    }

    //获取指定日期的前后几个月
    public function getMonth($sign, $month = '')
    {
        //得到系统的年月
        $tmp_date = empty($month) ? date("Ym") : $month;
        //切割出年份
        $tmp_year = substr($tmp_date,0,4);
        //切割出月份
        $tmp_mon = substr($tmp_date,4,2);
        // 得到当前月份的下几月
        $tmp_nextmonth = mktime(0,0,0,$tmp_mon+$sign,1,$tmp_year);
        // 得到当前月份的前几月
        $tmp_forwardmonth = mktime(0,0,0,$tmp_mon-$sign,1,$tmp_year);
        return $fm_next_month = date("Ym",$tmp_forwardmonth);
    }



    public function getEveryMonthIncome($customer_id, $father_id, $product_id, $month, &$customer_tmp, &$customer_apikeys, &$first_arr, &$sort_key){
        $first_arr[$sort_key]['customer_id'] = $customer_id;
        $first_arr[$sort_key]['father_id'] = $father_id;
        $first_arr[$sort_key]['product_id'] = $product_id;

        $first_arr[$sort_key]['customer_name'] = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
        $first_arr[$sort_key]['father_name'] = RedisCache::instance('productId_productName_mapping')->get($father_id);
        $first_arr[$sort_key]['product_name'] = RedisCache::instance('productId_productName_mapping')->get($product_id);
        $first_arr[$sort_key]['s_month'] = $month;
        //正常收入
        $income = $customer_tmp[$customer_id][$father_id][$product_id][$month]['total_money'];
        $condition['customer_id'] = $customer_id;
        $condition['father_id'] = $father_id;
        $condition['product_id'] = $product_id;
        $condition['s_date'] = $month.'01';
        $condition['e_date'] = $this->getLastDay($month.'01');
        //特殊消耗
        $incomesub = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 1]));
        $incomeadd = CustomerExpend::getBetweenDate(array_merge($condition, ['type' => 2]));
        $incomesub = $incomesub[0]['s_money'] ?? 0;
        $incomeadd = $incomeadd[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['income'] = bcsub(bcadd($income, $incomeadd, $this->degree), $incomesub, $this->degree);

        $condition2['apikey'] = $customer_apikeys;
        $condition2['product_id'] = $product_id;
        $condition2['s_date'] = $condition['s_date'];
        $condition2['e_date'] = $condition['e_date'];
        //普通成本
        $cost = BillCost::getBetweenDate($condition2);
        $cost = $cost[0]['s_money'] ?? 0;

        $condition3['customer_id'] = $customer_id;
        $condition3['product_id'] = $product_id;
        $condition3['s_date'] = $condition['s_date'];
        $condition3['e_date'] = $condition['e_date'];
        //客户成本调整
        $adjust_cost = CustomerBillAdjust::getBetweenDate($condition3);
        $adjust_cost = $adjust_cost[0]['s_money'] ?? 0;
        $first_arr[$sort_key]['cost'] = bcadd($cost, $adjust_cost, $this->degree);

        $customer_info = Customer::getCustomerInfo($customer_id);
        $first_arr[$sort_key]['salesman'] = $customer_info['salesman'];
        $sys_user = SystemUser::getPhoneByUserName($customer_info['salesman']);
        $dept_info = SystemDept::getInfoByDeptId($sys_user['dept_id']);
        $first_arr[$sort_key]['area'] = $dept_info['dept_name'];

        $sort_key++;

        return true;
    }



    //php 获取开始日期与结束日期之间所有月份
    public function showMonthRange($start, $end)
    {
        $end = date('Y-m', strtotime($end)); // 转换为月
        $range = [];
        $i = 0;
        do {
            $month = date('Y-m', strtotime($start . ' + ' . $i . ' month'));
            //echo $i . ':' . $month . '<br>';
            $range[] = $month;
            $i++;
        } while ($month < $end);

        return $range;
    }


}