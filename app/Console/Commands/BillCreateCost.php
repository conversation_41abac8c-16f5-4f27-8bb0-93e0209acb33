<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\Product;
use App\Providers\BillCost\CreateBillCost;
use Illuminate\Console\Command;

//计算成本账单
class BillCreateCost extends Command
{
	protected $signature = 'bill:create_bill_cost
	{--date= : 账单日，默认昨日（格式Ymd）}
    {--customer_id= : 客户ID（多个客户ID以,隔开）}
    {--apikey= : 账号标识（多个标识以,隔开）}
    {--father_id= : 父产品ID（多个产品ID以,隔开）}
    {--product_id= : 子产品ID（多个产品ID以,隔开）}
    {--interface_id= : 接口ID（多个接口ID以,隔开）}
    {--days=1 : 生成多少日的账单}';
	
	protected $description = '创建成本账单脚本';
	
	public function handle()
	{
		//获取参数
		$params = $this->getParams();
		
		$date          = array_get($params, 'date');
		$days          = array_get($params, 'days');
		$interface_ids = array_get($params, 'interface_ids');
		$product_ids   = array_get($params, 'product_ids');
		$apikeys       = array_get($params, 'apikeys');
		
		$createBillIncome = new CreateBillCost($date, $days, $interface_ids, $apikeys, $product_ids);
		$createBillIncome->run();
		
		$this->output->success("账单任务已成功入队，请稍后确认账单是否成功执行");
	}
	
	/**
	 * 获取命令参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/18 15:53
	 *
	 * @return array
	 */
	protected function getParams()
	{
		$params = [];
		
		//账单日
		$params['date'] = $this->input->getOption('date') ?: date('Ymd');
		if (!preg_match('/^\d{8}$/', $params['date'])) {
			$this->output->error('日期格式不正确');
			die;
		}
		
		//客户ID
		$customer_id = $this->input->getOption('customer_id');
		if (!empty($customer_id)) {
			$customer_id       = explode(',', $customer_id);
			$params['apikeys'] = Account::whereIn('customer_id', $customer_id)
										->pluck('apikey')
										->toArray();
			$params['apikeys'] = array_filter($params['apikeys']);
		}
		
		//APIKEY
		$apikeys = $this->input->getOption('apikey');
		if (!empty($apikeys)) {
			$apikeys           = explode(',', $apikeys);
			$params['apikeys'] = empty($params['apikeys']) ? $apikeys : array_intersect($params['apikeys'], $apikeys);
		}
		
		//父产品ID
		$father_id = $this->input->getOption('father_id');
		if (!empty($father_id)) {
			$father_id             = explode(',', $father_id);
			$params['product_ids'] = Product::whereIn('father_id', $father_id)
											->pluck('product_id')
											->toArray();
		}
		
		//子产品ID
		$product_ids = $this->input->getOption('product_id');
		if (!empty($product_ids)) {
			$product_ids           = explode(',', $product_ids);
			$params['product_ids'] = empty($params['product_ids']) ? $product_ids : array_intersect($params['product_ids'], $product_ids);
		}
		
		//接口ID
		$interface_ids = $this->input->getOption('interface_id');
		if (!empty($interface_ids)) {
			$params['interface_ids'] = explode(',', $interface_ids);
		}
		
		//天数
		$params['days'] = $this->input->getOption('days') ?: 1;
		if (!is_numeric($params['days'])) {
			$this->output->error('天数格式不正确');
			die;
		}
		
		return $params;
	}
}