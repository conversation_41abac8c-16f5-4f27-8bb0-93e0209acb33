<?php

namespace App\Console\Commands;

use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Models\Account;
use App\Models\BillCustomerIncome;
use App\Models\BillProductIncome;
use App\Models\MongoBillMonth;
use App\Models\MongoStatis;
use App\Models\Product;
use function GuzzleHttp\Promise\promise_for;
use Illuminate\Console\Command;

/**
 * Class TempTransferBillMonths 转移历史账单
 * @package App\Console\Commands
 */
class TempTransferBillMonths extends Command
{
	protected $signature = "temp:transfer_bill_months";
	
	/**
	 * @var array 新版账单中，子产品ID与父产品ID的对应关系
	 *            如果不存在父产品，则父产品ID就是自己的ID
	 */
	protected $productIdToFatherIdMapping = [];
	
	/**
	 * @var array 账号与APIKEY映射
	 */
	protected $accountIdToApikeyMapping = [];
	
	public function __construct()
	{
		parent::__construct();
		
		$productIds = Product::select(['product_id', 'father_id'])
							 ->get()
							 ->map(function ($item) {
								 //兼容拆分的邦信分快捷版
								 if (1000 == $item['father_id']) {
									 $item['father_id'] = 210;
								 }
			
								 if (!$item['father_id']) {
									 $item['father_id'] = $item['product_id'];
								 }
			
								 return $item;
							 })
							 ->toArray();
		
		$this->productIdToFatherIdMapping = array_column($productIds, 'father_id', 'product_id');
		
		$this->accountIdToApikeyMapping = Account::pluck('apikey', 'account_id')
												 ->toArray();
	}
	
	
	public function handle()
	{
		$data = $this->getBills();
		
		//客户对账单入库
		$this->output->writeln(">>> 客户对账单入库");
		$bill_customer_income = $data['bill_customer_income'];
		$progress             = $this->output->createProgressBar(count($bill_customer_income));
		array_walk($bill_customer_income, function ($item) use ($progress) {
			//判断是否
			BillCustomerIncome::updateOrCreate(array_only($item, [
				'father_id',
				'product_ids',
				'apikey',
				'date',
			]), $item);
			$progress->advance();
		});
		$progress->finish();
		$this->output->success('客户对账单入库成功');
		
		//产品对账单入库
		$this->output->writeln(">>> 产品对账单入库");
		$bill_product_income = $data['bill_product_income'];
		$progress            = $this->output->createProgressBar(count($bill_product_income));
		array_walk($bill_product_income, function ($item) use ($progress) {
			//判断是否
			BillProductIncome::updateOrCreate(array_only($item, ['product_id', 'apikey', 'date']), $item);
			$progress->advance();
		});
		$progress->finish();
		$this->output->success('产品对账单入库成功');
	}
	
	/**
	 * 将旧版账单按新版账单格式进行整理
	 *
	 * @access
	 * <AUTHOR>
	 * @datetime 2020/11/3 13:45
	 *
	 * @return array
	 */
	protected function getBills()
	{
		//获取全量的月账单数据（2020.10月之前的）
//		$data = MongoBillMonth::where('month', '<', '202010')
//							  ->where('money', '!=', 0)
//							  ->get()
//							  ->toArray();
//
//		halt($data);
		
		$data = MongoBillMonth::query()
							  ->raw(function ($collection) {
								  $aggregate = [
									  [
										  '$match' => [
											  'month' => ['$lt' => '202010'],
											  'money' => ['$ne' => 0],
										  ],
									  ],
									  [
										  '$group' => [
											  '_id'                    => [
												  'account_id' => '$account_id',
												  'product_id' => '$product_id',
												  'month'      => '$month',
											  ],
											  'account_id'             => [
												  '$first' => '$account_id',
											  ],
											  'product_id'             => [
												  '$first' => '$product_id',
											  ],
											  'month'                  => [
												  '$first' => '$month',
											  ],
											  'section_invoked_number' => ['$sum' => '$section_invoked_number'],
											  'money'                  => ['$sum' => '$money'],
										  ],
									  ],
								  ];
								  return $collection->aggregate($aggregate);
							  })->toArray();
		
		//halt($data);
		
		
		//做一个提示
		$this->output->writeln('>>> 开始解析账单');
		$progress = $this->output->createProgressBar(count($data));
		//预设结果集
		$bill_customer_income = [];
		$bill_product_income  = [];
		
		//处理数据
		foreach ($data as $item) {
			$product_id               = $item['product_id'];
			$account_id               = $item['account_id'];
			$apikey                   = $this->accountIdToApikeyMapping[$account_id];
			$father_id                = $this->productIdToFatherIdMapping[$product_id];
			$product_ids              = $product_id;
			$operator                 = '';
			$together_call_number     = 0;
			$config_price_customer_id = 0;
			$date                     = $item['month'] . '01';
			$price                    = '0';
			$number                   = array_get($item, 'section_invoked_number', 0);
			$money                    = round($item['money'], 6);
			
			//客户对账单直接插入
			$bill_customer_income[] = compact('apikey', 'father_id', 'product_ids', 'operator', 'together_call_number', 'config_price_customer_id', 'date', 'price', 'number', 'money');
			//产品对账单/如果是父级产品，需要如何处理
			if (615 == $product_id) {
				$children_product_ids = [
					641,
					642,
					643,
					644,
					645,
					646,
					647,
					648,
					649,
					661,
					662,
					663,
				];
				//获取这些统计量，然后按统计比例拆分
				$month      = $item['month'];
				$statistics = MongoStatis::query()
										 ->raw(function ($collection) use ($apikey, $children_product_ids, $month) {
											 $aggregate = [
												 [
													 '$match' => [
														 'apikey'      => $apikey,
														 'amount_date' => [
															 '$gte' => $month . '01',
															 '$lte' => date('Ymd', strtotime('last day of this month', strtotime($month))),
														 ],
														 'product_id'  => [
															 '$in' => $children_product_ids,
														 ],
													 ],
												 ],
												 [
													 '$group' => [
														 '_id'    => '$product_id',
														 'number' => ['$sum' => '$stat_data.cdnums'],
													 ],
												 ],
											 ];
					
											 return $collection->aggregate($aggregate);
										 })
										 ->toArray();
				$statistics = array_column($statistics, 'number', '_id');
				$total      = array_sum($statistics);
				$residue    = $money;
				foreach ($statistics as $children_product_id => $number) {
					$children_money        = bcdiv(bcmul($number, $money, 6), $total, 6);
					$residue               = bcsub($residue, $children_money, 6);
					$bill_product_income[] = [
						'apikey'                  => $apikey,
						'father_id'               => $father_id,
						'product_id'              => $children_product_id,
						'operator'                => '',
						'bill_customer_income_id' => 0,
						'date'                    => $date,
						'number'                  => $number,
						'money'                   => $children_money,
					];
				}
				$last = array_pop($bill_product_income);
				
				$last['money'] = bcadd($last['money'], $residue, 6);
				array_push($bill_product_income, $last);
			} else if (210 == $item['product_id']) {
				$children_product_ids = [
					251,
					288,
					289,
					290,
					291,
					292,
					293,
					294,
					295,
					296,
					297,
					298,
					241,
					242,
					243,
					244,
					245,
					246,
					247,
					252,
					253,
					254,
					255,
					256,
					257,
					258,
					259,
					260,
					261,
					262,
					263,
					264,
					265,
					266,
					267,
					268,
					269,
					270,
					271,
					272,
					273,
					274,
					275,
					276,
					277,
					278,
					279,
					280,
					281,
					282,
					283,
					284,
					285,
					286,
					287,
					711,
					299,
				];
				//获取这些统计量，然后按统计比例拆分
				$month      = $item['month'];
				$statistics = MongoStatis::query()
										 ->raw(function ($collection) use ($apikey, $children_product_ids, $month) {
											 $aggregate = [
												 [
													 '$match' => [
														 'apikey'      => $apikey,
														 'amount_date' => [
															 '$gte' => $month . '01',
															 '$lte' => date('Ymd', strtotime('last day of this month', strtotime($month))),
														 ],
														 'product_id'  => [
															 '$in' => $children_product_ids,
														 ],
													 ],
												 ],
												 [
													 '$group' => [
														 '_id'    => '$product_id',
														 'number' => ['$sum' => '$stat_data.success'],
													 ],
												 ],
											 ];
					
											 return $collection->aggregate($aggregate);
										 })
										 ->toArray();
				$statistics = array_column($statistics, 'number', '_id');
				$total      = array_sum($statistics);
				$residue    = $money;
				foreach ($statistics as $children_product_id => $number) {
					$children_money        = bcdiv(bcmul($number, $money, 6), $total, 6);
					$residue               = bcsub($residue, $children_money, 6);
					$bill_product_income[] = [
						'apikey'                  => $apikey,
						'father_id'               => $father_id,
						'product_id'              => $children_product_id,
						'operator'                => '',
						'bill_customer_income_id' => 0,
						'date'                    => $date,
						'number'                  => $number,
						'money'                   => $children_money,
					];
				}
				$last = array_pop($bill_product_income);
				
				$last['money'] = bcadd($last['money'], $residue, 6);
				array_push($bill_product_income, $last);
			} else {
				$bill_customer_income_id = 0;
				$bill_product_income[]   = compact('apikey', 'father_id', 'product_id', 'operator', 'bill_customer_income_id', 'date', 'number', 'money');
			}
			
			$progress->advance(1);
		}
		
		$progress->finish();
		$this->output->success("数据解析完成");
		
		return compact('bill_product_income', 'bill_customer_income');
	}
}