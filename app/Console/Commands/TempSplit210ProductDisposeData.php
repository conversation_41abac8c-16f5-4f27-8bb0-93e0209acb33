<?php

namespace App\Console\Commands;

use App\Models\AccountProduct;
use Illuminate\Console\Command;

/**
 * Class TempSplit210ProductDisposeData 拆分邦信分快捷版时，需要对各类数据进行处理
 * @package App\Console\Commands
 */
class TempSplit210ProductDisposeData extends Command
{
	protected $signature = 'temp:split_210_dispose_data';
	
	
	public function handle()
	{
		//处理<已开通产品>数据
		$this->disposeAccountProducts();
		
		//处理<客户计费配置> (不需要处理)
		
		//
	}
	
	/**
	 * 对<已开通产品>开始处理
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/23 15:04
	 *
	 * @return void
	 */
	protected function disposeAccountProducts()
	{
		$this->output->writeln('开始处理<已开通产品>数据');
		$account_products = AccountProduct::where('product_id', 210)
										  ->get()
										  ->toArray();
		$progress         = $this->output->createProgressBar(count($account_products));
		
		$score_product_ids = getBxfShortScoreProductIds();
		
		foreach ($account_products as $account_product) {
			$data                    = json_decode($account_product['data'], true);
			$store_score_product_ids = array_values(array_intersect($data['product_ids'], $score_product_ids));
			//删除fields，这个字段已经没有作用了
			//unset($data['fields']);
			//unset($data['cucc_cmcc_map']);
			
			//校验一下是否开通了邦信分-通信评分产品，如果开通了，则需要开通1000产品
			if (count($store_score_product_ids) > 0) {
				$new_account_product_data                = $data;
				$new_account_product_data['product_ids'] = $store_score_product_ids;
				$new_account_product                     = [
					'account_id'       => $account_product['account_id'],
					'product_id'       => 1000,
					'status'           => $account_product['status'],
					'use_type'         => $account_product['use_type'],
					'contract_status'  => $account_product['contract_status'],
					'end_time'         => $account_product['end_time'],
					'daily_limit'      => $account_product['daily_limit'],
					'month_limit'      => $account_product['month_limit'],
					'year_limit'       => $account_product['year_limit'],
					'total_limit'      => $account_product['total_limit'],
					'concurrency'      => $account_product['concurrency'],
					'data'             => json_encode($new_account_product_data, JSON_UNESCAPED_UNICODE),
					'create_at'        => $account_product['create_at'],
					'update_at'        => $account_product['update_at'],
					'limit_start_date' => $account_product['limit_start_date'],
				];
				//增加新开通的产品
				AccountProduct::create($new_account_product);
				
				//修改或删除210产品
				$data['product_ids'] = array_values(array_diff($data['product_ids'], $store_score_product_ids));
				if (count($data['product_ids']) === 0) {
					AccountProduct::where('id', $account_product['id'])
								  ->delete();
				} else {
					$data = json_encode($data, JSON_UNESCAPED_UNICODE);
					
					AccountProduct::where('id', $account_product['id'])
								  ->update(compact('data'));
				}
				
			} else {
				$data = json_encode($data, JSON_UNESCAPED_UNICODE);
				
				AccountProduct::where('id', $account_product['id'])
							  ->update(compact('data'));
			}
			$progress->advance();
			
		}
		
		$progress->finish();
		$this->output->success("处理<已开通产品>成功");
	}
	
	/**
	 *
	 *
	 * @access
	 * <AUTHOR>
	 * @datetime 2020/10/26 13:53
	 *
	 * @param name string this is params
	 *
	 * @throws \Exception
	 *
	 * @return
	 */
}