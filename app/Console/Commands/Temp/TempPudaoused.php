<?php


namespace App\Console\Commands\Temp;

use App\Models\AccountProduct;
use App\Models\BillCustomerIncome;
use App\Models\BillCustomerIncomeV2;
use App\Models\BillProductIncome;
use App\Models\BillProductIncomeV2;
use App\Models\Channel;
use App\Models\ClickHouse\RequestLog;
use App\Models\ClickHouse\RequestProductLog;
use App\Models\Product;
use App\Models\StatisticsCustomerTogetherCallUsage;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class TempPudaoused extends Command
{
    protected $signature = 'tmp_add_pudao_usage 
    {type : fix-修复数据，back-备份数据，done-执行}
    {params? : 参数}';

    protected $carriermap = [
        'carrier_1' => ['name' => 'CUCC'],//联通
        'carrier_2' => ['name' => 'CTCC'],//电信
        'carrier_3' => ['name' => 'CMCC']//移动
    ];

    /**
     * @throws \Exception
     */
    public function handle()
    {
        $type = $this->argument('type');
        $params = $this->argument('params');
        if (!$type) {
            exit('请输入参数');
        }
        switch ($type) {
            case 'back':
                $this->backData();
                break;
            case 'done':
                //获取主产品调用次数
                $mainProLog = $this->getMainProductLogData();

                //添加/更新 statistics_customer_together_call_usage
                $this->updateMianProduct($mainProLog);

                //获取子产品调用次数
                $subProLog = $this->getSubProductLogData();

                //添加/更新 statistics_customer_usage
                $this->updateSubProduct($subProLog);
                break;
            case 'export':
                $this->export();
                break;
            case 'copyData':
                $this->copyData($params);
                break;
            case 'copyDataV2':
                $this->copyDataV2();
                break;
            case 'test':
                $this->test();
                break;
            default:
                exit('非法参数');
        }
        echo 'DONE!' . PHP_EOL;
    }

    protected function getMainProductLogData()
    {
//        $file = 'mainPro.csv';
//        $file = 'fixMainPro.csv';
        $file = 'fixMainPro0808.csv';
        return $this->getCsvFileData($file);
    }

    /**
     * 更新主产品
     *
     * @param $datas
     */
    protected function updateMianProduct($datas)
    {
        $map = [];
        foreach ($datas as $item) {
            $item['product_id'] = $item['pid'];
            $item = $this->specialDispose($item);
            $key = $item['apikey'] . '_' . $item['pid']. '_' . $item['d'] . '_' . $item['node'];
           if (!isset($map[$key][$item['channel_id']][$item['pid_count']])) {
               $map[$key][$item['channel_id']][$item['pid_count']] = [
                   'total' => 0,
                   'success' => 0,
                   'valid' => 0,
                   'apikey' => $item['apikey'],
                   'pid' => $item['pid'],
                   'd' => $item['d'],
                   'pid_count' => $item['pid_count'],
                   'node' => $item['node'],
                   'channel_id' => $item['channel_id'],
               ];
           }
            $map[$key][$item['channel_id']][$item['pid_count']]['total'] += $item['total'];
            $map[$key][$item['channel_id']][$item['pid_count']]['success'] += $item['successNum'];
            $map[$key][$item['channel_id']][$item['pid_count']]['valid'] += $item['validNum'];
        }
        unset($datas);
        $channelIds = array_filter(array_unique(array_column($map,'channel_id')));
        $channelInfos = [];
        if ($channelIds) {
            $channelInfos = (new Channel())->whereIn('channel_id', $channelIds)->get()->toArray();
        }
        $channelInfos = !empty($channelInfos) ? array_column($channelInfos, null, 'channel_id') : [];
        $channelInfos = array_merge($channelInfos,$this->carriermap);
        $stCuTogetherModel = new StatisticsCustomerTogetherCallUsage();
        $stCuModel = new StatisticsCustomerUsage();
        //遍历数据
        foreach ($map as $key => $arr) {
            $tmpValue = explode('_', $key);
            foreach ($arr as $channelId => $val) {
                $operator = isset($channelInfos[$channelId]) ? $channelInfos[$channelId]['name'] : '';
                $total = 0;
                $success = 0;
                $vaild = 0;

                //确认 statistics_customer_together_call_usage 表数据
                foreach ($val as $pidCount => $item) {
                    $total += $item['total'];
                    $success += $item['success'];
                    $vaild += $item['valid'];

                    $params = [
                        'time' => $item['d'],
                        'apikey' => $item['apikey'],
                        'pid' => $item['pid'],
                        'product_id' => $item['pid'],
                        'pid_count' => $item['pid_count'],
                        'node' => $item['node'],
                        'channel_id' => $item['channel_id'],
                        'operator' => $operator,
                    ];
                    //是否已有朴道数据
                    $tmpP = $params;
                    $tmpP['source'] = 1;
                    $exist = $this->getStatisticsCustomerUsageTogetherData($tmpP);
                    //存在时判断数据是否正确，不正确则合并数据
                    if ($exist) {
                        if ($exist['total'] == $item['total']) {
                            echo json_encode(['exist' => $exist, 'log' => $item]) . '-error1-已经存在朴道记录' . PHP_EOL;
                            continue;
                        }
                        //查询统计表中原数据
                        $tmpP['source'] = 0;
                        $existDianhuaData = $this->getStatisticsCustomerUsageTogetherData($tmpP);

                        try {
                            DB::beginTransaction();
                            $existUpdate = [
                                'total' => $existDianhuaData['total'] + $exist['total'],
                                'success' => $existDianhuaData['success'] + $exist['success'],
                                'valid' => $existDianhuaData['valid'] + $exist['valid'],
                            ];
                            $upRes = $stCuTogetherModel->where('id',$existDianhuaData['id'])->update($existUpdate);
                            $upRes1 = $stCuTogetherModel->where('id',$exist['id'])->delete();
                            if (!$upRes || !$upRes1) {
                                throw new \Exception('error');
                            }
                            DB::commit();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            echo json_encode(['exist' => $exist, 'log' => $item]) . '-error1-删除记录失败' . PHP_EOL;
                            continue;
                        }
                    }
                    //查询统计表中原数据
                    $oldData = $this->getStatisticsCustomerUsageTogetherData($params);
                    //获取日志总数
                    $tmpLog = $this->getRequestLogBywhere($params)[0];
                    echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-data1-日志与已存在数据' . PHP_EOL;
                    //对比
                    if (
                        $oldData &&
                        ($tmpLog['total'] != $oldData['total'] ||
                        $tmpLog['successNum'] != $oldData['success'] ||
                        $tmpLog['validNum'] != $oldData['valid'])
                    ) {
                        echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-error1-日志与已存在数据不一致' . PHP_EOL;
//                        continue;
                    }
                    $now = time();
                    //添加并更新
                    if ($oldData && $item['total'] == $oldData['total']) {
                        $res = $stCuTogetherModel->where('id' ,$oldData['id'])->update(['source' => 1]);
                        if (!$res) {
                            echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-error1-更新失败' . PHP_EOL;
                        }
                        echo json_encode(['old' => $oldData, 'log' => $item]) . '-data1-更新来源成功' . PHP_EOL;
                        continue;
                    }
                    $addData = [
                        'apikey' => $item['apikey'],
                        'product_id' => $item['pid'],
                        'node' => $item['node'],
                        'date' => date('Ymd',strtotime($item['d'])),
                        'sub_product_number' => $item['pid_count'],
                        'total' => $item['total'],
                        'success' => $item['success'],
                        'valid' => $item['valid'],
                        'source' => '1',
                        'operator' => '',
                        'create_time' => $now,
                        'update_time' => $now,
                    ];
                    if (!in_array($item['pid'], [615,1000])) {
                        $addData['operator'] = $operator;
                    }
                    $updateData = [];
                    try {
                        DB::beginTransaction();
                        $res = $stCuTogetherModel->insert($addData);
                        $res1 = true;
                        if ($oldData) {
                            $updateData = [
                                'total' => ($oldData['total'] - $item['total']) > 0 ? $oldData['total'] - $item['total'] : 0,
                                'success' => ($oldData['success'] - $item['success']) > 0 ? $oldData['success'] - $item['success'] : 0,
                                'valid' => ($oldData['valid'] - $item['valid']) > 0 ? $oldData['valid'] - $item['valid'] : 0,
                            ];
                            $res1 = $stCuTogetherModel->where('id',$oldData['id'])->update($updateData);
                        }
                        if (!$res || !$res1) {
                            throw new \Exception('error');
                        }
                        DB::commit();
                        echo json_encode(['old' => $oldData, 'new' => $addData, 'update' => $updateData]) . '-data1-新增数据' . PHP_EOL;
                    } catch (\Exception $e) {
                        echo json_encode(['old' => $oldData, 'log' => $tmpLog, 'update' => $updateData, 'msg' => $e->getMessage()]) . '-error1-更新失败' . PHP_EOL;
                        DB::rollBack();
                    }
                }

                //确认 statistics_customer_usage 表数据
                $params = [
                    'time' => $tmpValue[2],
                    'apikey' => $tmpValue[0],
                    'pid' => $tmpValue[1],
                    'product_id' => $tmpValue[1],
                    'node' => $tmpValue[3],
                    'channel_id' => $channelId,
                    'operator' => $operator,
                    'total' => $total,
                    'success' => $success,
                    'valid' => $vaild,
                ];
                //是否已有朴道数据
                $tmpP = $params;
                $tmpP['source'] = 1;
                $exist = $this->getStatisticsCustomerUsageData($tmpP);
                if ($exist) {
                    if ($exist['total'] == $total) {
                        echo json_encode(['exist' => $exist, 'log' => $params]) . '-error2-已经存在朴道记录' . PHP_EOL;
                        continue;
                    }
                    //查询统计表中原数据
                    $tmpP['source'] = 0;
                    $existDianhuaData = $this->getStatisticsCustomerUsageData($tmpP);
                    try {
                        DB::beginTransaction();
                        $existUpdate = [
                            'total' => $existDianhuaData['total'] + $exist['total'],
                            'success' => $existDianhuaData['success'] + $exist['success'],
                            'valid' => $existDianhuaData['valid'] + $exist['valid'],
                        ];
                        $upRes = $stCuModel->where('id',$existDianhuaData['id'])->update($existUpdate);
                        $upRes1 = $stCuModel->where('id',$exist['id'])->delete();
                        if (!$upRes || !$upRes1) {
                            throw new \Exception('error');
                        }
                        DB::commit();
                    } catch (\Exception $e) {
                        DB::rollBack();
                        echo json_encode(['exist' => $exist, 'log' => $item]) . '-error2-删除记录失败' . PHP_EOL;
                        continue;
                    }
                }
                //查询统计表中原数据
                $oldData = $this->getStatisticsCustomerUsageData($params);
                //获取日志总数
                $tmpLog = $this->getRequestLogBywhere($params)[0];
                //对比
                if (
                    $oldData &&
                    ($tmpLog['total'] != $oldData['total'] ||
                    $tmpLog['successNum'] != $oldData['success'] ||
                    $tmpLog['validNum'] != $oldData['valid'])
                ) {
                    echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-error2-日志与已存在数据不一致' . PHP_EOL;
//                    continue;
                }
                $now = time();
                //添加并更新
                if ($oldData && $total == $oldData['total']) {
                    $res = $stCuModel->where('id',$oldData['id'])->update(['source' => 1]);
                    if (!$res) {
                        echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-error2-更新失败' . PHP_EOL;
                    }
                    continue;
                    echo json_encode(['old' => $oldData, 'log' => $item]) . '-data2-更新来源成功' . PHP_EOL;
                }
                $addData = [
                    'apikey' => $params['apikey'],
                    'product_id' => $params['pid'],
                    'node' => $params['node'],
                    'date' => date('Ymd',strtotime($params['time'])),
                    'total' => $total,
                    'success' => $success,
                    'valid' => $vaild,
                    'operator' => '',
                    'source' => 1,
                    'create_time' => $now,
                    'update_time' => $now,
                ];
                if (!in_array($item['pid'], [615,1000])) {
                    $addData['operator'] = $operator;
                }
                $updateData = [];
                try {
                    DB::beginTransaction();
                    $res = $stCuModel->insert($addData);
                    $res1 = true;
                    if ($oldData) {
                        $updateData = [
                            'total' => ($oldData['total'] - $total) > 0 ? $oldData['total'] - $total : 0,
                            'success' => ($oldData['success'] - $success) > 0 ? $oldData['success'] - $success : 0,
                            'valid' => ($oldData['valid'] - $vaild) > 0 ? $oldData['valid'] - $vaild : 0,
                        ];
                        $res1 = $stCuModel->where('id',$oldData['id'])->update($updateData);
                    }
                    if (!$res || !$res1) {
                        throw new \Exception('error');
                    }
                    DB::commit();
                    echo json_encode(['old' => $oldData, 'new' => $addData, 'update' => $updateData]) . '-data2-更新成功' . PHP_EOL;
                } catch (\Exception $e) {
                    echo json_encode(['old' => $oldData, 'new' => $addData, 'update' => $updateData, 'msg' => $e->getMessage()]) . '-error2-更新失败' . PHP_EOL;
                    DB::rollBack();
                }
            }

        }
    }

    /**
     * 获取子产品朴道信息
     *
     * @return array
     */
    protected function getSubProductLogData()
    {
//        $file = 'subPro.csv';
//        $file = 'fixSubPro.csv';
        $file = 'fixSubPro0808.csv';
        return $this->getCsvFileData($file);
    }

    /**
     * 更新子产品朴道统计用量
     *
     * @param $datas
     */
    protected function updateSubProduct($datas)
    {
        $channelInfos = [];
        $channelIds = array_filter(array_unique(array_column($datas,'channel_id')));
        if ($channelIds) {
            $channelInfos = (new Channel())->whereIn('channel_id', $channelIds)->get()->toArray();
        }
        $channelInfos = !empty($channelInfos) ? array_column($channelInfos, null, 'channel_id') : [];
        $channelInfos = array_merge($channelInfos,$this->carriermap);
        $stCuModel = new StatisticsCustomerUsage();
        foreach ($datas as $item) {
            $item['product_id'] = $item['pids'];
            $item = $this->specialDispose($item);
            $operator = isset($channelInfos[$item['channel_id']]) ? $channelInfos[$item['channel_id']]['name'] : '';
            $params = [
                'time' => $item['d'],
                'apikey' => $item['apikey'],
                'pid' => $item['pid'],
                'product_id' => $item['pids'],
                'node' => $item['node'],
                'channel_id' => $item['channel_id'],
                'operator' => $operator,
            ];
            //是否已有朴道数据
            $tmpP = $params;
            $tmpP['source'] = 1;
            $exist = $this->getStatisticsCustomerUsageData($tmpP);
            if ($exist) {
                if ($exist['total'] == $item['total']) {
                    echo json_encode(['exist' => $exist, 'log' => $item]) . '-error3-已经存在朴道记录' . PHP_EOL;
                    continue;
                }
                //查询统计表中原数据
                $tmpP['source'] = 0;
                $existDianhuaData = $this->getStatisticsCustomerUsageData($tmpP);
                try {
                    DB::beginTransaction();
                    $existUpdate = [
                        'total' => $existDianhuaData['total'] + $exist['total'],
                        'success' => $existDianhuaData['success'] + $exist['success'],
                        'valid' => $existDianhuaData['valid'] + $exist['valid'],
                    ];
                    $upRes = $stCuModel->where('id',$existDianhuaData['id'])->update($existUpdate);
                    $upRes1 = $stCuModel->where('id',$exist['id'])->delete();
                    if (!$upRes || !$upRes1) {
                        throw new \Exception('error');
                    }
                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    echo json_encode(['exist' => $exist, 'log' => $item]) . '-error3-删除记录失败' . PHP_EOL;
                    continue;
                }
            }
            //查询统计表中原数据
            $oldData = $this->getStatisticsCustomerUsageData($params);
            //获取日志总数
            $tmpLog = $this->getRequestProductLogBywhere($params)[0];
            //对比
            if (
                $oldData &&
                ($tmpLog['total'] != $oldData['total'] ||
                $tmpLog['successNum'] != $oldData['success'] ||
                $tmpLog['validNum'] != $oldData['valid'])
            ) {
                echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-error3-日志与已存在数据不一致' . PHP_EOL;
//                continue;
            }
            $now = time();
            //添加并更新
            if ($oldData && $item['total'] == $oldData['total']) {
                $res = $stCuModel->where('id',$oldData['id'])->update(['source' => 1]);
                if (!$res) {
                    echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-error3-更新失败' . PHP_EOL;
                }
                echo json_encode(['old' => $oldData, 'log' => $tmpLog]) . '-data3-更新来源成功' . PHP_EOL;
                continue;
            }
            $addData = [
                'apikey' => $item['apikey'],
                'product_id' => $item['pids'],
                'node' => $item['node'],
                'date' => date('Ymd',strtotime($item['d'])),
                'total' => $item['total'],
                'success' => $item['successNum'],
                'valid' => $item['validNum'],
                'source' => 1,
                'operator' => '',
                'create_time' => $now,
                'update_time' => $now,
            ];
            if (!in_array($item['pid'], [615,1000])) {
                $addData['operator'] = $operator;
            }
            $updateData = [];

            try {
                DB::beginTransaction();
                $res = $stCuModel->insert($addData);
                $res1 = true;
                if ($oldData) {
                    $updateData = [
                        'total' => ($oldData['total'] - $item['total']) > 0 ? $oldData['total'] - $item['total'] : 0,
                        'success' => ($oldData['success'] - $item['successNum']) > 0 ? $oldData['success'] - $item['successNum'] : 0,
                        'valid' => ($oldData['valid'] - $item['validNum']) > 0 ? $oldData['valid'] - $item['validNum'] : 0,
                    ];
                    $res1 = $stCuModel->where('id',$oldData['id'])->update($updateData);
                }

                if (!$res || !$res1) {
                    throw new \Exception('error');
                }
                DB::commit();
                echo json_encode(['old' => $oldData, 'new' => $addData, 'update' => $updateData]) . '-data3-新增数据成功' . PHP_EOL;
            } catch (\Exception $e) {
                echo json_encode(['old' => $oldData, 'new' => $addData, 'update' => $updateData, 'msg' => $e->getMessage()]) . '-error3-更新失败' . PHP_EOL;
                DB::rollBack();
            }
        }
    }

    /**
     * 获取日志统计信息
     *
     * @param $params
     * @return array
     */
    protected function getRequestLogBywhere($params)
    {
        $startTime = strtotime($params['time']);
        $endTime = $startTime + 86400;
        $sql = <<<SQL
SELECT
	COUNT(*) as total,
	COUNT(IF (success==1,1,NULL)) as successNum,
	COUNT(IF (valid==1,1,NULL)) as validNum
FROM
	user_request_log urpl
WHERE
	request_at >= {$startTime}
	AND request_at < {$endTime}
	AND apikey = '{$params['apikey']}'
	AND pid = '{$params['pid']}'
	AND node = '{$params['node']}'
SQL;
        if (!empty($params['pid_count'])) {
            $sql .= " AND pid_count ='{$params['pid_count']}'";
        }
        if (strpos($params['channel_id'],'carrier') === false) {
            $sql .= " AND channel_id ='{$params['channel_id']}'";
        } else {
            $carrier = explode('_', $params['channel_id']);
            if ($carrier[1] == 3) {
                $sql .= " AND carrier in (0,3)";
            } else {
                $sql .= " AND carrier = {$carrier[0]}";
            }
        }
        $logDatas = (new RequestLog())->clickHouse->select($sql)->rows();
        return $logDatas;
    }

    /**
     * 获取产品调用明细
     *
     * @param $params
     * @return array
     */
    protected function getRequestProductLogBywhere($params)
    {
        $startTime = strtotime($params['time']);
        $endTime = $startTime + 86400;
        $sql = <<<SQL
SELECT
	COUNT(*) as total,
	COUNT(IF (success==1,1,NULL)) as successNum,
	COUNT(IF (valid==1,1,NULL)) as validNum
FROM
	user_request_product_log urpl
WHERE
	request_at >= {$startTime}
	AND request_at < {$endTime}
	AND apikey = '{$params['apikey']}'
	AND pids = '{$params['product_id']}'
	AND node = '{$params['node']}'
SQL;
        if (strpos($params['channel_id'],'carrier') === false) {
            $sql .= " AND channel_id ='{$params['channel_id']}'";
        } else {
            $carrier = explode('_', $params['channel_id']);
            if ($carrier[1] == 3) {
                $sql .= " AND carrier in (0,3)";
            } else {
                $sql .= " AND carrier = {$carrier[1]}";
            }
        }
        $logDatas = (new RequestProductLog())->clickHouse->select($sql)->rows();
        return $logDatas;
    }

    /**
     * 获取文件内容
     *
     * @param $file
     */
    protected function getCsvFileData($file)
    {
        $return = [];
        $file = __DIR__ . '/' . $file;
        $f = fopen($file, 'r');
        $header = fgets($f);
        $header = explode(',', str_replace('"', '', trim($header)));
        while(!feof($f)){
            $value = fgets($f);
            if (!$value) {
                continue;
            }
            $value = explode(',', str_replace('"', '', trim($value)));
            $return[] = array_combine($header, $value);
        }
        fclose($f);
        return $return;
    }


    /**
     * 特殊情况需要特殊处理
     * 存在两个客户（杭银消金、量化派）的邦秒验计费配置不能合并到一起，所以需要后台进行特殊处理，
     * 将不能合并的邦秒验子产品的客户调用量统计转移到相同客户下的另一个账号中，然后运营增加两种计费配置
     *        量化派：78093b56a19a1a6a2a34be5d4518226d（量化派）216（手机号实时在网状态）>>>>
     *        4c9e0b7da381474fd3d7faaa83eab8d8(量化派2)216（手机号实时在网状态）
     *        杭银消金：a01463e997ba1afa11df6bf4720e9e3d（杭银消金）231（线上现金分期场景分）>>>>
     *        30c5250ff8db7d5cae96bfe481be48a2（杭银消金2）231（线上现金分期场景分）
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/10/21 14:15
     *
     * @param $data array 将要入库的数据
     *
     * @return array
     */
    protected function specialDispose($data)
    {
        $apikey     = $data['apikey'];
        $product_id = $data['product_id'];

        if ('78093b56a19a1a6a2a34be5d4518226d' == $apikey && 216 == $product_id) {
            //量化派需要转移的产品
            $data['apikey'] = '4c9e0b7da381474fd3d7faaa83eab8d8';

            return $data;
        }

        if ('a01463e997ba1afa11df6bf4720e9e3d' == $apikey && 231 == $product_id) {
            //杭银消金需要转移的产品
            $data['apikey'] = '30c5250ff8db7d5cae96bfe481be48a2';

            return $data;
        }

        return $data;
    }

    protected function getStatisticsCustomerUsageData($params)
    {
        $time =date('Ymd',strtotime($params['time']));
        $where = [
            ['apikey', $params['apikey']],
            ['product_id', $params['product_id']],
            ['date', $time],
            ['node', $params['node']],
        ];
        if (isset($params['source'])) {
            $where[] = ['source', $params['source']];
        }
        if (!in_array($params['pid'], [615,10000])) {
            $where[] = ['operator', $params['operator']];
        }
        $datas = (new StatisticsCustomerUsage())->select('*')->where($where)->first();
        return $datas ? $datas->toArray() : [];
    }

    protected function getStatisticsCustomerUsageTogetherData($params)
    {
        $time =date('Ymd',strtotime($params['time']));
        $where = [
            ['apikey', $params['apikey']],
            ['product_id', $params['product_id']],
            ['date', $time],
            ['node', $params['node']],
            ['sub_product_number', $params['pid_count']]
        ];
        if (isset($params['source'])) {
            $where[] = ['source', $params['source']];
        }

        if (!in_array($params['pid'], [615,10000])) {
            $where[] = ['operator', $params['operator']];
        }

        $datas = (new StatisticsCustomerTogetherCallUsage())->select('*')->where($where)->first();
        return $datas ? $datas->toArray() : [];
    }

    protected function backData()
    {
        $file = 'fixSubPro.csv';
        $subData = $this->getCsvFileData($file);
        $file = 'fixMainPro.csv';
        $mainData = $this->getCsvFileData($file);
        $channelIds = array_filter(array_unique(array_column($mainData,'channel_id')));
        $channelIds1 = array_filter(array_unique(array_column($subData,'channel_id')));
        $channelIds = array_filter(array_unique(array_merge($channelIds, $channelIds1)));
        $channelInfos = [];
        if ($channelIds) {
            $channelInfos = (new Channel())->whereIn('channel_id', $channelIds)->get()->toArray();
        }
        $channelInfos = !empty($channelInfos) ? array_column($channelInfos, null, 'channel_id') : [];
        $channelInfos = array_merge($channelInfos,$this->carriermap);
        foreach ($subData as $item) {
            $operator = isset($channelInfos[$item['channel_id']]) ? $channelInfos[$item['channel_id']]['name'] : '';
            $params = [
                'time' => $item['d'],
                'apikey' => $item['apikey'],
                'pid' => $item['pid'],
                'product_id' => $item['pids'],
                'node' => $item['node'],
                'operator' => $operator,
            ];
            //是否已有朴道数据
            $oldData = $this->getStatisticsCustomerUsageData($params);
            echo 'statistics_customer_usage' .'_' .  json_encode($oldData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            if (!$oldData) {
                echo 'kong' .'_' .  json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            }
        }

        foreach ($mainData as $item) {
            $operator = isset($channelInfos[$item['channel_id']]) ? $channelInfos[$item['channel_id']]['name'] : '';
            $params = [
                'time' => $item['d'],
                'apikey' => $item['apikey'],
                'pid' => $item['pid'],
                'product_id' => $item['pid'],
                'pid_count' => $item['pid_count'],
                'node' => $item['node'],
                'operator' => $operator,
            ];

            $oldData = $this->getStatisticsCustomerUsageTogetherData($params);
            echo 'statistics_customer_together_call_usage' . '_' . json_encode( $oldData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $oldData = $this->getStatisticsCustomerUsageData($params);
            echo 'statistics_customer_usage' .'_' .json_encode($oldData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        }
    }

    /**
     * 数据导出
     * 开启指定产品的账户
     */
    public function export()
    {
        //获取账号信息
        $sql = "SELECT
	c.customer_id ,
	c.name ,
	a.account_id ,
	a.apikey,
	ap.product_id 
from
	account_product ap
	left join account a on a.account_id = ap.account_id 
	LEFT JOIN customer c on c.customer_id = a.customer_id 
WHERE
	ap.status = 1
	AND ap.product_id in (202, 203, 216)
	AND ap.end_time >= **********
	AND c.customer_id != 'C20180828LOCNMG';";

        $accountInfos = DB::select($sql);
        foreach ($accountInfos as $kkk => $item) {
            $apikey = $item['apikey'];
            $pid = $item['product_id'];
            //获取调用量
            $sql = "SELECT
	sum(total) as num
FROM
	statistics_customer_usage scu
where
	apikey = '$apikey'
	AND `date` >='********'
	AND `date` <'********'
	AND product_id = '$pid';
";
            $num = DB::select($sql)[0];
            $accountInfos[$kkk]['usage'] = empty($num['num']) ? 0 : $num['num'];
            //获取计费配置
            $productInfo = Product::findOneItem([['product_id', $pid]]);
            $fatherId = $productInfo->father_id ?? $pid;
            $sql = "SELECT
	price 
FROM
	config_price_customer cpc
WHERE
	apikey = '$apikey'
	AND father_id = {$fatherId}
	AND product_ids like '%$pid%'
	order BY  start_date DESC;";
            $price = DB::select($sql);
            $price = empty($price[0]) ?['price' => "{'$pid':'0'}"] : $price[0];
            $price = json_decode($price['price'], true)[$pid];
            if (is_array($price)) {
                foreach ($price as $key => $v) {
                    $accountInfos[$kkk][$key] = $v;
                }
            } else {
                $accountInfos[$kkk]['不区分'] = $price;
            }
        }
        /**
         *     ["customer_id"]=>
        string(15) "C20210205DN7DR9"
        ["name"]=>
        string(18) "快乐通宝小贷"
        ["account_id"]=>
        string(16) "****************"
        ["apikey"]=>
        string(32) "5727f79422b87784753feca30aff0878"
        ["product_id"]=>
        int(216)
         */
        foreach ($accountInfos as $item) {
            $item['CMCC'] = isset($item['CMCC']) ? $item['CMCC'] : '-';
            $item['CUCC'] = isset($item['CUCC']) ? $item['CUCC'] : '-';
            $item['CTCC'] = isset($item['CTCC']) ? $item['CTCC'] : '-';
            $item['不区分'] = isset($item['不区分']) ? $item['不区分'] : '-';
            echo $item['name'] . "\t" . $item['apikey'] . "\t" . $item['product_id'] . "\t" . $item['usage'] . "\t" . $item['CMCC'] . "\t" . $item['CUCC'] . "\t" . $item['CTCC'] . "\t" . $item['不区分'] . PHP_EOL;
//            echo implode("\t", $item);
        }
    }

    /**
     * 数据导入
     * bill_customer_income -> bill_customer_income_v2
     * bill_product_income -> bill_product_income_v2
     *
     * @param string $params
     *               20220101_20220701_billCustomerIncome
     */
    protected function copyData($params)
    {
        $params = explode('_', $params);
        if (count($params) != 2) {
            exit('参数异常');
        }
        $startDate = $params[0];
        $endDate = $params[1];

        $oldCusModel = new BillCustomerIncome();
        $newCusModel = new BillCustomerIncomeV2();
        $oldProModel = new BillProductIncome();
        $newProModel = new BillProductIncomeV2();

        $lastId = 0;
        $cusTotal = 0;
        $proTotal = 0;
        //获取旧数据
        while (true) {
            $where = [
                ['date', '>=', $startDate],
                ['date', '<=', $endDate],
                ['id', '>', $lastId],
            ];
            $oldCusDatas = $oldCusModel->where($where)->orderBy('id','asc')->limit(500)->get();

            if (!count($oldCusDatas)) {
                break;
            }
            $oldCusDatas = $oldCusDatas->toArray();
            $lastId  = end($oldCusDatas)['id'];
            reset($oldCusDatas);
            $cusTotal+= count($oldCusDatas);
            foreach ($oldCusDatas as $item) {
                //查询product_income表
                $relationId = $item['id'];
                $oldProDatas = $oldProModel->where('bill_customer_income_id', $relationId)->get();
                $oldProDatas = count($oldProDatas) ? $oldProDatas->toArray() : [];

                //customer_income 新增数据
                unset($item['id']);
                $item['create_time'] = strtotime($item['create_time']);

                try {
                    DB::beginTransaction();

                    $newId = $newCusModel->insertGetId($item);
                    $addProDatas = [];
                    $proIds = [];
                    foreach ($oldProDatas as $ipro) {
                        $proTotal++;
                        $proIds[] = $ipro['id'];
                        unset($ipro['id']);
                        $ipro['create_time'] = strtotime($ipro['create_time']);
                        $ipro['bill_customer_income_id'] = $newId;
                        $addProDatas[] = $ipro;
                    }

                    $res = true;
                    if ($addProDatas) {
                        $res = $newProModel->insert($addProDatas);
                        echo 'PRODUCT_INCOME_IDS_' . json_encode(['oldCusId' => $relationId, 'proIds' => $proIds], JSON_UNESCAPED_UNICODE) . PHP_EOL;
                    } else {
                        echo 'PRODUCT_INCOME_EMPTY_' . json_encode(['oldCusId' => $relationId], JSON_UNESCAPED_UNICODE) . PHP_EOL;
                    }

                    if (!$newId || !$res) {
                        throw new \Exception('error');
                    }
                    DB::commit();

                } catch (\Exception $e) {

                    DB::rollBack();
                }
            }
        }
        var_dump(['custotal' => $cusTotal, 'prototal' => $proTotal]);
    }

    protected function copyDataV2()
    {
        $this->copyData('20220101_20220101');

        $oldProModel = new BillProductIncome();
        $newProModel = new BillProductIncomeV2();
        $lastId = 0;
        $total = 0;
        while (true) {
            $where = [
                ['id', '>', $lastId],
                ['bill_customer_income_id', 0]
            ];

            $olds = $oldProModel->where($where)->orderBy('id','asc')->limit(500)->get();
            $olds = count($olds) ? $olds->toArray() : [];
            if (!$olds) {
                break;
            }
            $total += count($olds);
            $lastId = end($olds)['id'];
            reset($olds);
            $addDatas = [];
            foreach ($olds as $item) {
                unset($item['id']);
                $item['create_time'] = strtotime($item['create_time']);
                $addDatas[] = $item;
            }
            $newProModel->insert($addDatas);
            var_dump($total);
        }
    }

    public function test()
    {
        $endTime = strtotime('today');
        $startTime = $endTime - 7 * 24 * 3600;
        $redis = Redis::connection('redis_cache');
        $cacheKey = 'CZTY_TEST_DATAS_0923';
        for ($i=0;$i<8;$i++) {
            if ($i > 0) {
                $endTime = $startTime;
                $startTime = $startTime - 7 * 24 * 3600;
            }

            $sql = <<<SQL
SELECT
tel ,
`data` ,
sid ,
FROM_UNIXTIME(request_at,'%Y-%m-%d') as d
FROM
	user_request_product_log url
WHERE
	request_at >= {$startTime}
	AND request_at < {$endTime}
	AND pids = '216'
	AND channel_id = 127
	ORDER BY RAND() LIMIT 200
;
SQL;

            $datas = (new RequestProductLog())->clickHouse->select($sql)->rows();
            $num = 0;
            foreach ($datas as $item) {
                if ($redis->sismember($cacheKey, $item['tel']) || $num >= 125) {
                    continue;
                }

                $num++;
                $redis->sadd($cacheKey, $item['tel']);
                $txt = implode("\t", $item) ;
                file_put_contents('kkkkkkkk.txt', $txt . PHP_EOL, FILE_APPEND);
            }
            var_dump($num);
        }
    }
}