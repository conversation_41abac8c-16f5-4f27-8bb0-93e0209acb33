<?php

namespace App\Console\Commands\CompareData;

use Illuminate\Console\Command;
use App\Providers\ClickHouse\ClientDb;
use App\Repositories\ClickHouse\CustomerRepository;
use App\Providers\RedisCache\RedisCache;
use App\Models\ChannelInterface;
use App\Providers\Tool\SendMailService;

class UsageRelated extends Command
{
    protected $signature = 'compare:usage
    {--start_date= : 统计起始日期（格式Ymd）}
    {--end_date= : 统计截止日期 (格式Ymd)}
    {--type= : 邮件内容发送的类型 (customer_month客户子产品月总量,)}
    ';
    protected $description = '后台调用量和clickhouse调用量日志比较';
    private $start_date;
    private $end_date;
    private $type;
    private $type_map = [
        'total_num' => '总量',
        'success_num' => '成功量',
        'valid_num' => '查得量',
    ];
    private $customerRepository = null;

    /*
       不同产品计费维度不同
       邦信分、金盾(号码风险等级)----->盛哥负责 channel_id
       帮秒验、邦企查(该产品先不弄)----->刘畅负责  carrier
       号码风险等级归类没有渠道量(因此只需比较独立子产品量和打包量(且打包量只有615有),渠道量没有不用比较)
       号码分属于自己的产品，只有独立子产品量和打包量，没有渠道量
       */

    /**
     * @throws \Exception
     */
    public function handle()
    {
        if(!$this->checkParams()){
            return;
        }

        if(empty($this->start_date) && empty($this->end_date)){
            $this->start_date = $this->end_date = date("Ymd",strtotime("-1 day"));//统计时间
        }

        try {
            $job_start_time = microtime(true);
            $this->customerRepository = new CustomerRepository();
            //生成HTML页面
            $html = $this->createHtml();
            $job_end_time = microtime(true);
            $execution_time = ($job_end_time - $job_start_time);
            $html .= "<h5>注：脚本统计耗时{$execution_time}s</h5><br/>";

            //发送邮件
            $this->sendMail($html);

        } catch (\Exception $exception) {
            sendCommandExceptionNotice($this, $exception);
        }

    }

    private function childrenProductCompare(){
        $customerRep = $this->customerRepository;
        $data = $customerRep->getChildrenProductUsage($this->start_date, $this->end_date);
        return $data;
    }

    private function packageVolumeCompare(){
        $customerRep = $this->customerRepository;
        $data = $customerRep->packageVolumeCompare($this->start_date, $this->end_date);
        return $data;
    }

    private function channelVolumeCompare(){
        $customerRep = $this->customerRepository;
        $data = $customerRep->channelVolumeCompare($this->start_date, $this->end_date);
        return $data;
    }

    private function customerMonthCompare(){
        $customerRep = $this->customerRepository;
        $data = $customerRep->getCustomerMonthUsage($this->start_date, $this->end_date);
        return $data;
    }

    private function checkParams(){
        $options = $this->options();
        $this->start_date = $options['start_date'];
        $this->end_date = $options['end_date'];
        $this->type = $options['type'];
        if(!empty($this->start_date) && empty($this->end_date)){
            echo "截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->end_date) && empty($this->start_date)){
            echo "起始日期不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->start_date) && !empty($this->start_date)) {
            echo "起始日期格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date) && !empty($this->end_date)) {
            echo "截止日期格式不正确\r\n";
            return false;
        }

        return true;
    }

    private function createHtml()
    {
        $html = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"></head><body>';

        //设置样式
        $html .= $this->createHtmlStyle();

        if($this->type == 'customer_month'){
            //$this->start_date = $this->getMonthStartDate();
            //生成第四个表格所需数据 每月客户子产品量比较
            $customer_res = $this->customerMonthCompare();

            //生成第四个表格
            $html .= $this->createFourthTableHtml($customer_res);
        }else{

            //生成第一个表格所需的数据 独立子产品量比较
            $product_res = $this->childrenProductCompare();
            //生成第一个表格
            $html .= $this->createFirstTableHtml($product_res);

            //生成第二个表格所需数据 打包量比较
            $package_res = $this->packageVolumeCompare();
            //生成第二个表格
            $html .= $this->createTwiceTableHtml($package_res);

            //生成第三个表格所需数据 渠道量比较
            $channel_res = $this->channelVolumeCompare();
            //生成第三个表格
            $html .= $this->createThirdTableHtml($channel_res);

            //生成第四个表格
            $exception_interface = $this->customerRepository->exception_interface;
            $html .= $this->createExceptionTableHtml($exception_interface);

        }


        return $html . '</body></html>';
    }

    private function createHtmlStyle()
    {
        return <<<HTML
<style>
	body {
		color : #333333;
	}

	.table_title {
        width       : 98%;
        height      : 30px;
        line-height : 30px;
        color       : #333333;
        border      : 700;
    }

	table {
		width         : 98%;
		border        : none;
		padding       : 0;
		margin        : 0 auto  10px;
		font-size     : 14px;
		color         : #666666;
		border-bottom : none;
		border-right  : none;
		border-left   : 1px solid #CCCCCC;
		border-top    : 1px solid #CCCCCC;
	}

	tr {
		border-right  : none;
		border-bottom : none;
		border-left   : 1px solid #CCCCCC;
		border-top    : 1px solid #CCCCCC;
	}

	td, th {
		border-right  : 1px solid #CCCCCC;
		border-bottom : 1px solid #CCCCCC;
		border-left   : none;
		border-top    : none;
		padding       : 5px 5px 5px 0;
	}

	th {
		color       : #333333;
		font-weight : bold;
		height      : 20px;
		line-height : 20px;
		padding     : 8px 0;
	}

	tr:nth-child(even) {
		background : #EEEEEE;
	}

	tr:hover {
		background : #CCCCCC;
	}

	.bold {
		font-weight : bold;
	}

	.fsz-16 {
		font-size : 15px;
	}

	.space {
		width  : 100%;
		height : 40px;
	}

	.remark {
		width       : 98%;
		margin      : 10px auto 20px;
		padding     : 5px;
		color       : red;
		font-size   : 14px;
		font-weight : bolder;
	}
</style>
HTML;

    }


    private function createFirstTableHtml($data)
    {
        $display = <<<HTML
<b class="table_title">一: 每日独立子产品量比较</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">产品分类</th>
	<th align="center" nowrap="nowrap">产品ID</th>
	<th align="center" nowrap="nowrap">产品名称</th>
	<th align="center" nowrap="nowrap">账号apikey</th>
	<th align="center" nowrap="nowrap">运营商</th>
	<th align="center" nowrap="nowrap">比较类型</th>
	<th align="center" nowrap="nowrap">系统日志数值</th>
	<th align="center" nowrap="nowrap">后台数值</th>
</tr>
HTML;

        //bxf list
        $bxf_list = array_merge($data['bxf_compare_res']['total'], $data['bxf_compare_res']['success'], $data['bxf_compare_res']['valid']);
        $rowspan = count($bxf_list);
        $bxf_display = '';
        $firstRow  = array_shift($bxf_list);
        $detail = $this->dealProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $bxf_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦信分</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($bxf_list as $item) {
                $detail = $this->dealProductData($item);
                $item['type'] = $this->type_map[$item['type']];
                $bxf_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $bxf_display;
        }


        //bmy list =================================================================================

        $bmy_list = array_merge($data['bmy_compare_res']['total'], $data['bmy_compare_res']['success'], $data['bmy_compare_res']['valid']);
        $rowspan = count($bmy_list);
        $bmy_display = '';
        $firstRow  = array_shift($bmy_list);
        $detail = $this->dealProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $bmy_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦秒验</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($bmy_list as $item) {
                $detail = $this->dealProductData($item);
                $item['type'] = $this->type_map[$item['type']];
                $bmy_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $bmy_display;
        }


        //hmfxdj list =================================================================================

        $hmfxdj_list = array_merge($data['hmfxdj_compare_res']['total'], $data['hmfxdj_compare_res']['success'], $data['hmfxdj_compare_res']['valid']);
        $rowspan = count($hmfxdj_list);
        $hmfxdj_display = '';
        $firstRow  = array_shift($hmfxdj_list);
        $detail = $this->dealHmfxdjProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $hmfxdj_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">号码风险等级</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($hmfxdj_list as $item) {
                $detail = $this->dealHmfxdjProductData($item);
                $item['type'] = $this->type_map[$item['type']];
                $hmfxdj_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $hmfxdj_display;
        }


        //hmf list =================================================================================

        $hmf_list = array_merge($data['hmf_compare_res']['total'], $data['hmf_compare_res']['success'], $data['hmf_compare_res']['valid']);
        $rowspan = count($hmf_list);
        $hmf_display = '';
        $firstRow  = array_shift($hmf_list);
        $detail = $this->dealHmfProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $hmf_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">号码分</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($hmf_list as $item) {
                $detail = $this->dealHmfProductData($item);
                $item['type'] = $this->type_map[$item['type']];
                $hmf_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $hmf_display;
        }


        //dh list =================================================================================
        $dh_list = array_merge($data['dh_compare_res']['total'], $data['dh_compare_res']['success'], $data['dh_compare_res']['valid']);
        $rowspan = count($dh_list);
        $dh_display = '';
        $firstRow  = array_shift($dh_list);
        $detail = $this->dealDhProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $dh_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">贷后风险</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($dh_list as $item) {
                $detail = $this->dealDhProductData($item);
                $item['type'] = $this->type_map[$item['type']];
                $dh_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $dh_display;
        }

        //jindun list =================================================================================

        $jindun_list = array_merge($data['jindun_compare_res']['total'], $data['jindun_compare_res']['success'], $data['jindun_compare_res']['valid']);
        $rowspan = count($jindun_list);
        $jindun_display = '';
        $firstRow  = array_shift($jindun_list);
        $detail = $this->dealHmfProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $jindun_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">金盾</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($jindun_list as $item) {
                $detail = $this->dealHmfProductData($item);
                $item['type'] = $this->type_map[$item['type']];
                $jindun_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $jindun_display;
        }


        $display .= "</table><br/>";

        return $display;
    }



    private function createTwiceTableHtml($data)
    {
        $display = <<<HTML
<b class="table_title">二: 每日打包量比较</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">产品分类</th>
	<th align="center" nowrap="nowrap">子产品个数</th>
	<th align="center" nowrap="nowrap">产品ID</th>
	<th align="center" nowrap="nowrap">产品名称</th>
	<th align="center" nowrap="nowrap">账号apikey</th>
	<th align="center" nowrap="nowrap">运营商</th>
	<th align="center" nowrap="nowrap">比较类型</th>
	<th align="center" nowrap="nowrap">系统日志数值</th>
	<th align="center" nowrap="nowrap">后台数值</th>
</tr>
HTML;

        //bxf list
        $bxf_list = array_merge($data['bxf_compare_res']['total'], $data['bxf_compare_res']['success'], $data['bxf_compare_res']['valid']);
        $rowspan = count($bxf_list);
        $bxf_display = '';
        $firstRow  = array_shift($bxf_list);
        $detail = $this->dealPackageData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $bxf_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦信分</td>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($bxf_list as $item) {
                $detail = $this->dealPackageData($item);
                $item['type'] = $this->type_map[$item['type']];
                $bxf_display .= <<<HTML
<tr>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $bxf_display;
        }


        //bmy list =================================================================================

        $bmy_list = array_merge($data['bmy_compare_res']['total'], $data['bmy_compare_res']['success'], $data['bmy_compare_res']['valid']);
        $rowspan = count($bmy_list);
        $bmy_display = '';
        $firstRow  = array_shift($bmy_list);
        $detail = $this->dealPackageData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $bmy_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦秒验</td>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($bmy_list as $item) {
                $detail = $this->dealPackageData($item);
                $item['type'] = $this->type_map[$item['type']];
                $bmy_display .= <<<HTML
<tr>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $bmy_display;
        }


        //hmfxdj list =================================================================================

        $hmfxdj_list = array_merge($data['hmfxdj_compare_res']['total'], $data['hmfxdj_compare_res']['success'], $data['hmfxdj_compare_res']['valid']);
        $rowspan = count($hmfxdj_list);
        $hmfxdj_display = '';
        $firstRow  = array_shift($hmfxdj_list);
        $detail = $this->dealHmfxdjPackageData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $hmfxdj_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">号码风险等级</td>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($hmfxdj_list as $item) {
                $detail = $this->dealHmfxdjPackageData($item);
                $item['type'] = $this->type_map[$item['type']];
                $hmfxdj_display .= <<<HTML
<tr>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $hmfxdj_display;
        }

        //hmf list =================================================================================

        $hmf_list = array_merge($data['hmf_compare_res']['total'], $data['hmf_compare_res']['success'], $data['hmf_compare_res']['valid']);
        $rowspan = count($hmf_list);
        $hmf_display = '';
        $firstRow  = array_shift($hmf_list);
        $detail = $this->dealHmfPackageData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $hmf_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">号码分</td>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($hmf_list as $item) {
                $detail = $this->dealHmfxdjPackageData($item);
                $item['type'] = $this->type_map[$item['type']];
                $hmf_display .= <<<HTML
<tr>
	<td align="right">{$detail['sub_num']}</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $hmf_display;
        }


        $display .= "</table><br/>";

        return $display;
    }


    private function createThirdTableHtml($data)
    {
        $display = <<<HTML
<b class="table_title">三: 每日渠道量比较</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">产品分类</th>
	<th align="center" nowrap="nowrap">接口id</th>
	<th align="center" nowrap="nowrap">接口名称</th>
	<th align="center" nowrap="nowrap">渠道id</th>
	<th align="center" nowrap="nowrap">账号apikey</th>
	<th align="center" nowrap="nowrap">运营商</th>
	<th align="center" nowrap="nowrap">加密方式</th>
	<th align="center" nowrap="nowrap">比较类型</th>
	<th align="center" nowrap="nowrap">系统日志数值</th>
	<th align="center" nowrap="nowrap">后台数值</th>
</tr>
HTML;

        //bxf list
        $bxf_list = array_merge($data['bxf_compare_res']['total'], $data['bxf_compare_res']['success'], $data['bxf_compare_res']['valid']);
        $rowspan = count($bxf_list);
        $bxf_display = '';
        $firstRow  = array_shift($bxf_list);
        $detail = $this->dealBxfChannelData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $bxf_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦信分</td>
	<td align="right">{$detail['iid']}</td>
	<td align="right">{$detail['iname']}</td>
	<td align="right">{$detail['channel_id']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$detail['encrypt']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($bxf_list as $item) {
                $detail = $this->dealBxfChannelData($item);
                $item['type'] = $this->type_map[$item['type']];
                $bxf_display .= <<<HTML
<tr>
	<td align="right">{$detail['iid']}</td>
	<td align="right">{$detail['iname']}</td>
	<td align="right">{$detail['channel_id']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$detail['encrypt']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $bxf_display;
        }


        //bmy list =================================================================================

        $bmy_list = array_merge($data['bmy_compare_res']['total'], $data['bmy_compare_res']['success'], $data['bmy_compare_res']['valid']);
        $rowspan = count($bmy_list);
        $bmy_display = '';
        $firstRow  = array_shift($bmy_list);
        $detail = $this->dealBmyChannelData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $bmy_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦秒验</td>
	<td align="right">{$detail['iid']}</td>
	<td align="right">{$detail['iname']}</td>
	<td align="right">{$detail['channel_id']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$detail['encrypt']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
</tr>
HTML;
            foreach ($bmy_list as $item) {
                $detail = $this->dealBmyChannelData($item);
                $item['type'] = $this->type_map[$item['type']];
                $bmy_display .= <<<HTML
<tr>
	<td align="right">{$detail['iid']}</td>
	<td align="right">{$detail['iname']}</td>
	<td align="right">{$detail['channel_id']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$detail['encrypt']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
</tr>
HTML;

            }

            $display .= $bmy_display;
        }


        $display .= "</table><br/>";

        return $display;
    }


    private function createFourthTableHtml($data)
    {
        $display = <<<HTML
<b class="table_title">一: 每月客户子产品量比较</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" nowrap="nowrap">产品分类</th>
	<th align="center" nowrap="nowrap">产品ID</th>
	<th align="center" nowrap="nowrap">产品名称</th>
	<th align="center" nowrap="nowrap">账号apikey</th>
	<th align="center" nowrap="nowrap">运营商</th>
	<th align="center" nowrap="nowrap">比较类型</th>
	<th align="center" nowrap="nowrap">系统日志数值</th>
	<th align="center" nowrap="nowrap">后台数值</th>
	<th align="center" nowrap="nowrap">差值</th>
</tr>
HTML;

        //bxf list
        $bxf_list = array_merge($data['bxf_compare_res']['total'], $data['bxf_compare_res']['success'], $data['bxf_compare_res']['valid']);
        $rowspan = count($bxf_list);
        $bxf_display = '';
        $firstRow  = array_shift($bxf_list);
        $detail = $this->dealProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $diff = $this->getDiffValue($firstRow);
            $bxf_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦信分</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;
            foreach ($bxf_list as $item) {
                $detail = $this->dealProductData($item);
                $diff = $this->getDiffValue($item);
                $item['type'] = $this->type_map[$item['type']];
                $bxf_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;

            }

            $display .= $bxf_display;
        }


        //bmy list =================================================================================

        $bmy_list = array_merge($data['bmy_compare_res']['total'], $data['bmy_compare_res']['success'], $data['bmy_compare_res']['valid']);
        $rowspan = count($bmy_list);
        $bmy_display = '';
        $firstRow  = array_shift($bmy_list);
        $detail = $this->dealProductData($firstRow);

        if($rowspan) {
            $diff = $this->getDiffValue($firstRow);
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $bmy_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">邦秒验</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;
            foreach ($bmy_list as $item) {
                $detail = $this->dealProductData($item);
                $item['type'] = $this->type_map[$item['type']];
                $diff = $this->getDiffValue($item);

                $bmy_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;

            }

            $display .= $bmy_display;
        }


        //hmfxdj list =================================================================================

        $hmfxdj_list = array_merge($data['hmfxdj_compare_res']['total'], $data['hmfxdj_compare_res']['success'], $data['hmfxdj_compare_res']['valid']);
        $rowspan = count($hmfxdj_list);
        $hmfxdj_display = '';
        $firstRow  = array_shift($hmfxdj_list);
        $detail = $this->dealHmfxdjProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $diff = $this->getDiffValue($firstRow);
            $hmfxdj_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">号码风险等级</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;
            foreach ($hmfxdj_list as $item) {
                $detail = $this->dealHmfxdjProductData($item);
                $diff = $this->getDiffValue($item);

                $item['type'] = $this->type_map[$item['type']];
                $hmfxdj_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;

            }

            $display .= $hmfxdj_display;
        }


        //hmf list =================================================================================

        $hmf_list = array_merge($data['hmf_compare_res']['total'], $data['hmf_compare_res']['success'], $data['hmf_compare_res']['valid']);
        $rowspan = count($hmf_list);
        $hmf_display = '';
        $firstRow  = array_shift($hmf_list);
        $detail = $this->dealHmfProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $diff = $this->getDiffValue($firstRow);

            $hmf_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">号码分</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;
            foreach ($hmf_list as $item) {
                $detail = $this->dealHmfProductData($item);
                $diff = $this->getDiffValue($item);

                $item['type'] = $this->type_map[$item['type']];
                $hmf_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;

            }

            $display .= $hmf_display;
        }

        //jindun list =================================================================================

        $jindun_list = array_merge($data['jindun_compare_res']['total'], $data['jindun_compare_res']['success'], $data['jindun_compare_res']['valid']);
        $rowspan = count($jindun_list);
        $jindun_display = '';
        $firstRow  = array_shift($jindun_list);
        $detail = $this->dealHmfProductData($firstRow);
        if($rowspan) {
            $firstRow['type'] = $this->type_map[$firstRow['type']];
            $diff = $this->getDiffValue($firstRow);

            $jindun_display .= <<<HTML
<tr>
	<td align="center" rowspan="{$rowspan}">金盾</td>
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$firstRow['type']}</td>
	<td align="right">{$firstRow['ck']}</td>
	<td align="center">{$firstRow['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;
            foreach ($jindun_list as $item) {
                $detail = $this->dealHmfProductData($item);
                $diff = $this->getDiffValue($item);

                $item['type'] = $this->type_map[$item['type']];
                $jindun_display .= <<<HTML
<tr>
	
	<td align="right">{$detail['pid']}</td>
	<td align="right">{$detail['pname']}</td>
	<td align="right">{$detail['apikey']}</td>
	<td align="right">{$detail['operator']}</td>
	<td align="right">{$item['type']}</td>
	<td align="right">{$item['ck']}</td>
	<td align="center">{$item['mysql']}</td>
	<td align="center">{$diff}</td>
</tr>
HTML;

            }

            $display .= $jindun_display;
        }


        $display .= "</table><br/>";

        return $display;
    }



    private function createExceptionTableHtml($data)
    {
        $display = <<<HTML
<b class="table_title">四: 异常信息</b>
<table border="1" cellpadding="5" cellspacing="0" width="98%">
<tr>
	<th align="center" colspan="2" nowrap="nowrap">接口异常</th>	
</tr>
HTML;

        $exception_display = '';
        $exception_list = $this->dealExceptionData($data);

        foreach ($exception_list as $item) {
            $exception_display .= <<<HTML
<tr>
<td align="left">channel_id:{$item['channel_id']}</td>
<td align="left">interface_name:{$item['interface_name']}</td>
</tr>
HTML;

        }

        $display .= $exception_display;

        $display .= "</table><br/>";

        return $display;
    }





    private function dealProductData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['pid'] = $arr[0];
            $detail['apikey'] = $arr[1];
            $detail['operator'] = $arr[2] ? $arr[2] : '空';
            $detail['pname'] = RedisCache::instance('productId_productName_mapping')->get($detail['pid']);

            return $detail;
        }

        return [];
    }

    private function dealHmfxdjProductData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['pid'] = $arr[0];
            $detail['apikey'] = $arr[1];
            $detail['operator'] = '空';
            $detail['pname'] = RedisCache::instance('productId_productName_mapping')->get($detail['pid']);

            return $detail;
        }

        return [];
    }

    private function dealHmfProductData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['pid'] = $arr[0];
            $detail['apikey'] = $arr[1];
            $detail['operator'] = '空';
            $detail['pname'] = RedisCache::instance('productId_productName_mapping')->get($detail['pid']);

            return $detail;
        }

        return [];
    }

    private function dealDhProductData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['pid'] = $arr[0];
            $detail['apikey'] = $arr[1];
            $detail['operator'] = '空';
            $detail['pname'] = RedisCache::instance('productId_productName_mapping')->get($detail['pid']);

            return $detail;
        }

        return [];
    }

    private function dealPackageData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['sub_num'] = $arr[0];
            $detail['pid'] = $arr[1];
            $detail['apikey'] = $arr[2];
            $detail['operator'] = $arr[3] ? $arr[3] : '空';
            $detail['pname'] = RedisCache::instance('productId_productName_mapping')->get($detail['pid']);

            return $detail;
        }

        return [];
    }

    private function dealHmfxdjPackageData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['sub_num'] = $arr[0];
            $detail['pid'] = $arr[1];
            $detail['apikey'] = $arr[2];
            $detail['operator'] = '空';
            $detail['pname'] = RedisCache::instance('productId_productName_mapping')->get($detail['pid']);

            return $detail;
        }

        return [];
    }

    private function dealHmfPackageData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['sub_num'] = $arr[0];
            $detail['pid'] = $arr[1];
            $detail['apikey'] = $arr[2];
            $detail['operator'] = '空';
            $detail['pname'] = RedisCache::instance('productId_productName_mapping')->get($detail['pid']);

            return $detail;
        }

        return [];
    }

    private function dealBxfChannelData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['iid'] = $arr[0];
            $detail['apikey'] = $arr[1];
            $detail['operator'] = $arr[2] ? $arr[2] : '空';
            $detail['encrypt'] = '固定';

            if($detail['iid']){
                $interface = ChannelInterface::getInterfaceInfo(['id'=>$detail['iid']]);
                $detail['iname'] = $interface['name'];//获取interface name
                $detail['channel_id'] = RedisCache::instance('iid_channelId_mapping')->get($detail['iid']);
            }else{
                $detail['iname'] = 'iid异常';
                $detail['channel_id'] = 'iid异常';
            }

            return $detail;
        }

        return [];
    }

    private function dealBmyChannelData($row){
        if(isset($row['item']) && !empty($row['item'])){
            $arr = explode('_', $row['item']);
            $detail['iid'] = $arr[0];
            $detail['apikey'] = $arr[1];
            $detail['operator'] = $arr[2] ? $arr[2] : '空';
            $detail['encrypt'] = $arr[3];
            if($detail['iid']){
                $interface = ChannelInterface::getInterfaceInfo(['id'=>$detail['iid']]);
                $detail['iname'] = $interface['name'];//获取interface name
                $detail['channel_id'] = RedisCache::instance('iid_channelId_mapping')->get($detail['iid']);
            }else{
                $detail['iname'] = 'iid异常';
                $detail['channel_id'] = 'iid异常';
            }
            return $detail;
        }

        return [];
    }

    private function dealExceptionData($data){
        $result = [];
        foreach ($data as $val){
            $arr = explode(':', $val);
            $deatil['interface_name'] = $arr[0] ?? '接口异常信息设置有误interface_name';
            $deatil['channel_id'] = $arr[1]?? '接口异常信息设置有误channel_id';
            $result[] = $deatil;
        }

        return $result;
    }

    private function getDiffValue($row){
        $diff = intval($row['ck']) - intval($row['mysql']);
        return $diff;
    }

    private function getMonthStartDate(){
        return substr($this->end_date, 0, 6).'01';
    }


    private function sendMail($html)
    {
        if($this->start_date == $this->end_date){
            $title = $this->start_date . "产品调用量mysql数据与ck数据比较";
        }else{
            $title = $this->start_date.'至'.$this->end_date. "产品调用量mysql数据与ck数据比较";
        }

        $sendMailService = new SendMailService();

        $addressee = array_map(function ($email) {
            return compact('email');
        }, Config('clickhouse.mail_direct_list'));

        $sendMailService->setSubject($title)
            ->setContent($html)
            ->setAddressee($addressee)
            ->setCC(Config('clickhouse.mail_cc_list'))
            ->setFromName('金融后台项目组')
            ->send();
    }


    private function log($content)
    {

        $filename = __DIR__ . '/../../../runtime/test.log';
        $fp = fopen($filename, 'a+');

        $time = date('Y-m-d H:i:s');

        $content = $time . ' : ' . $content . "\n";

        fwrite($fp, $content);
    }

}
