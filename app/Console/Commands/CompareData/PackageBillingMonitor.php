<?php

namespace App\Console\Commands\CompareData;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\ConfigPriceCustomer;
use App\Models\EmailConfig;
use App\Models\Product;
use App\Models\StatisticsCustomerTogetherCallUsage;
use App\Models\StatisticsCustomerUsage;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

//打包计费监控
class PackageBillingMonitor extends Command
{
	protected $signature = 'package_billing_monitor
	{--start_date= : 起始日期, 格式Ymd，默认今日}
	{--end_date= : 结束日期, 格式Ymd，默认今日}
	{--email= : 指定发送的邮箱}
	';
	
	protected $description = '打包计费监控';

    private $scene = 'package_billing_warning';
	protected $start_date;
	protected $end_date;
	protected $current_date;
    protected $buy_product_map = [];
	protected $email = null;
	protected $special_customer_rule1 = [
        '4c573202ec031d87e2b26a36332e5eef_200' => [
            'apikey' => '4c573202ec031d87e2b26a36332e5eef',
            'father_id' => 200,
            'product_ids' => '321,322,323,324,325,326,327,328',
        ],
        'b3d1e801811c11177b0d44933a4dc0b4_30000' => [
            'apikey' => 'b3d1e801811c11177b0d44933a4dc0b4',
            'father_id' => 30000,
            'product_ids' => '30702,31201',
        ],
    ];

    protected $special_customer_rule2 = [
        'cf8b499ad98da6e2f94d8baaf013c3f9_200' => [
            'apikey' => 'cf8b499ad98da6e2f94d8baaf013c3f9',
            'father_id' => 200,
            'product_ids' => [
                ['322,323,324']
            ],
        ],
        'a01463e997ba1afa11df6bf4720e9e3d_200' => [
            'apikey' => 'a01463e997ba1afa11df6bf4720e9e3d',
            'father_id' => 200,
            'product_ids' => [
                ['321,322,323,324,325,326,327,328']
            ],
        ],
        'd0a72d812586c05eed851957d58faff0_200' => [
            'apikey' => 'd0a72d812586c05eed851957d58faff0',
            'father_id' => 200,
            'product_ids' => [
                ['321,322,323,324,325,326,327,328']
            ],
        ],
        'ccbb28c25ce4493a07f21a605c13f51d_10000' => [
            'apikey' => 'ccbb28c25ce4493a07f21a605c13f51d',
            'father_id' => 10000,
            'product_ids' => [
                [10101,10103,10106],
                [14201,14202,14203,14204,14205],
            ],
        ],
        '22b8c6baabd2164b1b3e5d842ac9bed5_200' => [
            'apikey' => '22b8c6baabd2164b1b3e5d842ac9bed5',
            'father_id' => 200,
            'product_ids' => [
                [321,322,323,324,325,326,327,328]
            ],
        ],
        'cf8b499ad98da6e2f94d8baaf013c3f9_615' => [
            'apikey' => 'cf8b499ad98da6e2f94d8baaf013c3f9',
            'father_id' => 615,
            'product_ids' => [
                [661,662]
            ],
        ],
    ];

    protected $send_msg = [];

	
	public function handle()
	{
		//校验参数
        if(!$this->checkParams()){
            return;
        }

        //获取账号开通产品情况
        $this->customerBuyProductsInfo();

        $date = $this->start_date;
        while ($date <= $this->end_date){
            #todo try catch
            $this->done($date);
            $date = date('Ymd', strtotime($date) + 86400);
        }

        if(!empty($this->send_msg)){
            $data = $this->formatMsg();

            $this->sendMail($data);
        }

        $this->output->success("打包计费监控成功执行");
	}
	
	/**
	 * 校验参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2021/12/29
	 *
	 * @return void
	 **/
	protected function checkParams()
	{
        $options = $this->options();

        $this->start_date = $options['start_date'] ?: date('Ymd', strtotime('-1 days'));
        $this->end_date = $options['end_date'] ?: date('Ymd', strtotime('-1 days'));

        if(!empty($this->start_date) && empty($this->end_date)){
            echo "截止日期不能为空\r\n";
            return false;
        }

        if(!empty($this->end_date) && empty($this->start_date)){
            echo "起始日期不能为空\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->start_date) && !empty($this->start_date)) {
            echo "起始日期格式不正确\r\n";
            return false;
        }

        if (!preg_match('/^\d{8}$/', $this->end_date) && !empty($this->end_date)) {
            echo "截止日期格式不正确\r\n";
            return false;
        }

        if($this->end_date < $this->start_date){
            echo "起始日期不能大于结束日期\r\n";
            return false;
        }

        $this->email = $options['email'] ?: null;

        return true;

	}

    protected function customerBuyProductsInfo(){
        //需要排除的主产品
        $main_product = Product::getHasProductFatherId();
        $main_product = array_column($main_product, 'product_id');

        //$result = [];
        AccountProduct::where('account_product.status', 1)
            ->whereNotIn('product_id', $main_product)
            ->select([
                'apikey',
                'product_id',
            ])
            ->leftJoin('account', 'account.account_id', '=', 'account_product.account_id')
            ->get()
            ->map(function ($item) {
                $apikey = $item['apikey'];
                $product_id = $item['product_id'];
                $father_id = RedisCache::instance('productId_fatherId_mapping')->get($product_id);
                $key = $apikey.'_'.$father_id;

                $this->buy_product_map[$key][] = $product_id;
            });

    }

	protected function done($date){
        $this->current_date = $date;
        //获取客户计费的配置列表
        $config_list = $this->getCustomerPriceConfig();

        foreach ($config_list as $item){
            $index = $item['apikey'].'_'.$item['father_id'];
            if(isset($this->special_customer_rule1[$index])){
                $item['rule'] = 1;
                //重置mode 、product_ids
                $item['mode'] = 2;
                $item['product_ids'] = $this->special_customer_rule1[$index]['product_ids'];
            }else if(isset($this->special_customer_rule2[$index])){
                $item['rule'] = 2;
                //重置mode 、product_ids
                $item['mode'] = 2;
                $item['product_ids'] = $this->special_customer_rule2[$index]['product_ids'];
            }else{
                $item['rule'] = 0;
            }

            //非打包计费跳过
            if($item['mode'] != 2){
                continue;
            }

            if(in_array($item['rule'], [0, 1])){
                $res = $this->checkCustomerCallUsage($item);
            }else{
                $res = $this->checkCustomerCallUsageV2($item);
            }

            if($res['code'] == 0){
                continue;
            }else{
                $res['date'] = $date;
                $this->send_msg[] = $res;
            }
        }

    }


    protected  function  sendMail($data)
    {
        $emails = EmailConfig::getEamilAddressByScene($this->scene);
        //日报发送内容拼接
        $html = $this->createHtml($data);
        $mail = new SendMailService();
        $subject = $this->start_date.'-'.$this->end_date.'打包计费监控';

        if(!empty($emails)){
            foreach ($emails as $v){
                if (!is_null($this->email)){
                    if ($v['email'] != $this->email){
                        continue;
                    }
                }
                //日报 每日发送
                $mail->setFromName('金融后台项目组')
                    ->setAddressee([$v])
                    //->setCC($this->cc)
                    ->setSubject($subject)
                    ->setContent($html)
//                    ->sendByAsync();
                    ->send();
            }
        }


    }


    public function formatMsg(){
        $result = [];
        foreach ($this->send_msg as $item){
            $index = $item['apikey'].'_'.$item['father_id'];
            $result[$index][] = $item;
        }

        unset($this->send_msg);
        return $result;
    }

    public function checkCustomerCallUsageV2($condition = []){
        $msg = '';
        foreach ($condition['product_ids'] as $package_pids){
            $list = StatisticsCustomerUsage::select([
                'call_product_id as product_id',
                DB::raw('SUM(`total`) as total')
            ])
                ->where('date', '=', $this->current_date)
                ->where('apikey', '=', $condition['apikey'])
                ->whereIn('call_product_id', $package_pids)
                ->groupBy(['call_product_id'])
                ->get()
                ->toArray();

            if(empty($list)){
                continue;
            }

            $total = array_sum(array_column($list, 'total'));
            $avg = bcdiv($total, count($list), 0);
            $is_error = false;
            foreach ($list as $item){
                $diff = $avg - $item['total'];
                if(abs($diff) > 30){
                    $is_error = true;
                    break;
                }
            }

            if($is_error){
                $msg .= "打包调用产品:".implode(',', $package_pids).",他们的总量有较多差异;".PHP_EOL;
            }

        }

        if(empty($msg)){
            return ['code' => 0];
        }else{
            return ['code' => 1000, 'apikey' => $condition['apikey'], 'father_id' => $condition['father_id'], 'msg' => $msg];
        }

    }

    public function checkCustomerCallUsage($condition = []){
        $index = $condition['apikey'].'_'.$condition['father_id'];

        //计费配置中的产品信息情况
        $product_ids = explode(',', $condition['product_ids']);
        $call_product_number = count($product_ids);

        //1、运营会禁用产品或取消开通产品 但未从新配置计费(计费配置中的产品信息未更新) (实际调用没那么多产品)
        //2、还有一种情况是新增产品 但未从新配置计费 (实际少调用了一些产品)
        $buy_products = $this->buy_product_map[$index] ?? [];//实际开通使用产品

        //上面第1种情况， 其他情况较复杂 按计费配置中产品信息来检测就行
        if(count($buy_products) < $call_product_number){
            $is_cover = true;//是否覆盖计费配置中产品信息 默认覆盖
            foreach ($buy_products as $pid){
                if(!in_array($pid, $product_ids)){
                    $is_cover = false;
                    break;
                }
            }

            if($is_cover){
                $product_ids = $buy_products;
                $call_product_number = count($product_ids);
            }
        }


        $list = StatisticsCustomerTogetherCallUsage::select([
            'sub_product_number'
        ])
            ->where('date', '=', $this->current_date)
            ->where('apikey', '=', $condition['apikey'])
            ->where('product_id', $condition['father_id'])
            ->groupBy(['sub_product_number'])
            ->get()
            ->toArray();

        if(empty($list)){
            return ['code' => 0];
        }

        $error_call_product_number = [];
        foreach ($list as $item){
            if($item['sub_product_number'] != $call_product_number){
                $error_call_product_number[] = $item['sub_product_number'].'个';
            }
        }
        if(!empty($error_call_product_number)){
            $msg = "使用计费id为".$condition['id'].", 打包调用产品应为".$call_product_number."个".PHP_EOL;
            $msg .= '错误出现'.implode(',', $error_call_product_number)."的情况";
            return ['code' => 1000, 'apikey' => $condition['apikey'], 'father_id' => $condition['father_id'], 'msg' => $msg];
        }


        $list = StatisticsCustomerUsage::select([
            'call_product_id as product_id',
            DB::raw('SUM(`total`) as total')
        ])
            ->where('date', '=', $this->current_date)
            ->where('apikey', '=', $condition['apikey'])
            ->whereIn('call_product_id', $product_ids)
            ->groupBy(['call_product_id'])
            ->get()
            ->toArray();

        if(empty($list)){
            $msg = '打包量表有调用记录,客户子产品调用量表却为空';
            return ['code' => 1000, 'apikey' => $condition['apikey'], 'father_id' => $condition['father_id'], 'msg' => $msg];
        }

        $list = array_column($list, 'total', 'product_id');
        //$list['666'] = 100;
        $no_call_pids = [];
        foreach ($product_ids as $pid){
            $total = $list[$pid] ?? 0;
            if($total == 0){
                $no_call_pids[] = $pid;
            }
            unset($list[$pid]);
        }

        if(!empty($no_call_pids)){
            $msg = "使用计费id为".$condition['id'].", 打包未调用产品".implode(',', $no_call_pids);
            return ['code' => 1000, 'apikey' => $condition['apikey'], 'father_id' => $condition['father_id'], 'msg' => $msg];
        }

        if(!empty($list)){
            $other_call_product = array_keys($list);
            $msg = "使用计费id为".$condition['id'].", 打包多调用产品".implode(',', $other_call_product);
            return ['code' => 1000, 'apikey' => $condition['apikey'], 'father_id' => $condition['father_id'], 'msg' => $msg];
        }

        return ['code' => 0];
    }

    public function getCustomerPriceConfig(){
        $result = [];
        ConfigPriceCustomer::where('start_date', '<=', $this->current_date)
            ->whereNull('delete_time')
            ->orderBy('start_date', 'desc')
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($item) use (&$result) {
                $apikey    = $item['apikey'];
                $father_id = $item['father_id'];

                $key = $apikey . '_' . $father_id;
                if (!array_key_exists($key, $result)) {
                    $result[$key] = $item->toArray();
                }

            });

        return $result;
    }


    /*
     * 生成邮件发送内容
     */
    private function createHtml($data)
    {
        $content = '';

        $content .= '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
            font-size   : 12px;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<div>';
//<h1>'; $content .= '</h1>';

        $html =  $this->createFirstTable($data);

        $content .= $html;

        $content .= '<br />
</div> 

';

        return $content;

    }


    //警报内容
    private function createFirstTable($data)
    {
        $html = '';

        $html .= <<<HTML
        <h2>打包计费监控未按正常情况调用列表</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th align="center">账号</th>
        <th align="center">父产品</th>
        <th align="left">日期</th>
        <th align="left">错误原因</th>
        </tr>

HTML;
        //动态加载成功列

        foreach($data as $key=>$item){
            $rowspan = count($item);
            $arr = explode('_', $key);
            $apikey = $arr[0];
            $father_id = $arr[1];
            $account_name = $this->getAccountName($apikey);
            $father_name = $this->getProductName($father_id);
            $firstRow  = array_shift($item);
            $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center"  text-align:center" align="center" rowspan="{$rowspan}">$account_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;  text-align:center" align="center" rowspan="{$rowspan}">$father_name</td>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;" align="left">{$firstRow['date']}</td>
	<td align="left">{$firstRow['msg']}</td>
</tr>
HTML;
            foreach ($item as $key2 => $item2){

                $html .= <<<HTML
<tr>
	<td style="width:110px;padding-left: 4px;padding-right: 4px;" align="center">{$item2['date']}</td>
	<td align="left">{$item2['msg']}</td>
</tr>
HTML;
            }

        }


        $html .= '</table>';

        return $html;
    }

    public function getAccountName($apikey = ''){
        $info = Account::getAccountByApikey($apikey);
        return $info['account_name'] ?? '';
    }

    public function getProductName($product_id = ''){
        $p_name = RedisCache::instance('productId_productName_mapping')->get($product_id);
        return $p_name;
    }

    public function postRawJson($url, $params, $options = [])
    {
        $params                      = json_encode($params, JSON_UNESCAPED_UNICODE);
        $options[CURLOPT_HTTPHEADER] = [
            'X-AjaxPro-Method:ShowList',
            'Content-Type: application/json; charset=utf-8',
            'Content-Length: ' . strlen($params),
        ];
        $options[CURLOPT_POST]       = true;
        $options[CURLOPT_POSTFIELDS] = $params;

        return $this->curl($url, $options);
    }

    public function curl($url, $options)
    {
        $curl = curl_init();

        $defaultOptions = [
            CURLOPT_URL            => $url,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_TIMEOUT        => 20,
            CURLOPT_RETURNTRANSFER => true,
        ];
        $options        = $defaultOptions + $options;
        curl_setopt_array($curl, $options);

        $data    = curl_exec($curl);
        $code    = curl_errno($curl);
        $message = curl_error($curl);
        $httpcode = curl_getinfo($curl,CURLINFO_HTTP_CODE); ;

        return compact('code', 'message', 'data');
    }





}