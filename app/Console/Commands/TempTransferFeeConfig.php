<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\AccountProduct;
use App\Models\AccountProductModel;
use App\Models\BillConfig;
use App\Models\BillCustomerIncome;
use App\Models\ConfigPriceCustomer;
use App\Models\Product;
use Illuminate\Console\Command;
use Symfony\Component\Console\Helper\ProgressBar;

/**
 * Class TempTransferFeeConfig 转移历史计费配置数据
 * @package App\Console\Commands
 */
class TempTransferFeeConfig extends Command
{
	public $signature = 'temp:transfer_fee_config';
	
	/**
	 * @var ProgressBar
	 */
	protected $progressBar;
	
	protected $handTransferId = [];
	
	/**
	 * @var array 需要手动转移的数据
	 */
	protected $handleTransferCustomerIds = [];
	
	public function handle()
	{
		$result = [];
		BillConfig::where('is_delete', 0)
				  ->whereNotIn('product_id', [301, 302, 105, 801])
				  ->where(function ($query) {
					  if ($this->id) {
						  $query->where('id', $this->id);
					  }
				  })
				  ->orderBy('start_date', 'asc')
				  ->get()
				  ->map(function ($item) use (&$result) {
					  $account_id                        = $item['account_id'];
					  $product_id                        = $item['product_id'];
					  $start_date                        = $item['start_date'];
					  $result[$account_id . $product_id] = $item->toArray();
			
				  });
		
		//部分客户、账号已经不使用了，不需要弄计费配置
		
		$filterCustomerIds = [
			'C20180831BZ7V8N',
			'C20180904KQV9M4',
			'C20181016J5QX55',
			'C2018101602KY9F',
			'********1WSQR8U',
			'********1YPB9L7',
			'********15ZS131',
			'********19FAXD9',
			'********1V3HLY3',
			'********16LKRUP',
			'********1T9LQK1',
			'********1OU3082',
			'********1CCE390',
			'********1VS6AJO',
			'********1V0DTMJ',
			'********1R0FWSS',
			'********130YKSX',
			'********1Q0ILM1',
			'********1XSMW9V',
			'********1GGOBS2',
			'********1QBHHO0',
			'********18G270Y',
			'********16QTZSB',
			'********1JI6H4L',
			'********14JOQPE',
			'********1OT2IDD',
			'********1ZQRQMO',
			'********10OWLVK',
			'********1UQILA4',
			'********1FV5032',
			'********1X507LE',
			'********1LJJT2T',
			'********12A06ZS',
			'********1N2RO9B',
			'********1Z1HDCN',
			'********1UDRGMY',
			'********1QZFBJQ',
			'********1ZQMALK',
			'********1NRILDC',
			'********1V774XF',
			'********1G4VNHX',
			'********1P3VET6',
			'********1CZWJBE',
			'********1F2WWS2',
			'********1JLUMLU',
			'********16S79YF',
			'********1A9XUFO',
			'********1LILKX5',
			'********1O1XSCW',
			'********1JNYDTO',
			'********1A3WI3V',
			'********1AMO3JX',
			'********1AUVQMT',
			'********14DOEVV',
			'********1MY46ID',
			'********1UYCKEO',
			'********1RFXXMA',
			'********1VXPDD1',
			'********1XMRKMI',
			'********1XLNNEC',
			'********1ZYCO4U',
			'********1IUB34M',
			'********15PKP5M',
			'********1X16FXP',
			'C20181205NFDYPH',
		];
		$filterAccountIds  = [''];
		
		$result = array_filter($result, function ($item) use ($filterCustomerIds, $filterAccountIds) {
			return $item['remarks'] != '系统归零设置' && !in_array($item['customer_id'], $filterCustomerIds) && !in_array($item['account_id'], $filterAccountIds);
		});
		
		$this->progressBar = $this->output->createProgressBar(count($result));
		
		foreach ($result as $item) {
			$this->transferItem($item);
		}
		
		
		//		$total = ;
		//		$this->output->writeln("合计{$total}条配置");
		//		$end     = 0;
		//		$counter = 0;
		//		foreach ($result as $item) {
		//			if ($end == $counter) {
		//				halt($item);
		//			}
		//			$counter++;
		//		}
		
		
		$this->output->success("已完成");
		$customerIds = array_unique($this->handleTransferCustomerIds);
		
		$this->output->warning("请手动处理邦秒验计费配置：" . implode(',', $customerIds));
		//		halt();
		//
		//		halt(count($result));
		//		array_walk($result, function ($item) {
		//
		//			//如果是非父子产品的
		//			$product_id = $item['product_id'];
		//			$father_id  = Product::where('product_id', $product_id)
		//								 ->pluck('father_id');
		//			if (210 == $product_id) {
		//
		//			} else if (0 == $father_id) {
		//				//
		//			} else {
		//				//需要调整到记录在父产品上
		//				//拉取相同时间、相同账号、
		//			}
		//			halt($item, $father_id);
		//		});
		//
		//		halt(array_values($result));
		
		
	}
	
	/**
	 * 转移某一条计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/12 13:07
	 *
	 * @param $config array 配置
	 *
	 * @return void
	 */
	protected function transferItem($config)
	{
		if (!$config['fee_price']) {
			$this->progressBar->advance();
			
			return;
		}
		
		$apikey     = $this->getApikey($config);
		$father_id  = $this->getFatherId($config);
		$start_date = $this->getStartDate($config);
		
		//如果已经处理过了，就不在处理了
		if ($this->isSubProductId615($config) || $this->isSubProductId210($config) || $this->is200($config)) {
			$count = ConfigPriceCustomer::where('start_date', $start_date)
										->where('apikey', $apikey)
										->where('father_id', $father_id)
										->count();
			if ($count > 0) {
				$this->progressBar->advance();
				
				return;
			}
		}
		
		$product_ids   = $this->getProductIds($config);
		$accord        = $this->getAccord($config);
		$methods       = $this->getMethods($config);
		$period        = $this->getPeriod($config);
		$diff_operator = $this->getDiffOperator($config);
		$mode          = $this->getMode($config);
		$price         = $this->getPrice($config);
		$remark        = $this->getRemark($config);
		
		if (!$price) {
			$this->progressBar->advance();
			
			return;
		}
		
		
		ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'remark', 'mode', 'start_date', 'price'));
		
		$this->progressBar->advance();
	}
	
	/**
	 * 转移金盾子产品计费的模式
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/14 15:07
	 *
	 * @param $config array
	 *
	 * @return void
	 */
	protected function transferGoldenShieldSubConfig($config)
	{
		$account_id        = $config['account_id'];
		$start_date        = date('Ymd', strtotime($config['start_date']));
		$apikey            = Account::where('account_id', $account_id)
									->pluck('apikey')
									->toArray()[0];
		$father_id         = 615;
		$store_product_ids = AccountProduct::leftJoin('product', 'product.product_id', '=', 'account_product.product_id')
										   ->where('account_id', '=', $account_id)
										   ->where('product.father_id', '=', 615)
										   ->pluck('product.product_id')
										   ->toArray();
		$product_ids       = implode(',', $store_product_ids);
		$remark            = $config['remarks'] . '(系统自动迁移的计费配置)';
		$admin             = 'system';
		$diff_operator     = 0;
		$period            = 0;
		$mode              = 1;
		
		$count = ConfigPriceCustomer::where('apikey', $apikey)
									->where('father_id', $father_id)
									->where('start_date', $start_date)
									->count();
		if (0 != $count) {
			return;
		}
		
		$data = BillConfig::where('account_id', $config['account_id'])
						  ->whereIn('product_id', $store_product_ids)
						  ->where('start_date', $config['start_date'])
						  ->get()
						  ->toArray();
		if (2 != $config['fee_basis']) {
			$this->output->error('金盾区分子产品计费不支持计费依据');
			halt($config);
		}
		
		$accord = 2;
		
		$price = [];
		foreach ($data as $item) {
			if (1 == $config['fee_amount_rule']) {
				//固定价格
				$price[$item['product_id']] = $config['fee_price'];
				$methods                    = 2;
			} else {
				$this->output->error("金盾区分子产品计费计费配置不支持其他模式计费");
				halt($config);
			}
		}
		
		$price = json_encode($price, JSON_UNESCAPED_UNICODE);
		ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'mode', 'price', 'start_date', 'remark', 'admin'));
	}
	
	/**
	 * 转移邦信分快捷版210父产品的计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/12 18:15
	 *
	 * @param $config array
	 *
	 * @return void
	 */
	protected function transferBxfShortFatherConfig($config)
	{
		if (in_array($config['id'], [3457, 3458])) {
			//惠金所
			return;
		}
		$account_id        = $config['account_id'];
		$apikey            = Account::where('account_id', $account_id)
									->pluck('apikey')
									->toArray()[0];
		$father_id         = 210;
		$store_product_ids = AccountProduct::leftJoin('product', 'product.product_id', '=', 'account_product.product_id')
										   ->where('account_id', '=', $account_id)
										   ->where('product.father_id', '=', 210)
										   ->pluck('product.product_id')
										   ->toArray();
		$product_ids       = implode(',', $store_product_ids);
		$start_date        = date('Ymd', strtotime($config['start_date']));
		$start_date        = $start_date >= ******** ? $start_date : ********;
		$remark            = $config['remarks'] . '(系统自动迁移的计费配置)';
		$admin             = 'system';
		$accord            = 1;
		
		
		if (2 == $config['fee_price_rule']) {
			//区分运营商的
			if (1 == $config['fee_basis']) {
				//汇总计费
				$mode = 3;
				//如果是固定价格
				if (1 == $config['fee_amount_rule']) {
					$price   = json_encode([210 => $config['fee_price']], JSON_UNESCAPED_UNICODE);
					$methods = 2;
					$period  = 0;
				} else if (3 == $config['fee_amount_rule']) {
					//到达阶梯
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[1];
					}
					$price   = json_encode([210 => $price], JSON_UNESCAPED_UNICODE);
					$methods = 4;
					
				} else {
					$this->output->error("金盾计费配置不支持其他模式计费");
					halt($config);
				}
				
			} else {
				//打包计费
				$mode = 2;
				//如果是固定价格
				if (1 == $config['fee_amount_rule']) {
					$price   = json_encode([1 => $config['fee_price']], JSON_UNESCAPED_UNICODE);
					$methods = 2;
				} else if (3 == $config['fee_amount_rule']) {
					//到达阶梯
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[1];
					}
					$price   = json_encode([1 => $price], JSON_UNESCAPED_UNICODE);
					$methods = 4;
				} else {
					$this->output->error("邦信分快捷版计费配置不支持其他模式计费");
					halt($config);
				}
			}
		} else {
			halt($config);
		}
		
		
		ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'mode', 'price', 'start_date', 'remark', 'admin'));
	}
	
	/**
	 * 转移615父产品的计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/12 15:48
	 *
	 * @param $config array
	 *
	 * @return void
	 */
	protected function transferGoldenShieldFatherConfig($config)
	{
		$account_id  = $config['account_id'];
		$apikey      = Account::where('account_id', $account_id)
							  ->pluck('apikey')
							  ->toArray()[0];
		$father_id   = 615;
		$product_ids = AccountProduct::leftJoin('product', 'product.product_id', '=', 'account_product.product_id')
									 ->where('account_id', '=', $account_id)
									 ->where('product.father_id', '=', 615)
									 ->pluck('product.product_id')
									 ->toArray();
		$product_ids = implode(',', $product_ids);
		$start_date  = date('Ymd', strtotime($config['start_date']));
		$remark      = $config['remarks'] . '(系统自动迁移的计费配置)';
		$admin       = 'system';
		
		
		//小花钱包单独处理一下
		if ('4064' == $config['id']) {
			$accord        = 1;
			$methods       = 2;
			$period        = 0;
			$diff_operator = 0;
			$mode          = 2;
			$price         = json_encode([1 => $config['fee_price']], JSON_UNESCAPED_UNICODE);
			ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'mode', 'price', 'start_date', 'remark', 'admin'));
			
			return;
		}
		
		$fee_basis = $config['fee_basis'];
		
		if (3 != $fee_basis) {
			$this->output->error("金盾计费配置fee_basis不为3不支持");
			halt($config);
		}
		
		
		if (2 != $config['fee_method']) {
			$this->output->error("金盾计费配置不支持按时间计费");
			halt($config);
		}
		
		$accord        = 2;
		$period        = 0;
		$diff_operator = 0;
		$mode          = 2;
		
		//如果是固定价格
		if (1 == $config['fee_amount_rule']) {
			$price   = json_encode([1 => $config['fee_price']], JSON_UNESCAPED_UNICODE);
			$methods = 2;
		} else if (3 == $config['fee_amount_rule']) {
			//到达阶梯
			$oldPrice = json_decode($config['fee_price'], true);
			$price    = [];
			foreach ($oldPrice as $item) {
				$price[$item[0]] = $item[1];
			}
			$price   = json_encode([1 => $price], JSON_UNESCAPED_UNICODE);
			$methods = 4;
		} else {
			$this->output->error("金盾计费配置不支持其他模式计费");
			halt($config);
		}
		
		
		ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'mode', 'price', 'start_date', 'remark', 'admin'));
	}
	
	/**
	 * 转移到达阶梯的计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/12 13:34
	 *
	 * @param $config array 配置项
	 *
	 * @return void
	 */
	protected function transferDaoDa($config)
	{
		$account_id = $config['account_id'];
		$apikey     = Account::where('account_id', $account_id)
							 ->pluck('apikey')
							 ->toArray()[0];
		
		$father_id     = $product_ids = $config['product_id'];
		$accord        = $this->getAccord($config);
		$methods       = 4;
		$period        = $config['fee_step_rule'] ?: 0;
		$diff_operator = 0;
		$mode          = 0;
		$start_date    = date('Ymd', strtotime($config['start_date']));
		$remark        = $config['remarks'] . '(系统自动迁移的计费配置)';
		
		$priceOld = json_decode($config['fee_price'], true);
		$price    = [];
		foreach ($priceOld as $priceItem) {
			$price[$priceItem[0]] = $priceItem[1];
		}
		$price = json_encode([$father_id => $price], JSON_UNESCAPED_UNICODE);
		$admin = 'system';
		
		ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'mode', 'price', 'start_date', 'remark', 'admin'));
	}
	
	/**
	 * 转移固定单价的计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/12 13:58
	 *
	 * @param $config array
	 *
	 * @return void
	 */
	protected function transferGuDing($config)
	{
		$account_id = $config['account_id'];
		$apikey     = Account::where('account_id', $account_id)
							 ->pluck('apikey')
							 ->toArray()[0];
		
		$father_id     = $product_ids = $config['product_id'];
		$accord        = $this->getAccord($config);
		$methods       = 2;
		$period        = 0;
		$diff_operator = 0;
		$mode          = 0;
		$start_date    = date('Ymd', strtotime($config['start_date']));
		$remark        = $config['remarks'] . '(系统自动迁移的计费配置)';
		
		$price = json_encode([$father_id => $config['fee_price']], JSON_UNESCAPED_UNICODE);
		$admin = 'system';
		ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'mode', 'price', 'start_date', 'remark', 'admin'));
	}
	
	/**
	 * 转移累进阶梯的计费配置
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/12 13:58
	 *
	 * @param $config array
	 *
	 * @return void
	 */
	protected function transferLeiJin($config)
	{
		
		
		$father_id     = $product_ids = $config['product_id'];
		$accord        = $this->getAccord($config);
		$methods       = 2;
		$period        = $config['fee_step_rule'] ?: 0;
		$diff_operator = 0;
		$mode          = 0;
		$start_date    = date('Ymd', strtotime($config['start_date']));
		$remark        = $config['remarks'] . '(系统自动迁移的计费配置)';
		
		$priceOld = json_decode($config['fee_price'], true);
		$price    = [];
		foreach ($priceOld as $priceItem) {
			$price[$priceItem[0]] = $priceItem[2];
		}
		$price = json_encode([$father_id => $price], JSON_UNESCAPED_UNICODE);
		$admin = 'system';
		ConfigPriceCustomer::insert(compact('apikey', 'father_id', 'product_ids', 'accord', 'methods', 'period', 'diff_operator', 'mode', 'price', 'start_date', 'remark', 'admin'));
	}
	
	
	/**
	 * 获取计费依据
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/10/12 13:39
	 *
	 * @param $config array 配置
	 *
	 * @return integer
	 */
	protected function getAccord($config)
	{
		$product_id = $config['product_id'];
		if (501 == $product_id) {
			return 1;
		} else if (101 == $product_id) {
			return 1;
		} else if (601 == $product_id) {
			return 1;
		} else if (604 == $product_id) {
			return 2;
		} else if (612 == $product_id) {
			return 1;
		} else if (401 == $product_id) {
			return 2;
		} else if (104 == $product_id) {
			return 1 == $config['fee_basis'] ? 1 : 2;
		} else if (664 == $product_id) {
			return 1;
		} else if (614 == $product_id) {
			return 1;
		} else if (616 == $product_id) {
			return 1;
		} else if ($this->is210($config)) {
			return 1;
		} else if ($this->is615($config)) {
			if (1 == $config['fee_basis']) {
				return 1;
			} else {
				return 2;
			}
		} else if ($this->is200($config)) {
			if (1 == $config['fee_basis']) {
				return 2;
			} else {
				return 1;
			}
		}
		
		
		halt("未确认计费依据", $config);
	}
	
	
	/**
	 * 获取计费周期
	 */
	protected function getPeriod($config)
	{
		$methods = $this->getMethods($config);
		if (in_array($methods, [0, 1, 2])) {
			return 0;
		}
		
		return $config['fee_step_rule'];
	}
	
	/**
	 * 二级产品计费模式
	 */
	protected function getMode($config)
	{
		if (!$this->is200($config) && !$this->is210($config) && !$this->is615($config)) {
			return 0;
		}
		
		//邦信分快捷版
		if ($this->isFatherProductId210($config)) {
			$fee_basis = $config['fee_basis'];
			if (1 == $fee_basis) {
				//汇总子产品计费
				return 3;
			} else {
				//打包计费
				return 2;
			}
		}
		if ($this->isSubProductId210($config)) {
			return 1;
		}
		
		
		//金盾
		if ($this->isFatherProductId615($config)) {
			//打包
			return 2;
		}
		if ($this->isSubProductId615($config)) {
			return 1;
		}
		
		//邦秒验
		if ($this->is200($config)) {
			return 1;
		}
		
		return false;
	}
	
	/**
	 * 获取计费方式
	 */
	protected function getMethods($config)
	{
		$fee_method      = $config['fee_method'];
		$fee_amount_rule = $config['fee_amount_rule'];
		$fee_time_rule   = $config['fee_time_rule'];
		
		if (2 == $fee_method && 1 == $fee_amount_rule) {
			//固定价格
			return 2;
		} else if (2 == $fee_method && 2 == $fee_amount_rule) {
			//累进阶梯
			return 3;
		} else if (2 == $fee_method && 3 == $fee_amount_rule) {
			//到达阶梯
			return 4;
		} else if (1 == $fee_method && 3 == $fee_time_rule) {
			//包年
			return 1;
		}
		$this->output->error("未知的计费配置");
		halt($config);
		
		return false;
	}
	
	/**
	 * 获取计费开始时间
	 */
	protected function getStartDate($config)
	{
		return date('Ymd', strtotime($config['start_date']));
	}
	
	/**
	 * 获取备注信息
	 */
	protected function getRemark($config)
	{
		return $config['remarks'] . '(系统自动迁移的计费配置)';
	}
	
	/**
	 * 是否区分运营商
	 */
	protected function getDiffOperator($config)
	{
		if (!$this->is200($config) && !$this->is210($config)) {
			return 0;
		}
		
		return $config['fee_price_rule'] == 2 ? 1 : 0;
	}
	
	
	/**
	 * 获取价格
	 */
	protected function getPrice($config)
	{
		if (!$this->is615($config) && !$this->is210($config) && !$this->is200($config)) {
			return $this->getPriceByNormarl($config);
		}
		
		//记录在了父产品身上
		if ($this->isFatherProductId210($config)) {
			$diffOperator = $this->getDiffOperator($config);
			if (1 == $diffOperator) {
				return $this->getPriceByDiffOperatorIsFather210($config);
			} else {
				return $this->getPriceByNotDiffOperatorIsFather210($config);
			}
		}
		
		//邦信分快捷版记录在子产品身上了
		if ($this->isSubProductId210($config)) {
			$diffOperator = $this->getDiffOperator($config);
			if (1 == $diffOperator) {
				return $this->getPriceByDiffOperatorSub210($config);
			} else {
				return $this->getPriceByNotDiffOperatorSub210($config);
			}
		}
		
		//金盾-父产品
		if ($this->isFatherProductId615($config)) {
			return $this->getPriceByFather615($config);
		}
		
		if ($this->isSubProductId615($config)) {
			return $this->getPriceBySub615($config);
		}
		
		//邦秒验
		if ($this->is200($config)) {
			//$this->customer_id[] = $config
			$diffOperator = $this->getDiffOperator($config);
			if (1 == $diffOperator) {
				return $this->getPriceByDiffOperator200($config);
			} else {
				return $this->getPriceByNotDiffOperator200($config);
			}
		}
		
		return false;
	}
	
	protected function getPriceByDiffOperator200($config)
	{
		//获取其他的子产品计费配置
		$product_ids = $this->getProductIds($config);
		$feeConfigs  = BillConfig::whereIn('product_id', explode(',', $product_ids))
								 ->where('account_id', $config['account_id'])
								 ->where('start_date', $config['start_date'])
								 ->get()
								 ->toArray();
		
		$methods       = $this->getMethods($config);
		$diff_operator = $this->getDiffOperator($config);
		$start_date    = $this->getStartDate($config);
		
		//确认每个计费配置的模式是相同的
		foreach ($feeConfigs as $feeConfig) {
			if ($methods !== $this->getMethods($feeConfig) || $diff_operator != $this->getDiffOperator($feeConfig)) {
				//$this->output->error("邦秒验子产品计费模式不同 : " . $config['customer']);
				$this->handleTransferCustomerIds[] = $feeConfig['customer_id'];
				
				return false;
			}
		}
		
		switch ($methods) {
			case 1:
				//包年
				$this->output->error("邦秒验不支持包年");
				halt($config);
				break;
			case 2:
				//固定价格
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$oldPrice                        = json_decode($feeConfig['fee_price'], true);
					$price[$feeConfig['product_id']] = [
						'CMCC' => array_get($oldPrice, 0, 0),
						'CUCC' => array_get($oldPrice, 1, 0),
						'CTCC' => array_get($oldPrice, 2, 0),
					];
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 3:
				//累进阶梯
				$this->output->error("邦秒验不支持区分运营商 累进阶梯");
				halt($config);
				
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$newPriceItem = [];
					$oldPrice     = json_decode($feeConfig['fee_price'], true);
					foreach ($oldPrice as $priceItem) {
						$newPriceItem[$priceItem[0]] = $priceItem[2];
					}
					$price[$feeConfig['product_id']] = $newPriceItem;
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 4:
				//到达阶梯
				$this->output->error("邦秒验不支持区分运营商 到达阶梯");
				halt($config);
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$newPriceItem = [];
					$oldPrice     = json_decode($feeConfig['fee_price'], true);
					foreach ($oldPrice as $priceItem) {
						$newPriceItem[$priceItem[0]] = $priceItem[1];
					}
					$price[$feeConfig['product_id']] = $newPriceItem;
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
		}
	}
	
	/**
	 * 邦秒验 - 不区分运营商
	 */
	protected function getPriceByNotDiffOperator200($config)
	{
		//获取其他的子产品计费配置
		$product_ids = $this->getProductIds($config);
		$feeConfigs  = BillConfig::whereIn('product_id', explode(',', $product_ids))
								 ->where('account_id', $config['account_id'])
								 ->where('start_date', $config['start_date'])
								 ->get()
								 ->toArray();
		
		$methods       = $this->getMethods($config);
		$diff_operator = $this->getDiffOperator($config);
		$start_date    = $this->getStartDate($config);
		
		//确认每个计费配置的模式是相同的
		foreach ($feeConfigs as $feeConfig) {
			if ($methods !== $this->getMethods($feeConfig) || $diff_operator != $this->getDiffOperator($feeConfig)) {
				//$this->output->error("邦秒验子产品计费模式不同 : " . $config['customer']);
				$this->handleTransferCustomerIds[] = $feeConfig['customer_id'];
				
				return false;
			}
		}
		
		switch ($methods) {
			case 1:
				//包年
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$price[$feeConfig['product_id']] = $feeConfig['fee_price'];
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 2:
				//固定价格
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$price[$feeConfig['product_id']] = $feeConfig['fee_price'];
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 3:
				//累进阶梯
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$newPriceItem = [];
					$oldPrice     = json_decode($feeConfig['fee_price'], true);
					foreach ($oldPrice as $priceItem) {
						$newPriceItem[$priceItem[0]] = $priceItem[2];
					}
					$price[$feeConfig['product_id']] = $newPriceItem;
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 4:
				//到达阶梯
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$newPriceItem = [];
					$oldPrice     = json_decode($feeConfig['fee_price'], true);
					foreach ($oldPrice as $priceItem) {
						$newPriceItem[$priceItem[0]] = $priceItem[1];
					}
					$price[$feeConfig['product_id']] = $newPriceItem;
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
		}
	}
	
	/**
	 * 金盾-父产品计费
	 */
	protected $id = null;
	
	protected function getPriceByFather615($config)
	{
		$methods = $this->getMethods($config);
		$price   = $config['fee_price'];
		
		switch ($methods) {
			case 1:
				//包年
				return json_encode([1 => $price]);
				break;
			case 2:
				//固定价格
				return json_encode([1 => $price]);
				break;
			case 3:
				//累进阶梯
				$oldPrice = json_decode($price, true);
				$price    = [];
				foreach ($oldPrice as $priceItem) {
					$price[$priceItem[0]] = $priceItem[2];
				}
				
				return json_encode([1 => $price], JSON_UNESCAPED_UNICODE);
				break;
			case 4:
				//到达阶梯
				$oldPrice = json_decode($price, true);
				$price    = [];
				foreach ($oldPrice as $priceItem) {
					$price[$priceItem[0]] = $priceItem[1];
				}
				
				return json_encode([1 => $price], JSON_UNESCAPED_UNICODE);
				break;
		}
		$this->output->error("金盾-打包计费（未识别计费模式）");
		halt($config);
	}
	
	/**
	 * 金盾-子产品计费
	 */
	protected function getPriceBySub615($config)
	{
		//获取其他的子产品计费配置
		$product_ids = $this->getProductIds($config);
		$feeConfigs  = BillConfig::whereIn('product_id', explode(',', $product_ids))
								 ->where('account_id', $config['account_id'])
								 ->where('start_date', $config['start_date'])
								 ->get()
								 ->toArray();
		
		$methods = $this->getMethods($config);
		switch ($methods) {
			case 1:
				//包年
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$price[$feeConfig['product_id']] = $feeConfig['fee_price'];
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 2:
				//固定价格
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$price[$feeConfig['product_id']] = $feeConfig['fee_price'];
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 3:
				//累进阶梯
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$newPriceItem = [];
					$oldPrice     = json_decode($feeConfig['fee_price'], true);
					foreach ($oldPrice as $priceItem) {
						$newPriceItem[$priceItem[0]] = $priceItem[2];
					}
					$price[$feeConfig['product_id']] = $newPriceItem;
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 4:
				//到达阶梯
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$newPriceItem = [];
					$oldPrice     = json_decode($feeConfig['fee_price'], true);
					foreach ($oldPrice as $priceItem) {
						$newPriceItem[$priceItem[0]] = $priceItem[1];
					}
					$price[$feeConfig['product_id']] = $newPriceItem;
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
		}
		
		
		halt($feeConfigs);
	}
	
	/**
	 * 邦信分快捷版-区分运营商的价格整理
	 */
	protected function getPriceByDiffOperatorIsFather210($config)
	{
		$oldPrice   = json_decode($config['fee_price'], true);
		$methods    = $this->getMethods($config);
		$mode       = $this->getMode($config);
		$product_id = 210;
		//打包计费
		if (2 == $mode) {
			switch ($methods) {
				case 1:
					//包年
					$price            = [];
					$price['ALLCMCC'] = array_get($oldPrice, 3, 0);
					$price['PROCMCC'] = array_get($oldPrice, 0, 0);
					$price['CUCC']    = array_get($oldPrice, 1, 0);
					$price['CTCC']    = array_get($oldPrice, 2, 0);
					
					return json_encode([1 => $price]);
					break;
				case 2:
					//固定价格
					$price            = [];
					$price['ALLCMCC'] = array_get($oldPrice, 3, 0);
					$price['PROCMCC'] = array_get($oldPrice, 0, 0);
					$price['CUCC']    = array_get($oldPrice, 1, 0);
					$price['CTCC']    = array_get($oldPrice, 2, 0);
					
					return json_encode([1 => $price]);
					break;
				case 3:
					//累进阶梯
					$this->output->error("邦信分快捷版-打包计费-累进阶梯（未定义转移逻辑）");
					halt($config);
					break;
				case 4:
					//到达阶梯
					$this->output->error("邦信分快捷版-打包计费-到达阶梯（未定义转移逻辑）");
					halt($config);
					//					$oldPrice = json_decode($config['fee_price'], true);
					//					$price    = [];
					//					foreach ($oldPrice as $item) {
					//						$price[$item[0]] = $item[1];
					//					}
					//
					//					return json_encode([1 => $price], JSON_UNESCAPED_UNICODE);
					break;
			}
		} else {
			//汇总子产品计费
			switch ($methods) {
				case 1:
					//包年
					$this->output->error("邦信分快捷版-汇总子产品计费-包年（未定义转移逻辑）");
					halt($config);
					break;
				case 2:
					//固定价格
					$price            = [];
					$price['ALLCMCC'] = array_get($oldPrice, 3, 0);
					$price['PROCMCC'] = array_get($oldPrice, 0, 0);
					$price['CUCC']    = array_get($oldPrice, 1, 0);
					$price['CTCC']    = array_get($oldPrice, 2, 0);
					
					return json_encode([210 => $price]);
					break;
				case 3:
					//累进阶梯
					$this->output->error("邦信分快捷版-汇总子产品计费-累进阶梯（未定义转移逻辑）");
					halt($config);
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[2];
					}
					
					return json_encode([$product_id => $price], JSON_UNESCAPED_UNICODE);
					break;
				case 4:
					//到达阶梯
					$this->output->error("邦信分快捷版-汇总子产品计费-到达阶梯（未定义转移逻辑）");
					halt($config);
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[1];
					}
					
					return json_encode([$product_id => $price], JSON_UNESCAPED_UNICODE);
					break;
			}
		}
		$this->output->error("邦信分快捷版-父产品计费配置(不区分运营商)导入失败，未识别计费模式");
		halt($config);
	}
	
	/**
	 * 邦信分快捷版-区分运营商的价格整理
	 */
	protected function getPriceByNotDiffOperatorIsFather210($config)
	{
		$methods = $this->getMethods($config);
		//如果是打包计费
		$mode       = $this->getMode($config);
		$product_id = 210;
		if (2 == $mode) {
			switch ($methods) {
				case 1:
					//包年
					return json_encode([1 => $config['fee_price']]);
					break;
				case 2:
					//固定价格
					
					return json_encode([1 => $config['fee_price']]);
					break;
				case 3:
					//累进阶梯
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[2];
					}
					
					return json_encode([1 => $price], JSON_UNESCAPED_UNICODE);
					break;
				case 4:
					//到达阶梯
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[1];
					}
					
					return json_encode([1 => $price], JSON_UNESCAPED_UNICODE);
					break;
			}
		} else {
			//汇总子产品计费
			switch ($methods) {
				case 1:
					//包年
					
					return json_encode([$product_id => $config['fee_price']]);
					break;
				case 2:
					//固定价格
					
					return json_encode([$product_id => $config['fee_price']]);
					break;
				case 3:
					//累进阶梯
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[2];
					}
					
					return json_encode([$product_id => $price], JSON_UNESCAPED_UNICODE);
					break;
				case 4:
					//到达阶梯
					$oldPrice = json_decode($config['fee_price'], true);
					$price    = [];
					foreach ($oldPrice as $item) {
						$price[$item[0]] = $item[1];
					}
					
					return json_encode([$product_id => $price], JSON_UNESCAPED_UNICODE);
					break;
			}
		}
		$this->output->error("邦信分快捷版-父产品计费配置(不区分运营商)导入失败");
		halt($config);
	}
	
	/**
	 * 邦信分快捷版-不区分运营商-独立子产品计费
	 */
	
	protected function getPriceByNotDiffOperatorSub210($config)
	{
		
		//获取其他的子产品计费配置
		$product_ids = $this->getProductIds($config);
		$feeConfigs  = BillConfig::whereIn('product_id', explode(',', $product_ids))
								 ->where('account_id', $config['account_id'])
								 ->where('start_date', $config['start_date'])
								 ->get()
								 ->toArray();
		
		$methods = $this->getMethods($config);
		//独立子产品计费
		switch ($methods) {
			case 1:
				//包年
				$this->output->error("邦信分快捷版-独立子产品计费-包年-不区分运营商（未定义转移逻辑）");
				halt($config);
				break;
			case 2:
				//固定价格
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$price[$feeConfig['product_id']] = $feeConfig['fee_price'];
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 3:
				//累进阶梯
				$this->output->error("邦信分快捷版-独立子产品计费-累进阶梯-不区分运营商（未定义转移逻辑）");
				halt($config);
				break;
			case 4:
				//到达阶梯
				$this->output->error("邦信分快捷版-独立子产品计费-到达阶梯-不区分运营商（未定义转移逻辑）");
				halt($config);
				break;
		}
	}
	
	/**
	 * 邦信分快捷版-不区分运营商-独立子产品计费
	 */
	protected function getPriceByDiffOperatorSub210($config)
	{
		//获取其他的子产品计费配置
		$product_ids = $this->getProductIds($config);
		$feeConfigs  = BillConfig::whereIn('product_id', explode(',', $product_ids))
								 ->where('account_id', $config['account_id'])
								 ->where('start_date', $config['start_date'])
								 ->get()
								 ->toArray();
		
		$methods = $this->getMethods($config);
		//独立子产品计费
		switch ($methods) {
			case 1:
				//包年
				$this->output->error("邦信分快捷版-独立子产品计费-包年-区分运营商（未定义转移逻辑）");
				halt($config);
				break;
			case 2:
				//固定价格
				$price = [];
				foreach ($feeConfigs as $feeConfig) {
					$priceItem            = [];
					$oldPrice             = json_decode($feeConfig['fee_price'], true);
					$priceItem['ALLCMCC'] = array_get($oldPrice, 3, 0);
					$priceItem['PROCMCC'] = array_get($oldPrice, 0, 0);
					$priceItem['CUCC']    = array_get($oldPrice, 1, 0);
					$priceItem['CTCC']    = array_get($oldPrice, 2, 0);
					
					$price[$feeConfig['product_id']] = $priceItem;
				}
				
				return json_encode($price, JSON_UNESCAPED_UNICODE);
				break;
			case 3:
				//累进阶梯
				$this->output->error("邦信分快捷版-独立子产品计费-累进阶梯-区分运营商（未定义转移逻辑）");
				halt($config);
				break;
			case 4:
				//到达阶梯
				$this->output->error("邦信分快捷版-独立子产品计费-到达阶梯-区分运营商（未定义转移逻辑）");
				halt($config);
				break;
		}
	}
	
	/**
	 * 普通产品价格处理
	 */
	protected function getPriceByNormarl($config)
	{
		$methods    = $this->getMethods($config);
		$product_id = $config['product_id'];
		switch ($methods) {
			case 1:
				//包年
				
				return json_encode([$product_id => $config['fee_price']]);
				break;
			case 2:
				//固定价格
				
				return json_encode([$product_id => $config['fee_price']]);
				break;
			case 3:
				//累进阶梯
				$oldPrice = json_decode($config['fee_price'], true);
				$price    = [];
				foreach ($oldPrice as $item) {
					$price[$item[0]] = $item[2];
				}
				
				return json_encode([$product_id => $price], JSON_UNESCAPED_UNICODE);
				break;
			case 4:
				//到达阶梯
				$oldPrice = json_decode($config['fee_price'], true);
				$price    = [];
				foreach ($oldPrice as $item) {
					$price[$item[0]] = $item[1];
				}
				
				return json_encode([$product_id => $price], JSON_UNESCAPED_UNICODE);
				break;
		}
	}
	
	/**
	 * 获取新计费配置中的父产品ID字段
	 */
	protected function getFatherId($config)
	{
		$father_id = $this->getFatherIdNotField($config);
		
		return 0 == $father_id ? $config['product_id'] : $father_id;
	}
	
	
	/**
	 * 获取子级IDS
	 */
	protected function getProductIds($config)
	{
		$father_id = $this->getFatherId($config);
		if (!$this->is210($config) && !$this->is615($config) && !$this->is200($config)) {
			return $father_id;
		}
		
		$account_id = $config['account_id'];
		
		$product_ids = AccountProduct::leftJoin('product', 'product.product_id', '=', 'account_product.product_id')
									 ->where('account_id', '=', $account_id)
									 ->where('product.father_id', '=', $father_id)
									 ->pluck('product.product_id')
									 ->toArray();
		
		return implode(',', $product_ids);
	}
	
	/**
	 * 获取apikey
	 */
	protected function getApikey($config)
	{
		$account_id = $config['account_id'];
		
		return Account::where('account_id', $account_id)
					  ->pluck('apikey')
					  ->toArray()[0];
	}
	
	/**
	 * 是否为邦信分快捷版 父产品计费
	 */
	protected function isFatherProductId210($config)
	{
		$product_id = $config['product_id'];
		
		return 210 == $product_id;
	}
	
	/**
	 * 是否为邦信分快捷版 子产品计费
	 */
	protected function isSubProductId210($config)
	{
		$father_id = $this->getFatherIdNotField($config);
		
		return 210 == $father_id;
	}
	
	/**
	 * 是否为金盾 父产品计费
	 */
	protected function isFatherProductId615($config)
	{
		$product_id = $config['product_id'];
		
		return 615 == $product_id;
	}
	
	/**
	 * 是否为金盾 子产品计费
	 */
	protected function isSubProductId615($config)
	{
		$father_id = $this->getFatherIdNotField($config);
		
		return 615 == $father_id;
	}
	
	/**
	 * 是否为邦秒验
	 */
	protected function is200($config)
	{
		$father_id = $this->getFatherIdNotField($config);
		
		return 200 == $father_id;
	}
	
	/**
	 * 是否为邦信分快捷版
	 */
	protected function is210($config)
	{
		return $this->isFatherProductId210($config) || $this->isSubProductId210($config);
	}
	
	/**
	 * 是否为金盾
	 */
	protected function is615($config)
	{
		return $this->isFatherProductId615($config) || $this->isSubProductId615($config);
	}
	
	/**
	 * 获取父级ID
	 */
	protected function getFatherIdNotField($config)
	{
		$product_id = $config['product_id'];
		
		return Product::where('product_id', '=', $product_id)
					  ->pluck('father_id')
					  ->toArray()[0];
	}
}