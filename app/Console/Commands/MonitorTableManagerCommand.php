<?php

namespace App\Console\Commands;

use App\Models\MonitorLogChannelModel;
use App\Models\MonitorLogProductModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * Class ProductLogTableManagerCommand 产品日志表管理，每日生成、删除固定的表数据
 * @package App\Console\Commands
 */
class MonitorTableManagerCommand extends Command
{
	protected $signature   = "monitor:table_manager";
	protected $description = '监控系统表管理';
	
	public function handle()
	{
		//删除数据
		$this->delete();
	}
	
	/**
	 * 删除15天之前的数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2020/12/17 17:52
	 *
	 * @return void
	 */
	private function delete()
	{
		$time = strtotime(date('Y-m-d', strtotime('-15 days')));
		MonitorLogProductModel::where('time', '<', $time)
							  ->delete();
		MonitorLogChannelModel::where('time', '<', $time)
							  ->delete();
	}
}