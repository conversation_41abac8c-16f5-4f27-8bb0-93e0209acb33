<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Providers\BillIncome\CreateBillIncome;
use Illuminate\Console\Command;

/**
 * Class CreateBillCustomerIncomeCommand
 * @package App\Console\Commands
 * 创建营收账单脚本
 */
class BillCreateIncome extends Command
{
	/** @var string 命令 */
	public $signature = 'bill:create_bill_income
    {--date= : 账单日，默认昨日（格式Ymd）}
    {--customer_id= : 客户ID（多个客户ID以,隔开）}
    {--product_id= : 产品ID（父产品ID，多个产品以,隔开）}
    {--days=1 : 生成多少日的账单}
    ';
	
	protected $description = '创建营收账单脚本';
	
	public function handle()
	{
		//获取参数
		$params = $this->getParams();
		
		$createBillIncome = new CreateBillIncome(array_get($params, 'date'), array_get($params, 'days'), array_get($params, 'apikeys'), array_get($params, 'product_id'));
		$createBillIncome->run();
		
		$this->output->success("账单任务已成功入队，请稍后确认账单是否成功执行");
	}
	
	/**
	 * 获取命令参数
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2020/9/18 15:53
	 *
	 * @return array
	 */
	protected function getParams()
	{
		$params         = [];
		$params['date'] = $this->input->getOption('date') ?: date('Ymd');
		
		if (!preg_match('/^\d{8}$/', $params['date'])) {
			$this->output->error('日期格式不正确');
			die;
		}
		
		$customer_id = $this->input->getOption('customer_id');
		if (!empty($customer_id)) {
			$customer_id       = explode(',', $customer_id);
			$params['apikeys'] = Account::whereIn('customer_id', $customer_id)
										->pluck('apikey')
										->toArray();
			$params['apikeys'] = array_filter($params['apikeys']);
		}
		
		$product_id = $this->input->getOption('product_id');
		if (!empty($product_id)) {
			$params['product_id'] = explode(',', $product_id);
		}
		
		$params['days'] = $this->input->getOption('days') ?: 1;
		if (!is_numeric($params['days'])) {
			$this->output->error('天数格式不正确');
			die;
		}
		
		return $params;
	}
	
	
}