<?php

namespace App\Console\Commands;

use App\Models\BxfPeriod;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Mail\BxfPeriodEmail;
use App\Models\EmailConfig;
class BxfPeriodCommand extends Command
{
    /**
     * 命令行执行命令
     */
    protected $signature = 'bxf:period-email';
    protected  $scene = 'bxf_period_email';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $email_model = new EmailConfig();
        $email_config = $email_model->getRecipientsAndCCByScene($this->scene);
        $to_email = array_column($email_config['recipients'], 'address');
        $cc_email = array_column($email_config['cc'], 'address');

        $sql = <<<EOF
select b.* from ((select channel, max(created_at) created_at from bxf_period group by channel) a join (select * from bxf_period) b on a.channel=b.channel and a.created_at=b.created_at);
EOF;
        $res = DB::select($sql);
        if(empty($res)){
            return false;
        }
        $arr_info = [];
        $arr_id = [];
        $time = time();
        array_walk($res, function($val) use (&$arr_info, &$arr_id, $time){
            $t = abs(strtotime($val['period']) - $time);
            if($t > 259200){
                $arr_info[] = $val;
                $arr_id[] = $val['id'];
            }
        });
        if(!empty($arr_info)){
            //进行数据更新
            BxfPeriod::whereIn('id', $arr_id)->update(['daytime'=>1]);
            
            //$to_email = ['<EMAIL>', '<EMAIL>'];
            //$cc_email = ['<EMAIL>'];
            Mail::to($to_email)
                ->cc($cc_email)
                ->send(new BxfPeriodEmail($arr_info));
        }
    }
}