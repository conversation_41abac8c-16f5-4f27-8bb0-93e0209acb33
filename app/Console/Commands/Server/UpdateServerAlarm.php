<?php

namespace App\Console\Commands\Server;

use App\Models\Crs\SystemUser;
use App\Models\Customer;
use App\Models\EmailConfig;
use App\Models\Server;
use App\Providers\Tool\SendMailService;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\ExcelTrait;
use App\Utils\AliCall;
use Illuminate\Console\Command;

class UpdateServerAlarm extends Command
{
    //use  WechatExceptionTrait;
    use CurlTrait;
    use ExcelTrait;
    protected $signature = 'update_server {email?}';

    protected $description = '更新服务器未更新至数据库报警';

    protected  $email;

    protected $sheet = [
         0=>'办公室',
         1=>'北京',
         2=>'深圳',
         3=>'其他',
    ];

    public function  __construct()
    {
        parent::__construct();
        $this->file_out_init();
    }


    public function handle()
    {
        $this->email = $this->argument('email');
        $Emails = EmailConfig::getEamilAddressByScene('server_to_be_confirmed');
        $data = $this->readExcel('/root/Yulore_Finance_CP_vm硬件使用情况一览表-3.xlsx','xlsx');

       $result_data = $this->ToDealWithData($data);

       if (empty($result_data)){
           return false;
       }

       $html = $this->createHtml($result_data);
       $mail = new SendMailService();

       foreach ($Emails as $v){
           if (!is_null($this->email)){
               if ($v['email'] != $this->email){
                   continue;
               }
           }
           //日报 每日发送
           $mail->setFromName('金融后台项目组')
               ->setAddressee([$v])
               //->setCC($this->cc)
               ->setSubject('新增服务器确认')
               ->setContent($html)
               ->sendByAsync();
       }


    }

    private function createHtml($result_data)
    {
        $title = "新增服务器未确认（负责人-用途）";
        $content = '';

        $content .= '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
            font-size   : 12px;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<div>
<h1>'; $title; $content .= '</h1>';

        $html =   $this->createSpecificHtml($result_data);

        $content .= $html;

        $content .= '<br />
</div>';

        return $content;
    }


    /*
     * 生成邮件发送内容中的表格
    */
    private function createSpecificHtml($result_data)
    {
        $html = '';

        $html .= <<<HTML
      
        <h2> 服务器待确认数据</h2>
        <table border="1" cellspacing="0" cellpadding="0">
        <tr>
        <th  style="width:100px;  text-align:center" align="center">名称</th>
        <th style="width:50px;  text-align:center" align="center">ip地址</th>
        <th style="width:100px;  text-align:center" align="center">所属机房</th>
        </tr>

HTML;
        //动态加载成功列

        $today = date('Y-m-d',time());

        foreach($result_data as $v){
            $html .= '<tr><td align="center">'.$v['server_name'].'</td>';
            $html .= '<td align="center">'.$v['server_ip'].'</td>';
            $html .= '<td align="center">'.$v['server_address'].'</td>';
        }
        $html .= '</table>';


        return $html;
    }


    private function ToDealWithData($data)
    {
        $result_data = [];
        $database_ips = array_column(Server::getServerIps(),'ip');
        foreach ($data as $key=>$value){
            //机房
            $computer_room = $this->sheet[$key];
           // $keyip_servers = array_column($v,null,1);
            foreach ($value as $k =>$v){
                if (in_array($v[1],$database_ips)){
                    continue;
                }
                $result_data[] = ['server_ip'=>$v[1],'server_name'=>$v[0],'server_address'=>$computer_room];
            }
        }
        return $result_data;
    }

    private function readExcel($filename,$extension)
    {
        $objReader =\PHPExcel_IOFactory::createReader("Excel5");
        $objReader->setReadDataOnly(true);

        if ($extension =='xlsx') {
            $objReader = new \PHPExcel_Reader_Excel2007();
            $objPHPExcel = $objReader ->load($filename);
        } else if ($extension =='xls') {
            $objReader = new \PHPExcel_Reader_Excel5();
            $objPHPExcel = $objReader ->load($filename);
        } else if ($extension=='csv') {
            $PHPReader = new \PHPExcel_Reader_CSV();
            //默认输入字符集
            $PHPReader->setInputEncoding('GBK');
            //默认的分隔符
            $PHPReader->setDelimiter(',');
            //载入文件
            $objPHPExcel = $PHPReader->load($filename);
        }

        //$objWorksheet= $objPHPExcel->getActiveSheet();

        $excelData =array();

        $sheetCount = $objPHPExcel->getSheetCount();

        for ( $i = 0; $i < $sheetCount; $i++){

            $objWorksheet = $objPHPExcel->getSheet($i);

            $highestRow =$objWorksheet->getHighestRow();

            //echo$highestRow;die;
            $highestColumn = $objWorksheet->getHighestColumn();

            //echo$highestColumn;die;
            $highestColumnIndex =\PHPExcel_Cell::columnIndexFromString($highestColumn);

            for($row = 2;$row <= $highestRow; $row++) {
                for ($col = 0; $col < $highestColumnIndex; $col++) {
                    $excelData[$i][$row][] = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
                }
            }
        }

        return $excelData;
    }


}