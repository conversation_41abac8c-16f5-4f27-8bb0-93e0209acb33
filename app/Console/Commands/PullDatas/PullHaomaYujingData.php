<?php

/**
 * 企服产品拉取
 */
namespace App\Console\Commands\PullDatas;


use App\Models\BillCustomerIncome;
use App\Models\BillCustomerIncomeV2;
use App\Models\BillProductIncome;
use App\Models\BillProductIncomeV2;
use App\Models\Product;
use App\Models\StatisticsCustomerUsage;
use Illuminate\Console\Command;
use App\Models\AccountProduct;
use App\Models\Account;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PullHaomaYujingData extends Command
{
    use CurlTrait;

    protected $signature = 'pull_data:enterprise_service_products
    {--start_date= : 开始日期，格式：Ymd}
    {--end_date= : 结束日期，格式:Ymd}
    {--product_ids= : 企服产品IDs（多个产品ID用 , 隔开）}
    {--accountId= : 更新的用户account_id}
    {--lastMonthData= : 是否重新更新上个月所有用户数据}
    ';

    //企服产品ID
    protected static $productIds = [664, 3101, 3102, 3105, 3106, 3107, 3108];

    //企服产品ID 对应 产品类型
    protected static $typeMap = [
        664  => 1,   //外呼风险验证 2.0
        3101 => 2,   //短信风险验证
        3102 => 3,   //疑似失效号码检测
        3105 => 4,   //全网标记监测
        3106 => 5,   //闪信服务
        3107 => 6,   //智能语音质检
        3108 => 1,   //金盾
    ];

    protected $description = '企服产品数据拉取';

    protected $secret_key = 'dOzpBw4Z2lh2P6CmCmELnLovbvzkPest';
    protected $url = 'http://apis.donotcall.com.cn/v1/risktel/risktel_day_pro.php';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        //接收变量
        $pre_day = date('Ymd',strtotime('-1 day'));
        $start_date = !empty($this->option('start_date')) ? $this->option('start_date') : $pre_day;
        $end_date = !empty($this->option('end_date')) ? $this->option('end_date') : $pre_day;
        if($start_date > $end_date){
            $this->output->error('开始时间不能大于结束时间');
            exit;
        }
        if(!empty($this->option('start_date')) && $this->option('start_date') < '********'){
            $this->output->error('开始时间必须大于********');
            exit;
        }
        $accountId = $this->option('accountId');
        $accountId = empty($accountId) ? '' : $accountId;
        $productIds = $this->option('product_ids');
        $productIds = $productIds ? explode(',', $productIds) : self::$productIds;
        $productIds = array_intersect($productIds, self::$productIds);
        if (!$productIds) {
            $this->output->error('产品ID非法');
            exit;
        }

        //更新收入时间
        $upStartTime = date('Ymd', strtotime($start_date) - 86400);
        $upEndTime = date('Ymd', strtotime($end_date) - 86400);

        //整体更新上个月数据
        $updateLastMonthFlage = empty($this->option('lastMonthData')) ? false : true;
        if ($updateLastMonthFlage) {
            $currentMonth = date('Ym01');
            $start_date = date('Ym01', strtotime($currentMonth) - 86400);
            $end_date = date('Ymd', strtotime($currentMonth) - 86400);;
            $upStartTime = $start_date;
            $upEndTime = $end_date;
        }

        foreach ($productIds as $productId) {
            //拉取昨天的调用量
            $this->addUsageDatas($start_date, $end_date, $productId, $accountId);
            //更新前天的费用
            $this->updateIncomeDatas($upStartTime, $upEndTime, $productId, $accountId);
            echo $productId . '_SUCCESS' . PHP_EOL;
        }
    }

    /**
     * 增加统计用量数据
     *
     * @param $start_date
     * @param $end_date
     * @return false
     */
    public function addUsageDatas($start_date, $end_date, $productId, $accountId)
    {
        $accountProModel = new AccountProduct();
        $accountProWhere = [
            ['product_id', $productId]
        ];
        if ($accountId) {
            $accountProWhere[] = ['account_id', $accountId];
        }
        $accountProInfos = $accountProModel->where($accountProWhere)->get();
        $accountProInfos = count($accountProInfos) ? $accountProInfos->toArray() : [];
        if (!$accountProInfos) {
            $this->output->error('暂无客户信息');
            return;
        }

        $hmyjApikeyMap = [];
        foreach ($accountProInfos as $item) {
            if (empty($item['data'])) {
                continue;
            }
            $tmp = json_decode($item['data'], true);
            if (empty($tmp['apikey_map'])) {
                continue;
            }
            $hmyjApikeyMap[$tmp['apikey_map']] = $item['account_id'];
        }

        if (!$hmyjApikeyMap) {
            $this->output->error('暂无可用信息1');
            return;
        }

        $accountIds = array_column($accountProInfos, 'account_id');

        $accountModel = new Account();
        $apikeyWhere = [
            ['is_delete', 0],
            ['apikey', '!=', ''],
        ];
        $apikeyMap = $accountModel->select(['account_id', 'apikey'])
            ->whereIn('account_id', $accountIds)
            ->where($apikeyWhere)
            ->get();
        $apikeyMap = count($apikeyMap) ? $apikeyMap->toArray() : [];
        if (!$apikeyMap) {
            $this->output->error('暂无apikey信息');
            return;
        }

        $apikeyMap = array_column($apikeyMap, 'apikey', 'account_id');
        $apikey_str = implode(',', array_keys($hmyjApikeyMap));

        $params = [
            'apikey' => $apikey_str,
            'type' => self::$typeMap[$productId],
            'start_date' => $start_date,
            'end_date' => $end_date
        ];
        $sign = $this->createSign($params, $this->secret_key);
        $params['sign'] = $sign;
        $res_data = $this->post($this->url, $params);
        Log::info('PULL_DATA_HAOMAYUJING_DATAS_USAGENUM：' . json_encode([$res_data,$params]));
        if(empty($res_data) || $res_data['status'] != 0 || empty($res_data['data'])){
            return false;
        }

        $statCusUsageModel = new StatisticsCustomerUsage();
        foreach($res_data['data'] as $hmyjApikey => $datas){
            $create_time = time();
            $apikey = $apikeyMap[$hmyjApikeyMap[$hmyjApikey]];
            foreach ($datas as $value) {

                if (!$value['search_count']) {
                    continue;
                }

                $tmpDate = date('Ymd', strtotime($value['date']));
                //新增statistics_customer_usage
                $usageWhere = [
                    ['date' , $tmpDate],
                    ['apikey', $apikey],
                    ['product_id', $productId],
                    ['node', 'beijing']
                ];
                $log = $statCusUsageModel->where($usageWhere)->first();
                //删除记录，重新添加
                if (count($log)) {
                    $deleteRes = $statCusUsageModel->where('id', $log['id'])->delete();
                    if (!$deleteRes) {
                        continue;
                    }
                }

                $addUsage = [
                    'apikey' => $apikey,
                    'product_id' => $productId,
                    'call_product_id' => $productId,
                    'node' => 'beijing',
                    'operator' => '',
                    'date' => $tmpDate,
                    'total' => $value['search_count'],
                    'success' => $value['success_count'],
                    'valid' => empty($value['hit_num']) ? $value['success_count'] : $value['hit_num'],
                    'create_time' => $create_time,
                ];
                $statCusUsageModel->insert($addUsage);
            }
        }
    }

    /**
     * 更新收入用量
     *
     * @param $upStartTime
     * @param $upEndTime
     * @return false
     */
    public function updateIncomeDatas($upStartTime, $upEndTime, $productId, $accountId)
    {
        $accountModel = new Account();
        //获取指定apikey
        $appointApikey = '';
        if ($accountId) {
            //获取对应的账号信息
            $appointApikey = $accountModel->where('account_id', $accountId)->get()->toArray();
            $appointApikey = $appointApikey[0]['apikey'];
        }

        //获取对应的统计记录
        $statCusUsageModel = new StatisticsCustomerUsage();
        $usageWhere = [
            ['date', '>=', $upStartTime],
            ['date', '<=', $upEndTime],
            ['product_id', $productId],
            ['node', 'beijing']
        ];
        if ($appointApikey) {
            $usageWhere[] = ['apikey', $appointApikey];
        }
        $usageDatas = $statCusUsageModel->where($usageWhere)->get();
        $usageDatas = count($usageDatas) ? $usageDatas->toArray() : [];
        if (!$usageDatas) {
            $this->output->error('暂无可用信息2');
            return;
        }
        $apikeys = array_unique(array_column($usageDatas, 'apikey'));

        //获取具有调用量的日期
        $usageDate = array_unique(array_column($usageDatas, 'date'));

        //获取对应的账号信息
        $accountDatas = $accountModel->whereIn('apikey', $apikeys)->get()->toArray();
        $accountMap = array_column($accountDatas, 'apikey', 'account_id');

        $accountProModel = new AccountProduct();
        $accountProInfos = $accountProModel->where('product_id', $productId)
                                        ->whereIn('account_id', array_keys($accountMap))
                                        ->get()->toArray();

        //获取号码预警提示apikey
        $hmyjApikeyMap = [];
        foreach ($accountProInfos as $item) {
            if (empty($item['data'])) {
                continue;
            }
            $tmp = json_decode($item['data'], true);
            if (empty($tmp['apikey_map'])) {
                continue;
            }
            $hmyjApikeyMap[$tmp['apikey_map']] = $item['account_id'];
        }

        if (!$hmyjApikeyMap) {
            $this->output->error('暂无可用信息');
            return;
        }

        $apikeyStr = implode(',', array_keys($hmyjApikeyMap));

        $curlParams = [
            'apikey' => $apikeyStr,
            'type' => self::$typeMap[$productId],
            'start_date' => $upStartTime,
            'end_date' => $upEndTime
        ];
        $sign = $this->createSign($curlParams, $this->secret_key);
        $curlParams['sign'] = $sign;
        $res_data = $this->post($this->url, $curlParams);
        Log::info('PULL_DATA_HAOMAYUJING_DATAS_USAGENUM：' . json_encode($res_data));
        if(empty($res_data) || $res_data['status'] != 0 || empty($res_data['data'])){
            return false;
        }
        $fatherId = (new Product())->select('father_id')->where('product_id', $productId)->first();
        $fatherId = $fatherId['father_id'];

        foreach($res_data['data'] as $hmyjApikey => $datas) {
            $create_time = time();
            $apikey = $accountMap[$hmyjApikeyMap[$hmyjApikey]];
            foreach ($datas as $value) {

                if (!$value['search_count']) {
                    continue;
                }

                $tmpDate = date('Ymd', strtotime($value['date']));

                if (!in_array($tmpDate, $usageDate)) {
                    continue;
                }

//                $billCustomerIncomeModel = new BillCustomerIncome();
                $billCustomerIncomeV2Model = new BillCustomerIncomeV2();
//                $billProductIncomeModel = new BillProductIncome();
                $billProductIncomeV2Model = new BillProductIncomeV2();
                try {
                    DB::beginTransaction();

                    //新增customer_income / customer_income_v2
                    $where = [
                        ['apikey', $apikey],
                        ['father_id',$fatherId],
                        ['product_ids', $productId],
                        ['date', $tmpDate],
                        ['operator', '']
                    ];
                    $save = [
                        'money' => $value['total_price'] ?? 0,
                    ];
                    $addCsuInData = [
                        'apikey' => $apikey,
                        'father_id' => $fatherId,
                        'product_ids' => $productId,
                        'date' => $tmpDate,
                        'number' => empty($value['hit_num']) ? $value['success_count'] : $value['hit_num'],
                        'money' => $value['total_price'] ?? 0,
                        'create_time' => $create_time,
                    ];
//                    $addCusId = $billCustomerIncomeModel->where($where)->value('id');
//                    //存在更新/不存在新增
//                    if ($addCusId) {
//                        $billCustomerIncomeModel->where('id', $addCusId)->update($save);
//                        $billProductIncomeModel->where('bill_customer_income_id', $addCusId)->delete();
//                    } else {
//                        $addCusId = $billCustomerIncomeModel->insertGetId($addCsuInData);
//                    }

                    $addCusV2Id = $billCustomerIncomeV2Model->where($where)->value('id');
                    //存在更新/不存在新增
                    if ($addCusV2Id) {
                        $billCustomerIncomeV2Model->where('id', $addCusV2Id)->update($save);
                        $billProductIncomeV2Model->where('bill_customer_income_id', $addCusV2Id)->delete();
                    } else {
                        $addCusV2Id = $billCustomerIncomeV2Model->insertGetId($addCsuInData);
                    }

                    //新增product_income / product_income_v2
                    $money =  $value['total_price'] ?? 0;
                    $money_finance = bcdiv($money, 2, 6);
                    $addProIn = [
                        'apikey' => $apikey,
                        'father_id' => $fatherId,
                        'product_id' => $productId,
                        'call_product_id' => $productId,
                        'bill_customer_income_id' => $addCusV2Id,
                        'date' => $tmpDate,
                        'number' => empty($value['hit_num']) ? $value['success_count'] : $value['hit_num'],
                        'money' => $money,
                        'money_finance' => $money_finance,
                        'create_time' => $create_time,
                    ];
//                    $billProductIncomeModel->insert($addProIn);

//                    $addProIn['bill_customer_income_id'] = $addCusV2Id;
                    $billProductIncomeV2Model->insert($addProIn);

                    DB::commit();
                } catch (\Exception $e) {
                    $dataJson = json_encode(['apikey' =>  $apikey,'data' => $value]);
                    $message = $e->getMessage();
                    $log = <<<TEXT
号码预警提示数据拉取添加失败

数据日期：{$tmpDate}
错误信息：{$message}
数据json：{$dataJson}
TEXT;
                    sendWechatNotice($log);
                    DB::rollBack();
                }
            }
        }
    }

    /**
     * 生成签名算法
     */
    public function  createSign($params = [], $secret_key = ''){
        ksort($params);
        $buff = '';
        foreach($params as $key=>$value){
            if($key != 'sign' && $value != '' && !is_array($value)){
                $buff .= $key . '=' . $value . '&';
            }
        }
        $buff = trim($buff, '&') . '&secret_key='.$secret_key;
        return strtoupper(md5($buff));
    }
}
