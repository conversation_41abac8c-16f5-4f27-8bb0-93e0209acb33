<?php

/**
 * 1、销帮帮、后台客户数据检测
 * 2、拉取销帮帮数据到后台
 */
namespace App\Console\Commands\PullDatas;


use App\Http\Repository\StatProductRepository;
use App\Models\BillProductIncomeV2;
use App\Models\CompanyType;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\RemitSplitPrice;
use App\Models\SystemUser;
use App\Models\XbbCustomer;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\CustomerIncomeRepository;
use Illuminate\Console\Command;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PullXBongBong extends Command
{
    use CurlTrait;

    protected $signature = 'pull_xiao_bong_bong
    {--type= : 任务类型，值: 1数据检测、2全量数据同步、3增量数据同步}
    {--update_time= : 指定更新时间,当type=3时，可以指定该值，会查询更新时间大于等于该传参值的数据}
    ';

    protected $description = '销帮帮数据同步后台&数据检测';

    protected $corpid = '17ff29537d95d758';//公司id
    protected $token = '5e77fdfbcf74e2b137407406f07cd749';//api签名使用token
    protected $host = 'https://appapi.xbongbong.com';
    //接口列表
    protected $apiList = [
        'formList' => '/pro/v2/api/form/list', //表单模板列表接口
        'formInfo' => '/pro/v2/api/form/get', //表单模板字段解释接口
        'addFormData' => '/pro/v2/api/paas/add', //新建自定义表单数据接口
        'listFormData' => '/pro/v2/api/paas/list', //自定义表单数据列表接口
        'editFormData' => '/pro/v2/api/paas/edit', //编辑自定义表单数据接口
        'userList' => '/pro/v2/api/user/list', //用户列表接口
        'customerList' => '/pro/v2/api/customer/list', //客户列表接口
    ];

    protected $type = '';//任务类型
    protected $customerMap = [];
    protected $companyTypeMap = [];
    protected $realnameMap = [];
    protected $deptMap = [];
    protected $crmUserList = [];
    protected $feishuRep = '';
    protected $crmFeishuChatId = 'oc_16bf9210c93a87026da0db687d1cce3a';
    protected $nonBankCustomerErrorInfo = [];
    protected $bankCustomerErrorInfo = [];
    protected $crmAllCustomerName = [];
    protected $nonBankLevelScaleMap = [];
    protected $nonBankLevelIncomeMap = [];
    protected $bankLevelScaleMap = [];
    protected $bankLevelIncomeMap = [];
    protected $appointTime = 0;

    public function handle()
    {
        //$this->getFormList();获取所有表格信息
        //$this->getFormFields();//获取某个表格所有字段信息

        //接收变量
        $this->checkParams();
        $this->initData();
        
        //数据检测
        if($this->type == 1){
            $this->checkCustomerData();
            $this->output->success('数据检测任务执行完毕');
        }

        //全量数据同步
        if($this->type == 2){
            $this->syncFullCustomerData();
            $this->output->success('全量数据同步任务执行完毕');
        }

        //增量数据同步
        if($this->type == 3){
            $this->syncIncrCustomerData();
            $this->output->success('增量数据同步任务执行完毕');
        }

    }

    public function checkCustomerData(){
        //检测crm非银团队数据列表
        $this->checkNonBankTeamCustomer();

        //检测crm银行团队数据列表
        $this->checkBankTeamCustomer();

        //crm客户简称全局唯一性检测
        $this->checkCustomerNameUnique();

        //后台客户简称命名规范检测
        $this->checkBackendCustomer();
    }

    public function syncFullCustomerData(){
        //非银团队数据客户级别字段对应关系获取
        $this->getNonBankTeamFormFields();
        //全量同步非银团队数据列表
        $this->syncFullNonBankTeamCustomer();

        //银行团队数据客户级别字段对应关系获取
        $this->getBankTeamFormFields();
        //全量同步银行团队数据列表
        $this->syncFullBankTeamCustomer();
    }

    public function syncIncrCustomerData(){
        //如果没指定更新时间，当前时间往前推一个小时(任务半个小时执行一次，查近半个小时内的更新数据，但是为了以防特殊原因有遗漏数据，在往前推半个小时)
        //指定更新时间的话 直接用指定值
        if(!$this->appointTime){
            $current_minute = date('Y-m-d H:i:00');
            $current_time = strtotime($current_minute) - 3600;

            $this->appointTime = $current_time;
        }

        //非银团队数据客户级别字段对应关系获取
        $this->getNonBankTeamFormFields();
        //增量同步非银团队数据列表
        $this->syncIncrNonBankTeamCustomer();

        //银行团队数据客户级别字段对应关系获取
        $this->getBankTeamFormFields();
        //增量同步银行团队数据列表
        $this->syncIncrBankTeamCustomer();
    }

    public function getNonBankTeamFormFields(){
        $url = $this->host . $this->apiList['formInfo'];
        $postData = [
            'formId' => 7504308,
            'corpid' => $this->corpid
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        if($res['code'] != 1){
            $this->sendNoticeToTech('非银表单模板字段解释接口出错', json_encode($postData), json_encode($res));
        }

        $list = $res['result']['explainList'] ?? [];
        unset($res);
        foreach ($list as $item){
            //客户规模级别
            if($item['attr'] == 'text_33'){
                foreach ($item['items'] as $scale_item){
                    $this->nonBankLevelScaleMap[$scale_item['value']] = $scale_item['text'];
                }
            }

            //客户收入级别
            if($item['attr'] == 'text_34'){
                foreach ($item['items'] as $scale_item){
                    $this->nonBankLevelIncomeMap[$scale_item['value']] = $scale_item['text'];
                }
            }
        }

        unset($list);
        $list = null;
    }

    public function getBankTeamFormFields(){
        $url = $this->host . $this->apiList['formInfo'];
        $postData = [
            'formId' => 8981806,
            'corpid' => $this->corpid
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        if($res['code'] != 1){
            $this->sendNoticeToTech('银行表单模板字段解释接口出错', json_encode($postData), json_encode($res));
        }

        $list = $res['result']['explainList'] ?? [];
        unset($res);
        foreach ($list as $item){
            //客户规模级别
            if($item['attr'] == 'text_38'){
                foreach ($item['items'] as $scale_item){
                    $this->bankLevelScaleMap[$scale_item['value']] = $scale_item['text'];
                }
            }

            //客户收入级别
            if($item['attr'] == 'text_33'){
                foreach ($item['items'] as $scale_item){
                    $this->bankLevelIncomeMap[$scale_item['value']] = $scale_item['text'];
                }
            }
        }

        unset($list);
        $list = null;
    }

    public function syncFullNonBankTeamCustomer(){
        $currentPage = 1;//默认从第一页开始
        $res = $this->getNonBankCutomerByCondition($currentPage, []);
        $totalPage = $res['totalPage'];

        $this->syncNonBankCustomerInfo($res['list']);
        for($page = $currentPage + 1; $page<=$totalPage; $page++){
            $res = $this->getNonBankCutomerByCondition($page, []);
            $this->syncNonBankCustomerInfo($res['list']);
        }
    }

    public function syncIncrNonBankTeamCustomer(){
        $currentPage = 1;//默认从第一页开始
        $conditions = [
            "attr" => "updateTime",
            "symbol" => "greaterequal",//大于等于
            "value" => [$this->appointTime]
        ];
        $res = $this->getNonBankCutomerByCondition($currentPage, $conditions);
        $totalPage = $res['totalPage'];
        $this->syncNonBankCustomerInfo($res['list']);
        for($page = $currentPage + 1; $page<=$totalPage; $page++){
            $res = $this->getNonBankCutomerByCondition($page, $conditions);
            $this->syncNonBankCustomerInfo($res['list']);
        }
    }

    public function syncFullBankTeamCustomer(){
        $currentPage = 1;//默认从第一页开始
        $res = $this->getBankCutomerByCondition($currentPage, []);
        $totalPage = $res['totalPage'];

        $this->syncBankCustomerInfo($res['list']);
        for($page = $currentPage + 1; $page<=$totalPage; $page++){
            $res = $this->getBankCutomerByCondition($page, []);
            $this->syncBankCustomerInfo($res['list']);
        }
    }

    public function syncIncrBankTeamCustomer(){
        $currentPage = 1;//默认从第一页开始
        $conditions = [
            "attr" => "updateTime",
            "symbol" => "greaterequal",//大于等于
            "value" => [$this->appointTime]
        ];
        $res = $this->getBankCutomerByCondition($currentPage, $conditions);
        $totalPage = $res['totalPage'];

        $this->syncBankCustomerInfo($res['list']);
        for($page = $currentPage + 1; $page<=$totalPage; $page++){
            $res = $this->getBankCutomerByCondition($page, $conditions);
            $this->syncBankCustomerInfo($res['list']);
        }
    }

    public function checkNonBankTeamCustomer(){
        $currentPage = 1;//默认从第一页开始
        $res = $this->getNonBankCutomerByCondition($currentPage, []);
        $totalPage = $res['totalPage'];

        $this->checkNonBankCustomerInfo($res['list']);
        for($page = $currentPage + 1; $page<=$totalPage; $page++){
            $res = $this->getNonBankCutomerByCondition($page, []);
            $this->checkNonBankCustomerInfo($res['list']);
        }

        //遍历错误信息
        $tips = '';
        foreach ($this->nonBankCustomerErrorInfo as $info){
            $tips .= $info.PHP_EOL;
        }

        if(!empty($tips)){
            $this->sendNoticeToSaleMan('非银团队客户错误信息', $tips);
        }

        unset($this->nonBankCustomerErrorInfo);
    }

    public function checkBankTeamCustomer(){
        $currentPage = 1;//默认从第一页开始
        $res = $this->getBankCutomerByCondition($currentPage, []);
        $totalPage = $res['totalPage'];

        $this->checkBankCustomerInfo($res['list']);
        for($page = $currentPage + 1; $page<=$totalPage; $page++){
            $res = $this->getBankCutomerByCondition($page, []);
            $this->checkBankCustomerInfo($res['list']);
        }

        //遍历错误信息
        $tips = '';
        foreach ($this->bankCustomerErrorInfo as $info){
            $tips .= $info.PHP_EOL;
        }

        if(!empty($tips)){
            $this->sendNoticeToSaleMan('银行团队客户错误信息', $tips);
        }

        unset($this->bankCustomerErrorInfo);
    }

    public function checkCustomerNameUnique(){
        $tips = '';
        foreach ($this->crmAllCustomerName as $key => $num){
            if($num > 1){
                $tips .= $key.PHP_EOL;
            }
        }

        unset($this->crmAllCustomerName);
        if(!empty($tips)){
            $this->sendNoticeToSaleMan('客户简称不唯一', $tips);
        }
    }

    public function checkBackendCustomer(){
        $error_info = '';
        $list = Customer::getListByCondition(['is_delete' => 0])->toArray();
        foreach ($list as $item){
            $company_name = $item['company'];
            $customer_name = $item['name'];
            //检测公司简称是否符合命名规则
            $is_right = $this->checkCustomerNameRule($customer_name, $company_name);

            $str = '';
            if(empty($company_name)){
                $str .= "公司全称为空\t";
            }

            if(empty($customer_name)){
                $str .= "公司简称为空\t";
            }

            if(!$is_right){
                $str .= "公司简称命名不符合规则\t";
            }

            if(!empty($str)){
                $error_info .= '公司全称='.$company_name.',公司简称='.$customer_name.', 错误信息:'.$str.PHP_EOL;
            }
        }

        if(!empty($error_info)){
            $this->sendNoticeToSaleMan('后台系统客户错误信息', $error_info);
        }
    }

    public function getNonBankCutomerByCondition($page, $conditions = []){
        $url = $this->host . $this->apiList['customerList'];
        $postData = [
            'formId' => 7504308,
            'corpid' => $this->corpid,
            'conditions' => empty($conditions) ? [] : [$conditions],
            'isPublic' => 0, //0：非公海客户（即正常列表客户），1：公海客户，不传：非公海客户+公海客户
            'page' => $page,
            'pageSize' => 50 //最大值100
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);

        if($res['code'] != 1){
            $this->sendNoticeToTech('非银客户列表查询失败', json_encode($postData), json_encode($res));
        }

        return ['totalCount' => $res['result']['totalCount'] ?? 0, 'totalPage' => $res['result']['totalPage'] ?? 0, 'list' => $res['result']['list'] ?? []];
    }

    public function getBankCutomerByCondition($page, $conditions = []){
        $url = $this->host . $this->apiList['customerList'];
        $postData = [
            'formId' => 8981806,
            'corpid' => $this->corpid,
            'conditions' => empty($conditions) ? [] : [$conditions],
            'isPublic' => 0, //0：非公海客户（即正常列表客户），1：公海客户，不传：非公海客户+公海客户
            'page' => $page,
            'pageSize' => 50 //最大值100
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);

        if($res['code'] != 1){
            $this->sendNoticeToTech('银客户列表查询失败', json_encode($postData), json_encode($res));
        }

        return ['totalCount' => $res['result']['totalCount'] ?? 0, 'totalPage' => $res['result']['totalPage'] ?? 0, 'list' => $res['result']['list'] ?? []];
    }

    public function syncNonBankCustomerInfo($data = []){
        foreach ($data as $item){
            $xbb_data_id = $item['dataId'];
            $group_name = $item['data']['text_30'] ?? '';//内部统称(主体)
            $company_name = $item['data']['text_1'] ?? '';//公司全称(公司名称)
            $customer_name = $item['data']['text_2'] ?? '';//公司简称(客户名称)
            $level_scale = $item['data']['text_33'] ?? '';//客户规模级别
            $level_scale = $this->nonBankLevelScaleMap[$level_scale] ?? '';
            $level_income = $item['data']['text_34'] ?? '';//客户收入级别
            $level_income = $this->nonBankLevelIncomeMap[$level_income] ?? 0;
            $level_scale_income = empty($level_scale.$level_income) ? '' : $level_scale.$level_income;
            $owner_ids = $item['data']['ownerId'] ?? "{}";//商务负责人为多个
            $owner_ids = json_decode($owner_ids, true);
            $owner_id = $owner_ids[0] ?? '';//只取第一个负责人
            $sale_man = $this->crmUserList[$owner_id] ?? '';
            $real_name = $this->realnameMap[$sale_man]['username'] ?? '';

            //检测公司简称是否符合命名规则
            $is_right = $this->checkCustomerNameRule($customer_name, $company_name);

            if(empty($group_name) || empty($company_name) || empty($customer_name)
                || empty($sale_man) || empty($real_name) || !$is_right
            ){
                continue;
            }

            $row = XbbCustomer::getCustomerInfoByXbbId($xbb_data_id);
            if(empty($row)){
                //新增数据
                $this->addXbbCustomerRecord($xbb_data_id, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income, $real_name);
            }else{
                //存在更新数据
                $this->updateXbbCustomerRecord($row, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income);
            }

        }
    }

    public function syncBankCustomerInfo($data = []){
        foreach ($data as $item){
            $xbb_data_id = $item['dataId'];
            $group_name = $item['data']['text_32'] ?? '';//内部统称(主体)
            $company_name = $item['data']['text_1'] ?? '';//公司全称(公司名称)
            $customer_name = $item['data']['text_2'] ?? '';//公司简称(客户名称)
            $level_scale = $item['data']['text_38'] ?? '';//客户规模级别
            $level_scale = $this->bankLevelScaleMap[$level_scale] ?? '';
            $level_income = $item['data']['text_33'] ?? '';//客户收入级别
            $level_income = $this->bankLevelIncomeMap[$level_income] ?? 0;
            $level_scale_income = empty($level_scale.$level_income) ? '' : $level_scale.$level_income;
            $owner_ids = $item['data']['ownerId'] ?? "{}";//商务负责人为多个
            $owner_ids = json_decode($owner_ids, true);
            $owner_id = $owner_ids[0] ?? '';//只取第一个负责人
            $sale_man = $this->crmUserList[$owner_id] ?? '';
            $real_name = $this->realnameMap[$sale_man]['username'] ?? '';

            //检测公司简称是否符合命名规则
            $is_right = $this->checkCustomerNameRule($customer_name, $company_name);

            if(empty($group_name) || empty($company_name) || empty($customer_name)
                || empty($sale_man) || empty($real_name) || !$is_right
            ){
                continue;
            }

            $row = XbbCustomer::getCustomerInfoByXbbId($xbb_data_id);
            if(empty($row)){
                //新增数据
                $this->addXbbCustomerRecord($xbb_data_id, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income, $real_name, 2);
            }else{
                //存在更新数据
                $this->updateXbbCustomerRecord($row, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income, 2);
            }

        }
    }

    public function checkNonBankCustomerInfo($data = []){
        foreach ($data as $item){
            $group_name = $item['data']['text_30'] ?? '';//内部统称(主体)
            $company_name = $item['data']['text_1'] ?? '';//公司全称(公司名称)
            $customer_name = $item['data']['text_2'] ?? '';//公司简称(客户名称)
            $owner_ids = $item['data']['ownerId'] ?? "{}";//商务负责人为多个
            $owner_ids = json_decode($owner_ids, true);
            $owner_id = $owner_ids[0] ?? '';//只取第一个负责人
            $sale_man = $this->crmUserList[$owner_id] ?? '';
            $real_name = $this->realnameMap[$sale_man] ?? [];
            //检测公司简称是否符合命名规则
            $is_right = $this->checkCustomerNameRule($customer_name, $company_name);

            $str = '';
            if(empty($group_name)){
                $str .= "内部统称为空\t";
            }

            if(empty($company_name)){
                $str .= "公司全称为空\t";
            }

            if(empty($customer_name)){
                $str .= "公司简称为空\t";
            }

            if(empty($sale_man)){
                $str .= "商务为空\t";
            }

            if(empty($real_name)){
                $str .= "后台无对应商务\t";
            }

            if(!$is_right){
                $str .= "公司简称命名不符合规则\t";
            }

            if(!empty($str)){
                $this->nonBankCustomerErrorInfo[] = '公司全称='.$company_name.',公司简称='.$customer_name.', 错误信息:'.$str;
            }

            if(isset($this->crmAllCustomerName[$customer_name])){
                $this->crmAllCustomerName[$customer_name]++;
            }else{
                $this->crmAllCustomerName[$customer_name] = 1;
            }
        }
    }

    public function checkBankCustomerInfo($data = []){
        foreach ($data as $item){
            $group_name = $item['data']['text_32'] ?? '';//内部统称(主体)
            $company_name = $item['data']['text_1'] ?? '';//公司全称(公司名称)
            $customer_name = $item['data']['text_2'] ?? '';//公司简称(客户名称)
            $owner_ids = $item['data']['ownerId'] ?? "{}";//商务负责人为多个
            $owner_ids = json_decode($owner_ids, true);
            $owner_id = $owner_ids[0] ?? '';//只取第一个负责人
            $sale_man = $this->crmUserList[$owner_id] ?? '';
            $real_name = $this->realnameMap[$sale_man] ?? [];
            //检测公司简称是否符合命名规则
            $is_right = $this->checkCustomerNameRule($customer_name, $company_name);

            $str = '';
            if(empty($group_name)){
                $str .= "内部统称为空\t";
            }

            if(empty($company_name)){
                $str .= "公司全称为空\t";
            }

            if(empty($customer_name)){
                $str .= "公司简称为空\t";
            }

            if(empty($sale_man)){
                $str .= "商务为空\t";
            }

            if(empty($real_name)){
                $str .= "后台无对应商务\t";
            }

            if(!$is_right){
                $str .= "公司简称命名不符合规则\t";
            }

            if(!empty($str)){
                $this->bankCustomerErrorInfo[] = '公司全称='.$company_name.',公司简称='.$customer_name.', 错误信息:'.$str;
            }

            if(isset($this->crmAllCustomerName[$customer_name])){
                $this->crmAllCustomerName[$customer_name]++;
            }else{
                $this->crmAllCustomerName[$customer_name] = 1;
            }
        }
    }



    public function initData(){
        $list = Customer::getListByCondition([], ['customer_id', 'name', 'company', 'c_type', 'type', 'channel_mode'])->toArray();
        $this->customerMap = array_column($list, null, 'customer_id');
        $this->companyTypeMap = CompanyType::getCompanyTypeMap('father');
        $list = SystemUser::getAllUsers();
        foreach ($list as $item){
            $this->realnameMap[$item['realname']] = [
                'username' => $item['username'],
                'dept_id' => $item['dept_id'],
            ];
        }

        $list = SystemDept::getAllDeptInfo();
        $this->deptMap = array_column($list, 'dept_name', 'dept_id');
        unset($list);
        $this->userListData();
        $this->feishuRep = new FeishuRepository();
        return;
    }

    public function checkParams(){
        $this->type = !empty($this->option('type')) ? $this->option('type') : 1;
        if($this->type == 3){
            $this->appointTime = !empty($this->option('update_time')) ? $this->option('update_time') : 0;
        }
    }

    //crm用户列表接口
    public function userListData(){
        $url = $this->host . $this->apiList['userList'];
        $postData = [
            'corpid' => $this->corpid,
            'pageSize' => 100 //最大值100
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        if($res['code'] != 1){
            usleep(500000);//500毫秒
            //sleep(1);
            //失败重试一次
            $res = $this->postRawJson($url, $postData, $headers);
            if($res['code'] != 1){
                //重试失败报警
                $msg = "crm获取用户列表失败".PHP_EOL;
                $msg .= PHP_EOL;
                $msg .= "数据日期时间:".date('Y-m-d H:i:s').PHP_EOL;

                $msg .= "接口响应:".json_encode($res, JSON_UNESCAPED_UNICODE);
                //sendWechatNotice($msg);
                #todo 报警
            }
        }
        usleep(300000);
        $this->crmUserList = array_column($res['result']['userList'], 'name','userId');
    }

    //表单模板字段解释接口
    public function getFormFields(){
        $url = $this->host . $this->apiList['formInfo'];
        $postData = [
            'formId' => 7504308,
            'corpid' => $this->corpid
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        dd(json_encode($res));
    }

    //表单模板列表接口
    public function getFormList(){
        $url = $this->host . $this->apiList['formList'];
        $postData = [
            'saasMark' => 2,
            'corpid' => $this->corpid
        ];
        $headers = $this->setHeaderData($postData);
        $res = $this->postRawJson($url, $postData, $headers);
        dd($res);

    }


    public function setHeaderData($data){
        $str = json_encode($data, JSON_UNESCAPED_UNICODE+JSON_UNESCAPED_SLASHES) . $this->token;
        $sign = hash('sha256', $str);
        $headers = ['sign: '.$sign];
        return $headers;
    }

    public function checkCustomerNameRule($customer_name, $company_name){
        $is_right = true;//默认符合规则
        //其中客户简称带杠只验证杠前面的部分
        if(mb_strpos($customer_name, '-') !== false){
            $customer_name = explode('-', $customer_name);
            $customer_name = $customer_name[0];
        }

        $length = mb_strlen($customer_name);
        for ($i=0; $i<$length; $i++){
            $char = mb_substr($customer_name, $i, 1);
            if(mb_strpos($company_name, $char) === false){
                $is_right = false;
                break;
            }
        }

        return $is_right;
    }

    public function updateXbbCustomerRecord($row, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income, $type = 1){
        //检测三要素、客户级别是否真正变更
        $is_change = $this->checkCustomerInfoChange($row, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income);
        $up_data = [
            'group_name' => $group_name,
            'company' => $company_name,
            'customer_name' => $customer_name,
            'level_scale' => $level_scale,
            'level_income' => $level_income,
            'level_scale_income' => $level_scale_income,
            'update_at' => time(),
        ];

        try {
            //状态已转换,不能直接自动更新customer(加提醒或允许更新 在列表标记)
            if($row['status'] == 2 && $is_change){
                $up_data['is_allow_update'] = 1;
                XbbCustomer::where(['id' => $row['id']])->update($up_data);
            }

            //状态非已转换，更新三要素、客户级别
            if($row['status'] != 2 && $is_change){
                XbbCustomer::where(['id' => $row['id']])->update($up_data);
            }
        }catch (\Exception $e){
            $this->sendNoticeToTech('销帮帮数据同步更新出错-'.$type, json_encode($up_data), $e->getMessage());
        }

    }

    public function addXbbCustomerRecord($xbb_data_id, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income, $real_name, $type = 1){
        $nowtime = time();
        $add = [
            'group_name' => $group_name,
            'company' => $company_name,
            'customer_name' => $customer_name,
            'level_scale' => $level_scale,
            'level_income' => $level_income,
            'level_scale_income' => $level_scale_income,
            'salesman' => $real_name,
            'type' => $type,
            'create_at' => $nowtime,
            'update_at' => $nowtime,
            'xbb_data_id' => $xbb_data_id,
        ];
        try {
            XbbCustomer::insert($add);
        }catch (\Exception $e){
            $this->sendNoticeToTech('销帮帮数据同步出错-'.$type, json_encode($add), $e->getMessage());
        }

    }

    public function checkCustomerInfoChange($row, $group_name, $company_name, $customer_name, $level_scale, $level_income, $level_scale_income){
        if($row['group_name'] == $group_name && $row['company'] == $company_name
            && $row['customer_name'] == $customer_name && $row['level_scale'] == $level_scale
            && $row['level_income'] == $level_income && $row['level_scale_income'] == $level_scale_income
        ){
            return false;//未真正变更
        }

        return true;//三要素或客户级别信息变更了
    }

    public function sendNoticeToTech($title, $request, $response){
        //echo $response.PHP_EOL;
        $this->feishuRep->send_card_message_to_chat_group($title, ['request' => $request, 'response' => $response]);
    }

    public function sendNoticeToSaleMan($title, $content){
        //echo $title.PHP_EOL.$content.PHP_EOL;
        $this->feishuRep->send_card_message_to_chat_group($title, [$content], $this->crmFeishuChatId);
    }

}
