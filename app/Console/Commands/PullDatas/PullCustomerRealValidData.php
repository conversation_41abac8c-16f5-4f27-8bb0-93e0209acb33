<?php

/**
 * 从ck拉取客户真实查得量
 */
namespace App\Console\Commands\PullDatas;

use App\Models\ClickHouse\RequestProductLog;
use App\Models\CustomerRealValid;
use App\Providers\RedisCache\RedisCache;
use Illuminate\Console\Command;


class PullCustomerRealValidData extends Command
{
    protected $signature = 'pull_customer_real_valid
    {--start_date= : 开始日期，格式：Ymd}
    {--end_date= : 结束日期，格式:Ymd}
    ';

    //查询父产品ID
    protected static $productIds = [10000, 20000];
    protected static $project = ['hmf', 'dh'];

    protected $description = '从ck查询客户产品真实查得量';

    public function handle()
    {
        //接收变量
        $pre_day = date('Ymd');
        $start_date = !empty($this->option('start_date')) ? $this->option('start_date') : $pre_day;
        $end_date = !empty($this->option('end_date')) ? $this->option('end_date') : $pre_day;
        if($start_date > $end_date){
            $this->output->error('开始时间不能大于结束时间');
            exit;
        }
        if(!empty($this->option('start_date')) && $this->option('start_date') < '20200701'){
            $this->output->error('开始时间必须大于20200701');
            exit;
        }

        try{
            $date = $start_date;
            while ($date <= $end_date){
                //echo $date.PHP_EOL;
                $this->getCustomerRealValidNum($date);
                $date = date('Ymd', strtotime($date) + 86400);
            }
        }catch (\Exception $e){
            $msg = "获取客户真实查得量".PHP_EOL;
            $msg .= PHP_EOL;
            $msg .= "数据日期:".$date.PHP_EOL;
            $msg .= "异常信息:".$e->getMessage().PHP_EOL;
            sendWechatNotice($msg);
        }


    }


    public function getCustomerRealValidNum($date){
        $start_time = strtotime($date);
        $end_time = strtotime($date) + 86400;
        $ck = new RequestProductLog();
        $where['project'] = self::$project;
        $where['pid'] = self::$productIds;
        $list = $ck->getCustomerRealValidNum($start_time, $end_time, $where);
        if(empty($list)){
            $this->sendAlarm($date);
            return;
        }

        //删除历史数据
        CustomerRealValid::delData($date);
        //从新添加数据
        foreach ($list as &$item){
            $father_id = RedisCache::instance('productId_fatherId_mapping')->get($item['product_id']);
            $item['father_id'] = $father_id ? $father_id : $item['product_id'];
            $item['date'] = $date;
            $item['create_time'] = time();
        }
        $addLogChunk = array_chunk($list, 100);
        foreach ($addLogChunk as $addArr) {
            CustomerRealValid::addData($addArr);
        }

        unset($list);
        return;
    }


    public function sendAlarm($date){
        $msg = "获取客户真实查得量".PHP_EOL;
        $msg .= PHP_EOL;
        $msg .= "数据日期:".$date.PHP_EOL;
        $msg .= "报警原因:无数据".PHP_EOL;
        sendWechatNotice($msg);
    }




}
