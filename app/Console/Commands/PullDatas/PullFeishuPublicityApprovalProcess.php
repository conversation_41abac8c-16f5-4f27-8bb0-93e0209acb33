<?php

/**
 * 拉取飞书公出审批实例
 */
namespace App\Console\Commands\PullDatas;


use App\Http\Repository\StatProductRepository;
use App\Models\BillProductIncomeV2;
use App\Models\CompanyType;
use App\Models\Crs\SystemDept;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\feishu\FeishuPublicityApprovalProcess;
use App\Models\RemitSplitPrice;
use App\Models\SystemUser;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\CustomerIncomeRepository;
use Illuminate\Console\Command;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PullFeishuPublicityApprovalProcess extends Command
{
    use CurlTrait;

    protected $signature = 'pull_publicity_approval';

    protected $description = '拉取飞书公出审批实例';

    protected $feishuRep = '';
    protected $feishuDaKaRep = '';

    public function handle()
    {
        //获取近一周且状态为审批中的公出审批数据
        $start_date = date('Ymd', strtotime('-7 days'));
        $start_time = strtotime($start_date);
        $list = $this->getApprovelProcessData($start_time)->toArray();
        if(empty($list)){
            return false;
        }

        try{
            $all_status = array_keys(FeishuPublicityApprovalProcess::$status);
            $this->feishuRep = new FeishuRepository();
            $this->feishuDaKaRep = new FeishuRepository('feishu_daka');
            foreach ($list as $item){
                $id = $item['id'];
                $instance_id = $item['process_instance_id'];
                $info = $this->feishuRep->get_publicity_approval_instance_info($instance_id);
                usleep(200000);
                if(isset($info['code']) && $info['code'] == 0 && in_array($info['data']['status'], $all_status)){
                    $this->updateApprovalProcess($id, $info['data']['status'], $item);
                }else{
                    $msg = $info['msg'] ?? '';
                    $this->sendException($item['plan_no'], $msg);
                }

            }
        }catch (\Exception $e){
            $this->sendException('', $e->getMessage(), '拉取飞书公出审批异常');
        }

        $this->output->success("拉取公出审批实例结束");
    }

    //获取审批数据
    private function getApprovelProcessData($start_time)
    {
        //获取近一周且状态为审批中的公出审批数据
        $where = [
            ['create_time', '>=', $start_time],
            ['status', '=', 'PENDING'],
            ['real_result', '=', 1],
        ];
        $list = FeishuPublicityApprovalProcess::getListByCondition($where);

        return $list;
    }

    public function updateApprovalProcess($id, $status, $row = []){
        if($status == 'PENDING'){
            return;
        }

        //审批通过则同步外出考勤
        if($status == 'APPROVED'){
            $this->insertOutsRecord($row);
        }

        //更新审批结果
        $updateData['status'] = $status;
        $updateData['real_result'] = 2;
        FeishuPublicityApprovalProcess::updateData(['id' => $id], $updateData);

        return;
    }

    public function insertOutsRecord($row){
        $user_id = $row['user_id'];
        $plan_no = $row['plan_no'];
        $start_time = $row['visit_s_time'];
        $end_time = $row['visit_e_time'];
        $reason = $row['plan_name'].';'.$row['visit_purpose'];

        $extend_time = $this->feishuDaKaRep->extendOutTime($start_time, $end_time);

        $res = $this->feishuDaKaRep->insert_outs_record($user_id, $extend_time['visit_s_time'], $extend_time['visit_e_time'], $reason, $plan_no);
        if(isset($res['code']) && $res['code'] == 0){
            //更新外出考勤标识
            $updateData['is_out'] = 1;
            FeishuPublicityApprovalProcess::updateData(['id' => $row['id']], $updateData);
            usleep(200000);
        }else{
            $this->sendException($row['plan_no'], $res, '同步外出考勤失败');
        }
        return;
    }

    public function sendException($plan_no = '', $message = '', $title = '飞书公出获取实例失败'){
        $msg = $title.PHP_EOL;
        $msg .= PHP_EOL;
        $msg .= "计划编号:".$plan_no.PHP_EOL;
        $msg .= "失败时间:".date('Y-m-d H:i').PHP_EOL;
        $msg .= "错误信息:".json_encode($message, JSON_UNESCAPED_UNICODE);
        sendWechatNotice($msg);
        return;
    }


}
