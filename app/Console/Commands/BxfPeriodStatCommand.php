<?php

namespace App\Console\Commands;

use App\Models\BxfPeriod;
use App\Models\BxfPeriodStat;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
class BxfPeriodStatCommand extends Command
{
    /**
     * 命令行执行命令
     */
    protected $signature = 'bxf:period-stat';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        //获取本月的第一天
        $begin_date =date('Y-m-01', strtotime(date("Y-m-d")));
        $end_date = date('Y-m-d 23:59:59', strtotime("$begin_date +1 month -1 day"));
        $obj = BxfPeriod::where('daytime', 1)
            ->where('created_at', '>=', strtotime($begin_date))
            ->where('created_at', '<=', strtotime($end_date))
            ->select('channel', 'channel_name', 'daytime', DB::raw('count(daytime) as day'))
            ->groupBy('channel')
            ->get();
        if(!$obj){
            return false;
        }
        $data = $obj->toArray();
        $created_at = time();
        array_walk($data, function($val) use ($begin_date, $created_at){
            $where = ['channel'=>$val['channel'], 'month'=>$begin_date];
            $param = [
                'channel'=>$val['channel'],
                'channel_name'=>$val['channel_name'],
                'num'=>$val['day'],
                'month'=>$begin_date,
                'created_at'=>$created_at
            ];
            BxfPeriodStat::updateOrCreate($where, $param);
        });
    }
}
