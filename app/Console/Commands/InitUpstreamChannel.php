<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\Upstream;
use App\Models\UpstreamChannelPrice;
use Illuminate\Console\Command;

//初始化上游渠道

/**
 * 【目标表】 -- upstream_channel
 *
 * 整理[mysql]产品表product中channel_stat字段，将结果插入值【目标表】中
 *  1.邦秒验的产品中，获取'upstream'渠道值，然后找出在【目标表】不存在的渠道并插入
 *  2. 同邦秒验产品
 *  3. 快捷版产品中，获取'upstream'渠道值，然后在'interface_item'索引中，获取每个渠道的字段，组合为新的渠道（渠道名_字段名），然后找出在【目标表】不存在的渠道并插入
 **/
class InitUpstreamChannel extends Command
{

    public $signature = 'upstream:init-channel';

    /** @var string 命令提示 */
    public $description = '整理【邦企查】【邦秒验】【快捷版】的上游数据源单元';

    protected $upstreamModel;

    protected $time;

    public function __construct()
    {
        parent::__construct();
        $this->time          = time();
        $this->upstreamModel = new Upstream();
    }

    public function handle()
    {
//        $data = Upstream::where('product_id', 210)
//            ->get()
//            ->filter(
//                function ($item) {
//                    return strpos($item->channel, 'BJCMCC') !== false;
//                }
//            )->map(function ($item) {
//               $price = '{"all":0.1}';
//               $id = $item->id;
//               $channel = $item->channel;
//               $product_id = $item->product_id;
//               return compact('price', 'id', 'channel', 'product_id');
//            });
//        UpstreamChannelPrice::insert($data->toArray());
//
//        halt($data);


        $this->dispose200();
        $this->dispose401();
        $this->dispose210();
        //添加号码状态查询
        $this->dispose801();

        $this->output->success('执行成功');
    }

    /**
     * 整理邦秒验的上游渠道
     *      每个子产品与每个渠道两两匹配，组合成上游渠道
     *
     * @access protected
     *
     * @return void
     **/
    protected function dispose200()
    {
        $product_id = 200;

        //取出子产品的ID
        $children_product_id = Product::where('father_id', '=', $product_id)
            ->pluck('product_id')
            ->toArray();

        //拿出upstream字段
        $channelStat = Product::where('product_id', '=', $product_id)
            ->value('channel_stat');
        if (empty($channelStat)) {
            return;
        }
        $channelStat = json_decode($channelStat, true);
        $upstream    = array_key_exists('upstream', $channelStat) ? $channelStat['upstream'] : [];
        if (empty($upstream)) {
            return;
        }

        //全量渠道数据
        $channel = [];
        foreach ($children_product_id as $childrenProductItemId) {
            foreach (array_keys($upstream) as $upstreamItem) {
                $channel[] = $childrenProductItemId . '|' . $upstreamItem;
            };
        }

        //获取【目标表】中不存在的渠道
        $existsChannel = $this->upstreamModel->whereIn('product_id', $children_product_id)
            ->get()
            ->map(
                function ($item) {
                    return $item->product_id . '|' . $item->channel;
                }
            )
            ->toArray();

        //求差集获取需要增加的渠道（数据源）
        //生成待创建的数据

        $willCreateData = array_map(
            function ($item) use ($upstream) {
                $create_time = $this->time;
                list($product_id, $channel) = explode('|', $item);
                $name = $upstream[$channel];
                return compact('channel', 'name', 'product_id', 'create_time');
            },
            array_diff($channel, $existsChannel)
        );

        $this->upstreamModel->insert($willCreateData);

    }

    /**
     * 整理邦企查的上游渠道
     *
     * @access protected
     *
     * @return void
     **/
    protected function dispose401()
    {
        $product_id = 401;
        //拿出upstream字段
        $channelStat = Product::where('product_id', '=', $product_id)
            ->value('channel_stat');
        if (empty($channelStat)) {
            return;
        }
        $channelStat = json_decode($channelStat, true);
        $upstream    = array_key_exists('upstream', $channelStat) ? $channelStat['upstream'] : [];
        if (empty($upstream)) {
            return;
        }
        $channel = array_keys($upstream);

        //获取【目标表】中不存在的渠道
        $existsChannel = $this->upstreamModel->where('product_id', $product_id)
            ->pluck('channel')
            ->toArray();

        //求差集获取需要增加的渠道（数据源）
        //生成待创建的数据

        $willCreateData = array_map(
            function ($channel) use ($product_id, $upstream) {
                $create_time = $this->time;
                $name        = $upstream[$channel];
                return compact('channel', 'name', 'product_id', 'create_time');
            },
            array_diff($channel, $existsChannel)
        );

        $this->upstreamModel->insert($willCreateData);
    }

    /**
     * 整理快捷版的上游渠道
     *
     * @access protected
     *
     * @return void
     **/
    protected function dispose210()
    {
        $product_id = 210;
        //拿出upstream字段
        $channelStat = Product::where('product_id', '=', $product_id)
            ->value('channel_stat');
        if (empty($channelStat)) {
            return;
        }
        $channelStat = json_decode($channelStat, true);
        $upstream    = array_key_exists('upstream', $channelStat) ? $channelStat['upstream'] : [];
        if (empty($upstream)) {
            return;
        }

        //拿出interface_item字段
        $interface = array_key_exists('interface_item', $channelStat) ? $channelStat['interface_item'] : [];
        if (empty($interface)) {
            return;
        }
        //取出每个渠道中的每个字段，组成新的渠道
        $channel = [];
        foreach (array_keys($upstream) as $upstreamItem) {
            if (array_key_exists($upstreamItem, $interface) && !empty($interface[$upstreamItem])) {
                foreach (array_keys($interface[$upstreamItem]) as $interfaceItem) {
                    if (!empty($interfaceItem)) {
                        array_push($channel, $upstreamItem . '_' . $interfaceItem);
                    }
                }
            }
        }

        //获取【目标表】中不存在的渠道
        $existsChannel = $this->upstreamModel->where('product_id', $product_id)
            ->pluck('channel')
            ->toArray();

        //求差集获取需要增加的渠道（数据源）
        //生成待创建的数据

        $willCreateData = array_map(
            function ($channel) use ($product_id, $upstream, $interface) {
                $create_time    = $this->time;
                $channelPrefix  = strstr($channel, '_', true);
                $channelPostfix = substr($channel, strlen($channelPrefix) + 1);
                $name           = "{$upstream[$channelPrefix]} - {$interface[$channelPrefix][$channelPostfix]}";
                return compact('channel', 'name', 'product_id', 'create_time');
            },
            array_diff($channel, $existsChannel)
        );

        $this->upstreamModel->insert($willCreateData);
    }
    /**
     * 处理号码状态查询上游渠道配置
     *
     */
    protected function dispose801()
    {
        $product_id = 801;
        //拿出upstream字段
        $channelStat = Product::where('product_id', '=', $product_id)
            ->value('channel_stat');
        if (empty($channelStat)) {
            return;
        }
        $channelStat = json_decode($channelStat, true);
        $upstream    = array_key_exists('upstream', $channelStat) ? $channelStat['upstream'] : [];
        if (empty($upstream)) {
            return;
        }
        $channel = array_keys($upstream);

        //获取【目标表】中不存在的渠道
        $existsChannel = $this->upstreamModel->where('product_id', $product_id)
            ->pluck('channel')
            ->toArray();

        //求差集获取需要增加的渠道（数据源）
        //生成待创建的数据

        $willCreateData = array_map(
            function ($channel) use ($product_id, $upstream) {
                $create_time = $this->time;
                $name        = $upstream[$channel];
                return compact('channel', 'name', 'product_id', 'create_time');
            },
            array_diff($channel, $existsChannel)
        );
        $this->upstreamModel->insert($willCreateData);
    }
}