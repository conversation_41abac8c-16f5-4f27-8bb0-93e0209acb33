<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\MongoBillDay;
use App\Models\MongoBillMonth;
use App\Models\ShortcutPackage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

//生成邦信分快捷版打包计费的月账单
class GenShortcutPackageDayBill extends Command
{
	
	/** @var string 命令 */
	public $signature = 'bill:generate-shortcut-package-day
    {--customer_id= : 客户ID}
    {--date= : 日期, 格式Ymd}
    ';
	
	protected $description = '生成快捷版打包客户的日账单';
	
	protected $customer_id = [
		'C201811012TPQQ8',      //读秒
		'C20190225VJOUQA',      //广汽租赁
		'C20190801R97IIL'       //杭银消金
	];
	
	protected $account_id = [
		'C201811012TPQQ8' => '****************',      //读秒
		'C20190225VJOUQA' => '****************',      //广汽租赁
		'C20190801R97IIL' => '****************'       //杭银消金
	];
	protected $start_date = '********';
	
	public function handle()
	{
		try {
			// 实际执行
			$this->handleDo();
			
			$this->output->writeln('生成账单成功');
		} catch (\Exception $e) {
			$msg = '生成账单失败  msg: ' . $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
			$this->output->error($msg);
		}
	}
	
	/**
	 * 生成打包账单
	 *
	 * @access protected
	 *
	 * @return void
	 **/
	protected function handleDo()
	{
		$billDate    = $this->getBillDate();
		$customer_id = $this->getBillCustomerId();
		
		if (empty($billDate)) {
			return;
		}
		
		//生成日账单
		foreach ($customer_id as $itemCustomerId) {
			$this->createItemMonthBill($billDate, $itemCustomerId);
		}
		
		
	}
	
	/**
	 * 获取需要生成的日期
	 *
	 * @access protected
	 *
	 * @return string
	 **/
	protected function getBillDate()
	{
		$date = $this->option('date');
		if ($date) {
			return $date >= $this->start_date ? $date : '';
		} else {
			return date('Ymd', strtotime('-1 days'));
		}
	}
	
	/**
	 * 获取需要生成的客户ID
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function getBillCustomerId()
	{
		$customer_id = $this->option('customer_id');
		if ($customer_id) {
			return array_intersect($this->customer_id, [$customer_id]);
		}
		
		return $this->customer_id;
	}
	
	/**
	 * 生成某一个月份某一个客户的月账单
	 *
	 * @access protected
	 *
	 * @param $date        string 账单日
	 * @param $customer_id string 客户ID
	 *
	 * @return void
	 **/
	protected function createItemMonthBill($date, $customer_id)
	{
		//获取客户与之对应的apikey
		$apikey = Account::where('customer_id', '=', $customer_id)
						 ->pluck('apikey')
						 ->filter(function ($item) {
							 return !empty($item);
						 })
						 ->toArray();
		
		$functionName = 'get' . $customer_id . 'NumberAndMoney';
		
		if (!method_exists($this, $functionName)) {
			throw new \Exception("不存在{$customer_id}的计费规则");
		}
		$detail                 = $this->$functionName($apikey, $date);
		$account_id             = $this->account_id[$customer_id];
		$product_id             = 210;
		$month                  = date('Ym', strtotime($date));
		$money                  = array_sum(array_column($detail, 'money'));
		$section_invoked_number = array_sum(array_column($detail, 'number'));
		$slug_id                = '';
		$dayBill                = compact('account_id', 'product_id', 'customer_id', 'date', 'month', 'money', 'section_invoked_number', 'slug_id');
		MongoBillDay::create($dayBill);
	}
	
	/**
	 * 获取读秒的计费用量&&费用
	 *
	 * @access protected
	 *
	 * @param $apikey array
	 * @param $date   string 日期
	 *
	 * @return array
	 **/
	protected function getC201811012TPQQ8NumberAndMoney($apikey, $date)
	{
		
		//获取计费用量
		//评分字段为1的，普通字段为0的请求数量
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where('graded_number', '=', 1)
								 ->where('normal_number', '=', 0)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 0.5;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		//评分字段为0的，普通字段为小于等于4的
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where('graded_number', '=', 0)
								 ->where('normal_number', '<=', 4)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 1.4;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		//平分字段为1，普通字段不为0
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where('graded_number', '=', 1)
								 ->where('normal_number', '!=', 0)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 2.57;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		
		return $list;
	}
	
	/**
	 * 获取广汽租赁的计费用量&&费用
	 *
	 * @access protected
	 *
	 * @param $apikey array
	 * @param $date   string 日期
	 *
	 * @return array
	 **/
	protected function getC20190225VJOUQANumberAndMoney($apikey, $date)
	{
		//字段个数小于等于2个的
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where(DB::raw('`graded_number` + `normal_number`'), '<=', 2)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 0.5;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		
		//字段格式为3的
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where(DB::raw('`graded_number` + `normal_number`'), '=', 3)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 0.45;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		
		//字段格式为4的
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where(DB::raw('`graded_number` + `normal_number`'), '=', 4)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 0.425;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		
		//字段格式为5的
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where(DB::raw('`graded_number` + `normal_number`'), '>=', 3)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 0.4;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		
		return $list;
	}
	
	/**
	 * 获取杭银消金的计费用量&&费用
	 *
	 * @access protected
	 *
	 * @param $apikey array
	 * @param $month  string 月份
	 *
	 * @return array
	 **/
	protected function getC20190801R97IILNumberAndMoney($apikey, $date)
	{
		//字段个数等于1个的
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where(DB::raw('`graded_number` + `normal_number`'), '=', 1)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 0.3;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		
		//字段格式为3的
		$number = ShortcutPackage::whereIn('apikey', $apikey)
								 ->where(DB::raw('`graded_number` + `normal_number`'), '=', 2)
								 ->where('request_date', '=', $date)
								 ->count();
		$price  = 0.29;
		$money  = $number * $price;
		$list[] = compact('number', 'price', 'money');
		
		return $list;
	}
}