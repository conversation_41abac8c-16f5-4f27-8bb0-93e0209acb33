<?php

namespace App\Console\Commands\Contract;

use App\Http\Repository\ContractRepositorie;
use App\Models\Contract;
use App\Models\SystemUser;
use App\Providers\Auth\DataAuth;
use App\Providers\Tool\SendMailService;
use App\TraitUpgrade\ExcelTrait;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
/**
 * 合同未归档提醒邮件
 *
 * php artisan contract:archive_reminder --date 20250616 --sendto wei.xiu
 */
class ContractArchiveReminder extends Command
{
    use ExcelTrait;

    protected $signature   = 'contract:archive_reminder
    {--date= : 日期, 格式Ymd，默认当天}
    {--sendto= : 收件人, 添加此选项将强制使收件人为sendto, 使用username指定的商务的数据}
    ';
    protected $description = '合同未归档提醒邮件';

    /**
     * 脚本用于比较的时间
     *
     * @var array|false|string
     */
    private $date;


    private $cc = [
        'leader' => [
            ['email' => '<EMAIL>'],
            ['email' => '<EMAIL>'],
        ],
        'all' => [
            ['email' => '<EMAIL>'],
            ['email' => '<EMAIL>'],
        ],
        'channel'  => [
            ['email' => '<EMAIL>'],
            ['email' => '<EMAIL>'],
        ],
        'customer' => [
            //华东银行部 艳君，vincent， 黄晶晶
            'DEPT2023070615053952' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
            //华北银行部 艳君，vincent 张龙
            'DEPT2023070615055818' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
            //华南银行部 艳君，vincent
            'DEPT2024031311512954' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
            //华北非银部 小兰，赵志辉
            'DEPT2019061815085572' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
            //华东非银部 丽丽，mandy
            'DEPT2019061815103928' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
            //华南非银部 小兰，秦超
            'DEPT2019061815114998' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
            'DEPT2022120220115177' => [
                ['email' => '<EMAIL>'],
                ['email' => '<EMAIL>'],
            ],
        ],
    ];

    private $all_send = 'ren.zhang';

    /**
     * @var array|string
     */
    private $sendto;


    /**
     *
     * 脚本执行
     *
     * @return void
     * <AUTHOR> 2025-06-20 11:59:15
     *
     */
    public function handle() {
        try {
            //设置参数
            $this->set_params();

            $data = $this->get_data();
            if (empty($data)) {
                Log::info($this->description . ': 未发送, 没有数据');
                return;
            }

            $this->send($data);
        } catch (Exception $exception) {
            $msg = $exception->getFile() . ':' . $exception->getLine() . ':' . $exception->getMessage();
            Log::info($this->description . ': 发送失败, ' . $msg);
            sendCommandExceptionNotice($this, $exception);
        }
    }

    /**
     *
     * 获取数据
     *
     * @return array
     * <AUTHOR> 2025-06-20 12:15:28
     *
     */
    private function get_data(){
        $day_60_days_early_date = date('Y-m-d H:i:s',(strtotime('-60 day',strtotime($this->date))));
        $where =  [
            ['approval_time','<=',$day_60_days_early_date],//超过60天
            ['archive_status','=',0],//未归档
        ];
        $list = Contract::where($where)->whereNull('delated_at')->get();

        if(empty($list)){
            return [];
        }
        $list = $list->toArray();

        $res = [];

        foreach ($list as $item) {
            $res[$item['salesman']][] = $item;
        }

        return $res;
    }

    /**
     * 对每一个用户发送邮件
     *
     * @access   private
     *
     * @param array $data
     *
     * @return void
     * @throws Exception
     * <AUTHOR>
     * @datetime 2021/1/21 17:49
     */
    private function send(array $data) {

        // all
        {
            $username        = $this->all_send;
            $cc              = $this->cc['all'];
            $dataAuthService = new DataAuth($username);
            //标题
            $title      = date('m月d日', strtotime($this->date)) . '合同超期未归档数据汇总';
            $table_html = $this->all_user_html($data);
            $isCCTOLeader = $this->isCCToLeader($data);
            if ($isCCTOLeader){
               $cc =  array_merge($cc,$this->cc['leader']);
            }
            $html       = $this->create_html($title, $table_html);

            $this->email($username, $cc, $title, $html, $dataAuthService->getRealName());
        }


        //single
        foreach ($data as $username => $unarchive_list) {
            $dataAuthService = new DataAuth($username);
            $salesman_arr    = [$username];
            $salesman_arr    = SystemUser::getDeptInfo($salesman_arr);
            $salesman_arr    = array_column($salesman_arr, null, 'username');
            $salesman_info   = $salesman_arr[$username];

            $customer_unarchive_list = [];
            $channel_unarchive_list  = [];
            foreach ($unarchive_list as $item) {
                if ($item['contract_category'] == ContractRepositorie::CONTRACT_CATEGORY_CUSTOMER) {
                    $customer_unarchive_list[] = $item;
                } else {
                    $channel_unarchive_list[] = $item;
                }
            }


            $unarchive_list = [
                'customer' => [
                    'list'   => $customer_unarchive_list,
                    'prefix' => '客户',
                    'cc'     => $this->cc['customer'][$salesman_info['dept_id']] ?? [],
                ],
                'channel'  => [
                    'list'   => $channel_unarchive_list,
                    'prefix' => '渠道',
                    'cc'     => $this->cc['channel'],
                ],
            ];
            foreach ($unarchive_list as $type => $unarchive_info) {
                if (empty($unarchive_info['list'])) {
                    continue;
                }
                $title = date('m月d日', strtotime($this->date)) . $unarchive_info['prefix'] . '合同超期未归档提醒 -- （' . $salesman_info['realname'] . '）收';

                $table_html = $this->single_user_html($unarchive_info['list']);
                $html       = $this->create_html($title, $table_html);

                $this->email($username, $unarchive_info['cc'], $title, $html, $dataAuthService->getRealName());
            }
        }
    }

    /**
     * @param $data
     * @return bool
     */
    private function isCCToLeader($data){
        foreach($data as $salesman => $contract_list) {
            foreach($contract_list as $item) {
                $overdue_days = ceil((strtotime($this->date) - strtotime($item['approval_time']))/86400);
                if ($overdue_days > 100){
                    return true;
                }
            }
        }
        return  false;
    }

    /**
     * 发送邮件
     *
     * @param $username
     * @param $cc
     * @param $title
     * @param $html
     * @param $name
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2025-06-30 15:59:47
     */
    private function email($username, $cc, $title, $html, $name) {
        Log::info($this->description . ': 发送邮件给' . $username . ', 抄送:' . json_encode($cc));

        if ($this->sendto) {//如果指定收件人
            $username = $this->sendto;
            $cc = [['email' => $username.'@yulore.com']];
            Log::info($this->description . ', 更改为发送邮件给: ' . $username . ', 抄送:' . json_encode($cc));
        }

        //发送邮件
        $mail = new SendMailService();
        $mail->setFromName('金融后台项目组')
             ->setAddressee([['email' => $username . '@yulore.com', 'name' => $name]])
             ->setCC($cc)
             ->setSubject($title)
             ->setContent($html)
             ->sendByAsync();
             // ->send();

        $this->output->success("[$name] 邮件发送成功");
    }


    /**
     * 创建HTML内容
     *
     * @access   private
     *
     * @param string $title 邮件标题
     * @param        $table_html
     *
     * @return string
     * <AUTHOR>
     * @datetime 2021/1/22 14:49
     *
     */
    private function create_html($title, $table_html) {

        return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>

        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }
        table {
            width         : 98%;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-top: 1px solid #CCCCCC;border-right-style: none;border-bottom-style: none;border-left: 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

    </style>
    <title>超期未归档提醒</title>
</head>
<body>
<h1>$title</h1>
<table>
    {$table_html}
</table>
HTML;
    }

    /**
     * 设置参数
     *
     * @access   private
     * @return void
     * @throws Exception
     * <AUTHOR>
     * @datetime 2021/1/19 11:12
     *
     */
    private function set_params() {
        //设置所需要的日期
        $date       = $this->option('date');
        $this->date = $date ?: date('Ymd', strtotime('-1 days'));
        if ($this->date > date('Ymd') || !preg_match('/^\d{8}$/', $this->date)) {
            throw new Exception('日期格式不正确');
        }

        $sendto = $this->option('sendto');
        if ($sendto) {
            $this->sendto = $sendto;
        }
    }


    /**
     * 单个商务
     *
     * @param $data
     *
     * @return string
     * <AUTHOR> 2025-06-20 15:30:32
     */
    private function single_user_html($data){
        $table = '<tr>
                    <th>客户名称</th>
                    <th>OA编号</th>
                    <th>合同名称</th>
                    <th>合同盖章审批通过时间</th>
                    <th>状态</th>
                    <th>超期未归档天数</th>
                </tr>';


        foreach ($data as $item) {
            $overdue_days = ceil((strtotime($this->date) - strtotime($item['approval_time'])) / 86400);
            // 公司名,渠道名 其中有一个有值
            $table        .= '<tr>
                        <td>' . $item['company_name'] . $item['channel_full_name'] . '</td>
                        <td>' . $item['serial_number'] . '</td>
                        <td>' . $item['contract_name'] . '</td>
                        <td>' . $item['approval_time'] . '</td>
                        <td>' . ($item['archive_status'] == 0 ? '未归档' : '已归档') . '</td>
                        <td>' . $overdue_days . '天</td>
                    </tr>';
        }

        return $table;
    }

    /**
     *
     *
     * @param $data
     *
     * @return string
     * <AUTHOR> 2025-06-20 14:38:41
     */
    private function all_user_html($data){

        $table = '<tr>
                    <th>客户名称</th>
                    <th>OA编号</th>
                    <th>合同名称</th>
                    <th>合同盖章审批通过时间</th>
                    <th>状态</th>
                    <th>超期未归档天数</th>
                    <th>区域</th>
                    <th>商务</th>
                </tr>';
        $salesman_arr = array_keys($data);
        $salesman_arr = SystemUser::getDeptInfo($salesman_arr);
        $salesman_arr = array_column($salesman_arr,null,'username');
        foreach($data as $salesman => $contract_list) {
            foreach($contract_list as $item) {
            $overdue_days = ceil((strtotime($this->date) - strtotime($item['approval_time']))/86400);
                // 公司名,渠道名 其中有一个有值
                $table .= '<tr>
                        <td>' . $item['company_name'] . $item['channel_full_name'] . '</td>
                        <td>' . $item['serial_number'] . '</td>
                        <td>' . $item['contract_name'] . '</td>
                        <td>' . $item['approval_time'] . '</td>
                        <td>' . ($item['archive_status'] == 0 ? '未归档' : '已归档') . '</td>
                        <td>' . $overdue_days . '天</td>
                        <td>' . ($salesman_arr[$salesman]['dept_name'] ?? '') . '</td>
                        <td>' . ($salesman_arr[$salesman]['realname'] ?? '') . '</td>
                    </tr>';
            }
        }

        return $table;
    }
}