<?php

namespace App\Console\Commands;

use App\Jobs\UpdateBillSectionNumberJob;
use App\Models\Account;
use App\Models\MongoBillMonth;
use App\Models\MongoBillSection;
use App\Models\MongoLog;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

class GenMonthBillCommand extends Command
{
    use WechatExceptionTrait;

    /** @var string 命令 */
    public $signature = 'bill:generate-month
    {--range=month : month上个月的账单, all所有月份的账单, year 本年度所有月份}
    {--customer_id= : 客户ID}
    {--product_id= : 产品ID}
    {--account_id= : 账单ID}
    {--month= : 月份, 在设置此参数的之后,则--range选项则被忽略, 格式Ym}
    ';

    /** @var string 命令提示 */
    public $description = '生成月账单';

    /** @var   object 进度条 */
    private $bar;

    /** @var array 时间计费规则映射 */
    private $list_time_rule_mapping = [
        1 => 'day',
        2 => 'month',
        3 => 'year',
    ];

    /** @var string 调用量驱动的前缀 */
    private $driver_prefix = 'BillStat';

    /** @var array 账单费用明细, eg: 如果是天周期的话 则记录每一天的费用 */
    private $bill_money_detail;

    /** @var array 账单调用量明细 eg: 如果是天周期的话 则记录每一天的调用量 */
    private $bill_number_detail;

    /** @var int 一年按照固定的365计算 */
    private $days_in_years = 365;

    /** @var MongoBillSection 正在操作的section_item */
    private $section_item;

    /** @var array 操作的uuid的集合 */
    private $list_operator_uuids = [];

    /** @var string redis链接 */
    private $redis_bill_connection = 'db_backend';

    /** @var string 账单rollback的key */
    private $redis_bill_rollback_key = 'back_api_bill_rollback_key';

    /**
     * @throws \Exception
     */
    public function handle()
    {
        try {
            // 实际执行
            $this->handleDo();

            $msg = '生成账单成功 --range:' . $this->option('range');
            $this->outputInOneWay($msg);
        } catch (\Exception $e) {

            // 将本次命令生成的uuid,存储到redis中，给rollback备用
            $this->storeUuidForRollback();

            $msg = '生成账单失败 --range:' . $this->option('range') . ' uuid:' . $this->section_item->uuid . '  msg: ' . $e->getMessage() . ' at line ' . $e->getLine() . ' at file ' . $e->getFile();
            $this->outputInOneWay($msg, false);
        }
    }

    /**
     * 统一输出
     * @param string $msg
     * @param bool $success 是否选择成功的输出方式
     * @throws \Exception
     */
    private function outputInOneWay(string $msg, $success = true)
    {
        if ($success) {
            $this->output->success($msg);
            $this->wechatException($msg);
            return;
        }
        MongoLog::create(['error' => $msg, 'msg' => '遇到异常', 'uuid' => $this->section_item->uuid, 'action' => 'bill']);
        $this->output->error($msg);
        $this->wechatException($msg);
    }

    /**
     * 实际执行
     * @throws \Exception
     */
    private function handleDo()
    {
        // 检查输入
        $this->validateParams();

        // 获取要生成月份的账单片段
        $list_sections_months = $this->getSectionMonthList();

        // 生成月账单
        $this->WriteMonthBill($list_sections_months);

        // 将本次命令生成的uuid,存储到redis中，给rollback备用
        $this->storeUuidForRollback();
    }

    /**
     * 将本次命令生成的uuid,存储到redis中，给rollback备用
     */
    private function storeUuidForRollback()
    {
        $uuid_json = json_encode($this->list_operator_uuids, JSON_UNESCAPED_UNICODE);
        app('redis')->connection($this->redis_bill_connection)
            ->set($this->redis_bill_rollback_key, $uuid_json);
    }

    /**
     * 生成月账单
     * @param object $list_sections_months
     */
    private function WriteMonthBill($list_sections_months)
    {
        // 生成进度条
        $this->bar = $this->output->createProgressBar($list_sections_months->count());

        $list_sections_months->each(function ($section_item) {
            // 格式化计费单元
            $this->section_item = $section_item = $this->formatSectionItem($section_item);
	
			if (1 == $section_item->section_source->is_reset) {
				return;
			}
            
            // log
            $this->logWhenBegin();

            // 生成计费单元账单
            $this->writeOneSection($section_item);

            // 异步的更新本次调用产生的调用量的信息
            dispatch(new UpdateBillSectionNumberJob($this->section_item->uuid));

            $this->bar->advance();
            $this->logWhenOperatorEnd();
        });
        $this->bar->finish();
    }

    /**
     * 单元完结的时候的操作
     */
    private function logWhenOperatorEnd()
    {
        MongoLog::create(['msg' => '处理结束', 'uuid' => $this->section_item->uuid, 'action' => 'bill']);
        array_push($this->list_operator_uuids, $this->section_item->uuid);
    }

    /**
     * 开始处理log
     */
    private function logWhenBegin()
    {
        $msg = '开始处理';
        $uuid = $this->section_item->uuid;
        $section_item = $this->section_item->toArray();
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'section_item', 'action'));
    }

    /**
     * 格式化计费单元
     * @param $section_item
     * @return mixed
     */
    private function formatSectionItem($section_item)
    {
        $section_item->section_source = (object)$section_item->section_source;
        if (property_exists($section_item, 'section_refer')) {
            $section_item->section_refer = (object)$section_item->section_refer;
        }
        return $section_item;
    }

    /**
     * 处理一个计费片段
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeOneSection($section_item)
    {
        // 这条信息是否已经处理过了
        if ($this->determineThisSectionIsOver($section_item)) {
            $this->output->warning('uuid : ' . $section_item->uuid . ' 已经被处理过了,不再进行操作');
            return;
        }

        // 如果是通用的按时间
        if ($this->determineIfSectionIsCommonByDate($section_item)) {
            $this->writeWhenSectionIsCommonByDate($section_item);
            return;
        }

        // 如果通用的按用量 && 固定价格
        if ($this->determineIfSectionIdCommonByNumberFixed($section_item)) {
            $this->writeWhenSectionIsCommonByNumberFixed($section_item);
            return;
        }

        // 通用的按用量 && 累进阶梯
        if ($this->determineIfSectionIsCommonByNumberProgression($section_item)) {
            $this->writeWhenSectionIsCommonByNumberProgression($section_item);
            return;
        }

        // 通用的按用量 && 到达阶梯
        if ($this->determineSectionIsCommonByNumberReach($section_item)) {
            $this->writeWhenSectionIsCommonByNumberReach($section_item);
            return;
        }

        // 区分运营商按时间
        if ($this->determineSectionIsDateOperator($section_item)) {
            $this->writeWhenSectionIsDateOperator($section_item);
            return;
        }

        // 区分运营商按用量 && 固定价格
        if ($this->determineIfSectionIdOperatorByNumberFixed($section_item)) {
            $this->writeWhenSectionIsOperatorByNumberFixed($section_item);
            return;
        }

        // 区分运营商按用量 && 累进阶梯
        if ($this->determineIfSectionIsOperatorByNumberProgression($section_item)) {
            $this->writeWhenSectionIsOperatorByNumberProgression($section_item);
            return;
        }

        // 区分运营商按用量 && 到达阶梯
        if ($this->determineSectionIsOperatorByNumberReach($section_item)) {
            $this->writeWhenSectionIsOperatorByNumberReach($section_item);
            return;
        }
    }

    /**
     * 检查金额是否异常
     * @param float $money
     * @throws \Exception
     */
    private function checkMoney(float $money)
    {
        if ($money < 0) {
            $msg = 'uuid:' . $this->section_item->uuid . ' 计费金额：' . $money . '异常, 请排查';
            $this->wechatException($msg);
            $this->output->warning($msg);
        }
    }

    /**
     * 运营商的按用量 && 到达阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsOperatorByNumberReach($section_item)
    {
        // 阶梯周期
        switch ($section_item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                $this->writeWhenOperatorByNumberReachAndDayCycle($section_item);
                break;
            case 2:
                // 月周期
                $this->writeWhenOperatorByNumberReachAndMonthCycle($section_item);
                break;
            case 3:
                // 年周期
                $this->writeWhenOperatorByNumberReachAndYearCycle($section_item);
                break;
            default:
                // 无周期
                $this->writeWhenOperatorByNumberReachAndNoCycle($section_item);
        }
    }

    /**
     * 无周期 && 运营商 && 到达阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenOperatorByNumberReachAndNoCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenOperatorByNumberReachAndNoCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 无周期 && 通用 && 达到阶梯
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenOperatorByNumberReachAndNoCycle($section_item): array
    {
        $this->bill_number_detail = $this->bill_money_detail = [];

        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenOperatorReachAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenOperatorReachAndYearNoCycleSecond($section_item);

        // 计费 && log
        $money = $money_second - $money_first;
        $this->logWhenOperatorByNumberReachAndNoCycle(compact('money', 'money_first', 'money_second'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     *  无周期 && 通用 && 达到阶梯
     * @param array $content
     */
    private function logWhenOperatorByNumberReachAndNoCycle(array $content)
    {
        $msg = '场景: 无周期 && 通用 && 达到阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorReachAndYearNoCycleSecond($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearNoCycleSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorReachAndYearNoCycleSecond(compact('params_money', 'fee_number', 'fee_price'));

        return $this->bill_money_detail['second_part'] = app('bill.number.reach.operator')->genMoney($params_money);
    }

    /**
     *  无周期 && 通用 && 达到阶梯
     * @param array $content
     */
    private function logWhenOperatorReachAndYearNoCycleSecond(array $content)
    {
        $msg = '场景: 无周期 && 通用 && 达到阶梯 && 从开始计费时间到片段的结束时间(第二部分)';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段的结束时间(第二部分)
     * @param MongoBillSection $section_item
     * @throws \Exception
     * @return array
     */
    private function genParamsWhenOperatorReachAndYearNoCycleSecond($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorReachAndYearNoCycleFirst($section_item): float
    {
        // 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            $this->logWhenCommonProgressAndYearNoCycleFirst(['msg' => '开始计费时间和片段开始时间是一样的话,则第一部分的账单是0']);
            $this->bill_number_detail['first_part'] = 0;
            return $this->bill_money_detail['first_part'] = 0;
        }

        return $this->getMoneyWhenOperatorReachAndYearNoCycleFirstAndTimeDiff($section_item);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）时间不同
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorReachAndYearNoCycleFirstAndTimeDiff($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearNoCycleFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorReachAndYearNoCycleFirst(compact('params_money', 'fee_price', 'fee_number'));
        return $this->bill_money_detail['first_part'] = app('bill.number.reach.operator')->genMoney($params_money);
    }

    /**
     *  从开始计费时间到片段计费开始的前一天（第一部分）
     * @param array $content
     */
    private function logWhenOperatorReachAndYearNoCycleFirst(array $content)
    {
        $msg = '场景: 无周期 && 通用 && 达到阶梯 && 从开始计费时间到片段计费开始的前一天（第一部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorReachAndYearNoCycleFirst($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 运营商 && 达到阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenOperatorByNumberReachAndYearCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenOperatorByNumberReachAndYearCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 年周期 && 运营商 && 达到阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenOperatorByNumberReachAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {
            return $this->genMoneyWhenOperatorReachAndYearCycleInTwoYear($section_item);
        }

        // 片段在同一年
        return $this->genMoneyWhenOperatorReachAndYearCycleInSameYear($section_item);
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在同一年
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenOperatorReachAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            list($money, $fee_number) = $this->genMoneyWhenOperatorByNumberReach($section_item);
            return [$money, $fee_number, []];
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.number.reach.operator')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.number.reach.operator')->genMoney($params_money_before);

        // 设置属性
        $this->bill_number_detail = compact('fee_number_before', 'fee_number_end');
        $this->bill_money_detail = compact('money_end', 'money_before');
        $money = $money_end - $money_before;

        // log
        $this->logWhenOperatorReachAndYearCycleInSameYear(compact('fee_price', 'fee_number_before',
            'params_money_before', 'fee_number_end', 'params_money_end', 'money', 'money_before', 'money_end'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在同一年
     * @param array $content
     */
    private function logWhenOperatorReachAndYearCycleInSameYear(array $content)
    {
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在同一年';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenOperatorReachAndYearCycleInTwoYear($section_item): array
    {
        // 格式化价格
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenOperatorReachAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenOperatorReachAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenOperatorReachAndYearInTwoYearThird($section_item);

        // 计费 && log
        $money = $money_second - $money_first + $money_third;
        $this->logWhenOperatorReachAndYearCycleInTwoYear(compact('money', 'money_second', 'money_first', 'money_third'));
        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达
     * @param array $content
     */
    private function logWhenOperatorReachAndYearCycleInTwoYear(array $content)
    {
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @throws \Exception
     * @return float
     */
    private function getMoneyWhenOperatorReachAndYearInTwoYearThird($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearInTwoYearThird($section_item);
        $this->bill_number_detail['third_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorReachAndYearInTwoYearThird(compact('params_money', 'fee_price', 'fee_number'));

        return $this->bill_money_detail['third_part'] = app('bill.number.reach.operator')->genMoney($params_money);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param array $content
     *
     */
    private function logWhenOperatorReachAndYearInTwoYearThird(array $content)
    {
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达 && 新的部分的开始时间到片段结束时间(第三部分)';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorReachAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorReachAndYearInTwoYearSecond($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorReachAndYearInTwoYearSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorReachAndYearInTwoYearSecond(compact('fee_number', 'fee_price'));

        return $this->bill_money_detail['second_part'] = app('bill.number.reach.operator')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param array $content
     *
     */
    private function logWhenOperatorReachAndYearInTwoYearSecond(array $content)
    {
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达 && 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorReachAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorReachAndYearInTwoYearFirst($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 数量
        $params = $this->genParamsWhenOperatorReachAndYearInTwoYearFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorReachAndYearInTwoYearFirst(compact('fee_price', 'fee_number'));

        return $this->bill_money_detail['first_part'] = app('bill.number.reach.operator')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param array $content
     *
     */
    private function logWhenOperatorReachAndYearInTwoYearFirst(array $content)
    {
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达 && 某一轮询的开始时间到片段开始时间的前一天（第一部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorReachAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 月周期 && 运营商 && 达到阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenOperatorByNumberReachAndMonthCycle($section_item)
    {
        // 生成money && log
        list($money, $fee_number) = $this->genMoneyWhenOperatorByNumberReach($section_item);
        $this->logWhenOperatorByNumberReachAndMonthCycle(compact('money', 'fee_number'));
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 月周期 && 运营商 && 达到阶梯
     * @param array $content
     */
    private function logWhenOperatorByNumberReachAndMonthCycle(array $content)
    {
        $msg = '场景: 月周期 && 运营商 && 达到阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 生成Money
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenOperatorByNumberReach($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费 && log
        $money = app('bill.number.reach.operator')->genMoney($params_money);
        $this->logWhenOperatorByNumberReach(compact('money', 'params_money', 'fee_number', 'fee_price'));
        return [$money, $fee_number];
    }

    /**
     * 到达 && 运营商
     * @param array $content
     */
    private function logWhenOperatorByNumberReach(array $content)
    {
        $msg = '场景: 到达 && 运营商 && 数据支撑';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 天周期 && 运营商 && 到达阶梯
     * @param $section_item
     * @throws \Exception
     */
    private function writeWhenOperatorByNumberReachAndDayCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenOperatorByNumberReachAndDayCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 天周期 && 通用 && 达到阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenOperatorByNumberReachAndDayCycle($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReachOperator($section_item);

        // 计算这里每天的量
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 总价格
        $money = 0;
        $this->bill_money_detail = [];

        // 计算每天的费用
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$money) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $money += ($this->bill_money_detail[$day] = app('bill.number.reach.operator')->genMoney($params_money));
        });

        // log
        $this->logWhenOperatorByNumberReachAndDayCycle(compact('fee_price', 'fee_number', 'money'));

        return [$money, $fee_number, $this->bill_number_detail];
    }

    /**
     * 天周期 && 通用 && 达到阶梯
     * @param array $content
     */
    private function logWhenOperatorByNumberReachAndDayCycle(array $content)
    {
        $msg = '场景: 天周期 && 通用 && 达到阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 格式化运营商达到阶梯的价格
     * @param MongoBillSection $section_item
     * @return array
     */
    private function formatPriceForNumberReachOperator($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'reach_standard' => $item[0],
                'price_yd' => $item[1],
                'price_lt' => $item[2],
                'price_dx' => $item[3],
                'price_all_yd' => $item[4]??0,
            ];
            return $carry;
        }, []);
    }

    /**
     * 片段是否是(运营商的按用量 && 到达阶梯)
     * @param MongoBillSection $section_item
     * @return bool
     */
    private function determineSectionIsOperatorByNumberReach($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule == 2;
    }

    /**
     * 通用的按用量 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsOperatorByNumberProgression($section_item)
    {
        // 阶梯周期
        switch ($section_item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                $this->writeWhenOperatorByNumberProgressionAndDayCycle($section_item);
                break;
            case 2:
                // 月周期
                $this->writeWhenOperatorByNumberProgressionAndMonthCycle($section_item);
                break;
            case 3:
                // 年周期
                $this->writeWhenOperatorByNumberProgressionAndYearCycle($section_item);
                break;
            default:
                // 无周期
                $this->writeWhenOperatorByNumberProgressionAndNoCycle($section_item);
        }
    }

    /**
     * 无周期 && 运营商 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenOperatorByNumberProgressionAndNoCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenOperatorByNumberProgressionAndNoCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 无周期 && 运营商 && 累进阶梯
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenOperatorByNumberProgressionAndNoCycle($section_item): array
    {
        $this->bill_number_detail = $this->bill_money_detail = [];

        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenOperatorProgressAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenOperatorProgressAndYearNoCycleSecond($section_item);

        // 计费 && log
        $money = $money_second - $money_first;
        $this->logWhenOperatorByNumberProgressionAndNoCycle(compact('money', 'money_first', 'money_second'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 无周期 && 运营商 && 累进阶梯
     * @param array $content
     */
    private function logWhenOperatorByNumberProgressionAndNoCycle(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 无周期 && 运营商 && 累进阶梯';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorProgressAndYearNoCycleSecond($section_item): float
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonProgressAndYearNoCycleSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorProgressAndYearNoCycleSecond(compact('fee_number', 'fee_price', 'params_money'));

        return $this->bill_money_detail['second_part'] = app('bill.number.progression.operator')->genMoney($params_money);
    }

    /**
     *  从开始计费时间到片段的结束时间
     * @param array $content
     */
    private function logWhenOperatorProgressAndYearNoCycleSecond(array $content)
    {
        $msg = '场景: 无周期 && 运营商 && 累进阶梯 && 从开始计费时间到片段的结束时间（第二部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorProgressAndYearNoCycleFirst($section_item): float
    {
        // 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            $this->logWhenCommonProgressAndYearNoCycleFirst(['msg' => '开始计费时间和片段开始时间是一样的话,则第一部分的账单是0']);
            $this->bill_number_detail['first_part'] = 0;
            return $this->bill_money_detail['first_part'] = 0;
        }

        return $this->getMoneyWhenOperatorProgressAndYearNoCycleFirstAndTimeDiff($section_item);
    }

    /**
     * 计费开始时间和片段开始时间不同
     * @param $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorProgressAndYearNoCycleFirstAndTimeDiff($section_item): float
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearNoCycleFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 日志
        $this->logWhenOperatorProgressAndYearNoCycleFirst(compact('fee_price', 'fee_number'));

        return $this->bill_money_detail['first_part'] = app('bill.number.progression.operator')->genMoney($params_money);
    }

    /**
     * 无周期 && 运营商 && 累进阶梯 && 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param array $content
     */
    private function logWhenOperatorProgressAndYearNoCycleFirst(array $content)
    {
        $msg = '场景: 无周期 && 运营商 && 累进阶梯 && 从开始计费时间到片段计费开始的前一天（第一部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorProgressAndYearNoCycleFirst($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 运营商 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     *
     */
    private function writeWhenOperatorByNumberProgressionAndYearCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenOperatorByNumberProgressionAndYearCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 年周期 && 运营商 && 累进阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenOperatorByNumberProgressionAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {
            return $this->genMoneyWhenOperatorProgressionAndYearCycleInTwoYear($section_item);
        }

        return $this->genMoneyWhenOperatorProgressionAndYearCycleInSameYear($section_item);
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在同一年
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenOperatorProgressionAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            list($money, $fee_number) = $this->genMoneyWhenOperatorByNumberProgression($section_item);
            return [$money, $fee_number, []];
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.number.progression.operator')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.number.progression.operator')->genMoney($params_money_before);

        // 设置属性
        $this->bill_number_detail = compact('fee_number_before', 'fee_number_end');
        $this->bill_money_detail = compact('money_end', 'money_before');
        $money = $money_end - $money_before;

        // 日志
        $this->logWhenOperatorProgressionAndYearCycleInSameYear(compact('fee_price', 'fee_number_end',
            'money_end', 'fee_number_before', 'money_before', 'money', 'params_money_end', 'params_money_before'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在同一年
     * @param array $content
     */
    private function logWhenOperatorProgressionAndYearCycleInSameYear(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在同一年';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在不同的两年
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenOperatorProgressionAndYearCycleInTwoYear($section_item): array
    {
        // 格式化价格
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenOperatorProgressAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenOperatorProgressAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenOperatorProgressAndYearInTwoYearThird($section_item);

        // 计费
        $money = $money_second - $money_first + $money_third;
        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @throws \Exception
     * @return float
     */
    private function getMoneyWhenOperatorProgressAndYearInTwoYearThird($section_item): float
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearInTwoYearThird($section_item);
        $this->bill_number_detail['third_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorProgressAndYearInTwoYearThird(compact('fee_number', 'fee_price'));

        return $this->bill_money_detail['third_part'] = app('bill.number.progression.operator')->genMoney($params_money);
    }

    /**
     * @param array $content
     */
    private function logWhenOperatorProgressAndYearInTwoYearThird(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在不同的两年 && 新的部分的开始时间到片段结束时间(第三部分)';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorProgressAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorProgressAndYearInTwoYearSecond($section_item): float
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearInTwoYearSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorProgressAndYearInTwoYearSecond(compact('fee_price', 'fee_number'));

        return $this->bill_money_detail['second_part'] = app('bill.number.progression.operator')->genMoney($params_money);
    }

    /**
     * 年周期 && 运营商 && 按用量 && 在不同的两年 && 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param array $content
     */
    private function logWhenOperatorProgressAndYearInTwoYearSecond(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在不同的两年 && 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorProgressAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenOperatorProgressAndYearInTwoYearFirst($section_item): float
    {
        // 价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 调用量
        $params = $this->genParamsWhenOperatorProgressAndYearInTwoYearFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenOperatorProgressAndYearInTwoYearFirst(compact('fee_number', 'fee_price'));

        return $this->bill_money_detail['first_part'] = app('bill.number.progression.operator')->genMoney($params_money);
    }

    /**
     * @param array $content
     */
    private function logWhenOperatorProgressAndYearInTwoYearFirst(array $content)
    {
        $msg = '场景: 年周期 && 运营商 && 按用量 && 在不同的两年 && 某一轮询的开始时间到片段开始时间的前一天（第一部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenOperatorProgressAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 月周期 && 运营商 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenOperatorByNumberProgressionAndMonthCycle($section_item)
    {
        // log
        $this->logWhenOperatorByNumberProgressionAndMonthCycle([]);

        // 生成money
        list($money, $fee_number) = $this->genMoneyWhenOperatorByNumberProgression($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 月周期 && 运营商 && 累进阶梯
     * @param array $content
     */
    private function logWhenOperatorByNumberProgressionAndMonthCycle(array $content)
    {
        $msg = '场景: 月周期 && 运营商 && 累进阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 生成Money
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenOperatorByNumberProgression($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费 && log
        $money = app('bill.number.progression.operator')->genMoney($params_money);
        $this->logWhenOperatorByNumberProgression(compact('fee_price', 'fee_number', 'money'));

        return [$money, $fee_number];
    }

    /**
     * 月周期 && 运营商 && 累进阶梯 && 支撑
     * @param array $content
     */
    private function logWhenOperatorByNumberProgression(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景：运营商 && 累进阶梯 && 支撑服务';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 天周期 && 运营商 && 累进阶梯
     * @param $section_item
     * @throws \Exception
     */
    private function writeWhenOperatorByNumberProgressionAndDayCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenOperatorByNumberProgressionAndDayCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 天周期 && 运营商 && 累进阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenOperatorByNumberProgressionAndDayCycle($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgressionOperator($section_item);

        // 计算这里每天的量
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 总价格
        $money = 0;
        $this->bill_money_detail = [];

        // 计算每天的费用
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$money) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $money += ($this->bill_money_detail[$day] = app('bill.number.progression.operator')->genMoney($params_money));
        });

        // log
        $this->logWhenOperatorByNumberProgressionAndDayCycle(compact('fee_number', 'fee_price', 'money'));
        return [$money, $fee_number, $this->bill_number_detail];
    }

    /**
     * 天周期 && 运营商 && 累进阶梯
     * @param array $content
     */
    private function logWhenOperatorByNumberProgressionAndDayCycle(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 天周期 && 运营商 && 累进阶梯';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 格式化通用累进阶梯的价格
     * @param MongoBillSection $section_item
     * @return array
     */
    private function formatPriceForNumberProgressionOperator($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'left' => $item[0],
                'right' => $item[1],
                'price_yd' => $item[2],
                'price_lt' => $item[3],
                'price_dx' => $item[4],
				'price_all_yd' => $item[5]??0,
            ];
            return $carry;
        }, []);
    }

    /**
     * @param MongoBillSection $section_item
     * @return bool
     */
    private function determineIfSectionIsOperatorByNumberProgression($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule == 2;
    }

    /**
     *  如果区分运营商的按用量 && 固定价格
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsOperatorByNumberFixed($section_item)
    {
        // 生成money;
        list($money, $fee_number) = $this->genMoneyWhenOperatorByNumberFixed($section_item);
        $this->checkMoney($money);

        $params_bill = $this->genCreateParams($section_item, $money, $fee_number);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 生成money 区分运营商的按用量 && 固定价格
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenOperatorByNumberFixed($section_item): array
    {
        // 价格
        $fee_price = $this->formatPriceWhenOperatorAndNumberFixed($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费 && log
        $money = app('bill.number.fixed.operator')->genMoney($params_money);
        $this->logWhenSectionIsOperatorByNumberFixed(compact('fee_price', 'fee_number', 'money'));

        return [$money, $fee_number];
    }

    /**
     * 区分运营商的按用量 && 固定价格 日志
     * @param array $content
     */
    private function logWhenSectionIsOperatorByNumberFixed(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 区分运营商的按用量 && 固定价格';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 格式化价格
     * @param $section_item
     * @return array
     */
    private function formatPriceWhenOperatorAndNumberFixed($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return [
            'price_yd' => $fee_price[0],
            'price_lt' => $fee_price[1],
            'price_dx' => $fee_price[2],
			'price_all_yd' => $fee_price[3]??0
        ];
    }

    /**
     * 计费片段是否是运营商固定价格模式
     * @param $section_item
     * @return bool
     */
    private function determineIfSectionIdOperatorByNumberFixed($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule == 2;
    }

    /**
     * 通用的按用量 && 到达阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsDateOperator($section_item)
    {
        // 获取这段时间消费的金额
        $params_money = $this->genParamsForOperatorByDate($section_item);
        $money = app('bill.date.operator')->genMoney($params_money);

        // log
        $this->logWhenSectionIsDateOperator(compact('params_money', 'money'));
        $this->checkMoney($money);

        // 写月账单
        $params_bill = $this->genCreateParams($section_item, $money);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 通用的按用量 && 到达阶梯
     * @param array $content
     */
    private function logWhenSectionIsDateOperator(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 通用的按用量 && 到达阶梯';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 通用按日期生成参数
     * @param object $section_item
     * @return array
     */
    private function genParamsForOperatorByDate($section_item): array
    {
        // 价格格式化
        $fee_price = $this->formatPriceWhenOperatorDate($section_item);

        $fee_time_rule = $section_item->section_source->fee_time_rule;
        return [
            'section_begin' => $section_item->section_begin,
            'section_end' => $section_item->section_end,
            'fee_price' => $fee_price,
            'fee_time_rule' => $this->list_time_rule_mapping[$fee_time_rule] ?? '异常的fee_time_rule：' . $fee_time_rule,
            'month' => $section_item->month
        ];
    }

    /**
     * 格式化价格
     * @param $section_item
     * @return array
     */
    private function formatPriceWhenOperatorDate($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return [
            'price_yd' => $fee_price[0],
            'price_lt' => $fee_price[1],
            'price_dx' => $fee_price[2],
        ];
    }

    /**
     * 是否是区分运营商按时间的场景
     * @param MongoBillSection $section_item
     * @return bool
     */
    private function determineSectionIsDateOperator($section_item): bool
    {
        // 按时间
        $fee_method = $section_item->section_source->fee_method;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return 1 == $fee_method && $fee_price_rule == 2;
    }

    /**
     * 通用的按用量 && 到达阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsCommonByNumberReach($section_item)
    {
        // 阶梯周期
        switch ($section_item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                $this->writeWhenCommonByNumberReachAndDayCycle($section_item);
                break;
            case 2:
                // 月周期
                $this->writeWhenCommonByNumberReachAndMonthCycle($section_item);
                break;
            case 3:
                // 年周期
                $this->writeWhenCommonByNumberReachAndYearCycle($section_item);
                break;
            default:
                // 无周期
                $this->writeWhenCommonByNumberReachAndNoCycle($section_item);
        }
    }

    /**
     * 无周期 && 通用 && 到达阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenCommonByNumberReachAndNoCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenCommonByNumberReachAndNoCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 无周期 && 通用 && 达到阶梯
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenCommonByNumberReachAndNoCycle($section_item): array
    {
        $this->bill_number_detail = $this->bill_money_detail = [];

        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenCommonReachAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenCommonReachAndYearNoCycleSecond($section_item);

        // 计费 && log
        $money = $money_second - $money_first;
        $this->logWhenCommonByNumberReachAndNoCycle(compact('money', 'money_second', 'money_first'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 无周期 && 通用 && 达到阶梯
     * @param array $content
     */
    private function logWhenCommonByNumberReachAndNoCycle(array $content)
    {
        $msg = '场景: 无周期 && 通用 && 达到阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonReachAndYearNoCycleSecond($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearNoCycleSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonReachAndYearNoCycleSecond(compact('fee_number', 'fee_price'));
        return $this->bill_money_detail['second_part'] = app('bill.number.reach')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param array $content
     */
    private function logWhenCommonReachAndYearNoCycleSecond(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '场景: 无周期 && 通用 && 达到阶梯 && 从开始计费时间到片段的结束时间(第二部分)';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段的结束时间(第二部分)
     * @param MongoBillSection $section_item
     * @throws \Exception
     * @return array
     */
    private function genParamsWhenCommonReachAndYearNoCycleSecond($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonReachAndYearNoCycleFirst($section_item): float
    {
        // 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            $this->logWhenCommonProgressAndYearNoCycleFirst(['msg' => '开始计费时间和片段开始时间是一样的话,则第一部分的账单是0']);
            $this->bill_number_detail['first_part'] = 0;
            return $this->bill_money_detail['first_part'] = 0;
        }

        return $this->getMoneyWhenCommonReachAndYearNoCycleFirstAndTimeDiff($section_item);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分） 时间不同
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonReachAndYearNoCycleFirstAndTimeDiff($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearNoCycleFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonReachAndYearNoCycleFirst(compact('fee_price', 'fee_number'));
        return $this->bill_money_detail['first_part'] = app('bill.number.reach')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param array $content
     */
    private function logWhenCommonReachAndYearNoCycleFirst(array $content)
    {
        $msg = '场景: 无周期 && 通用 && 达到阶梯 && 从开始计费时间到片段计费开始的前一天（第一部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonReachAndYearNoCycleFirst($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 通用 && 达到阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenCommonByNumberReachAndYearCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenCommonByNumberReachAndYearCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 年周期 && 通用 && 达到阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenCommonByNumberReachAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {
            return $this->genMoneyWhenCommonReachAndYearCycleInTwoYear($section_item);
        }

        // 片段在同一年
        return $this->genMoneyWhenCommonReachAndYearCycleInSameYear($section_item);
    }

    /**
     * 年周期 && 通用 && 按用量 && 在同一年
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenCommonReachAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            list($money, $fee_number) = $this->genMoneyWhenCommonByNumberReach($section_item);
            return [$money, $fee_number, []];
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberReach($section_item);
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.number.reach')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.number.reach')->genMoney($params_money_before);

        // 设置属性
        $this->bill_number_detail = compact('fee_number_before', 'fee_number_end');
        $this->bill_money_detail = compact('money_end', 'money_before');
        $money = $money_end - $money_before;

        // log
        $this->logWhenCommonReachAndYearCycleInSameYear(compact('fee_price', 'fee_number_before',
            'money_before', 'fee_number_end', 'money_end', 'money', 'params_money_before', 'params_money_end'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 年周期 && 通用 && 按用量 && 在同一年
     * @param array $content
     */
    private function logWhenCommonReachAndYearCycleInSameYear(array $content)
    {
        $msg = '进入场景: 年周期 && 通用 && 按用量 && 在同一年';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 年周期 && 通用 && 按用量 && 在不同的两年 && 到达
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenCommonReachAndYearCycleInTwoYear($section_item): array
    {
        // 格式化价格
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenCommonReachAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenCommonReachAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenCommonReachAndYearInTwoYearThird($section_item);

        // 计费 && log
        $money = $money_second - $money_first + $money_third;
        $this->logWhenCommonReachAndYearCycleInTwoYear(compact('money_first', 'money_second', 'money_third', 'money'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     *  年周期 && 通用 && 按用量 && 在不同的两年 && 到达
     * @param array $content
     */
    private function logWhenCommonReachAndYearCycleInTwoYear(array $content)
    {
        $msg = '进入场景: 年周期 && 通用 && 按用量 && 在不同的两年 && 到达';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @throws \Exception
     * @return float
     */
    private function getMoneyWhenCommonReachAndYearInTwoYearThird($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearInTwoYearThird($section_item);
        $this->bill_number_detail['third_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonReachAndYearInTwoYearThird(compact('fee_price', 'fee_number'));

        return $this->bill_money_detail['third_part'] = app('bill.number.reach')->genMoney($params_money);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param array $content
     */
    private function logWhenCommonReachAndYearInTwoYearThird(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '进入场景: 年周期 && 通用 && 按用量 && 在不同的两年 && 到达 && 新的部分的开始时间到片段结束时间(第三部分)';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonReachAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonReachAndYearInTwoYearSecond($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 调用量
        $params = $this->genParamsWhenCommonReachAndYearInTwoYearSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonReachAndYearInTwoYearSecond(compact('fee_number', 'fee_price'));
        return $this->bill_money_detail['second_part'] = app('bill.number.reach')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param array $content
     */
    private function logWhenCommonReachAndYearInTwoYearSecond(array $content)
    {
        $msg = '进入场景: 年周期 && 通用 && 按用量 && 在不同的两年 && 到达 && 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonReachAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonReachAndYearInTwoYearFirst($section_item): float
    {
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 数量
        $params = $this->genParamsWhenCommonReachAndYearInTwoYearFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonReachAndYearInTwoYearFirst(compact('fee_price', 'fee_number', 'params_money'));

        return $this->bill_money_detail['first_part'] = app('bill.number.reach')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param array $content
     */
    private function logWhenCommonReachAndYearInTwoYearFirst(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '进入场景: 年周期 && 通用 && 按用量 && 在不同的两年 && 到达 && 某一轮询的开始时间到片段开始时间的前一天（第一部分）';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonReachAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 月周期 && 通用 && 达到阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenCommonByNumberReachAndMonthCycle($section_item)
    {
        // log
        $this->logWhenCommonByNumberReachAndMonthCycle([]);

        // 生成money
        list($money, $fee_number) = $this->genMoneyWhenCommonByNumberReach($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 日志
     * @param array $content
     */
    private function logWhenCommonByNumberReachAndMonthCycle(array $content)
    {
        $msg = '进入场景: 月周期 && 通用 && 达到阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 生成Money
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenCommonByNumberReach($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费 && log
        $money = app('bill.number.reach')->genMoney($params_money);
        $this->logWhenCommonByNumberReach(compact('fee_number', 'fee_price', 'params_money', 'money'));
        return [$money, $fee_number];
    }

    /**
     * 通用 && 到达 && 支撑日志
     * @param array $content
     */
    private function logWhenCommonByNumberReach(array $content)
    {
        $msg = '进入场景: 通用 && 到达 && 支撑';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 天周期 && 通用 && 到达阶梯
     * @param $section_item
     * @throws \Exception
     */
    private function writeWhenCommonByNumberReachAndDayCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenCommonByNumberReachAndDayCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 天周期 && 通用 && 到达阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenCommonByNumberReachAndDayCycle($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberReach($section_item);

        // 计算这里每天的量
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 总价格
        $money = 0;
        $this->bill_money_detail = [];

        // 计算每天的费用
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$money) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $money += ($this->bill_money_detail[$day] = app('bill.number.reach')->genMoney($params_money));
        });

        // log
        $this->logWhenCommonByNumberReachAndDayCycle(compact('fee_price', 'fee_number', 'money'));

        return [$money, $fee_number, $this->bill_number_detail];
    }

    /**
     * 天周期 && 通用 && 到达阶梯
     * @param array $content
     */
    private function logWhenCommonByNumberReachAndDayCycle(array $content)
    {
        $msg = '进入场景: 天周期 && 通用 && 到达阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 格式化通用达到阶梯的价格
     * @param MongoBillSection $section_item
     * @return array
     */
    private function formatPriceForNumberReach($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'reach_standard' => $item[0],
                'price' => $item[1],
            ];
            return $carry;
        }, []);
    }

    /**
     * 片段是否是(通用的按用量 && 到达阶梯)
     * @param MongoBillSection $section_item
     * @return bool
     */
    private function determineSectionIsCommonByNumberReach($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule != 2;
    }

    /**
     * 通用的按用量 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsCommonByNumberProgression($section_item)
    {
        // 阶梯周期
        switch ($section_item->section_source->fee_step_rule) {
            case 1:
                // 天周期
                $this->writeWhenCommonByNumberProgressionAndDayCycle($section_item);
                break;
            case 2:
                // 月周期
                $this->writeWhenCommonByNumberProgressionAndMonthCycle($section_item);
                break;
            case 3:
                // 年周期
                $this->writeWhenCommonByNumberProgressionAndYearCycle($section_item);
                break;
            default:
                // 无周期
                $this->writeWhenCommonByNumberProgressionAndNoCycle($section_item);
        }
    }

    /**
     * 无周期 && 通用 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenCommonByNumberProgressionAndNoCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenCommonByNumberProgressionAndNoCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 无周期 && 通用 && 累进阶梯
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenCommonByNumberProgressionAndNoCycle($section_item): array
    {
        $this->bill_number_detail = $this->bill_money_detail = [];

        // 从开始计费时间到片段计费开始的前一天
        $money_first = $this->getMoneyWhenCommonProgressAndYearNoCycleFirst($section_item);

        // 从开始计费时间到片段的结束时间
        $money_second = $this->getMoneyWhenCommonProgressAndYearNoCycleSecond($section_item);

        // 计费 && log
        $money = $money_second - $money_first;
        $this->logWhenCommonByNumberProgressionAndNoCycle(compact('money_first', 'money_second', 'money'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 无周期 && 通用 && 累进阶梯
     * @param array $content
     */
    private function logWhenCommonByNumberProgressionAndNoCycle(array $content)
    {
        $msg = '进入场景: 无周期 && 通用 && 累进阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段的结束时间
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonProgressAndYearNoCycleSecond($section_item): float
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearNoCycleSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonProgressAndYearNoCycleSecond(compact('fee_number', 'fee_price', 'params_money'));

        return $this->bill_money_detail['second_part'] = app('bill.number.progression')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段的结束时间 日志
     * @param array $content
     */
    private function logWhenCommonProgressAndYearNoCycleSecond(array $content)
    {
        $msg = '进入场景: 通用 && 累进 && 无周期 && 从开始计费时间到片段的结束时间';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段的结束时间(第二部分)
     * @param MongoBillSection $section_item
     * @throws \Exception
     * @return array
     */
    private function genParamsWhenCommonProgressAndYearNoCycleSecond($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonProgressAndYearNoCycleFirst($section_item): float
    {
        // 如果开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
        if ($section_item->section_begin == $section_item->start_day) {
            $this->logWhenCommonProgressAndYearNoCycleFirst(['msg' => '如果开始计费时间和片段开始时间是一样的话,则第一部分的账单是0']);
            $this->bill_number_detail['first_part'] = 0;
            return $this->bill_money_detail['first_part'] = 0;
        }

        return $this->getMoneyWhenCommonProgressAndYearNoCycleFirstAndTimeDiff($section_item);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）时间不同
     * @param $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonProgressAndYearNoCycleFirstAndTimeDiff($section_item): float
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearNoCycleFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonProgressAndYearNoCycleFirst(compact('fee_price', 'fee_number', 'params_money'));

        return $this->bill_money_detail['first_part'] = app('bill.number.progression')->genMoney($params_money);
    }

    /**
     * 从开始计费时间到片段计费开始的前一天（第一部分）
     * @param array $content
     */
    private function logWhenCommonProgressAndYearNoCycleFirst(array $content)
    {
        $msg = '进入场景: 从开始计费时间到片段计费开始的前一天（第一部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 从开始计费时间到片段计费开始的前一天(第一部分)
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonProgressAndYearNoCycleFirst($section_item): array
    {
        $section_begin = $section_item->start_day;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 年周期 && 通用 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     *
     */
    private function writeWhenCommonByNumberProgressionAndYearCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenCommonByNumberProgressionAndYearCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 年周期 && 通用 && 累进阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenCommonByNumberProgressionAndYearCycle($section_item): array
    {
        // 计费片段如果在两个跨度
        if ($this->determineInTwoYearFromStart($section_item)) {
            return $this->genMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item);
        }

        return $this->genMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item);
    }

    /**
     * 年周期 && 通用 && 按用量 && 在不同的两年
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item): array
    {
        // 格式化价格
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 某一轮询的开始时间到片段开始时间的前一天
        $money_first = $this->getMoneyWhenCommonProgressAndYearInTwoYearFirst($section_item);

        // 某一轮询的开始时间到新的部分开始时间的前一天
        $money_second = $this->getMoneyWhenCommonProgressAndYearInTwoYearSecond($section_item);

        // 新的部分的开始时间到片段结束时间
        $money_third = $this->getMoneyWhenCommonProgressAndYearInTwoYearThird($section_item);

        // 计费 && log
        $money = $money_second - $money_first + $money_third;
        $this->logWhenCommonProgressionAndYearCycleInTwoYear(compact('money_first', 'money_second', 'money_third', 'money'));

        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 年周期 && 通用 && 按用量 && 在不同的两年
     * @param array $content
     */
    private function logWhenCommonProgressionAndYearCycleInTwoYear(array $content)
    {
        $msg = '进入场景: 年周期 && 通用 && 按用量 && 在不同的两年';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @throws \Exception
     * @return float
     */
    private function getMoneyWhenCommonProgressAndYearInTwoYearThird($section_item): float
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearInTwoYearThird($section_item);
        $this->bill_number_detail['third_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonProgressAndYearInTwoYearThird(compact('fee_price', 'fee_number', 'params_money'));

        return $this->bill_money_detail['third_part'] = app('bill.number.progression')->genMoney($params_money);
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param array $content
     */
    private function logWhenCommonProgressAndYearInTwoYearThird(array $content)
    {
        $msg = '进入场景: 通用 && 累进 && 按年 && 片段不再同一年 && 新的部分的开始时间到片段结束时间(第三部分)';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 新的部分的开始时间到片段结束时间(第三部分)
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonProgressAndYearInTwoYearThird($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonProgressAndYearInTwoYearSecond($section_item): float
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearInTwoYearSecond($section_item);
        $this->bill_number_detail['second_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonProgressAndYearInTwoYearSecond(compact('fee_price', 'fee_number', 'params_money'));

        return $this->bill_money_detail['second_part'] = app('bill.number.progression')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param array $content
     */
    private function logWhenCommonProgressAndYearInTwoYearSecond(array $content)
    {
        $msg = '进入场景: 通用 && 累进 && 按年 && 片段不再同一年 && 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonProgressAndYearInTwoYearSecond($section_item)
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = $day_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param MongoBillSection $section_item
     * @return float
     * @throws \Exception
     */
    private function getMoneyWhenCommonProgressAndYearInTwoYearFirst($section_item): float
    {
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $params = $this->genParamsWhenCommonProgressAndYearInTwoYearFirst($section_item);
        $this->bill_number_detail['first_part'] = $fee_number = $this->numberForSectionByParams($section_item, $params);
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // log
        $this->logWhenCommonProgressAndYearInTwoYearFirst(compact('fee_price', 'fee_number', 'params_money'));

        return $this->bill_money_detail['first_part'] = app('bill.number.progression')->genMoney($params_money);
    }

    /**
     * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param array $content
     */
    private function logWhenCommonProgressAndYearInTwoYearFirst(array $content)
    {
        $msg = '进入场景: 通用 && 累进 && 按年 && 片段不再同一年 && 某一轮询的开始时间到片段开始时间的前一天（第一部分）';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsWhenCommonProgressAndYearInTwoYearFirst($section_item): array
    {
        list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);

        $section_begin = $day_begin;
        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 获取某一轮询的结束日期
     * @param MongoBillSection $section_item
     * @return array
     */
    private function getTheLastDayOfThisSection($section_item): array
    {
        list($section_begin, $section_end, $start_day) = [$section_item->section_begin, $section_item->section_end, $section_item->start_day];

        // 这里一定不会出现等于的情况，如果出现出现的话,那么这个片段是在一个循环
        $timestamp_end = strtotime($start_day) + 86400 * $this->days_in_years;
        $day_end = date('Ymd', $timestamp_end);
        $day_begin = $start_day;
        while (true) {
            if ($day_end > $section_begin && $day_end < $section_end) {
                break;
            }

            $day_begin = date('Ymd', strtotime('+1 day', strtotime($day_end)));
            $timestamp_end = strtotime($day_end) + 86400 * $this->days_in_years;
            $day_end = date('Ymd', $timestamp_end);
        }

        return [$day_begin, $day_end];
    }

    /**
     * 年周期 && 通用 && 按用量 && 在同一年
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item): array
    {
        // 如果计费开始时间和片段开始时间相同(那么正常就可以了)
        if ($section_item->start_day == $section_item->section_begin) {
            list($money, $fee_number) = $this->genMoneyWhenCommonByNumberProgression($section_item);
            return [$money, $fee_number, []];
        }

        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgression($section_item);
        $this->bill_money_detail = $this->bill_number_detail = [];

        // 开始计费到节点结束时候的调用量
        $params_end = $this->genParamsFromEndWhenSameYear($section_item);
        $fee_number_end = $this->numberForSectionByParams($section_item, $params_end);
        $params_money_end = array_merge(compact('fee_price'), $fee_number_end);
        $money_end = app('bill.number.progression')->genMoney($params_money_end);

        // 开始计费到节点开始时候的调用量
        $params_before = $this->genParamsFromBeginWhenSameYear($section_item);
        $fee_number_before = $this->numberForSectionByParams($section_item, $params_before);
        $params_money_before = array_merge(compact('fee_price'), $fee_number_before);
        $money_before = app('bill.number.progression')->genMoney($params_money_before);

        // 设置属性
        $this->bill_number_detail = compact('fee_number_before', 'fee_number_end');
        $this->bill_money_detail = compact('money_end', 'money_before');
        $money = $money_end - $money_before;

        // log
        $this->logWhenCommonProgressionAndYearCycleInSameYear(compact('fee_price', 'fee_number_before',
            'money_before', 'fee_number_end', 'money_end', 'money'));
        return [$money, $this->bill_number_detail, $this->bill_money_detail];
    }

    /**
     * 年周期 && 通用 && 按用量 && 在同一年
     * @param array $content
     */
    private function logWhenCommonProgressionAndYearCycleInSameYear(array $content)
    {
        $msg = '进入场景: 年周期 && 通用 && 按用量 && 累进 && 在同一年';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 开始计费到节点开始时候的参数 && 同一年
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsFromEndWhenSameYear($section_item): array
    {
        // 同一年的开始时间
        $section_begin = $this->genBeginDayWhenSameYear();

        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 开始计费到节点开始时候的参数 && 同一年
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsFromBeginWhenSameYear($section_item): array
    {
        //  在同一年的时候,获取第一阶段的开始时间
        $section_begin = $this->genBeginDayWhenSameYear();

        $section_end = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 在同一年的时候,获取第一阶段的开始时间
     * @return string
     */
    private function genBeginDayWhenSameYear(): string
    {
        list($section_begin, $start_day) = [$this->section_item['section_begin'], $this->section_item['start_day']];

        // 如果开始计费时间和片段时间是在一年
        $days_from_begin = (strtotime($section_begin) - strtotime($start_day)) / 86400;

        // 和365天求余数
        $times = floor($days_from_begin / $this->days_in_years);
        $diff_days = $this->days_in_years * $times;

        return date('Ymd', strtotime('+' . $diff_days . ' days', strtotime($start_day)));
    }

    /**
     * 计费片段的产品调用量
     * @param MongoBillSection $section_item
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function numberForSectionByParams($section_item, array $params): array
    {
        // 驱动名称
        $driver_name = $this->genStatDriverName($section_item);
        return app('bill.statistic')->driver($driver_name)->billStat($params);
    }

    /**
     * 本次计费片段是否包含了两年的时间
     * @param $section_item
     * @return bool
     */
    private function determineInTwoYearFromStart($section_item): bool
    {
        // 距离开始日期的天数
        list($section_begin, $section_end, $start_day) = [$section_item->section_begin, $section_item->section_end, $section_item->start_day];
        $days_from_begin = (strtotime($section_begin) - strtotime($start_day)) / 86400;
        $days_from_end = (strtotime($section_end) - strtotime($start_day)) / 86400;

        // 和365天求余数
        $remainder_from_begin = $days_from_begin % $this->days_in_years;
        $remainder_from_end = $days_from_end % $this->days_in_years;

        // 因为section_begin && section_end最多间隔一个月， 所以 如果结束的余数小于了开始的余数 则说明进入两个周期
        return $remainder_from_end < $remainder_from_begin;
    }

    /**
     * 天周期 && 通用 && 累进阶梯
     * @param $section_item
     * @throws \Exception
     */
    private function writeWhenCommonByNumberProgressionAndDayCycle($section_item)
    {
        // 生成money
        list($money, $fee_number, $money_detail) = $this->genMoneyWhenCommonByNumberProgressionAndDayCycle($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number, $money_detail, $this->bill_number_detail);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 天周期 && 通用 && 累进阶梯
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenCommonByNumberProgressionAndDayCycle($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgression($section_item);

        // 计算这里每天的量
        $fee_number = $this->numberSectionWhenDayCycle($section_item);

        // 总价格
        $money = 0;
        $this->bill_money_detail = [];

        // 计算每天的费用
        collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$money) {
            // 参数
            $params_money = array_merge(compact('fee_price'), $fee_number_item);

            // 计费
            $money += ($this->bill_money_detail[$day] = app('bill.number.progression')->genMoney($params_money));
        });

        // 日志
        $this->logWhenCommonAndProgressionAndDayCycle(compact('fee_number', 'fee_price', 'money'));
        return [$money, $fee_number, $this->bill_number_detail];
    }

    /**
     * 累进阶梯 按天 日志
     * @param array $content
     */
    private function logWhenCommonAndProgressionAndDayCycle(array $content)
    {
        $msg = '进入场景: 通用 && 累进 && 按天';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 日调用量
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function numberSectionWhenDayCycle($section_item): array
    {
        // 驱动名称
        $driver_name = $this->genStatDriverName($section_item);

        // 参数
        $params = $this->genParamsForStatDay($section_item);

        // 容器
        $list_container = [];
        $this->bill_number_detail = [];

        array_walk($params, function ($param) use (&$list_container, $driver_name) {
            $day = $param['section_begin'];
            $this->bill_number_detail[$day] = $list_container[$day] = app('bill.statistic')->driver($driver_name)->billStat($param);
        });

        return $list_container;
    }

    /**
     * 生成天周期的参数
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsForStatDay($section_item)
    {
        $begin = $section_item->section_begin;
        $end = $section_item->section_end;

        // 基础信息
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        $base_item = compact('apikey', 'uuid', 'fee_basis', 'fee_price_rule');

        // 容器
        $list_container = [];
        while ($begin <= $end) {
            $time_item = [
                'section_begin' => $begin,
                'section_end' => $begin
            ];
            $list_container[] = array_merge($base_item, $time_item);
            $begin = date('Ymd', strtotime('+1 day', strtotime($begin)));
        }

        return $list_container;
    }

    /**
     * 月周期 && 通用 && 累进阶梯
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenCommonByNumberProgressionAndMonthCycle($section_item)
    {
        // log
        $this->logWhenCommonProgressionAndMonthCycle();

        // 生成money
        list($money, $fee_number) = $this->genMoneyWhenCommonByNumberProgression($section_item);
        $this->checkMoney($money);

        // 计算
        $params_bill = $this->genCreateParams($section_item, $money, $fee_number);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     *  月周期 && 通用 && 累进阶梯日志
     */
    private function logWhenCommonProgressionAndMonthCycle()
    {
        $msg = '进入场景: 月周期 && 通用 && 累进阶梯';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 生成Money
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function genMoneyWhenCommonByNumberProgression($section_item): array
    {
        // 格式化价格
        $fee_price = $this->formatPriceForNumberProgression($section_item);

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费
        $money = app('bill.number.progression')->genMoney($params_money);

        // log
        $this->logWhenCommonProgression(compact('fee_price', 'fee_number', 'params_money', 'money'));

        return [$money, $fee_number];
    }

    /**
     * 通用 && 累进日志
     * @param array $content
     */
    private function logWhenCommonProgression(array $content)
    {
        $msg = '进入场景: 为通用累进提供支撑';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 格式化通用累进阶梯的价格
     * @param MongoBillSection $section_item
     * @return array
     */
    private function formatPriceForNumberProgression($section_item): array
    {
        $fee_price = json_decode($section_item->section_source->fee_price, true);
        return array_reduce($fee_price, function ($carry, $item) {
            $carry[] = [
                'left' => $item[0],
                'right' => $item[1],
                'price' => $item[2],
            ];
            return $carry;
        }, []);
    }

    /**
     * @param MongoBillSection $section_item
     * @return bool
     */
    private function determineIfSectionIsCommonByNumberProgression($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule != 2;
    }

    /**
     *  如果通用的按用量 && 固定价格
     * @param MongoBillSection $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsCommonByNumberFixed($section_item)
    {
        // 生成money && check
        list($money, $fee_number) = $this->genMoneyWhenCommonByNumberFixed($section_item);
        $this->checkMoney($money);

        $params_bill = $this->genCreateParams($section_item, $money, $fee_number);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 生成money
     * @param $section_item
     * @throws \Exception
     * @return array
     */
    private function genMoneyWhenCommonByNumberFixed($section_item): array
    {
        // 价格
        $fee_price = $section_item->section_source->fee_price;

        // 计费片段时间内的调用量
        $fee_number = $this->numberInTheSection($section_item);

        // 参数
        $params_money = array_merge(compact('fee_price'), $fee_number);

        // 计费 && log
        $money = app('bill.number.fixed')->genMoney($params_money);
        $this->logWhenCommonAndNumberFixed(compact('fee_number', 'fee_price', 'params_money', 'money'));

        return [$money, $fee_number];
    }

    /**
     * 通用 && 按用量 && 固定单价日志
     * @param array $content
     */
    private function logWhenCommonAndNumberFixed(array $content)
    {
        $uuid = $this->section_item->uuid;
        $msg = '进入了通用 && 按用量 && 固定单价的流程';
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 计费片段的产品调用量
     * @param MongoBillSection $section_item
     * @return array
     * @throws \Exception
     */
    private function numberInTheSection($section_item): array
    {
        // 驱动名称
        $driver_name = $this->genStatDriverName($section_item);

        // 参数
        $params = $this->genParamsForStatDriver($section_item);

        return app('bill.statistic')->driver($driver_name)->billStat($params);
    }

    /**
     * 获取调用量驱动的参数
     * @param $section_item
     * @return array
     * @throws \Exception
     */
    private function genParamsForStatDriver($section_item): array
    {
        $section_begin = $section_item->section_begin;
        $section_end = $section_item->section_end;
        $uuid = $section_item->uuid;
        $fee_basis = $section_item->section_source->fee_basis;
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        // 获取apikey
        $apikey = $this->getApikeyForSection($section_item);
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * @param MongoBillSection $section_item
     * @return string
     * @throws \Exception
     */
    private function getApikeyForSection($section_item): string
    {
        $account_id = $section_item->account_id;
        $account = Account::getOneItemByCondition(compact('account_id'), 'apikey');
        if (!$account || !$account->apikey) {
            throw new \Exception('uuid : ' . $section_item->uuid . ' account_id:' . $section_item->account_id . '没有找到合法的账号');
        }
        return $account->apikey;
    }

    /**
     * 获取调用量驱动的名称
     * @param MongoBillSection $section_item
     * @return string
     */
    private function genStatDriverName($section_item): string
    {
        $product_id = $section_item->product_id;
        return $this->driver_prefix . $product_id;
    }

    /**
     * 计费片段是否是通用固定价格模式
     * @param $section_item
     * @return bool
     */
    private function determineIfSectionIdCommonByNumberFixed($section_item): bool
    {
        // 计费方式(时间用量)
        $fee_method = $section_item->section_source->fee_method;

        // 用量计费规则
        $fee_amount_rule = $section_item->section_source->fee_amount_rule;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;

        return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule != 2;
    }

    /**
     * @param object $section_item
     * @throws \Exception
     */
    private function writeWhenSectionIsCommonByDate($section_item)
    {
        // 获取这段时间消费的金额
        $params_money = $this->genParamsForCommonByDate($section_item);
        $money = app('bill.date')->genMoney($params_money);

        // log && 检查账单
        $this->logWhenCommonByDate(compact('params_money', 'money'));
        $this->checkMoney($money);

        // 写月账单
        $params_bill = $this->genCreateParams($section_item, $money);
        MongoBillMonth::writeOneItem($params_bill);
    }

    /**
     * 通用 && 按日期的流程
     * @param array $content
     */
    private function logWhenCommonByDate(array $content)
    {
        $msg = '进入通用按日期的场景, 生成参数 && 开始计费';
        $uuid = $this->section_item->uuid;
        $action = 'bill';
        MongoLog::create(compact('msg', 'uuid', 'content', 'action'));
    }

    /**
     * 生成单元
     * @param $section_item
     * @param float $money
     * @param null $section_number
     * @param array $money_detail
     * @param array $number_detail
     * @return array
     */
    private function genCreateParams($section_item, float $money, $section_number = null, $money_detail = [], $number_detail = []): array
    {
        return [
            'uuid' => $section_item->uuid,
            'month' => $section_item->month,
            'account_id' => $section_item->account_id,
            'product_id' => $section_item->product_id,
            'customer_id' => $section_item->customer_id,
            'section_source' => $section_item->section_source,
            'section_begin' => $section_item->section_begin,
            'section_end' => $section_item->section_end,
            'money' => $money,
            'section_number' => $section_number,
            'money_detail' => $money_detail,
            'number_detail' => $number_detail
        ];
    }

    /**
     * 通用按日期生成参数
     * @param object $section_item
     * @return array
     */
    private function genParamsForCommonByDate($section_item): array
    {
        $fee_time_rule = $section_item->section_source->fee_time_rule;
        return [
            'section_begin' => $section_item->section_begin,
            'section_end' => $section_item->section_end,
            'fee_price' => $section_item->section_source->fee_price,
            'fee_time_rule' => $this->list_time_rule_mapping[$fee_time_rule] ?? '异常的fee_time_rule：' . $fee_time_rule,
            'month' => $section_item->month
        ];
    }

    /**
     * 是否是通用时间
     * @param object $section_item
     * @return bool
     */
    private function determineIfSectionIsCommonByDate($section_item)
    {
        // 按时间
        $fee_method = $section_item->section_source->fee_method;

        // 模式(标准1 运营商2)
        $fee_price_rule = $section_item->section_source->fee_price_rule;
        return 1 == $fee_method && $fee_price_rule != 2;
    }

    /**
     * 是否已经处理过了
     * @param $section_item
     * @return bool
     */
    private function determineThisSectionIsOver($section_item)
    {
        $uuid = $section_item->uuid;
        return (bool)MongoBillMonth::getOneItemByCondition(compact('uuid'));
    }


    /**
     * @throws \Exception
     */
    private function validateParams()
    {
        // 检查 range
        $range = $this->option('range');
        if (!in_array($range, ['month', 'year', 'all'])) {
            throw new \Exception('range选项输入错误');
        }

        // 检查 month
        $month = $this->option('month');
        if ($month && strpos($month, '-')) {
            throw new \Exception('month的格式必须是Ym');
        }

    }

    /**
     * 获取要生成月份的账单片段
     * @throws \Exception
     */
    private function getSectionMonthList()
    {
        // 条件
        $where = $this->genParamsForSectionList();

        // 列表
        return MongoBillSection::getListByCondition($where);
    }

    /**
     * 为账单配置列表生成条件
     * @return array
     * @throws \Exception
     */
    private function genParamsForSectionList(): array
    {
        // 基本限制
        list($customer_id, $product_id, $account_id, $month) = [
            trim($this->option('customer_id')),
            trim($this->option('product_id')),
            trim($this->option('account_id')),
            trim($this->option('month')),
        ];

        $where = array_filter(compact('customer_id', 'product_id', 'account_id', 'month'), function($item){
            return $item;
        });

        if (array_key_exists('product_id', $where)) {
            $where['product_id'] = (int) $where['product_id'];
        }

        if (array_key_exists('month', $where)) {
            return $where;
        }

        $range = $this->option('range');
        // 时间限制
        switch ($range) {
            case "month":
                $where['month'] = date('Ym', strtotime('first day of last month'));
                return $where;
                break;
            case "year":
                $where['month'] = [
                    '$gte' => date('Y') . '01',
                    '$lt' => date('Ym')
                ];
                return $where;
                break;
            case "all":
                $where['month'] = [
                    '$lt' => date('Ym')
                ];
                return $where;
                break;
            default:
                throw new \Exception('不支持的range类型 --range=' . $range);
        }
    }
}
