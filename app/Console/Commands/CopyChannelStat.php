<?php

namespace App\Console\Commands;

use App\Models\Channel;
use App\Models\ChannelInterface;
use App\Models\StatisticsInterfaceUsage;
use Illuminate\Console\Command;

// 复制运营商分支渠道统计
class CopyChannelStat extends Command
{
    // 定义命令签名
    protected $signature = 'copy_channel_stat
	{--start_date= : 开始日期（格式Ymd）}
	{--end_date= : 结束日期（格式Ymd）}
	{--channel_id= : 新渠道ID}';

    // 定义命令描述
    protected $description = '复制运营商分支渠道统计';

    // 命令处理逻辑
    public function handle()
    {
        $start_date = $end_date = date('Ymd', strtotime('-1 day'));

        // 从输入参数中获取日期和渠道ID，如果没有提供则使用默认值
        $start_date = $this->input->getOption('start_date') ?: $start_date;
        $end_date = $this->input->getOption('end_date') ?: $end_date;
        $channel_id = $this->input->getOption('channel_id') ?: 0;

        // 验证日期格式
        if (!preg_match('/^\d{8}$/', $start_date) || !preg_match('/^\d{8}$/', $end_date)) {
            $this->output->error('日期格式错误');
            return;
        }
        $this->copyChannelStat($start_date, $end_date, $channel_id);
    }

    public function copyChannelStat($start_date, $end_date, $channel_id)
    {
        // 定义渠道映射，键为分支渠道ID，值为被复制的渠道ID
        $channel_map = [
            //501 => [1], // 三数
            //502 => [8, 40, 42], // 诸天
            503 => [25], // 雄源
        ];

        // 如果提供了渠道ID，则验证并更新渠道映射
        if ($channel_id && !isset($channel_map[$channel_id])) {
            $this->error('渠道ID错误,请传入以下渠道ID【501对应联通， 502对应移动，503对应广东】');
            return;
        }
        if ($channel_id) {
            $channel_map = [$channel_id => $channel_map[$channel_id]];
        }

        // 获取渠道列表和渠道ID与名称的映射
        $channel_list = Channel::getChannelByChannelIdS(array_keys($channel_map));
        $channel_id_name = array_column($channel_list, 'name', 'channel_id');

        // 遍历渠道映射，复制统计信息
        foreach ($channel_map as $channel_id => $channel_ids) {
            $insert_count = $exist_count = 0;
            // 根据渠道ID获取接口ID列表
            $interface_ids = $this->getIids($channel_ids);
            // 根据接口ID和日期范围获取统计列表
            $stat_list = StatisticsInterfaceUsage::getDataByInterface($interface_ids, $start_date, $end_date);
            $this->output->success($channel_id . '数据总条数：' . count($stat_list));
            if (!$stat_list) {
                continue;
            }
            // 获取旧渠道接口列表
            $interface_list = ChannelInterface::getInterfaceByChannelIds($channel_ids);
            $source_interface_list = array_column($interface_list, 'name', 'id');
            // 获取新渠道接口列表
            $interface_list = ChannelInterface::getInterfaceByChannelIds([$channel_id]);
            $interface_list = array_column($interface_list, 'id', 'name');

            foreach ($stat_list as $item) {
                if (!isset($interface_list[$source_interface_list[$item['interface_id']]])) {
                    echo $item['id'] . ' 未找到对应接口ID' . PHP_EOL;
                    continue;
                }
                $item['iid'] = $item['interface_id'];
                $item['interface_id'] = $interface_list[$source_interface_list[$item['interface_id']]];
                $item['operator'] = $channel_id_name[$channel_id];
                $item['create_time'] = strtotime($item['create_time']);
                $item['update_time'] = strtotime($item['update_time']);
                $where = [
                    ['source', '=', $item['source']],
                    ['apikey', '=', $item['apikey']],
                    ['operator', '=', $item['operator']],
                    ['product_id', '=', $item['product_id']],
                    ['node', '=', $item['node']],
                    ['date', '=', $item['date']],
                    ['interface_id', '=', $item['interface_id']],
                ];
                // 如果记录已存在，则跳过
                if (StatisticsInterfaceUsage::where($where)->exists()) {
                    $exist_count++;
                    continue;
                }
                unset($item['id']);
                // 插入新记录
                if (StatisticsInterfaceUsage::insert($item)) {
                    $insert_count++;
                }
            }
            $this->output->success($channel_id . '写入成功总条数：' . $insert_count);
            $this->output->success($channel_id . '表中已存在，跳过写入总条数：' . $exist_count);
        }
    }

    // 根据渠道ID获取接口ID
    protected function getIids($channel_ids)
    {
        return array_column(ChannelInterface::getListByCondition([], ['id'], $channel_ids)->toArray(), 'id');
    }

}
