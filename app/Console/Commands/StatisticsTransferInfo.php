<?php

namespace App\Console\Commands;


use App\Console\Commands\CacheDriver\CacheDriverFacade;
use App\Models\MongoStatis;
use App\Models\Product;
use App\Models\ShortcutPackage;
use App\Models\StatisticsCustomerTogetherCallUsage;
use App\Models\StatisticsCustomerUsage;
use Illuminate\Console\Command;
use Symfony\Component\Console\Helper\ProgressBar;

/**
 * Class TempTransferStatistics 转移历史客户调用量统计
 * @package App\Console\Commands
 */
class StatisticsTransferInfo extends Command
{
    protected $signature = 'statistics:transfer_info
	{--product_id=101,105,104,601,604,401,501 : 转移的那个产品ID的统计数据，多个产品以,隔开}
	{--apikey= : 转移的那个账号的统计数据，多个账号以,隔开}
	{--start_date= : 从那天开始转移，Ymd，默认今天}
	{--end_date= : 从那天截止转移，Ymd，默认今天}';

    //664
    /**
     * @var array 产品ID
     */
    protected $productIds;
    /**
     * @var integer 开始日期
     */
    protected $startDate;
    /**
     * @var integer 截止日期
     */
    protected $endDate;

    /**
     * @var array 账号唯一标识apikey
     */
    protected $apikey;

    /**
     * @var ProgressBar 进度条
     */
    protected $progressBar;

    public function handle()
    {
        try {
            $this->checkParams();

            $this->transferData();
        } catch (\Exception $exception) {
	        sendCommandExceptionNotice($this, $exception);
        }
    }

    /**
     * 校验参数
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/24 14:28
     *
     * @return boolean
     */
    protected function checkParams()
    {
        $productIds = $this->input->getOption('product_id');
        if (empty($productIds)) {
            $productIds = Product::pluck('product_id')
                ->toArray();
        } else {
            $productIds = explode(',', $productIds);
            if (in_array(210, $productIds)) {
                $productIds[] = 1000;
            }

            $productIds = array_merge($productIds, Product::whereIn('father_id', $productIds)
                ->pluck('product_id')
                ->toArray());
        }

        $this->productIds = array_map('intval', $productIds);

        $startDate = $this->input->getOption('start_date');
        $this->startDate = $startDate ?: date('Ymd');

        if (!preg_match('/^\d{8}$/', $this->startDate)) {
            $this->output->error('开始日期格式不正确');

            return false;
        }

        $endDate = $this->input->getOption('end_date');
        $this->endDate = $endDate ?: date('Ymd');
        if (!preg_match('/^\d{8}$/', $this->endDate)) {
            $this->output->error('截止日期格式不正确');

            return false;
        }

        //如果凌晨0点--1点执行命令，则操作的是前一天的数据
        if ((time() - strtotime(date('Ymd'))) < 3600) {
            $this->startDate = $this->endDate = date('Ymd', strtotime('-1 days'));
        }

        if ($this->endDate < $this->startDate) {
            $this->output->error('截止日期小于开始日期，任务失败');
        }

        if ($this->input->getOption('apikey')) {
            $this->apikey = explode(',', $this->input->getOption('apikey'));
        }

        return true;
    }

    /**
     * 迁移数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/25 17:52
     *
     * @return void
     */
    protected function transferData()
    {
        $count = MongoStatis::whereIn('product_id', $this->productIds)
            ->where('amount_date', '>=', $this->startDate)
            ->where('amount_date', '<=', $this->endDate)
            ->where(function ($query) {
                if ($this->apikey) {
                    $query->whereIn('apikey', $this->apikey);
                }
            })
            ->count();
        $this->progressBar = $this->output->createProgressBar($count);

        $serial = 0;
        MongoStatis::whereIn('product_id', $this->productIds)
            ->where('amount_date', '>=', $this->startDate)
            ->where('amount_date', '<=', $this->endDate)
            ->where(function ($query) {
                if ($this->apikey) {
                    $query->whereIn('apikey', $this->apikey);
                }
            })
            ->chunk(1000, function ($data) use (&$serial) {
                $data = $data->toArray();
                foreach ($data as $item) {
                    $product_id = $item['product_id'];
                    if ($this->isGoldShield($product_id)) {
                        //金盾
                        $this->transferGoldShieldData($item);
                    } else if ($this->isBmy($product_id)) {
                        //邦秒验
                        $this->transferBmyData($item);
                    } else if ($this->isBxfShort($product_id)) {
                        //邦信分快捷版
                        $this->transferBxfShortData($item);
                    } else if (101 == $product_id) {
                        //邦信分详单版V1
                        $this->transferBxfBillV1Data($item);
                    } else if (105 == $product_id) {
                        //邦信分详单版V2
                        $this->transferBxfBillV2Data($item);
                    } else if (104 == $product_id) {
                        //邦秒配详单版
                        $this->transferBmpBillData($item);
                    } else if (601 == $product_id) {
                        //邦秒配单号版
                        $this->transferBmpSingleData($item);
                    } else if (604 == $product_id) {
                        //批量版邦秒配
                        $this->transferBmpBatchData($item);
                    } else if (401 == $product_id) {
                        //邦企查
                        $this->transferBqcData($item);
                    } else if (501 == $product_id) {
                        //邦信分私有云
                        $this->transferBxfPrivateCloudData($item);
                    } else if (612 == $product_id) {
                        //金盾贷前
                        $this->transfer612Data($item);
                    } else if (616 == $product_id) {
                        //616
                        $this->transfer616Data($item);
                    } else if (664 == $product_id) {
                        //664
                        $this->transfer664Data($item);
                    } else if (in_array($product_id, [613, 614, 801])) {
                        //已经没有客户在使用了，可以不导入
                    } else {
                        $this->output->error("未知的产品ID{$product_id}");
                    }
                    $this->progressBar->advance(1);
                    $serial++;
                }
            });


        $this->progressBar->finish();
        $this->output->success("迁移成功");

    }

    /**
     * 判断是否为金盾产品
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/25 17:57
     *
     * @param $product_id integer 产品ID
     *
     * @return boolean
     */
    protected function isGoldShield($product_id)
    {
        if (615 == $product_id) {
            return true;
        }
        $subProductIds = (new CacheDriverFacade())->get615ChildrenProductId();
        if (in_array($product_id, $subProductIds)) {
            return true;
        }

        return false;
    }

    /**
     * 转移金盾产品统计
     * 20200923及之后日期的量是新推送的，之前的需要导入
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/25 18:02
     *
     * @param $data array 原始统计数据
     *
     * @return void
     */
    protected function transferGoldShieldData($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        //20201020及之后日期的量是新推送的，之前的需要导入
        if ($date >= 20201020) {
            throw new \Exception("金盾统计数据不可导入20201020之后的数据");
        }

        //615的话
        if (615 == $product_id) {
            //cus_succ 先上的线，所以如果直接使用all字段，可能导致查得率非常低
            $result['total'] = $stat_data['cus_all']??($stat_data['cus_succ']??$stat_data['all']);
            $result['success'] = $stat_data['cus_succ']??$stat_data['succ'];
        } else {
            $result['total'] = $stat_data['all'];
            $result['success'] = $stat_data['succ'];
        }

        if (0 == $result['total']) {
            return;
        }

        $result['valid'] = array_get($stat_data, 'cdnums', $result['success']);
        $result['product_id'] = $product_id;
        $result['node'] = $data['node_area'];
        $result['apikey'] = $data['apikey'];
        $result['cache'] = 0;
        $result['operator'] = '';
        $result['date'] = $date;


        $this->create($result);

        //金盾父产品需要迁移打包量
        if (615 == $result['product_id']) {
            $togetherCallResult = [
                'apikey' => $result['apikey'],
                'product_id' => $result['product_id'],
                'node' => $result['node'],
                'operator' => $result['operator'],
                'date' => $result['date'],
                'sub_product_number' => 1,
                'total' => $result['total'],
                'success' => $result['success'],
                'valid' => array_get($stat_data, 'succ661662', 0),
            ];

            $this->createPackage($togetherCallResult);
        }
    }

    /**
     * 判断是否为邦秒验产品
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 11:22
     *
     * @param $product_id integer 产品ID
     *
     * @return boolean
     */
    protected function isBmy($product_id)
    {
        if (200 == $product_id) {
            return true;
        }
        $subProductIds = (new CacheDriverFacade())->get200ChildrenProductId();
        if (in_array($product_id, $subProductIds)) {
            return true;
        }

        return false;
    }

    /**
     * 转移邦秒验产品统计
     * 20200928及之后日期的量是新推送的，之前的需要导入
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 11:25
     *
     * @param $data array 原始统计数据
     *
     * @return void
     */
    protected function transferBmyData($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        //20200923及之后日期的量是新推送的，之前的需要导入
        if ($date >= 20200928) {
            throw new \Exception("邦秒验统计数据不可导入20200928之后的数据");
        }
        if (200 == $data) {
            return;
        }

        //区分运营商
        //移动
        $result = [
            'apikey' => $data['apikey'],
            'product_id' => $product_id,
            'node' => $data['node_area'],
            'operator' => 'CMCC',
            'date' => $date,
            'total' => $stat_data['yd_all'],
            'success' => $stat_data['yd_all'],
            'valid' => $stat_data['yd_succ'],
            'cache' => $stat_data['yd_cache'],
        ];
        if (0 != $result['total']) {
            $this->create($result);
        }

        //联通
        $result = [
            'apikey' => $data['apikey'],
            'product_id' => $product_id,
            'node' => $data['node_area'],
            'operator' => 'CUCC',
            'date' => $date,
            'total' => $stat_data['lt_all'],
            'success' => $stat_data['lt_all'],
            'valid' => $stat_data['lt_succ'],
            'cache' => $stat_data['lt_cache'],
        ];
        if (0 != $result['total']) {
            $this->create($result);
        }

        //电信
        $result = [
            'apikey' => $data['apikey'],
            'product_id' => $product_id,
            'node' => $data['node_area'],
            'operator' => 'CTCC',
            'date' => $date,
            'total' => $stat_data['dx_all'],
            'success' => $stat_data['dx_all'],
            'valid' => $stat_data['dx_succ'],
            'cache' => $stat_data['dx_cache'],
        ];
        if (0 != $result['total']) {
            $this->create($result);
        }
    }

    /**
     * 判断是否为邦信分快捷版产品
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 14:03
     *
     * @param $product_id integer 产品ID
     *
     * @return bool
     */
    protected function isBxfShort($product_id)
    {
        if (210 == $product_id) {
            return true;
        }

        return in_array($product_id, [
            251,
            252,
            253,
            254,
            255,
            256,
            257,
            258,
            259,
            260,
            261,
            262,
            263,
            264,
            265,
            266,
            267,
            268,
            269,
            270,
            271,
            272,
            273,
            274,
            275,
            276,
            277,
            278,
            279,
            280,
            281,
            282,
            283,
            284,
            285,
            286,
            287,
            288,
            241,
            242,
            243,
            244,
            245,
            246,
            247,
            289,
            290,
            291,
            292,
            293,
            294,
            295,
            296,
            297,
            298,
            711,
            299,
        ]);
        //		$cache         = new CacheDriverFacade();
        //		$subProductIds = $cache->get210ChildrenProductId();
        //		if (in_array($product_id, $subProductIds)) {
        //			return true;
        //		}
        //		//1000的也需要处理
        //		$subProductIds = $cache->get1000ChildrenProductId();
        //		if (in_array($product_id, $subProductIds)) {
        //			return true;
        //		}
        //
        //		return false;
    }

    /**
     * 转移邦信分快捷版产品统计
     * 20200928及之后日期的量是新推送的，之前的需要导入
     *
     * 20200723及之后修改过一版， 原来仅仅只有移动、联通、电信的运营商，上线后拆分为各省移动、联通、电信、全国移动的运营商
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 11:25
     *
     * @param $data array 原始统计数据
     *
     * @return void
     */
    protected function transferBxfShortData($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        //20200923及之后日期的量是新推送的，之前的需要导入
        //		if ($date >= 20200928) {
        //			throw new \Exception("邦信分快捷版统计数据不可导入20200928之后的数据");
        //		}

        //20200723及之后的统计数据，都将统计量统计到了各个省渠道上
        if ($date >= 20200723) {
            $operators = [
                'ALLCMCC' => 'allcmcc',    //全国移动
                'BJCMCC' => 'bjcmcc',    //北京移动
                'JSCMCC' => 'jscmcc',    //江苏移动
                'SCCMCC' => 'sccmcc',    //四川移动
                'SDCMCC' => 'sdcmcc',      //山东移动
                'HBCMCC' => 'hbcmcc',    //河北移动
                'CUCC' => 'liantong',    //联通
                'CTCC' => 'dianxin'        //电信
            ];
        } else {
            $operators = ['HBCMCC' => 'yidong', 'CUCC' => 'liantong', 'CTCC' => 'dianxin'];
        }

        $result = $this->imposeBxfShortData($data, $operators);
        if (empty($result)) {
            return;
        }

        array_walk($result, function ($item) {
            $this->create($item);
        });

        //如果是快捷版父产品，需要导入打包量
        if (210 == $product_id) {
            $apikey = $data['apikey'];
            $product_id = 210;
            $node = 'shenzhen';
            $date = date('Ymd', strtotime($data['amount_date']));

            if ('5039f1d8f90eb7162b1d1ebf15686d54' == $apikey) {
                //HBCMCC的打包数据
                $total = ShortcutPackage::where('apikey', '5039f1d8f90eb7162b1d1ebf15686d54')
                    ->where('normal_number', 5)
                    ->where('request_date', $date)
                    ->count();
                if ($total != 0) {
                    $success = $valid = $total;
                    $sub_product_number = 5;
                    $operator = 'HBCMCC';
                    $data = compact('apikey', 'product_id', 'node', 'operator', 'date', 'sub_product_number', 'total', 'success', 'valid');
                    $this->createPackage($data);
                }


            } else if ('89a95da508313950066ef22180e60e4e' == $apikey) {
                //HBCMCC的打包数据
                $total = ShortcutPackage::where('apikey', '89a95da508313950066ef22180e60e4e')
                    ->where('normal_number', 4)
                    ->where('request_date', $date)
                    ->count();
                if ($total != 0) {
                    $success = $valid = $total;
                    $sub_product_number = 4;
                    $operator = 'HBCMCC';
                    $data = compact('apikey', 'product_id', 'node', 'operator', 'date', 'sub_product_number', 'total', 'success', 'valid');
                    $this->createPackage($data);
                }

                //联通的打包数据
                $total = ShortcutPackage::where('apikey', '89a95da508313950066ef22180e60e4e')
                    ->where('graded_number', 1)
                    ->where('request_date', $date)
                    ->count();
                if ($total != 0) {
                    $success = $valid = $total;
                    $sub_product_number = 1;
                    $operator = 'CUCC';
                    $data = compact('apikey', 'product_id', 'node', 'operator', 'date', 'sub_product_number', 'total', 'success', 'valid');
                    $this->createPackage($data);
                }
            } else {
                $node = $data['node_area'];
                foreach ($operators as $operator => $oldOperator) {
                    $stat_data = array_get($data, 'stat_data', []);
                    $oldSuccess = array_get($stat_data, $oldOperator . '_success', 0);
                    $oldFail = array_get($stat_data, $oldOperator . '_fail', 0);
                    $oldCache = array_get($stat_data, $oldOperator . '_cache', 0);
                    $sub_product_number = 1;
                    $total = $oldCache + $oldFail + $oldSuccess;
                    $success = $oldSuccess + $oldCache;

                    if ($total != 0) {
                        $this->createPackage(compact('apikey', 'product_id', 'node', 'operator', 'date', 'sub_product_number', 'total', 'success', 'valid'));
                    }
                }
            }
        }
    }

    /**
     * 整理V2版本的邦信分快捷版的统计
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 15:26
     *
     * @param $data array 统计数据
     *
     * @return array
     */
    protected function imposeBxfShortData($data, $operators)
    {
        $stat_data = $data['stat_data'];
        $apikey = $data['apikey'];
        $product_id = $data['product_id'];
        $node = $data['node_area'];
        $date = $data['amount_date'];
        $result = [];

        foreach ($operators as $operator => $statistics_key_prefix) {
            $oldSuccess = array_get($stat_data, $statistics_key_prefix . '_success', 0);
            $oldFail = array_get($stat_data, $statistics_key_prefix . '_fail', 0);
            $oldCache = array_get($stat_data, $statistics_key_prefix . '_cache', 0);
            $total = $oldSuccess + $oldFail + $oldCache;
            $success = $oldSuccess + $oldCache;
            $valid = $success;
            $cache = $oldCache;
            if (0 != $total) {
                $result[] = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
            }
        }

        //计算未知的运营商的量级
        //		$total           = array_get($stat_data, 'total', 0);
        //		$success         = array_get($stat_data, 'success', 0);
        //		$operatorTotal   = array_sum(array_column($result, 'total'));
        //		$operatorSuccess = array_sum(array_column($result, 'success'));
        //		if ($total != $operatorTotal) {
        //			$operator = 'UNKNOWN';
        //			$total    = $total - $operatorTotal;
        //			$success  = $success - $operatorSuccess;
        //			$valid    = $success;
        //			$cache    = 0;
        //			$result[] = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        //		}

        return $result;
    }

    /**
     * 转移邦信分详单版V1的统计数据
     * 每隔半个小时执行一次
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 16:32
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function transferBxfBillV1Data($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'all', 0);
        $success = array_get($stat_data, 'success', $total);
        $valid = $success;
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移邦信分详单版V2的统计数据
     * 每隔半个小时执行一次
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 16:32
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function transferBxfBillV2Data($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'all', 0);
        $success = array_get($stat_data, 'succ', $total);
        $valid = $success;
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移邦秒配详单版的统计数据
     * 每隔半个小时执行一次
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 16:32
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function transferBmpBillData($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'all', 0);
        $success = array_get($stat_data, 'succ', $total);
        $valid = array_get($stat_data, 'telnums', 0);
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移邦秒配批量版的统计数据
     * 每隔半个小时执行一次
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 16:32
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function transferBmpBatchData($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'all', 0);
        $success = array_get($stat_data, 'succ', $total);
        $valid = array_get($stat_data, 'telnums', 0);
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移邦秒配单号版的统计数据
     * 每隔半个小时执行一次
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 16:32
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function transferBmpSingleData($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'itag', 0);
        $success = array_get($stat_data, 'itag_valid', $total);
        $valid = $success;
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移邦企查的统计数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 16:32
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function transferBqcData($data)
    {
        $product_id = $data['product_id'];
        $call_product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'total_num', 0);
        $success = array_get($stat_data, 'valid_num', $total);
        $valid = array_get($stat_data, 'valid_name_or_address_num', $success);
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'call_product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移邦信分-私有云的统计数据
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/27 16:32
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function transferBxfPrivateCloudData($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];

        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'counts', 0);
        $success = array_get($stat_data, 'valid_counts', $total);
        $valid = array_get($stat_data, 'valid_counts', $success);
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移612产品
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/28 14:12
     *
     * @param $data array 数据
     *
     * @return void
     */
    protected function transfer612Data($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];

        //20201020及之后日期的量是新推送的，之前的需要导入
        if ($date >= 20201020) {
            throw new \Exception("612统计数据不可导入20201020之后的数据");
        }


        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'all', 0);
        $success = array_get($stat_data, 'succ', $total);
        $valid = $success;
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }

    /**
     * 转移616产品
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/28 14:12
     *
     * @param $data array 数据
     *
     * @return void
     */
    protected function transfer616Data($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];

        //20201020及之后日期的量是新推送的，之前的需要导入
        if ($date >= 20201020) {
            throw new \Exception("616统计数据不可导入20201020之后的数据");
        }

        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'all', 0);
        $success = array_get($stat_data, 'succ', $total);
        $valid = $success;
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        StatisticsCustomerUsage::updateOrCreate($result, array_only($result, [
            'product_id',
            'apikey',
            'node',
            'date',
            'operator',
        ]));
    }

    /**
     * 转移664产品
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/9/28 14:12
     *
     * @param $data array 数据
     *
     * @return void
     */
    protected function transfer664Data($data)
    {
        $product_id = $data['product_id'];
        $stat_data = $data['stat_data'];
        $date = $data['amount_date'];
        $apikey = $data['apikey'];
        $node = $data['node_area'];
        $total = array_get($stat_data, 'all', 0);
        $success = array_get($stat_data, 'succ', $total);
        $valid = array_get($stat_data, 'cdnums', $success);
        $operator = '';
        $cache = 0;

        if (0 == $total) {
            return;
        }

        $result = compact('apikey', 'product_id', 'node', 'operator', 'date', 'total', 'success', 'valid', 'cache');
        $this->create($result);
    }


    /**
     * 客户调用量入库
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/10/19 14:21
     *
     * @param $data array 统计数据
     *
     * @return void
     */
    protected function create($data)
    {
        StatisticsCustomerUsage::updateOrCreate(array_only($data, [
            'product_id',
            'apikey',
            'node',
            'date',
            'operator',
        ]), $data);
    }

    /**
     * 打包调用量入库
     *
     * @access   protected
     * <AUTHOR>
     * @datetime 2020/10/19 14:22
     *
     * @param $data array 打包统计数据
     *
     * @return void
     */
    protected function createPackage($data)
    {
        StatisticsCustomerTogetherCallUsage::updateOrCreate(array_only($data, [
            'apikey',
            'product_id',
            'node',
            'operator',
            'date',
            'sub_product_number',
        ]), $data);
    }
}