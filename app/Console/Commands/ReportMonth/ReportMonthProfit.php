<?php

namespace App\Console\Commands\ReportMonth;

use App\Http\Repository\DeptRepository;
use App\Http\Repository\StatBaseRepository;
use App\Models\BillCost;
use App\Models\BillNotes;
use App\Models\BillProductIncome;
use App\Models\BillProductIncomeV2;
use App\Models\CommonInfoModel;
use App\Models\Customer;
use App\Models\CustomerExpend;
use App\Models\Dept;
use App\Models\DeptGrade;
use App\Models\MoneyRecharge;
use App\Models\SystemUser;
use App\Models\UpstreamBillAdjust;
use App\Models\CustomerBillAdjust;
use App\Providers\Auth\DataAuth;
use App\Providers\RedisCache\RedisCache;
use App\Providers\Tool\SendMailService;
use App\TraitUpgrade\ExcelTrait;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * 领导月报
 * php artisan report_month:profit --sendto wei.xiu
 */
class ReportMonthProfit extends Command
{
	protected $signature   = "report_month:profit
	{--month= : 月份, 格式Ym}
    {--username= : 收件人，以用户在后台的用户名为基准，多个用户使用,隔开，该参数不会改变用户的邮件授权情况，不传则对所有授权用户发送}
    {--sendto= : 收件人, 添加此选项将强制使收件人为sendto, 使用username指定的商务的数据}
	";
	protected $description = '收入、成本、利润月报表邮件发送（新版本）';
	
	//各个商务部门的映射数据
	protected $deptId2DeptNameMapping = [];
	//用户与商务部门的映射数据
	protected $salesman2DeptIdMapping = [];
	//客户与商务用户的映射关系数据
	protected $customerId2SalesmanMapping = [];
	//商务用户与真实姓名的映射关系
	protected $salesman2RealNameMapping = [];
	
	/**
	 * @var array 约定发送给那些用户，为null代表发送给所有用户
	 */
	protected $username = null;
	
	/**
	 * @var integer 日期
	 */
	protected $month;       //所处的月份
    protected $lastDateOfMonth;
	protected $firstDateOfMonth;
	protected $firstDateOfYear;

	/**
	 * @var array 基础的数据
	 */
	protected $data     = [];
	protected $baseData = [
		'this_month_income_money'  => 0,
		'this_month_income_number' => 0,
		'this_month_cost_money'    => 0,
		'this_month_cost_number'   => 0,
		'this_month_profit'        => 0,
		'this_year_income_money'   => 0,
		'this_year_income_number'  => 0,
		'this_year_cost_money'     => 0,
		'this_year_cost_number'    => 0,
		'this_year_recharge_money' => 0,
		'this_year_profit'         => 0,
	];
	
	//排序的字段
	protected $orderField = 'this_month_income_money';
	
	/**
	 * @var array 邮件抄送人
	 */
	protected $cc = [

	];

	// 可见收入 成本 利润的人员
	protected $authCostPorfit = [
		'yong.liu'
	];

    /** @var array 获取部门的后代部门 */
    protected $sub_depts = [
        '银行销售部' => [],
        '非银销售部' => [],
    ];

    /** @var string[] 可查看银行/非银行合计数据人员 */
    protected $authBankCount = [
        'yong.liu',
        'mengsi.wang'
    ];

    /** @var null 指定收件人 */
    protected $sendto = null;


    /**
	 * @var array 客户充值数据
	 */
	protected $recharge = [];
	/**
	 * @var array 客户的备注信息
	 */
	protected $remarks = [];
	
	/**
	 * @var string 如果保存HTML，只保存最后一个客户的
	 **/
	protected $saveHtml = '';
	
	//存储用户财务字段查看权限
	protected $financeAuthMap =[];
	// 当前发送账号的财务字段查看权限
	protected $financeAuth =[
		'show_money'=>0,
		'show_money_agent'=>0,//收入(征信)
		'show_cost'=>0,
		'show_profit'=>0,
	];
	
	/**
	 * @var array 商务能看到的用户
	 **/
	protected $auth_customer_ids = [];
	
	//存储用户查看客户及来源查看权限
	protected $customerSourceAuthMap =[];

	//渠道跟进人是当前发送用户的 所有客户
	protected $auth_channel_follower_customers = [];
	
	//当前发送用户
	protected $current_name = '';
	
	//脚本执行
	public function handle()
	{
		try {
			//设置参数
			$this->setParams();
			//对每一个用户发送邮件
			array_walk($this->username, [$this, 'send']);
		} catch (\Exception $exception) {
			sendCommandExceptionNotice($this, $exception);
		}
	}
	
	/**
	 * 对每一个用户发送邮件
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:49
	 *
	 * @param $username string 用户名
	 *
	 * @return void
	 */
	private function send($username)
	{
		//获取权限服务对象
		$dataAuthService = new DataAuth($username);
		$this->current_name = $username;

		//获取这个用户的所能查看的所有客户ID
		$this->auth_customer_ids = $dataAuthService->getCustomerIds();

		$this->auth_channel_follower_customers = $dataAuthService->getCustomerIdsSourceByUserName();

        $dept_struct = DeptRepository::getSaleDeptStruct($username);
        $sort_dept = $this->sort_dept($dept_struct);

        //获取需要发送的数据
		$data = $this->getUsernameData($dataAuthService);

		//获取大区维度的统计
		$businessInfo = $this->getBusinessInfo($data,$sort_dept);

		//获取产品维度统计
		$productInfo = $this->getProductInfo($data);

		//获取客户维度统计
		$customerInfo = $this->getCustomerInfo($data,$username);

		//标题
		$title = date('Y年m月', strtotime($this->lastDateOfMonth)) . '权责收入及毛利数据统计 -- （' . $dataAuthService->getRealName() . '）收';

		//获取当前用户查看权限
		$this->financeAuthFormat($username,0);

		//创建HTML
		$html = $this->createHtml($businessInfo, $productInfo, $customerInfo, $title, $username);

        $sendto = $username;
        if($this->sendto) {//如果指定收件人
            $sendto = $this->sendto;
        }

        //发送邮件
		$mail = new SendMailService();
		$mail->setFromName('金融后台项目组')
			 ->setAddressee([['email' => $sendto . '@yulore.com', 'name' => $dataAuthService->getRealName()]])
			 ->setCC($this->cc)
			 ->setSubject($title)
			 ->setContent($html)
			 ->sendByAsync();
             // ->send();

		$this->output->success("[{$username}] 邮件发送成功");
	}


    /**
     * 按照商务架构排序,添加每个部门的层级数(deep)用于合并单元格,区分层级计算合计
     *
     * @param $dept_info
     *
     * @return array
     * <AUTHOR> 2024-03-22 20:13:56
     */
    private function sort_dept($dept_info){
        if(empty($dept_info)){
            return [];
        }
        $res = [];

        foreach($dept_info as $dept){
            $res[] = [
                'dept_id'   => $dept['value'],
                'dept_name' => $dept['label'],
            ];
            if(!empty($dept['children'])){
                $_res = $this->sort_dept($dept['children']);
                if(key_exists($dept['label'],$this->sub_depts)){
                    $this->sub_depts[$dept['label']] = array_column($_res, 'dept_id');
                }
                if(!empty($_res)) {
                    $res = array_merge($res, $_res);
                }
            }
        }
        return $res;
    }


    private function financeAuthFormat($username,$auth_type=1){
		if (in_array(-1, $this->financeAuthMap[$username]['user_money_product'])) {
			$this->financeAuth['show_money'] = 1;
		} else {
			$this->financeAuth['show_money'] = 0;
		}

		if($auth_type==0){

            if(in_array($username,$this->authCostPorfit)){
            	$this->financeAuth['show_cost'] = 1;
                $this->financeAuth['show_profit'] = 1;

            }else{
            	$this->financeAuth['show_cost'] = 0;
                $this->financeAuth['show_profit'] = 0;
            }
        }else{

			if (in_array(-1, $this->financeAuthMap[$username]['user_cost_product'])) {
				$this->financeAuth['show_cost'] = 1;
			} else {
				$this->financeAuth['show_cost'] = 0;
			}
			if ($this->financeAuth['show_money']&&$this->financeAuth['show_cost']) {
				$this->financeAuth['show_profit'] = 1;
			} else {
				$this->financeAuth['show_profit'] = 0;
			}
		}
	}

	/**
	 * 创建HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:49
	 *
	 * @param $businessInfo array 商务部门维度的统计数据
	 * @param $productInfo  array 产品维度的统计数据
	 * @param $customerInfo array 客户维度的统计数据
	 * @param $title        string 邮件标题
	 *
	 * @return string
	 */
	private function createHtml($businessInfo, $productInfo, $customerInfo, $title, $username)
	{
		return <<<HTML
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            color : #333333;
        }

        h1 {
            width       : 98%;
            height      : 30px;
            line-height : 30px;
            font-size   : 22px;
            font-weight : bold;
            text-align  : center;
            margin      : 0 auto;
        }

        h2 {
            width       : 98%;
            height      : 44px;
            line-height : 44px;
            font-size   : 20px;
            font-weight : bold;
            text-align  : center;
            margin      : 20px auto 2px;
            background  : rgba(229, 82, 45, 1);
            color       : #FFFFFF;
        }

        table {
            width         : 98%;
            border        : none;
            padding       : 0;
            margin        : 0 auto;
            font-size     : 14px;
            color         : #666666;
            border-bottom : none;
            border-right  : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        tr {
            border-right  : none;
            border-bottom : none;
            border-left   : 1px solid #CCCCCC;
            border-top    : 1px solid #CCCCCC;
        }

        td, th {
            border-right  : 1px solid #CCCCCC;
            border-bottom : 1px solid #CCCCCC;
            border-left   : none;
            border-top    : none;
            text-align    : center;
            padding       : 5px 0;
        }

        th {
            color       : #333333;
            font-weight : bold;
            height      : 20px;
            line-height : 20px;
            padding     : 8px 0;
        }

        tr:nth-child(even) {
            background : #EEEEEE;
        }

        tr:hover {
            background : #CCCCCC;
        }

        .bold {
            font-weight : bold;
        }

        .fsz-16 {
            font-size : 15px;
        }

        .space {
            width  : 100%;
            height : 40px;
        }

        .remark {
            width       : 98%;
            margin      : 10px auto;
            padding     : 5px;
            color       : red;
            font-size   : 16px;
            font-weight : bolder;
        }
    </style>
</head>
<body>
<h1>{$title}</h1>
<h2>商务维度权责报表</h2>
<table border="1" cellspacing="0" cellpadding="0">
{$this->createBusinessHtml($businessInfo,$username)}
</table>
<h2>产品维度权责报表</h2>
<table border="1" cellspacing="0" cellpadding="0">
{$this->createProductHtml($productInfo)}
</table>
<h2>客户维度权责报表</h2>
<table border="1" cellspacing="0" cellpadding="0">
{$this->createCustomerHtml($customerInfo)}
</table>
HTML;
	}
	
	/**
	 * 创建客户部门的HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:36
	 *
	 * @param $data array 客户维度的统计数据
	 *
	 * @return string
	 */
	private function createCustomerHtml($data)
	{
		$total = $data['total'];
		$data  = $data['result'];
		
		//标题
		$html = <<<HTML
<tr>
<th align="center">客户名称</th>
<th align="center">计费用量</th>
HTML;
$show_money = $this->financeAuth['show_money'];
$show_cost = $this->financeAuth['show_cost'];
$show_profit = $this->financeAuth['show_profit'];

$show_money && $html .= '<th align="center">当月收入</th>';
$show_cost && $html .= '<th align="center">当月成本</th>';
$show_profit && $html .= '<th align="center">当月毛利</th>';
$show_money && $html .= '<th align="center">现金收入(本年)</th>';
$show_money && $html .= '<th align="center">本年收入</th>';
$show_cost && $html .= '<th align="center">本年成本</th>';
$show_profit && $html .= '<th align="center">本年毛利</th>';
$html .= <<<HTML
</tr>
<tr>
<td align="center">合计</td>
<td align="center">{$this->formatNumber($total['this_month_income_number'])}</td>
HTML;

$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_year_recharge']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_year_profit']).'</td>';
$html .= '<td align="center"></td>
</tr>';

		array_walk($data, function ($customer) use (&$html,$show_money,$show_cost,$show_profit) {
			$html .= <<<HTML
<tr>
<td align="center">{$customer['customer_name']}</td>
<td align="center">{$this->formatNumber($customer['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_recharge']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($customer['this_year_profit']).'</td>';
$html .= <<<HTML
<!-- <td align="center">{$customer['remark']}</td> -->
</tr>
HTML;
		});
		
		return $html;
	}
	
	/**
	 * 创建产品部门的HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:24
	 *
	 * @param $data array 商务部门的统计数据
	 *
	 * @return string
	 */
	private function createProductHtml($data)
	{
		$total = $data['total'];
		$data  = $data['result'];
		//标题
		$html = <<<HTML
<tr>
<th align="center">父产品</th>
<th align="center">子产品</th>
<th align="center">计费用量</th>
HTML;
$show_money = $this->financeAuth['show_money'];
$show_cost = $this->financeAuth['show_cost'];
$show_profit = $this->financeAuth['show_profit'];
$show_money && $html .= '<th align="center">当月收入</th>';
$show_cost && $html .= '<th align="center">当月成本</th>';
$show_profit && $html .= '<th align="center">当月毛利</th>';
$show_money && $html .= '<th align="center">本年收入</th>';
$show_cost && $html .= '<th align="center">本年成本</th>';
$show_profit && $html .= '<th align="center">本年毛利</th>';
$html .= <<<HTML
</tr>
<tr>
<td align="center" colspan="2">合计</td>
<td align="center">{$this->formatNumber($total['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_year_profit']).'</td>';
$html .= '</tr>';
		
		array_walk($data, function ($father) use (&$html,$show_money,$show_cost,$show_profit) {
			//部门维度
			$mergeRow = count($father['sub_product']) + 1;
			if ($mergeRow > 2) {
				$html .= <<<HTML
<tr>
<td align="center" rowspan="{$mergeRow}">{$father['product_name']}</td>
<td align="center">小计</td>
<td align="center">{$this->formatNumber($father['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($father['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($father['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($father['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($father['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($father['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($father['this_year_profit']).'</td>';
$html .= '</tr>';

				array_walk($father['sub_product'], function ($product) use (&$html,$show_money,$show_cost,$show_profit) {
					$html .= <<<HTML
<tr>
<td align="center">{$product['product_name']}</td>
<td align="center">{$this->formatNumber($product['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($product['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($product['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($product['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($product['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($product['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($product['this_year_profit']).'</td>';
$html .= '</tr>';

				});
			} else {
				$html .= <<<HTML
<tr>
<td align="center">{$father['product_name']}</td>
<td align="center">--</td>
<td align="center">{$this->formatNumber($father['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($father['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($father['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($father['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($father['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($father['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($father['this_year_profit']).'</td>';
$html .= '</tr>';

			}
		});
		
		return $html;
	}
	
	/**
	 * 创建商务部门的HTML内容
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:50
	 *
	 * @param $data array 商务部门的统计数据
	 *
	 * @return string
	 */
	private function createBusinessHtml($data,$username)
	{
		$total = $data['total'];
        $sub_dept_total = $data['sub_dept_total'];
        $data  = $data['result'];
		//标题
		$html = <<<HTML
<tr>
<th align="center">区域</th>
<th align="center">商务</th>
<th align="center">计费用量</th>
HTML;
$show_money = $this->financeAuth['show_money'];
$show_cost = $this->financeAuth['show_cost'];
$show_profit = $this->financeAuth['show_profit'];
$show_money && $html .= '<th align="center">当月收入</th>';
$show_cost && $html .= '<th align="center">当月成本</th>';
$show_profit && $html .= '<th align="center">当月毛利</th>';
$show_money && $html .= '<th align="center">本年收入</th>';
$show_cost && $html .= '<th align="center">本年成本</th>';
$show_profit && $html .= '<th align="center">本年毛利</th>';
$html .= <<<HTML
</tr>
<tr>
<td align="center" colspan="2">合计</td>
<td align="center">{$this->formatNumber($total['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($total['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($total['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($total['this_year_profit']).'</td>';
$html .= '</tr>';

		array_walk($data, function ($business) use (&$html,$username,$show_money,$show_cost,$show_profit,&$sub_dept_total) {
            $mergeRow = count($business['salesman']) + 1;

            $hit_dept_name = '';
            if(in_array($username,$this->authBankCount)) {
                foreach ($this->sub_depts as $sub_dept_name => $sub_dept_ids) {
                    if (in_array($business['dept_id'], $sub_dept_ids) && isset($sub_dept_total[$sub_dept_name])) {
                        $hit_dept_name = $sub_dept_name;
                        $sub_total     = $sub_dept_total[$sub_dept_name];

// ------------------ 合计
                        $html .= <<<HTML
<td align="center" rowspan="1">{$sub_dept_name}</td>
<td align="center">小计</td>
<td align="center">{$this->formatNumber($sub_total['this_month_income_number'])}</td>
HTML;
                        $show_money && $html .= '<td align="center">'.$this->formatMoney($sub_total['this_month_income_money']).'</td>';
                        $show_cost && $html .= '<td align="center">'.$this->formatMoney($sub_total['this_month_cost_money']).'</td>';
                        $show_profit && $html .= '<td align="center">'.$this->formatMoney($sub_total['this_month_profit']).'</td>';
                        $show_money && $html .= '<td align="center">'.$this->formatMoney($sub_total['this_year_income_money']).'</td>';
                        $show_cost && $html .= '<td align="center">'.$this->formatMoney($sub_total['this_year_cost_money']).'</td>';
                        $show_profit && $html .= '<td align="center">'.$this->formatMoney($sub_total['this_year_profit']).'</td>';
                        $html .= '</tr>';
                    }
                }

                unset($sub_dept_total[$hit_dept_name]);
            }


            //部门维度
			$html     .= <<<HTML
<tr>
<td align="center" rowspan="{$mergeRow}">{$business['dept_name']}</td>
<td align="center">小计</td>
<td align="center">{$this->formatNumber($business['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($business['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($business['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($business['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($business['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($business['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($business['this_year_profit']).'</td>';
$html .= '</tr>';

			array_walk($business['salesman'], function ($salesman) use (&$html,$show_money,$show_cost,$show_profit) {
				$html .= <<<HTML
<tr>
<td align="center">{$salesman['real_name']}</td>
<td align="center">{$this->formatNumber($salesman['this_month_income_number'])}</td>
HTML;
$show_money && $html .= '<td align="center">'.$this->formatMoney($salesman['this_month_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($salesman['this_month_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($salesman['this_month_profit']).'</td>';
$show_money && $html .= '<td align="center">'.$this->formatMoney($salesman['this_year_income_money']).'</td>';
$show_cost && $html .= '<td align="center">'.$this->formatMoney($salesman['this_year_cost_money']).'</td>';
$show_profit && $html .= '<td align="center">'.$this->formatMoney($salesman['this_year_profit']).'</td>';
$html .= '</tr>';

			});
			
		});
		
		return $html;
	}
	
	/**
	 * 获取客户维度的统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:41
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	private function getCustomerInfo($data,$username=null)
	{
		//生成一个客户的数据容器
		$result = [];
		
		//融入到客户的数据容器中
		array_walk($data, function ($item) use (&$result) {
			$this->fillCustomer($item, $result);
		});
    	//过滤recharge数据，并返回
		$recharge = array_filter($this->recharge, function ($key) use($username) {
			list($customreid,$source) = explode('_',$key);
			return  !$this->filterDataSource($username,$customreid,$source);
		}, ARRAY_FILTER_USE_KEY);
		// 合并不同来源的充值记录
		$rechargeMap = [];
		array_walk($recharge, function ($item,$key) use (&$rechargeMap) {
			list($customreid,$source) = explode('_',$key);
			if(!isset($rechargeMap[$customreid])){
				$rechargeMap[$customreid] = 0.00;
			}
			$rechargeMap[$customreid] = bcadd($item,$rechargeMap[$customreid],6);
		});
		//将客户充值数据填充到容器中
		array_walk($rechargeMap, function ($money, $customer_id) use (&$result) {
			$this->fillCustomerRecharge($customer_id, $money, $result);
		});

		//将客户的备注信息填充到容器中
		array_walk($this->remarks, function ($content, $customer_id) use (&$result) {
			$this->fillCustomerRemark($customer_id, $content, $result);
		});
		
		//对数据进行排序
		array_multisort(array_column($result, $this->orderField), SORT_DESC, $result);
		
		$total = $this->getTotal($result);
		//汇总中加上本年累计充值
		$total['this_year_recharge'] = array_sum(array_column($result, 'this_year_recharge'));
		
		return compact('total', 'result');
	}
	
	/**
	 * 填充客户的充值数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/3/5 16:13
	 *
	 * @param $customer_id string 客户ID
	 * @param $money       float 充值金额
	 * @param $container   array 客户的数据容器
	 *
	 * @return void
	 */
	private function fillCustomerRecharge($customer_id, $money, &$container)
	{
		if (!array_key_exists($customer_id, $container)) {
			$container[$customer_id] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')
												  ->get($customer_id),
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$customer_id] = array_merge($container[$customer_id], $this->baseData);
		}
		
		$container[$customer_id]['this_year_recharge'] = $money;
	}
	
	/**
	 * 填充客户的备注信息数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/3/5 16:35
	 *
	 * @param $customer_id   string 客户ID
	 * @param $content       string 备注信息
	 * @param $container     array 客户的数据容器
	 *
	 * @return void
	 */
	private function fillCustomerRemark($customer_id, $content, &$container)
	{
		if (!array_key_exists($customer_id, $container)) {
			$container[$customer_id] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')
												  ->get($customer_id),
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$customer_id] = array_merge($container[$customer_id], $this->baseData);
		}
		
		$container[$customer_id]['remark'] = $content;
	}
	
	/**
	 * 将最小颗粒的数据填充到客户的数据容器中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:42
	 *
	 * @param $data      array 最小颗粒的数据
	 * @param $container array 容器
	 *
	 * @return void
	 */
	private function fillCustomer($data, &$container)
	{
		$customer_id = $data['customer_id'];
		
		if (!array_key_exists($customer_id, $container)) {
			$container[$customer_id] = [
				'customer_id'        => $customer_id,
				'customer_name'      => RedisCache::instance('customerId_customerName_mapping')
												  ->get($customer_id),
				'remark'             => '',
				'this_year_recharge' => 0,
			];
			$container[$customer_id] = array_merge($container[$customer_id], $this->baseData);
		}
		
		foreach (array_keys($this->baseData) as $key) {
			$container[$customer_id][$key] = bcadd($container[$customer_id][$key], $data[$key], 6);
		}
	}
	
	/**
	 * 获取产品维度的统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 14:29
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	private function getProductInfo($data)
	{
		//生成一个产品的数据容器
		$result = [];
		
		//融入到产品的数据容器中
		array_walk($data, function ($item) use (&$result) {
			$this->fillProduct($item, $result);
		});
		
		//对每一个父产品求和，并且对每一个子产品的数据进行排序
		$result = array_map(function ($item) {
			//子产品排序
			$sub_products = $item['sub_product'];
			array_multisort(array_column($sub_products, $this->orderField), SORT_DESC, $sub_products);
			$item['sub_product'] = $sub_products;
			
			//求和数据
			$item = array_merge($item, $this->baseData);
			foreach ($sub_products as $sub_product) {
				foreach (array_keys($this->baseData) as $key) {
					$item[$key] = bcadd($item[$key], $sub_product[$key], 6);
				}
			}
			
			return $item;
		}, $result);
		
		//对数据进行排序
		array_multisort(array_column($result, $this->orderField), SORT_DESC, $result);
		
		$total = $this->getTotal($result);
		
		return compact('total', 'result');
	}
	
	/**
	 * 将最小颗粒的数据填充到产品的数据容器中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 13:56
	 *
	 * @param $data      array 最小颗粒的数据
	 * @param $container array 容器
	 *
	 * @return void
	 */
	private function fillProduct($data, &$container)
	{
		$product_id = $data['product_id'];
		$father_id  = $data['father_id'];
		
		//父产品
		if (!array_key_exists($father_id, $container)) {
			$container[$father_id] = [
				'product_id'   => $father_id,
				'product_name' => RedisCache::instance('productId_productName_mapping')
											->get($father_id),
				'sub_product'  => [],
			];
		}
		
		//子产品
		if (!array_key_exists($product_id, $container[$father_id]['sub_product'])) {
			$product_name                                      = RedisCache::instance('productId_productName_mapping')
																		   ->get($product_id);
			$container[$father_id]['sub_product'][$product_id] = array_merge(compact('product_id', 'product_name'), $this->baseData);
		}
		
		//数据填充
		foreach (array_keys($this->baseData) as $key) {
			$container[$father_id]['sub_product'][$product_id][$key] = bcadd($container[$father_id]['sub_product'][$product_id][$key], $data[$key], 6);
		}
	}
	
	/**
	 * 获取大区维度的统计
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 11:56
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 */
	private function getBusinessInfo($data,$sort_dept)
	{
		//生成一个大区的数据容器
		$result = [];
		
		//融入到大区的数据容器中
		array_walk($data, function ($item) use (&$result) {
			$this->fillBusiness($item, $result);
		});
		
		//对每一个大区求和，并且对每一个商务的数据进行排序
		$result = array_map(function ($item) {
			//商务排序
			$salesman = $item['salesman'];
			array_multisort(array_column($salesman, $this->orderField), SORT_DESC, $salesman);
			$item['salesman'] = $salesman;
			
			//求和数据
			$item = array_merge($item, $this->baseData);
			foreach ($salesman as $everySalesman) {
				foreach (array_keys($this->baseData) as $key) {
					$item[$key] = bcadd($item[$key], $everySalesman[$key], 6);
				}
			}
			
			return $item;
		}, $result);

        $sort_dept = array_column($sort_dept,'dept_name','dept_id');
        $sort_dept = array_keys($sort_dept);

        uasort($result,function($a,$b) use ($sort_dept) {
            if(array_search($a['dept_id'],$sort_dept) < array_search($b['dept_id'],$sort_dept)) {
                return -1;
            } else {
                return 1;
            }
        });
		//对数据进行排序
		//array_multisort(array_column($result, $this->orderField), SORT_DESC, $result);
		
		$total = $this->getTotal($result);
        $sub_dept_total = $this->getSubDeptTotal($result);

		return compact('total', 'result', 'sub_dept_total');
	}
	
	/**
	 * 汇总合计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:05
	 *
	 * @param $data array 各个维度的结果数据
	 *
	 * @return array
	 */
	private function getTotal($data)
	{
		$total = $this->baseData;
		
		array_walk($data, function ($item) use (&$total) {
			foreach (array_keys($this->baseData) as $key) {
				$total[$key] = bcadd($total[$key], $item[$key], 6);
			}
		});
		
		return $total;
	}

    /**
     * 获取指定部门的合计数据
     *
     * @param $data
     *
     * @return array
     * <AUTHOR> 2024-03-25 08:49:53
     */
    private function getSubDeptTotal($data){
        $dept_total = [];

        foreach($this->sub_depts as $dept_name => $sub_dept_ids){
            array_walk($data, function ($item) use ($sub_dept_ids,$dept_name,&$dept_total) {
                $dept_id = $item['dept_id'];
                foreach (array_keys($this->baseData) as $key) {
                    if(in_array($dept_id,$sub_dept_ids)){
                        if(!isset($dept_total[$dept_name])) {
                            $dept_total[$dept_name] = $this->baseData;
                        }
                        $dept_total[$dept_name][$key] = bcadd($dept_total[$dept_name][$key], $item[$key], 6);
                    }
                }
            });
        }

        return $dept_total;
    }


	/**
	 * 将最小颗粒的数据填充到大区的数据容器中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 13:56
	 *
	 * @param $data      array 最小颗粒的数据
	 * @param $container array 容器
	 *
	 * @return void
	 */
	private function fillBusiness($data, &$container)
	{
		$customer_id = $data['customer_id'];
		//获取所属商务用户
		$salesman = array_get($this->customerId2SalesmanMapping, $customer_id, '');
		// 如果客户的渠道跟进人是自己 
		if(in_array($data['customer_id'],array_keys($this->auth_channel_follower_customers))){
			$salesman =$item['saleman'] = $this->current_name;
		}
		if (!$salesman) {
			return;
		}
		
		//获取所属部门
		$dept_id = array_get($this->salesman2DeptIdMapping, $salesman, '');
		if (!$dept_id) {
			return;
		}
		
		if (!array_key_exists($dept_id, $container)) {
			$container[$dept_id] = [
				'dept_id'   => $dept_id,
				'dept_name' => array_get($this->deptId2DeptNameMapping, $dept_id, '--'),
				'salesman'  => [],
			];
		}
		
		if (!array_key_exists($salesman, $container[$dept_id]['salesman'])) {
			$real_name = array_get($this->salesman2RealNameMapping, $salesman, $salesman);
			
			$container[$dept_id]['salesman'][$salesman] = array_merge(compact('salesman', 'real_name'), $this->baseData);
		}
		
		foreach (array_keys($this->baseData) as $key) {
			$container[$dept_id]['salesman'][$salesman][$key] = bcadd($container[$dept_id]['salesman'][$salesman][$key], $data[$key], 6);
		}
	}
	
	/**
	 * 获取某个用户所能看到的数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:54
	 *
	 * @param $dataAuthService DataAuth 权限服务对象
	 *
	 * @return array
	 */
	private function getUsernameData($dataAuthService)
	{
		//获取当前用户能查看哪些客户的数据
		$username = $dataAuthService->getUserName();
		//过滤data数据，并返回
		$data = array_filter($this->data, function ($key) use($username) {
			list($customreid,$product_id_not_use,$source) = explode('_',$key);
			return  !$this->filterDataSource($username,$customreid,$source);
		},ARRAY_FILTER_USE_KEY);

		return $this->mergeSource($data);
	}
	/**
	 * 去掉用于数据过滤的来源字段
	 *
	 * @param [type] $data
	 * @return void
	 */
	public function mergeSource($data){
		$result = [];

		array_walk($data,function($item,$key)use(&$result){
			list($customer_id,$product_id,$source) = explode('_',$key);
			$unique_key = $customer_id . '_' . $product_id;
			if (!array_key_exists($unique_key, $result)) {
				$result[$unique_key]                = $this->baseData;
				$result[$unique_key]['customer_id'] = $customer_id;
				$result[$unique_key]['product_id']  = $product_id;
				$result[$unique_key]['source']  = $source;
				$result[$unique_key]['father_id']  = $item['father_id'];
			}
			foreach ($item as $colkey => $value) {
				if(in_array($colkey,array_keys($this->baseData))){
					$result[$unique_key][$colkey] = bcadd($result[$unique_key][$colkey], $value, 6);
				}
			}
		});
		return $result;
	}
	
	/**
	 * 设置参数
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:12
	 *
	 * @return void
	 */
	private function setParams()
	{
		//校验是否发送报表
		$this->checkSend();

        //设置所需要的日期
        $this->month = empty($this->option('month')) ? date('Ym', strtotime('first day of last month')) : $this->option('month');
        if (!preg_match('/^\d{6}$/', $this->month)) {
            $this->output->error('月份格式不正确');
            return false;
        }
        if ($this->month >= date('Ym')) {
            $this->output->error('您所设置的月份大于当前月份，不可生成');
            return false;
        }
        $this->firstDateOfMonth = $this->month.'01';
        $this->lastDateOfMonth = date('Ymd', strtotime('last day of ' . date('Y-m', strtotime($this->month . '01'))));
        $this->firstDateOfYear  = date('Y', strtotime($this->firstDateOfMonth)) . '0101';
		
		//给那个用户发送
		$username = $this->option('username');
		if ($username) {
			$this->username = explode(',', $username);
		} else {
			//获取需要接收邮件的所有用户
			$this->username = $this->getUsers();
		}

        $sendto = $this->option('sendto');
        if($sendto){
            $this->sendto = $sendto;
        }

        // 检查收入 成本 利润 显示权限
		$baseStatRepository = new StatBaseRepository();
		foreach ($this->username  as  $user) {
			$baseStatRepository->renderAuth($user);
			$this->financeAuthMap[$user] =$baseStatRepository->getFinanceAuthProduct();
			$this->customerSourceAuthMap[$user] = $baseStatRepository->getDataAndSourceAuthCustomerIdsForReport($user);
		}

		//设置毛利及相关数据
		$this->setData();
		
		//数据校验
		//        $this->check();
		
		//设置客户的本年充值数据
		$this->setRecharge();
		
		//设置客户的备注数据
		//        $this->setRemarks();
		
		//设置大区所需的各种映射数据
		$this->setBusinessMappingInfo();
	}
	
	/**
	 * 校验是否发送
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/2/1 17:27
	 *
	 * @return void
	 */
	private function checkSend()
	{
		$isSend = Redis::connection('default')
					   ->get('is_send_profit');
		
		if (1 != $isSend) {
			throw new \Exception("根据后台设置，停止发送报表");
		}
	}
	
	/**
	 * 设置大区所需的各种映射数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 11:57
	 *
	 * @return void
	 */
	private function setBusinessMappingInfo()
	{
		//查询所有商务的子部门
        $dept_ids = DeptRepository::getSalesDept();

		//设置部门ID->部门名称的映射关系
		$this->deptId2DeptNameMapping = Dept::whereIn('dept_id', $dept_ids)
											->pluck('dept_name', 'dept_id')
											->toArray();
		//设置商务用户->商务部门ID的映射关系
		$this->salesman2DeptIdMapping = SystemUser::whereIn('dept_id', $dept_ids)
												  ->pluck('dept_id', 'username')
												  ->toArray();
		
		//设置商务用户->商务用户真实名称的映射关系
		$this->salesman2RealNameMapping = SystemUser::whereIn('dept_id', $dept_ids)
													->pluck('realname', 'username')
													->toArray();
		
		
		//设置客户ID->商务用户的映射关系
		$this->customerId2SalesmanMapping = Customer::where('is_delete', 0)
													->pluck('salesman', 'customer_id')
													->toArray();
		
	}
	
	/**
	 * 获取全量需要发送的用户
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/21 17:36
	 *
	 * @return array
	 */
	private function getUsers()
	{
		return SystemUser::where('profit_auth', 1)
						 ->pluck('username')
						 ->toArray();
	}
	
	/**
	 * 设置每个客户的本年充值数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 16:42
	 *
	 * @return void
	 */
	private function setRecharge()
	{
		$data = MoneyRecharge::select(DB::raw('SUM(`money`) as money'), 'customer_id','source')
							 ->where('status', 3)
							 ->where('remit_date', '>=', strtotime($this->firstDateOfYear))
							 ->where('remit_date', '<=', strtotime($this->lastDateOfMonth))
							 ->groupBy('customer_id','source')
							 ->get()
							 ->toArray();
		
							 array_walk($data,function($item){
								$uniquekey = $item['customer_id'].'_'.$item['source'];
								$this->recharge[$uniquekey ] = $item['money'];
							});	
	}
	
	/**
	 * 设置data参数，data参数中，存储的是每个客户、每个子产品的月、年的收入、成本、毛利数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 16:30
	 *
	 * @return void
	 */
	private function setData()
	{
		/**
		 * 获取不同维度【时间维度（当月、当年）、客户维度、子产品维度】的营收数据（包含特殊消费、特殊赠送）
		 * 并将获取到的数据按一定的格式填充到$this->data中
		 */
		$this->fillIncome();
		
		// 企服收入减半
		$this->formatQifuIncome();

		/**
		 * 获取不同维度【时间维度（当月、当年）、客户维度、子产品维度】的成本数据（包含特殊成本费用）
		 * 并将获取到的数据按一定的格式填充到$this->data中
		 */
		$this->fillCost();
		
		/**
		 * 计算不同维度【时间维度（当月、当年）、客户维度、子产品维度】的毛利数据
		 */
		array_walk($this->data, [$this, 'calculateItemProfit']);
	}
	
	/**
	 * 对每一条数据求年、月的利润数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/20 14:16
	 *
	 * @return void
	 */
	private function calculateItemProfit(&$data)
	{
		//月利润
		$data['this_month_profit'] = bcsub($data['this_month_income_money'], $data['this_month_cost_money'], 6);
		
		//年利润
		$data['this_year_profit'] = bcsub($data['this_year_income_money'], $data['this_year_cost_money'], 6);
		
		//补充一个父产品ID
		$data['father_id'] = RedisCache::instance('productId_fatherId_mapping')
									   ->get($data['product_id']) ?: $data['product_id'];
		
	}
	
	/**
	 * 填充成本数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 14:57
	 *
	 * @return void
	 */
	private function fillCost()
	{
		//填充本年的成本统计数据
		$this->fillThisYearCost();
		
		//填充本月的成本统计数据
		$this->fillThisMonthCost();
	}
	
	/**
	 * 填充本年的成本统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisYearCost()
	{
		//填充本年的成本统计数据，并将其保存在容器中
		$this->getBillCost($this->firstDateOfYear, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')
										  ->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_year_cost_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_year_cost_money', $customer_id, $product_id, $money,$item->source);
			 });
		
		//填充本年的特殊营收数据，并将其保存在容器中
		$this->getSpecialCost($this->firstDateOfYear, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			
				 //填充量和钱
				 $this->fillItem('this_year_cost_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_year_cost_money', $customer_id, $product_id, $money,$item->source);
			 });
		
	}
	
	/**
	 * 填充本月的成本统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisMonthCost()
	{
		//填充本月的成本统计数据，并将其保存在容器中
		$this->getBillCost($this->firstDateOfMonth, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')
										  ->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_month_cost_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_month_cost_money', $customer_id, $product_id, $money,$item->source);
			 });
		
		//填充本月的特殊营收数据，并将其保存在容器中
		$this->getSpecialCost($this->firstDateOfMonth, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			
				 //填充量和钱
				 $this->fillItem('this_month_cost_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_month_cost_money', $customer_id, $product_id, $money,$item->source);
			 });
		
	}
	
	/**
	 * 填充营收数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:07
	 *
	 * @return void
	 */
	private function fillIncome()
	{
		//填充本年的营收统计数据
		$this->fillThisYearIncome();
		
		//填充本月的
		$this->fillThisMonthIncome();
	}
	
	/**
	 * 填充本年的营收统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisYearIncome()
	{
		//填充本年的营收账单数据，并将其保存在容器中
		$this->getBillIncome($this->firstDateOfYear, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')
										  ->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_year_income_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_year_income_money', $customer_id, $product_id, $money,$item->source);
			 });
		
		//填充本年的特殊营收数据，并将其保存在容器中
		$this->getSpecialIncome($this->firstDateOfYear, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			
				 //填充量和钱
				 $this->fillItem('this_year_income_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_year_income_money', $customer_id, $product_id, $money,$item->source);
			 });
	}
	
	/**
	 * 填充本月的营收统计数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:53
	 *
	 * @return void
	 */
	private function fillThisMonthIncome()
	{
		//填充本月的营收账单数据，并将其保存在容器中
		$this->getBillIncome($this->firstDateOfMonth, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $apikey      = $item->apikey;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
				 $customer_id = RedisCache::instance('apikey_customerId_mapping')
										  ->get($apikey);
			
				 //填充量和钱
				 $this->fillItem('this_month_income_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_month_income_money', $customer_id, $product_id, $money,$item->source);
			 });
		
		//填充本月的特殊营收数据，并将其保存在容器中
		$this->getSpecialIncome($this->firstDateOfMonth, $this->lastDateOfMonth)
			 ->each(function ($item) {
				 $customer_id = $item->customer_id;
				 $product_id  = $item->product_id;
				 $number      = $item->number;
				 $money       = $item->money;
			
				 //填充量和钱
				 $this->fillItem('this_month_income_number', $customer_id, $product_id, $number,$item->source);
				 $this->fillItem('this_month_income_money', $customer_id, $product_id, $money,$item->source);
			 });
	}

	/**
	 * 获取营收账单数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:08
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getBillIncome($start_date, $end_date)
	{
		return BillProductIncomeV2::select([
			'apikey',
			'product_id',
			'source',
			DB::raw('SUM(`money`) as money'),
			DB::raw('SUM(`number`) as number'),
		])
								->where('date', '>=', $start_date)
								->where('date', '<=', $end_date)
								->groupBy('apikey', 'product_id','source')
								->get();
	}
	
	/**
	 * 获取成本账单数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 14:58
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getBillCost($start_date, $end_date)
	{
		return BillCost::select([
			'apikey',
			'product_id',
			'source',
			DB::raw('SUM(`money`) as money'),
			DB::raw('SUM(`number`) as number'),
		])
					   ->where('date', '>=', $start_date)
					   ->where('date', '<=', $end_date)
					   ->groupBy('apikey', 'product_id','source')
					   ->get();
	}
	
	/**
	 * 获取特殊营收数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:09
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getSpecialIncome($start_date, $end_date)
	{
		return CustomerExpend::select([
			'customer_id',
			'product_id',
			'type',
			'source',
			DB::raw('SUM( `money` ) as money'),
			DB::raw('SUM(`fee_number`) as number'),
		])
							 ->where('profile_show_date', '>=', date('Y-m-d', strtotime($start_date)))
							 ->where('profile_show_date', '<=', date('Y-m-d', strtotime($end_date)))
							 ->groupBy('customer_id', 'product_id', 'type','source')
							 ->get()
							 ->map(function ($item) {
								 $type = $item->type;
								 if ($type == 1) {
									 $item->money  = bcsub(0, $item->money, 6);
									 $item->number = bcsub(0, $item->number, 0);
								 }
			
								 return $item;
							 });
	}
	
	/**
	 * 获取特殊成本数据
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 11:09
	 *
	 * @param $start_date integer 开始日期
	 * @param $end_date   integer 截止日期
	 *
	 * @return Collection
	 */
	private function getSpecialCost($start_date, $end_date)
	{
	    /*
		return UpstreamBillAdjust::select([
			'customer_id',
			'product_id',
			DB::raw('SUM(`money`) as money'),
			DB::raw('SUM(`fee_number`) as number'),
		])
								 ->where('date', '>=', date('Y-m-d', strtotime($start_date)))
								 ->where('date', '<=', date('Y-m-d', strtotime($end_date)))
								 ->groupBy('customer_id', 'product_id')
								 ->get();
	    */

        return CustomerBillAdjust::getCustomerProductCost($start_date, $end_date);
	}
	
	/**
	 * 将最小颗粒的数据填充到基础的数据中
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/19 13:58
	 *
	 * @param $key         string 数据的key
	 * @param $customer_id string 客户ID
	 * @param $product_id  string 产品ID
	 * @param $value       integer|float 值
	 *
	 * @return void
	 */
	private function fillItem($key, $customer_id, $product_id, $value,$source=null)
	{
		$unique_key = $customer_id . '_' . $product_id . '_' .$source;
		
		if (!array_key_exists($unique_key, $this->data)) {
			$this->data[$unique_key]                = $this->baseData;
			$this->data[$unique_key]['customer_id'] = $customer_id;
			$this->data[$unique_key]['product_id']  = $product_id;
			$this->data[$unique_key]['source']  = $source;
		}
		
		if (!array_key_exists($key, $this->data[$unique_key])) {
			throw new \Exception("暂不支持填充{$key}数据");
		}
		
		$this->data[$unique_key][$key] = bcadd($this->data[$unique_key][$key], $value, 6);
	}
	
	// 企服收入减半
	private function formatQifuIncome(){
		$deductCol = ['this_date_income_money','this_month_income_money','this_year_income_money'];
		foreach ($this->data as $unique_key => $item) {
			list($customer_id,$product_id) = explode('_',$unique_key);
			$customerType = RedisCache::instance('customerId_customerType_mapping')->get($customer_id);
			if($customerType==2){// 企服企业收入减半
				foreach ($item as $key => $value) {
					if(in_array($key,$deductCol)){
						$this->data[$unique_key][$key] = bcdiv($value,2,6);
					}
				}
			}
		}
	}
	
	/**
	 * 对量格式化
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:11
	 *
	 * @param $number float 数值
	 *
	 * @return integer
	 */
	private function formatNumber($number)
	{
		return number_format($number, 0);
	}
	
	/**
	 * 对金额格式化
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/22 15:12
	 *
	 * @param $money float 金额
	 *
	 * @return integer
	 */
	private function formatMoney($money)
	{
		return number_format(round($money, 0), 0);
	}

	// 按照客户及客户来源过滤数据
	public function filterDataSource($username,$customer_id,$source){
		$auth = $this->customerSourceAuthMap[$username];

		if(!isset($auth[$customer_id])){
			return true;// 被过滤掉
		}
		if(in_array(-1,$auth[$customer_id])||in_array($source,$auth[$customer_id])){
			return false;// 有权限，不用过滤
		}else{
			return true;// 无权限，被过滤掉
		}
	}
}