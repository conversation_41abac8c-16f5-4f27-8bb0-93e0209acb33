<?php
namespace App\Console\Commands\WeeklyStatistic;

use App\Define\StatDefine;
use App\Models\Common\CommonEnumModel;
use App\Models\Crs\SystemDept;
use App\Models\Crs\SystemUser;
use App\Models\Customer;
use App\Models\Customer\CustomerGroup;
use App\Models\Remit;
use App\Models\RemitSplitPrice;
use App\Models\WeeklyStatisticData;
use App\Providers\RedisCache\RedisCache;
use App\Repositories\Customer\ConsumeRepositorie;
use App\Repositories\CustomerSalesmanHistoryRepository;
use App\Repositories\FeishuRepository;
use App\Repositories\Income\MainRepository;
use App\Utils\Helpers\Func;
use Exception;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Console\Command;

/**
 * 每周统计数据 数据统计->周报数据
 *
 * 企服产品金额减半 3100
 * 哆唻科技金额减半 C202306133YCXJQ
 *
 * 统计当月以及之前各个月份的消耗
 * 权责收入-区域 按客户的商务区域区分 商务根据crs用户表的dept_id获取对应部门即为区域
 * 权责收入-产品
 * 权责收入-银行客户
 * 现金收入分析 -> 回款数据统计
 * 上周客户到款统计 -> 上周一到周日
 *
 * php artisan weekly_statistic:weekly_statistic
 */
class WeeklyStatistic extends Command
{
    protected $signature   = 'weekly_statistic:weekly_statistic';
    protected $description = '每周统计数据';


    protected const PRODUCT_QIFU        = 3100;             //企服产品
    protected const PRODUCT_BMY         = 200;              //邦秒验
    protected const CUSTOMER_DUOLAIKEJI = 'C202306133YCXJQ';//哆唻科技
    protected const SUB_PRODUCTS        = [50101];          //风险验证
    protected const PRODUCT_JINDUN = 50000;            //金盾  收款暂时按 50000 算

    /**
     * @var array
     */
    private $cshr_month;

    /**
     * @var array
     */
    private $customer_ids;

    /**
     * @var array
     */
    private $source_map;

    /**
     * @var null
     */
    private $customer_dept_map;

    private $sort_roles = [
        WeeklyStatisticData::TASK_TYPE_DEPT => [
            '华东非银部' => 50,
            '华南非银部' => 40,
            '华北非银部' => 30,
            '生态合作部' => 20,
            '华东银行区' => 10,
            '合计'       => -1,
        ],
        WeeklyStatisticData::TASK_TYPE_PRODUCT => [
            // '邦信分-通信指数' => 90,
            // '邦信分-通信评分' => 80,
            // '邦秒验'          => 70,
            // '号码风险等级'    => 60,
            // '号码分'          => 50,
            // '企服产品'        => 40,
            // '存量洞察'        => 30,
            // '前筛'            => 20,
            // '其它'            => 10,
            // '运营商' => 90,
            // '代理产品' => 70,
            // '自有' => 50,
            // '号码融' => 30,
            // '存量洞察' => 20,
            // '其它' => 10,
            //
            '运营商_邦信分-通信指数' => 900,
            '运营商_邦信分-通信评分' => 850,
            '运营商_其它'            => 800,
            '代理产品_在网时长'      => 750,
            '代理产品_在网状态'      => 700,
            '代理产品_三要素'        => 650,
            '代理产品_其它'          => 600,
            '自有_号码风险等级'      => 550,
            '自有_号码活跃指数'      => 500,
            '自有_号码分'            => 450,
            '自有_金盾'              => 400,
            '自有_其它'              => 350,
            '号码融_号码融'          => 300,
            '存量洞察_AI创新'        => 250,
            '存量洞察_存量评分'      => 200,
            '存量洞察_存量运营'      => 150,
            '其它_其它'              => 100,
            '合计'                   => - 1,
        ],
        WeeklyStatisticData::TASK_TYPE_BANK => [
            '合计' => - 1,
        ],
        WeeklyStatisticData::TASK_TYPE_REMIT => [
            '华东非银部' => 50,
            '华南非银部' => 40,
            '华北非银部' => 30,
            '生态合作部' => 20,
            '华东银行区' => 10,
            '合计'        => -1,
        ],
        WeeklyStatisticData::TASK_TYPE_REMIT_LAST_WEEK => [
            '合计' => - 1,
        ],
        WeeklyStatisticData::TASK_TYPE_CUSTOMER_LAST_WEEK => [
            '-' => -1,
            '合计' => -10,
        ],
        WeeklyStatisticData::TASK_TYPE_SALEMAN_LAST_WEEK => [
            '合计' => - 1,
        ],
    ];

    private $product_title_map = [
        201   => '代理产品_三要素',
        202   => '代理产品_在网时长',
        203   => '代理产品_在网状态',
        204   => '代理产品_其它',
        205   => '代理产品_其它',
        206   => '代理产品_其它',
        207   => '代理产品_其它',
        208   => '代理产品_其它',
        211   => '代理产品_其它',
        212   => '代理产品_其它',
        213   => '代理产品_三要素',
        214   => '代理产品_其它',
        215   => '代理产品_其它',
        216   => '代理产品_在网状态',
        217   => '代理产品_其它',
        218   => '代理产品_其它',
        219   => '代理产品_其它',
        220   => '代理产品_其它',
        221   => '代理产品_其它',
        222   => '代理产品_其它',
        224   => '代理产品_在网时长',
        225   => '代理产品_在网状态',
        226   => '代理产品_其它',
        227   => '代理产品_其它',
        230   => '代理产品_其它',
        231   => '代理产品_其它',
        232   => '代理产品_其它',
        233   => '代理产品_其它',
        234   => '代理产品_其它',
        235   => '代理产品_其它',
        236   => '代理产品_其它',
        237   => '代理产品_其它',
        238   => '代理产品_其它',
        239   => '代理产品_其它',
        240   => '代理产品_其它',
        310   => '代理产品_其它',
        311   => '代理产品_其它',
        312   => '代理产品_其它',
        313   => '代理产品_其它',
        314   => '代理产品_其它',
        315   => '代理产品_其它',
        316   => '代理产品_其它',
        317   => '代理产品_其它',
        318   => '代理产品_其它',
        320   => '自有_号码活跃指数',
        321   => '自有_号码活跃指数',
        322   => '自有_号码活跃指数',
        323   => '自有_号码活跃指数',
        324   => '自有_号码活跃指数',
        325   => '自有_号码活跃指数',
        326   => '自有_号码活跃指数',
        327   => '自有_号码活跃指数',
        328   => '自有_号码活跃指数',
        329   => '自有_号码活跃指数',
        330   => '自有_号码活跃指数',
        331   => '自有_号码活跃指数',
        340   => '代理产品_其它',
        341   => '代理产品_其它',
        900   => '代理产品_其它',
        901   => '代理产品_其它',
        41001 => '代理产品_在网时长',
        41002 => '代理产品_在网状态',
        41012 => '代理产品_在网状态',
        41003 => '代理产品_其它',
        41004 => '代理产品_其它',
        41005 => '代理产品_其它',
        41006 => '代理产品_三要素',
        41007 => '代理产品_在网状态',
        241   => '运营商_邦信分-通信指数',
        242   => '运营商_邦信分-通信指数',
        243   => '运营商_邦信分-通信指数',
        244   => '运营商_邦信分-通信指数',
        245   => '运营商_邦信分-通信指数',
        246   => '运营商_邦信分-通信指数',
        247   => '运营商_邦信分-通信指数',
        252   => '运营商_邦信分-通信指数',
        253   => '运营商_邦信分-通信指数',
        254   => '运营商_邦信分-通信指数',
        255   => '运营商_邦信分-通信指数',
        256   => '运营商_邦信分-通信指数',
        257   => '运营商_邦信分-通信指数',
        258   => '运营商_邦信分-通信指数',
        259   => '运营商_邦信分-通信指数',
        260   => '运营商_邦信分-通信指数',
        261   => '运营商_邦信分-通信指数',
        262   => '运营商_邦信分-通信指数',
        263   => '运营商_邦信分-通信指数',
        264   => '运营商_邦信分-通信指数',
        265   => '运营商_邦信分-通信指数',
        266   => '运营商_邦信分-通信指数',
        267   => '运营商_邦信分-通信指数',
        268   => '运营商_邦信分-通信指数',
        269   => '运营商_邦信分-通信指数',
        270   => '运营商_邦信分-通信指数',
        271   => '运营商_邦信分-通信指数',
        272   => '运营商_邦信分-通信指数',
        273   => '运营商_邦信分-通信指数',
        274   => '运营商_邦信分-通信指数',
        275   => '运营商_邦信分-通信指数',
        276   => '运营商_邦信分-通信指数',
        277   => '运营商_邦信分-通信指数',
        278   => '运营商_邦信分-通信指数',
        279   => '运营商_邦信分-通信指数',
        280   => '运营商_邦信分-通信指数',
        281   => '运营商_邦信分-通信指数',
        282   => '运营商_邦信分-通信指数',
        283   => '运营商_邦信分-通信指数',
        284   => '运营商_邦信分-通信指数',
        285   => '运营商_邦信分-通信指数',
        286   => '运营商_邦信分-通信指数',
        287   => '运营商_邦信分-通信指数',
        800   => '运营商_邦信分-通信指数',
        801   => '运营商_邦信分-通信指数',
        802   => '运营商_邦信分-通信指数',
        803   => '运营商_邦信分-通信指数',
        804   => '运营商_邦信分-通信指数',
        805   => '运营商_邦信分-通信指数',
        806   => '运营商_邦信分-通信指数',
        807   => '运营商_邦信分-通信指数',
        871   => '运营商_邦信分-通信指数',
        872   => '运营商_邦信分-通信指数',
        873   => '运营商_邦信分-通信指数',
        874   => '运营商_邦信分-通信指数',
        875   => '运营商_邦信分-通信指数',
        401   => '自有_其它',
        640   => '自有_号码风险等级',
        641   => '自有_号码风险等级',
        642   => '自有_号码风险等级',
        643   => '自有_号码风险等级',
        644   => '自有_号码风险等级',
        645   => '自有_号码活跃指数',
        646   => '自有_号码风险等级',
        647   => '自有_号码风险等级',
        648   => '自有_号码风险等级',
        649   => '自有_号码风险等级',
        651   => '自有_号码风险等级',
        652   => '自有_号码活跃指数',
        661   => '自有_号码风险等级',
        662   => '自有_号码风险等级',
        663   => '自有_号码风险等级',
        665   => '自有_号码风险等级',
        666   => '自有_号码风险等级',
        667   => '自有_号码风险等级',
        669   => '自有_号码风险等级',
        670   => '自有_号码风险等级',
        671   => '自有_号码风险等级',
        672   => '自有_号码风险等级',
        673   => '自有_号码风险等级',
        681   => '自有_号码风险等级',
        682   => '自有_号码风险等级',
        683   => '自有_号码风险等级',
        684   => '自有_号码风险等级',
        61001 => '自有_号码风险等级',
        61002 => '自有_号码风险等级',
        61003 => '自有_号码风险等级',
        62001 => '自有_号码风险等级',
        62002 => '自有_号码风险等级',
        62003 => '自有_号码风险等级',
        62004 => '自有_号码风险等级',
        62005 => '自有_号码风险等级',
        62006 => '自有_号码风险等级',
        62007 => '自有_号码风险等级',
        62008 => '自有_号码风险等级',
        62009 => '自有_号码风险等级',
        62010 => '自有_号码风险等级',
        62011 => '自有_号码风险等级',
        62012 => '自有_号码风险等级',
        62013 => '自有_号码风险等级',
        62015 => '自有_号码风险等级',
        62016 => '自有_号码风险等级',
        62017 => '自有_号码风险等级',
        62018 => '自有_号码风险等级',
        62019 => '自有_号码风险等级',
        62020 => '自有_号码风险等级',
        62021 => '自有_号码风险等级',
        62022 => '自有_号码风险等级',
        62023 => '自有_号码风险等级',
        62024 => '自有_号码风险等级',
        62025 => '自有_号码风险等级',
        62026 => '自有_号码风险等级',
        62027 => '自有_号码风险等级',
        62028 => '自有_号码风险等级',
        62032 => '自有_号码风险等级',
        62033 => '自有_号码风险等级',
        62034 => '自有_号码风险等级',
        62035 => '自有_号码风险等级',
        62036 => '自有_号码风险等级',
        62037 => '自有_号码风险等级',
        62038 => '自有_号码风险等级',
        62039 => '自有_号码风险等级',
        62040 => '自有_号码风险等级',
        62041 => '自有_号码风险等级',
        62042 => '自有_号码风险等级',
        62043 => '自有_号码风险等级',
        62044 => '自有_号码风险等级',
        62045 => '自有_号码风险等级',
        62046 => '自有_号码风险等级',
        62047 => '自有_号码风险等级',
        62048 => '自有_号码风险等级',
        62049 => '自有_号码风险等级',
        62050 => '自有_号码风险等级',
        62051 => '自有_号码风险等级',
        62052 => '自有_号码风险等级',
        62053 => '自有_号码风险等级',
        62054 => '自有_号码风险等级',
        62055 => '自有_号码风险等级',
        63001 => '自有_号码风险等级',
        63002 => '自有_号码风险等级',
        63003 => '自有_号码风险等级',
        63004 => '自有_号码风险等级',
        63005 => '自有_号码风险等级',
        63006 => '自有_号码风险等级',
        63007 => '自有_号码风险等级',
        63008 => '自有_号码风险等级',
        63009 => '自有_号码风险等级',
        63010 => '自有_号码风险等级',
        63011 => '自有_号码风险等级',
        63012 => '自有_号码风险等级',
        63013 => '自有_号码风险等级',
        64001 => '自有_号码风险等级',
        64002 => '自有_号码风险等级',
        64003 => '自有_号码风险等级',
        64004 => '自有_号码风险等级',
        64005 => '自有_号码风险等级',
        64006 => '自有_号码风险等级',
        64007 => '自有_号码风险等级',
        64008 => '自有_号码风险等级',
        64009 => '自有_号码风险等级',
        64010 => '自有_号码风险等级',
        64011 => '自有_号码风险等级',
        64012 => '自有_号码风险等级',
        64013 => '自有_号码风险等级',
        64014 => '自有_号码风险等级',
        64015 => '自有_号码风险等级',
        64016 => '自有_号码风险等级',
        64017 => '自有_号码风险等级',
        64018 => '自有_号码风险等级',
        64019 => '自有_号码风险等级',
        64020 => '自有_号码风险等级',
        64021 => '自有_号码风险等级',
        64022 => '自有_号码风险等级',
        64023 => '自有_号码风险等级',
        64024 => '自有_号码风险等级',
        64025 => '自有_号码风险等级',
        64026 => '自有_号码风险等级',
        64027 => '自有_号码风险等级',
        64028 => '自有_号码风险等级',
        64029 => '自有_号码风险等级',
        64030 => '自有_号码风险等级',
        64031 => '自有_号码风险等级',
        64032 => '自有_号码风险等级',
        64033 => '自有_号码风险等级',
        64034 => '自有_号码风险等级',
        64035 => '自有_号码风险等级',
        64036 => '自有_号码风险等级',
        64037 => '自有_号码风险等级',
        64038 => '自有_号码风险等级',
        64039 => '自有_号码风险等级',
        64040 => '自有_号码风险等级',
        64041 => '自有_号码风险等级',
        64042 => '自有_号码风险等级',
        64043 => '自有_号码风险等级',
        64044 => '自有_号码风险等级',
        64045 => '自有_号码风险等级',
        64046 => '自有_号码风险等级',
        64047 => '自有_号码风险等级',
        64048 => '自有_号码风险等级',
        64049 => '自有_号码风险等级',
        64050 => '自有_号码风险等级',
        64051 => '自有_号码风险等级',
        64052 => '自有_号码风险等级',
        64053 => '自有_号码风险等级',
        64054 => '自有_号码风险等级',
        64055 => '自有_号码风险等级',
        64056 => '自有_号码风险等级',
        64057 => '自有_号码风险等级',
        64058 => '自有_号码风险等级',
        64059 => '自有_号码风险等级',
        64060 => '自有_号码风险等级',
        64061 => '自有_号码风险等级',
        64062 => '自有_号码风险等级',
        64063 => '自有_号码风险等级',
        64064 => '自有_号码风险等级',
        64065 => '自有_号码风险等级',
        64066 => '自有_号码风险等级',
        64067 => '自有_号码风险等级',
        64068 => '自有_号码风险等级',
        64069 => '自有_号码风险等级',
        64070 => '自有_号码风险等级',
        64071 => '自有_号码风险等级',
        64072 => '自有_号码风险等级',
        64073 => '自有_号码风险等级',
        64074 => '自有_号码风险等级',
        64075 => '自有_号码风险等级',
        64076 => '自有_号码风险等级',
        64077 => '自有_号码风险等级',
        64078 => '自有_号码风险等级',
        64079 => '自有_号码风险等级',
        64080 => '自有_号码风险等级',
        64081 => '自有_号码风险等级',
        64082 => '自有_号码风险等级',
        64083 => '自有_号码风险等级',
        64084 => '自有_号码风险等级',
        65001 => '自有_号码风险等级',
        251   => '运营商_邦信分-通信评分',
        288   => '运营商_邦信分-通信评分',
        289   => '运营商_邦信分-通信评分',
        290   => '运营商_邦信分-通信评分',
        291   => '运营商_邦信分-通信评分',
        292   => '运营商_邦信分-通信评分',
        293   => '运营商_邦信分-通信评分',
        294   => '运营商_邦信分-通信评分',
        295   => '运营商_邦信分-通信评分',
        296   => '运营商_邦信分-通信评分',
        297   => '运营商_邦信分-通信评分',
        298   => '运营商_邦信分-通信评分',
        299   => '运营商_邦信分-通信评分',
        711   => '运营商_邦信分-通信评分',
        712   => '运营商_邦信分-通信评分',
        713   => '运营商_邦信分-通信评分',
        751   => '运营商_邦信分-通信评分',
        752   => '运营商_邦信分-通信评分',
        753   => '运营商_邦信分-通信评分',
        754   => '运营商_邦信分-通信评分',
        755   => '运营商_邦信分-通信评分',
        811   => '运营商_邦信分-通信评分',
        1101  => '自有_其它',
        1102  => '自有_其它',
        1103  => '自有_其它',
        1104  => '自有_其它',
        1301  => '运营商_邦信分-通信指数',
        2001  => '自有_其它',
        3001  => '代理产品_在网状态',
        3002  => '自有_其它',
        3003  => '自有_其它',
        664   => '自有_其它',
        3101  => '自有_其它',
        3102  => '自有_其它',
        3103  => '自有_其它',
        3104  => '自有_其它',
        3105  => '自有_其它',
        3106  => '自有_其它',
        3107  => '自有_其它',
        3108  => '自有_其它',
        10101 => '自有_号码分',
        10102 => '自有_号码分',
        10103 => '自有_号码分',
        10104 => '自有_号码分',
        10105 => '自有_号码分',
        10106 => '自有_号码分',
        10107 => '自有_号码分',
        10108 => '自有_号码分',
        10109 => '自有_号码分',
        10110 => '自有_号码分',
        10111 => '自有_号码分',
        10112 => '自有_号码分',
        10113 => '自有_号码分',
        10114 => '自有_号码分',
        10115 => '自有_号码分',
        10116 => '自有_号码分',
        10117 => '自有_号码分',
        10118 => '自有_号码分',
        10119 => '自有_号码分',
        10120 => '自有_号码分',
        11101 => '自有_号码分',
        11102 => '自有_号码分',
        11103 => '自有_号码分',
        11108 => '自有_号码分',
        11201 => '自有_号码分',
        11202 => '自有_号码分',
        11203 => '自有_号码分',
        11204 => '自有_号码分',
        11301 => '自有_号码分',
        11401 => '自有_号码分',
        11402 => '自有_号码分',
        11403 => '自有_号码分',
        11404 => '自有_号码分',
        11405 => '自有_号码分',
        11501 => '自有_号码分',
        11502 => '自有_号码分',
        11503 => '自有_号码分',
        11601 => '自有_号码分',
        11602 => '自有_号码分',
        11603 => '自有_号码分',
        11604 => '自有_号码分',
        11605 => '自有_号码分',
        11606 => '自有_号码分',
        11607 => '自有_号码分',
        11608 => '自有_号码分',
        11609 => '自有_号码分',
        11610 => '自有_号码分',
        11611 => '自有_号码分',
        11612 => '自有_号码分',
        11613 => '自有_号码分',
        11614 => '自有_号码分',
        11616 => '自有_号码分',
        11617 => '自有_号码分',
        11618 => '自有_号码分',
        11619 => '自有_号码分',
        11620 => '自有_号码分',
        11621 => '自有_号码分',
        11622 => '自有_号码分',
        11623 => '自有_号码分',
        11624 => '自有_号码分',
        11625 => '自有_号码分',
        11626 => '自有_号码分',
        11627 => '自有_号码分',
        11701 => '自有_号码分',
        11702 => '自有_号码分',
        11805 => '自有_号码分',
        11901 => '自有_号码分',
        11902 => '自有_号码分',
        11903 => '自有_号码分',
        11904 => '自有_号码分',
        11905 => '自有_号码分',
        11906 => '自有_号码分',
        11907 => '自有_号码分',
        12001 => '自有_号码分',
        12101 => '自有_号码分',
        12102 => '自有_号码分',
        12201 => '自有_号码分',
        12401 => '自有_号码分',
        12402 => '自有_号码分',
        12501 => '自有_号码分',
        12601 => '自有_号码分',
        12602 => '自有_号码分',
        12603 => '自有_号码分',
        12701 => '自有_号码分',
        12702 => '自有_号码分',
        12801 => '自有_号码分',
        12901 => '自有_号码分',
        13001 => '自有_号码分',
        13002 => '自有_号码分',
        13201 => '自有_号码分',
        13301 => '自有_号码分',
        13401 => '自有_号码分',
        13402 => '自有_号码分',
        13403 => '自有_号码分',
        13501 => '自有_号码分',
        13601 => '自有_号码分',
        13701 => '自有_号码分',
        13702 => '自有_号码分',
        13703 => '自有_号码分',
        13801 => '自有_号码分',
        13901 => '自有_号码分',
        14001 => '自有_号码分',
        14101 => '自有_号码分',
        14201 => '自有_号码分',
        14202 => '自有_号码分',
        14203 => '自有_号码分',
        14204 => '自有_号码分',
        14205 => '自有_号码分',
        15001 => '自有_号码分',
        15002 => '自有_号码分',
        15003 => '自有_号码分',
        15004 => '自有_号码分',
        15005 => '自有_号码分',
        15006 => '自有_号码分',
        15007 => '自有_号码分',
        15008 => '自有_号码分',
        15009 => '自有_号码分',
        15010 => '自有_号码分',
        15011 => '自有_号码分',
        15012 => '自有_号码分',
        15013 => '自有_号码分',
        20101 => '自有_其它',
        20102 => '自有_其它',
        1200  => '存量洞察_存量运营',
        11104 => '存量洞察_存量评分',
        11105 => '存量洞察_存量评分',
        11106 => '存量洞察_存量评分',
        11107 => '存量洞察_存量评分',
        11801 => '存量洞察_存量评分',
        11802 => '存量洞察_存量评分',
        11803 => '存量洞察_存量评分',
        11804 => '存量洞察_存量评分',
        12301 => '存量洞察_存量评分',
        12302 => '存量洞察_存量评分',
        12303 => '存量洞察_存量评分',
        13101 => '存量洞察_存量评分',
        13102 => '存量洞察_存量评分',
        13103 => '存量洞察_存量评分',
        13104 => '存量洞察_存量评分',
        13105 => '存量洞察_存量评分',
        14301 => '存量洞察_存量评分',
        30101 => '存量洞察_存量评分',
        30201 => '存量洞察_存量评分',
        30301 => '存量洞察_存量评分',
        30302 => '存量洞察_存量评分',
        30303 => '存量洞察_存量评分',
        30401 => '存量洞察_存量评分',
        30501 => '存量洞察_存量评分',
        30601 => '存量洞察_存量评分',
        30602 => '存量洞察_存量评分',
        30701 => '存量洞察_存量评分',
        30702 => '存量洞察_存量评分',
        30703 => '存量洞察_存量评分',
        30704 => '存量洞察_存量评分',
        30801 => '存量洞察_存量评分',
        30802 => '存量洞察_存量评分',
        30901 => '存量洞察_存量评分',
        30902 => '存量洞察_存量评分',
        30903 => '存量洞察_存量评分',
        30904 => '存量洞察_存量评分',
        31001 => '存量洞察_存量评分',
        31002 => '存量洞察_存量评分',
        31003 => '存量洞察_存量评分',
        31004 => '存量洞察_存量评分',
        31101 => '存量洞察_存量评分',
        31102 => '存量洞察_存量评分',
        31103 => '存量洞察_存量评分',
        31104 => '存量洞察_存量评分',
        31201 => '存量洞察_存量评分',
        31301 => '存量洞察_存量评分',
        31302 => '存量洞察_存量评分',
        31303 => '存量洞察_存量评分',
        31304 => '存量洞察_存量评分',
        38001 => '存量洞察_存量运营',
        38002 => '存量洞察_存量运营',
        38003 => '存量洞察_存量运营',
        39001 => '存量洞察_AI创新',
        39002 => '存量洞察_AI创新',
        39003 => '存量洞察_AI创新',
        50101 => '自有_金盾',
        50102 => '自有_金盾',
        50201 => '自有_金盾',
        50202 => '自有_金盾',
        50203 => '自有_金盾',
        50204 => '自有_金盾',
        50205 => '自有_金盾',
        50206 => '自有_金盾',
        50207 => '自有_金盾',
        50208 => '自有_金盾',
        50209 => '自有_金盾',
        50210 => '自有_金盾',
        50211 => '自有_金盾',
        50212 => '自有_金盾',
        50213 => '自有_金盾',
        50214 => '自有_金盾',
        50215 => '自有_金盾',
        50216 => '自有_金盾',
        50217 => '自有_金盾',
        50218 => '自有_金盾',
        50219 => '自有_金盾',
        50220 => '自有_金盾',
        50221 => '自有_金盾',
        50222 => '自有_金盾',
        50223 => '自有_金盾',
        50224 => '自有_金盾',
        50225 => '自有_金盾',
        50226 => '自有_金盾',
        50227 => '自有_金盾',
        71001 => '号码融_号码融',
        71002 => '号码融_号码融',
        71003 => '号码融_号码融',
        71004 => '号码融_号码融',
        71005 => '号码融_号码融',
        71006 => '号码融_号码融',
        71007 => '号码融_号码融',
        71008 => '号码融_号码融',
        71009 => '号码融_号码融',
        71010 => '号码融_号码融',
        71011 => '号码融_号码融',
        72001 => '号码融_号码融',
        72002 => '号码融_号码融',
        72003 => '号码融_号码融',
        72004 => '号码融_号码融',
        72005 => '号码融_号码融',
        72006 => '号码融_号码融',
        72007 => '号码融_号码融',
        72008 => '号码融_号码融',
        72009 => '号码融_号码融',
        72010 => '号码融_号码融',
        72011 => '号码融_号码融',
        72012 => '号码融_号码融',
        72013 => '号码融_号码融',
        72014 => '号码融_号码融',
        72015 => '号码融_号码融',
        72016 => '号码融_号码融',
        72017 => '号码融_号码融',
        72018 => '号码融_号码融',
        72019 => '号码融_号码融',
        72020 => '号码融_号码融',
        72021 => '号码融_号码融',
        72022 => '号码融_号码融',
        72023 => '号码融_号码融',
        72024 => '号码融_号码融',
        72025 => '号码融_号码融',
        72026 => '号码融_号码融',
        75001 => '号码融_号码融',
        72036 => '号码融_号码融',//	HMRO_C32001	70000	号码融
        72035 => '号码融_号码融',//HMRO_C31003	70000	号码融
        72034 => '号码融_号码融',//HMRO_C31002	70000	号码融
        72033 => '号码融_号码融',//HMRO_C31001	70000	号码融
        72032 => '号码融_号码融',//HMRO_C13005	70000	号码融
        72031 => '号码融_号码融',//HMRO_C13004	70000	号码融
        72030 => '号码融_号码融',//HMRO_C13003	70000	号码融
        72029 => '号码融_号码融',//HMRO_C13002	70000	号码融
        72028 => '号码融_号码融',//HMRO_C13001	70000	号码融
        72027 => '号码融_号码融',//HMRO_C26001	70000	号码融
        71013 => '号码融_号码融',//HMRO_C11012	70000	号码融
        71012 => '号码融_号码融',//HMRO_C11011	70000	号码融

        104   => '自有_其它',
        601   => '自有_其它',
        616   => '自有_其它',
        103   => '自有_其它',
        1300  => '运营商_邦信分-通信指数',
        3100  => '自有_其它',
        3000  => '代理产品_其它',

        41011 => '代理产品_其它',//通用分v3.2
        41010 => '代理产品_其它',//通用分v3.1

        715 => '运营商_邦信分-通信评分',//邦信分_评分Y05
        714 => '运营商_邦信分-通信评分',//邦信分_评分Y04

        31403 => '存量洞察_存量评分',//存量洞察_14_A_03	30000	存量洞察
        31309 => '存量洞察_存量评分',//质量分C0209	30000	存量洞察
        31308 => '存量洞察_存量评分',//质量分C0208	30000	存量洞察
        31307 => '存量洞察_存量评分',//质量分C0207	30000	存量洞察
        31306 => '存量洞察_存量评分',//质量分C0206	30000	存量洞察
        31305 => '存量洞察_存量评分',//质量分C0205	30000	存量洞察

        // 主产品
        210   => '运营商_邦信分-通信指数',// 邦信分-通信指数 210
        615   => '自有_号码风险等级',//615     号码风险等级
        1000  => '运营商_邦信分-通信评分',// 邦信分-通信评分 1000
        10000 => '自有_号码分',// 号码分 10000
        30000 => '存量洞察_存量评分',//30000	存量洞察
        50000 => '自有_金盾',// 金盾 50000
        70000 => '号码融_号码融',// 号码融 70000

        -1    => '其它_其它',
    ];


    /** @var array 其它_其它 */
    private $report_data = [];



    /**
     *
     * 从数据库获取待执行的统计数据类型以及时间
     *
     * @return void
     * @throws Exception
     * <AUTHOR> 2024-01-23 14:38:57
     */
    public function handle() {
        $this->output->success("【{$this->description}】 开始");

        $this->source_map = array_keys(CommonEnumModel::getTypeMaps('1'));
        $this->customer_ids = Customer::getAllCustomerIds();
        $cshr = new CustomerSalesmanHistoryRepository();

        //判断是否存在正在执行的任务
        $running_tsak_list = WeeklyStatisticData::getRunningTask();
        if(!empty($running_tsak_list)){
            $this->output->warning("【{$this->description}】 有任务正在执行! id:".$running_tsak_list[0]['id']);
            die;
        }

        $task_list = WeeklyStatisticData::getInitTask();
        $today = date('Ymd');

        foreach($task_list as $task){
            $task_type = $task['type'];
            $task_id = $task['id'];

            $params = json_decode($task['params'],true);

            $this->cshr_month = $cshr->getListMonthly($this->customer_ids,date('Ym',strtotime('20190101')),'','Ym');
            $this->get_customer_dept_map();
            //不包含结束日期的数据 [$start,$end)
            $start = $params['start'];
            // $end   = $params['end'];
            // 比参数少1 查询条件为=
            $end   = date('Ymd', strtotime($params['end']) - 86400);
            if($end > $today){
                $this->output->warning("【{$this->description}】 ".$task['id'] . '未到执行时间,当前时间: ' . $today . ' ,须要在 ' . $end . ' 后执行!');
                continue;
            }
            WeeklyStatisticData::TaskRun($task_id);
            switch ($task_type) {
                case WeeklyStatisticData::TASK_TYPE_COST:
                    [$status,$res] = $this->weekly_cost($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_DEPT:
                    [$status,$res] = $this->weekly_income_dept($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_PRODUCT:
                    [$status,$res] = $this->weekly_income_product($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_BANK:
                    [$status,$res] = $this->weekly_income_bank($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_REMIT:
                    [$status,$res] = $this->weekly_remit($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_REMIT_LAST_WEEK:
                    [$status,$res] = $this->remit_last_week($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_PRODUCT_LAST_WEEK:
                    [$status,$res] = $this->product_last_week($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_DEPT_LAST_WEEK:
                    [$status,$res] = $this->dept_last_week($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_CUSTOMER_LAST_WEEK:
                    [$status,$res] = $this->customer_last_week($start,$end);
                    break;
                case WeeklyStatisticData::TASK_TYPE_SALEMAN_LAST_WEEK:
                    [$status,$res] = $this->salesman_last_week($start,$end);
                    break;

                default:
                    $status = WeeklyStatisticData::TASK_STATUS_TYPE_ERROR;
                    $res = '任务类型错误';
                    break;
            }

            $res = json_encode($res,JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            WeeklyStatisticData::TaskDone($task_id,$status,$res);
        }
        $this->output->success("【{$this->description}】 结束");
    }

    /**
     * 统计当月以及之前各个月份的消耗
     *
     * @param $start
     * @param $end
     *
     * @return array
     * <AUTHOR> 2024-01-23 15:47:48
     */
    private function weekly_cost($start, $end): array {
        return [
            WeeklyStatisticData::TASK_STATUS_INIT,
            ''
        ];
    }


    /**
     * 权责收入-区域
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-23 15:47:48
     */
    private function weekly_income_dept($start, $end): array {
        $res = [];
        $months = Func::getMonthRanges($start,$end);

        foreach ($months as $month) {
            $dimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;
            $consume = (new MainRepository())->getBaseIncome($dimension, $month[0], $month[1], $this->customer_ids,[],[],[],['is_query_month_data' => 0, 'is_halve_income' => 1]);

            $_month = date('Ym',strtotime($month[0]));

            foreach ($consume['data'] as $c){
                if(bccomp($c['income'], 0,6) == 0){
                    continue;
                }

                $tk   = Func::get_tmp_key($_month, $c['customer_id']);
                $dept = $this->customer_dept_map[$tk];
                //营销-内部 合并到 华东非银部
                if($dept == '营销-内部'){
                    $dept = '华东非银部';
                }
                if (isset($res[$_month][$dept])) {
                    $res[$_month][$dept] = bcadd($res[$_month][$dept],$c['income'], 6);
                } else {
                    $res[$_month][$dept] = $c['income'];
                }
            }
        }
        return $this->add_total($res,WeeklyStatisticData::TASK_TYPE_DEPT);
    }


    /**
     * 获取客户 - 月份 -> 区域 映射
     *
     * @return void
     * <AUTHOR> 2024-01-23 18:20:44
     *
     */
    private function get_customer_dept_map(){
        $cshr_salesman = [];
        foreach($this->cshr_month as $customer_id => $cshr_months) {
            foreach($cshr_months as $username) {
                $cshr_salesman[$username] = $username;
            }
        }
        $salesman = SystemUser::getInfosByUserName($cshr_salesman);
        $salesman_dept = array_column($salesman,'dept_id', 'username');

        $dept_list = SystemDept::getAllDeptInfo();
        $dept_list = array_column($dept_list,'dept_name', 'dept_id');

        $this->customer_dept_map = [];
        foreach($this->cshr_month as $customer_id => $cshr_months) {
            foreach($cshr_months as $month => $username) {
                if(empty($username)) {
                    continue;
                }
                $tk = Func::get_tmp_key($month,$customer_id);

                $dept = $dept_list[$salesman_dept[$username]] ?? '';
                $this->customer_dept_map[$tk] = $dept;
            }
        }
    }


    /**
     *
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-23 19:47:21
     */
    private function weekly_income_product($start, $end): array {
        $res = [];
        $months = Func::getMonthRanges($start,$end);

        foreach ($months as $month) {
            $dimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;
            $consume = (new MainRepository())->getBaseIncome($dimension, $month[0], $month[1], $this->customer_ids,[],[],[],['is_query_month_data' => 0, 'is_halve_income' => 1]);

            $_month = date('Ym',strtotime($month[0]));
            foreach ($consume['data'] as $c) {
                if(bccomp($c['income'], 0,6) == 0){
                    continue;
                }

                $father_id = $c['father_id'];
                $product_id = $c['product_id'];
                $title = $this->get_product_title($father_id, $product_id);

                if (isset($res[$_month][$title])) {
                    $res[$_month][$title] = bcadd($res[$_month][$title], $c['income'], 6);

                    if($title == '其它_其它') {
                        $this->report_data[$father_id][$product_id] = $res[$_month][$title];
                    }
                } else {
                    $res[$_month][$title] = $c['income'];
                }
            }
        }

        if($this->report_data) {
            $feishu = new FeishuRepository();
            $feishu->send_card_message_to_chat_group('周报数据->权责收入-产品 ,出现 "其它_其它" 分类:',$this->report_data);
            $this->report_data = [];
        }

        return $this->add_total_product($res,WeeklyStatisticData::TASK_TYPE_PRODUCT);
    }


    /**
     * 获取两个日期之间的所有月份
     * @param $start_day
     * @param $end_day
     * @param $fmort
     * @return array
     */
    private function getMonthList($start_day,$end_day,$fmort){
        //获取间隔时间相隔的月数
        $months = [];
        try {
            $start = new DateTime($start_day);
            $end   = new DateTime($end_day);
            // 时间间距 这里设置的是一个月
            $interval = DateInterval::createFromDateString('1 month');
            $period   = new DatePeriod($start, $interval, $end);
            foreach ($period as $dt) {
                $months[] = $dt->format($fmort);
            }
        } catch (Exception $e) {
        }
        return $months;
    }


    /**
     * 产品统计标题
     *
     * @param $father_id
     * @param $product_id
     *
     * @return mixed|string
     * <AUTHOR> 2024-01-24 15:36:55
     */
    private function get_product_title($father_id, $product_id) {
        if (in_array($product_id, array_keys($this->product_title_map))) {
            $title = $this->product_title_map[$product_id];
        }else if (in_array($father_id, array_keys($this->product_title_map))) {
            $title = $this->product_title_map[$father_id];
        } else {
            $title = '其它_其它';
        }

        return $title;
    }


    /**
     * 银行
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-24 17:33:14
     */
    private function weekly_income_bank($start, $end): array {
        //获取银行客户id
        $bank_list = Customer::getBankCustomers();
        $bank_customer_ids = array_column($bank_list, 'customer_id');
        $bank_customer_names = array_column($bank_list, 'name','customer_id');

        $res = [];
        $months = Func::getMonthRanges($start,$end);

        foreach ($months as $month) {

            $dimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;
            $consume = (new MainRepository())->getBaseIncome($dimension, $month[0], $month[1], $bank_customer_ids,[],[],[],['is_query_month_data' => 0, 'is_halve_income' => 1]);

            $_month = date('Ym',strtotime($month[0]));

            foreach ($consume['data'] as $c) {
                if(bccomp($c['income'], 0,6) == 0){
                    continue;
                }

                $customer_name = $bank_customer_names[$c['customer_id']];
                if (isset($res[$_month][$customer_name])) {
                    $res[$_month][$customer_name] = bcadd($res[$_month][$customer_name],$c['income'], 6);
                } else {
                    $res[$_month][$customer_name] = $c['income'];
                }
            }
        }
        return $this->add_total($res,WeeklyStatisticData::TASK_TYPE_BANK);
    }

    /**
     *
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws \Exception
     * <AUTHOR> 2025-01-20 18:57:58
     */
    private function weekly_remit($start, $end) {
        $res = [];
        $months = Func::getMonthRanges($start,$end);

        foreach($months as $month) {
            $start_day = strtotime($month[0]);
            $end_day   = strtotime($month[1]);//这里调用方法中条件使用小于而不是小于等于
            $remit_res = Remit::getListByCustomerIdsAndRemitDate($this->customer_ids, $start_day, $end_day);

            $receipt_serials = array_column($remit_res,'receipt_serial');

            $_split_receipt_list = RemitSplitPrice::getListWithReceiptSerials($receipt_serials);
            $split_receipt_list = [];
            foreach($_split_receipt_list as $_sp_info){
                $split_receipt_list[$_sp_info['receipt_serial']][] = $_sp_info;
            }

            foreach($remit_res as $info) {
                $receipt_serial = $info['receipt_serial'];
                $month = date('Ym', $info['remit_date']);

                if(!empty($split_receipt_list[$receipt_serial])){
                    foreach($split_receipt_list[$receipt_serial] as $sp_info) {

                        //企服产品 哆唻科技 减半
                        $father_id = RedisCache::instance('productId_fatherId_mapping')->get($sp_info['product_id']);
                        $father_id = $father_id == 0 ? $sp_info['product_id'] : $father_id;
                        if ($info['customer_id'] == self::CUSTOMER_DUOLAIKEJI || $father_id == self::PRODUCT_QIFU) {
                            $sp_info['money'] = bcdiv($sp_info['money'], 2, 6);
                        }

                        $dept = $this->get_dept_by_customer_id_and_monty($info['customer_id'],$month);

                        if (isset($res[$month][$dept])) {
                            $res[$month][$dept] = bcadd($res[$month][$dept], $sp_info['money'], 6);
                        } else {
                            $res[$month][$dept] = $sp_info['money'];
                        }
                    }
                }
            }

        }

        return $this->add_total($res,WeeklyStatisticData::TASK_TYPE_REMIT);
    }

    private function get_dept_by_customer_id_and_monty($customer_id,$month){
        $tk    = Func::get_tmp_key($month, $customer_id);
        $dept  = $this->customer_dept_map[$tk];
        //营销-内部 合并到 华东非银部
        if($dept == '营销-内部'){
            $dept = '华东非银部';
        }

        return $dept;
    }


    /**
     *
     *
     * @param array $res
     * @param       $type
     *
     * @return array
     * <AUTHOR> 2024-01-24 19:12:16
     */
    private function add_total(array $res,$type): array {
        $result = [];
        $sr = $this->sort_roles[$type];

        foreach ($res as $month => $info) {
            $month_count = 0;
            foreach ($info as $money) {
                $month_count = bcadd($month_count, $money, 6);
            }

            uksort($info, function ($a, $b) use ($sr) {
                return ($sr[$a] ?? 0) < ($sr[$b] ?? 0);
            });

            $result[$month]         = $info;
            $result[$month]['合计'] = $month_count;
        }

        return [
            WeeklyStatisticData::TASK_STATUS_DONE,
            $result
        ];
    }



    /**
     *
     *
     * @param array $res
     * @param       $type
     *
     * @return array
     * <AUTHOR> 2024-01-24 19:12:16
     */
    private function add_total_product(array $res,$type): array {
        $result = [];
        $sr = $this->sort_roles[$type];

        $product_names = array_unique(array_values($this->product_title_map));

        foreach ($res as $month => $info) {
            $month_count = 0;

            foreach($product_names as $product_name) {
                if (!isset($info[$product_name])) {
                    $info[$product_name] = 0;
                }
            }

            foreach ($info as $money) {
                $month_count = bcadd($month_count, $money, 6);
            }

            // 排序主分类,次分类
            uksort($info, function ($a, $b) use ($sr) {
                return ($sr[$a] ?? 0) < ($sr[$b] ?? 0);
            });

            $result[$month]         = $info;
            $result[$month]['合计_'] = $month_count;
        }
        $final_result = [];

        $final_result[0]['count'] = '1';
        $final_result[0]['prefix'] = '_';
        $final_result[0]['suffix'] = '_';
        $title_count = [];

        // 每行: 合并数量 主分类,次分类 月份金额
        //  _ _ _ 202401 202402 ...
        // 2 运营商 邦信分-通信指数 2222 1111 ....
        // 0 运营商 邦信分-通信评分 2222 1111 ....
        foreach($result as $month => $info) {
            $final_result[0][$month] = $month;
            $idx = 1;
            foreach($info as $product_name => $money) {
                [$prefix,$suffix] = explode('_',$product_name);

                // 计算合并单元格數量
                if(isset($title_count[$product_name])) {
                    $title_count[$product_name] = $title_count[$product_name] + 1;
                }else {
                    $title_count[$product_name] = 1;
                }

                if($title_count[$product_name] == 1) {
                    $final_result[$idx]['prefix'] = $prefix;
                    $final_result[$idx]['suffix'] = $suffix;
                }
                $final_result[$idx][$month] = $money;
                $idx = $idx + 1;
            }
        }

        // 合并的是具体数字,被合并的是0
        $len = count($final_result) - 1;
        $last_prefix = '';
        $last_count = 1;
        for($i = $len; $i >= 0; $i--) {
            $curr_prefix = $final_result[$i]['prefix'];
            if($curr_prefix != $last_prefix) {
                $last_prefix = $curr_prefix;
                if($i < $len) {
                    $final_result[$i + 1]['count'] = $last_count;
                }
                $last_count = 1;
            }else {
                $last_count = $last_count + 1;
            }
            if($i > 0) {
                $final_result[$i]['count'] = '0';
            }
        }



        // 排序  将分类放在前面
        $final_sort = [
            'count' => 10,
            'prefix' => 20,
            'suffix' => 60,
        ];

        foreach($final_result as &$info) {
            uksort($info, function ($a, $b) use ($final_sort) {
                return ($final_sort[$a] ?? $a) > ($final_sort[$b] ?? $b);
            });

        }

        return [
            WeeklyStatisticData::TASK_STATUS_DONE,
            $final_result
        ];
    }


    /**
     *
     *
     * @param array $res
     * @param       $type
     *
     * @return array
     * <AUTHOR> 2025-03-24 10:18:52
     */
    private function add_total_customer(array $res,$type): array {
        $result = [];
        $sr = $this->sort_roles[$type];

        foreach ($res as $month => $info) {
            ksort($info,SORT_STRING);
            $month_count = 0;

            foreach ($info as $money) {
                $month_count = bcadd($month_count, $money, 6);
            }

            // 排序主分类,次分类
            uksort($info, function ($a, $b) use ($sr) {
                $a = explode('_',$a)[0];
                $b = explode('_',$b)[0];

                return ($sr[$a] ?? 0) < ($sr[$b] ?? 0);
            });

            $result[$month]         = $info;
            $result[$month]['合计_'] = $month_count;
        }
        $final_result = [];

        $final_result[0]['count'] = '1';
        $final_result[0]['prefix'] = '主体';
        $final_result[0]['suffix'] = '客户';
        $title_count = [];

        // 每行: 合并数量 主分类,次分类 月份金额
        //  _ _ _ 202401 202402 ...
        // 2 运营商 邦信分-通信指数 2222 1111 ....
        // 0 运营商 邦信分-通信评分 2222 1111 ....
        foreach($result as $month => $info) {
            $final_result[0][$month] = $month;
            $idx = 1;
            foreach($info as $product_name => $money) {
                [$prefix,$suffix] = explode('_',$product_name);

                // 计算合并单元格數量
                if(isset($title_count[$product_name])) {
                    $title_count[$product_name] = $title_count[$product_name] + 1;
                }else {
                    $title_count[$product_name] = 1;
                }

                if($title_count[$product_name] == 1) {
                    $final_result[$idx]['prefix'] = $prefix;
                    $final_result[$idx]['suffix'] = $suffix;
                }
                $final_result[$idx][$month] = $money;
                $idx = $idx + 1;
            }
        }

        // 合并的是具体数字,被合并的是0
        $len = count($final_result) - 1;
        $last_prefix = '';
        $last_count = 1;
        for($i = $len; $i >= 0; $i--) {
            $curr_prefix = $final_result[$i]['prefix'];
            if($curr_prefix != $last_prefix) {
                $last_prefix = $curr_prefix;
                if($i < $len) {
                    $final_result[$i + 1]['count'] = $last_count;
                }
                $last_count = 1;
            }else {
                $last_count = $last_count + 1;
            }
            if($i > 0) {
                $final_result[$i]['count'] = '0';
            }
        }



        // 排序  将分类放在前面
        $final_sort = [
            'count' => 10,
            'prefix' => 20,
            'suffix' => 60,
        ];

        foreach($final_result as &$info) {
            uksort($info, function ($a, $b) use ($final_sort) {
                return ($final_sort[$a] ?? $a) > ($final_sort[$b] ?? $b);
            });

        }

        return [
            WeeklyStatisticData::TASK_STATUS_DONE,
            $final_result
        ];
    }









    /**
     *
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-24 19:22:46
     */
    private function remit_last_week($start, $end) {
        $start_day = strtotime($start);
        $end_day   = strtotime($end);//这里调用方法中条件使用小于而不是小于等于
        $remit_res = Remit::getListByCustomerIdsAndRemitDate($this->customer_ids, $start_day, $end_day);
        $receipt_serials = array_column($remit_res,'receipt_serial');
        $_split_receipt_list = RemitSplitPrice::getListWithReceiptSerials($receipt_serials);
        $customer_ids = array_column($remit_res,'customer_id');
        $customer_infos = Customer::getCustomerListByCustomerIds('*', $customer_ids);
        $company_names = array_column($customer_infos,'company','customer_id');
        $split_receipt_list = [];
        foreach($_split_receipt_list as $_sp_info){
            $split_receipt_list[$_sp_info['receipt_serial']][] = $_sp_info;
        }

        $res = [];
        $salsemans = [];

        $sourceMap = CommonEnumModel::getListByType(1);
        $sourceMap = array_column($sourceMap, 'value', 'name');

        foreach($remit_res as $info) {
            $receipt_serial = $info['receipt_serial'];
            $customer_id = $info['customer_id'];
            $month = date('Ym', $info['remit_date']);

            if ($info['customer_id'] == self::CUSTOMER_DUOLAIKEJI) {//哆唻科技 减半
                $info['money'] = bcdiv($info['money'], 2, 6);
            }

            $product_name = [];
            if(!empty($split_receipt_list[$receipt_serial])){
                foreach($split_receipt_list[$receipt_serial] as $sp_info) {
                    $father_id = RedisCache::instance('productId_fatherId_mapping')->get($sp_info['product_id']);
                    $father_id = $father_id ?: $sp_info['product_id'];
                    $product_name[$father_id] = RedisCache::instance('productId_productName_mapping')->get($father_id);
                    if ($father_id == self::PRODUCT_QIFU) {//企服产品 减半
                        $info['money'] = bcsub($info['money'],bcdiv($sp_info['money'], 2, 6),6);
                    }
                }
            }


            $tk = Func::get_tmp_key($month, $customer_id);
            $dept  = $this->customer_dept_map[$tk];

            //到款日期,公司名称,客户名称,金额,区域,产品,商务
            $remit_date = date('Y-m-d', $info['remit_date']);
            $ttk = Func::get_tmp_key($remit_date, $info['name']);

            if(key_exists($ttk, $res)){//合并一天多条回款
                $res[$ttk]['money'] = bcadd($res[$ttk]['money'],$info['money'],6);
                $res[$ttk]['product_name'] = array_unique(array_merge($res[$ttk]['product_name'],$product_name));
            }else{
                $customer_name = RedisCache::instance('customerId_customerName_mapping')->get($customer_id);
                $company_name = $company_names[$customer_id];
                $salesman = $this->cshr_month[$customer_id][$month];
                if($info['source'] != 0){
                    $company_name = $sourceMap[$info['source']];
                    $customer_name = '';
                    $salesman = '';
                }
                $item = [
                    'remit_date'    => $remit_date,
                    'company_name'  => $company_name,
                    'customer_name' => $customer_name,
                    'money'         => $info['money'],
                    'dept'          => $dept,
                    'product_name'  => $product_name,//implode(",",$product_name),
                    'salesman'      => $salesman,
                ];
                $res[$ttk]   = $item;
                $salsemans[] = $item['salesman'];
            }
        }

        $crs_user_map = SystemUser::getInfosByUserName($salsemans);
        $crs_user_map = array_column($crs_user_map, 'realname','username');

        $result = [];
        foreach($res as $res_info){
            $res_info['product_name'] = implode(',', $res_info['product_name']);
            $res_info['salesman'] = $crs_user_map[$res_info['salesman']] ?? $res_info['salesman'];
            $result[] = $res_info;
        }

        return [
            WeeklyStatisticData::TASK_STATUS_DONE,
            $result
        ];
    }

    /**
     *
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws Exception
     * <AUTHOR> 2024-01-24 19:22:46
     */
    private function product_last_week($start, $end) {
        $dimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;
        $result = (new MainRepository())->getBaseIncome($dimension, $start, $end, $this->customer_ids,[],[],[],['is_query_month_data' => 0, 'is_halve_income' => 1]);

        $res = [];
        // 用于复用 add_total_product 方法
        $month = $start.'-'.$end;
        foreach ($result['data'] as $item){
            $father_id = $item['father_id'];
            $product_id = $item['product_id'];

            $title = $this->get_product_title($father_id, $product_id);

            if(!isset($res[$month][$title])){
                $res[$month][$title] = 0;
            }
            $res[$month][$title] = bcadd($res[$month][$title],$item['income'],6);

            if($title == '其它_其它') {
                $this->report_data[$father_id][$product_id] = $res[$month][$title];
            }
        }

        if($this->report_data) {
            $feishu = new FeishuRepository();
            $feishu->send_card_message_to_chat_group('周报数据->上周产品权责收 ,出现 "其它_其它" 分类:',$this->report_data);
            $this->report_data = [];
        }

        return $this->add_total_product($res,WeeklyStatisticData::TASK_TYPE_PRODUCT);
    }


    /**
     *
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws \DateMalformedStringException
     * <AUTHOR> 2025-03-21 18:08:43
     */
    private function dept_last_week($start, $end): array {
        $dimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;

        $res = [];
        $months = Func::getMonthRanges($start,$end);

        $title = $start.'-'.$end;
        foreach($months as $month) {
            //获取消耗
            $consume = (new MainRepository())->getBaseIncome($dimension, $month[0], $month[1], $this->customer_ids,[],[],[],['is_query_month_data' => 0, 'is_halve_income' => 1]);
            $_month = date('Ym',strtotime($month[0]));
            foreach ($consume['data'] as $c) {
                if(bccomp($c['income'], 0,6) == 0){
                    continue;
                }

                $tk   = Func::get_tmp_key($_month, $c['customer_id']);
                $dept = $this->customer_dept_map[$tk];
                //营销-内部 合并到 华东非银部
                if($dept == '营销-内部'){
                    $dept = '华东非银部';
                }
                if (!isset($res[$title][$dept])) {
                    $res[$title][$dept] = 0;
                }

                $res[$title][$dept] = bcadd($res[$title][$dept],$c['income'], 6);
            }
        }

        //排序规则与 权责收入-区域 一致
        return $this->add_total($res,WeeklyStatisticData::TASK_TYPE_DEPT);
    }


    /**
     *
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws \DateMalformedStringException
     * @throws Exception
     * <AUTHOR> 2025-03-21 18:38:43
     */
    private function customer_last_week($start, $end) {
        $dimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;


        $customer_list = Customer::getAllCustomerWithDeleted();
        $customer_id_name_map = [];
        $customer_id_group_id_map = [];
        foreach($customer_list as $customer){
            $customer_id_name_map[$customer['customer_id']] = $customer['name'];
            $customer_id_group_id_map[$customer['customer_id']] = $customer['group_id'];
        }
        $group_list = CustomerGroup::getGroupList();
        $group_naem_map = array_column($group_list, 'group_name', 'group_id');

        $res = [];
        $months = Func::getMonthRanges($start,$end);

        $_month = $start.'-'.$end;
        foreach($months as $month) {
            //获取消耗
            $consume = (new MainRepository())->getBaseIncome($dimension, $month[0], $month[1], $this->customer_ids,[],[],[],['is_query_month_data' => 0, 'is_halve_income' => 1]);

            foreach ($consume['data'] as $c) {
                if(bccomp($c['income'], 0,6) == 0){
                    continue;
                }

                $customer_name = $customer_id_name_map[$c['customer_id']] ?? '';
                $group_name = $group_naem_map[$customer_id_group_id_map[$c['customer_id']]] ?? '-';
                $title = $group_name.'_'.$customer_name;

                if (!isset($res[$_month][$title])) {
                    $res[$_month][$title] = 0;
                }

                $res[$_month][$title] = bcadd($res[$_month][$title],$c['income'], 6);
            }
        }

        return $this->add_total_customer($res,WeeklyStatisticData::TASK_TYPE_CUSTOMER_LAST_WEEK);
    }


    /**
     * 生成上周商务数据
     *
     * @param $start
     * @param $end
     *
     * @return array
     * @throws \DateMalformedStringException
     * <AUTHOR> 2025-03-24 10:21:11
     */
    private function salesman_last_week($start, $end) {

        $dimension = StatDefine::DIMENSION_APIKEY_PRODUCT_OPERATOR;

        $res = [];
        $months = Func::getMonthRanges($start,$end);


        $user_list = \App\Models\SystemUser::getAllUsers();
        $user_list = array_column($user_list, 'realname','username');

        $title = $start.'-'.$end;
        foreach($months as $month) {
            //获取消耗
            $consume = (new MainRepository())->getBaseIncome($dimension, $month[0], $month[1], $this->customer_ids,[],[],[],['is_query_month_data' => 0, 'is_halve_income' => 1]);

            $_month = date('Ym',strtotime($month[0]));

            foreach ($consume['data'] as $c) {
                if(bccomp($c['income'], 0,6) == 0){
                    continue;
                }

                $salesman = $this->cshr_month[$c['customer_id']][$_month] ?? '';
                if($salesman != '') {
                    $salesman = $user_list[$salesman] ?? '';
                }

                if (!isset($res[$title][$salesman])) {
                    $res[$title][$salesman] = 0;
                }

                $res[$title][$salesman] = bcadd($res[$title][$salesman],$c['income'], 6);
            }
        }

        return $this->add_total($res,WeeklyStatisticData::TASK_TYPE_SALEMAN_LAST_WEEK);
    }
}
