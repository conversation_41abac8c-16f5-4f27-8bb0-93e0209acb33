<?php
namespace App\Console\Commands\WeeklyStatistic;

use App\Http\Repository\WeeklyStatisticRepository;
use Illuminate\Console\Command;

/**
 * 每周统计数据 添加统计任务脚本
 * php artisan weekly_statistic:add_task
 */
class AddTask extends Command
{
    protected $signature   = 'weekly_statistic:add_task';
    protected $description = '添加每周统计数据任务';

    /**
     *
     * 从数据库获取待执行的统计数据类型以及时间
     *
     * @return void
     * <AUTHOR> 2024-01-23 14:38:57
     */
    public function handle() {
        $res = WeeklyStatisticRepository::batch_add_task();

        if(!$res){
            $this->output->warning("【{$this->description}】 添加任务失败!");
        }
    }
}