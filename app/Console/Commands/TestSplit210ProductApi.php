<?php

namespace App\Console\Commands;

use App\TraitUpgrade\CurlTrait;
use Illuminate\Console\Command;

/**
 * Class TestSplit210ProductApi 拆分邦信分快捷版产品时，对接口提供测试
 * @package App\Console\Commands
 */
class TestSplit210ProductApi extends Command
{
	use CurlTrait;
	protected $signature = 'test:split_210_api';
	
	public function handle()
	{
		//获取线上的数据
		$prod = $this->getProdData(210, '99bbdb8426f8b4e8d0cc3ebd92484590');
		ksort($prod);
		file_put_contents('./prod_210.json', json_encode($prod, JSON_UNESCAPED_UNICODE));
		//获取测试环境的数据
		$dev = $this->getDevData(210, '99bbdb8426f8b4e8d0cc3ebd92484590');
		ksort($dev);
		file_put_contents('./dev_210.json', json_encode($dev, JSON_UNESCAPED_UNICODE));
		$this->compare($prod, $dev);
		
		$prod_200 = $this->getProdData('201,202,203,204,205,206,207,208,211,212,213,214,215,216,230,217,231,218,900,219,220,232,233,234,237,235,221,222,238,310,311,312,313,314,315,316,317', 'fbaace1340a8706863ed6ae17560355c');
		$dev_200 = $this->getProdData('201,202,203,204,205,206,207,208,211,212,213,214,215,216,230,217,231,218,900,219,220,232,233,234,237,235,221,222,238,310,311,312,313,314,315,316,317', 'fbaace1340a8706863ed6ae17560355c');
		file_put_contents('./prod_200.json', json_encode($prod_200, JSON_UNESCAPED_UNICODE));
		file_put_contents('./dev_200.json', json_encode($dev_200, JSON_UNESCAPED_UNICODE));
		$this->compare($prod, $dev);
	}
	
	protected function getProdData($product_tag, $key)
	{
		$url  = 'http://back-api.dianhua.cn/product/getAccountInfo';
		$data = $this->postRawJson($url, compact('product_tag', 'key'));
		
		return $data['data'];
	}
	
	protected function getDevData($product_tag, $key)
	{
		$url  = 'http://backapi-cui.dianhua.cn/product/getAccountInfo';
		$data = $this->postRawJson($url, compact('product_tag', 'key'));
		
		return $data['data'];
	}
	
	protected function compare($prod, $dev, $prefix = '')
	{
		
		foreach ($prod as $prod_key => $prod_item) {
		
		}
	}
}