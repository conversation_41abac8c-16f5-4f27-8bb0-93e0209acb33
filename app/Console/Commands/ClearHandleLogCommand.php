<?php

namespace App\Console\Commands;

use App\Models\HandleLog;
use App\TraitUpgrade\WechatExceptionTrait;
use Illuminate\Console\Command;

class ClearHandleLogCommand extends Command
{
    use WechatExceptionTrait;

    public $signature = 'clear:handle-log';
    public $description = '定时清理handle_log';

    /**
     * @throws \Exception
     */
    public function handle()
    {
        try {
            $this->clearHistoryLog();
        } catch (\Exception $e) {
            $msg = 'back-api: 定期清理handle_log失败 msg:' . $e->getMessage();
            $this->wechatException($msg);
        }
    }

    /**
     * 定期清理历史数据
     */
    private function clearHistoryLog()
    {
        // 条件
        $where = $this->genParamsForClear();

        // do
        HandleLog::where($where)
            ->delete();

        $this->info('历史日志已经被清理！');
    }

    /**
     * 条件
     * @return array
     */
    private function genParamsForClear(): array
    {
        $created_at = strtotime('-3 weeks');
        return [
            ['created_at', '<', $created_at]
        ];
    }
}