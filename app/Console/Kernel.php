<?php

namespace App\Console;

use App\Console\Commands\Alarm\ChannelQualicationExpireAlarm;
use App\Console\Commands\BcChannelBalance;
use App\Console\Commands\AutoBottomChannelCost;
use App\Console\Commands\BaseCheck\PullChannelStatDataCommand;
use App\Console\Commands\BaseCheck\PullUserStatDataCommand;
use App\Console\Commands\BaseCheck\PushDataCommand;
use App\Console\Commands\ChannelBalanceAlert;
use App\Console\Commands\CopyChannelStat;
use App\Console\Commands\Customer\BalanceWarningByWechat;
use App\Console\Commands\Customer\CustomerProductLimitWarning;
use App\Console\Commands\Customer\CustomerProductUsage;
use App\Console\Commands\Customer\CustomerAccountByTimeAlarm;
use App\Console\Commands\Customer\DingdingCustomerApprovalProcess;
use App\Console\Commands\Customer\DingdingCustomerApprovalProcessOver;
use App\Console\Commands\BalanceWarningUpdateInfoCommand;
use App\Console\Commands\BillCreateCost;
use App\Console\Commands\BillCostV2\BillCreateCost as BillCreateCostV2;
use App\Console\Commands\BillCreateIncome;
use App\Console\Commands\BillIncomeV2\BillCreateIncome as BillCreateIncomeV2;
use App\Console\Commands\BillMonthRollbackCommand;
use App\Console\Commands\BillReCreateProductIncome;
use App\Console\Commands\BillSectionRollbackCommand;
use App\Console\Commands\BxfPeriodStatCommand;
use App\Console\Commands\BxfQudaoFenbuCommand;
use App\Console\Commands\CacheExcel;
use App\Console\Commands\ClearHandleLogCommand;
use App\Console\Commands\CreateTestStatisticsData;
use App\Console\Commands\Alarm\AccountExpireAlarm;
use App\Console\Commands\Dashboard\CronGatherMiddleCustomer;
use App\Console\Commands\Dashboard\CronGatherMiddleProduct;
use App\Console\Commands\GenCacheCommand;
use App\Console\Commands\GenCrawlerStat;
use App\Console\Commands\GenDayBillCommand;
use App\Console\Commands\GenEconomyUpstreamBill;
use App\Console\Commands\GenMonthBillCommand;
use App\Console\Commands\GenMonthBillWarningCommand;
use App\Console\Commands\GenShortcutPackageDayBill;
use App\Console\Commands\GenShortcutPackageMonthBill;
use App\Console\Commands\GenUpstreamBill;
use App\Console\Commands\InitAmountCacheCommand;
use App\Console\Commands\InitBillSectionCommand;
use App\Console\Commands\InitBillSectionWarningCommand;
use App\Console\Commands\InitProductInvokedAmount;
use App\Console\Commands\InitUpstreamChannel;
// use App\Console\Commands\Invoice\CollectionIncomeData;
// use App\Console\Commands\Invoice\CollectionInvoiceData;
use App\Console\Commands\Invoice\InitCustomerModelConfig;
use App\Console\Commands\Invoice\GenerateMonthConsume;
use App\Console\Commands\Invoice\AutoRelateRemitInvoice;
use App\Console\Commands\Invoice\TransferInvoiceData;
use App\Console\Commands\Invoice\FixOldInvoiceData;
use App\Console\Commands\Invoice\FixCustomerConsumeData;
use App\Console\Commands\Invoice\CheckInvoiceData;
use App\Console\Commands\Invoice\MatchRemitInvoice;
use App\Console\Commands\Invoice\CustomerBalance;
use App\Console\Commands\LtSendEmailCommand;
use App\Console\Commands\BxfreturnSendEmailCommand;
use App\Console\Commands\Monitor\AntFinancialValueSpreadCommand;
use App\Console\Commands\Monitor\MonitorCreateSuccessRateCacheCommand;
use App\Console\Commands\Monitor\MonitorCreateValueSpreadCacheCommand;
use App\Console\Commands\MonitorQueueManagerCommand;
use App\Console\Commands\MonitorTableManagerCommand;
use App\Console\Commands\MonitorTestInQueueCommand;
use App\Console\Commands\Opdata\CurlOpdata227Command;
use App\Console\Commands\Opdata\CustomerUsageSplitCommand;
use App\Console\Commands\Opdata\PullSelfRuleMonitor;
use App\Console\Commands\Opdata\StatSelfRuleExtend;
use App\Console\Commands\Operator\OperatorMinimumCharge;
use App\Console\Commands\Operator\UpdateChannelAccountData;
use App\Console\Commands\PullDatas\PullHaomaYujingData;
use App\Console\Commands\PullDatas\PullCustomerRealValidData;
use App\Console\Commands\PullDatas\PullFeishuPublicityApprovalProcess;
use App\Console\Commands\PullDatas\PullXBongBong;
use App\Console\Commands\SendPeriodReminderMail;
use App\Console\Commands\TransferData\TransferHmrInterfaceUsage;
use App\Console\Commands\TransferData\CreateBillIncomeMonthData;
use App\Console\Commands\TransferData\CreateBillCostMonthData;
use App\Console\Commands\TransferData\CreateCustomerUsageMonthData;
use App\Console\Commands\TransferData\UpdateSpecialProductBillIncome;
use App\Console\Commands\PushDatas\PushInterfaceUsageForEnterpriseService;
use App\Console\Commands\PushDatas\PushFeishuPublicityApprovalProcess;
//use App\Console\Commands\PushDatas\PushXBongBong2;
use App\Console\Commands\PushDatas\SyncCommonData;
use App\Console\Commands\RedisCache\RedisCacheReloadCommand;
use App\Console\Commands\ReportDay\ProfitForLeader;
use App\Console\Commands\ReportDay\SendReportDayNotice;
use App\Console\Commands\ReportDay\CreateProfitDataForLeader;
use App\Console\Commands\ReportDay\ProfitForUser;
use App\Console\Commands\ReportDay\ProfitForBusinessman;
use App\Console\Commands\ReportDay\StatisticForProduct;
use App\Console\Commands\ReportDay\CostForChannel;
use App\Console\Commands\ReportDay\StatisticForProductNoAuth;
use App\Console\Commands\ReportDayProfitBmy;
use App\Console\Commands\ReportDayProfitGoldShield;
use App\Console\Commands\ReportDayProfitNormalProduct;
//use App\Console\Commands\ReportDayStatistic200;
//use App\Console\Commands\ReportDayStatistic210;
//use App\Console\Commands\ReportDayStatistic210Field;
//use App\Console\Commands\ReportDayStatistic210Score;
use App\Console\Commands\ReportDayStatistic615;
//use App\Console\Commands\ReportDayStatistic615V2;
//use App\Console\Commands\ReportDayStatistic664;
use App\Console\Commands\ReportDayUpstreamChannelBill;
use App\Console\Commands\ReportMonthProfit;
use App\Console\Commands\ReportMonthProfitBmy;
use App\Console\Commands\ReportMonthProfitGoldShield;
use App\Console\Commands\ReportMonthProfitNormalProduct;
use App\Console\Commands\ReportWeekProfit;
use App\Console\Commands\ReportWeek\ReportWeekProfit as ReportWeekProfitNew;
use App\Console\Commands\ReportWeek\CostForChannel as CostForChannelWeek;
use App\Console\Commands\ReportMonth\ReportMonthProfit as ReportMonthProfitNew;
use App\Console\Commands\ReportWeekProfitBmy;
use App\Console\Commands\ReportWeekProfitGoldShield;
use App\Console\Commands\ReportWeekProfitNormalProduct;
use App\Console\Commands\ReportWeekUpsteamChannelBill;
use App\Console\Commands\SecondaryProcessingScoreRequestInfo;
use App\Console\Commands\SecondaryProcessingStatisRequestInfo;
use App\Console\Commands\StatisticsTransferInfo;
use App\Console\Commands\Temp\TempPudaoused;
use App\Console\Commands\TemporaryRegulateUpstreamStatisticsCommand;
use App\Console\Commands\TempSplit210ProductDisposeData;
use App\Console\Commands\TempSplitBillMonths;
use App\Console\Commands\TempTransferBillMonths;
use App\Console\Commands\TempTransferConfigPriceInterface;
use App\Console\Commands\TempTransferFeeConfig;
use App\Console\Commands\TempTransOldCost;
use App\Console\Commands\TempUpdateAccountProduct210;
use App\Console\Commands\Test;
use App\Console\Commands\TestAlarmRedis;
use App\Console\Commands\TestCreateAllValueSpread;
use App\Console\Commands\TestSplit210ProductApi;
use App\Console\Commands\TestWashChannelLogs;
use App\Console\Commands\TransferRevenue;
use App\Console\Commands\TransInterfaceStat;
use App\Console\Commands\UploadCommand;
use App\Console\Commands\SendEmailCommand;
use App\Console\Commands\BxfPeriodCommand;
use App\Console\Commands\WeeklyStatistic\AddTask;
use App\Console\Commands\WeeklyStatistic\WeeklyStatistic;
use Illuminate\Console\Scheduling\Schedule;
use Laravel\Lumen\Console\Kernel as ConsoleKernel;
use App\Console\Commands\ReportDay\OperatorMonitoring;
use App\Console\Commands\Monitor\OperatorMature;
use App\Console\Commands\ProcessCostData;
use App\Console\Commands\CompareData\UsageRelated;
use App\Console\Commands\CompareData\PackageBillingMonitor;
use  App\Console\Commands\ProcessData\ProcessCustomerAlarmData;
use  App\Console\Commands\ProcessData\DingdingApprovalProcess;
use App\Console\Commands\ProcessData\DingdingApprovalProcessOver;
use App\Console\Commands\TempTransOldCostAdjust;
use App\Console\Commands\Server\UpdateServerAlarm;
use App\Console\Commands\TransferData\TransferTogetherCallUsage;
use App\Console\Commands\SXCmccRemainAmount;
use App\Console\Commands\Customer\CustomerFirstIncome;
use App\Console\Commands\Customer\CustomerFirstIncomeV1;
use App\Console\Commands\Customer\CustomerIncomeDetail;
use App\Console\Commands\Customer\CustomerExpireAlarm;
use App\Console\Commands\Monitor\ProductPeroidList;
use App\Console\Commands\Customer\ChannelIncome;
use App\Console\Commands\Customer\ProductWeekIncome;
use App\Console\Commands\AccountProductExpire;
use App\Console\Commands\ChannelCostMinConsumeSpread;
use App\Console\Commands\Customer\AccountProductLimitAlarm;
//use App\Console\Commands\Monitor\SourceProportionMonitor;
use App\Console\Commands\Customer\CustomerIncomeCalculate;
use App\Console\Commands\Customer\YearlyNewOldCustomer;
use App\Console\Commands\Balance\MonthlyBalance;
use App\Console\Commands\ReportDay\ChangeToPudao;
use App\Console\Commands\Remit\AutoSplitPrice;
use App\Console\Commands\Monitor\ProductNoPriceMonitor;
use App\Console\Commands\Monitor\ChannelNoPriceMonitor;
use App\Console\Commands\Monitor\DiffOperatorNoPriceMonitor;
use App\Console\Commands\Monitor\ChannelContractDueMonitor;
use App\Console\Commands\PreTestManage\PullApplyList;
use App\Console\Commands\PreTestManage\FeedbackClock;
use App\Console\Commands\PreTestManage\CheckProductAccess;
use App\Console\Commands\PreTestManage\StatisticsApplyProduct;
use App\Console\Commands\PreTestManage\ImportApplyResult;
use App\Console\Commands\PreTestManage\RecordActionLog;
use App\Console\Commands\PreTestManage\Monitor\InitCustomerProduct;
use App\Console\Commands\PreTestManage\Monitor\StatisticsCustomerProduct;
use App\Console\Commands\PreTestManage\CheckApplyStatus;
use App\Console\Commands\PreTestManage\CheckManual;
use App\Console\Commands\PreTestManage\Monitor\RunDockingStatus;
use App\Console\Commands\PreTestManage\ReportWeeklyPre;
use App\Console\Commands\PreTestManage\Monitor\RunAbnormalEmail;
use App\Console\Commands\PreTestManage\Monitor\PullApprovalList;
use App\Console\Commands\PreTestManage\Monitor\RunDockingApproval;
use App\Console\Commands\PreTestManage\Monitor\CompleteCustomerProduct;
use App\Console\Commands\PreTestManage\Monitor\StatisticsCustomerProductWeek;
use App\Console\Commands\Feishu\PullApprovalInstance;
use App\Console\Commands\Contract\ContractArchiveReminder;
use App\Console\Commands\ReportTask\RunEstimateWeeklyIncome;

class  Kernel extends ConsoleKernel
{
	/**
	 * The Artisan commands provided by your application.
	 *
	 * @var array
	 */
	protected $commands = [
	    InitAmountCacheCommand::class, // 初始化每天每个待用产品的日月年调用量的redis属性
		ClearHandleLogCommand::class, // 定时清理handle_log
		InitProductInvokedAmount::class, // 初始化每天每个待用产品的日月年调用量的redis属性（全量版本）
		InitBillSectionCommand::class, // 生成账单的分割区间
		GenMonthBillCommand::class, // 生成月账单的命令
		BillMonthRollbackCommand::class, // 回滚上次执行generate:bill-month --range=$range产生的账单和日志
		BillSectionRollbackCommand::class, // 回滚上次之心 bill:inti-sections产生的计费配置
		InitBillSectionWarningCommand::class, // 初始化本月的账单计费配置区间(余额预警使用)
		GenMonthBillWarningCommand::class, // 生成本月的账单(余额预警使用)
		GenDayBillCommand::class,  // 生成日账单

		//SendEveryWeekBillCommand::class,

		GenCrawlerStat::class, // 生成爬虫统计相关的统计信息

		CreateTestStatisticsData::class,
		CacheExcel::class,       //生成CacheExcel缓存
		GenShortcutPackageMonthBill::class,      //快捷版打包的月账单生成
		GenShortcutPackageDayBill::class,         //快捷版打包的日账单生成
		GenCacheCommand::class,                    //缓存信息生成

		//临时脚本
		TemporaryRegulateUpstreamStatisticsCommand::class,      //整理上游数据源统计量的账单
		TransferRevenue::class,            //转移邦秒验的营收到金盾中

		//上游数据源及成本核算
		InitUpstreamChannel::class,     //同步上游数据源的渠道
		GenUpstreamBill::class,         //生成上游的数据源的账单
		GenEconomyUpstreamBill::class,    //计算金盾为邦秒验节约的成本账单

		#权责利润报表
		ReportWeekProfit::class,     //周报
        ReportWeekProfitNew::class,     //周报(新版本)
		ReportMonthProfit::class,    //旧版月报
        ReportMonthProfitNew::class,    //新版月报
		ReportDayProfitNormalProduct::class,    //综合产品（产品ID:101、501、210（区分普通字段和评分字段）、104、105、601、604）日报
		ReportWeekProfitNormalProduct::class,    //综合产品周报
		ReportMonthProfitNormalProduct::class,    //综合产品月报
		ReportDayProfitGoldShield::class,    //金盾产品（产品ID：603、612、613、614、615、616、664）日报
		ReportWeekProfitGoldShield::class,    //金盾产品周报
		ReportMonthProfitGoldShield::class,    //金盾产品月报
		ReportDayProfitBmy::class,        //技术二部产品（401、200、801）日报
		ReportWeekProfitBmy::class,        //技术二部产品周报
		ReportMonthProfitBmy::class,    //技术二部产品月报


		#邦信分监控数据的二次处理
		SecondaryProcessingScoreRequestInfo::class,     //评分类字段的数据处理
		SecondaryProcessingStatisRequestInfo::class,    //统计类字段的数据处理

		#产品统计日报
		//ReportDayStatistic210::class,       //邦信分快捷版产品调用量日报
		ReportDayStatistic615::class,       //金盾产品调用量日报
		//ReportDayStatistic615V2::class,     //金盾产品调用量日报 V2.0
		//ReportDayStatistic664::class,       //号码预警提示产品调用量日报
		//ReportDayStatistic210Score::class,    //邦信分快捷版-通信评分调用量日报
		//ReportDayStatistic210Field::class,    //邦信分快捷版-通信字段调用量日报
		//ReportDayStatistic200::class,        //邦秒验日报表

		//成本渠道日报、周报
		ReportWeekUpsteamChannelBill::class,        //成本渠道周报
		ReportDayUpstreamChannelBill::class,        //成本渠道日报


		UploadCommand::class,               //删除金盾历史数据
		SendEmailCommand::class,            //邦信分邮件预警
		BxfPeriodCommand::class,            //邦信分账期邮件预警
		BxfPeriodStatCommand::class,        //邦信分账期统计脚本

		LtSendEmailCommand::class,          //联通数据稳定性监控
		BxfreturnSendEmailCommand::class,   //邦信分不同返回值占比监控
		BxfQudaoFenbuCommand::class,        //邦信分各个渠道分布占比监控


		//------账单（新）
		BillCreateIncome::class,            //计算并生成营收账单脚本
		BillReCreateProductIncome::class,    //重新生成产品营收账单
		BillCreateCost::class,                //计算并生成成本账单脚本
		StatisticsTransferInfo::class,            //转移客户统计数据

		//临时处理脚本
		TempUpdateAccountProduct210::class,        //修改210客户的特殊配置项
		TempTransferFeeConfig::class,        //转移历史的计费配置
		TempSplit210ProductDisposeData::class,    //拆分邦信分快捷版时需要对数据进行处理
		TempTransferBillMonths::class,            //转移旧版本月账单
		TempSplitBillMonths::class,                //拆分历史的邦信分快捷版的账单
		TempTransferConfigPriceInterface::class,    //迁移旧版的接口单价

		//预警系统相关命令
		MonitorTableManagerCommand::class,        //预警库中用户请求日志、渠道访问日志表维护脚本
		MonitorQueueManagerCommand::class,        //预警的队列管理
		MonitorTestInQueueCommand::class,          //预警的队列入队测试脚本
		MonitorCreateSuccessRateCacheCommand::class,    //排重计算蚂蚁的成功调用率
		MonitorCreateValueSpreadCacheCommand::class,    //创建蚂蚁金服值分布的缓存数据
		AntFinancialValueSpreadCommand::class,        //蚂蚁金服去重值分布数据

		//余额预警
		BalanceWarningUpdateInfoCommand::class,    //余额预警数据更新脚本
		BalanceWarningByWechat::class,        //通过微信进行余额预警

		//日报
		ProfitForUser::class,        //权责利润日报表（用户）
		ProfitForLeader::class,        //权责利润日报表（领导）
        SendReportDayNotice::class,        //发送日报链接（领导）
        CreateProfitDataForLeader::class,        //生成权责利润日报基础数据（领导）
        StatisticForProduct::class,     //产品调用量日报
        StatisticForProductNoAuth::class,     //产品调用量日报
        CostForChannel::class,          //成本渠道产品日报
		ProfitForBusinessman::class,	//权责利润日报表（只发销售 20230302）

        CostForChannelWeek::class,  //成本统计周报

		//RedisCache
		RedisCacheReloadCommand::class,        //RedisCache重载

        TempTransOldCost::class,    //将旧版成本数据导入到新版中
        TransInterfaceStat::class, //将旧版接口调用量转移到新版中

		//各类测试
		TestAlarmRedis::class,            //测试报警redis性能的脚本
		TestSplit210ProductApi::class,            //拆分邦信分快捷版时需要进行测试
		TestWashChannelLogs::class,        //清洗渠道日志
		TestCreateAllValueSpread::class,    //测试直接从日志表中获取统计数据的速度
		Test::class,    //测试脚本

        OperatorMature::class,//运营商合同到期/剩余调用天数 警报
        ProcessCostData::class,
        UsageRelated::class, //后台调用量和clickhouse调用量日志比较
        PackageBillingMonitor::class, //根据计费配置打包属性检测检测客户是否打包计费

        ProcessCustomerAlarmData::class, //处理客户报警数据
        DingdingApprovalProcess::class, //钉钉审批处理
        DingdingApprovalProcessOver::class, //钉钉审批超过8小时未处理
        TempTransOldCostAdjust::class, //历史数据调整

        CustomerProductLimitWarning::class,//客户产品限量预警
        CustomerProductUsage::class,//客户产品用量统计
        CustomerAccountByTimeAlarm::class,//客户账号截至日期预警
        DingdingCustomerApprovalProcess::class,//客户钉钉报警流程处理
        DingdingCustomerApprovalProcessOver::class,//客户钉钉报警超8小时未处理

        UpdateServerAlarm::class, //获取更新服务器对比数据库server报警
        TransferTogetherCallUsage::class, //客户非打包账号量转移到打包账号中
        TransferHmrInterfaceUsage::class, //号码融渠道用量预估
        CreateBillIncomeMonthData::class, //生成月账单收入数据到中间表
        CreateBillCostMonthData::class, //生成月账单成本数据到中间表
        CreateCustomerUsageMonthData::class, //生成客户用量月数据到中间表
        UpdateSpecialProductBillIncome::class, //soure为朴道的核验前筛和号码融等特殊产品需要对收入进行更新处理
        SXCmccRemainAmount::class, //获取山西移动实时余额

        CustomerFirstIncome::class, //客户首次权责收入,用于商务提成
        CustomerFirstIncomeV1::class, //客户首次权责收入,用于商务提成
        CustomerIncomeDetail::class, //客户首次权责收入,用于商务提成
        ProductPeroidList::class, //获取各个项目组产品当前使用账期

        ChannelIncome::class, //渠道收入
        ProductWeekIncome::class, //产品周收入
        AccountProductExpire::class, // 账号-产品过期之后自动更改状态
        AccountProductLimitAlarm::class, // 账号-产品测试限量预警
        CustomerIncomeCalculate::class, // /根据客户特殊配置，计算收入设置特殊消耗
        YearlyNewOldCustomer::class,//新老客户统计
        CustomerExpireAlarm::class,// 客户账号到期预警

		ChannelCostMinConsumeSpread::class,//渠道成本保底分摊

        //SourceProportionMonitor::class,//产品调用来源占比监控
        TempPudaoused::class,//产品调用来源占比监控
        AccountExpireAlarm::class,//账号过期时间预警，自动发起钉钉流程
        PullHaomaYujingData::class,//企服产品 用量及费用拉取
        PullCustomerRealValidData::class,//从ck获取计算客户产品真实查得量
        PushInterfaceUsageForEnterpriseService::class,//推送渠道用量到企服
        PullFeishuPublicityApprovalProcess::class,//从飞书获取自定义公出审批实例
        PushFeishuPublicityApprovalProcess::class,//拉取crm访客计划同步至飞书公出审批
        PullXBongBong::class,//销帮帮数据同步后台&数据检测
        //PushXBongBong2::class,
        SyncCommonData::class,//同步后台公用表到ck字典
        OperatorMinimumCharge::class,//运营商最低消费处理
        UpdateChannelAccountData::class,//更新channel_account数据
        PushDataCommand::class,//推送产品用户信息到base-check项目
        PullUserStatDataCommand::class,//拉取指定日期用户统计(base-check)
        PullChannelStatDataCommand::class,//拉取指定日期渠道统计(base-check)

        BillCreateIncomeV2::class,//新版账单专用
        BillCreateCostV2::class,//新版账单专用

        MonthlyBalance::class,//计算每月的余额信息

        ChangeToPudao::class,//切换朴道客户统计

        AutoSplitPrice::class,//预付费客户自动拆单

        CurlOpdata227Command::class,//邦秒验反电诈产品定时调用（临时脚本）
        CustomerUsageSplitCommand::class,//收入拆分（临时脚本）


        // CollectionIncomeData::class,//生成待开具发票数据
        // CollectionInvoiceData::class,//生成发票汇总数据
        TransferInvoiceData::class,//发票数据转移
        FixOldInvoiceData::class,//发票数据修复
        CustomerBalance::class,
        FixCustomerConsumeData::class,//发票数据修复
        CheckInvoiceData::class,//发票数据修复
        MatchRemitInvoice::class,//匹配拆单回款数据于发票数据
        InitCustomerModelConfig::class, // 初始化客户各渠道开票模式
        GenerateMonthConsume::class, // 生成客户上个月消耗数据
        AutoRelateRemitInvoice::class, // 自动关联[到款remit-票invoice]关系


        WeeklyStatistic::class,//每周统计数据
        AddTask::class,//每周统计数据
        ProductNoPriceMonitor::class,//客户有调用量无计费产品预警
        ChannelNoPriceMonitor::class,//渠道有调用量无计费产品预警
        DiffOperatorNoPriceMonitor::class,//客户区分运营商计费有量无钱预警
        ChannelContractDueMonitor::class,//渠道合同到期预警

        // 领导驾驶舱(pc端) 相关脚本
        CronGatherMiddleProduct::class,
        CronGatherMiddleCustomer::class,

        BcChannelBalance::class, #实时计算渠道余额
        AutoBottomChannelCost::class, #每月2号自动执行保底分摊
        CopyChannelStat::class, #复制运营商分支渠道统计
        OperatorMonitoring::class, //运营商每日监控数据
        ChannelBalanceAlert::class, //运营商余额预警

        // 售前测试管理系统 相关脚本
        PullApplyList::class, // 拉取飞书售前测试申请
        FeedbackClock::class, // 打卡信息处理
        CheckProductAccess::class, // 检查产品是否接入
        StatisticsApplyProduct::class, // 统计接入客户调用情况
        ImportApplyResult::class, // 数据导入
        RecordActionLog::class, // 操作日志分析
        InitCustomerProduct::class, // 客户产品监控数据初始化
        StatisticsCustomerProduct::class, // 客户产品监控增量数据计算
        StatisticsCustomerProductWeek::class,//客户产品监控调用量计算
        CheckApplyStatus::class, // 检查工单状态
        CheckManual::class, // 检查需人工介入工单状态
        RunDockingStatus::class, // 计算对接状态
        ReportWeeklyPre::class, // 售前部门-周报统计
        RunAbnormalEmail::class, // 日报-邮件提醒:计算售前客户开通产品异常状态
        PullApprovalList::class,// 拉取客户监控流程工单
        RunDockingApproval::class, // 根据状态提交流程工单
        CompleteCustomerProduct::class, //客户产品监控数据补全

        SendPeriodReminderMail::class, // 金融业务部门账期邮件提醒
        ChannelQualicationExpireAlarm::class, //渠道资质临期报警

        PullSelfRuleMonitor::class,
        StatSelfRuleExtend::class,
        ContractArchiveReminder::class,//合同归档提醒
        PullApprovalInstance::class,//飞书获取审批实例

        RunEstimateWeeklyIncome::class,

    ];
	
	/**
	 * Define the application's command schedule.
	 *
	 * @param  \Illuminate\Console\Scheduling\Schedule $schedule
	 *
	 * @return void
	 */
	protected function schedule(Schedule $schedule)
	{
        ######################### 南哥开发的都在这里面，你们不要往这里写 start ###################################
        $schedule->command('bc_channel_balance')->cron('*/10 * * * *'); #实时计算渠道余额
        $schedule->command('auto_bottom_channel_cost')->cron('0 3 2 * *');#每月2号自动执行保底分摊
        $schedule->command('copy_channel_stat')->cron('30 0 * * *');#复制运营商分支渠道统计
        $schedule->command('channel_balance_alert')->cron('*/5 * * * *');#渠道余额报警
        ######################### 南哥开发的都在这里面，你们不要往这里写 end  ###################################

        ## 测试是否执行
        /**
         * $schedule->command('test')
         * ->everyMinute();
         */

		// 初始化每天每个待用产品的日月年调用量的redis属性
		//		$schedule->command('init-amount:cache')
		//				 ->cron('0 3 * * *');
		//
		//		定时清理handle_log
		//		$schedule->command('clear:handle-log')
		//				 ->cron('0 4 15 * *');
		//
		//		// 初始化每个产品的调用量(新版全量)
		//		$schedule->command('product:init-invoked')
		//				 ->cron('10 3 * * *');
		
		//每隔30分钟重新拉取一次缓存  notice
		$schedule->command('gen:cache')
				 ->everyThirtyMinutes();
		
		/**
		 * +---------------
		 * | 营收账单脚本（月账单）
		 * +---------------
		 **/
		#生成账单片段（切割计费配置）20220325-add comments
		//$schedule->command('bill:init-sections')
		//		 ->cron('0 2 1 * *');
		#根据计费配置生成月账单 20220325-add comments
		//$schedule->command('bill:generate-month')
		//		 ->cron('0 5 1 * *');
		#快捷版打包的月账单 20220325-add comments
		//$schedule->command('bill:generate-shortcut-package-month')
		//		 ->cron('0 6 1 * *');
		#缓存上个月的账单信息，20220325-add comments
		//$schedule->command('bill:cache-excel')
		//		 ->cron('0 7 1 * *');
		/**
		 * +---------------
		 * | 营收账单脚本（日账单）
		 * +---------------
		 **/
		#生成账单片段（切割计费配置）20220325-add comments
        //$schedule->command('bill:warning-init-sections')
		//		 ->cron('0 2 * * *');
		#每天凌晨6点生成本月账单 20220325-add comments 生成本月的账单(余额预警使用)
		//$schedule->command('bill:warning-generate-month')
		//		 ->cron('0 4 * * *');
		#快捷版打包的日账单 20220325-add comments 快捷版打包的日账单生成
		//$schedule->command('bill:generate-shortcut-package-day')
		//		 ->cron('0 5 * * *');
		#根据月账单生成日账单s 20220325-add comments 生成日账单
		//$schedule->command('bill:generate-day')
		//		 ->cron('0 6 * * *');
		
		/**
		 * +---------------
		 * | 成本账单脚本（日账单）
		 * +---------------
		 **/
		#更新渠道 20220325-add comments 同步上游数据源的渠道
		//$schedule->command('upstream:init-channel')
		//		 ->cron('0 2 * * *');
		#根据量与单价生成数据源的日账单 20220325-add comments  生成上游的数据源的账单
		//$schedule->command('upstream:gen-bill')
		//		 ->cron('0 3 * * *');
		#根据金盾成本量，转移邦秒验的收入 20220325-add comments 转移邦秒验的营收到金盾中
		//$schedule->command('bill:transfer-revenue')
		//		 ->cron('0 3 * * *');
		#根据金盾的成本量，计算金盾为邦秒验节约的成本金额  20220325-add comments
		//$schedule->command('upstream:gen-economy-bill')
		//		 ->cron('0 3 * * *');
		
		/**
		 * +---------------
		 * | 毛利润报表
		 * +---------------
		 **/
		# 权责日报
		//		$schedule->command('report_day:profit_for_user --username=xiaogang.cui,chang.liu')
		//				 ->cron('30 6 * * *');
		//
		//		$schedule->command('report_day:profit_for_user')
		//				 ->cron('20 7 * * *');
		
		

		#周报
//        $schedule->command('reportWeek:profit')
//                 ->cron('30 7 * * 1');
//
//        $schedule->command('reportWeek:profit --username=xiaogang.cui')
//                 ->cron('30 6 * * 1');

        #周报
        $schedule->command('report_week:profit')
            ->cron('30 7 * * 1');

		#月报
        $schedule->command('report_month:profit')
                 ->cron('30 7 1 * *');
		
		#技术二部日报/周报/月报
//		$schedule->command('reportDay:profit_bmy')
//				 ->cron('30 7 * * *');
//		$schedule->command('reportWeek:profit_bmy')
//				 ->cron('30 7 * * 1');
//		$schedule->command('reportMonth:profit_bmy')
//				 ->cron('30 7 1 * *');
		#金盾产品日报/周报/月报
//		$schedule->command('reportDay:profit_gold_shield')
//				 ->cron('30 7 * * *');
//		$schedule->command('reportWeek:profit_gold_shield')
//				 ->cron('30 7 * * 1');
//		$schedule->command('reportMonth:profit_gold_shield')
//				 ->cron('30 7 1 * *');
		#综合产品日报/周报/月报
//		$schedule->command('reportDay:profit_normal_product')
//				 ->cron('30 7 * * *');
//		$schedule->command('reportWeek:profit_normal_product')
//				 ->cron('30 7 * * 1');
//		$schedule->command('reportMonth:profit_normal_product')
//				 ->cron('30 7 1 * *');
		
		/**
		 * +---------------
		 * | 各种邮件预警
		 * +---------------
		 **/
		#根据日账单计算客户的余额并预警
		//$schedule->command('bill:day-email-warning')
		// ->cron('30 7 * * *');
		
		/**
		 * +---------------
		 * | 邦信分快捷版监控数据整理
		 * +---------------
		 **/
		#统计类字段 20220325-add comments 对邦信分快捷版统计类字段进行二次处理，形成半成品数据
		//$schedule->command('bxf:process_statis_request')
		//		 ->cron('0 7 * * *');
		#评分类字段 20220325-add comments 对邦信分快捷版评分类字段进行二次处理，形成半成品数据
		//$schedule->command('bxf:process_score_request')
		//		 ->cron('0 7 * * *');

		
		/**
		 * +---------------
		 * | 成本渠道汇总报表
		 * +---------------
		 **/
		#日报
//		$schedule->command('reportDay:upstreamChannel')
//				 ->cron('30 7 * * *');
		#周报
//		$schedule->command('reportWeek:upstreamChannel')
//				 ->cron('0 8 * * 1');

        ///渠道成本账单-日报
        /*
         *成本账单渠道日报表 20240218注
        $schedule->command('report_day:cost_for_channel')
				 ->cron('30 7 * * *');
        */
        $schedule->command('report_week:cost_for_channel')
                 ->cron('0 8 * * 1');
		
		/**
		 * 每天23点删除金盾上传的历史数据 20220325-add comments
		 */
		//$schedule->command('jindun:deljindundata')
		//		 ->cron('0 23 * * *');
		/**
		 * 每天9点发送邦信分产品预警邮件
		 * 每天20点30分发送邦信分账期邮件预警
		 * 每天23点统计邦信分账期月次数
		 * 每天18点发送邦信分运营商数据稳定性监控
		 * 每天9点发送邦信分不同返回值占比监控
		 * 每天9点发送邦信分渠道信息
		 * 每天凌晨2点处理号码预警提示脚本(由于第三方数据7点才能跑完，因此脚本从第三方拉取从2点调整到7点了)
		 */
		$schedule->command('bxf:sendemail')
				 ->cron('0 9 * * *');
		$schedule->command('bxf:period-email')
				 ->cron('30 20 * * *');
		$schedule->command('bxf:period-stat')
				 ->cron('0 23 * * *');
		$schedule->command('bxf:liantong-sendemail')
				 ->cron('0 18 * * *');
		$schedule->command('bxf:return-sendemail')
				 ->cron('0 9 * * *');
        #运营商数据监控 20240218注
        #20250227注释，张宁宁需求
//        $schedule->command('operator:monitoring')
//            ->cron('0 10 * * *');
        // $schedule->command('operator:mature_monitoring')
        //     ->cron('10 9-18/1 * * *');

        //拉取数据相关
        $this->pullData($schedule);
        //bxf渠道调用量推送企服
        $this->pushBxfInterfaceUsage($schedule);
        //后台公用表账号 产品同步到ck字典表
        $schedule->command('sync_common_data_to_ck')
            ->cron('0 1 * * *');
        //拉取crm访客计划同步至飞书公出审批
        $schedule->command('push_publicity_approval')
            ->cron('*/3 * * * *');
		
		//每日的不定时任务执行
		

		//执行的每日任务
		$this->everydayRun($schedule);
		
		
		/**
		 * 账单脚本(新版本账单)
		 */
		$yesterday = date('Ymd', strtotime('-1 days'));
		$date      = date('Ymd', strtotime('-1 days'));
		
		
		//定时生成营收账单(于20230530注释关闭)
		//$schedule->command('bill:create_bill_income')
		//		 ->cron('03,33 * * * *');

        //定时生成营收账单新版
        $schedule->command('bill:create_bill_income_v2')
            ->cron('10,40 * * * *');

		//每隔半小时生成成本账单(于20240903注释关闭)
		//$schedule->command('bill:create_bill_cost')
		//		 ->cron('03,33 * * * *');
        //每小时生成成本账单新版
        $schedule->command('bill:create_bill_cost_v2')
            ->cron('50 */1 * * *');
		
		//转移旧版本的统计到新版统计的脚本
		$schedule->command('statistics:transfer_info')
				 ->everyThirtyMinutes();
		
		//报警表维护脚本(增加、删除用户_渠道请求日志) 20220325-add comments
		//$schedule->command('monitor:table_manager')
		//		 ->cron('00 18 * * *');
		
		//蚂蚁金服跑批去重值分布计算脚本
		$schedule->command('monitor:value_spread_ant_financial --product_ids=280')
				 ->everyTenMinutes();
		$schedule->command('monitor:value_spread_ant_financial --product_ids=281')
				 ->everyTenMinutes();

        //后台调用量和clickhouse调用量日志比较或其他比较
        $this->compareUsage($schedule);
        //有调用量无计费产品预警检测 或 有渠道接口用量无计费预警
        $this->checkNoPrice($schedule);
        //号码融渠道用量预估
        $this->transferHmrUsage($schedule);
        //生成账单、用量等月数据
        $this->createMonthData($schedule);

		/**
		 * +---------------
		 * | 余额预警
		 * +---------------
		 **/
		//更新余额预警数据 余额预警数据更新脚本 创建余额预警所需的数据
		$schedule->command('balance_warning:update_info')
				 ->cron('40 5-23/1 * * *');
		//发送余额预警（钉钉）
		//$schedule->command('balance_warning:send_wechat')
		//		 ->cron('0 14,22 * * *');

		//修改客户报警处理状态
        $schedule->command('customer:process_alarm_data')
            ->cron('0 5,13,21 * * *');
		
		
		/**
		 * +---------------
		 * | 缓存
		 * +---------------
		 **/
		//更新全部缓存数据
		$schedule->command('redis_cache:reload --all')
				 ->everyTenMinutes();

        //更新钉钉审批结果
        $schedule->command('ding:approval_process')
            ->cron('00 09,18 * * *');

        //钉钉审批超过8小时未处理
        $schedule->command('ding:approval_process_over')
            ->everyTenMinutes();

        //客户限量报警
        $schedule->command('customer_product_limit_warning')
            ->everyFiveMinutes();

        //每1天更新一次客户产品历史用量
        $schedule->command('statis:customer_product_usage')
            ->cron('10 0 * * *');

        //客户账户产品限期报警
//        $schedule->command('customer_account_bytime_alarm')
//            ->daily();
        //客户钉钉处理流程结果
        // $schedule->command('ding:customer_approval_process')
        //     ->daily();
        //客户钉钉报警超8小时未处理
//        $schedule->command('ding:customer_approval_process_over')
//            ->hourly();
        //服务器待确认邮件
        $schedule->command('update_server')
            ->daily();

        //客户非打包账号量转移到打包账号中
        //$schedule->command('usage:transfer_together_call')
        //    ->cron('0 */1 * * *');

        //更新山西移动当前余额
        $schedule->command('sx_cmcc_remain_amount')
            ->everyFiveMinutes();

        //每隔15分钟获取一下项目组产品对应最新账期
        $schedule->command('product_peroid_save')
            ->cron('*/15 * * * *');

        // 每天凌晨1点半检测账号-产品过期更改为禁用状态
        $schedule->command('account_product_expire')
            ->cron('30 01 * * *');

        // 每天8点预警测试限量账号情况
        $schedule->command('account:product_limit')
            ->cron('0 08 * * *');

        //账号过期时间预警，自动发起审批流程
        $schedule->command('account_expire_alarm:by_im')
            ->cron('00 9 * * *');
        //运营商低消补偿
//        $schedule->command('operator:minimum_charge')
//            ->cron('50 9 * * *');
        //运营商 日均调用量 更新
        $schedule->command('operator:update_channel_account_data --func=updateDailyCallNum')
            ->cron('30 9 * * *');
        //baseCheck 更新客户信息
        $schedule->command('push:product_account_info')
            ->cron('* * * * *');
        //baseCheck 拉取用户统计并推到后台
        $schedule->command('pull:user_stat')
            ->cron('*/5 * * * *');
        //baseCheck 拉取渠道统计并推到后台
        $schedule->command('pull:channel_stat')
            ->cron('*/5 * * * *');
		
		//每20分钟计算征信机构数据
        $schedule->command('credit_agency:calculate_money_agent')
            ->cron('*/20 * * * *');
		
		//每隔10分钟 查看是否有销售提成计算任务 有则计算
		$schedule->command('customer_first_income')
		->cron('*/10 * * * *');

		//每隔10分钟 查看是否有渠道成本保底分摊计算任务 有则计算
		$schedule->command('spreaded_min_consume_cost')
		->cron('*/10 * * * *');

        //临时脚本每分钟请求邦秒验227 产品
//        $schedule->command('curl_opdata_227')->cron('* * * * *');



        //每日匹配发票与收款单
        // $schedule->command('invoice:match_remit_invoice')->cron('0 01 * * *');



        //合同
        // 每天拉取合同审批数据
        // 每半小时一次更新审批
        $schedule->command('fieshu_appoval:pull_approval --type=add')->cron('10 05 * * *');
        $schedule->command('fieshu_appoval:pull_approval --type=update')->cron('*/30 * * * *');

        // 每月1号 09：00 合同未归档提醒邮件
        $schedule->command('contract:archive_reminder')->cron('0 9 1 * *');

		//每隔10分钟 查看是否有领导周报数据需要跑
		$schedule->command('weekly_statistic:weekly_statistic')->cron('*/10 * * * *');

		//领导周报任务 周1早添加上周的任务
		$schedule->command('weekly_statistic:add_task')->cron('55 07 * * 1');

        // 渠道资质到期报警 邮件提醒 每日09：00
        $schedule->command('send_mail:channel_qualication_alarm')->cron('0 9 * * *');

        // 售前测试管理系统
        $this->runPreTestCron($schedule);

        //渠道合同到期预警(邮件&飞书)
        $this->channelContractDueMonitor($schedule);

        // 金融业务部门账期邮件提醒
        $this->PeriodReminderMail($schedule);

        // 邦秒验自有渠道规则配置监控
        $schedule->command('pull_self_rule_monitor')->cron('*/10 * * * *');

        // 邦秒验客户关机和自有渠道占比统计
        $schedule->command('stat_self_rule_extend')->cron('*/30 * * * *');


        //领导日报收入预估
        //更新前一日的收入
        $schedule->command('report_task:run_estimate_weekly_income')->cron('7 05 * * *');
        //12月15日添加次年的初始数据
        $next_year = date('Y', strtotime('+1 year'));
        $schedule->command('report_task:run_estimate_weekly_income --mode=2 --year='.$next_year)->cron('0 05 15 12 *');
	}

	/**
	 * 每日执行的脚本任务
	 *
	 * @access   protected
	 * <AUTHOR>
	 * @datetime 2021/1/29 17:07
	 *
	 * @param $schedule Schedule
	 *
	 * @return void
	 */
	protected function everydayRun(Schedule $schedule)
	{
		/**
		 * 转移客户调用量统计数据 && 生成账单 && 发送权责利润报表
		 */
		$this->runProfitReportDay($schedule);
	}

    protected function pullData(Schedule $schedule){
        //拉取企服产品 前一天的调用量，并且更新两天前的费用信息
        $schedule->command('pull_data:enterprise_service_products')
            ->cron('0 7 * * *');
        //每月月初更新上月所有账单
        $schedule->command('pull_data:enterprise_service_products --lastMonthData=1 ')
            ->cron('0 6 03 * *');
        //从ck获取客户产品真实查得量
        $schedule->command('pull_customer_real_valid')
            ->cron('*/30 * * * *');
        $yesterday = date('Ymd', strtotime('-1 days'));
        $schedule->command('pull_customer_real_valid --start_date='.$yesterday.' --end_date='.$yesterday)
            ->cron('15 0 * * *');
        //拉取飞书公出审批实例
        $schedule->command('pull_publicity_approval')
            ->cron('*/10 * * * *');
    }

    protected function pushBxfInterfaceUsage(Schedule $schedule){
        $yesterday = date('Ymd', strtotime('-1 days'));
        $schedule->command('push_interface_usage_for_enterprise_service')
            ->cron('*/5 * * * *');
        $schedule->command('push_interface_usage_for_enterprise_service --start_date='.$yesterday .' --end_date='.$yesterday)
            ->cron('30 0 * * *');

        $pre_month = date('Ym', strtotime('-1 month'));
        //每月2号推上个月的数据
        $schedule->command('push_interface_usage_for_enterprise_service --month='.$pre_month)
            ->cron('0 6 2 * *');
    }

    protected function compareUsage(Schedule $schedule){
        $schedule->command('compare:usage')
            ->cron('0 9 * * *');

        //每月1号发上个月的
        $end_date = date('Ymd', strtotime('-1 days'));
        $start_date = date('Ym01', strtotime($end_date));
        $schedule->command('compare:usage --type=customer_month --start_date='.$start_date.' --end_date='.$end_date.' --type=customer_month')
            ->cron('40 8 1 * *');

        //每天检测上一天的
        $schedule->command('package_billing_monitor --start_date='.$end_date.' --end_date='.$end_date)
            ->cron('50 8 * * *');
    }

    protected function checkNoPrice(Schedule $schedule){
        //每月1号检测上个月是否存在有调用量无计费的产品
        $end_date = date('Ymd', strtotime('-1 days'));
        $start_date = date('Ym01', strtotime($end_date));
        $schedule->command('product_no_price_warning --start_date='.$start_date.' --end_date='.$end_date)
            ->cron('45 8 1 * *');
        //有渠道接口用量无计费预警
        $schedule->command('channel_no_price_warning --start_date='.$start_date.' --end_date='.$end_date)
            ->cron('50 8 1 * *');
        //客户区分运营商计费有量无钱预警
        $schedule->command('diff_operator_no_price_warning --start_date='.$start_date.' --end_date='.$end_date)
            ->cron('55 8 1 * *');
    }

    protected function transferHmrUsage(Schedule $schedule){
        $yesterday = date('Ymd', strtotime('-1 days'));
        $schedule->command('transfer_hmr_usage')
            ->cron('*/5 * * * *');
        $schedule->command('transfer_hmr_usage --start_date='.$yesterday.' --end_date='.$yesterday)
            ->cron('35 0 * * *');
    }

    protected function createMonthData(Schedule $schedule){
        //每5分钟检测根据重跑账单记录和特殊消耗记录作为条件生成部分月数据
        $schedule->command('create_bill_income_month_data --type=2')
            ->cron('*/5 * * * *');

        //每10分钟根据渠道成本调整、客户成本调整、固定费用等记录生成部分月数据
        $schedule->command('create_bill_cost_month_data --type=2')
            ->cron('*/10 * * * *');

        //每月1号生成上个月的收入月账单和成本月账单、客户用量等月数据
        $month = date('Ym', strtotime('-1 days'));
        $schedule->command('create_bill_income_month_data --start_month='.$month.' --end_month='.$month)
            ->cron('58 07 1 * *');

        $schedule->command('create_bill_cost_month_data --start_month='.$month.' --end_month='.$month)
            ->cron('0 08 1 * *');

        $schedule->command('create_customer_usage_month_data --start_month='.$month.' --end_month='.$month)
            ->cron('02 08 1 * *');
    }

	
	/**
	 * 转移客户调用量统计数据 && 生成账单 && 发送权责利润报表
	 *
	 * @access   private
	 * <AUTHOR>
	 * @datetime 2021/1/29 17:12
	 *
	 * @param $schedule Schedule
	 *
	 * @return void
	 */
	protected function runProfitReportDay(Schedule $schedule)
	{
		$yesterday = date('Ymd', strtotime('-1 days'));
		$thereDaysAgo = date('Ymd', strtotime('-3 days'));

		/**
		 * 这时生成的账单，缺少501/664产品的账单
		 * 为了在凌晨的时候检查一次，所以必须生成
		 */
		$schedule->command('statistics:transfer_info --start_date=' . $yesterday . ' --end_date=' . $yesterday)
				 ->cron('15 00 * * *');

		//每天12:20点跑前一天营收账单（除了501/664）,(于20230530注释关闭)
		//$schedule->command('bill:create_bill_income --date=' . $yesterday)
		//		 ->cron('20 00 * * *');
        //每天12:50点跑前一天营收账单新版（除了501/664）
        $schedule->command('bill:create_bill_income_v2 --date=' . $yesterday)
            ->cron('50 00 * * *');

        //因为存量洞察AI产品特殊原因(当天还要推前3天的数据) 需要对存量洞察重跑账单
        $schedule->command('bill:create_bill_income_v2 --date=' . $thereDaysAgo . ' --product_id=30000 --days=3')
            ->cron('05 01 * * *');

        //处理特殊账单(号码融、核验前筛产品、移动02等)
        $schedule->command('update_special_product_bill_income --start_date=' . $yesterday . ' --end_date='.$yesterday)
            ->cron('30 01 * * *');


		//每天12:20跑前一天成本账单 (于20240903注释关闭)
		//$schedule->command('bill:create_bill_cost --date=' . $yesterday)
		//		 ->cron('20 00 * * *');
        //每天12:50跑前一天成本账单 新版
        $schedule->command('bill:create_bill_cost_v2 --date=' . $yesterday)
            ->cron('50 00 * * *');

        //因为存量洞察AI产品特殊原因(当天还要推前3天的数据) 需要对存量洞察重跑账单
        $schedule->command('bill:create_bill_cost_v2 --date=' . $thereDaysAgo. ' --father_id=30000 --days=3')
            ->cron('05 01 * * *');
		
		/**
		 * 由于神盾（664）、私有云（501）产品延后推送统计数据，(因为号码预警提示第三方的量同步从2点调整到了7点,因此该脚本有原来的4.30改为7.10)
		 */
		//每天4:30将501/664产品的客户调用量统计数据转移到新的版本中
		$schedule->command('statistics:transfer_info --product_id=501 --start_date=' . $yesterday . ' --end_date=' . $yesterday)
				 ->cron('10 07 * * *');

		//每天4:40跑一下501/664的客户营收账单(501/664产品的客户调用量统计数据转移到新的版本中时间从4.30改为了7.10，因此该时间也做调整到7.20)
		//(于20230530注释关闭)
        //$schedule->command('bill:create_bill_income --product_id=664,501 --date=' . $yesterday)
		//		 ->cron('20 07 * * *');
        $schedule->command('bill:create_bill_income_v2 --product_id=664,501 --date=' . $yesterday)
            ->cron('20 07 * * *');


		/**
		 * 每天5:30发送一下测试账单，如果可能，早晨起来看一下，确保没有问题 现把时间改为7.30
         * 改时间为7.30原因是:最早之前每天4:40跑一下501/664的客户营收账单时间改为了每天7.20
		 */
		/*
		$schedule->command('report_day:profit_for_user --username=shuangxian.zhang')
				 ->cron('30 07 * * *');
		$schedule->command('report_day:profit_for_leader --addressee=<EMAIL>')
				 ->cron('30 07 * * *');
		*/

        //领导日报汇总数据生成
        $schedule->command('create_profit_data_for_leader')
            ->cron('27 07 * * *');

        //领导日报发送链接通知
        $schedule->command('send_report_day_notice')
            ->cron('30 07 * * *');

		/**
		 * 每天7.30发送所有的权责利润账单
		 */
		$schedule->command('report_day:profit_for_user')
				 ->cron('30 07 * * *');
		$schedule->command('report_day:profit_for_leader')
				 ->cron('30 07 * * *');
		
		$schedule->command('report_day:profit_for_businessman')
		->cron('30 07 * * *');
        /**
         * 每天7.30发送所有的产品调用量
         */

        /*
         * 日产品调用量统计 20240218注
        $schedule->command('report_day:statistic_for_product')
            ->cron('30 07 * * *');
         */
        $schedule->command('report_day:statistic_for_product_no_auth')
            ->cron('30 07 * * *');

        //每月1号9点0分 根据客户特殊配置，计算收入设置特殊消耗
        $schedule->command('customer_income_calculate')
            ->cron('0 09 1 * *');

        //每日3点0分 计算用户每月余额数据脚本
        $schedule->command('balance:monthly')
            ->cron('0 03 * * *');

        //每天10:00 客户账号产品到期提醒
        $schedule->command('customer:exprie_alarm')
            ->cron('0 10 * * *');

        //每天09:09 发送切换朴道客户统计数据
        $schedule->command('report_day:change_to_pudao')
            ->cron('09 09 * * *');
        //每月2号2点0分 计算上个月的预付费客户自动拆单
        // $schedule->command('remit:auto_split_price')
        //     ->cron('0 02 2 * *');


        // 每月1号1点0分 计算上个月待开票数据
        $schedule->command('invoice:generate_month_consume')
            ->cron('0 9 3 * *');

        //每月1号5点7分 计算上个月发票汇总
        $schedule->command('invoice:customer_balance')
                ->cron('07 05 1 * *');
        // $schedule->command('invoice:collection_cost_data')
        //     ->cron('0 01 1 * *');
        //
        // //每月1号5点7分 计算上个月发票数据汇总
        // $schedule->command('invoice:collection_invoice_data')
        //     ->cron('07 05 1 * *');

        $last_month_last_day = date('Ymt', strtotime('-1 days'));
        // 每月1号5点0分 计算上个月新老数据
        $schedule->command('customer:yearly_new_old_customer_v2 --date='.$yesterday.' --export_type=group')
                 ->cron('0 5 1 * *');
        $schedule->command('customer:yearly_new_old_customer_v2 --date='.$yesterday.' --export_type=customer')
                 ->cron('5 5 1 * *');
        /*
         * 领导驾驶舱(pc端) 相关脚本
         */
        $schedule->command('dashboard:cron_gather_middle_product')
            ->cron('20 7 * * *');
        $schedule->command('dashboard:cron_gather_middle_customer')
            ->cron('15 7 * * *');
	}

    /**
     * @param Schedule $schedule
     * @return bool
     */
    protected function runPreTestCron(Schedule $schedule) {
        /**
         * 拉取测试申请
         * 每小时
         */
        $schedule->command('pre_test_manage:pull_apply_list')->cron('*/60 * * * *');
        /**
         * 检查工单状态
         * 2点-23点, 每小时
         */
        $schedule->command('pre_test_manage:check_apply_status')->cron('0 2-23 * * *');
        /**
         * 检查需人工介入工单状态
         * 每天 3点
         */
        $schedule->command('pre_test_manage:check_manual')->cron('0 3 * * *');
        /**
         * 打卡提示
         * 每周一,周五 10点
         */
        $schedule->command('pre_test_manage:feedback_clock --action=1')->cron('0 10 * * 1,5');
        /**
         * 未打卡统计 和 重置打卡状态
         * 每周日 2点
         */
        $schedule->command('pre_test_manage:feedback_clock --action=2')->cron('0 2 * * 0');
        /**
         * 检查产品是否接入情况
         * 每天 3点
         */
        $schedule->command('pre_test_manage:check_product_access')->cron('0 3 */1 * *');
        /**
         * 统计调用情况
         * 每天 17点
         */
        $schedule->command('pre_test_manage:statistics_apply_product')->cron('0 17 */1 * *');

        /**
         * 售前测试数据周报
         *  每周四 18点
         */
        $schedule->command('pre_test_manage:report_weekly_pre')->cron('0 18 * * 4');


        /**
         * 客户监控-数据更新
         * 每天 6点
         */
        $schedule->command('pre_test_manage_monitor:statistics_customer_product_all_time')->cron('0 6 * * *');
        /**
         * 客户监控-调用量更新
         * 周五 6点
         */
        $schedule->command('pre_test_manage_monitor:statistics_customer_product_week')->cron('0 6 * * 5');
        /**
         * 客户监控-计算对接状态
         * 每天 7点
         */
        $schedule->command('pre_test_manage_monitor:run_docking_status')->cron('0 7 * * *');
        /**
         * 客户监控-客户开通产品异常状态提醒
         * 每天 9点
         */
        $schedule->command('pre_test_manage_monitor:run_abnormal_email')->cron('0 9 * * *');
        /**
         * 客户监控-根据状态提交流程工单
         * 每天 11点
         */
        $schedule->command('pre_test_manage_monitor:run_docking_approval')->cron('0 11 * * *');
        /**
         * 客户监控-拉取客户监控流程工单
         * 每天 1点
         */
        $schedule->command('pre_test_manage_monitor:pull_approval_list')->cron('0 1 * * *');

        return true;
    }

    protected function channelContractDueMonitor(Schedule $schedule) {
        //每隔2小时拉取渠道合同到期飞书审批实例情况
        $schedule->command('channel_contract_due --type=2')->cron('0 */2 * * *');

        //每天9点检测一下渠道合同是否到期
        $schedule->command('channel_contract_due --type=1')->cron('0 9 * * *');
    }

    /**
     * 金融业务部门账期邮件提醒
     * @param Schedule $schedule
     */
    protected function PeriodReminderMail(Schedule $schedule)
    {
        // 领导邮件提醒 - 每周五早上7:40
        $schedule->command('send_mail:period_reminder leader')->cron('40 7 * * 5');

        // 区域总监邮件提醒 - 每天早上7:40
        $schedule->command('send_mail:period_reminder area_leader')->cron('40 7 * * *');

        // 商务邮件提醒 - 每天早上7:40
        $schedule->command('send_mail:period_reminder salesman')->cron('40 7 * * *');

        // 财务邮件提醒 - 每月10号早上7:40
        $schedule->command('send_mail:period_reminder finance')->cron('40 7 10 * *');

        // 运营邮件提醒 - 每天早上7:40
        $schedule->command('send_mail:period_reminder yun_ying')->cron('40 7 * * *');
    }

	/**
	 * Get the timezone that should be used by default for scheduled events.
	 *
	 * @return \DateTimeZone|string|null
	 */
	protected function scheduleTimezone()
	{
		return 'Asia/Shanghai';
	}
}
