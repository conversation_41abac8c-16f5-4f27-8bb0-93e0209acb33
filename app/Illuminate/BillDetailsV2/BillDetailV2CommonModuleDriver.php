<?php


namespace App\Illuminate\BillDetailsV2;

use App\Models\{
	Product, MongoBillMonth, Account
};
use App\Support\CustomException;

class BillDetailV2CommonModuleDriver
{
	
	/** @var array 账单时间映射关系 */
	private $list_fee_time_rule_mapping = [
		1 => '日',
		2 => '月',
		3 => '年',
	];
	
	/** @var int 一年按照固定的365计算 */
	private $days_in_years = 365;
	
	/** @var bool 是否需要生成催收分分析快捷版的明细 */
	private $_determine_need_create_shortcut = false;
	
	
	/** @var array 快捷版整合成的属性 */
	private $_list_shortcut_alo_computed = [];
	
	/** @var array 今年快捷版生成账单列表 */
	private $_list_shortcut_bill_this_year = [];
	
	/** @var array 时间计费规则映射 */
	private $list_time_rule_mapping = [
		1 => 'day',
		2 => 'month',
		3 => 'year',
	];
	
	/** @var string 调用量驱动的前缀 */
	private $driver_prefix = 'BillStat';
	
	/**
	 * 通用明细v2
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 */
	public function _getDetailInfoForAlo(MongoBillMonth $item): array
	{
		switch ($item->product_alo) {
			case '_1':
				// 通用 && 按时间
				return $this->writeConsumptionDetailByMonthWhenCommonDate($item);
				break;
			case '_2_1':
				// 如果通用的按用量 && 固定价格
				return $this->writeConsumptionDetailByMonthWhenCommonNumberFixed($item);
				break;
			case "_2_2":
				// 通用的按用量 && 累进阶梯
				return $this->writeConsumptionDetailByMonthWhenCommonProgression($item);
				break;
			case "_2_3":
				// 通用的按用量 && 到达阶梯
				return $this->writeConsumptionDetailByMonthWhenCommonReach($item);
				break;
			case "_1_2":
				// 区分运营商按时间
				return $this->writeConsumptionDetailByMonthWhenOperatorDate($item);
				break;
			case "_2_1_2":
				//  区分运营商按用量 && 固定价格
				return $this->writeConsumptionDetailByMonthWhenOperatorFixed($item);
				break;
			case "_2_2_2":
				//  区分运营商按用量 && 累进阶梯
				return $this->writeConsumptionDetailByMonthWhenOperatorProgression($item);
				break;
			case '_2_3_2':
				// 区分运营商按用量 && 到达阶梯
				return $this->writeConsumptionDetailByMonthWhenOperatorReach($item);
				break;
		}
	}
	
	/**
	 * 区分运营商按用量 && 到达阶梯
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 */
	private function writeConsumptionDetailByMonthWhenOperatorReach(MongoBillMonth $item): array
	{
		// 容器
		$container                  = [];
		$container['price']         = $item->section_source->fee_price;
		$container['product_name']  = $item->product_name;
		$container['section_begin'] = $item->section_begin;
		$container['section_end']   = $item->section_end;
		$container['money']         = $item->money;
		$container['product_id']    = $item->product_id;
		
		// 获取调用量
		$container               = $this->getNumberOperatorWhenOperator($container, $item);
		$container['list_money'] = $this->distributeNumberOnPriceWhenOperatorReach($item);
		
		// 格式化区间分布
		$container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenOperatorProgression($container);
		
		return $container;
	}
	
	/**
	 * 价格的分布 && 区分运营商 && 到达阶梯
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function distributeNumberOnPriceWhenOperatorReach($section_item): array
	{
		// 阶梯周期
		switch ($section_item->section_source->fee_step_rule) {
			case 1:
				// 天周期
				return $this->getMoneyInfoWhenOperatorReachDayCycle($section_item);
				break;
			case 2:
				// 月周期
				return $this->genMoneyWhenOperatorByNumberReach($section_item);
				break;
			case 3:
				// 年周期
				return $this->genMoneyWhenOperatorByNumberReachAndYearCycle($section_item);
				break;
			default:
				// 无周期
				return $this->genMoneyWhenOperatorByNumberReachAndNoCycle($section_item);
		}
	}
	
	/**
	 * 无周期 && 通用 && 达到阶梯
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenOperatorByNumberReachAndNoCycle($section_item): array
	{
		// 从开始计费时间到片段计费开始的前一天
		$money_first = $this->getMoneyWhenOperatorReachAndYearNoCycleFirst($section_item);
		
		// 从开始计费时间到片段的结束时间
		$money_second = $this->getMoneyWhenOperatorReachAndYearNoCycleSecond($section_item);
		
		// 格式化
		return $this->formatMoneyWhenOperatorReachAndNoCycle($money_first, $money_second);
	}
	
	/**
	 * 无周期 && 运营商 && 按用
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 *
	 * @return array
	 */
	private function formatMoneyWhenOperatorReachAndNoCycle(array $money_info_first, array $money_info_second): array
	{
		return $this->formatMoneyWhenOperatorAndYearCycleInSameYear($money_info_first, $money_info_second);
	}
	
	/**
	 * 从开始计费时间到片段的结束时间
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return float
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorReachAndYearNoCycleSecond($section_item): float
	{
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorReachAndYearNoCycleSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		// 计算
		return app('bill.number.reach.operator')->genMoney($params_money);
	}
	
	/**
	 * 从开始计费时间到片段的结束时间(第二部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genParamsWhenOperatorReachAndYearNoCycleSecond($section_item): array
	{
		$section_begin  = $section_item->start_day;
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorReachAndYearNoCycleFirst($section_item): array
	{
		// 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
		if ($section_item->section_begin == $section_item->start_day) {
			return [
				'money_yd' => 0,
				'money_lt' => 0,
				'money_dx' => 0,
			];
		}
		
		return $this->getMoneyWhenOperatorReachAndYearNoCycleFirstAndTimeDiff($section_item);
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天（第一部分）时间不同
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorReachAndYearNoCycleFirstAndTimeDiff($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorReachAndYearNoCycleFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach.operator')->genMoney($params_money);
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天(第一部分)
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorReachAndYearNoCycleFirst($section_item): array
	{
		$section_begin  = $section_item->start_day;
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 年周期 && 运营商 && 达到阶梯
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenOperatorByNumberReachAndYearCycle($section_item): array
	{
		// 计费片段如果在两个跨度
		if ($this->determineInTwoYearFromStart($section_item)) {
			return $this->genMoneyWhenOperatorReachAndYearCycleInTwoYear($section_item);
		}
		
		// 片段在同一年
		return $this->genMoneyWhenOperatorReachAndYearCycleInSameYear($section_item);
	}
	
	/**
	 * 年周期 && 运营商 && 按用量 && 在同一年
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenOperatorReachAndYearCycleInSameYear($section_item): array
	{
		// 如果计费开始时间和片段开始时间相同(那么正常就可以了)
		if ($section_item->start_day == $section_item->section_begin) {
			return $this->genMoneyWhenOperatorByNumberReach($section_item);
		}
		
		// 格式化价格
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 开始计费到节点结束时候的调用量
		$params_end       = $this->genParamsFromEndWhenSameYear($section_item);
		$fee_number_end   = $this->numberForSectionByParams($section_item, $params_end);
		$params_money_end = array_merge(compact('fee_price'), $fee_number_end);
		$money_end        = app('bill.excel.reach.operator')->genMoney($params_money_end);
		
		// 开始计费到节点开始时候的调用量
		$params_before       = $this->genParamsFromBeginWhenSameYear($section_item);
		$fee_number_before   = $this->numberForSectionByParams($section_item, $params_before);
		$params_money_before = array_merge(compact('fee_price'), $fee_number_before);
		$money_before        = app('bill.excel.reach.operator')->genMoney($params_money_before);
		
		// 格式化数据
		return $this->formatMoneyWhenOperatorReachAndYearCycleInSameYear($money_before, $money_end);
	}
	
	/**
	 * 年周期 && 运营商 && 按用量 && 在同一年 格式化金钱
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 *
	 * @return array
	 */
	private function formatMoneyWhenOperatorReachAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
	{
		return $this->formatMoneyWhenOperatorAndYearCycleInSameYear($money_info_first, $money_info_second);
	}
	
	/**
	 * 年周期 && 运营商 && 按用量 && 在不同的两年 && 到达
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenOperatorReachAndYearCycleInTwoYear($section_item): array
	{
		// 某一轮询的开始时间到片段开始时间的前一天
		$money_first = $this->getMoneyWhenOperatorReachAndYearInTwoYearFirst($section_item);
		
		// 某一轮询的开始时间到新的部分开始时间的前一天
		$money_second = $this->getMoneyWhenOperatorReachAndYearInTwoYearSecond($section_item);
		
		// 新的部分的开始时间到片段结束时间
		$money_third = $this->getMoneyWhenOperatorReachAndYearInTwoYearThird($section_item);
		
		// 格式化
		return $this->formatMoneyWhenOperatorReachAndYearCycleInTwoYear($money_first, $money_second, $money_third);
	}
	
	/**
	 * 格式化
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 * @param array $money_info_third
	 *
	 * @return array
	 */
	private function formatMoneyWhenOperatorReachAndYearCycleInTwoYear(array $money_info_first, array $money_info_second, array $money_info_third): array
	{
		list($money_first_yd, $money_first_lt, $money_first_dx) = [
			$money_info_first['money_yd'] ?? 0,
			$money_info_first['money_lt'] ?? 0,
			$money_info_first['money_dx'] ?? 0,
		];
		
		list($money_second_yd, $money_second_lt, $money_second_dx) = [
			$money_info_second['money_yd'] ?? 0,
			$money_info_second['money_lt'] ?? 0,
			$money_info_second['money_dx'] ?? 0,
		];
		
		list($money_third_yd, $money_third_lt, $money_third_dx) = [
			$money_info_third['money_yd'] ?? 0,
			$money_info_third['money_lt'] ?? 0,
			$money_info_third['money_dx'] ?? 0,
		];
		
		return [
			'money_yd' => $money_second_yd - $money_first_yd + $money_third_yd,
			'money_lt' => $money_second_lt - $money_first_lt + $money_third_lt,
			'money_dx' => $money_second_dx - $money_first_dx + $money_third_dx,
		];
	}
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function getMoneyWhenOperatorReachAndYearInTwoYearThird($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorReachAndYearInTwoYearThird($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach.operator')->genMoney($params_money);
	}
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorReachAndYearInTwoYearThird($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = date('Ymd', strtotime('+1 day', strtotime($day_end)));
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorReachAndYearInTwoYearSecond($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorReachAndYearInTwoYearSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach.operator')->genMoney($params_money);
	}
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorReachAndYearInTwoYearSecond($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = $day_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorReachAndYearInTwoYearFirst($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 数量
		$params       = $this->genParamsWhenOperatorReachAndYearInTwoYearFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach.operator')->genMoney($params_money);
	}
	
	/**
	 *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorReachAndYearInTwoYearFirst($section_item): array
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 生成Money
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenOperatorByNumberReach($section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 计费片段时间内的调用量
		$fee_number = $this->numberInTheSection($section_item);
		
		// 参数
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		// 计费
		return app('bill.excel.reach.operator')->genMoney($params_money);
	}
	
	/**
	 * 天周期金额计算
	 *
	 * @param $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyInfoWhenOperatorReachDayCycle($item): array
	{
		$list_day_moneys = $this->genMoneyWhenOperatorByNumberReachAndDayCycle($item);
		
		// 格式化
		return $this->formatMoneyInfoWhenOperatorReachDayCycle($list_day_moneys);
	}
	
	/**
	 * @param array $list_day_money
	 *
	 * @return array
	 */
	private function formatMoneyInfoWhenOperatorReachDayCycle(array $list_day_money): array
	{
		return array_reduce($list_day_money, function ($carry, $item) {
			$carry['money_yd'] += $item['money_yd'];
			$carry['money_lt'] += $item['money_lt'];
			$carry['money_dx'] += $item['money_dx'];
			
			return $carry;
		}, ['money_yd' => 0, 'money_lt' => 0, 'money_dx' => 0]);
	}
	
	/**
	 * 天周期 && 通用 && 达到阶梯
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenOperatorByNumberReachAndDayCycle($section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberReachOperator($section_item);
		
		// 计算这里每天的量
		$fee_number = $this->numberSectionWhenDayCycle($section_item);
		
		// 容器
		$list_container = [];
		
		// 计算每天的费用
		collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
			// 参数
			$params_money = array_merge(compact('fee_price'), $fee_number_item);
			
			// 计费
			$list_container[$day] = app('bill.excel.reach.operator')->genMoney($params_money);
		});
		
		return $list_container;
	}
	
	/**
	 * 格式化运营商达到阶梯的价格
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 */
	private function formatPriceForNumberReachOperator($section_item): array
	{
		$fee_price = json_decode($section_item->section_source->fee_price, true);
		
		return array_reduce($fee_price, function ($carry, $item) {
			$carry[] = [
				'reach_standard' => $item[0],
				'price_yd'       => $item[1],
				'price_lt'       => $item[2],
				'price_dx'       => $item[3],
			];
			
			return $carry;
		}, []);
	}
	
	/**
	 * 设置各个运营商的调用量
	 *
	 * @param array          $carry
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getNumberOperatorWhenOperator(array $carry, $item): array
	{
		// 计费片段的调用量
		$fee_number = $this->numberInTheSection($item);
		list($number_yd, $number_lt, $number_dx) = [
			$fee_number['fee_number']['yd'] ?? 0,
			$fee_number['fee_number']['lt'] ?? 0,
			$fee_number['fee_number']['dx'] ?? 0,
		];
		
		$carry['list_number'] = [
			'yd' => $number_yd,
			'lt' => $number_lt,
			'dx' => $number_dx,
		];
		
		return $carry;
	}
	
	/**
	 * 区分运营商按用量 && 累进阶梯
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 */
	private function writeConsumptionDetailByMonthWhenOperatorProgression(MongoBillMonth $item): array
	{
		// 容器
		$container                  = [];
		$container['price']         = $item->section_source->fee_price;
		$container['product_name']  = $item->product_name;
		$container['section_begin'] = $item->section_begin;
		$container['section_end']   = $item->section_end;
		$container['money']         = $item->money;
		$container['product_id']    = $item->product_id;
		
		// 各个运营商的调用量  && 金钱
		$container               = $this->getNumberOperatorWhenOperator($container, $item);
		$container['list_money'] = $this->distributeNumberOnPriceWhenOperatorProgression($item);
		
		// 格式化区间分布
		$container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenOperatorProgression($container);
		
		return $container;
	}
	
	/**
	 * 格式化区间分布
	 *
	 * @param array $list_container
	 *
	 * @return array
	 */
	private function formatDistributeNumberOnPriceWhenOperatorProgression(array $list_container): array
	{
		// 格式化金额
		$list_money = $list_container['list_money'];
		
		return [
			[
				'operator' => '移动',
				'money'    => $list_money['money_yd'],
				'number'   => $list_container['list_number']['yd'] ?? 0,
			],
			[
				'operator' => '联通',
				'money'    => $list_money['money_lt'],
				'number'   => $list_container['list_number']['lt'] ?? 0,
			],
			[
				'operator' => '电信',
				'money'    => $list_money['money_dx'],
				'number'   => $list_container['list_number']['dx'] ?? 0,
			],
		];
	}
	
	
	/**
	 * 通用 && 累进阶梯
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function distributeNumberOnPriceWhenOperatorProgression($item): array
	{
		// 阶梯周期
		switch ($item->section_source->fee_step_rule) {
			case 1:
				// 天周期
				return $this->getMoneyInfoWhenOperatorProgressionDayCycle($item);
				break;
			case 2:
				// 月周期
				return $this->getMoneyInfoWhenOperatorProgressionMonthCycle($item);
				break;
			case 3:
				// 年周期
				return $this->genMoneyWhenOperatorByNumberProgressionAndYearCycle($item);
				break;
			default:
				// 无周期
				return $this->genMoneyWhenOperatorByNumberProgressionAndNoCycle($item);
		}
	}
	
	/**
	 * 无周期 && 运营商 && 累进阶梯
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenOperatorByNumberProgressionAndNoCycle($section_item): array
	{
		// 从开始计费时间到片段计费开始的前一天
		$money_first = $this->getMoneyWhenOperatorProgressAndYearNoCycleFirst($section_item);
		
		// 从开始计费时间到片段的结束时间
		$money_second = $this->getMoneyWhenOperatorProgressAndYearNoCycleSecond($section_item);
		
		// 计费
		return $this->formatMoneyWhenOperatorByNumberProgressionAndNoCycle($money_first, $money_second);
	}
	
	
	/**
	 * 年周期 && 运营商 && 按用量 && 在同一年 格式化金钱
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 *
	 * @return array
	 */
	private function formatMoneyWhenOperatorByNumberProgressionAndNoCycle(array $money_info_first, array $money_info_second): array
	{
		list($money_first_yd, $money_first_lt, $money_first_dx) = [
			$money_info_first['money_yd'] ?? 0,
			$money_info_first['money_lt'] ?? 0,
			$money_info_first['money_dx'] ?? 0,
		];
		
		list($money_second_yd, $money_second_lt, $money_second_dx) = [
			$money_info_second['money_yd'] ?? 0,
			$money_info_second['money_lt'] ?? 0,
			$money_info_second['money_dx'] ?? 0,
		];
		
		return [
			'money_yd' => $money_second_yd - $money_first_yd,
			'money_lt' => $money_second_lt - $money_first_lt,
			'money_dx' => $money_second_dx - $money_first_dx,
		];
	}
	
	
	/**
	 * 从开始计费时间到片段的结束时间
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorProgressAndYearNoCycleSecond($section_item): array
	{
		// 价格
		$fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenCommonProgressAndYearNoCycleSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression.operator')->genMoney($params_money);
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorProgressAndYearNoCycleFirst($section_item): array
	{
		// 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
		if ($section_item->section_begin == $section_item->start_day) {
			return [
				'money_yd' => 0,
				'money_lt' => 0,
				'money_dx' => 0,
			];
		}
		
		return $this->getMoneyWhenOperatorProgressAndYearNoCycleFirstAndTimeDiff($section_item);
	}
	
	
	/**
	 * 计费开始时间和片段开始时间不同
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorProgressAndYearNoCycleFirstAndTimeDiff($section_item): array
	{
		// 价格
		$fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorProgressAndYearNoCycleFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression.operator')->genMoney($params_money);
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天(第一部分)
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorProgressAndYearNoCycleFirst($section_item): array
	{
		$section_begin  = date('Ymd', strtotime($section_item->section_source->start_date));
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 年周期 && 运营商 && 累进阶梯
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenOperatorByNumberProgressionAndYearCycle($section_item): array
	{
		// 计费片段如果在两个跨度
		if ($this->determineInTwoYearFromStart($section_item)) {
			return $this->genMoneyWhenOperatorProgressionAndYearCycleInTwoYear($section_item);
		}
		
		return $this->genMoneyWhenOperatorProgressionAndYearCycleInSameYear($section_item);
	}
	
	/**
	 * 年周期 && 运营商 && 按用量 && 在同一年
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenOperatorProgressionAndYearCycleInSameYear($section_item): array
	{
		// 如果计费开始时间和片段开始时间相同(那么正常就可以了)
		if ($section_item->start_day == $section_item->section_begin) {
			return $this->getMoneyInfoWhenOperatorProgressionMonthCycle($section_item);
		}
		
		// 格式化价格
		$fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
		
		// 开始计费到节点结束时候的调用量
		$params_end       = $this->genParamsFromEndWhenSameYear($section_item);
		$fee_number_end   = $this->numberForSectionByParams($section_item, $params_end);
		$params_money_end = array_merge(compact('fee_price'), $fee_number_end);
		$money_end        = app('bill.excel.progression.operator')->genMoney($params_money_end);
		
		// 开始计费到节点开始时候的调用量
		$params_before       = $this->genParamsFromBeginWhenSameYear($section_item);
		$fee_number_before   = $this->numberForSectionByParams($section_item, $params_before);
		$params_money_before = array_merge(compact('fee_price'), $fee_number_before);
		$money_before        = app('bill.excel.progression.operator')->genMoney($params_money_before);
		
		// 设置属性
		return $this->formatMoneyWhenOperatorProgressionAndYearCycleInSameYear($money_before, $money_end);
	}
	
	/**
	 * 年周期 && 运营商 && 按用量 && 在同一年 格式化金钱
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 *
	 * @return array
	 */
	private function formatMoneyWhenOperatorProgressionAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
	{
		return $this->formatMoneyWhenOperatorAndYearCycleInSameYear($money_info_first, $money_info_second);
	}
	
	/**
	 * 格式化 运营商 && 年周期 && 在同一周期
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 *
	 * @return array
	 */
	private function formatMoneyWhenOperatorAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
	{
		list($money_first_yd, $money_first_lt, $money_first_dx) = [
			$money_info_first['money_yd'] ?? 0,
			$money_info_first['money_lt'] ?? 0,
			$money_info_first['money_dx'] ?? 0,
		];
		
		list($money_second_yd, $money_second_lt, $money_second_dx) = [
			$money_info_second['money_yd'] ?? 0,
			$money_info_second['money_lt'] ?? 0,
			$money_info_second['money_dx'] ?? 0,
		];
		
		return [
			'money_yd' => $money_second_yd - $money_first_yd,
			'money_lt' => $money_second_lt - $money_first_lt,
			'money_dx' => $money_second_dx - $money_first_dx,
		];
		
	}
	
	/**
	 * 年周期 && 运营商 && 按用量 && 在不同的两年
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenOperatorProgressionAndYearCycleInTwoYear($section_item): array
	{
		// 某一轮询的开始时间到片段开始时间的前一天
		$money_first = $this->getMoneyWhenOperatorProgressAndYearInTwoYearFirst($section_item);
		
		// 某一轮询的开始时间到新的部分开始时间的前一天
		$money_second = $this->getMoneyWhenOperatorProgressAndYearInTwoYearSecond($section_item);
		
		// 新的部分的开始时间到片段结束时间
		$money_third = $this->getMoneyWhenOperatorProgressAndYearInTwoYearThird($section_item);
		
		// 格式化
		return $this->formatMoneyWhenOperatorProgressionAndYearCycleInTwoYear($money_first, $money_second, $money_third);
	}
	
	/**
	 * 格式化
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 * @param array $money_info_third
	 *
	 * @return array
	 */
	private function formatMoneyWhenOperatorProgressionAndYearCycleInTwoYear(array $money_info_first, array $money_info_second, array $money_info_third): array
	{
		list($money_first_yd, $money_first_lt, $money_first_dx) = [
			$money_info_first['money_yd'] ?? 0,
			$money_info_first['money_lt'] ?? 0,
			$money_info_first['money_dx'] ?? 0,
		];
		
		list($money_second_yd, $money_second_lt, $money_second_dx) = [
			$money_info_second['money_yd'] ?? 0,
			$money_info_second['money_lt'] ?? 0,
			$money_info_second['money_dx'] ?? 0,
		];
		
		list($money_third_yd, $money_third_lt, $money_third_dx) = [
			$money_info_third['money_yd'] ?? 0,
			$money_info_third['money_lt'] ?? 0,
			$money_info_third['money_dx'] ?? 0,
		];
		
		return [
			'money_yd' => $money_second_yd - $money_first_yd + $money_third_yd,
			'money_lt' => $money_second_lt - $money_first_lt + $money_third_lt,
			'money_dx' => $money_second_dx - $money_first_dx + $money_third_dx,
		];
	}
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function getMoneyWhenOperatorProgressAndYearInTwoYearThird($section_item): array
	{
		// 价格
		$fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorProgressAndYearInTwoYearThird($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression.operator')->genMoney($params_money);
	}
	
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorProgressAndYearInTwoYearThird($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = date('Ymd', strtotime('+1 day', strtotime($day_end)));
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorProgressAndYearInTwoYearSecond($section_item): array
	{
		// 价格
		$fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorProgressAndYearInTwoYearSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression.operator')->genMoney($params_money);
	}
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorProgressAndYearInTwoYearSecond($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = $day_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenOperatorProgressAndYearInTwoYearFirst($section_item): array
	{
		// 价格
		$fee_price = $this->formatPriceForNumberProgressionOperator($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenOperatorProgressAndYearInTwoYearFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression.operator')->genMoney($params_money);
	}
	
	/**
	 *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenOperatorProgressAndYearInTwoYearFirst($section_item): array
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 运营商 && 累进阶梯 && 月周期
	 *
	 * @param MongoBillMonth $bill_section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyInfoWhenOperatorProgressionMonthCycle($bill_section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberProgressionOperator($bill_section_item);
		
		// 计费片段时间内的调用量
		$fee_number = $this->numberInTheSection($bill_section_item);
		
		// 参数
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		// 计费
		return app('bill.excel.progression.operator')->genMoney($params_money);
	}
	
	/**
	 * 天周期金额计算
	 *
	 * @param $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyInfoWhenOperatorProgressionDayCycle($item): array
	{
		$list_day_moneys = $this->genMoneyWhenOperatorByNumberProgressionAndDayCycle($item);
		
		// 格式化
		return $this->formatMoneyInfoWhenOperatorProgressionDayCycle($list_day_moneys);
	}
	
	/**
	 * 格式化数据
	 *
	 * @param array $list_day_money
	 *
	 * @return array
	 */
	private function formatMoneyInfoWhenOperatorProgressionDayCycle(array $list_day_money): array
	{
		return array_reduce($list_day_money, function ($carry, $item) {
			$carry['money_yd'] += $item['money_yd'];
			$carry['money_lt'] += $item['money_lt'];
			$carry['money_dx'] += $item['money_dx'];
			
			return $carry;
		}, ['money_yd' => 0, 'money_lt' => 0, 'money_dx' => 0]);
	}
	
	/**
	 * 天周期 && 运营商 && 累进阶梯
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenOperatorByNumberProgressionAndDayCycle($section_item): array
	{
		// 格式化价格 && 计算这里每天的量
		$fee_price  = $this->formatPriceForNumberProgressionOperator($section_item);
		$fee_number = $this->numberSectionWhenDayCycle($section_item);
		
		// 容器
		$list_container = [];
		collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
			// 参数
			$params_money = array_merge(compact('fee_price'), $fee_number_item);
			
			// 计费
			$list_container[$day] = app('bill.excel.progression.operator')->genMoney($params_money);
		});
		
		return $list_container;
	}
	
	
	/**
	 * 格式化通用累进阶梯的价格
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 */
	private function formatPriceForNumberProgressionOperator($section_item): array
	{
		$fee_price = json_decode($section_item->section_source->fee_price, true);
		
		return array_reduce($fee_price, function ($carry, $item) {
			$carry[] = [
				'left'     => $item[0],
				'right'    => $item[1],
				'price_yd' => $item[2],
				'price_lt' => $item[3],
				'price_dx' => $item[4],
			];
			
			return $carry;
		}, []);
	}
	
	/**
	 * 区分运营商的金额
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 */
	private function writeConsumptionDetailByMonthWhenOperatorFixed(MongoBillMonth $item): array
	{
		// 容器
		$container          = [];
		$container['price'] = $item->section_source->fee_price;;
		$container['product_name']  = $item->product_name;
		$container['section_begin'] = $item->section_begin;
		$container['section_end']   = $item->section_end;
		$container['money']         = $item->money;
		$container['product_id']    = $item->product_id;
		
		// 各个运营商的调用量
		$container['fee_number'] = [
			'yd' => $item->section_number['fee_number']['yd'] ?? 0,
			'dx' => $item->section_number['fee_number']['dx'] ?? 0,
			'lt' => $item->section_number['fee_number']['lt'] ?? 0,
		];
		
		// 格式化区间分布
		$container['list_distribute'] = $this->formatDistributeNumberOnPriceWhenOperatorFixed($container);
		
		return $container;
	}
	
	/**
	 * 运营商 && 固定价格
	 *
	 * @param array $list_container
	 *
	 * @return array
	 */
	private function formatDistributeNumberOnPriceWhenOperatorFixed(array $list_container): array
	{
		list($list_fee_number, $list_price) = [
			$list_container['fee_number'],
			json_decode($list_container['price'], true),
		];
		
		// 计算金额
		list($price_yd, $price_lt, $price_dx) = [$list_price[0], $list_price[1], $list_price[2]];
		list($number_yd, $number_lt, $number_dx) = [
			$list_fee_number['yd'],
			$list_fee_number['lt'],
			$list_fee_number['dx'],
		];
		$money_yd = $price_yd * $number_yd;
		$money_lt = $price_lt * $number_lt;
		$money_dx = $price_dx * $number_dx;
		
		return [
			['price' => $price_yd, 'money' => $money_yd, 'number' => $number_yd, 'operator' => '移动'],
			['price' => $price_lt, 'money' => $money_lt, 'number' => $number_lt, 'operator' => '联通'],
			['price' => $price_dx, 'money' => $money_dx, 'number' => $number_dx, 'operator' => '电信'],
		];
	}
	
	/**
	 * 区分运营商 按日期
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 */
	private function writeConsumptionDetailByMonthWhenOperatorDate(MongoBillMonth $item): array
	{
		// 容器
		$container                  = [];
		$container['price']         = $item->section_source->fee_price;
		$container['product_name']  = $item->product_name;
		$container['section_begin'] = $item->section_begin;
		$container['section_end']   = $item->section_end;
		$container['money']         = $item->money;
		$container['product_id']    = $item->product_id;
		
		// 各个运营商的调用量 && 金额
		$container = $this->getNumberOperatorWhenOperator($container, $item);
		$container = $this->getMoneyOperatorWhenOperatorDate($container, $item);
		
		// 格式化
		return $this->formatDistributeNumberOnPriceWhenOperatorDate($container);
	}
	
	/**
	 * 格式化区间分布
	 *
	 * @param array $list_container
	 *
	 * @return array
	 */
	private function formatDistributeNumberOnPriceWhenOperatorDate(array $list_container): array
	{
		return [
			"section_end"     => $list_container["section_end"],
			"section_begin"   => $list_container["section_begin"],
			"list_distribute" => [
				[
					'operator' => '移动',
					'money'    => $list_container['list_money']['money_yd'] ?? 0,
					'number'   => $list_container['list_number']['yd'] ?? 0,
				],
				[
					'operator' => '联通',
					'money'    => $list_container['list_money']['money_lt'] ?? 0,
					'number'   => $list_container['list_number']['lt'] ?? 0,
				],
				[
					'operator' => '电信',
					'money'    => $list_container['list_money']['money_dx'] ?? 0,
					'number'   => $list_container['list_number']['dx'] ?? 0,
				],
			],
		];
	}
	
	
	/**
	 * 各个运营商的累加
	 *
	 * @param array          $carry
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyOperatorWhenOperatorDate(array $carry, $item): array
	{
		$fee_number          = $this->getFeeNumberWhenOperatorDate($item);
		$carry['list_money'] = [
			'money_yd' => $fee_number['money_yd'] ?? 0,
			'money_lt' => $fee_number['money_lt'] ?? 0,
			'money_dx' => $fee_number['money_dx'] ?? 0,
		];
		
		return $carry;
	}
	
	/**
	 * 运营商 && 按时间 当前片段的各个运营商的金额
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getFeeNumberWhenOperatorDate($section_item): array
	{
		// 获取这段时间消费的金额
		$params_money = $this->genParamsForOperatorByDate($section_item);
		
		return app('bill.excel.date.operator')->genMoney($params_money);
	}
	
	/**
	 * 通用按日期生成参数
	 *
	 * @param object $section_item
	 *
	 * @return array
	 */
	private function genParamsForOperatorByDate($section_item): array
	{
		// 价格格式化
		$fee_price = $this->formatPriceWhenOperatorDate($section_item);
		
		$fee_time_rule = $section_item->section_source->fee_time_rule;
		
		return [
			'section_begin' => $section_item->section_begin,
			'section_end'   => $section_item->section_end,
			'fee_price'     => $fee_price,
			'fee_time_rule' => $this->list_time_rule_mapping[$fee_time_rule] ?? '异常的fee_time_rule：' . $fee_time_rule,
			'month'         => $section_item->month,
		];
	}
	
	/**
	 * 格式化价格
	 *
	 * @param $section_item
	 *
	 * @return array
	 */
	private function formatPriceWhenOperatorDate($section_item): array
	{
		$fee_price = json_decode($section_item->section_source->fee_price, true);
		
		return [
			'price_yd' => $fee_price[0],
			'price_lt' => $fee_price[1],
			'price_dx' => $fee_price[2],
		];
	}
	
	
	/**
	 *  通用的按用量 && 到达阶梯
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function writeConsumptionDetailByMonthWhenCommonReach(MongoBillMonth $item): array
	{
		// 容器
		$container                  = [];
		$container['price']         = $item->section_source->fee_price;
		$container['product_name']  = $item->product_name;
		$container['section_begin'] = $item->section_begin;
		$container['section_end']   = $item->section_end;
		$container['money']         = $item->money;
		$container['product_id']    = $item->product_id;
		
		// 价格
		$container['list_distribute'] = $this->distributeNumberOnPriceWhenCommonReach($item);
		
		return $container;
	}
	
	/**
	 * 账单金额,调用量和单价的分布
	 *
	 * @param  MongoBillMonth $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function distributeNumberOnPriceWhenCommonReach($item)
	{
		switch ($item->section_source->fee_step_rule) {
			case 1:
				// 天周期
				return $this->getMoneyInfoWhenCommonReachDayCycle($item);
				break;
			case 2:
				// 月周期
				return $this->genMoneyWhenCommonByNumberReach($item);
				break;
			case 3:
				// 年周期
				return $this->genMoneyWhenCommonByNumberReachAndYearCycle($item);
				break;
			default:
				// 无周期
				return $this->genMoneyWhenCommonByNumberReachAndNoCycle($item);
		}
	}
	
	/**
	 * 无周期 && 通用 && 达到阶梯
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenCommonByNumberReachAndNoCycle($section_item): array
	{
		// 从开始计费时间到片段计费开始的前一天
		$money_first = $this->getMoneyWhenCommonReachAndYearNoCycleFirst($section_item);
		
		// 从开始计费时间到片段的结束时间
		$money_second = $this->getMoneyWhenCommonReachAndYearNoCycleSecond($section_item);
		
		// 格式化
		return $this->formatMoneyWhenCommonByNumberReachAndNoCycle($money_second, $money_first);
	}
	
	/**
	 * 格式化
	 *
	 * @param array $money_info_second
	 * @param array $money_info_first
	 *
	 * @return array
	 */
	private function formatMoneyWhenCommonByNumberReachAndNoCycle(array $money_info_second, array $money_info_first): array
	{
		// 容器
		$list_container = [];
		
		list($money_first, $section_number_first) = $money_info_first ? [
			$money_info_first['money'],
			$money_info_first['section_number'],
		] : [0, 0];
		list($money_second, $section_number_second, $section_key_second, $price_second) = $money_info_second ? [
			$money_info_second['money'],
			$money_info_second['section_number'],
			$money_info_second['section_key'],
			$money_info_second['price'],
		] : [0, 0, 0, 0];
		
		$list_container[$section_key_second] = [
			'price'          => $price_second,
			'section_key'    => $section_key_second,
			'section_number' => $section_number_second - $section_number_first,
			'money'          => $money_second - $money_first,
		];
		
		return $list_container;
	}
	
	/**
	 * 从开始计费时间到片段的结束时间
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonReachAndYearNoCycleSecond($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenCommonReachAndYearNoCycleSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach')->genMoney($params_money);
	}
	
	
	/**
	 * 从开始计费时间到片段的结束时间(第二部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genParamsWhenCommonReachAndYearNoCycleSecond($section_item): array
	{
		$section_begin  = $section_item->start_day;
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonReachAndYearNoCycleFirst($section_item): array
	{
		// 开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
		if ($section_item->section_begin == $section_item->start_day) {
			return [];
		}
		
		return $this->getMoneyWhenCommonReachAndYearNoCycleFirstAndTimeDiff($section_item);
	}
	
	
	/**
	 * 从开始计费时间到片段计费开始的前一天（第一部分） 时间不同
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonReachAndYearNoCycleFirstAndTimeDiff($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenCommonReachAndYearNoCycleFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach')->genMoney($params_money);
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天(第一部分)
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonReachAndYearNoCycleFirst($section_item): array
	{
		$section_begin  = $section_item->start_day;
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 年周期 && 通用 && 达到阶梯
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenCommonByNumberReachAndYearCycle($section_item): array
	{
		// 计费片段如果在两个跨度
		if ($this->determineInTwoYearFromStart($section_item)) {
			
			return $this->genMoneyWhenCommonReachAndYearCycleInTwoYear($section_item);
		}
		
		// 片段在同一年
		return $this->genMoneyWhenCommonReachAndYearCycleInSameYear($section_item);
	}
	
	/**
	 * 年周期 && 通用 && 按用量 && 在同一年
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenCommonReachAndYearCycleInSameYear($section_item): array
	{
		// 如果计费开始时间和片段开始时间相同(那么正常就可以了)
		if ($section_item->start_day == $section_item->section_begin) {
			return $this->genMoneyWhenCommonByNumberReach($section_item);
		}
		
		// 格式化价格
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 开始计费到节点结束时候的调用量
		$params_end       = $this->genParamsFromEndWhenSameYear($section_item);
		$fee_number_end   = $this->numberForSectionByParams($section_item, $params_end);
		$params_money_end = array_merge(compact('fee_price'), $fee_number_end);
		$money_end        = app('bill.excel.reach')->genMoney($params_money_end);
		
		// 开始计费到节点开始时候的调用量
		$params_before       = $this->genParamsFromBeginWhenSameYear($section_item);
		$fee_number_before   = $this->numberForSectionByParams($section_item, $params_before);
		$params_money_before = array_merge(compact('fee_price'), $fee_number_before);
		$money_before        = app('bill.excel.reach')->genMoney($params_money_before);
		
		// 格式化
		return $this->formatMoneyWhenCommonReachAndYearCycleInSameYear($money_before, $money_end);
	}
	
	/**
	 * 格式化
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 *
	 * @return array
	 */
	private function formatMoneyWhenCommonReachAndYearCycleInSameYear(array $money_info_first, array $money_info_second): array
	{
		// 容器
		$list_container = [];
		
		list($money_first, $section_number_first) = $money_info_first ? [
			$money_info_first['money'],
			$money_info_first['section_number'],
		] : [0, 0];
		list($money_second, $section_number_second, $section_key_second, $price_second) = $money_info_second ? [
			$money_info_second['money'],
			$money_info_second['section_number'],
			$money_info_second['section_key'],
			$money_info_second['price'],
		] : [0, 0, 0, 0];
		
		$list_container[$section_key_second] = [
			'price'          => $price_second,
			'section_key'    => $section_key_second,
			'section_number' => $section_number_second - $section_number_first,
			'money'          => $money_second - $money_first,
		];
		
		return $list_container;
	}
	
	/**
	 * 年周期 && 通用 && 按用量 && 在不同的两年 && 到达
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenCommonReachAndYearCycleInTwoYear($section_item): array
	{
		// 某一轮询的开始时间到片段开始时间的前一天
		$money_first = $this->getMoneyWhenCommonReachAndYearInTwoYearFirst($section_item);
		
		// 某一轮询的开始时间到新的部分开始时间的前一天
		$money_second = $this->getMoneyWhenCommonReachAndYearInTwoYearSecond($section_item);
		
		// 新的部分的开始时间到片段结束时间
		$money_third = $this->getMoneyWhenCommonReachAndYearInTwoYearThird($section_item);
		
		// 格式化
		return $this->formatMoneyWhenCommonReachAndYearCycleInTwoYear($money_first, $money_second, $money_third);
	}
	
	/**
	 * 格式化
	 *
	 * @param array $money_info_first
	 * @param array $money_info_second
	 * @param array $money_info_third
	 *
	 * @return mixed
	 */
	private function formatMoneyWhenCommonReachAndYearCycleInTwoYear(array $money_info_first, array $money_info_second, array $money_info_third)
	{
		// 容器
		$list_container = [];
		
		list($money_first, $section_number_first) = $money_info_first ? [
			$money_info_first['money'],
			$money_info_first['section_number'],
		] : [0, 0];
		list($money_second, $section_number_second, $section_key_second, $price_second) = $money_info_second ? [
			$money_info_second['money'],
			$money_info_second['section_number'],
			$money_info_second['section_key'],
			$money_info_second['price'],
		] : [0, 0, 0, 0];
		
		$list_container[$section_key_second] = [
			'price'          => $price_second,
			'section_key'    => $section_key_second,
			'section_number' => $section_number_second - $section_number_first,
			'money'          => $money_second - $money_first,
		];
		
		// 如果第三个和第二个命中是同一个阶梯 则需要合并
		if ($section_key_second == $money_info_third['section_key']) {
			$list_container[$section_key_second]['section_number'] += $money_info_third['section_number'];
			$list_container[$section_key_second]['money']          += $money_info_third['money'];
		} else {
			$list_container[$money_info_third['section_key']] = $money_info_third;
		}
		
		return $list_container;
	}
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function getMoneyWhenCommonReachAndYearInTwoYearThird($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenCommonReachAndYearInTwoYearThird($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach')->genMoney($params_money);
	}
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonReachAndYearInTwoYearThird($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = date('Ymd', strtotime('+1 day', strtotime($day_end)));
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonReachAndYearInTwoYearSecond($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 调用量
		$params       = $this->genParamsWhenCommonReachAndYearInTwoYearSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach')->genMoney($params_money);
	}
	
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonReachAndYearInTwoYearSecond($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = $day_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonReachAndYearInTwoYearFirst($section_item): array
	{
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 数量
		$params       = $this->genParamsWhenCommonReachAndYearInTwoYearFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.reach')->genMoney($params_money);
	}
	
	/**
	 *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonReachAndYearInTwoYearFirst($section_item): array
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 生成Money
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenCommonByNumberReach($section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 计费片段时间内的调用量
		$fee_number = $this->numberInTheSection($section_item);
		
		// 参数
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
//		$price          = 0;
//		$section_number = 0;
//		$money          = $params_money;
//		$section_key    = 1;
//
//		$fee_price = array_column($fee_price, null, 'reach_standard');
//		ksort($fee_price, SORT_NUMERIC);
		
		//"price":"0.25","section_number":0,"money":0,"section_key":"1"
		
//		foreach ($fee_price as $item) {
//			$reach_standard_item = $item['reach_standard'];
//			$price_item          = $item['price'];
//
//			if ($fee_number >= $reach_standard_item) {
//				$section_key = $reach_standard_item;
//				$price       = $price_item;
//			}
//		}
		
		//return [$section_key => compact('price', 'section_number', 'money', 'section_key')];
		
		// 计费
		$money_info = app('bill.excel.reach')->genMoney($params_money);
		
		//$money_info['price']          = $price;
		//$money_info['section_number'] = $section_number;
		
		// 如果没有section_key 说明调用量为0,这时候返回[]
		return isset($money_info['section_key']) ? [$money_info['section_key'] => $money_info] : [];
	}
	
	/**
	 * 通用 && 到达 && 日周期
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyInfoWhenCommonReachDayCycle($section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberReach($section_item);
		
		// 计算这里每天的量
		$fee_number = $this->numberSectionWhenDayCycle($section_item);
		
		// 容器
		$list_container = [];
		
		// 计算每天的费用
		collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
			// 参数
			$params_money = array_merge(compact('fee_price'), $fee_number_item);
			
			// 计费
			$list_container[$day] = app('bill.excel.reach')->genMoney($params_money);
		});
		
		// 格式化金额
		return $this->formatMoneyInfoWhenCommonReachDayCycle($list_container);
	}
	
	/**
	 * 格式化金额
	 *
	 * @param array $list_container
	 *
	 * @return array
	 */
	private function formatMoneyInfoWhenCommonReachDayCycle(array $list_container): array
	{
		
		return array_reduce($list_container, function ($carry, $item) use ($list_container) {
			// 如果这天没有命中的section_key  (区间)
			if (!array_key_exists('section_key', $item)) {
				return $carry;
			}
			
			$section_key = $item['section_key'];
			
			// 如果之前没有这个阶梯的效果 则初始化
			if (!isset($carry[$section_key])) {
				$carry[$section_key] = $item;
				
				return $carry;
			}
			
			$carry[$section_key]['money']          += $item['money'];
			$carry[$section_key]['section_number'] += $item['section_number'];
			
			return $carry;
		}, []);
	}
	
	
	/**
	 * 格式化通用达到阶梯的价格
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 */
	private function formatPriceForNumberReach($section_item): array
	{
		$fee_price = json_decode($section_item->section_source->fee_price, true);
		
		return array_reduce($fee_price, function ($carry, $item) {
			$carry[] = [
				'reach_standard' => $item[0],
				'price'          => $item[1],
			];
			
			return $carry;
		}, []);
	}
	
	
	/**
	 * 通用的按用量 && 累进阶梯
	 *
	 * @param MongoBillMonth $item_bill
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function writeConsumptionDetailByMonthWhenCommonProgression(MongoBillMonth $item_bill): array
	{
		$container                  = [];
		$container['price']         = $item_bill->section_source->fee_price;
		$container['product_name']  = $item_bill->product_name;
		$container['section_begin'] = $item_bill->section_begin;
		$container['section_end']   = $item_bill->section_end;
		$container['money']         = $item_bill->money;
		$container['product_id']    = $item_bill->product_id;
		
		// 账单区间分布
		$container['list_distribute'] = $this->distributeNumberOnPriceWhenCommonProgression($item_bill);
		
		return $container;
	}
	
	/**
	 * 通用 && 累进阶梯
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function distributeNumberOnPriceWhenCommonProgression(MongoBillMonth $item): array
	{
		switch ($item->section_source->fee_step_rule) {
			case 1:
				// 天周期
				return $this->getMoneyInfoWhenCommonProgressionDayCycle($item);
				break;
			case 2:
				// 月周期
				return $this->getMoneyInfoWhenCommonProgressionMonthCycle($item);
				break;
			case 3:
				// 年周期
				return $this->genMoneyWhenCommonByNumberProgressionAndYearCycle($item);
				break;
			default:
				// 无周期
				return $this->genMoneyWhenCommonByNumberProgressionAndNoCycle($item);
		}
	}
	
	/**
	 * 无周期 && 通用 && 累进阶梯
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenCommonByNumberProgressionAndNoCycle($section_item): array
	{
		// 从开始计费时间到片段计费开始的前一天
		$money_first = $this->getMoneyWhenCommonProgressAndYearNoCycleFirst($section_item);
		
		// 从开始计费时间到片段的结束时间
		$money_second = $this->getMoneyWhenCommonProgressAndYearNoCycleSecond($section_item);
		
		// 格式化
		return $this->formatMoneyWhenCommonByNumberProgressionAndNoCycle($section_item, $money_first, $money_second);
	}
	
	/**
	 * @param       $section_item
	 * @param array $money_info_first
	 * @param array $money_info_second
	 *
	 * @return array
	 */
	private function formatMoneyWhenCommonByNumberProgressionAndNoCycle($section_item, array $money_info_first, array $money_info_second): array
	{
		// key列表
		$list_section_keys = $this->formatProgressionPrice($section_item);
		
		// 各个区间的金额和用量的分布
		$list_section_distribute = array_reduce($list_section_keys, function ($carry, $item) use ($money_info_first, $money_info_second) {
			list($section_key, $price) = [$item['section_key'], $item['price']];
			list($money_first, $section_number_first) = isset($money_info_first[$section_key]) ? [
				$money_info_first[$section_key]['money'],
				$money_info_first[$section_key]['section_number'],
			] : [0, 0];
			
			list($money_second, $section_number_second) = isset($money_info_second[$section_key]) ? [
				$money_info_second[$section_key]['money'],
				$money_info_second[$section_key]['section_number'],
			] : [0, 0];
			
			
			// 三者的聚合
			$money          = $money_second - $money_first;
			$section_number = $section_number_second - $section_number_first;
			
			$carry[$section_key] = compact('money', 'section_number', 'section_key', 'price');
			
			return $carry;
		}, []);
		
		// 如果每个区间都是0的话 则只保留第一个区间的信息
		if ($this->_determineEverySpaceIsZero($list_section_distribute)) {
			$key_current = key($list_section_distribute);
			
			return [$key_current => $list_section_distribute[$key_current]];
		}
		
		return $list_section_distribute;
	}
	
	
	/**
	 * 累进每个区间的金额是否每个区间都是0
	 *
	 * @param array $list_section_distribute
	 *
	 * @return bool
	 */
	private function _determineEverySpaceIsZero(array $list_section_distribute): bool
	{
		// 默认全是0
		$zero = true;
		collect($list_section_distribute)->each(function ($item) use (&$zero) {
			if ($item['section_number']) {
				$zero = false;
				
				return false;
			}
		});
		
		return $zero;
	}
	
	
	/**
	 * 从开始计费时间到片段的结束时间
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return float
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonProgressAndYearNoCycleSecond($section_item): array
	{
		$fee_price    = $this->formatPriceForNumberProgression($section_item);
		$params       = $this->genParamsWhenCommonProgressAndYearNoCycleSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression')->genMoney($params_money);
	}
	
	/**
	 * 从开始计费时间到片段的结束时间(第二部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genParamsWhenCommonProgressAndYearNoCycleSecond($section_item): array
	{
		$section_begin  = $section_item->start_day;
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return float
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonProgressAndYearNoCycleFirst($section_item): array
	{
		// 如果开始计费时间和片段开始时间是一样的话,则第一部分的账单是0
		if ($section_item->section_begin == $section_item->start_day) {
			return [];
		}
		
		return $this->getMoneyWhenCommonProgressAndYearNoCycleFirstAndTimeDiff($section_item);
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天（第一部分）时间不同
	 *
	 * @param $section_item
	 *
	 * @return float
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonProgressAndYearNoCycleFirstAndTimeDiff($section_item): array
	{
		$fee_price    = $this->formatPriceForNumberProgression($section_item);
		$params       = $this->genParamsWhenCommonProgressAndYearNoCycleFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression')->genMoney($params_money);
	}
	
	/**
	 * 从开始计费时间到片段计费开始的前一天(第一部分)
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonProgressAndYearNoCycleFirst($section_item): array
	{
		$section_begin  = $section_item->start_day;
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 年周期 && 通用 && 累进阶梯
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenCommonByNumberProgressionAndYearCycle($section_item): array
	{
		// 计费片段如果在两个跨度
		if ($this->determineInTwoYearFromStart($section_item)) {
			return $this->genMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item);
		}
		
		return $this->genMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item);
	}
	
	/**
	 * 年周期 && 通用 && 按用量 && 在同一年
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item): array
	{
		// 如果计费开始时间和片段开始时间相同(那么正常就可以了)
		if ($section_item->start_day == $section_item->section_begin) {
			return $this->genMoneyWhenCommonByNumberProgression($section_item);
		}
		
		// 格式化价格
		$fee_price = $this->formatPriceForNumberProgression($section_item);
		
		// 开始计费到节点结束时候的调用量
		$params_end       = $this->genParamsFromEndWhenSameYear($section_item);
		$fee_number_end   = $this->numberForSectionByParams($section_item, $params_end);
		$params_money_end = array_merge(compact('fee_price'), $fee_number_end);
		$money_end        = app('bill.excel.progression')->genMoney($params_money_end);
		
		// 开始计费到节点开始时候的调用量
		$params_before       = $this->genParamsFromBeginWhenSameYear($section_item);
		$fee_number_before   = $this->numberForSectionByParams($section_item, $params_before);
		$params_money_before = array_merge(compact('fee_price'), $fee_number_before);
		$money_before        = app('bill.excel.progression')->genMoney($params_money_before);
		
		// 设置属性
		return $this->formatMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item, $money_end, $money_before);
	}
	
	/**
	 * @param MongoBillMonth $section_item
	 * @param array          $money_info_second
	 * @param array          $money_info_first
	 *
	 * @return mixed
	 */
	private function formatMoneyWhenCommonProgressionAndYearCycleInSameYear($section_item, array $money_info_second, array $money_info_first): array
	{
		// key列表
		$list_section_keys = $this->formatProgressionPrice($section_item);
		
		return array_reduce($list_section_keys, function ($carry, $item) use ($money_info_first, $money_info_second) {
			list($section_key, $price) = [$item['section_key'], $item['price']];
			list($money_first, $section_number_first) = isset($money_info_first[$section_key]) ? [
				$money_info_first[$section_key]['money'],
				$money_info_first[$section_key]['section_number'],
			] : [0, 0];
			
			list($money_second, $section_number_second) = isset($money_info_second[$section_key]) ? [
				$money_info_second[$section_key]['money'],
				$money_info_second[$section_key]['section_number'],
			] : [0, 0];
			
			
			// 三者的聚合
			$money          = $money_second - $money_first;
			$section_number = $section_number_second - $section_number_first;
			
			$carry[$section_key] = compact('money', 'section_number', 'section_key', 'price');
			
			return $carry;
		}, []);
	}
	
	/**
	 * 开始计费到节点开始时候的参数 && 同一年
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsFromBeginWhenSameYear($section_item): array
	{
		//  在同一年的时候,获取第一阶段的开始时间
		$section_begin = $this->genBeginDayWhenSameYear($section_item);
		
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	
	/**
	 * 开始计费到节点开始时候的参数 && 同一年
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsFromEndWhenSameYear($section_item): array
	{
		// 同一年的开始时间
		$section_begin = $this->genBeginDayWhenSameYear($section_item);
		
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 在同一年的时候,获取第一阶段的开始时间
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return string
	 */
	private function genBeginDayWhenSameYear($section_item): string
	{
		list($section_begin, $start_day) = [$section_item->section_begin, $section_item->start_day];
		
		// 如果开始计费时间和片段时间是在一年
		$days_from_begin = (strtotime($section_begin) - strtotime($start_day)) / 86400;
		
		// 和365天求余数
		$times     = floor($days_from_begin / $this->days_in_years);
		$diff_days = $this->days_in_years * $times;
		
		return date('Ymd', strtotime('+' . $diff_days . ' days', strtotime($start_day)));
	}
	
	/**
	 * 生成Money
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenCommonByNumberProgression($section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberProgression($section_item);
		
		// 计费片段时间内的调用量
		$fee_number = $this->numberInTheSection($section_item);
		
		// 参数
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		// 计费
		return app('bill.excel.progression')->genMoney($params_money);
	}
	
	/**
	 * 年周期 && 通用 && 按用量 && 在不同的两年
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item): array
	{
		// 某一轮询的开始时间到片段开始时间的前一天
		$money_first = $this->getMoneyWhenCommonProgressAndYearInTwoYearFirst($section_item);
		
		// 某一轮询的开始时间到新的部分开始时间的前一天
		$money_second = $this->getMoneyWhenCommonProgressAndYearInTwoYearSecond($section_item);
		
		// 新的部分的开始时间到片段结束时间
		$money_third = $this->getMoneyWhenCommonProgressAndYearInTwoYearThird($section_item);
		
		// 格式化计费
		return $this->formatMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item, $money_first, $money_second, $money_third);
	}
	
	
	/**
	 * 格式化
	 *
	 * @param MongoBillMonth $section_item
	 * @param array          $money_info_first
	 * @param array          $money_info_second
	 * @param array          $money_info_third
	 *
	 * @return mixed
	 */
	private function formatMoneyWhenCommonProgressionAndYearCycleInTwoYear($section_item, array $money_info_first, array $money_info_second, array $money_info_third)
	{
		// key列表
		$list_section_keys = $this->formatProgressionPrice($section_item);
		
		return array_reduce($list_section_keys, function ($carry, $item) use ($money_info_first, $money_info_second, $money_info_third) {
			list($section_key, $price) = [$item['section_key'], $item['price']];
			list($money_first, $section_number_first) = isset($money_info_first[$section_key]) ? [
				$money_info_first[$section_key]['money'],
				$money_info_first[$section_key]['section_number'],
			] : [0, 0];
			
			list($money_second, $section_number_second) = isset($money_info_second[$section_key]) ? [
				$money_info_second[$section_key]['money'],
				$money_info_second[$section_key]['section_number'],
			] : [0, 0];
			
			list($money_third, $section_number_third) = isset($money_info_third[$section_key]) ? [
				$money_info_third[$section_key]['money'],
				$money_info_third[$section_key]['section_number'],
			] : [0, 0];
			
			// 三者的聚合
			$money          = $money_second - $money_first + $money_third;
			$section_number = $section_number_second - $section_number_first + $section_number_third;
			
			$carry[$section_key] = compact('money', 'section_number', 'section_key', 'price');
			
			return $carry;
		}, []);
	}
	
	/**
	 * 格式化累进阶梯的参数
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 */
	private function formatProgressionPrice($section_item): array
	{
		$fee_price = json_decode($section_item->section_source->fee_price);
		
		return array_reduce($fee_price, function ($carry, $item) {
			list($left, $right, $price) = [$item[0], $item[1], $item[2]];
			if ($right != -1) {
				$section_key = $left . '-' . $right;
			} else {
				$section_key = $left . '+';
			}
			
			array_push($carry, compact('price', 'section_key'));
			
			return $carry;
		}, []);
	}
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function getMoneyWhenCommonProgressAndYearInTwoYearThird($section_item): array
	{
		$fee_price    = $this->formatPriceForNumberProgression($section_item);
		$params       = $this->genParamsWhenCommonProgressAndYearInTwoYearThird($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression')->genMoney($params_money);
	}
	
	/**
	 * 新的部分的开始时间到片段结束时间(第三部分)
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonProgressAndYearInTwoYearThird($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = date('Ymd', strtotime('+1 day', strtotime($day_end)));
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonProgressAndYearInTwoYearSecond($section_item): array
	{
		$fee_price    = $this->formatPriceForNumberProgression($section_item);
		$params       = $this->genParamsWhenCommonProgressAndYearInTwoYearSecond($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression')->genMoney($params_money);
	}
	
	/**
	 * 某一轮询的开始时间到新的部分开始时间的前一天（第二部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonProgressAndYearInTwoYearSecond($section_item)
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = $day_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyWhenCommonProgressAndYearInTwoYearFirst($section_item): array
	{
		$fee_price    = $this->formatPriceForNumberProgression($section_item);
		$params       = $this->genParamsWhenCommonProgressAndYearInTwoYearFirst($section_item);
		$fee_number   = $this->numberForSectionByParams($section_item, $params);
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		return app('bill.excel.progression')->genMoney($params_money);
	}
	
	/**
	 * 计费片段的产品调用量
	 *
	 * @param MongoBillMonth $section_item
	 * @param array          $params
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function numberForSectionByParams($section_item, array $params): array
	{
		// 驱动名称
		$driver_name = $this->genStatDriverName($section_item);
		
		return app('bill.statistic')
			->driver($driver_name)
			->billStat($params);
	}
	
	/**
	 *  某一轮询的开始时间到片段开始时间的前一天（第一部分）
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsWhenCommonProgressAndYearInTwoYearFirst($section_item): array
	{
		list($day_begin, $day_end) = $this->getTheLastDayOfThisSection($section_item);
		
		$section_begin  = $day_begin;
		$section_end    = date('Ymd', strtotime('-1 day', strtotime($section_item->section_begin)));
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * 获取某一轮询的结束日期
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 */
	private function getTheLastDayOfThisSection($section_item): array
	{
		list($section_begin, $section_end, $start_day) = [
			$section_item->section_begin,
			$section_item->section_end,
			$section_item->start_day,
		];
		
		// 这里一定不会出现等于的情况，如果出现出现的话,那么这个片段是在一个循环
		$timestamp_end = strtotime($start_day) + 86400 * $this->days_in_years;
		$day_end       = date('Ymd', $timestamp_end);
		$day_begin     = $start_day;
		while (true) {
			if ($day_end > $section_begin && $day_end < $section_end) {
				break;
			}
			
			$day_begin     = date('Ymd', strtotime('+1 day', strtotime($day_end)));
			$timestamp_end = strtotime($day_end) + 86400 * $this->days_in_years;
			$day_end       = date('Ymd', $timestamp_end);
		}
		
		return [$day_begin, $day_end];
	}
	
	/**
	 * 本次计费片段是否包含了两年的时间
	 *
	 * @param $section_item
	 *
	 * @return bool
	 */
	private function determineInTwoYearFromStart($section_item): bool
	{
		// 距离开始日期的天数
		list($section_begin, $section_end, $start_day) = [
			$section_item->section_begin,
			$section_item->section_end,
			$section_item->section_source->start_date,
		];
		
		$days_from_begin = (strtotime($section_begin) - strtotime($start_day)) / 86400;
		$days_from_end   = (strtotime($section_end) - strtotime($start_day)) / 86400;
		
		// 和365天求余数
		$remainder_from_begin = $days_from_begin % $this->days_in_years;
		$remainder_from_end   = $days_from_end % $this->days_in_years;
		
		// 因为section_begin && section_end最多间隔一个月， 所以 如果结束的余数小于了开始的余数 则说明进入两个周期
		return $remainder_from_end < $remainder_from_begin;
	}
	
	/**
	 * 通用 && 累进阶梯 && 月周期
	 *
	 * @param MongoBillMonth $bill_section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyInfoWhenCommonProgressionMonthCycle($bill_section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberProgression($bill_section_item);
		
		// 计费片段时间内的调用量
		$fee_number = $this->numberInTheSection($bill_section_item);
		
		// 参数
		$params_money = array_merge(compact('fee_price'), $fee_number);
		
		// 计费
		return app('bill.excel.progression')->genMoney($params_money);
	}
	
	/**
	 * 通用 && 累进阶梯 && 天周期
	 *
	 * @param MongoBillMonth $bill_section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function getMoneyInfoWhenCommonProgressionDayCycle($bill_section_item): array
	{
		$list_container = $this->genMoneyWhenCommonByNumberProgressionAndDayCycle($bill_section_item);
		
		// 格式化
		return $this->formatMoneyInfoWhenCommonProgressionDayCycle($list_container);
	}
	
	/**
	 * 格式化数据
	 *
	 * @param array $list_day_money
	 *
	 * @return array
	 */
	private function formatMoneyInfoWhenCommonProgressionDayCycle(array $list_day_money): array
	{
		// 容器
		$list_container = [];
		
		array_walk($list_day_money, function ($info_day, $day) use (&$list_container) {
			array_walk($info_day, function ($section_money, $section_key) use (&$list_container) {
				if (!isset($list_container[$section_key])) {
					$list_container[$section_key] = $section_money;
					
					return;
				}
				$list_container[$section_key]['section_number'] += $section_money['section_number'];
				$list_container[$section_key]['money']          += $section_money['money'];
			});
		});
		
		return $list_container;
	}
	
	/**
	 * 天周期 && 通用 && 累进阶梯
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @throws CustomException
	 * @return array
	 */
	private function genMoneyWhenCommonByNumberProgressionAndDayCycle($section_item): array
	{
		// 格式化价格
		$fee_price = $this->formatPriceForNumberProgression($section_item);
		
		// 计算这里每天的量
		$fee_number = $this->numberSectionWhenDayCycle($section_item);
		
		// 总价格
		$list_container = [];
		
		// 计算每天的费用
		collect($fee_number)->each(function ($fee_number_item, $day) use ($fee_price, &$list_container) {
			// 参数
			$params_money = array_merge(compact('fee_price'), $fee_number_item);
			
			// 计费
			$list_container[$day] = app('bill.excel.progression')->genMoney($params_money);
		});
		
		return $list_container;
	}
	
	/**
	 * 日调用量
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function numberSectionWhenDayCycle($section_item): array
	{
		// 驱动名称
		$driver_name = $this->genStatDriverName($section_item);
		
		// 参数
		$params = $this->genParamsForStatDay($section_item);
		
		// 容器
		$list_container = [];
		
		array_walk($params, function ($param) use (&$list_container, $driver_name) {
			$day                  = $param['section_begin'];
			$list_container[$day] = app('bill.statistic')
				->driver($driver_name)
				->billStat($param);
		});
		
		return $list_container;
	}
	
	/**
	 * 生成天周期的参数
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsForStatDay($section_item)
	{
		$begin = $section_item->section_begin;
		$end   = $section_item->section_end;
		
		// 基础信息
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey    = $this->getApikeyForSection($section_item);
		$base_item = compact('apikey', 'uuid', 'fee_basis', 'fee_price_rule');
		
		// 容器
		$list_container = [];
		while ($begin <= $end) {
			$time_item        = [
				'section_begin' => $begin,
				'section_end'   => $begin,
			];
			$list_container[] = array_merge($base_item, $time_item);
			$begin            = date('Ymd', strtotime('+1 day', strtotime($begin)));
		}
		
		return $list_container;
	}
	
	/**
	 * 格式化通用累进阶梯的价格
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 */
	private function formatPriceForNumberProgression($section_item): array
	{
		$fee_price = json_decode($section_item->section_source->fee_price, true);
		
		return array_reduce($fee_price, function ($carry, $item) {
			$carry[] = [
				'left'  => $item[0],
				'right' => $item[1],
				'price' => $item[2],
			];
			
			return $carry;
		}, []);
	}
	
	/**
	 * 如果通用的按用量 && 固定价格 则生成特定月分明的消费明细
	 *
	 * @param MongoBillMonth $item_bill
	 *
	 * @return array
	 */
	private function writeConsumptionDetailByMonthWhenCommonNumberFixed(MongoBillMonth $item_bill): array
	{
		// 容器
		$container                  = [];
		$container['price']         = $item_bill->section_source->fee_price;
		$container['product_name']  = $item_bill->product_name;
		$container['section_begin'] = $item_bill->section_begin;
		$container['section_end']   = $item_bill->section_end;
		$container['money']         = $item_bill->money;
		$container['fee_number']    = ($item_bill->section_number['fee_number'] ?? 0);
		$container['product_id']    = $item_bill->product_id;
		
		return $container;
		
	}
	
	/**
	 * 按月生成各种算法的账单明细 && 通用按时间
	 *
	 * @param MongoBillMonth $item_bill
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function writeConsumptionDetailByMonthWhenCommonDate(MongoBillMonth $item_bill): array
	{
		$container                  = [];
		$container['price']         = $item_bill->section_source->fee_price . '/' . $this->list_fee_time_rule_mapping[$item_bill->section_source->fee_time_rule];
		$container['product_name']  = $item_bill->product_name;
		$container['section_begin'] = $item_bill->section_begin;
		$container['section_end']   = $item_bill->section_end;
		$container['money']         = $item_bill->money;
		$container['product_id']    = $item_bill->product_id;
		$container['fee_number']    = $this->getFeeNumberWhenCommonDate($item_bill);
		
		return $container;
	}
	
	/**
	 * 通用 && 按日期 && 获取调用量
	 *
	 * @param $section_item
	 *
	 * @throws CustomException
	 * @return int
	 */
	private function getFeeNumberWhenCommonDate($section_item)
	{
		$fee_number = $this->numberInTheSection($section_item);
		
		return $fee_number['fee_number'] ?? 0;
	}
	
	/**
	 * 计费片段的产品调用量
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function numberInTheSection($section_item): array
	{
		// 驱动名称
		$driver_name = $this->genStatDriverName($section_item);
		
		// 参数
		$params = $this->genParamsForStatDriver($section_item);
		
		return app('bill.statistic')
			->driver($driver_name)
			->billStat($params);
	}
	
	/**
	 * 获取调用量驱动的参数
	 *
	 * @param $section_item
	 *
	 * @return array
	 * @throws CustomException
	 */
	private function genParamsForStatDriver($section_item): array
	{
		$section_begin  = $section_item->section_begin;
		$section_end    = $section_item->section_end;
		$uuid           = $section_item->uuid;
		$fee_basis      = $section_item->section_source->fee_basis;
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		// 获取apikey
		$apikey = $this->getApikeyForSection($section_item);
		
		return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
	}
	
	/**
	 * @param MongoBillMonth $section_item
	 *
	 * @return string
	 * @throws CustomException
	 */
	private function getApikeyForSection($section_item): string
	{
		$account_id = $section_item->account_id;
		$account    = Account::getOneItemByCondition(compact('account_id'), 'apikey');
		if (!$account || !$account->apikey) {
			throw new CustomException('uuid : ' . $section_item->uuid . ' account_id:' . $section_item->account_id . '没有找到合法的账号');
		}
		
		return $account->apikey;
	}
	
	/**
	 * 获取调用量驱动的名称
	 *
	 * @param MongoBillMonth $section_item
	 *
	 * @return string
	 */
	private function genStatDriverName($section_item): string
	{
		$product_id = $section_item->product_id;
		
		return $this->driver_prefix . $product_id;
	}
	
	/**
	 * 按照账号分组计费配置
	 * @return array
	 */
	private function _getMoneyDetailForGroup(): array
	{
		// 产品列表
		$list_products = Product::getListByCondition([], ['product_id', 'product_name'])
								->pluck('product_name', 'product_id')
								->all();
		$list_accounts = Account::getListByCondition([], ['account_id', 'account_name'])
								->pluck('account_name', 'account_id')
								->all();
		
		return MongoBillMonth::where([
			'customer_id' => $this->customer_id,
			'month'       => date('Ym', strtotime('-1 month')),
		])
							 ->get()
							 ->filter(function ($item) {
								 $remarks = $item['section_source']['remarks'] ?? '';
			
								 return trim($remarks) != '系统归零设置';
							 })
							 ->reduce(function ($carry, $item) use ($list_products, $list_accounts) {
								 // 充值属性类型,方便操作
								 $item = $this->_setObjectAttr($item);
			
								 //  设置算法 && 其他的基本属性
								 $item = $this->_setAloAttr($item, $list_products);
			
								 // 返回赋值
								 return $carry = $this->_setValueForReturn($carry, $item, $list_accounts);
							 }, []);
	}
	
	/**
	 * 返回赋值
	 *
	 * @param array          $carry
	 * @param MongoBillMonth $item
	 *
	 * @return array
	 */
	private function _setValueForReturn(array $carry, MongoBillMonth $item, array $list_accounts): array
	{
		$carry[$item->account_id]['list_bills'][] = $item;
		if (!isset($carry[$item->account_id]) || !isset($carry[$item->account_id]['account_name'])) {
			$carry[$item->account_id]['account_name'] = $list_accounts[$item->account_id] ?? "没有找到对应的账号";
		}
		
		return $carry;
	}
	
	/**
	 * 设置算法
	 *
	 * @param MongoBillMonth $item
	 * @param array          $list_products
	 *
	 * @return MongoBillMonth
	 */
	private function _setAloAttr(MongoBillMonth $item, array $list_products): MongoBillMonth
	{
		list($product_alo, $product_alo_cn) = $this->_getProductAlo($item);
		$item->product_alo    = $product_alo;
		$item->product_alo_cn = $product_alo_cn;
		$item->product_name   = $list_products[$item->product_id] ?? '没有找到对应的产品';
		
		return $item;
	}
	
	/**
	 * 重置属性类型,方便操作
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return MongoBillMonth
	 */
	private function _setObjectAttr(MongoBillMonth $item): MongoBillMonth
	{
		$item->section_source = (object)$item->section_source;
		$item->start_day      = date('Ymd', strtotime($item->section_source->start_date));
		
		return $item;
	}
	
	/**
	 * 获取计费片段对应的算法
	 *
	 * @param MongoBillMonth $item_bill
	 *
	 * @return array
	 */
	public function _getProductAlo(MongoBillMonth $item_bill): array
	{
		// 通用 && 按时间
		if ($this->determineIfSectionIsCommonByDate($item_bill)) {
			// 设置算法分组
			return [
				$this->getBillGroupAlgWhenCommonByDate($item_bill),
				"通用 && 按时间",
			];
		}
		
		// 如果通用的按用量 && 固定价格
		if ($this->determineIfSectionIdCommonByNumberFixed($item_bill)) {
			// 设置算法分组
			return [
				$this->getBillGroupAlgWhenCommonByNumberFixed($item_bill),
				"通用的按用量 && 固定价格",
			];
		}
		
		// 通用的按用量 && 累进阶梯
		if ($this->determineIfSectionIsCommonByNumberProgression($item_bill)) {
			// 设置算法分组
			return [
				$this->getBillGroupAlgWhenCommonByNumberProgression($item_bill),
				"通用的按用量 && 累进阶梯",
			];
		}
		
		// 通用的按用量 && 到达阶梯
		if ($this->determineSectionIsCommonByNumberReach($item_bill)) {
			return [
				$this->getBillGroupAlgWhenCommonNumberReach($item_bill),
				"通用的按用量 && 到达阶梯",
			];
		}
		
		// 区分运营商按时间
		if ($this->determineSectionIsDateOperator($item_bill)) {
			return [
				$this->getBillGroupAlgWhenDateOperator($item_bill),
				"区分运营商按时间",
			];
		}
		
		// 区分运营商按用量 && 固定价格
		if ($this->determineIfSectionIdOperatorByNumberFixed($item_bill)) {
			return [
				$this->getBillGroupAlgWhenOperatorByNumberFixed($item_bill),
				"区分运营商按用量 && 固定价格",
			];
		}
		
		// 区分运营商按用量 && 累进阶梯
		if ($this->determineIfSectionIsOperatorByNumberProgression($item_bill)) {
			return [
				$this->getBillGroupAlgWhenOperatorByNumberProgression($item_bill),
				"区分运营商按用量 && 累进阶梯",
			];
		}
		
		// 区分运营商按用量 && 到达阶梯
		if ($this->determineSectionIsOperatorByNumberReach($item_bill)) {
			return [
				$this->getBillGroupAlgWhenOperatorNumberReach($item_bill),
				"区分运营商按用量 && 到达阶梯",
			];
		}
	}
	
	/**
	 * 区分运营商按用量 && 到达阶梯算法服务
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return string
	 */
	private function getBillGroupAlgWhenOperatorNumberReach(MongoBillMonth $item): string
	{
		list($fee_method, $fee_amount_rule, $fee_price_rule) = [
			$item->section_source->fee_method,
			$item->section_source->fee_amount_rule,
			$item->section_source->fee_price_rule,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule, $fee_price_rule);
	}
	
	/**
	 * 片段是否是(运营商的按用量 && 到达阶梯)
	 *
	 * @param object $section_item
	 *
	 * @return bool
	 */
	private function determineSectionIsOperatorByNumberReach($section_item): bool
	{
		// 计费方式(时间用量)
		$fee_method = $section_item->section_source->fee_method;
		
		// 用量计费规则
		$fee_amount_rule = $section_item->section_source->fee_amount_rule;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule == 2;
	}
	
	/**
	 * 区分运营商按用量 && 累进阶梯分组算法
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return string
	 */
	private function getBillGroupAlgWhenOperatorByNumberProgression(MongoBillMonth $item): string
	{
		list($fee_method, $fee_amount_rule, $fee_price_rule) = [
			$item->section_source->fee_method,
			$item->section_source->fee_amount_rule,
			$item->section_source->fee_price_rule,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule, $fee_price_rule);
	}
	
	/**
	 * @param object $section_item
	 *
	 * @return bool
	 */
	private function determineIfSectionIsOperatorByNumberProgression($section_item): bool
	{
		// 计费方式(时间用量)
		$fee_method = $section_item->section_source->fee_method;
		
		// 用量计费规则
		$fee_amount_rule = $section_item->section_source->fee_amount_rule;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule == 2;
	}
	
	
	/**
	 * 区分运营商按用量 && 固定价格算法分组
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return string
	 */
	private function getBillGroupAlgWhenOperatorByNumberFixed(MongoBillMonth $item): string
	{
		list($fee_method, $fee_amount_rule, $fee_price_rule) = [
			$item->section_source->fee_method,
			$item->section_source->fee_amount_rule,
			$item->section_source->fee_price_rule,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule, $fee_price_rule);
	}
	
	/**
	 * 计费片段是否是运营商固定价格模式
	 *
	 * @param $section_item
	 *
	 * @return bool
	 */
	private function determineIfSectionIdOperatorByNumberFixed($section_item): bool
	{
		// 计费方式(时间用量)
		$fee_method = $section_item->section_source->fee_method;
		
		// 用量计费规则
		$fee_amount_rule = $section_item->section_source->fee_amount_rule;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule == 2;
	}
	
	
	/**
	 * 区分运营商按时间算法分组
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return string
	 */
	private function getBillGroupAlgWhenDateOperator(MongoBillMonth $item): string
	{
		list($fee_method, $fee_price_rule) = [
			$item->section_source->fee_method,
			$item->section_source->fee_price_rule,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method, $fee_price_rule);
	}
	
	/**
	 * 是否是区分运营商按时间的场景
	 *
	 * @param object $section_item
	 *
	 * @return bool
	 */
	private function determineSectionIsDateOperator($section_item): bool
	{
		// 按时间
		$fee_method = $section_item->section_source->fee_method;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return 1 == $fee_method && $fee_price_rule == 2;
	}
	
	/**
	 * 通用的按用量 && 到达阶梯设置算法分组
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return string
	 */
	private function getBillGroupAlgWhenCommonNumberReach(MongoBillMonth $item): string
	{
		list($fee_method, $fee_amount_rule) = [
			$item->section_source->fee_method,
			$item->section_source->fee_amount_rule,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule);
	}
	
	/**
	 * 片段是否是(通用的按用量 && 到达阶梯)
	 *
	 * @param object $section_item
	 *
	 * @return bool
	 */
	private function determineSectionIsCommonByNumberReach($section_item): bool
	{
		// 计费方式(时间用量)
		$fee_method = $section_item->section_source->fee_method;
		
		// 用量计费规则
		$fee_amount_rule = $section_item->section_source->fee_amount_rule;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return $fee_method == 2 && $fee_amount_rule == 3 && $fee_price_rule != 2;
	}
	
	/**
	 * 通用的按用量 && 累进阶梯设置算法分组
	 *
	 * @param MongoBillMonth $item
	 *
	 * @return string
	 */
	private function getBillGroupAlgWhenCommonByNumberProgression(MongoBillMonth $item): string
	{
		list($fee_method, $fee_amount_rule) = [
			$item->section_source->fee_method,
			$item->section_source->fee_amount_rule,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule);
	}
	
	/**
	 * @param  $section_item
	 *
	 * @return bool
	 */
	private function determineIfSectionIsCommonByNumberProgression($section_item): bool
	{
		// 计费方式(时间用量)
		$fee_method = $section_item->section_source->fee_method;
		
		// 用量计费规则
		$fee_amount_rule = $section_item->section_source->fee_amount_rule;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return $fee_method == 2 && $fee_amount_rule == 2 && $fee_price_rule != 2;
	}
	
	/**
	 * 通用的按用量 && 固定价格设置算法分组
	 *
	 * @param MongoBillMonth $item_bill
	 *
	 * @return mixed
	 */
	private function getBillGroupAlgWhenCommonByNumberFixed(MongoBillMonth $item_bill)
	{
		list($fee_method, $fee_amount_rule) = [
			$item_bill->section_source->fee_method,
			$item_bill->section_source->fee_amount_rule,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method, $fee_amount_rule);
	}
	
	/**
	 * 计费片段是否是通用固定价格模式
	 *
	 * @param $section_item
	 *
	 * @return bool
	 */
	private function determineIfSectionIdCommonByNumberFixed($section_item): bool
	{
		// 计费方式(时间用量)
		$fee_method = $section_item->section_source->fee_method;
		
		// 用量计费规则
		$fee_amount_rule = $section_item->section_source->fee_amount_rule;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule != 2;
	}
	
	/**
	 * 通用 && 按时间设置
	 *
	 * @param MongoBillMonth $section_bill_item
	 *
	 * @return mixed
	 */
	private function getBillGroupAlgWhenCommonByDate(MongoBillMonth $section_bill_item)
	{
		list($fee_method) = [
			$section_bill_item->section_source->fee_method,
		];
		
		// 生成算法分组key
		return $this->genBillGroupAlgKey($fee_method);
	}
	
	/**
	 * 生成算法分组key
	 * @return mixed
	 */
	private function genBillGroupAlgKey()
	{
		$arg_list = func_get_args();
		
		return array_reduce($arg_list, function ($carry, $item) {
			if ($item) {
				$carry .= ('_' . $item);
			}
			
			return $carry;
		}, '');
	}
	
	/**
	 * 是否是通用时间
	 *
	 * @param object $section_item
	 *
	 * @return bool
	 */
	private function determineIfSectionIsCommonByDate($section_item)
	{
		// 按时间
		$fee_method = $section_item->section_source->fee_method;
		
		// 模式(标准1 运营商2)
		$fee_price_rule = $section_item->section_source->fee_price_rule;
		
		return 1 == $fee_method && $fee_price_rule != 2;
	}
	
	
}