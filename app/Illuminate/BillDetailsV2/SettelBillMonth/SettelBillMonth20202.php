<?php

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;


//按用量 && 累进阶梯 && 无区分运营商配置项 && 周期为 月
class SettelBillMonth20202 extends SettelBillMonth
{
    public function settel($bill_month_item)
    {
        $fee_price      = json_decode($bill_month_item['section_source']['fee_price'], true);
        $section_number = $this->getBillStat($bill_month_item['product_id'])
            ->billStat($this->getParams($bill_month_item))['fee_number'];

        //初始化一个存放结果的数据
        $result = [];
        foreach ($fee_price as $fee_price_item) {
            $min   = $fee_price_item[0];
            $max   = $fee_price_item[1];
            $price = $fee_price_item[2];
            $temp  = [
                'name'   => $min . ' - ' . $max,
                'price'  => $price,
                'number' => 0,
                'money'  => 0.00
            ];
            if ($section_number >= $min && $section_number <= $max) {
                //正好位于当前区间中（不是最后一个区间）
                $temp['number'] = bcadd(bcsub($section_number, $min), 1);
            } elseif ($section_number >= $min && $max == -1) {
                //当前区间是最后一个区间，并且在该区间中
                $temp['number'] = bcadd(bcsub($section_number, $min), 1);
            } elseif ($section_number > $max && $max != -1) {
                //位于当前区间后面的区间
                $temp['number'] = bcadd(bcsub($max, $min), 1);
            }
            $temp['money'] = bcmul($temp['number'], $temp['price'], 6);
            $result[$min]  = $temp;
        }

        //校验结果
        $money = round(array_sum(array_column($result, 'money')), 2);
        if ($money != round($bill_month_item['money'], 2)) {
            throw new \Exception('20202校验结果失败');
        }
        return $result;
    }
}