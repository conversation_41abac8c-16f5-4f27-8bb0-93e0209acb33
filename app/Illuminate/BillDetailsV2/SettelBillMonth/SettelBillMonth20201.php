<?php

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;


//按用量 && 累进阶梯 && 无区分运营商配置项 && 周期为 日
class SettelBillMonth20201 extends SettelBillMonth
{
    public function settel($bill_month_item)
    {
        $fee_price = json_decode($bill_month_item['section_source']['fee_price'], true);

        //获取每日的调用量
        $section_number = $this->getSectionNumber($bill_month_item);

        //初始化一个存放结果的数据
        $result = array_map(
            function ($fee_price_item) {
                list($min, $max, $price) = $fee_price_item;
                $number = 0;
                $money  = 0.00;
                return compact('min', 'max', 'price', 'number', 'money');
            },
            $fee_price
        );

        //遍历每日的数据，将数据进行分割、组装
        array_map(
            function ($section_number_item) use (&$result) {
                $section_number_item = $section_number_item['fee_number'];
                $result              = array_map(
                    function ($step_item) use ($section_number_item) {
                        $min = $step_item['min'];
                        $max = $step_item['max'];
                        if ($section_number_item >= $min && $section_number_item <= $max) {
                            //正好位于非最后一个区间的区间中
                            $step_number         = bcadd(bcsub($section_number_item, $min), 1);
                            $step_item['number'] = bcadd($step_item['number'], $step_number);
                        } elseif ($section_number_item >= $min && $max == -1) {
                            //正好位于最后的一个区间中
                            $step_number         = bcadd(bcsub($section_number_item, $min), 1);
                            $step_item['number'] = bcadd($step_item['number'], $step_number);
                        } elseif ($section_number_item > $max && $max != -1) {
                            //在此区间之上
                            $step_number         = bcadd(bcsub($max, $min), 1);
                            $step_item['number'] = bcadd($step_item['number'], $step_number);
                        }
                        return $step_item;
                    },
                    $result
                );
            },
            $section_number
        );

        //整理结果数据
        $result = array_map(
            function ($step_item) {
                $money              = round(bcmul($step_item['price'], $step_item['number'], 4), 4);
                $step_item['money'] = sprintf("%1\$.4f", $money);
                $step_item['name']  = $step_item['max'] == '-1' ? $step_item['min'] . '+' : $step_item['min'] . '-' . $step_item['max'];
                return $step_item;
            },
            $result
        );

        //校验结果
        $result_money = round(array_sum(array_column($result, 'money')), 2);
        if ($result_money != round($bill_month_item['money'], 2)) {
            throw new \Exception('20201校验结果失败');
        }

        $result['name'] = $this->getDateTitle($bill_month_item);

        return $result;
    }

    /**
     * 获取计费周期内的每日调用量
     *
     * @access private
     *
     * @param $bill_month_item array
     *
     * @return array
     **/
    private function getSectionNumber($bill_month_item)
    {
        $params         = $this->getParams($bill_month_item);
        $section_number = [];
        $section_begin  = strtotime($params['section_begin']);
        $section_end    = strtotime($params['section_end']);
        $product_id     = $bill_month_item['product_id'];
        for ($i = $section_begin; $i <= $section_end; $i += 86400) {
            $date                    = date('Ymd', $i);
            $params['section_begin'] = $date;
            $params['section_end'] = $date;
            $section_number[$date]   = $this->getBillStat($product_id)
                ->billStat($params);
        }
        return $section_number;
    }
}