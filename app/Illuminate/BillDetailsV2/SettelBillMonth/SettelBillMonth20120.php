<?php

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;

//按用量 && 固定单价 && 区分运营商 && 无周期配置
class SettelBillMonth20120 extends SettelBillMonth
{
    private $operator = [
        '移动' => [
            'fee_config_label' => 0,
            'bill_month_label' => 'yd'
        ],
        '联通' => [
            'fee_config_label' => 1,
            'bill_month_label' => 'lt'
        ],
        '电信' => [
            'fee_config_label' => 2,
            'bill_month_label' => 'dx'
        ]
    ];

    public function settel($bill_month_item)
    {
        halt('未完成');

//        //价格
//        $fee_price = json_decode($bill_month_item['section_source']['fee_price'], true);
//
//        //用量
//        $fee_number = $this->getBillStat($bill_month_item['product_id'])
//            ->billStat($this->getParams($bill_month_item));
//
//        //结果数据
//        $result = [];
//
//        foreach ($this->operator as $name => $label) {
//            $fee_config_label = $label['fee_config_label'];
//            $bill_month_label = $label['bill_month_label'];
//            $price            = $fee_price[$fee_config_label];
//            $number           = $fee_number[$bill_month_label];
//            $money            = bcmul($number, $price, 6);
//            $result[]         = compact('name', 'price', 'number', 'money');
//        }
//
//        //校验
//        $money = round(array_sum(array_column($result, 'money')), 2);
//        if ($money != $bill_month_item['money']) {
//            throw new \Exception('20120校验结果失败');
//        }
//
//        return $result;
    }
}