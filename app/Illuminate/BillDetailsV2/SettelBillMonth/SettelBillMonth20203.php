<?php

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;


//按用量 && 累进阶梯 && 无区分运营商配置项 && 周期为 年
class SettelBillMonth20203 extends SettelBillMonth
{
    public function settel($bill_month_item)
    {
        $fee_price = json_decode($bill_month_item['section_source']['fee_price'], true);

        //获取用量
        $section_number = $this->getSectionNumber($bill_month_item);

        //是否以到达当前计费周期到达的阶梯段位
        $is_arrive = false;

        foreach ($fee_price as $fee_price_item) {
            $min   = $fee_price_item[0];
            $max   = $fee_price_item[1];
            $price = $fee_price_item[2];


        }


        halt($section_number, $bill_month_item);


        //$section_number = $bill_month_item['section_number']['fee_number'];
        //halt($fee_price, $bill_month_item);

//        //初始化一个存放结果的数据
//        $result = [];
//        foreach ($fee_price as $fee_price_item) {
//            $min   = $fee_price_item[0];
//            $max   = $fee_price_item[1];
//            $price = $fee_price_item[2];
//            $temp  = [
//                'name'   => $min . ' - ' . $max,
//                'price'  => $price,
//                'number' => 0,
//                'money'  => 0.00
//            ];
//            if ($section_number >= $min && $section_number <= $max) {
//                //正好位于当前区间中（不是最后一个区间）
//                $temp['number'] = bcadd(bcsub($section_number, $min), 1);
//            } elseif ($section_number >= $min && $max == -1) {
//                //当前区间是最后一个区间，并且在该区间中
//                $temp['number'] = bcadd(bcsub($section_number, $min), 1);
//            } elseif ($section_number > $max && $max != -1) {
//                //位于当前区间后面的区间
//                $temp['number'] = bcadd(bcsub($max, $min), 1);
//            }
//            $temp['money'] = bcmul($temp['number'], $temp['price'], 6);
//            $result[$min]  = $temp;
//        }
//
//        //校验结果
//        $money = round(array_sum(array_column($result, 'money')), 2);
//        if ($money != round($bill_month_item['money'], 2)) {
//            throw new \Exception('20202校验结果失败');
//        }
//        return $result;
    }

    /**
     * 获取计费周期内、之前的调用量
     *
     * @access private
     *
     * @param $bill_month_item array
     *
     * @return array
     **/
    private function getSectionNumber($bill_month_item)
    {
        //计费开始时间
        $fee_config_start_date = $bill_month_item['section_source']['start_date'];
        //账单月开始时间戳
        $this_month_first_day  = strtotime($bill_month_item['month'] . '01 midnight');
        //账单月截止时间戳
        $this_month_last_day = strtotime($bill_month_item['month'] . '01 +1 month -1 day midnight');

        //获取当前周期开始时间，并使用此周期开始时间进行分割本月的调用量
        $period_start_date = $fee_config_start_date;
//        while (true) {
//
//        }

        halt($bill_month_item);


        //$this_month_end_datetime = strtotime($bill_month_item['month'] . '01 +1 month -1 day midnight');

        halt(date('Ymd', $this_month_last_day));

        //因为是按年为周期、获取当前周期的开始时间
        $checked = false;
        //while (strtotime($fee_config_start_date) > )


        //$this_month_first_date =  . '01';

        //判断是否超过了一年 20190401-20180401 大于10000则是超过了一年
        if ($this_month_first_date - $fee_config_start_date > 10000) {

        }

        halt($this_month_first_date, $fee_config_start_date);


        //获取计费周期内的调用量
        $params             = $this->getParams($bill_month_item);
        $section_number_end = $this->getBillStat($bill_month_item['product_id'])
            ->billStat($params)['fee_number'];

        //获取计费周期之前的调用量
        if ($fee_config_start_date_month == $this_month) {
            $section_number_before = 0;
        } else {
            $fee_config_start_datetime = strtotime($bill_month_item['section_source']['start_date']);


            $params['section_begin'] = date('Ymd', strtotime($bill_month_item['section_source']['start_date']));


            $params['section_end'] = date('Ymd', strtotime($this_month . '01') - 1);
            $section_number_before = $this->getBillStat($bill_month_item['product_id'])
                ->billStat($params)['fee_number'];
        }
        return compact('section_number_before', 'section_number_end');
    }

}