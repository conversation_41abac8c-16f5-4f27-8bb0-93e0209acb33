<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/24 0024
 * Time: 15:48
 */

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;

//按周期 && 包日 && 无区分运营商配置项 && 无周期配置
class SettelBillMonth11000 extends SettelBillMonth
{
    public function settel($bill_month_item)
    {
        $price = $bill_month_item['section_source']['fee_price'] . '/日';
        $name  = $this->getDateTitle($bill_month_item);
        //获取量
        $params = $this->getParams($bill_month_item);
        $number = $this->getBillStat($bill_month_item['product_id'])
            ->billStat($params)['fee_number'];


        //计算价格
        $money = bcmul(
            bcadd(bcsub($bill_month_item['section_end'], $bill_month_item['section_begin'], 0), 1),
            $bill_month_item['section_source']['fee_price'],
            6
        );

        //校验结果
        if (round($money, 2) != round($bill_month_item['money'], 2)) {
            throw new \Exception('11000校验结果失败');
        }

        return compact('name', 'price', 'number', 'money');
    }
}