<?php

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;


//按用量 && 到达阶梯 && 区分运营商 && 周期为 年
class SettelBillMonth20323 extends SettelBillMonth
{
    private $operator = [
        '移动' => [
            'fee_config_label' => 1,
            'bill_month_label' => 'yd'
        ],
        '联通' => [
            'fee_config_label' => 2,
            'bill_month_label' => 'lt'
        ],
        '电信' => [
            'fee_config_label' => 3,
            'bill_month_label' => 'dx'
        ]
    ];

    public function settel($bill_month_item)
    {
        //价格
        $fee_price = json_decode($bill_month_item['section_source']['fee_price'], true);


        //举例 当前计算的计费周期为20190401-20190403    计费配置中的开始时间为20190309
        //当前账单周期之前的统计量 适用上面的例子：20190309-02190331
        $fee_number_before = $bill_month_item['section_number']['fee_number_before']['fee_number'];

        //包含当前账单周期的统计量
        $fee_number_end = $bill_month_item['section_number']['fee_number_end']['fee_number'];

        $result = [];
        //计算移动、电信、联通的详细月账单
        foreach ($this->operator as $name => $label) {
            $fee_config_label = $label['fee_config_label'];
            $bill_month_label = $label['bill_month_label'];
            $temp             = $this->getItemMoney(
                array_column($fee_price, $fee_config_label, 0),
                $fee_number_before[$bill_month_label],
                $fee_number_end[$bill_month_label]
            );
            $temp['name']     = $name;
            $result[]         = $temp;
        }

        //校验结果
        $money = round(array_sum(array_column($result, 'money')), 2);
        if ($money!=$bill_month_item['money']) {
            throw new \Exception('20323校验结果失败');
        }

        return $result;
    }

    /**
     * 计算到达阶梯的信息
     *
     * @access private
     *
     * @param $fee_price     array 到达阶梯的计费价格{max => price]
     * @param $before_number integer 计费周期开始之前的计费用量
     * @param $end_number    integer 计费周期截止的计费用量
     *
     * @return array
     **/
    private function getItemMoney($fee_price, $before_number, $end_number)
    {
        $before_money = 0;
        $end_money    = 0;
        foreach ($fee_price as $max => $price) {
            if ($before_number > $max) {
                $before_money = bcmul($before_number, $price, 6);
            }
            if ($end_number > $max) {
                $end_money = bcmul($end_number, $price, 6);
            }
        }

        $money = bcsub($end_money, $before_money, 6);
        $number = bcsub($end_number, $before_number, 0);
        return compact('money', 'number');
    }
}