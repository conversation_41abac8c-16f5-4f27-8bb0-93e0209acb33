<?php

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;

//按用量 && 固定单价 && 无区分运营商配置项 && 无周期配置
class SettelBillMonth20100 extends SettelBillMonth
{
    public function settel($bill_month_item)
    {
        $price  = $bill_month_item['section_source']['fee_price'];
        $name   = $this->getDateTitle($bill_month_item);
        $number = $this->getBillStat($bill_month_item['product_id'])
            ->billStat($this->getParams($bill_month_item))['fee_number'];
        $money  = bcmul($price, $number, 6);

        if (round($money, 2) != round($bill_month_item['money'], 2)) {
            throw new \Exception('20100校验结果失败');
        }
        return compact('name', 'price', 'number', 'money');
    }
}