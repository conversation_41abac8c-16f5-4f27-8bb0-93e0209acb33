<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/24 0024
 * Time: 15:48
 */

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;

//按周期 && 包月 && 无区分运营商配置项 && 无周期配置
class SettelBillMonth12000 extends SettelBillMonth
{
    public function settel($bill_month_item)
    {
        $price = $bill_month_item['section_source']['fee_price'];
        $name  = $this->getDateTitle($bill_month_item);
        //获取量
        $params = $this->getParams($bill_month_item);
        $number = $this->getBillStat($bill_month_item['product_id'])
            ->billStat($params)['fee_number'];

        //计算每日的价格
        $days       = $this->getMonthDays($bill_month_item['month']);
        $price_days = bcdiv($price, $days, 6);

        //校验金额
        $money = bcmul(
            $price_days,
            bcadd(bcsub($bill_month_item['section_end'], $bill_month_item['section_begin'], 0), 1, 0),
            6
        );
        if (round($money, 2) != round($bill_month_item['money'], 2)) {
            throw new \Exception('12000校验结果失败');
        }

        $price .= '/月';

        return compact('name', 'price', 'number', 'money');
    }

    /**
     * 获取账单月的天数
     *
     * @access private
     *
     * @param $month string 账单月
     *
     * @return integer
     **/
    private function getMonthDays($month)
    {
        $start_time = strtotime($month . '01');
        $end_time   = strtotime(date('Ym', bcadd($start_time, 2764800)) . '01');
        return bcdiv(bcsub($end_time, $start_time), 86400, 0);
    }
}