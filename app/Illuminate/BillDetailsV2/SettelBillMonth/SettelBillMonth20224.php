<?php

namespace App\Illuminate\BillDetailsV2\SettelBillMonth;

use App\Illuminate\BillDetailsV2\SettelBillMonth;

//按用量 && 累进阶梯 && 区分运营商 && 周期为 无周期
class SettelBillMonth20224 extends SettelBillMonth
{
    private $operator = [
        '移动' => [
            'fee_config_label' => 2,
            'bill_month_label' => 'yd'
        ],
        '联通' => [
            'fee_config_label' => 3,
            'bill_month_label' => 'lt'
        ],
        '电信' => [
            'fee_config_label' => 4,
            'bill_month_label' => 'dx'
        ]
    ];

    public function settel($bill_month_item)
    {
        //价格
        $fee_price = json_decode($bill_month_item['section_source']['fee_price'], true);
        //初始化结果数据
        $result = [];

        halt('未开发完成');
        foreach ($this->operator as $name => $label) {
            $fee_config_label = $label['fee_config_label'];
            $bill_month_label = $label['bill_month_label'];

            $price      = array_map(
                function ($item) use ($fee_config_label) {
                    $min = $item[0];
                    $max = $item[1];
                    $price = $item[$fee_config_label];
                    return compact('min', 'max', 'price');
                },
                $fee_price
            );
            $fee_number = array_sum(
                array_column(array_column($bill_month_item['section_number'], 'fee_number'), $bill_month_label)
            );
            $temp       = $this->getItemMoney($fee_number, $price);
            halt($temp);


            //用量
            $fee_number = array_sum(
                array_column(
                    array_column($bill_month_item['section_number'], 'fee_number'),
                    $bill_month_label
                )
            );
            //计算账单详细信息

        }

        //halt($bill_month_item);
    }

    /**
     * 计算某个运营商的账单详细
     *
     * @access private
     *
     * @param $fee_number integer 计费用量
     * @param $price      array 累进阶梯计费 [ [min=> max=> price=> ] [min=> max=> price=> ]]
     *
     * @return array
     **/
    private function getItemMoney($fee_number, $price)
    {
        $result = [];
        array_walk(
            $price,
            function ($price_item) use (&$result, $fee_number) {
                $min = $price_item['min'];
                $max = $price_item['max'];
                $price = $price_item['price'];


            }
        );
    }
}