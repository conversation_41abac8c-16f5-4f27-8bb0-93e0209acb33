<?php

namespace App\Illuminate\BillDetailsV2;

class BillDetailV2Manager implements Factory
{
    private $app;

    /**
     * Create a new Cache manager instance.
     *
     * @param  $app
     * @return void
     */
    public function __construct($app)
    {
        $this->app = $app;
    }

    /**
     * 获取驱动
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    public function driver(string $driver_name)
    {
        return $this->resolve($driver_name);
    }

    /**
     * 解析
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    private function resolve(string $driver_name)
    {
        $driver_method = 'create' . ucfirst($driver_name) . 'ModuleDriver';

        if (method_exists($this, $driver_method)) {
            return $this->{$driver_method}();
        } else {
            throw new \Exception('抱歉，账单详情V2服务目前不支持' . ucfirst($driver_name) . 'Driver类型的账单模块');
        }
    }

    /**
     * @return BillDetailV2CommonModuleDriver
     */
    private function createBillDetailV2CommonModuleDriver()
    {
        return new BillDetailV2CommonModuleDriver();
    }

    private function createBillDetailV2ShortcutModuleDriver()
    {
        return new BillDetailV2ShortcutModuleDriver();
    }

    private function createBillDetailV2GoldenModuleDriver()
    {
        return new BillDetailV2GoldenModuleDriver();
    }
}
