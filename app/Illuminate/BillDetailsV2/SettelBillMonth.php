<?php

namespace App\Illuminate\BillDetailsV2;

use App\Illuminate\BillStatistics\DriverFactory;
use App\Models\Account;


/**
 * 记录累进阶梯与到达阶梯的区别
 *      累进阶梯：   到达阶梯节点，接下来所产生的数据量将会按照新的价格计费
 *      到达阶梯：   到达阶梯节点，所有的数据量将会按照新的价格计费
 **/
abstract class SettelBillMonth
{
    /**
     * 账单数据的整理
     *
     * @access public
     *
     * @param $bill_month_item array bill_months表中的一条账单数据
     *
     * @return array
     **/
    abstract public function settel($bill_month_item);

    /**
     * 获取查询量的接口
     *
     * @access protected
     *
     * @param $product_id string 产品ID
     * @param $params     array 查询的参数
     *
     * @return DriverFactory
     **/
    protected function getBillStat($product_id)
    {
        return app('bill.statistic')->driver('BillStat' . $product_id);
    }

    /**
     * 获取用量查询的条件
     *
     * @access protected
     *
     * @param $bill_month_item array
     *
     * @return array
     **/
    protected function getParams($bill_month_item)
    {
        $section_begin = $bill_month_item['section_begin'];
        $section_end   = $bill_month_item['section_end'];

        $account_id     = $bill_month_item['account_id'];
        $apikey         = Account::where('account_id', $account_id)
            ->value('apikey');
        $uuid           = $bill_month_item['uuid'];
        $fee_basis      = $bill_month_item['section_source']['fee_basis'];
        $fee_price_rule = $bill_month_item['section_source']['fee_price_rule'];
        return compact('section_begin', 'section_end', 'apikey', 'uuid', 'fee_basis', 'fee_price_rule');
    }

    /**
     * 获取期间名称
     *
     * @access protected
     *
     * @param $bill_month_item array
     *
     * @return string
     **/
    protected function getDateTitle($bill_month_item)
    {
        return $bill_month_item['section_begin'] . ' -- ' . $bill_month_item['section_end'];
    }
}