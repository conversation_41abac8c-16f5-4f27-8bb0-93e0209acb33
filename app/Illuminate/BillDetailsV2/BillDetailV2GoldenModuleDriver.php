<?php


namespace App\Illuminate\BillDetailsV2;


use App\Models\Account;
use App\TraitUpgrade\CurlTrait;

class BillDetailV2GoldenModuleDriver
{
    use CurlTrait;
    private $alo;
    private $short_list = [];

    /**
     * 不同算法之间，催收快捷版的整合
     *
     * @param array  $product_consumption
     * @param string $alo
     *
     * @return array
     */
    public function _aggregateInAccount(array $product_consumption, string $alo): array
    {
        $this->alo = $alo;

        //halt(parseMongoBillMonthData($product_consumption));

        $this->init615Number($product_consumption);

        switch ($alo) {
            case '_1':
                // 通用 && 按时间
                return $this->_computedShortcutWhenCommonDateAndNum($product_consumption);
                break;
            case '_2_1':
                // 如果通用的按用量 && 固定价格
                return $this->_computedShortcutWhenCommonDateAndNum($product_consumption);
                break;
            case "_2_2":
                // 通用的按用量 && 累进阶梯
                return $this->_computedShortcutWhenCommonProgression($product_consumption);
                break;
            case "_2_3":
                // 通用的按用量 && 到达阶梯
                return $this->_computedShortcutWhenCommonReach($product_consumption);
                break;
            case "_1_2":
                // 区分运营商按时间
                return $this->_computedShortcutWhenOperatorDate($product_consumption);
                break;
            case "_2_1_2":
                //  区分运营商按用量 && 固定价格
                return $this->_computedShortcutWhenOperatorFixed($product_consumption);
                break;
            case "_2_2_2":
                //  区分运营商按用量 && 累进阶梯
                return $this->_computedShortcutWhenOperatorProgressionAndReach($product_consumption);
                break;
            case '_2_3_2':
                // 区分运营商按用量 && 到达阶梯
                return $this->_computedShortcutWhenOperatorProgressionAndReach($product_consumption);
                break;
        }
    }

    /**
     * 区分运营商 按累进
     *
     * @param array $product_consumption
     *
     * @return array
     */
    private function _computedShortcutWhenOperatorProgressionAndReach(array $product_consumption): array
    {

        $info_begin = $this->_getBaseInfo();
        return array_reduce($product_consumption, function ($carry, $item_bill) {
            $carry["uuid"][] = $item_bill->uuid;
            $carry["money"]  += $item_bill->money;

            // 如果是初始化的时候
            if (!$carry['details']) {
                $carry['details']                 = $item_bill->details;
                $carry['details']['product_name'] = "金盾";
                $carry['details']['product_id']   = 615;
                return $carry;
            }

            $carry['details']['money'] += $item_bill["details"]["money"];

            // 调整各个区间的分布
            $carry["details"]["list_distribute"] = $this->_computedDistributeWhenOperatorProgressionForShortcutAndReach($carry['details'],
                $item_bill->details);
            return $carry;
        }, $info_begin);
    }

    /**
     * 快捷版运营商 固定单价
     *
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     *
     * @return array
     */
    private function _computedDistributeWhenOperatorProgressionForShortcutAndReach(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 格式化数据
        $list_distribute_new = array_column($list_distribute_new, null, 'operator');
        $list_distribute_old = array_column($list_distribute_old, null, 'operator');
        $list_operators      = array_unique(array_merge(array_keys($list_distribute_new),
                array_keys($list_distribute_old)));

        // 累加
        return array_map(function ($operator) use ($list_distribute_old, $list_distribute_new) {
            list($number_old, $number_new, $money_old, $money_new) = [
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['number'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['number'] : 0,
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['money'] : 0,
            ];
            return [
                'number'   => $number_old + $number_new,
                'money'    => $money_old + $money_new,
                'operator' => $operator,
            ];
        }, $list_operators);
    }

    /**
     * 区分运营商按时间
     *
     * @param array $product_consumption
     *
     * @return array
     */
    private function _computedShortcutWhenOperatorDate(array $product_consumption): array
    {
        $info_begin         = $this->_getBaseInfo();
        $info_begin['list'] = $product_consumption;
        return array_reduce($product_consumption, function ($carry, $item_bill) {
            $carry["uuid"][] = $item_bill->uuid;
            $carry["money"]  += $item_bill->money;

            // 如果是初始化的时候
            if (!$carry['details']) {
                $carry['details']                 = $item_bill->details;
                $carry['details']['product_id']   = 615;
                $carry['details']['product_name'] = '金盾';

                $operator_hash                       = [
                    '移动' => 'yd',
                    '联通' => 'lt',
                    '电信' => 'dx'
                ];
                $carry['details']['list_distribute'] = array_map(function ($item) use ($operator_hash) {
                    $operator                = $item['operator'];
                    //$item['shortcut_number'] = $this->short_list[$operator_hash[$operator] . '_success'];
                    return $item;
                }, $carry['details']['list_distribute']);
                return $carry;
            }

            // 调整各个区间的分布
            $carry["details"]["list_distribute"] = $this->_computedDistributeWhenOperatorDataForShortcut($carry['details'],
                $item_bill->details);
            return $carry;
        }, $info_begin);
    }

    /**
     * 快捷版通用按累进 计算分布
     *
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     *
     * @return array
     */
    private function _computedDistributeWhenOperatorDataForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 格式化数据
        $list_distribute_new = array_column($list_distribute_new, null, 'operator');
        $list_distribute_old = array_column($list_distribute_old, null, 'operator');
        $list_operators      = array_unique(array_merge(array_keys($list_distribute_new),
                array_keys($list_distribute_old)));

        // 累加
        return array_map(function ($operator) use ($list_distribute_old, $list_distribute_new) {
            list($number_old, $number_new, $money_old, $money_new) = [
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['number'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['number'] : 0,
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['money'] : 0,
            ];
            return [
                'number'   => $number_old + $number_new,
                'money'    => $money_old + $money_new,
                'operator' => $operator
            ];
        }, $list_operators);
    }

    /**
     * 通用的按用量 && 到达阶梯计算快捷版的消费明细
     *
     * @param array $product_consumption
     *
     * @return array
     */
    private function _computedShortcutWhenCommonReach(array $product_consumption): array
    {
        $info_begin = $this->_getBaseInfo();
        return array_reduce($product_consumption, function ($carry, $item_bill) {
            $carry["uuid"][] = $item_bill->uuid;
            $carry["money"]  += $item_bill->money;

            // 如果是初始化的时候
            if (!$carry['details']) {
                $carry['details']                 = $item_bill->details;
                $carry['details']['product_name'] = "金盾";
                $carry['details']['product_id']   = 615;
                return $carry;
            }

            $carry['details']['money'] += $item_bill["details"]["money"];

            // 调整各个区间的分布
            $carry["details"]["list_distribute"] = $this->_computedDistributeWhenCommonReachForShortcut($carry['details'],
                $item_bill->details);
            return $carry;
        }, $info_begin);
    }

    /**
     * 快捷版通用按累进 计算分布
     *
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     *
     * @return array
     */
    private function _computedDistributeWhenCommonReachForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 获取key的聚合
        $section_keys_old = array_keys($list_distribute_old);
        $section_keys_new = array_keys($list_distribute_new);
        $section_keys     = array_unique(array_merge($section_keys_old, $section_keys_new));


        return array_reduce($section_keys,
            function ($carry, $section_key) use ($list_distribute_old, $list_distribute_new) {
                list ($money_old, $money_new, $section_number_old, $section_number_new, $price) = [
                    isset($list_distribute_old[$section_key]) ? $list_distribute_old[$section_key]['money'] : 0,
                    isset($list_distribute_new[$section_key]) ? $list_distribute_new[$section_key]['money'] : 0,
                    isset($list_distribute_old[$section_key]) ? $list_distribute_old[$section_key]['section_number'] : 0,
                    isset($list_distribute_new[$section_key]) ? $list_distribute_new[$section_key]['section_number'] : 0,
                    isset($list_distribute_old[$section_key]['price']) ? $list_distribute_old[$section_key]['price'] : $list_distribute_new[$section_key]['price']
                ];

                $carry[$section_key] = [
                    'money'          => $money_new + $money_old,
                    'section_number' => $section_number_old + $section_number_new,
                    'price'          => $price,
                    'section_key'    => $section_key
                ];
                return $carry;
            }, []);
    }

    /**
     * 通用的按用量 && 累进阶梯计算快捷版的消费明细
     *
     * @param array $product_consumption
     *
     * @return array
     */
    private function _computedShortcutWhenCommonProgression(array $product_consumption): array
    {
        $info_begin         = $this->_getBaseInfo();
        $info_begin['list'] = $product_consumption;
        return array_reduce($product_consumption, function ($carry, $item_bill) {
            $carry["uuid"][] = $item_bill->uuid;
            $carry["money"]  += $item_bill->money;

            // 如果是初始化的时候
            if (!$carry['details']) {
                $carry['details']                 = $item_bill->details;
                $carry['details']['product_name'] = "金盾";
                $carry['details']['product_id']   = 615;
                return $carry;
            }

            $carry['details']['money'] += $item_bill["details"]["money"];
            // 调整各个区间的分布
            $carry["details"]["list_distribute"] = $this->_computedDistributeWhenCommonProgressionForShortcut($carry['details'],
                $item_bill->details);
            return $carry;
        }, $info_begin);
    }

    /**
     * 快捷版通用按累进 计算分布
     *
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     *
     * @return array
     */
    private function _computedDistributeWhenCommonProgressionForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($distribute_old, $distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? [],
        ];

        // 累计
        $distribute_base    = count($distribute_old) > count($distribute_new) ? $distribute_old : $distribute_new;
        $distribute_another = count($distribute_old) > count($distribute_new) ? $distribute_new : $distribute_old;

        return array_map(function ($item) use ($distribute_another) {
            $section_key            = $item['section_key'] ?? 'none';
            $item['money']          += isset($distribute_another[$section_key]) ? $distribute_another[$section_key]['money'] : 0;
            $item['section_number'] += isset($distribute_another[$section_key]) ? $distribute_another[$section_key]['section_number'] : 0;
            return $item;
        }, $distribute_base);
    }

    /**
     * 通用按时间 && 固定单价 算法计算快捷版数据
     *
     * @param array $product_consumption
     *
     * @return array
     */
    private function _computedShortcutWhenCommonDateAndNum(array $product_consumption): array
    {
        $info_begin = $this->_getBaseInfo();

        //获取210调用量
        $details = array_column(array_map(function ($item) {
            return $item->toArray();
        }, $product_consumption), 'details');

        $money                 = array_sum(array_column($details, 'money'));
        $product_name          = '金盾';
        $fee_number            = array_sum(array_column($details, 'fee_number'));
        $shortcut_number       = $this->short_list['success'];
        $price                 = $details[0]['price'];
        $section_begin         = $details[0]['section_begin'];
        $section_end           = $details[0]['section_end'];
        $product_id            = 615;
        $info_begin['details'] = compact('money', 'product_name', 'fee_number', 'shortcut_number', 'price',
            'section_begin', 'section_end', 'product_id');
        $info_begin['money']   = $money;
        $info_begin['list']    = $product_consumption;
        return $info_begin;
    }

    /**
     * 启动数据
     * @return array
     */
    private function _getBaseInfo(): array
    {
        return [
            "product_id"   => "615",
            "money"        => 0,
            "product_name" => "金盾",
            "uuid"         => [],
            "details"      => [], // 用来渲染页面的数据
            "product_alo"  => $this->alo
        ];
    }

    /**
     * 区分运营商按固定价格
     *
     * @param array $product_consumption
     *
     * @return array
     */
    private function _computedShortcutWhenOperatorFixed(array $product_consumption): array
    {

        $info_begin         = $this->_getBaseInfo();
        $info_begin['list'] = $product_consumption;

        return array_reduce($product_consumption, function ($carry, $item_bill) {
            $carry["uuid"][] = $item_bill->uuid;
            $carry["money"]  += $item_bill->money;

            // 如果是初始化的时候
            if (!$carry['details']) {
                $carry['details']                 = $item_bill->details;
                $carry['details']['product_name'] = "金盾";
                $carry['details']['product_id']   = 615;

                $operator_hash                       = [
                    '移动' => 'yd',
                    '联通' => 'lt',
                    '电信' => 'dx'
                ];
                $carry['details']['list_distribute'] = array_map(function ($item) use ($operator_hash) {
                    $operator                = $item['operator'];
                    //$item['shortcut_number'] = $this->short_list[$operator_hash[$operator] . '_success'];
                    return $item;
                }, $carry['details']['list_distribute']);
                return $carry;
            }

            $carry['details']['money'] += $item_bill["details"]["money"];


            // 调整各个区间的分布
            $carry["details"]["list_distribute"] = $this->_computedDistributeWhenOperatorFixedForShortcut($carry['details'],
                $item_bill->details);
            return $carry;
        }, $info_begin);
    }

    /**
     * 快捷版运营商 固定单价
     *
     * @param array $product_consumption_month_old
     * @param array $product_consumption_month_new
     *
     * @return array
     */
    private function _computedDistributeWhenOperatorFixedForShortcut(array $product_consumption_month_old, array $product_consumption_month_new): array
    {
        list($list_distribute_old, $list_distribute_new) = [
            $product_consumption_month_old['list_distribute'] ?? [],
            $product_consumption_month_new['list_distribute'] ?? []
        ];

        // 如果都是空的话
        if (!$list_distribute_new && !$list_distribute_old) {
            return [];
        }

        // 格式化数据
        $list_distribute_new = array_column($list_distribute_new, null, 'operator');
        $list_distribute_old = array_column($list_distribute_old, null, 'operator');
        $list_operators      = array_unique(array_merge(array_keys($list_distribute_new),
                array_keys($list_distribute_old)));

        // 累加
        return array_map(function ($operator) use ($list_distribute_old, $list_distribute_new) {
            list($number_old, $number_new, $money_old, $money_new, $price) = [
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['number'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['number'] : 0,
                isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['money'] : 0,
                isset($list_distribute_new[$operator]) ? $list_distribute_new[$operator]['price'] : (isset($list_distribute_old[$operator]) ? $list_distribute_old[$operator]['price'] : 0),
            ];
            return [
                'number'   => $number_old + $number_new,
                'money'    => $money_old + $money_new,
                'operator' => $operator,
                'price'    => $price
            ];
        }, $list_operators);
    }

    /**
     * 获取210产品调用量
     *
     * @access public
     *
     * @param $product_consumption array
     *
     * @return void
     **/
    public function init615Number($product_consumption)
    {
        if (empty($product_consumption)) {
            return;
        }
        $temp_comumption = array_shift($product_consumption);
        if (empty($temp_comumption)) {
            halt($temp_comumption);
        }

        //$customer_id = $temp_comumption->customer_id;
        //$start_time  = $temp_comumption->details['section_begin'];
        //$end_time    = $temp_comumption->details['section_end'];
        //$key         = '973cfad2f2b36e7c8c0a33b959efb2bb';
        //$apikey      = array_filter(Account::where('customer_id', $customer_id)
        //    ->pluck('apikey')
        //    ->toArray());

        //$product_id = 615;

        //$data = $this->post(env('LIST_STAT_COMMON_API'),
        //    compact('apikey', 'start_time', 'end_time', 'product_id', 'key'));
        //$success = $data['list_stat']['goldenShieldNew615']['total']['succ'];

        $success = $temp_comumption->section_invoked_number;
        //halt($data);
//        $total      = $data['list_stat']['cuishouShort210']['total']['total'];
//        $success    = $data['list_stat']['cuishouShort210']['total']['success'];
//        $yd_success = $data['list_stat']['cuishouShort210']['total']['yidong_success'] + $data['list_stat']['cuishouShort210']['total']['yidong_cache'];
//        $lt_success = $data['list_stat']['cuishouShort210']['total']['liantong_success'] + $data['list_stat']['cuishouShort210']['total']['liantong_cache'];
//        $dx_success = $data['list_stat']['cuishouShort210']['total']['dianxin_success'] + $data['list_stat']['cuishouShort210']['total']['dianxin_cache'];
//        $yd_total   = $yd_success + $data['list_stat']['cuishouShort210']['total']['yidong_fail'];
//        $lt_total   = $lt_success + $data['list_stat']['cuishouShort210']['total']['liantong_fail'];
//        $dx_total   = $lt_success + $data['list_stat']['cuishouShort210']['total']['dianxin_fail'];

        $this->short_list = compact('success');
    }
}