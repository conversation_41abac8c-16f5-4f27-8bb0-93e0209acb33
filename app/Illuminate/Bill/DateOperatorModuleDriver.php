<?php

namespace App\Illuminate\Bill;

class DateOperatorModuleDriver implements DriverFactory
{
    /**
     * 校验参数
     * @param array $params
     * @throws \Exception
     */
    public function validateParams(array $params)
    {
        // 基础校验
        $this->validateBaseParams($params);

        // 校验价格
        $this->validatePrice($params);
    }

    /**
     * 基础校验
     * @param array $params
     * @throws \Exception
     */
    private function validateBaseParams(array $params)
    {
        $section_begin = $params['section_begin'] ?? '';
        if (!$section_begin) {
            throw new \Exception('账单服务(运营商按时间)提示: 请输入section_begin参数');
        }

        $section_end = $params['section_end'] ?? '';
        if (!$section_end) {
            throw new \Exception('账单服务(运营商按时间)提示: 请输入section_end参数');
        }

        $fee_time_rule = $params['fee_time_rule'] ?? '';
        if ($fee_time_rule === '') {
            throw new \Exception('账单服务(运营商按时间)提示: 请输入fee_time_rule参数');
        }

        if (!in_array($fee_time_rule, ['day', 'month', 'year'])) {
            throw new \Exception('账单服务(运营商按时间)提示: 请输入合法的fee_time_rule参数 msg: fee_time_rule=' . $fee_time_rule);
        }

        $month = $params['month'];
        if (!$month) {
            throw new \Exception('账单服务(运营商按时间)提示: 请输入month参数');
        }
    }

    /**
     * 校验价格
     * @param array $params
     * @throws \Exception
     */
    private function validatePrice(array $params)
    {
        $fee_price = $params['fee_price'] ?? [];
        if (!$fee_price || !is_array($fee_price)) {
            $msg = '账单服务(运营商按时间)提示: 请传入合法的fee_price msg:' . json_encode(compact('fee_price'));
            throw new \Exception($msg);
        }

        // 三种运营商价格校验
        list($price_yd, $price_lt, $price_dx) = [$fee_price['price_yd'] ?? '', $fee_price['price_lt'] ?? '', $fee_price['price_dx'] ?? ''];
        if ($price_dx === '' || $price_lt === '' || $price_yd === '' || !is_numeric($price_yd) || !is_numeric($price_lt) || !is_numeric($price_dx)) {
            $msg = '账单服务(运营商按时间)提示: 请传入合法的fee_price msg:' . json_encode(compact('list_operator_prices'));
            throw new \Exception($msg);
        }
    }

    /**
     * 计费
     * @param array $params
     * @return float
     * @throws \Exception
     */
    public function genMoney(array $params): float
    {
        // 校验参数
        $this->validateParams($params);

        // 计费
        return $this->genMoneyDo($params);
    }

    /**
     * 计费
     * @param array $params
     * @return float
     */
    private function genMoneyDo(array $params): float
    {
        // 参数赋值
        list($price_yd, $price_lt, $price_dx,
            $fee_time_rule, $month_days, $day_diff) = $this->getListParams($params);

        // 因为它们是按照日期计费的,所以本质是个累加的关系
        $fee_price = ($price_lt + $price_dx + $price_yd);

        // 计费
        switch ($fee_time_rule) {
            case "day":
                return floatval($day_diff * $fee_price);
                break;
            case "month":
                return floatval($fee_price / $month_days * $day_diff);
                break;
            case "year":
                return floatval($fee_price / 365 * $day_diff);
                break;
        }
    }

    /**
     * 参数列表
     * @param array $params
     * @return array
     */
    private function getListParams(array $params): array
    {
        list($section_begin, $section_end) = [$params['section_begin'], $params['section_end']];
        $day_diff = (strtotime($section_end) - strtotime($section_begin)) / 86400 + 1;
        $month_days = date('d', strtotime('last day of this month', strtotime($params['month'] . '01')));

        // 三种运营商价格
        $fee_price = $params['fee_price'];
        list($price_yd, $price_lt, $price_dx) = [$fee_price['price_yd'], $fee_price['price_lt'], $fee_price['price_dx']];
        return [$price_yd, $price_lt, $price_dx, $params['fee_time_rule'], $month_days, $day_diff];
    }
}