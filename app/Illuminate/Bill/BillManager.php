<?php

namespace App\Illuminate\Bill;

class BillManager implements BillFactory
{
    private $app;

    /**
     * Create a new Cache manager instance.
     *
     * @param  $app
     * @return void
     */
    public function __construct($app)
    {
        $this->app = $app;
    }

    /**
     * 获取驱动
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    public function driver(string $driver_name)
    {
        return $this->resolve($driver_name);
    }

    /**
     * 解析
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    private function resolve(string $driver_name)
    {
        $driver_method = 'create' . ucfirst($driver_name) . 'ModuleDriver';

        if (method_exists($this, $driver_method)) {
            return $this->{$driver_method}();
        } else {
            throw new \Exception('抱歉，账单服务目前不支持' . ucfirst($driver_name) . 'Driver类型的账单模块');
        }
    }

    /**
     * 通用的按时间
     * @return DateModuleDriver
     */
    private function createDateModuleDriver()
    {
        return new DateModuleDriver();
    }

    /**
     * 通用的按用量 && 固定价格
     * @return NumberFixedModuleDriver
     */
    private function createNumberFixedModuleDriver()
    {
        return new NumberFixedModuleDriver();
    }

    /**
     * 通用的累进阶梯
     * @return NumberProgressionModuleDriver
     */
    private function createNumberProgressionModuleDriver()
    {
        return new NumberProgressionModuleDriver();
    }

    /**
     * 通用的达到阶梯
     * @return NumberReachModuleDriver
     */
    private function createNumberReachModuleDriver()
    {
        return new NumberReachModuleDriver();
    }

    /**
     * 区分运营商按时间
     * @return DateOperatorModuleDriver
     */
    private function createDateOperatorModuleDriver()
    {
        return new DateOperatorModuleDriver();
    }

    /**
     * 区分运营商按用量 && 固定价格
     * @return NumberFixedOperatorModuleDriver
     */
    private function createNumberFixedOperatorModuleDriver()
    {
        return new NumberFixedOperatorModuleDriver();
    }

    /**
     * 区分运营商按用量 && 累进阶梯
     * @return NumberProgressionOperatorModuleDriver
     */
    private function createNumberProgressionOperatorModuleDriver()
    {
        return new NumberProgressionOperatorModuleDriver();
    }

    /**
     * 区分运营商按用量 && 到达阶梯
     * @return NumberReachOperatorModuleDriver
     */
    private function createNumberReachOperatorModuleDriver()
    {
        return new NumberReachOperatorModuleDriver();
    }
}
