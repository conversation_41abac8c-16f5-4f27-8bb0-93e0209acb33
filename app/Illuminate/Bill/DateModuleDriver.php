<?php

namespace App\Illuminate\Bill;

class DateModuleDriver implements DriverFactory
{
    /**
     * 计算消费金额
     * @param array $params
     * @return float
     * @throws \Exception
     */
    public function genMoney(array $params): float
    {
        // 校验参数
        $this->validateParams($params);

        // 计算消费金额
        return $this->genMoneyDo($params);
    }

    /**
     * 计算消费金额
     * @param array $params
     * @return float
     */
    private function genMoneyDo(array $params): float
    {
        // 参数赋值
        list($fee_price,
            $fee_time_rule, $month_days, $day_diff) = $this->getListParams($params);

        // 计费
        switch ($fee_time_rule) {
            case "day":
                return floatval($day_diff * $fee_price);
                break;
            case "month":
                return  floatval($fee_price/$month_days * $day_diff);
                break;
            case "year":
                return  floatval($fee_price/365 * $day_diff);
                break;
        }
    }

    /**
     * 参数列表
     * @param array $params
     * @return array
     */
    private function getListParams(array $params): array
    {
        list($section_begin, $section_end) = [$params['section_begin'], $params['section_end']];
        $day_diff = (strtotime($section_end) - strtotime($section_begin))/86400 +1;
        $month_days = date('d', strtotime('last day of this month', strtotime($params['month'] . '01')));

        return [$params['fee_price'], $params['fee_time_rule'], $month_days, $day_diff];
    }

    /**
     * 校验参数
     * @throws \Exception
     * @param array $params
     */
    public function validateParams(array $params)
    {
        $section_begin = $params['section_begin'] ?? '';
        if (!$section_begin) {
            throw new \Exception('请输入section_begin参数');
        }

        $section_end = $params['section_end'] ?? '';
        if (!$section_end) {
            throw new \Exception('请输入section_end参数');
        }

        $fee_price = $params['fee_price'] ?? '';
        if ($fee_price === '' || !is_numeric($fee_price)) {
            throw new \Exception('请输入合法的fee_price参数');
        }

        $fee_time_rule = $params['fee_time_rule'] ?? '';
        if ($fee_time_rule === '') {
            throw new \Exception('请输入fee_time_rule参数');
        }

        if (!in_array($fee_time_rule, ['day', 'month', 'year'])) {
            throw new \Exception('请输入合法的fee_time_rule参数 msg: fee_time_rule=' . $fee_time_rule);
        }

        $month = $params['month'];
        if (!$month) {
            throw new \Exception('请输入month参数');
        }
    }
}