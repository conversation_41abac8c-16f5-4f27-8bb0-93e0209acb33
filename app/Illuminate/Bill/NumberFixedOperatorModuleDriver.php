<?php

namespace App\Illuminate\Bill;

class NumberFixedOperatorModuleDriver implements DriverFactory
{
    /**
     * 校验参数
     * @param array $params
     * @throws \Exception
     */
    public function validateParams(array $params)
    {
        // 校验价格
        $this->validatePrice($params);

        // 校验数量
        $this->validateNumber($params);
    }

    /**
     * 校验数量
     * @param array $params
     * @throws \Exception
     */
    private function validateNumber(array $params)
    {
        $fee_number = $params['fee_number'] ?? [];
        if (!$fee_number || !is_array($fee_number)) {
            $msg = '账单服务(区分运营商按用量,固定价格) 提示: 请传入合法的fee_number msg:'
                . json_encode(compact('params'), JSON_UNESCAPED_UNICODE);
            throw new \Exception($msg);
        }

        list($number_yd, $number_lt, $number_dx) = [$fee_number['yd'] ?? '', $fee_number['lt'] ?? '', $fee_number['dx'] ?? ''];
        if ($number_dx === '' || !is_numeric($number_dx) || $number_lt === '' || !is_numeric($number_lt) || $number_yd === '' || !is_numeric($number_yd)) {
            $msg = '账单服务(区分运营商按用量,固定价格) 提示: 请传入合法的fee_number msg:'
                . json_encode(compact('params'), JSON_UNESCAPED_UNICODE);
            throw new \Exception($msg);
        }
    }

    /**
     * 校验价格
     * @param array $params
     * @throws \Exception
     */
    private function validatePrice(array $params)
    {
        $fee_price = $params['fee_price'] ?? [];
        if (!$fee_price || !is_array($fee_price)) {
            throw new \Exception('通用按用量固定单价： 请传入合法的fee_price');
        }

        list($price_yd, $price_lt, $price_dx) = [$fee_price['price_yd'] ?? '', $fee_price['price_lt'] ?? '', $fee_price['price_dx'] ?? ''];
        if ($price_yd === '' || !is_numeric($price_yd) || $price_lt === '' || !is_numeric($price_lt) || $price_dx === '' || !is_numeric($price_dx)) {
            $msg = '账单服务(区分运营商按用量,固定价格) 提示: 请输入合法的fee_price msg:'
                . json_encode(compact('params'), JSON_UNESCAPED_UNICODE);
            throw new \Exception($msg);
        }
    }

    /**
     * 校验参数
     * @param array $params
     * @return float
     * @throws \Exception
     */
    public function genMoney(array $params): float
    {
        // 校验参数
        $this->validateParams($params);

        // 计费
        return $this->genMoneyDo($params);
    }

    /**
     * 奇怪
     * @param array $params
     * @return float
     */
    private function genMoneyDo(array $params): float
    {
        // 价格 数量分散
        list($fee_number, $fee_price) = [$params['fee_number'], $params['fee_price']];
        list($number_yd, $number_lt, $number_dx) = [$fee_number['yd'], $fee_number['lt'], $fee_number['dx']];
        list($price_yd, $price_lt, $price_dx) = [$fee_price['price_yd'], $fee_price['price_lt'], $fee_price['price_dx']];
		
        $price_all_yd = $fee_price['price_all_yd']??0;
        $number_all_yd = $fee_number['all_yd']??0;
		
        return floatval($price_dx * $number_dx + $price_lt * $number_lt + $price_yd * $number_yd + $price_all_yd*$number_all_yd);
    }
}
