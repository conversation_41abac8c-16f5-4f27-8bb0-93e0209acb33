<?php

namespace App\Illuminate\Bill;

class NumberReachModuleDriver implements DriverFactory
{
    /**
     * 计费
     * @param array $params
     * @return float
     * @throws \Exception
     */
    public function genMoney(array $params): float
    {
        // 校验参数
        $this->validateParams($params);

        return $this->genMoneyDo($params);
    }

    /**
     * 计费
     * @param array $params
     * @return float
     */
    private function genMoneyDo(array $params): float
    {
        list($fee_number, $fee_price) = [$params['fee_number'], $params['fee_price']];
        $fee_price = array_column($fee_price, null, 'reach_standard');
        ksort($fee_price, SORT_NUMERIC);

        // 最终的价格
        $price = array_reduce($fee_price, function ($carry, $fee_rule_item) use ($fee_number) {
            list($reach_standard, $price) = [$fee_rule_item['reach_standard'], $fee_rule_item['price']];
            if ($fee_number >= $reach_standard) {
                $carry = $price;
            }
            return $carry;
        }, 0);

        return (float)$price * $fee_number;
    }

    /**
     * 校验参数
     * @param array $params
     * @throws \Exception
     */
    public function validateParams(array $params)
    {
        // 计费规则总结起来就是 新总价-历史月份总价格=当月的价格
        $this->validateNumber($params);

        // 单价规则校验
        $this->validatePrice($params);
    }

    /**
     * 校验数量
     * @param array $params
     * @throws \Exception
     */
    private function validateNumber(array $params)
    {
        $fee_number = $params['fee_number'] ?? '';
        if ($fee_number === '' || !is_numeric($fee_number)) {
            throw new \Exception('通用按用到达阶梯服务提示: 请输入合法的fee_number msg:' .
                json_encode(compact('params'), JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 校验价格
     * @param array $params
     * @throws \Exception
     */
    private function validatePrice(array $params)
    {
        $fee_price = $params['fee_price'] ?? [];
        $msg = '通用按用量到达阶梯服务提示：请输入合法的fee_price msg:' .
            json_encode(compact('params'), JSON_UNESCAPED_UNICODE);

        if (!is_array($fee_price) || !$fee_price) {
            throw new \Exception($msg);
        }

        // 是否包含 左右边界 && 区间价格
        array_walk($fee_price, function ($fee_rule_item) use ($msg) {
            list($reach_standard, $price) = [$fee_rule_item['reach_standard'] ?? '', $fee_rule_item['price'] ?? ''];

            // 如果不存在
            if ($reach_standard === '' || $price === '') {
                throw new \Exception($msg);
            }

            // 不是合法的数值类型
            if (!is_numeric($reach_standard) || !is_numeric($price)) {
                throw new \Exception($msg);
            }
        });
    }
}
