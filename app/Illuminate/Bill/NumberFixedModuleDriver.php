<?php

namespace App\Illuminate\Bill;

class NumberFixedModuleDriver implements DriverFactory
{
    /**
     * 校验参数
     * @throws \Exception
     * @param array $params
     */
    public function validateParams(array $params)
    {
        $fee_number = $params['fee_number'] ?? '';
        $fee_price = $params['fee_price'] ?? '';
        if ($fee_number === '' || !is_numeric($fee_number)) {
            throw new \Exception('通用按用量固定单价： 请传入合法的fee_number');
        }

        if ($fee_price === '' || !is_numeric($fee_price)) {
            throw new \Exception('通用按用量固定单价： 请传入合法的fee_price');
        }
    }

    /**
     * 计费费用
     * @param array $params
     * @return float
     * @throws \Exception
     */
    public function genMoney(array $params): float
    {
        // 校验参数
        $this->validateParams($params);

        // 计算费用
        return $this->genMoneyDo($params);
    }

    /**
     * 计算费用
     * @param array $params
     * @return float
     */
    private function genMoneyDo(array $params): float
    {
        list($fee_number, $fee_price) = [trim($params['fee_number']), trim($params['fee_price'])];

        return (float)$fee_number * $fee_price;
    }
}