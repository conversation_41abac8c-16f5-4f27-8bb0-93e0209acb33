<?php

namespace App\Illuminate\BillStatistics;

use GuzzleHttp;
use App\Models\MongoLog;

trait CuishouPrivateTrait
{
//    /** @var int 产品ID 需要覆盖 */
//    private $product_id;
//
//    /** @var string back-api调用接口返回的标记字段  需要覆盖 */
//    private $field_back_api;
//
//
//    /** @var array 允许的计费依据  需要覆盖 */
//    private $list_fee_basis;
//
//    /** @var array 计费依据和字段的映射关系  需要覆盖 */
//    private $list_fee_basis_mapping;
//
//    /** @var array 传递进来的参数 需要覆盖 */
//    private $params;


    /** @var string 异常提示前缀 */
    private $title_prefix = '(账单用量服务)';

    /**
     * 生成账单统计数据
     * @throws \Exception
     */
    private function genBillStat(): array
    {
        // 生成参数
        $params = $this->genParamsList();

        // 获取统计信息
        $list_stat = $this->getInvokedNumber($params);

        // 获取对应的计费字段
        return $this->getTheChooseField($list_stat);
    }

    /**
     * 获取调用量
     * @param array $params
     * @return array
     * @throws \Exception
     */
    private function getInvokedNumber(array $params): array
    {
        $api_url = env('LIST_STAT_PRIVATE_API');
        $http = new GuzzleHttp\Client();

        $i = 0;
        while (true) {
            $request = $http->post($api_url, ['json' => $params]);
            $response = json_decode((string)$request->getBody(), true);

            if ($i > 2 || $response['code'] == 0) {
                break;
            }
            $i++;
        }

        $this->logInvokedApi($response, $params, $api_url);
        return $response['data']['stat'][0] ?? [];
    }

    /**
     * 记录日志
     * @param array $response
     * @param array $params
     * @param string $api_url
     * @throws \Exception
     */
    private function logInvokedApi(array $response, array $params, string $api_url)
    {
        // log
        $uuid = $this->params['uuid'];
        $msg = '场景:调用api:' . $api_url . '接口获取产品的调用情况';
        MongoLog::create(compact('uuid', 'msg', 'response', 'params', 'api_url'));

        // 校验
        if ($response['code'] != 0) {
            $msg = 'uuid:' . $uuid . ' 获取调用量失败,请检查原因 response:' . json_encode(compact('response', 'api_url'));
            throw new \Exception($msg);
        }
    }

    /**
     * 生成账单参数列表
     * @return array
     */
    private function genParamsList(): array
    {
        list($date_start, $date_end, $apikey) = [
            $this->params['section_begin'], $this->params['section_end'],
            $this->params['apikey']
        ];

        return compact('date_end', 'date_start', 'apikey');
    }

    /**
     * 校验参数
     * @return mixed|void
     * @throws \Exception
     */
    public function validateParams()
    {
        list($start_time, $end_time, $apikey, $uuid, $fee_basis, $fee_price_rule) = [$this->params['section_begin'] ?? '',
            $this->params['section_end'] ?? '', $this->params['apikey'] ?? '',
            $this->params['uuid'] ?? '', $this->params['fee_basis'] ?? '', $this->params['fee_price_rule'] ?? ''];

        $params = $this->params;
        if (!$start_time || !$end_time) {
            $msg = $this->title_prefix . '请输入合法的时间段' . json_encode(compact('params'));
            $this->outputWhenException($msg);
        }

        if (!$apikey) {
            $msg = $this->title_prefix . '请输入合法的apikey msg:' . json_encode(compact('params'));
            $this->outputWhenException($msg);
        }

        if (!$uuid) {
            $msg = $this->title_prefix . '请输入合法的uuid msg:' . json_encode(compact('params'));
            $this->outputWhenException($msg);
        }

        if (!$fee_basis) {
            $msg = $this->title_prefix . '请输入合法的fee_basis msg:' . json_encode(compact('params'));
            $this->outputWhenException($msg);
        }

        if (!in_array($fee_basis, $this->list_fee_basis)) {
            $msg = $this->title_prefix . ' product_id:' . $this->product_id . ' fee_basis只允许' . implode(',', $this->list_fee_basis) . ' msg:' . json_encode(compact('params'));
            $this->wechatException($msg);
        }

        if ($fee_price_rule === '') {
            $msg = $this->title_prefix . '请输入fee_price_rule msg:' . json_encode(compact('params'));
            $this->outputWhenException($msg);
        }
    }

    /**
     * 异常的时候的处理
     * @param string $msg
     * @throws \Exception
     */
    private function outputWhenException(string $msg)
    {
        $this->wechatException($msg);
        throw new \Exception($msg);
    }

}