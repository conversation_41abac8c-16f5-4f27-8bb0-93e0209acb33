<?php

namespace App\Illuminate\BillStatistics\Cuishou;

use App\Illuminate\BillStatistics\{
	BackApiTrait, DriverFactory
};
use App\Models\MongoLog;
use App\TraitUpgrade\{
	CurlTrait, WechatExceptionTrait
};

class BillStat210Driver implements DriverFactory
{
	use CurlTrait, WechatExceptionTrait, BackApiTrait;
	
	/** @var int 当前产品ID */
	private $product_id = 210;
	
	/** @var string 如果是使用back-api的list统计接口的话 则 */
	private $field_back_api = '';
	
	/** @var array 传递进来的参数 */
	private $params;
	
	/** @var array 允许的计费依据 */
	private $list_fee_basis = [1, 2];
	
	/** @var array 计费依据和字段的映射关系 */
	private $list_fee_basis_mapping;
	
	/**
	 * 生成账单调用量信息
	 *
	 * @param array $params
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	public function billStat(array $params): array
	{
		$this->params = $params;
		
		// 校验参数
		$this->validateParams();
		
		// 生成调用量
		return $this->genBillStat();
	}
	
	/**
	 * 生成账单参数列表
	 * @return array
	 */
	private function genParamsList(): array
	{
		list($start_time, $end_time, $apikey) = [
			$this->params['section_begin'],
			$this->params['section_end'],
			$this->params['apikey'],
		];
		
		//由于今日的调用量不全，所以最多只能到昨日
		$end_time = min($end_time, date('Ymd', strtotime('-1 day')));
		
		// 产品的apikey
		$key = $this->getProductKey();
		if ($this->isRequestChildren()) {
			return compact('start_time', 'end_time', 'apikey', 'key');
		}
		$product_id = $this->product_id;
		
		return compact('start_time', 'end_time', 'apikey', 'key', 'product_id');
	}
	
	/**
	 * 获取调用量
	 *
	 * @param array $params
	 *
	 * @return array
	 * @throws \Exception
	 */
	private function getInvokedNumber(array $params): array
	{
		
		// 重置成参数
		
		$api_url = env('LIST_STAT_COMMON_API');
		$i       = 0;
		while (true) {
			$response = $this->post($api_url, $params);
			
			if ($i > 2 || $response['status'] == 0) {
				break;
			}
			$i++;
		}
		
		// 校验返回&&记录日志
		$this->logInvokedApi($response, $params, $api_url);
		if ($this->isRequestChildren()) {
			// 累加各个子产品的数值
			return $this->_getTotalOfSubChild($response['list_stat']);
		}
		
		return $response['list_stat']['cuishouShort210']['total'];
	}
	
	/**
	 * 累加各个子产品的数值
	 *
	 * @param array $list_children_stat
	 *
	 * @return array
	 */
	private function _getTotalOfSubChild(array $list_children_stat): array
	{
		return array_reduce($list_children_stat, function ($carry, $item_product) {
			$total_item                = $item_product['total'] ?? [];
			$carry['yidong_success']   += $total_item['yidong_success'] ?? 0;
			$carry['dianxin_success']  += $total_item['dianxin_success'] ?? 0;
			$carry['liantong_success'] += $total_item['liantong_success'] ?? 0;
			$carry['liantong_cache']   += $total_item['liantong_cache'] ?? 0;
			$carry['yidong_cache']     += $total_item['yidong_cache'] ?? 0;
			$carry['dianxin_cache']    += $total_item['dianxin_cache'] ?? 0;
			$carry['allcmcc_success'] += $total_item['allcmcc_success']??0;
			$carry['allcmcc_cache'] += $total_item['allcmcc_cache']??0;
			
			return $carry;
		}, [
			'yidong_success'   => 0,
			'dianxin_success'  => 0,
			"liantong_success" => 0,
			"liantong_cache"   => 0,
			"yidong_cache"     => 0,
			"dianxin_cache"    => 0,
			"allcmcc_success"    => 0,
			"allcmcc_cache"    => 0,
		]);
	}
	
	/**
	 * 获取对应的计费字段
	 *
	 * @param array $list_stat
	 *
	 * @return array
	 * @throws \Exception
	 */
	private function getTheChooseField(array $list_stat): array
	{
		// 获取统计的字段
		list($fee_basis, $fee_price_rule) = [$this->params['fee_basis'], $this->params['fee_price_rule']];
		list($yd_succ, $dx_succ, $lt_succ, $lt_cache, $yd_cache, $dx_cache, $allcmcc_success, $allcmcc_cache) = [
			$list_stat['yidong_success'] ?? 0,
			$list_stat['dianxin_success'] ?? 0,
			$list_stat['liantong_success'] ?? 0,
			$list_stat['liantong_cache'] ?? 0,
			$list_stat['yidong_cache'] ?? 0,
			$list_stat['dianxin_cache'] ?? 0,
			$list_stat['allcmcc_success']??0,
			$list_stat['allcmcc_cache']??0,
		];
		
		// 有效调用量并且不区分运营商
		if ($fee_price_rule != 2) {
			$fee_number = $yd_succ + $dx_succ + $lt_succ + $lt_cache + $yd_cache + $dx_cache;
			
			return compact('fee_number');
		} else if ($fee_price_rule == 2) {
			// 有效调用量 && 区分运营商
			$yd         = $yd_succ + $yd_cache - $allcmcc_success - $allcmcc_cache;
			$dx         = $dx_succ + $dx_cache;
			$lt         = $lt_succ + $lt_cache;
			$all_yd     = $allcmcc_success + $allcmcc_cache;
            $fee_number = compact('yd', 'dx', 'lt', 'all_yd');
            return compact('fee_number');
        }
		
		$msg = $this->title_prefix . $this->product_id . '永远都不应该触发的情况, 标记(20190322)';
		$this->outputWhenException($msg);
	}
	
	/**
	 * 是否请求子产品统计数据
	 *  如果计费规则为 1 有效调用字段量，则请求的是每个字段的合计数据
	 *              2 有效调用量，则请求的是210产品的成功量
	 *
	 * @access protected
	 *
	 * @return boolean
	 **/
	protected function isRequestChildren()
	{
		return $this->params['fee_basis'] == 1;
	}
	
}