<?php

namespace App\Illuminate\BillStatistics\Cuishou;

use App\Illuminate\BillStatistics\{
    BackApiTrait, DriverFactory
};
use App\TraitUpgrade\{
    CurlTrait, WechatExceptionTrait
};

class BillStat294Driver implements DriverFactory
{
    use CurlTrait, WechatExceptionTrait, BackApiTrait;

    /** @var int 当前产品ID */
    private $product_id= 294;

    /** @var string 如果是使用back-api的list统计接口的话 则 */
    private $field_back_api = 'cuishouShort294';

    /** @var array 传递进来的参数 */
    private $params;

    /** @var array 允许的计费依据 */
    private $list_fee_basis = [1];

    /** @var array 计费依据和字段的映射关系 */
    private $list_fee_basis_mapping;

    /**
     * 生成账单调用量信息
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    public function billStat(array $params): array
    {
        $this->params = $params;

        // 校验参数
        $this->validateParams();

        // 生成调用量
        return $this->genBillStat();
    }

    /**
     * 记录日志
     * @param array $response
     * @return mixed|void
     */
    public function logApi(array $response)
    {
        // TODO 校验返回

        // TODO 记录日志
    }

    /**
     * 获取对应的计费字段
     * @param array $list_stat
     * @return array
     * @throws \Exception
     */
    private function getTheChooseField(array $list_stat): array
    {
		// 获取统计的字段
		list($fee_basis, $fee_price_rule) = [$this->params['fee_basis'], $this->params['fee_price_rule']];
		list($yd_succ, $dx_succ, $lt_succ, $lt_cache, $yd_cache, $dx_cache, $allcmcc_success, $allcmcc_cache) = [
			$list_stat['yidong_success'] ?? 0,
			$list_stat['dianxin_success'] ?? 0,
			$list_stat['liantong_success'] ?? 0,
			$list_stat['liantong_cache'] ?? 0,
			$list_stat['yidong_cache'] ?? 0,
			$list_stat['dianxin_cache'] ?? 0,
			$list_stat['allcmcc_success']??0,
			$list_stat['allcmcc_cache']??0,
		];
	
		// 有效调用量并且不区分运营商
		if ($fee_price_rule != 2) {
			$fee_number = $yd_succ + $dx_succ + $lt_succ + $lt_cache + $yd_cache + $dx_cache;
			return compact('fee_number');
		} elseif ($fee_price_rule == 2) {
			// 有效调用量 && 区分运营商
			$yd         = $yd_succ + $yd_cache - $allcmcc_success - $allcmcc_cache;
			$dx         = $dx_succ + $dx_cache;
			$lt         = $lt_succ + $lt_cache;
			$all_yd     = $allcmcc_success + $allcmcc_cache;
			$fee_number = compact('yd', 'dx', 'lt', 'all_yd');
			return compact('fee_number');
		}
	
		$msg = $this->title_prefix . $this->product_id . '永远都不应该触发的情况, 标记(20190322)';
		$this->outputWhenException($msg);
    }

}