<?php

namespace App\Illuminate\BillStatistics\GoldenShield;

use App\Illuminate\BillStatistics\BackApiTrait;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\WechatExceptionTrait;
use App\Illuminate\BillStatistics\DriverFactory;

class GoldenShield613Driver implements DriverFactory
{
    use CurlTrait, BackApiTrait, WechatExceptionTrait;

    /** @var int 当前产品ID */
    private $product_id = 613;

    /** @var string 如果是使用back-api的list统计接口的话 则 */
    private $field_back_api = 'goldenShield613';

    /** @var array 传递进来的参数 */
    private $params;

    /** @var array 允许的计费依据 */
    private $list_fee_basis = [1];

    /** @var array 计费依据和字段的映射关系 */
    private $list_fee_basis_mapping = [
        1 => 'succ'
    ];

    /**
     * 生成计费依据
     *
     * @param array $params
     *
     * @return array
     * @throws \Exception
     */
    public function billStat(array $params): array
    {
        $this->params = $params;

        // 校验参数
        $this->validateParams();

        // 生成调用量
        return $this->genBillStat();
    }

    public function logApi(array $response)
    {
        // TODO log
    }

    /**
     * 获取对应的计费字段
     *
     * @param array $list_stat
     *
     * @return array
     * @throws \Exception
     */
    private function getTheChooseField(array $list_stat): array
    {
        // 找到fee_basis对应的字段,
        $fee_basis = $this->params['fee_basis'];
        $fee_field = $this->list_fee_basis_mapping[$fee_basis];
        if (!array_key_exists($fee_field, $list_stat)) {
            $msg = $this->title_prefix . ' product_id:' . $this->product_id . ' 返回值中缺少映射的字段 msg:' .
                json_encode(compact('fee_field', 'list_stat', 'fee_basis'));
            $this->outputWhenException($msg);
        }

        $fee_number = $list_stat[$fee_field];
        return compact('fee_number');
    }
}