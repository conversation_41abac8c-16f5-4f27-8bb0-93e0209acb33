<?php

namespace App\Illuminate\BillStatistics\GoldenShieldNew;

use App\Illuminate\BillStatistics\BackApiTrait;
use App\TraitUpgrade\CurlTrait;
use App\TraitUpgrade\WechatExceptionTrait;
use App\Illuminate\BillStatistics\DriverFactory;

class BillStat615Driver implements DriverFactory
{
	use CurlTrait, BackApiTrait, WechatExceptionTrait;
	
	/** @var int 当前产品ID */
	private $product_id = 615;
	
	/** @var string 如果是使用back-api的list统计接口的话 则 */
	private $field_back_api = 'goldenShieldNew615';
	
	/** @var array 传递进来的参数 */
	private $params;
	
	/** @var array 允许的计费依据 */
	private $list_fee_basis = [1, 2, 3, 4];
	
	/** @var array 计费依据和字段的映射关系 */
	private $list_fee_basis_mapping = [
		1 => 'cus_succ',
		2 => 'cdnums',
		3 => 'succ661662',
		4 => 'not_timeout_num',
	];
	
	//需要排除的子产品字段(651、652为内部分流接口，不计入计费用量中)
	private $filter_children_product_id = [651, 652, 681, 682, 683, 684];
	
	/**
	 * 生成计费依据
	 *
	 * @param array $params
	 *
	 * @return array
	 * @throws \Exception
	 */
	public function billStat(array $params): array
	{
		$this->params = $params;
		
		// 校验参数
		$this->validateParams();
		
		// 生成调用量
		return $this->genBillStat();
	}
	
	/**
	 * 生成账单参数列表
	 * @return array
	 */
	private function genParamsList(): array
	{
		list($start_time, $end_time, $apikey) = [
			$this->params['section_begin'],
			$this->params['section_end'],
			$this->params['apikey'],
		];
		
		//由于今日的调用量不全，所以最多只能到昨日
		$end_time = min($end_time, date('Ymd', strtotime('-1 day')));
		
		// 产品的apikey
		$key = $this->getProductKey();
		
		$product_id = $this->product_id;
		//如果计费依据为查得量，则查询所有子产品的用量
		if ($this->isChilrenParams()) {
			return compact('start_time', 'end_time', 'apikey', 'key');
		}
		
		return compact('start_time', 'end_time', 'apikey', 'key', 'product_id');
	}
	
	public function logApi(array $response)
	{
		// TODO log
	}
	
	/**
	 * 获取调用量
	 *
	 * @param array $params
	 *
	 * @return array
	 * @throws \Exception
	 */
	private function getInvokedNumber(array $params): array
	{
		$api_url = env('LIST_STAT_COMMON_API');
		$i = 0;
		while (true) {
			$response = $this->post($api_url, $params);
			
			if ($i > 2 || $response['status'] == 0) {
				break;
			}
			$i++;
		}
		// 校验返回&&记录日志
		$this->logInvokedApi($response, $params, $api_url);
		
		return $this->getChildrenTotalArray($response['list_stat']);
	}
	
	/**
	 * 获取统计产品的字段
	 *
	 * @access protected
	 *
	 * @param $data array 数据
	 *
	 * @return array
	 **/
	protected function getChildrenTotalArray($data)
	{
		if (!$this->isChilrenParams()) {
			return $data[$this->field_back_api]['total'] ?? [];
		}
		
		//删除掉对应的需要过滤掉的子产品
		foreach ($this->filter_children_product_id as $children_product_id) {
			if (array_key_exists('goldenShieldNew' . $children_product_id, $data)) {
				unset($data['goldenShieldNew' . $children_product_id]);
			}
		}
		
		return array_reduce($data, function ($carry, $item_product) {
			$total_item        = $item_product['total'] ?? [];
			$carry['cus_succ'] += $total_item['succ'] ?? 0;
			$carry['cdnums']   += $total_item['cdnums'] ?? 0;
			$carry['succ661662']   += $total_item['succ661662'] ?? 0;
			
			return $carry;
		}, [
			'succ661662' => 0,
			'cus_succ'   => 0,
			"cdnums"     => 0,
		]);
	}
	
	/**
	 * 获取对应的计费字段
	 *
	 * @param array $list_stat
	 *
	 * @return array
	 * @throws \Exception
	 */
	private function getTheChooseField(array $list_stat): array
	{
		// 找到fee_basis对应的字段,
		$fee_basis = $this->params['fee_basis'];
		
		$fee_field = $this->list_fee_basis_mapping[$fee_basis];
		
		if (!array_key_exists($fee_field, $list_stat)) {
			$msg = $this->title_prefix . ' product_id:' . $this->product_id . ' 返回值中缺少映射的字段 msg:' . json_encode(compact('fee_field', 'list_stat', 'fee_basis'));
			$this->outputWhenException($msg);
		}
		
		$fee_number = $list_stat[$fee_field];
		
		return compact('fee_number');
	}
	
	
	/**
	 * 校验是否需要获取子产品的调用量情况
	 *      如果计费依据为 1（有效调用量），则计费用量为615产品的有效调用量
	 *      如果计费依据为 2（查得量）,则计费用量为615各子产品的查得量之和（需要排除651、652）
	 *
	 * @access protected
	 *
	 * @return boolean
	 **/
	protected function isChilrenParams()
	{
		return $this->params['fee_basis'] == 2;
	}
	
}