<?php

namespace App\Illuminate\BillStatistics\DataValidate;

use App\Illuminate\BillStatistics\{
    BackApiTrait, DriverFactory
};
use App\TraitUpgrade\{
    CurlTrait, WechatExceptionTrait
};

class BillStat240Driver implements DriverFactory
{
    use CurlTrait, WechatExceptionTrait, BackApiTrait;

    /** @var int 当前产品ID */
    private $product_id = 240;

    /** @var string 如果是使用back-api的list统计接口的话 则 */
    private $field_back_api = 'dataValidate238';

    /** @var array 传递进来的参数 */
    private $params;

    /** @var array 允许的计费依据 */
    private $list_fee_basis = [1, 2];

    /** @var array 计费依据和字段的映射关系 */
    private $list_fee_basis_mapping;

    /**
     * 生成账单调用量信息
     * @param array $params
     * @return mixed
     * @throws \Exception
     */
    public function billStat(array $params): array
    {
        $this->params = $params;

        // 校验参数
        $this->validateParams();

        // 生成调用量
        return $this->genBillStat();
    }

    /**
     * 记录日志
     * @param array $response
     * @return mixed|void
     */
    public function logApi(array $response)
    {
        // TODO 校验返回

        // TODO 记录日志
    }

    /**
     * 获取对应的计费字段
     * @param array $list_stat
     * @return array
     * @throws \Exception
     */
    private function getTheChooseField(array $list_stat): array
    {
        // 获取统计的字段
        list($fee_basis, $fee_price_rule) = [$this->params['fee_basis'], $this->params['fee_price_rule']];

        list($yd, $dx, $lt, $yd_all, $dx_all, $lt_all) = [
            $list_stat['yd_succ'] ?? 0, $list_stat['dx_succ'] ?? 0,
            $list_stat['lt_succ'] ?? 0, $list_stat['yd_all'] ?? 0,
            $list_stat['dx_all'] ?? 0, $list_stat['lt_all'] ?? 0
        ];

        // 有效调用量并且不区分运营商
        if ($fee_basis == 1 && $fee_price_rule != 2) {
            $fee_number = $yd + $dx + $lt;
            return compact('fee_number');
        } elseif ($fee_basis == 1 && $fee_price_rule == 2) {
            // 有效调用量 && 区分运营商
            $fee_number = compact('yd', 'dx', 'lt');
            return compact('fee_number');
        } elseif($fee_basis ==2 && $fee_price_rule !=2){
            // 总调用量 && 不区分运营商
            $fee_number = $yd_all + $dx_all + $lt_all;
            return compact('fee_number');

        } elseif ($fee_basis == 2 && $fee_price_rule ==2) {
            // 总调用量 && 区分运营商
            $yd = $yd_all;
            $dx = $dx_all;
            $lt = $lt_all;

            $fee_number = compact('yd', 'dx', 'lt');
            return compact('fee_number');
        }

        $msg = $this->title_prefix . $this->product_id . '永远都不应该触发的情况, 标记(23790322)';
        $this->outputWhenException($msg);
    }

}