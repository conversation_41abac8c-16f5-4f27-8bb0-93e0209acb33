<?php

namespace App\Illuminate\BillStatistics;

use App\Illuminate\BillStatistics\Bang\BillStat401Driver;
use App\Illuminate\BillStatistics\Crawler\{
	BillStat102Driver, BillStat301Driver, BillStat302Driver
};

use App\Illuminate\BillStatistics\Cuishou\{
	BillStat107Driver, BillStat210Driver, BillStat241Driver, BillStat242Driver, BillStat243Driver, BillStat244Driver, BillStat245Driver, BillStat246Driver, BillStat247Driver, <PERSON><PERSON>tat253Driver, BillStat252Driver, BillStat251Driver, Bill<PERSON>tat105Driver, BillStat101Driver, BillStat254Driver, BillStat255Driver, BillStat256Driver, BillStat257Driver, BillStat258Driver, BillStat259Driver, BillStat260Driver, BillStat261Driver, BillStat262Driver, BillStat263Driver, BillStat264Driver, BillStat265Driver, BillStat266Driver, BillS<PERSON>267Driver, BillStat268Driver, <PERSON>S<PERSON>269Driver, <PERSON><PERSON><PERSON>270Driver, <PERSON><PERSON><PERSON>271Driver, <PERSON>Stat272Driver, <PERSON>S<PERSON>273Driver, BillStat274Driver, BillStat275Driver, BillStat276Driver, BillStat277Driver, BillStat278Driver, BillStat279Driver, BillStat280Driver, BillStat281Driver, BillStat282Driver, BillStat283Driver, BillStat284Driver, BillStat285Driver, BillStat286Driver, BillStat287Driver, BillStat288Driver, BillStat289Driver, BillStat290Driver, BillStat291Driver, BillStat292Driver, BillStat293Driver, BillStat294Driver, BillStat295Driver, BillStat296Driver, BillStat297Driver, BillStat298Driver, BillStat299Driver, BillStat501Driver, BillStat711Driver, BillStat712Driver
};
use App\Illuminate\BillStatistics\DataValidate\{
	BillStat201Driver, BillStat202Driver, BillStat203Driver, BillStat204Driver, BillStat205Driver, BillStat206Driver, BillStat207Driver, BillStat208Driver, BillStat211Driver, BillStat212Driver, BillStat213Driver, BillStat214Driver, BillStat215Driver, BillStat216Driver, BillStat217Driver, BillStat218Driver, BillStat219Driver, BillStat220Driver, BillStat221Driver, BillStat222Driver, BillStat230Driver, BillStat231Driver, BillStat232Driver, BillStat233Driver, BillStat234Driver, BillStat235Driver, BillStat237Driver, BillStat238Driver, BillStat239Driver, BillStat240Driver, BillStat310Driver, BillStat311Driver, BillStat312Driver, BillStat313Driver, BillStat314Driver, BillStat315Driver, BillStat316Driver, BillStat317Driver, BillStat318Driver, BillStat320Driver, BillStat321Driver, BillStat322Driver, BillStat323Driver, BillStat324Driver, BillStat325Driver, BillStat326Driver, BillStat327Driver, BillStat328Driver, BillStat329Driver, BillStat330Driver, BillStat331Driver, BillStat900Driver, BillStat901Driver
};

use App\Illuminate\BillStatistics\Matching\{
	BillStat104Driver, BillStat601Driver, BillStat604Driver
};

use App\Illuminate\BillStatistics\Other\BillStat616Driver;
use App\Illuminate\BillStatistics\Other\BillStat664Driver;
use App\Illuminate\BillStatistics\TelStatus\BillStat801Driver;

use App\Illuminate\BillStatistics\GoldenShield\{
	GoldenShield612Driver, GoldenShield613Driver, GoldenShield614Driver
};

use App\Illuminate\BillStatistics\GoldenShieldNew\{
	BillStat615Driver, BillStat645Driver, BillStat646Driver, BillStat647Driver, BillStat648Driver, BillStat649Driver, BillStat651Driver, BillStat652Driver, BillStat661Driver, BillStat662Driver, BillStat663Driver, BillStat641Driver, BillStat642Driver, BillStat643Driver, BillStat644Driver
};

use Laravel\Lumen\Application;

class Manger implements MangerFactory
{
	/** @var array 邦秒爬产品的产品ID列表 */
	private $list_crawler_product_ids = [
		301,
		302,
	];
	
	/** @var  Application The application instance. */
	private $app;
	
	public function __construct($app)
	{
		$this->app = $app;
	}
	
	/**
	 * what fucked! 每个产品的需要的调用量 && 有效调用量的定义是不同的 （有些是一个字段, 有些是三种运营商的累加，
	 * 各个产品相同含义的字段还是不同的；所以需要写不同的驱动 fucked）
	 *
	 * @param string $driver
	 *
	 * @throws \Exception
	 * @return mixed
	 */
	public function driver(string $driver)
	{
		return $this->resolve($driver);
	}
	
	/**
	 * 解析
	 *
	 * @param string $driver_name
	 *
	 * @return mixed
	 * @throws \Exception
	 */
	private function resolve(string $driver_name)
	{
		$driver_method = 'create' . ucfirst($driver_name) . 'Driver';
		
		if (method_exists($this, $driver_method)) {
			return $this->{$driver_method}();
		} else {
			throw new \Exception('抱歉，账单调用依据服务目前不支持' . ucfirst($driver_name) . 'Driver类型的账单模块');
		}
	}
	
	/**
	 * @return BillStat102Driver
	 */
	private function createBillStat102Driver()
	{
		return new BillStat102Driver();
	}
	
	
	/**
	 * 101 催收分析详单版
	 * @return BillStat101Driver
	 */
	private function createBillStat101Driver()
	{
		return new BillStat101Driver();
	}
	
	/**
	 * 104 邦秒配（详单版）
	 */
	private function createBillStat104Driver()
	{
		return new BillStat104Driver();
	}
	
	/**
	 * 104 邦秒配（详单版）
	 */
	private function createBillStat604Driver()
	{
		return new BillStat604Driver();
	}
	
	/**
	 * @return BillStat107Driver
	 */
	private function createBillStat107Driver()
	{
		return new BillStat107Driver();
	}
	
	/**
	 * 105 催收分析（详单版V2）
	 * @return BillStat105Driver
	 */
	private function createBillStat105Driver()
	{
		return new BillStat105Driver();
	}
	
	/**
	 * 201手机号三要素验证详版
	 * @return BillStat201Driver
	 */
	private function createBillStat201Driver()
	{
		return new BillStat201Driver();
	}
	
	private function createBillStat237Driver()
	{
		return new BillStat237Driver();
	}
	
	private function createBillStat238Driver()
	{
		return new BillStat238Driver();
	}
	
	private function createBillStat239Driver()
	{
		return new BillStat239Driver();
	}
	
	private function createBillStat240Driver()
	{
		return new BillStat240Driver();
	}
	
	private function createBillStat233Driver()
	{
		return new BillStat233Driver();
	}
	
	private function createBillStat234Driver()
	{
		return new BillStat234Driver();
	}
	
	private function createBillStat235Driver()
	{
		return new BillStat235Driver();
	}
	
	
	private function createBillStat232Driver()
	{
		return new BillStat232Driver();
	}
	
	/**
	 *    手机号在网时长
	 * @return BillStat202Driver
	 */
	private function createBillStat202Driver()
	{
		return new BillStat202Driver();
	}
	
	/**
	 * 手机号消费档次
	 * @return BillStat204Driver
	 */
	private function createBillStat204Driver()
	{
		return new BillStat204Driver();
	}
	
	/**
	 * 手机号常在地址对比
	 * @return BillStat205Driver
	 */
	private function createBillStat205Driver()
	{
		return new BillStat205Driver();
	}
	
	/**
	 * 近三个月手机停机次数
	 * @return BillStat206Driver
	 */
	private function createBillStat206Driver()
	{
		return new BillStat206Driver();
	}
	
	/**
	 * 近三个月手机流量查询
	 * @return BillStat207Driver
	 */
	private function createBillStat207Driver()
	{
		return new BillStat207Driver();
	}
	
	/**
	 * 银行卡四要素
	 * @return BillStat208Driver
	 */
	private function createBillStat208Driver()
	{
		return new BillStat208Driver();
	}
	
	/**
	 * 手机号码归属地
	 * @return BillStat211Driver
	 */
	private function createBillStat211Driver()
	{
		return new BillStat211Driver();
	}
	
	/**
	 *    手机号家庭住址核验
	 * @return BillStat212Driver
	 */
	private function createBillStat212Driver()
	{
		return new BillStat212Driver();
	}
	
	/**
	 * 手机号三要素验证
	 * @return BillStat213Driver
	 */
	private function createBillStat213Driver()
	{
		return new BillStat213Driver();
	}
	
	/**
	 *
	 * @return BillStat214Driver
	 */
	private function createBillStat214Driver()
	{
		return new BillStat214Driver();
	}
	
	private function createBillStat216Driver()
	{
		return new BillStat216Driver();
	}
	
	private function createBillStat217Driver()
	{
		return new BillStat217Driver();
	}
	
	private function createBillStat218Driver()
	{
		return new BillStat218Driver();
	}
	
	private function createBillStat219Driver()
	{
		return new BillStat219Driver();
	}
	
	private function createBillStat221Driver()
	{
		return new BillStat221Driver();
	}
	
	private function createBillStat220Driver()
	{
		return new BillStat220Driver();
	}
	
	private function createBillStat222Driver()
	{
		return new BillStat222Driver();
	}
	
	private function createBillStat230Driver()
	{
		return new BillStat230Driver();
	}
	
	private function createBillStat231Driver()
	{
		return new BillStat231Driver();
	}
	
	private function createBillStat310Driver()
	{
		return new BillStat310Driver();
	}
	
	private function createBillStat311Driver()
	{
		return new BillStat311Driver();
	}
	
	private function createBillStat312Driver()
	{
		return new BillStat312Driver();
	}
	
	private function createBillStat313Driver()
	{
		return new BillStat313Driver();
	}
	
	private function createBillStat314Driver()
	{
		return new BillStat314Driver();
	}
	
	private function createBillStat315Driver()
	{
		return new BillStat315Driver();
	}
	
	private function createBillStat316Driver()
	{
		return new BillStat316Driver();
	}
	
	private function createBillStat317Driver()
	{
		return new BillStat317Driver();
	}
	
	private function createBillStat318Driver()
	{
		return new BillStat318Driver();
	}
	
	private function createBillStat321Driver()
	{
		return new BillStat321Driver();
	}
	
	private function createBillStat322Driver()
	{
		return new BillStat322Driver();
	}
	
	private function createBillStat323Driver()
	{
		return new BillStat323Driver();
	}
	
	private function createBillStat324Driver()
	{
		return new BillStat324Driver();
	}
	
	private function createBillStat325Driver()
	{
		return new BillStat325Driver();
	}
	
	private function createBillStat326Driver()
	{
		return new BillStat326Driver();
	}
	
	private function createBillStat327Driver()
	{
		return new BillStat327Driver();
	}
	
	private function createBillStat328Driver()
	{
		return new BillStat328Driver();
	}
	
	private function createBillStat329Driver()
	{
		return new BillStat329Driver();
	}
	
	private function createBillStat330Driver()
	{
		return new BillStat330Driver();
	}
	
	private function createBillStat331Driver()
	{
		return new BillStat331Driver();
	}
	
	private function createBillStat320Driver()
	{
		return new BillStat320Driver();
	}
	
	
	private function createBillStat900Driver()
	{
		return new BillStat900Driver();
	}
	
	private function createBillStat901Driver()
	{
		return new BillStat901Driver();
	}
	
	/**
	 * 手机号在网状态
	 * @return BillStat203Driver
	 */
	private function createBillStat203Driver()
	{
		return new BillStat203Driver();
	}
	
	/**
	 * 风险名单V2
	 * @return BillStat215Driver
	 */
	private function createBillStat215Driver()
	{
		return new BillStat215Driver();
	}
	
	private function createBillStat210Driver()
	{
		return new BillStat210Driver();
	}
	
	private function createBillStat241Driver()
	{
		return new BillStat241Driver();
	}
	
	private function createBillStat242Driver()
	{
		return new BillStat242Driver();
	}
	
	private function createBillStat243Driver()
	{
		return new BillStat243Driver();
	}
	
	private function createBillStat244Driver()
	{
		return new BillStat244Driver();
	}
	
	private function createBillStat245Driver()
	{
		return new BillStat245Driver();
	}
	
	private function createBillStat246Driver()
	{
		return new BillStat246Driver();
	}
	
	private function createBillStat247Driver()
	{
		return new BillStat247Driver();
	}
	
	/**
	 * 251催收分
	 * @return BillStat251Driver
	 */
	private function createBillStat251Driver()
	{
		return new BillStat251Driver();
	}
	
	/**
	 * 252    (催收)最近一次通话时间间隔
	 * @return BillStat252Driver
	 */
	private function createBillStat252Driver()
	{
		return new BillStat252Driver();
	}
	
	/**
	 * 253    (催收)首次通话时间间隔
	 * @return BillStat253Driver
	 */
	private function createBillStat253Driver()
	{
		return new BillStat253Driver();
	}
	
	/**
	 * 254 (催收)通话号码总个数
	 * @return BillStat254Driver
	 */
	private function createBillStat254Driver()
	{
		return new BillStat254Driver();
	}
	
	/**
	 * (催收)总通话次数
	 */
	private function createBillStat255Driver()
	{
		return new BillStat255Driver();
	}
	
	/**
	 *    (催收)被叫次数
	 * @return BillStat256Driver
	 */
	private function createBillStat256Driver()
	{
		return new BillStat256Driver();
	}
	
	/**
	 * (催收)被叫通话时长15秒以下的次数
	 * @return BillStat257Driver
	 */
	private function createBillStat257Driver()
	{
		return new BillStat257Driver();
	}
	
	/**
	 * (催收)被叫通话时长15-30秒的次数
	 * @return BillStat258Driver
	 */
	private function createBillStat258Driver()
	{
		return new BillStat258Driver();
	}
	
	/**
	 * (催收)被叫通话时长60秒以上的次数
	 * @return BillStat259Driver
	 */
	private function createBillStat259Driver()
	{
		return new BillStat259Driver();
	}
	
	/**
	 * (疑似催收)最近一次通话时间
	 * @return BillStat260Driver
	 */
	private function createBillStat260Driver()
	{
		return new BillStat260Driver();
	}
	
	/**
	 * (疑似催收)首次通话时间
	 * @return BillStat261Driver
	 */
	private function createBillStat261Driver()
	{
		return new BillStat261Driver();
	}
	
	/**
	 * 疑似催收)通话号码总个数
	 * @return BillStat262Driver
	 */
	private function createBillStat262Driver()
	{
		return new BillStat262Driver();
	}
	
	/**
	 * (疑似催收)总通话次数
	 * @return BillStat263Driver
	 */
	private function createBillStat263Driver()
	{
		return new BillStat263Driver();
	}
	
	/**
	 *   (疑似催收)被叫次数
	 * @return BillStat264Driver
	 */
	private function createBillStat264Driver()
	{
		return new BillStat264Driver();
	}
	
	/**
	 *    (疑似催收)被叫通话时长15秒以下的次数
	 * @return BillStat265Driver
	 */
	private function createBillStat265Driver()
	{
		return new BillStat265Driver();
	}
	
	/**
	 * (疑似催收)被叫通话时长15-30秒的次数
	 * @return BillStat266Driver
	 */
	private function createBillStat266Driver()
	{
		return new BillStat266Driver();
	}
	
	/**
	 * (疑似催收)通话时长60秒以上的次数
	 * @return BillStat267Driver
	 */
	private function createBillStat267Driver()
	{
		return new BillStat267Driver();
	}
	
	/**
	 * (近一周)(催收)通话号码总个数
	 * @return BillStat268Driver
	 */
	private function createBillStat268Driver()
	{
		return new BillStat268Driver();
	}
	
	/**
	 *    (近一周)(催收)被叫次数
	 * @return BillStat269Driver
	 */
	private function createBillStat269Driver()
	{
		return new BillStat269Driver();
	}
	
	/**
	 * 近一周)(疑似催收)通话号码总个数
	 * @return BillStat270Driver
	 */
	private function createBillStat270Driver()
	{
		return new BillStat270Driver();
	}
	
	/**
	 * (近一周)(疑似催收)被叫次数
	 * @return BillStat271Driver
	 */
	private function createBillStat271Driver()
	{
		return new BillStat271Driver();
	}
	
	/**
	 * (近二周)(催收)通话号码总个数
	 * @return BillStat272Driver
	 */
	private function createBillStat272Driver()
	{
		return new BillStat272Driver();
	}
	
	/**
	 *    (近二周)(催收)被叫次数
	 * @return BillStat273Driver
	 */
	private function createBillStat273Driver()
	{
		return new BillStat273Driver();
	}
	
	/**
	 *    (近二周)(疑似催收)通话号码总个数
	 * @return BillStat274Driver
	 */
	private function createBillStat274Driver()
	{
		return new BillStat274Driver();
	}
	
	/**
	 * (近二周)(疑似催收)被叫次数
	 * @return BillStat275Driver
	 */
	private function createBillStat275Driver()
	{
		return new BillStat275Driver();
	}
	
	/**
	 * (近三周)(催收)通话号码总个数
	 * @return BillStat276Driver
	 */
	private function createBillStat276Driver()
	{
		return new BillStat276Driver();
	}
	
	/**
	 *    (近三周)(催收)被叫次数
	 * @return BillStat277Driver
	 */
	private function createBillStat277Driver()
	{
		return new BillStat277Driver();
	}
	
	/**
	 *    (近三周)(疑似催收)通话号码总个数
	 * @return BillStat278Driver
	 */
	private function createBillStat278Driver()
	{
		return new BillStat278Driver();
	}
	
	/**
	 * (近三周)(疑似催收)被叫次数
	 * @return BillStat279Driver
	 */
	private function createBillStat279Driver()
	{
		return new BillStat279Driver();
	}
	
	/**
	 * (近30个自然日)(催收)通话号码总个数
	 * @return BillStat280Driver
	 */
	private function createBillStat280Driver()
	{
		return new BillStat280Driver();
	}
	
	/**
	 * (近30个自然日)(催收)被叫次数
	 * @return BillStat281Driver
	 */
	private function createBillStat281Driver()
	{
		return new BillStat281Driver();
	}
	
	/**
	 * (近30个自然日)(疑似催收)通话号码总个数
	 * @return BillStat282Driver
	 */
	private function createBillStat282Driver()
	{
		return new BillStat282Driver();
	}
	
	/**
	 * (近30个自然日)(疑似催收)被叫次数
	 * @return BillStat283Driver
	 */
	private function createBillStat283Driver()
	{
		return new BillStat283Driver();
	}
	
	/**
	 *    (近30-60个自然日)(催收)通话号码总个数
	 * @return BillStat284Driver
	 */
	private function createBillStat284Driver()
	{
		return new BillStat284Driver();
	}
	
	/**
	 * (近30-60个自然日)(催收)被叫次数
	 * @return BillStat285Driver
	 */
	private function createBillStat285Driver()
	{
		return new BillStat285Driver();
	}
	
	/**
	 * 近30-60个自然日)(疑似催收)通话号码总个数
	 * @return BillStat286Driver
	 */
	private function createBillStat286Driver()
	{
		return new BillStat286Driver();
	}
	
	/**
	 * (近30-60个自然日)(疑似催收)被叫次数
	 * @return BillStat287Driver
	 */
	private function createBillStat287Driver()
	{
		return new BillStat287Driver();
	}
	
	/**
	 * 读秒定制评分
	 * @return BillStat288Driver
	 */
	private function createBillStat288Driver()
	{
		return new BillStat288Driver();
	}
	
	private function createBillStat289Driver()
	{
		return new BillStat289Driver();
	}
	
	private function createBillStat290Driver()
	{
		return new BillStat290Driver();
	}
	
	private function createBillStat291Driver()
	{
		return new BillStat291Driver();
	}
	
	private function createBillStat292Driver()
	{
		return new BillStat292Driver();
	}
	
	private function createBillStat293Driver()
	{
		return new BillStat293Driver();
	}
	
	private function createBillStat294Driver()
	{
		return new BillStat294Driver();
	}
	
	private function createBillStat295Driver()
	{
		return new BillStat295Driver();
	}
	
	private function createBillStat296Driver()
	{
		return new BillStat296Driver();
	}
	
	private function createBillStat297Driver()
	{
		return new BillStat297Driver();
	}
	
	private function createBillStat298Driver()
	{
		return new BillStat298Driver();
	}
	
	private function createBillStat299Driver()
	{
		return new BillStat299Driver();
	}
	
	private function createBillStat711Driver()
	{
		return new BillStat711Driver();
	}
	
	private function createBillStat712Driver()
	{
		return new BillStat712Driver();
	}
	
	/**
	 * 邦秒爬H5版
	 * @return BillStat301Driver
	 */
	private function createBillStat301Driver()
	{
		return new BillStat301Driver();
	}
	
	/**
	 * 邦秒爬API版本
	 * @return BillStat302Driver
	 */
	private function createBillStat302Driver()
	{
		return new BillStat302Driver();
	}
	
	/**
	 * 催收分析私有云
	 * @return BillStat501Driver
	 */
	private function createBillStat501Driver()
	{
		return new BillStat501Driver();
	}
	
	/**
	 * 邦秒配详单版
	 * @return BillStat601Driver
	 */
	private function createBillStat601Driver()
	{
		return new BillStat601Driver();
	}
	
	/**
	 * 号码状态查询
	 * @return BillStat801Driver
	 */
	private function createBillStat801Driver()
	{
		return new BillStat801Driver();
	}
	
	/**
	 * 邦企查
	 * @return BillStat401Driver
	 */
	private function createBillStat401Driver()
	{
		return new BillStat401Driver();
	}
	
	/**
	 * 614 号码风险等级
	 *
	 * @return GoldenShield614Driver
	 **/
	private function createBillStat614Driver()
	{
		return new GoldenShield614Driver();
	}
	
	/**
	 * 612 号码风险等级
	 *
	 * @return GoldenShield612Driver
	 **/
	private function createBillStat612Driver()
	{
		return new GoldenShield612Driver();
	}
	
	/**
	 * 613 金盾贷后
	 *
	 * @return GoldenShield613Driver
	 **/
	private function createBillStat613Driver()
	{
		return new GoldenShield613Driver();
	}
	
	/**
	 * 新版金盾 v2.0
	 **/
	private function createBillStat615Driver()
	{
		return new BillStat615Driver();
	}
	
	private function createBillStat641Driver()
	{
		return new BillStat641Driver();
	}
	
	private function createBillStat642Driver()
	{
		return new BillStat642Driver();
	}
	
	private function createBillStat643Driver()
	{
		return new BillStat643Driver();
	}
	
	private function createBillStat644Driver()
	{
		return new BillStat644Driver();
	}
	
	private function createBillStat645Driver()
	{
		return new BillStat645Driver();
	}
	
	private function createBillStat646Driver()
	{
		return new BillStat646Driver();
	}
	
	private function createBillStat647Driver()
	{
		return new BillStat647Driver();
	}
	
	private function createBillStat648Driver()
	{
		return new BillStat648Driver();
	}
	
	private function createBillStat649Driver()
	{
		return new BillStat649Driver();
	}
	
	private function createBillStat651Driver()
	{
		return new BillStat651Driver();
	}
	
	private function createBillStat652Driver()
	{
		return new BillStat652Driver();
	}
	
	private function createBillStat661Driver()
	{
		return new BillStat661Driver();
	}
	
	private function createBillStat662Driver()
	{
		return new BillStat662Driver();
	}
	
	private function createBillStat663Driver()
	{
		return new BillStat663Driver();
	}
	
	private function createBillStat664Driver()
	{
		return new BillStat664Driver();
	}
	
	//风险符号
	private function createBillStat616Driver()
	{
		return new BillStat616Driver();
	}
}
