<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/28 0028
 * Time: 14:26
 */

namespace App\Illuminate\BillSpecialCustomer;

use App\Models\Account;
use App\Models\MongoBillMonth;
use App\Models\Product;
use App\TraitUpgrade\CurlTrait;

//好还
class ExcelC20181101M41IR3Driver implements ExcelFactory
{
    use CurlTrait;

    /** @var string 客户ID */
    private $customer_id = 'C20181101M41IR3';

    private $product_id_and_key = [
        101 => '5168b337cb7cdc2cd11675d634719ee9'  //邦信分详单版V1
    ];

    public function _getMoneyInfo(): array
    {
        // 初步整理计费片段
        $list_account_money_details = $this->_getMoneyDetailForGroup();

        // 计算各种算法需要详细数据
        $list_account_common_products = $this->_genBillDetailsForAlo($list_account_money_details);

        // 设置是否需要生成催收快捷版的明细
        //$list_shortcut_products = $this->_getDetermineCreateShortcut($list_account_money_details);

        //halt($list_shortcut_products);
        // 快捷版和通用部分进行聚合
        //return $this->_aggregateCommonAndShortcut($list_account_common_products, $list_shortcut_products);

    }

    /**
     * 计算各种算法需要详细数据
     *
     * @param array $list_account_money_details
     *
     * @return array
     */
    private function _genBillDetailsForAlo(array $list_account_money_details): array
    {
        return array_map(
            function ($item_account) {
                $item_account['list_bills'] = array_map(
                    function ($item_bill) {
                        // 获取当前算法类型树妖的数据相关
                        $item_bill['details'] = $this->_getDetailInfoForAlo($item_bill);
                        return $item_bill;
                    },
                    $item_account['list_bills']
                );

                return $item_account;
            },
            $list_account_money_details
        );
    }

    /**
     * 按照账号分组计费配置
     * @return array
     */
    private function _getMoneyDetailForGroup(): array
    {
        // 产品列表
        $list_products = Product::getListByCondition([], ['product_id', 'product_name'])
            ->pluck('product_name', 'product_id')
            ->all();
        $list_accounts = Account::getListByCondition([], ['account_id', 'account_name'])
            ->pluck('account_name', 'account_id')
            ->all();

        return MongoBillMonth::where(
            ['customer_id' => $this->customer_id, 'month' => $this->getBillMonth()]
        )
            ->get()
            ->filter(
                function ($item) {
                    $remarks = $item['section_source']['remarks'] ?? '';
                    return trim($remarks) != '系统归零设置';
                }
            )
            ->reduce(
                function ($carry, $item) use ($list_products, $list_accounts) {
                    // 充值属性类型,方便操作
                    $item = $this->_setObjectAttr($item);

                    //  设置算法 && 其他的基本属性
                    $item = $this->_setAloAttr($item, $list_products);

                    // 返回赋值
                    return $carry = $this->_setValueForReturn($carry, $item, $list_accounts);
                },
                []
            );
    }

    /**
     * 返回赋值
     *
     * @param array          $carry
     * @param MongoBillMonth $item
     *
     * @return array
     */
    private function _setValueForReturn(array $carry, MongoBillMonth $item, array $list_accounts): array
    {
        $carry[$item->account_id]['list_bills'][] = $item;
        if (!isset($carry[$item->account_id]) || !isset($carry[$item->account_id]['account_name'])) {
            $carry[$item->account_id]['account_name'] = $list_accounts[$item->account_id] ?? "没有找到对应的账号";
        }

        return $carry;
    }

    /**
     * 重置属性类型,方便操作
     *
     * @param MongoBillMonth $item
     *
     * @return MongoBillMonth
     */
    private function _setObjectAttr(MongoBillMonth $item): MongoBillMonth
    {
        $item->section_source = (object)$item->section_source;
        $item->start_day      = date('Ymd', strtotime($item->section_source->start_date));
        return $item;
    }

    /**
     * 设置算法
     *
     * @param MongoBillMonth $item
     * @param array          $list_products
     *
     * @return MongoBillMonth
     */
    private function _setAloAttr(MongoBillMonth $item, array $list_products): MongoBillMonth
    {
        list($product_alo, $product_alo_cn) = $this->_getProductAlo($item);
        $item->product_alo    = $product_alo;
        $item->product_alo_cn = $product_alo_cn;
        $item->product_name   = $list_products[$item->product_id] ?? '没有找到对应的产品';
        return $item;
    }

    /**
     * 获取账单月份
     *
     * @access private
     *
     * @return string
     **/
    private function getBillMonth()
    {
        $month = request()->get('month', date('Ym', strtotime('first day of last month')));
        if (!preg_match('/^20[12]\d[01]\d$/', $month)) {
            throw new \Exception('不正确的month参数');
        }
        return strval($month);
    }

    public function _validateCustomer()
    {
        //校验是否已开通邦信分详单版V1
        //return true;
        // TODO: Implement _validateCustomer() method.
    }
    /** @var string 秒配单号版 */
//    private $product_key_pei_dan = "fb53fddb7157dd76fd1bb656df4980a3";
//
//    /** @var string 详单v2 */
//    private $product_key_xiangdan_v2 = "1dc50df0fe0074cc3f1ac000bfe15589";
//
//    /** @var int 配配单号版本的产品ID */
//    private $product_id_pei_dan = 601;
//
//    /** @var int 邦信分详单v2产品ID */
//    private $product_id_xiangdan_v2 = 105;
//
//    /** @var string 特定的秒配账号 */
//    private $account_id_pei = "****************";


//    /**
//     * 萨摩耶客户的金额
//     * @return array
//     * @throws \Exception
//     */
//    public function _getMoneyInfo(): array
//    {
//
//
//        // 校验
//        $this->_validateCustomer();
//
//        return [
//            "xiangDanV2" => $this->_getXiangdanV2Info(),
//            "pei" => $this->_getPeiInfo(),
//        ];
//    }
//
//    /**
//     * 秒配单号版的信息
//     * @return array
//     * @throws \Exception
//     */
//    private function _getPeiInfo(): array
//    {
//        // 开始月分 结束月份  价格
//        list($month_begin, $month_end, $price) = $this->_getCommonInfoForPei();
//
//        // 秒配和快捷版本的调用量
//        list($number_pei, $number_xiangdan_v2, $number_valid) = $this->_getNumberForPei();
//
//        $money_total = $money_this_month = $number_valid < 0 ? 0 : ($price * $number_valid);
//        return compact("month_end", "month_begin", "number_valid", "number_xiangdan_v2", "number_pei", "money_total", "money_this_month", "price");
//
//    }
//
//    /**
//     * 秒配和快捷版本的调用量
//     * @return array
//     * @throws \Exception
//     */
//    private function _getNumberForPei(): array
//    {
//        // 秒配的调用量
//        $number_pei = $this->_getPeiNumber();
//
//        // 详单版本v2调用量
//        $number_xiangdan_v2 = $this->_getXiangdanV2Number();
//
//        // 核算调用量
//        $number_valid = $number_pei - $number_xiangdan_v2 * 400;
//
//        return [$number_pei, $number_xiangdan_v2, $number_valid];
//    }
//
//    /**
//     * 详单版本v2调用量
//     * @return int
//     * @throws \Exception
//     */
//    private function _getXiangdanV2Number(): int
//    {
//        return MongoBillMonth::where([
//                                         "customer_id" => $this->customer_id,
//                                         "month" => date("Ym", strtotime("last day of last month")),
//                                         "product_id" => $this->product_id_xiangdan_v2
//                                     ])->sum("section_invoked_number");
//
//    }
//
//    /**
//     * 条件
//     * @return array
//     */
//    private function _genParamsForXiangdanV2Number(): array
//    {
//        $start_time = date("Ymd", strtotime("first day of last month"));
//        $end_time = date("Ymd", strtotime("last day of last month"));
//        $apikey = $this->_getProductApikey($this->product_id_xiangdan_v2);
//        $key = $this->product_key_xiangdan_v2;
//        return compact("start_time", "end_time", "apikey", "key");
//    }
//
//    /**
//     * 秒配的调用量
//     * @return int
//     * @throws \Exception
//     */
//    private function _getPeiNumber(): int
//    {
//        // 条件
//        $params = $this->_genParamsForPeiNumber();
//
//        // 调用量
//        $list_stat = $this->_getInvokedNumber($params);
//
//        // 整合
//        $matching = $list_stat["matching"] ?? [];
//        $total = $matching["total"] ?? [];
//        return $total["itag"] ?? 0;
//    }
//
//    /**
//     * 条件
//     * @return array
//     */
//    private function _genParamsForPeiNumber(): array
//    {
//        $start_time = date("Ymd", strtotime("first day of last month"));
//        $end_time = date("Ymd", strtotime("last day of last month"));
//        $apikey = [$this->_getSpecialPeiApikey()];
//        $key = $this->product_key_pei_dan;
//        return compact("start_time", "end_time", "apikey", "key");
//    }
//
//    /**
//     * 秒配apikey
//     * @return string
//     */
//    private function _getSpecialPeiApikey() : string
//    {
//        return Account::getOneItemByCondition([
//                                                  "account_id" => $this->account_id_pei
//                                              ], ["apikey"])->apikey;
//    }
//
//
//    /**
//     * @param int $product_id
//     * @return array
//     */
//    private function _getProductApikey(int $product_id): array
//    {
//
//        $list_account_prodcuts = AccountProduct::where([
//                                                           "product_id" => $product_id
//                                                       ])->distinct("account_id")
//            ->select(["account_id"])
//            ->get()
//            ->pluck("account_id")
//            ->toArray();
//
//        return Account::where([
//                                  'customer_id' => $this->customer_id
//                              ])->select(["account_id", "apikey"])
//            ->get()
//            ->reduce(function ($carry, $item) use ($list_account_prodcuts) {
//                if (in_array($item->account_id, $list_account_prodcuts)) {
//                    $carry[] = $item->apikey;
//                }
//                return $carry;
//            }, []);
//    }
//
//
//    /**
//     * 通用信息
//     * @return array
//     */
//    private function _getCommonInfoForPei(): array
//    {
//        // 一月的开始和结束
//        $month_begin = date("Ymd", strtotime("first day of last month"));
//        $month_end = date("Ymd", strtotime("last day of last month"));
//
//        // 单价
//        $price = 0.3;
//
//        return [$month_begin, $month_end, $price];
//    }
//
//    /**
//     * 详单V2产品的相关信息
//     */
//    private function _getXiangdanV2Info(): array
//    {
//        // 上月结转余额信息
//        $money_last_before_this_month = $this->_getMoneyBeforeThisMonth();
//
//        // 本月的消费情况
//        $details = $this->_getDetailsForXiangdanV2();
//
//        //  合计-充值：账单月充值金额+上月结转余额
//        $money_recharge_sum = $money_last_before_this_month["money_last_before_this_month"] + $details["recharge"];
//
//        // 合计-消耗金额：消耗金额之和 (备注 这部分不要关心特殊消费)
//        $money_consume_sum = $money_last_before_this_month["money_history_consume"] + $details["money"];
//
//        // 剩余金额
//        $money_last_now = $this->_getLastMoneyForXiangDanV2();
//
//        return compact("money_last_before_this_month", "details", "money_recharge_sum", "money_consume_sum", "money_last_now");
//
//    }
//
//    private function _getLastMoneyForXiangDanV2(): array
//    {
//        // 历史充值
//        $money_recharge_history = MoneyRecharge::getRechargeMoney([
//                                                                      ['status', 3],
//                                                                      ['customer_id', $this->customer_id]
//                                                                  ]);
//
//        // 历史消费
//        $money_consume_history = MongoBillMonth::sumMoney([
//                                                              'customer_id' => $this->customer_id,
//                                                          ]);
//
//        // 历史特殊
//        $money_special_history = CustomerExpend::sumMoney([
//                                                              'customer_id' => $this->customer_id
//                                                          ]);
//
//        // 剩余的金额
//        $money_last_now = $money_recharge_history - $money_consume_history - $money_special_history;
//        return compact("money_recharge_history", "money_consume_history", "money_special_history", "money_last_now");
//    }
//
//    /**
//     * 详单v2的详细消费情况
//     * @return array
//     */
//    private function _getDetailsForXiangdanV2(): array
//    {
//        $money_info_this_month = $this->getPriceAndInvokedNumberForXiangDanV2();
//
//        return [
//            "month_begin" => date("Ymd", strtotime("first day of last month")),
//            "month_end" => date("Ymd", strtotime("last day of last month")),
//            "recharge" => $this->_getRechargeThisMonth(),  // 本月充值
//            "price" => $money_info_this_month["price"], // 价格
//            "number" => $money_info_this_month["number"],  // 本月的掉用量
//            "money" => $money_info_this_month["money"],  // 本月消费金额
//        ];
//    }
//
//    /**
//     * 详单v2获取消费金额和掉用量
//     * @return array
//     */
//    private function getPriceAndInvokedNumberForXiangDanV2(): array
//    {
//        return MongoBillMonth::where([
//                                         "customer_id" => $this->customer_id,
//                                         "month" => date("Ym", strtotime("last day of last month")),
//                                         "product_id" => $this->product_id_xiangdan_v2
//                                     ])->get()
//            ->reduce(function ($carry, $item) {
//                $carry["price"] = $item->section_source["fee_price"];
//                $carry["number"] += $item->section_invoked_number;
//                $carry["money"] += $item->money;
//                return $carry;
//            }, ["price" => 0.3, "number" => 0, "money" => 0]);
//    }
//
//    /**
//     * 本月充值金额
//     * @return float
//     */
//    private function _getRechargeThisMonth(): float
//    {
//        return MoneyRecharge::where([
//                                        ['status', 3],
//                                        ['customer_id', $this->customer_id]]
//        )
//            ->whereIn('remit_date', [
//                strtotime("first day of last month"),
//                strtotime("last day of last month"),
//            ])
//            ->sum('money');
//    }
//
//    /**
//     * 上月结转余额
//     * @return float
//     */
//    private function _getMoneyBeforeThisMonth(): array
//    {
//        // 客户的账单月之前的充值之和
//        $money_recharge_before_this_month = $this->_getRechargeMoneyBeforeThisMonth();
//
//        // 客户的账单月之前的历史消费
//        $money_history_consume = $this->_getConsumeMoneyBeforeThisMonth();
//
//        // 客户的特殊消费
//        $money_special_before_this_month = $this->_getSpeicalMoney();
//
//        // 上月剩余金额
//        $money_last_before_this_month = $money_recharge_before_this_month - $money_history_consume - $money_special_before_this_month;
//
//        return compact("money_special_before_this_month", "money_recharge_before_this_month", "money_history_consume", "money_last_before_this_month");
//    }
//
//    /**
//     * 客户的历史充值之和
//     * @return float
//     */
//    private function _getRechargeMoneyBeforeThisMonth(): float
//    {
//        $timestamp_this_month = strtotime("first day of last month");
//        $where = [
//            ['remit_date', '<', $timestamp_this_month],
//            ['status', 3],
//            ['customer_id', $this->customer_id]
//        ];
//
//        return MoneyRecharge::getRechargeMoney($where);
//    }
//
//
//    /**
//     * 客户的历史消费
//     * @return float
//     */
//    private function _getConsumeMoneyBeforeThisMonth(): float
//    {
//        return MongoBillMonth::where([
//                                         "customer_id" => $this->customer_id,
//                                         'month' => [
//                                             '$lt' => date("Ym", strtotime("last day of last month"))
//                                         ]
//                                     ])->sum("money");
//    }
//
//    /**
//     * 获取客户特殊的充值或者赠送的情况
//     * @return float
//     */
//    private function _getSpeicalMoney(): float
//    {
//        return CustomerExpend::sumMoney([
//                                            'customer_id' => $this->customer_id,
//                                            'start_date' => [
//                                                '$lt' => date("Ym", strtotime("last day of last month"))
//                                            ]
//                                        ]);
//    }
//
//
//    /**
//     * 获取调用量
//     * @param array $params
//     * @param null $api_url
//     * @return array
//     * @throws \Exception
//     */
//    private function _getInvokedNumber(array $params, $api_url = null): array
//    {
//        $api_url = $api_url ?? env('LIST_STAT_COMMON_API');
//
//        $i = 0;
//        while (true) {
//            $response = $this->post($api_url, $params);
//
//            if ($i > 2 || $response['status'] == 0) {
//                break;
//            }
//            $i++;
//        }
//
//        // 校验返回&&记录日志
//        $this->logInvokedApi($response, $params, $api_url);
//
//        return $response['list_stat'] ?? [];
//    }
//
//    /**
//     * 记录日志
//     * @param array $response
//     * @param array $params
//     * @param string $api_url
//     * @throws \Exception
//     */
//    private function logInvokedApi(array $response, array $params, string $api_url)
//    {
//        $customer_id = $this->customer_id;
//        $intro = "特殊客户获取调用量";
//        $msg = '场景:调用api:' . $api_url . '接口获取产品的调用情况';
//        MongoLog::create(compact('uuid', 'msg', 'response', 'params', 'api_url', 'intro', 'customer_id'));
//
//        // 校验
//        if ($response['status'] != 0) {
//            $msg = 'customer_id:' . $this->customer_id . ' 获取调用量失败,请检查原因 response:' . json_encode(compact('response', 'api_url'));
//            throw new CustomException($msg);
//        }
//    }
//
//
//    /**
//     * 校验客户是否满足模板的需求
//     * @return mixed|void
//     */
//    public function _validateCustomer()
//    {
//        // 如果账号的数量大于1 则不再适用于现在
//        MongoBillMonth::where([
//                                  "customer_id" => $this->customer_id,
//                                  "month" => date("Ym", strtotime("fist day of last month"))
//                              ])->get()
//            ->each(function($item){
//                // 获取当前的算法
//                if (!$this->_determineIfSectionIdCommonByNumberFixed($item)) {
//                    throw new CustomException("特快含有非固定单价不区分运营商的计费配置,所以不再适用于当前的模板");
//                }
//            });
//    }
//
//    /**
//     * 计费片段是否是通用固定价格模式
//     * @param $section_item
//     * @return bool
//     */
//    private function _determineIfSectionIdCommonByNumberFixed($section_item): bool
//    {
//        // 计费方式(时间用量)
//        $fee_method = $section_item->section_source["fee_method"];
//
//        // 用量计费规则
//        $fee_amount_rule = $section_item->section_source["fee_amount_rule"];
//
//        // 模式(标准1 运营商2)
//        $fee_price_rule = $section_item->section_source["fee_price_rule"];
//
//        return $fee_method == 2 && $fee_amount_rule == 1 && $fee_price_rule != 2;
//    }
}