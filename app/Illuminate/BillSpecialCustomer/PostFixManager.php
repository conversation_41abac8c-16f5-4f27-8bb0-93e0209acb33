<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/29 0029
 * Time: 11:05
 */

namespace App\Illuminate\BillSpecialCustomer;


class PostFixManager
{
    public function driver($driver_name)
    {
        $driver_method = 'createCustomer' . ucfirst($driver_name) . 'ModuleDriver';

        if (method_exists($this, $driver_method)) {
            return $this->{$driver_method}();
        } else {
            throw new \Exception('抱歉，特殊客户账单信息服务目前不支持' . ucfirst($driver_name) . 'Driver类型的后置账单模块');
        }
    }

    /**
     * C20181101SQ9IIO 量化派
     **/
    public function createCustomerC20181101SQ9IIOModuleDriver()
    {
        return new ExcelC20181101SQ9IIODriver();
    }

    /**
     * C20181101QU9C0N 人人贷
     **/
    private function createCustomerC20181101QU9C0NModuleDriver()
    {
        return new ExcelC20181101QU9C0NDriver();
    }

    //骑呗
    private function createCustomerC20181101G8K9DAModuleDriver()
    {
        return new ExcelC20181101G8K9DADriver();
    }

    //	极速云
    private function createCustomerC201903077LXWJMModuleDriver()
    {
        return new ExcelC201903077LXWJMDriver();
    }

    //亿数科技
    private function createCustomerC2018110146N4NVModuleDriver()
    {
        return new ExcelC2018110146N4NVDriver;
    }

    //读秒
    private function createCustomerC201811012TPQQ8ModuleDriver()
    {
        return new ExcelC201811012TPQQ8Driver();
    }

    //杭银消金
    private function createCustomerC20190801R97IILModuleDriver()
    {
        return new ExcelC20190801R97IILDriver();
    }

    //广汽租赁
    private function createCustomerC20190225VJOUQAModuleDriver()
    {
        return new ExcelC20190225VJOUQADriver();
    }

    //小象优品
    private function createCustomerC201908074WYCOAModuleDriver()
    {
        return new ExcelC201908074WYCOADriver();
    }

}