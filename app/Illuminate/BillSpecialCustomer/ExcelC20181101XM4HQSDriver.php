<?php

namespace App\Illuminate\BillSpecialCustomer;

use App\Models\{
    Account, AccountProduct, MongoBillMonth, MongoLog, Product
};
use App\Support\CustomException;
use App\TraitUpgrade\CurlTrait;

class ExcelC20181101XM4HQSDriver implements ExcelFactory
{
    use CurlTrait;
    /** @var string 快捷版产品Key */
    private $product_key_shortcut = '99bbdb8426f8b4e8d0cc3ebd92484590';

    /** @var string 客户ID */
    private $customer_id = 'C20181101XM4HQS';

    private $year_has_number = ********;

    /** @var string 秒配单号版 */
    private $product_key_pei_dan = "fb53fddb7157dd76fd1bb656df4980a3";

    /** @var int 配配单号版本的产品ID */
    private $product_id_pei_dan = 601;

    /** @var string 配产品开始计费的日期 */
    private $pei_begin_day = "********";

    /**
     * 萨摩耶客户的金额
     * @return array
     * @throws \Exception
     */
    public function _getMoneyInfo(): array
    {
        // 校验
        $this->_validateCustomer();

        return [
            "pei_total" => $this->_getPeiTotalInfo(),  // 秒配结算单
            "pei_details" => $this->_getPeiDetailInfo(),
            "shortcut_total" => $this->_getShortcutTotalInfo(), // 快捷版结算单
            "shortcut_details" => $this->_getShortcutMoneyDetails(), // 快捷版明细
        ];
    }

    /**
     * 获取秒配的详细信息
     * @return array
     * @throws \Exception
     */
    private function _getPeiDetailInfo(): array
    {
        // 获取每天的掉用量
        $list_days_numbers = $this->_getDayNumberForPei();

        // 整合
        $total = $list_days_numbers["matching"]["total"]["itag"];
        return array_reduce($list_days_numbers["matching"]["items"], function($carry, $item_account){
            array_walk($item_account, function($item_day, $day) use(&$carry) {
                // 如果之前是没有这一天的
                if (!array_key_exists($day, $carry)) {
                    $carry["details"][$day] = $item_day["itag"];
                } else {
                    $carry["details"][$day] += $item_day["itag"];
                }
            });

            return $carry;
        }, [
            "total" => $total,
            "details" => [], // 每天的掉用量
        ]);

    }

    /**
     * 配产品获取每天的掉用量
     * @return array
     * @throws \Exception
     */
    private function _getDayNumberForPei(): array
    {
        // 条件
        $params = $this->_genParamsForPeiDay();
        $api_url = env("LIST_STAT_DAY_API");

        // 获取信息
        return $this->_getInvokedNumber($params, $api_url);
    }

    /**
     * 条件
     * @return array
     */
    private function _genParamsForPeiDay(): array
    {
        $apikey = $this->_getProductApikey([$this->product_id_pei_dan]);
        $key = $this->product_key_pei_dan;
        $start_time = $this->pei_begin_day;
        $end_time = date("Ymd", strtotime("last day of -1 months"));
        return compact("apikey", "key", "start_time", "end_time");
    }

    /**
     * 秒配结算单
     * @return array
     * @throws \Exception
     */
    private function _getPeiTotalInfo(): array
    {
        $year_has_number = $this->year_has_number; // 固定的数值
        $number_last_before_this_month = $this->_getPeiLastNumberBeforeThisMonth(); // 账单月之前剩下的掉用量
        $details = $this->_getDetailsForThisMonth(); // 账单月的掉用量情况
        $number_total_this_month = $details["number"]; // 账单月有效掉用量之和
        $number_last_now = $number_last_before_this_month - $number_total_this_month; // 现在剩余的掉用量
        return compact("year_has_number", "number_last_before_this_month", "details", "number_total_this_month", "number_last_now");
    }

    /**
     * 账单月的调用情况
     * @return array
     * @throws \Exception
     */
    private function _getDetailsForThisMonth(): array
    {
        // 条件
        list($params, $month_begin, $month_end) = $this->_genParamForPeiThisMonth();

        // 掉用量
        $number = $this->_getPeiInvokedWith($params);
        return compact("month_end", "month_begin", "number");
    }

    /**
     * 账单月的条件
     * @return array
     */
    private function _genParamForPeiThisMonth(): array
    {
        $apikey = $this->_getProductApikey([$this->product_id_pei_dan]);
        $key = $this->product_key_pei_dan;
        $start_time = date("Ymd", strtotime("first day of last month"));
        $end_time = date("Ymd", strtotime("last day of last month"));
        return [
            compact("apikey", "key", "start_time", "end_time"),
            $start_time,
            $end_time
        ];
    }

    /**
     * 账单月之前的剩余的掉用量
     * @return int
     * @throws \Exception
     */
    private function _getPeiLastNumberBeforeThisMonth(): int
    {
        // 账单月之前的掉用量是多少
        $number_before_this_month = $this->_getPeiInvoked();

        // 剩余掉用量
        return $this->year_has_number - $number_before_this_month;
    }

    /**
     * 获取秒配的掉用量
     * @return array
     * @throws \Exception
     */
    private function _getPeiInvoked(): int
    {
        // 条件
        $params = $this->genParamsForPeiBeforeThisMonth();

        // 获取调用量
        return $this->_getPeiInvokedWith($params);
    }

    /**
     * 配的用量
     * @param array $params
     * @return int
     * @throws \Exception
     */
    private function _getPeiInvokedWith(array $params): int
    {
        $api_url = env('LIST_STAT_COMMON_API');

        // 获取掉用量信息
        $list_stat = $this->_getInvokedNumber($params, $api_url);

        $total = $list_stat["matching"]["total"] ?? [];
        return $total["itag"] ?? 0;
    }

    /**
     * 配账单月之前的条件
     */
    private function genParamsForPeiBeforeThisMonth(): array
    {
        $apikey = $this->_getProductApikey([$this->product_id_pei_dan]);
        $key = $this->product_key_pei_dan;
        $start_time = $this->pei_begin_day;
        $end_time = date("Ymd", strtotime("last day of -2 months"));
        return compact("apikey", "key", "start_time", "end_time");
    }


    /**
     * 快捷版明细
     * @return array
     * @throws \Exception
     */
    private function _getShortcutMoneyDetails(): array
    {
        // 有效字段量每天的调用情况
        $list_sub_field_numbers = $this->_getDaySubFieldNumber();

        // 有销量每天的调用情况
        $list_210_numbers = $this->_getDay210ValidNumber();

        // 整合
        return $this->_aggregateSubAnd210ForShortcut($list_sub_field_numbers, $list_210_numbers);
    }

    /**
     *  将210和子产品天的点用量合并到一起
     * @param array $list_sub_field_numbers
     * @param array $list_210_numbers
     * @return array
     */
    private function _aggregateSubAnd210ForShortcut(array $list_sub_field_numbers, array $list_210_numbers): array
    {
        // 获取两者有掉用量的日期
        $list_days_sub = array_keys($list_sub_field_numbers);
        $list_days_210 = array_keys($list_210_numbers);
        $list_days = array_unique(array_merge($list_days_210, $list_days_sub));
        sort($list_days);

        return array_reduce($list_days, function ($carry, $day) use ($list_sub_field_numbers, $list_210_numbers) {
            // 总量变化
            $carry["total"]["valid_number"] += $list_210_numbers[$day] ?? 0;
            $carry["total"]["valid_field_number"] += $list_sub_field_numbers[$day] ?? 0;

            // 日调用量赋值
            $carry["details"][$day]["valid_number"] = $list_210_numbers[$day] ?? 0;
            $carry["details"][$day]["valid_field_number"] = $list_sub_field_numbers[$day] ?? 0;
            return $carry;
        }, [
            "total" => [
                "valid_number" => 0, // 有效量
                "valid_field_number" => 0, // 有效字段量
            ],
            "details" => []
        ]);
    }

    /**
     * 获取210产品每日直属的有效掉用量
     * @return array
     * @throws \Exception
     */
    private function _getDay210ValidNumber(): array
    {
        // 每天的调用情况
        $list_source_day_numbers = $this->_getDay210Info();

        // 获取每日的有效掉用量
        return $this->_getDayValidNumber($list_source_day_numbers);
    }

    /**
     * 获取210产品的每天的掉用量
     * @return array
     * @throws \Exception
     */
    private function _getDay210Info(): array
    {
        // 生成参数
        $params = $this->_genParamsForDaySubFiledInfo("210");

        return $this->_getInvokedNumber($params);
    }


    /**
     * 子产品的每天的调用量
     * @return array
     * @throws \Exception
     */
    private function _getDaySubFieldNumber(): array
    {
        // 每天的调用情况
        $list_source_day_numbers = $this->_getDaySubFiledInfo();

        // 获取每日的有效掉用量
        return $this->_getDayValidNumber($list_source_day_numbers);
    }

    /**
     * 获取每日的有效掉用量
     * @param array $list_source_day_numbers
     * @return array
     */
    private function _getDayValidNumber(array $list_source_day_numbers): array
    {
        // 整合
        $list_container = [];
        array_walk($list_source_day_numbers, function ($item_product) use (&$list_container) {
            array_walk($item_product["items"], function ($item_product_account) use (&$list_container) {
                array_walk($item_product_account, function ($item) use (&$list_container) {

                    // 累计快捷版的属性
                    $list_container = $this->_tidyShortcutAttr($item, $list_container);
                });
            });
        });

        // 每天的有效字段量
        return $this->_computedValidNumberForShortcut($list_container);
    }

    /**
     * 计算每个单元的有效掉用量
     * @param array $item
     * @return mixed
     */
    private function _computedItemValidForShortcut(array $item)
    {
        list($yd_succ, $dx_succ, $lt_succ, $lt_cache, $yd_cache, $dx_cache) = [
            $item['yidong_success'] ?? 0,
            $item['dianxin_success'] ?? 0,
            $item['liantong_success'] ?? 0,
            $item['liantong_cache'] ?? 0,
            $item['yidong_cache'] ?? 0,
            $item['dianxin_cache'] ?? 0
        ];

        return $yd_succ + $dx_succ + $lt_succ + $lt_cache + $yd_cache + $dx_cache;
    }

    /**
     * 计算快捷版本的有效调用量
     * @param array $list_container
     * @return array
     */
    private function _computedValidNumberForShortcut(array $list_container): array
    {
        return array_map(function ($item) {
            return $this->_computedItemValidForShortcut($item);
        }, $list_container);
    }

    /**
     * 累计快捷版的属性
     * @param array $item_stat
     * @param $list_container
     * @return array
     */
    private function _tidyShortcutAttr(array $item_stat, $list_container): array
    {
        $day = $item_stat['amount_date'];
        if (!array_key_exists($day, $list_container)) {
            $list_container[$day] = [
                "yidong_success" => $item_stat["yidong_success"] ?? 0,
                "liantong_success" => $item_stat["liantong_success"] ?? 0,
                "dianxin_success" => $item_stat["dianxin_success"] ?? 0,
                "liantong_cache" => $item_stat["liantong_cache"] ?? 0,
                "yidong_cache" => $item_stat["yidong_cache"] ?? 0,
                "dianxin_cache" => $item_stat["dianxin_cache"] ?? 0
            ];
            return $list_container;
        }
        $list_container[$day]["yidong_success"] += $item_stat["yidong_success"];
        $list_container[$day]["liantong_success"] += $item_stat["liantong_success"];
        $list_container[$day]["dianxin_success"] += $item_stat["dianxin_success"];
        $list_container[$day]["liantong_cache"] += $item_stat["liantong_cache"];
        $list_container[$day]["yidong_cache"] += $item_stat["yidong_cache"];
        $list_container[$day]["dianxin_cache"] += $item_stat["dianxin_cache"];
        return $list_container;
    }

    /**
     * 获取快捷版子产品的日调量
     * @return array
     * @throws \Exception
     */
    private function _getDaySubFiledInfo()
    {
        // 生成参数
        $params = $this->_genParamsForDaySubFiledInfo();

        return $this->_getInvokedNumber($params);
    }

    /**
     * 获取调用量
     * @param array $params
     * @param null $api_url
     * @return array
     * @throws \Exception
     */
    private function _getInvokedNumber(array $params, $api_url = null): array
    {
        $api_url = $api_url ?? env('LIST_STAT_DAY_API');

        $i = 0;
        while (true) {
            $response = $this->post($api_url, $params);

            if ($i > 2 || $response['status'] == 0) {
                break;
            }
            $i++;
        }

        // 校验返回&&记录日志
        $this->logInvokedApi($response, $params, $api_url);

        return $response['list_stat'] ?? [];
    }

    /**
     * 记录日志
     * @param array $response
     * @param array $params
     * @param string $api_url
     * @throws \Exception
     */
    private function logInvokedApi(array $response, array $params, string $api_url)
    {
        $customer_id = $this->customer_id;
        $intro = "特殊客户获取调用量";
        $msg = '场景:调用api:' . $api_url . '接口获取产品的调用情况';
        MongoLog::create(compact('uuid', 'msg', 'response', 'params', 'api_url', 'intro', 'customer_id'));

        // 校验
        if ($response['status'] != 0) {
            $msg = 'customer_id:' . $this->customer_id . ' 获取调用量失败,请检查原因 response:' . json_encode(compact('response', 'api_url'));
            throw new CustomException($msg);
        }
    }

    /**
     * 子产品生成参数
     * @param string $product_id
     * @return array
     */
    private function _genParamsForDaySubFiledInfo(string $product_id = ""): array
    {
        return $this->_genParamsForShortcut($product_id);
    }


    /**
     * 快捷版本生成日调用量参数
     * @param string $product_id
     * @return array
     */
    private function _genParamsForShortcut(string $product_id = ""): array
    {
        $apikey = $this->_getShortcutApikey();
        $start_time = date("Ymd", strtotime("first day of last month"));
        $end_time = date("Ymd", strtotime("last day of last month"));
        $key = $this->product_key_shortcut;
        $product_id = $product_id ? (int)$product_id : "";

        return compact('apikey', 'start_time', "end_time", "key", "product_id");
    }

    /**
     * 获取快捷版的账号apikey
     * @return array
     */
    private function _getShortcutApikey(): array
    {
        $list_shortcut_ids = $this->_getShortcutProductIds();
        return $this->_getProductApikey($list_shortcut_ids);
    }

    /**
     * @param array $product_ids
     * @return array
     */
    private function _getProductApikey(array $product_ids): array
    {

        $list_account_prodcuts = AccountProduct::where([
            "product_id" => [
                '$in' => $product_ids
            ]
        ])->distinct("account_id")
            ->select(["account_id"])
            ->get()
            ->pluck("account_id")
            ->toArray();

        return Account::where([
            'customer_id' => $this->customer_id
        ])->select(["account_id", "apikey"])
            ->get()
            ->reduce(function ($carry, $item) use ($list_account_prodcuts) {
                if (in_array($item->account_id, $list_account_prodcuts)) {
                    $carry[] = $item->apikey;
                }
                return $carry;
            }, []);
    }

    /**
     * 校验客户是否满足模板的需求
     * @return mixed|void
     * @throws CustomException
     */
    public function _validateCustomer()
    {
        // 在2020-01-01之后需要重新厘定萨摩耶的模板
        if (date('Ymd') >= "********") {
            throw new CustomException("在2020-01-01之后需要重新厘定萨摩耶的模板");
        }
    }

    /**
     * 快捷版的账单明细
     * @return array
     */
    private function _getShortcutTotalInfo(): array
    {
        $product_id = [
            '$in' => $this->_getShortcutProductIds()
        ];
        $customer_id = $this->customer_id;
        $month_money = date("Ym", strtotime("first day of last month"));

        return MongoBillMonth::where(compact('product_id', 'customer_id'))
            ->get()
            ->reduce(function ($carry, $item) use ($month_money) {
                // 如果是账单月
                if ($month_money == $item->month) {
                    $carry["details"]["section_invoked_number"] += $item->section_invoked_number;
                    $carry["details"]["price"] = $item->section_source["fee_price"];
                    $carry["details"]["consume"] += $item->money;

                } else {
                    // 之前的月份
                    $carry["money_before_this_month"] += $item->money;
                }

                $carry["money_consume_history"] += $item->money;
                return $carry;
            }, [
                "money_before_this_month" => 0,  // 账单月之前的消费金额
                "details" => [
                    "recharge" => "", // 本月充值金额， 这里需要置空字符串
                    "section_invoked_number" => 0, // 调用量
                    "consume" => 0, // 本月消费金额
                    "price" => "",  // 依最新的单价
                    "month_begin" => date('Ymd', strtotime('first day of last month')),
                    "month_end" => date('Ymd', strtotime('last day of last month')),
                ],
                "money_recharge_history" => "", // 置空 历史充值金额之和
                "money_consume_history" => 0, // 历史消费金额之和
                "money_last_now" => "", // 置空
            ]);

    }

    /**
     * 获取快捷版ID
     * @return array
     */
    private function _getShortcutProductIds(): array
    {
        static $list_shortcut_ids;
        if ($list_shortcut_ids) {
            return $list_shortcut_ids;
        }

        return $list_shortcut_ids = Product::where(['product_key' => $this->product_key_shortcut])
            ->select('product_id')
            ->get()
            ->pluck('product_id')
            ->all();
    }
}