<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/31 0031
 * Time: 13:58
 */

namespace App\Illuminate\BillSpecialCustomer;


use App\Models\Account;
use App\Models\MongoBillMonth;
use App\Models\MongoStatis;

class ExcelC20181101QU9C0NDriver
{
	//合同约定的邦秒配总调用量
	private $pei_total = ********;
	
	public function _getMoneyInfo($data): array
	{
		//人人贷账号
		$account1_data                          = array_map(function ($item) {
			return $item->toArray();
		}, $data['****************']['list_bills']);
		$account1_data                          = array_column($account1_data, null, 'product_id');
		$data['****************']['list_bills'] = $account1_data;
		
		//人人贷单独账号
		$account2_data = array_map(function ($item) {
			return $item->toArray();
		}, $data['****************']['list_bills']);
		
		//邦信分详单版结算单
		$details_101                     = $account1_data[101];
		$list_distribute                 = array_shift($details_101['details']['list_distribute']);
		$details_101['details']['price'] = $list_distribute['price'];
		
		//获取每日的调用量
		$apikey                             = Account::where('account_id', '****************')
													 ->value('apikey');
		$start_date                         = strval($account1_data[101]['section_begin']);
		$end_date                           = strval($account1_data[101]['section_end']);
		$total                              = 0;
		$day_list                           = MongoStatis::raw(function ($collection) use ($apikey, $start_date, $end_date) {
			return $collection->aggregate([
				[
					'$match' => [
						'product_id'  => 101,
						'apikey'      => $apikey,
						'amount_date' => [
							'$lte' => $end_date,
							'$gte' => $start_date,
						],
					],
				],
				[
					'$group' => [
						'_id' => '$amount_date',
						'sum' => ['$sum' => '$stat_data.success'],
					],
				],
			]);
		})
														 ->map(function ($item) use (&$total) {
															 $total += $item['sum'];
			
															 return [
																 'success'     => $item['sum'],
																 'amount_date' => $item['_id'],
															 ];
														 })
														 ->all();
		$day_list                           = array_column($day_list, 'success', 'amount_date');
		$details_101['details']['day_list'] = $day_list;
		
		//邦秒配结算单
		
		$details_601 = $this->tidyPeiData($account2_data);
		
		
		$data['****************']['list_bills'][101] = $details_101;
		$data['****************']['list_bills'][0]   = $details_601;
		$data['****************']['list_bills']      = array_column($data['****************']['list_bills'], null, 'product_id');
		$data['****************']['list_bills']      = array_column($data['****************']['list_bills'], null, 'product_id');
		
		return $data;
	}
	
	/**
	 * 整理邦秒配的明细
	 *
	 * @access protected
	 *
	 * @return array
	 **/
	protected function tidyPeiData($account2_data)
	{
		$details_601 = $account2_data[0];
		
		
		$bill_start_datetime = strtotime('first day of last month');
		$bill_end_datetime   = strtotime('last day of last month');
		$bill_month          = date('Ym', $bill_start_datetime);
		
		
		$bill_month_start_residue = $this->pei_total;
		$bill_month_consume       = 0;
		
		
		MongoBillMonth::where('month', '<=', date('Ym', $bill_start_datetime))
					  ->where('account_id', '****************')
					  ->where('product_id', 601)
					  ->get()
					  ->map(function ($item) use (&$bill_month_consume, &$bill_month_start_residue, $bill_month) {
						  $tmp    = $item->toArray();
						  $number = (array_key_exists('section_number', $tmp) && array_key_exists('fee_number', $tmp['section_number'])) ? $tmp['section_number']['fee_number'] : 0;
						  if ($tmp['month'] == $bill_month) {
							  $bill_month_consume += $number;
						  } else if ($tmp['month'] < $bill_month) {
							  $bill_month_start_residue -= $number;
						  }
					  });
		
		$bill_month_residue = $bill_month_start_residue - $bill_month_consume;
		
		
		$details_601['details']['use']        = $bill_month_consume;
		$details_601['details']['super']      = $bill_month_residue;
		$details_601['details']['prev_month'] = $bill_month_start_residue;
		$details_601['details']['total']      = $this->pei_total;
		
		//获取每日的调用量
		$start_date                         = strval($account2_data[0]['section_begin']);
		$end_date                           = strval($account2_data[0]['section_end']);
		$total                              = 0;
		$apikey                             = Account::where('account_id', '****************')
													 ->value('apikey');
		$day_list                           = MongoStatis::raw(function ($collection) use ($apikey, $start_date, $end_date) {
			return $collection->aggregate([
				[
					'$match' => [
						'product_id'  => 601,
						'apikey'      => $apikey,
						'amount_date' => [
							'$lte' => $end_date,
							'$gte' => $start_date,
						],
					],
				],
				[
					'$group' => [
						'_id' => '$amount_date',
						'sum' => ['$sum' => '$stat_data.itag_valid'],
					],
				],
			]);
		})
														 ->map(function ($item) use (&$total) {
															 $total += $item['sum'];
			
															 return [
																 'success'     => $item['sum'],
																 'amount_date' => $item['_id'],
															 ];
														 })
														 ->all();
		$day_list                           = array_column($day_list, 'success', 'amount_date');
		$details_601['details']['day_list'] = $day_list;
		
		//历史消耗
		$month                                                      = date('Ym', strtotime('first day of last month'));
		$prev_month_history_consume_money                           = MongoBillMonth::where('month', '<', $month)
																					->where('product_id', '=', 601)
																					->where('account_id', '=', '****************')
																					->sum('money');
		$details_601['details']['prev_month_history_consume_money'] = $prev_month_history_consume_money;
		
		return $details_601;
	}
	
	public function _validateCustomer()
	{
		// TODO: Implement _validateCustomer() method.
	}
	
}