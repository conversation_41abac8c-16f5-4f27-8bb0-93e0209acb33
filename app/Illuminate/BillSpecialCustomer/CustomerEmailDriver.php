<?php


namespace App\Illuminate\BillSpecialCustomer;


use App\Support\CustomException;
use Maatwebsite\Excel\Facades\Excel;

class CustomerEmailDriver implements EmailFactory
{
    private $customer_id;

    /**
     * @param mixed $customer_id
     */
    public function setCustomerId($customer_id)
    {
        $this->customer_id = $customer_id;
    }

    /**
     * @return mixed|void
     * @throws CustomException
     */
    public function _validateParams()
    {
        if (!$this->customer_id) {
            throw new CustomException("特殊客户邮件发送服务需要传递customer_id参数");
        }

        if (!in_array($this->customer_id, config("params.list_special_customer")) && !in_array(
                $this->customer_id,
                config(
                    'params.postfix_special_customer'
                )
            )
        ) {
            throw new CustomException("customer_id : " . $this->customer_id . "是正常的客户, 但是却进入了特殊客户发送邮件的模式");
        }
    }

    /**
     * @param string $customer_id
     * @param array  $params
     *
     * @return mixed|void
     * @throws CustomException
     */
    public function _genExcel(string $customer_id, array $params)
    {
        // 设置实列变量
        $this->setCustomerId($customer_id);

        // 检查条件
        $this->_validateParams();

        // 生成excel
        $this->_genExcelDo($params);
    }

    /**
     * 生成excel
     *
     * @param array $params
     *
     * @throws CustomException
     */
    private function _genExcelDo(array $params)
    {
        list($customer_info, $history, $list_product_bill_alo, $file_name) = $params;
        $driver = $this->_genDriver();
        if (!class_exists($driver)) {
            throw new CustomException("目前不支持" . $this->customer_id . "生成excel");
        }
        Excel::store(new $driver($customer_info, $history, $list_product_bill_alo), $file_name);
    }

    /**
     * excel 驱动
     * @return string
     */
    private function _genDriver(): string
    {
        $prefix = "App\Exports\SpecialCustomerExports\\" . $this->customer_id . "\\";
        return $prefix . $this->customer_id . "EmailBillV2";
    }
}