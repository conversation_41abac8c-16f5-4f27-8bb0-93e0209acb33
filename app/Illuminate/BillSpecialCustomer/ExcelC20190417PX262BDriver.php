<?php

namespace App\Illuminate\BillSpecialCustomer;

use App\Models\{
    CustomerExpend, MoneyRecharge, MongoBillMonth
};
use App\TraitUpgrade\CurlTrait;

class ExcelC20190417PX262BDriver implements ExcelFactory
{
    use CurlTrait;

    /** @var string 客户ID */
    private $customer_id = 'C20190417PX262B';

    /**
     * 萨摩耶客户的金额
     * @return array
     * @throws \Exception
     */
    public function _getMoneyInfo(): array
    {
        // 校验
        $this->_validateCustomer();

        // 消费信息
        return $this->_getMoneyInfoDo();
    }

    /**
     *
     * @return array
     */
    private function _getMoneyInfoDo(): array
    {
        list($money_special_of_this_month, $money_special_last_before_this_month) = $this->_getSpeicalMoneyInfo();

        // 账单月之前的剩余金额
        $money_last_before_this_month = $this->_getMoneyBeforeThisMonth($money_special_last_before_this_month);

        // 本月消费的金额
        $money_detail_this_month = $this->_getMoneyOfThisMonth();

        // 未消费金额
        $list_aggregate_money = $this->aggregateMoneyForTotalList($money_last_before_this_month, $money_detail_this_month, $money_special_of_this_month);

        return compact('money_last_before_this_month', 'money_detail_this_month', 'list_aggregate_money');
    }

    /**
     * 总结
     * @param array $money_last_before_this_month 账单月之前的金额情况
     * @param array $money_detail_this_month 本月的消费情况
     * @param array $money_special_of_this_month 本月特殊费用
     * @return array
     */
    private function aggregateMoneyForTotalList(array $money_last_before_this_month, array $money_detail_this_month, array $money_special_of_this_month): array
    {
        // 账单月之前的剩余金额 账单月之前充值的金额  账单月之前消费金额
        list(
            $money_recharge_before_this_month,  // 账单月之前充值的金额
            $money_consume_before_this_month, // 账单月之前消费的金额
            $money_consume_of_this_month, // 账单月的消费金额
            $money_recharge_of_this_month, // 账单月的充值金额
            ) = [
            $money_last_before_this_month['money_recharge'],
            $money_last_before_this_month['money_consume'],
            $money_detail_this_month['money_consume_of_this_month'],
            $money_detail_this_month['money_recharge_of_this_month'],
        ];

        // 历史充值
        $money_recharge_aggregate = $money_recharge_before_this_month + $money_recharge_of_this_month;

        // 历史消费金额
        $money_consume_aggregate = $money_consume_before_this_month + $money_consume_of_this_month;

        //  处理特殊的金额
        foreach ($money_special_of_this_month as $item_special) {
            $money_recharge_aggregate += $item_special['recharge'];
            $money_consume_aggregate += $item_special['consume'];
        }

        // 总的剩余金额
        $money_residue_now = $money_recharge_aggregate - $money_consume_aggregate;

        return compact('money_recharge_aggregate', 'money_consume_aggregate', 'money_residue_now');
    }

    /**
     * 账单月的消费和重置
     * @return array
     */
    private function _getMoneyOfThisMonth(): array
    {
        // 账单月充值金额
        $money_recharge_of_this_month = $this->_getRechargeMonthOfThisMonth();

        // 账单月消费
        $money_consume_of_this_month = $this->_getConsumeOfThisMonth();

        // 账单月的调用量
        $number = $this->getNumberOfThisMonth();

        // 本月开始和结束的时间
        $month_begin = date('Ym01', strtotime('-1 month'));
        $month_end = date('Ymd', strtotime('last day of last month'));

        return compact('money_consume_of_this_month', 'money_recharge_of_this_month', 'month_begin', 'month_end', 'number');
    }

    /**
     * 账单月的调用量
     * @return int
     */
    private function getNumberOfThisMonth(): int
    {
        $customer_id = $this->customer_id;
        $month = [
            '$gte' => date('Ym', strtotime('last day of last month'))
        ];

        return MongoBillMonth::where(compact('customer_id', 'month'))
            ->sum("section_invoked_number");
    }

    /**
     * 今年消费金额每月的分布
     * @return float
     */
    private function _getConsumeOfThisMonth(): float
    {
        $customer_id = $this->customer_id;
        $month = [
            '$gte' => date('Ym', strtotime('last day of last month'))
        ];

        return MongoBillMonth::sumMoney(compact('customer_id', 'month'));
    }

    /**
     * 账单月充值金额
     * @return array
     */
    private function _getRechargeMonthOfThisMonth(): float
    {
        $where = [
            ['customer_id', $this->customer_id],
            ['remit_date', '>=', strtotime(date('Y-m-01', strtotime('-1 month')))],
            ['status', 3]
        ];

        return MoneyRecharge::getRechargeMoney($where);
    }

    /**
     * 账单月之前的剩余金额
     * @param array $money_special_last_before_this_month
     * @return array
     */
    private function _getMoneyBeforeThisMonth(array $money_special_last_before_this_month): array
    {
        // 账单月之前的充值金额
        $money_recharge = $this->_getRechargeBeforeThisMonth();

        // 截至到去年的历史消费金额
        $money_consume = $this->getConsumeBeforeThisMonth();

        //  处理特殊的金额
        foreach ($money_special_last_before_this_month as $item_special) {
            $money_recharge += $item_special['recharge'];
            $money_consume += $item_special['consume'];
        }

        // 去年剩余的金额
        $money_residue = $money_recharge - $money_consume;
        return compact('money_residue', 'money_consume', 'money_recharge');
    }

    /**
     * 获取客户截至到上上个月的消费金额
     * @return float
     */
    private function getConsumeBeforeThisMonth(): float
    {
        $month = ['$lt' => date('Ym', strtotime('last day of last month'))];
        $customer_id = $this->customer_id;
        return MongoBillMonth::sumMoney(compact('month', 'customer_id'));
    }

    /**
     * 截止到上上个月的历史充值金until
     * @return float
     */
    private function _getRechargeBeforeThisMonth(): float
    {
        // 到去年为止
        $timestamp_this_month = strtotime(date('Y-m-01', strtotime('-1 month')));
        $where = [
            ['remit_date', '<', $timestamp_this_month],
            ['status', 3],
            ['customer_id', $this->customer_id]
        ];

        return MoneyRecharge::getRechargeMoney($where);
    }

    /**
     * 获取客户特殊的充值或者赠送的情况
     * @return array
     */
    private function _getSpeicalMoneyInfo(): array
    {
        // 历史特殊费用
        $money_special_all_history = CustomerExpend::where(['customer_id' => $this->customer_id])
            ->get()
            ->reduce(function ($carry, $item) {

                $carry[] = [
                    'month' => formatMonth($item->start_date),
                    'title' => $item->start_date . "月" . $item->name,
                    'recharge' => ($item->type==1) ? $item->money : 0,
                    'consume' => ($item->type==2) ? $item->money : 0,
                ];

                return $carry;
            }, []);

        //    本月特殊的费用
        $money_special_of_this_month = array_filter($money_special_all_history, function ($item) {
            return $item['month'] == date('Ym', strtotime('first day of last month'));
        });

        // 账单月之前特殊的消费
        $money_special_last_before_this_month = array_filter($money_special_all_history, function ($item) {
            return $item['month'] < date('Ym', strtotime('first day of last month'));
        });

        return [$money_special_of_this_month, $money_special_last_before_this_month];
    }


    /**
     * 校验客户是否满足模板的需求
     * @return mixed|void
     */
    public function _validateCustomer()
    {
        // 这个模板没有过期的时候,

    }
}