<?php

namespace App\Illuminate\BillSpecialCustomer;

use App\Models\{
    Customer, CustomerExpend, MoneyRecharge, MongoBillMonth
};
use App\Support\CustomException;
use App\TraitUpgrade\CurlTrait;

class ExcelC20181101YRXUX2Driver implements ExcelFactory
{
    use CurlTrait;

    /** @var string 客户ID */
    private $customer_id = 'C20181101YRXUX2';

    /** @var int 目前的总余额 */
    private $money_last_now = 0;

    /** @var string 公司名称固定展示 */
    private $company = "北京羽乐创新科技有限公司";

    /**
     * 萨摩耶客户的金额
     * @return array
     * @throws \Exception
     */
    public function _getMoneyInfo(): array
    {
        // 校验
        $this->_validateCustomer();

        // 消费信息
        return $this->_getMoneyInfoDo();
    }

    /**
     *
     * @return array
     */
    private function _getMoneyInfoDo(): array
    {
        // 年前的剩余金额
        $money_last_before_this_year = $this->_getMoneyBeforeThisYear();

        // 各个月的消费
        $money_detail_this_month = $this->_getMoneyOfThisYear($money_last_before_this_year);

        // 基本信息
        $base_info = [
            "money_last_now" => $this->money_last_now,
            "company"        => $this->company,
            "customer_name"  => $this->_getCustomerName(),
        ];

        return compact("money_last_before_this_year", "money_detail_this_month", "base_info");
    }

    /**
     * @return string
     */
    private function _getCustomerName(): string
    {
        return Customer::getOneItemByCondition(['customer_id' => $this->customer_id])
            ->company;
    }


    /**
     * 本年各个月的消费充值情况
     *
     * @param array $money_last_before_this_year
     *
     * @return array
     */
    private function _getMoneyOfThisYear(array $money_last_before_this_year): array
    {
        // 获取本年个月的调用量 消费情况
        $list_consume_month_this_year = $this->_getMoneyInfoGroupMonthForThisYear();

        // 各个月的充值情况
        $list_recharge_month_this_year = $this->_getRechargeGroupMonthOfThisYear();

        // 本年的月份列表
        $list_months = $this->_getMonthListForThisYear();

        // 组合
        return $this->_aggregateMoneyForThisYear($list_months, $list_consume_month_this_year, $list_recharge_month_this_year, $money_last_before_this_year);
    }

    /**
     * 组合出需要的结果
     *
     * @param array $list_months
     * @param array $list_consume_month_this_year
     * @param array $list_recharge_month_this_year
     * @param array $money_last_before_this_year
     *
     * @return array
     */
    private function _aggregateMoneyForThisYear(array $list_months, array $list_consume_month_this_year, array $list_recharge_month_this_year, array $money_last_before_this_year): array
    {
        $this->money_last_now = $money_last_before_this_year["money_residue"] ?? 0;

        return array_map(function ($month) use ($list_consume_month_this_year, $list_recharge_month_this_year) {
            $money_consume_month    = $list_consume_month_this_year[$month] ?? [];
            $price                  = $money_consume_month["fee_price"] ?? "缺少价格";
            $section_invoked_number = $money_consume_month["section_invoked_number"] ?? "缺少调用量字段";
            $money_consume          = $money_consume_month["consume_money"] ?? 0;
            $month_begin            = $money_consume_month["month_begin"] ?? "";
            $month_end              = $money_consume_month["month_end"] ?? "";
            $money_recharge         = $list_recharge_month_this_year[$month] ?? 0;

            // 不断更新剩下的金额
            $this->money_last_now = $money_last_now = $this->money_last_now - $money_consume + $money_recharge;

            $product_name = "邦信分详单版V1";

            // 现在剩余的金额
            return compact("month", "price", "section_invoked_number", "money_consume", "month_begin", "month_end", "money_recharge", "money_last_now", "product_name");
        }, $list_months);
    }

    /**
     * 获取月份列表
     * @return array
     */
    private function _getMonthListForThisYear(): array
    {
        $month_loop = date("Y01");

        // 容器
        $list_container = [];
        while (true) {
            if ($month_loop >= date("Ym")) {
                break;
            }
            array_push($list_container, $month_loop);
            $month_loop = date("Ym", strtotime("first day of next month", strtotime($month_loop."01")));
        }

        return $list_container;
    }


    /**
     * 今年各个月份充值的情况
     * @return array
     */
    private function _getRechargeGroupMonthOfThisYear(): array
    {
        // 到去年为止
        $timestamp_this_month = strtotime(date('Y-01-01'));
        $where                = [
            ['remit_date', '>=', $timestamp_this_month],
            ['status', 3],
            ['customer_id', $this->customer_id],
        ];

        $recharge = MoneyRecharge::getListByCondition($where, ["money", "remit_date"])
            ->reduce(function ($carry, $item) {
                $month = date("Ym", $item->remit_date);
                if (!array_key_exists($month, $carry)) {
                    $carry[$month] = $item->money;

                    return $carry;
                }

                $carry[$month] += $item->money;

                return $carry;
            }, []);
        //有个特殊计费配置,处理一下

        if (date('Y') == 2020) {
            $recharge[202004] = array_get($recharge, 202004, 0) + 4756.00;
        }

        return $recharge;
    }

    /**
     * 获取本年个月的调用量 消费情况
     * @return array
     */
    private function _getMoneyInfoGroupMonthForThisYear(): array
    {
        return MongoBillMonth::getListByCondition([
            "customer_id" => $this->customer_id,
            "month"       => ['$gte' => date("Y01")],
        ], ["month", "money", "section_invoked_number", "product_id", "section_source.fee_price", "section_source.remarks"])
            ->reduce(function ($carry, $item_bill) {
                // 如果之前没有本月的数据
                if (!array_key_exists($item_bill->month, $carry)) {
                    $carry[$item_bill->month] = [
                        "month"                  => $item_bill->month,
                        "consume_money"          => $item_bill->money,
                        "section_invoked_number" => $item_bill->section_invoked_number,
                        "fee_price"              => $item_bill->section_source["fee_price"],
                        "product_id"             => [$item_bill->product_id],
                        "month_begin"            => $item_bill->month."01",
                        "month_end"              => date("Ymt", strtotime($item_bill->month."01")),
                    ];

                    return $carry;
                }

                // 累加和充值
                $carry[$item_bill->month]["consume_money"]          += $item_bill->money;
                $carry[$item_bill->month]["section_invoked_number"] += $item_bill->section_invoked_number;
                $carry[$item_bill->month]["fee_price"]              = $item_bill->section_source["fee_price"];
                $carry[$item_bill->month]["product_id"][]           = $item_bill->product_id;

                return $carry;
            }, []);
    }


    /**
     * 账单月之前的剩余金额
     * @return array
     */
    private function _getMoneyBeforeThisYear(): array
    {
        // 今年之前的充值金额
        $money_recharge = $this->_getRechargeBeforeThisYear();

        // 截至到去年的历史消费金额
        $money_consume = $this->getConsumeBeforeThisYear();

        // 去年剩余的金额
        $money_residue = $money_recharge - $money_consume;

        // 说明
        $title = date("Y")."年期初余额/元";

        return compact('money_residue', 'money_consume', 'money_recharge', "title");
    }

    /**
     * 去年的消费
     * @return float
     */
    private function getConsumeBeforeThisYear(): float
    {
        $month       = ['$lt' => date('Y01')];
        $customer_id = $this->customer_id;

        return MongoBillMonth::sumMoney(compact('month', 'customer_id'));
    }

    /**
     * 今年之前的充值金额
     * @return float
     */
    private function _getRechargeBeforeThisYear(): float
    {
        // 到去年为止
        $timestamp_this_month = strtotime(date('Y-01-01'));
        $where                = [
            ['remit_date', '<', $timestamp_this_month],
            ['status', 3],
            ['customer_id', $this->customer_id],
        ];

        return MoneyRecharge::getRechargeMoney($where);
    }


    /**
     * 校验客户是否满足模板的需求
     * @return mixed|void
     * @throws CustomException
     */
    public function _validateCustomer()
    {
        // 如果存在多个计费产品 则这个模板不可以使用
        $product_counter = MongoBillMonth::where([
            "customer_id" => $this->customer_id,
            "month"       => date("Ym", strtotime("last day of last month")),
        ])->distinct("product_id")
            ->get()
            ->count();

        if ($product_counter > 1) {
            throw new CustomException("检测到账计费产品的数目大于1，所以卡牛模板失效");
        }

        // 如果有特殊消费的话 则这个模板不再适用
        if ($this->_determineHasSpecialMoney()) {
            throw new CustomException("检测到含有特殊消费，所以卡牛模板失效");
        }

    }

    /**
     * 是否有特殊消费
     * @return int
     */
    private function _determineHasSpecialMoney(): int
    {
        // 历史特殊费用
        return CustomerExpend::where(['customer_id' => $this->customer_id])
            ->where('id', '<>', 145)
            ->count();
    }
}