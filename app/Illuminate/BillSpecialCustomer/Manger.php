<?php


namespace App\Illuminate\BillSpecialCustomer;

class Manger implements Factory
{
    private $app;

    /**
     * Create a new Cache manager instance.
     *
     * @param  $app
     * @return void
     */
    public function __construct($app)
    {
        $this->app = $app;
    }

    /**
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    public function driver(string $driver_name)
    {
        return $this->resolve($driver_name);
    }


    /**
     * 解析
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    private function resolve(string $driver_name)
    {
        $driver_method = 'createCustomer' . ucfirst($driver_name) . 'ModuleDriver';

        if (method_exists($this, $driver_method)) {
            return $this->{$driver_method}();
        } else {
            throw new \Exception('抱歉，特殊客户账单信息服务目前不支持' . ucfirst($driver_name) . 'Driver类型的账单模块');
        }
    }

    /**
     * C20181101XM4HQS 萨摩耶
     */
    private function createCustomerC20181101XM4HQSModuleDriver()
    {
        return new ExcelC20181101XM4HQSDriver();
    }

    /**
     * C20181101XM4HQS 恒信永利
     */
    private function createCustomerC2019032668WHR7ModuleDriver()
    {
        return new ExcelC2019032668WHR7Driver();
    }

    /**
     * C20181101PQ6OVN 特快
     */
    private function createCustomerC20181101PQ6OVNModuleDriver()
    {
        return new ExcelC20181101PQ6OVNDriver();
    }

    /**
     * C20190417PX262B 凡普金科
     */
    private function createCustomerC20190417PX262BModuleDriver()
    {
        return new ExcelC20190417PX262BDriver();
    }

    /**
     * C20181101YRXUX2 卡牛
     */
    private function createCustomerC20181101YRXUX2ModuleDriver()
    {
        return new ExcelC20181101YRXUX2Driver();
    }

    /**
     * C20181101LHVREG 随手记
     */
    private function createCustomerC20181101LHVREGModuleDriver()
    {
        return new ExcelC20181101LHVREGDriver();
    }

    /**
     * C20181101M41IR3 好还
     **/
    private function createCustomerC20181101M41IR3ModuleDriver()
    {
        return new ExcelC20181101M41IR3Driver();
    }

    /**
     * C20181101SQ9IIO 量化派
     **/
    private function createCustomerC20181101SQ9IIOModuleDriver()
    {
        return new ExcelC20181101SQ9IIODriver();
    }

    /**
     * C20181101QU9C0N 人人贷
     **/
    private function createCustomerC20181101QU9C0NModuleDriver()
    {
        return new ExcelC20181101SQ9IIODriver();
    }

    private function createCustomerC201811012TPQQ8ModuleDriver()
    {
        return new ExcelC201811012TPQQ8Driver();
    }

    /**
     * 特殊客户发送邮件
     */
    private function createCustomerEmailModuleDriver()
    {
        return new CustomerEmailDriver();
    }
}