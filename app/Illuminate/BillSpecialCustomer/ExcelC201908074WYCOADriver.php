<?php

namespace App\Illuminate\BillSpecialCustomer;

use App\Models\{
    Customer, CustomerExpend, MoneyRecharge, MongoBillMonth, Product
};
use App\Support\CustomException;
use App\TraitUpgrade\CurlTrait;

//小象优品
class ExcelC201908074WYCOADriver
{
    use CurlTrait;

    /** @var string 客户ID */
    private $customer_id = 'C201908074WYCOA';
    private $account_id  = '****************';

    /** @var int 目前的总余额 */
    private $money_last_now = 0;

    /** @var string 公司名称固定展示 */
    private $company = "北京羽乐创新科技有限公司";


    public function _getMoneyInfo($data): array
    {

        //账单月
        $bill_month = date('Ym', strtotime('first day of last month'));
        //获取当前客户的特殊消耗
        $special = CustomerExpend::where('customer_id', $this->customer_id)
            ->where('start_date', $bill_month)
            ->get()
            ->map(
                function ($item) {
                    $consume  = $item->type == 1 ? 0.00 : $item->money;
                    $recharge = $item->type == 2 ? 0.00 : $item->money;
                    $month    = $item->start_date;
                    $name     = $item->name;
                    return compact('consume', 'recharge', 'month', 'name');
                }
            )
            ->toArray();
        //增加邦秒验的结算单
        /**
         * 1.获取邦秒验所有的子产品ID
         * 2.获取所有子产品当前账号的账单月及历史的消耗金额
         * 3.整合模板
         **/
        $children_product_id = Product::where('father_id', '200')
            ->pluck('product_id');
        $billMonthMoney      = MongoBillMonth::whereIn('product_id', $children_product_id)
            ->where('month', $bill_month)
            ->where('customer_id', $this->customer_id)
            ->get()
            ->reduce(
                function ($money, $item) {
                    $money += $item->money ? $item->money : 0;
                    return $money;
                },
                0
            );
        $historyMoney        = MongoBillMonth::whereIn('product_id', $children_product_id)
            ->where('month', '<', $bill_month)
            ->where('customer_id', $this->customer_id)
            ->get()
            ->reduce(
                function ($money, $item) {
                    $money += $item->money ? $item->money : 0;
                    return $money;
                },
                0
            );

        $section_begin = date('Ymd', strtotime('first day of last month'));
        $section_end   = date('   Ymd', strtotime('last day of last month'));
        $details       = [
            'bill_month_prev_month_money' => number_format($historyMoney, 2),
            'section_begin'               => $section_begin,
            'section_end'                 => $section_end,
            'recharge'                    => number_format(0.00, 2),
            'consume'                     => number_format($billMonthMoney, 2),
            'special'                     => $special,
            'product_name'                => '邦秒验',
            'total_recharge'              => number_format(
                $historyMoney + array_sum(array_column($special, 'recharge')),
                2
            ),
            'total_consume'               => number_format(
                $billMonthMoney + array_sum(array_column($special, 'consume')),
                2
            )
        ];

        array_unshift(
            $data[$this->account_id]['list_bills'],
            [
                'month'          => $bill_month,
                'account_id'     => $this->account_id,
                'product_id'     => 200,
                'product_name'   => '邦秒验',
                'customer_id'    => $this->customer_id,
                'section_source' => [],
                'section_begin'  => $section_begin,
                'section_end'    => $section_end,
                'details'        => $details,
                'product_alo'    => 'product_bill'
            ]
        );

        //新增 邦秒配详单版 结算单
        $product_id     = 104;
        $billMonthMoney = MongoBillMonth::where('product_id', $product_id)
            ->where('month', $bill_month)
            ->where('customer_id', $this->customer_id)
            ->get()
            ->reduce(
                function ($money, $item) {
                    $money += $item->money ? $item->money : 0;
                    return $money;
                },
                0
            );
        $historyMoney   = MongoBillMonth::where('product_id', $product_id)
            ->where('month', '<', $bill_month)
            ->where('customer_id', $this->customer_id)
            ->get()
            ->reduce(
                function ($money, $item) {
                    $money += $item->money ? $item->money : 0;
                    return $money;
                },
                0
            );

        //
        $details = [
            'bill_month_prev_month_money' => number_format($historyMoney, 2),
            'section_begin'               => $section_begin,
            'section_end'                 => $section_end,
            'recharge'                    => 0.00,
            'consume'                     => number_format($billMonthMoney, 2),
            'special'                     => $special,
            'product_name'                => '邦秒配详单版',
            'total_recharge'              => number_format(
                $historyMoney + array_sum(array_column($special, 'recharge')),
                2
            ),
            'total_consume'               => number_format(
                $billMonthMoney + array_sum(array_column($special, 'consume')),
                2
            )
        ];

        array_unshift(
            $data[$this->account_id]['list_bills'],
            [
                'month'          => $bill_month,
                'account_id'     => $this->account_id,
                'product_id'     => 104,
                'product_name'   => '邦秒配详单版',
                'customer_id'    => $this->customer_id,
                'section_source' => [],
                'section_begin'  => $section_begin,
                'section_end'    => $section_end,
                'details'        => $details,
                'product_alo'    => 'product_bill'
            ]
        );

        return $data;
    }


}