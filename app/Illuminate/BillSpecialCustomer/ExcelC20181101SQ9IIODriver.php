<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/29 0029
 * Time: 10:42
 */

namespace App\Illuminate\BillSpecialCustomer;


use App\Models\Customer;
use App\Models\MoneyRecharge;
use App\Models\MongoBillMonth;
use App\Support\CustomException;
use App\TraitUpgrade\CurlTrait;
use Illuminate\Support\Facades\DB;

class ExcelC20181101SQ9IIODriver
{
    use CurlTrait;

    /** @var string 客户ID */
    private $customer_id = 'C20181101SQ9IIO';

    private $account_id = '****************';

    private $must_fee_config_product_id = [
        101,
        202,
        213,
        216
    ];

    public function _validateCustomer()
    {
        // 如果存在多个计费产品 则这个模板不可以使用
        $fee_config_product_ids = MongoBillMonth::where(
            [
                "customer_id" => $this->customer_id,
                "month"       => date("Ym", strtotime("last day of last month"))
            ]
        )
            ->pluck('product_id')
            ->toArray();

        foreach ($this->must_fee_config_product_id as $product_id) {
            if (!in_array($product_id, $fee_config_product_ids)) {
                throw new CustomException('不存在' . $product_id . '的计费片段，所以量化派的模板失效');
            }
        }
    }

    public function _getMoneyInfo($data): array
    {
        // 校验
        $this->_validateCustomer();

        //邦信分累计消耗金额
        $carry_over_money_101 = $this->getCarryOverMoney([101]);

        //邦秒验sheet数据
        $details_200 = [
            'product_name'  => '邦秒验',
            'section_begin' => date('Ymd', strtotime('first day of last month')),
            'section_end'   => date('Ymd', strtotime('last day of last month')),
            'money'         => 0,
            'carry'         => $this->getCarryOverMoney([202, 213, 216]),   //结转金额
            'product_id'    => 200
        ];

        $result = [];
        foreach ($data[$this->account_id]['list_bills'] as $key => $list_bills) {
            $details    = $list_bills['details'];
            $product_id = $details['product_id'];
            if ($product_id == 101) {
                $list_bills['details'] = array_merge(
                    $list_bills['details'],
                    ['carry' => $carry_over_money_101]
                );
            } elseif (in_array($product_id, [202, 213, 216])) {
                $details_200['money'] = bcadd($details_200['money'], $details['money'], 4);
            }
            $result[$product_id] = $list_bills->toArray();
        }

        //添加邦秒验sheet数据
        $details_200['money'] = round($details_200['money'], 2);
        $result[200] = ['details' => $details_200];
        ksort($result);

        return [$this->account_id => ['list_bills' => $result]];
    }

    /**
     * 获取上月结转金额(获取累计消耗)
     *
     * @access protected
     *
     * @param $product_id array 产品ID
     *
     * @return float
     **/
    private function getCarryOverMoney($product_id)
    {
        $money = MongoBillMonth::whereIn('product_id', $product_id)
            ->where('account_id', $this->account_id)
            ->where('month', '<', date('Ym', strtotime('last day of last month')))
            ->sum('money');
        return $money;
    }
}