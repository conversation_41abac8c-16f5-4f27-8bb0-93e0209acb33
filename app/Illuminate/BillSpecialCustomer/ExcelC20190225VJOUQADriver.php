<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/1/7 0007
 * Time: 15:23
 */

namespace App\Illuminate\BillSpecialCustomer;

use App\Models\MongoBillMonth;
use App\TraitUpgrade\CurlTrait;


//读秒特殊模板
class ExcelC20190225VJOUQADriver
{
    use CurlTrait;

    private $customer_id = 'C20190225VJOUQA';
    private $account_id  = '****************';

    public function _getMoneyInfo($data): array
    {
        //获取210打包的账单
        $bill_list = MongoBillMonth::where('customer_id', $this->customer_id)
            ->where('month', date('Ym', strtotime('first day of last month')))
            ->where('account_id', '')
            ->where('product_id', 210)
            ->first()
            ->toArray();
        array_push(
            $data[$this->account_id]['list_bills'],
            [
                '_id'                    => $bill_list['_id'],
                'uuid'                   => $bill_list['uuid'],
                'month'                  => $bill_list['month'],
                'account_id'             => $bill_list['account_id'],
                'product_id'             => $bill_list['product_id'],
                'customer_id'            => $bill_list['customer_id'],
                'section_source'         => [],
                'section_begin'          => $bill_list['section_begin'],
                'section_end'            => $bill_list['section_end'],
                'money'                  => $bill_list['money'],
                'section_number'         => $bill_list['section_number'],
                'section_invoked_number' => $bill_list['section_invoked_number'],
                'product_alo'            => 'shortcut_package',
                'details'                => [
                    [
                        'section_begin' => $bill_list['section_begin'],
                        'section_end'   => $bill_list['section_end'],
                        'name'          => '≤2',
                        'price'         => $bill_list['section_number']['fee_number'][0]['price'],
                        'number'        => $bill_list['section_number']['fee_number'][0]['number'],
                        'money'         => $bill_list['section_number']['fee_number'][0]['money']
                    ],
                    [
                        'section_begin' => $bill_list['section_begin'],
                        'section_end'   => $bill_list['section_end'],
                        'name'          => '3',
                        'price'         => $bill_list['section_number']['fee_number'][1]['price'],
                        'number'        => $bill_list['section_number']['fee_number'][1]['number'],
                        'money'         => $bill_list['section_number']['fee_number'][1]['money']
                    ],
                    [
                        'section_begin' => $bill_list['section_begin'],
                        'section_end'   => $bill_list['section_end'],
                        'name'          => '4',
                        'price'         => $bill_list['section_number']['fee_number'][2]['price'],
                        'number'        => $bill_list['section_number']['fee_number'][2]['number'],
                        'money'         => $bill_list['section_number']['fee_number'][2]['money']
                    ],
                    [
                        'section_begin' => $bill_list['section_begin'],
                        'section_end'   => $bill_list['section_end'],
                        'name'          => '≥5',
                        'price'         => $bill_list['section_number']['fee_number'][3]['price'],
                        'number'        => $bill_list['section_number']['fee_number'][3]['number'],
                        'money'         => $bill_list['section_number']['fee_number'][3]['money']
                    ]
                ]
            ]
        );

        return $data;
    }

    public function _validateCustomer()
    {
        // 这个模板没有过期的时候,

    }
}