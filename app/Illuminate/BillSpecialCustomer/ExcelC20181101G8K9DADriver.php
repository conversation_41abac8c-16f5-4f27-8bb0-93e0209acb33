<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/21 0021
 * Time: 09:51
 */

namespace App\Illuminate\BillSpecialCustomer;


use App\Models\MongoBillMonth;
use App\Models\MongoBillSection;
use App\Models\Product;

class ExcelC20181101G8K9DADriver
{
    protected $old_excel_data;    //已处理完成的excel数据
    protected $account_id                = '****************';
    protected $customer_id               = 'C20181101G8K9DA';
    protected $short_children_product_id = [];
    protected $bill_month;
    //分的产品ID
    protected $fen_product_id = [251, 288];
    protected $fen_price      = null;
    protected $not_fen_price  = null;


    public function __construct()
    {
        //获取所有210的子产品
        $this->getShortChildrenProductId();

        $this->bill_month = date('Ym', strtotime('first day of last month'));
    }

    private function getShortChildrenProductId()
    {
        $this->short_children_product_id = Product::where('father_id', '=', '210')
            ->pluck('product_id')
            ->toArray();
    }

    public function _validateCustomer()
    {
        //只存在一个账号的计费
        if (count($this->old_excel_data) != 1) {
            throw new \Exception('账号数量与预设模板不一致，特殊模板出现错误');
        }

        //校验账号ID
        if (!array_key_exists($this->account_id, $this->old_excel_data)) {
            throw new \Exception('账号ID与预设模板不一致，特殊模板出现错误');
        }

        //校验是否存在210产品及计费配置
        $bill_month_fee_config = MongoBillSection::where('account_id', '=', $this->account_id)
            ->where('month', '=', $this->bill_month)
            ->whereIn('product_id', $this->short_children_product_id)
            ->where('section_source.is_reset', '!=', 1)
            ->get()
            ->toArray();

        if (empty($bill_month_fee_config)) {
            throw new \Exception('账单月不存在邦信分快捷版的计费配置，特殊模板出现错误');
        }

        //校验是否存在[分的计费配置]
        $billMonthFeeConfigProductId = array_column($bill_month_fee_config, 'product_id');
        //重置对象中的快捷版子产品ID的属性，将其修改为存在计费配置的快捷版子产品ID
        $this->short_children_product_id = $billMonthFeeConfigProductId;

        if (empty(array_intersect($billMonthFeeConfigProductId, $this->fen_product_id))) {
            throw new \Exception('不存在邦信分快捷版分字段的计费配置，特殊模板出现错误');
        }

        if (empty(array_diff($billMonthFeeConfigProductId, $this->fen_product_id))) {
            throw new \Exception('不存在邦信分快捷版非分字段的计费配置，特殊模板出现错误');
        }

        //出现重复的产品ID代表存在多条计费配置
        if (count($billMonthFeeConfigProductId) != count(array_unique($billMonthFeeConfigProductId))) {
            throw new \Exception('账单月内存在多条邦信分快捷版的计费配置，特殊模板出现错误');
        }

        $billMonthFeeConfigItem = array_first($bill_month_fee_config);

        //校验计费配置
        //按用量	固定价格
        $fee_config      = $billMonthFeeConfigItem['section_source'];
        $fee_basis       = $fee_config['fee_basis'];
        $fee_method      = $fee_config['fee_method'];
        $fee_amount_rule = $fee_config['fee_amount_rule'];
        $fee_price_rule  = $fee_config['fee_price_rule'];
        if ($fee_basis != 1 || $fee_method != 2 || $fee_amount_rule != 1 || $fee_price_rule != 1) {
            throw new \Exception('计费配置与预设模板不一致，特殊模板出现错误');
        }

        //获取分|非分字段的单价
        array_walk(
            $bill_month_fee_config,
            function ($item) {
                $product_id = $item['product_id'];
                $fee_price  = $item['section_source']['fee_price'];
                if (in_array($product_id, $this->fen_product_id)) {
                    if (is_null($this->fen_price)) {
                        $this->fen_price = $fee_price;
                    } else {
                        if ($this->fen_price != $fee_price) {
                            throw new \Exception('邦信分快捷版分字段的单价不一致，特殊模板出现错误');
                        }
                    }
                } else {
                    if (is_null($this->not_fen_price)) {
                        $this->not_fen_price = $fee_price;
                    } else {
                        if ($this->not_fen_price != $fee_price) {
                            throw new \Exception('邦信分快捷版非分字段的单价不一致，特殊模板出现错误');
                        }
                    }
                }
            }
        );
    }

    public function _getMoneyInfo($data): array
    {
        $this->old_excel_data = $data;

        //校验
        $this->_validateCustomer();

        //获取账单信息
        $billMonth = $this->getBillMonth();

        //整理成为账单的
        $this->makeShortBill($billMonth);

        return $this->old_excel_data;
    }

    private function makeShortBill($billMonth)
    {
        $shortBill = [
            '_id'            => [],
            'uuid'           => [],
            'month'          => $this->bill_month,
            'account_id'     => $this->account_id,
            'product_id'     => 210,
            'customer_id'    => $this->customer_id,
            'section_begin'  => '',
            'section_end'    => '',
            'money'          => 0,
            'product_alo'    => '特殊',
            'section_number' => [
                'fee_number' => 0
            ],
            'details'        => [
                'product_name'  => '邦信分快捷版',
                'section_begin' => '',
                'section_end'   => '',
                'fen'           => [
                    'price'      => $this->fen_price,
                    'money'      => 0,
                    'fee_number' => 0
                ],
                'not_fen'       => [
                    'price'      => $this->not_fen_price,
                    'money'      => 0,
                    'fee_number' => 0
                ]
            ]
        ];
        array_walk(
            $billMonth,
            function ($item) use (&$shortBill) {
                $id            = $item['_id'];
                $uuid          = $item['uuid'];
                $section_begin = $item['section_begin'];
                $section_end   = $item['section_end'];
                $money         = $item['money'];
                $fee_number    = $item['section_number']['fee_number'];
                //通用的值
                $shortBill['_id'][]  = $id;
                $shortBill['uuid'][] = $uuid;
                if (empty($shortBill['section_end'])) {
                    $shortBill['section_begin']                       = $section_begin;
                    $shortBill['section_end']                         = $section_end;
                    $shortBill['details']['fen']['section_begin']     = $section_begin;
                    $shortBill['details']['fen']['section_end']       = $section_end;
                    $shortBill['details']['not_fen']['section_begin'] = $section_begin;
                    $shortBill['details']['not_fen']['section_end']   = $section_end;
                }
                $shortBill['money']                        += $money;
                $shortBill['section_number']['fee_number'] += $fee_number;

                $product_id = $item['product_id'];
                if (in_array($product_id, $this->fen_product_id)) {
                    $shortBill['details']['fen']['money']      += $money;
                    $shortBill['details']['fen']['fee_number'] += $fee_number;
                } else {
                    $shortBill['details']['not_fen']['money']      += $money;
                    $shortBill['details']['not_fen']['fee_number'] += $fee_number;
                }
            }
        );


        //替换快捷版的账单
        $this->old_excel_data[$this->account_id]['list_bills'] = array_map(
            function ($item) use ($shortBill) {
                if (array_key_exists('product_name', $item) && $item['product_name'] == '邦信分快捷版') {
                    return $shortBill;
                }
                return $item;
            },
            $this->old_excel_data[$this->account_id]['list_bills']
        );
    }

    private function getBillMonth()
    {
        return MongoBillMonth::whereIn('product_id', $this->short_children_product_id)
            ->where('account_id', '=', $this->account_id)
            ->where('month', '=', $this->bill_month)
            ->where('section_source.is_reset', '!=', 1)
            ->get()
            ->toArray();
    }
}