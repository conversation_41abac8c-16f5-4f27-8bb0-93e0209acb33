<?php

namespace App\Illuminate\BillDistributeSection;


class ReachModuleDriver implements DriverFactory
{
    /**
     * 校验参数
     * @param array $params
     * @throws \Exception
     */
    public function validateParams(array $params)
    {
        // 计费规则总结起来就是 新总价-历史月份总价格=当月的价格
        $this->validateNumber($params);

        // 单价规则校验
        $this->validatePrice($params);
    }

    /**
     * 校验数量
     * @param array $params
     * @throws \Exception
     */
    private function validateNumber(array $params)
    {
        $fee_number = $params['fee_number'] ?? '';
        if ($fee_number === '' || !is_numeric($fee_number)) {
            throw new \Exception('通用按用到达阶梯服务提示: 请输入合法的fee_number msg:' .
                json_encode(compact('params'), JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 校验价格
     * @param array $params
     * @throws \Exception
     */
    private function validatePrice(array $params)
    {
        $fee_price = $params['fee_price'] ?? [];
        $msg = '通用按用量到达阶梯服务提示：请输入合法的fee_price msg:' .
            json_encode(compact('params'), JSON_UNESCAPED_UNICODE);

        if (!is_array($fee_price) || !$fee_price) {
            throw new \Exception($msg);
        }

        // 是否包含 左右边界 && 区间价格
        array_walk($fee_price, function ($fee_rule_item) use ($msg) {
            list($reach_standard, $price) = [$fee_rule_item['reach_standard'] ?? '', $fee_rule_item['price'] ?? ''];

            // 如果不存在
            if ($reach_standard === '' || $price === '') {
                throw new \Exception($msg);
            }

            // 不是合法的数值类型
            if (!is_numeric($reach_standard) || !is_numeric($price)) {
                throw new \Exception($msg);
            }
        });
    }

    /**
     * 生成money
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function genMoney(array $params): array
    {
        // 校验参数
        $this->validateParams($params);

        return $this->genMoneyDo($params);
    }

    /**
     * 计费
     * @param array $params
     * @return array
     */
    private function genMoneyDo(array $params): array
    {
        list($fee_number, $fee_price) = [$params['fee_number'], $params['fee_price']];
        $fee_price = array_column($fee_price, null, 'reach_standard');
        ksort($fee_price, SORT_NUMERIC);

        //halt($fee_price);
        // 最终的价格
        $list_prices = array_reduce($fee_price, function ($carry, $fee_rule_item) use ($fee_number) {
            list($reach_standard, $price) = [$fee_rule_item['reach_standard'], $fee_rule_item['price']];
            if ($fee_number >= $reach_standard) {
                $carry['price'] = $price;
                $carry['section_key'] = $reach_standard;
            }
            return $carry;
        }, ['price' => 0, 'section_number' => $fee_number]);
		
        $list_prices['money'] = $list_prices['price'] * $fee_number;

        // 如果调用量为0 则意味没有没有命中任何一个, 则赋予第一个区间
        return $this->_rebackSectionKeyWhenEver($list_prices, $fee_price);
    }

    /**
     * 如果调用量为0 则意味没有没有命中任何一个, 则赋予第一个区间
     * @param array $list_prices
     * @param array $fee_price
     * @return array
     */
    private function _rebackSectionKeyWhenEver(array $list_prices, array $fee_price): array
    {
        // 如果调用量不为0
        if (isset($list_prices['section_key'])) {
            return $list_prices;
        }
        reset($fee_price);
        $key_first = key($fee_price);
        $list_prices['section_key'] = $fee_price[$key_first]['reach_standard'];
        $list_prices['price'] = $fee_price[$key_first]['price'];
        return $list_prices;
    }
}