<?php

namespace App\Illuminate\BillDistributeSection;

class BillManager implements BillFactory
{
    private $app;

    /**
     * Create a new Cache manager instance.
     *
     * @param  $app
     * @return void
     */
    public function __construct($app)
    {
        $this->app = $app;
    }

    /**
     * 获取驱动
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    public function driver(string $driver_name)
    {
        return $this->resolve($driver_name);
    }

    /**
     * 解析
     * @param string $driver_name
     * @return mixed
     * @throws \Exception
     */
    private function resolve(string $driver_name)
    {
        $driver_method = 'create' . ucfirst($driver_name) . 'ModuleDriver';

        if (method_exists($this, $driver_method)) {
            return $this->{$driver_method}();
        } else {
            throw new \Exception('抱歉，账单服务目前不支持' . ucfirst($driver_name) . 'Driver类型的账单模块');
        }
    }

    /**
     * 累进 && 通用
     * @return ProgressionModuleDriver
     */
    private function createProgressionModuleDriver()
    {
        return new ProgressionModuleDriver();
    }

    /**
     * 累进 && 到达
     * @return ReachModuleDriver
     */
    private function createReachModuleDriver()
    {
        return new ReachModuleDriver();
    }

    /**
     * 区分运营商 && 按时间
     * @return OperatorDateModuleDriver
     */
    private function createOperatorDateModuleDriver()
    {
        return new OperatorDateModuleDriver();
    }

    /**
     * 区分运营商 && 累进阶梯
     * @return OperatorProgressionModuleDriver
     */
    private function createOperatorProgressionModuleDriver()
    {
        return new OperatorProgressionModuleDriver();
    }

    /**
     * 按运营商 && 到达阶梯
     * @return OperatorReachModuleDriver
     */
    private function createOperatorReachModuleDriver()
    {
        return new OperatorReachModuleDriver();
    }

}
