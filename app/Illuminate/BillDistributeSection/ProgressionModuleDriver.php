<?php

namespace App\Illuminate\BillDistributeSection;

class ProgressionModuleDriver implements DriverFactory
{
    /** @var array 区间 , number , 金额的聚合 */
    private $list_number_price_distribute = [];

    /**
     * 校验参数
     * @param array $params
     * @throws \Exception
     */
    public function validateParams(array $params)
    {
        // 虽然有阶梯周期的存在， 但是对于计费服务来说，可以将阶梯周期的存在放在服务外部，被MVC的C层控制
        // 数量
        $this->validateNumber($params);

        // 价格
        $this->validatePrice($params);
    }

    /**
     * 数量
     * @param array $params
     * @throws \Exception
     */
    private function validateNumber(array $params)
    {
        $fee_number = trim($params['fee_number']) ?? '';
        if ($fee_number === '' || !is_numeric($fee_number)) {
            throw new \Exception('通用按用量累进阶梯服务提示: 请输入合法的fee_number msg:' .
                json_encode(compact('params'), JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 校验价格
     * @param array $params
     * @throws \Exception
     */
    private function validatePrice(array $params)
    {
        $fee_price = $params['fee_price'] ?? [];
        $msg = '通用按用量累进阶梯服务提示：请输入合法的fee_price msg:' .
            json_encode(compact('params'), JSON_UNESCAPED_UNICODE);

        if (!is_array($fee_price) || !$fee_price) {
            throw new \Exception($msg);
        }

        // 是否包含 左右边界 && 区间价格
        array_walk($fee_price, function ($fee_rule_item) use ($msg) {
            list($left, $right, $price) = [$fee_rule_item['left'] ?? '', $fee_rule_item['right'] ?? '',
                $fee_rule_item['price'] ?? ''];

            // 如果不存在
            if ($left === '' || $right === '' || $price === '') {
                throw new \Exception($msg);
            }

            // 不是合法的数值类型
            if (!is_numeric($left) || !is_numeric($right) || !is_numeric($price)) {
                throw new \Exception($msg);
            }
        });
    }

    /**
     * 生成阶梯价格对应的金额(数量)
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function genMoney(array $params): array
    {
        // 清空属性
        $this->list_number_price_distribute = [];

        // 校验参数
        $this->validateParams($params);

        // 计费
        return $this->genMoneyDo($params);
    }

    /**
     * 计费
     * @param array $params
     * @return array
     */
    private function genMoneyDo(array $params): array
    {
        list($fee_price, $fee_number) = [$params['fee_price'], $params['fee_number']];

        // 计费
        array_walk($fee_price, function ($price_item_rule) use ($fee_number) {
            list($left, $right, $price) = [$price_item_rule['left'], $price_item_rule['right'], $price_item_rule['price']];

            // 如果$fee_number 是大于当前区间的右边界的时候
            if ($fee_number >= $right && $right != -1) {
                $money = ($right - $left + 1) * $price;
                $section_key = $left . '-' . $right;
                $section_number = $right - $left + 1;
                $this->list_number_price_distribute[$section_key] = compact('money', 'price', 'section_number', 'section_key');
            }

            // 如果$fee_number是小于右边的边界 但是大于左边边界
            if ($fee_number < $right && $fee_number >= $left) {
                $money = ($fee_number - $left + 1) * $price;
                $section_key = $left . '-' . $right;
                $section_number = $fee_number - $left + 1;
                $this->list_number_price_distribute[$section_key] = compact('money', 'price', 'section_number', 'section_key');
            }

            // 如果遇到了右边界是-1的情况
            if ($right == -1 && $fee_number >= $left) {
                $money = ($fee_number - $left + 1) * $price;
                $section_key = $left . '+';
                $section_number = $fee_number - $left + 1;
                $this->list_number_price_distribute[$section_key] = compact('money', 'price', 'section_number', 'section_key');
            }
        });

        // 如果不再任何一个区间的话 则赋值给第一个区间

        return $this->_tidyFirstAreaWhenNone($fee_price, $fee_number);
    }

    /**
     * 如果不再任何一个区间的话 则赋值给第一个区间
     * @param array $fee_price
     * @param int $fee_number
     * @return array
     */
    private function _tidyFirstAreaWhenNone(array $fee_price, int $fee_number): array
    {
        if (!$this->list_number_price_distribute) {
            $price_first= current($fee_price);
            $money = 0;
            $section_key = $price_first['left'] . '-' . $price_first['right'];
            $section_number = 0;
            $price = $price_first['price'];
            $this->list_number_price_distribute[$section_key] = compact('money', 'price', 'section_number', 'section_key');
        }

        return $this->list_number_price_distribute;
    }
}