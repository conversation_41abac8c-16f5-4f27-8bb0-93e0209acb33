<?php

namespace App\Illuminate\BillDistributeSection;

class OperatorProgressionModuleDriver implements DriverFactory
{
    /**
     * 校验参数
     * @param array $params
     * @throws \Exception
     */
    public function validateParams(array $params)
    {
        // 虽然有阶梯周期的存在， 但是对于计费服务来说，可以将阶梯周期的存在放在服务外部，被MVC的C层控制

        // 校验调用量参数
        $this->validateNumberParams($params);

        // 校验价格参数
        $this->validatePriceParams($params);
    }

    /**
     * 校验价格参数
     * @param array $params
     * @throws \Exception
     */
    private function validatePriceParams(array $params)
    {
        $fee_price = $params['fee_price'] ?? [];
        $msg = '账单(区分运营商按用量 && 累进阶梯)服务提示：请输入合法的fee_price msg:' .
            json_encode(compact('params'), JSON_UNESCAPED_UNICODE);
        if (!is_array($fee_price) || !$fee_price) {
            throw new \Exception($msg);
        }

        // 是否包含 左右边界 && 区间价格
        array_walk($fee_price, function ($fee_rule_item) use ($msg) {
            list($left, $right, $price_yd, $price_lt, $price_dx) = [$fee_rule_item['left'] ?? '', $fee_rule_item['right'] ?? '',
                $fee_rule_item['price_yd'] ?? '', $fee_rule_item['price_lt'] ?? '', $fee_rule_item['price_dx'] ?? ''];

            // 如果不存在
            if ($left === '' || $right === '' || $price_yd === '' || $price_lt === '' || $price_dx === '') {
                throw new \Exception($msg);
            }

            // 不是合法的数值类型
            if (!is_numeric($left) || !is_numeric($right) || !is_numeric($price_yd) || !is_numeric($price_lt) || !is_numeric($price_dx)) {
                throw new \Exception($msg);
            }
        });
    }

    /**
     * 校验调用量参数
     * @param array $params
     * @throws \Exception
     */
    private function validateNumberParams(array $params)
    {
        $fee_number = $params['fee_number'] ?? [];

        // 是否是数组
        if (!$fee_number || !is_array($fee_number)) {
            throw new \Exception('账单(区分运营商按用量 && 累进阶梯)服务提示: 请输入合法的fee_number msg:' .
                json_encode(compact('params'), JSON_UNESCAPED_UNICODE));
        }

        // 是否包含三种运营商的价格
        list($number_yd, $number_lt, $number_dx) = [$fee_number['yd'] ?? '', $fee_number['lt'] ?? '', $fee_number['dx'] ?? ''];
        if ($number_yd === '' || !is_numeric($number_yd) || $number_lt === '' || !is_numeric($number_lt) || $number_dx === '' || !is_numeric($number_dx)) {
            throw new \Exception('账单(区分运营商按用量 && 累进阶梯)服务提示: 请输入合法的fee_number msg:' .
                json_encode(compact('params'), JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 计费
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function genMoney(array $params): array
    {
        // 校验参数
        $this->validateParams($params);

        // 计费
        return $this->genMoneyDo($params);
    }

    /**
     * 计费
     * @param array $params
     * @return array
     */
    private function genMoneyDo(array $params): array
    {
        // 参数罗列
        list($fee_price, $fee_number) = [$params['fee_price'], $params['fee_number']];
        list($number_yd, $number_lt, $number_dx) = [$fee_number['yd'], $fee_number['lt'], $fee_number['dx']];

        // 计费
        return array_reduce($fee_price, function ($carry, $price_item_rule) use ($number_dx, $number_lt, $number_yd) {

            list($left, $right, $price_yd, $price_lt, $price_dx) = [
                $price_item_rule['left'],
                $price_item_rule['right'],
                $price_item_rule['price_yd'],
                $price_item_rule['price_lt'],
                $price_item_rule['price_dx']
            ];

            // 如果 $fee_number 是大于当前区间的右边界的时候
            $carry['money_yd'] = $this->sumWhenNumberIsBiggerThanRight($number_yd, $right, $left, $carry['money_yd'], $price_yd);
            $carry['money_lt'] = $this->sumWhenNumberIsBiggerThanRight($number_lt, $right, $left, $carry['money_lt'], $price_lt);
            $carry['money_dx'] = $this->sumWhenNumberIsBiggerThanRight($number_dx, $right, $left, $carry['money_dx'], $price_dx);

            // 如果$fee_number是小于右边的边界 但是大于左边边界
            $carry['money_yd'] = $this->sumWhenNumberInMiddle($number_yd, $right, $left, $carry['money_yd'], $price_yd);
            $carry['money_lt'] = $this->sumWhenNumberInMiddle($number_lt, $right, $left, $carry['money_lt'], $price_lt);
            $carry['money_dx'] = $this->sumWhenNumberInMiddle($number_dx, $right, $left, $carry['money_dx'], $price_dx);

            // 如果遇到了右边界是-1的情况
            $carry['money_yd'] = $this->sumWhenRightIsNoLimit($number_yd, $right, $left, $carry['money_yd'], $price_yd);
            $carry['money_lt'] = $this->sumWhenRightIsNoLimit($number_lt, $right, $left, $carry['money_lt'], $price_lt);
            $carry['money_dx'] = $this->sumWhenRightIsNoLimit($number_dx, $right, $left, $carry['money_dx'], $price_dx);

            return $carry;
        }, ['money_yd' => 0, 'money_lt' => 0, 'money_dx' => 0]);
    }

    /**
     * 如果遇到了右边界是-1的情况
     * @param int $fee_number
     * @param int $right
     * @param int $left
     * @param float $sum
     * @param float $price
     * @return float
     */
    private function sumWhenRightIsNoLimit(int $fee_number, int $right, int $left, float $sum, float $price): float
    {
        if ($right == -1 && $fee_number >= $left) {
            $sum += ($fee_number - $left + 1) * $price;
        }
        return $sum;
    }

    /**
     * 如果$fee_number是小于右边的边界但是大于左边边界
     * @param int $fee_number
     * @param int $right
     * @param int $left
     * @param float $sum
     * @param float $price
     * @return float
     */
    private function sumWhenNumberInMiddle(int $fee_number, int $right, int $left, float $sum, float $price): float
    {
        if ($fee_number < $right && $fee_number >= $left) {
            $sum += ($fee_number - $left + 1) * $price;
        }
        return $sum;
    }

    /**
     * 当做调用量大于右边界的时候
     * @param int $fee_number
     * @param int $right
     * @param int $left
     * @param float $sum
     * @param float $price
     * @return float
     */
    private function sumWhenNumberIsBiggerThanRight(int $fee_number, int $right, int $left, float $sum, float $price): float
    {
        if ($fee_number >= $right && $right != -1) {
            $sum += ($right - $left + 1) * $price;
        }
        return $sum;
    }
}
