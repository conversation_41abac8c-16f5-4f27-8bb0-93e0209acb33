<?php

namespace App\Illuminate\BillDistributeSection;


class OperatorReachModuleDriver implements DriverFactory
{
    /**
     * 校验参数
     * @param array $params
     * @throws \Exception
     */
    public function validateParams(array $params)
    {
        // 计费规则总结起来就是 新总价-历史月份总价格=当月的价格
        // 校验调用量参数
        $this->validateNumberParams($params);

        // 校验价格参数
        $this->validatePriceParams($params);
    }

    /**
     * 校验价格参数
     * @throws \Exception
     * @param array $params
     */
    private function validatePriceParams(array $params)
    {
        $fee_price = $params['fee_price'] ?? [];
        $msg = '账单(区分运营商按用量 && 到达阶梯)服务提示：请输入合法的fee_price msg:' .
            json_encode(compact('params'), JSON_UNESCAPED_UNICODE);
        if (!is_array($fee_price) || !$fee_price) {
            throw new \Exception($msg);
        }

        // 是否包含 左右边界 && 区间价格
        array_walk($fee_price, function ($fee_rule_item) use ($msg) {
            list($reach_standard, $price_yd, $price_dx, $price_lt) = [
                $fee_rule_item['reach_standard'] ?? '',
                $fee_rule_item['price_yd'] ?? '',
                $fee_rule_item['price_dx'] ?? '',
                $fee_rule_item['price_lt'] ?? '',
            ];

            // 如果不存在
            if ($reach_standard === '' || $price_yd === '' || $price_dx === '' || $price_lt === '') {
                throw new \Exception($msg);
            }

            // 不是合法的数值类型
            if (!is_numeric($reach_standard) || !is_numeric($price_dx) || !is_numeric($price_lt) || !is_numeric($price_yd)) {
                throw new \Exception($msg);
            }
        });
    }

    /**
     * 校验调用量参数
     * @param array $params
     * @throws \Exception
     */
    private function validateNumberParams(array $params)
    {
        $fee_number = $params['fee_number'] ?? [];

        $msg = '账单(区分运营商按用量 && 到达阶梯)服务提示: 请输入合法的fee_number msg:' .
            json_encode(compact('params'), JSON_UNESCAPED_UNICODE);
        if (!$fee_number || !is_array($fee_number)) {
            throw new \Exception($msg);
        }

        list($number_yd, $number_lt, $number_dx) = [
            $fee_number['yd'] ?? '', $fee_number['lt'] ?? '', $fee_number['dx'] ?? ''
        ];

        if ($number_yd === '' || !is_numeric($number_yd) || $number_lt === '' || !is_numeric($number_lt) || $number_dx === '' || !is_numeric($number_dx)) {
            throw new \Exception($msg);
        }
    }

    /**
     * 计费
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function genMoney(array $params): array
    {
        // 校验参数
        $this->validateParams($params);

        // 计费
        return $this->genMoneyDo($params);
    }

    /**
     * 计费
     * @param array $params
     * @return array
     */
    private function genMoneyDo(array $params): array
    {
        // 参数整理
        list($fee_number, $fee_price) = [$params['fee_number'], $params['fee_price']];
        list($number_yd, $number_lt, $number_dx) = [
            $fee_number['yd'], $fee_number['lt'], $fee_number['dx']
        ];

        // 简单排序
        $fee_price = array_column($fee_price, null, 'reach_standard');
        ksort($fee_price, SORT_NUMERIC);

        // 最终的价格
        $list_price = array_reduce($fee_price, function ($carry, $fee_rule_item) use ($fee_number, $number_dx, $number_lt, $number_yd) {
            list($reach_standard, $price_yd, $price_dx, $price_lt) = [
                $fee_rule_item['reach_standard'],
                $fee_rule_item['price_yd'],
                $fee_rule_item['price_dx'],
                $fee_rule_item['price_lt'],
            ];

            // 对于电信
            if ($number_dx >= $reach_standard) {
                $carry['price_dx'] = $price_dx;
            }

            // 联通
            if ($number_lt >= $reach_standard) {
                $carry['price_lt'] = $price_lt;
            }

            // 移动
            if ($number_yd >= $reach_standard) {
                $carry['price_yd'] = $price_yd;
            }

            return $carry;
        }, ['price_dx' => 0, 'price_lt' => 0, 'price_yd' => 0]);

        list($price_dx, $price_lt, $price_yd) = [$list_price['price_dx'], $list_price['price_lt'], $list_price['price_yd']];

        return [
            'money_yd' => $price_yd * $number_yd,
            'money_lt' => $price_lt * $number_lt,
            'money_dx' => $price_dx * $number_dx,
        ];
    }

}