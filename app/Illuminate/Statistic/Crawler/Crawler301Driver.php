<?php

namespace App\Illuminate\Statistic\Crawler;

use App\Illuminate\Statistic\Driver;
use App\Illuminate\Statistic\{
    ValidateTrait, StatTrait, DayInvokedTrait, ReportTrait
};

class Crawler301Driver implements Driver
{
    use ValidateTrait, StatTrait, DayInvokedTrait, ReportTrait, CrawlerTrait;
    public $product_id = 301;

    /**
     * 日调用信息
     * @throws \Exception
     */
    public function day(): array
    {
        // 校验条件
        $this->validateParamsForDay();

        // 生成限制
        $where = $this->genLimitParamsForDay();

        // 条件限制
        $list = $this->getStatInfoForDay($where);

        // 追加唯一号码量
        return $this->_appendCrawlerTelForDay($list, $where);
    }

    /**
     * 生成列表参数
     * @return array
     * @throws \Exception
     */
    public function genLimitParamsForDay(): array
    {
        list($operator, $crawler, $province, $product_id, $apikey, $amount_date) = [
            trim(request()->post('operator')),
            trim(request()->post('crawler')),
            trim(request()->post('province')),
            (int)$this->product_id,
            $this->genApikeyLimitForDay(),
            $this->genTimeLimitForDay()
        ];

        return $this->_filterArray(compact('amount_date', 'apikey', 'product_id', 'operator', 'crawler', 'province'));
    }

    /**
     * 日报
     * @return array
     * @throws \Exception
     */
    public function report(): array
    {
        // 检查参数
        $this->validateParamsForReport();

        // 条件
        $where = $this->genLimitParamsForReport();

        // 统计信息
        $list = $this->getStatInfoForReport($where);

        // 附加唯一电话号码
        return $this->_appendUniqueTelToReport($list, $where);
    }

    /**
     * 生成列表参数
     * @return array
     * @throws \Exception
     */
    public function genLimitParamsForReport(): array
    {
        list($operator, $crawler, $province, $product_id, $apikey, $amount_date) = [
            trim(request()->post('operator')),
            trim(request()->post('crawler')),
            trim(request()->post('province')),
            (int)$this->product_id,
            $this->genApikeyLimitForReport(),
            $this->genTimeLimitForReport()
        ];

        return $this->_filterArray(compact('amount_date', 'apikey', 'product_id', 'operator', 'crawler', 'province'));
    }

    /**
     * 列表统计
     * @throws \Exception
     */
    public function list(): array
    {
        // 校验
        $this->validateParamsForList();

        // 生成限制
        $where = $this->genLimitParamsForList();

        // 统计信息
        $list = $this->getStatInfoForList($where);

        return $this->_appendCrawlerTelForList($list);
    }

    /**
     * 生成列表参数
     * @return array
     * @throws \Exception
     */
    public function genLimitParamsForList(): array
    {
        // operator
        list($operator, $crawler, $province, $amount_date, $apikey) = [
            trim(request()->post('operator')),
            trim(request()->post('crawler')),
            trim(request()->post('province')),
            $this->genTimeLimitForList(),
            $this->genApikeyLimitForList()
        ];

        $product_id = (int)$this->product_id;
        return $this->_filterArray(compact('amount_date', 'apikey', 'product_id', 'operator', 'crawler', 'province'));
    }

    /**
     * 详情统计
     * @return array
     * @throws \Exception
     */
    public function detail(): array
    {
        // 校验参数
        $this->validateParamsForDetail();

        // 条件限制
        $where = $this->genLimitParamsForDetail();

        // 统计信息
        $list = $this->getStatInfoForDetail($where);

        return $this->_appendCrawlerTelForDetail($list, $where);
    }

    /**
     * 详情限制
     * @return array
     */
    private function genLimitParamsForDetail(): array
    {
        list($operator, $crawler, $province, $product_id, $apikey, $amount_date) = [
            trim(request()->post('operator')),
            trim(request()->post('crawler')),
            trim(request()->post('province')),
            (int)$this->product_id,
            $this->genApikeyLimitForDetail(),
            $this->genTimeLimitForDetail()
        ];

        return $this->_filterArray(compact('amount_date', 'apikey', 'product_id', 'operator', 'crawler', 'province'));
    }
}