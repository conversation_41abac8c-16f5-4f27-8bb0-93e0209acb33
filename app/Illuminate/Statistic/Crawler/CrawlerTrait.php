<?php

namespace App\Illuminate\Statistic\Crawler;

use App\Models\MongoLog;
use Illuminate\Support\Facades\DB;
use App\Models\MongoCrawlerTels;

trait CrawlerTrait
{
    private $list_tel_list = [];

    /**
     * 重置为空
     */
    public function setListTelList()
    {
        $this->list_tel_list = [];
    }

    /**
     * 过滤
     * @param array $params
     * @return array
     */
    private function _filterArray(array $params): array
    {
        return array_filter($params, function ($item) {
            return $item;
        });
    }

    /**
     * @param array $list
     * @param array $where
     * @return array
     */
    private function _appendUniqueTelToReport(array $list, array $where): array
    {
        $list['total']['unique_tel_nums'] = $this->_getUniqueTelNumber($where);
        $list['items'] = array_map(function ($item) use ($where) {
            $where['apikey'] = $item['apikey'];
            $item['unique_tel_nums'] = $this->_getUniqueTelNumber($where);
            return $item;
        }, $list['items']);

        return $list;
    }

    /**
     * 附加去重电话号码
     * @param array $list
     * @param array $where
     * @return array
     */
    private function _appendCrawlerTelForDay(array $list, array $where): array
    {
        $this->_setUniqueTelNumberForDay($where);

        // 总计附加值
        $all = $this->list_tel_list['all'] ?? [];
        $list['total']['unique_tel_nums'] = count($all);


        // 每天去重后的调用量
        $list['items'] = array_map(function ($item) {

            return array_map(function ($item_day){
                $key = $item_day['amount_date'] . $item_day['apikey'];
                $list_amount = $this->list_tel_list['list_amount_apikey'] ?? [];
                $list_tels = $list_amount[$key] ?? [];
                $item['unique_tel_nums'] =count($list_tels);
                return $item_day;
            }, $item);
        }, $list['items']);
        return $list;
    }

    /**
     * 去重号码数量
     * @param array $where
     */
    private function _setUniqueTelNumberForDay(array $where)
    {
        // 初始化属性
        $this->setListTelList();

        // 获取mongo 游标
        $option = [
            'projection' => [
                'tel' => 1,
                'amount_date' => 1,
                'apikey' => 1,
                '_id' => 0
            ]
        ];
        $cursor = DB::connection('mongodb_backend')->collection('crawler_tels')->raw(function ($collection) use ($where, $option) {
            return $collection->find($where, $option);
        });
        $memory_start  = memory_get_usage();

        // 设置电话号码列表
        $start_time = microtime(true);
        $i = 0;
        foreach ($cursor as $item) {
            $i++;
            $key = $item->amount_date . $item->apikey;
            $this->list_tel_list['all'][$item->tel] = '';
            $this->list_tel_list['list_amount_apikey'][$key][$item->tel] = '';
        }

        $end_time = microtime(true);
        $memory_end = memory_get_usage();
        $memory_now = $memory_end/(1024*1024);
        $memory_need = ($memory_end - $memory_start)/(1024*1024);
        $memory_get_peak_usage  = memory_get_peak_usage()/(1024*1024);

        $msg = '消耗内存 ： ' . $memory_need . 'MB , 总分配: ' . $memory_get_peak_usage . ' MB 当前消耗: ' . $memory_now. ' MB ' . $this->product_id . ' 插入的数量 : ' . $i . ' 消耗时间 ' . ($end_time - $start_time) . '秒, 去重后的数量' . count($this->list_tel_list['all'] ?? []);
        $action = 'unqiue';
        $params = request()->post();
        MongoLog::create(compact('msg', 'action', 'params'));
    }


    /**
     * 附加去重电话号码
     * @param array $list
     * @param array $where
     * @return array
     */
    private function _appendCrawlerTelForDetail(array $list, array $where): array
    {
        $this->_setUniqueTelNumberForDetail($where);

        // 总计附加值
        $all = $this->list_tel_list['all'] ?? [];
        $list['total']['unique_tel_nums'] = count($all);

        // 每天去重后的调用量
        $list['items'] = array_map(function ($item) use ($where) {
            $list_amount = $this->list_tel_list['list_amount'] ?? [];
            $list_amount_tels = $list_amount[$item['amount_date']] ?? [];
            $item['unique_tel_nums'] =count($list_amount_tels);
            return $item;
        }, $list['items']);

        return $list;
    }


    /**
     * 去重号码数量
     * @param array $where
     */
    private function _setUniqueTelNumberForDetail(array $where)
    {
        // 初始化属性
        $this->setListTelList();

        // 获取mongo 游标
        $option = [
            'projection' => [
                'tel' => 1,
                'amount_date' => 1,
                '_id' => 0
            ]
        ];
        $cursor = DB::connection('mongodb_backend')->collection('crawler_tels')->raw(function ($collection) use ($where, $option) {
            return $collection->find($where, $option);
        });
        $memory_start  = memory_get_usage();

        // 设置电话号码列表
        $start_time = microtime(true);
        $i = 0;
        foreach ($cursor as $item) {
            $i++;
            $this->list_tel_list['all'][$item->tel] = '';
            $this->list_tel_list['list_amount'][$item->amount_date][$item->tel] = '';
        }

        $end_time = microtime(true);
        $memory_end = memory_get_usage();
        $memory_now = $memory_end/(1024*1024);
        $memory_need = ($memory_end - $memory_start)/(1024*1024);
        $memory_get_peak_usage  = memory_get_peak_usage()/(1024*1024);

        $msg = '消耗内存 ： ' . $memory_need . 'MB , 总分配: ' . $memory_get_peak_usage . ' MB 当前消耗: ' . $memory_now. ' MB ' . $this->product_id . ' 插入的数量 : ' . $i . ' 消耗时间 ' . ($end_time - $start_time) . '秒, 去重后的数量' . count($this->list_tel_list['all'] ?? []);
        $action = 'unqiue';
        $params = request()->post();
        MongoLog::create(compact('msg', 'action', 'params'));
    }

    /**
     * 获取爬去号码量
     * @param array $list
     * @return array
     */
    private function _appendCrawlerTelForList(array $list): array
    {
        $where = $this->genLimitParamsForList();
     
        // 设置去重电话号码的信息
        $this->_setUniqueTelNumberForList($where);

        // 总计附加值
        $all = $this->list_tel_list['all'] ?? [];
        $list['total']['unique_tel_nums'] = count($all);

        $list['items'] = array_map(function ($item) use ($where) {
            $list_apikey = $this->list_tel_list['list_apikey'] ?? [];
            $list_apikey_tels = $list_apikey[$item['apikey']] ?? [];
            $item['unique_tel_nums'] =count($list_apikey_tels);
            return $item;
        }, $list['items']);

        return $list;
    }


    /**
     * 去重号码数量
     * @param array $where
     */
    private function _setUniqueTelNumberForList(array $where)
    {
        // 初始化属性
        $this->setListTelList();

        // 获取mongo 游标
        $option = [
            'projection' => [
                'tel' => 1,
                'apikey' => 1,
                '_id' => 0
            ],
        ];
        $cursor = DB::connection('mongodb_backend')->collection('crawler_tels')->raw(function ($collection) use ($where, $option) {
            return $collection->find($where, $option);
        });
        $memory_start  = memory_get_usage();

        // 设置电话号码列表
        $start_time = microtime(true);
        $i = 0;
        foreach ($cursor as $item) {
            $i++;
            $this->list_tel_list['all'][$item->tel] = '';
            $this->list_tel_list['list_apikey'][$item->apikey][$item->tel] = '';
        }

        $end_time = microtime(true);
        $memory_end = memory_get_usage();
        $memory_now = $memory_end/(1024*1024);
        $memory_need = ($memory_end - $memory_start)/(1024*1024);
        $memory_get_peak_usage  = memory_get_peak_usage()/(1024*1024);

        $msg = '消耗内存 ： ' . $memory_need . 'MB , 总分配: ' . $memory_get_peak_usage . ' MB 当前消耗: ' . $memory_now. ' MB ' . $this->product_id . ' 插入的数量 : ' . $i . ' 消耗时间 ' . ($end_time - $start_time) . '秒, 去重后的数量' . count($this->list_tel_list['all'] ?? []);
        $action = 'unqiue';
        $params = request()->post();
        MongoLog::create(compact('msg', 'action', 'params'));
    }

    /**
     * 去重号码数量
     * @param array $where
     * @return int
     */
    private function _getUniqueTelNumber(array $where): int
    {
        return MongoCrawlerTels::where($where)
            ->distinct('tel')
            ->get()
            ->count();
    }
}