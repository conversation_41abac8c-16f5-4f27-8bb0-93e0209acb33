<?php

namespace App\Illuminate\Statistic\CuishouShort;

use App\Illuminate\Statistic\Driver;
use App\Illuminate\Statistic\{
    ValidateTrait, StatTrait, DayInvokedTrait, ReportTrait
};

class CuishouShort276Driver implements Driver
{
    use ValidateTrait, StatTrait, DayInvokedTrait, ReportTrait;

    public $product_id = 276;

    /**
     * 日调用信息
     * @throws \Exception
     */
    public function day(): array
    {
        // 校验条件
        $this->validateParamsForDay();

        // 获取调用信息
        return $this->dayInvoked();
    }

    /**
     * 日报
     * @return array
     * @throws \Exception
     */
    public function report(): array
    {
        // 检查参数
        $this->validateParamsForReport();

        // 统计信息
        return $this->getReportInfo();
    }

    /**
     * 列表统计
     * @throws \Exception
     */
    public function list(): array
    {
        // 校验
        $this->validateParamsForList();

        // 生成限制
        $where = $this->genLimitParamsForList();

        // 统计信息
        return $this->getStatInfoForList($where);
    }

    /**
     * 详情统计
     * @return array
     * @throws \Exception
     */
    public function detail(): array
    {
        // 校验参数
        $this->validateParamsForDetail();

        // 条件限制
        $where = $this->genLimitParamsForDetail();

        // 统计信息
        return $this->getStatInfoForDetail($where);
    }
}
