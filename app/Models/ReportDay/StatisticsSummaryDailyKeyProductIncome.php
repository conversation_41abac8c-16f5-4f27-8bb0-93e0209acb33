<?php

namespace App\Models\ReportDay;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class StatisticsSummaryDailyKeyProductIncome extends Model
{
    public $table = 'statistics_summary_daily_key_prodcut_income';
    public $timestamps = false;


    public static function getListByCondition(array $where, $fields = null, $sortBy = null)
    {
        return static::where($where)
            ->when($sortBy, function ($query, $sortBy) {
                return $query->orderByRaw($sortBy);
            })
            ->when($fields, function ($query, $fields) {
                return $query->select($fields);
            })->get();
    }


    public static function deleteById($ids)
    {
        if (!$ids) {
           return false;
        }

        return self::whereIn('id', $ids)->delete();
    }
}