<?php
/**
 * 统计相关常量定义
 */

namespace App\Define;


class StatDefine
{
    /**
     * 查看视角
     */
    const OP_ANGLE = 0;
    const PM_ANGLE = 1;
    /**
     * 查看视角map
     * 运营查看收入按call_product_id走
     * 产品查看收入按product_id走
     */
    public static $viewMap = [
        self::OP_ANGLE => '运营视角',
        self::PM_ANGLE => '产品视角'
    ];

    /**
     * 统计维度
     */
    const DIMENSION_HUIZONG = 0;
    const DIMENSION_DEVELOPER = 1001;
    const DIMENSION_APIKEY_PRODUCT_OPERATOR = 11;
    const INCOME_DIMENSION_SOURCE = 22;
    const INCOME_DIMENSION_DATE = 33;
    const INCOME_DIMENSION_DATE_CUSTOMER = 44;
    const INCOME_DIMENSION_CUSTOMER = 55;
    const INCOME_DIMENSION_DATE_PRODUCT = 1002;
    const INCOME_DIMENSION_PRODUCT_CUSTOMER_OP_SOURCE = 1003;

    //成本统计维度--------------
    const COST_DIMENSION_MONTH_CHANNEL = 2001;
    const COST_DIMENSION_DATE_PRODUCT = 2002;
    const COST_DIMENSION_CHANNEL_PRODUCT_CUSTOMER_SOURCE = 2003;
    const DIMENSION_DATE_PRODUCT = 1002;
    //客户用量统计维度--------------
    const USAGE_DIMENSION_DATE_CUSTOMER = 3001;

    /**
     * 统计维度map
     * @var int[]
     */
    public static $dimensionMap = [
        self::DIMENSION_HUIZONG => '汇总维度统计',
        self::DIMENSION_APIKEY_PRODUCT_OPERATOR => '账号产品运营商维度统计',
        self::INCOME_DIMENSION_SOURCE => '收入按source维度',
        self::INCOME_DIMENSION_DATE => '收入按date维度',
        self::INCOME_DIMENSION_DATE_CUSTOMER => '收入按date、customer维度',
        self::INCOME_DIMENSION_CUSTOMER => '收入按customer维度',
        self::INCOME_DIMENSION_DATE_PRODUCT => '收入按照date、product维度',
        self::INCOME_DIMENSION_PRODUCT_CUSTOMER_OP_SOURCE => '收入按照product、customer、operator、source维度',
        self::DIMENSION_DEVELOPER => '开发维度',
        //----------- 下面成本相关的 ----------
        self::COST_DIMENSION_MONTH_CHANNEL => '成本按月、渠道维度',
        self::COST_DIMENSION_DATE_PRODUCT => '成本按照date、product维度',
        self::COST_DIMENSION_CHANNEL_PRODUCT_CUSTOMER_SOURCE => '成本按照渠道、产品、客户、来源维度',
        //----------- 客户调用量相关的 ----------
        self::USAGE_DIMENSION_DATE_CUSTOMER => '用量按date、customer维度',
        self::DIMENSION_DEVELOPER => '开发维度',
        self::DIMENSION_DATE_PRODUCT => '收入按照date、product纬度'
    ];

    /**
     * 特殊账号apikey
     */
    const CUSTOMER_EXPEND = 'customer_expend';
    const CUSTOMER_ADJUST = 'customer_adjust';
    const FIXED_ADJUST = 'fixed_adjust';
    const CHANNEL_ADJUST = 'channel_adjust';

    /**
     * 特殊账号map
     * @var int[]
     */
    public static $apikeyMap = [
        self::CUSTOMER_EXPEND => '特殊消耗',
        self::CUSTOMER_ADJUST => '客户调整',
        self::FIXED_ADJUST => '固定费用',
        self::CHANNEL_ADJUST => '渠道调整',
    ];

    /**
     * 成本类型
     */
    const COST_TYPE_BILL = 0;//普通账单成本
    const COST_TYPE_CHANNEL_ADJUST = 1;//渠道成本调整
    const COST_TYPE_CUSTOMER_ADJUST = 2;//客户成本调整
    const COST_TYPE_FIXED_ADJUST = 3;//固定费用成本





}