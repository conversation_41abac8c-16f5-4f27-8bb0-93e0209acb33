<?php

namespace App\Define;

/**
 *
 * Class PreTestMonitor 常量
 * @package App\Define
 */
class PreTestMonitor
{

    /**
     * [调用量级稳定] 审批定义模板code
     */
    const APPROVAL_CODE_CALL_STABLE = '31C2A52C-2519-4DD8-93AE-D21F12FA9F29';
    /**
     * [灰度上线时间] 审批定义模板code
     */
    const APPROVAL_CODE_GRAYSCALE = '1FF12876-597D-43B2-813A-D7B74117DD4F';
    /**
     * [超出预计上线时间] 审批定义模板code
     */
    const APPROVAL_CODE_EXPECT_ONLINE = '7DE45851-179F-4E1D-9892-16F197E373DA';
    /**
     * [已开通未联调状态确认] 审批定义模板code
     */
    const APPROVAL_CODE_UNDONE_DEBUG = '7021472B-F87A-454C-A924-1664D9DB124D';
    /**
     * [已联调未转正状态确认] 审批定义模板code
     */
    const APPROVAL_CODE_UNDONE_FEE = 'B0584522-5451-44DE-B5AF-7C58186D7791';
    /**
     *
     */
    const APPROVAL_CODE_MAP = [
        self::APPROVAL_CODE_CALL_STABLE => '调用量级稳定',
        self::APPROVAL_CODE_GRAYSCALE => '灰度上线时间',
        self::APPROVAL_CODE_EXPECT_ONLINE => '超出预计上线时间',
        self::APPROVAL_CODE_UNDONE_DEBUG => '已开通未联调状态确认',
        self::APPROVAL_CODE_UNDONE_FEE => '已联调未转正状态确认',
    ];

    /**
     * 客户监控 不统计用户
     */
    const BAN_CUSTOMER_LIST = [
        'C20180828LOCNMG', // 电话邦内部
        'C20200622KF31GS', // 售前测试
        'C202112223GQOG8', // 朴道
        'C20230801HY2WHW', // 浙数交（征信）
        'C20231113E66N9F', // 技术部-线上
        'C20250312N7RJ1A', // 钱塘征信
    ];

    /**
     *  原因类别
     */
    const REASON_TYPE_1 = 1; // 效果原因
    const REASON_TYPE_2 = 2; // 客户原因
    const REASON_TYPE_3 = 3; // 价格原因
    const REASON_TYPE_4 = 4; // 其他原因
    const REASON_TYPE_MAP = [
        self::REASON_TYPE_1 => '效果原因',
        self::REASON_TYPE_2 => '客户原因',
        self::REASON_TYPE_3 => '价格原因',
        self::REASON_TYPE_4 => '其他原因',
    ];

    /**
     * [调用量级稳定] 表单字段配置
     */
    const CALL_STABLE_FORM = [
        [
            'id' => 'widget17447047077130001',
            'name' => '触发次数',
            'type' => 'radioV2',
            'field' => 'notice_times',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447047557860001',
            'name' => '客户简称',
            'type' => 'input',
            'field' => 'company_short_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447047613600001',
            'name' => '产品',
            'type' => 'textarea',
            'field' => 'father_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447048566590001',
            'name' => '日均量级',
            'type' => 'input',
            'field' => 'daily_number',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447048779470001',
            'name' => '目前调用量级是否稳定',
            'type' => 'radioV2',
            'field' => 'is_stable',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447049201520001',
            'name' => '预估日均量级',
            'type' => 'input',
            'field' => 'daily_number_estimate',
            'is_form' => false,
        ]
    ];

    /**
     * [灰度上线时间提醒] 表单字段配置
     */
    const GRAYSCALE_FORM = [
        [
            'id' => 'widget17447028996630001',
            'name' => '触发次数',
            'type' => 'radioV2',
            'field' => 'notice_times',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447024469930001',
            'name' => '客户简称',
            'type' => 'input',
            'field' => 'company_short_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447024885400001',
            'name' => '产品',
            'type' => 'textarea',
            'field' => 'father_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447025071600001',
            'name' => '灰度开始时间',
            'type' => 'input',
            'field' => 'grayscale_start_time',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447025142150001',
            'name' => '灰度结束时间',
            'type' => 'input',
            'field' => 'grayscale_end_time',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447025374250001',
            'name' => '是否继续灰度',
            'type' => 'radioV2',
            'field' => 'is_continue_gray',
            'is_form' => false,
        ],
        [
            'id' => 'widget17496380494990001',
            'name' => '原因类别',
            'type' => 'radioV2',
            'field' => 'reason_type',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447039366960001',
            'name' => '请说明延期原因',
            'type' => 'textarea',
            'field' => 'reason',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447027436440001',
            'name' => '请说明原因',
            'type' => 'textarea',
            'field' => 'reason',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447026849580001',
            'name' => '预计灰度开始时间',
            'type' => 'date',
            'field' => 'new_grayscale_start_time',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447026933080001',
            'name' => '预计灰度开始时间',
            'type' => 'date',
            'field' => 'new_grayscale_end_time',
            'is_form' => false,
        ]
    ];

    /**
     * [超出预计上线时间] 表单字段配置
     */
    const EXPECT_ONLINE_FORM = [
        [
            'id' => 'widget17447038678630001',
            'name' => '触发次数',
            'type' => 'radioV2',
            'field' => 'notice_times',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447038919770001',
            'name' => '客户简称',
            'type' => 'input',
            'field' => 'company_short_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447039043870001',
            'name' => '产品',
            'type' => 'textarea',
            'field' => 'father_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447038978580001',
            'name' => '预计上线时间',
            'type' => 'input',
            'field' => 'expect_online_time',
            'is_form' => true,
        ],
        [
            'id' => 'widget17447040533750001',
            'name' => '是否延期上线',
            'type' => 'radioV2',
            'field' => 'is_extend',
            'is_form' => false,
        ],
        [
            'id' => 'widget17496383070440001',
            'name' => '原因类别',
            'type' => 'radioV2',
            'field' => 'reason_type',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447040985450001',
            'name' => '延期上线原因',
            'type' => 'textarea',
            'field' => 'reason',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447041381920001',
            'name' => '请说明原因',
            'type' => 'textarea',
            'field' => 'reason',
            'is_form' => false,
        ],
        [
            'id' => 'widget17447040724610001',
            'name' => '最新预计上线时间',
            'type' => 'date',
            'field' => 'new_expect_online_time',
            'is_form' => false,
        ]
    ];

    /**
     * [已开通未联调状态确认] 表单字段配置
     */
    const UNDONE_DEBUG_FORM = [
        [
            'id' => 'widget17525705175150001',
            'name' => '客户简称',
            'type' => 'input',
            'field' => 'company_short_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17525705323670001',
            'name' => '产品',
            'type' => 'textarea',
            'field' => 'father_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17525705582880001',
            'name' => '开通时间',
            'type' => 'input',
            'field' => 'activate_time',
            'is_form' => true,
        ],
        [
            'id' => 'widget17525707257020001',
            'name' => '不上线原因类别',
            'type' => 'radioV2',
            'field' => 'reason_type',
            'is_form' => false,
        ],
        [
            'id' => 'widget17525707557570001',
            'name' => '请说明具体原因',
            'type' => 'textarea',
            'field' => 'reason',
            'is_form' => false,
        ]
    ];

    /**
     * [已联调未转正状态确认] 表单字段配置
     */
    const UNDONE_FEE_FORM = [
        [
            'id' => 'widget17525711897780001',
            'name' => '客户简称',
            'type' => 'input',
            'field' => 'company_short_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17525712020050001',
            'name' => '产品',
            'type' => 'textarea',
            'field' => 'father_name',
            'is_form' => true,
        ],
        [
            'id' => 'widget17525712090250001',
            'name' => '开通时间',
            'type' => 'input',
            'field' => 'activate_time',
            'is_form' => true,
        ],
        [
            'id' => 'widget17525712190220001',
            'name' => '联调时间',
            'type' => 'input',
            'field' => 'debug_time',
            'is_form' => true,
        ],
        [
            'id' => 'widget17525712387480001',
            'name' => '不上线原因类别',
            'type' => 'radioV2',
            'field' => 'reason_type',
            'is_form' => false,
        ],
        [
            'id' => 'widget17525712913990001',
            'name' => '请说明具体原因',
            'type' => 'textarea',
            'field' => 'reason',
            'is_form' => false,
        ]
    ];
}