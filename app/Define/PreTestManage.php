<?php

namespace App\Define;

/**
 *
 * Class PreTestManage 常量
 * @package App\Define
 */
class PreTestManage
{

    /**
     * [售前测试流程-新版] 审批定义模板code
     */
    const APPROVAL_CODE_TEST = 'A743783F-3E0D-4A70-B1CF-254E9EE59E9A';
    /**
     * [售前测试&测试账号申请] 审批定义模板code
     */
    const APPROVAL_CODE_TEST_WITH_ACCOUNT = '37E23EE8-E880-4A8B-A291-C00E50B54A23';
    /**
     * [产品建模&定制申请] 审批定义模板code
     */
    const APPROVAL_CODE_TEST_WITH_MODEL = 'BD65205C-84C4-437B-8BE9-ABDED8451972';

    /**
     * 审批实例状态
     * PENDING：审批中
     * RECALL：撤回
     * REJECT：拒绝
     * DELETED：已删除
     * APPROVED：通过
     * ALL：所有状态
     */
    const INSTANCE_STATUS_PENDING = 'PENDING';
    const INSTANCE_STATUS_RECALL = 'RECALL'; // 终态
    const INSTANCE_STATUS_CANCELED = 'CANCELED'; // 终态
    const INSTANCE_STATUS_REJECT = 'REJECT'; // 终态
    const INSTANCE_STATUS_REJECTED = 'REJECTED'; // 终态
    const INSTANCE_STATUS_DELETED = 'DELETED';
    const INSTANCE_STATUS_APPROVED = 'APPROVED'; // 终态
    const INSTANCE_STATUS_ALL = 'ALL';
    const INSTANCE_STATUS_MAP = [
        self::INSTANCE_STATUS_PENDING => '审批中',
        self::INSTANCE_STATUS_RECALL => '撤回',
        self::INSTANCE_STATUS_CANCELED => '取消',
        self::INSTANCE_STATUS_REJECTED => '已拒绝',
        self::INSTANCE_STATUS_DELETED => '删除',
        self::INSTANCE_STATUS_APPROVED => '通过',
    ];

    /**
     * status : 测试状态
     * 5: 待测试（初始值/默认值）
     * 10: 测试中
     * 15: 已终止 测试暂停/终止
     * 20: 待反馈 售前或技术测试完毕，已返回测试结果, 等待客户反馈结果
     *   21: 已逾期
     *   23: 超3个月未反馈
     * 30: 已反馈 已反馈结果，等待接入意向反馈
     * 40: 不接入 无法接入，终态
     * 45: 已撤回 申请撤回
     * 50: 可接入
     * 60: 已接入
     */
    const STATUS_WAITING = 5;
    const STATUS_TESTING = 10;
    const STATUS_TEST_ABORT = 15;
    const STATUS_TEST_DONE = 20;
    const STATUS_SCHEDULE_UNDONE = 21;
    const STATUS_SCHEDULE_OVER3 = 23;
    const STATUS_FEEDBACK = 30;
    const STATUS_ACCESS_UNABLE = 40;
    const STATUS_WITHDRAW = 45;
    const STATUS_ACCESS_ABLE = 50;
    const STATUS_ACCESS_DONE = 60;

    const STATUS_TEXT_MAP = [
        self::STATUS_WAITING => '待测试',
        self::STATUS_TESTING => '测试中',
        self::STATUS_TEST_ABORT => '已终止',
        self::STATUS_TEST_DONE => '待反馈',
        self::STATUS_FEEDBACK => '已反馈',
        self::STATUS_ACCESS_UNABLE => '不接入',
//        self::STATUS_WITHDRAW => '已撤回',
        self::STATUS_ACCESS_ABLE => '可接入',
        self::STATUS_ACCESS_DONE => '已接入',
    ];

    const STATUS_TEXT_MAP_SUB = [
        self::STATUS_TEST_DONE => [
            self::STATUS_TEST_DONE => '待反馈',
            self::STATUS_SCHEDULE_UNDONE => '已逾期',
            self::STATUS_SCHEDULE_OVER3 => '超3个月未反馈',
        ],
    ];

    /**
     * [销售]保存接入信息时, 可操作的测试状态限制list
     */
    const CAN_SAVE_ACCESS_STATUS = [
        self::STATUS_TEST_DONE,
        self::STATUS_FEEDBACK,
        self::STATUS_ACCESS_UNABLE,
        self::STATUS_WITHDRAW,
        self::STATUS_ACCESS_ABLE,
    ];

    /**
     * [售前]修改测试状态时, 可操作的测试状态限制list
     */
    const CAN_MODIFY_STATUS = [
        self::STATUS_WAITING,
        self::STATUS_TESTING,
        self::STATUS_TEST_ABORT,
        self::STATUS_TEST_DONE,
    ];

    /**
     * 测试状态优先级(数字越大,优先级越高)
     */
    const STATUS_PRIORITY = [
        self::STATUS_ACCESS_DONE => 100,
        self::STATUS_ACCESS_ABLE => 90,
        self::STATUS_TESTING => 80,
        self::STATUS_WAITING => 75,
        self::STATUS_TEST_DONE => 70,
        self::STATUS_FEEDBACK => 60,
        self::STATUS_ACCESS_UNABLE => 50,
        self::STATUS_TEST_ABORT => 40,
        self::STATUS_WITHDRAW => 30,
    ];

    /**
     * company_type : 客户类型
     * 0: 默认值
     * 1: 大型股份制银行
     * 2: 小型银行(城商行)
     * 3: 持牌消金
     * 4: 小额贷款公司
     * 5: 互金
     * 6: 汽车金融
     * 7: 数据公司/金融科技公司
     * 8: 保险
     * 9: 助贷
     * 10: 其他
     * 11: 银行
     */
    const COMPANY_TYPE_DEFAULT = 0;
    const COMPANY_TYPE_LARGE_BANK = 1;
    const COMPANY_TYPE_SMALL_BANK = 2;
    const COMPANY_TYPE_CONSUMER_FINANCE = 3;
    const COMPANY_TYPE_LOAN_COMPANY = 4;
    const COMPANY_TYPE_ITFIN = 5;
    const COMPANY_TYPE_AUTO_FINANCE = 6;
    const COMPANY_TYPE_DATA_COMPANY = 7;
    const COMPANY_TYPE_INSURE_COMPANY = 8;
    const COMPANY_TYPE_ASSIST_LOAN = 9;
    const COMPANY_TYPE_OTHER = 10;
    const COMPANY_TYPE_BANK = 11;

    const COMPANY_TYPE_TEXT_MAP = [
        self::COMPANY_TYPE_LARGE_BANK => '大型股份制银行',
        self::COMPANY_TYPE_SMALL_BANK => '小型银行/城商行',
        self::COMPANY_TYPE_CONSUMER_FINANCE => '持牌消金',
        self::COMPANY_TYPE_LOAN_COMPANY => '小额贷款',
        self::COMPANY_TYPE_ITFIN => '互金',
        self::COMPANY_TYPE_AUTO_FINANCE => '汽车金融',
        self::COMPANY_TYPE_DATA_COMPANY => '数据/金融科技',
        self::COMPANY_TYPE_INSURE_COMPANY => '保险',
        self::COMPANY_TYPE_ASSIST_LOAN => '助贷',
        self::COMPANY_TYPE_OTHER => '其他',
        self::COMPANY_TYPE_BANK => '银行',
    ];

    /**
     * 反馈期限为1个月
     * 其他则为2周
     */
    const LONG_SCHEDULE_LIMIT = [
        self::COMPANY_TYPE_LARGE_BANK,
        self::COMPANY_TYPE_SMALL_BANK,
        self::COMPANY_TYPE_CONSUMER_FINANCE,
    ];

    /**
     * 客户类型 飞书枚举值
     */
    // [售前测试流程-新版], [售前测试&测试账号] 枚举值
    const ENUM_COMPANY_TYPE_LARGE_BANK = 'option_0'; // 大型股份制银行
    const ENUM_COMPANY_TYPE_SMALL_BANK = 'option_1'; // 小型银行(城商行)
    const ENUM_COMPANY_TYPE_CONSUMER_FINANCE = 'option_2'; // 持牌消金
    const ENUM_COMPANY_TYPE_LOAN_COMPANY = 'option_XTALPDY4CPC0'; // 小额贷款公司
    const ENUM_COMPANY_TYPE_ITFIN = 'option_VOPBCRWCNR40'; // 互金
    const ENUM_COMPANY_TYPE_AUTO_FINANCE = 'option_18YS1A6L3N8G0'; // 汽车金融
    const ENUM_COMPANY_TYPE_DATA_COMPANY = 'option_T6SER0RNXV40'; // 数据公司/金融科技公司
    const ENUM_COMPANY_TYPE_INSURE_COMPANY = 'option_UH6R40WSY5C0'; // 保险
    const ENUM_COMPANY_TYPE_ASSIST_LOAN = 'option_1D1R71SZ1ZDS0'; // 助贷
    const ENUM_COMPANY_TYPE_OTHER = 'option_Z38P6UJZ2TC0'; // 其他
    // [产品建模&定制申请] 枚举值
    const ENUM_COMPANY_TYPE_ITFIN_1 = 'ly5oozlp-ov77bme1rs-0'; // 互金
    const ENUM_COMPANY_TYPE_CONSUMER_FINANCE_1 = 'ly5oozlp-2ac9q6mq6x8-0'; // 持牌消金
    const ENUM_COMPANY_TYPE_BANK = 'm53pw4zc-4w9modrkkk9-0'; // 银行
    const ENUM_COMPANY_TYPE_OTHER_1 = 'm53pw4zc-lromz2q892g-1'; // 其他

    /**
     * 客户类型 飞书枚举值 : 系统值 map
     */
    const COMPANY_TYPE_ENUM_MAP = [
        // [售前测试流程-新版], [售前测试&测试账号] 枚举值
        self::ENUM_COMPANY_TYPE_LARGE_BANK => self::COMPANY_TYPE_LARGE_BANK,
        self::ENUM_COMPANY_TYPE_SMALL_BANK => self::COMPANY_TYPE_SMALL_BANK,
        self::ENUM_COMPANY_TYPE_CONSUMER_FINANCE => self::COMPANY_TYPE_CONSUMER_FINANCE,
        self::ENUM_COMPANY_TYPE_LOAN_COMPANY => self::COMPANY_TYPE_LOAN_COMPANY,
        self::ENUM_COMPANY_TYPE_ITFIN => self::COMPANY_TYPE_ITFIN,
        self::ENUM_COMPANY_TYPE_AUTO_FINANCE => self::COMPANY_TYPE_AUTO_FINANCE,
        self::ENUM_COMPANY_TYPE_DATA_COMPANY => self::COMPANY_TYPE_DATA_COMPANY,
        self::ENUM_COMPANY_TYPE_INSURE_COMPANY => self::COMPANY_TYPE_INSURE_COMPANY,
        self::ENUM_COMPANY_TYPE_ASSIST_LOAN => self::COMPANY_TYPE_ASSIST_LOAN,
        self::ENUM_COMPANY_TYPE_OTHER => self::COMPANY_TYPE_OTHER,
        // [产品建模&定制申请] 枚举值
        self::ENUM_COMPANY_TYPE_ITFIN_1 => self::COMPANY_TYPE_ITFIN,
        self::ENUM_COMPANY_TYPE_CONSUMER_FINANCE_1 => self::COMPANY_TYPE_AUTO_FINANCE,
        self::ENUM_COMPANY_TYPE_BANK => self::COMPANY_TYPE_BANK,
        self::ENUM_COMPANY_TYPE_OTHER_1 => self::COMPANY_TYPE_OTHER,
    ];

    /**
     * sample_source : 样本来源
     * 0: 默认值
     * 1: 科技公司
     * 2: 征信公司
     * 3: 直客
     * 4: 浙数交
     */
    const SAMPLE_SOURCE_DEFAULT = 0;
    const SAMPLE_SOURCE_TECHNOLOGY = 1;
    const SAMPLE_SOURCE_PUDAO_CREDIT = 2;
    const SAMPLE_SOURCE_CUSTOMER = 3;
    const SAMPLE_SOURCE_ZJDEX = 4;

    const SAMPLE_SOURCE_TEXT_MAP = [
        self::SAMPLE_SOURCE_TECHNOLOGY => '科技公司',
        self::SAMPLE_SOURCE_PUDAO_CREDIT => '朴道',
        self::SAMPLE_SOURCE_CUSTOMER => '直客',
        self::SAMPLE_SOURCE_ZJDEX => '浙数交',
    ];

    /**
     * 样本来源 飞书枚举值
     */
    const ENUM_SAMPLE_SOURCE_TECHNOLOGY = 'lssmlx61-55k4hcgojv5-0';
    const ENUM_SAMPLE_SOURCE_PUDAO_CREDIT = 'lssmlx61-eu00hiahr6g-0';
    const ENUM_SAMPLE_SOURCE_CUSTOMER = 'lsydmgg6-g8yrnwbsjb-1';
    const ENUM_SAMPLE_SOURCE_ZJDEX = 'm4ax02eh-0dv1jrsowf8n-1';

    /**
     * 样本来源 飞书枚举值 : 系统值 map
     */
    const SAMPLE_SOURCE_ENUM_MAP = [
        self::ENUM_SAMPLE_SOURCE_TECHNOLOGY => self::SAMPLE_SOURCE_TECHNOLOGY,
        self::ENUM_SAMPLE_SOURCE_PUDAO_CREDIT => self::SAMPLE_SOURCE_PUDAO_CREDIT,
        self::ENUM_SAMPLE_SOURCE_CUSTOMER => self::SAMPLE_SOURCE_CUSTOMER,
        self::ENUM_SAMPLE_SOURCE_ZJDEX => self::SAMPLE_SOURCE_ZJDEX,
    ];

    /**
     * 0: 否
     * 1: 是
     */
    const  IS_NO_HAVE_CUSTOMER_PRODUCT = -2;
    const IS_MINUS_ONE = -1;
    const IS_MINUS_ALL = -999;
    const IS_NO = 0;
    const IS_YES = 1;

    // 是否人工处理
    const BUSINESS_TYPE_ONE = 1;
    const BUSINESS_TYPE_TWO = 2;

    const WHETHER_TEXT_MAP = [
        self::IS_YES => '是',
        self::IS_NO => '否',
    ];

    /**
     * 打卡操作终态
     */
    const IS_DONE = 2;
    /**
     * 数据来源: 绑定
     */
    const IS_BIND = 10;

    /**
     * test_result : 测试结果
     * 0: 默认值
     * 1: 效果好
     * 2: 效果一般
     * 3: 效果不好
     * 4: 客户未提供, 无法提供
     * 5: 部分好
     * 6: 客户未评估
     * -10: 未获得 (客户未提供 + 客户未评估)
     * -20: 已获得 (好 + 一般 + 不好)
     */
    const TEST_RESULT_DEFAULT = 0;
    const TEST_RESULT_GOOD = 1;
    const TEST_RESULT_AVERAGE = 2;
    const TEST_RESULT_BAD = 3;
    const TEST_RESULT_UNABLE = 4;
    const TEST_RESULT_GOOD_PART = 5;
    const TEST_RESULT_NA = 6;
    const TEST_RESULT_UNDONE = -10;
    const TEST_RESULT_DONE = -20;

    const TEST_RESULT_TEXT_MAP = [
        self::TEST_RESULT_GOOD => '好',
//        self::TEST_RESULT_GOOD_PART => '部分好',
        self::TEST_RESULT_AVERAGE => '一般',
        self::TEST_RESULT_BAD => '不好',
        self::TEST_RESULT_UNABLE => '客户未提供',
        self::TEST_RESULT_NA => '客户未评估',
    ];

    /**
     * (反馈结果)消息来源
     * 1: 产品侧
     * 2: 商务侧
     * 3: 其他
     */
    const FEEDBACK_SOURCE_PRODUCT = 1;
    const FEEDBACK_SOURCE_BUSINESS = 2;
    const FEEDBACK_SOURCE_OTHER = 3;

    const FEEDBACK_SOURCE_TEXT_MAP = [
        self::FEEDBACK_SOURCE_PRODUCT => '产品侧',
        self::FEEDBACK_SOURCE_BUSINESS => '商务侧',
        self::FEEDBACK_SOURCE_OTHER => '其他',
    ];

    /**
     * (反馈结果)能否线下沟通效果
     * 1: 能
     * 2: 不能
     */
    const IS_CAN = 1;
    const IS_CAN_NOT = 2;
    const WHETHER_CAN_TEXT_MAP = [
        self::IS_CAN => '能',
        self::IS_CAN_NOT => '不能',
    ];

    /**
     * access_action : 接入意向
     * 0: 默认值
     * 1: 可以接入
     * 2: 无法接入
     */
    const ACCESS_ACTION_DEFAULT = 0;
    const ACCESS_ACTION_ABLE = 1;
    const ACCESS_ACTION_UNABLE = 2;

    const ACCESS_ACTION_TEXT_MAP = [
        self::ACCESS_ACTION_ABLE => '可以接入',
        self::ACCESS_ACTION_UNABLE => '无法接入',
    ];

    const ACCESS_ACTION_STATUS_MAP = [
        self::ACCESS_ACTION_ABLE => self::STATUS_ACCESS_ABLE,
        self::ACCESS_ACTION_UNABLE => self::STATUS_ACCESS_UNABLE,
    ];

    /**
     * not_access_reason : 无法接入的原因
     * 0: 默认值
     * 1: 客户侧收益不足
     * 2: 价格问题
     * 3: 客户需求不清
     * 4: 不满足客户场景
     * 5: 产品力不足
     * 6: 产品表现异常
     * 7: 法务合规原因
     * 8: 其他
     */
    const UNABLE_REASON_DEFAULT = 0;
    const UNABLE_REASON_INCOME = 1;
    const UNABLE_REASON_PRICE = 2;
    const UNABLE_REASON_DEMAND = 3;
    const UNABLE_REASON_SCENE = 4;
    const UNABLE_REASON_PRODUCT = 5;
    const UNABLE_REASON_PERFORMANCE = 6;
    const UNABLE_REASON_LAW = 7;
    const UNABLE_REASON_OTHER = 8;

    const UNABLE_REASON_TEXT_MAP = [
        self::UNABLE_REASON_INCOME => '客户侧收益不足',
        self::UNABLE_REASON_PRICE => '价格问题',
        self::UNABLE_REASON_DEMAND => '客户需求不清',
        self::UNABLE_REASON_SCENE => '不满足客户场景',
        self::UNABLE_REASON_PRODUCT => '产品力不足',
        self::UNABLE_REASON_PERFORMANCE => '产品表现异常',
        self::UNABLE_REASON_LAW => '法务合规原因',
        self::UNABLE_REASON_OTHER => '其他',
    ];

    /**
     * 测试目的
     * 1: 能
     * 2: 不能
     */
    const IS_DOCKING = 1;
    const IS_IS_DOCKING_NOT = 2;
    const WHETHER_DOCKING_TEXT_MAP = [
        self::IS_DOCKING => '对接',
        self::IS_IS_DOCKING_NOT => '非对接',
    ];

    /**
     * 测试产品 飞书枚举值
     */
    const ENUM_PRODUCT_OPTION_0 = 'option_0'; // 号码分
    const ENUM_PRODUCT_OPTION_1 = 'option_1'; // 号码风险等级
    const ENUM_PRODUCT_OPTION_2 = 'option_2'; // 号码活跃指数
    const ENUM_PRODUCT_OPTION_3 = 'option_1HPWV07GIYKG0'; // 在网状态及时长核验
    const ENUM_PRODUCT_OPTION_3_0 = 'option_0'; // 在网时长
    const ENUM_PRODUCT_OPTION_3_1 = 'option_1'; // 在网状态
    const ENUM_PRODUCT_OPTION_4 = 'option_OYI899SZWXC0'; // 事件分
    const ENUM_PRODUCT_OPTION_5 = 'option_1Q8U3IYPSSYO0'; // 邦信分-通信指数
    const ENUM_PRODUCT_OPTION_6 = 'option_42BXSZNO5N00'; // 金盾-风险验证【50101】
    const ENUM_PRODUCT_OPTION_7 = 'option_79TYG38R3EG'; // 空号检测 (已废弃)
    const ENUM_PRODUCT_OPTION_8 = 'option_5UDYRYK3A080'; // 特征变量
    const ENUM_PRODUCT_OPTION_9 = 'lk0s8na2-icdo2lyzscd-1'; // 号码风险等级-特征变量 (已废弃)
    const ENUM_PRODUCT_OPTION_10 = 'lkuly5z1-pg5gs7z8hn-1'; // 联合建模
    const ENUM_PRODUCT_OPTION_11 = 'lxioxsbq-7fzs01ju7sm-1'; // 号码融-融合产品
    const ENUM_PRODUCT_OPTION_12 = 'm0arf08u-zfgy6tuxf3g-1'; // 号码融内部建模 (已废弃)
    const ENUM_PRODUCT_OPTION_13 = 'lpqpcyjx-ymijbl3hjbo-1'; // 其他产品（ 备注中说明）
    const ENUM_PRODUCT_OPTION_14 = 'm178636k-g9bnzk7q89h-1'; // 易诉
    const ENUM_PRODUCT_OPTION_15 = 'm4b53oha-tysbqt5h8k-1'; // 号码融-内部产品
    const ENUM_PRODUCT_OPTION_16 = 'lrsu8qz1-1th9opsb1cb-1'; // 金盾-策略字段 (已废弃)
    const ENUM_PRODUCT_OPTION_17 = 'm7brt1mn-9pkiicb4loq-2'; // 号码融
    const ENUM_PRODUCT_OPTION_18 = 'm7brt1mn-3qhgfy4swi3-5'; // 金盾
    const ENUM_PRODUCT_OPTION_19 = 'm7brt1mn-tzj6lz8eqpc-8'; // 联合建模-仅售前测试使用
    const ENUM_PRODUCT_OPTION_20 = 'm7brt1mn-3q3f6np7ui-3'; // 邦秒验
    const ENUM_PRODUCT_OPTION_21 = 'm7brt1mn-4w9vcyhd3ge-7'; // 邦信分-通信评分
    const ENUM_PRODUCT_OPTION_43 = 'm847lkvy-n6v562c3mjo-1'; // 存量洞察建模
    const ENUM_PRODUCT_OPTION_44 = 'm6t12jkb-4pqg946o6z5-0'; // 自有产品建模
    const ENUM_PRODUCT_OPTION_45 = 'm6t12jkb-ejs7dcl1w67-0'; // 三方数据融合建模
    const ENUM_PRODUCT_OPTION_46 = 'm6t12jkb-2mbbeq4xkyg-0'; // 运营商建模
    const ENUM_PRODUCT_OPTION_48 = 'mbop34qp-e1jzlsaxirn-1'; // 易诉建模
    const ENUM_PRODUCT_OPTION_49 = 'm7brt1mn-3q3f6np7ui-3'; // 邦秒验

    /**
     * [产品建模&定制申请] 指定主产品
     */
    const APPROVAL_MODEL_FATHER = [
        [
            'key' => self::ENUM_PRODUCT_OPTION_19,
            'text' => '联合建模-仅售前测试使用',
        ],
    ];

    /**
     * 实际样本量【邦信分】：通信指数, 通信评分
     * 实际样本量【号码融】：号码融
     * 实际样本量【核验类产品】：邦秒验
     * 实际样本量【自有】：号码分, 号码风险等级, 存量洞察, 金盾
     * 建模样本量: 存量洞察建模, 自有产品建模, 三方数据融合建模, 运营商建模
     */
    const DEFAULT_SAMPLE_SIZE_ACTUAL = 'sample_size_actual'; // 实际样本量【自有产品】
    const SAMPLE_SIZE_ACTUAL_0 = 'sample_size_actual_txzs';
    const SAMPLE_SIZE_ACTUAL_1 = 'sample_size_actual_hmr';
    const SAMPLE_SIZE_ACTUAL_2 = 'sample_size_actual_hy';
    const SAMPLE_SIZE_ACTUAL_MODEL = 'sample_size_model';
    /**
     * 测试产品 => 实际样本量 对照
     * 其余: sample_size_actual
     */
    const SAMPLE_SIZE_MAP = [
        self::ENUM_PRODUCT_OPTION_5 => self::SAMPLE_SIZE_ACTUAL_0, // 实际样本量【邦信分】
        self::ENUM_PRODUCT_OPTION_21 => self::SAMPLE_SIZE_ACTUAL_0, // 实际样本量【邦信分】
        self::ENUM_PRODUCT_OPTION_17 => self::SAMPLE_SIZE_ACTUAL_1, // 实际样本量【号码融】
        self::ENUM_PRODUCT_OPTION_20 => self::SAMPLE_SIZE_ACTUAL_2, // 实际样本量【核验类产品】
        self::ENUM_PRODUCT_OPTION_43 => self::SAMPLE_SIZE_ACTUAL_MODEL, // 建模样本量
        self::ENUM_PRODUCT_OPTION_44 => self::SAMPLE_SIZE_ACTUAL_MODEL, // 建模样本量
        self::ENUM_PRODUCT_OPTION_45 => self::SAMPLE_SIZE_ACTUAL_MODEL, // 建模样本量
        self::ENUM_PRODUCT_OPTION_46 => self::SAMPLE_SIZE_ACTUAL_MODEL, // 建模样本量
        self::ENUM_PRODUCT_OPTION_48 => self::SAMPLE_SIZE_ACTUAL_MODEL, // 建模样本量
    ];

//    /**
//     * 测试产品 飞书枚举值 : 系统信息 map
//     * @deprecated
//     */
//    const PRODUCT_MAP = [
//        self::ENUM_PRODUCT_OPTION_3 => [
//            'type' => self::PRODUCT_MAP_MAPPING,
//            'father_id' => 200,
//            'product_map' => [
//                self::ENUM_PRODUCT_OPTION_3_0 => 202, // 手机号在网时长
//                self::ENUM_PRODUCT_OPTION_3_1 => 203, // 手机号在网状态
//            ],
//            'product_key' => 'product_list_hyqs',
//            'product_id' => [],
//        ],
//        self::ENUM_PRODUCT_OPTION_0 => [
//            'type' => self::PRODUCT_MAP_VAGUE,
//            'father_id' => 10000,
//            'product_id' => [
//                10111,
//                10112,
//                10113,
//                10114,
//                10115,
//                10116,
//                10117,
//            ],
//        ],
//        self::ENUM_PRODUCT_OPTION_1 => [
//            'type' => self::PRODUCT_MAP_VAGUE,
//            'father_id' => 615,
//            'product_id' => [
//                661,
//                662,
//                666,
//                667,
//                61001,
//                61002,
//                61003,
//            ],
//        ],
//        self::ENUM_PRODUCT_OPTION_2 => [
//            'type' => self::PRODUCT_MAP_VAGUE,
//            'father_id' => 200,
//            'product_id' => [
//                321,
//                322,
//                323,
//                324,
//                325,
//                326,
//                327,
//                328,
//            ],
//        ],
//        self::ENUM_PRODUCT_OPTION_4 => [
//            'type' => self::PRODUCT_MAP_VAGUE,
//            'father_id' => 30000,
//            'product_id' => [
//                30501,
//                30101,
//                30201,
//                30701,
//                30702,
//                30901,
//                31001,
//                31002,
//            ],
//        ],
//        self::ENUM_PRODUCT_OPTION_5 => [
//            'type' => self::PRODUCT_MAP_VAGUE,
//            'father_id' => 210,
//            'product_id' => [
//                281,
//                256,
//            ],
//        ],
//        self::ENUM_PRODUCT_OPTION_6 => [
//            'type' => self::PRODUCT_MAP_VAGUE,
//            'father_id' => 50000,
//            'product_id' => [
//                50101,
//            ],
//        ],
//        self::ENUM_PRODUCT_OPTION_7 => [
//            'type' => self::PRODUCT_MAP_VAGUE,
//            'father_id' => 200,
//            'product_id' => [
//                226,
//            ],
//        ],
//        self::ENUM_PRODUCT_OPTION_8 => [
//            'type' => self::PRODUCT_MAP_FAKE,
//            'father_id' => self::FAKE_PRODUCT_ID,
//            'product_id' => [
//                self::FAKE_PRODUCT_ID
//            ]
//        ],
//        self::ENUM_PRODUCT_OPTION_9 => [
//            'type' => self::PRODUCT_MAP_FAKE,
//            'father_id' => self::FAKE_PRODUCT_ID_2,
//            'product_id' => [
//                self::FAKE_PRODUCT_ID_2
//            ]
//        ],
//        self::ENUM_PRODUCT_OPTION_10 => [
//            'type' => self::PRODUCT_MAP_FAKE,
//            'father_id' => self::FAKE_PRODUCT_ID_3,
//            'product_id' => [
//                self::FAKE_PRODUCT_ID_3
//            ]
//        ],
//        self::ENUM_PRODUCT_OPTION_11 => [
//            'type' => self::PRODUCT_MAP_FAKE,
//            'father_id' => self::FAKE_PRODUCT_ID_4,
//            'product_id' => [
//                self::FAKE_PRODUCT_ID_4
//            ]
//        ],
//        self::ENUM_PRODUCT_OPTION_12 => [
//            'type' => self::PRODUCT_MAP_FAKE,
//            'father_id' => self::FAKE_PRODUCT_ID_5,
//            'product_id' => [
//                self::FAKE_PRODUCT_ID_5
//            ]
//        ],
//        self::ENUM_PRODUCT_OPTION_13 => [
//            'type' => self::PRODUCT_MAP_FAKE,
//            'father_id' => self::FAKE_PRODUCT_ID_6,
//            'product_id' => [
//                self::FAKE_PRODUCT_ID_6
//            ]
//        ],
//        self::ENUM_PRODUCT_OPTION_14 => [
//            'type' => self::PRODUCT_MAP_FAKE,
//            'father_id' => self::FAKE_PRODUCT_ID_7,
//            'product_id' => [
//                self::FAKE_PRODUCT_ID_7
//            ]
//        ],
//        // 已停止申请测试产品
//        'option_18A4A8ADPQ2K0' => [],
//        'lrsu8qz1-1th9opsb1cb-1' => [],
//    ];

//    const FATHER_NAME_MAP = [
//        self::ENUM_PRODUCT_OPTION_0 => '号码分',
//        self::ENUM_PRODUCT_OPTION_1 => '号码风险等级',
//        self::ENUM_PRODUCT_OPTION_2 => '号码活跃指数',
//        self::ENUM_PRODUCT_OPTION_3 => '在网状态及时长核验',
//        self::ENUM_PRODUCT_OPTION_4 => '事件分',
//        self::ENUM_PRODUCT_OPTION_5 => '邦信分-通信指数',
//        self::ENUM_PRODUCT_OPTION_6 => '金盾-风险验证【50101】',
//        self::ENUM_PRODUCT_OPTION_7 => '空号检测',
//        self::ENUM_PRODUCT_OPTION_8 => '特征变量',
//        self::ENUM_PRODUCT_OPTION_9 => '号码风险等级-特征变量',
//        self::ENUM_PRODUCT_OPTION_10 => '联合建模',
//        self::ENUM_PRODUCT_OPTION_11 => '号码融',
//        self::ENUM_PRODUCT_OPTION_12 => '号码融内部建模',
//        self::ENUM_PRODUCT_OPTION_13 => '其他产品（ 备注中说明）',
//        self::ENUM_PRODUCT_OPTION_14 => '易诉',
//        // 已停止申请测试产品
////        'option_18A4A8ADPQ2K0' => '邦秒验',
////        'lrsu8qz1-1th9opsb1cb-1' => '金盾-策略字段',
//    ];

//    const PRODUCT_NAME_MAP = [
//        self::ENUM_PRODUCT_OPTION_3 => [
//            self::ENUM_PRODUCT_OPTION_3_0 => '在网时长',
//            self::ENUM_PRODUCT_OPTION_3_1 => '在网状态',
//        ],
//    ];

    /**
     * 子产品可做申请的主产品 配置
     */
    const HAS_PRODUCT = [
        self::ENUM_PRODUCT_OPTION_3 => 'product_list_hyqs',
        self::ENUM_PRODUCT_OPTION_18 => 'product_list_jd',
        self::ENUM_PRODUCT_OPTION_49 => 'product_list_bmy',
    ];

    /**
     * 表单字段键值映射
     * 飞书key : 系统key 映射
     */
    const APPROVAL_FORM_MAP = [
        // [售前测试流程-新版], [售前测试&测试账号] 枚举值
        'widget17363340547280001' => 'is_approval_pre', // 是否申请售前测试
        'widget17363934168850001' => 'is_approval_account', // 是否申请线上测试账号
        'widget17363919232880001' => 'is_approval_model', // 是否已提交建模&产品开发申请
        'widget16891312221959859456553641108' => 'company_short_name', // 客户简称
        'widget16891312226500110379176014105' => 'company_name', // 客户全称
        'widget16891312221923643867554375845' => 'company_type', // 客户类型
        'widget17399468352860001' => 'purpose', // 本次测试目的
        'widget17403938791560001' => 'customer_id', // 客户ID
        'widget17467783671410001' => 'product_id_list', // 开发回填子产品id
        'widget17404489484460001' => 'apikey', // 客户线上apikey
        'widget17083283786630001' => 'sample_source', // 样本来源
        'widget16891312222390120855639588261' => 'is_pay', // 是否付费测试
        'widget16957196535280001' => 'is_top',
        'widget17092587202800001' => 'sales_opportunity_code', // 销售机会编码
        'widget16891312226130589867869082925' => 'sample_description', // 测试样本说明
        'widget16891312224953387082893528065' => 'father_list', // 主产品
        'widget16891312221969523683966848951' => 'note', // 其他
        'widget16891312221003865620538587513' => 'product_list_bmy',
        'widget16891312227505265805416220994' => 'product_list_hyqs',
        'widget17361448173470001' => 'product_list_hmr', // 号码融产品清单
        'widget17401067526810001' => 'product_list_jd', // 金盾测试产品
        'widget17423625436660001' => 'product_list_bmy', // 邦秒验测试产品
        'widget17389155091050001' => 'sample_size_actual', // 实际样本量【自有产品】
        'widget16957947343250001' => 'sample_size_50000', // 样本量【金盾】
        'widget16891312226567114838982081282' => 'sample_size_210', // 预估样本量【邦信分-通信指数】
        'widget16957944625610001' => 'sample_size_10000', // 预估样本量【自有产品】
        'widget16957945451420001' => 'sample_size_615', // 样本量【号码风险等级】
        'widget16957946317980001' => 'sample_size_200', // 样本量【号码活跃指数】
        'widget16891312223686049791971957496' => 'sample_size_param_10000', // 样本量【号码分-特征变量】
        'widget16957947819440001' => 'sample_size_param_615', // 样本量【号码风险等级-特征变量】
        'widget16957945024280001' => 'sample_size_30000', // 样本量【事件分】
        'widget17186118515590001' => 'sample_size_hmr', // 预估样本量【号码融-融合产品】
        'widget17344145729480001' => 'sample_size_lhjm', // 预估样本量【联合建模】
        'widget16891312226264197978094860826' => 'sample_size_203', // 预估样本量【核验类产品】
        'widget17266254271620001' => 'sample_size_ys', // 样本量【易诉】
        'widget17266253094360001' => 'ext_25', // 预估样本量【特征变量】
        'widget17333951958240001' => self::SAMPLE_SIZE_ACTUAL_0, // 实际样本量【邦信分-通信指数】
        'widget17333952206960001' => self::SAMPLE_SIZE_ACTUAL_1, // 实际样本量【号码融-融合产品】
        'widget17333952355210001' => self::SAMPLE_SIZE_ACTUAL_2, // 实际样本量【核验类产品】
        'widget16975365838070001' => 'variable_num', // 特征变量数量
        'widget16910340090460001' => 'modeling_scene', // 联合建模场景
        'widget16910341150070001' => 'modeling_way', // 联合建模方式
        'widget17095204937700001' => 'upload_agreement_type', // 上传方式-保密协议
        'widget17173955034870001' => 'attachment', // 附件
        'widget17454896263460001' => 'grayscale_time', // 灰度时间
        'widget16904286974570001' => 'ext_01', // 数据公司信息填报
        'widget17193884391940001' => 'ext_02', // 模型是否包含外部数据
        'widget17200646650300001' => 'ext_03', // 客户使用数据源情况
        'widget17200651339160001' => 'ext_04', // 号码融样本来源
        'widget17200653659110001' => 'ext_05', // 自营资产需要补充
        'widget17200656204170001' => 'ext_06', // 客户可接受的模型价格范围
        'widget17200656806680001' => 'ext_07', // 是否可向外数机构披露甲方
        'widget17200659097400001' => 'ext_08', // 准入标准
        'widget17200659880600001' => 'ext_09', // 准入标准详细
        'widget17200660963960001' => 'ext_10', // 预计使用策略
        'widget17200661575800001' => 'ext_11', // 预计调用量(条/月)
        'widget17200661989800001' => 'ext_12', // 模型使用环节
        'widget17204089475070001' => 'ext_13', // 售前建议（三方数据测试）
        'widget17204091056410001' => 'ext_14', // 生态建议（三方数据测试）
        'widget17204090383200001' => 'ext_15', // 产品建议（三方数据测试）
        'widget17200654733330001' => 'ext_16', // 助贷资产需要补充
        'widget17083264873550001' => 'ext_17', // 关联审批（资质准入）
        'widget16891312223172166922230276623' => 'ext_18', // 付费测试来源
        'widget168913122222286528169904422' => 'ext_19', // 付费测试付款方
        'widget17083300478940001' => 'ext_20', // 请上传双方盖章版保密协议
        'widget17095205825110001' => 'ext_21', // 关联审批（保密协议）
        'widget16957196944910001' => 'ext_22', // 头部13家客户
        'widget17141037049160001' => 'ext_23', // 【在网时长核验】备注
        'widget17200655115330001' => 'ext_24', // 其他资产
        'widget17313134711300001' => 'ext_26', // 客户介绍（公司背景/APP/品牌）
        'widget17301008426740001' => 'ext_27', // 核验类产品上线方式
        'widget17316438375990001' => 'ext_28', // 易诉是否测试运营商版
        'widget17298425014840001' => 'ext_29', // 号码融外部数据是否接受付费测试
        'widget17363950512320001' => 'ext_30', // 办公地所在区域
        'widget17363935437870001' => 'not_save_1', // 说明 1 - 对接产品信息
        'widget17367523318130001' => 'not_save_2', // 说明 2 - 售前测试说明
        'widget17400174159670001' => 'ext_41', // 是否测试特征变量
        'widget17363934808000001' => 'ext_42', // 来源
        'widget17364116790020001' => 'ext_43', // 公司-类型
        'widget17364111772870001' => 'ext_44', // 是否有牌照
        'widget17364112131300001' => 'ext_45', // 牌照类别说明
        'widget17364112583870001' => 'ext_46', // 是否为渠道客户
        'widget17364084234470001' => 'ext_47', // 对接产品
        'widget17401258329430001' => 'ext_48', // 是否对接【风险验证】
        'widget17399471726120001' => 'ext_49', // 子产品名称
        'widget17364101101500001' => 'ext_50', // 账号有效期
        'widget17364102139800001' => 'ext_51', // 预计业务上线时间
        'widget17364102477790001' => 'ext_52', // 是否有赠送量级
        'widget17364102907510001' => 'ext_53', // 赠送申请方式
        'widget17364104637110001' => 'ext_54', // 赠送说明(量级/时间)
        'widget17364105552630001' => 'ext_55', // 赠送原因
        'widget17364107716530001' => 'ext_56', // 是否线上灰度测试
        'widget17364113147600001' => 'ext_57', // 客户账号通知邮箱地址
        'widget17364118650930001' => 'ext_58', // 双方盖章版保密协议
        'widget17364119178100001' => 'ext_59', // 附件(营业执照)
        'widget17364119413560001' => 'ext_60', // 附件(授权样例)
        'widget17364107935270001' => 'ext_61', // 备注
        'widget17364104176770001' => 'ext_62', // 账号总限量(条)
        'widget17363922864510001' => 'union_apply_list', // 关联审批
        'widget17364101669980001' => 'ext_73', // 自定义有效期(天)
        'widget17364115089980001' => 'ext_74', // 公司类型
        'widget17397646519240001' => 'ext_75', // 客户在征信侧签约主体
        // [产品建模&定制申请] 枚举值
        'widget17363148720350001' => 'is_approval_model_done', // 是否已完成建模或售前测试
        'widget17363200483180001' => 'is_approval_model_product', // 是否定制产品
        'widget17121277970270001' => 'company_name', // 甲方机构全称
        'widget17199952915400001' => 'company_short_name', // 甲方机构-简称
        'widget17200018529880001' => 'company_type', // 客户类型
        'widget17121288064700001' => 'sales_opportunity_code', // 销售机会编码
        'widget17406510402680001' => 'product_id_list', // 开发回填子产品id
        'widget17367580961230001' => 'union_apply_list', // 关联审批
        'widget17363194952500001' => 'not_save_3', // 说明 1 - 建模需求填写
        'widget17367643940060001' => 'ext_31', // 主产品
        'widget17388276215140001' => 'model_father_list', // 建模分类 => 主产品
        'widget17199953656440001' => 'ext_33', // 客户联合建模现状
        'widget17199953678770001' => 'ext_34', // 客户使用数据源情况
        'widget17200019225970001' => self::SAMPLE_SIZE_ACTUAL_MODEL, // 建模样本量(条)
        'widget17200017029510001' => 'ext_07', // 是否可向外数机构披露甲方
        'widget17121282827680001' => 'ext_35', // 建模后续测试安排
        'widget17200024636680001' => 'ext_36', // 建模需求说明
        'widget17200024831410001' => 'ext_37', // 是否需要我司驻场建模
        'widget17200025123110001' => 'ext_38', // 建模方式和地点
        'widget17200025511110001' => 'ext_08', // 准入指标
        'widget17200026058850001' => 'ext_09', // 具体准入标准
        'widget17200026891640001' => 'ext_11', // 预计调用量(条/月)
        'widget17200026450340001' => 'ext_12', // 模型使用环节
        'widget17363200736150001' => 'not_save_4', // 说明 2 - 产品开发需求
        'widget17363924174440001' => 'ext_39', // 需求类型
        'widget17363201357610001' => 'note', // 产品需求说明
        'widget17363201659750001' => 'ext_40', // 期望上线时间
        'widget17200011126450001' => 'ext_63', // 样本来源
        'widget17218092139470001' => 'ext_64', // 是否支持付费建模
        'widget17218092375530001' => 'ext_65', // 预估三方建模费用
        'widget17200011831090001' => 'ext_66', // 自营资产-补充说明
        'widget17200591037040001' => 'ext_67', // 其他资产-补充说明
        'widget17200012274040001' => 'ext_68', // 助贷资产-补充说明
        'widget17200013174160001' => 'ext_69', // 客户接受的模型价格范围
        'widget17200018049220001' => 'ext_70', // 是否支持客户发送样本至朴道公开测试
        'widget17121264209260001' => 'ext_71', // 预计样本到达时间
        'widget17121279922210001' => 'ext_72', // 预计项目开始时间-预计项目结束时间
        'widget17410877184850001' => 'ext_76', // 提交部门
    ];

    /**
     * 表单字段键值名称映射
     * 系统key : 飞书表单名称 映射
     */
    const FORM_NAME_MAP = [
        'is_approval_pre' => '是否申请售前测试',
        'is_approval_account' => '是否申请线上测试账号',
        'is_approval_model' => '是否已提交建模&产品开发申请',
        'is_approval_model_product' => '是否定制产品',
        // 基础字段
        'sales_opportunity_code' => '销售机会编码',
        'is_top' => '是否为头部13家客户',
        'company_short_name' => '客户简称',
        'company_name' => '客户全称',
        'company_type' => '客户类型',
        'sample_source' => '样本来源',
        'is_pay' => '是否付费测试',
        'sample_description' => '测试样本说明',
        'note' => '其他',
        'purpose' => '测试目的',
        'customer_id' => '客户ID',
        'apikey' => '客户线上apikey',
        'product_id_list' => '上线子产品ID',
        // 测试产品相关字段
        'father_list' => '测试产品',
        'model_father_list' => '建模分类',
        'product_list_bmy' => '邦秒验测试产品',
        'product_list_hyqs' => '核验前筛-子产品清单',
        'product_list_hmr' => '号码融产品清单',
        'product_list_jd' => '金盾测试产品',
        'sample_size_50000' => '样本量【金盾】',
        'sample_size_210' => '预估样本量【邦信分-通信指数】',
        'sample_size_10000' => '预估样本量【自有产品】',
        'sample_size_actual' => '实际样本量【自有产品】',
        'sample_size_615' => '样本量【号码风险等级】',
        'sample_size_200' => '样本量【号码活跃指数】',
        'sample_size_param_10000' => '样本量【号码分-特征变量】',
        'sample_size_param_615' => '样本量【号码风险等级-特征变量】',
        'variable_num' => '特征变量数量',
        'modeling_scene' => '联合建模场景',
        'modeling_way' => '联合建模方式',
        'grayscale_time' => '灰度时间',
        'sample_size_30000' => '样本量【事件分】',
        'sample_size_hmr' => '预估样本量【号码融-融合产品】',
        'sample_size_203' => '预估样本量【核验类产品】',
        'sample_size_ys' => '样本量【易诉】',
        'ext_25' => '预估样本量【特征变量】',
        'sample_size_lhjm' => '预估样本量【联合建模】',
        'sample_size_model' => '建模样本量(条)',
        self::SAMPLE_SIZE_ACTUAL_0 => '实际样本量【邦信分-通信指数】',
        self::SAMPLE_SIZE_ACTUAL_1 => '实际样本量【号码融-融合产品】',
        self::SAMPLE_SIZE_ACTUAL_2 => '实际样本量【核验类产品】',
        // 扩展字段
        'union_apply_list' => '关联审批',
        'upload_agreement_type' => '上传方式-保密协议',
        'attachment' => '附件',
        'ext_01' => '数据公司信息填报',
        'ext_02' => '模型是否包含外部数据',
        'ext_03' => '客户使用数据源情况',
        'ext_04' => '号码融样本来源',
        'ext_05' => '自营资产需要补充',
        'ext_06' => '客户可接受的模型价格范围',
        'ext_07' => '是否可向外数机构披露甲方',
        'ext_08' => '准入标准',
        'ext_09' => '准入标准详细',
        'ext_10' => '预计使用策略',
        'ext_11' => '预计调用量(条/月)',
        'ext_12' => '模型使用环节',
        'ext_13' => '售前建议（三方数据测试）',
        'ext_14' => '生态建议（三方数据测试）',
        'ext_15' => '产品建议（三方数据测试）',
        'ext_16' => '助贷资产需要补充',
        'ext_17' => '关联审批（资质准入）',
        'ext_18' => '付费测试来源',
        'ext_19' => '付费测试付款方',
        'ext_20' => '请上传双方盖章版保密协议',
        'ext_21' => '关联审批（保密协议）',
        'ext_22' => '头部13家客户',
        'ext_23' => '【在网时长核验】备注',
        'ext_24' => '其他资产',
        'ext_26' => '客户介绍（公司背景/APP/品牌）',
        'ext_27' => '核验类产品上线方式',
        'ext_28' => '易诉是否测试运营商版',
        'ext_29' => '号码融外部数据是否接受付费测试',
        'ext_30' => '办公地所在区域',
        'ext_31' => '开发产品',
        'ext_32' => '建模分类',
        'ext_33' => '客户联合建模现状',
        'ext_34' => '客户使用数据源情况',
        'ext_35' => '建模后续测试安排',
        'ext_36' => '建模需求说明',
        'ext_37' => '是否需要我司驻场建模',
        'ext_38' => '建模方式和地点',
        'ext_39' => '需求类型',
        'ext_40' => '期望上线时间',
        'ext_41' => '是否测试特征变量',
        'ext_42' => '来源',
        'ext_43' => '公司-类型',
        'ext_44' => '是否有牌照',
        'ext_45' => '牌照类别说明',
        'ext_46' => '是否为渠道客户',
        'ext_47' => '对接产品',
        'ext_48' => '是否对接【风险验证】',
        'ext_49' => '子产品名称',
        'ext_50' => '账号有效期',
        'ext_51' => '预计业务上线时间',
        'ext_52' => '是否有赠送量级',
        'ext_53' => '赠送申请方式',
        'ext_54' => '赠送说明(量级/时间)',
        'ext_55' => '赠送原因',
        'ext_56' => '是否线上灰度测试',
        'ext_57' => '客户账号通知邮箱地址',
        'ext_58' => '双方盖章版保密协议',
        'ext_59' => '附件(营业执照)',
        'ext_60' => '附件(授权样例)',
        'ext_61' => '备注',
        'ext_62' => '账号总限量(条)',
        'ext_63' => '样本来源',
        'ext_64' => '是否支持付费建模',
        'ext_65' => '预估三方建模费用',
        'ext_66' => '自营资产-补充说明',
        'ext_67' => '其他资产-补充说明',
        'ext_68' => '助贷资产-补充说明',
        'ext_69' => '客户接受的模型价格范围',
        'ext_70' => '是否支持客户发送样本至朴道公开测试',
        'ext_71' => '预计样本到达时间',
        'ext_72' => '预计项目开始时间-预计项目结束时间',
        'ext_73' => '自定义有效期(天)',
        'ext_74' => '公司类型',
        'ext_75' => '客户在征信侧签约主体',
        'ext_76' => '提交部门',
    ];

    const FATHER_LIST = [
        'father_list',
    ];
    const FATHER_LIST_MODEL = [
        'model_father_list',
    ];

    /**
     * 测试产品相关字段
     */
    const PRODUCT_LIST = [
        'product_list_bmy',
        'product_list_hyqs',
        'product_list_jd',
    ];

    /**
     * 测试产品-扩展字段相关字段
     */
    const PRODUCT_INFO_LIST = [
        'sample_size_50000',
        'sample_size_210',
        'sample_size_10000',
        'sample_size_615',
        'sample_size_200',
        'sample_size_param_10000',
        'sample_size_param_615',
        'sample_size_30000',
        'sample_size_hmr',
        'sample_size_203',
        'ext_25',
        'sample_size_ys',
        'sample_size_lhjm',
        'sample_size_actual',
        'product_list_hmr',
        'variable_num',
        'modeling_scene',
        'modeling_way',
        'sample_size_model',
        self::SAMPLE_SIZE_ACTUAL_0,
        self::SAMPLE_SIZE_ACTUAL_1,
        self::SAMPLE_SIZE_ACTUAL_2,
    ];

    /**
     * 扩展字段
     */
    const EXTEND_LIST = [
        'union_apply_list',
        'product_id_list',
        'upload_agreement_type',
        'attachment',
        'ext_01',
        'ext_02',
        'ext_03',
        'ext_04',
        'ext_05',
        'ext_06',
        'ext_07',
        'ext_08',
        'ext_09',
        'ext_10',
        'ext_11',
        'ext_12',
        'ext_13',
        'ext_14',
        'ext_15',
        'ext_16',
        'ext_17',
        'ext_18',
        'ext_19',
        'ext_20',
        'ext_21',
        'ext_23',
        'ext_24',
        'ext_26',
        'ext_27',
        'ext_28',
        'ext_29',
        'ext_30',
        'ext_31',
        'ext_32',
        'ext_33',
        'ext_34',
        'ext_35',
        'ext_36',
        'ext_37',
        'ext_38',
        'ext_39',
        'ext_40',
        'ext_41',
        'ext_42',
        'ext_43',
        'ext_44',
        'ext_45',
        'ext_46',
        'ext_47',
        'ext_48',
        'ext_49',
        'ext_50',
        'ext_51',
        'ext_52',
        'ext_53',
        'ext_54',
        'ext_55',
        'ext_56',
        'ext_57',
        'ext_58',
        'ext_59',
        'ext_60',
        'ext_61',
        'ext_62',
        'ext_63',
        'ext_64',
        'ext_65',
        'ext_66',
        'ext_67',
        'ext_68',
        'ext_69',
        'ext_70',
        'ext_71',
        'ext_72',
        'ext_73',
        'ext_74',
        'ext_75',
        'ext_76',
    ];

    /**
     * 售前测试行为日志表
     * 10 : 完成测试
     * 11 : 批量完成测试
     * 20 : 记录未打卡
     * 21 : 一键无反馈
     * 30 : 绑定客户ID
     * 40 : 绑定产品/添加产品
     * 50 : 保存反馈信息
     * 51 : 批量保存反馈信息
     * 60 : 保存接入信息
     * 61 : 批量保存反馈信息
     */
//    const LOG_ACTION_COMPLETE_TEST = 10;
//    const LOG_ACTION_BATCH_COMPLETE_TEST = 11;
//    const LOG_ACTION_CLOCK_UNDONE = 20;
//    const LOG_ACTION_ALL_STAY = 21;
//    const LOG_ACTION_BIND_CUSTOMER = 30;
//    const LOG_ACTION_BIND_PRODUCT = 40;
//    const LOG_ACTION_SAVE_FEEDBACK = 50;
//    const LOG_ACTION_SAVE_FEEDBACK_BATCH = 51;
//    const LOG_ACTION_SAVE_ACCESS = 60;
//    const LOG_ACTION_SAVE_ACCESS_BATCH = 61;

    const ACTION_ADMIN_SYS = 'SYS';
    const ACTION_ADMIN_SCRIPT = 'SCRIPT';
    const ACTION_ADMIN_UNKNOWN = 'UNKNOWN';

    /**
     * 接口 => 操作 map
     */
    const LOG_ACTION_MAP = [
        'list' => '查看-测试明细',
        'stay_single' => '延期反馈',
        'save_feedback' => '保存-反馈信息',
        'save_access' => '保存-接入信息',
        'batch_save_access' => '批量保存-接入信息',
        'bind_customer' => '绑定-客户ID',
        'process_status' => '修改-进度(开始, 完成, 终止)',
        'bind_product' => '绑定-测试产品',
        'delete_product' => '删除-测试产品',
        'apply_info' => '查看-测试申请详情',
        'risk_list' => '查看-风险统计',
        'statistics_overview' => '查看-数据统计-总览数据',
        'statistics_feedback' => '查看-数据统计-反馈数据',
        'log_list' => '查看-日志列表',
    ];

    const MANAGE_USER_ID = '8cc1f829';
    const MANAGE_PRETERT_USER_ID = 'c73ag39a';

}