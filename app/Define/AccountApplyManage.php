<?php

namespace App\Define;

/**
 *
 * Class AccountApplyManage 常量
 * @package App\Define
 */
class AccountApplyManage
{
    /**
     * [正式账号申请] 审批定义模板code
     */
    const APPROVAL_CODE_TEST_WITH_APPLY_ACCOUNT = '********-5495-424E-96A0-4870CADA2AE2';



    /**
     * 表单字段键值映射
     * 飞书key : 系统key 映射
     */
    const APPROVAL_FORM_MAP = [
        // 正式账号申请
        'widget16901696760080001' => 'customer_name', // 账号名称
        'widget17313132432950001' => 'customer_introduce', // 账号介绍
        'widget16933911215670001' => 'contract_partyA', // 签约甲方
        'widget16920693113000001' => 'office_area', // 办公地所在区域
        'widget17043690408140001' => 'is_license', // 是否有牌照
        'widget16901696920410001' => 'is_channel_customer', // 是否为渠道客户
        'widget16952017037230001' => 'is_relationship_company', // 是否与已签约公司主体有关联关系
        'widget16901697690070001' => 'amount', // 合同总金额（元）
        'widget16901697866160001' => 'price', // 合同单价
        'widget17454899085320001' => 'expect_online_time', // 预计业务上线时间
        'widget17153995397030001' => 'is_within_range', // 是否在报价范围内
        'widget17274185380620001' => 'is_price_lowered', // 价格是否有下调
        'widget16901697999160001' => 'company_type', // 公司类型
        'widget16901698913720001' => 'billing_method', // 计费方式
        'widget16901699195370001' => 'pay_method', // 付款方式
        'widget16901699946370001' => 'sign_product', // 签约产品
        'widget16901705994930001' => 'is_sign_contract', // 是否签署合同
        'widget16901706161290001' => 'is_pay', // 是否到款
        'widget17034988639900001' => 'is_gift', // 是否有赠送量级
        'widget16933915151240001' => 'picture', // 图片（上传邮件价格截图）
        'widget17470222519740001' => 'customer_id', // 客户id
        'widget17467789106850001' => 'product_id_list', // 产品id
    ];




}