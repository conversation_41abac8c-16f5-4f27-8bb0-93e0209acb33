<?php
/**
 * 产品分类展示定义
 */

namespace App\Define;
class ProductCategory
{
    const SHOW_PRODUCT_NAME = [
        0 => '邦信分-通信字段', //210
        1 => '邦信分-通信评分', //1000

        2 => '在网时长',    //200-202、200-224、200-41001
        3 => '三要素',      //200-213 200-41006 200-201
        4 => '实时在网状态', //200-216 200-41007 200-41002
        5 => '在网状态',    //200-203、200-225、200-41012
        6 => '读秒分',      //200-310,200-311,200-312,200-313,200-314,200-315,200-316,200-317, 200-318,200-41004
        7 => '其他',        //200-other 其他
        19 => 'AI创新', //30000-39001,30000-39002,30000-39003

        8 => '号码风险等级',     //615, 664
        9 => '号码分',          //10000
        10 => '邦秒验自有分',     //200-41003,200-41005,200-331,200-330,200-329,200-328,200-327,200-326,200-325,200-324,200-323,200-322,200-321,200-320 (通用分v2,v3,活跃指数)

        12 => '金盾',           //50000-other,612 (金盾(不包含风险验证),金盾贷前)
        13 => '其他',           //401, 604,601,104, 3000(邦企查,邦秒配,邦秒验-IND)
        14 => '企服-品牌号',     //3100-3103,3100-3104
        15 => '企服-神盾',       //3100-other (3100-664,3100-3101,3100-3102,3100-3105,3100-3106,3100-3107,3100-3108) 50000:50101除品牌号,电话名片外的企服产品 + 金盾的风险验证
        16 => '存量运营',  //30000-1200,30000-38001,30000-38002,30000-38003
        17 => '存量分层',//存量洞察-存量分层: 30000-other (除1200、39001、39002、39003、38001、38002、38003外剩下所有的30000的子产品)
        18 => '号码融',          //70000

//        20 => '号码融-外数',
//        21 => '号码融-自有',
//        22 => '附:号码融-客户',
    ];

    //定义一个类别下展示名称 和 类别的关系
    const SHOW_PRODUCT_NAME_AND_CATEGORY = [
        0 => 0,
        1 => 0,

        2 => 1,
        3 => 1,
        4 => 1,
        5 => 1,
        6 => 1,
        7 => 1,
        //19 => 1,
        //20 => 1,

        8 => 2,
        9 => 2,
        10 => 2,
        11 => 2,
        12 => 2,
        13 => 2,
        14 => 2,
        15 => 2,
        //        16 => 2,
        //        17 => 2,
        18 => 2,

        19 => 3,
        16 => 3,
        17 => 3,

        20 => 4,
        21 => 4,
        22 => 4,
    ];

    const PRODUCT_CATEGORY_MAP = [
        210 => 0,
        1000 => 1,
        '200-202' => 2,
        '200-224' => 2,
        '200-41001' => 2,
        '200-213' => 3,
        '200-201' => 3,
        '200-41006' => 3,
        '200-216' => 4,
        '200-41007' => 4,
        '200-41002' => 4,
        '200-203' => 5,
        '200-225' => 5,
        '200-41012' => 5,
        '200-310' => 6,
        '200-311' => 6,
        '200-312' => 6,
        '200-313' => 6,
        '200-314' => 6,
        '200-315' => 6,
        '200-316' => 6,
        '200-317' => 6,
        '200-318' => 6,
        '200-41004' => 6,
        '200-other' => 7,
        615 => 8,
        664 => 8,
        10000 => 9,
        '200-41003' => 10,
        '200-41005' => 10,
        '200-331' => 10,
        '200-330' => 10,
        '200-329' => 10,
        '200-328' => 10,
        '200-327' => 10,
        '200-326' => 10,
        '200-325' => 10,
        '200-324' => 10,
        '200-323' => 10,
        '200-322' => 10,
        '200-321' => 10,
        '200-320' => 10,
        '50000-other' => 12,
        612 => 12,
        401 => 13,
        604 => 13,
        601 => 13,
        104 => 13,
        3000 => 13,
        '3100-3103' => 14,
        '3100-3104' => 14,
        '3100-other' => 15,
        '50000-50101' => 15,
        '30000-39001' => 19,
        '30000-39002' => 19,
        '30000-39003' => 19,
        '30000-1200' => 16,
        '30000-38001' => 16,
        '30000-38002' => 16,
        '30000-38003' => 16,
        '30000-other' => 17,
        70000 => 18,
    ];

    const SHOW_PRODUCT_NAME_200 = [
        201, 202, 213, 216, 203, 224, 225, 310, 311, 312, 313, 314, 315, 316, 317, 318, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 41001, 41002, 41003, 41004, 41005, 41006, 41007, 41012
    ];

    const SHOW_PRODUCT_NAME_50000 = [
        50101
    ];

    const SHOW_PRODUCT_NAME_3100 = [
        3103, 3104
    ];

    const SHOW_PRODUCT_NAME_30000 = [
        1200, 39001, 39002, 39003, 38001, 38002, 38003
    ];

    //需要转换的父产品id
    const CONVERT_FATHER_ID = [200, 3100, 30000, 50000];

}