<?php

namespace App\Define;

use App\Http\Repository\ContractRepositorie;

/**
 *
 * Class PreTestMonitor 常量
 *
 * @package App\Define
 */
class FeishuApprovalDefine
{

    /**
     * [调用量级稳定] 审批定义模板code
     */
    public const APPROVAL_CODE_CALL_STABLE = '31C2A52C-2519-4DD8-93AE-D21F12FA9F29';
    /**
     * [灰度上线时间] 审批定义模板code
     */
    public const APPROVAL_CODE_GRAYSCALE = '1FF12876-597D-43B2-813A-D7B74117DD4F';
    /**
     * [超出预计上线时间] 审批定义模板code
     */
    public const APPROVAL_CODE_EXPECT_ONLINE = '7DE45851-179F-4E1D-9892-16F197E373DA';
    /**
     * [售前测试流程-新版] 审批定义模板code
     */
    public const APPROVAL_CODE_TEST = 'A743783F-3E0D-4A70-B1CF-254E9EE59E9A';
    /**
     * [售前测试&测试账号申请] 审批定义模板code
     */
    public const APPROVAL_CODE_TEST_WITH_ACCOUNT = '37E23EE8-E880-4A8B-A291-C00E50B54A23';
    /**
     * [产品建模&定制申请] 审批定义模板code
     */
    public const APPROVAL_CODE_TEST_WITH_MODEL = 'BD65205C-84C4-437B-8BE9-ABDED8451972';
    /**
     * [合同及印章审批申请流程] 审批定义模板code
     */
    public const APPROVAL_CODE_CONTRACT_SEAL = '39B932E3-6E6A-484C-8E3F-7A2A6BF8C030';


    /** @var string 飞书 审批实例状态: 初始化 获取instance_code时插入数据待后续更新 */
    public const INSTANCE_STATUS_INIT  = 'INIT';

    /** @var string 飞书 审批实例状态: 审批中 初态 */
    public const INSTANCE_STATUS_PENDING  = 'PENDING';

    /** @var string 飞书 审批实例状态: 撤回  */
    public const INSTANCE_STATUS_RECALL   = 'RECALL';

    /** @var string 飞书 审批实例状态: 撤回  */
    public const INSTANCE_STATUS_CANCELED = 'CANCELED';

    /** @var string 飞书 审批实例状态: 拒绝  */
    public const INSTANCE_STATUS_REJECTED = 'REJECTED';

    /** @var string 飞书 审批实例状态: 拒绝 */
    public const INSTANCE_STATUS_REJECT   = 'REJECT'; // 终态

    /** @var string 飞书 审批实例状态: 删除  */
    public const INSTANCE_STATUS_DELETED  = 'DELETED';

    /** @var string 飞书 审批实例状态: 通过  */
    public const INSTANCE_STATUS_APPROVED = 'APPROVED';

    /** @var string 飞书 审批实例状态: 所有状态  */
    public const INSTANCE_STATUS_ALL      = 'ALL';





    /**
     * 表单字段键值映射
     * 飞书key : 系统key 映射
     * 合同及印章审批申请流程
     */
    public const APPROVAL_CODE_CONTRACT_SEAL_FORM_MAP = [
        'widget16891313237095787846936497810' => 'contract_name',//合同名称
        'widget16891313238042030275850556422' => 'sign_group',//所属公司 -> 签约主体
        'widget16891313233139809313857750797' => 'dept',//所属业务部 这个不存入合同表
        // 'widget16895514366400001'             => '',//所属部门
        'widget16891313236707154059782353808' => 'salesman_name',//我方负责人
        'widget16891313235062421680593473615' => 'contract_type',//合同类型  客户  1:收入, 2:支出, 3:保密, 4:其他;    渠道  51:补充协议 52:测试协议 53:保密协议 54:代理协议 55:合作协议 56:三方协议;
        'widget17447119309800001'             => 'contract_category',//合同大类 1:客户, 2:渠道
        // 'widget16932157927330001'             => '',//用章类别
        // 'widget16891313237594241478264690974' => '',//合同金额（元）
        // 'widget16915660275000001'             => '',//付费方式
        // 'widget16891313237590638804674244890' => '',//回款账期(月)
        // 'widget17417688069470001'             => '',//客户类别
        // 'widget16891313236707095719401863510' => '',//客户所属行业
        'widget16891313234620450936620104906' => 'company_name',//客户单位全称
        'widget16891313234874660183092744295' => 'sign_year',//签约日期 -> 签约年份
        'widget17036695325490001'             => 'contract_start_contract_end',//合同开始结束时间（合同开始结束日期）contract_start contract_end
        // 'widget16891313238613046264747164187' => '',//收付条款
        // 'widget16891313234870332828329575185' => '',//合同内容
        'widget16891313233633628250423665893' => 'product_category',//所属产品名称
        'widget16891313235365568760783863000' => 'finance_product_name',//金融 产品名称
        'widget16891313233586326454772223037' => 'oversea_product_name',//海外 产品名称
        // 'widget16891313233372376037952894601' => '',//是否为保密协议
        // 'widget1689131323564187506773977779'  => '',//法务是否已审核
        // 'widget17417620793200001'             => '',//未经法务审核的原因
        // 'widget16896701317580001'             => '',//电子版合同
        // 'widget17417609218830001'             => '',//客户介绍
    ];

    public const YULORE_GROUP_NAME_MAP = [
        '北京德宝天辰信息技术有限公司' => ContractRepositorie::CONTRACT_SIGN_GROUP_DBTC,//'北京德宝天辰',
        '北京尚昕科技有限公司'         => ContractRepositorie::CONTRACT_SIGN_GROUP_SXKJ,//'北京尚昕科技',
        '北京羽乐互通信息技术有限公司' => ContractRepositorie::CONTRACT_SIGN_GROUP_HT,//'互通',
        '北京羽乐创新科技有限公司'     => ContractRepositorie::CONTRACT_SIGN_GROUP_CX,//'创新',
    ];

}