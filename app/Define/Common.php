<?php

namespace App\Define;

/**
 *
 * Class Common 常量
 * @package App\Define
 */
class Common
{
    /******************** show name ******************************/
    const COMPANY_CN_NAME = '羽乐科技';

    /***************     source    ******************************/
    const SOURCE_DIANHUABANG  = 0;
    const SOURCE_PUDAO        = 1;
    const SOURCE_ZHESHUJIAO   = 10;
    const SOURCE_ZHENGSHUJIAO = 2;

    /**
     * mysql加密密钥
     */
    const AES_ENCRYPT_KEY = 'database.mysql_aes_encrypt_key';

    /** @var string 0 */
    const AES_ENCRYPT_ZERO = '6fbc46d52ce46e0dd3a3bf45a35d86d3';

    /** @var int 开票模式 1:后付费(主产品) */
    const INVOICE_MODEL_1 = 1;
    /** @var int 开票模式 2:后付费(主产品,可拆金额) */
    const INVOICE_MODEL_2 = 2;
    /** @var int 开票模式 3:后付费(子产品) */
    const INVOICE_MODEL_3 = 3;
    /** @var int 开票模式 4:后付费(子产品,可拆金额) */
    const INVOICE_MODEL_4 = 4;
    /** @var int 开票模式 5:预付费 */
    const INVOICE_MODEL_5 = 5;
    /** @var int 开票模式 6:预付费(先票后款后消耗) */
    const INVOICE_MODEL_6 = 6;
    /** @var int 开票模式 10:后付费 混合模式 包含1 2 3 4的一种或几种 */
    const INVOICE_MODEL_10 = 10;

    /**
     * 开票状态
     * 0:未申请
     * 10:已申请
     * 15:已驳回
     * 16:已撤销
     * 20:已审核
     * 25:部分开票
     * 30:已开票
     * 40:红冲中
     * 50:已邮寄
     * 70:已退票
     * 90:部分回款
     * 100:已回款
     */
    const INVOICE_STATUS_INIT = 0;
    const INVOICE_STATUS_APPLY = 10;
    const INVOICE_STATUS_APPLY_PART = 12;
    const INVOICE_STATUS_REJECT = 15;
    const INVOICE_STATUS_CANCEL = 16;
    const INVOICE_STATUS_VOID = 17;
    const INVOICE_STATUS_APPROVE = 20;
    const INVOICE_STATUS_PART = 25;
    const INVOICE_STATUS_INVOICE = 30;
    const INVOICE_STATUS_RED_FLUSH = 40;
    const INVOICE_STATUS_SEND = 50;
    const INVOICE_STATUS_BACK = 70;
    const INVOICE_STATUS_PART_REMIT = 90;
    const INVOICE_STATUS_DONE_REMIT = 100;

    /**
     * 到款状态
     * 0:未到款
     * 1:部分到款
     * 2:已到款
     */
    const INVOICE_REMIT_STATUS_UNPAID = 0;
    const INVOICE_REMIT_STATUS_PART = 1;
    const INVOICE_REMIT_STATUS_PAID = 2;

    /**
     * 红冲状态
     * 0:正常
     * 1:部分退款
     * 2:重新开票
     */
    const INVOICE_FLUSH_STATUS_0 = 0;
    const INVOICE_FLUSH_STATUS_1 = 1;
    const INVOICE_FLUSH_STATUS_2 = 2;

    /**
     * 发票关联类型
     * 1:关联消耗
     * 2:关联到款
     */
    const INVOICE_REL_CONSUME = 1;
    const INVOICE_REL_REMIT = 2;

    /**
     * 到款状态
     * 0:已提交
     * 1:已修改
     * 2:已驳回
     * 3:已认款
     */
    const REMIT_STATUS_INIT = 0;
    const REMIT_STATUS_EDIT = 1;
    const REMIT_STATUS_REJECT = 2;
    const REMIT_STATUS_CONFIRM = 3;




    /***************     新老客户    ******************************/


    /** @var int 新老客户 客户 */
    const CUSTOMER_NEW_OLD_COLLECT_TYPE_CUSTOMER = 1;
    /** @var int 新老客户 主体 */
    const CUSTOMER_NEW_OLD_COLLECT_TYPE_GROUP = 2;

    /** @var int 新老客户 客户新老 新*/
    const CUSTOMER_NEW_OLD_NEW_CUSTOMER = 1;
    /** @var int 新老客户 客户新老 老*/
    const CUSTOMER_NEW_OLD_OLD_CUSTOMER = 2;

    /** @var int 新老客户 产品新老 新*/
    const CUSTOMER_NEW_OLD_NEW_PRODUCT = 1;
    /** @var int 新老客户 产品新老 老*/
    const CUSTOMER_NEW_OLD_OLD_PRODUCT = 2;

    /** @var int 新老客户 客户禁用*/
    const CUSTOMER_NEW_OLD_CUSTOMER_DISABLE = 1;
    /** @var int 新老客户 客户未禁用*/
    const CUSTOMER_NEW_OLD_CUSTOMER_ABLE = 2;
}