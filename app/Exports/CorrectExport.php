<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithH<PERSON>ings, WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class CorrectExport implements From<PERSON>rray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list;

    public function __construct(array $list)
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->list = array_reduce($list, function ($carry, $item) {
            $carry[] = [
                $item['standard_day'],
                $item['customer_name'],
                $item['product_name'],
                $item['account_name'],
                $this->_getSourceNumber($item),
                $this->getUpdatedNumber($item),
                $item['created_at'],
                $item['operator'],
            ];
            return $carry;
        }, []);
    }

    /**
     * 更新后的用量
     * @param array $item
     * @return string
     */
    private function getUpdatedNumber(array $item): string
    {
        return $item['type'] === 'common' ? $item['data']['updated_number'] : ($item['data']['yd_updated_number']
            . ',' . $item['data']['lt_updated_number'] . ',' . $item['data']['dx_updated_number']);
    }

    /**
     * 实际用量
     * @param array $item
     * @return string
     */
    private function _getSourceNumber(array $item): string
    {
        return $item['type'] === 'common' ? $item['data']['source_number'] : ($item['data']['yd_source_number']
            . ',' . $item['data']['lt_source_number'] . ',' . $item['data']['dx_source_number']);
    }

    public function array(): array
    {
        return $this->list;
    }

    public function headings(): array
    {
        return [
            '校正日期',
            '客户名称',
            '产品',
            '账号',
            '实际用量',
            '更新后用量',
            '修复时间',
            '操作人'
        ];
    }

}