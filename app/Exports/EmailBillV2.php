<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class EmailBillV2 implements WithMultipleSheets
{
    use Exportable;
    /** @var array 客户信息 */
    private $customer_info;

    /** @var array 结算单信息 */
    private $history;

    /** @var array 各个产品的算法账单信息 */
    private $list_product_bill_alo;

    /**
     * EmailBill constructor.
     *
     * @param array $customer_info
     * @param array $history
     * @param array $list_product_bill_alo
     */
    public function __construct(array $customer_info, array $history, array $list_product_bill_alo)
    {
        $this->history               = $history;
        $this->customer_info         = $customer_info;
        $this->list_product_bill_alo = $list_product_bill_alo;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheet[] = new HistoryV2BillExport($this->customer_info, $this->history);

        return array_reduce(
            $this->list_product_bill_alo,
            function ($carry, $item_account) {
                $account_name = $item_account['account_name'];
                return array_reduce(
                    $item_account["list_bills"],
                    function ($carry_section, $item_bill_section) use ($account_name) {
                        if ($item_bill_section['money'] != 0) {
                            $carry_section[] = new ProductAlgoV2Export(
                                $item_bill_section, $this->customer_info, $account_name
                            );
                        }
                        return $carry_section;
                    },
                    $carry
                );
            },
            $sheet
        );
    }
}