<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\{
    FromCollection,
    ShouldAutoSize,
    WithHeadings,
    WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class BillHistoryExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list_bill;

    public function __construct(array $list_bill)
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->list_bill = array_reduce($list_bill, function ($carry, $item) {
            $carry[] = [
                $item['customer_id'] ?? ' ',
                $item['name'] ?? ' ',
                $item['company'] ?? ' ',
                number_format($item['money_consumption_in_dates'], 2),
                number_format($item['money_residue_now'], 2),
            ];
            return $carry;
        }, []);
    }

    public function collection()
    {
        return new Collection($this->list_bill);
    }

    public function headings(): array
    {
        return [
            '客户ID',
            '客户名称',
            '公司名称',
            '费用',
            '余额 ',

        ];
    }
}