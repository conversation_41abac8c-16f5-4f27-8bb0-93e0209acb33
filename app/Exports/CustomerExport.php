<?php

namespace App\Exports;

use App\User;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;

class CustomerExport implements FromCollection, WithTitle
{

    public function collection()
    {
        return User::all();
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return '美国' . mt_rand(1, 10000);
    }
}
