<?php

namespace App\Exports\StatTmp;

use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithHeadings, WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class Stat401Export implements FromArray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $lists;

    public function __construct(array $lists)
    {
        Font::setTrueTypeFontPath(storage_path() .'/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);

        $this->lists = array_reduce($lists, function ($carry, $item) {
            $carry[] = [
                $item['customer_id'] ?? ' ',
                $item['name'] ?? ' ',
                $item['time_start'] ?? ' ',
                $item['time_end'] ?? ' ',
                $item['valid_num'] ?? ' ',
                $item['valid_name_or_address_num'] ?? ' ',
            ];
            return $carry;
        }, []);
    }

    public function array(): array
    {
        return $this->lists;
    }

    public function headings(): array
    {
        return [
            '客户ID',
            '客户名称',
            "开始时间",
            "结束时间",
            '有效查询量',
            '总查得量',
        ];
    }
}