<?php

namespace App\Exports;

use App\Define\Common;
use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Style;
use PhpOffice\PhpSpreadsheet\Writer\Ods\Styles;

class HistoryV2SheetGenerate
{
    /**
     * @var \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet
     **/
    private $sheet;
    private $history;
    private $customer_info;

    /** @var int body 开始行数 */
    private $line_body = 10;

    //主体结算单的行范围，目的是为了做样式
    private $body_start_row = 6;
    private $body_end_row   = 11;

    /**
     * @param $sheet
     * @param $history
     * @param $customer_info
     */
    public function generate($sheet, $history, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $history, $customer_info);

        // 设置excel主体内容
        $this->setBody();


        // 设置 head
//        $this->setHeadForHistory();
//
//        // 设置 body
//        $this->setBodyForHistory();
//
//        // 设置 foot
//        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();
    }

    /**
     * 设置内容
     *
     * @access protected
     *
     * @return void
     **/
    protected function setBody()
    {
        $result = $this->history['result'];

        //题头
        $this->sheet->setCellValue('A1', '羽乐科技');
        $this->sheet->setCellValue('A2', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('A4', '收费通知单');


        //结算单主体部分
        $this->sheet->setCellValue('A6', '公司名称');
        $this->sheet->setCellValue('B6', $this->customer_info['company']);
        $this->sheet->mergeCells('B6:C6');

        $this->sheet->setCellValue('A7', '客户名称');
        $this->sheet->setCellValue('B7', $this->customer_info['name']);
        $this->sheet->mergeCells('B7:C7');
        $this->sheet->getStyle('A1:C7')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $this->sheet->setCellValue('A8', '上月剩余金额');
        $this->sheet->setCellValue('B8', $result['bill_month_start_residue_money']);
        $this->sheet->mergeCells('B8:C8');
        $this->sheet->getStyle('A8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->getStyle('B8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $this->sheet->setCellValue('A9', '期间');
        $this->sheet->setCellValue('B9', '充值金额');
        $this->sheet->setCellValue('C9', '消耗金额');
        $this->sheet->setCellValue('A10', $result['bill_month_data']['name']);
        $this->sheet->setCellValue('B10', $result['bill_month_data']['recharge']);
        $this->sheet->setCellValue('C10', $result['bill_month_data']['consume']);
        $this->sheet->getStyle('A9:C9')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->getStyle('A10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->getStyle('B10:C10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        //特殊金额
        $row = 11;
        foreach ($result['bill_month_special_data'] as $item) {
            $this->sheet->setCellValue('A' . $row, '「' . $item['month'] . '」' . $item['name']);
            $this->sheet->setCellValue('B' . $row, $item['recharge']);
            $this->sheet->setCellValue('C' . $row, $item['consume']);
            $row++;
        }
        $row--;
        $this->sheet->getStyle('A11:A' . $row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->getStyle('B11:C' . $row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        //合计
        $row++;
        $this->sheet->setCellValue('A' . $row, '合计');
        $this->sheet->setCellValue('B' . $row, $result['total']['recharge']);
        $this->sheet->setCellValue('C' . $row, $result['total']['consume']);
        $this->sheet->getStyle('A' . $row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->getStyle('B' . $row . ':C' . $row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        //剩余金额
        $row++;
        $this->sheet->setCellValue('A' . $row, '剩余金额');
        $this->sheet->setCellValue('B' . $row, $result['residue_money']);
        $this->sheet->mergeCells('B' . $row . ':C' . $row);
        $this->sheet->getStyle('A' . $row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->getStyle('B' . $row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->body_end_row = $row;


        //页脚
        $row += 3;
        $this->sheet->setCellValue('C' . $row, '北京羽乐创新有限公司');
        $row++;
        $this->sheet->setCellValue('C' . $row, '运营组');
        $row++;
        $this->sheet->setCellValue('C' . $row, $this->history['today']);

        $this->sheet->getStyle("A1:C" . $row)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        $this->sheet->getStyle('A' . ($row - 3) . ':C' . $row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);


        $col = ['A', 'B', 'C'];
        foreach ($col as $colItem) {
            $this->sheet->getColumnDimension($colItem)
                ->setAutoSize(true);
        }

        for ($i = 1; $i < $row; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A' . $this->body_start_row . ':C' . $this->body_end_row;
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $result = $this->history['result'];

        $this->sheet->setCellValue('A' . ($this->line_body + 1), '合计');
        $this->sheet->setCellValue('B' . ($this->line_body + 1), $result['total']['recharge']);
        $this->sheet->setCellValue('C' . ($this->line_body + 1), $result['total']['consume']);

        $this->sheet->setCellValue('A' . ($this->line_body + 2), '剩余金额');
        $this->sheet->setCellValue('B' . ($this->line_body + 2), $result['residue_money']);
        $this->sheet->mergeCells('B' . ($this->line_body + 2) . ':C' . ($this->line_body + 2));


        // 设置下标
        $this->sheet->setCellValue('C' . ($this->line_body + 5), '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('C' . ($this->line_body + 6), '运营组');
        $this->sheet->setCellValue('C' . ($this->line_body + 7), $this->history['today']);
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {
        // 添加本月的账单信息
        list($money_detail_this_month, $money_special_which_show) = [
            $this->history['money_detail_this_month'],
            $this->history["money_special_which_show"]
        ];
        $this->sheet->setCellValue(
            'A' . $this->line_body,
            $money_detail_this_month['month_begin'] . '--' . $money_detail_this_month['month_end']
        );
        $this->sheet->setCellValue(
            'B' . $this->line_body,
            number_format($money_detail_this_month["money_recharge_of_this_month"] ?? 0, 2)
        );
        $this->sheet->setCellValue(
            'C' . $this->line_body,
            number_format($money_detail_this_month["money_consume_of_this_month"] ?? 0, 2)
        );
        $this->line_body++;

        // 添加特殊费用
        collect($money_special_which_show)->each(
            function ($date_item) {
                $this->sheet->setCellValue('A' . $this->line_body, $date_item["title"]);
                $this->sheet->setCellValue('B' . $this->line_body, number_format($date_item["recharge"] ?? 0, 2));
                $this->sheet->setCellValue('C' . $this->line_body, number_format($date_item["consume"] ?? 0, 2));
                $this->line_body++;
            }
        );
    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $this->sheet->setCellValue('A1', Common::COMPANY_CN_NAME);
        $this->sheet->setCellValue('A2', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('A4', '收费通知单');
        $this->sheet->setCellValue('A6', '客户名称');

        list($company, $name, $money_residue) = [
            $this->customer_info['company'],
            $this->customer_info['name'],
            $this->history['money_last_before_this_month']['money_residue']
        ];

        $this->sheet->setCellValue('B6', $company);
        $this->sheet->mergeCells('B6:C6');
        $this->sheet->setCellValue('A7', '合作项目');
        $this->sheet->setCellValue('B7', $name);
        $this->sheet->mergeCells('B7:C7');

        // 去年生于金额是否为0
        if ($money_residue == 0) {
            $this->sheet->setCellValue('A8', '期间');
            $this->sheet->setCellValue('B8', '充值金额');
            $this->sheet->setCellValue('C8', '消耗金额');
            $this->line_body = 9;

        } else {
            $this->sheet->setCellValue('A8', '上月剩余金额');
            $this->sheet->setCellValue('B8', number_format($money_residue, 2));
            $this->sheet->mergeCells('B8:C8');

            $this->sheet->setCellValue('A9', '期间');
            $this->sheet->setCellValue('B9', '充值金额');
            $this->sheet->setCellValue('C9', '消耗金额');
        }
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $history
     * @param $customer_info
     */
    private function setAttr($sheet, $history, $customer_info)
    {
        $this->sheet         = $sheet;
        $this->history       = $history;
        $this->customer_info = $customer_info;
    }
}