<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class CustomerExpendExport implements FromArray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list;

    public function __construct($list = [])
    {
        $this->list = array_reduce($list, function ($arr, $item) {
            $arr[] = [
                $item['customer_id'] ?? ' ',
                $item['customer_name'] ?? ' ',
                $item['company'] ?? ' ',
                $item['salesman'] ?? ' ',
                $item['dept'] ?? ' ',
                $item['father_name'] ?? ' ',
                $item['product_name'] ?? ' ',
                $item['operator_name'] ?? ' ',
                $item['name'] ?? ' ',
                date('Y-m', strtotime($item['start_date'] . '01')),
                number_format($item['money'], 2),
                $item['fee_number'] ?? 0,
                $item['remark'] ?? ' ',
                $item['sourceName'] ?? ' ',
                $item['update_time'] ? date('Y-m-d H:i:s', $item['update_time']) : ' ',
                empty($item['should_date'])?'':date('Y-m', strtotime($item['should_date'])),
                $item['is_old_income']?'是':'否',
                $item['channel_mode'] == 1?'渠道客户':'非渠道客户',
            ];
            return $arr;
        }, []);
    }

    public function array(): array
    {
        return $this->list;
    }

    public function headings(): array
    {
        return [
            '客户ID',
            '客户名称',
            '公司名称',
            '商务',
            '区域',
            '主产品',
            '子产品',
            '运营商',
            '标题',
            '调整月份',
            '金额',
            '计费用量',
            '备注信息',
            '来源',
            '操作时间',
            '真实调整月份',
            '是否为老收入',
            '是否为渠道客户',
        ];
    }
}