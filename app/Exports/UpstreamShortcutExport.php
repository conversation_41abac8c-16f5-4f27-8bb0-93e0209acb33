<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithH<PERSON>ings, WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class UpstreamShortcutExport implements  FromArray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list;

    public function __construct(array $list)
    {
        Font::setTrueTypeFontPath(storage_path() .'/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->list = array_reduce($list, function ($carry, $item) {
            $carry[] = [
                $item['field_cn'] ?? ' ',
                $item['field'] ?? '',
                number_format($item['success']),
                $item['upstream_cn'] ?? ''
            ];
            return $carry;
        }, []);
    }

    public function array(): array
    {
        return $this->list;
    }

    public function headings(): array
    {
        return [
            '字段中文名',
            '字段英文名',
            '有效调用量',
            '数据源'
        ];
    }

}