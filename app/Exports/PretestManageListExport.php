<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\{
    FromCollection,
    ShouldAutoSize,
    WithHeadings,
    WithStrictNullComparison
};

class PretestManageListExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $pretest_manage_list;

    public function __construct(array $pretest_manage_list)
    {
        $this->pretest_manage_list = array_reduce($pretest_manage_list, function ($carry, $item) {
            // 遍历 objective_list
            foreach ($item['objective_list'] ?? [] as $objective) {
                $carry[] = [
                    $item['apply_time'] ?? ' ',
                    $item['company_short_name'] ?? ' ',
                    $item['dept_name'] ?? ' ',
                    $item['salesman'] ?? ' ',
                    $item['father_name'] ?? ' ',
                    $item['test_result_text'] ?? ' ',
                    $item['return_time'] ?? ' ',
                    $objective['objective_name'] ?? ' ',  // 使用当前遍历的 objective 数据
                    $objective['result_value'] ?? ' ',
                    $objective['test_result_remark'] ?? ' ',
                    $item['is_open_text'] ?? ' ',
                    $item['is_call_text'] ?? ' ',
                    $item['test_status_text'] ?? ' ',
                ];
            }
            // 如果 objective_list 为空，至少保留一行基础数据
            if (empty($item['objective_list'])) {
                $carry[] = [
                    $item['apply_time'] ?? ' ',
                    $item['company_short_name'] ?? ' ',
                    $item['dept_name'] ?? ' ',
                    $item['salesman'] ?? ' ',
                    $item['father_name'] ?? ' ',
                    $item['test_result_text'] ?? ' ',
                    $item['return_time'] ?? ' ',
                    ' ',  // objective_name 留空
                    ' ',  // result_value 留空
                    ' ',  // remark 留空
                    $item['is_open_text'] ?? ' ',
                    $item['is_call_text'] ?? ' ',
                    $item['test_status_text'] ?? ' ',
                ];
            }
            return $carry;
        }, []);
    }

    public function collection()
    {
        return new Collection($this->pretest_manage_list);
    }

    public function headings(): array
    {
        return [
            '申请时间',
            '客户简称',
            '区域',
            '销售',
            '测试产品',
            '反馈结果',
            '返回时间',
            '反馈指标',
            '反馈指标结果',
            '反馈备注',
            '是否开通',
            '是否调用',
            '进度',
        ];
    }
}