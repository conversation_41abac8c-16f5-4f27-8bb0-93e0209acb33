<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;

class RemitSplitPriceExport implements FromArray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list;

    public function __construct($list = [])
    {
        $this->list = array_reduce($list, function ($arr, $item) {
            $arr[] = [
                $item['receipt_serial'] ?? ' ',
                $item['customer_id'] ?? ' ',
                $item['customer_name'] ?? ' ',
                $item['company'] ?? ' ',
                $item['product_name'] ?? ' ',
                date('Y-m', strtotime($item['month'] . '01')),
                number_format($item['money'], 2),
                date('Y-m-d', $item['remit_date']),
                $item['source_name'] ?? ' ',
                $item['salesname'] ?? ' ',
                $item['area_person'] ?? ' ',

            ];
            return $arr;
        }, []);
    }

    public function array(): array
    {
        return $this->list;
    }

    public function headings(): array
    {
        return [
            '流水号',
            '客户ID',
            '客户名称',
            '公司名称',
            '产品名称',
            '拆分月份',
            '拆分金额',
            '交易日期',
            '来源',
            '销售',
            '区域',
        ];
    }
}