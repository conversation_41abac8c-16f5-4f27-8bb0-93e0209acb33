<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\{
    FromCollection,
    ShouldAutoSize,
    WithHeadings,
    WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class ProductBillExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list_bill;

    public function __construct(array $list_bill)
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->list_bill = array_reduce($list_bill, function ($carry, $item) {
            $carry[] = [
                $item['product_name'] ?? ' ',
                number_format($item['section_number']),
                number_format($item['money'], 2)
            ];
            return $carry;
        }, []);
    }

    public function collection()
    {
        return new Collection($this->list_bill);
    }

    public function headings(): array
    {
        return [
            '产品名称',
            '计费用量',
            '费用',
        ];
    }
}