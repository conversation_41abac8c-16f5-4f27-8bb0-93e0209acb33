<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithH<PERSON>ings, WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class UpstreamShortcutDetailExport implements  FromArray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list;

    public function __construct(array $list)
    {
        Font::setTrueTypeFontPath(storage_path() .'/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->list = array_reduce($list, function ($carry, $item) {
            $carry[] = [
                $item['day'] ?? ' ',
                number_format($item['success']),
            ];
            return $carry;
        }, []);
    }

    public function array(): array
    {
        return $this->list;
    }

    public function headings(): array
    {
        return [
            '日期',
            '有效调用量',
        ];
    }

}