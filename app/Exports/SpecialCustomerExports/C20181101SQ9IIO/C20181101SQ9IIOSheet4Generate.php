<?php

namespace App\Exports\SpecialCustomerExports\C20181101SQ9IIO;

use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101SQ9IIOSheet4Generate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        //设置样式
        $this->setStyle();
    }

    //设置样式
    private function setStyle()
    {

        //行高
        for ($i = 1; $i <= 11; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:E11')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:E11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('C8:E11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A5:E11';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $details = $this->list_product_bill_alo["FA201812143COLWP"]['list_bills'][202]['details'];

        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);
        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B2', $this->customer_info['name']);

        $this->sheet->setCellValue('A5', '公司名称');
        $this->sheet->setCellValue('B5', $this->customer_info['company']);
        $this->sheet->mergeCells('B5:E5');

        $this->sheet->setCellValue('B6', '客户名称');
        $this->sheet->setCellValue('B6', $this->customer_info['name']);
        $this->sheet->mergeCells('B6:E6');

        $this->sheet->setCellValue('A7', '期间');
        $this->sheet->setCellValue('B7', '运营商');
        $this->sheet->setCellValue('C7', '单价');
        $this->sheet->setCellValue('D7', '调取量');
        $this->sheet->setCellValue('E7', '消耗金额');

        $this->sheet->setCellValue('A8', $details['section_begin'] . '--' . $details['section_end']);
        $this->sheet->mergeCells('A8:A10');
        $list_distribute = $details['list_distribute'];
        $i               = 8;
        foreach ($list_distribute as $item) {
            $this->sheet->setCellValue('B' . $i, $item['operator']);
            $this->sheet->setCellValue('C' . $i, number_format($item['price'], 2));
            $this->sheet->setCellValue('D' . $i, number_format($item['number']));
            $this->sheet->setCellValue('E' . $i, number_format($item['money'], 2));
            $i++;
        }

        $this->sheet->setCellValue('A11', '合计');
        $this->sheet->setCellValue('E11', number_format($details['money'], 2));
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {

    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {

    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet                 = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info         = $customer_info;
    }
}