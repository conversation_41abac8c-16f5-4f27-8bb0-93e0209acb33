<?php

namespace App\Exports\SpecialCustomerExports\C20181101SQ9IIO;

use App\Define\Common;
use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101SQ9IIOSheet2Generate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        //设置样式
        $this->setStyle();
    }

    //设置样式
    private function setStyle()
    {

        //行高
        for ($i = 1; $i <= 16; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:E11')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:E11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('B10:E11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('B8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        //备注样式
        $this->sheet->getStyle('A19')
            ->applyFromArray(["font" => ["color" => ['rgb' => 'FF0000']]]);
        $this->sheet->getStyle('A19')->getAlignment()->setWrapText(true);
        $this->sheet->getRowDimension(19)
            ->setRowHeight('100');
        $this->sheet->getStyle('A19')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);
    }


    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:C11';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $details = $this->list_product_bill_alo["FA201812143COLWP"]['list_bills'][200]['details'];

        $this->sheet->setCellValue('A1', Common::COMPANY_CN_NAME);
        $this->sheet->setCellValue('A2', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('A4', '收费通知单');

        $this->sheet->setCellValue('A6', '公司名称');
        $this->sheet->setCellValue('B6', $this->customer_info['company']);
        $this->sheet->mergeCells('B6:C6');

        $this->sheet->setCellValue('A7', '客户名称');
        $this->sheet->setCellValue('B7', $this->customer_info['name']);
        $this->sheet->mergeCells('B7:C7');

        $this->sheet->setCellValue('A8', '上月结转余额');
        $this->sheet->setCellValue('B8', round($details['carry'], 2));
        $this->sheet->mergeCells('B8:C8');

        $this->sheet->setCellValue('A9', '期间');
        $this->sheet->setCellValue('B9', '充值金额');
        $this->sheet->setCellValue('C9', '消耗金额');

        $this->sheet->setCellValue('A10', $details['section_begin'] . '--' . $details['section_end']);
        $this->sheet->setCellValue('B10', 0.00);
        $this->sheet->setCellValue('C10', $details['money']);


        $this->sheet->setCellValue('A11', '未消耗金额');
        $this->sheet->setCellValue('B11', 0.00);
        $this->sheet->mergeCells('B11:C11');

        $this->sheet->setCellValue('C14', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('C15', '运营组');
        $this->sheet->setCellValue('C16', date('Y年m月d日'));

        //备注
        $this->sheet->mergeCells('A19:E19');
        $remark = <<<REMARK
备注：   上月结转余额当前展示的是（t-2）月的总消耗金额，实际上月结转余额=邦秒验总充值-（t-2）月的总消耗
        未消耗金额=上月结转余额+充值金额-当月消耗金额
        请注意，需要修改3处：上月结转余额、充值金额、未消耗金额
        t-代表当前月份
REMARK;

        $this->sheet->setCellValue('A19', $remark);
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {

    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {

    }

    /**
     * 设置属性
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
    }
}