<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/10/29 0029
 * Time: 16:06
 */

namespace App\Exports\SpecialCustomerExports\C20181101SQ9IIO;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class C20181101SQ9IIOEmailBillV2 implements WithMultipleSheets
{
    use Exportable;
    /** @var array 客户信息 */
    private $customer_info;

    /** @var array 结算单信息 */
    private $history;

    /** @var array 各个产品的算法账单信息 */
    private $list_product_bill_alo;

    /**
     * EmailBill constructor.
     *
     * @param array $customer_info
     * @param array $history
     * @param array $list_product_bill_alo
     */
    public function __construct(array $customer_info, array $history, array $list_product_bill_alo)
    {
        $this->history               = $history;
        $this->customer_info         = $customer_info;
        $this->list_product_bill_alo = $list_product_bill_alo;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        // sheet1 邦信分详单版V1
        $sheet[] = new C20181101SQ9IIOSheet1Export($this->customer_info, $this->list_product_bill_alo);

        // sheet2  邦秒验
        $sheet[] = new C20181101SQ9IIOSheet2Export($this->customer_info, $this->list_product_bill_alo);

        // sheet3  手机号三要素验证
        $sheet[] = new C20181101SQ9IIOSheet3Export($this->customer_info, $this->list_product_bill_alo);

        // sheet4  手机号在网时长
        $sheet[] = new C20181101SQ9IIOSheet4Export($this->customer_info, $this->list_product_bill_alo);

        return $sheet;
    }
}