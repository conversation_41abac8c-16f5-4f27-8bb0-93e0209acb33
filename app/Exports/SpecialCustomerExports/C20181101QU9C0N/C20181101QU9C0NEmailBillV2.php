<?php

namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class C20181101QU9C0NEmailBillV2 implements WithMultipleSheets
{
    use Exportable;
    /** @var array 客户信息 */
    private $customer_info;

    /** @var array 结算单信息 */
    private $history;

    /** @var array 各个产品的算法账单信息 */
    private $list_product_bill_alo;

    /**
     * EmailBill constructor.
     *
     * @param array $customer_info
     * @param array $history
     * @param array $list_product_bill_alo
     */
    public function __construct(array $customer_info, array $history, array $list_product_bill_alo)
    {
        $this->history               = $history;
        $this->customer_info         = $customer_info;
        $this->list_product_bill_alo = $list_product_bill_alo;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        // sheet1 结算单
        $sheet[] = new C20181101QU9C0NSheet1Export($this->customer_info, $this->history);

        // 增加一个sheet2 非邦秒配产品的结算单
        $sheet[] = new C20181101QU9C0NExtraSheet2Export($this->customer_info, $this->list_product_bill_alo, $this->history);

        // sheet2  邦信分详单版v1 消耗明细
        $sheet[] = new C20181101QU9C0NSheet2Export($this->customer_info, $this->list_product_bill_alo);

        //sheet3 邦信分详单版V1 每日调用量
        $sheet[] = new C20181101QU9C0NSheet3Export($this->customer_info, $this->list_product_bill_alo);

        //sheet 邦秒配消耗明细
        $sheet[] = new C20181101QU9C0NSheet4Export($this->customer_info, $this->list_product_bill_alo);

        //sheet5 邦秒配 每日调用量
        $sheet[] = new C20181101QU9C0NSheet5Export($this->customer_info, $this->list_product_bill_alo);

        //sheet6 - sheet17
        $sheet[] = new C20181101QU9C0NSheet6Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet7Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet8Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet9Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet10Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet11Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet12Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet13Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet14Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet15Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet16Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet17Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet18Export($this->customer_info, $this->list_product_bill_alo);
        $sheet[] = new C20181101QU9C0NSheet19Export($this->customer_info, $this->list_product_bill_alo);

        return $sheet;
    }
}