<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/4 0004
 * Time: 14:05
 */

namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;

use App\Define\Common;
use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101QU9C0NSheet1Generate
{
    private $sheet;
    private $customer_info;
    private $history_list;
    private $extra_rows = 0;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $history_list, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $history_list, $customer_info);

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        //设置样式
        $this->setStyle();
    }

    private function setStyle()
    {
        $endCol = 'C';
        $endRow = 17 + $this->extra_rows;
        //行高
        for ($i = 1; $i <= $endRow; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:' . $endCol . $endRow)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:' . $endCol . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('B10:' . $endCol . ($endRow - 4))
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('B8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:C' . (12 + $this->extra_rows);
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {

        $this->sheet->setCellValue('A1', Common::COMPANY_CN_NAME);
        $this->sheet->setCellValue('A2', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('A4', '收费通知单');


        $this->sheet->setCellValue('A6', '公司名称');
        $this->sheet->setCellValue('B6', $this->customer_info['company']);
        $this->sheet->mergeCells('B6:C6');

        $this->sheet->setCellValue('A7', '客户名称');
        $this->sheet->setCellValue('B7', $this->customer_info['name']);
        $this->sheet->mergeCells('B7:C7');

        $this->sheet->setCellValue('A8', '上月结转余额');
        $this->sheet->setCellValue(
            'B8',
            $this->history_list['result']['bill_month_start_residue_money']
        );
        $this->sheet->mergeCells('B8:C8');

        $this->sheet->setCellValue('A9', '期间');
        $this->sheet->setCellValue('B9', '充值金额');
        $this->sheet->setCellValue('C9', '消耗金额');

        $this->sheet->setCellValue('A10', $this->history_list['result']['bill_month_data']['name']);
        $this->sheet->setCellValue(
            'B10',
            $this->history_list['result']['bill_month_data']['recharge']
        );
        $this->sheet->setCellValue(
            'C10',
            $this->history_list['result']['bill_month_data']['consume']
        );

        //特殊消耗
        foreach ($this->history_list['result']['bill_month_special_data'] as $item) {
            $this->extra_rows++;
            $row = 10 + $this->extra_rows;
            $this->sheet->setCellValue('A' . $row, $item['month'] . $item['name']);
            $this->sheet->setCellValue('B' . $row, $item['recharge']);
            $this->sheet->setCellValue('C' . $row, $item['consume']);
        }

        $row = 10 + $this->extra_rows + 1;

        $this->sheet->setCellValue('A' . $row, '合计');
        $this->sheet->setCellValue(
            'B' . $row,
            $this->history_list['result']['total']['recharge']
        );
        $this->sheet->setCellValue('C' . $row, $this->history_list['result']['total']['consume']);

        $row++;
        $this->sheet->setCellValue('A' . $row, '未消耗金额');
        $this->sheet->setCellValue(
            'B' . $row,
            $this->history_list['result']['residue_money']
        );
        $this->sheet->mergeCells('B' . $row . ':C' . $row);

        $row += 3;
        $this->sheet->setCellValue('C' . $row, '北京羽乐创新科技有限公司');
        $row++;
        $this->sheet->setCellValue('C' . $row, '运营组');
        $row++;
        $this->sheet->setCellValue('C' . $row, date('Y年m月d日'));
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $history_list
     * @param $customer_info
     */
    private function setAttr($sheet, array $history_list, array $customer_info)
    {
        $this->sheet         = $sheet;
        $this->history_list  = $history_list;
        $this->customer_info = $customer_info;
    }
}