<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/4 0004
 * Time: 14:47
 */

namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;

use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;

class C20181101QU9C0NSheet17Export implements WithEvents, WithTitle
{
    use RegistersEventListeners;
    /** @var array 历史账单信息 */
    private static $list_product_bill_alo;

    /** @var array 客户信息 */
    private static $customer_info;

    /** @var object 定制sheet的对象 */
    private static $history_sheet_generate;

    /**
     * HistoryBillExport constructor.
     *
     * @param array $customer_info
     * @param array $list_product_bill_alo
     */
    public function __construct(array $customer_info, array $list_product_bill_alo)
    {
        static::$list_product_bill_alo           = $list_product_bill_alo;
        static::$customer_info          = $customer_info;
        static::$history_sheet_generate = new C20181101QU9C0NSheet17Generate();
    }

    /**
     * sheet
     * @return string
     */
    public function title(): string
    {
        return '手机号消费档次消耗明细';
    }

    /**
     * 定制插入单元
     *
     * @param AfterSheet $event
     */
    public static function afterSheet(AfterSheet $event)
    {
        $sheet = $event->sheet;
        static::$history_sheet_generate->generate($sheet, static::$list_product_bill_alo, static::$customer_info);
    }
}