<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/4 0004
 * Time: 14:05
 */

namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;

use App\Define\Common;
use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101QU9C0NExtraSheet2Generate
{
    private $sheet;
    private $customer_info;
    private $list_product_bill_alo;
    private $history_list;
    private $extra_rows = 0;

    private $account_id = '****************';

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info, $history_list)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info, $history_list);

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        $this->setStyle();
    }

    private function setStyle()
    {
        $endCol = 'C';
        $endRow = 17 + $this->extra_rows;
        //行高
        for ($i = 1; $i <= $endRow; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:' . $endCol . $endRow)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:' . $endCol . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('B10:' . $endCol . ($endRow - 4))
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('B8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $this->sheet->getStyle('E6')
            ->applyFromArray(["font" => ["color" => ['rgb' => 'FF0000']]]);
        $this->sheet->getStyle('E6')
            ->getAlignment()
            ->setWrapText(true);
        $this->sheet->getStyle('E6')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:C' . (12 + $this->extra_rows);
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $this->sheet->setCellValue('A1', Common::COMPANY_CN_NAME);
        $this->sheet->setCellValue('A2', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('A4', '收费通知单');


        $this->sheet->setCellValue('A6', '公司名称');
        $this->sheet->setCellValue('B6', $this->customer_info['company']);
        $this->sheet->mergeCells('B6:C6');

        $this->sheet->setCellValue('A7', '客户名称');
        $this->sheet->setCellValue('B7', $this->customer_info['name']);
        $this->sheet->mergeCells('B7:C7');

        $this->sheet->setCellValue('A8', '上月结转余额');

        //上月非邦秒配消耗
        $total_history_consume = round(
            $this->history_list['recharge']['history_recharge_money'] -
            $this->history_list['consume']['history_consume_money'] +
            $this->history_list['special']['history_special_recharge_money'] -
            $this->history_list['special']['history_special_consume_money'] +
            $this->list_product_bill_alo[$this->account_id]['list_bills'][0]['details']['prev_month_history_consume_money'],
            2
        );

        //本月非邦秒配消耗
        $bill_month_consume = round(
            $this->history_list['consume']['bill_month_consume_money'] - $this->list_product_bill_alo[$this->account_id]['list_bills'][0]['details']['money'],
            2
        );
        $this->sheet->setCellValue(
            'B8',
            $total_history_consume
        );
        $this->sheet->mergeCells('B8:C8');

        $this->sheet->setCellValue('A9', '期间');
        $this->sheet->setCellValue('B9', '充值金额');
        $this->sheet->setCellValue('C9', '消耗金额');

        $this->sheet->setCellValue('A10', $this->history_list['result']['bill_month_data']['name']);
        $this->sheet->setCellValue(
            'B10',
            $this->history_list['recharge']['bill_month_recharge_money']
        );
        $this->sheet->setCellValue(
            'C10',
            $bill_month_consume
        );

        //特殊消耗
        foreach ($this->history_list['result']['bill_month_special_data'] as $item) {
            $this->extra_rows++;
            $row = 10 + $this->extra_rows;
            $this->sheet->setCellValue('A' . $row, $item['month'] . $item['name']);
            $this->sheet->setCellValue('B' . $row, $item['recharge']);
            $this->sheet->setCellValue('C' . $row, $item['consume']);
        }

        $row = 10 + $this->extra_rows + 1;

        $this->sheet->setCellValue('A' . $row, '合计');
        $this->sheet->setCellValue(
            'B' . $row,
            0
        );
        //合计消耗
        $total_consume = round(
            $this->history_list['special']['bill_month_special_consume_money'] + $bill_month_consume,
            2
        );
        $this->sheet->setCellValue('C' . $row, $total_consume);

        $row++;
        $this->sheet->setCellValue('A' . $row, '未消耗金额');
        $this->sheet->setCellValue(
            'B' . $row,
            0
        );
        $this->sheet->mergeCells('B' . $row . ':C' . $row);

        $row += 3;
        $this->sheet->setCellValue('C' . $row, '北京羽乐创新科技有限公司');
        $row++;
        $this->sheet->setCellValue('C' . $row, '运营组');
        $row++;
        $this->sheet->setCellValue('C' . $row, date('Y年m月d日'));

        $row    += 8;
        $remark = <<<REMARK
备注：   上月结转余额当前展示的是客户（t-1）月总充值金额-(t-1)月邦秒配以外产品总消耗金额，需在此基础上再减去邦秒配的充值金额
        充值金额默认是账单月份总充值金额，注意区分充值归属哪个产品
        合计=上月结转余额+充值金额+特殊赠送
        未消耗金额=合计-消耗金额
        请注意，需要修改4处：上月结转余额、充值金额、合计、未消耗金额
        ps:t代表账单月份
REMARK;

        $this->sheet->setCellValue('E6', $remark);
        $this->sheet->mergeCells('E6:J17');
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info, array $history_list)
    {
        $this->sheet                 = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info         = $customer_info;
        $this->history_list          = $history_list;
    }
}