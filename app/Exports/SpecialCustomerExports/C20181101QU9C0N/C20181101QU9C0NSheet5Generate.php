<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/4 0004
 * Time: 14:49
 */

namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;

use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101QU9C0NSheet5Generate
{
    private $sheet;
    private $customer_info;
    private $list_product_bill_alo;

    private $account_id = '****************';
    private $product_id = 0;
    private $i          = 10;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        $this->setStyle();
    }

    private function setStyle()
    {
        //行高
        for ($i = 1; $i <= 40; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:B40')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('B1')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $this->sheet->getStyle('A1:A40')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('B2:B40')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A1:B' . $this->i;
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $details = $this->list_product_bill_alo[$this->account_id]['list_bills'][$this->product_id];

        $this->sheet->setCellValue('A1', '时间');
        $this->sheet->setCellValue('B1', '有效查询量');

        $this->sheet->setCellValue('A2', '合计');
        $this->sheet->setCellValue('B2', number_format(array_sum($details['details']['day_list'])));

        $i = 3;
        foreach ($details['details']['day_list'] as $date => $number) {
            $this->sheet->setCellValue('A' . $i, $date);
            $this->sheet->setCellValue('B' . $i, number_format($number));
            $i++;
        }

        $this->i = $i - 1;
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet                 = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info         = $customer_info;
    }
}