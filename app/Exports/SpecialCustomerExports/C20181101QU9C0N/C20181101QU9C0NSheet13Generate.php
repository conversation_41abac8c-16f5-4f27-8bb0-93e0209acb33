<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/4 0004
 * Time: 14:49
 */

namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;

use PhpOffice\PhpSpreadsheet\Shared\Font;

class C20181101QU9C0NSheet13Generate
{
    use C20181101QU9C0NCommonStyle;

    private $sheet;
    private $customer_info;
    private $list_product_bill_alo;

    private $account_id = '****************';
    private $product_id = 213;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        $this->setStyle2();
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'B6:F11';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $details = $this->list_product_bill_alo[$this->account_id]['list_bills'][$this->product_id];
        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);
        $this->sheet->setCellValue('B2', $this->customer_info['name']);

        $this->sheet->setCellValue('B6', '服务项目');
        $this->sheet->setCellValue('C6', '手机号三要素验证');
        $this->sheet->mergeCells('C6:F6');

        $this->sheet->setCellValue('B7', '期间');
        $this->sheet->setCellValue('C7', '运营商');
        $this->sheet->setCellValue('D7', '单价');
        $this->sheet->setCellValue('E7', '有效调用量');
        $this->sheet->setCellValue('F7', '消耗金额');

        $this->sheet->setCellValue(
            'B8',
            $details['details']['section_begin'] . ' - ' . $details['details']['section_end']
        );
        $this->sheet->mergeCells('B8:B10');

        $i = 8;
        foreach ($details['details']['list_distribute'] as $item) {
            $this->sheet->setCellValue('C' . $i, $item['operator']);
            $this->sheet->setCellValue('D' . $i, $item['price']);
            $this->sheet->setCellValue('E' . $i, number_format($item['number']));
            $this->sheet->setCellValue('F' . $i, number_format($item['money'], 2));
            $i++;
        }

        $this->sheet->setCellValue('B11', '合计');
        $this->sheet->setCellValue('C11', number_format($details['details']['money'], 2));
        $this->sheet->mergeCells('C11:F11');
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet                 = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info         = $customer_info;
    }
}