<?php
namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;


use PhpOffice\PhpSpreadsheet\Style\Alignment;

trait C20181101QU9C0NCommonStyle
{
    //不区分运营商
    protected function setStyle1()
    {
        //行高
        for ($i = 1; $i <= 9; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:E9')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:E9')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('C8:E9')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    //区分运营商
    protected function setStyle2()
    {
        //行高
        for ($i = 1; $i <= 11; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }
        //垂直居中
        $this->sheet->getStyle('A1:F11')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:F11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('D8:F10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('C11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }
}