<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/4 0004
 * Time: 14:49
 */

namespace App\Exports\SpecialCustomerExports\C20181101QU9C0N;

use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101QU9C0NSheet4Generate
{
    private $sheet;
    private $customer_info;
    private $list_product_bill_alo;

    private $account_id = '****************';
    private $product_id = 0;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        $this->setStyle();
    }

    private function setStyle()
    {
        //行高
        for ($i = 1; $i <= 11; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:C11')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:C11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('C7:C8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('C10:C11')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'B6:C11';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $details = $this->list_product_bill_alo[$this->account_id]['list_bills'][$this->product_id];
        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);
        $this->sheet->setCellValue('B2', $this->customer_info['name']);

        $this->sheet->setCellValue('B6', '服务项目');
        $this->sheet->setCellValue('C6', '邦秒配');

        $this->sheet->setCellValue('B7', '合同可调用次数');
        $this->sheet->setCellValue('C7', number_format($details['details']['total']));

        $this->sheet->setCellValue('B8', '上月结转剩余次数');
        $this->sheet->setCellValue('C8', number_format($details['details']['prev_month']));

        $this->sheet->setCellValue('B9', '期间');
        $this->sheet->setCellValue('C9', '有效调取量');

        $this->sheet->setCellValue('B10', $details['details']['section_begin'] . ' - ' . $details['details']['section_end']);
        $this->sheet->setCellValue('C10', number_format($details['section_number']['fee_number']));

        $this->sheet->setCellValue('B11', '剩余可调用次数');
        $this->sheet->setCellValue('C11', number_format($details['details']['super']));
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
    }
}