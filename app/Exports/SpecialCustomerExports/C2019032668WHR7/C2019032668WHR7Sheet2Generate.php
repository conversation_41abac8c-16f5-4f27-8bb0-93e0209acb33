<?php

namespace App\Exports\SpecialCustomerExports\C2019032668WHR7;

use PhpOffice\PhpSpreadsheet\Shared\Font;

class C2019032668WHR7Sheet2Generate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:F9';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $shortcut_total = $this->list_product_bill_alo["pei"];
        $this->sheet->setCellValue('A9', '合计');
        $this->sheet->setCellValue('B9', number_format($shortcut_total["money_total"], 2));
        $this->sheet->mergeCells('B9:F9');

        $this->sheet->setCellValue('A11', '备注：核算成详单量=邦秒配调用量-催收分调用量*400');
        $this->_setRedFont('A11');
        $this->sheet->mergeCells('A11:F11');

        $this->sheet->setCellValue('F13', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('F14', '运营组');
        $this->sheet->setCellValue('F15', date("Y年m月d日"));
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {
        $this->sheet->setCellValue('A7', '期间');
        $this->sheet->setCellValue('B7', "单价");
        $this->sheet->setCellValue('C7', "邦秒配调取量");
        $this->sheet->setCellValue('D7', "催收分调用量");
        $this->sheet->setCellValue('E7', "核算成详单量");
        $this->sheet->setCellValue('F7', "消耗金额");

        $details = $this->list_product_bill_alo["pei"];

        $this->sheet->setCellValue('A8', $details["month_begin"] . "--" . $details["month_end"]);
        $this->sheet->setCellValue('B8', $details["price"]);
        $this->sheet->setCellValue('C8', number_format($details["number_pei"]));
        $this->sheet->setCellValue('D8', number_format($details["number_xiangdan_v2"]));

        if ($details["number_valid"] >= 0) {
            $this->sheet->setCellValue('E8', number_format($details["number_valid"]));
        } else {
            $this->sheet->setCellValue('E8', "(" . number_format(-$details["number_valid"]) . ")");
            $this->_setRedFont("E8");
        }
        $this->sheet->setCellValue('F8', number_format($details["money_this_month"], 2));
    }

    /**
     * 设置红色字体
     * @param string $cell
     */
    private function _setRedFont(string $cell)
    {
        $this->sheet->getStyle($cell)
            ->applyFromArray(["font" => ["color" => ['rgb' => 'FF0000']]]);
    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);
        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B2', $this->customer_info['name']);

        $this->sheet->setCellValue('A6', '服务项目');
        $this->sheet->setCellValue('B6', "邦秒配");
        $this->sheet->mergeCells('B6:F6');
    }

    /**
     * 设置属性
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
    }
}