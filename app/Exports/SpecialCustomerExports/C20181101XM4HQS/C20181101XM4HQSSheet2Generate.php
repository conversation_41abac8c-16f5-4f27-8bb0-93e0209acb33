<?php

namespace App\Exports\SpecialCustomerExports\C20181101XM4HQS;

use PhpOffice\PhpSpreadsheet\Shared\Font;

class C20181101XM4HQSSheet2Generate
{
    private $sheet;

    /** @var array 每日调用信息 */
    private $list_product_bill_alo;

    /** @var array 客户信息 */
    private $customer_info;

    /** @var int 开始每日循环的起始行数 */
    private $line_body = 3;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet,array $list_product_bill_alo, array $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
//        $this->setBorder();
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:E11';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {
        $details = $this->list_product_bill_alo["shortcut_details"]["details"];
        array_walk($details, function($item, $day){
            $this->sheet->setCellValue('A' . $this->line_body, $day);
            $this->sheet->setCellValue('B' . $this->line_body, number_format($item["valid_number"] ?? ""));
            $this->sheet->setCellValue('C' . $this->line_body, number_format($item["valid_field_number"] ?? ""));
            $this->line_body ++;
        });
    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $total = $this->list_product_bill_alo["shortcut_details"]["total"] ?? [];

        $this->sheet->setCellValue('A1', '时间');
        $this->sheet->setCellValue('B1', "有效查询量");;
        $this->sheet->setCellValue('C1', "有效查询字段量");
        $this->sheet->setCellValue('A2', "合计");
        $this->sheet->setCellValue('B2', $total["valid_number"] ?? "");
        $this->sheet->setCellValue('C2', $total["valid_field_number"] ?? "");
    }

    /**
     * 设置属性
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
    }
}