<?php

namespace App\Exports\SpecialCustomerExports\C20181101XM4HQS;

use PhpOffice\PhpSpreadsheet\Shared\Font;

class C20181101XM4HQSSheet3Generate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
//        $this->setBorder();
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:E11';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {
        $this->sheet->setCellValue('A9', '期间');
        $this->sheet->setCellValue('B9', "有效调取量");

        $pei_total = $this->list_product_bill_alo["pei_total"] ?? [];
        $details = $pei_total["details"] ?? [];

        $this->sheet->setCellValue('A10', $details["month_begin"] . "--" . $details["month_end"]);
        $this->sheet->setCellValue('B10', number_format($details["number"]));

        $this->sheet->setCellValue('A11', '合计');
        $this->sheet->setCellValue('B11', number_format($pei_total["number_total_this_month"] ?? ""));

        $this->sheet->setCellValue('A12', "剩余调用次数");
        $this->sheet->setCellValue('B12', number_format($pei_total["number_last_now"]));

    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $pei_total = $this->list_product_bill_alo["pei_total"] ?? [];
        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);
        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B2', '萨摩耶');

        $this->sheet->setCellValue('A6', '服务项目');
        $this->sheet->setCellValue('B6', "邦秒配单号版");

        $this->sheet->setCellValue('A7', '包年调用次数');
        $this->sheet->setCellValue('B7', number_format($pei_total["year_has_number"]));

        $this->sheet->setCellValue('A8', '上月结转次数');
        $this->sheet->setCellValue('B8', number_format($pei_total["number_last_before_this_month"]));
    }

    /**
     * 设置属性
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
    }
}