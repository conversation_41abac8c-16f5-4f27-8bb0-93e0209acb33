<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/21 0021
 * Time: 13:46
 */

namespace App\Exports\SpecialCustomerExports\C20181101G8K9DA;

use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101G8K9DASheetGenerate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;
    private $account_name;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info, $account_name)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info, $account_name);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        //设置样式
        $this->setStyle();
    }

    private function setStyle()
    {
        //行高
        for ($i = 2; $i <= 10; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:E9')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:E9')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('C7:E10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A5:E9';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        list($money_consume_of_this_month, $money_residue_now) = [
            $this->list_product_bill_alo["money_detail_this_month"]["money_consume_of_this_month"],
            $this->list_product_bill_alo["list_aggregate_money"]["money_residue_now"],
        ];

        $this->sheet->setCellValue('A9', '合计');
        $this->sheet->setCellValue('B9', "");
        $this->sheet->setCellValue('C9', "");
        $this->sheet->setCellValue('D9', number_format($money_consume_of_this_month, 2));

        $this->sheet->setCellValue('A10', '剩余金额');
        $this->sheet->setCellValue('B10', number_format($money_residue_now, 2)); // 置空字符串 所以不可以使用number_format
        $this->sheet->mergeCells('B10:D10');

        $this->sheet->setCellValue('D14', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('D15', '运营组');
        $this->sheet->setCellValue('D16', date("Y年m月d日"));
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {


    }

    /**
     * 设置head
     * @return void
     */
    private function setHeadForHistory()
    {
        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);

        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B2', $this->account_name);

        $this->sheet->setCellValue('A5', '服务项目');
        $this->sheet->setCellValue('B5', '邦信分快捷版');
        $this->sheet->mergeCells('B5:E5');

        $this->sheet->setCellValue('A6', '期间');
        $this->sheet->setCellValue('B6', '区分字段');
        $this->sheet->setCellValue('C6', '单价');
        $this->sheet->setCellValue('D6', '有效查询字段量');
        $this->sheet->setCellValue('E6', '消耗金额');

        $this->sheet->setCellValue(
            'A7',
            $this->list_product_bill_alo['details']['fen']['section_begin'] . '--' . $this->list_product_bill_alo['details']['fen']['section_end']
        );
        $this->sheet->setCellValue('B7', '普通字段');
        $this->sheet->setCellValue('C7', number_format($this->list_product_bill_alo['details']['not_fen']['price'], 2));
        $this->sheet->setCellValue(
            'D7',
            number_format($this->list_product_bill_alo['details']['not_fen']['fee_number'])
        );
        $this->sheet->setCellValue('E7', number_format($this->list_product_bill_alo['details']['not_fen']['money'], 2));


        $this->sheet->mergeCells('A7:A8');
        $this->sheet->setCellValue('B8', '评分字段');
        $this->sheet->setCellValue('C8', number_format($this->list_product_bill_alo['details']['fen']['price'], 2));
        $this->sheet->setCellValue(
            'D8',
            number_format($this->list_product_bill_alo['details']['fen']['fee_number'])
        );
        $this->sheet->setCellValue('E8', number_format($this->list_product_bill_alo['details']['fen']['money'], 2));


        $this->sheet->setCellValue('A9', '合计');
        $this->sheet->setCellValue(
            'D9',
            number_format($this->list_product_bill_alo['section_number']['fee_number'])
        );
        $this->sheet->setCellValue('E9', number_format($this->list_product_bill_alo['money'], 2));
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info, $account_name)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
        $this->account_name = $account_name;
    }
}