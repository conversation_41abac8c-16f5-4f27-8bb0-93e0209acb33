<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/21 0021
 * Time: 13:43
 */

namespace App\Exports\SpecialCustomerExports\C20181101G8K9DA;


use Maatwebsite\Excel\Concerns\{
    RegistersEventListeners, WithTitle, WithEvents
};
use Maatwebsite\Excel\Events\AfterSheet;

class C20181101G8K9DASheet2Export implements WithTitle, WithEvents
{

    use RegistersEventListeners;
    /** @var array 历史账单信息 */
    private static $list_product_bill_alo;

    /** @var array 客户信息 */
    private static $customer_info;

    /** @var object 定制sheet的对象 */
    private static $history_sheet_generate;

    private static $account_name;

    public function __construct(array $customer_info, array $list_product_bill_alo, $account_name)
    {
        static::$list_product_bill_alo = $list_product_bill_alo;
        static::$customer_info = $customer_info;
        static::$history_sheet_generate = new C20181101G8K9DASheetGenerate();
        static::$account_name = $account_name;
    }

    public function title(): string
    {
        return '邦信分快捷版消耗明细';
        // TODO: Implement title() method.
    }

    /**
     * 定制插入单元
     *
     * @param AfterSheet $event
     */
    public static function afterSheet(AfterSheet $event)
    {
        $sheet = $event->sheet;
        static::$history_sheet_generate->generate(
            $sheet,
            static::$list_product_bill_alo,
            static::$customer_info,
            static::$account_name
        );
    }

}