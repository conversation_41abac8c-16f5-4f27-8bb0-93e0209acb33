<?php

namespace App\Exports\SpecialCustomerExports\C20190417PX262B;

use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20190417PX262BSheet1Generate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;
    private $history_list;
    private $row = 9;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info, $history_list)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info, $history_list);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        //设置样式
        $this->setStyle();
    }

    private function setStyle()
    {
        //行高
        for ($i = 1; $i <= $this->row; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:D' . $this->row)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:D' . $this->row)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('B8:D' . ($this->row - 4))
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:D' . ($this->row - 4);
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        list($money_consume_of_this_month, $money_residue_now) = [
            $this->list_product_bill_alo["money_detail_this_month"]["money_consume_of_this_month"],
            $this->list_product_bill_alo["list_aggregate_money"]["money_residue_now"],
        ];

        $this->sheet->setCellValue('A' . $this->row, '合计');
        $this->sheet->setCellValue('B' . $this->row, "");
        $this->sheet->setCellValue('C' . $this->row, $this->history_list['result']['total']['recharge']);
        $this->sheet->setCellValue('D' . $this->row, $this->history_list['result']['total']['consume']);

        $this->row++;

        $this->sheet->setCellValue('A' . $this->row, '剩余金额');
        $this->sheet->setCellValue(
            'B' . $this->row,
            $this->history_list['result']['residue_money']
        ); // 置空字符串 所以不可以使用number_format
        $this->sheet->mergeCells('B' . $this->row . ':D' . $this->row);

        $this->row++;
        $this->row++;

        $this->sheet->setCellValue('D' . $this->row, '北京羽乐创新科技有限公司');
        $this->row++;
        $this->sheet->setCellValue('D' . $this->row, '运营组');
        $this->row++;
        $this->sheet->setCellValue('D' . $this->row, date("Y年m月d日"));
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {
        $this->sheet->setCellValue('A7', '期间');
        $this->sheet->setCellValue('B7', "调用量");
        $this->sheet->setCellValue('C7', "充值金额");
        $this->sheet->setCellValue('D7', "消耗金额");

        $details = $this->list_product_bill_alo["money_detail_this_month"];
        $this->sheet->setCellValue('A8', $this->history_list['result']['bill_month_data']['name']);
        $this->sheet->setCellValue('B8', number_format($details["number"]));
        $this->sheet->setCellValue('C8', $this->history_list['result']['bill_month_data']['recharge']);
        $this->sheet->setCellValue('D8', $this->history_list['result']['bill_month_data']['consume']);

        foreach ($this->history_list['result']['bill_month_special_data'] as $item) {
            $this->sheet->setCellValue('A' . $this->row, $item['name']);
            $this->sheet->setCellValue('B' . $this->row, '--');
            $this->sheet->setCellValue('C' . $this->row, $item['recharge']);
            $this->sheet->setCellValue('D' . $this->row, $item['consume']);

            $this->row++;
        }
    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);
        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B2', $this->customer_info['name']);

        $money_last_before_this_month = $this->list_product_bill_alo["money_last_before_this_month"]["money_residue"] ?? 0;
        $this->sheet->setCellValue('A6', '上月结转余额');
        $this->sheet->setCellValue('B6', number_format($money_last_before_this_month, 2));
        $this->sheet->mergeCells('B6:D6');
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info, array $history_list)
    {
        $this->sheet                 = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info         = $customer_info;
        $this->history_list          = $history_list;
    }
}