<?php

namespace App\Exports\SpecialCustomerExports\C20181101PQ6OVN;

use PhpOffice\PhpSpreadsheet\Shared\Font;

class C20181101PQ6OVNSheet1Generate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置 foot
        $this->setFootForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:E11';
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        $shortcut_total = $this->list_product_bill_alo["xiangDanV2"];
        $this->sheet->setCellValue('A10', '合计');
        $this->sheet->setCellValue('B10', number_format($shortcut_total["money_recharge_sum"], 2));
        $this->sheet->setCellValue('C10', "");
        $this->sheet->setCellValue('D10', "");
        $this->sheet->setCellValue('E10', number_format($shortcut_total["money_consume_sum"], 2));

        $this->sheet->setCellValue('A11', '剩余金额');
        $this->sheet->setCellValue('B11', number_format($shortcut_total["money_last_now"]["money_last_now"], 2)); // 置空字符串 所以不可以使用number_format
        $this->sheet->mergeCells('B11:E11');

        $this->sheet->setCellValue('E15', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('E16', '运营组');
        $this->sheet->setCellValue('E17', date("Y年m月d日"));
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {
        $this->sheet->setCellValue('A8', '期间');
        $this->sheet->setCellValue('B8', "充值金额");
        $this->sheet->setCellValue('C8', "单价");
        $this->sheet->setCellValue('D8', "调取量");
        $this->sheet->setCellValue('E8', "消耗金额");

        $details = $this->list_product_bill_alo["xiangDanV2"]["details"];

        $this->sheet->setCellValue('A9', $details["month_begin"] . "--" . $details["month_end"]);
        $this->sheet->setCellValue('B9', number_format($details["recharge"], 2));
        $this->sheet->setCellValue('C9', $details["price"]);
        $this->sheet->setCellValue('D9', number_format($details["number"]));
        $this->sheet->setCellValue('E9', number_format($details["money"], 2));
    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $this->sheet->setCellValue('A1', '公司名称：');
        $this->sheet->setCellValue('B1', $this->customer_info['company']);
        $this->sheet->setCellValue('A2', '客户名称：');
        $this->sheet->setCellValue('B2', $this->customer_info['name']);

        $this->sheet->setCellValue('A6', '服务项目');
        $this->sheet->setCellValue('B6', "邦信分详单版v2");
        $this->sheet->mergeCells('B6:E6');

        $money_last_before_this_month = $this->list_product_bill_alo["xiangDanV2"]["money_last_before_this_month"] ?? [];
        $this->sheet->setCellValue('A7', '上月结转余额');
        $this->sheet->setCellValue('B7', number_format($money_last_before_this_month["money_last_before_this_month"] ?? 0, 2));
        $this->sheet->mergeCells('B7:E7');
    }

    /**
     * 设置属性
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
    }
}