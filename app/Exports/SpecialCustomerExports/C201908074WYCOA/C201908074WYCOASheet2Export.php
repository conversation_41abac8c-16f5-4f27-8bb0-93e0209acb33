<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/21 0021
 * Time: 13:43
 */

namespace App\Exports\SpecialCustomerExports\C201908074WYCOA;


use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\{
    RegistersEventListeners, WithTitle, WithEvents
};
use Maatwebsite\Excel\Events\AfterSheet;

class C201908074WYCOASheet2Export implements WithTitle, WithEvents
{

    use RegistersEventListeners;
    /** @var array 历史账单信息 */
    private $list_product_bill_alo;

    /** @var array 客户信息 */
    private $customer_info;


    private $account_name;

    public function __construct(array $customer_info, array $list_product_bill_alo, $account_name)
    {
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info         = $customer_info;
        $this->account_name          = $account_name;
    }

    public function title(): string
    {
        return $this->list_product_bill_alo['product_name'] . '-结算单';
        // TODO: Implement title() method.
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $obj = new C201908074WYCOASheetGenerate();
                $obj->generate($event->sheet, $this->list_product_bill_alo, $this->customer_info, $this->account_name);
            }
        ];

    }

}