<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/11/21 0021
 * Time: 13:46
 */

namespace App\Exports\SpecialCustomerExports\C201908074WYCOA;

use App\Define\Common;
use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C201908074WYCOASheetGenerate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;
    private $account_name;
    private $endRow = 10;


    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info, $account_name)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info, $account_name);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        //设置样式
        $this->setStyle();
    }

    private function setStyle()
    {
        //行高
        for ($i = 2; $i <= 10; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:C' . ($this->endRow + 5))
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:C' . ($this->endRow + 5))
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A6:C' . $this->endRow;
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside'  => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        list($money_consume_of_this_month, $money_residue_now) = [
            $this->list_product_bill_alo["money_detail_this_month"]["money_consume_of_this_month"],
            $this->list_product_bill_alo["list_aggregate_money"]["money_residue_now"],
        ];

        $this->sheet->setCellValue('A9', '合计');
        $this->sheet->setCellValue('B9', "");
        $this->sheet->setCellValue('C9', "");
        $this->sheet->setCellValue('D9', number_format($money_consume_of_this_month, 2));

        $this->sheet->setCellValue('A10', '剩余金额');
        $this->sheet->setCellValue('B10', number_format($money_residue_now, 2)); // 置空字符串 所以不可以使用number_format
        $this->sheet->mergeCells('B10:D10');

        $this->sheet->setCellValue('D14', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('D15', '运营组');
        $this->sheet->setCellValue('D16', date("Y年m月d日"));
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {


    }

    /**
     * 设置head
     * @return void
     */
    private function setHeadForHistory()
    {
        $this->sheet->setCellValue('A1', Common::COMPANY_CN_NAME);
        $this->sheet->setCellValue('A2', '北京羽乐创新科技有限公司');

        $this->sheet->setCellValue('A4', '收费通知单');

        $this->sheet->setCellValue('A6', '公司名称');
        $this->sheet->setCellValue('B6', $this->customer_info['company']);
        $this->sheet->mergeCells('B6:C6');

        $this->sheet->setCellValue('A7', '客户名称');
        $this->sheet->setCellValue('B7', $this->customer_info['name']);
        $this->sheet->mergeCells('B7:C7');


        $this->sheet->setCellValue('A8', '上月剩余金额');
        $this->sheet->setCellValue('B8', $this->list_product_bill_alo['details']['bill_month_prev_month_money']);
        $this->sheet->mergeCells('B8:C8');

        $this->sheet->setCellValue('A9', '期间');
        $this->sheet->setCellValue('B9', '充值金额');
        $this->sheet->setCellValue('C9', '消耗金额');

        $this->sheet->setCellValue(
            'A10',
            $this->list_product_bill_alo['details']['section_begin'] . ' -- ' . $this->list_product_bill_alo['details']['section_end']
        );
        $this->sheet->setCellValue('B10', $this->list_product_bill_alo['details']['recharge']);
        $this->sheet->setCellValue('C10', $this->list_product_bill_alo['details']['consume']);

        $row = 11;
        foreach ($this->list_product_bill_alo['details']['special'] as $item) {
            $this->sheet->setCellValue(
                'A' . $row,
                '「' . $item['month'] . '」 ' . $item['name']
            );
            $this->sheet->setCellValue('B' . $row, $item['recharge']);
            $this->sheet->setCellValue('C' . $row, $item['consume']);

            $row++;
        }

        $this->sheet->setCellValue('A' . $row, '合计');
        $this->sheet->setCellValue('B' . $row, $this->list_product_bill_alo['details']['total_recharge']);
        $this->sheet->setCellValue('C' . $row, $this->list_product_bill_alo['details']['total_consume']);
        $row++;

        $this->sheet->setCellValue('A' . $row, '剩余金额');
        $this->sheet->setCellValue('B' . $row, '0.00');
        $this->sheet->mergeCells('B' . $row . ':C' . $row);
        $this->endRow = $row;


        $row += 3;
        $this->sheet->setCellValue('C' . $row, '北京羽乐创新有限公司');
        $row++;
        $this->sheet->setCellValue('C' . $row, '运营组');
        $row++;
        $this->sheet->setCellValue('C' . $row, date('Y年m月d日'));
    }

    /**
     * 设置属性
     *
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info, $account_name)
    {
        $this->sheet                 = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info         = $customer_info;
        $this->account_name          = $account_name;
    }
}