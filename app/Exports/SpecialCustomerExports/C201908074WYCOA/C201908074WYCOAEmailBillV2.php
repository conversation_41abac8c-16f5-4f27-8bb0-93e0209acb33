<?php

namespace App\Exports\SpecialCustomerExports\C201908074WYCOA;

use App\Exports\HistoryV2BillExport;
use App\Exports\ProductAlgoV2Export;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class C201908074WYCOAEmailBillV2 implements WithMultipleSheets
{
    use Exportable;
    /** @var array 客户信息 */
    private $customer_info;

    /** @var array 结算单信息 */
    private $history;

    /** @var array 各个产品的算法账单信息 */
    private $list_product_bill_alo;

    /**
     * EmailBill constructor.
     *
     * @param array $customer_info
     * @param array $history
     * @param array $list_product_bill_alo
     */
    public function __construct(array $customer_info, array $history, array $list_product_bill_alo)
    {
        $this->history               = $history;
        $this->customer_info         = $customer_info;
        $this->list_product_bill_alo = $list_product_bill_alo;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        // sheet1 结算单
        $sheet = [];

        return array_reduce(
            $this->list_product_bill_alo,
            function ($carry, $item_account) {
                $account_name = $item_account['account_name'];
                return array_reduce(
                    $item_account["list_bills"],
                    function ($carry_section, $item_bill_section) use ($account_name) {
                        if ($item_bill_section['product_alo'] != 'product_bill') {
                            $carry_section[] = new ProductAlgoV2Export(
                                $item_bill_section, $this->customer_info, $account_name
                            );
                        } else {

                            $carry_section[] = new C201908074WYCOASheet2Export(
                                $this->customer_info, $item_bill_section, $account_name
                            );
                        }
                        return $carry_section;
                    },
                    $carry
                );
            },
            $sheet
        );

    }
}