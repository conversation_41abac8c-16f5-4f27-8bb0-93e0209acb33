<?php

namespace App\Exports\SpecialCustomerExports\C20181101LHVREG;

use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class C20181101LHVREGSheet1Generate
{
    private $sheet;
    private $list_product_bill_alo;
    private $customer_info;

    /** @var int 展示的月份的个数 */
    private $loop_times;

    /** @var int 开始循环的行数 */
    private $line_begin_loop = 4;

    /**
     * @return mixed
     */
    public function getLoopTimes()
    {
        return $this->loop_times;
    }

    /**
     * 设置月份的次数
     * @param array $money_detail_this_month
     */
    public function setLoopTimes(array $money_detail_this_month)
    {
        $this->loop_times = count($money_detail_this_month);
    }

    /**
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    public function generate($sheet, $list_product_bill_alo, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $list_product_bill_alo, $customer_info);

        // 设置循环的测试
        $this->setLoopTimes($list_product_bill_alo["money_detail_this_month"]);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $this->setBodyForHistory();

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder();

        //设置样式
        $this->setStyle();
    }

    private function setStyle()
    {
        $endRow = $this->getLoopTimes() + 3;
        $this->sheet->getRowDimension('1')
            ->setRowHeight('36');
        //行高
        for ($i = 2; $i <= $endRow; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:K' . $endRow)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:K' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('F3:K' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('K2')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('C3')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A1:K' . ($this->getLoopTimes() + 3);
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     */
    private function setFootForHistory()
    {
        list($money_consume_of_this_month, $money_residue_now) = [
            $this->list_product_bill_alo["money_detail_this_month"]["money_consume_of_this_month"],
            $this->list_product_bill_alo["list_aggregate_money"]["money_residue_now"],
        ];

        $this->sheet->setCellValue('A9', '合计');
        $this->sheet->setCellValue('B9', "");
        $this->sheet->setCellValue('C9', "");
        $this->sheet->setCellValue('D9', number_format($money_consume_of_this_month, 2));

        $this->sheet->setCellValue('A10', '剩余金额');
        $this->sheet->setCellValue('B10', number_format($money_residue_now, 2)); // 置空字符串 所以不可以使用number_format
        $this->sheet->mergeCells('B10:D10');

        $this->sheet->setCellValue('D14', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('D15', '运营组');
        $this->sheet->setCellValue('D16', date("Y年m月d日"));
    }

    /**
     * 设置 body
     */
    private function setBodyForHistory()
    {
        $money_detail_this_month = $this->list_product_bill_alo["money_detail_this_month"] ?? [];
        array_walk($money_detail_this_month, function($item){
            $this->sheet->setCellValue('D' . $this->line_begin_loop, $item["month_begin"] . "--" . $item["month_end"]);
            $this->sheet->setCellValue('E' . $this->line_begin_loop, $item["product_name"]);
            $this->sheet->setCellValue('F' . $this->line_begin_loop, number_format($item["section_invoked_number"]));
            $this->sheet->setCellValue('G' . $this->line_begin_loop, $item["price"]);
            $this->sheet->setCellValue('H' . $this->line_begin_loop, number_format($item["money_consume"], 2));
            $this->sheet->setCellValue('I' . $this->line_begin_loop, number_format($item["money_consume"], 2));
            $this->sheet->setCellValue('J' . $this->line_begin_loop, number_format($item["money_recharge"], 2));
            $this->sheet->setCellValue('K' . $this->line_begin_loop, number_format($item["money_last_now"], 2));

            $this->line_begin_loop ++;
        });

    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $base_info = $this->list_product_bill_alo["base_info"] ?? [];
        $this->sheet->setCellValue('A1', $base_info["customer_name"] ?? "");
        $this->sheet->mergeCells('A1:K1');

        // 序号
        $this->sheet->setCellValue('A2', '序号');
        $this->sheet->setCellValue('A3', 1);
        $this->sheet->mergeCells('A3:A' . ($this->getLoopTimes() + 3));

        // 公司名称
        $this->sheet->setCellValue('B2', '公司名称');
        $this->sheet->setCellValue('B3', $base_info["company"] ?? "");
        $this->sheet->mergeCells('B3:B' . ($this->getLoopTimes() + 3));

        // 目前总余额
        $this->sheet->setCellValue("C2", "目前总余额(元)");
        $this->sheet->setCellValue("C3", number_format($base_info["money_last_now"] ?? 0, 2));
        $this->sheet->mergeCells('C3:C' . ($this->getLoopTimes() + 3));

        // 时间
        $money_last_before_this_year = $this->list_product_bill_alo["money_last_before_this_year"] ?? [];
        $this->sheet->setCellValue("D2", "时间");
        $this->sheet->setCellValue("D3", $money_last_before_this_year["title"] ?? "");

        // 产品
        $this->sheet->setCellValue("E2", "产品");
        $this->sheet->mergeCells('E3:J3');

        // 计费次数 && 其他
        $this->sheet->setCellValue("F2", "计费次数");
        $this->sheet->setCellValue("G2", "单价(元)");
        $this->sheet->setCellValue("H2", "消耗金额(元)");
        $this->sheet->setCellValue("I2", "合计(元)");
        $this->sheet->setCellValue("J2", "本期充值(元)");

        // 余额
        $this->sheet->setCellValue("K2", "余额(元)");
        $this->sheet->setCellValue("K3", number_format($money_last_before_this_year["money_residue"] ?? 0, 2));
    }

    /**
     * 设置属性
     * @param $sheet
     * @param $list_product_bill_alo
     * @param $customer_info
     */
    private function setAttr($sheet, array $list_product_bill_alo, array $customer_info)
    {
        $this->sheet = $sheet;
        $this->list_product_bill_alo = $list_product_bill_alo;
        $this->customer_info = $customer_info;
    }
}