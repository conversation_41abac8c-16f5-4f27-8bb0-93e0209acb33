<?php

namespace App\Exports\SpecialCustomerExports\C20181101LHVREG;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class C20181101LHVREGEmailBillV2 implements WithMultipleSheets
{
    use Exportable;
    /** @var array 客户信息 */
    private $customer_info;

    /** @var array 结算单信息 */
    private $history;

    /** @var array 各个产品的算法账单信息 */
    private $list_product_bill_alo;

    /**
     * EmailBill constructor.
     * @param array $customer_info
     * @param array $history
     * @param array $list_product_bill_alo
     */
    public function __construct(array $customer_info, array $history, array $list_product_bill_alo)
    {
        $this->history = $history;
        $this->customer_info = $customer_info;
        $this->list_product_bill_alo = $list_product_bill_alo;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        // sheet1 结算单-邦信分快捷版
        $sheet[] = new C20181101LHVREGSheet1Export($this->customer_info, $this->list_product_bill_alo);

        return $sheet;
    }
}