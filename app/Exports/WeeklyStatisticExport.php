<?php

namespace App\Exports;

use App\Models\WeeklyStatisticData;
use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithStrictNullComparison
};

class WeeklyStatisticExport implements FromArray, ShouldAutoSize, WithStrictNullComparison
{
    private $list;

    public function __construct(array $list,$type) {
        $fmt_list = [];
        if($type == WeeklyStatisticData::TASK_TYPE_REMIT_LAST_WEEK) {
            $fmt_info   = [
                '回款时间',
                '公司名称',
                '客户',
                '回款金额',
                '区域',
                '产品',
                '商务',
            ];
            $fmt_list[] = $fmt_info;
            foreach ($list as $info) {
                $fmt_info   = [
                    $info['remit_date'],
                    $info['company_name'],
                    $info['customer_name'],
                    $info['money'],
                    $info['dept'],
                    $info['product_name'],
                    $info['salesman'],
                ];
                $fmt_list[] = $fmt_info;
            }
        }elseif($type == WeeklyStatisticData::TASK_TYPE_PRODUCT || $type == WeeklyStatisticData::TASK_TYPE_CUSTOMER_LAST_WEEK || $type == WeeklyStatisticData::TASK_TYPE_PRODUCT_LAST_WEEK){
            foreach ($list as $info) {
                unset($info['count']);
                $fmt_list[] = $info;
            }
        }else{
            foreach ($list as $name => $info){
                $fmt_info = [];
                $fmt_info[] = $name;
                foreach($info as $money){
                    $fmt_info[] = $money;
                }
                $fmt_list[] = $fmt_info;
            }
        }

        $this->list = $fmt_list;
    }

    public function array(): array {
        return $this->list;
    }
}