<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithHeadings, WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class UpstreamVerificationDetailsExport implements  From<PERSON>rray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list;

    public function __construct(array $list)
    {
        Font::setTrueTypeFontPath(storage_path() .'/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->list = array_reduce($list, function ($carry, $item) {
            $carry[] = [
                $item['day'] ?? ' ',
                number_format($item['success']),
                number_format($item['yd_succ']),
                number_format($item['lt_succ']),
                number_format($item['dx_succ']),
            ];
            return $carry;
        }, []);
    }

    public function array(): array
    {
        return $this->list;
    }

    public function headings(): array
    {
        return [
            '日期',
            '有效调用量',
            '移动有效调用量',
            '联通有效调用量',
            '电信有效调用量',
        ];
    }

}