<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    RegistersEventListeners,
    WithTitle,
    WithEvents
};
use Maatwebsite\Excel\Events\AfterSheet;

class HistoryBillExport implements WithTitle, WithEvents
{
    use RegistersEventListeners;
    /** @var array 历史账单信息 */
    private static $history;

    /** @var array 客户信息 */
    private static $customer_info;

    /** @var object 定制sheet的对象 */
    private static $history_sheet_generate;

    /**
     * HistoryBillExport constructor.
     * @param array $customer_info
     * @param array $history
     */
    public function __construct(array $customer_info, array $history)
    {
        static::$history = $history;
        static::$customer_info = $customer_info;
        static::$history_sheet_generate = new HistorySheetGenerate();
    }

    /**
     * sheet
     * @return string
     */
    public function title(): string
    {
        return '结算单';
    }

    /**
     * 定制插入单元
     * @param AfterSheet $event
     */
    public static function afterSheet(AfterSheet $event)
    {
        $sheet = $event->sheet;
        static::$history_sheet_generate->generate($sheet, static::$history, static::$customer_info);
    }

}