<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maat<PERSON>bsite\Excel\Concerns\{
    FromCollection,
    ShouldAutoSize,
    WithHeadings,
    WithStrictNullComparison,
    WithEvents
};

use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class MixDataExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStrictNullComparison, WithEvents
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function collection()
    {
        // 平铺数据，生成表格行
        $rows = [];
        foreach ($this->data as $item) {
            $channelCount = count($item['channels'] ?? []);
            // 主行数据
            $mainRow = [
                $item['customer_name'],
                $item['father_name'],
                $item['product_id'],
                $item['product_name'],
                $item['call_number'],
                $item['success_number'],
                $item['success_rate'],
                $item['cache_number'],
                $item['uncache_number'],
                $item['valid_number'],
                $item['valid_rate'],
                $item['charge_number'],
                $item['charge_rate'],
                $item['income_money'],
                $item['cost_money'],
                $item['profit_money'],
                $item['profit_rate'],
                $item['channels'][0]['channel_name'] ?? '',
                $item['channels'][0]['channel_code'] ?? '',
                $item['channels'][0]['cache_number'] ?? '',
                $item['channels'][0]['call_number'] ?? '',
                $item['channels'][0]['success_number'] ?? '',
                $item['channels'][0]['success_rate'] ?? '',
                $item['channels'][0]['valid_number'] ?? '',
                $item['channels'][0]['valid_rate'] ?? '',
                $item['channels'][0]['charge_number'] ?? '',
                $item['channels'][0]['cost_money'] ?? '',
                $item['channels'][0]['cost_rate'] ?? '',
            ];

            $rows[] = $mainRow;
            for ($i = 1; $i < $channelCount; $i++) {
                $rows[] = [
                    '', '', '', '', '', '','','','','','','','','','','','', // 前17列留空
                    $item['channels'][$i]['channel_name'] ?? '',
                    $item['channels'][$i]['channel_code'] ?? '',
                    $item['channels'][$i]['cache_number'] ?? '',
                    $item['channels'][$i]['call_number'] ?? '',
                    $item['channels'][$i]['success_number'] ?? '',
                    $item['channels'][$i]['success_rate'] ?? '',
                    $item['channels'][$i]['valid_number'] ?? '',
                    $item['channels'][$i]['valid_rate'] ?? '',
                    $item['channels'][$i]['charge_number'] ?? '',
                    $item['channels'][$i]['cost_money'] ?? '',
                    $item['channels'][$i]['cost_rate'] ?? '',
                ];
            }
        }

        return collect($rows);
    }

    public function headings(): array
    {
        return [
            '客户',
            '主产品',
            '子产品编码',
            '子产品名称',
            '调用量',
            '成功量',
            '成功率',
            '命中缓存量',
            '非缓存量',
            '查得量',
            '查得率',
            '计费量',
            '计费率',
            '总收入',
            '总成本',
            '毛利',
            '毛利率',
            '渠道',
            '产品编码',
            '渠道命中缓存量',
            '渠道查询量',
            '渠道成功量',
            '渠道成功率',
            '渠道查得量',
            '渠道查得率',
            '渠道计费量',
            '渠道成本',
            '渠道成本占比',
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // 前17列样式
                $sheet->getStyle('A1:Q1')->applyFromArray([
                    'font' => ['bold' => true],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['argb' => 'FFf0f9eb']
                    ]
                ]);

                // 后11列样式
                $sheet->getStyle('R1:AB1')->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['argb' => 'FFfdf6ec']
                    ]
                ]);

                // 合并单元格
                $row = 2;
                foreach ($this->data as $item) {
                    $channelCount = count($item['channels'] ?? 1);
                    if ($channelCount > 1) {
                        for ($col = 'A'; $col <= 'Q'; $col++) {
                            $sheet->mergeCells($col.$row.':'.$col.($row + $channelCount - 1));
                        }
                    }
                    $row += $channelCount;
                }
            }
        ];
    }
}