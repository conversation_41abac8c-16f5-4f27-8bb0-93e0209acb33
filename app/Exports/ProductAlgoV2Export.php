<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    WithEvents, WithTitle
};
use Maatwebsite\Excel\Events\AfterSheet;

class ProductAlgoV2Export implements WithTitle, WithEvents
{
    /** @var array 产品下辖的账单算法组合 */
    private $list_product_algos;

    /** @var array 客户信息 */
    private $customer_info;

    /** @var string */
    private $account_name;

    public function __construct(array $list_product_algos, array $customer_info, string $account_name)
    {
        $this->list_product_algos = $list_product_algos;
        $this->customer_info      = $customer_info;
        $this->account_name       = $account_name;
    }

    /**
     * @return string
     */
    public function title(): string
    {
        $section_source = $this->list_product_algos["section_source"] ?? [];
        $bill_cid       = $section_source["id"] ?? "";
        $bill_cid_str   = $bill_cid ? "_" . $bill_cid . "_" : "";

        $title = $this->account_name . "_" . $this->list_product_algos["product_name"] . $bill_cid_str . "消耗明细";

        if (mb_strlen($title, 'UTF8')<30) {
            return $title;
        }
        return mb_substr($this->account_name, 0, 7, 'UTF8') . "_" . $this->list_product_algos["product_name"] . $bill_cid_str . "消耗明细";
    }

    public function registerEvents(): array
    {

        return [
            AfterSheet::class => function (AfterSheet $event) {
                (new ProductAlgoV2Generate($event->sheet, $this->list_product_algos, $this->customer_info))->generate();
            }
        ];

    }
}