<?php

namespace App\Exports;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class EmailBill implements WithMultipleSheets
{
    use Exportable;
    /** @var array 客户信息 */
    private $customer_info;

    /** @var array 结算单信息 */
    private $history;

    /** @var array 各个产品的算法账单信息 */
    private $list_product_bill_alo;

    /**
     * EmailBill constructor.
     * @param array $customer_info
     * @param array $history
     * @param array $list_product_bill_alo
     */
    public function __construct(array $customer_info, array $history, array $list_product_bill_alo)
    {
        $this->history = $history;
        $this->customer_info = $customer_info;
        $this->list_product_bill_alo = $list_product_bill_alo;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheet[] = new HistoryBillExport($this->customer_info, $this->history);
        array_walk($this->list_product_bill_alo, function ($list_product_algos, $product_id) use (&$sheet) {
            $product_info = Product::findOneItem(compact('product_id'))->toArray();
            $sheet[] = new ProductAlgoExport($product_info, $list_product_algos, $this->customer_info);
        });

        return $sheet;
    }
}