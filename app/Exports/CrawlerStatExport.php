<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithHeadings, WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class CrawlerStatExport implements From<PERSON>rray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list_bills;

    public function __construct(array $list_bills)
    {
        Font::setTrueTypeFontPath(storage_path() .'/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);

        $this->list_bills = array_reduce($list_bills, function ($carry, $item) {
            $carry[] = [
                $item['customer_id'] ?? ' ',
                $item['customer_name'] ?? ' ',
                $item['start_time'] ?? ' ',
                $item['end_time'] ?? ' ',
                $item['authen_nums'] ?? ' ',
                $item['crawl_nums'] ?? ' ',
                $item['report_nums'] ?? ' ',
            ];
            return $carry;
        }, []);
    }

    public function array(): array
    {
        return $this->list_bills;
    }

    public function headings(): array
    {
        return [
            '客户ID',
            '客户名称',
            "开始时间",
            "结束时间",
            '授权成功量',
            "爬取成功量",
            "报告生成量",
        ];
    }
}