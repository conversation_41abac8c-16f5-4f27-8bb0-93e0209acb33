<?php

namespace App\Exports;

use PhpOffice\PhpSpreadsheet\Shared\Font;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class ProductAlgoV2Generate
{
    /** @var array 产品ID */
    private $product_info;

    /** @var array 产品下辖的账单算法组合 */
    private $list_product_algos;

    /** @var array 客户信息 */
    private $customer_info;

    /** @var \Maatwebsite\Excel\Sheet 正在操作的sheet */
    private $sheet;

    /** @var int 正在操作的行,初始化为5 */
    private $line = 5;

    /** @var int  算法需要扩展的最大列 */
    private $max_cell = 4;

    /** @var int 金额合计 */
    private $money_sum = 0;

    /** @var array 可能用到的终结性cell */
    private $list_cells = [
        3 => 'C',
        4 => 'D',
        5 => 'E',
        6 => 'F'
    ];

    /** @var bool 是否是催收快捷版 */
    private $_determine_is_shortcut = false;

    /**
     * ProductAlgoGenerate constructor.
     *
     * @param       $sheet
     * @param array $list_product_algos
     * @param array $customer_info
     */
    public function __construct($sheet, array $list_product_algos, array $customer_info)
    {
        if (!array_key_exists('product_id', $list_product_algos)) {
            throw new \Exception('LIST_PRODUCT_ALGOS中不存在的PRODUCT_ID参数');
        }

        $this->customer_info          = $customer_info;
        $this->list_product_algos     = $list_product_algos;
        $this->sheet                  = $sheet;
        $this->_determine_is_shortcut = $list_product_algos['product_id'] == 210;
    }

    /**
     * 生成excel
     */
    public function generate()
    {
        // 生成head
        $this->setHead();

        //  生成body
        $this->setBody();

        // 生成foot
        $this->setFoot();

        // 自适应 && 边框
        $this->setAutoSizeAndBorder();
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSizeAndBorder()
    {
        // 宽度
        $this->autoFitColumnWidthToContent();

        // 边框
        $this->setBorder();
    }

    /**
     * 设置边框
     */
    private function setBorder()
    {
        $range_cell = 'A5:' . $this->list_cells[$this->max_cell] . $this->line;
        $this->sheet->styleCells($range_cell, [
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
                'inside'  => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ]
        ]);
    }

    /**
     * 设置自动宽度
     */
    private function autoFitColumnWidthToContent()
    {
        Font::setTrueTypeFontPath(storage_path() . '/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 生成foot
     */
    private function setFoot()
    {
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, '合计');
        $this->sheet->setCellValue('B' . $this->line, number_format($this->money_sum, 2));
        $cell_end = $this->list_cells[$this->max_cell];
        $this->sheet->mergeCells('B' . $this->line . ':' . $cell_end . $this->line);
    }

    /**
     * 生成body
     */
    private function setBody()
    {
        // 各种算法场景下的填充
        $this->setAlgoContent();

        //  设置常量
        $this->setConstant();
    }

    /**
     * 各种算法场景下的填充
     */
    private function setAlgoContent()
    {
        //['_2_1', "_2_2", "_2_1_2", "_2_3"]
        $allow_alo = ['_1', '_2_1', '_2_2', '_2_3', '_2_3_2', '_2_2_2', '_1_2', '_2_1_2'];
        if (!in_array($this->list_product_algos['product_alo'], $allow_alo)) {
            throw new \Exception('不支持的PRODUCT_ALO：' . $this->list_product_algos['product_alo']);
        }

        switch ($this->list_product_algos["product_alo"]) {
            case '_1':
                // 通用按时间
                $this->setContentWhenCommonDate();
                break;
            case '_2_1':
                // 如果通用的按用量 && 固定价格
                $this->setContentWhenCommonFixed();
                break;
            case '_2_2':
                // 通用的按用量 && 累进阶梯
                $this->setContentWhenCommonProgression();
                break;
            case '_2_3':
                // 通用 && 达到阶梯
                $this->setContentWhenCommonReach();
                break;
            case '_2_3_2':
                // 区分运营商按时间  区分运营商按用量 && 累进阶梯 区分运营商按用量 && 到达阶梯
                $this->setContentWhenOperatorCommon();
                break;
            case '_2_2_2':
                // 区分运营商按时间  区分运营商按用量 && 累进阶梯 区分运营商按用量 && 到达阶梯
                $this->setContentWhenOperatorCommon();
                break;
            case '_1_2':
                // 区分运营商按时间  区分运营商按用量 && 累进阶梯 区分运营商按用量 && 到达阶梯
                $this->setContentWhenOperatorCommon();
                break;
            case '_2_1_2':
                // 区分运营商按用量 && 固定价格
                $this->setContentWhenOperatorFixed();
                break;
        }
    }

    /**
     * 区分运营商按用量 && 固定价格
     */
    private function setContentWhenOperatorFixed()
    {
        // 设置head
        $this->setHeadWhenOperatorFixed();

        // 设置body
        $this->setBodyWhenOperatorFixed();

        //设置样式
        $this->setStyleWhenOperatorFixed();
    }

    private function setStyleWhenOperatorFixed()
    {
        $endCol = $this->_determine_is_shortcut ? 'F' : 'E';
        //行高
        for ($i = 1; $i <= 10; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:' . $endCol . '10')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:' . $endCol . '10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('C7:' . $endCol . '9')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('B10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置body
     */
    private function setBodyWhenOperatorFixed()
    {

        $date_item = $this->list_product_algos['details'];
        $i         = 0;
        $rowspan   = count($date_item['list_distribute']);
        array_walk($date_item['list_distribute'], function ($distribute) use (&$i, $rowspan, $date_item) {
            $this->line++;
            if ($i === 0) {
                $this->sheet->setCellValue('A' . $this->line,
                    $date_item['section_begin'] . '--' . $date_item['section_end']);
                $this->sheet->mergeCells('A' . $this->line . ':A' . ($this->line + $rowspan - 1));
            }
            $this->sheet->setCellValue('B' . $this->line, $distribute['operator']);
            $this->sheet->setCellValue('C' . $this->line, $distribute['price']);
            $this->sheet->setCellValue('D' . $this->line, number_format($distribute['number']));
            if ($this->_determine_is_shortcut) {
                $this->sheet->setCellValue('E' . $this->line, number_format($distribute['shortcut_number']));
                $this->sheet->setCellValue('F' . $this->line, number_format($distribute['money'], 2));
            } else {
                $this->sheet->setCellValue('E' . $this->line, number_format($distribute['money'], 2));
            }

            $this->money_sum += $distribute['money'] ?? 0;
            $i++;
        });
    }

    /**
     * 设置head
     */
    private function setHeadWhenOperatorFixed()
    {
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, '期间');
        $this->sheet->setCellValue('B' . $this->line, '运营商');
        $this->sheet->setCellValue('C' . $this->line, '单价');
        if ($this->_determine_is_shortcut) {
            $this->sheet->setCellValue('D' . $this->line, '有效查询字段量');
            $this->sheet->setCellValue('E' . $this->line, '有效调用量');
            $this->sheet->setCellValue('F' . $this->line, '消耗金额');
            $this->setMaxCell(6);
        } else {
            $this->sheet->setCellValue('D' . $this->line, '有效调用量');
            $this->sheet->setCellValue('E' . $this->line, '消耗金额');
            $this->setMaxCell(5);
        }
    }

    /**
     * 运营商中通用的部分
     */
    private function setContentWhenOperatorCommon()
    {
        // 设置head
        $this->setHeadWhenOperatorCommon();

        // 设置body
        $this->setBodyWhenOperatorCommon();

        //设置样式
        $this->setStyleWhenOperatorCommon();
    }

    private function setStyleWhenOperatorCommon()
    {
        $endCol = $this->_determine_is_shortcut ? 'E' : 'D';

        //行高
        for ($i = 1; $i <= 10; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:' . $endCol . '10')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:' . $endCol . '10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('C7:' . $endCol . '9')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('B10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置body
     */
    private function setBodyWhenOperatorCommon()
    {
        $date_item = $this->list_product_algos["details"];

        $i       = 0;
        $rowspan = count($date_item['list_distribute']);
        array_walk($date_item['list_distribute'], function ($distribute) use (&$i, $rowspan, $date_item) {
            $this->line++;
            if ($i === 0) {
                $this->sheet->setCellValue('A' . $this->line,
                    $date_item['section_begin'] . '--' . $date_item['section_end']);
                if ($rowspan >= 2) {
                    $this->sheet->mergeCells('A' . $this->line . ':A' . ($this->line + $rowspan - 1));
                }
            }

            $this->sheet->setCellValue('B' . $this->line, $distribute['operator']);
            $this->sheet->setCellValue('C' . $this->line, number_format($distribute['number']));
            if ($this->_determine_is_shortcut) {
                $this->sheet->setCellValue('D' . $this->line, number_format($distribute['shortcut_number']));
                $this->sheet->setCellValue('E' . $this->line, number_format($distribute['money'], 2));
            } else {
                $this->sheet->setCellValue('D' . $this->line, number_format($distribute['money'], 2));
            }

            $this->money_sum += $distribute['money'] ?? 0;
            $i++;
        });

    }

    /**
     * 设置head
     */
    private function setHeadWhenOperatorCommon()
    {
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, '期间');
        $this->sheet->setCellValue('B' . $this->line, '运营商');

        if ($this->_determine_is_shortcut) {
            $this->sheet->setCellValue('C' . $this->line, '有效查询字段量');
            $this->sheet->setCellValue('D' . $this->line, '有效调用量');
            $this->sheet->setCellValue('E' . $this->line, '消耗金额');
            $this->setMaxCell(5);
        } else {
            $this->sheet->setCellValue('C' . $this->line, '有效调用量');
            $this->sheet->setCellValue('D' . $this->line, '消耗金额');
            $this->setMaxCell(4);
        }

    }

    /**
     * 通用的按用量 && 达到阶梯
     */
    private function setContentWhenCommonReach()
    {
        // 设置head
        $this->setHeadWhenCommonReach();

        // 设置body
        $this->setBodyWhenCommonReach();

        //设置样式
        $this->setStyleWhenCommonReach();
    }

    private function setStyleWhenCommonReach()
    {
        $date_item = $this->list_product_algos["details"];
        $i         = 0;
        $rowspan   = count($date_item['list_distribute']);
        $endRow    = $this->line + $rowspan;

        //行高
        for ($i = 1; $i <= $endRow; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:E' . $endRow)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:E' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('D7:E' . ($endRow - 1))
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('B' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置body
     */
    private function setBodyWhenCommonReach()
    {
        $date_item = $this->list_product_algos["details"];
        $i         = 0;
        $rowspan   = count($date_item['list_distribute']);
        array_walk($date_item['list_distribute'], function ($distribute) use (&$i, $rowspan, $date_item) {
            $this->line++;
            if ($i === 0) {
                $this->sheet->setCellValue('A' . $this->line,
                    $date_item['section_begin'] . '--' . $date_item['section_end']);
                if ($rowspan >= 2) {
                    $this->sheet->mergeCells('A' . $this->line . ':A' . ($this->line + $rowspan - 1));
                }
            }

            $this->sheet->setCellValue('B' . $this->line, $distribute['price']);
            $this->sheet->setCellValue('C' . $this->line, number_format($distribute['section_key']));
            $this->sheet->setCellValue('D' . $this->line, number_format($distribute['section_number']));
            $this->sheet->setCellValue('E' . $this->line, number_format($distribute['money'], 2));

            $this->money_sum += $distribute['money'] ?? 0;
            $i++;
        });
    }

    /**
     * 设置head
     */
    private function setHeadWhenCommonReach()
    {
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, '期间');
        $this->sheet->setCellValue('B' . $this->line, '单价');
        $this->sheet->setCellValue('C' . $this->line, '到达用量档次');
        $this->sheet->setCellValue('D' . $this->line, '有效调用量');
        $this->sheet->setCellValue('E' . $this->line, '消耗金额');

        $this->setMaxCell(5);
    }


    /**
     * 通用的按用量 && 累进阶梯
     */
    private function setContentWhenCommonProgression()
    {
        // 设置head
        $this->setHeadWhenCommonProgression();

        // 设置body
        $this->setBodyWhenCommonProgression();

        //设置样式
        $this->setStyleWhenCommonProgression();
    }

    private function setStyleWhenCommonProgression()
    {
        // 总行数
        $date_item       = $this->list_product_algos['details'];
        $list_distribute = $date_item['list_distribute'];
        $rowspan         = $this->_numberSection($list_distribute);

        $endRow = $this->line + $rowspan - 2;
        //行高
        for ($i = 1; $i <= $endRow; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:E' . $endRow)
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:E' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('C7:E' . ($endRow - 1))
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $this->sheet->getStyle('B' . $endRow)
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置body
     */
    private function setBodyWhenCommonProgression()
    {

        $date_item = $this->list_product_algos['details'];
        // 抛出阶梯调用量为0的部分
        $list_distribute = $date_item['list_distribute'];

        // 总行数
        $rowspan = $this->_numberSection($list_distribute);

        // 展示的一个section_key
        $first_section_key = $this->_getFirstSectionKey($list_distribute);

        // 生成excel
        array_walk($list_distribute,
            function ($distribute, $section_key) use ($rowspan, $date_item, $list_distribute, $first_section_key) {

                // 是否需要跳过（调用量为0）
                if (!$this->_determineShowItem($list_distribute, $distribute)) {
                    return;
                }

                $this->line++;
                if ($first_section_key == $section_key) {
                    $this->sheet->setCellValue('A' . $this->line,
                        $date_item['section_begin'] . '--' . $date_item['section_end']);
                    if ($rowspan >= 3) {
                        $this->sheet->mergeCells('A' . $this->line . ':A' . ($this->line + $rowspan - 2));
                    }
                }

                $this->sheet->setCellValue('B' . $this->line, $distribute['section_key']);
                $this->sheet->setCellValue('C' . $this->line, $distribute['price']);
                $this->sheet->setCellValue('D' . $this->line, number_format($distribute['section_number']));
                $this->sheet->setCellValue('E' . $this->line, number_format($distribute['money'], 2));

                $this->money_sum += $distribute['money'] ?? 0;
            });
    }

    /**
     * 是否按照第一个来展示
     *
     * @param array $list_distribute
     *
     * @return bool
     */
    private function _getFirstSectionKey(array $list_distribute): bool
    {
        // 如果不存在section_number > 0的情况  则展示第一条
        $first_section_key = '';
        collect($list_distribute)->each(function ($distribute, $section_key) use (&$first_section_key) {
            if ($distribute['section_number'] > 0) {
                $first_section_key = $section_key;
                return false;
            }
        });

        return $first_section_key ?? array_keys($list_distribute)[0];
    }

    /**
     * 当前部分是否展示
     *
     * @param array $list_distribute
     * @param array $distribute
     *
     * @return bool
     */
    private function _determineShowItem(array $list_distribute, array $distribute): bool
    {
        // 如果总条数和允许显示的条数是一致的 那么一定是显示的
        $length  = $this->_numberSection($list_distribute);
        $counter = count($list_distribute);
        if ($length == $counter) {
            return true;
        }

        return $distribute['section_number'] > 0;
    }

    /**
     * 需要展示的单元数
     *
     * @param array $list_distribute
     *
     * @return int
     */
    private function _numberSection(array $list_distribute): int
    {
        $liner = array_reduce($list_distribute, function ($carry, $distribute) {
            if ($distribute['section_number'] > 0) {
                $carry++;
            }
            return $carry;
        }, 0);

        return $liner ? $liner : 1;
    }

    /**
     * 设置head
     */
    private function setHeadWhenCommonProgression()
    {
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, '期间');
        $this->sheet->setCellValue('B' . $this->line, '区间');
        $this->sheet->setCellValue('C' . $this->line, '单价');
        $this->sheet->setCellValue('D' . $this->line, '有效调用量');
        $this->sheet->setCellValue('E' . $this->line, '消耗金额');

        $this->setMaxCell(5);
    }

    /**
     * 如果通用的按用量 && 固定价格
     */
    private function setContentWhenCommonFixed()
    {
        // 设置head
        $this->setHeadWhenCommonFixed();

        // 设置body
        $this->setBodyWhenCommonFixed();

        // 设置样式
        $this->setStyleWhenCommonFixed();
    }

    private function setStyleWhenCommonFixed()
    {
        $endCol = $this->_determine_is_shortcut ? 'E' : 'D';
        //行高
        for ($i = 1; $i <= 8; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:' . $endCol . '8')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:' . $endCol . '8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('B7:' . $endCol . '8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置body
     */
    private function setBodyWhenCommonFixed()
    {
        $date_item = $this->list_product_algos['details'];
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, $date_item['section_begin'] . '--' . $date_item['section_end']);
        $this->sheet->setCellValue('B' . $this->line, $date_item['price']);
        $this->sheet->setCellValue('C' . $this->line, number_format($date_item['fee_number']));

        // 如果是快捷版
        if ($this->_determine_is_shortcut) {
            $this->sheet->setCellValue('D' . $this->line, number_format($date_item['shortcut_number']));
            $this->sheet->setCellValue('E' . $this->line, number_format($date_item['money'], 2));
        } else {
            $this->sheet->setCellValue('D' . $this->line, number_format($date_item['money'], 2));
        }
        $this->money_sum += $date_item['money'] ?? 0;
    }

    /**
     * 设置标题
     */
    private function setHeadWhenCommonFixed()
    {
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, '期间');
        $this->sheet->setCellValue('B' . $this->line, '单价');

        // 如果是快捷版的话
        if ($this->_determine_is_shortcut) {
            $this->sheet->setCellValue('C' . $this->line, '有效查询字段量');
            $this->sheet->setCellValue('D' . $this->line, '有效调用量');
            $this->sheet->setCellValue('E' . $this->line, '消耗金额');
            $this->setMaxCell(5);
            return;
        }
        $this->sheet->setCellValue('C' . $this->line, '有效调用量');
        $this->sheet->setCellValue('D' . $this->line, '消耗金额');
        $this->setMaxCell(4);
    }

    /**
     * 设置excel内容,当不能输出的时候
     *
     * @param array $alo_item
     */
    private function setContentWhenCommonDate()
    {
        // 设置标题
        $this->setHeadWhenCommonDate();

        // 设置body
        $this->setBodyWhenCommonDate();

        //美化excel样式
        $this->setStyleWhenCommonDate();
    }


    private function setStyleWhenCommonDate()
    {
        $endCol = $this->_determine_is_shortcut ? 'E' : 'D';
        //行高
        for ($i = 1; $i <= 10; $i++) {
            $this->sheet->getRowDimension($i)
                ->setRowHeight('18');
        }

        //垂直居中
        $this->sheet->getStyle('A1:' . $endCol . '10')
            ->getAlignment()
            ->setVertical(Alignment::VERTICAL_CENTER);

        //水平居中
        $this->sheet->getStyle('A1:' . $endCol . '10')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        //金额水平居右
        $this->sheet->getStyle('B7:' . $endCol . '8')
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_RIGHT);
    }

    /**
     * 设置body
     *
     * @param array $alo_item
     */
    private function setBodyWhenCommonDate()
    {
        $date_item = $this->list_product_algos['details'];

        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, $date_item['section_begin'] . '--' . $date_item['section_end']);
        $this->sheet->setCellValue('B' . $this->line, $date_item['price']);
        $this->sheet->setCellValue('C' . $this->line, number_format($date_item['fee_number']));

        // 如果是快捷版
        if ($this->_determine_is_shortcut) {
            $this->sheet->setCellValue('D' . $this->line, number_format($date_item['shortcut_number']));
            $this->sheet->setCellValue('E' . $this->line, number_format($date_item['money'], 2));
        } else {
            $this->sheet->setCellValue('D' . $this->line, number_format($date_item['money'], 2));
        }

        $this->money_sum += $date_item['money'] ?? 0;
    }

    /**
     * 设置标题
     */
    private function setHeadWhenCommonDate()
    {
        $this->line++;
        $this->sheet->setCellValue('A' . $this->line, '期间');
        $this->sheet->setCellValue('B' . $this->line, '价格');

        // 如果是快捷版的话
        if ($this->_determine_is_shortcut) {
            $this->sheet->setCellValue('C' . $this->line, '有效字段调用量');
            $this->sheet->setCellValue('D' . $this->line, '有效调取量');
            $this->sheet->setCellValue('E' . $this->line, '消耗金额');
            $this->setMaxCell(5);
            return;
        }
        $this->sheet->setCellValue('C' . $this->line, '有效调取量');
        $this->sheet->setCellValue('D' . $this->line, '消耗金额');
        $this->setMaxCell(4);
    }

    /**
     * @param int $max_cell
     */
    public function setMaxCell(int $max_cell)
    {
        if ($this->max_cell < $max_cell) {
            $this->max_cell = $max_cell;
        }
    }

    /**
     * 设置常量
     */
    private function setConstant()
    {
        $this->sheet->setCellValue('A5', '服务项目');
        $this->sheet->setCellValue('B5', $this->list_product_algos["product_name"]);

        // 合并单元格
        $this->sheet->mergeCells('B5:' . $this->list_cells[$this->max_cell] . '5');
    }

    /**
     * 生成head
     */
    private function setHead()
    {
        list($company, $name) = [$this->customer_info['company'], $this->customer_info['name']];
        $this->sheet->setCellValue('A1', '公司名称');
        $this->sheet->setCellValue('A2', '客户名称');
        $this->sheet->setCellValue('B1', $company);
        $this->sheet->setCellValue('B2', $name);
    }
}