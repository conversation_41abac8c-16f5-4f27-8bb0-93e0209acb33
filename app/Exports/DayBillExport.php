<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    FromArray, ShouldAutoSize, WithH<PERSON>ings, WithStrictNullComparison
};
use PhpOffice\PhpSpreadsheet\Shared\Font;

class DayBillExport implements FromArray, WithHeadings, ShouldAutoSize, WithStrictNullComparison
{
    private $list_bills;

    public function __construct(array $list_bills)
    {
        Font::setTrueTypeFontPath(storage_path() .'/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->list_bills = array_reduce($list_bills, function ($carry, $item) {
            $carry[] = [
                $item['date'] ?? ' ',
                number_format($item['section_number_sum']),
                number_format($item['money_sum'], 2)
            ];
            return $carry;
        }, []);
    }

    public function array(): array
    {
        return $this->list_bills;
    }

    public function headings(): array
    {
        return [
            '日期',
            '计费用量',
            '费用(元)'
        ];
    }
}