<?php

namespace App\Exports;

use App\Define\Common;
use PhpOffice\PhpSpreadsheet\Shared\Font;

class HistorySheetGenerate
{
    private $sheet;
    private $history;
    private $customer_info;

    /** @var int body 开始行数 */
    private $line_body = 10;

    /**
     * @param $sheet
     * @param $history
     * @param $customer_info
     */
    public function generate($sheet, $history, $customer_info)
    {
        // 设置属性
        $this->setAttr($sheet, $history, $customer_info);

        // 设置 head
        $this->setHeadForHistory();

        // 设置 body
        $line = $this->setBodyForHistory();

        // 设置 foot
        $this->setFootForHistory($line);

        // 设置自适应的宽度
        $this->setAutoSize();

        // 设置边框
        $this->setBorder($line);
    }

    /**
     * 设置边框
     * @param int $line
     */
    private function setBorder(int $line)
    {
        $range_cell = 'A6:C' . ($line+2);
        $this->sheet->styleCells(
            $range_cell,
            [
                'borders' => [
                    'outline' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                    'inside' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ]
            ]
        );
    }

    /**
     * 设置自适应的宽度
     */
    private function setAutoSize()
    {
        Font::setTrueTypeFontPath(storage_path() .'/fonts/');
        Font::setAutoSizeMethod(Font::AUTOSIZE_METHOD_EXACT);
        $this->sheet->autoSize();
    }

    /**
     * 设置 foot
     * @param int $line
     */
    private function setFootForHistory(int $line)
    {
        list($money_recharge_aggregate, $money_consume_aggregate, $money_residue_now) = [
            $this->history['list_aggregate_money']['money_recharge_aggregate'],
            $this->history['list_aggregate_money']['money_consume_aggregate'],
            $this->history['list_aggregate_money']['money_residue_now'],
        ];

        $this->sheet->setCellValue('A' . ($line + 1), '合计');
        $this->sheet->setCellValue('B' . ($line + 1), number_format($money_recharge_aggregate, 2));
        $this->sheet->setCellValue('C' . ($line + 1), number_format($money_consume_aggregate, 2));

        $this->sheet->setCellValue('A' . ($line + 2), '未消耗金额');
        $this->sheet->setCellValue('B' . ($line + 2), number_format($money_residue_now, 2));
        $this->sheet->mergeCells('B' . ($line + 2) . ':C' . ($line + 2));


        // 设置下标
        $this->sheet->setCellValue('C' . ($line + 5), '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('C' . ($line + 6), '运营组');
        $this->sheet->setCellValue('C' . ($line + 7), $this->history['today']);
    }

    /**
     * 设置 body
     * @return int 返回已经在操作的line
     */
    private function setBodyForHistory(): int
    {
        list($list_show_date, $list_month_recharge, $list_month_consume) = [
            $this->history['month_month_this_year']['list_show_date'],
            $this->history['month_month_this_year']['list_month_recharge'],
            $this->history['month_month_this_year']['list_month_consume'],
        ];

        $line = $this->line_body;
        collect($list_show_date)->each(function ($date_item, $month) use (&$line, $list_month_recharge, $list_month_consume) {
            $this->sheet->setCellValue('A' . $line, $date_item['section_begin'] . '--' . $date_item['last_day_of_month']);
            $this->sheet->setCellValue('B' . $line, number_format($list_month_recharge[$month] ?? 0, 2));
            $this->sheet->setCellValue('C' . $line, number_format($list_month_consume[$month] ?? 0, 2));
            $line ++;
        });

        return $line;
    }

    /**
     * 设置head
     * @return mixed
     */
    private function setHeadForHistory()
    {
        $this->sheet->setCellValue('A1', Common::COMPANY_CN_NAME);
        $this->sheet->setCellValue('A2', '北京羽乐创新科技有限公司');
        $this->sheet->setCellValue('A4', '收费通知单');
        $this->sheet->setCellValue('A6', '客户名称');

        list($company, $name, $money_residue) = [
            $this->customer_info['company'],
            $this->customer_info['name'],
            $this->history['money_last_year']['money_residue']
        ];

        $this->sheet->setCellValue('B6', $company);
        $this->sheet->mergeCells('B6:C6');
        $this->sheet->setCellValue('A7', '合作项目');
        $this->sheet->setCellValue('B7', $name);
        $this->sheet->mergeCells('B7:C7');

        // 去年生于金额是否为0
        if ($money_residue == 0) {
            $this->sheet->setCellValue('A8', '期间');
            $this->sheet->setCellValue('B8', '充值金额');
            $this->sheet->setCellValue('C8', '消耗金额');
            $this->line_body = 9;

        } else {
            $this->sheet->setCellValue('A8', '去年剩余金额');
            $this->sheet->setCellValue('B8', number_format($money_residue, 2));
            $this->sheet->mergeCells('B8:C8');

            $this->sheet->setCellValue('A9', '期间');
            $this->sheet->setCellValue('B9', '充值金额');
            $this->sheet->setCellValue('C9', '消耗金额');
        }
    }

    /**
     * 设置属性
     * @param $sheet
     * @param $history
     * @param $customer_info
     */
    private function setAttr($sheet, $history, $customer_info)
    {
        $this->sheet = $sheet;
        $this->history = $history;
        $this->customer_info = $customer_info;
    }
}