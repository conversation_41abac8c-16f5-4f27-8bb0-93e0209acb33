<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\{
    WithEvents, WithTitle
};
use Maatwebsite\Excel\Events\AfterSheet;

class ProductAlgoExport implements WithTitle, WithEvents
{

    /** @var array 产品ID */
    private $product_info;

    /** @var array 产品下辖的账单算法组合 */
    private $list_product_algos;

    /** @var array 客户信息 */
    private $customer_info;

    public function __construct(array $product_info, array $list_product_algos, array $customer_info)
    {
        $this->product_info = $product_info;
        $this->list_product_algos = $list_product_algos;
        $this->customer_info = $customer_info;
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->product_info['product_name'] . '消耗明细';
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                (new ProductAlgoGenerate($event->sheet, $this->list_product_algos, $this->product_info, $this->customer_info))->generate();
            }
        ];
    }
}